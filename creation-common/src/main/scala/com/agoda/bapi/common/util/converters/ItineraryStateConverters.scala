package com.agoda.bapi.common.util.converters

import com.agoda.bapi.common.model.car.VehicleBookingStateModel.VehicleModelInternal
import com.agoda.bapi.common.model.flight.flightModel.FlightModelInternal
import com.agoda.bapi.common.model.itinerary.ItineraryInternalModel
import com.agoda.bapi.common.model.property.PropertyBookingStateModel.PropertyModelInternal
import com.agoda.bapi.common.model.protection.ProtectionBookingStateModel.ProtectionModelInternal
import com.agoda.bapi.common.util.converters.product.{FlightConverters, PropertyConverters, TripProtectionConverters, VehicleConverters}
import com.agoda.commons.agprotobuf.scalapb.utils.ProtoConverter
import com.agoda.mpbe.state.itinerary.ItineraryState
import com.agoda.mpbe.state.product.ProductModel
import com.agoda.mpbe.state.product.activity.ActivityProductModel
import com.agoda.mpbe.state.product.addOn.AddOnProductModel
import com.agoda.mpbe.state.product.cegFastTrack.CegFastTrackProductModel

object ItineraryStateConverters {
  def toItineraryState(itineraryInternalModel: ItineraryInternalModel): ItineraryState = {
    ItineraryState(
      itinerary = ItineraryConverters.toItineraryModel(itineraryInternalModel)
    )
  }

  def toItineraryState(
      flightInternalModel: FlightModelInternal,
      protectionInternalModels: Seq[ProtectionModelInternal] = Seq.empty
  ): ItineraryState = {
    //  Note here we don't convert FlightModelInternal to Itinerary one
    ItineraryState(
      product = ProductModel(
        flights = Seq(FlightConverters.toProtoFlightProductModel(flightInternalModel)),
        protections = protectionInternalModels.map(TripProtectionConverters.toProtoProtectionModel)
      )
    )
  }

  def toItineraryState(protectionModelInternal: ProtectionModelInternal): ItineraryState = {
    ItineraryState(
      product = ProductModel(
        protections = Seq(TripProtectionConverters.toProtoProtectionModel(protectionModelInternal))
      )
    )
  }

  def toItineraryState(propertyModelInternal: PropertyModelInternal): ItineraryState = {
    ItineraryState(
      product = ProductModel(
        properties = Seq(PropertyConverters.toProtoPropertyProductModel(propertyModelInternal))
      )
    )
  }

  def toItineraryState(vehicleInternalModel: VehicleModelInternal): ItineraryState = {
    ItineraryState(
      product = ProductModel(
        vehicles = Seq(VehicleConverters.toProtoVehicleProductModel(vehicleInternalModel))
      )
    )
  }

  def toItineraryState(activityProductModel: ActivityProductModel): ItineraryState = {
    ItineraryState(
      product = ProductModel(
        activities = Seq(activityProductModel)
      )
    )
  }

  def toItineraryState(cegFastTrackProductModel: CegFastTrackProductModel): ItineraryState = {
    ItineraryState(
      product = ProductModel(
        cegFastTracks = Seq(cegFastTrackProductModel)
      )
    )
  }

  def toItineraryState(addOnProductModel: AddOnProductModel): ItineraryState = {
    ItineraryState(
      product = ProductModel(
        addOns = Seq(addOnProductModel)
      )
    )
  }

  implicit class ItineraryStateToString(val itineraryState: ItineraryState) extends AnyVal {
    def toBase64String: String            = ProtoConverter.protoToString(itineraryState)
    def toBase64OptString: Option[String] = Option(toBase64String)
  }
}
