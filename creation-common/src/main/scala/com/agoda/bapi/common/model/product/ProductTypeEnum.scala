package com.agoda.bapi.common.model.product

import com.agoda.bapi.common.util.JacksonSerializer.EnumAsStringDeserializer
import com.agoda.mpb.common.models.state.ProductType
import com.fasterxml.jackson.core.`type`.TypeReference

object ProductTypeEnum extends Enumeration {
  type ProductTypeEnum = Value

  /* We should make com.agoda.mpb.common.models.state.ProductType and ProductTypeEnum (this object) equal but somehow
   * currently they are not. */
  // Need to unified this in the future
  val Property: ProductTypeEnum = Value(ProductType.Hotel.id) // 1
  val Flight: ProductTypeEnum   = Value(2)
  @deprecated("Multi Product type is deprecated, please use other single product type instead")
  val Multi: ProductTypeEnum              = Value(3)
  val Protection: ProductTypeEnum         = Value(5)
  val Activity: ProductTypeEnum           = Value(ProductType.Activity.id)           // 12
  val CegFastTrack: ProductTypeEnum       = Value(ProductType.CEGFastTrack.id)       // 14
  val CancelForAnyReason: ProductTypeEnum = Value(ProductType.CancelForAnyReason.id) // 15
  val Car: ProductTypeEnum                = Value(ProductType.Cars.id)               // 101
  val FlightCheckIn: ProductTypeEnum      = Value(ProductType.FlightCheckIn.id)      // 16
  val Unknown: ProductTypeEnum            = Value(99)

  def toId(name: String): Int =
    values.find(v => Option(name).map(_.toLowerCase).contains(v.toString.toLowerCase())).getOrElse(Unknown).id

  def fromValue(i: Int): ProductTypeEnum =
    values.find(_.id == i).getOrElse(Unknown)
}

class ProductTypeEnumType         extends TypeReference[ProductTypeEnum.type]
class ProductTypeEnumDeserializer extends EnumAsStringDeserializer(ProductTypeEnum)
