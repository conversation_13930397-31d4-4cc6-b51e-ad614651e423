package com.agoda.bapi.common.util.converters.product.to.bapi

import com.agoda.bapi.common.model.base._
import com.agoda.bapi.common.model.consumerFintech.PropertyBookingConsumerFintech
import com.agoda.bapi.common.model.creation.common.JsonAccountingEntity
import com.agoda.bapi.common.model.property.PropertyBookingStateModel._
import com.agoda.bapi.common.util.converters.CommonProductConverters
import com.agoda.mpbe.state.product.property.{ReplacementResult => ProtobufReplacementResult, _}

/**
  * Methods below are converters for Protobuf to BAPI internal models
  */
protected[converters] trait BapiPropertyConverters {

  def toBapiPropertyProductModel(model: PropertyProductModel): PropertyModelInternal = {
    PropertyModelInternal(
      bookingList = Seq(toBapiPropertyBookingModel(model)),
      itinerary = toBapiPropertyItinerary(model.itinerary)
    )
  }

  private def toBapiPropertyBookingModel(model: PropertyProductModel): PropertyBookingModel = {
    PropertyBookingModel(
      additionalData = toBapiAdditionalBookingData(model.additionalData),
      booking = toBapiPropertyBooking(model.booking),
      bookingAgent = toBapiPropertyBookingAgent(model.bookingAgent),
      bookingSummary = toBapiPropertyBookingSummary(model.bookingSummary),
      bookingAttributionV2 = model.bookingAttributionsV2.map(toBapiPropertyBookingAttributionV2),
      bookingHotel = toBapiPropertyBookingHotel(model.bookingHotel),
      bookingHotelRoom = model.bookingHotelRooms.map(toBapiPropertyBookingHotelRoom),
      bookingHotelRoomChildren = model.bookingHotelRoomChildren.map(toBapiPropertyBookingHotelRoomChildren),
      bookingSetting = model.bookingSetting.map(toBapiPropertyBookingSetting),
      bookingToken = model.bookingToken.map(toBapiPropertyBookingToken),
      fraudInfo = model.fraudInfo.map(toBapiPropertyBookingFraudInfo),
      financialBreakdown = model.financialBreakdowns.map(toBapiPropertyFinancialBreakdown),
      bookingSucc = model.bookingSingleUsedCreditCards.map(toBapiPropertyBookingSucc),
      charges = model.charges.map(toBapiPropertyBookingCharges),
      sellInfo = model.sellInfo.map(toBapiPropertyBookingSellInfo),
      sellInfoHistory = model.sellInfoHistories.map(toBapiPropertyBookingSellInfoHistory),
      partnerLoyaltyPoint = model.partnerLoyaltyPoint.map(toBapiPropertyBookingPartnerLoyaltyPoint),
      rateCategory = model.rateCategory.map(toBapiPropertyBookingRateCategory),
      rateCategoryBenefit = model.rateCategoryBenefits.map(toBapiPropertyBookingRateCategoryBenefit),
      paymentsAdvanceInfo = model.paymentsAdvanceInfos.map(toBapiPropertyBookingPaymentsAdvanceInfo),
      payment = model.payments.map(toBapiPropertyBookingPayment),
      provisioning = model.provisionings.map(toBapiPropertyBookingProvisioning),
      earningGiftCard = model.earningGiftCard.map(toBapiPropertyBookingEarningGiftCard),
      earningGiftCardInfo = model.earningGiftCardInfo.map(toBapiPropertyBookingEarningGiftCardInfo),
      schedule = model.schedule.map(toBapiPropertyBookingSchedule),
      delaySettlementSchedule = model.delaySettlementSchedule.map(toBapiPropertyDelaySettlementBookingSchedule),
      propertyBookingPax = model.bookingPassengers.map(toBapiPropertyBookingPax),
      fraudCcBlacklist = model.fraudCcBlacklist.map(toBapiPropertyFraudCcBlacklist),
      fraudCookieBlacklist = model.fraudCookieBlacklist.map(toBapiPropertyFraudCookieBlacklist),
      productKey = model.productKey,
      bookingSupplierData = model.bookingSupplierData.map(toBapiPropertyBookingSupplierData),
      bookingChildPromotions = model.childPromotions.map(toBapiPropertyBookingChildPromotions),
      rewardEarning = model.rewardEarning.map(toBapiPropertyRewardEarning),
      bookingAmendments = model.bookingAmendments.map(toBapiPropertyBookingAmendments),
      propertyBookingAcknowledgement = model.propertyBookingAcknowledgement.map(toBapiPropertyBookingAcknowledgement),
      bookingResell = model.bookingResell.map(toBapiPropertyBookingResell),
      bookingCampaign = model.bookingCampaign.map(toBapiBookingCampaign),
      bookingDiscountInformations = model.bookingDiscountInformations.map(toBapiPropertyBookingDiscountInformations),
      bookingConsumerFintech = model.bookingConsumerFintech.map(toBapiPropertyBookingConsumerFintech),
      essInfos = model.essInfos.map(BaseBookingEssInfoInternal.mapFromBaseBooking),
      baseBooking = model.baseBooking.map(BaseBookingModelInternal.mapFromBaseBooking)
    )
  }

  private def toBapiPropertyBookingDiscountInformations(
      model: EbePropertyBookingDiscountInformation
  ): PropertyBookingDiscountInformation = PropertyBookingDiscountInformation(
    bookingId = model.bookingId,
    discountType = model.discountType,
    discountId = model.discountId,
    discountName = model.discountName,
    discountRateType = model.discountRateType,
    discountRate = model.discountRate,
    appliedDate = model.appliedDate,
    recStatus = model.recStatus,
    recCreatedWhen = model.recCreatedWhen,
    recCreatedBy = model.recCreatedBy,
    recModifyWhen = model.recModifyWhen,
    recModifyBy = model.recModifyBy,
    discountRateDouble = model.discountRateDouble,
    isActive = model.isActive,
    discountAppliedDate = model.discountAppliedDate
  )

  private def toBapiPropertyBookingSupplierData(model: EbePropertyBookingSupplierData): PropertyBookingSupplierData = {
    PropertyBookingSupplierData(
      bookingId = model.bookingId,
      supplierSiteId = model.supplierSiteId,
      supplierSubSiteId = model.supplierSubSiteId,
      supplierTransactionId = model.supplierTransactionId,
      additionalInfo = model.additionalInfo,
      freeItem = model.freeItem,
      reservationInfo = model.reservationInfo,
      inventoryData = model.inventoryData,
      childRateSettings = model.childRateSettings,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = model.recCreatedBy,
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy,
      supplierResponseInfo = model.supplierResponseInfo,
      paymentChannel = model.paymentChannel
    )
  }

  private def toBapiAdditionalBookingData(model: EbeAdditionalBookingData): AdditionalBookingData = {
    AdditionalBookingData(
      destinationCityId = model.destinationCityId,
      destinationCountryId = model.destinationCountryId,
      isSendSmsForBooking = model.isSendSmsForBooking,
      isGlobalPartnerServiceBranding = model.isGlobalPartnerServiceBranding,
      localizedRatePlanName = model.localizedRatePlanName,
      localizedBenefits = Some(model.localizedBenefits),
      isShowCustomerInfoInVoucher = model.isShowCustomerInfoInVoucher,
      emailDisplaySetting = model.emailDisplaySetting,
      supplierDisplayName = model.supplierDisplayName,
      supplierContact = model.supplierContact.map(toBapiContact),
      dmcType = model.dmcType,
      hotelPrimaryContact = model.hotelPrimaryContact.map(toBapiContact),
      hotelYcsContact = model.hotelYcsContact.map(toBapiContact),
      isMessagingAllowedWithBooking = model.isMessagingAllowedWithBooking,
      ackRequestId = model.ackRequestId,
      hotelDisplayName = model.hotelDisplayName,
      hotelLocalName = model.hotelLocalName,
      hotelLocale = model.hotelLocale,
      hotelDefaultName = model.hotelDefaultName,
      // Moved from paymentInfo section because this is property specific
      destinationCurrency = model.destinationCurrency,
      destinationExchangeRate = model.destinationExchangeRate,
      rateQuoteId = model.rateQuoteId,
      paymentOption = model.paymentOption,
      hotelPaymentMethodId = model.hotelPaymentMethodId,
      hotelPaymentConditionId = model.hotelPaymentConditionId,
      unmodifiedSpecialRequest = model.unmodifiedSpecialRequest.map(toBapiSpecialRequest),
      occupancy = Option(model.occupancy.map(toOccupancy)),
      supplierData = model.supplierData.map(toSupplierData),
      childRateSettings = Option(model.childRateSettings.map(toChildRateSetting)).filter(_.nonEmpty),
      isCrossSellAvailable = model.isCrossSellAvailable,
      selfServiceUrl = model.selfServiceUrl,
      isAddDelay = model.isAddDelay,
      supplierSessionId = model.supplierSessionId,
      bookingHoldingPartnerNames = model.bookingHoldingPartnerNames.map(toBapiPropertyBookingBookingHoldingPartnerName),
      replacementResults = model.replacementResults.map(toBapiPropertyBookingReplacementResult),
      ebeLiteStateId = model.ebeLiteStateId
    )
  }

  private def toBapiSpecialRequest(model: UnmodifiedSpecialRequest): SpecialRequest = {
    SpecialRequest(
      additionalNotes = model.additionalNotes,
      bookerAnswer = model.bookerAnswer,
      arrivalTime = model.arrivalTime
    )
  }

  private def toOccupancy(model: EbePropertyBookingOccupancy): PropertyBookingOccupancy = {
    PropertyBookingOccupancy(
      roomNo = model.roomNo,
      noOfAdultMales = model.noOfMaleAdults,
      noOfAdultFemales = model.noOfFemaleAdults,
      childOccupancy = Option(
        model.childOccupancy.map(occupancyItem =>
          ChildOccupancyInfo(
            typeId = occupancyItem.typeId,
            count = occupancyItem.count
          )
        )
      )
    )
  }

  private def toSupplierData(model: EbePropertyBookingSupplier): PropertyBookingSupplier = {
    PropertyBookingSupplier(
      supplierSiteId = model.supplierSiteId,
      supplierSubSiteId = model.supplierSubSiteId,
      supplierTransactionId = model.supplierTransactionId,
      externalBookingId = model.externalBookingId,
      supplierAdditionalInfo = model.supplierAdditionalInfo,
      supplierFreeItem = model.supplierFreeItem,
      supplierReservationInfo = model.supplierReservationInfo,
      supplierChainId = model.supplierChainId,
      authenticationKey = model.authenticationKey,
      paymentChannel = model.paymentChannel
    )
  }

  private def toChildRateSetting(model: EbePropertyBookingChildRateSetting): PropertyBookingChildRateSetting = {
    PropertyBookingChildRateSetting(
      childRateTypeId = model.childRateTypeId,
      childRatePricingTypeId = model.childRatePricingTypeId,
      value = model.value,
      isCountAsRoomOcc = model.isCountedAsRoomOccupancy
    )
  }

  private def toBapiContact(model: EbeContact): Contact = {
    Contact(
      phone = model.phone,
      email = model.email,
      sms = model.sms
    )
  }

  private def toBapiPropertyItinerary(model: EbePropertyItinerary): PropertyItinerary = {
    PropertyItinerary(
      itineraryId = model.itineraryId,
      itineraryStatusId = model.itineraryStatusId,
      itineraryTypeId = model.itineraryTypeId,
      memberID = model.memberID,
      itineraryName = model.itineraryName,
      itineraryDate = model.itineraryDate,
      isAutoGenerated = model.isAutoGenerated,
      tripStartDate = model.tripStartDate,
      tripEndDate = model.tripEndDate,
      isMultipleBookings = model.isMultipleBookings,
      isProcessed = model.isProcessed,
      isFraudVerifySent = model.isFraudVerifySent,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = model.recCreatedBy,
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy
    )
  }

  private def toBapiPropertyBooking(model: EbePropertyBooking): PropertyBooking = {
    PropertyBooking(
      bookingId = model.bookingId,
      itineraryId = model.itineraryId,
      dataCenter = model.dataCenter,
      preBookingIdOld = model.preBookingIdOld,
      bookingDate = model.bookingDate,
      bookingDateFrom = model.bookingDateFrom,
      bookingDateUntil = model.bookingDateUntil,
      arrivalTimeWindow = model.arrivalTimeWindow,
      availabilityType = model.availabilityType,
      paymentModel = model.paymentModel,
      ccReceived = model.ccReceived,
      fraudScore = model.fraudScore,
      fraudAction = model.fraudAction,
      rewardsSpecialOfferId = model.rewardsSpecialOfferId,
      cancellationPolicyCode = model.cancellationPolicyCode,
      cancellationPolicy = model.cancellationPolicy,
      cancellationFeeAmount = model.cancellationFeeAmount,
      cancellationFeeLocalAmount = model.cancellationFeeLocalAmount,
      cancellationFeeLocalCurrency = model.cancellationFeeLocalCurrency,
      dmcId = model.dmcId,
      dmcCode = model.dmcCode,
      dmcSpecificData = model.dmcSpecificData,
      bookingExternalReference = model.bookingExternalReference,
      isAutoProcessed = model.isAutoProcessed,
      workflowId = model.workflowId,
      workflowStateId = model.workflowStateId,
      workflowActionId = model.workflowActionId,
      workflowParameterId = model.workflowParameterId,
      workflowParameterValue = model.workflowParameterValue,
      bookingTypeId = model.bookingTypeId,
      workflowStateSince = model.workflowStateSince,
      cidList = model.cidList,
      sessionId = model.sessionId,
      serverName = model.serverName,
      clientIpAddress = model.clientIpAddress,
      referralUrl = model.referralUrl,
      ssResultId = model.ssResultId,
      ssId = model.ssId,
      ssIdFb = model.ssIdFb,
      ssResultType = model.ssResultType,
      ssProviderId = model.ssProviderId,
      languageId = model.languageId,
      storefrontId = model.storefrontId,
      isChargeBackRequested = model.isChargeBackRequested,
      isSpecialOffer = model.isSpecialOffer,
      isAffiliateProcessed = model.isAffiliateProcessed,
      isRewardsProcessed = model.isRewardsProcessed,
      isEmailWhitelist = model.isEmailWhitelist,
      userTracking = model.userTracking,
      agentName = model.agentName,
      agentBookingChannel = model.agentBookingChannel,
      agentClientIp = model.agentClientIp,
      isFraudReview = model.isFraudReview,
      reviewBy = model.reviewBy,
      isLocked = model.isLocked,
      promotionCode = model.promotionCode,
      promotionText = model.promotionText,
      discountType = model.discountType,
      discountAmount = model.discountAmount,
      discountSavings = model.discountSavings,
      ycsPromotionId = model.ycsPromotionId,
      ycsPromotionText = model.ycsPromotionText,
      ycsForeignPromotionText = model.ycsForeignPromotionText,
      included = model.included,
      excluded = model.excluded,
      isAgentAssist = model.isAgentAssist,
      trackingCookieId = model.trackingCookieId,
      trackingCookieDate = model.trackingCookieDate,
      faxFormRequestStatus = model.faxFormRequestStatus,
      affiliateModel = model.affiliateModel,
      affiliatePaymentMethod = model.affiliatePaymentMethod,
      fallbackCxlPolicyCode = model.fallbackCxlPolicyCode,
      pointMultiply = model.pointMultiply,
      trackingTag = model.trackingTag,
      isReminderSent = model.isReminderSent,
      rateChannel = model.rateChannel,
      promotionCampaignId = model.promotionCampaignId,
      membershipContentText = model.membershipContentText,
      membershipContentLabel = model.membershipContentLabel,
      agodaCancellationFeeId = model.agodaCancellationFeeId,
      fraudCheckIp = model.fraudCheckIp,
      rateModelType = model.rateModelType,
      feCid = model.feCid,
      preBookingId = model.preBookingId,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = model.recCreatedBy,
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy,
      agodaManagedYcs = Some(model.agodaManagedYcs),
      childPromotions = Option(model.childPromotions.map(toChildPromotion)),
      externalLoyaltyDisplayTierId = model.externalLoyaltyDisplayTierId
    )
  }

  private def toChildPromotion(model: EbePropertyBookingChildPromotion): PropertyBookingChildPromotion = {
    PropertyBookingChildPromotion(
      campaignId = model.campaignId,
      promotionCode = model.promotionCode,
      discountAmount = model.discountAmount,
      localDiscountAmount = model.hotelLocalCurrencyDiscountAmount
    )
  }

  private def toBapiPropertyBookingAgent(model: EbePropertyBookingAgent): PropertyBookingAgent = {
    PropertyBookingAgent(
      bookingId = model.bookingId,
      origin = model.origin,
      osName = model.osName,
      osVersion = model.osVersion,
      browserName = model.browserName,
      browserLanguage = model.browserLanguage,
      browserVersion = model.browserVersion,
      browserSubversion = model.browserSubversion,
      browserBuildNumber = model.browserBuildNumber,
      deviceBrand = model.deviceBrand,
      deviceModel = model.deviceModel,
      isMobile = model.isMobile,
      isTouch = model.isTouch,
      deviceInfo = model.deviceInfo,
      additionalInfo = model.additionalInfo,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = model.recCreatedBy,
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy
    )
  }

  private[common] def toBapiPropertyBookingSummary(model: EbePropertyBookingSummary): PropertyBookingSummary = {
    PropertyBookingSummary(
      bookingId = model.bookingId,
      multiProductId = model.multiProductId,
      displayCurrency = model.displayCurrency,
      originalSellingAmount = model.originalSellingAmount,
      originalSupplierAmount = model.originalSupplierAmount,
      originalLocalCurrency = model.originalLocalCurrency,
      originalLocalSupplierAmount = model.originalLocalSupplierAmount,
      originalPaymentCurrency = model.originalPaymentCurrency,
      originalLocalPaymentAmount = model.originalLocalPaymentAmount,
      originalLocalCcPaymentAmount = model.originalLocalCcPaymentAmount,
      originalRedeemAmount = model.originalRedeemAmount,
      platformId = model.platformId,
      chargeOptionId = model.chargeOptionId,
      originalFullyChargeDate = model.originalFullyChargeDate,
      isUserLoggedIn = model.isUserLoggedIn,
      isTravelAgency = model.isTravelAgency,
      travelAgencyLoginChannel = model.travelAgencyLoginChannel,
      originalFullyAuthDate = model.originalFullyAuthDate,
      taxSurchargeInfo = model.taxSurchargeInfo,
      isNotCcRequired = model.isNotCcRequired,
      exchangeRateOption = model.exchangeRateOption,
      priceDisplaySetting = model.priceDisplaySetting,
      displayAmount = model.displayAmount,
      accountingEntity = model.accountingEntityJson.orElse {
        JsonAccountingEntity.accountingEntityToOptionJson(
          model.accountingEntity.map(CommonProductConverters.toBAPIAccountingEntity)
        )
      },
      bookingCreatedDate = model.bookingCreatedDate,
      isConfirmedBooking = model.isConfirmedBooking,
      whitelabelId = model.whitelabelId,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = model.recCreatedBy,
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy,
      bookingFormSessionId = model.bookingFormSessionId,
      commonBookingEventsInfo =
        model.commonBookingEventsInfo.map(CommonProductConverters.toBAPICommonBookingEventsInfo),
      stayType = model.stayType,
      isTestBooking = model.isTestBooking,
      pricefreezeBookingId = None,
      isFullyCharged = model.isFullyCharged,
      isSmartFlex = model.isSmartFlex,
      originalCancellationPolicyCode = model.originalCancellationPolicyCode,
      allGuestSameNationality = model.allGuestSameNationality,
      cancellationChargeItemId = model.cancellationChargeItemId,
      cancellationChargeType = model.cancellationChargeType,
      isSmartSaver = model.isSmartSaver
    )
  }

  private def toBapiPropertyBookingAttributionV2(
      model: EbePropertyBookingAttributionV2
  ): PropertyBookingAttributionV2 = {
    PropertyBookingAttributionV2(
      bookingId = model.bookingId,
      modelId = model.modelId,
      siteId = model.siteId,
      tag = model.tag,
      clickDate = model.clickDate,
      additionalData = model.additionalData,
      lastUpdateWhen = model.lastUpdateWhen,
      lastUpdateBy = model.lastUpdateBy
    )
  }

  private def toBapiPropertyBookingToken(model: EbePropertyBookingToken): PropertyBookingToken = {
    PropertyBookingToken(
      bookingId = model.bookingId,
      token = model.token
    )
  }

  private def toBapiPropertyBookingFraudInfo(model: EbePropertyBookingFraudInfo): PropertyBookingFraudInfo = {
    PropertyBookingFraudInfo(
      bookingId = model.bookingId,
      isAcknowledgedPolicy = model.isAcknowledgedPolicy,
      acknowledgedDate = model.acknowledgedDate,
      isActive = model.isActive,
      createdWhen = model.createdWhen,
      lastUpdatedWhen = model.lastUpdatedWhen
    )
  }

  private def toBapiPropertyBookingHotel(model: EbePropertyBookingHotel): PropertyBookingHotel = {
    PropertyBookingHotel(
      referenceId = model.referenceId,
      bookingHotelId = model.bookingHotelId,
      bookingId = model.bookingId,
      hotelId = model.hotelId,
      checkInDate = model.checkInDate,
      checkOutDate = model.checkOutDate,
      noOfRooms = model.noOfRooms,
      noOfAdults = model.noOfAdults,
      noOfChildren = model.noOfChildren,
      flightNumber = model.flightNumber,
      arrivalDatetime = model.arrivalDatetime,
      airportTransfer = model.airportTransfer,
      expectedCheckInTime = model.expectedCheckInTime,
      specialRequestNonsmoking = model.specialRequestNonsmoking,
      specialRequestHighFloor = model.specialRequestHighFloor,
      otherSpecialNeeds = model.otherSpecialNeeds,
      noExtraBedRateChoice = model.noExtraBedRateChoice,
      hotelRemark = model.hotelRemark,
      occupancyFreeSearch = model.occupancyFreeSearch,
      isAgencyPrepay = model.isAgencyPrepay,
      isNha = model.isNha,
      isAgodaReception = model.isAgodaReception,
      greetingMessage = model.greetingMessage,
      isAgodaAgency = model.isAgodaAgency,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = model.recCreatedBy,
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy,
      otherQuestionToBooker = model.otherQuestionToBooker
    )
  }

  private def toBapiPropertyBookingHotelRoom(model: EbePropertyBookingHotelRoom): PropertyBookingHotelRoom = {
    PropertyBookingHotelRoom(
      referenceId = model.referenceId,
      bookingHotelReferenceId = model.bookingHotelReferenceId,
      bookingId = model.bookingId,
      bookingHotelId = model.bookingHotelId,
      bookingRoomId = model.bookingRoomId,
      roomTypeId = model.roomTypeId,
      roomTypeName = model.roomTypeName,
      roomNo = model.roomNo,
      occupancy = model.occupancy,
      noOfBeds = model.noOfBeds,
      noOfExtrabeds = model.noOfExtrabeds,
      breakfastIncluded = model.breakfastIncluded,
      breakfastInfo = model.breakfastInfo,
      roomRemark = model.roomRemark,
      noOfMales = model.noOfMales,
      noOfFemales = model.noOfFemales,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = model.recCreatedBy,
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy,
      masterRoomTypeId = model.masterRoomTypeId,
      dynamicMappingTypeId = model.dynamicMappingTypeId
    )
  }

  private def toBapiPropertyBookingHotelRoomChildren(
      model: EbePropertyBookingHotelRoomChildren
  ): PropertyBookingHotelRoomChildren = {
    PropertyBookingHotelRoomChildren(
      referenceId = model.referenceId,
      bookingRoomId = model.bookingRoomId,
      childRateTypeId = model.childRateTypeId,
      number = model.number,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = model.recCreatedBy,
      recModifiedWhen = model.recModifiedWhen,
      recModifiedBy = model.recModifiedBy
    )
  }

  private def toBapiPropertyFinancialBreakdown(model: EbePropertyFinancialBreakdown): PropertyFinancialBreakdown = {
    PropertyFinancialBreakdown(
      referenceId = model.referenceId,
      bookingRoomReferenceId = model.bookingRoomReferenceId,
      breakdownId = model.breakdownId,
      bookingId = model.bookingId,
      bookingRoomId = model.bookingRoomId,
      dateOfStay = model.dateOfStay,
      itemId = model.itemId,
      typeId = model.typeId,
      surchargeId = model.surchargeId,
      taxFeeId = model.taxFeeId,
      quantity = model.quantity,
      localCurrency = model.localCurrency,
      localAmount = model.localAmount,
      exchangeRate = model.exchangeRate,
      usdAmount = model.usdAmount,
      supplierPaymentCurrency = model.supplierPaymentCurrency,
      supplierPaymentAmount = model.supplierPaymentAmount,
      supplierPaymentExchangeRate = model.supplierPaymentExchangeRate,
      refBreakdownId = model.refBreakdownId,
      isTransfer = model.isTransfer,
      hotelPaymentMethodId = model.hotelPaymentMethodId,
      upcId = model.upcId,
      taxPrototypeId = model.taxPrototypeId,
      subtypeId = model.subtypeId,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = model.recCreatedBy,
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy,
      actionId = model.actionId
    )
  }

  private def toBapiPropertyBookingCharges(model: EbePropertyBookingCharges): PropertyBookingCharges = {
    PropertyBookingCharges(
      referenceId = model.referenceId,
      bookingRoomReferenceId = model.bookingRoomReferenceId,
      chargeId = model.chargeId,
      bookingId = model.bookingId,
      bookingRoomId = model.bookingRoomId,
      chargeDate = model.chargeDate,
      chargeTypeId = model.chargeTypeId,
      chargeName = model.chargeName,
      description = model.description,
      quantity = model.quantity,
      unitOfMeasure = model.unitOfMeasure,
      unitSellingPrice = model.unitSellingPrice,
      unitSupplierPrice = model.unitSupplierPrice,
      sellingAmount = model.sellingAmount,
      sellingTax = model.sellingTax,
      supplierAmount = model.supplierAmount,
      supplierTax = model.supplierTax,
      localSupplierAmount = model.localSupplierAmount,
      localSupplierTax = model.localSupplierTax,
      localCurrency = model.localCurrency,
      exchangeRateLocalToSupplier = model.exchangeRateLocalToSupplier,
      exchangeRateLastUpdate = model.exchangeRateLastUpdate,
      refChargeId = model.refChargeId,
      isRecorded = model.isRecorded,
      localSellingAmount = model.localSellingAmount,
      b2bNetAmount = model.b2BNetAmount,
      b2bLocalNetAmount = model.b2BLocalNetAmount,
      b2bLocalCurrency = model.b2BLocalCurrency,
      b2bExchangeRateLocalToUSD = model.b2BExchangeRateLocalToUSD,
      hasBreakdown = model.hasBreakdown,
      isBreakdownProcessed = model.isBreakdownProcessed,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = model.recCreatedBy,
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy
    )
  }

  private def toBapiPropertyBookingSetting(model: EbePropertyBookingSetting): PropertyBookingSetting = {
    PropertyBookingSetting(
      settingId = model.settingId,
      bookingId = model.bookingId,
      settingTypeId = model.settingTypeId,
      settingJsonData = model.settingJsonData,
      createdWhen = model.createdWhen,
      createdBy = model.createdBy
    )
  }

  private def toBapiPropertyBookingSellInfo(model: EbePropertyBookingSellInfo): PropertyBookingSellInfo = {
    PropertyBookingSellInfo(
      bookingId = model.bookingId,
      pricingTemplateId = model.pricingTemplateId,
      downLiftAmountUsd = model.downLiftAmountUsd,
      sellTagId = model.sellTagId,
      searchId = model.searchId,
      isAdvanceGuarantee = model.isAdvanceGuarantee,
      offerId = model.offerId,
      lastUpdatedWhen = model.lastUpdatedWhen,
      lastUpdatedBy = model.lastUpdatedBy,
      pricingRequestId = model.pricingRequestId,
      firedrillContractId = model.firedrillContractId,
      firedrillContractTypeId = model.firedrillContractTypeId
    )
  }

  private def toBapiPropertyBookingSellInfoHistory(
      model: EbePropertyBookingSellInfoHistory
  ): PropertyBookingSellInfoHistory = {
    PropertyBookingSellInfoHistory(
      historyActionId = model.historyActionId,
      historyActionDate = model.historyActionDate,
      historyActionBy = model.historyActionBy,
      bookingId = model.bookingId,
      pricingTemplateId = model.pricingTemplateId,
      downliftAmountUsd = model.downliftAmountUsd,
      sellTagId = model.sellTagId,
      lastUpdatedWhen = model.lastUpdatedWhen,
      searchId = model.searchId,
      isAdvanceGuarantee = model.isAdvanceGuarantee,
      lastUpdatedBy = model.lastUpdatedBy,
      bookingSellInfoHistoryId = model.bookingSellInfoHistoryId,
      firedrillContractId = model.firedrillContractId,
      firedrillContractTypeId = model.firedrillContractTypeId
    )
  }

  private def toBapiPropertyBookingPartnerLoyaltyPoint(
      model: EbePropertyBookingPartnerLoyaltyPoint
  ): PropertyBookingPartnerLoyaltyPoint = {
    PropertyBookingPartnerLoyaltyPoint(
      bookingId = model.bookingId,
      programId = model.programId,
      membershipId = model.membershipId,
      loyaltyPoint = model.loyaltyPoint,
      pointCostAmountUsd = model.pointCostAmountUsd,
      pointCostAmountLocal = model.pointCostAmountLocal,
      gstCostAmountUsd = model.gstCostAmountUsd,
      gstCostAmountLocal = model.gstCostAmountLocal,
      currency = model.currency,
      lastUpdatedWhen = model.lastUpdatedWhen
    )
  }

  private def toBapiPropertyBookingRateCategory(model: EbePropertyBookingRateCategory): PropertyBookingRateCategory = {
    PropertyBookingRateCategory(
      referenceId = model.referenceId,
      bookingRateCategoryId = model.bookingRateCategoryId,
      bookingId = model.bookingId,
      rateCategoryId = model.rateCategoryId,
      rateCategoryCode = model.rateCategoryCode,
      chargeTypeCode = model.chargeTypeCode,
      rateCategoryValue = model.rateCategoryValue,
      isActive = model.isActive,
      isRoh = model.isRoh,
      inventoryTypeId = model.inventoryTypeId,
      stayPackageType = model.stayPackageType,
      rateCategoryContentToken = model.rateCategoryContentToken,
      lastUpdatedWhen = model.lastUpdatedWhen,
      lastUpdatedBy = model.lastUpdatedBy
    )
  }

  private def toBapiPropertyBookingRateCategoryBenefit(
      model: EbePropertyBookingRateCategoryBenefit
  ): PropertyBookingRateCategoryBenefit = {
    PropertyBookingRateCategoryBenefit(
      referenceId = model.referenceId,
      rateCategoryReferenceId = model.rateCategoryReferenceId,
      rateCategoryBenefitId = model.rateCategoryBenefitId,
      bookingRateCategoryId = model.bookingRateCategoryId,
      benefitId = model.benefitId,
      benefitValue = model.benefitValue,
      benefitDescription = model.benefitDescription,
      benefitRemark = model.benefitRemark,
      benefitParameters = model.benefitParams,
      targetType = model.targetType
    )
  }

  private def toBapiPropertyBookingPaymentsAdvanceInfo(
      model: EbePropertyBookingPaymentsAdvanceInfo
  ): PropertyBookingPaymentsAdvanceInfo = {
    PropertyBookingPaymentsAdvanceInfo(
      paymentId = model.paymentId,
      token = model.token,
      referencePaymentId = Some(model.referencePaymentId)
    )
  }

  private def toBapiPropertyBookingPayment(model: EbePropertyBookingPayment): PropertyBookingPayment = {
    PropertyBookingPayment(
      paymentId = model.paymentId,
      bookingId = model.bookingId,
      creditCardId = model.creditCardId,
      transactionDate = model.transactionDate,
      transactionType = Some(model.transactionType.value),
      refNo = model.refNo,
      refType = model.refType,
      paymentTypeId = Some(model.paymentTypeId.value),
      gatewayId = Some(model.gatewayId.value),
      transactionId = model.transactionId,
      gatewayReferenceIn = model.gatewayReferenceIn,
      gatewayReferenceOut = model.gatewayReferenceOut,
      redeemedPoints = model.redeemedPoints,
      paymentCurrency = model.paymentCurrency,
      paymentAmount = model.paymentAmount,
      refundAmount = model.refundAmount,
      bookingProviderTypeId = model.bookingProviderTypeId,
      bookingDaysUntilArrival = model.bookingDaysUntilArrival,
      gatewayTestMode = model.gatewayTestMode,
      paymentRequestStatus = Some(model.paymentRequestStatus.value),
      localAmount = model.localAmount,
      localCurrency = model.localCurrency,
      exchangeRateLocalToPayment = model.exchangeRateLocalToPayment,
      creditCardCurrency = model.creditCardCurrency,
      exchangeRateLocalToCcCurrency = model.exchangeRateLocalToCcCurrency,
      refBookingId = model.refBookingId,
      refPaymentId = model.refPaymentId,
      upliftAmount = model.upliftAmount,
      siteExchangeRate = model.siteExchangeRate,
      upliftExchangeRate = model.upliftExchangeRate,
      destinationCurrency = model.destinationCurrency,
      destinationExchangeRate = model.destinationExchangeRate,
      rateQuoteId = model.rateQuoteId,
      fxmpTranId = model.fxmpTranId,
      statusId = model.statusId,
      statusMessage = model.statusMessage,
      submitDate = model.submitDate,
      IsRecorded = model.isRecorded,
      isTransfer = model.isTransfer,
      rowVersion = model.rowVersion,
      paymentOption = model.paymentOption,
      paymentToken = model.paymentToken,
      custVisibleUpliftAmount = model.custVisibleUpliftAmount,
      processed_3dsOption = model.processed3DsOption,
      planId = model.planId,
      installmentPlanId = model.installmentPlanId,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = Some(model.recCreatedBy),
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy,
      referenceId = model.referenceId,
      internalToken = model.internalToken
    )
  }

  private def toBapiPropertyBookingProvisioning(model: EbePropertyBookingProvisioning): PropertyBookingProvisioning = {
    PropertyBookingProvisioning(
      referenceId = model.referenceId,
      bookingProvisioningId = model.bookingProvisioningId,
      bookingId = model.bookingId,
      dmcId = model.dmcId,
      methodId = model.methodId,
      transmittalDate = model.transmittalDate,
      replyDate = model.replyDate,
      bookingExternalReference = model.bookingExternalReference,
      dmcDueDate = model.dmcDueDate,
      localDmcCurrency = model.localDmcCurrency,
      originalNetRate = model.originalNetRate,
      netRate = model.netRate,
      request_XML = model.requestXML,
      response_XML = model.responseXML,
      remarks = model.remarks,
      replyStatus = model.replyStatus,
      purchaseDetails = model.purchaseDetails,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = model.recCreatedBy,
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy,
      token = model.token
    )
  }

  private def toBapiPropertyBookingEarningGiftCard(
      model: EbePropertyBookingEarningGiftCard
  ): PropertyBookingEarningGiftCard = {
    PropertyBookingEarningGiftCard(
      bookingId = model.bookingId,
      dateOffset = model.dateOffset,
      earnDate = model.earnDate,
      usdAmount = model.usdAmount,
      isProcessed = model.isProcessed,
      giftCardGuid = model.giftCardGuid,
      isAutoMigrate = model.isAutoMigrate,
      earnDatetime = model.earnDatetime,
      processDatetime = model.processDatetime,
      processStatus = model.processStatus,
      rescheduleCount = model.rescheduleCount,
      lastUpdatedWhen = model.lastUpdatedWhen,
      lastUpdatedBy = model.lastUpdatedBy
    )
  }

  private def toBapiPropertyBookingEarningGiftCardInfo(
      model: EbePropertyBookingEarningGiftCardInfo
  ): PropertyBookingEarningGiftCardInfo = {
    PropertyBookingEarningGiftCardInfo(
      giftCardGuid = model.giftCardGuid,
      expiryDays = model.expiryDays,
      expiryAlreadyExist = model.expiryAlreadyExist,
      expiryMatch = model.expiryMatch,
      ebeExpiry = model.ebeExpiry,
      createdWhen = model.createdWhen,
      createdBy = model.createdBy,
      lastUpdatedWhen = model.lastUpdatedWhen,
      lastUpdatedBy = model.lastUpdatedBy
    )
  }

  private def toBapiPropertyBookingSchedule(model: EbePropertyBookingSchedule): PropertyBookingSchedule = {
    PropertyBookingSchedule(
      bookingId = model.bookingId,
      fullyChargeDate = model.fullyChargeDate,
      fraudCheckDate = model.fraudCheckDate,
      isFraudChecked = model.isFraudChecked,
      fullyAuthDate = model.fullyAuthDate,
      GMTOffset = model.GMTOffset,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = model.recCreatedBy,
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy
    )
  }

  private def toBapiPropertyDelaySettlementBookingSchedule(
      model: EbePropertyDelaySettlementSchedule
  ): PropertyDelaySettlementSchedule = {
    PropertyDelaySettlementSchedule(
      bookingId = model.bookingId,
      delaySettlementDate = model.delaySettlementDate,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = model.recCreatedBy,
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy
    )
  }

  private def toBapiPropertyBookingSucc(model: EbePropertyBookingSingleUsedCreditCard): PropertyBookingSucc = {
    PropertyBookingSucc(
      bookingSuccId = model.bookingSuccId,
      bookingId = model.bookingId,
      succId = model.succId,
      last4 = model.last4,
      exchangeRate = model.exchangeRate,
      requestCreditLimit = model.requestCreditLimit,
      actualCreditLimit = model.actualCreditLimit,
      currencyCode = model.currencyCode,
      cardType = model.cardType,
      cardStatus = model.cardStatus,
      isSubmitted = model.isSubmitted,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = Some(model.recCreatedBy),
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy,
      requestCurrency = model.requestCurrency,
      amount = model.amount,
      payoutUuid = model.payoutUuid
    )
  }

  private def toBapiPropertyBookingPax(model: EbePropertyBookingPax): PropertyBookingPax = {
    PropertyBookingPax(
      referenceId = model.referenceId,
      bookingRoomReferenceId = model.bookingRoomReferenceId,
      bookingPaxId = model.bookingPaxId,
      bookingRoomId = model.bookingRoomId,
      guestNo = model.guestNo,
      title = model.title,
      firstName = model.firstName,
      middleName = model.middleName,
      lastName = model.lastName,
      suffix = model.suffix,
      isExtraBed = model.isExtraBed,
      age = model.age,
      nationalityId = model.nationalityId,
      isPrimary = model.isPrimary,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = model.recCreatedBy,
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy
    )
  }

  private def toBapiPropertyFraudCcBlacklist(model: EbePropertyFraudCcBlacklist): PropertyFraudCcBlacklist = {
    PropertyFraudCcBlacklist(
      fdCcBlacklistId = model.fdCcBlacklistId,
      ccHash = model.ccHash,
      ccNo = model.ccNo,
      recStatus = model.recStatus,
      recCreatedBy = Some(model.recCreatedBy),
      recCreatedWhen = model.recCreatedWhen,
      recModifyBy = model.recModifyBy,
      recModifyWhen = model.recModifyWhen
    )
  }

  private def toBapiPropertyFraudCookieBlacklist(
      model: EbePropertyFraudCookieBlacklist
  ): PropertyFraudCookieBlacklist = {
    PropertyFraudCookieBlacklist(
      fdCookieBlacklistId = model.fdCookieBlacklistId,
      cookieId = model.cookieId,
      recStatus = model.recStatus,
      recCreatedBy = Some(model.recCreatedBy),
      recCreatedWhen = model.recCreatedWhen,
      recModifyBy = model.recModifyBy,
      recModifyWhen = model.recModifyWhen
    )
  }

  private def toBapiPropertyBookingChildPromotions(
      model: EbePropertyBookingChildPromotions
  ): PropertyBookingChildPromotions = {
    PropertyBookingChildPromotions(
      bookingChildPromotionId = model.bookingChildPromotionId,
      bookingId = model.bookingId,
      campaignId = model.campaignId,
      promotionCode = model.promotionCode,
      amountUsd = model.amountUsd,
      amount = model.amount,
      fundBy = model.fundBy,
      isActive = model.isActive,
      recModifiedBy = model.recModifiedBy,
      recModifiedWhen = model.recModifiedWhen
    )
  }

  private def toBapiPropertyRewardEarning(
      model: RewardEarning
  ): PropertyRewardEarning = {
    PropertyRewardEarning(
      bookingId = model.bookingId,
      itineraryId = model.itineraryId,
      rewardTypeId = model.rewardTypeId,
      rewardJsonToken = model.rewardJsonToken.getOrElse(""),
      recStatus = model.recStatus,
      recCreatedBy = model.recCreatedBy,
      recCreatedWhen = model.recCreatedWhen,
      recModifyBy = model.recModifyBy,
      recModifyWhen = model.recModifyWhen
    )
  }

  private def toBapiPropertyBookingAmendments(
      model: EbePropertyBookingAmendment
  ): PropertyBookingAmendment = {
    PropertyBookingAmendment(
      itineraryId = model.itineraryId,
      sellingCurrency = model.sellingCurrency,
      supplierCurrency = model.supplierCurrency,
      paymentInfo = model.paymentInfo,
      bookingId = model.bookingId,
      requestType = model.requestType,
      requestStatus = model.requestStatus,
      originalBookingDateFrom = model.originalBookingDateFrom,
      originalBookingDateUntil = model.originalBookingDateUntil,
      bookingDateFrom = model.bookingDateFrom,
      bookingDateUntil = model.bookingDateUntil,
      sellingTotalAmount = model.sellingTotalAmount,
      supplierTotalAmount = model.supplierTotalAmount,
      cancellationPolicyCode = model.cancellationPolicyCode,
      cancellationPolicy = model.cancellationPolicy,
      recStatus = model.recStatus,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = model.recCreatedBy,
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy,
      fullyAuthDate = model.fullyAuthDate,
      fullyChargeDate = model.fullyChargeDate,
      cancellationInfo = model.cancellationInfo,
      amendmentId = model.amendmentId,
      sellingCurrencyOpt = model.sellingCurrencyOpt,
      supplierCurrencyOpt = model.supplierCurrencyOpt,
      paymentId = model.paymentId,
      recCreatedByStr = model.recCreatedByStr,
      recModifyByStr = model.recModifyByStr,
      loyaltyInfo = model.loyaltyInfo
    )
  }

  private def toBapiPropertyBookingAcknowledgement(
      model: EbePropertyBookingAcknowledgement
  ): PropertyBookingAcknowledgement = {
    PropertyBookingAcknowledgement(
      bookingId = model.bookingId,
      ackTypeId = model.ackTypeId,
      isAcknowledged = model.isAcknowledged,
      checkInDate = model.checkInDate,
      checkOutDate = model.checkOutDate,
      guestName = model.guestName,
      guestNationalityId = model.guestNationalityId,
      noOfRoom = model.noOfRoom,
      noOfExtrabed = model.noOfExtrabed,
      occupancy = model.occupancy,
      noOfAdult = model.noOfAdult,
      noOfChild = model.noOfChild,
      hotelId = model.hotelId,
      roomTypeId = model.roomTypeId,
      roomTypeName = model.roomTypeName,
      cancellationPolicyCode = model.cancellationPolicyCode,
      paymentModel = model.paymentModel,
      breakfastIncluded = model.breakfastIncluded,
      ycsPromotionId = model.ycsPromotionId,
      specialRequest = model.specialRequest,
      breakdownId = model.breakdownId,
      recStatus = model.recStatus,
      rateChannelId = model.rateChannelId,
      bookingRateCategoryId = model.bookingRateCategoryId,
      rateModelType = model.rateModelType,
      isAdvanceGuarantee = model.isAdvanceGuarantee,
      ycsPromotionText = model.ycsPromotionText
    )
  }
  private def toBapiPropertyBookingResell(
      model: EbePropertyBookingResell
  ): PropertyBookingResell = {
    PropertyBookingResell(
      model.bookingRelationshipId,
      model.bookingId,
      model.resellBookingId,
      model.resellStatusId,
      model.resellTypeId,
      model.recStatus,
      model.recCreatedWhen,
      model.recCreatedBy,
      model.recModifyWhen,
      model.recModifyBy
    )
  }

  private def toBapiPropertyBookingConsumerFintech(
      model: EbePropertyBookingConsumerFintech
  ): PropertyBookingConsumerFintech = PropertyBookingConsumerFintech(
    bookingConsumerFintechId = model.bookingConsumerFintechId,
    bookingId = model.bookingId,
    consumerFintechDetails = model.consumerFintechDetails,
    recStatus = model.recStatus,
    recCreatedWhen = model.recCreatedWhen,
    recCreatedBy = model.recCreatedBy
  )

  private def toBapiPropertyBookingBookingHoldingPartnerName(
      model: EbePropertyBookingBookingHoldingPartnerName
  ): PropertyBookingBookingHoldingPartnerName = PropertyBookingBookingHoldingPartnerName(
    bookingId = model.bookingId,
    level = model.level,
    partnerId = model.partnerId,
    name = model.name
  )

  private def toBapiBookingCampaign(
      model: EbePropertyBookingCampaign
  ): PropertyBookingCampaign = {
    PropertyBookingCampaign(
      bookingId = model.bookingId,
      bookingRefId = model.bookingRefId,
      campaignData = model.campaignData,
      recCreatedWhen = model.recCreatedWhen,
      recCreatedBy = model.recCreatedBy,
      recModifyWhen = model.recModifyWhen,
      recModifyBy = model.recModifyBy,
      campaignId = model.campaignId,
      bookingRefIdStr = model.bookingRefIdStr
    )
  }

  private def toBapiPropertyBookingReplacementResult(
      model: ProtobufReplacementResult
  ): ReplacementResult = ReplacementResult(
    bookingId = model.bookingId,
    status = model.status.value,
    errorCode = model.errorCode,
    errorMessage = model.errorMessage,
    subErrorCode = model.subErrorCode,
    dateOfStay = model.dateOfStay,
    roomIndex = model.roomIndex
  )
}
