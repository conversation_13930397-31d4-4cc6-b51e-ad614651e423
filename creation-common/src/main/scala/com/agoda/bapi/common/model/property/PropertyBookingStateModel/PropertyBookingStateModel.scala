package com.agoda.bapi.common.model.property

import org.joda.time.DateTime

import java.util.UUID

package PropertyBookingStateModel {
  import com.agoda.bapi.common.model.CommonBookingEventsInfo
  import com.agoda.bapi.common.model.base._
  import com.agoda.bapi.common.model.consumerFintech.PropertyBookingConsumerFintech
  import com.fasterxml.jackson.databind.annotation.JsonDeserialize

  final case class PropertyModelInternal(
      itinerary: PropertyItinerary,
      bookingList: Seq[PropertyBookingModel]
  )

  final case class PropertyBookingModel(
      additionalData: AdditionalBookingData,
      booking: PropertyBooking,
      bookingAgent: PropertyBookingAgent,
      bookingSummary: PropertyBookingSummary,
      bookingAttributionV2: Seq[PropertyBookingAttributionV2] = Nil,
      bookingHotel: PropertyBookingHotel,
      bookingHotelRoom: Seq[PropertyBookingHotelRoom] = Nil,
      bookingHotelRoomChildren: Seq[PropertyBookingHotelRoomChildren] = Nil,
      bookingSetting: Option[PropertyBookingSetting] = None,
      bookingToken: Option[PropertyBookingToken] = None,
      fraudInfo: Option[PropertyBookingFraudInfo] = None,
      financialBreakdown: Seq[PropertyFinancialBreakdown] = Nil,
      bookingSucc: Seq[PropertyBookingSucc] = Nil,
      charges: Seq[PropertyBookingCharges] = Nil,
      sellInfo: Option[PropertyBookingSellInfo] = None,
      sellInfoHistory: Seq[PropertyBookingSellInfoHistory] = Nil,
      partnerLoyaltyPoint: Option[PropertyBookingPartnerLoyaltyPoint] = None,
      rateCategory: Option[PropertyBookingRateCategory] = None,
      rateCategoryBenefit: Seq[PropertyBookingRateCategoryBenefit] = Nil,
      paymentsAdvanceInfo: Seq[PropertyBookingPaymentsAdvanceInfo] = Nil,
      payment: Seq[PropertyBookingPayment] = Nil,
      provisioning: Seq[PropertyBookingProvisioning] = Nil,
      earningGiftCard: Option[PropertyBookingEarningGiftCard] = None,
      earningGiftCardInfo: Option[PropertyBookingEarningGiftCardInfo] = None,
      schedule: Option[PropertyBookingSchedule] = None,
      delaySettlementSchedule: Option[PropertyDelaySettlementSchedule] = None,
      propertyBookingPax: Seq[PropertyBookingPax] = Nil,
      fraudCcBlacklist: Option[PropertyFraudCcBlacklist] = None,
      fraudCookieBlacklist: Option[PropertyFraudCookieBlacklist] = None,
      productKey: Option[String] = None,
      bookingSupplierData: Option[PropertyBookingSupplierData] = None,
      bookingChildPromotions: Seq[PropertyBookingChildPromotions] = Nil,
      rewardEarning: Option[PropertyRewardEarning] = None,
      bookingAmendments: Seq[PropertyBookingAmendment] = Nil,
      propertyBookingAcknowledgement: Option[PropertyBookingAcknowledgement] = None,
      bookingResell: Option[PropertyBookingResell] = None,
      bookingCampaign: Option[PropertyBookingCampaign] = None,
      bookingDiscountInformations: Seq[PropertyBookingDiscountInformation] = Nil,
      bookingConsumerFintech: Option[PropertyBookingConsumerFintech] = None,
      essInfos: Seq[BaseBookingEssInfoInternal] = Nil,
      baseBooking: Option[BaseBookingModelInternal] = None
  )

  final case class PropertyBookingDiscountInformation(
      bookingId: Int,
      discountType: Int,
      discountId: Int,
      discountName: String,
      discountRateType: Int,
      @deprecated
      discountRate: Int = 0,
      @deprecated
      appliedDate: String,
      @deprecated
      recStatus: Int = 0,
      recCreatedWhen: DateTime,
      recCreatedBy: UUID,
      recModifyWhen: Option[DateTime] = None,
      recModifyBy: Option[UUID] = None,
      discountRateDouble: Double,
      isActive: Boolean,
      discountAppliedDate: Option[DateTime] = None
  )

  final case class PropertyBookingSupplierData(
      bookingId: Int,
      supplierSiteId: Option[String],
      supplierSubSiteId: Option[String],
      supplierTransactionId: Option[String],
      additionalInfo: Option[String],
      freeItem: Option[String],
      reservationInfo: Option[String],
      inventoryData: Option[String],
      childRateSettings: Option[String],
      recStatus: Int,
      recCreatedWhen: DateTime,
      recCreatedBy: UUID,
      recModifyWhen: Option[DateTime] = None,
      recModifyBy: Option[UUID] = None,
      supplierResponseInfo: Option[String],
      paymentChannel: Option[Int]
  )

  final case class AdditionalBookingData(
      destinationCityId: Long,
      destinationCountryId: Long,
      isSendSmsForBooking: Option[Boolean] = None,
      isGlobalPartnerServiceBranding: Option[Boolean] = None,
      localizedRatePlanName: Option[String] = None,
      localizedBenefits: Option[Seq[String]] = None,
      isShowCustomerInfoInVoucher: Option[Boolean] = None,
      emailDisplaySetting: Option[Int] = None,
      supplierDisplayName: Option[String] = None,
      supplierContact: Option[Contact] = None,
      dmcType: Option[Int] = None,
      hotelPrimaryContact: Option[Contact] = None,
      hotelYcsContact: Option[Contact] = None,
      isMessagingAllowedWithBooking: Option[Boolean] = None,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      ackRequestId: Option[Long] = None,
      hotelDisplayName: Option[String] = None,
      hotelDefaultName: Option[String] = None,
      hotelLocalName: Option[String] = None,
      hotelLocale: Option[String] = None,
      // Moved from paymentInfo section because this is property specific
      destinationCurrency: Option[String] = None,
      destinationExchangeRate: Option[BigDecimal] = None,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      rateQuoteId: Option[Long] = None,
      paymentOption: Option[Int] = None,
      hotelPaymentMethodId: Option[Int] = None,
      hotelPaymentConditionId: Option[Int] = None,
      supplierData: Option[PropertyBookingSupplier] = None,
      occupancy: Option[Seq[PropertyBookingOccupancy]] = None,
      childRateSettings: Option[Seq[PropertyBookingChildRateSetting]] = None,
      unmodifiedSpecialRequest: Option[SpecialRequest] = None,
      isCrossSellAvailable: Option[Boolean] = None,
      selfServiceUrl: Option[String] = None,
      isAddDelay: Option[Boolean] = None,
      supplierSessionId: Option[String] = None,
      bookingHoldingPartnerNames: Seq[PropertyBookingBookingHoldingPartnerName] = Nil,
      replacementResults: Seq[ReplacementResult] = Nil,
      ebeLiteStateId: Option[Int] = None
  )
  final case class SpecialRequest(
      additionalNotes: Option[String] = None,
      arrivalTime: Option[String] = None,
      bookerAnswer: Option[String] = None
  )

  final case class PropertyBookingChildRateSetting(
      childRateTypeId: String,
      childRatePricingTypeId: String,
      value: BigDecimal,
      isCountAsRoomOcc: Boolean
  )

  final case class Contact(
      phone: Option[String] = None,
      email: Option[String] = None,
      sms: Option[String] = None
  )

  final case class PropertyItinerary(
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      itineraryId: Option[Long] = None,
      itineraryStatusId: Option[Int] = None,
      itineraryTypeId: Option[Int] = None,
      memberID: Option[Int] = None,
      itineraryName: Option[String] = None,   // max length(50)
      itineraryDate: Option[DateTime] = None, // smalldatetime in sql
      isAutoGenerated: Option[Boolean] = None,
      tripStartDate: Option[DateTime] = None, // smalldatetime in sql
      tripEndDate: Option[DateTime] = None,   // smalldatetime in sql
      isMultipleBookings: Option[Boolean] = None,
      isProcessed: Option[Boolean] = None,
      isFraudVerifySent: Option[Boolean] = None,
      recStatus: Int,
      recCreatedWhen: DateTime, // smalldatetime in sql
      recCreatedBy: UUID,
      recModifyWhen: Option[DateTime] = None, // smalldatetime in sql
      recModifyBy: Option[UUID] = None
  )

  final case class PropertyBooking(
      bookingId: Int,
      itineraryId: Option[Int] = None,
      dataCenter: Option[String] = None, // max length(3),
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      preBookingIdOld: Option[Long] = None,
      bookingDate: Option[DateTime] = None,      // smalldatetime in sql
      bookingDateFrom: Option[DateTime] = None,  // smalldatetime in sql
      bookingDateUntil: Option[DateTime] = None, // smalldatetime in sql
      arrivalTimeWindow: Option[Int] = None,
      availabilityType: Option[Int] = None,
      paymentModel: Option[Int] = None,
      ccReceived: Option[Int] = None,
      fraudScore: Option[Int] = None,
      fraudAction: Option[Int] = None,
      rewardsSpecialOfferId: Option[Int] = None,
      cancellationPolicyCode: Option[String] = None,         // max length(500)
      cancellationPolicy: Option[String] = None,             // max length(4000)
      cancellationFeeAmount: Option[BigDecimal] = None,      // decimal(18, 2),
      cancellationFeeLocalAmount: Option[BigDecimal] = None, // decimal(18, 2),
      cancellationFeeLocalCurrency: Option[String] = None,   // max length(3)
      dmcId: Option[Int] = None,
      dmcCode: Option[String] = None, // max length(20)
      dmcSpecificData: Option[String] = None,
      bookingExternalReference: Option[String] = None, // max length(150)
      isAutoProcessed: Option[Boolean] = None,
      workflowId: Option[Int] = None,
      workflowStateId: Option[Int] = None,
      workflowActionId: Option[Int] = None,
      workflowParameterId: Option[Int] = None,
      workflowParameterValue: Option[String] = None, // max length(1000)
      bookingTypeId: Option[Int] = None,
      workflowStateSince: Option[DateTime] = None,
      cidList: Option[String] = None,         // max length(100)
      sessionId: Option[String] = None,       // max length(100)
      serverName: Option[String] = None,      // max length(100)
      clientIpAddress: Option[String] = None, // max length(20)
      referralUrl: Option[String] = None,     // max length(500)
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      ssResultId: Option[Long] = None,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      ssId: Option[Long] = None,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      ssIdFb: Option[Long] = None,
      ssResultType: Option[Int] = None,
      ssProviderId: Option[Int] = None,
      languageId: Option[Int] = None,
      storefrontId: Option[Int] = None,
      isChargeBackRequested: Option[Boolean] = None,
      isSpecialOffer: Option[Boolean] = None,
      isAffiliateProcessed: Option[Boolean] = None,
      isRewardsProcessed: Option[Boolean] = None,
      isEmailWhitelist: Option[Boolean] = None,
      userTracking: Option[UUID] = None,
      agentName: Option[String] = None, // max length(25)
      agentBookingChannel: Option[Int] = None,
      agentClientIp: Option[String] = None, // max length(15),
      isFraudReview: Option[Boolean] = None,
      reviewBy: Option[UUID] = None,
      isLocked: Option[Boolean] = None,
      promotionCode: Option[String] = None, // max length(20),
      promotionText: Option[String] = None, // max length(256)
      discountType: Option[Int] = None,
      discountAmount: Option[BigDecimal] = None,  // decimal(18, 2),
      discountSavings: Option[BigDecimal] = None, // decimal(18, 2),
      ycsPromotionId: Option[Int] = None,
      ycsPromotionText: Option[String] = None,        // max length(1000)
      ycsForeignPromotionText: Option[String] = None, // max length(1000)
      included: Option[String] = None,                // max length(1000)
      excluded: Option[String] = None,                // max length(1000)
      isAgentAssist: Option[Boolean] = None,
      trackingCookieId: Option[String] = None, // max length(36)
      trackingCookieDate: Option[DateTime] = None,
      faxFormRequestStatus: Option[Int] = None,
      affiliateModel: Option[Int] = None,
      affiliatePaymentMethod: Option[Int] = None,
      fallbackCxlPolicyCode: Option[String] = None, // max length(25)
      pointMultiply: Option[Int] = None,
      trackingTag: Option[String] = None, // max length($1),
      isReminderSent: Option[Boolean] = None,
      rateChannel: Option[Int] = None,
      promotionCampaignId: Option[Int] = None,
      membershipContentText: Option[String] = None,  // max length($1)
      membershipContentLabel: Option[String] = None, // max length(256)
      agodaCancellationFeeId: Option[Int] = None,
      fraudCheckIp: Option[String] = None, // max length(45)
      rateModelType: Option[Int] = None,
      feCid: Option[Int] = None,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      preBookingId: Option[Long] = None,
      recStatus: Int,
      recCreatedWhen: DateTime, // smalldatetime in sql
      recCreatedBy: UUID,
      recModifyWhen: Option[DateTime] = None, // smalldatetime in sql
      recModifyBy: Option[UUID] = None,
      agodaManagedYcs: Option[Boolean] = None,
      childPromotions: Option[Seq[PropertyBookingChildPromotion]] = None,
      externalLoyaltyDisplayTierId: Option[Int] = None
  )

  final case class PropertyBookingAgent(
      bookingId: Int,                        // not null
      origin: String,                        // max length(2)     not null,
      osName: String,                        // max length($1)    not null,
      osVersion: String,                     // max length($1)    not null,
      browserName: String,                   // max length($1)    not null,
      browserLanguage: String,               // max length(5)     not null,
      browserVersion: String,                // max length($1)    not null,
      browserSubversion: String,             // max length($1)    not null,
      browserBuildNumber: String,            // max length($1)    not null,
      deviceBrand: String,                   // max length($1)    not null,
      deviceModel: String,                   // max length($1)    not null,
      isMobile: Boolean,                     // not null,
      isTouch: Boolean,                      // not null,
      deviceInfo: Option[String] = None,     // byte array max length(2000),
      additionalInfo: Option[String] = None, // byte array max length(4000)
      recStatus: Int,                        // not null,
      recCreatedWhen: DateTime,              // not null,
      recCreatedBy: UUID,                    // not null,
      recModifyWhen: Option[DateTime] = None,
      recModifyBy: Option[UUID] = None
  )

  final case class PropertyBookingSummary(
      bookingId: Int, // not null,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      multiProductId: Option[Long] = None,
      displayCurrency: String,                                 // max length(3),
      originalSellingAmount: Option[BigDecimal] = None,        // decimal(18, 2),
      originalSupplierAmount: Option[BigDecimal] = None,       // decimal(18, 2),
      originalLocalCurrency: Option[String] = None,            // max length(3),
      originalLocalSupplierAmount: Option[BigDecimal] = None,  // decimal(18, 2),
      originalPaymentCurrency: Option[String] = None,          // max length(3),
      originalLocalPaymentAmount: Option[BigDecimal] = None,   // decimal(18, 2),
      originalLocalCcPaymentAmount: Option[BigDecimal] = None, // decimal(18, 2),
      originalRedeemAmount: Option[BigDecimal] = None,         // decimal(18, 2),
      platformId: Option[Int] = None,
      chargeOptionId: Option[Int] = None,
      originalFullyChargeDate: Option[DateTime] = None,
      isUserLoggedIn: Option[Boolean] = None,
      isTravelAgency: Option[Boolean] = None,
      travelAgencyLoginChannel: Option[String] = None, // max length(20),
      originalFullyAuthDate: Option[DateTime] = None,
      taxSurchargeInfo: Option[String] = None, // byte array max length(2000),
      isNotCcRequired: Option[Boolean] = None,
      exchangeRateOption: Option[Int] = None,
      priceDisplaySetting: Option[String] = None, // max length(1000),
      displayAmount: Option[BigDecimal] = None,   // decimal(18, 2),
      accountingEntity: Option[String] = None,    // max length(300),
      bookingCreatedDate: Option[DateTime] = None,
      isConfirmedBooking: Option[Boolean] = None,
      whitelabelId: Option[Int] = None,
      recStatus: Int,           // not null,
      recCreatedWhen: DateTime, // not null,
      recCreatedBy: UUID,       // not null,
      recModifyWhen: Option[DateTime] = None,
      recModifyBy: Option[UUID] = None,
      bookingFormSessionId: Option[String] = None, // max length(2000)
      commonBookingEventsInfo: Option[CommonBookingEventsInfo] = None,
      stayType: Option[Int] = None,
      isTestBooking: Option[Boolean] = None,
      pricefreezeBookingId: Option[Int] = None,
      isFullyCharged: Option[Boolean] = None,
      isSmartFlex: Option[Boolean] = None,
      originalCancellationPolicyCode: Option[String] = None,
      allGuestSameNationality: Option[Boolean] = None,
      cancellationChargeType: Option[Int] = None,
      cancellationChargeItemId: Option[Int] = None,
      isSmartSaver: Option[Boolean] = None
  )

  final case class PropertyBookingAttributionV2(
      bookingId: Int, // not null,
      modelId: Int,   // not null,
      siteId: Int,    // not null,
      tag: String,    // max length(3000) not null,
      clickDate: Option[DateTime] = None,
      additionalData: Option[String] = None, // max length(3000)
      lastUpdateWhen: DateTime,              // not null,
      lastUpdateBy: UUID                     // not null,
  )

  /* todo:need clarify where we will create token */
  final case class PropertyBookingToken(
      bookingId: Int, // not null,
      token: String   // max length(128) not null
  )

  final case class PropertyBookingFraudInfo(
      bookingId: Int,                // not null,
      isAcknowledgedPolicy: Boolean, // not null,
      acknowledgedDate: DateTime,    // not null,
      isActive: Boolean,             // not null,
      createdWhen: DateTime,         // not null,
      lastUpdatedWhen: Option[DateTime] = None
  )

  final case class PropertyBookingHotel(
      referenceId: Long,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      bookingHotelId: Option[Long] = None,
      bookingId: Int,                           // not null,
      hotelId: Int,                             // not null,
      checkInDate: DateTime,                    // smalldatetime not null,
      checkOutDate: DateTime,                   // smalldatetime not null,
      noOfRooms: Int,                           // not null,
      noOfAdults: Int,                          // not null,
      noOfChildren: Int,                        // not null,
      flightNumber: Option[String] = None,      // max length(20)
      arrivalDatetime: Option[DateTime] = None, // smalldatetime in sql
      airportTransfer: Option[Boolean] = None,
      expectedCheckInTime: Option[DateTime] = None, // smalldatetime in sql
      specialRequestNonsmoking: Option[Boolean] = None,
      specialRequestHighFloor: Option[Int] = None,
      otherSpecialNeeds: Option[String] = None, // max length(4000)
      noExtraBedRateChoice: Option[Int] = None,
      hotelRemark: Option[String] = None, // max length(1024)
      occupancyFreeSearch: Option[Boolean] = None,
      @deprecated
      isAgencyPrepay: Option[Boolean] = None,
      isNha: Option[Boolean] = None,
      isAgodaReception: Option[Boolean] = None,
      greetingMessage: Option[String] = None, // byte array max length(4000)
      isAgodaAgency: Option[Boolean] = None,
      recStatus: Int,
      recCreatedWhen: DateTime, // smalldatetime in sql
      recCreatedBy: UUID,
      recModifyWhen: Option[DateTime] = None, // smalldatetime in sql
      recModifyBy: Option[UUID] = None,
      otherQuestionToBooker: Option[String] = None
  )

  final case class PropertyBookingHotelRoom(
      referenceId: Long,
      bookingHotelReferenceId: Long,
      bookingId: Int, // not null,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      bookingHotelId: Option[Long] = None, // not null,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      bookingRoomId: Option[Long] = None,
      roomTypeId: Option[Int] = None,
      roomTypeName: Option[String] = None, // max length(200)
      roomNo: Option[Int] = None,
      occupancy: Option[Int] = None,
      @deprecated
      noOfBeds: Option[Int] = None,
      noOfExtrabeds: Option[Int] = None,
      breakfastIncluded: Option[Boolean] = None,
      breakfastInfo: Option[String] = None, // max length(200)
      @deprecated
      roomRemark: Option[String] = None, // max length(1024)
      noOfMales: Option[Int] = None,
      noOfFemales: Option[Int] = None,
      recStatus: Int,
      recCreatedWhen: DateTime, // smalldatetime in sql
      recCreatedBy: UUID,
      recModifyWhen: Option[DateTime] = None, // smalldatetime in sql
      recModifyBy: Option[UUID] = None,
      dynamicMappingTypeId: Option[Int] = None,
      masterRoomTypeId: Option[Int] = None
  )

  final case class PropertyBookingHotelRoomChildren(
      referenceId: Long,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      bookingRoomId: Option[Long],
      childRateTypeId: Int,
      number: Int,
      recStatus: Int,
      recCreatedWhen: DateTime, // smalldatetime in sql
      recCreatedBy: UUID,
      recModifiedWhen: Option[DateTime] = None, // smalldatetime in sql
      recModifiedBy: Option[UUID] = None
  )

  final case class PropertyFinancialBreakdown(
      referenceId: Long,
      bookingRoomReferenceId: Long,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      breakdownId: Option[Long] = None, // not null,
      bookingId: Int,                   // not null,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      bookingRoomId: Option[Long] = None,
      dateOfStay: Option[DateTime] = None,
      itemId: Int, // not null,
      typeId: Option[Int] = None,
      surchargeId: Option[Int] = None,
      taxFeeId: Option[Int] = None,
      quantity: Option[Int] = None,
      localCurrency: Option[String] = None,                   // max length(3) not null,
      localAmount: Option[BigDecimal] = None,                 // decimal(18, 2) not null,
      exchangeRate: Option[BigDecimal] = None,                // decimal(18, 4) not null,
      usdAmount: Option[BigDecimal] = None,                   // decimal(18, 2) not null,
      supplierPaymentCurrency: Option[String] = None,         // max length(3),
      supplierPaymentAmount: Option[BigDecimal] = None,       // decimal(18, 2),
      supplierPaymentExchangeRate: Option[BigDecimal] = None, // decimal(18, 4),
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      refBreakdownId: Option[Long] = None,
      isTransfer: Boolean, // not null,
      hotelPaymentMethodId: Option[Int] = None,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      upcId: Option[Long] = None,
      taxPrototypeId: Option[Int] = None,
      subtypeId: Option[Int] = None,
      recStatus: Int,
      recCreatedWhen: DateTime, // smalldatetime in sql
      recCreatedBy: UUID,
      recModifyWhen: Option[DateTime] = None, // smalldatetime in sql
      recModifyBy: Option[UUID] = None,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      actionId: Option[Long] = None
  )

  final case class PropertyBookingCharges(
      referenceId: Long,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      bookingRoomReferenceId: Option[Long] = None,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      chargeId: Option[Long] = None,
      bookingId: Long, // not null,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      bookingRoomId: Option[Long] = None,
      chargeDate: Option[DateTime] = None, // smalldatetime in sql
      chargeTypeId: Option[Int] = None,
      chargeName: Option[String] = None,  // max length(50)
      description: Option[String] = None, // max length(100)
      quantity: Option[Int] = None,
      unitOfMeasure: Option[String] = None,                   // max lenght(3),
      unitSellingPrice: Option[BigDecimal] = None,            // decimal(18, 2),
      unitSupplierPrice: Option[BigDecimal] = None,           // decimal(18, 2),
      sellingAmount: Option[BigDecimal] = None,               // decimal(18, 2),
      sellingTax: Option[BigDecimal] = None,                  // decimal(18, 2),
      supplierAmount: Option[BigDecimal] = None,              // decimal(18, 2),
      supplierTax: Option[BigDecimal] = None,                 // decimal(18, 2),
      localSupplierAmount: Option[BigDecimal] = None,         // decimal(18, 2),
      localSupplierTax: Option[BigDecimal] = None,            // decimal(18, 2),
      localCurrency: Option[String] = None,                   // max length(3)
      exchangeRateLocalToSupplier: Option[BigDecimal] = None, // decimal(18, 4),
      exchangeRateLastUpdate: Option[DateTime] = None,        // smalldatetime in sql
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      refChargeId: Option[Long] = None,                     // default 0
      isRecorded: Option[Int] = None,                       // default 0
      localSellingAmount: Option[BigDecimal] = None,        // decimal(18, 2),
      b2bNetAmount: Option[BigDecimal] = None,              // decimal(18, 2),
      b2bLocalNetAmount: Option[BigDecimal] = None,         // decimal(18, 2),
      b2bLocalCurrency: Option[String] = None,              // max length(3),
      b2bExchangeRateLocalToUSD: Option[BigDecimal] = None, // decimal(18, 4),
      hasBreakdown: Option[Boolean] = None,                 // default 'false'
      isBreakdownProcessed: Option[Boolean] = None,         // default 'false'
      recStatus: Int,
      recCreatedWhen: DateTime, // smalldatetime in sql default getdate()
      recCreatedBy: UUID,
      recModifyWhen: Option[DateTime] = None, // smalldatetime in sql default getdate()
      recModifyBy: Option[UUID] = None
  )

  final case class PropertyBookingSetting(
      settingId: Option[Int] = None,
      bookingId: Int,          // not null,
      settingTypeId: Int,      // not null,
      settingJsonData: String, // max length(2000)   not null,
      createdWhen: DateTime,   // not null,
      createdBy: UUID          // not null
  )

  final case class PropertyBookingSellInfo(
      bookingId: Int, // not null,
      pricingTemplateId: Option[Int] = None,
      downLiftAmountUsd: Option[BigDecimal] = None, // decimal(18, 4),
      sellTagId: Option[Int] = None,
      searchId: Option[String] = None, // max length(128),
      isAdvanceGuarantee: Option[Boolean] = None,
      offerId: Option[String] = None, // max length(100)
      lastUpdatedWhen: DateTime,      // not null,
      lastUpdatedBy: UUID,            // not null,
      pricingRequestId: Option[String] = None,
      firedrillContractId: Option[Int] = None,
      firedrillContractTypeId: Option[Int] = None
  )

  final case class PropertyBookingSellInfoHistory(
      historyActionId: Int,
      historyActionDate: DateTime,
      historyActionBy: UUID,
      bookingId: Long,
      pricingTemplateId: Option[Int] = None,
      downliftAmountUsd: Option[BigDecimal] = None,
      sellTagId: Option[Int] = None,
      lastUpdatedWhen: DateTime,
      searchId: Option[String] = None,
      isAdvanceGuarantee: Option[Boolean] = None,
      lastUpdatedBy: UUID,
      bookingSellInfoHistoryId: Option[Int] = None,
      firedrillContractId: Option[Int] = None,
      firedrillContractTypeId: Option[Int] = None
  )

  final case class PropertyBookingPartnerLoyaltyPoint(
      bookingId: Int,                      // not null,
      programId: Int,                      // not null,
      membershipId: Option[String] = None, // max length($1),
      loyaltyPoint: Int,                   // not null,
      pointCostAmountUsd: BigDecimal,      // decimal(18, 2) not null,
      pointCostAmountLocal: BigDecimal,    // decimal(18, 2) not null,
      gstCostAmountUsd: BigDecimal,        // decimal(18, 2) not null,
      gstCostAmountLocal: BigDecimal,      // decimal(18, 2) not null,
      currency: String,                    // max length(3) not null,
      lastUpdatedWhen: DateTime            // not null
  )

  final case class PropertyBookingRateCategory(
      referenceId: Long,
      bookingRateCategoryId: Option[Int] = None,
      bookingId: Int,                        // not null,
      rateCategoryId: Int,                   // not null,
      rateCategoryCode: String,              // max length(50) not null,
      chargeTypeCode: Option[String] = None, // max length(4),
      rateCategoryValue: BigDecimal,         // decimal(19, 2) not null,
      isActive: Boolean,                     // not null,
      isRoh: Option[Boolean] = None,
      inventoryTypeId: Option[Int] = None,
      stayPackageType: Option[Int] = None,
      rateCategoryContentToken: Option[String] = None,
      lastUpdatedWhen: DateTime, // not null,
      lastUpdatedBy: UUID        // not null,
  )

  final case class PropertyBookingRateCategoryBenefit(
      referenceId: Long,
      rateCategoryReferenceId: Long,
      rateCategoryBenefitId: Option[Int] = None,
      bookingRateCategoryId: Option[Int] = None, // not null,
      benefitId: Int,                            // not null,
      benefitValue: Option[BigDecimal] = None,   // decimal(18, 2),
      benefitDescription: Option[String] = None, // max length(200)
      benefitRemark: Option[String] = None,      // max length(200)
      benefitParameters: Option[String] = None,
      targetType: Option[Int] = None
  )

  final case class PropertyBookingPaymentsAdvanceInfo(
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      paymentId: Option[Long] = None,
      token: String, // nvarchar(4000) not null
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      referencePaymentId: Option[Long] = None
  )

  /**
    * generated by pre-auth, settlement agent
    */
  final case class PropertyBookingPayment(
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      paymentId: Option[Long] = None,
      bookingId: Option[Int] = None,
      creditCardId: Option[Int] = None,
      transactionDate: Option[DateTime] = None, // smalldatetime in sql
      transactionType: Option[Int] = None,
      refNo: Option[String] = None, // max length(50)
      refType: Option[Int] = None,
      paymentTypeId: Option[Int] = None,
      gatewayId: Option[Int] = None,
      transactionId: Option[String] = None,       // max length(50)
      gatewayReferenceIn: Option[String] = None,  // max length(2000)
      gatewayReferenceOut: Option[String] = None, // max length(2000)
      redeemedPoints: Option[Int] = None,
      paymentCurrency: Option[String] = None,   // max length(3)
      paymentAmount: Option[BigDecimal] = None, // decimal(18, 2),
      refundAmount: Option[BigDecimal] = None,  // decimal(18, 2),
      bookingProviderTypeId: Option[Int] = None,
      bookingDaysUntilArrival: Option[Int] = None,
      gatewayTestMode: Option[Boolean] = None,
      paymentRequestStatus: Option[Int] = None,
      localAmount: Option[BigDecimal] = None,                   // decimal(18, 2),
      localCurrency: Option[String] = None,                     // max length(3)
      exchangeRateLocalToPayment: Option[BigDecimal] = None,    // decimal(18, 4),
      creditCardCurrency: Option[String] = None,                // max length(3)
      exchangeRateLocalToCcCurrency: Option[BigDecimal] = None, // decimal(18, 4),
      refBookingId: Option[Int] = None,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      refPaymentId: Option[Long] = None,
      upliftAmount: Option[BigDecimal] = None,            // decimal(18, 2),
      siteExchangeRate: Option[BigDecimal] = None,        // decimal(18, 4),
      upliftExchangeRate: Option[BigDecimal] = None,      // decimal(18, 4),
      destinationCurrency: Option[String] = None,         // max length(3)
      destinationExchangeRate: Option[BigDecimal] = None, // decimal(18, 4),
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      rateQuoteId: Option[Long] = None,
      fxmpTranId: Option[Int] = None,
      statusId: Option[Int] = None,
      statusMessage: Option[String] = None, // max length(1000)
      submitDate: Option[DateTime] = None,  // smalldatetime in sql
      IsRecorded: Option[Int] = None,
      isTransfer: Option[Boolean] = None,
      rowVersion: DateTime, // not null,
      paymentOption: Option[Int] = None,
      paymentToken: Option[String] = None,                // byte array max length(4000),
      custVisibleUpliftAmount: Option[BigDecimal] = None, // decimal(18, 2),
      processed_3dsOption: Option[Int] = None,
      planId: Option[Int] = None,
      installmentPlanId: Option[Int] = None,
      recStatus: Option[Int] = None,
      recCreatedWhen: Option[DateTime] = None, // smalldatetime in sql
      recCreatedBy: Option[UUID] = None,
      recModifyWhen: Option[DateTime] = None, // smalldatetime in sql
      recModifyBy: Option[UUID] = None,
      internalToken: Option[String] = None, // ebe_booking_payments_advance_info.token
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      referenceId: Option[Long] = None // internal key for a payment record
  )

  final case class PropertyBookingProvisioning(
      referenceId: Int,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      bookingProvisioningId: Option[Long] = None,
      bookingId: Int,
      dmcId: Option[Int] = None,
      methodId: Option[Int] = None,
      transmittalDate: Option[DateTime] = None,        // smalldatetime in sql
      replyDate: Option[DateTime] = None,              // smalldatetime in sql
      bookingExternalReference: Option[String] = None, // max length(150)
      dmcDueDate: Option[DateTime] = None,             // smalldatetime in sql
      localDmcCurrency: Option[String] = None,         // max length(3)
      originalNetRate: Option[BigDecimal] = None,      // decimal(18, 2),
      netRate: Option[BigDecimal] = None,              // decimal(18, 2),
      request_XML: Option[String] = None,
      response_XML: Option[String] = None,
      remarks: Option[String] = None,
      replyStatus: Option[Int] = None,
      purchaseDetails: Option[String] = None, // byte array max length(4000)
      recStatus: Int,
      recCreatedWhen: DateTime, // smalldatetime in sql
      recCreatedBy: UUID,
      recModifyWhen: Option[DateTime] = None, // smalldatetime in sql
      recModifyBy: Option[UUID] = None,
      token: Option[String] = None
  )

  final case class PropertyBookingEarningGiftCard(
      bookingId: Int, // not null
      dateOffset: Option[Int] = None,
      earnDate: DateTime,     // not null
      usdAmount: BigDecimal,  // decimal(19, 2) not null
      isProcessed: Boolean,   // not null
      giftCardGuid: UUID,     // not null
      isAutoMigrate: Boolean, // not null
      earnDatetime: Option[DateTime] = None,
      processDatetime: Option[DateTime] = None,
      processStatus: Option[Int] = None,
      rescheduleCount: Option[Int] = None,
      lastUpdatedWhen: DateTime, // not null
      lastUpdatedBy: UUID        // not null
  )

  final case class PropertyBookingEarningGiftCardInfo(
      giftCardGuid: UUID, // not null
      expiryDays: Int,    // not null
      expiryAlreadyExist: Option[Boolean] = None,
      expiryMatch: Option[Boolean] = None,
      ebeExpiry: Option[Int] = None,
      createdWhen: DateTime, // not null
      createdBy: UUID,       // not null
      lastUpdatedWhen: Option[DateTime] = None,
      lastUpdatedBy: Option[UUID] = None
  )

  final case class PropertyBookingSchedule(
      bookingId: Int,                         // int              not null
      fullyChargeDate: DateTime,              // datetime         not null,
      fraudCheckDate: DateTime,               // datetime         not null,
      isFraudChecked: Boolean,                // bit              not null,
      fullyAuthDate: Option[DateTime] = None, // datetime,
      GMTOffset: Option[Int] = None,          // smallint
      recStatus: Int,                         // smallint         not null,
      recCreatedWhen: DateTime,               // datetime         not null,
      recCreatedBy: UUID,                     // uniqueidentifier not null,
      recModifyWhen: Option[DateTime] = None, // datetime,
      recModifyBy: Option[UUID] = None        // uniqueidentifier,
  )

  final case class PropertyDelaySettlementSchedule(
      bookingId: Int,                         // int              not null
      delaySettlementDate: DateTime,          // datetime         not null,
      recStatus: Int,                         // smallint         not null,
      recCreatedWhen: DateTime,               // datetime         not null,
      recCreatedBy: UUID,                     // uniqueidentifier not null,
      recModifyWhen: Option[DateTime] = None, // datetime,
      recModifyBy: Option[UUID] = None        // uniqueidentifier,
  )

  /**
    * after get upc agent
    */
  final case class PropertyBookingSucc(
      bookingSuccId: Option[Int] = None,
      bookingId: Int,                 // not null
      succId: Long,                   // not null
      last4: String,                  // max length(4)  not null
      exchangeRate: BigDecimal,       // not null
      requestCreditLimit: BigDecimal, // not null
      actualCreditLimit: BigDecimal,  // not null
      currencyCode: String,           // max lenght(3) not null
      cardType: Int,                  // not null
      cardStatus: Int,                // not null
      isSubmitted: Boolean,           // not null
      recStatus: Option[Int] = None,
      recCreatedWhen: Option[DateTime] = None, // smalldatetime in sql
      recCreatedBy: Option[UUID] = None,
      recModifyWhen: Option[DateTime] = None, // smalldatetime in sql
      recModifyBy: Option[UUID] = None,
      requestCurrency: Option[String] = None, // max length(3)
      amount: Option[BigDecimal] = None,
      payoutUuid: Option[UUID] = None
  )

  /**
    * will be generate and saved by booking agent, so this one maybe removed
    */
  final case class PropertyBookingPax(
      referenceId: Long,
      bookingRoomReferenceId: Long,
      bookingPaxId: Option[Int] = None,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      bookingRoomId: Option[Long] = None,
      guestNo: Option[Int] = None,
      title: String,                     // max length(20) not null,
      firstName: Option[String] = None,  // max length(64)
      middleName: Option[String] = None, // max length(64)
      lastName: Option[String] = None,   // max length(64)
      suffix: Option[String] = None,     // max length(20)
      isExtraBed: Option[Boolean] = None,
      age: Option[Int] = None,
      nationalityId: Option[Int] = None,
      isPrimary: Option[Boolean] = None,
      recStatus: Int,
      recCreatedWhen: DateTime,               // smalldatetime in sql
      recCreatedBy: UUID,                     // smalldatetime in sql
      recModifyWhen: Option[DateTime] = None, // smalldatetime in sql
      recModifyBy: Option[UUID] = None
  )

  /**
    * after fraud check
    */
  final case class PropertyFraudCcBlacklist(
      fdCcBlacklistId: Option[Int] = None,
      ccHash: String, // max length(300) not null,
      ccNo: String,   // max length(30)  not null,
      recStatus: Option[Int] = None,
      recCreatedBy: Option[UUID] = None,
      recCreatedWhen: Option[DateTime] = None,
      recModifyBy: Option[UUID] = None,
      recModifyWhen: Option[DateTime] = None
  )

  /**
    * after fraud check
    */
  final case class PropertyFraudCookieBlacklist(
      fdCookieBlacklistId: Option[Int] = None,
      cookieId: String, // max length(36) not null,
      recStatus: Option[Int] = None,
      recCreatedBy: Option[UUID] = None,
      recCreatedWhen: Option[DateTime] = None,
      recModifyBy: Option[UUID] = None,
      recModifyWhen: Option[DateTime] = None
  )

  final case class PropertyBookingOccupancy(
      roomNo: Int,
      noOfAdultMales: Int,
      noOfAdultFemales: Int,
      childOccupancy: Option[Seq[ChildOccupancyInfo]] = None
  )

  final case class ChildOccupancyInfo(
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      typeId: Long,
      count: Int
  )

  final case class PropertyBookingSupplier(
      supplierSiteId: Option[String],
      supplierSubSiteId: Option[String],
      supplierTransactionId: Option[String],
      externalBookingId: Option[String],
      supplierAdditionalInfo: Option[String],
      supplierFreeItem: Option[String],
      supplierReservationInfo: Option[String],
      supplierChainId: Option[Int],
      authenticationKey: Option[String],
      paymentChannel: Option[Int]
  )

  @deprecated("Deprecated, use PropertyBookingChildPromotions instead")
  final case class PropertyBookingChildPromotion(
      campaignId: Int,
      promotionCode: String,
      discountAmount: Double,
      localDiscountAmount: Double
  )

  final case class PropertyBookingChildPromotions(
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      bookingChildPromotionId: Option[Long] = None,
      bookingId: Int,
      campaignId: Int,
      promotionCode: Option[String],
      amountUsd: BigDecimal,
      amount: BigDecimal,
      fundBy: Option[Int],
      isActive: Boolean,
      recModifiedBy: Option[UUID] = None,
      recModifiedWhen: Option[DateTime] = None
  )

  final case class PropertyRewardEarning(
      itineraryId: Int,
      bookingId: Int,
      rewardTypeId: Int,
      rewardJsonToken: String,
      recStatus: Int,
      recCreatedBy: UUID,
      recCreatedWhen: DateTime,
      recModifyBy: Option[UUID] = None,
      recModifyWhen: Option[DateTime] = None
  )

  final case class PropertyBookingAmendment(
      @deprecated
      itineraryId: Int = 0,
      bookingId: Int = 0,
      requestType: Option[Int] = None,
      requestStatus: Option[Int] = None,
      originalBookingDateFrom: Option[DateTime] = None,
      originalBookingDateUntil: Option[DateTime] = None,
      bookingDateFrom: Option[DateTime] = None,
      bookingDateUntil: Option[DateTime] = None,
      sellingTotalAmount: Option[BigDecimal] = None,
      @deprecated
      sellingCurrency: Option[BigDecimal] = None,
      supplierTotalAmount: Option[BigDecimal] = None,
      @deprecated
      supplierCurrency: Option[BigDecimal] = None,
      cancellationPolicyCode: Option[String] = None,
      cancellationPolicy: Option[String] = None,
      recStatus: Int = 0,
      recCreatedWhen: Option[DateTime] = None,
      @deprecated
      recCreatedBy: Option[UUID] = None,
      recModifyWhen: Option[DateTime] = None,
      recModifyBy: Option[UUID] = None,
      fullyAuthDate: Option[DateTime] = None,
      fullyChargeDate: Option[DateTime] = None,
      cancellationInfo: Option[String] = None,
      @deprecated
      paymentInfo: Option[Int] = None,
      amendmentId: Int = 0,
      sellingCurrencyOpt: Option[String] = None,
      supplierCurrencyOpt: Option[String] = None,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      paymentId: Option[Long] = None,
      recCreatedByStr: Option[String] = None,
      recModifyByStr: Option[String] = None,
      loyaltyInfo: Option[String] = None
  )

  final case class PropertyBookingAcknowledgement(
      bookingId: Int = 0,
      ackTypeId: Int = 0,
      isAcknowledged: Option[Boolean] = None,
      checkInDate: Option[DateTime] = None,
      checkOutDate: Option[DateTime] = None,
      guestName: Option[String] = None,
      guestNationalityId: Option[Int] = None,
      noOfRoom: Option[Int] = None,
      noOfExtrabed: Option[Int] = None,
      occupancy: Option[Int] = None,
      noOfAdult: Option[Int] = None,
      noOfChild: Option[Int] = None,
      hotelId: Option[Int] = None,
      roomTypeId: Option[Int] = None,
      roomTypeName: Option[String] = None,
      cancellationPolicyCode: Option[String] = None,
      paymentModel: Option[String] = None,
      breakfastIncluded: Option[Boolean] = None,
      ycsPromotionId: Option[Int] = None,
      specialRequest: Option[String] = None,
      @JsonDeserialize(contentAs = classOf[java.lang.Long])
      breakdownId: Option[Long] = None,
      recStatus: Int = 0,
      rateChannelId: Option[Int] = None,
      bookingRateCategoryId: Option[Int] = None,
      rateModelType: Option[String] = None,
      isAdvanceGuarantee: Option[Boolean] = None,
      ycsPromotionText: Option[String] = None
  )

  final case class PropertyBookingResell(
      bookingRelationshipId: Int,
      bookingId: Int,
      resellBookingId: Option[Int],
      resellStatusId: Int,
      resellTypeId: Int,
      recStatus: Int,
      recCreatedWhen: DateTime,
      recCreatedBy: UUID,
      recModifyWhen: Option[DateTime],
      recModifyBy: Option[UUID]
  )

  final case class PropertyBookingCampaign(
      bookingId: Int,
      @deprecated
      bookingRefId: Option[Int],
      campaignData: String,
      recCreatedWhen: DateTime,
      recCreatedBy: UUID,
      recModifyWhen: Option[DateTime],
      recModifyBy: Option[UUID],
      campaignId: Int,
      bookingRefIdStr: Option[String]
  )

  final case class PropertyBookingBookingHoldingPartnerName(
      bookingId: Int,
      level: Option[Int],
      partnerId: Option[Int],
      name: Option[String]
  )

  final case class ReplacementResult(
      bookingId: Int,
      status: Int,
      errorCode: String,
      errorMessage: String,
      subErrorCode: String,
      dateOfStay: DateTime,
      roomIndex: Int
  )
}
