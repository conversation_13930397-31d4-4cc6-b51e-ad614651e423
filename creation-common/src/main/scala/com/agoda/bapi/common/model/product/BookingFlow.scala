package com.agoda.bapi.common.model.product

import com.agoda.bapi.common.util.JacksonSerializer.EnumAsStringDeserializer
import com.agoda.mpb.common.models.state.ProductType
import com.agoda.mpb.common.serialization.Serialization.EnumAsOptStringDeserializer
import com.fasterxml.jackson.core.`type`.TypeReference

object BookingFlow extends Enumeration {
  type BookingFlow = Value

  val SingleProperty: BookingFlow             = Value(1)
  val SingleFlight: BookingFlow               = Value(2)
  val MixAndSave: BookingFlow                 = Value(3)
  val Package: BookingFlow                    = Value(4)
  val SingleVehicle: BookingFlow              = Value(5)
  val FlightWithProtection: BookingFlow       = Value(6)
  val Hackerfare: BookingFlow                 = Value(7)
  val MultiHotel: BookingFlow                 = Value(8)
  val UmrahPackage: BookingFlow               = Value(9)
  val MultiFlightsWithProtection: BookingFlow = Value(10)
  val Cart: BookingFlow                       = Value(11)
  val SingleActivity: BookingFlow             = Value(ProductType.Activity.id)
  val CegFastTrack: BookingFlow               = Value(ProductType.CEGFastTrack.id)
  val Unknown: BookingFlow                    = Value(99)

  def isOnlyFlightRelatedBookingFlow(bookingFlow: BookingFlow): Boolean = {
    val onlyFlightRelatedBookingFlow = Seq(SingleFlight, FlightWithProtection, Hackerfare)
    onlyFlightRelatedBookingFlow.contains(bookingFlow)
  }
  def isFlightPartialSuccessRelatedBookingFlow(bookingFlow: BookingFlow): Boolean = {
    val flightPartialSuccessRelatedBookingFlow =
      Set(BookingFlow.Hackerfare, BookingFlow.FlightWithProtection, BookingFlow.MultiFlightsWithProtection)
    flightPartialSuccessRelatedBookingFlow.contains(bookingFlow)
  }
}

class BookingFlowType extends TypeReference[BookingFlow.type]

class BookingFlowDeserializer extends EnumAsStringDeserializer(BookingFlow)

class BookingFlowOptionDeserializer extends EnumAsOptStringDeserializer(BookingFlow)
