package com.agoda.bapi.common.util.converters.product.to.protobuf

import com.agoda.bapi.common.model.base._
import com.agoda.bapi.common.model.creation.common.JsonAccountingEntity
import com.agoda.bapi.common.model.property.PropertyBookingStateModel._
import com.agoda.bapi.common.util.converters.CommonProductConverters
import com.agoda.mpbe.state.common.enums.Gateway.Gateway
import com.agoda.mpbe.state.common.enums.PaymentStatus.PaymentStatus
import com.agoda.mpbe.state.common.enums.PaymentType.PaymentType
import com.agoda.mpbe.state.common.enums.ReplacementResultStatus.ReplacementResultStatus
import com.agoda.mpbe.state.common.enums.TransactionType.TransactionType
import com.agoda.mpbe.state.product.property.{ReplacementResult => ProtobufReplacementResult, _}
import io.scalaland.chimney.dsl._

/**
  * Methods below are converters for BAPI to Protobuf internal models
  */
protected[converters] trait ProtoPropertyConverters {
  import com.agoda.bapi.common.util.converters.TypeConverters._

  def toProtoPropertyProductModel(model: PropertyModelInternal): PropertyProductModel = {
    val booking = CommonProductConverters.toSingleElement(
      "PropertyProductModel.bookingList",
      model.bookingList,
      identity[PropertyBookingModel] _
    )

    PropertyProductModel(
      additionalData = toAdditionalBookingData(booking.additionalData),
      booking = toPropertyBooking(booking.booking),
      bookingAgent = toPropertyBookingAgent(booking.bookingAgent),
      bookingSummary = toPropertyBookingSummary(booking.bookingSummary),
      bookingAttributionsV2 = booking.bookingAttributionV2.map(_.transformInto[EbePropertyBookingAttributionV2]),
      bookingHotel = booking.bookingHotel.transformInto[EbePropertyBookingHotel],
      bookingHotelRooms = booking.bookingHotelRoom.map(_.transformInto[EbePropertyBookingHotelRoom]),
      bookingHotelRoomChildren =
        booking.bookingHotelRoomChildren.map(_.transformInto[EbePropertyBookingHotelRoomChildren]),
      bookingSetting = booking.bookingSetting.map(_.transformInto[EbePropertyBookingSetting]),
      bookingToken = booking.bookingToken.map(_.transformInto[EbePropertyBookingToken]),
      fraudInfo = booking.fraudInfo.map(_.transformInto[EbePropertyBookingFraudInfo]),
      financialBreakdowns = booking.financialBreakdown.map(_.transformInto[EbePropertyFinancialBreakdown]),
      bookingSingleUsedCreditCards = booking.bookingSucc.map(toPropertyBookingSucc),
      charges = booking.charges.map(toPropertyBookingCharges),
      sellInfo = booking.sellInfo.map(_.transformInto[EbePropertyBookingSellInfo]),
      sellInfoHistories = booking.sellInfoHistory.map(_.transformInto[EbePropertyBookingSellInfoHistory]),
      partnerLoyaltyPoint = booking.partnerLoyaltyPoint.map(_.transformInto[EbePropertyBookingPartnerLoyaltyPoint]),
      rateCategory = booking.rateCategory.map(_.transformInto[EbePropertyBookingRateCategory]),
      rateCategoryBenefits = booking.rateCategoryBenefit.map(toPropertyBookingRateCategoryBenefit),
      paymentsAdvanceInfos = booking.paymentsAdvanceInfo.map(toPropertyBookingPaymentsAdvanceInfo),
      payments = booking.payment.map(toPropertyBookingPayment),
      provisionings = booking.provisioning.map(toPropertyBookingProvisioning),
      earningGiftCard = booking.earningGiftCard.map(_.transformInto[EbePropertyBookingEarningGiftCard]),
      earningGiftCardInfo = booking.earningGiftCardInfo.map(_.transformInto[EbePropertyBookingEarningGiftCardInfo]),
      schedule = booking.schedule.map(_.transformInto[EbePropertyBookingSchedule]),
      bookingPassengers = booking.propertyBookingPax.map(_.transformInto[EbePropertyBookingPax]),
      fraudCcBlacklist = booking.fraudCcBlacklist.map(toPropertyFraudCcBlacklist),
      fraudCookieBlacklist = booking.fraudCookieBlacklist.map(toPropertyFraudCookieBlacklist),
      itinerary = model.itinerary.transformInto[EbePropertyItinerary],
      productKey = booking.productKey,
      delaySettlementSchedule =
        booking.delaySettlementSchedule.map(_.transformInto[EbePropertyDelaySettlementSchedule]),
      bookingSupplierData = booking.bookingSupplierData.map(_.transformInto[EbePropertyBookingSupplierData]),
      childPromotions = booking.bookingChildPromotions.map(_.transformInto[EbePropertyBookingChildPromotions]),
      rewardEarning = booking.rewardEarning.map(toPropertyRewardEarning),
      bookingAmendments = booking.bookingAmendments.map(_.transformInto[EbePropertyBookingAmendment]),
      propertyBookingAcknowledgement =
        booking.propertyBookingAcknowledgement.map(_.transformInto[EbePropertyBookingAcknowledgement]),
      bookingResell = booking.bookingResell.map(_.transformInto[EbePropertyBookingResell]),
      bookingCampaign = booking.bookingCampaign.map(_.transformInto[EbePropertyBookingCampaign]),
      bookingDiscountInformations =
        booking.bookingDiscountInformations.map(_.transformInto[EbePropertyBookingDiscountInformation]),
      bookingConsumerFintech = booking.bookingConsumerFintech.map(_.transformInto[EbePropertyBookingConsumerFintech]),
      essInfos = booking.essInfos.map(BaseBookingEssInfoInternal.mapToBaseBookingEssInfoProto),
      baseBooking = booking.baseBooking.map(BaseBookingModelInternal.mapToBaseBooking)
    )
  }

  private def toPropertyBookingCharges(model: PropertyBookingCharges): EbePropertyBookingCharges =
    model
      .into[EbePropertyBookingCharges]
      .withFieldRenamed(_.b2bNetAmount, _.b2BNetAmount)
      .withFieldRenamed(_.b2bLocalCurrency, _.b2BLocalCurrency)
      .withFieldRenamed(_.b2bLocalNetAmount, _.b2BLocalNetAmount)
      .withFieldRenamed(_.b2bExchangeRateLocalToUSD, _.b2BExchangeRateLocalToUSD)
      .transform

  private def toAdditionalBookingData(model: AdditionalBookingData): EbeAdditionalBookingData =
    model
      .into[EbeAdditionalBookingData]
      .withFieldComputed(_.localizedBenefits, _.localizedBenefits.getOrElse(Seq.empty))
      .withFieldComputed(_.supplierContact, _.supplierContact.map(toContact))
      .withFieldComputed(_.hotelPrimaryContact, _.hotelPrimaryContact.map(toContact))
      .withFieldComputed(_.hotelYcsContact, _.hotelYcsContact.map(toContact))
      .withFieldComputed(
        _.unmodifiedSpecialRequest,
        _.unmodifiedSpecialRequest.map(_.transformInto[UnmodifiedSpecialRequest])
      )
      .withFieldComputed(_.occupancy, _.occupancy.map(toOccupancy).getOrElse(Seq.empty))
      .withFieldComputed(_.supplierData, _.supplierData.map(_.transformInto[EbePropertyBookingSupplier]))
      .withFieldComputed(_.childRateSettings, _.childRateSettings.map(toChildRateSettings).getOrElse(Seq.empty))
      .withFieldComputed(
        _.bookingHoldingPartnerNames,
        _.bookingHoldingPartnerNames.map(toPropertyBookingBookingHoldingPartnerName)
      )
      .withFieldComputed(
        _.replacementResults,
        _.replacementResults.map(toPropertyBookingReplacementResult)
      )
      .transform

  private def toPropertyBookingRateCategoryBenefit(
      model: PropertyBookingRateCategoryBenefit
  ): EbePropertyBookingRateCategoryBenefit =
    model
      .into[EbePropertyBookingRateCategoryBenefit]
      .withFieldRenamed(_.benefitParameters, _.benefitParams)
      .transform

  private def toOccupancy(models: Seq[PropertyBookingOccupancy]): Seq[EbePropertyBookingOccupancy] =
    models.map { model =>
      val childOccupancy: Option[Seq[EbeChildOccupancyInfo]] =
        model.childOccupancy.map(_.map(_.transformInto[EbeChildOccupancyInfo]))
      model
        .into[EbePropertyBookingOccupancy]
        .withFieldRenamed(_.noOfAdultFemales, _.noOfFemaleAdults)
        .withFieldRenamed(_.noOfAdultMales, _.noOfMaleAdults)
        .withFieldComputed(_.childOccupancy, _ => childOccupancy.getOrElse(Seq.empty))
        .transform
    }

  private def toChildRateSettings(
      models: Seq[PropertyBookingChildRateSetting]
  ): Seq[EbePropertyBookingChildRateSetting] =
    models.map(
      _.into[EbePropertyBookingChildRateSetting]
        .withFieldRenamed(_.isCountAsRoomOcc, _.isCountedAsRoomOccupancy)
        .transform
    )

  private def toContact(model: Contact): EbeContact = model.transformInto[EbeContact]

  private def toPropertyBooking(model: PropertyBooking): EbePropertyBooking =
    model
      .into[EbePropertyBooking]
      .withFieldComputed(_.agodaManagedYcs, _.agodaManagedYcs.getOrElse(false))
      .withFieldComputed(_.childPromotions, _.childPromotions.map(toChildPromotions).getOrElse(Seq.empty))
      .transform

  private def toChildPromotions(models: Seq[PropertyBookingChildPromotion]): Seq[EbePropertyBookingChildPromotion] =
    models.map(model =>
      model
        .into[EbePropertyBookingChildPromotion]
        .withFieldRenamed(_.localDiscountAmount, _.hotelLocalCurrencyDiscountAmount)
        .transform
    )

  private def toPropertyBookingSummary(model: PropertyBookingSummary): EbePropertyBookingSummary =
    model
      .into[EbePropertyBookingSummary]
      .withFieldComputed(
        _.accountingEntity,
        model =>
          JsonAccountingEntity
            .jsonToOptionAccountingEntity(model.accountingEntity)
            .map(CommonProductConverters.toAccountingEntity)
      )
      .withFieldComputed(
        _.commonBookingEventsInfo,
        _.commonBookingEventsInfo.map(CommonProductConverters.toCommonBookingEventsInfo)
      )
      .withFieldComputed(_.accountingEntityJson, _.accountingEntity)
      .transform

  private def toPropertyBookingPaymentsAdvanceInfo(
      model: PropertyBookingPaymentsAdvanceInfo
  ): EbePropertyBookingPaymentsAdvanceInfo =
    model
      .into[EbePropertyBookingPaymentsAdvanceInfo]
      .withFieldComputed(_.referencePaymentId, _.referencePaymentId.getOrElse(0L))
      .transform

  private def toPropertyBookingPayment(model: PropertyBookingPayment): EbePropertyBookingPayment =
    model
      .into[EbePropertyBookingPayment]
      .withFieldComputed(_.transactionType, m => TransactionType.toEnum(m.transactionType))
      .withFieldComputed(_.paymentTypeId, m => PaymentType.toEnum(m.paymentTypeId))
      .withFieldComputed(_.gatewayId, m => Gateway.toEnum(m.gatewayId))
      .withFieldComputed(_.paymentRequestStatus, m => PaymentStatus.toEnum(m.paymentRequestStatus))
      .withFieldComputed(_.recCreatedBy, _.recCreatedBy.toNonOptionalUUID)
      .withFieldRenamed(_.processed_3dsOption, _.processed3DsOption)
      .withFieldRenamed(_.IsRecorded, _.isRecorded)
      .transform

  private def toPropertyBookingProvisioning(model: PropertyBookingProvisioning): EbePropertyBookingProvisioning =
    model
      .into[EbePropertyBookingProvisioning]
      .withFieldRenamed(_.request_XML, _.requestXML)
      .withFieldRenamed(_.response_XML, _.responseXML)
      .transform

  private def toPropertyFraudCcBlacklist(model: PropertyFraudCcBlacklist): EbePropertyFraudCcBlacklist =
    model
      .into[EbePropertyFraudCcBlacklist]
      .withFieldComputed(_.recCreatedBy, _.recCreatedBy.toNonOptionalUUID)
      .transform

  private def toPropertyFraudCookieBlacklist(model: PropertyFraudCookieBlacklist): EbePropertyFraudCookieBlacklist =
    model
      .into[EbePropertyFraudCookieBlacklist]
      .withFieldComputed(_.recCreatedBy, _.recCreatedBy.toNonOptionalUUID)
      .transform

  private def toPropertyRewardEarning(model: PropertyRewardEarning): RewardEarning =
    model.into[RewardEarning].withFieldComputed(_.rewardJsonToken, m => Some(m.rewardJsonToken)).transform

  private def toPropertyBookingSucc(model: PropertyBookingSucc): EbePropertyBookingSingleUsedCreditCard =
    model
      .into[EbePropertyBookingSingleUsedCreditCard]
      .withFieldComputed(_.recCreatedBy, _.recCreatedBy.toNonOptionalUUID)
      .transform

  private def toPropertyBookingBookingHoldingPartnerName(
      model: PropertyBookingBookingHoldingPartnerName
  ): EbePropertyBookingBookingHoldingPartnerName =
    model
      .into[EbePropertyBookingBookingHoldingPartnerName]
      .withFieldRenamed(_.bookingId, _.bookingId)
      .withFieldRenamed(_.level, _.level)
      .withFieldRenamed(_.partnerId, _.partnerId)
      .withFieldRenamed(_.name, _.name)
      .transform

  private def toPropertyBookingReplacementResult(
      model: ReplacementResult
  ): ProtobufReplacementResult =
    model
      .into[ProtobufReplacementResult]
      .withFieldRenamed(_.bookingId, _.bookingId)
      .withFieldComputed(_.status, m => ReplacementResultStatus.toEnum(m.status))
      .withFieldRenamed(_.errorCode, _.errorCode)
      .withFieldRenamed(_.errorMessage, _.errorMessage)
      .withFieldRenamed(_.subErrorCode, _.subErrorCode)
      .withFieldRenamed(_.dateOfStay, _.dateOfStay)
      .withFieldRenamed(_.roomIndex, _.roomIndex)
      .transform

  private def toPropertyBookingAgent(model: PropertyBookingAgent): EbePropertyBookingAgent =
    model
      .into[EbePropertyBookingAgent]
      .withFieldComputed(_.deviceInfo, _.deviceInfo)
      .transform
}
