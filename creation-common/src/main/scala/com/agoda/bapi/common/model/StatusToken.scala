package com.agoda.bapi.common.model

import com.agoda.adp.messaging.message.logging.LogLevel
import com.agoda.bapi.common.exception.{StatusTokenDeserializationException, UnSupportedStatusTokenVersionException}
import com.agoda.bapi.common.logging.StatusTokenLogMessage
import com.agoda.bapi.common.model.product.ProductTypeEnum
import com.agoda.bapi.common.token.BookingTokenEncryptionHelper
import com.agoda.bapi.common.exception.BookingCreationLogMessageBase
import com.agoda.commons.agprotobuf.scalapb.utils.ProtoConverter
import com.agoda.mpbe.statusToken.{StatusTokenV3, StatusTokenWrapper}
import org.json4s.{DefaultFormats, Formats}

import scala.util.{Failure, Try}

/**
  * StatusToken is supposed to have only Itinerary level data.
  *
  * @param itineraryId
  * @param actionId
  * @param productType
  * @param dc
  * @param actionIds
  * @param topic
  * @param operationType
  * @param whitelabelId
  * @param bookingId
  * @param version
  * @param operationId
  */
final case class StatusToken(
    itineraryId: ItineraryId,
    actionId: ActionId,
    productType: Set[String],
    dc: String,
    whitelabelId: Option[Int] = None,
    @deprecated
    actionIds: Option[Seq[Long]] = None,
    topic: Option[Int] = None,
    operationType: Option[String] = None,
    @deprecated
    bookingId: Option[Long] = None,
    // Current version of the status token
    // 1, 2, 3, 4 etc (same as setupBooking token TokenMessage)
    // supports from version 3 and onwards
    version: Option[Int] = None,
    // supports from version 5 onwards
    operationId: Option[Long] = None
)
object StatusToken {
  val Version4: Int = 4
  val Version6: Int = 6

  private val defaultVersion    = Version4
  private val DeserializeMetric = "bcre.statustoken.deserialize"
  private val SerializeMetric   = "bcre.statustoken.serialize"

  implicit private val formats: Formats = DefaultFormats

  implicit class StatusTokenSerializer(val token: StatusToken) {
    def serialize(
        loggerFunction: BookingCreationLogMessageBase => Unit,
        sendMeasurementFunction: (String, Long, Map[String, String]) => Unit
    ): String = {
      val statusToken = StatusTokenV3(
        itineraryId = token.itineraryId,
        actionId = token.actionId,
        productTypes = token.productType.map(ProductTypeEnum.toId).toSeq,
        dc = token.dc,
        whiteLabelId = token.whitelabelId,
        actionIds = token.actionIds.getOrElse(Seq.empty),
        topic = token.topic,
        operationType = token.operationType,
        bookingId = token.bookingId,
        operationId = token.operationId
      )

      val statusTokenWrapper = encodeToken(statusToken, token.version)

      val serializedToken = ProtoConverter.protoToString(statusTokenWrapper)
      sendMeasurementAndLogSerialized(token, loggerFunction, serializedToken, sendMeasurementFunction)

      serializedToken
    }
  }

  private def encodeToken(statusToken: StatusTokenV3, version: Option[Int]): StatusTokenWrapper = {
    version match {
      case Some(Version6) =>
        StatusTokenWrapper(
          version = Version6,
          tokenString = BookingTokenEncryptionHelper.encrypt(statusToken.toByteArray).getOrElse("")
        )
      case _ =>
        val protoString = ProtoConverter.protoToString(statusToken)
        StatusTokenWrapper(
          version = defaultVersion,
          tokenString = BookingTokenEncryptionHelper.encrypt(protoString).getOrElse("")
        )
    }
  }

  private def decodeToken(statusTokenWrapper: StatusTokenWrapper): Try[StatusTokenV3] = {
    if (statusTokenWrapper.version >= Version6) {
      val bytes = BookingTokenEncryptionHelper
        .decryptToByteArray(statusTokenWrapper.tokenString)
        .getOrElse(Array.emptyByteArray)
      Try(StatusTokenV3.parseFrom(bytes))
    } else if (statusTokenWrapper.version >= defaultVersion) {
      val tokenString = BookingTokenEncryptionHelper.decrypt(statusTokenWrapper.tokenString).getOrElse("")
      Try(ProtoConverter.stringToProto(tokenString, StatusTokenV3))
    } else {
      Failure(
        new UnSupportedStatusTokenVersionException(
          s"StatusToken version: ${statusTokenWrapper.version.toString} is not supported"
        )
      )
    }
  }

  def deserialize(
      stringToken: String,
      loggerFunction: BookingCreationLogMessageBase => Unit,
      sendMeasurementFunction: (String, Long, Map[String, String]) => Unit
  ): Try[StatusToken] = {

    val result = Try(ProtoConverter.stringToProto(stringToken, StatusTokenWrapper))
      .flatMap { statusTokenWrapper =>
        val version = Some(statusTokenWrapper.version)
        if (statusTokenWrapper.version >= defaultVersion) {
          decodeToken(statusTokenWrapper).map { decodedStatusToken =>
            val statusToken = StatusToken(
              itineraryId = decodedStatusToken.itineraryId,
              actionId = decodedStatusToken.actionId,
              productType = decodedStatusToken.productTypes.map(ProductTypeEnum.fromValue).map(_.toString).toSet,
              dc = decodedStatusToken.dc,
              whitelabelId = decodedStatusToken.whiteLabelId,
              actionIds = if (decodedStatusToken.actionIds.nonEmpty) Some(decodedStatusToken.actionIds) else None,
              topic = decodedStatusToken.topic,
              operationType = decodedStatusToken.operationType,
              bookingId = decodedStatusToken.bookingId,
              version = version,
              operationId = decodedStatusToken.operationId
            )
            statusToken -> version
          }
        } else {
          val logMessage = StatusTokenLogMessage(
            logLevel = LogLevel.ERROR,
            message = "protobuf-model-version-error",
            statusTokenString = stringToken,
            version = version,
            itineraryId = None,
            isSuccess = false
          )
          loggerFunction(logMessage)
          Failure(
            new UnSupportedStatusTokenVersionException(
              s"StatusToken version: ${statusTokenWrapper.version.toString} is not supported"
            )
          )
        }
      }
      .recoverWith {
        case failure =>
          Failure(new StatusTokenDeserializationException(s"Couldn't deserialize status token: $stringToken", failure))
      }

    val tokenResultT = result.map(_._1)
    val versionT     = result.map(_._2)
    sendMeasurementAndLogDeserialized(
      stringToken,
      tokenResultT,
      versionT,
      result,
      loggerFunction,
      sendMeasurementFunction
    )

    tokenResultT
  }

  private def sendMeasurementAndLogSerialized(
      token: StatusToken,
      loggerFunction: BookingCreationLogMessageBase => Unit,
      serializedToken: String,
      sendMeasurementFunction: (String, Long, Map[String, String]) => Unit
  ): Unit = {
    if (Option(serializedToken).isEmpty || serializedToken.isEmpty) {
      loggerFunction(
        StatusTokenLogMessage(
          logLevel = LogLevel.ERROR,
          message = "serialize",
          statusTokenString = serializedToken,
          version = token.version,
          itineraryId = Option(token.itineraryId),
          isSuccess = false
        )
      )
    } else {
      loggerFunction(
        StatusTokenLogMessage(
          message = "serialize",
          statusTokenString = serializedToken,
          version = token.version,
          itineraryId = Option(token.itineraryId),
          isSuccess = true
        )
      )
    }

    sendMeasurementFunction(
      SerializeMetric,
      1,
      Map(
        "version"   -> token.version.map(_.toString).getOrElse(s"${defaultVersion.toString}"),
        "isSuccess" -> serializedToken.nonEmpty.toString
      )
    )
  }

  private def sendMeasurementAndLogDeserialized(
      stringToken: String,
      tokenResult: Try[StatusToken],
      versionT: Try[Option[Int]],
      result: Try[(StatusToken, Option[Int])],
      loggerFunction: BookingCreationLogMessageBase => Unit,
      sendMeasurementFunction: (String, Long, Map[String, String]) => Unit
  ): Unit = {
    loggerFunction(
      StatusTokenLogMessage(
        message = "deserialize",
        cause = tokenResult.failed.toOption,
        statusTokenString = stringToken,
        version = versionT.toOption.flatten,
        itineraryId = tokenResult.map(_.itineraryId).toOption,
        isSuccess = tokenResult.isSuccess
      )
    )

    sendMeasurementFunction(
      DeserializeMetric,
      1,
      Map("version" -> versionT.map(_.toString).getOrElse(""), "isSuccess" -> result.isSuccess.toString)
    )
  }
}
