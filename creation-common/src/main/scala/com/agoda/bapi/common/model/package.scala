package com.agoda.bapi.common

package object model {
  type AmendmentId                 = Int
  type CurrencyCode                = String
  type CurrencyScale               = Int // number of digit after comma
  type ItineraryId                 = Long
  type OperationId                 = Long
  type BookingId                   = Long
  type ProductBookingId            = Long
  type FlightEnigmaPassengerId     = Long
  type FlightBookingId             = Long
  type VehicleBookingId            = Long
  type VehicleEnigmaDriverId       = Long
  type ProtectionBookingId         = Long
  type MultiProductId              = Long
  type ActionId                    = Long
  type SurchargeId                 = Int
  type HotelId                     = Int
  type LanguageId                  = Int
  type SiteId                      = Int
  type StorefrontId                = Int
  type PNR                         = String
  type TotalRecordsCount           = Int
  type FlightPaxId                 = Long
  type FlightSliceId               = Long
  type CarrierType                 = String
  type WorkflowId                  = Int
  type TopicId                     = Int
  type PackageId                   = Long
  type CartId                      = Long
  type ActivityBookingId           = Long
  type BaseBookingId               = Long
  type OfferId                     = Long
  type BasePaxId                   = Long
  type MetaId                      = Long
  type GeoId                       = Long
  type EssInfoId                   = Long
  type BookingRelationshipId       = Long
  type BreakdownId                 = Long
  type VehicleBookingLocationId    = Long
  type VehicleInfoId               = Long
  type ItineraryPaymentId          = Long
  type FlightBaggageId             = Long
  type FlightBaggageAllowanceId    = Long
  type FlightBrandAttributeId      = Long
  type FlightBrandAttributeParamId = Long
  type FlightBrandSelectionId      = Long
  type FlightPolicyId              = Long
  type FlightSeatSelectionId       = Long
  type FlightSegmentId             = Long
  type FlightSegmentInfoId         = Long
  type FlightPaxBreakdownId        = Long

  object CurrencyCode {
    val USD: CurrencyCode = "USD"
    val JPY: CurrencyCode = "JPY"
    val CAD: CurrencyCode = "CAD"
    val AUD: CurrencyCode = "AUD"
    val RUB: CurrencyCode = "RUB"
    val INR: CurrencyCode = "INR"
  }

  final case class CurrencyInfo(
      currencyId: Int,
      currencyCode: String,
      noDecimal: Int
  )

  final case class CurrencyExchangeRate(
      code: String,
      exchangeRate: BigDecimal
  )

  final case class Campaign(
      campaignId: Int,
      campaignName: String,
      siteId: Int,
      promotionCode: String,
      discountType: Int,
      discountValue: BigDecimal,
      currencyCode: Option[String]
  )

  final case class Icon(paymentMethodId: Int, iconType: Int, iconUrl: String)

  final case class Locale(languageId: Int, locale: String)

  final case class Currency(
      code: String,
      noOfDecimals: Int,
      exchangeRate: Option[BigDecimal] = None
  )

  implicit def stringToCurrencyCode(s: String): CurrencyCode = s
}
