package com.agoda.bapi.common.util

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.{JsonGenerator, JsonParser}
import com.fasterxml.jackson.databind.module.SimpleModule
import com.fasterxml.jackson.databind.{DeserializationContext, DeserializationFeature, JsonDeserializer, JsonMappingException, JsonNode, JsonSerializer, MapperFeature, ObjectMapper, SerializationFeature, SerializerProvider}
import com.fasterxml.jackson.datatype.joda.JodaModule
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.scala.DefaultScalaModule
import com.fasterxml.jackson.module.scala.ScalaObjectMapper
import enumeratum.{Enum, EnumEntry}

import scala.jdk.CollectionConverters._
import scala.reflect.ClassTag
import scala.util.{Failure, Success, Try}
import com.agoda.upi.models.serializer.jackson.{JacksonSerialization => CartJacksonSerialization}

trait JacksonSerializer {
  def serialize[T: Manifest](input: T): Try[String]
  def deserialize[T: Manifest](input: String): Try[T]
}

object JacksonSerializer extends JacksonSerializer {

  private val simpleModule = new SimpleModule()

  CartJacksonSerialization.addUPISerialization(simpleModule)
  CartJacksonSerialization.addUPIDeserialization(simpleModule)

  val mapper: ObjectMapper with ScalaObjectMapper = new ObjectMapper() with ScalaObjectMapper
  mapper.registerModule(simpleModule)
  mapper.registerModule(DefaultScalaModule)
  mapper.registerModule(new JodaModule())
  mapper.registerModule(new JavaTimeModule())
  mapper.setSerializationInclusion(JsonInclude.Include.NON_ABSENT)
  mapper.configure(DeserializationFeature.FAIL_ON_TRAILING_TOKENS, true)
  mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
  mapper.configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, true)
  mapper.configure(DeserializationFeature.FAIL_ON_NULL_CREATOR_PROPERTIES, true)
  mapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true)
  mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)

  override def serialize[T: Manifest](input: T): Try[String] =
    Try(mapper.writeValueAsString(input)) match {
      case Success(value)     => Success(value)
      case Failure(exception) => Failure(new SerializeException[T](exception))
    }
  override def deserialize[T: Manifest](input: String): Try[T] =
    Try(mapper.readValue[T](input)) match {
      case Success(value)     => Success(value)
      case Failure(exception) => Failure(new DeserializeException[T](input, exception))
    }

  class NullableIntDeserializer extends JsonDeserializer[Int] {
    override def getNullValue: Int                                             = 0
    override def deserialize(p: JsonParser, ctxt: DeserializationContext): Int = ctxt.readValue(p, classOf[Int])
  }

  class NullableStringDeserializer extends JsonDeserializer[String] {
    override def getNullValue(ctx: DeserializationContext): String               = ""
    override def deserialize(p: JsonParser, ctx: DeserializationContext): String = ctx.readValue(p, classOf[String])
  }

  class NullableSeqDeserializer[T](implicit m: Manifest[Seq[T]]) extends JsonDeserializer[Seq[T]] {
    override def deserialize(p: JsonParser, ctxt: DeserializationContext): Seq[T] =
      ctxt.readValue(p, mapper.constructType[Seq[T]])

    override def getNullValue(ctxt: DeserializationContext): Seq[T] =
      Seq.empty[T]
  }

  class NullableBooleanDeserializer extends JsonDeserializer[Boolean] {
    override def getNullValue: Boolean                                             = false
    override def deserialize(p: JsonParser, ctxt: DeserializationContext): Boolean = ctxt.readValue(p, classOf[Boolean])
  }

  class EnumAsIntSerializer[T <: Enumeration#Value] extends JsonSerializer[T] {
    override def serialize(
        value: T,
        gen: JsonGenerator,
        serializers: SerializerProvider
    ): Unit =
      gen.writeNumber(value.id)
  }

  class EnumAsIntDeserializer[T <: Enumeration](enumCompanion: T) extends JsonDeserializer[T#Value] {
    override def deserialize(p: JsonParser, ctxt: DeserializationContext): T#Value =
      enumCompanion(p.getValueAsInt)
  }

  class EnumAsIntWithFallbackDeserializer[T <: Enumeration](enumCompanion: T, fallback: T#Value)
      extends JsonDeserializer[T#Value] {
    override def deserialize(p: JsonParser, ctxt: DeserializationContext): T#Value =
      Try(enumCompanion(p.getValueAsInt)).getOrElse(fallback)

    override def getNullValue: T#Value = fallback
  }

  class EnumAsOptIntSerializer[T <: Enumeration#Value] extends JsonSerializer[Option[T]] {
    override def serialize(
        value: Option[T],
        gen: JsonGenerator,
        serializers: SerializerProvider
    ): Unit =
      value match {
        case Some(mpType) => gen.writeNumber(mpType.id)
        case _            => gen.writeNull()
      }

    override def isEmpty(provider: SerializerProvider, value: Option[T]): Boolean = value.isEmpty
  }

  class EnumAsOptIntDeserializer[T <: Enumeration](enumCompanion: T) extends JsonDeserializer[Option[T#Value]] {
    override def getNullValue: Option[T#Value] = None

    override def deserialize(p: JsonParser, ctx: DeserializationContext): Option[T#Value] = {
      val intValue = p.getValueAsInt
      scala.util.Try(enumCompanion(intValue)).toOption
    }
  }

  class EnumAsStringSerializer[T <: Enumeration#Value] extends JsonSerializer[T] {
    override def serialize(
        value: T,
        gen: JsonGenerator,
        serializers: SerializerProvider
    ): Unit =
      gen.writeString(value.toString)
  }

  class EnumAsStringDeserializer[T <: Enumeration](enumCompanion: T) extends JsonDeserializer[T#Value] {
    override def deserialize(p: JsonParser, ctx: DeserializationContext): T#Value = {
      val strValue = p.getValueAsString
      enumCompanion.values
        .find(_.toString == strValue)
        .getOrElse(throw new NoSuchElementException(s"Unable to find $strValue in ${enumCompanion.toString} enum"))
    }
  }

  class EnumAsStringWithFallbackDeserializer[T <: Enumeration](enumCompanion: T, fallback: T#Value)
      extends JsonDeserializer[T#Value] {
    override def deserialize(p: JsonParser, ctx: DeserializationContext): T#Value = {
      val strValue = p.getValueAsString
      enumCompanion.values.find(_.toString == strValue).getOrElse(fallback)
    }

    override def getNullValue: T#Value = fallback
  }

  class EnumAsOptStringSerializer[T <: Enumeration#Value] extends JsonSerializer[Option[T]] {
    override def serialize(
        value: Option[T],
        gen: JsonGenerator,
        serializers: SerializerProvider
    ): Unit =
      value match {
        case Some(mpType) => gen.writeString(mpType.toString)
        case _            => gen.writeNull()
      }

    override def isEmpty(provider: SerializerProvider, value: Option[T]): Boolean = value.isEmpty
  }

  class EnumAsOptStringDeserializer[T <: Enumeration](enumCompanion: T) extends JsonDeserializer[Option[T#Value]] {
    override def getNullValue: Option[T#Value] = None

    override def deserialize(p: JsonParser, ctx: DeserializationContext): Option[T#Value] = {
      val strValue = p.getValueAsString
      //  ToDO: cache values to map
      enumCompanion.values.find(_.toString == strValue)
    }
  }
  class EnumAsOptStringWithExceptionDeserializer[T <: Enumeration](enumCompanion: T)
      extends JsonDeserializer[Option[T#Value]] {
    override def getNullValue: Option[T#Value] = None
    override def deserialize(p: JsonParser, ctx: DeserializationContext): Option[T#Value] = {
      // Always return None when json is null
      if (!p.hasTextCharacters) {
        None
      } else {
        val strValue = p.getValueAsString
        // Throw exception when enum can't be found
        val enumValue = enumCompanion.values
          .find(_.toString == strValue)
          .getOrElse(throw new NoSuchElementException(s"Unable to find $strValue in ${enumCompanion.toString} enum"))
        Some(enumValue)
      }
    }
  }

  abstract class OptionalMapDeserializer[TKey, TValue: Manifest] extends JsonDeserializer[Option[Map[TKey, TValue]]] {

    def getKey(keyString: String): TKey

    override def deserialize(
        parser: JsonParser,
        ctx: DeserializationContext
    ): Option[Map[TKey, TValue]] = {
      val tree: JsonNode = ctx.readTree(parser)

      Option(tree).map(
        _.fieldNames().asScala
          .map { keyString =>
            val key = Try(getKey(keyString))
              .getOrElse(throw new JsonMappingException(parser, s"[OptionalMapDeserializer] Invalid key: $keyString"))
            val contentNode = tree.get(keyString)
            val contentValue = Try(mapper.treeToValue[TValue](contentNode)).getOrElse(
              throw new JsonMappingException(
                parser,
                s"[OptionalMapDeserializer] Fail to deserialize value: ${contentNode.toString}"
              )
            )
            key -> contentValue
          }
          .toMap
      )
    }

  }

  abstract class OptionalIntMapDeserializer[TValue: Manifest] extends OptionalMapDeserializer[Int, TValue] {
    override def getKey(keyString: String): Int = keyString.toInt
  }

}

class EnumeratumEnumAsOptStringSerializer[T <: EnumEntry] extends JsonSerializer[Option[T]] {
  override def serialize(value: Option[T], gen: JsonGenerator, serializers: SerializerProvider): Unit =
    value match {
      case Some(value) => gen.writeString(value.entryName)
      case None        => gen.writeNull()
    }
}

class EnumeratumEnumAsOptStringDeserializer[T <: EnumEntry](enum: Enum[T])(implicit classTag: ClassTag[T])
    extends JsonDeserializer[Option[T]] {
  override def deserialize(parser: JsonParser, context: DeserializationContext): Option[T] =
    Option(parser.getValueAsString()).flatMap { strValue =>
      Try(enum.withName(strValue)).toOption.orElse {
        throw new NoSuchElementException(s"$strValue is not the member of ${classTag.runtimeClass.getSimpleName} enum")
      }
    }

  override def getNullValue(context: DeserializationContext): Option[T] = None
}

class DeserializeException[T: Manifest](inputString: String, cause: Throwable)
    extends Exception(s"couldn't deserialize: ${manifest[T].toString()} from input: ${inputString}", cause)
class SerializeException[T: Manifest](cause: Throwable)
    extends Exception(s"couldn't serialize: ${manifest[T].toString()}", cause)
