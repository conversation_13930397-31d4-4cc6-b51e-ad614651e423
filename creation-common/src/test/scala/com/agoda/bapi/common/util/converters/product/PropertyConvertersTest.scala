package com.agoda.bapi.common.util.converters.product

import com.agoda.bapi.common.model.creation.common.JsonAccountingEntity
import com.agoda.bapi.common.model.property.PropertyBookingStateModel._
import com.agoda.bapi.common.util.JacksonSerializer
import com.agoda.bapi.common.util.LocalDateTimeUtil.toJavaLocalDateTime
import com.agoda.bapi.common.util.converters.ProductUtils.CompareSettings
import com.agoda.bapi.common.util.converters.{CommonProductConverters, ProductUtils}
import com.agoda.bapi.common.util.generators.{AccountingEntityJsonGenerator, DataGenerator}
import com.agoda.commons.agprotobuf.scalapb.utils.validation.{EverythingMappedMappingValidationResult, ProtoValidator}
import com.agoda.mpbe.state.product.property.{EbePropertyBookingSummary, PropertyProductModel}
import org.joda.time.DateTime
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

class PropertyConvertersTest extends AnyWordSpec with Matchers {

  /**
    * Models are compared in the order Compare(Original, Converted)
    */

  "PropertyConverters" should {
    import com.agoda.bapi.common.util.generators.AlwaysSetDataGenerators._
    import com.agoda.bapi.common.util.generators.implicits.ProtobufEnumGeneration._

    "convert Property internal model to protobuf model" in {
      val original = DataGenerator.generate[PropertyBookingModel]

      val propertyBookingModel = original.copy(
        payment = original.payment.map(
          _.copy(
            paymentTypeId = Some(paymentTypeEnumGen.generate.value),
            paymentRequestStatus = Some(paymentStatusEnumGen.generate.value),
            gatewayId = Some(gatewayEnumGen.generate.value),
            transactionType = Some(transactionTypeEnumGen.generate.value)
          )
        ),
        bookingSummary = original.bookingSummary.copy(
          accountingEntity = AccountingEntityJsonGenerator.generate
        ),
        additionalData = original.additionalData.copy(
          replacementResults = original.additionalData.replacementResults.map(replacementResult =>
            replacementResult.copy(
              status = replacementResultStatusEnumGen.generate.value
            )
          )
        )
      )

      val model = PropertyModelInternal(
        itinerary = DataGenerator.generate[PropertyItinerary],
        bookingList = Seq(propertyBookingModel)
      )

      //  Do conversion
      val resultModel = PropertyConverters.toProtoPropertyProductModel(model)

      //  Compare results
      ProductUtils.compareProducts(
        propertyBookingModel,
        resultModel.copy(bookingAmendments = Seq.empty),
        CompareSettings(
          ignoreSrc = Set(),
          //  itinerary will be validated separately (below)
          ignoreDest = Set(
            "itinerary",
            "bookingSummary.accountingEntityJson",
            "bookingAmendment",
            "bookingAcknowledgement", // bookingAcknowledgement is DEPRECATED, please refer to propertyBookingAcknowledgement
            "bookingSummary.isAab",
            "consumerFintechDetails",
            "finProductInfo",
            "bookingHoldingPartnerNames",
            "financialBreakdowns.appliedTaxPrototypeLevels",
            "priceFreezeRefInfo"
          ),
          //  accountingEntity will be validated separately (below)
          ignoreSrcCompareFields = Set(
            "bookingSummary.accountingEntity",
            "bookingAmendments",
            "bookingAcknowledgement", // bookingAcknowledgement is DEPRECATED, please refer to propertyBookingAcknowledgement
            // base_* will have separated tests below
            "baseBooking.bookingDate",
            "baseBooking.bookingStartDate",
            "baseBooking.bookingEndDate",
            "baseBooking.bookingConfirmationDate",
            "baseBooking.recCreatedWhen",
            "baseBooking.recModifiedWhen",
            "essInfos.recCreatedWhen",
            "essInfos.recModifiedWhen",
            "financialBreakdowns.appliedTaxPrototypeLevels"
          ),
          nameMap = Map(
            "bookingAttributionV2"                              -> "bookingAttributionsV2",
            "bookingHotelRoom"                                  -> "bookingHotelRooms",
            "financialBreakdown"                                -> "financialBreakdowns",
            "bookingSucc"                                       -> "bookingSingleUsedCreditCards",
            "sellInfoHistory"                                   -> "sellInfoHistories",
            "rateCategoryBenefit"                               -> "rateCategoryBenefits",
            "paymentsAdvanceInfo"                               -> "paymentsAdvanceInfos",
            "payment"                                           -> "payments",
            "payment.IsRecorded"                                -> "isRecorded",
            "payment.processed_3dsOption"                       -> "processed3DsOption",
            "provisioning"                                      -> "provisionings",
            "provisioning.request_XML"                          -> "requestXML",
            "provisioning.response_XML"                         -> "responseXML",
            "charges.b2bLocalNetAmount"                         -> "b2BLocalNetAmount",
            "charges.b2bExchangeRateLocalToUSD"                 -> "b2BExchangeRateLocalToUSD",
            "charges.b2bNetAmount"                              -> "b2BNetAmount",
            "charges.b2bLocalCurrency"                          -> "b2BLocalCurrency",
            "bookingHotelRoom"                                  -> "bookingHotelRooms",
            "propertyBookingPax"                                -> "bookingPassengers",
            "financialBreakdown"                                -> "financialBreakdowns",
            "rateCategoryBenefit"                               -> "rateCategoryBenefits",
            "payment"                                           -> "payments",
            "sellInfoHistory"                                   -> "sellInfoHistoris",
            "bookingSucc"                                       -> "bookingSingleUsedCreditCards",
            "bookingAttributionV2"                              -> "bookingAttributionsV2",
            "provisioning"                                      -> "provisionings",
            "paymentsAdvanceInfo"                               -> "paymentsAdvanceInfos",
            "sellInfoHistory"                                   -> "sellInfoHistories",
            "additionalData.occupancy.noOfAdultMales"           -> "noOfMaleAdults",
            "additionalData.occupancy.noOfAdultFemales"         -> "noOfFemaleAdults",
            "additionalData.childRateSettings.isCountAsRoomOcc" -> "isCountedAsRoomOccupancy",
            "booking.childPromotions.localDiscountAmount"       -> "hotelLocalCurrencyDiscountAmount",
            "rateCategoryBenefit.benefitParameters"             -> "benefitParams",
            "bookingChildPromotions"                            -> "childPromotions"
          )
        )
      )
      ProductUtils.compareProducts(model.itinerary, resultModel.itinerary)

      //  Additional check for json serializer
      assert(resultModel.bookingSummary.accountingEntity.nonEmpty)
      ProductUtils.compareProducts(
        JsonAccountingEntity.jsonToOptionAccountingEntity(model.bookingList.head.bookingSummary.accountingEntity).get,
        resultModel.bookingSummary.accountingEntity.get
      )

      val booking = model.bookingList.head
      booking.bookingSummary.accountingEntity.get shouldBe resultModel.bookingSummary.accountingEntityJson.get

      val baseBooking         = resultModel.baseBooking.get
      val expectedBaseBooking = booking.baseBooking.get
      baseBooking.bookingDate shouldBe toJavaLocalDateTime(expectedBaseBooking.bookingDate)
      baseBooking.bookingStartDate shouldBe toJavaLocalDateTime(expectedBaseBooking.bookingStartDate)
      baseBooking.bookingEndDate shouldBe expectedBaseBooking.bookingEndDate.map(toJavaLocalDateTime)
      baseBooking.bookingConfirmationDate shouldBe expectedBaseBooking.bookingConfirmationDate.map(toJavaLocalDateTime)
      baseBooking.recCreatedWhen shouldBe toJavaLocalDateTime(expectedBaseBooking.recCreatedWhen)
      baseBooking.recModifiedWhen shouldBe expectedBaseBooking.recModifiedWhen.map(toJavaLocalDateTime)

      val essInfo         = resultModel.essInfos.head
      val expectedEssInfo = booking.essInfos.head
      essInfo.recCreatedWhen shouldBe toJavaLocalDateTime(expectedEssInfo.recCreatedWhen)
      essInfo.recModifiedWhen shouldBe expectedEssInfo.recModifiedWhen.map(toJavaLocalDateTime)

      //  Verify we map every property inside protobuf
      ProtoValidator.validateAllValuesAreMapped(
        resultModel,
        "com.agoda.mpbe.state.product.property.PropertyProductModel.bookingAmendment",
        "com.agoda.mpbe.state.product.property.PropertyProductModel.bookingAcknowledgement",
        "com.agoda.mpbe.state.product.property.PropertyProductModel.propertyBookingAcknowledgement",
        "com.agoda.mpbe.state.product.property.PropertyProductModel.financialBreakdowns.appliedTaxPrototypeLevels",
        "com.agoda.mpbe.state.product.property.PropertyProductModel.bookingSingleUsedCreditCards.payoutUuid",
        "com.agoda.mpbe.state.product.property.PropertyProductModel.bookingSummary.cancellationChargeType",
        "com.agoda.mpbe.state.product.property.PropertyProductModel.bookingSummary.cancellationChargeItemId",
        "com.agoda.mpbe.state.product.property.PropertyProductModel.priceFreezeRefInfo",
        "com.agoda.mpbe.state.product.property.PropertyProductModel.finProductInfo",
        "com.agoda.mpbe.state.product.property.PropertyProductModel.bookingSummary.isAab",
        "com.agoda.mpbe.state.product.property.PropertyProductModel.consumerFintechDetails",
        "com.agoda.mpbe.state.product.property.PropertyProductModel.bookingHoldingPartnerNames"
      ) shouldBe EverythingMappedMappingValidationResult
    }

    "convert protobuf to Property internal model" in {

      import com.agoda.bapi.common.util.generators.implicits.ProtobufEnumGeneration._

      val propertyProductModelTmp = DataGenerator.generate[PropertyProductModel]
      val entityAsJson            = JacksonSerializer.serialize(propertyProductModelTmp.bookingSummary.accountingEntity)
      val propertyProductModel = propertyProductModelTmp.copy(
        bookingSummary = propertyProductModelTmp.bookingSummary.copy(
          accountingEntityJson = entityAsJson.toOption
        )
      )

      //  Do conversion
      val resultModel: PropertyModelInternal = PropertyConverters.toBapiPropertyProductModel(propertyProductModel)

      val propertyBookingInternal: PropertyBookingModel = CommonProductConverters.toSingleElement(
        "PropertyModelInternal.bookingList",
        resultModel.bookingList,
        identity[PropertyBookingModel]
      )

      //  Compare results
      ProductUtils.compareProducts(
        propertyProductModel,
        propertyBookingInternal,
        CompareSettings(
          //  TODO: Remove once we add mapping in proto converters
          ignoreSrc = Set(
            "itinerary",
            "bookingAmendment",
            "bookingAcknowledgement", // bookingAcknowledgement is DEPRECATED, please refer to propertyBookingAcknowledgement
            "priceFreezeRefInfo",
            "finProductInfo",
            "bookingSummary.isAab",
            "bookingSummary.pricefreezeBookingId",
            "consumerFintechDetails",
            "bookingHoldingPartnerNames",
            "financialBreakdowns.appliedTaxPrototypeLevels"
          ),
          ignoreDest = Set(
            "bookingSummary.pricefreezeBookingId"
          ),
          ignoreSrcCompareFields = Set(
            "bookingAcknowledgement", // bookingAcknowledgement is DEPRECATED, please refer to propertyBookingAcknowledgement
            "bookingSummary.accountingEntity",
            "baseBooking.bookingDate",
            "baseBooking.bookingStartDate",
            "baseBooking.bookingEndDate",
            "baseBooking.bookingConfirmationDate",
            "baseBooking.recCreatedWhen",
            "baseBooking.recModifiedWhen",
            "essInfos.recCreatedWhen",
            "essInfos.recModifiedWhen",
            "financialBreakdowns.appliedTaxPrototypeLevels"
          ),
          nameMap = Map(
            "payments.isRecorded"                                       -> "IsRecorded",
            "payments.processed3DsOption"                               -> "processed_3dsOption",
            "provisionings.requestXML"                                  -> "request_XML",
            "provisionings.responseXML"                                 -> "response_XML",
            "charges.b2BLocalNetAmount"                                 -> "b2bLocalNetAmount",
            "charges.b2BExchangeRateLocalToUSD"                         -> "b2bExchangeRateLocalToUSD",
            "charges.b2BNetAmount"                                      -> "b2bNetAmount",
            "charges.b2BLocalCurrency"                                  -> "b2bLocalCurrency",
            "bookingHotelRooms"                                         -> "bookingHotelRoom",
            "bookingPassengers"                                         -> "propertyBookingPax",
            "financialBreakdowns"                                       -> "financialBreakdown",
            "rateCategoryBenefits"                                      -> "rateCategoryBenefit",
            "payments"                                                  -> "payment",
            "sellInfoHistoris"                                          -> "sellInfoHistory",
            "bookingSingleUsedCreditCards"                              -> "bookingSucc",
            "bookingAttributionsV2"                                     -> "bookingAttributionV2",
            "provisionings"                                             -> "provisioning",
            "paymentsAdvanceInfos"                                      -> "paymentsAdvanceInfo",
            "sellInfoHistories"                                         -> "sellInfoHistory",
            "additionalData.occupancy.noOfMaleAdults"                   -> "noOfAdultMales",
            "additionalData.occupancy.noOfFemaleAdults"                 -> "noOfAdultFemales",
            "additionalData.childRateSettings.isCountedAsRoomOccupancy" -> "isCountAsRoomOcc",
            "booking.childPromotions.hotelLocalCurrencyDiscountAmount"  -> "localDiscountAmount",
            "bookingSummary.accountingEntityJson"                       -> "accountingEntity",
            "rateCategoryBenefits.benefitParams"                        -> "benefitParameters",
            "childPromotions"                                           -> "bookingChildPromotions"
          )
        )
      )
      ProductUtils.compareProducts(resultModel.itinerary, propertyProductModel.itinerary)

      //  Additional check for bookingSummary.accountingEntity json serializer
      assert(propertyBookingInternal.bookingSummary.accountingEntity.nonEmpty)
      ProductUtils.compareProducts(
        propertyProductModel.bookingSummary.accountingEntity.get,
        JsonAccountingEntity.jsonToOptionAccountingEntity(propertyBookingInternal.bookingSummary.accountingEntity).get
      )

      // Checking all fields are populated
      ProductUtils.validateAllValuesAreMapped(
        propertyBookingInternal,
        "com.agoda.bapi.common.model.property.PropertyBookingStateModel.PropertyBookingModel.bookingSummary.pricefreezeBookingId"
      )
    }

    "compose into identity `f(g(x)) = x`" in {

      val original = DataGenerator.generate[PropertyBookingModel]
      val dateTime = DateTime.now()
      val propertyBookingModel = original.copy(
        payment = original.payment.map(
          _.copy(
            paymentTypeId = Some(paymentTypeEnumGen.generate.value),
            paymentRequestStatus = Some(paymentStatusEnumGen.generate.value),
            gatewayId = Some(gatewayEnumGen.generate.value),
            transactionType = Some(transactionTypeEnumGen.generate.value)
          )
        ),
        bookingSummary = original.bookingSummary.copy(
          accountingEntity = AccountingEntityJsonGenerator.generate
        ),
        baseBooking = original.baseBooking.map(
          _.copy(
            bookingDate = dateTime,
            bookingStartDate = dateTime,
            bookingEndDate = None,
            bookingConfirmationDate = None,
            recCreatedWhen = dateTime,
            recModifiedWhen = None
          )
        )
      )

      val generatedPropertyModelInternal = PropertyModelInternal(
        itinerary = DataGenerator.generate[PropertyItinerary],
        bookingList = Seq(propertyBookingModel)
      )

      val convertedPropertyProductModel = PropertyConverters.toProtoPropertyProductModel(generatedPropertyModelInternal)

      val testPropertyModelInternal = PropertyConverters.toBapiPropertyProductModel(convertedPropertyProductModel)

      ProductUtils.compareProducts(
        generatedPropertyModelInternal,
        testPropertyModelInternal,
        CompareSettings(ignoreSrcCompareFields = Set("bookingList.bookingSummary.pricefreezeBookingId"))
      )
    }

    "property accounting entity fallback test" in {
      val bookingSummary = DataGenerator.generate[EbePropertyBookingSummary].copy(accountingEntityJson = None)
      val result = JsonAccountingEntity
        .jsonToOptionAccountingEntity(PropertyConverters.toBapiPropertyBookingSummary(bookingSummary).accountingEntity)
        .get

      result.revenue shouldBe bookingSummary.accountingEntity.get.revenue
      result.rateContract shouldBe bookingSummary.accountingEntity.get.rateContract
      result.merchantOfRecord shouldBe bookingSummary.accountingEntity.get.merchantOfRecord
      result.argument shouldBe bookingSummary.accountingEntity.get.argument
    }
  }
}
