package com.agoda.bapi.server.handler.context

import com.agoda.bapi.common.directive.HttpHeaderBase
import com.agoda.bapi.common.handler.{RequestContext, RequestContextBuilder}
import com.agoda.bapi.common.message.creation.{BookingCreationContext, UserAgent}
import com.agoda.bapi.common.message.setupBooking.{PackageRequest, ProductsRequest, SetupBookingRequest}
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.message.{BookingCreationFlowContext, ExperimentData}
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{<PERSON><PERSON><PERSON>cyCode, UserContext, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.token.PropertySetupBookingToken
import com.agoda.bapi.server.facades.aggregator.MockProductUtils.createMockProduct
import com.agoda.bapi.server.model.pricebreakdown.flights.MockFlightsPricingData
import com.agoda.bapi.server.utils.ProductTokenExtractorUtils
import com.agoda.flights.client.v2.model.ExternalLoyaltyPricing
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfterEach
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future
import scala.util.Try

class SetupBookingContextBuilderSpec extends AsyncWordSpec with MockitoSugar with Matchers with BeforeAndAfterEach {

  private val requestContextBuilder = mock[RequestContextBuilder]
  private val tokenExtractor        = mock[ProductTokenExtractorUtils]
  private val header                = mock[HttpHeaderBase]
  private val mockFeatureAware      = mock[FeatureAware]
  private val mockRequestContext = RequestContext(
    languageId = 1,
    locale = "TH",
    clientId = 1,
    path = "/",
    messagesBag = null,
    whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, mock[FeaturesConfiguration]),
    userContext = Option(
      UserContext(
        languageId = 1,
        requestOrigin = "A",
        nationalityId = 1,
        currency = "USD",
        experimentData = Option(
          ExperimentData(
            userId = "User - 1",
            deviceTypeId = "WEB",
            memberId = None,
            cId = None,
            trafficGroup = None,
            aId = None,
            serverName = None
          )
        )
      )
    ),
    xForwardedForIp = "0.0.0.0",
    featureAware = Some(mockFeatureAware)
  )
  private val mockPropertySession = Map(
    "1" -> PropertySetupBookingToken(
      "1",
      Some("123"),
      Some(2),
      Some("df-token"),
      Some(1L),
      Some(true),
      Some("original-identifier")
    )
  )

  private val builder = new SetupBookingContextBuilderImpl(requestContextBuilder, tokenExtractor)

  val hkdCurrency: CurrencyCode = "HKD"
  val overrideMockProduct       = createMockProduct()
  private def flightsDataWithLoyalty(id: String) =
    overrideMockProduct.flights.map(flight =>
      flight.copy(
        flightPricing = Some(
          flight.flightPricing.get
            .updated(
              hkdCurrency,
              flight.flightPricing
                .get(hkdCurrency)
                .copy(
                  externalLoyalty = Some(
                    ExternalLoyaltyPricing(
                      offers = Vector(MockFlightsPricingData.itineraryOffer(id).get)
                    )
                  )
                )
            )
        )
      )
    )

  override def beforeEach: Unit = {
    reset(requestContextBuilder)
    reset(tokenExtractor)

    when(header.requestId).thenReturn("ReqId123")
    when(header.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.Agoda, mock[FeaturesConfiguration]))

    when(requestContextBuilder.build(any[BookingCreationFlowContext](), any(), any(), any(), any()))
      .thenReturn(
        Future.successful(
          mockRequestContext
            .copy(whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()))
        )
      )

    when(tokenExtractor.extractSetupBookingToken(any())).thenReturn(
      Try(
        SetupBookingSessionContext(
          mockPropertySession,
          Some(PackageRequest("client token", Some("package token"))),
          Some(1L)
        )
      )
    )
  }

  "setup booking request context" should {
    "build should make tokenized session available in context" in {
      val request = mock[SetupBookingRequest]
      when(request.productsRequest).thenReturn(ProductsRequest(Some(TokenMessage("ava", 3))))
      when(request.correlationId).thenReturn(Some("ReqId123"))
      when(request.bookingContext)
        .thenReturn(Some(BookingCreationContext(sessionId = "SessionId123", userAgent = new UserAgent)))

      val setupBookingContext = builder.build(request, header)

      setupBookingContext.map { c =>
        c.session.properties.head._1 shouldBe "1"
        val property = c.session.properties.head._2
        property.absRequestId shouldBe Some("123")
        property.allotmentResult shouldBe Some(2)
        property.productToken shouldBe Some("df-token")
        property.productTokenKey shouldBe "1"
        c.getIsRoomHasSwapped("1") shouldBe Some(true)
        c.getOriginalRoomIdentifier("1") shouldBe Some("original-identifier")

        c.correlationId shouldBe "ReqId123"
        c.session.timestamp shouldBe Some(1L)
        assert(c.requestContext.userContext.get.experimentData.isDefined)
        c.session.packages shouldBe Some(PackageRequest("client token", Some("package token")))
      }
    }

    "build should map correctly if no booking session id in request and booking session id in token" in {
      val expectedBookingSessionID = "booking-session-id"

      val request = mock[SetupBookingRequest]
      when(request.productsRequest).thenReturn(ProductsRequest(Some(TokenMessage("ava", 3))))
      when(request.correlationId).thenReturn(Some("ReqId123"))
      when(request.bookingContext).thenReturn(
        Some(
          BookingCreationContext(
            sessionId = "SessionId123",
            userAgent = new UserAgent,
            bookingSessionId = Some("some-id")
          )
        )
      )

      when(tokenExtractor.extractSetupBookingToken(any()))
        .thenReturn(Try(SetupBookingSessionContext(bookingSessionId = Some(expectedBookingSessionID))))

      val setupBookingContext = builder.build(request, header)

      setupBookingContext.map { c =>
        c.bookingSessionId shouldBe expectedBookingSessionID
      }
    }

    "build should generate random correctly if no booking session id in request and no booking session id in token" in {
      val request = mock[SetupBookingRequest]
      when(request.productsRequest).thenReturn(ProductsRequest(Some(TokenMessage("ava", 3))))
      when(request.correlationId).thenReturn(Some("ReqId123"))
      when(request.bookingContext).thenReturn(None)

      when(tokenExtractor.extractSetupBookingToken(any()))
        .thenReturn(Try(SetupBookingSessionContext(bookingSessionId = None)))

      val setupBookingContext = builder.build(request, header)

      setupBookingContext.map { c =>
        c.bookingSessionId.size shouldBe 36 // We are expecting random UUID here
      }
    }

    "build should map correctly if booking session id in request and no booking session id in token" in {
      val expectedBookingSessionID = "booking-session-id"

      val request = mock[SetupBookingRequest]
      when(request.productsRequest).thenReturn(ProductsRequest(Some(TokenMessage("ava", 3))))
      when(request.correlationId).thenReturn(Some("ReqId123"))
      when(request.bookingContext).thenReturn(
        Some(
          BookingCreationContext(
            sessionId = "SessionId123",
            userAgent = new UserAgent,
            bookingSessionId = Some(expectedBookingSessionID)
          )
        )
      )

      when(tokenExtractor.extractSetupBookingToken(any()))
        .thenReturn(Try(SetupBookingSessionContext(bookingSessionId = None)))

      val setupBookingContext = builder.build(request, header)

      setupBookingContext.map { c =>
        c.bookingSessionId shouldBe expectedBookingSessionID
      }
    }

    "build should pass booking session id to requestContext correctly, if booking session id is present in request" in {
      val request = mock[SetupBookingRequest]

      when(request.productsRequest).thenReturn(ProductsRequest(Some(TokenMessage("ava", 3))))
      when(request.correlationId).thenReturn(Some("ReqId123"))

      when(request.bookingContext).thenReturn(
        Some(
          BookingCreationContext(
            sessionId = "SessionId123",
            userAgent = new UserAgent,
            bookingSessionId = Some("booking-session-id")
          )
        )
      )

      val bookingCreationContext = request.bookingContext

      when(requestContextBuilder.build(any[BookingCreationFlowContext](), any(), any(), any(), any()))
        .thenReturn(
          Future.successful(
            mockRequestContext
              .copy(
                whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
                bookingCreationContext = bookingCreationContext
              )
          )
        )

      val setupBookingContext = builder.build(request, header)

      setupBookingContext.map { c =>
        c.requestContext.bookingCreationContext.get.bookingSessionId shouldBe Some("booking-session-id")
      }
    }

    "build should map flow type correctly when vehicle protection is present" in {
      val request        = mock[SetupBookingRequest]
      val productRequest = mock[ProductsRequest]
      when(request.productsRequest).thenReturn(productRequest)
      when(productRequest.packageRequest).thenReturn(None)
      when(productRequest.propertyRequests).thenReturn(Nil)
      when(productRequest.cartPricingContext).thenReturn(None)
      when(productRequest.bookingToken).thenReturn(Some(TokenMessage("ava", 3)))
      when(productRequest.hasVehicleWithProtection()).thenReturn(true)
      when(request.correlationId).thenReturn(Some("ReqId123"))
      when(request.bookingContext)
        .thenReturn(Some(BookingCreationContext(sessionId = "SessionId123", userAgent = new UserAgent)))
      val setupBookingContext = builder.build(request, header)

      setupBookingContext.map { c =>
        c.session.properties.head._1 shouldBe "1"
        val property = c.session.properties.head._2
        property.absRequestId shouldBe Some("123")
        property.allotmentResult shouldBe Some(2)
        property.productToken shouldBe Some("df-token")
        property.productTokenKey shouldBe "1"
        c.bookingFlowType shouldBe BookingFlow.Cart
      }
    }

    "get retryPaymentContext from token and put in context when ABEX-591 is B" in {
      val request = mock[SetupBookingRequest]
      when(request.productsRequest).thenReturn(ProductsRequest(Some(TokenMessage("ava", 3))))
      when(request.correlationId).thenReturn(Some("ReqId123"))
      when(request.bookingContext)
        .thenReturn(Some(BookingCreationContext(sessionId = "SessionId123", userAgent = new UserAgent)))
      when(mockFeatureAware.decryptRetryPaymentBookingToken).thenReturn(true)
      when(tokenExtractor.extractRetryPaymentBookingToken(any()))
        .thenReturn(Try(RetryPaymentContext(flights = flightsDataWithLoyalty(""))))

      val setupBookingContext = builder.build(request, header)

      setupBookingContext.map { c =>
        c.session.properties.head._1 shouldBe "1"
        val property = c.session.properties.head._2
        property.absRequestId shouldBe Some("123")
        property.allotmentResult shouldBe Some(2)
        property.productToken shouldBe Some("df-token")
        property.productTokenKey shouldBe "1"
        c.getIsRoomHasSwapped("1") shouldBe Some(true)
        c.getOriginalRoomIdentifier("1") shouldBe Some("original-identifier")

        c.correlationId shouldBe "ReqId123"
        c.session.timestamp shouldBe Some(1L)
        assert(c.requestContext.userContext.get.experimentData.isDefined)
        c.session.packages shouldBe Some(PackageRequest("client token", Some("package token")))
        c.retryPaymentContext.isDefined shouldBe true
        c.retryPaymentContext.get.flights shouldBe flightsDataWithLoyalty("")
      }
    }
  }
}
