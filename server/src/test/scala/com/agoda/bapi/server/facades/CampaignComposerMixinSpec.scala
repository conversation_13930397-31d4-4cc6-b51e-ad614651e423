package com.agoda.bapi.server.facades

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.message.{DurationRequest, ExperimentData, PropertySearchCriteria, SearchCampaignRequestV2}
import com.agoda.bapi.common.model.creation.DiscountType
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{CampaignInfo, CampaignInfoInternal, UserContext}
import com.agoda.bapi.server.facades.helpers.{SetupBookingContextFixture, SetupBookingRequestFixture}
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.repository.dto.papi.DiscountRequest
import com.agoda.bapi.server.service.CampaignService
import com.agoda.gandalf.common.CrgwCampaignProductType
import com.agoda.gandalf.response.{CreditCardPromotionResponse, Discount}
import org.joda.time.LocalDate
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{RETURNS_DEEP_STUBS, times, verify, when}
import org.scalatest.BeforeAndAfterEach
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.{ExecutionContext, Future}

class CampaignComposerMixinSpec
    extends AsyncWordSpec
    with MockitoSugar
    with Matchers
    with BeforeAndAfterEach
    with CampaignComposerMixin {

  override val campaignService: CampaignService = mock[CampaignService]

  val defaultCampaignInfo = CampaignInfoRequest(id = Some(1), cid = 0, promotionCode = "AGODA20")

  "getCampaignRequest" should {
    val propertyRequestItem    = mock[PropertyRequestItem]
    val propertySearchCriteria = mock[PropertySearchCriteria]
    val durationRequest        = mock[DurationRequest]
    when(durationRequest.checkIn).thenReturn(new LocalDate("2020-12-15"))
    when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)
    when(propertySearchCriteria.propertyId).thenReturn(Some(1234L))
    when(propertyRequestItem.propertySearchCriteria).thenReturn(propertySearchCriteria)

    val mockCcBinCampaign = CampaignInfoInternal(
      campaignInfo = CampaignInfo(
        id = 1126,
        cid = 1897965,
        promotionCode = "",
        discountType = DiscountType.Percent,
        amount = 0,
        currency = None
      ),
      promotionId = Some(392340)
    )
    val mockCcBinCampaign2 = CampaignInfoInternal(
      campaignInfo = CampaignInfo(
        id = 0,
        cid = 1897965,
        promotionCode = "HSBC_Flash_2",
        discountType = DiscountType.Amount,
        amount = 200,
        currency = None
      ),
      promotionId = Some(392340)
    )

    new SetupBookingRequestFixture with SetupBookingContextFixture {
      "return only request campaign since bin and ccid are not send in request" in {
        val paymentRequest = mock[PaymentRequest]
        when(paymentRequest.ccBin).thenReturn(None)
        when(paymentRequest.ccId).thenReturn(None)
        when(paymentRequest.ccToken).thenReturn(None)
        when(userContextFixture.experimentData.aId).thenReturn(Some("aId"))
        when(userContextFixture.experimentData.cId).thenReturn(Some("1556943"))
        when(setupBookingRequest.autoApplyCreditCardPromotionInfo).thenReturn(None)
        when(setupBookingRequest.campaignInfo).thenReturn(Some(defaultCampaignInfo))
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        when(productsRequest.propertyRequests).thenReturn(Seq(propertyRequestItem))
        val mockCcBinCampaign = CampaignInfoInternal(
          campaignInfo = CampaignInfo(
            id = 1231029,
            cid = 123,
            promotionCode = "",
            discountType = DiscountType.Percent,
            amount = 0,
            currency = None
          ),
          promotionId = Some(392340)
        )
        val memberEmail = Some("<EMAIL>")
        val expectedDiscount = DiscountRequest(
          email = memberEmail,
          campaignInfos = Some(
            List(
              CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None))
            )
          )
        )
        getCampaignRequest(
          request = setupBookingRequest,
          ccBinCampaigns = Seq(mockCcBinCampaign),
          memberEmail = memberEmail
        )
          .map { result =>
            result shouldBe Some(expectedDiscount)
          }
      }
    }

    new SetupBookingRequestFixture with SetupBookingContextFixture {
      "return combined request + cc bin campaign list" in {
        val paymentRequest = mock[PaymentRequest]
        when(paymentRequest.ccBin).thenReturn(Some("411111"))
        when(paymentRequest.ccId).thenReturn(None)
        when(productsRequest.propertyRequests).thenReturn(Seq(propertyRequestItem))
        when(userContextFixture.experimentData.aId).thenReturn(Some("aId"))
        when(userContextFixture.experimentData.cId).thenReturn(Some("1556943"))
        when(setupBookingRequest.autoApplyCreditCardPromotionInfo).thenReturn(None)
        when(setupBookingRequest.campaignInfo).thenReturn(Some(defaultCampaignInfo))
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        val mockCcBinCampaign = CampaignInfoInternal(
          campaignInfo = CampaignInfo(
            id = 1231029,
            cid = 123,
            promotionCode = "",
            discountType = DiscountType.Percent,
            amount = 0,
            currency = None
          ),
          promotionId = Some(392340)
        )
        val memberEmail = Some("<EMAIL>")
        val expectedDiscount = DiscountRequest(
          email = memberEmail,
          campaignInfos = Some(
            List(
              CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None)),
              mockCcBinCampaign
            )
          )
        )
        getCampaignRequest(
          request = setupBookingRequest,
          ccBinCampaigns = Seq(mockCcBinCampaign),
          memberEmail = memberEmail
        )
          .map { result =>
            result shouldBe Some(expectedDiscount)
          }
      }
    }

    new SetupBookingRequestFixture with SetupBookingContextFixture {
      "return discountRequest without campaign id correctly" in {
        val paymentRequest = mock[PaymentRequest]
        when(productsRequest.propertyRequests).thenReturn(Seq(propertyRequestItem))
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        when(setupBookingRequest.campaignInfo).thenReturn(Some(defaultCampaignInfo.copy(id = None, cid = 123)))
        when(setupBookingRequest.autoApplyCreditCardPromotionInfo).thenReturn(None)
        when(userContextFixture.experimentData.aId).thenReturn(Some("aId"))
        when(userContextFixture.experimentData.cId).thenReturn(Some("1556943"))

        when(campaignService.getCampaignInfo(any(), any(), any())(any(), any()))
          .thenReturn(
            Future.successful(
              Seq(CampaignInfoInternal(CampaignInfo(5, 1, "TEST", DiscountType.Percent, 5.0, None), None))
            )
          )

        val expectedDiscount = DiscountRequest(
          email = None,
          campaignInfos = Some(
            List(
              CampaignInfoInternal(CampaignInfo(0, 123, "AGODA20", DiscountType.None, 0, None)),
              mockCcBinCampaign
            )
          )
        )
        getCampaignRequest(
          request = setupBookingRequest,
          ccBinCampaigns = Seq(mockCcBinCampaign),
          memberEmail = None
        )
          .map { result =>
            result shouldBe Some(expectedDiscount)
          }
      }
    }

    new SetupBookingRequestFixture with SetupBookingContextFixture {
      "return Discount without CC bin Campaign call when no propertyRequests" in {
        val paymentRequest = mock[PaymentRequest]
        when(paymentRequest.ccBin).thenReturn(Some("411111"))
        when(paymentRequest.ccId).thenReturn(None)
        when(productsRequest.propertyRequests).thenReturn(Seq.empty)
        when(userContextFixture.experimentData.aId).thenReturn(Some("aId"))
        when(userContextFixture.experimentData.cId).thenReturn(Some("1556943"))
        when(setupBookingRequest.autoApplyCreditCardPromotionInfo).thenReturn(None)
        when(setupBookingRequest.campaignInfo).thenReturn(Some(defaultCampaignInfo))
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))

        val memberEmail = Some("<EMAIL>")

        val expectedDiscount = DiscountRequest(
          email = memberEmail,
          campaignInfos = Some(
            List(
              CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None))
            )
          )
        )
        getCampaignRequest(
          request = setupBookingRequest,
          ccBinCampaigns = Seq.empty,
          memberEmail = memberEmail
        )
          .map { result =>
            result shouldBe Some(expectedDiscount)
          }
      }
    }

    val mockSingleAutoApplyCreditCardCampaign = AutoApplyCreditCardPromotionInfo(
      campaigns = List(
        CreditCardPromotionCampaign(cid = Option(11111), campaignId = Option(-1))
      )
    )

    val mockMultipleAutoApplyCreditCardCampaign = AutoApplyCreditCardPromotionInfo(
      campaigns = List(
        CreditCardPromotionCampaign(cid = Option(11111), campaignId = Option(-1)),
        CreditCardPromotionCampaign(cid = Option(22222), campaignId = Option(-1))
      )
    )

    new SetupBookingRequestFixture with SetupBookingContextFixture {
      "return Discount with auto apply credit card campaign" in {
        val paymentRequest = mock[PaymentRequest]
        when(productsRequest.propertyRequests).thenReturn(Seq(propertyRequestItem))
        when(userContextFixture.experimentData.aId).thenReturn(Some("aId"))
        when(userContextFixture.experimentData.cId).thenReturn(Some("1556943"))
        when(setupBookingRequest.autoApplyCreditCardPromotionInfo).thenReturn(None)
        when(setupBookingRequest.campaignInfo).thenReturn(Some(defaultCampaignInfo))
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))

        when(setupBookingContext.requestContext.featureAware).thenReturn(Some(featureAware))
        when(setupBookingRequest.autoApplyCreditCardPromotionInfo).thenReturn(
          Some(mockSingleAutoApplyCreditCardCampaign)
        )
        val memberEmail = Some("<EMAIL>")

        val expectedDiscount = DiscountRequest(
          email = memberEmail,
          campaignInfos = Some(
            List(
              CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None)),
              CampaignInfoInternal(CampaignInfo(-1, 11111, "", DiscountType.None, 0, None))
            )
          )
        )
        getCampaignRequest(
          request = setupBookingRequest,
          ccBinCampaigns = Seq.empty,
          memberEmail = memberEmail
        )
          .map { result =>
            result shouldBe Some(expectedDiscount)
          }
      }
    }

    new SetupBookingRequestFixture with SetupBookingContextFixture {
      "return Discount with multiple auto apply credit card campaigns when enable rate campaign flow" in {
        val paymentRequest = mock[PaymentRequest]
        when(productsRequest.propertyRequests).thenReturn(Seq(propertyRequestItem))
        when(userContextFixture.experimentData.aId).thenReturn(Some("aId"))
        when(userContextFixture.experimentData.cId).thenReturn(Some("1556943"))
        when(setupBookingRequest.campaignInfo).thenReturn(Some(defaultCampaignInfo))
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        when(setupBookingContext.requestContext.featureAware).thenReturn(Some(featureAware))
        when(setupBookingRequest.autoApplyCreditCardPromotionInfo).thenReturn(
          Some(mockMultipleAutoApplyCreditCardCampaign)
        )
        val memberEmail = Some("<EMAIL>")

        val expectedDiscount = DiscountRequest(
          email = memberEmail,
          campaignInfos = Some(
            List(
              CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None)),
              CampaignInfoInternal(CampaignInfo(-1, 11111, "", DiscountType.None, 0, None)),
              CampaignInfoInternal(CampaignInfo(-1, 22222, "", DiscountType.None, 0, None))
            )
          )
        )
        getCampaignRequest(
          request = setupBookingRequest,
          ccBinCampaigns = Seq.empty,
          memberEmail = memberEmail
        )
          .map { result =>
            result shouldBe Some(expectedDiscount)
          }
      }
    }

    new SetupBookingRequestFixture with SetupBookingContextFixture {
      "return campaign with zero amount when AID not match with HSBC" in {
        val paymentRequest = mock[PaymentRequest]
        when(paymentRequest.ccBin).thenReturn(Some("411111"))
        when(paymentRequest.ccId).thenReturn(None)
        when(paymentRequest.ccToken).thenReturn(None)
        when(productsRequest.propertyRequests).thenReturn(Seq(propertyRequestItem))
        when(userContextFixture.experimentData.aId).thenReturn(Some("111111"))
        when(userContextFixture.experimentData.cId).thenReturn(Some("1556943"))
        when(setupBookingRequest.autoApplyCreditCardPromotionInfo).thenReturn(None)
        when(setupBookingRequest.campaignInfo).thenReturn(Some(defaultCampaignInfo))
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        val memberEmail = Some("<EMAIL>")
        val expectedDiscount = DiscountRequest(
          email = memberEmail,
          campaignInfos = Some(
            List(
              CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None)),
              mockCcBinCampaign,
              mockCcBinCampaign2
            )
          )
        )
        getCampaignRequest(
          request = setupBookingRequest,
          ccBinCampaigns = Seq(mockCcBinCampaign, mockCcBinCampaign2),
          memberEmail = memberEmail
        )
          .map { result =>
            result shouldBe Some(expectedDiscount)
          }
      }
    }

    new SetupBookingRequestFixture with SetupBookingContextFixture {
      "do not return campaign with zero amount when AID is HSBC" in {
        val paymentRequest = mock[PaymentRequest]
        when(paymentRequest.ccBin).thenReturn(Some("411111"))
        when(paymentRequest.ccId).thenReturn(None)
        when(paymentRequest.ccToken).thenReturn(None)
        when(productsRequest.propertyRequests).thenReturn(Seq(propertyRequestItem))
        when(userContextFixture.experimentData.aId).thenReturn(Some("281245"))
        when(userContextFixture.experimentData.cId).thenReturn(Some("1556943"))
        when(setupBookingRequest.autoApplyCreditCardPromotionInfo).thenReturn(None)
        when(setupBookingRequest.campaignInfo).thenReturn(Some(defaultCampaignInfo))
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))

        val memberEmail = Some("<EMAIL>")
        val expectedDiscount = DiscountRequest(
          email = memberEmail,
          campaignInfos = Some(
            List(
              CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None)),
              mockCcBinCampaign2
            )
          )
        )
        getCampaignRequest(
          request = setupBookingRequest,
          ccBinCampaigns = Seq(mockCcBinCampaign, mockCcBinCampaign2),
          memberEmail = memberEmail
        )
          .map { result =>
            result shouldBe Some(expectedDiscount)
          }
      }
    }

    ()
  }

  "getCcBinCampaigns" should {
    "returns campaigns in case propertyRequests is None and  propertySearchCriteria is None" in {
      implicit val setupContext: SetupBookingContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)
      val experimentData                             = mock[ExperimentData]
      val userContext                                = mock[UserContext]
      val context                                    = Some(userContext.copy(experimentData = Some(experimentData.copy(cId = Some("2302934")))))

      val flightItem     = mock[FlightRequestItem]
      val protectionItem = mock[TripProtectionRequestItem]
      val productRequest =
        ProductsRequest(flightRequests = Seq(flightItem), tripProtectionRequests = Some(Seq(protectionItem)))

      when(setupContext.bookingFlowType).thenReturn(BookingFlow.FlightWithProtection)

      val request: SetupBookingRequest = SetupBookingRequest(
        paymentRequest = Some(
          PaymentRequest(
            ccToken = None,
            ccId = None,
            ccBin = Some("411111")
          )
        ),
        userContext = Some(
          UserContext(
            experimentData = Some(
              ExperimentData(
                cId = Some("2302934"),
                userId = "122332",
                deviceTypeId = "1122",
                memberId = None,
                trafficGroup = None,
                aId = None,
                serverName = None
              )
            ),
            languageId = 2,
            requestOrigin = "None",
            currency = "USD",
            nationalityId = 2
          )
        ),
        productsRequest = productRequest
      )
      val mockCcCampaignResponse = CreditCardPromotionResponse(
        id = 1230913,
        campaignId = 23492,
        siteId = Some(20234),
        displayName = "cc contactless reduce COVID-19 chance",
        bin = Some("233333"),
        discount = Discount(
          discountType = "PERCENT",
          amount = 5.0,
          currency = Some("USD")
        ),
        membershipContentId = None,
        membershipContentCmsId = None,
        productType = CrgwCampaignProductType.Flight,
        isCashback = None
      )
      val expectedResult = Seq(
        CampaignInfoInternal(
          campaignInfo = CampaignInfo(
            id = mockCcCampaignResponse.campaignId,
            cid = mockCcCampaignResponse.siteId.get,
            promotionCode = "",
            discountType = DiscountType.Percent,
            amount = 5.0,
            currency = Some("USD")
          ),
          promotionId = Some(mockCcCampaignResponse.id)
        )
      )
      when(
        campaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any())
      )
        .thenReturn(Future.successful(expectedResult))

      val result = getCcBinCampaigns(request)(setupContext)
      result.map { res =>
        res shouldBe expectedResult
      }
    }
    "returns flights cc campaigns in case of SingleFlight bookingFlow and ccBin is present with exactBinMatch=true" in {
      implicit val setupContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)
      val experimentData        = mock[ExperimentData]
      val userContext           = mock[UserContext]

      val flightItem     = mock[FlightRequestItem]
      val productRequest = ProductsRequest(flightRequests = Seq(flightItem))

      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleFlight)
      val request: SetupBookingRequest = SetupBookingRequest(
        paymentRequest = Some(
          PaymentRequest(
            ccToken = None,
            ccId = None,
            ccBin = Some("411111")
          )
        ),
        userContext = Some(
          UserContext(
            experimentData = Some(
              ExperimentData(
                cId = Some("2302934"),
                userId = "122332",
                deviceTypeId = "1122",
                memberId = None,
                trafficGroup = None,
                aId = None,
                serverName = None
              )
            ),
            languageId = 2,
            requestOrigin = "None",
            currency = "USD",
            nationalityId = 2
          )
        ),
        productsRequest = productRequest
      )
      val mockCcCampaignResponse = CreditCardPromotionResponse(
        id = 1230913,
        campaignId = 23492,
        siteId = Some(20234),
        displayName = "cc contactless reduce COVID-19 chance",
        bin = Some("233333"),
        discount = Discount(
          discountType = "PERCENT",
          amount = 5.0,
          currency = Some("USD")
        ),
        membershipContentId = None,
        membershipContentCmsId = None,
        productType = CrgwCampaignProductType.Flight,
        isCashback = None
      )
      val expectedResult = Seq(
        CampaignInfoInternal(
          campaignInfo = CampaignInfo(
            id = mockCcCampaignResponse.campaignId,
            cid = mockCcCampaignResponse.siteId.get,
            promotionCode = "",
            discountType = DiscountType.Percent,
            amount = 5.0,
            currency = Some("USD")
          ),
          promotionId = Some(mockCcCampaignResponse.id)
        )
      )
      when(
        campaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any())
      )
        .thenReturn(Future.successful(expectedResult))

      val expectedSearchCampaignRequestV2 =
        SearchCampaignRequestV2(
          siteId = Some(2302934),
          countryId = None,
          paymentMethodId = None,
          emailDomain = None,
          bin = Some("411111"),
          exactBinMatch = Some(true),
          ccof = None,
          ccToken = None,
          overrideCampaigns = None
        )

      val result = getCcBinCampaigns(request)(setupContext)
      result.map { res =>
        verify(campaignService).searchCampaignByCcBinV2(
          ArgumentMatchers.eq(expectedSearchCampaignRequestV2),
          ArgumentMatchers.eq(BookingFlow.SingleFlight),
          ArgumentMatchers.eq(productRequest)
        )(
          any[RequestContext],
          any[ExecutionContext]
        )
        res shouldBe expectedResult
      }
    }
    "does not return flights cc campaigns in case of SingleFlight bookingFlow and ccBin is absent with exactBinMatch=true" in {
      implicit val setupContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)

      val flightItem     = mock[FlightRequestItem]
      val productRequest = ProductsRequest(flightRequests = Seq(flightItem))

      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleFlight)
      val request: SetupBookingRequest = SetupBookingRequest(
        paymentRequest = Some(
          PaymentRequest(
            ccToken = None,
            ccId = None,
            ccBin = None
          )
        ),
        userContext = Some(
          UserContext(
            experimentData = Some(
              ExperimentData(
                cId = Some("2302934"),
                userId = "122332",
                deviceTypeId = "1122",
                memberId = None,
                trafficGroup = None,
                aId = None,
                serverName = None
              )
            ),
            languageId = 2,
            requestOrigin = "None",
            currency = "USD",
            nationalityId = 2
          )
        ),
        productsRequest = productRequest
      )
      when(
        campaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any())
      )
        .thenReturn(Future.successful(Seq.empty[CampaignInfoInternal]))

      val expectedSearchCampaignRequestV2 =
        SearchCampaignRequestV2(
          siteId = Some(2302934),
          countryId = None,
          paymentMethodId = None,
          emailDomain = None,
          bin = None,
          exactBinMatch = Some(true),
          ccof = None,
          ccToken = None,
          overrideCampaigns = None
        )

      val result = getCcBinCampaigns(request)(setupContext)
      result.map { res =>
        verify(campaignService).searchCampaignByCcBinV2(
          ArgumentMatchers.eq(expectedSearchCampaignRequestV2),
          ArgumentMatchers.eq(BookingFlow.SingleFlight),
          ArgumentMatchers.eq(productRequest)
        )(
          any[RequestContext],
          any[ExecutionContext]
        )
        res shouldBe Seq.empty
      }
    }

    "called searchCampaignByCcBinV2 when BookingFlow = Cart and contain flight related product only" in {
      implicit val setupContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)

      val flightItem     = mock[FlightRequestItem]
      val protectionItem = mock[TripProtectionRequestItem]
      val productRequest = ProductsRequest(
        flightRequests = Seq(flightItem, flightItem, flightItem),
        tripProtectionRequests = Some(Seq(protectionItem))
      )

      when(setupContext.bookingFlowType).thenReturn(BookingFlow.Cart)
      val request: SetupBookingRequest = SetupBookingRequest(
        paymentRequest = Some(
          PaymentRequest(
            ccToken = None,
            ccId = None,
            ccBin = None
          )
        ),
        userContext = Some(
          UserContext(
            experimentData = Some(
              ExperimentData(
                cId = Some("2302934"),
                userId = "122332",
                deviceTypeId = "1122",
                memberId = None,
                trafficGroup = None,
                aId = None,
                serverName = None
              )
            ),
            languageId = 2,
            requestOrigin = "None",
            currency = "USD",
            nationalityId = 2
          )
        ),
        productsRequest = productRequest
      )
      when(
        campaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any())
      )
        .thenReturn(Future.successful(Seq.empty[CampaignInfoInternal]))

      val result = getCcBinCampaigns(request)(setupContext)
      result.map { res =>
        verify(campaignService).searchCampaignByCcBinV2(
          any(),
          ArgumentMatchers.eq(BookingFlow.Cart),
          ArgumentMatchers.eq(productRequest)
        )(
          any[RequestContext],
          any[ExecutionContext]
        )
        res shouldBe Seq.empty
      }
    }

    "not called searchCampaignByCcBinV2 when BookingFlow = Cart and not contain flight related product only" in {
      implicit val setupContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)

      val flightItem     = mock[FlightRequestItem]
      val protectionItem = mock[TripProtectionRequestItem]
      val carItem        = mock[CarRequestItem]
      val productRequest = ProductsRequest(
        flightRequests = Seq(flightItem),
        tripProtectionRequests = Some(Seq(protectionItem)),
        carRequestsOpt = Some(Seq(carItem))
      )

      when(setupContext.bookingFlowType).thenReturn(BookingFlow.Cart)
      val request: SetupBookingRequest = SetupBookingRequest(
        paymentRequest = Some(
          PaymentRequest(
            ccToken = None,
            ccId = None,
            ccBin = None
          )
        ),
        userContext = Some(
          UserContext(
            experimentData = Some(
              ExperimentData(
                cId = Some("2302934"),
                userId = "122332",
                deviceTypeId = "1122",
                memberId = None,
                trafficGroup = None,
                aId = None,
                serverName = None
              )
            ),
            languageId = 2,
            requestOrigin = "None",
            currency = "USD",
            nationalityId = 2
          )
        ),
        productsRequest = productRequest
      )
      when(
        campaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any())
      )
        .thenReturn(Future.successful(Seq.empty[CampaignInfoInternal]))

      val result = getCcBinCampaigns(request)(setupContext)
      result.map { res =>
        verify(campaignService, times(0)).searchCampaignByCcBinV2(
          any(),
          ArgumentMatchers.eq(BookingFlow.Cart),
          ArgumentMatchers.eq(productRequest)
        )(
          any[RequestContext],
          any[ExecutionContext]
        )
        res shouldBe Seq.empty
      }
    }
  }
}
