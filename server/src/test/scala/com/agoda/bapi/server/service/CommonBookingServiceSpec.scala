package com.agoda.bapi.server.service

import com.agoda.bapi.agent.common.schema.BookingActionMessageTopic.{BAM_Topic_PreAuth3dsRequired, BAM_Topic_PreAuthCvcRequired}
import com.agoda.bapi.agent.common.schema.{AgentBookingActionMessage, BookingActionMessageTopic}
import com.agoda.bapi.common.config.{AutoLoginTokenConfig, Configuration, KillSwitches}
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.localization._
import com.agoda.bapi.common.message.creation
import com.agoda.bapi.common.message.creation._
import com.agoda.bapi.common.message.creation.extensions.MFAResult
import com.agoda.bapi.common.model.StatusToken.Version4
import com.agoda.bapi.common.model.WhiteLabel.WhiteLabel
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.common.model.payment.PaymentContinuation
import com.agoda.bapi.common.model.product.ProductTypeEnum
import com.agoda.bapi.common.model.swipe.PaymentRedirect
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.model.{BookingId, StatusToken, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.{MessageService, MockRequestContext}
import com.agoda.bapi.creation.model.db.{BookingActionState, EbeLiteBookingObject}
import com.agoda.bapi.creation.repository.{EbeLiteBookingRepository, ProtectionBookingRepository, WorkflowRepository}
import com.agoda.bapi.creation.service._
import com.agoda.bapi.creation.service.processing.{ActivityInfoService, AddOnsInProcessingService}
import com.agoda.bapi.server.repository._
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpb.common.{BookingType, BorBookingStatus, CrossSellReasonType, MultiProductType, WorkflowId}
import com.agoda.mpbe.state.common.enums.Gateway.Gateway
import mocks.MeasurementStubs.{logBookingCreationLogMessageBaseStub, measureStub}
import mocks.{PropertyMock, ProtectionModelMock, RequestContextMock}
import org.joda.time.DateTime
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.{any, anyLong, anyString, contains, eq => eqTo}
import org.mockito.Mockito._
import org.scalatest.funsuite.AsyncFunSuite
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfter, OptionValues}
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.{Logger => Underlying}
import scalapb.json4s.JsonFormat

import scala.concurrent.Future
import scala.io.Source.fromFile

class CommonBookingServiceSpec
    extends AsyncFunSuite
    with MockitoSugar
    with BeforeAndAfter
    with Matchers
    with OptionValues
    with PropertyMock
    with RequestContextMock
    with ProtectionModelMock {

  private val paymentMethodRepository     = mock[PaymentMethodRepository]
  private val ebeLiteBookingRepository    = mock[EbeLiteBookingRepository]
  private val workflowRepository          = mock[WorkflowRepository]
  private val flightInfoService           = mock[FlightInfoService]
  private val flightInfoServiceV2         = mock[FlightInfoServiceV2]
  private val activityInfoService         = mock[ActivityInfoService]
  private val vehicleInfoService          = mock[VehicleInfoService]
  private val protectionBookingRepository = mock[ProtectionBookingRepository]
  private val config                      = mock[Configuration]
  private val cmsContextFactory           = mock[CmsContextFactory]
  private val requestContext              = mock[RequestContext]
  private val featureAware                = mock[FeatureAware]
  private val bookingActionMessageService = mock[BookingActionMessageService]
  private val urlService                  = mock[UrlService]
  private val loggerMock                  = mock[Underlying]
  private val mockMessagingService        = mock[MessageService]
  private val mockAddOnService            = mock[AddOnsInProcessingService]
  private val mockKillSwitch              = mock[KillSwitches]

  private val actionId          = 33333
  private val itineraryId       = 56969569
  private val hotelBookingId    = 11111
  private val flightBookingId   = 62077441
  private val activityBookingId = 62077442
  private val vehicleBookingId  = 62077443
  private val getStatusToken = StatusToken(
    itineraryId = 56969569L,
    actionId = 29392L,
    productType = Set(ProductTypeEnum.Property.toString, ProductTypeEnum.Flight.toString),
    dc = "bk"
  ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
  private val getSelfServiceUrl          = (bookingId: BookingId) => s"http://agoda.com/?id=$bookingId"
  private val getJapanicanSelfServiceUrl = (bookingId: BookingId) => s"http://master.qa.notyja.com/?id=$bookingId"
  private val getRurubuSelfServiceUrl    = (bookingId: BookingId) => ""
  private val getRurubuSelfServiceUrlForOutbountInventory = (bookingId: BookingId) =>
    s"http://master.qa.notyru.com/?id=$bookingId"
  private val getJTBSelfServiceUrl = (bookingId: BookingId) => ""
  private val getStatusRequest =
    GetStatusRequest(statusToken = getStatusToken, correlationId = None, userContext = None, bookingContext = None)

  private val ebeLiteBookingProcessing = EbeLiteBookingObject(
    itineraryId = itineraryId,
    bookingId = hotelBookingId,
    bookingData = "bookingData",
    stateId = Some(CreatedBookingStatus.BookingProcessing.id)
  )

  private val ebeLiteBookingBookingConfirm = EbeLiteBookingObject(
    itineraryId = itineraryId,
    bookingId = hotelBookingId,
    bookingData = "bookingData",
    stateId = Some(CreatedBookingStatus.BookingConfirmed.id)
  )

  private val defaultPaymentResult = PaymentResult(
    continuation = PaymentContinuation(
      gatewayId = Gateway.Monex,
      gatewayInfoId = Some(1),
      transactionId = "transaction-id",
      redirectPaymentToken = Some("internal token"),
      mpiId = Some(0)
    ),
    redirect3ds = Some(
      new Payment3DSResponse(
        post3DFields = Map("key" -> "value"),
        issuerUrl = "url",
        require3DFields = Vector("field"),
        returnUrlField = "",
        referenceToken = Some(""),
        internalToken = Some(""),
        processed3DSOption = Some(0),
        bankCallback3DS1Url = Some("")
      )
    ),
    redirectPayment = None,
    topic = None
  )

  private def getMasterBookingWorkflowStateWithCustomizedAction(bookingActionJsonState: String): BookingWorkflowAction =
    BookingWorkflowAction(
      actionId = actionId,
      itineraryId = itineraryId,
      bookingType = None,
      bookingId = None,
      memberId = 0,
      actionTypeId = 1,
      correlationId = "",
      requestId = "123",
      workflowId = WorkflowId.Master.id,
      workflowStateId = 0,
      productTypeId = None,
      stateSchemaVersion = 1,
      state = bookingActionJsonState,
      storefrontId = None,
      languageId = None
    )

  private def getBookingWorkflowStateWithCustomizedAction(bookingActionJsonState: String): BookingWorkflowAction =
    BookingWorkflowAction(
      actionId = actionId,
      itineraryId = itineraryId,
      bookingType = Some(BookingType.YcsAgencyBooking.id),
      bookingId = Some(hotelBookingId),
      memberId = 0,
      actionTypeId = 1,
      correlationId = "",
      requestId = "123",
      workflowId = WorkflowId.Property.id,
      workflowStateId = 0,
      productTypeId = Some(MultiProductType.SingleProperty.id),
      stateSchemaVersion = 1,
      state = bookingActionJsonState,
      storefrontId = None,
      languageId = None
    )

  private def getBookingActionMessageWithCustomizedContent(bookingContentJson: String, topic: Int) = {
    BookingActionMessage(
      messageId = Some(123),
      actionId = 456,
      topic = topic,
      content = bookingContentJson,
      recCreatedBy = "",
      recCreatedWhen = DateTime.parse("2019-08-02T16:01")
    )
  }

  private def commonBookingsService: CommonBookingsServiceImpl =
    new CommonBookingsServiceImpl(
      ebeLiteBookingRepository,
      workflowRepository,
      protectionBookingRepository,
      flightInfoService,
      flightInfoServiceV2,
      activityInfoService,
      vehicleInfoService,
      urlService,
      config,
      cmsContextFactory,
      bookingActionMessageService,
      mockMessagingService,
      mockAddOnService,
      new StatusTokenServiceImpl,
      mockKillSwitch
    )

  before {
    reset(
      paymentMethodRepository,
      ebeLiteBookingRepository,
      workflowRepository,
      flightInfoService,
      urlService,
      config,
      cmsContextFactory,
      bookingActionMessageService,
      featureAware,
      mockAddOnService,
      vehicleInfoService,
      activityInfoService,
      mockKillSwitch
    )
    when(mockKillSwitch.enableRebookAndCancelFlow).thenReturn(true)
    when(featureAware.enableStatusTokenV6).thenReturn(false)
    when(requestContext.featureAware).thenReturn(Some(featureAware))
    when(requestContext.bookingCreationContext).thenReturn(None)

    when(bookingActionMessageService.getContinueBookingActionMessageWithTopic(any(), any()))
      .thenReturn(Future.successful(None))

    when(config.autoLoginTokenConfig).thenReturn(AutoLoginTokenConfig("testSecret", "testSalt"))
    when(mockAddOnService.getGenericAddonStatuses(any(), any())).thenReturn(Future.successful(Seq.empty))
  }

  test("get status shouldn't call getBookingActionByItineraryId when bookingActionsFromParam is exists") {

    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "bk",
      topic = None,
      version = Some(Version4)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val masterBookingAction   = getMasterBookingWorkflowStateWithCustomizedAction("")
    val propertyBookingAction = getBookingWorkflowStateWithCustomizedAction("")
    val bookingActions        = Seq(masterBookingAction, propertyBookingAction)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(true)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      )
    )
    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, bookingActions).map { response =>
      verify(workflowRepository, times(0)).getBookingActionByItineraryId(any())

      response shouldEqual expectedResponse
    }
  }

  test("get status should call getBookingActionByItineraryId when bookingActionsFromParam is not  exists") {

    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "bk",
      topic = None,
      version = Some(Version4)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val masterBookingAction   = getMasterBookingWorkflowStateWithCustomizedAction("")
    val propertyBookingAction = getBookingWorkflowStateWithCustomizedAction("")
    val bookingActions        = Seq(masterBookingAction, propertyBookingAction)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(true)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      )
    )
    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(bookingActions))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      verify(workflowRepository, times(1)).getBookingActionByItineraryId(itineraryId)

      response shouldEqual expectedResponse
    }
  }

  test("get status of 1 hotel booking when bor booking flow") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "bk",
      topic = Some(BookingActionMessageTopic.BAM_Topic_BOR.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    val borBookingResult            = BorBookingResult(status = Some(BorBookingStatus.WaitForApproved.id))
    val borBookingResultContentJson = """{"borBooking":{"status":3}}"""

    implicit val context: RequestContext = requestContext
    when(context.featureAware).thenReturn(Some(featureAware))
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingProcessing,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = None,
      manualFraudSubmitUrl = None,
      borBookingResult = Some(borBookingResult)
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingProcessing)))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(
        Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(borBookingResultContentJson)))
      )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(
        Future.successful(
          Some(
            getBookingActionMessageWithCustomizedContent(
              borBookingResultContentJson,
              BookingActionMessageTopic.BAM_Topic_BOR.value
            )
          )
        )
      )

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
      response.status.map(_.bookingStatus) shouldBe Some(BookingStatus.WaitingForApproval)
      response.status.map(_.bookingStatusCategory) shouldBe Some(BookingStatusCategory.Success)
    }
  }

  test("get status of 1 hotel booking when request for cross sell") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "bk",
      topic = Some(BookingActionMessageTopic.BAM_Topic_CrossSell.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    val crossSellMessageContentJson = "{}"

    val fraudCrossSellResult = CrossSellResult(reasonId = Some(CrossSellReasonType.fraud.id))

    val crossSellResultContentJson = """{"crossSellResult":{"reasonId":1}}"""

    implicit val context: RequestContext = requestContext
    when(context.featureAware).thenReturn(Some(featureAware))
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingProcessing,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = None,
      manualFraudSubmitUrl = None,
      crossSellResult = Some(fraudCrossSellResult)
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingProcessing)))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(
        Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(crossSellMessageContentJson)))
      )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(
        Future.successful(
          Some(
            getBookingActionMessageWithCustomizedContent(
              crossSellResultContentJson,
              BookingActionMessageTopic.BAM_Topic_CrossSell.value
            )
          )
        )
      )

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
      response.status.map(_.bookingStatus) shouldBe Some(BookingStatus.CrossSell)
      response.status.map(_.bookingStatusCategory) shouldBe Some(BookingStatusCategory.BookingError)
    }
  }

  test("get status of 1 hotel booking when request for manual fraud submit url") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "bk",
      topic = Some(BookingActionMessageTopic.BAM_Topic_ManualFraud.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    val manualFruadResultStateJson = "{}"

    val manualFraudContentJson =
      "{\"manualFraudUrl\":\"http://qa.hq.agoda.com/paymentverification/index.aspx?token=XXXXXXX\"}"

    implicit val context: RequestContext = requestContext
    when(context.featureAware).thenReturn(Some(featureAware))
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingProcessing,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = None,
      manualFraudSubmitUrl = Some("http://qa.hq.agoda.com/paymentverification/index.aspx?token=XXXXXXX")
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingProcessing)))

    when(flightInfoService.getFlightsItineraryStatus(eqTo(itineraryId.toLong)))
      .thenReturn(
        Future
          .successful(Seq.empty)
      )

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(manualFruadResultStateJson))))

    // Using for Payment result
    when(workflowRepository.getBookingActionMessage(actionId)).thenReturn(
      Future.successful(
        Seq(
          getBookingActionMessageWithCustomizedContent(
            manualFraudContentJson,
            BookingActionMessageTopic.BAM_Topic_ManualFraud.value
          )
        )
      )
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(
        Future.successful(
          Some(
            getBookingActionMessageWithCustomizedContent(
              manualFraudContentJson,
              BookingActionMessageTopic.BAM_Topic_ManualFraud.value
            )
          )
        )
      )

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
      response.status.map(_.bookingStatus) shouldBe Some(BookingStatus.Manual_Fraud)
      response.status.map(_.bookingStatusCategory) shouldBe Some(BookingStatusCategory.RequireConfirmation)
    }
  }

  private val hotelBooking = HotelBooking(
    itineraryId = itineraryId,
    bookingId = hotelBookingId,
    bookingStatus = CreatedBookingStatus.BookingProcessing,
    selfServiceUrl = getSelfServiceUrl(hotelBookingId),
    isCurrentOperation = Some(false)
  )

  implicit val context: RequestContext = requestContext
  when(context.featureAware).thenReturn(Some(featureAware))
  when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

  test("get status when await JTB 3DS") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "dev",
      whitelabelId = Some(WhiteLabel.Rurubu.id),
      topic = Some(BookingActionMessageTopic.BAM_Topic_AwaitJTB3DS.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.rurubuWhiteLabelInfo)

    val preAuth3DSContentJson =
      "{\"preAuth3dsRequired\":{\"continuation\":{\"gatewayId\":\"Gateway_GMO\",\"gatewayInfoId\":0,\"transactionId\":\"transaction-id\",\"redirectPaymentToken\":\"internal token\"},\"redirect3ds\":{\"post3DFields\":{\"key\":\"value\"},\"issuerUrl\":\"url\",\"require3DFields\":[\"field\"]}}}"

    val clientSideJavaScriptPaymentResultJson =
      "{\"clientSideJavaScriptPaymentResult\":{\"continuation\":{\"gatewayId\":\"Gateway_GMO\",\"gatewayInfoId\":0},\"tokenConfiguration\":{\"externalScriptUrl\":\"url\",\"localScriptType\":1,\"tokenParameters\":{\"MerchantID\":\"123\"}}}}"

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingProcessing)))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(
        Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(clientSideJavaScriptPaymentResultJson)))
      )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(
        Future.successful(
          Some(
            getBookingActionMessageWithCustomizedContent(
              preAuth3DSContentJson,
              BookingActionMessageTopic.BAM_Topic_AwaitJTB3DS.value
            )
          )
        )
      )

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingProcessing,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = Some(
        defaultPaymentResult.copy(
          topic = Some(BookingActionMessageTopic.BAM_Topic_AwaitJTB3DS.value),
          continuation = defaultPaymentResult.continuation.copy(gatewayId = Gateway.GMO, gatewayInfoId = Some(0))
        )
      )
    )

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
      response.status.map(_.bookingStatus) shouldBe Some(BookingStatus.Challenge_3DS)
      response.status.map(_.bookingStatusCategory) shouldBe Some(BookingStatusCategory.RequireConfirmation)
    }
  }

  test("get status when await payment token") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "dev",
      whitelabelId = Some(WhiteLabel.Jtb.id),
      topic = Some(BookingActionMessageTopic.BAM_Topic_AwaitPaymentToken.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.jtbWhiteLabelInfo)

    val clientSideJavaScriptPaymentResultJson =
      "{\"clientSideJavaScriptPaymentResult\":{\"continuation\":{\"gatewayId\":\"Gateway_GMO\",\"gatewayInfoId\":0},\"tokenConfiguration\":{\"externalScriptUrl\":\"url\",\"localScriptType\":1,\"tokenParameters\":{\"MerchantID\":\"123\"}}}}"

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingProcessing)))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(
        Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(clientSideJavaScriptPaymentResultJson)))
      )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(
      Future.successful(
        Some(
          getBookingActionMessageWithCustomizedContent(
            clientSideJavaScriptPaymentResultJson,
            BookingActionMessageTopic.BAM_Topic_AwaitPaymentToken.value
          )
        )
      )
    )

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingProcessing,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = Some(
        PaymentResult(
          PaymentContinuation(Gateway.GMO, gatewayInfoId = Some(0)),
          redirect3ds = None,
          topic = Some(9),
          clientSideJavascriptPayment = Some(
            PaymentClientSideJavascriptResponse(
              TokenConfiguration(
                "url",
                LocalScriptType(1),
                Some(
                  Map("MerchantID" -> "123")
                )
              )
            )
          )
        )
      )
    )

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
      response.status.map(_.bookingStatus) shouldBe Some(BookingStatus.Challenge_PaymentToken)
      response.status.map(_.bookingStatusCategory) shouldBe Some(BookingStatusCategory.RequireConfirmation)
    }
  }

  test("get status when await payment token and last BAM written by BCRE") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "dev",
      whitelabelId = Some(WhiteLabel.Jtb.id)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.jtbWhiteLabelInfo)

    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingProcessing)))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(
        Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction("{}")))
      )

    // Get None BAM when last BAMis created by BCRE
    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingProcessing,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = None
    )

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
      response.status.map(_.bookingStatus) shouldBe Some(BookingStatus.Processing)
      response.status.map(_.bookingStatusCategory) shouldBe Some(BookingStatusCategory.Success)
    }
  }

  test("get status of 1 hotel booking when MFA booking flow") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "bk",
      topic = None
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    val mockRedirectUrl      = ""
    val mfaResult            = MFAResult(redirectUrl = Some(mockRedirectUrl))
    val mfaResultContentJson = s"""{"requiredMfaOTP":{"redirectUrl":"$mockRedirectUrl"}}"""

    implicit val context: RequestContext = requestContext
    when(context.featureAware).thenReturn(Some(featureAware))
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedGetStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "bk",
      topic = Some(BookingActionMessageTopic.BAM_Topic_FraudOTPRequired.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingProcessing,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          statusToken = expectedGetStatusToken
        )
      ),
      paymentResult = None,
      manualFraudSubmitUrl = None,
      mfaResult = Some(mfaResult)
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingProcessing)))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(mfaResultContentJson))))

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(
        Future.successful(
          Some(
            getBookingActionMessageWithCustomizedContent(
              mfaResultContentJson,
              BookingActionMessageTopic.BAM_Topic_FraudOTPRequired.value
            )
          )
        )
      )

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
      response.status.map(_.bookingStatus) shouldBe Some(BookingStatus.MFARequired)
      response.status.map(_.bookingStatusCategory) shouldBe Some(BookingStatusCategory.RequireConfirmation)
    }
  }

  test("get status of 1 hotel, 1 flights bookings and both are confirmed") {
    val preAuthResultStateJson =
      "{\"preAuthResult\":{\"type\":\"success\",\"gatewayId\":4,\"gwTransactionId\":\"66680340\",\"gatewayInfoId\":447,\"transactionDateTime\":1567582735527,\"status\":1,\"require3ds\":{\"url\":\"www.agoda.com\",\"postFields\":{\"additionalProp1\":\"string\",\"additionalProp2\":\"string\",\"additionalProp3\":\"string\"},\"requiredFields\":[\"string1\",\"string2\",\"string3\"]},\"errors\":[],\"transactionDate\":\"2019-09-04T14:38:55.527+0700\"}}"

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights =
            Seq(FlightBooking(bookingId = flightBookingId, bookingStatus = CreatedBookingStatus.BookingConfirmed)),
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      )
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    when(flightInfoService.getFlightsItineraryStatus(eqTo(itineraryId.toLong)))
      .thenReturn(
        Future
          .successful(Seq(creation.FlightBooking(flightBookingId, CreatedBookingStatus.BookingConfirmed, None, None)))
      )

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get status of 1 hotel, 1 flights bookings and both are confirmed and showRejectedReasonForProperty is off") {
    val preAuthResultStateJson =
      "{\"preAuthResult\":{\"type\":\"success\",\"gatewayId\":4,\"gwTransactionId\":\"66680340\",\"gatewayInfoId\":447,\"transactionDateTime\":1567582735527,\"status\":1,\"require3ds\":{\"url\":\"www.agoda.com\",\"postFields\":{\"additionalProp1\":\"string\",\"additionalProp2\":\"string\",\"additionalProp3\":\"string\"},\"requiredFields\":[\"string1\",\"string2\",\"string3\"]},\"errors\":[],\"transactionDate\":\"2019-09-04T14:38:55.527+0700\"}}"

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights =
            Seq(FlightBooking(bookingId = flightBookingId, bookingStatus = CreatedBookingStatus.BookingConfirmed)),
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      )
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    when(flightInfoService.getFlightsItineraryStatus(eqTo(itineraryId.toLong)))
      .thenReturn(
        Future
          .successful(Seq(creation.FlightBooking(flightBookingId, CreatedBookingStatus.BookingConfirmed, None, None)))
      )

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get payment status of a flight booking when it is processing and does not have postback fields") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Flight.toString),
      dc = "dev",
      topic = Some(BAM_Topic_PreAuth3dsRequired.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)
    val preAuth3DSContentJson =
      "{\"preAuth3dsRequired\":{\"continuation\":{\"gatewayId\":\"Gateway_GlobalCollect\",\"gatewayInfoId\":447,\"transactionId\":\"66680340\"},\"redirect3ds\":{\"post3DFields\":{\"additionalProp1\":\"string\",\"additionalProp2\":\"string\",\"additionalProp3\":\"string\"},\"issuerUrl\":\"www.agoda.com\",\"require3DFields\":[\"string1\",\"string2\",\"string3\"]}}}"

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq.empty,
          flights =
            Seq(FlightBooking(bookingId = flightBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing)),
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = Some(
        PaymentResult(
          PaymentContinuation(Gateway.GlobalCollect, gatewayInfoId = Some(447), "66680340", Some(""), None, Some(0)),
          redirect3ds = Some(
            Payment3DSResponse(
              Map("additionalProp1" -> "string", "additionalProp2" -> "string", "additionalProp3" -> "string"),
              issuerUrl = "www.agoda.com",
              Seq("string1", "string2", "string3"),
              "",
              Some(""),
              Some(""),
              Some(0),
              Some("")
            )
          ),
          topic = Some(BAM_Topic_PreAuth3dsRequired.value)
        )
      )
    )

    when(flightInfoService.getFlightsItineraryStatus(eqTo(itineraryId.toLong)))
      .thenReturn(
        Future
          .successful(Seq(creation.FlightBooking(flightBookingId, CreatedBookingStatus.BookingProcessing, None, None)))
      )

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(preAuth3DSContentJson))))

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(Future.successful(Some(getBookingActionMessageWithCustomizedContent(preAuth3DSContentJson, 2))))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get payment status of a flight booking when it is processing and have postback fields") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Flight.toString),
      dc = "dev"
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)
    val stateWithPostBackFields =
      "{\"preAuthResult\":{\"type\":\"success\",\"gatewayId\":4,\"gwTransactionId\":\"66680340\",\"gatewayInfoId\":447,\"transactionDateTime\":1567582735527,\"status\":1,\"require3ds\":{\"url\":\"www.agoda.com\",\"postFields\":{\"additionalProp1\":\"string\",\"additionalProp2\":\"string\",\"additionalProp3\":\"string\"},\"requiredFields\":[\"string1\",\"string2\",\"string3\"]},\"errors\":[],\"transactionDate\":\"2019-09-04T14:38:55.527+0700\"},\"payment3DS\":{\"payment3DSOption\":2,\"postBackFields\":{\"MD\":\"MDString\",\"PaRes\":\"PaResString\"},\"acceptHeader\":\"string\",\"userAgent\":\"userAgent\",\"3DS\":true}}"

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq.empty,
          flights =
            Seq(FlightBooking(bookingId = flightBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing)),
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = None
    )

    when(flightInfoService.getFlightsItineraryStatus(eqTo(itineraryId.toLong)))
      .thenReturn(
        Future
          .successful(Seq(creation.FlightBooking(flightBookingId, CreatedBookingStatus.BookingProcessing, None, None)))
      )

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(stateWithPostBackFields))))
    when(workflowRepository.getBookingAction(actionId))
      .thenReturn(Future.successful(Some(getMasterBookingWorkflowStateWithCustomizedAction(stateWithPostBackFields))))

    when(workflowRepository.getBookingActionMessage(actionId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("return error response when can't get status of of both products") {
    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = false,
      errorMessage = Some(s"There's no booking status of products: Property, Flight for itinerary id: $itineraryId")
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq.empty))

    when(flightInfoService.getFlightsItineraryStatus(eqTo(itineraryId.toLong)))
      .thenReturn(Future.successful(Seq.empty))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq.empty))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get status of 1 hotel, 1 flights bookings and 1 protection and all are confirmed") {
    val protectionBookingId = 456
    val preAuthResultStateJson =
      "{\"preAuthResult\":{\"type\":\"success\",\"gatewayId\":4,\"gwTransactionId\":\"66680340\",\"gatewayInfoId\":447,\"transactionDateTime\":1567582735527,\"status\":1,\"require3ds\":{\"url\":\"www.agoda.com\",\"postFields\":{\"additionalProp1\":\"string\",\"additionalProp2\":\"string\",\"additionalProp3\":\"string\"},\"requiredFields\":[\"string1\",\"string2\",\"string3\"]},\"errors\":[],\"transactionDate\":\"2019-09-04T14:38:55.527+0700\"}}"
    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)
    val getStatusTokenWithProtection = StatusToken(
      itineraryId = 56969569L,
      actionId = 29392L,
      productType =
        Set(ProductTypeEnum.Flight.toString, ProductTypeEnum.Property.toString, ProductTypeEnum.Protection.toString),
      dc = "bk"
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val getStatusRequestWithProtection = GetStatusRequest(getStatusTokenWithProtection, None, None, None)
    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights =
            Seq(FlightBooking(bookingId = flightBookingId, bookingStatus = CreatedBookingStatus.BookingConfirmed)),
          cars = Seq.empty,
          protections = Seq(
            ProtectionBooking(
              bookingId = protectionBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              supplierPolicyId = Some("ASXASDAS"),
              protectionTypeId = 1
            )
          ),
          activities = Seq.empty,
          statusToken = getStatusTokenWithProtection
        )
      )
    )

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(flightInfoService.getFlightsItineraryStatus(eqTo(itineraryId.toLong)))
      .thenReturn(
        Future
          .successful(Seq(creation.FlightBooking(flightBookingId, CreatedBookingStatus.BookingConfirmed, None, None)))
      )

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(protectionBookingRepository.getTripProtectionBookingInfoForItinerary(itineraryId)).thenReturn(
      Future.successful(Seq(mockProtectionItineraryStatus))
    )

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )
    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequestWithProtection, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get status of 1 hotel, 1 flights bookings and when protection throws error") {
    val preAuthResultStateJson =
      "{\"preAuthResult\":{\"type\":\"success\",\"gatewayId\":4,\"gwTransactionId\":\"66680340\",\"gatewayInfoId\":447,\"transactionDateTime\":1567582735527,\"status\":1,\"require3ds\":{\"url\":\"www.agoda.com\",\"postFields\":{\"additionalProp1\":\"string\",\"additionalProp2\":\"string\",\"additionalProp3\":\"string\"},\"requiredFields\":[\"string1\",\"string2\",\"string3\"]},\"errors\":[],\"transactionDate\":\"2019-09-04T14:38:55.527+0700\"}}"
    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)
    val getStatusTokenWithProtection = StatusToken(
      itineraryId = 56969569L,
      actionId = 29392L,
      productType =
        Set(ProductTypeEnum.Property.toString, ProductTypeEnum.Flight.toString, ProductTypeEnum.Protection.toString),
      dc = "bk"
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val getStatusRequestWithProtection = GetStatusRequest(getStatusTokenWithProtection, None, None, None)
    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights =
            Seq(FlightBooking(bookingId = flightBookingId, bookingStatus = CreatedBookingStatus.BookingConfirmed)),
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusTokenWithProtection
        )
      )
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))
    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    when(flightInfoService.getFlightsItineraryStatus(eqTo(itineraryId.toLong)))
      .thenReturn(
        Future
          .successful(Seq(creation.FlightBooking(flightBookingId, CreatedBookingStatus.BookingConfirmed, None, None)))
      )

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(protectionBookingRepository.getTripProtectionBookingInfoForItinerary(itineraryId)).thenReturn(
      Future.failed(new Exception("Something went wrong"))
    )

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequestWithProtection, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get payment status of a flight booking that have booking action message") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Flight.toString),
      dc = "bk",
      topic = Some(BookingActionMessageTopic.BAM_Topic_Redirect.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)
    val preAuthResultStateJson =
      "{\"paymentInfo\":{\"method\":18,\"paymentCurrency\":\"USD\",\"paymentAmount\":10.0,\"paymentAmountUSD\":20.0,\"isRedirect\":true}}"
    val preAuthRedirectContentJson =
      "{\"redirect\":{\"paymentMethodId\":1,\"status\":\"Transaction_Status_Success\",\"gatewayId\":1,\"gatewayInfoId\":1,\"gwTransactionId\":\"abc123\",\"redirect\":{\"issuerUrl\":\"google.com\",\"postFields\":{\"keyyy\":\"valueee\"},\"additionalFields\":{\"key1\":\"value1\"}}}}"

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq.empty,
          flights =
            Seq(FlightBooking(bookingId = flightBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing)),
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = Some(
        PaymentResult(
          PaymentContinuation(Gateway.Monex, gatewayInfoId = Some(1), "abc123", Some("")),
          redirect3ds = None,
          redirectPayment = Some(
            PaymentRedirect(
              url = "google.com",
              postFields = Some(Map("keyyy" -> "valueee")),
              additionalFields = Some(Map("key1" -> "value1"))
            )
          ),
          topic = Some(BookingActionMessageTopic.BAM_Topic_Redirect.value)
        )
      )
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(flightInfoService.getFlightsItineraryStatus(eqTo(itineraryId.toLong)))
      .thenReturn(
        Future
          .successful(Seq(creation.FlightBooking(flightBookingId, CreatedBookingStatus.BookingProcessing, None, None)))
      )

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getBookingAction(actionId))
      .thenReturn(Future.successful(Some(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getBookingActionMessage(actionId)).thenReturn(
      Future.successful(Seq(getBookingActionMessageWithCustomizedContent(preAuthRedirectContentJson, 1)))
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(Future.successful(Some(getBookingActionMessageWithCustomizedContent(preAuthRedirectContentJson, 1))))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get payment status of a flight booking that doesnt have booking action message") {
    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Flight.toString),
        dc = "dev"
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)
    val preAuthResultStateJson =
      "{\"paymentInfo\":{\"method\":18,\"paymentCurrency\":\"USD\",\"paymentAmount\":10.0,\"paymentAmountUSD\":20.0,\"isRedirect\":true}}"
    val preAuthRedirectContentJson =
      "{\"redirect\":{\"paymentMethodId\":1,\"status\":\"Transaction_Status_Success\",\"gatewayId\":1,\"gatewayInfoId\":1,\"gwTransactionId\":\"abc123\",\"redirect\":{\"issuerUrl\":\"google.com\",\"postFields\":{\"keyyy\":\"valueee\"}}}}"
    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq.empty,
          flights =
            Seq(FlightBooking(bookingId = flightBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing)),
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = None
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(flightInfoService.getFlightsItineraryStatus(eqTo(itineraryId.toLong)))
      .thenReturn(
        Future
          .successful(Seq(creation.FlightBooking(flightBookingId, CreatedBookingStatus.BookingProcessing, None, None)))
      )

    when(workflowRepository.getBookingAction(actionId))
      .thenReturn(Future.successful(Some(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getBookingActionMessage(actionId)).thenReturn(
      Future.successful(
        Seq(
          getBookingActionMessageWithCustomizedContent(preAuthRedirectContentJson, 1).copy(recCreatedBy = "Bapi-UUID")
        )
      )
    )

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test(
    "get itinerary status of a multi hotel booking when only one booking is for the current operation"
  ) {

    val ebeLiteBookingReject = EbeLiteBookingObject(
      itineraryId = itineraryId,
      bookingId = hotelBookingId,
      bookingData = "bookingData",
      stateId = Some(CreatedBookingStatus.BookingConfirmed.id)
    )

    val ebeLiteBookingReject2 = EbeLiteBookingObject(
      itineraryId = itineraryId,
      bookingId = hotelBookingId + 2,
      bookingData = "bookingData",
      stateId = Some(CreatedBookingStatus.BookingConfirmed.id)
    )
    val operationId               = 1337
    val provisioningRejectedState = "{\"rejectRootCause\": 11201}"
    val masterBookingAction =
      getMasterBookingWorkflowStateWithCustomizedAction("{}").copy(operationId = Some(operationId))
    val propertyBookingAction =
      getBookingWorkflowStateWithCustomizedAction(provisioningRejectedState).copy(operationId = Some(operationId))
    val propertyBookingAction2 =
      getBookingWorkflowStateWithCustomizedAction(provisioningRejectedState).copy(
        bookingId = Some(hotelBookingId + 2),
        operationId = Some(operationId - 3)
      )

    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Property.toString),
        dc = "dev",
        operationId = Some(operationId)
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)
    when(context.featureAware).thenReturn(Some(featureAware))
    when(featureAware.enableStatusTokenV6).thenReturn(true)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              rejectReason = None,
              isCurrentOperation = Some(true)
            ),
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId + 2,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId + 2),
              rejectReason = None,
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          priceFreezes = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = None
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingReject, ebeLiteBookingReject2)))

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(masterBookingAction, propertyBookingAction, propertyBookingAction2)))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
      response.status.map(_.bookingStatus) shouldBe Some(BookingStatus.BookingCreated)
      response.status.map(_.bookingStatusCategory) shouldBe Some(BookingStatusCategory.Success)
    }
  }

  test(
    "get itinerary status of a multi hotel booking was rejected when provisioning fail with rejectRootCause: 11201"
  ) {

    val ebeLiteBookingReject = EbeLiteBookingObject(
      itineraryId = itineraryId,
      bookingId = hotelBookingId,
      bookingData = "bookingData",
      stateId = Some(CreatedBookingStatus.BookingRejected.id)
    )

    val ebeLiteBookingReject2 = EbeLiteBookingObject(
      itineraryId = itineraryId,
      bookingId = hotelBookingId + 2,
      bookingData = "bookingData",
      stateId = Some(CreatedBookingStatus.BookingRejected.id)
    )
    val operationId               = 1337
    val provisioningRejectedState = "{\"rejectRootCause\": 11201}"
    val masterBookingAction =
      getMasterBookingWorkflowStateWithCustomizedAction("{}").copy(operationId = Some(operationId))
    val propertyBookingAction =
      getBookingWorkflowStateWithCustomizedAction(provisioningRejectedState).copy(operationId = Some(operationId))
    val propertyBookingAction2 =
      getBookingWorkflowStateWithCustomizedAction(provisioningRejectedState).copy(
        bookingId = Some(hotelBookingId + 2),
        operationId = Some(operationId + 1)
      )

    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Property.toString),
        dc = "dev",
        operationId = Some(operationId)
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)
    when(context.featureAware).thenReturn(Some(featureAware))
    when(featureAware.enableStatusTokenV6).thenReturn(true)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingRejected,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              rejectReason = Some(
                RejectedReason(
                  code = ErrorCode.ProvisioningCreateAllotmentRejected.id,
                  message = "",
                  subErrorCode = Some(ErrorCode.ProvisioningCreateAllotmentRejected.id)
                )
              ),
              isCurrentOperation = Some(true)
            ),
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId + 2,
              bookingStatus = CreatedBookingStatus.BookingRejected,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId + 2),
              rejectReason = None,
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = None
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingReject, ebeLiteBookingReject2)))

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(masterBookingAction, propertyBookingAction, propertyBookingAction2)))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
      response.status.map(_.bookingStatus) shouldBe Some(BookingStatus.NoAvailability)
      response.status.map(_.bookingStatusCategory) shouldBe Some(BookingStatusCategory.BookingError)
    }
  }

  test(
    "get itinerary status of a multi hotel booking was rejected when provisioning fail with rejectRootCause: 11211 (Instant book)"
  ) {

    val ebeLiteBookingReject = EbeLiteBookingObject(
      itineraryId = itineraryId,
      bookingId = hotelBookingId,
      bookingData = "bookingData",
      stateId = Some(CreatedBookingStatus.BookingRejected.id)
    )

    val ebeLiteBookingReject2 = EbeLiteBookingObject(
      itineraryId = itineraryId,
      bookingId = hotelBookingId + 2,
      bookingData = "bookingData",
      stateId = Some(CreatedBookingStatus.BookingRejected.id)
    )

    val operationId               = 1337
    val provisioningRejectedState = "{\"rejectRootCause\": 11211}"
    val masterBookingAction =
      getMasterBookingWorkflowStateWithCustomizedAction("{}").copy(operationId = Some(operationId))
    val propertyBookingAction =
      getBookingWorkflowStateWithCustomizedAction(provisioningRejectedState).copy(operationId = Some(operationId))
    val propertyBookingAction2 =
      getBookingWorkflowStateWithCustomizedAction(provisioningRejectedState).copy(
        bookingId = Some(hotelBookingId + 2),
        operationId = Some(operationId + 2)
      )

    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Property.toString),
        dc = "dev",
        operationId = Some(operationId)
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)
    when(context.featureAware).thenReturn(Some(featureAware))
    when(featureAware.enableStatusTokenV6).thenReturn(true)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingRejected,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              rejectReason = Some(
                RejectedReason(
                  code = ErrorCode.ProvisioningRejectAllotment.id,
                  message = "",
                  subErrorCode = Some(ErrorCode.ProvisioningRejectAllotment.id)
                )
              ),
              isCurrentOperation = Some(true)
            ),
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId + 2,
              bookingStatus = CreatedBookingStatus.BookingRejected,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId + 2),
              rejectReason = None,
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = None
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingReject, ebeLiteBookingReject2)))

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(masterBookingAction, propertyBookingAction, propertyBookingAction2)))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
      response.status.map(_.bookingStatus) shouldBe Some(BookingStatus.NoAvailability)
      response.status.map(_.bookingStatusCategory) shouldBe Some(BookingStatusCategory.BookingError)
    }
  }

  test("get itinerary status of a multi hotel booking was rejected when preauth fail") {

    val ebeLiteBookingReject = EbeLiteBookingObject(
      itineraryId = itineraryId,
      bookingId = hotelBookingId,
      bookingData = "bookingData",
      stateId = Some(CreatedBookingStatus.BookingRejected.id)
    )

    val ebeLiteBookingReject2 = EbeLiteBookingObject(
      itineraryId = itineraryId,
      bookingId = hotelBookingId + 2,
      bookingData = "bookingData",
      stateId = Some(CreatedBookingStatus.BookingRejected.id)
    )
    val operationId          = 1337
    val preAuthRejectedState = "{\"rejectRootCause\": 419}"
    val masterBookingAction =
      getMasterBookingWorkflowStateWithCustomizedAction(preAuthRejectedState).copy(operationId = Some(operationId))
    val propertyBookingAction = getBookingWorkflowStateWithCustomizedAction("").copy(operationId = Some(operationId))
    val propertyBookingAction2 =
      getBookingWorkflowStateWithCustomizedAction("").copy(
        bookingId = Some(hotelBookingId + 2),
        operationId = Some(operationId + 2)
      )

    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Property.toString),
        dc = "dev",
        operationId = Some(operationId)
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)
    when(context.featureAware).thenReturn(Some(featureAware))
    when(featureAware.enableStatusTokenV6).thenReturn(true)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingRejected,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              rejectReason = Some(
                RejectedReason(
                  code = ErrorCode.PreAuthorizeFail.id,
                  message = "",
                  subErrorCode = Some(ErrorCode.PreAuthorizeFail.id)
                )
              ),
              isCurrentOperation = Some(true)
            ),
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId + 2,
              bookingStatus = CreatedBookingStatus.BookingRejected,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId + 2),
              rejectReason = Some(
                RejectedReason(
                  code = ErrorCode.PreAuthorizeFail.id,
                  message = "",
                  subErrorCode = Some(ErrorCode.PreAuthorizeFail.id)
                )
              ),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = None
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))
    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingReject, ebeLiteBookingReject2)))

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(masterBookingAction, propertyBookingAction, propertyBookingAction2)))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
      response.status.map(_.bookingStatus) shouldBe Some(BookingStatus.IncompleteTransaction)
      response.status.map(_.bookingStatusCategory) shouldBe Some(BookingStatusCategory.BookingError)
    }
  }

  test("MPBE-5876 is A - get itinerary status of a vehicle booking successfully") {
    executeVehicleScenarioVariantForGetStatusDataFromBkgDb('A')
  }

  test("MPBE-5876 is B - get itinerary status of a vehicle booking successfully") {
    executeVehicleScenarioVariantForGetStatusDataFromBkgDb('B')
  }

  test("MPBE-5876 is Z - get itinerary status of a vehicle booking successfully") {
    executeVehicleScenarioVariantForGetStatusDataFromBkgDb('Z')
  }

  private def executeActivityScenarioVariantForGetStatusDataFromBkgDb(variant: Char) = {
    val isB = variant.toLower.equals('b')
    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Activity.toString),
        dc = "dev"
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest                 = GetStatusRequest(getStatusToken, None, None, None)
    implicit val context: RequestContext = requestContext
    val masterBookingAction              = getMasterBookingWorkflowStateWithCustomizedAction("{}")

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq.empty,
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities =
            Seq(ActivityBooking(bookingId = activityBookingId, bookingStatus = CreatedBookingStatus.BookingConfirmed)),
          statusToken = getStatusToken
        )
      ),
      paymentResult = None
    )

    when(featureAware.enableGetStatusDataFromBkgDb).thenReturn(isB)
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)
    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(
        Future.successful(Seq(masterBookingAction))
      )
    when(activityInfoService.getActivityItineraryStatus(any(), any())).thenReturn(
      Future.successful(
        Seq(ActivityBooking(bookingId = activityBookingId, bookingStatus = CreatedBookingStatus.BookingConfirmed))
      )
    )
    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      verify(activityInfoService).getActivityItineraryStatus(itineraryId, useBkgDb = isB)
      response shouldEqual expectedResponse
    }
  }

  test("MPBE-5876 is A - get itinerary status of an activity booking successfully") {
    executeActivityScenarioVariantForGetStatusDataFromBkgDb('A')
  }

  test("MPBE-5876 is B - get itinerary status of an activity booking successfully") {
    executeActivityScenarioVariantForGetStatusDataFromBkgDb('B')
  }

  test("MPBE-5876 is Z - get itinerary status of an activity booking successfully") {
    executeActivityScenarioVariantForGetStatusDataFromBkgDb('Z')
  }

  test("get payment status of a property booking that does have booking action message") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "bk",
      topic = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3dsRequired.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)
    val preAuthResultStateJson =
      "{\"preAuthResult\":{\"type\":\"success\",\"gatewayId\":4,\"gwTransactionId\":\"66680340\",\"gatewayInfoId\":447,\"transactionDateTime\":1567582735527,\"status\":1,\"require3ds\":{\"url\":\"www.agoda.com\",\"postFields\":{\"additionalProp1\":\"string\",\"additionalProp2\":\"string\",\"additionalProp3\":\"string\"},\"requiredFields\":[\"string1\",\"string2\",\"string3\"]},\"errors\":[],\"transactionDate\":\"2019-09-04T14:38:55.527+0700\"}}"
    val preAuth3DSContentJson =
      "{\"preAuth3dsRequired\":{\"continuation\":{\"gatewayId\":\"Gateway_Monex\",\"gatewayInfoId\":1,\"transactionId\":\"transaction-id\",\"redirectPaymentToken\":\"internal token\"},\"redirect3ds\":{\"post3DFields\":{\"key\":\"value\"},\"issuerUrl\":\"url\",\"require3DFields\":[\"field\"]}}}"

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingProcessing,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = Some(
        PaymentResult(
          PaymentContinuation(
            Gateway.Monex,
            gatewayInfoId = Some(1),
            "transaction-id",
            Some("internal token"),
            mpiId = Some(0)
          ),
          redirect3ds = Some(
            new Payment3DSResponse(
              Map("key" -> "value"),
              "url",
              Vector("field"),
              "",
              Some(""),
              Some(""),
              Some(0),
              Some("")
            )
          ),
          redirectPayment = None,
          topic = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3dsRequired.value)
        )
      )
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingProcessing)))

    when(workflowRepository.getBookingAction(actionId))
      .thenReturn(Future.successful(Some(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getBookingActionMessage(actionId)).thenReturn(
      Future.successful(Seq(getBookingActionMessageWithCustomizedContent(preAuth3DSContentJson, 2)))
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(Future.successful(Some(getBookingActionMessageWithCustomizedContent(preAuth3DSContentJson, 2))))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get payment status as required cvv for a property booking that does have booking action message") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "bk",
      topic = Some(BAM_Topic_PreAuthCvcRequired.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)
    val preAuthResultStateJson =
      "{\"preAuthResult\":{\"type\":\"success\",\"gatewayId\":4,\"gwTransactionId\":\"66680340\",\"gatewayInfoId\":447,\"transactionDateTime\":1567582735527,\"status\":1,\"require3ds\":{\"url\":\"www.agoda.com\",\"postFields\":{\"additionalProp1\":\"string\",\"additionalProp2\":\"string\",\"additionalProp3\":\"string\"},\"requiredFields\":[\"string1\",\"string2\",\"string3\"]},\"errors\":[],\"transactionDate\":\"2019-09-04T14:38:55.527+0700\"}}"
    val preAuthCvvMessageContentJson = "{}"

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingProcessing,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = Some(
        PaymentResult(
          PaymentContinuation(Gateway.None),
          redirect3ds = None,
          topic = Some(BAM_Topic_PreAuthCvcRequired.value),
          requireCvv = Some(true)
        )
      )
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingProcessing)))

    when(workflowRepository.getBookingAction(actionId))
      .thenReturn(Future.successful(Some(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getBookingActionMessage(actionId)).thenReturn(
      Future.successful(
        Seq(
          getBookingActionMessageWithCustomizedContent(preAuthCvvMessageContentJson, BAM_Topic_PreAuthCvcRequired.value)
        )
      )
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(
        Future.successful(
          Some(
            getBookingActionMessageWithCustomizedContent(
              preAuthCvvMessageContentJson,
              BAM_Topic_PreAuthCvcRequired.value
            )
          )
        )
      )

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get payment status as None for a property booking without booking action message and default topic") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "bk",
      topic = Some(BookingActionMessageTopic.BAM_Topic_Unknown.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)
    val preAuthResultStateJson =
      "{\"preAuthResult\":{\"type\":\"success\",\"gatewayId\":4,\"gwTransactionId\":\"66680340\",\"gatewayInfoId\":447,\"transactionDateTime\":1567582735527,\"status\":1,\"require3ds\":{\"url\":\"www.agoda.com\",\"postFields\":{\"additionalProp1\":\"string\",\"additionalProp2\":\"string\",\"additionalProp3\":\"string\"},\"requiredFields\":[\"string1\",\"string2\",\"string3\"]},\"errors\":[],\"transactionDate\":\"2019-09-04T14:38:55.527+0700\"}}"

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingProcessing,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = None
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingProcessing)))

    when(workflowRepository.getBookingAction(actionId))
      .thenReturn(Future.successful(Some(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))
    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getMasterBookingActionByItineraryId(itineraryId)).thenReturn(
      Future.successful(Some(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson)))
    )

    when(workflowRepository.getBookingActionMessage(actionId)).thenReturn(Future.successful(Seq()))

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get payment status as None for a property booking without booking action message and no topic") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "bk",
      topic = None
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)
    val preAuthResultStateJson =
      "{\"preAuthResult\":{\"type\":\"success\",\"gatewayId\":4,\"gwTransactionId\":\"66680340\",\"gatewayInfoId\":447,\"transactionDateTime\":1567582735527,\"status\":1,\"require3ds\":{\"url\":\"www.agoda.com\",\"postFields\":{\"additionalProp1\":\"string\",\"additionalProp2\":\"string\",\"additionalProp3\":\"string\"},\"requiredFields\":[\"string1\",\"string2\",\"string3\"]},\"errors\":[],\"transactionDate\":\"2019-09-04T14:38:55.527+0700\"}}"

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingProcessing,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = None
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingProcessing)))

    when(workflowRepository.getBookingAction(actionId))
      .thenReturn(Future.successful(Some(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getMasterBookingActionByItineraryId(itineraryId)).thenReturn(
      Future.successful(Some(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson)))
    )

    when(workflowRepository.getBookingActionMessage(actionId)).thenReturn(Future.successful(Seq()))

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get payment status as 3ds2 device fingerprint for a property booking with booking action message") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "bk",
      topic = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3ds2Required.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)
    val preAuthResultStateJson =
      """{"preAuthResult":{"type":"success","gatewayId":8,"gwTransactionId":"66680340","gatewayInfoId":443,"transactionDateTime":1567582735527,"status":1,"require3ds":{"url":"www.agoda.com","postFields":{"additionalProp1":"string","additionalProp2":"string","additionalProp3":"string"},"requiredFields":["string1","string2","string3"]},"errors":[{"id": 600, "message": "Required device fingerprint."}],"transactionDate":"2019-09-04T14:38:55.527+0700"}}"""
    val preAuth3DSContentJson =
      """{"preAuth3ds2Required":{"continuation":{"gatewayId":"Gateway_Adyen","gatewayInfoId":222,"transactionId":"transaction-id","redirectPaymentToken":"internal token"},"redirect3ds":{"post3DFields":{"key":"value"},"issuerUrl":"url","require3DFields":["field"]},"payment3ds2Type":2520}}"""
    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingProcessing,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = Some(
        PaymentResult(
          PaymentContinuation(
            Gateway.Adyen,
            gatewayInfoId = Some(222),
            "transaction-id",
            Some("internal token"),
            mpiId = Some(0)
          ),
          redirect3ds = Some(
            new Payment3DSResponse(
              Map("key" -> "value"),
              "url",
              Vector("field"),
              "",
              Some(""),
              Some(""),
              Some(0),
              Some("")
            )
          ),
          redirectPayment = None,
          topic = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3ds2Required.value),
          payment3ds2Type = Some(2520)
        )
      )
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingProcessing)))

    when(workflowRepository.getBookingAction(actionId))
      .thenReturn(Future.successful(Some(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getMasterBookingActionByItineraryId(itineraryId)).thenReturn(
      Future.successful(Some(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson)))
    )

    when(workflowRepository.getBookingActionMessage(actionId)).thenReturn(
      Future.successful(Seq(getBookingActionMessageWithCustomizedContent(preAuth3DSContentJson, 5)))
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(Future.successful(Some(getBookingActionMessageWithCustomizedContent(preAuth3DSContentJson, 5))))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get payment status as 3ds2 challenge for a property booking with booking action message") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "bk",
      topic = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3ds2Required.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)
    val preAuthResultStateJson =
      """{"preAuthResult":{"type":"success","gatewayId":8,"gwTransactionId":"66680340","gatewayInfoId":443,"transactionDateTime":1567582735527,"status":1,"require3ds":{"url":"www.agoda.com","postFields":{"additionalProp1":"string","additionalProp2":"string","additionalProp3":"string"},"requiredFields":["string1","string2","string3"]},"errors":[{"id":2521,"message":"Required challenge."}],"transactionDate":"2019-09-04T14:38:55.527+0700","internalToken":"randomInternalToken"}}"""
    val preAuth3DSContentJson =
      """{"preAuth3ds2Required":{"continuation":{"gatewayId":"Gateway_Adyen","gatewayInfoId":222,"transactionId":"transaction-id","redirectPaymentToken":"internal token"},"redirect3ds":{"post3DFields":{"key":"value"},"issuerUrl":"url","require3DFields":["field"]},"payment3ds2Type":2521}}"""

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingProcessing,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = Some(
        PaymentResult(
          PaymentContinuation(
            Gateway.Adyen,
            gatewayInfoId = Some(222),
            "transaction-id",
            Some("internal token"),
            mpiId = Some(0)
          ),
          redirect3ds = Some(
            new Payment3DSResponse(
              Map("key" -> "value"),
              "url",
              Vector("field"),
              "",
              Some(""),
              Some(""),
              Some(0),
              Some("")
            )
          ),
          redirectPayment = None,
          topic = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3ds2Required.value),
          payment3ds2Type = Some(2521)
        )
      )
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingProcessing)))

    when(workflowRepository.getBookingAction(actionId))
      .thenReturn(Future.successful(Some(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getMasterBookingActionByItineraryId(itineraryId)).thenReturn(
      Future.successful(Some(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson)))
    )

    when(workflowRepository.getBookingActionMessage(actionId)).thenReturn(
      Future.successful(Seq(getBookingActionMessageWithCustomizedContent(preAuth3DSContentJson, 5)))
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(Future.successful(Some(getBookingActionMessageWithCustomizedContent(preAuth3DSContentJson, 5))))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get payment status as 3ds2 unknown for a property booking with booking action message unexpected case") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "bk",
      topic = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3ds2Required.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)
    val preAuthResultStateJson =
      """{"preAuthResult":{"type":"success","gatewayId":8,"gwTransactionId":"66680340","gatewayInfoId":443,"transactionDateTime":1567582735527,"status":1,"require3ds":{"url":"www.agoda.com","postFields":{"additionalProp1":"string","additionalProp2":"string","additionalProp3":"string"},"requiredFields":["string1","string2","string3"]},"errors":[{"id":12,"message":"Random error message"}],"transactionDate":"2019-09-04T14:38:55.527+0700","internalToken":"randomInternalToken"}}"""
    val preAuth3DSContentJson =
      """{"preAuth3ds2Required":{"continuation":{"gatewayId":"Gateway_Adyen","gatewayInfoId":222,"transactionId":"transaction-id","redirectPaymentToken":"internal token"},"redirect3ds":{"post3DFields":{"key":"value"},"issuerUrl":"url","require3DFields":["field"]},"payment3ds2Type":0}}"""

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingProcessing,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = Some(
        PaymentResult(
          PaymentContinuation(
            Gateway.Adyen,
            gatewayInfoId = Some(222),
            "transaction-id",
            Some("internal token"),
            mpiId = Some(0)
          ),
          redirect3ds = Some(
            new Payment3DSResponse(
              Map("key" -> "value"),
              "url",
              Vector("field"),
              "",
              Some(""),
              Some(""),
              Some(0),
              Some("")
            )
          ),
          redirectPayment = None,
          topic = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3ds2Required.value),
          payment3ds2Type = None
        )
      )
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingProcessing)))

    when(workflowRepository.getBookingAction(actionId))
      .thenReturn(Future.successful(Some(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    when(workflowRepository.getBookingActionMessage(actionId)).thenReturn(
      Future.successful(Seq(getBookingActionMessageWithCustomizedContent(preAuth3DSContentJson, 5)))
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(Future.successful(Some(getBookingActionMessageWithCustomizedContent(preAuth3DSContentJson, 5))))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get payment status of 3ds activity booking that does have booking action message") {
    val activityBookingId = 62077443
    val itineraryId       = 62077443
    val actionId          = 1067754
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Activity.toString),
      dc = "bk",
      topic = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3dsRequired.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)
    val preAuth3DSContentJson =
      "{\"preAuth3dsRequired\":{\"continuation\":{\"gatewayId\":\"Gateway_Monex\",\"gatewayInfoId\":1,\"transactionId\":\"transaction-id\",\"redirectPaymentToken\":\"internal token\"},\"redirect3ds\":{\"post3DFields\":{\"key\":\"value\"},\"issuerUrl\":\"url\",\"require3DFields\":[\"field\"]}}}"

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq.empty,
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities =
            Seq(ActivityBooking(bookingId = activityBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing)),
          statusToken = getStatusToken
        )
      ),
      paymentResult = Some(
        defaultPaymentResult.copy(
          topic = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3dsRequired.value)
        )
      )
    )
    val masterBookingAction = getMasterBookingWorkflowStateWithCustomizedAction("{}")
    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(
        Future.successful(Seq(masterBookingAction))
      )

    when(activityInfoService.getActivityItineraryStatus(any(), any())).thenReturn(
      Future.successful(
        Seq(ActivityBooking(bookingId = activityBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing))
      )
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(Future.successful(Some(getBookingActionMessageWithCustomizedContent(preAuth3DSContentJson, 2))))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get payment status as 3ds2 device fingerprint for a activity booking with booking action message") {
    val activityBookingId = 62077443
    val itineraryId       = 62077443
    val actionId          = 1067754
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Activity.toString),
      dc = "bk",
      topic = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3ds2Required.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)
    val preAuth3DSContentJson =
      """{"preAuth3ds2Required":{"continuation":{"gatewayId":"Gateway_Adyen","gatewayInfoId":222,"transactionId":"transaction-id","redirectPaymentToken":"internal token"},"redirect3ds":{"post3DFields":{"key":"value"},"issuerUrl":"url","require3DFields":["field"]},"payment3ds2Type":2520}}"""
    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId,
          bookings = Seq.empty,
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities =
            Seq(ActivityBooking(bookingId = activityBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing)),
          statusToken = getStatusToken
        )
      ),
      paymentResult = Some(
        defaultPaymentResult.copy(
          continuation = defaultPaymentResult.continuation.copy(gatewayId = Gateway.Adyen, gatewayInfoId = Some(222)),
          topic = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3ds2Required.value),
          payment3ds2Type = Some(2520)
        )
      )
    )

    val masterBookingAction = getMasterBookingWorkflowStateWithCustomizedAction("{}")
    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(
        Future.successful(Seq(masterBookingAction))
      )

    when(activityInfoService.getActivityItineraryStatus(any(), any())).thenReturn(
      Future.successful(
        Seq(ActivityBooking(bookingId = activityBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing))
      )
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(Future.successful(Some(getBookingActionMessageWithCustomizedContent(preAuth3DSContentJson, 5))))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
      response.status shouldEqual BookingServerStatus.Challenge_3DS2_FingerPrint
    }
  }

  test("get payment status as 3ds2 challenge for a activity booking with booking action message") {
    val activityBookingId = 62077443
    val itineraryId       = 62077443
    val actionId          = 1067754
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Activity.toString),
      dc = "bk",
      topic = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3ds2Required.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)
    val preAuth3DSContentJson =
      """{"preAuth3ds2Required":{"continuation":{"gatewayId":"Gateway_Adyen","gatewayInfoId":222,"transactionId":"transaction-id","redirectPaymentToken":"internal token"},"redirect3ds":{"post3DFields":{"key":"value"},"issuerUrl":"url","require3DFields":["field"]},"payment3ds2Type":2521}}"""

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq.empty,
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities =
            Seq(ActivityBooking(bookingId = activityBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing)),
          statusToken = getStatusToken
        )
      ),
      paymentResult = Some(
        defaultPaymentResult.copy(
          continuation = defaultPaymentResult.continuation.copy(gatewayId = Gateway.Adyen, gatewayInfoId = Some(222)),
          topic = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3ds2Required.value),
          payment3ds2Type = Some(2521)
        )
      )
    )

    val masterBookingAction = getMasterBookingWorkflowStateWithCustomizedAction("{}")
    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(
        Future.successful(Seq(masterBookingAction))
      )

    when(activityInfoService.getActivityItineraryStatus(any(), any())).thenReturn(
      Future.successful(
        Seq(ActivityBooking(bookingId = activityBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing))
      )
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(Future.successful(Some(getBookingActionMessageWithCustomizedContent(preAuth3DSContentJson, 5))))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
      response.status shouldEqual BookingServerStatus.Challenge_3DS2
    }
  }

  test("get payment status as 3ds2 unknown for a activity booking with booking action message unexpected case") {
    val activityBookingId = 62077443
    val itineraryId       = 62077443
    val actionId          = 1067754
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Activity.toString),
      dc = "bk",
      topic = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3ds2Required.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)
    val preAuth3DSContentJson =
      """{"preAuth3ds2Required":{"continuation":{"gatewayId":"Gateway_Adyen","gatewayInfoId":222,"transactionId":"transaction-id","redirectPaymentToken":"internal token"},"redirect3ds":{"post3DFields":{"key":"value"},"issuerUrl":"url","require3DFields":["field"]},"payment3ds2Type":0}}"""

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq.empty,
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities =
            Seq(ActivityBooking(bookingId = activityBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing)),
          statusToken = getStatusToken
        )
      ),
      paymentResult = Some(
        defaultPaymentResult.copy(
          continuation = defaultPaymentResult.continuation.copy(gatewayId = Gateway.Adyen, gatewayInfoId = Some(222)),
          topic = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3ds2Required.value)
        )
      )
    )

    val masterBookingAction = getMasterBookingWorkflowStateWithCustomizedAction("{}")
    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(
        Future.successful(Seq(masterBookingAction))
      )

    when(activityInfoService.getActivityItineraryStatus(any(), any())).thenReturn(
      Future.successful(
        Seq(ActivityBooking(bookingId = activityBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing))
      )
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(Future.successful(Some(getBookingActionMessageWithCustomizedContent(preAuth3DSContentJson, 5))))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
      response.status shouldEqual BookingServerStatus.Challenge3ds
    }
  }

  test("get payment status ") {
    val getStatusToken = StatusToken(
      itineraryId = itineraryId,
      actionId = actionId,
      productType = Set(ProductTypeEnum.Property.toString),
      dc = "bk",
      topic = Some(BookingActionMessageTopic.BAM_Topic_Redirect.value)
    ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingProcessing,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      paymentResult = Some(
        PaymentResult(
          PaymentContinuation(Gateway.None),
          redirect3ds = None,
          topic = Some(1),
          thirdPartyStatus = Some(ThirdPartyStatus.Cancel)
        )
      )
    )

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingProcessing)))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction("{}"))))

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    when(
      bookingActionMessageService.getContinueBookingActionMessageWithTopic(
        any(),
        eqTo(BookingActionMessageTopic.BAM_Topic_Redirect)
      )
    ).thenReturn(
      Future.successful(
        Some(
          getBookingActionMessageWithCustomizedContent(
            "{\"continueFeedback\":{\"thirdPartyStatus\":3}}",
            BookingActionMessageTopic.BAM_Topic_Redirect.value
          )
        )
      )
    )

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get correct selfServiceUrl when booking with Japanican") {
    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Property.toString),
        dc = "dev",
        whitelabelId = Some(WhiteLabel.Japanican.id)
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest                 = GetStatusRequest(getStatusToken, None, None, None)
    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.japanicanWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getJapanicanSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      )
    )
    when(urlService.getSelfServiceURLs(WhiteLabel.Japanican, isDomainIncluded = false)(context))
      .thenReturn(Future.successful(getJapanicanSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction("{}"))))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      verify(urlService, times(1))
        .getSelfServiceURLs(eqTo(WhiteLabel.Japanican), eqTo(false), any())(any())
      response shouldEqual expectedResponse
    }
  }

  test("get correct selfServiceUrl when booking with Rurubu") {
    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Property.toString),
        dc = "dev",
        whitelabelId = Some(WhiteLabel.Rurubu.id)
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.rurubuWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getJTBSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      )
    )
    when(urlService.getSelfServiceURLs(WhiteLabel.Rurubu, isDomainIncluded = false))
      .thenReturn(Future.successful(getRurubuSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction("{}"))))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      verify(urlService, times(1))
        .getSelfServiceURLs(eqTo(WhiteLabel.Rurubu), eqTo(false), any())(any())
      response shouldEqual expectedResponse
    }
  }

  test("get correct selfServiceUrl when booking with Rurubu with experiment WLBE-533 enabled") {
    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Property.toString),
        dc = "dev",
        whitelabelId = Some(WhiteLabel.Rurubu.id)
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)
    val mockFeatureAware = mock[FeatureAware]
    when(mockFeatureAware.isRemoveFenceSelfServiceURLsForRurubu).thenReturn(true)

    implicit val context: RequestContext = requestContext
    context.copy(
      featureAware = Some(mockFeatureAware),
      whiteLabelInfo = MockRequestContext.rurubuWhiteLabelInfo
    )
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.rurubuWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getRurubuSelfServiceUrlForOutbountInventory(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      )
    )
    when(urlService.getSelfServiceURLs(WhiteLabel.Rurubu, isDomainIncluded = false))
      .thenReturn(Future.successful(getRurubuSelfServiceUrlForOutbountInventory))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction("{}"))))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty)(context).map { response =>
      verify(urlService, times(1))
        .getSelfServiceURLs(eqTo(WhiteLabel.Rurubu), eqTo(false), any())(any())
      response shouldEqual expectedResponse
    }
  }

  test("get correct selfServiceUrl when booking with JTB") {
    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Property.toString),
        dc = "dev",
        whitelabelId = Some(WhiteLabel.Jtb.id)
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.jtbWhiteLabelInfo)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getJTBSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      )
    )
    when(urlService.getSelfServiceURLs(WhiteLabel.Jtb, isDomainIncluded = false))
      .thenReturn(Future.successful(getJTBSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction("{}"))))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      verify(urlService, times(1))
        .getSelfServiceURLs(eqTo(WhiteLabel.Jtb), eqTo(false), any())(any())
      response shouldEqual expectedResponse
    }
  }

  test("get supplierResponse when Booking with JTB") {
    val operationId = 1336
    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Property.toString),
        dc = "dev",
        whitelabelId = Some(WhiteLabel.Jtb.id),
        operationId = Some(operationId)
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.jtbWhiteLabelInfo)

    val source = fromFile(
      getClass.getClassLoader
        .getResource("response/bookingActionMessage_content_jtbParnerProvisioningResult.json")
        .getPath
    )
    val jtbPartnerProvisioningResultJson = source.getLines.mkString
    source.close()

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getJTBSelfServiceUrl(hotelBookingId),
              instantBookingStatus = Some(InstantBookingStatus.Confirmed),
              isCurrentOperation = Some(true),
              supplierResponse = Some(
                SupplierResponse(
                  hrStockInformation = Some(
                    "{\"TripsInfo\":[{\"HTANo\":\"HTANo\",\"MMSNo\":\"MMSNo\",\"TripsNo\":\"TripsNo\",\"TripsCustomerNo\":\"TripsCustomerNo\",\"TripsTravelNo\":\"TripsTravelNo\"}],\"ReservePNR\":[{\"ReservePNRDate1\":\"ReservePNRDate1\",\"ReservePNRNo1\":\"ReservePNRNo1\"}]}"
                  ),
                  inventoryType = Some("3")
                )
              )
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      )
    )

    when(urlService.getSelfServiceURLs(WhiteLabel.Jtb)).thenReturn(Future.successful(getJTBSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    val masterBookingAction =
      getMasterBookingWorkflowStateWithCustomizedAction("{}").copy(operationId = Some(operationId))
    val propertyBookingAction = getBookingWorkflowStateWithCustomizedAction("{}").copy(operationId = Some(operationId))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(masterBookingAction, propertyBookingAction)))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    when(bookingActionMessageService.getContinueBookingActionMessageWithTopic(any(), any()))
      .thenReturn(
        Future.successful(
          Some(
            getBookingActionMessageWithCustomizedContent(
              jtbPartnerProvisioningResultJson,
              BookingActionMessageTopic.BAM_Topic_JTBPartnerProvisioningResult.value
            )
          )
        )
      )

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("get supplierResponse when Booking with JTB when Topic is not 10") {
    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Property.toString),
        dc = "dev",
        whitelabelId = Some(WhiteLabel.Jtb.id)
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.jtbWhiteLabelInfo)

    val source = fromFile(
      getClass.getClassLoader
        .getResource("response/bookingActionMessage_content_jtbParnerProvisioningResult.json")
        .getPath
    )
    val jtbPartnerProvisioningResultJson = source.getLines.mkString
    source.close()

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getJTBSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(true)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      manualFraudSubmitUrl = Some("")
    )

    when(urlService.getSelfServiceURLs(WhiteLabel.Jtb)).thenReturn(Future.successful(getJTBSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    val masterBookingAction   = getMasterBookingWorkflowStateWithCustomizedAction("{}")
    val propertyBookingAction = getBookingWorkflowStateWithCustomizedAction("{}")

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(masterBookingAction, propertyBookingAction)))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(
        Future.successful(
          Some(
            getBookingActionMessageWithCustomizedContent(
              jtbPartnerProvisioningResultJson,
              BookingActionMessageTopic.BAM_Topic_ManualFraud.value
            )
          )
        )
      )
    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("WhiteLabelId validation when create is Japanican and get status is Rurubu") {
    val createWhiteLabelId    = WhiteLabel.Japanican
    val getStatusWhiteLabelId = WhiteLabel.Rurubu
    val getStatusContext      = mock[WhiteLabelInfo]
    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Property.toString),
        dc = "dev",
        whitelabelId = Some(createWhiteLabelId.id)
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(getStatusContext)

    when(getStatusContext.whiteLabelId).thenReturn(WhiteLabel.Rurubu)
    when(getStatusContext.isFeatureEnabled(WhiteLabelFeatureName.IsRurubuWl)).thenReturn(true)

    val expectedResponse = GetStatusResponse(
      success = false,
      errorMessage = Some(
        s"whitelabelId in create is $createWhiteLabelId but in get status is $getStatusWhiteLabelId"
      )
    )

    when(urlService.getSelfServiceURLs(WhiteLabel.Rurubu)).thenReturn(Future.successful(getRurubuSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    when(requestContext.featureAware).thenReturn(Some(featureAware))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction("{}"))))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("WhiteLabelId validation when create is Agoda and get status is Rurubu") {
    val createWhiteLabelId    = WhiteLabel.Agoda
    val getStatusWhiteLabelId = WhiteLabel.Rurubu
    val getStatusContext      = mock[WhiteLabelInfo]
    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Property.toString),
        dc = "dev",
        whitelabelId = Some(createWhiteLabelId.id)
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(getStatusContext)
    when(getStatusContext.whiteLabelId).thenReturn(WhiteLabel.Rurubu)
    when(getStatusContext.isFeatureEnabled(WhiteLabelFeatureName.IsRurubuWl)).thenReturn(true)
    when(urlService.getSelfServiceURLs(WhiteLabel.Rurubu)).thenReturn(Future.successful(getSelfServiceUrl))

    val expectedResponse = GetStatusResponse(
      success = false,
      errorMessage = Some(
        s"whitelabelId in create is $createWhiteLabelId but in get status is $getStatusWhiteLabelId"
      )
    )

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    when(requestContext.featureAware).thenReturn(Some(featureAware))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction("{}"))))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("Should show memberId 1234 for ItineraryStatus when JTBFP-1414 is B") {
    val getStatusContext = mock[WhiteLabelInfo]
    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Property.toString),
        dc = "dev",
        whitelabelId = Some(4)
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    val mockedMemberId        = 1234
    val bookingStateJson      = s"""{"customer":{"memberId":$mockedMemberId}}"""
    val masterBookingAction   = getMasterBookingWorkflowStateWithCustomizedAction(bookingStateJson)
    val propertyBookingAction = getBookingWorkflowStateWithCustomizedAction(bookingStateJson)
    val bookingActions        = Seq(masterBookingAction, propertyBookingAction)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(getStatusContext)
    when(getStatusContext.whiteLabelId).thenReturn(WhiteLabel.Rurubu)
    when(context.whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.EnableMemberIdForBookingCreation))
      .thenReturn(true)
    when(requestContext.featureAware.exists(_.enableMemberId)).thenReturn(true)

    when(urlService.getSelfServiceURLs(WhiteLabel.Rurubu)).thenReturn(Future.successful(getRurubuSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    when(requestContext.featureAware).thenReturn(Some(featureAware))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, bookingActions).map { response =>
      response.memberId shouldEqual mockedMemberId
    }
  }

  test("Should show memberId 0 for ItineraryStatus when JTBFP-1414 is A") {
    val getStatusContext = mock[WhiteLabelInfo]
    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Property.toString),
        dc = "dev",
        whitelabelId = Some(4)
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    val mockedMemberId        = 1234
    val bookingStateJson      = s"""{"customer":{"memberId":$mockedMemberId}}"""
    val masterBookingAction   = getMasterBookingWorkflowStateWithCustomizedAction(bookingStateJson)
    val propertyBookingAction = getBookingWorkflowStateWithCustomizedAction(bookingStateJson)
    val bookingActions        = Seq(masterBookingAction, propertyBookingAction)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(getStatusContext)
    when(getStatusContext.whiteLabelId).thenReturn(WhiteLabel.Rurubu)
    when(context.whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.EnableMemberIdForBookingCreation))
      .thenReturn(true)
    when(requestContext.featureAware.exists(_.enableMemberId)).thenReturn(false)
    when(urlService.getSelfServiceURLs(WhiteLabel.Rurubu)).thenReturn(Future.successful(getRurubuSelfServiceUrl))

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    when(requestContext.featureAware).thenReturn(Some(featureAware))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, bookingActions).map { response =>
      response.memberId shouldEqual 0
    }
  }

  test("WhiteLabelId validation when create is Agoda and get status is Japanican") {
    val createWhiteLabelId    = WhiteLabel.Agoda
    val getStatusWhiteLabelId = WhiteLabel.Japanican
    val getStatusContext      = mock[WhiteLabelInfo]
    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Property.toString),
        dc = "dev",
        whitelabelId = Some(createWhiteLabelId.id)
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(getStatusContext)
    when(getStatusContext.whiteLabelId).thenReturn(WhiteLabel.Japanican)
    when(getStatusContext.isFeatureEnabled(WhiteLabelFeatureName.IsJapanicanWl)).thenReturn(true)
    when(urlService.getSelfServiceURLs(WhiteLabel.Japanican)).thenReturn(Future.successful(getJapanicanSelfServiceUrl))

    val expectedResponse = GetStatusResponse(
      success = false,
      errorMessage = Some(
        s"whitelabelId in create is $createWhiteLabelId but in get status is $getStatusWhiteLabelId"
      )
    )

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    when(requestContext.featureAware).thenReturn(Some(featureAware))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction("{}"))))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("WhiteLabelId validation when create is Priceline and get status is Agoda") {
    val createWhiteLabelId = WhiteLabel.Priceline
    val getStatusContext   = MockRequestContext.agodaWhiteLabelInfo
    val getStatusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Property.toString),
        dc = "dev",
        whitelabelId = Some(createWhiteLabelId.id)
      ).serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(getStatusToken, None, None, None)

    implicit val context: RequestContext = requestContext
    when(context.whiteLabelInfo).thenReturn(getStatusContext)

    when(urlService.getSelfServiceURLs(WhiteLabel.Agoda)).thenReturn(Future.successful(getSelfServiceUrl))

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = getStatusToken
        )
      )
    )

    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))

    when(requestContext.featureAware).thenReturn(Some(featureAware))

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction("{}"))))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("getBookingActionsByItineraryId should return bookingAction") {
    val bookingAction = BookingWorkflowAction(
      actionId = actionId,
      itineraryId = itineraryId,
      bookingType = Some(1),
      bookingId = Some(1),
      memberId = 1,
      actionTypeId = 1,
      correlationId = "",
      requestId = "",
      workflowId = 1,
      workflowStateId = 1,
      productTypeId = Some(1),
      stateSchemaVersion = 1,
      state = "",
      storefrontId = Some(1),
      languageId = Some(1)
    )

    when(workflowRepository.getBookingActionByItineraryId(itineraryId)).thenReturn(
      Future.successful(Seq(bookingAction))
    )

    commonBookingsService.getBookingActionsByItineraryId(itineraryId).map { response =>
      response shouldEqual Seq(bookingAction)
    }
  }

  test("getBookingActionsByItineraryId should return Seq.empty") {
    when(workflowRepository.getBookingActionByItineraryId(itineraryId)).thenReturn(Future.successful(Seq.empty))

    commonBookingsService.getBookingActionsByItineraryId(itineraryId).map { response =>
      response shouldEqual Seq.empty
    }
  }

  test("getBookingActionsByItineraryId should handle exception error by return Seq.empty") {
    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.failed(new Exception("Something broken")))

    commonBookingsService.getBookingActionsByItineraryId(itineraryId).map { response =>
      response shouldEqual Seq.empty
    }
  }

  test("if getIsPartialSuccessAllowed receive no state then it should return None") {
    val masterBookingAction = getMasterBookingWorkflowStateWithCustomizedAction("{}")
    commonBookingsService.getIsPartialSuccessAllowed(masterBookingAction) shouldEqual None
  }

  test("if getIsPartialSuccessAllowed has problem with parsing json then it should return None") {
    val masterBookingAction =
      getMasterBookingWorkflowStateWithCustomizedAction("""{"request":{"isPartialSuccessAllo}}""")
    commonBookingsService.getIsPartialSuccessAllowed(masterBookingAction) shouldEqual None
  }

  test(
    "if getIsPartialSuccessAllowed receive state with itPartialSuccessEligible is false then it should return false"
  ) {
    val masterBookingActionState = s"""{"request":{"isPartialSuccessAllowed": false}}"""
    val masterBookingAction      = getMasterBookingWorkflowStateWithCustomizedAction(masterBookingActionState)
    commonBookingsService.getIsPartialSuccessAllowed(masterBookingAction).value shouldEqual false
  }

  test(
    "if getIsPartialSuccessAllowed receive state with itPartialSuccessEligible is true then it should return true"
  ) {
    val masterBookingActionState = s"""{"request":{"isPartialSuccessAllowed": true}}"""
    val masterBookingAction      = getMasterBookingWorkflowStateWithCustomizedAction(masterBookingActionState)
    commonBookingsService.getIsPartialSuccessAllowed(masterBookingAction).value shouldEqual true
  }

  test(
    "if getRejectReason receive no state then it should return None"
  ) {
    val masterBookingActionState = s"""{}"""
    val masterBookingAction      = getMasterBookingWorkflowStateWithCustomizedAction(masterBookingActionState)
    commonBookingsService.getRejectReason(masterBookingAction) shouldEqual None
  }

  test(
    "if getRejectReason receive state have rejectRootCause as null then it should return None"
  ) {
    val masterBookingActionState = s"""{"rejectRootCause": null}"""
    val masterBookingAction      = getMasterBookingWorkflowStateWithCustomizedAction(masterBookingActionState)
    commonBookingsService.getRejectReason(masterBookingAction) shouldEqual None
  }

  test(
    "if getRejectReason receive state with rejectRootCause as not Int then it should return None"
  ) {
    val masterBookingActionState = s"""{"rejectRootCause": "abc"}"""
    val masterBookingAction      = getMasterBookingWorkflowStateWithCustomizedAction(masterBookingActionState)
    commonBookingsService.getRejectReason(masterBookingAction) shouldEqual None
  }

  test(
    "if getRejectReason has problem with parsing json then it should return None"
  ) {
    val masterBookingActionState = s"""{"rejectRootCause": "abcdefghij}"""
    val masterBookingAction      = getMasterBookingWorkflowStateWithCustomizedAction(masterBookingActionState)
    commonBookingsService.getRejectReason(masterBookingAction) shouldEqual None
  }

  test(
    "if getRejectReason receive state with rejectRootCause as Int then it should return rejectRootCause Option value"
  ) {
    val masterBookingActionState = s"""{"rejectRootCause": 14}"""
    val masterBookingAction      = getMasterBookingWorkflowStateWithCustomizedAction(masterBookingActionState)
    commonBookingsService.getRejectReason(masterBookingAction) shouldEqual Some(14)
  }

  test("if getProductKey receive no state then it should return None") {
    val bookingAction = getBookingWorkflowStateWithCustomizedAction("{}")
    commonBookingsService.getProductKey(bookingAction) shouldEqual None
  }

  test("if getProductKey has problem with parsing json then it should return None") {
    val bookingAction =
      getBookingWorkflowStateWithCustomizedAction("""{"propertyBookingState":{"bookingList":{"productK}}""")
    commonBookingsService.getProductKey(bookingAction) shouldEqual None
  }

  test(
    "if getProductKey receive state with productKey is define then it should return the productKey value"
  ) {
    val bookingActionState =
      s"""{"propertyBookingState":{"bookingList":{"productKey": "test-product-key-value"}}}"""
    val bookingAction = getBookingWorkflowStateWithCustomizedAction(bookingActionState)
    commonBookingsService.getProductKey(bookingAction).value shouldEqual "test-product-key-value"
  }

  test("if getLoginTokenAndRemoveIfExists receive no booking action message then it should return None") {
    val masterBookingActionMessage = None
    val loginToken                 = commonBookingsService.getLoginTokenAndRemoveIfExists(masterBookingActionMessage)
    verify(workflowRepository, times(0)).updateBookingActionMessage(anyLong(), anyString())
    loginToken shouldEqual None
  }

  test("if getLoginTokenAndRemoveIfExists receive empty message then it should return None") {
    val content = """{}"""
    val masterBookingActionMessage = Some(
      getBookingActionMessageWithCustomizedContent(content, BookingActionMessageTopic.BAM_Topic_AutoLoginToken.value)
    )
    val loginToken = commonBookingsService.getLoginTokenAndRemoveIfExists(masterBookingActionMessage)
    verify(workflowRepository, times(0)).updateBookingActionMessage(
      eqTo(masterBookingActionMessage.get.messageId.get),
      contains("""consumedWhen""")
    )
    loginToken shouldEqual None
  }

  test("if getLoginTokenAndRemoveIfExists has problem with parsing json then it should return None") {
    val content = """{"tok:}"""
    val masterBookingActionMessage = Some(
      getBookingActionMessageWithCustomizedContent(content, BookingActionMessageTopic.BAM_Topic_AutoLoginToken.value)
    )
    val loginToken = commonBookingsService.getLoginTokenAndRemoveIfExists(masterBookingActionMessage)
    verify(workflowRepository, times(0)).updateBookingActionMessage(
      eqTo(masterBookingActionMessage.get.messageId.get),
      contains("""consumedWhen""")
    )
    loginToken shouldEqual None
  }

  test(
    "if getLoginTokenAndRemoveIfExists receive message with cannot be decrypt loginToken field then should return None"
  ) {
    val encryptedToken = "myLoginToken"
    val content        = s"""{"autoLoginToken":{"token":"${encryptedToken}"}}"""
    val masterBookingActionMessage = Some(
      getBookingActionMessageWithCustomizedContent(content, BookingActionMessageTopic.BAM_Topic_AutoLoginToken.value)
    )
    val loginToken = commonBookingsService.getLoginTokenAndRemoveIfExists(masterBookingActionMessage)
    verify(workflowRepository, times(0)).updateBookingActionMessage(
      eqTo(masterBookingActionMessage.get.messageId.get),
      contains("""consumedWhen""")
    )
    loginToken shouldEqual None
  }

  test(
    "if getLoginTokenAndRemoveIfExists receive message with loginToken field that can be decrypt then should return loginToken"
  ) {
    val encryptedToken                          = "vWB5tjOncd2RtOsIRGQdeD8WUJj1u9giy/e1Se3W/NQJ6K4mZYa/mg=="
    val decryptedToken                          = "myLoginToken"
    val content                                 = s"""{"autoLoginToken":{"token":"${encryptedToken}"}}"""
    val resultBAMCaptor: ArgumentCaptor[String] = ArgumentCaptor.forClass(classOf[String])
    val masterBookingActionMessage = Some(
      getBookingActionMessageWithCustomizedContent(content, BookingActionMessageTopic.BAM_Topic_AutoLoginToken.value)
    )
    val loginToken = commonBookingsService.getLoginTokenAndRemoveIfExists(masterBookingActionMessage)
    verify(workflowRepository, times(1)).updateBookingActionMessage(
      eqTo(masterBookingActionMessage.get.messageId.get),
      resultBAMCaptor.capture()
    )
    loginToken.nonEmpty shouldBe true
    loginToken.get shouldEqual decryptedToken

    JsonFormat
      .fromJsonString[AgentBookingActionMessage](resultBAMCaptor.getValue)
      .autoLoginToken
      .get
      .consumedWhen
      .nonEmpty shouldBe true
  }

  test("should return customer loginToken if masterBooking") {
    val encryptedToken = "vWB5tjOncd2RtOsIRGQdeD8WUJj1u9giy/e1Se3W/NQJ6K4mZYa/mg=="
    val decryptedToken = "myLoginToken"
    val content        = s"""{"autoLoginToken":{"token":"${encryptedToken}"}}"""
    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      loginToken = Some(decryptedToken)
    )
    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(
        Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction("{}")))
      )
    when(bookingActionMessageService.getContinueBookingActionMessageWithTopic(any[Int], any[BookingActionMessageTopic]))
      .thenReturn(
        Future.successful(
          Some(
            getBookingActionMessageWithCustomizedContent(
              content,
              BookingActionMessageTopic.BAM_Topic_AutoLoginToken.value
            )
          )
        )
      )
    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))
    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))
    when(requestContext.featureAware).thenReturn(Some(featureAware))
    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )
    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))
    when(flightInfoService.getFlightsItineraryStatus(eqTo(itineraryId.toLong)))
      .thenReturn(
        Future
          .successful(Seq.empty)
      )
    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
      response.loginToken.nonEmpty shouldBe true
      response.loginToken.get shouldBe decryptedToken
    }
  }

  test("should not return customer loginToken if masterBooking with no action message") {
    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          statusToken = getStatusToken
        )
      ),
      loginToken = None
    )
    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(
        Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction("{}")))
      )
    when(bookingActionMessageService.getContinueBookingActionMessageWithTopic(any[Int], any[BookingActionMessageTopic]))
      .thenReturn(
        Future.successful(None)
      )
    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))
    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))
    when(requestContext.featureAware).thenReturn(Some(featureAware))
    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )
    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))
    when(flightInfoService.getFlightsItineraryStatus(eqTo(itineraryId.toLong)))
      .thenReturn(
        Future
          .successful(Seq.empty)
      )
    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
      response.loginToken.isEmpty shouldBe true
    }
  }

  test("should success and not log any error if masterBooking with no autoLoginToken object") {
    val content = s"""{"autoLoginToken":{}}"""
    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          statusToken = getStatusToken
        )
      )
    )
    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(
        Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction("{}")))
      )
    when(bookingActionMessageService.getContinueBookingActionMessageWithTopic(any[Int], any[BookingActionMessageTopic]))
      .thenReturn(
        Future.successful(
          Some(
            getBookingActionMessageWithCustomizedContent(
              content,
              BookingActionMessageTopic.BAM_Topic_AutoLoginToken.value
            )
          )
        )
      )
    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))
    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))
    when(requestContext.featureAware).thenReturn(Some(featureAware))
    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )
    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))
    when(flightInfoService.getFlightsItineraryStatus(eqTo(itineraryId.toLong)))
      .thenReturn(
        Future
          .successful(Seq.empty)
      )
    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      verify(loggerMock, times(0)).error(any[String], any[Throwable])
      response shouldEqual expectedResponse
      response.loginToken.isEmpty shouldBe true
    }
  }

  test("should success and not log any error if masterBooking with no autoLoginToken token string") {
    val content = s"""{"autoLoginToken":{"token":""}}"""
    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq(
            HotelBooking(
              itineraryId = itineraryId,
              bookingId = hotelBookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              selfServiceUrl = getSelfServiceUrl(hotelBookingId),
              isCurrentOperation = Some(false)
            )
          ),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          statusToken = getStatusToken
        )
      )
    )
    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(
        Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction("{}")))
      )
    when(bookingActionMessageService.getContinueBookingActionMessageWithTopic(any[Int], any[BookingActionMessageTopic]))
      .thenReturn(
        Future.successful(
          Some(
            getBookingActionMessageWithCustomizedContent(
              content,
              BookingActionMessageTopic.BAM_Topic_AutoLoginToken.value
            )
          )
        )
      )
    when(ebeLiteBookingRepository.getEbeLiteBooking(itineraryId))
      .thenReturn(Future.successful(Seq(ebeLiteBookingBookingConfirm)))
    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))
    when(requestContext.featureAware).thenReturn(Some(featureAware))
    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )
    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))
    when(flightInfoService.getFlightsItineraryStatus(eqTo(itineraryId.toLong)))
      .thenReturn(
        Future
          .successful(Seq.empty)
      )
    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      verify(loggerMock, times(0)).error(any[String], any[Throwable])
      response shouldEqual expectedResponse
      response.loginToken.isEmpty shouldBe true
    }
  }

  test("deserialize Booking state correctly: PartialSuccess: true, allowRetry: true") {
    val bookingActionStateStr       = s"""{"request":{"isPartialSuccessAllowed": true , "allowRetryPayment":true}}"""
    val flightBookingWorkflowAction = getMasterBookingWorkflowStateWithCustomizedAction(bookingActionStateStr)
    val bookingActionState          = commonBookingsService.deserializeBookingActionState(flightBookingWorkflowAction).value

    bookingActionState.request.isPartialSuccessAllowed.value shouldBe true
    bookingActionState.request.allowRetryPayment.value shouldBe true
  }

  test("deserialize Booking state correctly: PartialSuccess: false, allowRetry: false") {
    val bookingActionStateStr       = s"""{"request":{"isPartialSuccessAllowed": false , "allowRetryPayment":false}}"""
    val flightBookingWorkflowAction = getMasterBookingWorkflowStateWithCustomizedAction(bookingActionStateStr)
    val bookingActionState          = commonBookingsService.deserializeBookingActionState(flightBookingWorkflowAction).value

    bookingActionState.request.isPartialSuccessAllowed.value shouldBe false
    bookingActionState.request.allowRetryPayment.value shouldBe false
  }

  test("deserializeBookingActionState should return None for empty json ") {
    val result =
      commonBookingsService.deserializeBookingActionState(getMasterBookingWorkflowStateWithCustomizedAction(""))
    result shouldBe None
  }

  test("deserializeBookingActionState should return None for Invalid json ") {
    val result = commonBookingsService.deserializeBookingActionState(
      getMasterBookingWorkflowStateWithCustomizedAction("{\"request\" : \"\"}")
    )
    result shouldBe None
  }

  test("if getIsPartialSuccessAllowedFromState receive no state then it should return None") {
    commonBookingsService.getIsPartialSuccessAllowedFromState(None) shouldEqual None
  }

  test("if getIsRetryAllowed receive no state then it should return None") {
    commonBookingsService.getIsRetryAllowed(None) shouldEqual None
  }

  test("getIsRetryAllowed should return Some(true) when state allows retry") {
    val bookingStateJson =
      "{\"request\":{\"isPartialSuccessAllowed\": false , \"allowRetryPayment\":true}}"
    val result = commonBookingsService.deserializeBookingActionState(
      getMasterBookingWorkflowStateWithCustomizedAction(bookingStateJson)
    )
    commonBookingsService.getIsRetryAllowed(result) shouldEqual Some(true)
  }

  test("getStatus for 1 flight, with retry payment as false") {
    when(requestContext.featureAware.exists(_.enableRetryPaymentOnStatusEndpoint)).thenReturn(true)

    implicit val context: RequestContext = requestContext
    val bookingStateJson =
      "{\"request\":{\"isPartialSuccessAllowed\": false , \"allowRetryPayment\":false}}"

    val (statusRequest, statusToken) = setEnvForSingleFlight(bookingStateJson)
    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          flights =
            Seq(FlightBooking(bookingId = flightBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing)),
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = statusToken.serialize(logBookingCreationLogMessageBaseStub, measureStub)
        )
      ),
      isPartialSuccessAllowed = Some(false),
      isRetryPaymentAllowed = Some(false)
    )

    commonBookingsService.getItineraryStatus(statusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test(
    "getStatus for 1 flight, should return paymentResult with paymentReason if topic is BAM_Topic_RetryPayment and has preAuthResult"
  ) {
    val bookingStateJson =
      """{"retryFeedback":{"preAuthResult":{"reason":{"message":"Insufficient Fund","groupCode":1,"code":"code"}}}}"""

    val (statusRequest, statusToken) = setEnvForSingleFlight("")

    val expectedStatusToken = statusToken
      .copy(topic = Some(BookingActionMessageTopic.BAM_Topic_RetryPayment.value))
      .serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val retryContentJson =
      "{\"retryFeedback\":{\"preAuthResult\":{\"reason\":{\"message\":\"Insufficient Fund\",\"groupCode\":1,\"code\":\"code\"}}}}"
    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(
        Future.successful(
          Some(
            getBookingActionMessageWithCustomizedContent(
              retryContentJson,
              BookingActionMessageTopic.BAM_Topic_RetryPayment.value
            )
          )
        )
      )

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          flights =
            Seq(FlightBooking(bookingId = flightBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing)),
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = expectedStatusToken
        )
      ),
      paymentResult = Some(
        PaymentResult(
          topic = Some(BookingActionMessageTopic.BAM_Topic_RetryPayment.value),
          continuation = PaymentContinuation(Gateway.None),
          redirect3ds = None,
          paymentReason = Some(
            PaymentReason(
              groupCode = Some(1),
              code = Some("code"),
              message = Some("Insufficient Fund")
            )
          )
        )
      )
    )

    commonBookingsService.getItineraryStatus(statusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test(
    "getStatus for 1 flight, should not return paymentReason if topic is BAM_Topic_RetryPayment but does not have preAuthResult"
  ) {
    val bookingStateJson =
      """{"retryFeedback":{}}"""

    val (statusRequest, statusToken) = setEnvForSingleFlight("")

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any()))
      .thenReturn(
        Future.successful(
          Some(
            getBookingActionMessageWithCustomizedContent(
              bookingStateJson,
              BookingActionMessageTopic.BAM_Topic_RetryPayment.value
            )
          )
        )
      )

    val expectedStatusToken = statusToken
      .copy(topic = Some(BookingActionMessageTopic.BAM_Topic_RetryPayment.value))
      .serialize(logBookingCreationLogMessageBaseStub, measureStub)

    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          flights =
            Seq(FlightBooking(bookingId = flightBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing)),
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = expectedStatusToken
        )
      ),
      paymentResult = Some(
        PaymentResult(
          topic = Some(BookingActionMessageTopic.BAM_Topic_RetryPayment.value),
          continuation = PaymentContinuation(Gateway.None),
          redirect3ds = None,
          paymentReason = None
        )
      )
    )

    commonBookingsService.getItineraryStatus(statusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  test("getStatus for 1 flight, with retry payment as true") {
    when(requestContext.featureAware.exists(_.enableRetryPaymentOnStatusEndpoint)).thenReturn(true)

    implicit val context: RequestContext = requestContext
    val bookingStateJson =
      "{\"request\":{\"isPartialSuccessAllowed\": false , \"allowRetryPayment\":true}}"

    val (statusRequest, statusToken) = setEnvForSingleFlight(bookingStateJson)
    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          flights =
            Seq(FlightBooking(bookingId = flightBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing)),
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = statusToken.serialize(logBookingCreationLogMessageBaseStub, measureStub)
        )
      ),
      isPartialSuccessAllowed = Some(false),
      isRetryPaymentAllowed = Some(true)
    )

    commonBookingsService.getItineraryStatus(statusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }
  test("getStatus should return None for isRetryPaymentAllowed when feature flag is disabled") {
    when(requestContext.featureAware.exists(_.enableRetryPaymentOnStatusEndpoint)).thenReturn(false)

    implicit val context: RequestContext = requestContext
    val bookingStateJson =
      "{\"request\":{\"isPartialSuccessAllowed\": false , \"allowRetryPayment\":true}}"

    val (statusRequest, statusToken) = setEnvForSingleFlight(bookingStateJson)
    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          flights =
            Seq(FlightBooking(bookingId = flightBookingId, bookingStatus = CreatedBookingStatus.BookingProcessing)),
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = statusToken.serialize(logBookingCreationLogMessageBaseStub, measureStub)
        )
      ),
      isPartialSuccessAllowed = Some(false),
      isRetryPaymentAllowed = None
    )

    commonBookingsService.getItineraryStatus(statusRequest, Seq.empty).map { response =>
      response shouldEqual expectedResponse
    }
  }

  private def setEnvForSingleFlight(
      masterStateJson: String
  ): (GetStatusRequest, StatusToken) = {
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)

    when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
      .thenReturn(Future.successful(getSelfServiceUrl))

    when(flightInfoService.getFlightsItineraryStatus(itineraryId))
      .thenReturn(
        Future
          .successful(Seq(creation.FlightBooking(flightBookingId, CreatedBookingStatus.BookingProcessing, None, None)))
      )

    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(masterStateJson))))

    when(workflowRepository.getBookingActionMessage(itineraryId)).thenReturn(
      Future.successful(Seq.empty)
    )

    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))

    val statusToken = StatusToken(
      itineraryId = 56969569L,
      actionId = 29392L,
      productType = Set(ProductTypeEnum.Flight.toString),
      dc = "bk"
    )

    val request =
      GetStatusRequest(
        statusToken = statusToken.serialize(logBookingCreationLogMessageBaseStub, measureStub),
        correlationId = None,
        userContext = None,
        bookingContext = None
      )

    (request, statusToken)
  }

  private def executeVehicleScenarioVariantForGetStatusDataFromBkgDb(variant: Char) = {
    val isB = variant match {
      case 'A' | 'a' => false
      case 'B' | 'b' => true
      case _         => false
    }
    val statusToken =
      StatusToken(
        itineraryId = itineraryId,
        actionId = actionId,
        productType = Set(ProductTypeEnum.Car.toString),
        dc = "dev",
        operationType = Some("operation-type")
      )

    val statusTokenStr   = statusToken.serialize(logBookingCreationLogMessageBaseStub, measureStub)
    val getStatusRequest = GetStatusRequest(statusTokenStr, None, None, None)
    val preAuthResultStateJson =
      "{\"paymentInfo\":{\"method\":18,\"paymentCurrency\":\"USD\",\"paymentAmount\":10.0,\"paymentAmountUSD\":20.0,\"isRedirect\":true}}"
    implicit val context: RequestContext = requestContext
    val expectedResponse = GetStatusResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          bookings = Seq.empty,
          flights = Seq.empty,
          cars =
            Seq(VehicleBooking(bookingId = vehicleBookingId, bookingStatus = CreatedBookingStatus.BookingConfirmed)),
          protections = Seq.empty,
          activities = Seq.empty,
          statusToken = statusTokenStr
        )
      ),
      paymentResult = None
    )

    when(featureAware.enableGetStatusDataFromBkgDb).thenReturn(isB)
    when(context.whiteLabelInfo).thenReturn(MockRequestContext.agodaWhiteLabelInfo)
    when(vehicleInfoService.getVehicleItineraryStatus(any(), any(), any())(any())).thenReturn(
      Future.successful(
        Seq(VehicleBooking(bookingId = vehicleBookingId, bookingStatus = CreatedBookingStatus.BookingConfirmed))
      )
    )
    when(bookingActionMessageService.getContinueBookingActionMessage(any())(any())).thenReturn(Future.successful(None))
    when(workflowRepository.getBookingActionByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(getMasterBookingWorkflowStateWithCustomizedAction(preAuthResultStateJson))))

    commonBookingsService.getItineraryStatus(getStatusRequest, Seq.empty).map { response =>
      verify(vehicleInfoService).getVehicleItineraryStatus(
        itineraryId = itineraryId,
        operationType = statusToken.operationType,
        useBkgDb = isB
      )(context)
      verifyNoMoreInteractions(vehicleInfoService)
      response shouldEqual expectedResponse
    }
  }
}
