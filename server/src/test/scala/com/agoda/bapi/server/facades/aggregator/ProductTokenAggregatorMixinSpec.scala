package com.agoda.bapi.server.facades.aggregator

import cats.Id
import com.agoda.bapi.common.model.activity.{ActivityBookingToken, ActivityConfirmationData}
import com.agoda.bapi.common.model.car.{CarBookingToken, CarConfirmationData}
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.protection.ProtectionConfimationData
import com.agoda.bapi.common.model.tripProtection.TripProtectionToken
import com.agoda.bapi.common.model.{ChargeOption, CurrencyCode}
import com.agoda.bapi.common.token.{GenericAddOnBookingModel, PaymentRequestInfo}
import com.agoda.bapi.contracts.WithToBookingToken
import com.agoda.bapi.creation.ProductTokenMockHelper
import com.agoda.bapi.server.addon.{AddOnBookingTokenService, AddOnConfirmationData}
import com.agoda.bapi.server.facades.helpers.SetupBookingContextFixture
import com.agoda.bapi.server.model.BookingPropertiesData
import com.agoda.bapi.server.utils.{ProductTokenCreatorUtils, SetupBookingMock}
import com.agoda.mpb.common.models.state.ProductType
import mocks.MockAddOnV2Data
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{RETURNS_DEEP_STUBS, when}
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.util.Try

class ProductTokenAggregatorMixinSpec
    extends AsyncWordSpec
    with MockitoSugar
    with Matchers
    with SetupBookingMock
    with ProductTokenMockHelper
    with ProductTokenAggregatorMixin
    with MockAddOnV2Data {

  override val carService =
    mock[WithToBookingToken[Id, (Seq[CarConfirmationData], Option[CurrencyCode]), CarBookingToken]]
  override val activityService     = mock[WithToBookingToken[Try, Seq[ActivityConfirmationData], ActivityBookingToken]]
  override val cegFastTrackService = mock[AddOnBookingTokenService]
  override val productTokenUtils   = mock[ProductTokenCreatorUtils]
  override val protectionService   = mock[WithToBookingToken[Try, ProtectionConfimationData, TripProtectionToken]]

  "toPaymentRequestInfoModel" should {

    new SetupBookingContextFixture {
      "map payment request to payment request model correctly when value is existed" in {
        when(setupBookingContext.bookingFlowType) thenReturn (BookingFlow.SingleProperty)

        val actual =
          toPaymentRequestInfoModel(Some(ChargeOption.PayNow), MockProductUtils.createMockProduct().properties)

        val expected = Some(
          PaymentRequestInfo(
            Some(ChargeOption.PayNow),
            Some(new DateTime("2019-06-03T00:00+07:00")),
            Some(new DateTime("2019-06-04T00:00+07:00"))
          )
        )
        actual shouldEqual expected
      }
    }
    new SetupBookingContextFixture {
      "map payment request to payment request model correctly when value is None" in {
        when(setupBookingContext.bookingFlowType) thenReturn (BookingFlow.SingleProperty)

        val actual = toPaymentRequestInfoModel(Some(ChargeOption.PayNow), Seq.empty)

        actual shouldEqual None
      }
    }

    new SetupBookingContextFixture {
      "map to PaymentRequestInfo correctly for singleProperty when selectedChargeOption exists" in {
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
        val mockBookingPropertiesData = mock[BookingPropertiesData](RETURNS_DEEP_STUBS)
        when(mockBookingPropertiesData.isAlternativeRoomEnabled(any())).thenReturn(true)
        val actual =
          toPaymentRequestInfoModel(Some(ChargeOption.PayLater), MockProductUtils.createMockProduct().properties)

        val expected = Some(
          PaymentRequestInfo(
            Some(ChargeOption.PayLater),
            Some(new DateTime("2019-06-03T00:00+07:00")),
            Some(new DateTime("2019-06-04T00:00+07:00"))
          )
        )
        actual shouldEqual expected
      }
    }
    new SetupBookingContextFixture {
      "map to PaymentRequestInfo correctly for singleProperty when selectedChargeOption exists (case with alternatives)" in {
        when(setupBookingContext.bookingFlowType) thenReturn (BookingFlow.SingleProperty)

        val actual = toPaymentRequestInfoModel(
          Some(ChargeOption.PayLater),
          MockProductUtils.createMockProductWithAlternatives().properties
        )

        val expected = Some(
          PaymentRequestInfo(
            Some(ChargeOption.PayLater),
            Some(new DateTime("2019-06-03T00:00+07:00")),
            Some(new DateTime("2019-06-04T00:00+07:00"))
          )
        )
        actual shouldEqual expected
      }
    }
    new SetupBookingContextFixture {
      "map chargeOption to NONE for singleProperty when selectedChargeOption is empty" in {
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)

        val expected = Some(
          PaymentRequestInfo(
            selectedChargeOption = None,
            fullyAuthDate = Some(new DateTime("2019-06-03T00:00+07:00")),
            fullyChargeDate = Some(new DateTime("2019-06-04T00:00+07:00"))
          )
        )
        val actual = toPaymentRequestInfoModel(None, MockProductUtils.createMockProduct().properties)

        actual shouldEqual expected
      }
    }
    new SetupBookingContextFixture {
      "map fullyAuthDate to NONE for singleProperty when fullyAuthDate is empty" in {
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)

        val productRequest = MockProductUtils.createMockProduct(fullyAuthDate = None)

        val expected = Some(
          PaymentRequestInfo(
            selectedChargeOption = None,
            fullyAuthDate = None,
            fullyChargeDate = Some(new DateTime("2019-06-04T00:00+07:00"))
          )
        )

        val actual = toPaymentRequestInfoModel(None, productRequest.properties)

        actual shouldEqual expected
      }
    }
    new SetupBookingContextFixture {
      "map to NONE for singleProperty when fullyChargeDate is empty" in {
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)

        val productRequest = MockProductUtils.createMockProduct(fullyChargeDate = None)

        val expected = Some(
          PaymentRequestInfo(
            selectedChargeOption = None,
            fullyAuthDate = Some(new DateTime("2019-06-03T00:00+07:00")),
            fullyChargeDate = None
          )
        )

        val actual = toPaymentRequestInfoModel(None, productRequest.properties)

        actual shouldEqual expected
      }
    }
    new SetupBookingContextFixture {
      "map to PaymentRequestInfo correctly for cart flow when selectedChargeOption exists" in {
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)

        val actual = toPaymentRequestInfoModel(
          Some(ChargeOption.PayLater),
          MockProductUtils.createMockProduct(isCartMultiProperties = true).properties
        )

        val expected = Some(
          PaymentRequestInfo(
            selectedChargeOption = Some(ChargeOption.PayLater),
            fullyAuthDate = None,
            fullyChargeDate = None
          )
        )
        actual shouldEqual expected
      }
    }
    new SetupBookingContextFixture {
      "map chargeOption to NONE for cart flow when selectedChargeOption is empty" in {
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        val actual =
          toPaymentRequestInfoModel(None, MockProductUtils.createMockProduct(isCartMultiProperties = true).properties)
        actual shouldEqual None
      }
    }
    new SetupBookingContextFixture {
      "map single AddOn token" in {

        val addOnSample = sampleAddOn().copy(productType = ProductType.TripProtection)
        val cegUpsellAddOn = sampleAddOn().copy(
          productType = ProductType.CEGFastTrack,
          confirmationData = sampleAddOn().confirmationData.map { e =>
            e.copy(productTokenKey = "mock-ceg-upsell")
          }
        )
        val confirmationData: AddOnConfirmationData = addOnSample.confirmationData.head
        val productsWithAddons                      = MockProductUtils.createMockProduct().copy(addOnDataV2 = Seq(addOnSample))
        val result: GenericAddOnBookingModel        = toAddOn(productsWithAddons, false, false)
        result.size shouldBe 1
        val token = result.head
        token._1 shouldEqual confirmationData.productTokenKey
        token._2.productTokenKey shouldBe confirmationData.productTokenKey
        token._2.characteristics.productTypeId shouldBe addOnSample.productType.id
        token._2.characteristics.startDate shouldBe Some(confirmationData.startDateTime)
        token._2.characteristics.endDate shouldBe Some(confirmationData.endDateTime)
        token._2.bundle.get.addOnProductIds shouldBe addOnSample.productRefIds
        token._2.financials.get.productPaymentModel shouldBe confirmationData.paymentModel
        token._2.financials.get.priceBreakdown shouldEqual confirmationData.priceBreakdown
        token._2.supplier.get.supplierId shouldBe confirmationData.supplier.get.supplierId
        token._2.supplier.get.supplierData shouldBe confirmationData.supplier.get.supplierData
        token._2.meta shouldEqual confirmationData.metas
        token._2.productPayment.nonEmpty shouldBe true
      }

      "skip mapping if AddOn token has no confirmation data is present" in {
        val addOnSample                             = sampleAddOn().copy(productType = ProductType.TripProtection)
        val addOnSample2                            = sampleAddOn().copy(productType = ProductType.TripProtection, confirmationData = None)
        val confirmationData: AddOnConfirmationData = addOnSample.confirmationData.head
        val productsWithAddons                      = MockProductUtils.createMockProduct().copy(addOnDataV2 = Seq(addOnSample, addOnSample2))
        val result: GenericAddOnBookingModel        = toAddOn(productsWithAddons, false, false)
        result.size shouldBe 1
        val token = result.head
        token._1 shouldEqual confirmationData.productTokenKey
        token._2.productTokenKey shouldBe confirmationData.productTokenKey
        token._2.characteristics.productTypeId shouldBe addOnSample.productType.id
        token._2.characteristics.startDate shouldBe Some(confirmationData.startDateTime)
        token._2.characteristics.endDate shouldBe Some(confirmationData.endDateTime)
        token._2.bundle.get.addOnProductIds shouldBe addOnSample.productRefIds
        token._2.financials.get.productPaymentModel shouldBe confirmationData.paymentModel
        token._2.financials.get.priceBreakdown shouldEqual confirmationData.priceBreakdown
        token._2.supplier.get.supplierId shouldBe confirmationData.supplier.get.supplierId
        token._2.supplier.get.supplierData shouldBe confirmationData.supplier.get.supplierData
        token._2.meta shouldEqual confirmationData.metas
        token._2.productPayment.nonEmpty shouldBe true
      }

      "map only migrated addon to creation token" in {
        val addOnSample = sampleAddOn().copy(productType = ProductType.TripProtection)
        val cegUpsellAddOn = sampleAddOn().copy(
          productType = ProductType.CEGFastTrack,
          confirmationData = sampleAddOn().confirmationData.map { e =>
            e.copy(productTokenKey = "mock-ceg-upsell")
          }
        )
        val confirmationData: AddOnConfirmationData = addOnSample.confirmationData.head
        val productsWithAddons =
          MockProductUtils.createMockProduct().copy(addOnDataV2 = Seq(addOnSample, cegUpsellAddOn))
        val result: GenericAddOnBookingModel = toAddOn(productsWithAddons, false, false)
        result.size shouldBe 1
        val token = result.head
        token._1 shouldEqual confirmationData.productTokenKey
        token._2.productTokenKey shouldBe confirmationData.productTokenKey
        token._2.characteristics.productTypeId shouldBe addOnSample.productType.id
        token._2.characteristics.startDate shouldBe Some(confirmationData.startDateTime)
        token._2.characteristics.endDate shouldBe Some(confirmationData.endDateTime)
        token._2.bundle.get.addOnProductIds shouldBe addOnSample.productRefIds
        token._2.financials.get.productPaymentModel shouldBe confirmationData.paymentModel
        token._2.financials.get.priceBreakdown shouldEqual confirmationData.priceBreakdown
        token._2.supplier.get.supplierId shouldBe confirmationData.supplier.get.supplierId
        token._2.supplier.get.supplierData shouldBe confirmationData.supplier.get.supplierData
        token._2.meta shouldEqual confirmationData.metas
        token._2.productPayment.nonEmpty shouldBe true
      }

      "map CEG Fast track to creation token if enabled" in {
        val addOnSample = sampleAddOn().copy(productType = ProductType.TripProtection)
        val cegUpsellAddOn = sampleAddOn().copy(
          productType = ProductType.CEGFastTrack,
          confirmationData = sampleAddOn().confirmationData.map { e =>
            e.copy(productTokenKey = "mock-ceg-upsell")
          }
        )

        val tripProtectionConfirmationData: AddOnConfirmationData = addOnSample.confirmationData.head
        val cegUpsellConfirmationData: AddOnConfirmationData      = cegUpsellAddOn.confirmationData.head

        val productsWithAddons =
          MockProductUtils.createMockProduct().copy(addOnDataV2 = Seq(addOnSample, cegUpsellAddOn))
        val result: GenericAddOnBookingModel = toAddOn(productsWithAddons, true, false)
        result.size shouldBe 2
        val tripProtectionToken = result(tripProtectionConfirmationData.productTokenKey)
        tripProtectionToken.productTokenKey shouldBe tripProtectionConfirmationData.productTokenKey
        tripProtectionToken.characteristics.productTypeId shouldBe addOnSample.productType.id
        tripProtectionToken.characteristics.startDate shouldBe Some(tripProtectionConfirmationData.startDateTime)
        tripProtectionToken.characteristics.endDate shouldBe Some(tripProtectionConfirmationData.endDateTime)
        tripProtectionToken.bundle.get.addOnProductIds shouldBe addOnSample.productRefIds
        tripProtectionToken.financials.get.productPaymentModel shouldBe tripProtectionConfirmationData.paymentModel
        tripProtectionToken.financials.get.priceBreakdown shouldEqual tripProtectionConfirmationData.priceBreakdown
        tripProtectionToken.supplier.get.supplierId shouldBe tripProtectionConfirmationData.supplier.get.supplierId
        tripProtectionToken.supplier.get.supplierData shouldBe tripProtectionConfirmationData.supplier.get.supplierData
        tripProtectionToken.meta shouldEqual tripProtectionConfirmationData.metas
        tripProtectionToken.productPayment.nonEmpty shouldBe true

        val cegUpsellToken = result(cegUpsellConfirmationData.productTokenKey)
        cegUpsellToken.productTokenKey shouldBe cegUpsellToken.productTokenKey
        cegUpsellToken.characteristics.productTypeId shouldBe cegUpsellAddOn.productType.id
        cegUpsellToken.characteristics.startDate shouldBe Some(cegUpsellConfirmationData.startDateTime)
        cegUpsellToken.characteristics.endDate shouldBe Some(cegUpsellConfirmationData.endDateTime)
        cegUpsellToken.bundle.get.addOnProductIds shouldBe cegUpsellAddOn.productRefIds
        cegUpsellToken.financials.get.productPaymentModel shouldBe cegUpsellConfirmationData.paymentModel
        cegUpsellToken.financials.get.priceBreakdown shouldEqual cegUpsellConfirmationData.priceBreakdown
        cegUpsellToken.supplier.get.supplierId shouldBe cegUpsellConfirmationData.supplier.get.supplierId
        cegUpsellToken.supplier.get.supplierData shouldBe cegUpsellConfirmationData.supplier.get.supplierData
        cegUpsellToken.meta shouldEqual cegUpsellConfirmationData.metas
        cegUpsellToken.productPayment.nonEmpty shouldBe true
      }

      "map flightCheckIn to creation token if enabled : SKYA-9299 is B side" in {
        val addOnSample = sampleAddOn().copy(productType = ProductType.TripProtection)
        val flightCheckInAddOn = sampleAddOn().copy(
          productType = ProductType.FlightCheckIn,
          confirmationData = sampleAddOn().confirmationData.map { e =>
            e.copy(productTokenKey = "mock-flight-check-in")
          }
        )

        val tripProtectionConfirmationData: AddOnConfirmationData = addOnSample.confirmationData.head
        val flightCheckInConfirmationData: AddOnConfirmationData  = flightCheckInAddOn.confirmationData.head

        val productsWithAddons =
          MockProductUtils.createMockProduct().copy(addOnDataV2 = Seq(addOnSample, flightCheckInAddOn))
        val result: GenericAddOnBookingModel = toAddOn(productsWithAddons, false, true)
        result.size shouldBe 2
        val tripProtectionToken = result(tripProtectionConfirmationData.productTokenKey)
        tripProtectionToken.productTokenKey shouldBe tripProtectionConfirmationData.productTokenKey
        tripProtectionToken.characteristics.productTypeId shouldBe addOnSample.productType.id
        tripProtectionToken.characteristics.startDate shouldBe Some(tripProtectionConfirmationData.startDateTime)
        tripProtectionToken.characteristics.endDate shouldBe Some(tripProtectionConfirmationData.endDateTime)
        tripProtectionToken.bundle.get.addOnProductIds shouldBe addOnSample.productRefIds
        tripProtectionToken.financials.get.productPaymentModel shouldBe tripProtectionConfirmationData.paymentModel
        tripProtectionToken.financials.get.priceBreakdown shouldEqual tripProtectionConfirmationData.priceBreakdown
        tripProtectionToken.supplier.get.supplierId shouldBe tripProtectionConfirmationData.supplier.get.supplierId
        tripProtectionToken.supplier.get.supplierData shouldBe tripProtectionConfirmationData.supplier.get.supplierData
        tripProtectionToken.meta shouldEqual tripProtectionConfirmationData.metas
        tripProtectionToken.productPayment.nonEmpty shouldBe true

        val flightCheckInToken = result(flightCheckInConfirmationData.productTokenKey)
        flightCheckInToken.productTokenKey shouldBe flightCheckInToken.productTokenKey
        flightCheckInToken.characteristics.productTypeId shouldBe flightCheckInAddOn.productType.id
        flightCheckInToken.characteristics.startDate shouldBe Some(flightCheckInConfirmationData.startDateTime)
        flightCheckInToken.characteristics.endDate shouldBe Some(flightCheckInConfirmationData.endDateTime)
        flightCheckInToken.bundle.get.addOnProductIds shouldBe flightCheckInAddOn.productRefIds
        flightCheckInToken.financials.get.productPaymentModel shouldBe flightCheckInConfirmationData.paymentModel
        flightCheckInToken.financials.get.priceBreakdown shouldEqual flightCheckInConfirmationData.priceBreakdown
        flightCheckInToken.supplier.get.supplierId shouldBe flightCheckInConfirmationData.supplier.get.supplierId
        flightCheckInToken.supplier.get.supplierData shouldBe flightCheckInConfirmationData.supplier.get.supplierData
        flightCheckInToken.meta shouldEqual flightCheckInConfirmationData.metas
        flightCheckInToken.productPayment.nonEmpty shouldBe true
      }
    }

    ()
  }
}
