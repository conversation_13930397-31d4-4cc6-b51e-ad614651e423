package com.agoda.bapi.server.handler

import akka.http.scaladsl.testkit.ScalatestRouteTest
import com.agoda.adp.messaging.message.Message
import com.agoda.bapi.common.MessageService
import com.agoda.bapi.common.config.KillSwitches
import com.agoda.bapi.common.constants.UserPointBalanceErrorCode
import com.agoda.bapi.common.directive.BookingAPIHttpHeader
import com.agoda.bapi.common.exception.{BookingCreationExceptionBase, BookingUserPointBalanceExceptionForResponse, ProductNotFoundException}
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.logging.BapiLogger
import com.agoda.bapi.common.message.InitializeBookingSubStatus.StayPeriodNotAllowed
import com.agoda.bapi.common.message._
import com.agoda.bapi.common.message.creation.{BookingCreationContext, UserAgent}
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.model.creation.StayType
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{UserContextMock, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.reporting.logs.RequestContextMarker
import com.agoda.bapi.common.service.{FeatureAware, MessagesBag}
import com.agoda.bapi.creation.service.BcreEndpointMessageService.BcreSetupRequestResponse
import com.agoda.bapi.creation.service.observability.LogContext
import com.agoda.bapi.creation.service.{BcreEndpointMessageServiceImpl, HadoopMessagingService}
import com.agoda.bapi.server.facades.SetupBookingFacade
import com.agoda.bapi.server.handler.context.{SetupBookingContext, SetupBookingContextBuilder}
import com.agoda.bapi.server.reporting.BapiSetupV3DebugLogMessage
import com.agoda.bapi.server.validator.SetupBookingRequestValidator
import com.agoda.mpb.common.MultiProductType
import com.agoda.upi.models.enums.ProductEntries
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import com.softwaremill.quicklens._
import com.typesafe.scalalogging.Logger
import mocks.{PropertyMock, RequestContextMock, SetupBookingRequestMock}
import org.joda.time.LocalDate
import org.mockito.ArgumentMatchers.{any, eq => eqTo}
import org.mockito.BDDMockito.`given`
import org.mockito.Mockito._
import org.mockito.{ArgumentCaptor, ArgumentMatchers}
import org.scalatest.BeforeAndAfterEach
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.TableDrivenPropertyChecks
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.{Logger => UnderlyingLogger}

import scala.concurrent.Future

class SetupItineraryHandlerSpec
    extends AsyncWordSpec
    with Matchers
    with ScalatestRouteTest
    with MockitoSugar
    with BeforeAndAfterEach
    with RequestContextMock
    with PropertyMock
    with SetupBookingRequestMock
    with TableDrivenPropertyChecks {

  val setupBookingFacade                = mock[SetupBookingFacade]
  val setupBookingRequestValidator      = mock[SetupBookingRequestValidator]
  val messagesBag                       = mock[MessagesBag]
  val setupBookingRequestContextBuilder = mock[SetupBookingContextBuilder]
  val messageService                    = mock[MessageService]
  val bapiLogger                        = mock[BapiLogger]
  val hadoopMessagingService            = mock[HadoopMessagingService]
  val killSwitches                      = mock[KillSwitches]

  implicit val context = requestContext(messagesBag)

  private val bcreEndpointMessageService = new BcreEndpointMessageServiceImpl(messageService, killSwitches)
  private val setupBookingRequest = SetupBookingRequest(
    correlationId = None,
    userContext = Some(UserContextMock.value.copy(requestOrigin = "TH")),
    paymentRequest = Some(
      PaymentRequest(
        ccBin = None,
        selectedPaymentMethod = None
      )
    ),
    customerInfo = None,
    productsRequest = ProductsRequest(
      bookingToken = None,
      propertyRequests = Seq(),
      flightRequests = Seq(),
      packageRequest = None
    )
  )

  private val handler =
    new SetupItineraryHandlerImpl(
      setupBookingRequestValidator,
      setupBookingFacade,
      messageService,
      setupBookingRequestContextBuilder,
      bapiLogger,
      hadoopMessagingService,
      bcreEndpointMessageService
    )

  override def beforeEach: Unit = {
    reset(setupBookingFacade)
    reset(setupBookingRequestValidator)
    reset(messagesBag)
    reset(setupBookingRequestContextBuilder)
    reset(hadoopMessagingService)
    reset(messageService)
    reset(killSwitches)

    when(setupBookingRequestValidator.validate(any[SetupBookingRequest])(any[SetupBookingContext]))
      .thenReturn(Future.successful(None))

    when(setupBookingRequestContextBuilder.build(any[SetupBookingRequest], any[BookingAPIHttpHeader]))
      .thenReturn(
        Future.successful(
          SetupBookingContext(
            BookingFlow.Package,
            context,
            "",
            WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration()),
            None,
            sessionId = "",
            bookingSessionId = "",
            logContext = LogContext()
          )
        )
      )
  }

  "SetupBookingHandler" should {
    "returns mock response..." in {
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])

      when(setupBookingFacade.setupBooking(any[SetupBookingRequest])(any[SetupBookingContext]))
        .thenReturn(Future.successful(SetupBookingResponse(success = true)))

      handler
        .setupItinerary(
          BookingAPIHttpHeader("/", 1, "", "", WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration), ""),
          "",
          setupBookingRequest
        )
        .map { response =>
          verify(setupBookingFacade, times(1)).setupBooking(any[SetupBookingRequest])(any[SetupBookingContext])
          verify(messageService, times(1)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreSetupRequestResponse]
          response.success shouldBe true
        }
    }

    "with request validation error" in {
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])

      when(setupBookingRequestValidator.validate(any[SetupBookingRequest])(any[SetupBookingContext]))
        .thenReturn(Future.successful(Some(SetupBookingResponse())))

      handler
        .setupItinerary(
          BookingAPIHttpHeader("/", 1, "", "", WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration), ""),
          "",
          setupBookingRequest
        )
        .map { response =>
          verify(setupBookingFacade, times(0)).setupBooking(any[SetupBookingRequest])(any[SetupBookingContext])
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(0) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(1) shouldBe a[BcreSetupRequestResponse]
          response.success shouldBe false
        }
    }

    "determine call setupBookingRequestContextBuilder with correct input parameters" in
      handler
        .setupItinerary(
          BookingAPIHttpHeader("/", 1, "", "", WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration), ""),
          "",
          SetupBookingRequest(
            productsRequest = ProductsRequest(
              packageRequest = Some(PackageRequest("client-token", Some("package")))
            )
          )
        )
        .map { response =>
          verify(setupBookingRequestContextBuilder, times(1))
            .build(any[SetupBookingRequest], any[BookingAPIHttpHeader])
          response.success shouldBe false
        }

    "return ProductNotFound by logging ProductNotFoundException as warning" in {
      val propertySearchCriteria = mock[PropertySearchCriteria]
      val loggerMock             = mock[UnderlyingLogger]
      val mockHandler = spy(
        new SetupItineraryHandlerImpl(
          setupBookingRequestValidator,
          setupBookingFacade,
          messageService,
          setupBookingRequestContextBuilder,
          bapiLogger,
          hadoopMessagingService,
          bcreEndpointMessageService
        ) {
          override def logger: Logger = com.typesafe.scalalogging.Logger(loggerMock)
        }
      )

      given(setupBookingFacade.setupBooking(any())(any())).willAnswer(_ =>
        throw ProductNotFoundException("No rooms available!")
      )
      when(propertySearchCriteria.propertyId).thenReturn(Some(1L))
      when(
        setupBookingRequestContextBuilder.build(any[SetupBookingRequest], any[BookingAPIHttpHeader])
      )
        .thenReturn(
          Future.successful(
            SetupBookingContext(
              BookingFlow.Package,
              context,
              "",
              WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration()),
              None,
              sessionId = "",
              bookingSessionId = "",
              logContext = LogContext()
            )
          )
        )
      when(loggerMock.isWarnEnabled(any())).thenReturn(true)

      mockHandler
        .setupItinerary(
          bookingHttpHeader =
            BookingAPIHttpHeader("/", 1, "", "", WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration), ""),
          ip = "",
          request = SetupBookingRequest(
            productsRequest = ProductsRequest(
              propertyRequests = Seq(PropertyRequestItem("x1", propertySearchCriteria, None))
            )
          )
        )
        .map { response =>
          verify(loggerMock, times(1)).warn(
            ArgumentMatchers.any(classOf[RequestContextMarker]),
            ArgumentMatchers.eq("Product not found (No rooms available!)"),
            ArgumentMatchers.isNull()
          )
          response shouldBe SetupBookingResponse(
            success = true,
            bookingResponse = Some(
              SetupBookingResponseInfo(serverStatus =
                InitializeBookingServerStatus(
                  InitializeBookingStatus.ProductItemNotFound,
                  InitializeBookingStatusCategory.InvalidProductItem
                )
              )
            )
          )
        }
    }

    "return ExternalLoyaltyPointsError with correct subStatus" when {
      val testCase = Table(
        ("userPointBalanceErrorCode", "expectedSubStatus"),
        (None, InitializeBookingSubStatus.InsufficientPointsBalance),
        (
          Some(UserPointBalanceErrorCode.SoftInsufficientPointsBalance),
          InitializeBookingSubStatus.SoftInsufficientPointsBalance
        ),
        (
          Some(UserPointBalanceErrorCode.InsufficientPointsBalance),
          InitializeBookingSubStatus.InsufficientPointsBalance
        ),
        (
          Some(UserPointBalanceErrorCode.HardInsufficientPointsBalance),
          InitializeBookingSubStatus.HardInsufficientPointsBalance
        )
      )

      forAll(testCase) { (userPointBalanceErrorCode, expectedSubStatus) =>
        s"BookingUserPointBalanceExceptionForResponse is thrown with userPointBalanceErrorCode $userPointBalanceErrorCode" in {
          val propertySearchCriteria = mock[PropertySearchCriteria]
          val expectedException =
            BookingUserPointBalanceExceptionForResponse(
              "simulated error",
              userPointBalanceErrorCode = userPointBalanceErrorCode
            )
          given(setupBookingFacade.setupBooking(any())(any())).willAnswer(_ => throw expectedException)
          handler
            .setupItinerary(
              BookingAPIHttpHeader("/", 1, "", "", WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration), ""),
              "",
              SetupBookingRequest(
                productsRequest = ProductsRequest(
                  propertyRequests = Seq(PropertyRequestItem("x1", propertySearchCriteria, None))
                )
              )
            )
            .map { response =>
              response.success shouldBe true
              response.errorCode shouldBe None
              response.errorMessage shouldBe None
              val serverStatusOpt = response.bookingResponse.map(_.serverStatus)
              serverStatusOpt.map(_.status) shouldBe Some(InitializeBookingStatus.ExternalLoyaltyPointsError)
              serverStatusOpt.flatMap(_.subStatus) shouldBe Some(expectedSubStatus)
            }
        }
      }
    }

    "should return internal error by handling BookingApiExceptionBase exception" in {
      val propertySearchCriteria = mock[PropertySearchCriteria]
      val expectedException      = new BookingCreationExceptionBase(ResponseErrorCode.InvalidRequest, "simulated error") {}
      given(setupBookingFacade.setupBooking(any())(any())).willAnswer(_ => throw expectedException)
      handler
        .setupItinerary(
          BookingAPIHttpHeader("/", 1, "", "", WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration), ""),
          "",
          SetupBookingRequest(
            productsRequest = ProductsRequest(
              propertyRequests = Seq(PropertyRequestItem("x1", propertySearchCriteria, None))
            )
          )
        )
        .map { response =>
          response.success shouldBe false
          response.errorCode shouldBe Some(expectedException.getErrorCode.toString)
          response.errorMessage shouldBe Some(expectedException.getMessage)
          response.bookingResponse shouldBe empty
        }
    }

    "returns map unexpected error from setupBooking correctly" in {
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])

      when(setupBookingFacade.setupBooking(any[SetupBookingRequest])(any[SetupBookingContext]))
        .thenReturn(
          Future.failed(new Exception("Escaped Exception that hasn't converted into BookingApiExceptionBase!"))
        )

      handler
        .setupItinerary(
          BookingAPIHttpHeader("/", 1, "", "", WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration), ""),
          "",
          setupBookingRequest
        )
        .map { response =>
          verify(setupBookingFacade, times(1)).setupBooking(any[SetupBookingRequest])(any[SetupBookingContext])
          verify(messageService, times(1)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreSetupRequestResponse]

          response shouldBe SetupBookingResponse(
            success = false,
            errorCode = Some(ResponseErrorCode.InternalError.toString),
            errorMessage = Some("Escaped Exception that hasn't converted into BookingApiExceptionBase!")
          )
        }
    }
  }

  "createLoggerTags" should {
    "create Map of Logging Tags in camelCase " in {
      val setupRequest = setupBookingRequest
        .modify(_.bookingContext)
        .setTo(Some(BookingCreationContext("mySession", UserAgent())))
        .modify(_.userContext.each.experimentData.each.userId)
        .setTo("myUser")
        .modify(_.userContext.each.requestOrigin)
        .setTo("myOrigin")
        .modify(_.deviceContext)
        .setTo(Some(DeviceContext(DevicePlatform.WebDesktop)))
        .modify(_.productsRequest)
        .setTo(ProductsRequest())
      implicit val setupBooking = SetupBookingContext(
        BookingFlow.Package,
        context,
        "",
        WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration()),
        None,
        sessionId = "",
        bookingSessionId = "",
        logContext = LogContext()
      )
      val loggerTagMap = handler.createLoggerTags(setupRequest)
      val expectedMap = Map(
        "userId"      -> "myUser",
        "origin"      -> "myOrigin",
        "sessionId"   -> "mySession",
        "deviceType"  -> "WebDesktop",
        "productType" -> "Package"
      )
      loggerTagMap.toSet.union(expectedMap.toSet) shouldBe loggerTagMap.toSet
    }

    "create Map of Logging Tags in camelCase if cartPricingContext is defined and isActivityBFWebPlatform is true" in {
      val featureAware           = mock[FeatureAware]
      val context                = requestContext(messagesBag).copy(featureAware = Some(featureAware))
      val mockActivity           = mock[ActivityRequestItem]
      val mockCartPricingContext = mock[CartPricingContext]
      val setupRequest = setupBookingRequest
        .modify(_.bookingContext)
        .setTo(Some(BookingCreationContext("mySession", UserAgent())))
        .modify(_.userContext.each.experimentData.each.userId)
        .setTo("myUser")
        .modify(_.userContext.each.requestOrigin)
        .setTo("myOrigin")
        .modify(_.deviceContext)
        .setTo(Some(DeviceContext(DevicePlatform.WebDesktop)))
        .modify(_.productsRequest)
        .setTo(
          ProductsRequest(
            activityRequests = Some(Seq(mockActivity)),
            cartPricingContext = Some(mockCartPricingContext)
          )
        )
      implicit val setupBooking = SetupBookingContext(
        BookingFlow.Cart,
        context,
        "",
        WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration()),
        None,
        sessionId = "",
        bookingSessionId = "",
        logContext = LogContext()
      )
      val loggerTagMap = handler.createLoggerTags(setupRequest)
      val expectedMap = Map(
        "userId"           -> "myUser",
        "origin"           -> "myOrigin",
        "sessionId"        -> "mySession",
        "deviceType"       -> "WebDesktop",
        "productType"      -> "Cart",
        "multiProductType" -> "SingleActivity"
      )
      loggerTagMap.toSet.union(expectedMap.toSet) shouldBe loggerTagMap.toSet
    }

    "create Map of Logging Tags in camelCase if cartPricingContext is defined and isActivityBFWebPlatform is false" in {
      val featureAware           = mock[FeatureAware]
      val context                = requestContext(messagesBag).copy(featureAware = Some(featureAware))
      val mockActivity           = mock[ActivityRequestItem]
      val mockCartPricingContext = mock[CartPricingContext]
      val setupRequest = setupBookingRequest
        .modify(_.bookingContext)
        .setTo(Some(BookingCreationContext("mySession", UserAgent())))
        .modify(_.userContext.each.experimentData.each.userId)
        .setTo("myUser")
        .modify(_.userContext.each.requestOrigin)
        .setTo("myOrigin")
        .modify(_.deviceContext)
        .setTo(Some(DeviceContext(DevicePlatform.AppIPhone)))
        .modify(_.productsRequest)
        .setTo(
          ProductsRequest(
            activityRequests = Some(Seq(mockActivity)),
            cartPricingContext = Some(mockCartPricingContext)
          )
        )
      implicit val setupBooking = SetupBookingContext(
        BookingFlow.Cart,
        context,
        "",
        WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration()),
        None,
        sessionId = "",
        bookingSessionId = "",
        logContext = LogContext()
      )
      val loggerTagMap = handler.createLoggerTags(setupRequest)
      val expectedMap = Map(
        "userId"      -> "myUser",
        "origin"      -> "myOrigin",
        "sessionId"   -> "mySession",
        "deviceType"  -> "AppIPhone",
        "productType" -> "Cart"
      )
      loggerTagMap.toSet.union(expectedMap.toSet) shouldBe loggerTagMap.toSet
    }

    "create Map of Logging Tags in camelCase if cartPricingContext is defined and enableActivityCartBFWebview is false" in {
      val featureAware           = mock[FeatureAware]
      val context                = requestContext(messagesBag).copy(featureAware = Some(featureAware))
      val mockActivity           = mock[ActivityRequestItem]
      val mockCartPricingContext = mock[CartPricingContext]
      val setupRequest = setupBookingRequest
        .modify(_.bookingContext)
        .setTo(Some(BookingCreationContext("mySession", UserAgent())))
        .modify(_.userContext.each.experimentData.each.userId)
        .setTo("myUser")
        .modify(_.userContext.each.requestOrigin)
        .setTo("myOrigin")
        .modify(_.deviceContext)
        .setTo(Some(DeviceContext(DevicePlatform.WebDesktop)))
        .modify(_.productsRequest)
        .setTo(
          ProductsRequest(
            activityRequests = Some(Seq(mockActivity)),
            cartPricingContext = Some(mockCartPricingContext)
          )
        )
      implicit val setupBooking = SetupBookingContext(
        BookingFlow.Cart,
        context,
        "",
        WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration()),
        None,
        sessionId = "",
        bookingSessionId = "",
        logContext = LogContext()
      )
      val loggerTagMap = handler.createLoggerTags(setupRequest)
      val expectedMap = Map(
        "userId"      -> "myUser",
        "origin"      -> "myOrigin",
        "sessionId"   -> "mySession",
        "deviceType"  -> "WebDesktop",
        "productType" -> "Cart"
      )
      loggerTagMap.toSet.union(expectedMap.toSet) shouldBe loggerTagMap.toSet
    }
  }
  "createLoggerTagsWithBookingSession" should {
    "create Map of Logging Tags with Booking Session in camelCase " in {
      val setupRequest = setupBookingRequest
        .modify(_.bookingContext)
        .setTo(Some(BookingCreationContext("mySession", UserAgent(), bookingSessionId = Some("myBookingSession"))))
        .modify(_.userContext.each.experimentData.each.userId)
        .setTo("myUser")
        .modify(_.userContext.each.requestOrigin)
        .setTo("myOrigin")
        .modify(_.deviceContext)
        .setTo(Some(DeviceContext(DevicePlatform.WebDesktop)))
        .modify(_.productsRequest)
        .setTo(ProductsRequest())
      implicit val setupBooking = SetupBookingContext(
        BookingFlow.Package,
        context,
        "",
        WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration()),
        None,
        sessionId = "",
        bookingSessionId = "myBookingSession",
        logContext = LogContext()
      )
      val loggerTagMap = handler.createLoggerTags(setupRequest)
      val expectedMap = Map(
        "userId"      -> "myUser",
        "origin"      -> "myOrigin",
        "sessionId"   -> "mySession",
        "deviceType"  -> "WebDesktop",
        "productType" -> "Package"
      )
      loggerTagMap.toSet.union(expectedMap.toSet) shouldBe loggerTagMap.toSet
    }
  }
  "createLoggerTagsWithoutBookingSession" should {
    "create Map of Logging Tags with null Booking Session in camelCase " in {
      val setupRequest = setupBookingRequest
        .modify(_.bookingContext)
        .setTo(Some(BookingCreationContext("mySession", UserAgent(), bookingSessionId = None)))
        .modify(_.userContext.each.experimentData.each.userId)
        .setTo("myUser")
        .modify(_.userContext.each.requestOrigin)
        .setTo("myOrigin")
        .modify(_.deviceContext)
        .setTo(Some(DeviceContext(DevicePlatform.WebDesktop)))
        .modify(_.productsRequest)
        .setTo(ProductsRequest())
      implicit val setupBooking = SetupBookingContext(
        BookingFlow.Package,
        context,
        "",
        WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration()),
        None,
        sessionId = "",
        bookingSessionId = ""
      )
      val loggerTagMap = handler.createLoggerTags(setupRequest)
      val expectedMap = Map(
        "userId"      -> "myUser",
        "origin"      -> "myOrigin",
        "sessionId"   -> "mySession",
        "deviceType"  -> "WebDesktop",
        "productType" -> "Package"
      )
      loggerTagMap.toSet.union(expectedMap.toSet) shouldBe loggerTagMap.toSet
    }
  }
  "setupItinerary" should {
    "produce BapiProductSetupFact message" in
      handler
        .setupItinerary(
          BookingAPIHttpHeader("/", 1, "", "", WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration), ""),
          "",
          SetupBookingRequest(
            productsRequest = ProductsRequest(
              packageRequest = Some(PackageRequest("client-token", Some("package")))
            )
          )
        )
        .map { response =>
          verify(hadoopMessagingService, times(1))
            .generateAndSendBapiSetupFactMessage(
              any[SetupBookingRequest],
              any[SetupBookingResponse],
              eqTo(MultiProductType.Package),
              any[LogContext],
              any[String],
              any[String]
            )(any[RequestContext])
          response.success shouldBe false
        }
  }
  "createMetricTags" should {
    "create map of logging tags" in {
      val propertySearchCriteria = mock[PropertySearchCriteria]
      val occupancyRequest       = mock[OccupancyRequest]
      implicit val setupBookingContext = SetupBookingContext(
        BookingFlow.SingleProperty,
        requestContext = context,
        correlationId = "",
        whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration()),
        deviceContext = None,
        sessionId = "",
        bookingSessionId = "",
        logContext = LogContext()
      )
      when(propertySearchCriteria.occupancyRequest).thenReturn(
        occupancyRequest
      )
      when(propertySearchCriteria.simplifiedRoomSelectionRequest).thenReturn(None)
      when(occupancyRequest.overrideExtraBed).thenReturn(None)
      when(propertySearchCriteria.durationRequest).thenReturn(
        DurationRequest(
          checkIn = LocalDate.now(),
          lengthOfStay = 1,
          stayType = Some(StayType.Hourly),
          selectedHourlySlot = None
        )
      )
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          propertyRequests = Seq(PropertyRequestItem("x1", propertySearchCriteria, None))
        ),
        userContext = Some(UserContextMock.value.copy(requestOrigin = "TH"))
      )
      val actual = handler.createMetricTags(request, None)
      val expected = Map(
        "product_type"          -> "SingleProperty",
        "booking_status"        -> "Error",
        "initialize_sub_status" -> "Unknown",
        "device_type"           -> "Unknown",
        "isCCOF"                -> "false",
        "stay_package_type"     -> "0",
        "stay_type"             -> "1",
        "platform_id"           -> "1",
        "dmc_id"                -> "0",
        "site_id"               -> "-1",
        "isInternalTraffic"     -> "false",
        "origin"                -> "TH"
      )

      actual shouldBe expected
    }

    "create map of logging tags if cartPricingContext is defined and no deviceContext" in {
      val featureAware = mock[FeatureAware]
      when(featureAware.buildSeenExperimentTags(any())).thenReturn(Map.empty[String, String])
      val context = requestContext(messagesBag).copy(featureAware = Some(featureAware))
      implicit val setupBookingContext = SetupBookingContext(
        BookingFlow.Cart,
        requestContext = context,
        correlationId = "",
        whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration()),
        deviceContext = None,
        sessionId = "",
        bookingSessionId = "",
        logContext = LogContext()
      )
      val mockActivity       = mock[ActivityRequestItem]
      val cartPricingContext = mock[CartPricingContext]
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          activityRequests = Some(Seq(mockActivity)),
          cartPricingContext = Some(cartPricingContext)
        ),
        userContext = Some(UserContextMock.value.copy(requestOrigin = "TH"))
      )
      val actual = handler.createMetricTags(request, None)
      val expected = Map(
        "product_type"          -> "Cart",
        "booking_status"        -> "Error",
        "initialize_sub_status" -> "Unknown",
        "device_type"           -> "Unknown",
        "isCCOF"                -> "false",
        "stay_package_type"     -> "0",
        "stay_type"             -> "0",
        "platform_id"           -> "1",
        "dmc_id"                -> "0",
        "site_id"               -> "-1",
        "isInternalTraffic"     -> "false",
        "origin"                -> "TH"
      )

      actual shouldBe expected
    }

    "create map of logging tags if cartPricingContext is defined and deviceContext is AppIPhone" in {
      val featureAware = mock[FeatureAware]
      when(featureAware.buildSeenExperimentTags(any())).thenReturn(Map.empty[String, String])
      val context = requestContext(messagesBag).copy(featureAware = Some(featureAware))
      implicit val setupBookingContext = SetupBookingContext(
        BookingFlow.Cart,
        requestContext = context,
        correlationId = "",
        whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration()),
        deviceContext = None,
        sessionId = "",
        bookingSessionId = "",
        logContext = LogContext()
      )
      val mockActivity       = mock[ActivityRequestItem]
      val cartPricingContext = mock[CartPricingContext]
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          activityRequests = Some(Seq(mockActivity)),
          cartPricingContext = Some(cartPricingContext)
        ),
        userContext = Some(UserContextMock.value.copy(requestOrigin = "TH")),
        deviceContext = Some(DeviceContext(DevicePlatform.AppIPhone))
      )
      val actual = handler.createMetricTags(request, None)
      val expected = Map(
        "product_type"          -> "Cart",
        "multi_product_type"    -> "SingleActivity",
        "booking_status"        -> "Error",
        "initialize_sub_status" -> "Unknown",
        "device_type"           -> "AppIPhone",
        "isCCOF"                -> "false",
        "stay_package_type"     -> "0",
        "stay_type"             -> "0",
        "platform_id"           -> "1",
        "dmc_id"                -> "0",
        "site_id"               -> "-1",
        "isInternalTraffic"     -> "false",
        "origin"                -> "TH"
      )

      actual shouldBe expected
    }

    "create map of logging tags if cartPricingContext is defined" in {
      val featureAware = mock[FeatureAware]
      when(featureAware.buildSeenExperimentTags(any())).thenReturn(Map.empty[String, String])
      val context = requestContext(messagesBag).copy(featureAware = Some(featureAware))
      implicit val setupBookingContext = SetupBookingContext(
        BookingFlow.Cart,
        requestContext = context,
        correlationId = "",
        whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration()),
        deviceContext = None,
        sessionId = "",
        bookingSessionId = "",
        logContext = LogContext()
      )
      val mockActivity       = mock[ActivityRequestItem]
      val cartPricingContext = mock[CartPricingContext]
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          activityRequests = Some(Seq(mockActivity)),
          cartPricingContext = Some(cartPricingContext)
        ),
        userContext = Some(UserContextMock.value.copy(requestOrigin = "TH"))
      )
      val actual = handler.createMetricTags(request, None)
      val expected = Map(
        "product_type"          -> "Cart",
        "booking_status"        -> "Error",
        "initialize_sub_status" -> "Unknown",
        "device_type"           -> "Unknown",
        "isCCOF"                -> "false",
        "stay_package_type"     -> "0",
        "stay_type"             -> "0",
        "platform_id"           -> "1",
        "dmc_id"                -> "0",
        "site_id"               -> "-1",
        "isInternalTraffic"     -> "false",
        "origin"                -> "TH"
      )

      actual shouldBe expected
    }

    "create map of logging tags if cartPricingContext is defined and deviceContext is AppAndroidPhone" in {
      val featureAware = mock[FeatureAware]
      when(featureAware.buildSeenExperimentTags(any())).thenReturn(Map.empty[String, String])
      val context = requestContext(messagesBag).copy(featureAware = Some(featureAware))
      implicit val setupBookingContext = SetupBookingContext(
        BookingFlow.Cart,
        requestContext = context,
        correlationId = "",
        whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration()),
        deviceContext = None,
        sessionId = "",
        bookingSessionId = "",
        logContext = LogContext()
      )
      val mockActivity       = mock[ActivityRequestItem]
      val cartPricingContext = mock[CartPricingContext]
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          activityRequests = Some(Seq(mockActivity)),
          cartPricingContext = Some(cartPricingContext)
        ),
        userContext = Some(UserContextMock.value.copy(requestOrigin = "TH")),
        deviceContext = Some(DeviceContext(DevicePlatform.AppAndroidPhone))
      )
      val actual = handler.createMetricTags(request, None)
      val expected = Map(
        "product_type"          -> "Cart",
        "multi_product_type"    -> "SingleActivity",
        "booking_status"        -> "Error",
        "initialize_sub_status" -> "Unknown",
        "device_type"           -> "AppAndroidPhone",
        "isCCOF"                -> "false",
        "stay_package_type"     -> "0",
        "stay_type"             -> "0",
        "platform_id"           -> "1",
        "dmc_id"                -> "0",
        "site_id"               -> "-1",
        "isInternalTraffic"     -> "false",
        "origin"                -> "TH"
      )

      actual shouldBe expected
    }

    "create map of logging tags when setup response return sub_status" in {
      val propertySearchCriteria = mock[PropertySearchCriteria]
      val occupancyRequest       = mock[OccupancyRequest]
      val mockSetupResponse = SetupBookingResponse(bookingResponse =
        Some(
          SetupBookingResponseInfo(serverStatus =
            InitializeBookingServerStatus(InitializeBookingStatus.TokenExpired).withSubStatus(
              Some(StayPeriodNotAllowed)
            )
          )
        )
      )
      implicit val setupBookingContext = SetupBookingContext(
        BookingFlow.SingleProperty,
        requestContext = context,
        correlationId = "",
        whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration()),
        deviceContext = None,
        sessionId = "",
        bookingSessionId = "",
        logContext = LogContext()
      )
      when(propertySearchCriteria.occupancyRequest).thenReturn(
        occupancyRequest
      )
      when(propertySearchCriteria.simplifiedRoomSelectionRequest).thenReturn(None)
      when(occupancyRequest.overrideExtraBed).thenReturn(None)
      when(propertySearchCriteria.durationRequest).thenReturn(
        DurationRequest(
          checkIn = LocalDate.now(),
          lengthOfStay = 1,
          stayType = Some(StayType.Hourly),
          selectedHourlySlot = None
        )
      )
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          propertyRequests = Seq(PropertyRequestItem("x1", propertySearchCriteria, None))
        ),
        userContext = Some(UserContextMock.value.copy(requestOrigin = "TH"))
      )
      val actual = handler.createMetricTags(request, Some(mockSetupResponse))
      val expected = Map(
        "product_type"          -> "SingleProperty",
        "booking_status"        -> "TokenExpired",
        "initialize_sub_status" -> "StayPeriodNotAllowed",
        "device_type"           -> "Unknown",
        "isCCOF"                -> "false",
        "stay_package_type"     -> "0",
        "stay_type"             -> "1",
        "platform_id"           -> "1",
        "dmc_id"                -> "0",
        "site_id"               -> "-1",
        "isInternalTraffic"     -> "false",
        "origin"                -> "TH"
      )

      actual shouldBe expected
    }

    "create map of logging tags with productEntry tag" in {
      val featureAware = mock[FeatureAware]
      when(featureAware.buildSeenExperimentTags(any())).thenReturn(Map.empty[String, String])

      val bookingContext = mock[BookingCreationContext]
      when(bookingContext.productEntry).thenReturn(Some(ProductEntries.PackageProduct))
      val context = requestContext(messagesBag).copy(
        featureAware = Some(featureAware),
        bookingCreationContext = Some(bookingContext)
      )
      implicit val setupBookingContext: SetupBookingContext = SetupBookingContext(
        BookingFlow.Cart,
        requestContext = context,
        correlationId = "",
        whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
        deviceContext = None,
        sessionId = "",
        bookingSessionId = "",
        logContext = LogContext()
      )
      val mockActivity       = mock[ActivityRequestItem]
      val cartPricingContext = mock[CartPricingContext]
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          activityRequests = Some(Seq(mockActivity)),
          cartPricingContext = Some(cartPricingContext)
        ),
        userContext = Some(UserContextMock.value.copy(requestOrigin = "TH"))
      )
      val actual = handler.createMetricTags(request, None)

      actual.get("productEntry") shouldBe Some(ProductEntries.PackageProduct.entryName)
    }

    "create map of logging tags with experiment seen tags tag" in {
      val featureAware = mock[FeatureAware]
      when(featureAware.buildSeenExperimentTags(any())).thenReturn(Map("UNIBF-XXX" -> "false", "UNIBF-YYY" -> "true"))

      val bookingContext = mock[BookingCreationContext]
      when(bookingContext.productEntry).thenReturn(Some(ProductEntries.PackageProduct))
      val context = requestContext(messagesBag).copy(
        featureAware = Some(featureAware),
        bookingCreationContext = Some(bookingContext)
      )
      implicit val setupBookingContext: SetupBookingContext = SetupBookingContext(
        BookingFlow.Cart,
        requestContext = context,
        correlationId = "",
        whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
        deviceContext = None,
        sessionId = "",
        bookingSessionId = "",
        logContext = LogContext()
      )
      val mockActivity       = mock[ActivityRequestItem]
      val cartPricingContext = mock[CartPricingContext]
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          activityRequests = Some(Seq(mockActivity)),
          cartPricingContext = Some(cartPricingContext)
        ),
        userContext = Some(UserContextMock.value.copy(requestOrigin = "TH"))
      )
      val actual = handler.createMetricTags(request, None)
      actual.get("UNIBF-XXX") shouldBe Some("false")
      actual.get("UNIBF-YYY") shouldBe Some("true")
    }
  }
}
