package com.agoda.bapi.server.utils

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.PaymentAmount
import com.agoda.bapi.common.message.pricebreakdown.{PriceBreakdownNode, PriceBreakdownResponse, PriceBreakdownType}
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.model.booking.{PaymentFlow, PaymentMethodDetailsV2}
import com.agoda.bapi.common.model.cart.CartContext
import com.agoda.bapi.common.model.consumerFintech.ConsumerFintechDetail
import com.agoda.bapi.common.model.consumerFintech.products.ConsumerFintechProductDetail
import com.agoda.bapi.common.model.consumerFintech.products.cancelAndRebookV3.CancelAndRebookV3ProductDetail
import com.agoda.bapi.common.model.creation.CreditCardInfoModel
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.model.{ChargeOption, PaymentGroupCategory, WhiteLabelInfo}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.token.flight.FlightToken
import com.agoda.bapi.common.token.property.Token
import com.agoda.bapi.common.token.{Money => TMoney, _}
import com.agoda.bapi.common.util.TokenDeserializers._
import com.agoda.bapi.common.util.TokenSerializers._
import com.agoda.bapi.common.util.{TokenDeserializers, TokenSerializer, TokenSerializers}
import com.agoda.bapi.creation.CreateMultiBookingHelper
import com.agoda.bapi.server.handler.context.{SetupBookingContext, SetupBookingSessionContext}
import com.agoda.bapi.server.model.{FlightConfirmationData, ProductData}
import com.agoda.bapi.server.model.pricebreakdown.flights.MockFlightsPricingData
import com.agoda.flights.client.v2.model.SearchResponseCurrencyPricing
import com.agoda.mpb.common.models.state.{PriceBreakdownType => MPBPriceBreakdownType}
import com.agoda.mpb.common.{BenefitAcceleratorValueUnit, BenefitCategoryType, BenefitType}
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.agoda.winterfell.output
import com.agoda.winterfell.output.{ExternalLoyaltyUserProfileResponse, LoyaltyProfile, SubLoyaltyPrograms, UserProfileInfo}
import com.fasterxml.jackson.databind.node.ObjectNode
import mocks.ProductTokenMockHelper
import org.mockito.Mockito.{reset, when}
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.{BeforeAndAfter, TryValues}
import org.scalatestplus.mockito.MockitoSugar
import scalapb.json4s.JsonFormat

import scala.util.{Failure, Success, Try}

class ProductTokenCreatorUtilsSpec
    extends AnyWordSpec
    with MockitoSugar
    with Matchers
    with TryValues
    with ProductTokenMockHelper
    with CreateMultiBookingHelper
    with BeforeAndAfter {

  val mockSetupBookingContext = mock[SetupBookingContext]
  val mockRequestContext      = mock[RequestContext]
  val mockFeatureAware        = mock[FeatureAware]
  val mockWhiteLabelInfo      = mock[WhiteLabelInfo]
  before {
    reset(mockFeatureAware)
    reset(mockWhiteLabelInfo)
    when(mockRequestContext.whiteLabelInfo).thenReturn(mockWhiteLabelInfo)
    when(mockSetupBookingContext.requestContext).thenReturn(mockRequestContext)
    when(mockRequestContext.featureAware).thenReturn(Some(mockFeatureAware))
  }

  "create multi-product token functions" should {
    val utils                 = new ProductTokenCreatorUtilsImpl(encryptedHelper = BookingTokenEncryptionHelper)
    val productKey            = "1"
    val flightBookingTokens   = Map(productKey -> Seq(defaultFlightBookingToken))
    val tripProtectionTokens  = Map(productKey -> defaultTripProtectionToken)
    val propertyBookingTokens = Map(productKey -> defaultPropertyBookingToken)
    val activityBookingTokens = Map(productKey -> defaultActivityBookingToken)
    val addOnTokens           = Map(productKey -> baseProtectionAddOnToken)
    val productRequest        = ProductsRequest()

    val externalLoyaltyNode = PriceBreakdownNode(
      value = Option(
        PriceBreakdownResponse(
          `type` = PriceBreakdownType.ExternalLoyalty,
          amount = TMoney(amount = 100d, currencyCode = "THB"),
          points = Option(100d)
        )
      )
    )

    val priceBreakdown = Option(
      PriceBreakdownNode(
        value = Option(
          PriceBreakdownResponse(
            `type` = PriceBreakdownType.TotalPrice,
            amount = TMoney(amount = 2000d, currencyCode = "THB"),
            amountBeforeDiscount = Some(TMoney(2300d, currencyCode = "THB")),
            discountType = Some(1)
          )
        ),
        breakdowns = Some(
          Seq(
            PriceBreakdownNode(
              value = Option(
                PriceBreakdownResponse(
                  `type` = PriceBreakdownType.PayAgoda,
                  amount = TMoney(amount = 2300d, currencyCode = "THB")
                )
              ),
              breakdowns = None
            ),
            PriceBreakdownNode(
              value = Option(
                PriceBreakdownResponse(
                  `type` = PriceBreakdownType.TotalSavings,
                  amount = TMoney(amount = 300d, currencyCode = "THB")
                )
              ),
              breakdowns = Option(Seq(externalLoyaltyNode))
            )
          )
        )
      )
    )
    val creditCardInfo = Some(new CreditCardInfoModel(1, None, Some("411111")))

    val selectedPaymentMethodDetailsV2 = PaymentMethodDetailsV2(
      id = 1,
      name = "Visa",
      paymentFlow = PaymentFlow.CreditCard,
      paymentGroupCategory = PaymentGroupCategory.CreditDebitCard,
      timeout = Some(1),
      gatewayName = None,
      icons = Seq(),
      remarks = Seq(),
      chargeDateTypes = ChargeOption.PayNow,
      chargeOptions = Set(ChargeOption.PayNow, ChargeOption.PayLater),
      isRecommended = false,
      ranking = 1,
      defaultCurrency = "USD",
      cardNumRegEx = Some("^[0-9]{16}$"),
      cvcRegEx = Some("^[0-9]{3}$"),
      isLuhnCheckRecommended = None,
      requiredFields = Some(Map()),
      isFapiaoEligible = Some(false),
      isTokenEnabled = Some(false)
    )

    "createMultiProductCreationBookingToken" should {

      val mockSetupBookingSession = mock[SetupBookingSessionContext]
      when(mockSetupBookingContext.session).thenReturn(mockSetupBookingSession)

      "All Pricebreakdown type should be able to convert to MPB Pricebreakdown type" in {
        val allPossiblePriceBreakdownTypeEnums = PriceBreakdownType.values

        val mpbPriceBreakdownTypes = Try {
          allPossiblePriceBreakdownTypeEnums.map { priceBreakdownType =>
            MPBPriceBreakdownType(priceBreakdownType.id)
          }
        }

        mpbPriceBreakdownTypes match {
          case Success(value)     => succeed
          case Failure(exception) => fail(s"An enum is missing in MPB Commons: $exception")
        }

      }

      "should return MultiProductCreationBookingToken with Package correctly" in {
        val mockPackages = Some(PackageRequest("client", Some("interSystem")))
        when(mockSetupBookingSession.packages).thenReturn(mockPackages)

        val flightBookingToken =
          TokenSerializers[FlightBookingModel].serialize(flightBookingTokens, None, Some(20L)).success.value
        val propertyBookingToken =
          TokenSerializers[PropertyBookingModel].serialize(propertyBookingTokens, None, Some(20L)).success.value

        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = flightBookingTokens,
            propertyCreationBookings = propertyBookingTokens,
            tripProtections = Map.empty,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = Some(selectedPaymentMethodDetailsV2),
            isNewsLetterOptedIn = false,
            isBookingFromCart = false,
            addOns = Map.empty,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .success
          .value

        result.payment shouldBe defaultMultiProductCreationToken.payment

        result.bookingFlowType shouldBe BookingFlow.Package
        result.flights.isDefined shouldBe true
        result.flights.map { token =>
          token.content shouldBe flightBookingToken.content
          token.version shouldBe flightBookingToken.version
          token.expiresAfterMinutes shouldBe flightBookingToken.expiresAfterMinutes
        }

        result.properties.isDefined shouldBe true
        result.properties.map { token =>
          token.content shouldBe propertyBookingToken.content
          token.version shouldBe propertyBookingToken.version
          token.expiresAfterMinutes shouldBe propertyBookingToken.expiresAfterMinutes
        }
        result.priceBreakdown shouldBe priceBreakdown
        result.creditCardInfo shouldBe creditCardInfo

        result.commonPayment.isDefined shouldBe true
        result.commonPayment.get.method shouldBe PaymentMethod.Visa
        result.commonPayment.get.redirectInfo shouldBe None
        result.commonPayment.get.timeoutMinutes shouldBe Some(1)
      }

      "should return MultiProductCreationBookingToken with ConsumerFintechDetails correctly" in {
        val mockPackages = Some(PackageRequest("client", Some("interSystem")))
        when(mockSetupBookingSession.packages).thenReturn(mockPackages)

        val expectedBookingId   = 1234
        val expectedItineraryId = 13244
        val expectedConsumerFintechDetail = Some(
          ConsumerFintechDetail(
            ConsumerFintechProductDetail(
              smartFlex = None,
              cancelAndRebookV3 = Some(
                CancelAndRebookV3ProductDetail(
                  originalBookingId = expectedBookingId,
                  originalItineraryId = expectedItineraryId
                )
              ),
              smartSaver = None
            ),
            serviceTaxCountry = None
          )
        )
        val propertyBookingWithConsumerFintechDetailsTokens =
          Map(productKey -> defaultPropertyBookingToken.copy(consumerFintechDetails = expectedConsumerFintechDetail))
        val propertyBookingToken =
          TokenSerializers[PropertyBookingModel]
            .serialize(propertyBookingWithConsumerFintechDetailsTokens, None, Some(20L))
            .success
            .value

        val result = utils
          .createMultiProductCreationBookingToken(
            flightBookingTokens,
            propertyBookingWithConsumerFintechDetailsTokens,
            Map.empty,
            Map.empty,
            Map.empty,
            Map.empty,
            Map.empty,
            None,
            priceBreakdown,
            creditCardInfo,
            None,
            Some(selectedPaymentMethodDetailsV2),
            false,
            isBookingFromCart = false,
            addOns = Map.empty,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .success
          .value

        result.payment shouldBe defaultMultiProductCreationToken.payment

        result.bookingFlowType shouldBe BookingFlow.Package
        result.properties.isDefined shouldBe true
        result.properties.map { token =>
          token.content shouldBe propertyBookingToken.content
          token.version shouldBe propertyBookingToken.version
          token.expiresAfterMinutes shouldBe propertyBookingToken.expiresAfterMinutes
        }
        result.priceBreakdown shouldBe priceBreakdown
        result.creditCardInfo shouldBe creditCardInfo

        result.commonPayment.isDefined shouldBe true
      }

      "should return MultiProductCreationBookingToken with TripProtection correctly" in {
        when(mockSetupBookingSession.packages).thenReturn(None)

        when(mockSetupBookingContext.bookingFlowType).thenReturn(BookingFlow.FlightWithProtection)

        val flightBookingToken =
          TokenSerializers[FlightBookingModel].serialize(flightBookingTokens, None, Some(20L)).success.value
        val tripProtectionToken =
          TokenSerializers[TripProtectionModel].serialize(tripProtectionTokens, None, Some(20L)).success.value

        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = flightBookingTokens,
            propertyCreationBookings = Map.empty,
            tripProtections = tripProtectionTokens,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = Some(selectedPaymentMethodDetailsV2),
            isNewsLetterOptedIn = false,
            isBookingFromCart = false,
            addOns = Map.empty,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .success
          .value

        result.bookingFlowType shouldBe BookingFlow.FlightWithProtection
        result.flights.isDefined shouldBe true
        result.flights.map { token =>
          token.content shouldBe flightBookingToken.content
          token.version shouldBe flightBookingToken.version
          token.expiresAfterMinutes shouldBe flightBookingToken.expiresAfterMinutes
        }

        result.tripProtections.isDefined shouldBe true
        result.tripProtections.map { token =>
          token.content shouldBe tripProtectionToken.content
          token.version shouldBe tripProtectionToken.version
          token.expiresAfterMinutes shouldBe tripProtectionToken.expiresAfterMinutes
        }

        result.priceBreakdown shouldBe priceBreakdown
        result.creditCardInfo shouldBe creditCardInfo

        result.commonPayment.isDefined shouldBe true
        result.commonPayment.get.method shouldBe PaymentMethod.Visa
        result.commonPayment.get.redirectInfo shouldBe None
        result.commonPayment.get.timeoutMinutes shouldBe Some(1)
      }

      "should return MultiProductCreationBookingToken with TripProtection addons correctly" in {
        when(mockSetupBookingSession.packages).thenReturn(None)

        when(mockSetupBookingContext.bookingFlowType).thenReturn(BookingFlow.FlightWithProtection)

        val flightBookingToken =
          TokenSerializers[FlightBookingModel].serialize(flightBookingTokens, None, Some(20L)).success.value

        val addOnToken: Token =
          TokenSerializers[GenericAddOnBookingModel].serialize(addOnTokens, None, Some(20L)).success.value

        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = flightBookingTokens,
            propertyCreationBookings = Map.empty,
            tripProtections = Map.empty,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = Some(selectedPaymentMethodDetailsV2),
            isNewsLetterOptedIn = false,
            isBookingFromCart = false,
            addOns = addOnTokens,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .success
          .value

        result.bookingFlowType shouldBe BookingFlow.FlightWithProtection
        result.flights.isDefined shouldBe true
        result.flights.map { token =>
          token.content shouldBe flightBookingToken.content
          token.version shouldBe flightBookingToken.version
          token.expiresAfterMinutes shouldBe flightBookingToken.expiresAfterMinutes
        }

        result.tripProtections.map { token =>
          token.content shouldBe Some("{}") // make sure old trip protection is empty
        }

        result.cegFastTracks.map { token =>
          token.content shouldBe Some("{}") // make sure other ancillary is empty
        }

        result.addOns.isDefined shouldBe true
        result.addOns.map { token =>
          token.content shouldBe addOnToken.content
          token.version shouldBe addOnToken.version
          token.expiresAfterMinutes shouldBe addOnToken.expiresAfterMinutes
        }

        result.priceBreakdown shouldBe priceBreakdown
        result.creditCardInfo shouldBe creditCardInfo

        result.commonPayment.isDefined shouldBe true
        result.commonPayment.get.method shouldBe PaymentMethod.Visa
        result.commonPayment.get.redirectInfo shouldBe None
        result.commonPayment.get.timeoutMinutes shouldBe Some(1)
      }

      "should return MultiProductCreationBookingToken with hackerFare correctly" in {
        when(mockSetupBookingContext.bookingFlowType).thenReturn(BookingFlow.Hackerfare)
        val hackerFareBookingTokens = Map(productKey -> Seq(defaultFlightBookingToken, defaultFlightBookingToken))
        val flightBookingToken =
          TokenSerializers[FlightBookingModel].serialize(hackerFareBookingTokens, None, Some(20L)).success.value
        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = hackerFareBookingTokens,
            propertyCreationBookings = Map.empty,
            tripProtections = Map.empty,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = Some(selectedPaymentMethodDetailsV2),
            isNewsLetterOptedIn = false,
            isBookingFromCart = false,
            addOns = Map.empty,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .success
          .value
        val expectedPayment =
          defaultMultiProductCreationToken.payment.copy(paymentAmount = 4300, paymentAmountUSD = 120)
        val expected = defaultMultiProductCreationToken.copy(
          properties = None,
          tripProtections = None,
          cars = None,
          payment = expectedPayment
        )

        result.payment shouldBe expected.payment

        result.bookingFlowType shouldBe BookingFlow.Hackerfare
        result.flights.isDefined shouldBe true
        result.flights.map { token =>
          token.content shouldBe flightBookingToken.content
          token.version shouldBe flightBookingToken.version
          token.expiresAfterMinutes shouldBe flightBookingToken.expiresAfterMinutes
        }

        result.priceBreakdown shouldBe priceBreakdown
        result.creditCardInfo shouldBe creditCardInfo

        result.commonPayment.isDefined shouldBe true
        result.commonPayment.get.method shouldBe PaymentMethod.Visa
        result.commonPayment.get.redirectInfo shouldBe None
        result.commonPayment.get.timeoutMinutes shouldBe Some(1)
      }

      "should return MultiProductCreationBookingToken with hackerFare with protection correctly" in {
        val tripProtectionToken =
          TokenSerializers[TripProtectionModel].serialize(tripProtectionTokens, None, Some(20L)).success.value
        when(mockSetupBookingSession.packages).thenReturn(None)
        when(mockSetupBookingContext.session).thenReturn(mockSetupBookingSession)
        when(mockSetupBookingContext.bookingFlowType).thenReturn(BookingFlow.MultiFlightsWithProtection)
        val hackerFareBookingTokens = Map(productKey -> Seq(defaultFlightBookingToken, defaultFlightBookingToken))
        val flightBookingToken =
          TokenSerializers[FlightBookingModel].serialize(hackerFareBookingTokens, None, Some(20L)).success.value
        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = hackerFareBookingTokens,
            propertyCreationBookings = Map.empty,
            tripProtections = tripProtectionTokens,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = Some(selectedPaymentMethodDetailsV2),
            isNewsLetterOptedIn = false,
            isBookingFromCart = false,
            addOns = Map.empty,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .get

        val expectedPayment =
          defaultMultiProductCreationToken.payment.copy(paymentAmount = 4300, paymentAmountUSD = 120)

        result.payment shouldBe expectedPayment

        result.bookingFlowType shouldBe BookingFlow.MultiFlightsWithProtection
        result.flights.isDefined shouldBe true
        result.flights.map { token =>
          token.content shouldBe flightBookingToken.content
          token.version shouldBe flightBookingToken.version
          token.expiresAfterMinutes shouldBe flightBookingToken.expiresAfterMinutes
        }

        result.tripProtections.map { token =>
          token.content shouldBe tripProtectionToken.content
          token.version shouldBe tripProtectionToken.version
          token.expiresAfterMinutes shouldBe tripProtectionToken.expiresAfterMinutes
        }

        result.priceBreakdown shouldBe priceBreakdown
        result.creditCardInfo shouldBe creditCardInfo

        result.commonPayment.isDefined shouldBe true
        result.commonPayment.get.method shouldBe PaymentMethod.Visa
        result.commonPayment.get.redirectInfo shouldBe None
        result.commonPayment.get.timeoutMinutes shouldBe Some(1)
      }

      "should return MultiProductCreationBookingToken with hackerFare with TripProtection addons correctly" in {
        val addOnToken: Token =
          TokenSerializers[GenericAddOnBookingModel].serialize(addOnTokens, None, Some(20L)).success.value
        when(mockSetupBookingSession.packages).thenReturn(None)
        when(mockSetupBookingContext.session).thenReturn(mockSetupBookingSession)
        when(mockSetupBookingContext.bookingFlowType).thenReturn(BookingFlow.MultiFlightsWithProtection)
        val hackerFareBookingTokens = Map(productKey -> Seq(defaultFlightBookingToken, defaultFlightBookingToken))
        val flightBookingToken =
          TokenSerializers[FlightBookingModel].serialize(hackerFareBookingTokens, None, Some(20L)).success.value
        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = hackerFareBookingTokens,
            propertyCreationBookings = Map.empty,
            tripProtections = Map.empty,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = Some(selectedPaymentMethodDetailsV2),
            isNewsLetterOptedIn = false,
            isBookingFromCart = false,
            addOns = addOnTokens,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .get

        val expectedPayment =
          defaultMultiProductCreationToken.payment.copy(paymentAmount = 4300, paymentAmountUSD = 120)

        result.payment shouldBe expectedPayment

        result.bookingFlowType shouldBe BookingFlow.MultiFlightsWithProtection
        result.flights.isDefined shouldBe true
        result.flights.map { token =>
          token.content shouldBe flightBookingToken.content
          token.version shouldBe flightBookingToken.version
          token.expiresAfterMinutes shouldBe flightBookingToken.expiresAfterMinutes
        }

        result.tripProtections.map { token =>
          token.content shouldBe Some("{}") // make sure old trip protection is empty
        }

        result.cegFastTracks.map { token =>
          token.content shouldBe Some("{}") // make sure other ancillary is empty
        }

        result.addOns.isDefined shouldBe true
        result.addOns.map { token =>
          token.content shouldBe addOnToken.content
          token.version shouldBe addOnToken.version
          token.expiresAfterMinutes shouldBe addOnToken.expiresAfterMinutes
        }

        result.priceBreakdown shouldBe priceBreakdown
        result.creditCardInfo shouldBe creditCardInfo

        result.commonPayment.isDefined shouldBe true
        result.commonPayment.get.method shouldBe PaymentMethod.Visa
        result.commonPayment.get.redirectInfo shouldBe None
        result.commonPayment.get.timeoutMinutes shouldBe Some(1)
      }

      "should return MultiProductCreationBookingToken with payment request info correctly" in {
        val mockPackages = Some(PackageRequest("client", Some("interSystem")))
        when(mockSetupBookingSession.packages).thenReturn(mockPackages)

        val flightBookingToken =
          TokenSerializers[FlightBookingModel].serialize(flightBookingTokens, None, Some(20L)).success.value
        val propertyBookingToken =
          TokenSerializers[PropertyBookingModel].serialize(propertyBookingTokens, None, Some(20L)).success.value

        val paymentRequestInfo = Some(PaymentRequestInfo(selectedChargeOption = Some(ChargeOption.PayNow)))

        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = flightBookingTokens,
            propertyCreationBookings = propertyBookingTokens,
            tripProtections = Map.empty,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            paymentRequestInfo = paymentRequestInfo,
            selectedPaymentMethodDetails = Some(selectedPaymentMethodDetailsV2),
            isNewsLetterOptedIn = false,
            isBookingFromCart = false,
            addOns = Map.empty,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .success
          .value

        result.payment shouldBe defaultMultiProductCreationToken.payment

        result.bookingFlowType shouldBe BookingFlow.Package
        result.flights.isDefined shouldBe true
        result.flights.map { token =>
          token.content shouldBe flightBookingToken.content
          token.version shouldBe flightBookingToken.version
          token.expiresAfterMinutes shouldBe flightBookingToken.expiresAfterMinutes
        }

        result.properties.isDefined shouldBe true
        result.properties.map { token =>
          token.content shouldBe propertyBookingToken.content
          token.version shouldBe propertyBookingToken.version
          token.expiresAfterMinutes shouldBe propertyBookingToken.expiresAfterMinutes
        }
        result.priceBreakdown shouldBe priceBreakdown
        result.creditCardInfo shouldBe creditCardInfo
        result.paymentRequestInfo shouldBe paymentRequestInfo

        result.commonPayment.isDefined shouldBe true
        result.commonPayment.get.method shouldBe PaymentMethod.Visa
        result.commonPayment.get.redirectInfo shouldBe None
        result.commonPayment.get.timeoutMinutes shouldBe Some(1)
      }

      "should return MultiProductCreationBookingToken with MultiHotel correctly" in {

        when(mockSetupBookingContext.bookingFlowType).thenReturn(BookingFlow.MultiHotel)

        val propertyBookingTokens = Map("1" -> defaultPropertyBookingToken, "2" -> defaultPropertyBookingToken)

        val propertyBookingToken =
          TokenSerializers[PropertyBookingModel].serialize(propertyBookingTokens, None, Some(20L)).success.value

        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = Map.empty,
            propertyCreationBookings = propertyBookingTokens,
            tripProtections = Map.empty,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = None,
            isNewsLetterOptedIn = false,
            isBookingFromCart = false,
            addOns = Map.empty,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .success
          .value

        result.bookingFlowType shouldBe BookingFlow.MultiHotel
        result.properties.isDefined shouldBe true
        result.properties.map { token =>
          token.content shouldBe propertyBookingToken.content
          token.version shouldBe propertyBookingToken.version
          token.expiresAfterMinutes shouldBe propertyBookingToken.expiresAfterMinutes
        }
        result.priceBreakdown shouldBe priceBreakdown
        result.creditCardInfo shouldBe creditCardInfo
      }

      "should return MultiProductCreationBookingToken with Activity correctly" in {
        when(mockSetupBookingSession.packages).thenReturn(None)

        when(mockSetupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleActivity)

        val activitybookingToken =
          TokenSerializers[ActivityBookingModel].serialize(activityBookingTokens, None, Some(20L)).success.value

        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = Map.empty,
            propertyCreationBookings = Map.empty,
            tripProtections = Map.empty,
            carCreationBookings = Map.empty,
            activityCreationBookings = activityBookingTokens,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = Some(selectedPaymentMethodDetailsV2),
            isNewsLetterOptedIn = false,
            isBookingFromCart = false,
            addOns = Map.empty,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .success
          .value

        result.bookingFlowType shouldBe BookingFlow.SingleActivity
        result.activities.isDefined shouldBe true
        result.activities.map { token =>
          token.content shouldBe activitybookingToken.content
          token.version shouldBe activitybookingToken.version
          token.expiresAfterMinutes shouldBe activitybookingToken.expiresAfterMinutes
        }

        result.priceBreakdown shouldBe priceBreakdown
        result.creditCardInfo shouldBe creditCardInfo

        result.commonPayment.isDefined shouldBe true
        result.commonPayment.get.method shouldBe PaymentMethod.Visa
        result.commonPayment.get.redirectInfo shouldBe None
        result.commonPayment.get.timeoutMinutes shouldBe Some(1)
      }

      "should map payment amount correctly" in {
        when(mockSetupBookingContext.bookingFlowType).thenReturn(BookingFlow.Unknown)

        val flightsToken = defaultFlightBookingToken.copy(
          paymentAmount = Some(
            PaymentAmount(
              paymentCurrency = "THB",
              paymentAmount = 100,
              paymentAmountUSD = 60,
              exchangeRate = 0.1,
              upliftAmount = 0.1,
              siteExchangeRate = 0.1,
              upliftExchangeRate = 0.1,
              destinationCurrency = Some("string"),
              destinationExchangeRate = 0,
              rateQuoteId = 0,
              rewardsRedeemedPoint = 0,
              rewardsSaving = 0,
              giftcardAmount = 0,
              giftcardAmountUSD = 0,
              exchangeRateOption = 0,
              paymentToken = Some("string")
            )
          )
        )

        val propertyToken = defaultPropertyBookingToken.copy(
          paymentAmount = Some(
            PaymentAmount(
              paymentCurrency = "THB",
              paymentAmount = 200,
              paymentAmountUSD = 60,
              exchangeRate = 0.1,
              upliftAmount = 0.1,
              siteExchangeRate = 0.1,
              upliftExchangeRate = 0.1,
              destinationCurrency = Some("string"),
              destinationExchangeRate = 0,
              rateQuoteId = 0,
              rewardsRedeemedPoint = 0,
              rewardsSaving = 0,
              giftcardAmount = 0,
              giftcardAmountUSD = 0,
              exchangeRateOption = 0,
              paymentToken = Some("string")
            )
          )
        )

        val protectionToken = defaultTripProtectionToken.copy(
          paymentAmount = Some(
            PaymentAmount(
              paymentCurrency = "THB",
              paymentAmount = 300,
              paymentAmountUSD = 60,
              exchangeRate = 0.1,
              upliftAmount = 0.1,
              siteExchangeRate = 0.1,
              upliftExchangeRate = 0.1,
              destinationCurrency = Some("string"),
              destinationExchangeRate = 0,
              rateQuoteId = 0,
              rewardsRedeemedPoint = 0,
              rewardsSaving = 0,
              giftcardAmount = 0,
              giftcardAmountUSD = 0,
              exchangeRateOption = 0,
              paymentToken = Some("string")
            )
          )
        )

        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = Map(productKey -> Seq(flightsToken)),
            propertyCreationBookings = Map(productKey -> propertyToken),
            tripProtections = Map(productKey -> protectionToken),
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = None,
            isNewsLetterOptedIn = false,
            isBookingFromCart = false,
            addOns = Map.empty,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .success
          .value

        result.payment.paymentAmount shouldBe 600
      }

      "should return MultiProductCreationBookingToken with Cart correctly" in {
        when(mockSetupBookingSession.packages).thenReturn(None)
        when(mockSetupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        val productRequestWithCartPricingContext = productRequest.copy(
          cartPricingContext = Some(
            CartPricingContext(
              token = Some("Token..."),
              previousTotalPrice = Some(
                TMoney(
                  amount = 100,
                  currencyCode = "USD"
                )
              )
            )
          )
        )

        val propertyBookingTokens = Map("1" -> defaultPropertyBookingToken, "2" -> defaultPropertyBookingToken)

        val propertyBookingToken =
          TokenSerializers[PropertyBookingModel].serialize(propertyBookingTokens, None, Some(20L)).success.value

        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = Map.empty,
            propertyCreationBookings = propertyBookingTokens,
            tripProtections = Map.empty,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = Some(selectedPaymentMethodDetailsV2),
            isNewsLetterOptedIn = false,
            isBookingFromCart = true,
            addOns = Map.empty,
            productsRequest = productRequestWithCartPricingContext
          )(mockSetupBookingContext)
          .success
          .value

        result.payment shouldBe defaultMultiProductCreationToken.payment

        result.bookingFlowType shouldBe BookingFlow.Cart

        result.properties.isDefined shouldBe true
        result.properties.map { token =>
          token.content shouldBe propertyBookingToken.content
          token.version shouldBe propertyBookingToken.version
          token.expiresAfterMinutes shouldBe propertyBookingToken.expiresAfterMinutes
        }
        result.priceBreakdown shouldBe priceBreakdown
        result.creditCardInfo shouldBe creditCardInfo

        result.commonPayment.isDefined shouldBe true
        result.commonPayment.get.method shouldBe PaymentMethod.Visa
        result.commonPayment.get.redirectInfo shouldBe None
        result.commonPayment.get.timeoutMinutes shouldBe Some(1)
      }

      "should return bookingFlow type as Cart if has cart pricing context" in {
        when(mockSetupBookingSession.packages).thenReturn(None)
        val isBookingFromCart = false
        val flightTPProductRequest = productRequest.copy(
          flightRequests = Seq(mock[FlightRequestItem]),
          tripProtectionRequests = Some(Seq(mock[TripProtectionRequestItem])),
          cartPricingContext = Some(
            CartPricingContext(
              token = Some("Token..."),
              previousTotalPrice = Some(
                TMoney(
                  amount = 100,
                  currencyCode = "USD"
                )
              )
            )
          )
        )

        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = flightBookingTokens,
            propertyCreationBookings = Map.empty,
            tripProtections = tripProtectionTokens,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = Some(selectedPaymentMethodDetailsV2),
            isNewsLetterOptedIn = false,
            isBookingFromCart = isBookingFromCart,
            addOns = Map.empty,
            productsRequest = flightTPProductRequest
          )(mockSetupBookingContext)
          .success
          .value

        result.bookingFlowType shouldBe BookingFlow.Cart
      }

      "should return MultiProductCreationBookingToken with PartnerExternalInfo correctly along with corrected member id" in {
        when(mockSetupBookingSession.packages).thenReturn(None)
        when(mockSetupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(mockFeatureAware.removePIIFieldsFromHadoop).thenReturn(false)
        val propertyBookingTokens = Map("1" -> defaultPropertyBookingToken, "2" -> defaultPropertyBookingToken)

        val testMemberId                   = "test_member_id"
        val testProgramName                = "test_program_name"
        val testProgramDescription         = "test_program_description"
        val testChannelId                  = "test_channel_id"
        val testExternalPartnerProgramName = "test_external_partner_program_name"
        val testExternalPartnerProgramCode = "test_external_partner_program_code"

        val partnerSpecificParams = Map(
          "memberId"  -> testMemberId,
          "channelId" -> testChannelId
        )
        val accelerator = output.Accelerator(
          startDate = None,
          endDate = None,
          benefitAccelerator = Some(1)
        )
        val earnAccelerator = output.EarnAccelerators(
          benefitType = output.BenefitType.POINTS,
          benefitCategoryType = output.BenefitCategoryType.HOTELS,
          benefitAcceleratorValueUnit = output.BenefitAcceleratorValueUnit.AMOUNT,
          benefitAccelerator = Some(10),
          promotionalAccelerator = Some(accelerator),
          calculatedAccelerator = Some(accelerator)
        )
        val subLoyaltyPrograms = SubLoyaltyPrograms(
          programName = Some(testProgramName),
          programDescription = Some(testProgramDescription),
          pointsBalance = None,
          earnAccelerators = Some(Seq(earnAccelerator)),
          externalPartnerProgramCode = Some(testExternalPartnerProgramCode),
          externalPartnerProgramName = Some(testExternalPartnerProgramName)
        )

        val fixedMemberId   = "fixed_member_id"
        val userProfileInfo = UserProfileInfo(memberId = Some(fixedMemberId))
        val externalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
          partnerSpecificParams = Some(partnerSpecificParams),
          subLoyaltyPrograms = Seq(subLoyaltyPrograms),
          userProfileInfo = Some(userProfileInfo)
        )
        val loyaltyProfile =
          LoyaltyProfile(
            loyaltyProfile = None,
            eligibleVipCampaigns = Vector.empty[ObjectNode],
            memberBalance = None,
            achievements = None,
            cashBackBalance = None,
            externalLoyaltyProfileInfo = Some(externalLoyaltyUserProfileResponse)
          )

        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = Map.empty,
            propertyCreationBookings = propertyBookingTokens,
            tripProtections = Map.empty,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = Some(selectedPaymentMethodDetailsV2),
            isNewsLetterOptedIn = false,
            isBookingFromCart = true,
            loyaltyProfile = Some(loyaltyProfile),
            addOns = Map.empty,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .success
          .value

        result.properties.isDefined shouldBe true

        result.partnerExternalInfo.flatMap(_.programName) shouldBe Some(testProgramName)
        result.partnerExternalInfo.flatMap(_.programDescription) shouldBe Some(testProgramDescription)
        result.partnerExternalInfo.flatMap(_.externalMemberId) shouldBe Some(fixedMemberId)
        result.partnerExternalInfo.flatMap(_.thankYouMemberId) shouldBe Some(testMemberId)
        result.partnerExternalInfo.flatMap(_.channelId) shouldBe Some(testChannelId)
        result.partnerExternalInfo.flatMap(_.externalPartnerProgramCode) shouldBe Some(testExternalPartnerProgramCode)
        result.partnerExternalInfo.flatMap(_.externalPartnerProgramName) shouldBe Some(testExternalPartnerProgramName)

        val expectedAccelerator = Accelerator(
          startDate = None,
          endDate = None,
          benefitAccelerator = Some(1)
        )
        val expectedEarnAccelerators = EarnAccelerators(
          benefitType = BenefitType.POINTS,
          benefitCategoryType = BenefitCategoryType.HOTELS,
          benefitAcceleratorValueUnit = BenefitAcceleratorValueUnit.AMOUNT,
          benefitAccelerator = Some(10),
          promotionalAccelerator = Some(expectedAccelerator),
          calculatedAccelerator = Some(expectedAccelerator)
        )

        result.partnerExternalInfo.flatMap(_.earnAccelerators) shouldBe Some(Seq(expectedEarnAccelerators))
      }

      "should return MultiProductCreationBookingToken with PartnerExternalInfo correctly when HideBookingPIIHadoop feature is disabled" in {
        when(mockSetupBookingSession.packages).thenReturn(None)
        when(mockSetupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(mockWhiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.HideBookingPIIHadoop))
          .thenReturn(false)
        when(mockFeatureAware.removePIIFieldsFromHadoop).thenReturn(true)
        val propertyBookingTokens = Map("1" -> defaultPropertyBookingToken, "2" -> defaultPropertyBookingToken)

        val testMemberId          = "test_member_id"
        val subLoyaltyPrograms    = SubLoyaltyPrograms(pointsBalance = None)
        val fixedMemberId         = "fixed_member_id"
        val userProfileInfo       = UserProfileInfo(memberId = Some(fixedMemberId))
        val partnerSpecificParams = Map("memberId" -> testMemberId)
        val externalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
          partnerSpecificParams = Some(partnerSpecificParams),
          subLoyaltyPrograms = Seq(subLoyaltyPrograms),
          userProfileInfo = Some(userProfileInfo)
        )
        val loyaltyProfile =
          LoyaltyProfile(
            loyaltyProfile = None,
            eligibleVipCampaigns = Vector.empty[ObjectNode],
            memberBalance = None,
            achievements = None,
            cashBackBalance = None,
            externalLoyaltyProfileInfo = Some(externalLoyaltyUserProfileResponse)
          )

        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = Map.empty,
            propertyCreationBookings = propertyBookingTokens,
            tripProtections = Map.empty,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            selectedPaymentMethodDetails = Some(selectedPaymentMethodDetailsV2),
            isNewsLetterOptedIn = false,
            isBookingFromCart = true,
            loyaltyProfile = Some(loyaltyProfile),
            addOns = Map.empty,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .success
          .value

        result.properties.isDefined shouldBe true
        result.partnerExternalInfo.flatMap(_.externalMemberId) shouldBe Some(fixedMemberId)
        result.partnerExternalInfo.flatMap(_.thankYouMemberId) shouldBe Some(testMemberId)
      }

      // remove while integrating WLBO-1396
      "should return MultiProductCreationBookingToken with PartnerExternalInfo correctly along with member id and thank you id as None" in {
        when(mockSetupBookingSession.packages).thenReturn(None)
        when(mockSetupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(mockFeatureAware.removePIIFieldsFromHadoop).thenReturn(true)
        when(mockWhiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.HideBookingPIIHadoop))
          .thenReturn(true)
        val propertyBookingTokens = Map("1" -> defaultPropertyBookingToken, "2" -> defaultPropertyBookingToken)

        val testMemberId: Option[String]   = None
        val testProgramName                = "test_program_name"
        val testProgramDescription         = "test_program_description"
        val testChannelId                  = "test_channel_id"
        val testExternalPartnerProgramName = "test_external_partner_program_name"
        val testExternalPartnerProgramCode = "test_external_partner_program_code"
        val memberIdValue: String          = testMemberId.getOrElse("unknown")
        val partnerSpecificParams = Map(
          "memberId"  -> memberIdValue,
          "channelId" -> testChannelId
        )
        val accelerator = output.Accelerator(
          startDate = None,
          endDate = None,
          benefitAccelerator = Some(1)
        )
        val earnAccelerator = output.EarnAccelerators(
          benefitType = output.BenefitType.POINTS,
          benefitCategoryType = output.BenefitCategoryType.HOTELS,
          benefitAcceleratorValueUnit = output.BenefitAcceleratorValueUnit.AMOUNT,
          benefitAccelerator = Some(10),
          promotionalAccelerator = Some(accelerator),
          calculatedAccelerator = Some(accelerator)
        )
        val subLoyaltyPrograms = SubLoyaltyPrograms(
          programName = Some(testProgramName),
          programDescription = Some(testProgramDescription),
          pointsBalance = None,
          earnAccelerators = Some(Seq(earnAccelerator)),
          externalPartnerProgramCode = Some(testExternalPartnerProgramCode),
          externalPartnerProgramName = Some(testExternalPartnerProgramName)
        )

        val fixedMemberId: Option[String] = None
        val userProfileInfo               = UserProfileInfo(memberId = fixedMemberId)
        val externalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
          partnerSpecificParams = Some(partnerSpecificParams),
          subLoyaltyPrograms = Seq(subLoyaltyPrograms),
          userProfileInfo = Some(userProfileInfo)
        )
        val loyaltyProfile =
          LoyaltyProfile(
            loyaltyProfile = None,
            eligibleVipCampaigns = Vector.empty[ObjectNode],
            memberBalance = None,
            achievements = None,
            cashBackBalance = None,
            externalLoyaltyProfileInfo = Some(externalLoyaltyUserProfileResponse)
          )

        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = Map.empty,
            propertyCreationBookings = propertyBookingTokens,
            tripProtections = Map.empty,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = Some(selectedPaymentMethodDetailsV2),
            isNewsLetterOptedIn = false,
            isBookingFromCart = true,
            loyaltyProfile = Some(loyaltyProfile),
            addOns = Map.empty,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .success
          .value

        result.properties.isDefined shouldBe true

        result.partnerExternalInfo.flatMap(_.programName) shouldBe Some(testProgramName)
        result.partnerExternalInfo.flatMap(_.programDescription) shouldBe Some(testProgramDescription)
        result.partnerExternalInfo.flatMap(_.externalMemberId) shouldBe fixedMemberId
        result.partnerExternalInfo.flatMap(_.thankYouMemberId) shouldBe testMemberId
        result.partnerExternalInfo.flatMap(_.channelId) shouldBe Some(testChannelId)
        result.partnerExternalInfo.flatMap(_.externalPartnerProgramCode) shouldBe Some(testExternalPartnerProgramCode)
        result.partnerExternalInfo.flatMap(_.externalPartnerProgramName) shouldBe Some(testExternalPartnerProgramName)

        val expectedAccelerator = Accelerator(
          startDate = None,
          endDate = None,
          benefitAccelerator = Some(1)
        )
        val expectedEarnAccelerators = EarnAccelerators(
          benefitType = BenefitType.POINTS,
          benefitCategoryType = BenefitCategoryType.HOTELS,
          benefitAcceleratorValueUnit = BenefitAcceleratorValueUnit.AMOUNT,
          benefitAccelerator = Some(10),
          promotionalAccelerator = Some(expectedAccelerator),
          calculatedAccelerator = Some(expectedAccelerator)
        )

        result.partnerExternalInfo.flatMap(_.earnAccelerators) shouldBe Some(Seq(expectedEarnAccelerators))
      }

      "should return MultiProductCreationBookingToken with External Loyalty Request Info correctly" in {
        when(mockSetupBookingSession.packages).thenReturn(None)
        when(mockSetupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)

        val propertyBookingTokens = Map("1" -> defaultPropertyBookingToken, "2" -> defaultPropertyBookingToken)

        val externalLoyaltyReqInfo = Some(ExternalLoyaltyRequestInfo(Some("test")))
        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = Map.empty,
            propertyCreationBookings = propertyBookingTokens,
            tripProtections = Map.empty,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = Some(selectedPaymentMethodDetailsV2),
            isNewsLetterOptedIn = false,
            isBookingFromCart = true,
            loyaltyProfile = None,
            addOns = Map.empty,
            externalLoyaltyRequestInfo = externalLoyaltyReqInfo,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .success
          .value

        result.properties.isDefined shouldBe true

        result.externalLoyaltyRequestInfo shouldBe externalLoyaltyReqInfo
      }

      "should return MultiProductCreationBookingToken with isPartialSuccessAllowed correctly" in {
        when(mockSetupBookingSession.packages).thenReturn(None)

        val propertyBookingTokens = Map("1" -> defaultPropertyBookingToken, "2" -> defaultPropertyBookingToken)

        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = Map.empty,
            propertyCreationBookings = propertyBookingTokens,
            tripProtections = Map.empty,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = None,
            creditCardInfo = None,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = None,
            isNewsLetterOptedIn = false,
            isBookingFromCart = true,
            loyaltyProfile = None,
            isPartialSuccessAllowed = true,
            addOns = Map.empty,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .success
          .value

        result.isPartialSuccessAllowed shouldBe Some(true)
      }

      "should return MultiProductCreationBookingToken with cartContext correctly" in {
        when(mockSetupBookingSession.packages).thenReturn(None)

        val propertyBookingTokens = Map("1" -> defaultPropertyBookingToken, "2" -> defaultPropertyBookingToken)
        val cartContext           = CartContext("referenceId")

        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = Map.empty,
            propertyCreationBookings = propertyBookingTokens,
            tripProtections = Map.empty,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = None,
            creditCardInfo = None,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = None,
            isNewsLetterOptedIn = false,
            isBookingFromCart = true,
            cartContext = Some(cartContext),
            addOns = Map.empty,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .success
          .value

        result.cartContext shouldBe Some(cartContext)
      }

      "should return MultiProductCreationBookingToken with ItineraryContext correctly" in {
        val expectedJsonString = JsonFormat.toJsonString(defaultItineraryContext)
        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = Map.empty,
            propertyCreationBookings = propertyBookingTokens,
            tripProtections = Map.empty,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = Map.empty,
            timestamp = None,
            priceBreakdownNode = None,
            creditCardInfo = None,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = None,
            isNewsLetterOptedIn = false,
            isBookingFromCart = true,
            cartContext = None,
            addOns = Map.empty,
            productsRequest = productRequest,
            itineraryContext = Some(defaultItineraryContext)
          )(mockSetupBookingContext)
          .success
          .value

        result.itineraryContextStr shouldBe Some(expectedJsonString)
      }

      "should return paymentAmount that excludes FastTrack amount" in {
        when(mockSetupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleFlight)

        val flightBookingToken = defaultFlightBookingToken.copy(
          paymentAmount = Some(
            PaymentAmount(
              paymentCurrency = "USD",
              paymentAmount = 100,
              paymentAmountUSD = 100,
              exchangeRate = 1.69,
              siteExchangeRate = 1.69,
              upliftExchangeRate = 2.00,
              destinationExchangeRate = 1
            )
          )
        )
        val flightTokens = Map(productKey -> Seq(flightBookingToken))

        val cegFastTrackBookingToken = baseCegFastTrackToken.copy(
          paymentAmount = Some(
            PaymentAmount(
              paymentCurrency = "USD",
              paymentAmount = 0.69,
              paymentAmountUSD = 0.69,
              exchangeRate = 1.69,
              siteExchangeRate = 1.69,
              upliftExchangeRate = 2.00,
              destinationExchangeRate = 1
            )
          )
        )
        val cegFastTrackTokens = Map(productKey -> cegFastTrackBookingToken)

        val result = utils
          .createMultiProductCreationBookingToken(
            flightCreationBookings = flightTokens,
            propertyCreationBookings = Map.empty,
            tripProtections = Map.empty,
            carCreationBookings = Map.empty,
            activityCreationBookings = Map.empty,
            priceFreezeCreationBookings = Map.empty,
            cegFastTrackCreationBookings = cegFastTrackTokens,
            timestamp = None,
            priceBreakdownNode = priceBreakdown,
            creditCardInfo = creditCardInfo,
            paymentRequestInfo = None,
            selectedPaymentMethodDetails = Some(selectedPaymentMethodDetailsV2),
            isNewsLetterOptedIn = false,
            isBookingFromCart = false,
            addOns = Map.empty,
            productsRequest = productRequest
          )(mockSetupBookingContext)
          .get

        result.payment.paymentAmount shouldBe 100.00
      }

    }

    "createMultiProductSetupBookingToken" should {
      "should return MultiProductSetupBookingToken correctly" in {
        val propertySetupTokens = Map(productKey -> defaultPropertySetupToken)
        val propertySetupToken =
          TokenSerializers[PropertySetupModel].serialize(propertySetupTokens, None, Some(20L)).success.value

        val result =
          utils
            .createMultiProductSetupBookingToken(
              propertySetupTokens,
              None,
              None,
              None,
              None,
              None,
              None,
              None,
              ancillarySetupModelMap = Map.empty
            )(
              mockSetupBookingContext
            )
            .success
            .value

        result.properties.isDefined shouldBe true
        result.properties.map { token =>
          token.content shouldBe propertySetupToken.content
          token.version shouldBe propertySetupToken.version
          token.expiresAfterMinutes shouldBe propertySetupToken.expiresAfterMinutes
        }
      }

      "should return MultiProductSetupBookingToken correctly for trip protection data" should {
        "should put correct protectionData when token is not empty" in {
          when(mockSetupBookingContext.session).thenReturn(SetupBookingSessionContext())
          val result2 =
            utils
              .createMultiProductSetupBookingToken(
                Map.empty,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                ancillarySetupModelMap = Map.empty
              )(
                mockSetupBookingContext
              )
              .success
              .value
          result2.protectionData shouldBe None
        }

        "should put correct protectionData when token is empty" in {
          when(mockSetupBookingContext.session).thenReturn(SetupBookingSessionContext())
          val result2 =
            utils
              .createMultiProductSetupBookingToken(
                Map.empty,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                ancillarySetupModelMap = Map.empty
              )(
                mockSetupBookingContext
              )
              .success
              .value
          result2.protectionData shouldBe None
        }

      }
    }

    "createMultiProductRetryPaymentBookingToken" should {
      "return MultiProductRetryPaymentBookingToken correctly" in {
        val flightConfirmationData: Seq[FlightConfirmationData] =
          Seq(
            FlightConfirmationData(
              id = "1",
              token = "",
              submitToken = FlightToken(
                Some(
                  "AAAA.AAAADGI234Rbx1+ayHmxJLPcKKuulFVxiJaegQYFk6FEOzCGI8a7Ul6boIHj9QD4sq2wtzod77L7h+DQvSiI78z9k+nSR4pLLbw0TCbYbV79Lkg7umwlMgNzS49RVtcveQRSGRXZ7sn8ryGyE5e1loiciLvA8zKuV4YT3IbpahJK5kBhjHsTTEC32+SM0Rmlsi/pKYxGavkYjW8pf+h8PLhNPu6aiNjl0oBe1X6dlp7pjS0EoXWBoFlq20l8WL/8kZgEJZrFUXIXKeRZgYH1Y4ic7v9TXAhnq9f4cZi+A7FK/8wqvZEj8vfqUcOcfm3c4JuOxYBKxbFEvMEziG1wOHtL2VPDlnkONlMIdqIPOO35u/3+dHtCGWvUfKbNNQiymwMzrXPr0bn97oIRL+JnQCg8JWymU4wskIBCN4s77kaJ7YBSYc6+Z7ot4zOD7AodfVTu7VQw+VjzVtF7SPdaDRDzNWj0M4xpuef+4cNU9OErfrIKa04BazQoX4NuXlN7b3vuEk5APX4OQZLIWvfG8lcoKH4rIspv/VZ4r2pGKzhPghstYSXQOV/Yy4d98gNuSwIJyUirngvy2JDFqr4oporylxj0A6x3alrQ2gvzjSp1Z3JF3MvR4f7YBL3OmOlnu5B5j2jay6WCV70QvVcUDSbcJ0DTpXcX0nMhMOcFhMsIPA4yXk/zNzAW2+XqN7a7VrEyZoAQAZs8ynsbCwHyo1AVeyclWj8JsE1WcShnK2AWuKjvOtWWrxMbfWQpBcaWxAlPPnsALZFEf7J0yHsWbdVPwtP/quuuUxogOJWVA3bE8aNW8uRDDUaEwrkOKJ3Vw8fNxSL+v9JMb89jLoRefAN2zx9CbrAge+IDomMYOVqfqIJDvF+cOULCnhHrCwOHR63xwGhIP/LO460I7SVJXrth99mkET+OzW6ArkusvOnoLV4DWwnf7ymbaNkhLiOfAr0vaCMPaZtfuNVh3Ig5O/8w1WBSaFWMWXRyltXBv4NlJXDKeaKkaCuF5gn1NhnWfn3HRcPt7JUmaHPEVhKMsf1SS1Qne5PrYllZcOlH7ZAeE3CG4QlKoX/oX4GA2wNXgqXzvMVmHVMn7OgJqFI6ILgl1nlnSmFQDbMKDJhqiq0D1fxoQ851KsvZDiclHzyeEG2Uy1N8NpEEEsIS4W9DjYwI7GKoSWAlSzfbbVTQtW0rHIH68voj0CIIx2HVeE1N/qEC4UHpNmQM1MUwZc6/al3ngpauvkpMMXxv9rv/upYJmJ8A5LEw5gYiV8d9+uOzmw8wqeK3dCH8vu+ZW6ITCec5HkAabUFP6wUbEKdEBSo2XTcODwL4oFeEgYmNiqGyl6C1YQUE7yliTcOG77XmxTDf2PPCYIWkARTpmnTwroJ/rgKtDD4/XIird9JkOvHqcDO1KsRyf6fhcrF6JITdhQqSPdqYRpCgsmJhxB5Rks7pqukiZCD1/kq+Duxj2Cz3LdyzInOQ7Wn5A3E+WNIBdEBSaf8na0OgYArPYqff817zx8G00woY4izBf9eW4R1Zti45LoDaa3yMRBkx2VBruQHQNbkJuCtm"
                )
              ),
              isCompleted = true,
              hasFlight = true,
              hasContent = true,
              isHackerFare = false,
              packageRequest = None,
              flightPricing = Some(
                Map(
                  "HKD" -> SearchResponseCurrencyPricing(
                    charges = MockFlightsPricingData.flightPricing,
                    display = MockFlightsPricingData.chargeTotal,
                    discount = None,
                    crossedOutDisplay = None,
                    paymentModel = 1,
                    acceptedCreditCards = None
                  )
                )
              ),
              priceChange = Some(MockFlightsPricingData.priceChangeResponse),
              flightItinerary = None,
              paxNumberByType = Map.empty,
              campaignInfo = Some(MockFlightsPricingData.promotionInfoResponse)
            )
          )

        val productData: ProductData = ProductData(
          properties = Seq.empty,
          flights = flightConfirmationData,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          totalPriceDisplay = None,
          priceDisplayType = None,
          packageToken = None,
          priceChange = None,
          priceConfirmed = true
        )

        implicit val testFlightConfirmationDataSeqSerializer: TokenSerializer[Seq[FlightConfirmationData]] =
          new TokenSerializer[Seq[FlightConfirmationData]]
        val flightsRetryPaymentToken =
          TokenSerializers[Seq[FlightConfirmationData]].serialize(flightConfirmationData, None, Some(30L)).success.value

        val result =
          utils
            .createMultiProductRetryPaymentBookingToken(
              productData
            )(
              mockSetupBookingContext
            )
            .success
            .value

        result.flights.isDefined shouldBe true
        result.flights.map { token =>
          token.content shouldBe flightsRetryPaymentToken.content
          token.version shouldBe flightsRetryPaymentToken.version
          token.expiresAfterMinutes shouldBe flightsRetryPaymentToken.expiresAfterMinutes
        }
      }
    }

    "createMultiProductsToken" should {
      "should return encrypted MultiProductBookingToken correctly" in {
        val expectedCreationToken = TokenSerializers[MultiProductCreationBookingToken]
          .serialize(defaultMultiProductCreationToken, None, Some(20L))
          .success
        val expectedSetupToken = TokenSerializers[MultiProductSetupBookingToken]
          .serialize(defaultMultiProductSetupToken, None, Some(20L))
          .success

        val multiTokenResult = (for {
          result <-
            utils.createMultiProductsToken(Some(defaultMultiProductSetupToken), Some(defaultMultiProductCreationToken))
          decryptedResult        <- BookingTokenEncryptionHelper.decryptToken(result)
          deserializeResultToken <- TokenDeserializers.toToken(decryptedResult)
          multiTokenResult       <- TokenDeserializers[MultiProductBookingToken].deserialize(deserializeResultToken)
        } yield multiTokenResult).success.value

        multiTokenResult.setupBookingToken.isDefined shouldBe true
        multiTokenResult.setupBookingToken.map { token =>
          token.content shouldBe expectedSetupToken.value.content
          token.version shouldBe expectedSetupToken.value.version
          token.expiresAfterMinutes shouldBe expectedSetupToken.value.expiresAfterMinutes
        }
        multiTokenResult.creationBookingToken.isDefined shouldBe true
        multiTokenResult.creationBookingToken.map { token =>
          token.content shouldBe expectedCreationToken.value.content
          token.version shouldBe expectedCreationToken.value.version
          token.expiresAfterMinutes shouldBe expectedCreationToken.value.expiresAfterMinutes
        }
      }
    }
  }
}
