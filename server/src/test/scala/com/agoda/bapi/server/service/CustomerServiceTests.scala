package com.agoda.bapi.server.service

import com.agoda.bapi.common.constants.CashbackFeatureFlags
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.model.{WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.proxy.CustomerApiClientProxy
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.validation.WhiteLabelFeatureMock
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.winterfell.output.{CashBackBalanceInfo, GiftCardBalanceInfo, LoyaltyProfile, MemberDetails}
import com.agoda.winterfell.unified.{PartnerClaim, SSOMemberDetail}
import com.agoda.winterfell.unified.wl.WlAdditionalProperties
import mocks.RequestContextMock
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.{any, anyInt}
import org.mockito.Mockito.{atLeastOnce, times, verify, when}
import org.scalatest.{BeforeAndAfter, OptionValues}

import scala.concurrent.{Await, Future}
import org.scalatestplus.mockito.MockitoSugar
import org.scalatest.funsuite.AsyncFunSuite
import org.scalatest.matchers.should.Matchers

import scala.concurrent.duration._

class CustomerServiceTests
    extends AsyncFunSuite
    with MockitoSugar
    with BeforeAndAfter
    with RequestContextMock
    with Matchers
    with OptionValues
    with WhiteLabelFeatureMock {

  implicit val context: SetupBookingContext = mock[SetupBookingContext]

  private val customerApiClientProxy = mock[CustomerApiClientProxy]
  private val memberDetails          = mock[MemberDetails]
  private val rurubuMemberDetails    = mock[MemberDetails]
  private val wlAdditionalProps      = mock[WlAdditionalProperties]
  private val loyaltyProfile         = mock[LoyaltyProfile]
  private val partnerClaim           = mock[PartnerClaim]
  private val requestContext         = mock[RequestContext]
  private val mockFeatureAware       = mock[FeatureAware]
  private val ssoMemberDetail        = mock[SSOMemberDetail]

  private val mockFeaturesConfigurationForCiti  = featuresConfiguration(isGetMemberLoyaltyProfile = Some(true))
  private val mockFeaturesConfigurationForAgoda = featuresConfiguration(isGetMemberLoyaltyProfile = Some(false))

  def createCustomerService = new CustomerServiceImpl(customerApiClientProxy)

  test("getGiftCardBalance with member id") {
    when(customerApiClientProxy.getMemberBalanceById(anyInt())(any[RequestContext]))
      .thenReturn(Future.successful(GiftCardBalanceInfo(60, "USD", 60, 0, 0, 0, 0)))
    when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))
    when(requestContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.Agoda, mockFeaturesConfigurationForAgoda))
    val customerService = createCustomerService
    customerService
      .getGiftCardBalance(12, Some(memberDetails))(requestContext)
      .map(result => result shouldBe 60)
  }

  test("getGiftCardBalance with RewardPoints for Rurubu user") {
    val mockWhitelabelInfo = mock[WhiteLabelInfo]
    val customerService    = createCustomerService

    when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))
    when(requestContext.whiteLabelInfo).thenReturn(mockWhitelabelInfo)
    when(mockWhitelabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsRurubuWl)).thenReturn(true)
    when(mockWhitelabelInfo.whiteLabelId).thenReturn(WhiteLabel.Rurubu)
    when(rurubuMemberDetails.whiteLabelProperties).thenReturn(Some(wlAdditionalProps))
    when(wlAdditionalProps.tempJsonParser).thenReturn(Map("rewardPoints" -> Some(123.0)))

    customerService
      .getGiftCardBalance(12, Some(rurubuMemberDetails))(requestContext)
      .map(result => result shouldBe 123)
  }

  test("getGiftCardBalance with RewardPoints for Rurubu user fallback to default when not have rurubu wl properties") {
    val customerService = createCustomerService

    when(memberDetails.whiteLabelId).thenReturn(Some(4))
    when(memberDetails.whiteLabelProperties).thenReturn(None)

    customerService
      .getGiftCardBalance(12, Some(memberDetails))(requestContext)
      .map(result => result shouldBe 0)
  }

  test("getGiftCardBalance with no userContext") {
    val customerService = createCustomerService

    when(context.requestContext).thenReturn(requestContext)
    when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))
    when(requestContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.Agoda, mockFeaturesConfigurationForAgoda))
    customerService
      .getGiftCardBalance(0, Some(memberDetails))(requestContext)
      .map(result => result shouldBe 0)
  }

  test("getGiftCardBalance with member id but failed to get result from CAPI") {
    when(customerApiClientProxy.getMemberBalanceById(anyInt())(any[RequestContext]))
      .thenReturn(Future.failed(new Exception("Failure to connect to CAPI")))

    when(context.requestContext).thenReturn(requestContext)
    when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))
    when(requestContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.CitiUS, mockFeaturesConfigurationForCiti))

    val customerService = createCustomerService
    customerService
      .getGiftCardBalance(12, Some(memberDetails))(requestContext)
      .map(result => result shouldBe 0)
  }

  test("getMemberInfo with member id") {
    when(customerApiClientProxy.getMemberDetailsByMemberId(anyInt(), any())(any[RequestContext]))
      .thenReturn(Future.successful(memberDetails))

    val customerService = createCustomerService
    customerService
      .getMemberInfo(123)(requestContext)
      .map { result =>
        result.value shouldBe memberDetails
      }
  }
  test("getMemberInfo with member 0") {
    when(customerApiClientProxy.getMemberDetailsByMemberId(anyInt(), any())(any[RequestContext]))
      .thenReturn(Future.successful(memberDetails))

    val customerService = createCustomerService
    customerService
      .getMemberInfo(0)(requestContext)
      .map { result =>
        result shouldBe empty
      }
  }
  test("getMemberInfo with member id but failed to get result from capi") {
    when(customerApiClientProxy.getMemberDetailsByMemberId(anyInt(), any())(any[RequestContext]))
      .thenReturn(Future.failed(new Exception("Failure to connect to CAPI")))

    val customerService = createCustomerService
    customerService
      .getMemberInfo(123)(requestContext)
      .map { result =>
        result shouldBe None
      }
  }

  test(
    "getMemberInfo includes only cashbackRedemption featureFlag when passed and CASH-3628=A"
  ) {
    when(customerApiClientProxy.getMemberDetailsByMemberId(anyInt(), any())(any[RequestContext]))
      .thenReturn(Future.successful(memberDetails))
    when(mockFeatureAware.isForceMdbForCashbackRedemption).thenReturn(false)
    when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))

    val customerService = createCustomerService
    customerService
      .getMemberInfo(123, featureFlags = Some(Vector(CashbackFeatureFlags.cashbackRedemption)))(requestContext)
      .map { result =>
        verify(customerApiClientProxy, times(1)).getMemberDetailsByMemberId(
          ArgumentMatchers.eq(123),
          ArgumentMatchers.eq(Some(Vector(CashbackFeatureFlags.cashbackRedemption)))
        )(any())
        result.value shouldBe memberDetails
      }
  }

  test(
    "getMemberInfo includes cashbackRedemption when passed and cashbackForceMdbForRedemption featureFlags when CASH-3628=B"
  ) {
    when(customerApiClientProxy.getMemberDetailsByMemberId(anyInt(), any())(any[RequestContext]))
      .thenReturn(Future.successful(memberDetails))
    when(mockFeatureAware.isForceMdbForCashbackRedemption).thenReturn(true)
    when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))

    val customerService = createCustomerService
    customerService
      .getMemberInfo(123, featureFlags = Some(Vector(CashbackFeatureFlags.cashbackRedemption)))(requestContext)
      .map { result =>
        verify(customerApiClientProxy, times(1)).getMemberDetailsByMemberId(
          ArgumentMatchers.eq(123),
          ArgumentMatchers.eq(
            Some(Vector(CashbackFeatureFlags.cashbackRedemption, CashbackFeatureFlags.cashbackForceMdbForRedemption))
          )
        )(any())
        result.value shouldBe memberDetails
      }
  }

  test(
    "getMemberInfo doesn't include cashbackRedemption when not passed and cashbackForceMdbForRedemption featureFlags when CASH-3628=B"
  ) {
    when(customerApiClientProxy.getMemberDetailsByMemberId(anyInt(), any())(any[RequestContext]))
      .thenReturn(Future.successful(memberDetails))
    when(mockFeatureAware.isForceMdbForCashbackRedemption).thenReturn(true)
    when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))

    val customerService = createCustomerService
    customerService
      .getMemberInfo(123, featureFlags = None)(requestContext)
      .map { result =>
        verify(customerApiClientProxy, atLeastOnce())
          .getMemberDetailsByMemberId(ArgumentMatchers.eq(123), ArgumentMatchers.eq(None))(any())
        result.value shouldBe memberDetails
      }
  }

  test(
    "getMemberInfo doesn't include cashbackRedemption when not passed and cashbackForceMdbForRedemption featureFlags when CASH-3628=A"
  ) {
    when(customerApiClientProxy.getMemberDetailsByMemberId(anyInt(), any())(any[RequestContext]))
      .thenReturn(Future.successful(memberDetails))
    when(mockFeatureAware.isForceMdbForCashbackRedemption).thenReturn(false)
    when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))

    val customerService = createCustomerService
    customerService
      .getMemberInfo(123, featureFlags = None)(requestContext)
      .map { result =>
        verify(customerApiClientProxy, atLeastOnce())
          .getMemberDetailsByMemberId(ArgumentMatchers.eq(123), ArgumentMatchers.eq(None))(any())
        result.value shouldBe memberDetails
      }
  }

  test("getMemberLoyaltyProfile without Citi Whitelabel id will not call capi") {
    val customerService = createCustomerService

    when(context.requestContext).thenReturn(requestContext)
    when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))
    when(requestContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.Agoda, mockFeaturesConfigurationForAgoda))
    customerService
      .getMemberLoyaltyProfile(WhiteLabel.Agoda, None)
      .map { result =>
        verify(customerApiClientProxy, times(0)).getMemberLoyaltyProfile(any(), any(), any(), any(), any(), any())(
          any()
        )
        result shouldBe empty
      }
  }

  test("getMemberLoyaltyProfile with a Citi Whitelabel will call capi") {
    when(loyaltyProfile.externalLoyaltyProfileInfo).thenReturn(None)
    when(customerApiClientProxy.getMemberLoyaltyProfile(WhiteLabel.CitiUS.id, None, None, None)(requestContext))
      .thenReturn(Future.successful(loyaltyProfile))
    when(context.requestContext).thenReturn(requestContext)
    when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))
    when(requestContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.CitiUS, mockFeaturesConfigurationForCiti))
    val customerService = createCustomerService
    val timeout         = 2.seconds
    Await
      .result(
        customerService
          .getMemberLoyaltyProfile(WhiteLabel.CitiUS, None, None, None),
        timeout
      )
      .get shouldBe loyaltyProfile
  }

  test(
    "getMemberLoyaltyProfile with cashback redemption flag will call capi if member id > 0 and capi token is not null"
  ) {
    val cashBackBalanceInfo = Some(CashBackBalanceInfo(10d, "USD", 10d))
    when(loyaltyProfile.cashBackBalance).thenReturn(cashBackBalanceInfo)
    when(customerApiClientProxy.getMemberLoyaltyProfile(WhiteLabel.Agoda.id, Some("token"), None, None)(requestContext))
      .thenReturn(Future.successful(loyaltyProfile))
    when(context.requestContext).thenReturn(requestContext)
    when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))
    when(requestContext.whiteLabelInfo).thenReturn(
      WhiteLabelInfo(
        WhiteLabel.Agoda,
        mockFeaturesConfigurationForAgoda,
        None,
        instantBookingEnabled = false,
        (feature, _, _, _, _) => feature == "CashbackRedemption"
      )
    )

    val customerService = createCustomerService
    val timeout         = 2.seconds
    val result = Await
      .result(
        customerService
          .getMemberLoyaltyProfile(
            WhiteLabel.Agoda,
            Some("token"),
            None,
            None,
            Some(Seq("cashbackRedemption")),
            Some(1)
          ),
        timeout
      )
      .get

    result.cashBackBalance shouldBe cashBackBalanceInfo
  }

  test("getPartnerClaim with CITI WhiteLabel should call CAPI") {
    val customerService = createCustomerService
    val timeout         = 2.seconds

    val capiTokenMock     = "capi-token"
    val partnerClaimToken = "partner-claim-token"

    when(
      customerApiClientProxy.getPartnerClaim(WhiteLabel.CitiUS.id, capiTokenMock, partnerClaimToken)(
        requestContext
      )
    )
      .thenReturn(Future.successful(partnerClaim))
    when(partnerClaim.memberDetail).thenReturn(Some(ssoMemberDetail))
    when(ssoMemberDetail.loyaltyAccountNumber).thenReturn(None)
    when(ssoMemberDetail.partnerSpecificParameters).thenReturn(Map.empty[String, String])
    when(context.requestContext).thenReturn(requestContext)
    when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))
    when(requestContext.whiteLabelInfo).thenReturn(
      WhiteLabelInfo(
        WhiteLabel.CitiUS,
        mockFeaturesConfigurationForCiti,
        None,
        instantBookingEnabled = false,
        (feature, _, _, _, _) => feature == WhiteLabelFeatureName.PartnerClaimExchange.toString
      )
    )

    Await
      .result(
        customerService.getPartnerClaim(
          whiteLabelId = WhiteLabel.CitiUS,
          capiToken = Some(capiTokenMock),
          partnerClaim = Some(partnerClaimToken)
        ),
        timeout
      )
      .get shouldBe partnerClaim
  }
}
