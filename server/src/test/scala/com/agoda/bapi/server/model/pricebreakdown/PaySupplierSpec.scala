package com.agoda.bapi.server.model.pricebreakdown
import com.agoda.bapi.common.message.pricebreakdown.PriceBreakdownType
import com.agoda.bapi.common.model.Currency
import com.agoda.bapi.common.token.Money
import models.starfruit.{DisplayPrice, Price, PseudoCoupon}
import org.mockito.Mockito.{reset, when}
import org.scalatest.BeforeAndAfter
import transformers.{EnrichedCharge, EnrichedPricing}
import org.scalatestplus.mockito.MockitoSugar
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.must.Matchers
import com.agoda.bapi.common.model.payment.{PaymentModel => BapiPaymentModels}
import com.agoda.bapi.server.model.pricebreakdown.cart.CartPriceBreakdownBuilderParams
import com.agoda.upi.models.cart.pricing.CartBundleBasis
import com.agoda.upi.models.enums.ChargeEntries

class PaySupplierSpec extends AnyFunSpec with Matchers with MockitoSugar with BeforeAndAfter {
  val mockCurrency = mock[Currency]
  val pricing      = mock[EnrichedPricing]
  val agodaCharge1 = mock[EnrichedCharge]
  val agodaCharge2 = mock[EnrichedCharge]

  val propertyCharge1 = mock[EnrichedCharge]
  val propertyCharge2 = mock[EnrichedCharge]
  val displayPrice    = mock[com.agoda.upi.models.common.DisplayPrice]
  val cartBasis       = mock[CartBundleBasis]

  before {
    reset(mockCurrency, pricing, agodaCharge1, agodaCharge2, propertyCharge1, propertyCharge2)

    when(mockCurrency.code).thenReturn("THB")

    when(agodaCharge1.totalToAgoda).thenReturn(Price(exc = 1000, inc = 1100))
    when(agodaCharge2.totalToAgoda).thenReturn(Price(exc = 500, inc = 700))
    when(agodaCharge1.totalToHotel).thenReturn(Price(exc = 0, inc = 0))
    when(agodaCharge2.totalToHotel).thenReturn(Price(exc = 0, inc = 0))

    when(propertyCharge1.totalToAgoda).thenReturn(Price(exc = 0, inc = 0))
    when(propertyCharge2.totalToAgoda).thenReturn(Price(exc = 0, inc = 0))
    when(propertyCharge1.totalToHotel).thenReturn(Price(exc = 900, inc = 1000))
    when(propertyCharge2.totalToHotel).thenReturn(Price(exc = 200, inc = 300))

    when(pricing.charges).thenReturn(Seq(propertyCharge1, propertyCharge2))

  }
  describe("PaySupplierPriceBreakdown") {
    it("should return the pricebreakdown if there are any payments to Agoda") {
      val priceBreakdownBuilderParams = PriceBreakdownBuilderParams(
        requestedCurrency = mockCurrency,
        charges = pricing.charges,
        originalAmount = 200d,
        paymentModel = BapiPaymentModels.Merchant,
        chargeTotal = Some(mock[DisplayPrice]),
        pseudoCoupon = Option(PseudoCoupon(showBadge = true)),
        numberOfRooms = 2,
        lengthOfStay = 3
      )
      val payPropertyPriceBreakdown = PaySupplier.build(priceBreakdownBuilderParams)
      assert(payPropertyPriceBreakdown.isDefined)
      payPropertyPriceBreakdown.get.amount must be(Money(1100, "THB"))
      payPropertyPriceBreakdown.get.`type` must be(PriceBreakdownType.PaySupplier)
      val priceBreakdown = payPropertyPriceBreakdown.map(_.toPriceBreakdown())
      assert(priceBreakdown.isDefined)
      assert(priceBreakdown.get.`type` == payPropertyPriceBreakdown.get.`type`)
      assert(priceBreakdown.get.amount == payPropertyPriceBreakdown.get.amount)
      assert(priceBreakdown.get.averageAmountPerUnit.isEmpty)
      assert(priceBreakdown.get.date.isEmpty)
    }

    it("should return exclusive price when isInclusivePaySupplier is false") {
      val priceBreakdownBuilderParams = PriceBreakdownBuilderParams(
        requestedCurrency = mockCurrency,
        charges = pricing.charges,
        originalAmount = 200d,
        paymentModel = BapiPaymentModels.Merchant,
        chargeTotal = Some(mock[DisplayPrice]),
        pseudoCoupon = Option(PseudoCoupon(showBadge = true)),
        numberOfRooms = 2,
        lengthOfStay = 3
      )
      val payPropertyPriceBreakdown = PaySupplier.build(priceBreakdownBuilderParams)
      assert(payPropertyPriceBreakdown.isDefined)
      payPropertyPriceBreakdown.get.amount must be(Money(1100, "THB"))
      payPropertyPriceBreakdown.get.`type` must be(PriceBreakdownType.PaySupplier)
    }

    it("should return inclusive price when isInclusivePaySupplier is true") {
      val priceBreakdownBuilderParams = PriceBreakdownBuilderParams(
        requestedCurrency = mockCurrency,
        charges = pricing.charges,
        originalAmount = 200d,
        paymentModel = BapiPaymentModels.Merchant,
        chargeTotal = Some(mock[DisplayPrice]),
        pseudoCoupon = Option(PseudoCoupon(showBadge = true)),
        numberOfRooms = 2,
        lengthOfStay = 3,
        features = PriceBreakdownFeatures(isInclusivePaySupplier = true)
      )
      val payPropertyPriceBreakdown = PaySupplier.build(priceBreakdownBuilderParams)
      assert(payPropertyPriceBreakdown.isDefined)
      payPropertyPriceBreakdown.get.amount must be(Money(1300, "THB"))
      payPropertyPriceBreakdown.get.`type` must be(PriceBreakdownType.PaySupplier)
    }

    it("should not return the pricebreakdown if there are no payments to Agoda") {
      reset(pricing)
      when(pricing.charges).thenReturn(Seq(agodaCharge1, agodaCharge2))
      val priceBreakdownBuilderParams = PriceBreakdownBuilderParams(
        requestedCurrency = mockCurrency,
        chargeTotal = Some(mock[DisplayPrice]),
        originalAmount = 200d,
        charges = pricing.charges,
        paymentModel = BapiPaymentModels.Merchant,
        pseudoCoupon = Option(PseudoCoupon(showBadge = true)),
        numberOfRooms = 2,
        lengthOfStay = 3
      )
      val payProperty = PaySupplier.build(priceBreakdownBuilderParams)
      assert(payProperty.isEmpty)
    }

    it("should return the pricebreakdown if there are any payments to Agoda for cart") {
      val cartCharges = Seq(
        com.agoda.upi.models.common.Charge(
          `type` = ChargeEntries.Surcharge,
          toAgoda = com.agoda.upi.models.common.DisplayPrice(
            exclusive = 0d,
            inclusive = 0d
          ),
          toDestination = com.agoda.upi.models.common.DisplayPrice(
            exclusive = 900d,
            inclusive = 1000d
          ),
          total = displayPrice,
          breakdown = Seq.empty
        ),
        com.agoda.upi.models.common.Charge(
          `type` = ChargeEntries.Surcharge,
          toAgoda = com.agoda.upi.models.common.DisplayPrice(
            exclusive = 0d,
            inclusive = 0d
          ),
          toDestination = com.agoda.upi.models.common.DisplayPrice(
            exclusive = 200d,
            inclusive = 300d
          ),
          total = displayPrice,
          breakdown = Seq.empty
        )
      )
      val cartPriceBreakdownBuilderParams = CartPriceBreakdownBuilderParams(
        basis = cartBasis,
        charges = cartCharges,
        paymentModel = Some(BapiPaymentModels.Merchant),
        requestedCurrency = mockCurrency
      )
      val payPropertyPriceBreakdown = PaySupplier.buildCart(cartPriceBreakdownBuilderParams)
      assert(payPropertyPriceBreakdown.isDefined)
      payPropertyPriceBreakdown.get.amount must be(Money(1100, "THB"))
      payPropertyPriceBreakdown.get.`type` must be(PriceBreakdownType.PaySupplier)
      val priceBreakdown = payPropertyPriceBreakdown.map(_.toPriceBreakdown())
      assert(priceBreakdown.isDefined)
      assert(priceBreakdown.get.`type` == payPropertyPriceBreakdown.get.`type`)
      assert(priceBreakdown.get.amount == payPropertyPriceBreakdown.get.amount)
      assert(priceBreakdown.get.averageAmountPerUnit.isEmpty)
      assert(priceBreakdown.get.date.isEmpty)
    }

    it("should not return the pricebreakdown if there are no payments to Agoda for cart") {
      val cartCharges = Seq(
        com.agoda.upi.models.common.Charge(
          `type` = ChargeEntries.Surcharge,
          toAgoda = com.agoda.upi.models.common.DisplayPrice(
            exclusive = 1000d,
            inclusive = 1100d
          ),
          toDestination = com.agoda.upi.models.common.DisplayPrice(
            exclusive = 0d,
            inclusive = 0d
          ),
          total = displayPrice,
          breakdown = Seq.empty
        ),
        com.agoda.upi.models.common.Charge(
          `type` = ChargeEntries.Surcharge,
          toAgoda = com.agoda.upi.models.common.DisplayPrice(
            exclusive = 500d,
            inclusive = 700d
          ),
          toDestination = com.agoda.upi.models.common.DisplayPrice(
            exclusive = 0d,
            inclusive = 0d
          ),
          total = displayPrice,
          breakdown = Seq.empty
        )
      )
      val cartPriceBreakdownBuilderParams = CartPriceBreakdownBuilderParams(
        basis = cartBasis,
        charges = cartCharges,
        paymentModel = Some(BapiPaymentModels.Merchant),
        requestedCurrency = mockCurrency
      )
      val payProperty = PaySupplier.buildCart(cartPriceBreakdownBuilderParams)
      assert(payProperty.isEmpty)
    }

    describe("fixDoubleTaxesAndFeesPaySupplier") {
      val cartCharges = Seq(
        com.agoda.upi.models.common.Charge(
          `type` = ChargeEntries.Surcharge,
          toAgoda = com.agoda.upi.models.common.DisplayPrice(
            exclusive = 0d,
            inclusive = 0d
          ),
          toDestination = com.agoda.upi.models.common.DisplayPrice(
            exclusive = 100d,
            inclusive = 110d
          ),
          total = displayPrice,
          breakdown = Seq.empty
        ),
        com.agoda.upi.models.common.Charge(
          `type` = ChargeEntries.Room,
          toAgoda = com.agoda.upi.models.common.DisplayPrice(
            exclusive = 0d,
            inclusive = 0d
          ),
          toDestination = com.agoda.upi.models.common.DisplayPrice(
            exclusive = 500d,
            inclusive = 550d
          ),
          total = displayPrice,
          breakdown = Seq.empty
        ),
        com.agoda.upi.models.common.Charge(
          `type` = ChargeEntries.TaxAndFee,
          toAgoda = com.agoda.upi.models.common.DisplayPrice(
            exclusive = 0d,
            inclusive = 0d
          ),
          toDestination = com.agoda.upi.models.common.DisplayPrice(
            exclusive = 50d,
            inclusive = 50d
          ),
          total = displayPrice,
          breakdown = Seq.empty
        )
      )

      it(
        "returns the pricebreakdown without double taxes & fees when fixDoubleTaxesAndFeesPaySupplier (LT-1428 experiment) is ON"
      ) {
        val cartPriceBreakdownBuilderParams = CartPriceBreakdownBuilderParams(
          basis = cartBasis,
          charges = cartCharges,
          paymentModel = Some(BapiPaymentModels.Merchant),
          requestedCurrency = mockCurrency,
          features = PriceBreakdownFeatures(isInclusivePaySupplier = true, fixDoubleTaxesAndFeesPaySupplier = true)
        )

        val payPropertyPriceBreakdown = PaySupplier.buildCart(cartPriceBreakdownBuilderParams)
        payPropertyPriceBreakdown.get.amount must be(Money(660, "THB"))
      }

      it(
        "returns the pricebreakdown with (bug) double taxes & fees when fixDoubleTaxesAndFeesPaySupplier (LT-1428 experiment) is OFF"
      ) {
        val cartPriceBreakdownBuilderParams = CartPriceBreakdownBuilderParams(
          basis = cartBasis,
          charges = cartCharges,
          paymentModel = Some(BapiPaymentModels.Merchant),
          requestedCurrency = mockCurrency,
          features = PriceBreakdownFeatures(isInclusivePaySupplier = true)
        )

        val payPropertyPriceBreakdown = PaySupplier.buildCart(cartPriceBreakdownBuilderParams)
        payPropertyPriceBreakdown.get.amount must be(Money(710, "THB"))
      }
    }
  }

}
