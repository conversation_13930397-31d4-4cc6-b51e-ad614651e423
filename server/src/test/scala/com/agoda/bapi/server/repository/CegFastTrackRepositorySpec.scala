package com.agoda.bapi.server.repository

import com.agoda.adp.messaging.message.Message
import com.agoda.bapi.common.message.{CegFastTrackBookingStateWithItinerary, ProductStateWithItinerary}
import com.agoda.bapi.common.model.ItineraryId
import com.agoda.bapi.common.model.booking.BookingStateMessage.mapBaseBookingRelationshipToBaseBookingRelationshipForMessage
import com.agoda.bapi.common.model.booking._
import com.agoda.bapi.common.model.multiproduct.{MultiProductBookingGroupDBModel, MultiProductInfoDBModel}
import com.agoda.mpb.common.MultiProductType.MultiProductType
import com.agoda.bapi.common.proxy.EnigmaApiProxy
import com.agoda.bapi.common.util.converters.CommonProductConverters
import com.agoda.bapi.common.util.converters.ItineraryConverters.toBaseBookingRelationshipInternal
import com.agoda.bapi.creation.config.ReplicateStateConfig
import com.agoda.bapi.creation.model.cegfasttrack.messaging.CegFastTrackBookingMessage
import com.agoda.bapi.creation.proxy.{BaseBookingDBProxy, CegFastTrackActionDbProxy, CegFastTrackDbProxy}
import com.agoda.bapi.creation.repository.{CegFastTrackRepositoryImpl, MultiProductRepository}
import com.agoda.bapi.common.MessageService
import com.agoda.commons.agprotobuf.scalapb.utils.ProtoConverter
import com.agoda.commons.config.dynamic.DynamicObject
import com.agoda.mpb.common.MultiProductType
import com.agoda.mpbe.state.itinerary.ItineraryState
import com.agoda.mpbe.state.product.ProductModel
import com.agoda.mpbe.state.product.common.FinancialBreakdown
import com.softwaremill.quicklens._
import mocks.{AddOnModelMock, DBBookingModelHelper}
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito._
import org.scalatest.OptionValues
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import java.util.Date
import scala.concurrent.Future

class CegFastTrackRepositorySpec
    extends AsyncWordSpec
    with Matchers
    with MockitoSugar
    with DBBookingModelHelper
    with AddOnModelMock
    with OptionValues {

  "sendBapiCreateCegFastTrackBookingMessage" should {
    "call messaging successfully" in {
      val fixture = new CegFastTrackRepositoryFixture {}
      import fixture._

      when(messagingMock.sendMessage(any[Message])).thenReturn(Future.successful())

      val cegFastTrackModelMessageCaptor: ArgumentCaptor[CegFastTrackBookingMessage] =
        ArgumentCaptor.forClass(classOf[CegFastTrackBookingMessage])

      cegFastTrackRepository
        .sendBapiCreateBookingMessage(
          defaultCegFastTrackProductModel,
          defaultCegFastTrackBookingWorkflowAction
        )
        .map { _ =>
          verify(messagingMock, times(1)).sendMessage(cegFastTrackModelMessageCaptor.capture())
          val message = cegFastTrackModelMessageCaptor.getValue
          message shouldBe defaultCegFastTrackBookingMessage
        }
    }
  }

  "sendCegFastTrackModelForReplication" should {
    val mockItineraryState = ItineraryState(product = ProductModel(cegFastTracks = Seq(mockCegFastTrackProductModel)))
    val multiProductBookingGroupDBModel = MultiProductBookingGroupDBModel(
      bookingId = defaultBookingId,
      itineraryId = -2,
      cartId = -3,
      packageId = Some(-4)
    )
    val mockMultiProductInfos = Seq(MultiProductInfoDBModel(1, MultiProductType.CEGFastTrack))
    val mockCegFastTrackState = CegFastTrackBookingStateWithItinerary(
      itinerary = mockItineraryInternalModel.itinerary,
      itineraryHistories = mockItineraryInternalModel.history,
      payments = mockItineraryInternalModel.payments,
      bookingPayments = mockItineraryInternalModel.bookingPayments,
      relationships = mockItineraryInternalModel.relationships,
      cegFastTracks = Seq(mockCegFastTrackProductModel),
      multiProductBookingGroups = Seq(multiProductBookingGroupDBModel)
    )

    "call messaging send" in {
      val fixture = new CegFastTrackRepositoryFixture {}
      import fixture._

      when(messagingMock.sendMessage(any[Message])).thenReturn(Future.successful(()))
      when(multiProductRepositoryMock.saveMultiProductBookingGroupIfNotExist(multiProductBookingGroupDBModel))
        .thenReturn(Future.successful(multiProductBookingGroupDBModel))

      val expected = BookingStateMessage(
        actionType = defaultCegFastTrackBookingWorkflowAction.actionTypeId,
        actionId = defaultCegFastTrackBookingWorkflowAction.actionId,
        bookingType = None,
        bookingId = 0,
        schemaVersion = "1",
        flights = Seq.empty,
        slices = Seq.empty,
        segments = Seq.empty,
        passengers = Seq.empty,
        payments = Seq(
          PaymentForMessage(
            referenceId = mockCegFastTrackState.payments.head.referenceId,
            paymentId = mockCegFastTrackState.payments.head.paymentId,
            itineraryId = mockCegFastTrackState.payments.head.itineraryId,
            actionId = mockCegFastTrackState.payments.head.actionId,
            creditCardId = mockCegFastTrackState.payments.head.creditCardId,
            transactionDate = mockCegFastTrackState.payments.head.transactionDate.toDate,
            transactionType = mockCegFastTrackState.payments.head.transactionType,
            paymentState = mockCegFastTrackState.payments.head.paymentState,
            referenceNo = mockCegFastTrackState.payments.head.referenceNo,
            referenceType = mockCegFastTrackState.payments.head.referenceType,
            last4Digits = mockCegFastTrackState.payments.head.last4Digits,
            paymentMethodId = mockCegFastTrackState.payments.head.paymentMethodId,
            gatewayId = mockCegFastTrackState.payments.head.gatewayId,
            transactionId = mockCegFastTrackState.payments.head.transactionId,
            paymentCurrency = mockCegFastTrackState.payments.head.paymentCurrency,
            paymentAmount = mockCegFastTrackState.payments.head.paymentAmount,
            amountUsd = mockCegFastTrackState.payments.head.amountUsd,
            supplierCurrency = mockCegFastTrackState.payments.head.supplierCurrency,
            supplierAmount = mockCegFastTrackState.payments.head.supplierAmount,
            exchangeRateSupplierToPayment = mockCegFastTrackState.payments.head.exchangeRateSupplierToPayment,
            creditCardCurrency = mockCegFastTrackState.payments.head.creditCardCurrency,
            upliftAmount = mockCegFastTrackState.payments.head.upliftAmount,
            siteExchangeRate = mockCegFastTrackState.payments.head.siteExchangeRate,
            upliftExchangeRate = mockCegFastTrackState.payments.head.upliftExchangeRate,
            remark = mockCegFastTrackState.payments.head.remark,
            paymentTypeId = mockCegFastTrackState.payments.head.paymentTypeId,
            token = mockCegFastTrackState.payments.head.token,
            recStatus = mockCegFastTrackState.payments.head.recStatus,
            recCreatedWhen = mockCegFastTrackState.payments.head.recCreatedWhen.map(_.toDate),
            referencePaymentId = mockCegFastTrackState.payments.head.referencePaymentId,
            points = mockCegFastTrackState.payments.head.points
          )
        ),
        bookingPayments = Seq(
          BookingPaymentForMessage(
            paymentId = mockCegFastTrackState.bookingPayments.head.paymentId,
            bookingId = mockCegFastTrackState.bookingPayments.head.bookingId,
            paymentCurrency = mockCegFastTrackState.bookingPayments.head.paymentCurrency,
            paymentAmount = mockCegFastTrackState.bookingPayments.head.paymentAmount.toDouble,
            amountUsd = mockCegFastTrackState.bookingPayments.head.amountUsd.toDouble,
            recStatus = mockCegFastTrackState.bookingPayments.head.recStatus,
            recCreatedWhen = mockCegFastTrackState.bookingPayments.head.recCreatedWhen.map(_.toDate),
            fxiUplift = mockCegFastTrackState.bookingPayments.head.fxiUplift.map(_.toDouble),
            loyaltyPoints = mockCegFastTrackState.bookingPayments.head.loyaltyPoints,
            supplierCurrency = mockCegFastTrackState.bookingPayments.head.supplierCurrency,
            supplierExchangeRate = mockCegFastTrackState.bookingPayments.head.supplierExchangeRate.map(_.toDouble),
            bookingPaymentId = mockCegFastTrackState.bookingPayments.head.bookingPaymentId
          )
        ),
        bookingRelationships = mockCegFastTrackState.relationships.headOption
          .map(head => mapBaseBookingRelationshipToBaseBookingRelationshipForMessage(Seq(head)))
          .get,
        breakdown = Seq.empty,
        breakdownPerPax = Seq.empty,
        baggageAllowance = Seq.empty,
        baggage = Seq.empty,
        history = Seq(
          ItineraryHistoryForMessage(
            actionId = mockCegFastTrackState.itineraryHistories.head.actionId,
            itineraryId = mockCegFastTrackState.itineraryHistories.head.itineraryId.toInt,
            bookingType = mockCegFastTrackState.itineraryHistories.head.bookingType,
            bookingId = mockCegFastTrackState.itineraryHistories.head.bookingId,
            actionType = mockCegFastTrackState.itineraryHistories.head.actionType,
            version = mockCegFastTrackState.itineraryHistories.head.version,
            actionDate = mockCegFastTrackState.itineraryHistories.head.actionDate.toDate,
            parameters = mockCegFastTrackState.itineraryHistories.head.parameters,
            description = mockCegFastTrackState.itineraryHistories.head.description,
            recStatus = mockCegFastTrackState.itineraryHistories.head.recStatus,
            recCreatedWhen = mockCegFastTrackState.itineraryHistories.head.recCreatedWhen.map(_.toDate)
          )
        ),
        summary = Seq.empty,
        paxTickets = Seq.empty,
        itinerary = FlightItineraryForMessage(
          itineraryId = mockCegFastTrackState.itinerary.itineraryId,
          memberId = mockCegFastTrackState.itinerary.memberId,
          recStatus = mockCegFastTrackState.itinerary.recStatus,
          recCreatedWhen = mockCegFastTrackState.itinerary.recCreatedWhen.map(_.toDate),
          recModifiedWhen = mockCegFastTrackState.itinerary.recModifiedWhen.map(_.toDate)
        ),
        userAgent = None,
        bookingAttribution = Seq.empty,
        itineraryDate = new Date(),
        protectionModels = None,
        multiProductInfos =
          Some(mockMultiProductInfos.map(pf => MultiProductInfoForMessage(pf.multiProductId, pf.multiProductType.id))),
        flightSegmentInfoByPaxType = Seq.empty,
        segmentInfoByPaxType = Seq.empty,
        fareRulePolicies = None,
        flightSeatSelection = Seq.empty,
        vehicle = None,
        activities = None,
        properties = None,
        cegFastTracks = Some(
          Seq(
            CegFastTrackForMessage(
              ProtoConverter.protoToString(mockCegFastTrackState.cegFastTracks.head),
              mockCegFastTrackProductModel.product.booking.bookingId
            )
          )
        ),
        addOns = None,
        multiProductBookingGroups = Some(
          Seq(
            MultiProductBookingGroupModelMessage(
              bookingId = defaultBookingId,
              itineraryId = -2,
              cartId = -3,
              packageId = Some(-4)
            )
          )
        ),
        flightBrandSelections = None,
        flightBrandAttributes = None,
        flightBrandAttributeParams = None,
        flightBaseBooking = None,
        flightBaseCancellationInfo = None,
        crossProductIsolatedFeature = None
      )

      val bookingStateMessageCaptor: ArgumentCaptor[BookingStateMessage] =
        ArgumentCaptor.forClass(classOf[BookingStateMessage])

      cegFastTrackRepository
        .sendModelForReplication(
          mockCegFastTrackState,
          defaultCegFastTrackBookingWorkflowAction,
          mockMultiProductInfos
        )
        .map { _ =>
          verify(messagingMock, times(1))
            .sendMessage(
              bookingStateMessageCaptor.capture()
            )
          val message = bookingStateMessageCaptor.getValue
          message shouldBe expected.copy(itineraryDate = message.itineraryDate)
        }
    }
  }

  "saveCegFastTrackBookingState" should {
    val baseCegFastTrackBookingState = CegFastTrackBookingStateWithItinerary(
      itinerary = mockItineraryInternalModel.itinerary,
      itineraryHistories = mockItineraryInternalModel.history.map(_.copy(actionId = 10, version = 0)),
      payments = mockItineraryInternalModel.payments,
      bookingPayments = mockItineraryInternalModel.bookingPayments,
      relationships = mockItineraryInternalModel.relationships,
      cegFastTracks = Seq(mockCegFastTrackProductModel),
      multiProductBookingGroups = Seq(defaultMultiProductBookingGroups)
    )

    "save successfully with only new itineraryActionHistory, payment, bookingPayment and financialBreakdown" in {
      val fixture = new CegFastTrackRepositoryFixture {}
      import fixture._

      val existingItineraryHistories = baseCegFastTrackBookingState.itineraryHistories.zipWithIndex.map {
        case (itineraryHistory, index) =>
          itineraryHistory.copy(actionId = index, version = index)
      }

      val existingPaymentState = baseCegFastTrackBookingState.payments.zipWithIndex.map {
        case (paymentState, index) =>
          paymentState.copy(paymentId = index)
      }

      val existingBookingPaymentState = baseCegFastTrackBookingState.bookingPayments.map { bookingPaymentState =>
        bookingPaymentState.copy(paymentId = baseCegFastTrackBookingState.payments.head.paymentId)
      }

      val existingFinancialBreakdowns =
        baseCegFastTrackBookingState.cegFastTracks.flatMap(
          _.product.breakdowns.zipWithIndex
            .map { case (financialBreakDown, index) => financialBreakDown.copy(breakdownId = index, upcId = Some(3)) }
        )

      val existingBapiFinancialBreakdowns = existingFinancialBreakdowns.map(CommonProductConverters.toBapiBreakdown)

      val itineraryId = baseCegFastTrackBookingState.itinerary.itineraryId
      val bookingId   = itineraryProductModel.cegFastTracks.head.product.booking.bookingId

      when(cegFastTrackDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
        .thenReturn(Future.successful(existingItineraryHistories))
      when(cegFastTrackDbProxyMock.getItineraryPaymentByItineraryId(itineraryId))
        .thenReturn(Future.successful(existingPaymentState))
      when(cegFastTrackDbProxyMock.getBookingPaymentsByPaymentIds(Seq(0, 9)))
        .thenReturn(Future.successful(existingBookingPaymentState))
      when(cegFastTrackDbProxyMock.getFinancialBreakdown(bookingId))
        .thenReturn(Future.successful(existingBapiFinancialBreakdowns))

      val newItineraryHistory    = mockItineraryInternalModel.history.head.copy(actionId = 10, version = 2)
      val newPaymentState        = mockItineraryInternalModel.payments.head.copy(paymentId = 9)
      val newBookingPaymentState = mockItineraryInternalModel.bookingPayments.head.copy(paymentId = 9)
      val newFinancialBreakdowns = FinancialBreakdown.defaultInstance.copy(breakdownId = 10)
      val newRelationships       = mockItineraryInternalModel.relationships.head.copy(relationshipId = 11)

      val cegFastTrackBookingStateToSave = baseCegFastTrackBookingState.copy(
        itineraryHistories = existingItineraryHistories :+ newItineraryHistory,
        payments = existingPaymentState :+ newPaymentState,
        bookingPayments = existingBookingPaymentState :+ newBookingPaymentState,
        cegFastTracks = baseCegFastTrackBookingState.cegFastTracks
          .modify(_.each.product.breakdowns)
          .setTo(existingFinancialBreakdowns :+ newFinancialBreakdowns),
        relationships = baseCegFastTrackBookingState.relationships :+ newRelationships
      )

      val cegFastTrackBookingStateToSaveWithoutExistingRecord = baseCegFastTrackBookingState.copy(
        itineraryHistories = Seq(newItineraryHistory),
        payments = Seq(newPaymentState),
        bookingPayments = Seq(newBookingPaymentState),
        cegFastTracks = baseCegFastTrackBookingState.cegFastTracks
          .modify(_.each.product.breakdowns)
          .setTo(Seq(newFinancialBreakdowns)),
        relationships = Seq(newRelationships)
      )

      when(cegFastTrackActionDbProxyMock.insertCegFastTrack(any(), any()))
        .thenReturn(Future.successful(cegFastTrackBookingStateToSaveWithoutExistingRecord))
      when(multiProductRepositoryMock.saveMultiProductBookingGroupIfNotExist(defaultMultiProductBookingGroups))
        .thenReturn(Future.successful(defaultMultiProductBookingGroups))
      when(multiProductRepositoryMock.getMultiProductBookingGroupByItineraryId(itineraryId))
        .thenReturn(Future.successful(Seq(defaultMultiProductBookingGroups)))

      cegFastTrackRepository.saveBookingState(cegFastTrackBookingStateToSave).map { result =>
        verify(cegFastTrackDbProxyMock, times(2)).getItineraryActionHistoryByItineraryId(itineraryId)
        verify(cegFastTrackDbProxyMock, times(2)).getItineraryPaymentByItineraryId(itineraryId)
        verify(cegFastTrackDbProxyMock).getBookingPaymentsByPaymentIds(Seq(0, 9))
        verify(cegFastTrackDbProxyMock).getFinancialBreakdown(bookingId)
        result shouldEqual cegFastTrackBookingStateToSaveWithoutExistingRecord
      }
    }

    "save cegFastTrack with stale update" in {
      val fixture = new CegFastTrackRepositoryFixture {}
      import fixture._

      val newItineraryHistory = baseCegFastTrackBookingState.itineraryHistories.head.copy(version = 1)
      val itineraryId         = baseCegFastTrackBookingState.itinerary.itineraryId
      when(cegFastTrackDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
        .thenReturn(Future.successful(Seq(newItineraryHistory)))

      val repository = new CegFastTrackRepositoryImpl(
        cegFastTrackDbProxyMock,
        cegFastTrackActionDbProxyMock,
        baseBookingDbProxyMock,
        messagingMock,
        multiProductRepositoryMock,
        replicateConfig
      ) {
        override def getBookingState(
            itineraryId: ItineraryId
        ): Future[CegFastTrackBookingStateWithItinerary] =
          Future.failed(new Exception("stale update was detected"))
      }

      when(multiProductRepositoryMock.getMultiProductBookingGroupByItineraryId(itineraryId))
        .thenReturn(Future.successful(Seq(defaultMultiProductBookingGroups)))

      repository.saveBookingState(baseCegFastTrackBookingState).failed.map { failure =>
        verify(cegFastTrackDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
        failure.getMessage shouldEqual "stale update was detected"
      }
    }

    "save cegFastTrack with version collision" in {
      val fixture = new CegFastTrackRepositoryFixture {}
      import fixture._

      val baseCegFastTrackBookingState = CegFastTrackBookingStateWithItinerary(
        itinerary = mockItineraryInternalModel.itinerary,
        itineraryHistories = mockItineraryInternalModel.history.map(_.copy(actionId = 10, version = 1)),
        payments = mockItineraryInternalModel.payments,
        bookingPayments = mockItineraryInternalModel.bookingPayments,
        relationships = Seq.empty,
        cegFastTracks = Seq(mockCegFastTrackProductModel),
        multiProductBookingGroups = Seq.empty
      )

      val newItineraryHistory = baseCegFastTrackBookingState.itineraryHistories.head.copy(version = 1)
      val itineraryId         = baseCegFastTrackBookingState.itinerary.itineraryId
      when(cegFastTrackDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
        .thenReturn(Future.successful(Seq(newItineraryHistory)))

      val repository = new CegFastTrackRepositoryImpl(
        cegFastTrackDbProxyMock,
        cegFastTrackActionDbProxyMock,
        baseBookingDbProxyMock,
        messagingMock,
        multiProductRepositoryMock,
        replicateConfig
      ) {
        override def reportVersionCollision(
            cegFastTrackBookingState: ProductStateWithItinerary
        ): Future[CegFastTrackBookingStateWithItinerary] =
          Future.failed(new Exception("version collision detected"))
      }

      when(multiProductRepositoryMock.getMultiProductBookingGroupByItineraryId(itineraryId))
        .thenReturn(Future.successful(Seq(defaultMultiProductBookingGroups)))

      repository.saveBookingState(baseCegFastTrackBookingState).failed.map { failure =>
        verify(cegFastTrackDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
        failure.getMessage shouldEqual "version collision detected"
      }
    }

    "successful getCegFastTrackBookingState" in {
      val fixture = new CegFastTrackRepositoryFixture {}
      import fixture._

      val itineraryId                  = baseCegFastTrackBookingState.itinerary.itineraryId
      val bid                          = baseCegFastTrackBookingState.cegFastTracks.head.product.booking.bookingId
      val sourceBookingIds             = baseCegFastTrackBookingState.cegFastTracks.map(_.product.booking.bookingId)
      val paymentIds: Seq[ItineraryId] = baseCegFastTrackBookingState.payments.map(_.paymentId)

      when(cegFastTrackDbProxyMock.getItineraryPaymentByItineraryId(itineraryId))
        .thenReturn(Future.successful(baseCegFastTrackBookingState.payments))
      when(cegFastTrackDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
        .thenReturn(Future.successful(baseCegFastTrackBookingState.itineraryHistories))
      when(cegFastTrackDbProxyMock.getMultiProductItinerary(itineraryId))
        .thenReturn(
          Future.successful(Some(baseCegFastTrackBookingState.itinerary))
        )
      when(cegFastTrackDbProxyMock.getBookingPaymentsByPaymentIds(paymentIds))
        .thenReturn(Future.successful(baseCegFastTrackBookingState.bookingPayments))
      when(baseBookingDbProxyMock.getBaseBookingByItineraryId(itineraryId))
        .thenReturn(Future.successful(baseCegFastTrackBookingState.cegFastTracks.map(_.product.booking).toList))
      when(baseBookingDbProxyMock.getBaseBookingRelationshipsBySourceBookingIds(sourceBookingIds))
        .thenReturn(
          Future.successful(baseCegFastTrackBookingState.relationships.map(toBaseBookingRelationshipInternal))
        )
      when(cegFastTrackDbProxyMock.getCegFastTrackBookingByBookingId(bid))
        .thenReturn(Future.successful(itineraryProductModel.cegFastTracks.head))

      when(multiProductRepositoryMock.getMultiProductBookingGroup(bid))
        .thenReturn(Future.successful(Some(defaultMultiProductBookingGroups)))

      cegFastTrackRepository.getBookingState(itineraryId).map { result =>
        verify(cegFastTrackDbProxyMock).getItineraryPaymentByItineraryId(itineraryId)
        verify(cegFastTrackDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
        verify(cegFastTrackDbProxyMock).getBookingPaymentsByPaymentIds(paymentIds)
        verify(cegFastTrackDbProxyMock).getMultiProductItinerary(itineraryId)
        verify(baseBookingDbProxyMock).getBaseBookingByItineraryId(itineraryId)
        verify(cegFastTrackDbProxyMock).getCegFastTrackBookingByBookingId(bid)
        verify(multiProductRepositoryMock).getMultiProductBookingGroup(bid)

        result shouldEqual baseCegFastTrackBookingState
      }
    }

    "Got an exception when multiProductItinerary not exists" in {
      val fixture = new CegFastTrackRepositoryFixture {}
      import fixture._

      val itineraryId      = baseCegFastTrackBookingState.itinerary.itineraryId
      val bid              = baseCegFastTrackBookingState.cegFastTracks.head.product.booking.bookingId
      val sourceBookingIds = baseCegFastTrackBookingState.cegFastTracks.map(_.product.booking.bookingId)

      val paymentIds: Seq[ItineraryId] = baseCegFastTrackBookingState.payments.map(_.paymentId)

      when(cegFastTrackDbProxyMock.getItineraryPaymentByItineraryId(itineraryId))
        .thenReturn(Future.successful(baseCegFastTrackBookingState.payments))
      when(cegFastTrackDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
        .thenReturn(Future.successful(baseCegFastTrackBookingState.itineraryHistories))
      when(cegFastTrackDbProxyMock.getMultiProductItinerary(itineraryId))
        .thenReturn(
          Future.successful(None)
        )
      when(baseBookingDbProxyMock.getBaseBookingByItineraryId(itineraryId))
        .thenReturn(Future.successful(baseCegFastTrackBookingState.cegFastTracks.map(_.product.booking).toList))
      when(baseBookingDbProxyMock.getBaseBookingRelationshipsBySourceBookingIds(sourceBookingIds))
        .thenReturn(
          Future.successful(Seq.empty)
        )
      when(cegFastTrackDbProxyMock.getBookingPaymentsByPaymentIds(paymentIds))
        .thenReturn(Future.successful(baseCegFastTrackBookingState.bookingPayments))

      cegFastTrackRepository.getBookingState(itineraryId).failed.map { result =>
        verify(cegFastTrackDbProxyMock).getItineraryPaymentByItineraryId(itineraryId)
        verify(cegFastTrackDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
        verify(cegFastTrackDbProxyMock).getBookingPaymentsByPaymentIds(paymentIds)
        verify(cegFastTrackDbProxyMock).getMultiProductItinerary(itineraryId)

        result.getMessage shouldEqual s"Cannot itinerary by itineraryId ${itineraryId}"
      }
    }
  }

  trait CegFastTrackRepositoryFixture extends MockitoSugar {
    val cegFastTrackDbProxyMock       = mock[CegFastTrackDbProxy]
    val cegFastTrackActionDbProxyMock = mock[CegFastTrackActionDbProxy]
    val baseBookingDbProxyMock        = mock[BaseBookingDBProxy]
    val multiProductRepositoryMock    = mock[MultiProductRepository]
    val enigmaApiProxyMock            = mock[EnigmaApiProxy]
    val messagingMock                 = mock[MessageService]
    val mockKillSwitch                = mock[DynamicObject[Boolean]]
    val replicateConfig               = ReplicateStateConfig(mockKillSwitch)

    val cegFastTrackRepository = new CegFastTrackRepositoryImpl(
      cegFastTrackDbProxyMock,
      cegFastTrackActionDbProxyMock,
      baseBookingDbProxyMock,
      messagingMock,
      multiProductRepositoryMock,
      replicateConfig
    )
  }
}
