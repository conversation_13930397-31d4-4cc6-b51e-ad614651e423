package com.agoda.bapi.server.proxy.util

import com.agoda.bapi.common.{MaskUtilTestHelper, TestSugar}
import com.agoda.paymentapiv2.client.v2.common.model._
import org.scalatest.BeforeAndAfterEach
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

class PaymentApiProxyMaskUtilSpec
    extends AnyWordSpec
    with MaskUtilTestHelper
    with Matchers
    with BeforeAndAfterEach
    with TestSugar {

  val creditCardInfo: CreditCardInfo = CreditCardInfo(
    CCId = longOpt,
    CardHolderName = stringOpt,
    CardNumber = stringOpt,
    CardExpMonth = stringOpt,
    CardExpYear = stringOpt,
    Cvv = stringOpt,
    CardTypeId = integerOpt,
    IsCcof = Some(false),
    IsFirstCcof = Some(false)
  )

  val binRangeRequest: GetBinRangeRequest = GetBinRangeRequest(
    CreditCardInfo = Some(creditCardInfo),
    CcToken = stringOpt
  )

  val binRangeInfo: BinRangeInfo = BinRangeInfo(
    BinRangeId = longOpt,
    BinLower = longOpt,
    BinHigher = longOpt,
    CardType = stringOpt,
    CardScheme = stringOpt,
    CardIssuer = stringOpt,
    CountryCardIssuer = stringOpt,
    ContinentIdCardIssuer = integerOpt,
    CardClass = stringOpt,
    BankId = integerOpt,
    BinLowerOrginal = longOpt,
    BinLength = integerOpt
  )

  val getInstallmentDataRequest = GetInstallmentDataRequest(
    CcId = longOpt,
    CardNumber = stringOpt,
    CcToken = stringOpt,
    Amount = doubleOpt,
    Currency = stringOpt,
    IncludeThirdPartyInstallmentProviders = Some(false),
    BusinessCountryIdList = Some(List(integer)),
    CapiToken = Some("capi_token"),
    PartnerClaimToken = Some("partner_claim_token")
  )

  val term = TermsAndConditions(
    Url = stringOpt,
    Text = stringOpt
  )

  val installmentPlan = InstallmentPlan(
    PlanId = integerOpt,
    PlanCode = stringOpt,
    PlanToken = stringOpt,
    BankName = stringOpt,
    Currency = stringOpt,
    MonthPeriod = integerOpt,
    InterestType = stringOpt,
    InterestRate = doubleOpt,
    AgodaMinAmount = doubleOpt,
    GatewayMinAmount = doubleOpt,
    RecStatus = Some(false),
    GatewayId = integerOpt,
    MonthlyAmount = doubleOpt,
    MonthlyInterest = doubleOpt,
    CountryIso3 = stringOpt,
    CardClass = stringOpt,
    ValidFrom = jodaDateTimeOpt,
    ValidUntil = jodaDateTimeOpt,
    BinRangeId = longOpt,
    BinLower = longOpt,
    BinHigher = longOpt,
    CardType = stringOpt,
    CardScheme = stringOpt,
    CardIssuer = stringOpt,
    CountryCardIssuer = stringOpt,
    ContinentIdCardIssuer = integerOpt,
    BankId = integerOpt,
    BinLowerOrginal = longOpt,
    BinLength = integerOpt,
    Terms = Some(List(term))
  )

  val getInstallmentPlansResponse = InstallmentDataResponse(
    InstallmentPlans = Some(List(installmentPlan)),
    InstallmentAvailableProviders = Some(List(InstallmentAvailableProvider(ProviderId = integerOpt)))
  )

  val userContext = UserContext(
    UserId = stringOpt,
    CurrencyCode = stringOpt,
    UserOrigin = stringOpt,
    MemberId = integerOpt
  )

  val bookingContext = BookingContext(
    PaymentAmount = bigDecimalOpt
  )

  val setupPaymentRequest = SetupPaymentRequest(
    UserContext = Some(userContext),
    CorrelationId = stringOpt,
    PaymentMethodIds = Some(List(integer)),
    bookingContext = Some(bookingContext)
  )

  val setupPaymentResponse = SetupPaymentResponse(
    SuggestedPaymentMethod = Option(List(integer)),
    IsInstallmentEligible = Option(false)
  )

  val setupPaymentRequestV2 = SetupPaymentRequestV2(
    UserContext = Some(
      UserContextV2(
        UserId = stringOpt,
        MemberId = stringOpt,
        LanguageId = integerOpt,
        RequestOrigin = stringOpt,
        ResidentCountryId = integerOpt,
        DeviceTypeId = integerOpt
      )
    ),
    AnalyticContext = Some(
      AnalyticContext(
        PaymentAmount = doubleOpt,
        UserAgent = stringOpt,
        SessionId = stringOpt,
        AcceptHeader = stringOpt,
        BookingFlow = stringOpt,
        ProductType = stringOpt,
        BookingSessionId = stringOpt,
        BrowserUserAgent = stringOpt,
        AppUserAgent = stringOpt,
        AnalyticsSessionId = stringOpt,
        ClientIp = stringOpt,
        IsAffiliateUserLoggedIn = Some(false),
        IsLoggedInUser = Some(false)
      )
    ),
    PaymentContext = Some(
      PaymentContext(
        SelectedPaymentMethodId = integerOpt,
        SelectedChargeCurrency = stringOpt,
        CcToken = stringOpt,
        InstallmentPlanCode = stringOpt,
        IncludeThirdPartyInstallmentProviders = Some(false),
        SupportedChargeOptions = Some(List(integer)),
        PaymentModel = integerOpt,
        PaymentAmount = Some(
          Amount(
            Value = doubleOpt,
            Currency = stringOpt
          )
        )
      )
    ),
    ProductContext = Some(
      ProductContext(
        WhiteLabelId = integerOpt,
        ProductList = Some(
          List(
            Product(
              ProductId = integerOpt,
              ProductCount = integerOpt,
              BusinessCountryIds = Some(List(integer))
            )
          )
        )
      )
    ),
    FeatureContext = Some(
      FeatureContext(
        IncludedPaymentMethod = Some(List(integer)),
        ExcludedPaymentMethod = Some(List(integer)),
        FeeEnabledPaymentMethodIds = Some(List(integer))
      )
    ),
    ExperimentContext = Some(
      ExperimentContext(
        SupportedFeatures = Some(List(string)),
        ForcedExperiment = Some(Map.empty)
      )
    )
  )

  val setupPaymentResponseV2 = SetupPaymentResponseV2(
    SuggestedPaymentMethod = Some(List(integer)),
    PaymentMethodDetails = None,
    CreditCardOnFile = Some(
      CreditCardOnFileResponse(
        CcId = longOpt,
        Last4Digits = integerOpt,
        CardTypeId = integerOpt,
        Icons = stringOpt,
        ExpiryDate = stringOpt,
        IsPaymentMethodSupported = Some(false),
        CcName = stringOpt,
        IsExpiryDateRequired = Some(false),
        FeeInfo = None
      )
    ),
    CreditCardInfoSetup = Some(
      CreditCardInfoSetupResponse(
        IsNoCvv = Some(false),
        IsBankNameRequired = Some(false),
        IsSaveCcOfEligible = Some(false),
        PaymentLimitation = stringOpt,
        SaveCcOf = stringOpt
      )
    ),
    NonCardOnFile = None,
    InstallmentPlans = None,
    InstallmentAvailableProviders = None,
    InstallmentPlanCode = None,
    IsInstallmentEligible = None,
    RequestStatus = None,
    RequestMessage = None
  )

  "maskBinRangeRequest" should {
    "mask properly" in {
      val expectedCreditCardInfo: CreditCardInfo = CreditCardInfo(
        CCId = maskedLongOpt,
        CardHolderName = maskedStringOpt,
        CardNumber = maskedStringOpt,
        CardExpMonth = maskedStringOpt,
        CardExpYear = maskedStringOpt,
        Cvv = maskedStringOpt,
        CardTypeId = maskedIntegerOpt,
        IsCcof = Some(false),
        IsFirstCcof = Some(false)
      )

      val expectedBinRangeRequest: GetBinRangeRequest = GetBinRangeRequest(
        CreditCardInfo = Some(expectedCreditCardInfo),
        CcToken = maskedStringOpt
      )

      PaymentApiProxyMaskUtil.maskBinRangeRequest(Some(binRangeRequest)) shouldBe Some(expectedBinRangeRequest)
    }
  }

  "maskBinRangeInfo" should {
    "mask properly" in {
      val expectedBinRangeInfo: BinRangeInfo = BinRangeInfo(
        BinRangeId = maskedLongOpt,
        BinLower = maskedLongOpt,
        BinHigher = maskedLongOpt,
        CardType = maskedStringOpt,
        CardScheme = maskedStringOpt,
        CardIssuer = maskedStringOpt,
        CountryCardIssuer = maskedStringOpt,
        ContinentIdCardIssuer = maskedIntegerOpt,
        CardClass = maskedStringOpt,
        BankId = maskedIntegerOpt,
        BinLowerOrginal = maskedLongOpt,
        BinLength = maskedIntegerOpt
      )

      PaymentApiProxyMaskUtil.maskBinRangeInfo(Some(binRangeInfo)) shouldBe Some(expectedBinRangeInfo)
    }
  }

  "maskGetInstallmentDataRequest" should {
    "mask properly" in {
      val expectedGetInstallmentDataRequest = GetInstallmentDataRequest(
        CcId = maskedLongOpt,
        CardNumber = maskedStringOpt,
        CcToken = maskedStringOpt,
        Amount = doubleOpt,
        Currency = stringOpt,
        IncludeThirdPartyInstallmentProviders = Some(false),
        BusinessCountryIdList = Some(List(integer)),
        CapiToken = Some("**********"),
        PartnerClaimToken = Some("*******************")
      )

      PaymentApiProxyMaskUtil.maskGetInstallmentDataRequest(Some(getInstallmentDataRequest)) shouldBe Some(
        expectedGetInstallmentDataRequest
      )
    }
  }

  "maskGetInstallmentPlansResponse" should {
    "mask properly" in {

      val expectedTerm = TermsAndConditions(
        Url = maskedStringOpt,
        Text = maskedStringOpt
      )

      val expectedInstallmentPlan = InstallmentPlan(
        PlanId = integerOpt,
        PlanCode = stringOpt,
        PlanToken = stringOpt,
        BankName = maskedStringOpt,
        Currency = stringOpt,
        MonthPeriod = maskedIntegerOpt,
        InterestType = stringOpt,
        InterestRate = doubleOpt,
        AgodaMinAmount = doubleOpt,
        GatewayMinAmount = doubleOpt,
        RecStatus = Some(false),
        GatewayId = integerOpt,
        MonthlyAmount = doubleOpt,
        MonthlyInterest = doubleOpt,
        CountryIso3 = maskedStringOpt,
        CardClass = maskedStringOpt,
        ValidFrom = jodaDateTimeOpt,
        ValidUntil = jodaDateTimeOpt,
        BinRangeId = longOpt,
        BinLower = maskedLongOpt,
        BinHigher = maskedLongOpt,
        CardType = maskedStringOpt,
        CardScheme = maskedStringOpt,
        CardIssuer = maskedStringOpt,
        CountryCardIssuer = maskedStringOpt,
        ContinentIdCardIssuer = maskedIntegerOpt,
        BankId = maskedIntegerOpt,
        BinLowerOrginal = maskedLongOpt,
        BinLength = maskedIntegerOpt,
        Terms = Some(List(expectedTerm))
      )

      val expectedInstallmentPlansResponse = InstallmentDataResponse(
        InstallmentPlans = Some(List(expectedInstallmentPlan)),
        InstallmentAvailableProviders = Some(List(InstallmentAvailableProvider(ProviderId = integerOpt)))
      )

      PaymentApiProxyMaskUtil.maskGetInstallmentPlansResponse(Some(getInstallmentPlansResponse)) shouldBe Some(
        expectedInstallmentPlansResponse
      )
    }
  }

  "maskSetupPaymentRequest" should {
    "mask properly" in {
      val expectedUserContext = UserContext(
        UserId = stringOpt,
        CurrencyCode = maskedStringOpt,
        UserOrigin = maskedStringOpt,
        MemberId = maskedIntegerOpt
      )

      val expectedBookingContext = BookingContext(
        PaymentAmount = bigDecimalOpt
      )

      val expectedSetupPaymentRequest = SetupPaymentRequest(
        UserContext = Some(expectedUserContext),
        CorrelationId = stringOpt,
        PaymentMethodIds = Some(List(integer)),
        bookingContext = Some(expectedBookingContext)
      )

      PaymentApiProxyMaskUtil.maskSetupPaymentRequest(Some(setupPaymentRequest)) shouldBe Some(
        expectedSetupPaymentRequest
      )
    }
  }

  "maskSetupPaymentResponse" should {
    "mask properly" in {
      val expectedSetupPaymentResponse = SetupPaymentResponse(
        SuggestedPaymentMethod = Option(List(integer)),
        IsInstallmentEligible = Option(false)
      )

      PaymentApiProxyMaskUtil.maskSetupPaymentResponse(Some(setupPaymentResponse)) shouldBe Some(
        expectedSetupPaymentResponse
      )
    }
  }

  "maskSetupPaymentRequestV2" should {
    "mask properly" in {
      val expectedSetupPaymentRequestV2 = SetupPaymentRequestV2(
        UserContext = Some(
          UserContextV2(
            UserId = stringOpt,
            MemberId = stringOpt,
            LanguageId = integerOpt,
            RequestOrigin = maskedStringOpt,
            ResidentCountryId = maskedIntegerOpt,
            DeviceTypeId = integerOpt
          )
        ),
        AnalyticContext = Some(
          AnalyticContext(
            PaymentAmount = doubleOpt,
            UserAgent = stringOpt,
            SessionId = stringOpt,
            AcceptHeader = stringOpt,
            BookingFlow = stringOpt,
            ProductType = stringOpt,
            BookingSessionId = stringOpt,
            BrowserUserAgent = stringOpt,
            AppUserAgent = stringOpt,
            AnalyticsSessionId = stringOpt,
            ClientIp = maskedStringOpt,
            IsAffiliateUserLoggedIn = Some(false),
            IsLoggedInUser = Some(false)
          )
        ),
        PaymentContext = Some(
          PaymentContext(
            SelectedPaymentMethodId = integerOpt,
            SelectedChargeCurrency = stringOpt,
            CcToken = maskedStringOpt,
            InstallmentPlanCode = stringOpt,
            IncludeThirdPartyInstallmentProviders = Some(false),
            SupportedChargeOptions = Some(List(integer)),
            PaymentModel = integerOpt,
            PaymentAmount = Some(
              Amount(
                Value = doubleOpt,
                Currency = stringOpt
              )
            )
          )
        ),
        ProductContext = Some(
          ProductContext(
            WhiteLabelId = integerOpt,
            ProductList = Some(
              List(
                Product(
                  ProductId = integerOpt,
                  ProductCount = integerOpt,
                  BusinessCountryIds = Some(List(integer))
                )
              )
            )
          )
        ),
        FeatureContext = Some(
          FeatureContext(
            IncludedPaymentMethod = Some(List(integer)),
            ExcludedPaymentMethod = Some(List(integer)),
            FeeEnabledPaymentMethodIds = Some(List(integer))
          )
        ),
        ExperimentContext = Some(
          ExperimentContext(
            SupportedFeatures = Some(List(string)),
            ForcedExperiment = Some(Map.empty)
          )
        )
      )

      PaymentApiProxyMaskUtil.maskSetupPaymentRequestV2(Some(setupPaymentRequestV2)) shouldBe Some(
        expectedSetupPaymentRequestV2
      )
    }
  }

  "maskSetupPaymentResponseV2" should {
    "mask properly" in {
      val expectedSetupPaymentResponseV2 = SetupPaymentResponseV2(
        SuggestedPaymentMethod = Some(List(integer)),
        PaymentMethodDetails = None,
        CreditCardOnFile = Some(
          CreditCardOnFileResponse(
            CcId = longOpt,
            Last4Digits = maskedIntegerOpt,
            CardTypeId = integerOpt,
            Icons = stringOpt,
            ExpiryDate = maskedStringOpt,
            IsPaymentMethodSupported = Some(false),
            CcName = maskedStringOpt,
            IsExpiryDateRequired = Some(false),
            FeeInfo = None
          )
        ),
        CreditCardInfoSetup = Some(
          CreditCardInfoSetupResponse(
            IsNoCvv = Some(false),
            IsBankNameRequired = Some(false),
            IsSaveCcOfEligible = Some(false),
            PaymentLimitation = stringOpt,
            SaveCcOf = stringOpt
          )
        ),
        NonCardOnFile = None,
        InstallmentPlans = None,
        InstallmentAvailableProviders = None,
        InstallmentPlanCode = None,
        IsInstallmentEligible = None,
        RequestStatus = None,
        RequestMessage = None
      )

      PaymentApiProxyMaskUtil.maskSetupPaymentResponseV2(Some(setupPaymentResponseV2)) shouldBe Some(
        expectedSetupPaymentResponseV2
      )
    }
  }

}
