package com.agoda.bapi.server.utils

import com.agoda.bapi.common.NoOpMetricsReporter
import com.agoda.bapi.common.config.{FlightPaymentComplianceIndiaWhiteListConfig, InternalConsulState}
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.model.db.CountryInfo
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.reporting.logs.RequestContextMarker
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.model.CreditCardInfo
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.CountryId
import com.agoda.bapi.server.service.payment.model.AvailablePaymentMethodsResult
import com.agoda.commons.dynamic.state.State
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.flights.client.v2.model.{SearchResponseAirportContent, SearchResponseGenericSeatAssignCost, SearchResponseSegment, SearchResponseSlice}
import com.agoda.paymentapiv2.client.v2.common.model.SetupPaymentRequestV2
import mocks.{InternalConsulStateMock, MockFlightConfirmationData}
import com.softwaremill.quicklens._
import com.typesafe.scalalogging.Logger
import org.mockito.{ArgumentCaptor, ArgumentMatchers}
import org.mockito.ArgumentMatchers.{any, eq}
import org.mockito.Mockito.{times, verify, when}
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.TableDrivenPropertyChecks
import org.scalatest.wordspec.AnyWordSpec
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.{Logger => UnderlyingLogger, Marker}

import scala.collection.JavaConverters._
import java.util

class FlightComplianceServiceSpec extends AnyWordSpec with TableDrivenPropertyChecks with MockitoSugar with Matchers {
  private val mockedWlSupplier1 = 21
  private val mockedWlSupplier2 = 212

  private val defaultSegment = SearchResponseSegment(
    id = 1,
    arrivalDateTime = "2024-05-04T09:00",
    layoverAfter = None,
    departDateTime = "2024-05-04T00:00",
    destinationAirport = "NRT",
    duration = 420,
    aircraftCode = "333",
    flightNumber = "TG123",
    carrierCode = "SQ",
    originAirport = "BKK",
    bkgClass = "E",
    cabinClassCode = "ECO",
    genericSeatAssignCost = SearchResponseGenericSeatAssignCost(0.0, "USD"),
    genericSeatAssignFlag = false,
    numSeats = 3,
    seatMapAvailable = true,
    seatSelectionAllowed = true,
    allowStandby = true,
    allowCabinUpgrades = true,
    allowPreferredSeating = true,
    allowPriorityBoarding = true,
    baggageFee = 0.0,
    cabinClassContent = None,
    carrierContent = None,
    airportContent = None,
    aircraftContent = None,
    operatingCarrierCode = "SQ",
    operatingCarrierContent = None,
    bagsRecheckRequired = false,
    cancellationPolicies = Vector.empty,
    exchangePolicies = Vector.empty,
    freeBags = Vector.empty,
    segmentInfoByPaxTypes = Vector.empty,
    fareBasisCode = None,
    departureTerminal = None,
    arrivalTerminal = None,
    stops = Seq.empty
  )
  private val defaultSlice = SearchResponseSlice(
    id = 1,
    duration = 1,
    overnightFlight = true,
    segments = Seq(defaultSegment),
    freeBags = Seq.empty,
    cancellationPolicies = Seq.empty,
    exchangePolicies = Seq.empty,
    voidableWithinHours = 0,
    subSupplierId = Some(1)
  )
  private val defaultFlightData = MockFlightConfirmationData.mock.modify(_.flightItinerary.each.slices).setTo(Nil)
  private val indiaDepartureAirportContent = SearchResponseAirportContent(
    departureAirportName = "",
    arrivalAirportName = "",
    departureCountryId = Some(CountryId.India)
  )
  private val indiaArrivalAirportContent = SearchResponseAirportContent(
    departureAirportName = "",
    arrivalAirportName = "",
    arrivalCountryId = Some(CountryId.India)
  )

  private val indiaDepartureArrivalAirportContent = SearchResponseAirportContent(
    departureAirportName = "",
    arrivalAirportName = "",
    arrivalCountryId = Some(CountryId.India),
    departureCountryId = Some(CountryId.India)
  )

  private val indiaOutBoundFlightBlockedSupplier = defaultFlightData
    .modify(_.flightItinerary.each.slices)
    .setTo(
      Seq(
        defaultSlice
          .modify(_.segments)
          .setTo(Seq(defaultSegment.modify(_.airportContent).setTo(Some(indiaDepartureAirportContent))))
      )
    )

  private val indiaOutBoundFlightWLSupplier = indiaOutBoundFlightBlockedSupplier
    .modify(_.flightItinerary.each.slices.each.subSupplierId)
    .setTo(Some(mockedWlSupplier1))

  private val indiaInBoundFlightBlockedSupplier = defaultFlightData
    .modify(_.flightItinerary.each.slices)
    .setTo(
      Seq(
        defaultSlice
          .modify(_.segments)
          .setTo(Seq(defaultSegment.modify(_.airportContent).setTo(Some(indiaArrivalAirportContent))))
      )
    )

  private val indiaInBoundFlightWLSupplier = indiaOutBoundFlightBlockedSupplier
    .modify(_.flightItinerary.each.slices.each.subSupplierId)
    .setTo(Some(mockedWlSupplier2))

  private val indiaInBoundOutBoundFlightBlockedSupplier = defaultFlightData
    .modify(_.flightItinerary.each.slices)
    .setTo(
      Seq(
        defaultSlice
          .modify(_.segments)
          .setTo(
            Seq(
              defaultSegment
                .modify(_.airportContent)
                .setTo(Some(indiaDepartureAirportContent)),
              defaultSegment
            )
          ),
        defaultSlice
          .modify(_.segments)
          .setTo(
            Seq(
              defaultSegment
                .modify(_.airportContent)
                .setTo(Some(indiaDepartureArrivalAirportContent)),
              defaultSegment.modify(_.airportContent).setTo(Some(indiaDepartureArrivalAirportContent))
            )
          )
      )
    )

  private val indiaInBoundOutBoundFlightWLSupplier = indiaOutBoundFlightBlockedSupplier
    .modify(_.flightItinerary.each.slices.at(0).subSupplierId)
    .setTo(Some(mockedWlSupplier1))

  private val indiaCreditCard = mock[CreditCardInfo]
  when(indiaCreditCard.countryInfo).thenReturn(Some(CountryInfo(CountryId.India, "India", "India", "IN", "IND")))
  private val nonIndiaCreditCard = mock[CreditCardInfo]
  when(nonIndiaCreditCard.countryInfo).thenReturn(Some(CountryInfo(CountryId.Japan, "Japan", "Japan", "JP", "JPN")))

  "shouldBlockInboundOutBoundIndiaFlight with empty Supplier in Config" should {

    val testCaseWithEmptyWhitelistSupplier = Table(
      ("description", "flightData", "expEnabled", "creditCard", "ccCountryId", "expectedResult"),
      ("indiaOutBoundFlight", indiaOutBoundFlightBlockedSupplier, true, Some(indiaCreditCard), CountryId.India, true),
      ("indiaOutBoundFlight", indiaOutBoundFlightBlockedSupplier, false, Some(indiaCreditCard), CountryId.India, false),
      (
        "indiaOutBoundFlight",
        indiaOutBoundFlightBlockedSupplier,
        true,
        Some(nonIndiaCreditCard),
        CountryId.Japan,
        false
      ),
      ("indiaOutBoundFlight", indiaOutBoundFlightBlockedSupplier, true, None, 0, false),
      ("indiaInBoundFlight", indiaInBoundFlightBlockedSupplier, true, Some(indiaCreditCard), CountryId.India, true),
      ("indiaInBoundFlight", indiaInBoundFlightBlockedSupplier, false, Some(indiaCreditCard), CountryId.India, false),
      ("indiaInBoundFlight", indiaInBoundFlightBlockedSupplier, true, Some(nonIndiaCreditCard), CountryId.Japan, false),
      ("indiaInBoundFlight", indiaInBoundFlightBlockedSupplier, true, None, 0, false),
      (
        "indiaInBoundOutBoundFlight",
        indiaInBoundOutBoundFlightBlockedSupplier,
        true,
        Some(indiaCreditCard),
        CountryId.India,
        true
      ),
      (
        "indiaInBoundOutBoundFlight",
        indiaInBoundOutBoundFlightBlockedSupplier,
        false,
        Some(indiaCreditCard),
        CountryId.India,
        false
      ),
      (
        "indiaInBoundOutBoundFlight",
        indiaInBoundOutBoundFlightBlockedSupplier,
        true,
        Some(nonIndiaCreditCard),
        CountryId.Japan,
        false
      ),
      ("indiaInBoundOutBoundFlight", indiaInBoundOutBoundFlightBlockedSupplier, true, None, 0, false)
    )

    forAll(testCaseWithEmptyWhitelistSupplier) {
      (description, flightData, expEnabled, creditCard, ccCountryId, expectedResult) =>
        s"$description with ABEX-188 - $expEnabled creditCard -${creditCard.flatMap(_.countryInfo.map(_.countryId))}" in {
          val reporterMock = mock[NoOpMetricsReporter]
          val loggerMock   = mock[UnderlyingLogger]
          when(loggerMock.isInfoEnabled(any())).thenReturn(true)
          val setupBookingContext: SetupBookingContext = mock[SetupBookingContext]
          val requestContext                           = mock[RequestContext]
          val featureAware                             = mock[FeatureAware]
          when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Unknown)
          when(setupBookingContext.requestContext).thenReturn(requestContext)
          when(requestContext.userContext).thenReturn(None)
          when(requestContext.getSessionId()).thenReturn("s")
          when(setupBookingContext.correlationId).thenReturn("c")
          when(requestContext.featureAware).thenReturn(Some(featureAware))
          when(featureAware.isBlockFlightPayInPayOutIndiaEnabled).thenReturn(expEnabled)
          val markerCapture: ArgumentCaptor[RequestContextMarker] =
            ArgumentCaptor.forClass(classOf[RequestContextMarker])
          val metricMapCapture: ArgumentCaptor[Map[String, String]] =
            ArgumentCaptor.forClass(classOf[Map[String, String]])

          val config =
            """
              |config.payment.compliance.flight.IN.whiteList = ""
              |""".stripMargin
          val flightComplianceService =
            new FlightComplianceServiceImpl(
              new FlightPaymentComplianceIndiaWhiteListConfig(InternalConsulStateMock.mockFromConfigString(config))
            ) {
              override def logger: Logger            = com.typesafe.scalalogging.Logger(loggerMock)
              override def reporter: MetricsReporter = reporterMock
            }
          flightComplianceService
            .shouldBlockIndiaFlight(
              Seq(flightData),
              creditCard,
              AvailablePaymentMethodsResult(Nil, SetupPaymentRequestV2(), Nil, None)
            )(setupBookingContext) shouldBe expectedResult
          verify(loggerMock, times(1)).info(
            markerCapture.capture(),
            ArgumentMatchers.eq("[FlightSetupPaymentComplianceLog]"),
            ArgumentMatchers.eq(null)
          )
          verify(reporterMock, times(1)).report(
            ArgumentMatchers.eq("setup.flight.compliance"),
            ArgumentMatchers.eq(1L),
            metricMapCapture.capture()
          )

          val metricMap = metricMapCapture.getValue
          metricMap shouldBe Map(
            "requesttype"            -> "unknown_request_type",
            "isIndiaFlight"          -> "true",
            "creditCardIssueCountry" -> ccCountryId.toString,
            "isSupplierBlocked"      -> "true",
            "blocked"                -> expectedResult.toString
          )

          val loggedResponse   = markerCapture.getValue
          val flightSupplierId = flightData.flightItinerary.map(_.slices.flatMap(_.subSupplierId)).getOrElse(Nil).toSet
          loggedResponse.stringTags shouldBe
            Map(
              "sessionId"                  -> "s",
              "userId"                     -> "00000000-0000-0000-0000-000000000000",
              "origin"                     -> "",
              "correlationId"              -> "c",
              "paymentMethods"             -> "",
              "supplierId"                 -> flightSupplierId.mkString(","),
              "arrivalDepartureCountryIds" -> CountryId.India.toString,
              "creditCardIssueCountry"     -> ccCountryId.toString,
              "bookingFlow"                -> "Unknown",
              "isBlocked"                  -> expectedResult.toString
            ).asJava
        }
    }

  }

  "shouldBlockInboundOutBoundIndiaFlight with Some Supplier in Config" should {

    val testCaseWithEmptyWhitelistSupplier = Table(
      ("description", "flightData", "expEnabled", "creditCard", "ccCountryId", "expectedResult"),
      ("indiaOutBoundFlight", indiaOutBoundFlightWLSupplier, true, Some(indiaCreditCard), CountryId.India, false),
      ("indiaOutBoundFlight", indiaOutBoundFlightWLSupplier, false, Some(indiaCreditCard), CountryId.India, false),
      ("indiaOutBoundFlight", indiaOutBoundFlightWLSupplier, true, Some(nonIndiaCreditCard), CountryId.Japan, false),
      ("indiaOutBoundFlight", indiaOutBoundFlightWLSupplier, true, None, 0, false),
      ("indiaInBoundFlight", indiaInBoundFlightWLSupplier, true, Some(indiaCreditCard), CountryId.India, false),
      ("indiaInBoundFlight", indiaInBoundFlightWLSupplier, false, Some(indiaCreditCard), CountryId.India, false),
      ("indiaInBoundFlight", indiaInBoundFlightWLSupplier, true, Some(nonIndiaCreditCard), CountryId.Japan, false),
      ("indiaInBoundFlight", indiaInBoundFlightWLSupplier, true, None, 0, false),
      (
        "indiaInBoundOutBoundFlight",
        indiaInBoundOutBoundFlightWLSupplier,
        true,
        Some(indiaCreditCard),
        CountryId.India,
        false
      ),
      (
        "indiaInBoundOutBoundFlight",
        indiaInBoundOutBoundFlightWLSupplier,
        false,
        Some(indiaCreditCard),
        CountryId.India,
        false
      ),
      (
        "indiaInBoundOutBoundFlight",
        indiaInBoundOutBoundFlightWLSupplier,
        true,
        Some(nonIndiaCreditCard),
        CountryId.Japan,
        false
      ),
      ("indiaInBoundOutBoundFlight", indiaInBoundOutBoundFlightWLSupplier, true, None, 0, false)
    )

    forAll(testCaseWithEmptyWhitelistSupplier) {
      (description, flightData, expEnabled, creditCard, ccCountryId, expectedResult) =>
        s"$description with ABEX-188 - $expEnabled creditCard -${creditCard.flatMap(_.countryInfo.map(_.countryId))}" in {
          val loggerMock   = mock[UnderlyingLogger]
          val reporterMock = mock[NoOpMetricsReporter]
          when(loggerMock.isInfoEnabled(any())).thenReturn(true)
          val setupBookingContext: SetupBookingContext = mock[SetupBookingContext]
          val requestContext                           = mock[RequestContext]
          val featureAware                             = mock[FeatureAware]
          when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Unknown)
          when(setupBookingContext.requestContext).thenReturn(requestContext)
          when(requestContext.userContext).thenReturn(None)
          when(requestContext.getSessionId()).thenReturn("s")
          when(setupBookingContext.correlationId).thenReturn("c")
          when(requestContext.featureAware).thenReturn(Some(featureAware))
          when(featureAware.isBlockFlightPayInPayOutIndiaEnabled).thenReturn(expEnabled)
          val markerCapture: ArgumentCaptor[RequestContextMarker] =
            ArgumentCaptor.forClass(classOf[RequestContextMarker])
          val metricMapCapture: ArgumentCaptor[Map[String, String]] =
            ArgumentCaptor.forClass(classOf[Map[String, String]])
          val flightComplianceService =
            new FlightComplianceServiceImpl(
              new FlightPaymentComplianceIndiaWhiteListConfig(
                InternalConsulStateMock.mockFromConfigString(
                  """
                    |config.payment.compliance.flight.IN.whiteList = "21,212"
                    |""".stripMargin
                )
              )
            ) {
              override def logger: Logger            = com.typesafe.scalalogging.Logger(loggerMock)
              override def reporter: MetricsReporter = reporterMock
            }
          flightComplianceService.shouldBlockIndiaFlight(
            Seq(flightData),
            creditCard,
            AvailablePaymentMethodsResult(Nil, SetupPaymentRequestV2(), Nil, None)
          )(setupBookingContext) shouldBe expectedResult
          verify(loggerMock, times(1)).info(
            markerCapture.capture(),
            ArgumentMatchers.eq("[FlightSetupPaymentComplianceLog]"),
            ArgumentMatchers.eq(null)
          )
          verify(reporterMock, times(1)).report(
            ArgumentMatchers.eq("setup.flight.compliance"),
            ArgumentMatchers.eq(1L),
            metricMapCapture.capture()
          )

          val metricMap = metricMapCapture.getValue
          metricMap shouldBe Map(
            "requesttype"            -> "unknown_request_type",
            "isIndiaFlight"          -> "true",
            "creditCardIssueCountry" -> ccCountryId.toString,
            "isSupplierBlocked"      -> "false",
            "blocked"                -> expectedResult.toString
          )

          val loggedResponse   = markerCapture.getValue
          val flightSupplierId = flightData.flightItinerary.map(_.slices.flatMap(_.subSupplierId)).getOrElse(Nil).toSet
          loggedResponse.stringTags shouldBe
            Map(
              "sessionId"                  -> "s",
              "userId"                     -> "00000000-0000-0000-0000-000000000000",
              "origin"                     -> "",
              "correlationId"              -> "c",
              "paymentMethods"             -> "",
              "supplierId"                 -> flightSupplierId.mkString(","),
              "arrivalDepartureCountryIds" -> CountryId.India.toString,
              "creditCardIssueCountry"     -> ccCountryId.toString,
              "bookingFlow"                -> "Unknown",
              "isBlocked"                  -> expectedResult.toString
            ).asJava
        }
    }

  }
}
