package com.agoda.bapi.server.repository

import com.agoda.bapi.common.TestSugar
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.hadoop.HadoopMsgServiceHelper
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.message.{DeviceContext, DevicePlatform, ExperimentData}
import com.agoda.bapi.common.model.flight.Flight
import com.agoda.bapi.common.model.flight.flightModel.Scope
import com.agoda.bapi.common.model.payment.PaymentModel
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{User<PERSON>ontex<PERSON>, UserContextMock, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.reporting.logs.RequestContextMarker
import com.agoda.bapi.common.service.MessagesBag
import com.agoda.bapi.creation.service.HadoopMessagingService
import com.agoda.bapi.creation.util.CustomerApiTokenUtils
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.pricebreakdown.flights.MockFlightsPricingData
import com.agoda.bapi.server.proxy.flight.FLAPIClientProxyV2
import com.agoda.bapi.server.reporting.logs.FlapiSetupBookingServiceLog
import com.agoda.bapi.server.utils.SetupBookingMock
import com.agoda.flights.client.v2.model.{AddOns => FLAPIAddOns, AddonRequestPassenger, BaggageDetail, BundleContext, ChargeablePaymentMethod, ConfirmPriceRequest => FlapiConfirmPriceRequest, ConfirmPriceResponse, ExternalLoyaltyRequest, InstantPriceConfirmRequest => FlapiInstantPriceConfirmRequest, ItineraryDetailsResponse, PaymentInfo, PromotionInfoRequest, Retry, SearchResponseItinerary, SearchResponsePassenger, SearchResponseSupplierAcceptedCreditCard, SeatDetail}
import com.agoda.mpbe.state.common.enums.PaymentMethod.{PaymentMethod => MPBPaymentMethod}
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import com.typesafe.scalalogging.Logger
import mocks.{CartMock, RequestContextMock}
import org.mockito.ArgumentMatchers.{any, argThat}
import org.mockito.Mockito._
import org.mockito.invocation.InvocationOnMock
import org.mockito.{ArgumentCaptor, ArgumentMatcher, ArgumentMatchers => MockitoMatchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.TableDrivenPropertyChecks
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{BeforeAndAfter, TryValues}
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.{Logger => Underlying}
import serializer.Serialization

import java.time.LocalDateTime
import scala.collection.JavaConverters._
import scala.concurrent.Future

class FlightsRepositoryTest
    extends AsyncWordSpec
    with Matchers
    with MockitoSugar
    with TestSugar
    with BeforeAndAfter
    with ScalaFutures
    with RequestContextMock
    with SetupBookingMock
    with CartMock
    with TableDrivenPropertyChecks
    with TryValues {

  private val messagesBag            = mock[MessagesBag]
  private val flapiClientProxyV2Mock = mock[FLAPIClientProxyV2]
  private val loggerMock             = mock[Underlying]
  private val hadoopServiceMock      = mock[HadoopMessagingService]
  private val hadoopMsgServiceHelper = mock[HadoopMsgServiceHelper]
  private val customerApiTokenUtil   = mock[CustomerApiTokenUtils]

  implicit private val setupBookingContextIndian: SetupBookingContext = mock[SetupBookingContext]
  private val requestContextIndian                                    = mock[com.agoda.bapi.common.handler.RequestContext]

  implicit private val context: RequestContext = requestContext(messagesBag)

  private val sessionId          = "sid"
  private val mockUserContextOpt = Some(UserContextMock.value.copy(requestOrigin = "TH"))
  private val experimentData =
    ExperimentData("1", "1", Some("1"), Some("test"), Some("test"), Some("test"), Some("test"))
  private val userContext = UserContext(
    languageId = 1,
    currency = "USD",
    requestOrigin = "THB",
    nationalityId = 1,
    experimentData = Some(experimentData)
  )
  private val userContextOpt = Some(userContext)

  private val setupBookingRequestContext = SetupBookingContext(
    BookingFlow.Package,
    context,
    "",
    WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
    None,
    sessionId = sessionId,
    bookingSessionId = ""
  )

  private val flightsRepository =
    new FlapiClientFlightsRepository(
      flapiClientProxyV2 = flapiClientProxyV2Mock,
      customerApiTokenUtils = customerApiTokenUtil,
      hadoopMessagingService = hadoopServiceMock,
      hadoopMsgServiceHelper = hadoopMsgServiceHelper
    ) {
      override def logger: Logger = com.typesafe.scalalogging.Logger(loggerMock)
    }

  private val partnerClaimToken       = Some("partner-claim-0")
  private val selectedOfferIdentifier = Some("identifier-0")
  private val points                  = Some(1000d)
  private val loyaltySearchType       = Some("BURN")

  private val mockLoyaltyRequest = LoyaltyRequest(
    partnerClaimToken = partnerClaimToken,
    selectedOfferIdentifier = selectedOfferIdentifier,
    points = points,
    loyaltySearchType = Some("BURN")
  )

  private val mockExternalLoyaltyRequest = ExternalLoyaltyRequest(
    partnerClaimToken = partnerClaimToken,
    selectedOfferIdentifier = selectedOfferIdentifier,
    points = points,
    loyaltySearchType = loyaltySearchType
  )

  private val paymentRequest: PaymentRequest = PaymentRequest(
    ccBin = None,
    ccId = None,
    selectedPaymentMethod = None
  )

  private val productsRequest: ProductsRequest = ProductsRequest(
    bookingToken = None,
    propertyRequests = Seq.empty,
    flightRequests = Seq(FlightRequestItem(Some("test"), Some(ConfirmPriceRequest("test", "test")), None)),
    packageRequest = None
  )

  before {
    reset(messagesBag)
    reset(flapiClientProxyV2Mock)
    when(loggerMock.isInfoEnabled(any())).thenReturn(true)
  }

  "retrieveFlightConfirmationData" should {

    "returns flight search successfully" in {
      clearInvocations(loggerMock)
      val contextMarkerCapture: ArgumentCaptor[RequestContextMarker] =
        ArgumentCaptor.forClass(classOf[RequestContextMarker])
      val setupBookingRequest = SetupBookingRequest(
        correlationId = None,
        userContext = userContextOpt,
        paymentRequest = Some(paymentRequest),
        customerInfo = None,
        productsRequest = productsRequest
      )

      val content = StringContext treatEscapes flightsData.head.content

      val confirmPriceResponse =
        Serialization.jacksonMapper.readValue[ConfirmPriceResponse](content)

      val jsonContent = Serialization.jacksonMapper.writeValueAsString(confirmPriceResponse)

      when(flapiClientProxyV2Mock.postFlightsConfirmPrice(any(), any())(any()))
        .thenReturn(Future.successful(confirmPriceResponse, jsonContent))
      when(requestContextIndian.featureAware).thenReturn(None)
      when(requestContextIndian.pollingId).thenReturn(None)
      when(setupBookingContextIndian.requestContext).thenReturn(requestContextIndian)

      val confirmPriceRequest = getFlightRequest(setupBookingRequest)
      val expectedLog = FlapiSetupBookingServiceLog(
        Some("flight success"),
        Some(""),
        sessionId,
        BookingFlow.Package,
        userContextOpt,
        setupBookingRequest.productsRequest.flightRequests.head,
        Some(confirmPriceResponse),
        FlightsRepository.confirmPriceEndpoint,
        setupBookingRequestContext.deviceContext,
        confirmPriceRequest
      )

      flightsRepository.retrieveFlightConfirmationData(
        setupBookingRequest.productsRequest.flightRequests.head,
        setupBookingRequestContext.bookingFlowType,
        "THB",
        setupBookingRequestContext.whiteLabelInfo,
        setupBookingRequestContext.correlationId,
        setupBookingRequestContext.requestContext.locale,
        setupBookingRequestContext.sessionId,
        setupBookingRequest.userContext,
        setupBookingRequestContext.deviceContext,
        packagingToken = None,
        selectedCreditCardTypeId = None,
        campaignInfo = None,
        enabledFeatures = None,
        loyaltyRequestOpt = None,
        aabInfo = None
      )(setupBookingContextIndian) map (response => {
        verify(loggerMock).info(
          contextMarkerCapture.capture(),
          MockitoMatchers.eq("[FlightAPI]"),
          MockitoMatchers.eq(null)
        )
        contextMarkerCapture.getValue.stringTags.asScala should contain theSameElementsAs expectedLog.stringTags.asScala
        contextMarkerCapture.getValue.eventData.asScala should contain theSameElementsAs expectedLog.eventData.asScala

        response.token shouldBe jsonContent
        response.flightItinerary.head.virtualInterlining shouldBe false
        response.flightItinerary.head.baggageUrl shouldBe Some("https://somebaggage.site.com")
        response.chargeablePaymentMethods shouldBe None
      })
    }

    "returns flight search with payment method successfully" in {
      clearInvocations(loggerMock)
      val contextMarkerCapture: ArgumentCaptor[RequestContextMarker] =
        ArgumentCaptor.forClass(classOf[RequestContextMarker])
      val setupBookingRequest = SetupBookingRequest(
        correlationId = None,
        userContext = mockUserContextOpt,
        paymentRequest = Some(paymentRequest),
        customerInfo = None,
        productsRequest = productsRequest
      )

      val content = StringContext treatEscapes flightsData.head.content

      val confirmPriceResponse =
        Serialization.jacksonMapper.readValue[ConfirmPriceResponse](content)

      val jsonContent = Serialization.jacksonMapper.writeValueAsString(confirmPriceResponse)

      when(flapiClientProxyV2Mock.postFlightsConfirmPrice(any(), any())(any()))
        .thenReturn(Future.successful(confirmPriceResponse, jsonContent))
      when(setupBookingContextIndian.requestContext).thenReturn(requestContextIndian)

      val expectedPaymentMethod = Some(MPBPaymentMethod.Visa.value)
      val confirmPriceRequest   = getFlightRequest(setupBookingRequest, expectedPaymentMethod)
      val expectedLog = FlapiSetupBookingServiceLog(
        Some("flight success"),
        Some(""),
        sessionId,
        BookingFlow.Package,
        mockUserContextOpt,
        setupBookingRequest.productsRequest.flightRequests.head,
        Some(confirmPriceResponse),
        FlightsRepository.confirmPriceEndpoint,
        setupBookingRequestContext.deviceContext,
        confirmPriceRequest
      )

      println(" Expected event data")
      println(expectedLog.eventData)

      flightsRepository.retrieveFlightConfirmationData(
        setupBookingRequest.productsRequest.flightRequests.head,
        setupBookingRequestContext.bookingFlowType,
        "THB",
        setupBookingRequestContext.whiteLabelInfo,
        setupBookingRequestContext.correlationId,
        setupBookingRequestContext.requestContext.locale,
        setupBookingRequestContext.sessionId,
        setupBookingRequest.userContext,
        setupBookingRequestContext.deviceContext,
        None,
        expectedPaymentMethod,
        campaignInfo = None,
        enabledFeatures = None,
        loyaltyRequestOpt = None,
        aabInfo = None
      )(setupBookingContextIndian) map (response => {
        verify(flapiClientProxyV2Mock).postFlightsConfirmPrice(any(), any())(any())
        verify(loggerMock).info(
          contextMarkerCapture.capture(),
          MockitoMatchers.eq("[FlightAPI]"),
          MockitoMatchers.eq(null)
        )
        contextMarkerCapture.getValue.stringTags.asScala should contain theSameElementsAs expectedLog.stringTags.asScala
        contextMarkerCapture.getValue.eventData.asScala should contain theSameElementsAs expectedLog.eventData.asScala
        response.token shouldBe jsonContent
      })

    }

    "returns flight search successfully for agency itinerary" in {
      clearInvocations(loggerMock)
      val contextMarkerCapture: ArgumentCaptor[RequestContextMarker] =
        ArgumentCaptor.forClass(classOf[RequestContextMarker])
      val setupBookingRequest = SetupBookingRequest(
        correlationId = None,
        userContext = mockUserContextOpt,
        paymentRequest = Some(paymentRequest),
        customerInfo = None,
        productsRequest = productsRequest
      )

      val content = StringContext treatEscapes flightsAgencyData.head.content

      val confirmPriceResponse =
        Serialization.jacksonMapper.readValue[ConfirmPriceResponse](content)

      val jsonContent = Serialization.jacksonMapper.writeValueAsString(confirmPriceResponse)
      val expectedCCList = Seq(
        SearchResponseSupplierAcceptedCreditCard(
          creditCardTypeId = 1,
          creditCardFeeTypeId = 1,
          creditCardFeeAmount = 0.0
        ),
        SearchResponseSupplierAcceptedCreditCard(
          creditCardTypeId = 2,
          creditCardFeeTypeId = 1,
          creditCardFeeAmount = 0.0
        )
      )

      when(flapiClientProxyV2Mock.postFlightsConfirmPrice(any(), any())(any()))
        .thenReturn(Future.successful(confirmPriceResponse, jsonContent))
      when(setupBookingContextIndian.requestContext).thenReturn(requestContextIndian)

      val confirmPriceRequest = getFlightRequest(setupBookingRequest)
      val expectedLog = FlapiSetupBookingServiceLog(
        Some("flight success"),
        Some(""),
        sessionId,
        BookingFlow.Package,
        mockUserContextOpt,
        setupBookingRequest.productsRequest.flightRequests.head,
        Some(confirmPriceResponse),
        FlightsRepository.confirmPriceEndpoint,
        setupBookingRequestContext.deviceContext,
        confirmPriceRequest
      )

      flightsRepository.retrieveFlightConfirmationData(
        setupBookingRequest.productsRequest.flightRequests.head,
        setupBookingRequestContext.bookingFlowType,
        "THB",
        setupBookingRequestContext.whiteLabelInfo,
        setupBookingRequestContext.correlationId,
        setupBookingRequestContext.requestContext.locale,
        setupBookingRequestContext.sessionId,
        setupBookingRequest.userContext,
        setupBookingRequestContext.deviceContext,
        packagingToken = None,
        selectedCreditCardTypeId = None,
        campaignInfo = None,
        enabledFeatures = None,
        loyaltyRequestOpt = None,
        aabInfo = None
      )(setupBookingContextIndian) map (response => {
        verify(loggerMock).info(
          contextMarkerCapture.capture(),
          MockitoMatchers.eq("[FlightAPI]"),
          MockitoMatchers.eq(null)
        )
        contextMarkerCapture.getValue.stringTags.asScala should contain theSameElementsAs expectedLog.stringTags.asScala
        contextMarkerCapture.getValue.eventData.asScala should contain theSameElementsAs expectedLog.eventData.asScala
        response.token shouldBe jsonContent
        response.flightItinerary.get.acceptedCreditCards shouldBe expectedCCList
        response.flightItinerary.get.paymentModel shouldBe PaymentModel.Agency.id
      })

    }

    val instantPriceConfirmRequest = InstantPriceConfirmRequest(
      Vector(SearchRequestPassengers(1, "ADT")),
      Vector(
        InstantSearchRequestTrip(
          2,
          200.45,
          "ABC",
          "Roundtrip",
          Vector(
            InstantSearchRequestSlice(
              4,
              Vector(
                InstantSearchRequestSegment(
                  3,
                  "123",
                  345,
                  "ECO",
                  "XYZ",
                  LocalDateTime.now().plusDays(1),
                  "ABC",
                  "XYZ"
                )
              )
            )
          )
        )
      ),
      externalTrackingId = None
    )
    "returns flight search successfully for instant price confirm request" in {
      clearInvocations(loggerMock)
      val contextMarkerCapture: ArgumentCaptor[RequestContextMarker] =
        ArgumentCaptor.forClass(classOf[RequestContextMarker])
      val setupBookingRequest = SetupBookingRequest(
        correlationId = None,
        userContext = Some(UserContextMock.value.copy(requestOrigin = "TH")),
        paymentRequest = Some(paymentRequest),
        customerInfo = None,
        productsRequest = ProductsRequest(
          bookingToken = None,
          propertyRequests = Seq.empty,
          flightRequests = Seq(FlightRequestItem(Some("test"), None, Some(instantPriceConfirmRequest))),
          packageRequest = None
        )
      )

      val content = StringContext treatEscapes flightsData.head.content

      val confirmPriceResponse =
        Serialization.jacksonMapper.readValue[ConfirmPriceResponse](content)

      val jsonContent = Serialization.jacksonMapper.writeValueAsString(confirmPriceResponse)

      when(flapiClientProxyV2Mock.postFlightsInstantPriceConfirm(*, *)(any()))
        .thenReturn(Future.successful(confirmPriceResponse, jsonContent))
      when(setupBookingContextIndian.requestContext).thenReturn(requestContextIndian)

      val confirmPriceRequest = getFlightRequest(setupBookingRequest)
      val expectedLog = FlapiSetupBookingServiceLog(
        Some("flight success"),
        Some(""),
        sessionId,
        BookingFlow.Package,
        mockUserContextOpt,
        setupBookingRequest.productsRequest.flightRequests.head,
        Some(confirmPriceResponse),
        FlightsRepository.instantConfirmPriceEndpoint,
        setupBookingRequestContext.deviceContext,
        confirmPriceRequest
      )

      flightsRepository.retrieveFlightConfirmationData(
        setupBookingRequest.productsRequest.flightRequests.head,
        setupBookingRequestContext.bookingFlowType,
        "THB",
        setupBookingRequestContext.whiteLabelInfo,
        setupBookingRequestContext.correlationId,
        setupBookingRequestContext.requestContext.locale,
        setupBookingRequestContext.sessionId,
        setupBookingRequest.userContext,
        setupBookingRequestContext.deviceContext,
        packagingToken = None,
        selectedCreditCardTypeId = None,
        campaignInfo = None,
        enabledFeatures = None,
        loyaltyRequestOpt = None,
        aabInfo = None
      )(setupBookingContextIndian) map (response => {
        verify(loggerMock).info(
          contextMarkerCapture.capture(),
          MockitoMatchers.eq("[FlightAPI]"),
          MockitoMatchers.eq(null)
        )
        contextMarkerCapture.getValue.stringTags.asScala should contain theSameElementsAs expectedLog.stringTags.asScala
        contextMarkerCapture.getValue.eventData.asScala should contain theSameElementsAs expectedLog.eventData.asScala
        response.token shouldBe jsonContent
      })

    }
    "returns flight search successfully for instant price confirm request while passing headers from BCRE to FLAPI" in {
      clearInvocations(loggerMock)
      val contextMarkerCapture: ArgumentCaptor[RequestContextMarker] =
        ArgumentCaptor.forClass(classOf[RequestContextMarker])
      val setupBookingRequest = SetupBookingRequest(
        correlationId = None,
        userContext = Some(
          UserContextMock.value.copy(botProfile =
            Option(
              "test-ag-bot-info"
            )
          )
        ),
        paymentRequest = Some(paymentRequest),
        customerInfo = None,
        productsRequest = ProductsRequest(
          bookingToken = None,
          propertyRequests = Seq.empty,
          flightRequests = Seq(FlightRequestItem(Some("test"), None, Some(instantPriceConfirmRequest))),
          packageRequest = None
        )
      )

      val content = StringContext treatEscapes flightsData.head.content

      val confirmPriceResponse =
        Serialization.jacksonMapper.readValue[ConfirmPriceResponse](content)

      val jsonContent = Serialization.jacksonMapper.writeValueAsString(confirmPriceResponse)

      val expectedHeaders = Map(
        "ag-bot-info"       -> "test-ag-bot-info",
        "ag-gk-rq-priority" -> "R2"
      )

      when(flapiClientProxyV2Mock.postFlightsInstantPriceConfirm(*, MockitoMatchers.eq(expectedHeaders))(any()))
        .thenReturn(Future.successful(confirmPriceResponse, jsonContent))
      when(setupBookingContextIndian.requestContext).thenReturn(requestContextIndian)

      val mockUserContext = Some(
        UserContextMock.value.copy(botProfile =
          Option(
            "test-ag-bot-info"
          )
        )
      )

      val confirmPriceRequest = getFlightRequest(setupBookingRequest)
      val expectedLog = FlapiSetupBookingServiceLog(
        Some("flight success"),
        Some(""),
        sessionId,
        BookingFlow.Package,
        mockUserContext,
        setupBookingRequest.productsRequest.flightRequests.head,
        Some(confirmPriceResponse),
        FlightsRepository.instantConfirmPriceEndpoint,
        setupBookingRequestContext.deviceContext,
        confirmPriceRequest
      )

      flightsRepository.retrieveFlightConfirmationData(
        setupBookingRequest.productsRequest.flightRequests.head,
        setupBookingRequestContext.bookingFlowType,
        "THB",
        setupBookingRequestContext.whiteLabelInfo,
        setupBookingRequestContext.correlationId,
        setupBookingRequestContext.requestContext.locale,
        setupBookingRequestContext.sessionId,
        setupBookingRequest.userContext,
        setupBookingRequestContext.deviceContext,
        packagingToken = None,
        selectedCreditCardTypeId = None,
        campaignInfo = None,
        enabledFeatures = None,
        loyaltyRequestOpt = None,
        aabInfo = None
      )(setupBookingContextIndian) map (response => {
        verify(flapiClientProxyV2Mock, times(1)).postFlightsInstantPriceConfirm(*, MockitoMatchers.eq(expectedHeaders))(
          any()
        )
        verify(loggerMock).info(
          contextMarkerCapture.capture(),
          MockitoMatchers.eq("[FlightAPI]"),
          MockitoMatchers.eq(null)
        )
        contextMarkerCapture.getValue.stringTags.asScala should contain theSameElementsAs expectedLog.stringTags.asScala
        contextMarkerCapture.getValue.eventData.asScala should contain theSameElementsAs expectedLog.eventData.asScala
        response.token shouldBe jsonContent
      })

    }

    "returns flight search successfully for virtual interlining" in {
      clearInvocations(loggerMock)
      val contextMarkerCapture: ArgumentCaptor[RequestContextMarker] =
        ArgumentCaptor.forClass(classOf[RequestContextMarker])
      val setupBookingRequest = SetupBookingRequest(
        correlationId = None,
        userContext = userContextOpt,
        paymentRequest = Some(paymentRequest),
        customerInfo = None,
        productsRequest = productsRequest
      )

      val content = StringContext treatEscapes flightsDataWithVirtualInterlining.head.content

      val confirmPriceResponse =
        Serialization.jacksonMapper.readValue[ConfirmPriceResponse](content)

      val jsonContent = Serialization.jacksonMapper.writeValueAsString(confirmPriceResponse)

      when(flapiClientProxyV2Mock.postFlightsConfirmPrice(any(), any())(any()))
        .thenReturn(Future.successful(confirmPriceResponse, jsonContent))
      when(setupBookingContextIndian.requestContext).thenReturn(requestContextIndian)

      val confirmPriceRequest = getFlightRequest(setupBookingRequest)
      val expectedLog = FlapiSetupBookingServiceLog(
        Some("flight success"),
        Some(""),
        sessionId,
        BookingFlow.Package,
        userContextOpt,
        setupBookingRequest.productsRequest.flightRequests.head,
        Some(confirmPriceResponse),
        FlightsRepository.confirmPriceEndpoint,
        setupBookingRequestContext.deviceContext,
        confirmPriceRequest
      )

      flightsRepository.retrieveFlightConfirmationData(
        setupBookingRequest.productsRequest.flightRequests.head,
        setupBookingRequestContext.bookingFlowType,
        "THB",
        setupBookingRequestContext.whiteLabelInfo,
        setupBookingRequestContext.correlationId,
        setupBookingRequestContext.requestContext.locale,
        setupBookingRequestContext.sessionId,
        setupBookingRequest.userContext,
        setupBookingRequestContext.deviceContext,
        packagingToken = None,
        selectedCreditCardTypeId = None,
        campaignInfo = None,
        enabledFeatures = None,
        loyaltyRequestOpt = None,
        aabInfo = None
      )(setupBookingContextIndian) map (response => {
        verify(loggerMock).info(
          contextMarkerCapture.capture(),
          MockitoMatchers.eq("[FlightAPI]"),
          MockitoMatchers.eq(null)
        )
        contextMarkerCapture.getValue.stringTags.asScala should contain theSameElementsAs expectedLog.stringTags.asScala
        contextMarkerCapture.getValue.eventData.asScala should contain theSameElementsAs expectedLog.eventData.asScala
        response.token shouldBe jsonContent
        response.flightItinerary.head.virtualInterlining shouldBe true
      })
    }

    "return error on invalid conditions" in {
      clearInvocations(loggerMock)

      val setupBookingRequest = SetupBookingRequest(
        correlationId = None,
        userContext = Some(UserContextMock.value.copy(requestOrigin = "TH")),
        paymentRequest = Some(
          PaymentRequest(
            ccBin = None,
            selectedPaymentMethod = None
          )
        ),
        customerInfo = None,
        productsRequest = ProductsRequest(
          bookingToken = None,
          propertyRequests = Seq.empty,
          flightRequests = Seq(FlightRequestItem(None, None, None)),
          packageRequest = None
        )
      )

      flightsRepository
        .retrieveFlightConfirmationData(
          setupBookingRequest.productsRequest.flightRequests.head,
          setupBookingRequestContext.bookingFlowType,
          "THB",
          setupBookingRequestContext.whiteLabelInfo,
          setupBookingRequestContext.correlationId,
          setupBookingRequestContext.requestContext.locale,
          setupBookingRequestContext.sessionId,
          setupBookingRequest.userContext,
          setupBookingRequestContext.deviceContext,
          packagingToken = None,
          selectedCreditCardTypeId = None,
          campaignInfo = None,
          enabledFeatures = None,
          loyaltyRequestOpt = None,
          aabInfo = None
        )(setupBookingContextIndian)
        .failed map { error =>
        error shouldBe an[IllegalArgumentException]
        error.getMessage shouldBe "No flight request item is defined"
      }
    }
    "returns paxNumberByType correctly (normal case)" in {
      clearInvocations(loggerMock)

      val mockPassenger = Some(
        Seq(
          SearchResponsePassenger(3, Flight.PaxType.Adult.toString),
          SearchResponsePassenger(2, Flight.PaxType.Child.toString),
          SearchResponsePassenger(1, Flight.PaxType.Infant.toString)
        )
      )
      val confirmPriceResponse = ConfirmPriceResponse(
        isCompleted = true,
        itinerary = None,
        passengers = mockPassenger,
        priceChange = None,
        agePolicies = None,
        retry = Retry(None)
      )

      val jsonContent = Serialization.jacksonMapper.writeValueAsString(confirmPriceResponse)
      when(flapiClientProxyV2Mock.postFlightsConfirmPrice(any(), any())(any()))
        .thenReturn(Future.successful(confirmPriceResponse, jsonContent))

      flightsRepository.retrieveFlightConfirmationData(
        FlightRequestItem(Some("test"), Some(ConfirmPriceRequest("test", "test")), None),
        setupBookingRequestContext.bookingFlowType,
        "THB",
        setupBookingRequestContext.whiteLabelInfo,
        setupBookingRequestContext.correlationId,
        setupBookingRequestContext.requestContext.locale,
        setupBookingRequestContext.sessionId,
        Some(UserContextMock.value.copy(requestOrigin = "TH")),
        setupBookingRequestContext.deviceContext,
        packagingToken = None,
        selectedCreditCardTypeId = None,
        campaignInfo = None,
        enabledFeatures = None,
        loyaltyRequestOpt = None,
        aabInfo = None
      )(setupBookingContextIndian) map (response => {
        response.paxNumberByType.getOrElse(Flight.PaxType.Adult, 0) shouldBe 3
        response.paxNumberByType.getOrElse(Flight.PaxType.Child, 0) shouldBe 2
        response.paxNumberByType.getOrElse(Flight.PaxType.Infant, 0) shouldBe 1
      })

    }

    "returns paxNumberByType correctly (duplicate and mismatch key case)" in {
      clearInvocations(loggerMock)

      val mockPassenger = Some(
        Seq(
          SearchResponsePassenger(3, Flight.PaxType.Child.toString),
          SearchResponsePassenger(2, Flight.PaxType.Child.toString),
          SearchResponsePassenger(1, "OOD")
        )
      )
      val confirmPriceResponse = ConfirmPriceResponse(
        isCompleted = true,
        itinerary = None,
        passengers = mockPassenger,
        priceChange = None,
        agePolicies = None,
        retry = Retry(None)
      )

      val jsonContent = Serialization.jacksonMapper.writeValueAsString(confirmPriceResponse)
      when(flapiClientProxyV2Mock.postFlightsConfirmPrice(any(), any())(any()))
        .thenReturn(Future.successful(confirmPriceResponse, jsonContent))

      flightsRepository.retrieveFlightConfirmationData(
        FlightRequestItem(Some("test"), Some(ConfirmPriceRequest("test", "test")), None),
        setupBookingRequestContext.bookingFlowType,
        "THB",
        setupBookingRequestContext.whiteLabelInfo,
        setupBookingRequestContext.correlationId,
        setupBookingRequestContext.requestContext.locale,
        setupBookingRequestContext.sessionId,
        mockUserContextOpt,
        setupBookingRequestContext.deviceContext,
        packagingToken = None,
        selectedCreditCardTypeId = None,
        campaignInfo = None,
        enabledFeatures = None,
        loyaltyRequestOpt = None,
        aabInfo = None
      )(setupBookingContextIndian) map (response => {
        response.paxNumberByType.getOrElse(Flight.PaxType.Adult, 0) shouldBe 1
        response.paxNumberByType.getOrElse(Flight.PaxType.Child, 0) shouldBe 5
        response.paxNumberByType.getOrElse(Flight.PaxType.Infant, 0) shouldBe 0
      })
    }

    "returns cart information" in {
      clearInvocations(loggerMock)

      val setupBookingRequest = SetupBookingRequest(
        correlationId = None,
        userContext = userContextOpt,
        paymentRequest = Some(paymentRequest),
        customerInfo = None,
        productsRequest = productsRequest
      )
      val content = StringContext treatEscapes flightsDataWithCart.head.content
      val confirmPriceResponse =
        Serialization.jacksonMapper.readValue[ConfirmPriceResponse](content)
      val jsonContent = Serialization.jacksonMapper.writeValueAsString(confirmPriceResponse)

      when(flapiClientProxyV2Mock.postFlightsConfirmPrice(any(), any())(any()))
        .thenReturn(Future.successful(confirmPriceResponse, jsonContent))
      when(requestContextIndian.featureAware).thenReturn(None)
      when(setupBookingContextIndian.requestContext).thenReturn(requestContextIndian)

      flightsRepository.retrieveFlightConfirmationData(
        setupBookingRequest.productsRequest.flightRequests.head,
        setupBookingRequestContext.bookingFlowType,
        "THB",
        setupBookingRequestContext.whiteLabelInfo,
        setupBookingRequestContext.correlationId,
        setupBookingRequestContext.requestContext.locale,
        setupBookingRequestContext.sessionId,
        setupBookingRequest.userContext,
        setupBookingRequestContext.deviceContext,
        packagingToken = None,
        selectedCreditCardTypeId = None,
        campaignInfo = None,
        enabledFeatures = None,
        loyaltyRequestOpt = None,
        aabInfo = None
      )(setupBookingContextIndian) map (response => {
        response.cart shouldBe Some(expectedCart)
        response.token shouldBe jsonContent
        response.flightItinerary.head.virtualInterlining shouldBe false
        response.flightItinerary.head.baggageUrl shouldBe Some("https://somebaggage.site.com")
      })

    }
  }

  private def getFlightRequest(
      setupBookingRequest: SetupBookingRequest,
      selectedCreditCardTypeId: Option[Int] = None
  ) = {
    flightsRepository.getFlightsRequest(
      request = setupBookingRequest.productsRequest.flightRequests.head,
      chargeCurrency = "THB",
      whiteLabelInfo = setupBookingRequestContext.whiteLabelInfo,
      correlationId = setupBookingRequestContext.correlationId,
      locale = setupBookingRequestContext.requestContext.locale,
      userContext = setupBookingRequest.userContext,
      deviceContext = setupBookingRequestContext.deviceContext,
      packagingToken = None,
      selectedCreditCardTypeId = selectedCreditCardTypeId,
      campaignInfo = None,
      enabledFeatures = None,
      campaignsIds = None,
      loyaltyRequestOpt = None,
      selectedPaymentMethodId = setupBookingRequest.paymentRequest.flatMap(_.selectedPaymentMethod),
      aabInfo = None
    )
  }

  "getFlightRequest" should {
    "return correct flight request according PaymentMethodFee Flag in request" in {
      clearInvocations(loggerMock)

      val confirmPriceRequestWithPaymentFee = ConfirmPriceRequest(
        itineraryId = "test",
        searchToken = "test",
        enabledFeatures = Some(Seq("PaymentMethodFee"))
      )
      val flightRequestWithPaymentFee = Seq(
        FlightRequestItem(
          id = Some("1"),
          confirmPriceRequest = Some(confirmPriceRequestWithPaymentFee),
          instantPriceConfirmRequest = None
        )
      )

      val productRequestWithPaymentFee = productsRequest.copy(flightRequests = flightRequestWithPaymentFee)

      val paymentRequestWithPaymentFee = paymentRequest.copy(selectedPaymentMethod = Some(2))

      val setupBookingRequest = SetupBookingRequest(
        correlationId = None,
        userContext = mockUserContextOpt,
        paymentRequest = Some(paymentRequestWithPaymentFee),
        customerInfo = None,
        productsRequest = productRequestWithPaymentFee
      )

      val actualConfirmPriceRequest = getFlightRequest(setupBookingRequest)
      actualConfirmPriceRequest.get.left.get.paymentInfo.get.selectedPaymentMethodId shouldBe Some(2)
      actualConfirmPriceRequest.get.left.get.enabledFeatures.get.contains("PaymentMethodFee") shouldBe true
    }

    "return correct flight request when PaymentMethodFee Flag is not in request" in {
      clearInvocations(loggerMock)

      val confirmPriceRequestWithPaymentFee = ConfirmPriceRequest(
        itineraryId = "test",
        searchToken = "test",
        enabledFeatures = None
      )
      val flightRequestWithPaymentFee = Seq(
        FlightRequestItem(
          id = Some("1"),
          confirmPriceRequest = Some(confirmPriceRequestWithPaymentFee),
          instantPriceConfirmRequest = None
        )
      )

      val productRequestWithPaymentFee = productsRequest.copy(flightRequests = flightRequestWithPaymentFee)

      val paymentRequestWithPaymentFee = paymentRequest.copy(selectedPaymentMethod = Some(2))

      val setupBookingRequest = SetupBookingRequest(
        correlationId = None,
        userContext = mockUserContextOpt,
        paymentRequest = Some(paymentRequestWithPaymentFee),
        customerInfo = None,
        productsRequest = productRequestWithPaymentFee
      )

      val actualConfirmPriceRequest = getFlightRequest(setupBookingRequest)
      actualConfirmPriceRequest.get.left.get.paymentInfo.get.selectedPaymentMethodId shouldBe None
      actualConfirmPriceRequest.get.left.get.enabledFeatures shouldBe None
    }

    "return correct flight request with isAgentAssistedBooking = true for AAB token" in {
      clearInvocations(loggerMock)
      val mockCapiAABToken        = "~aabToken~"
      val userContextWithAABToken = mockUserContextOpt.map(_.copy(capiToken = Some(mockCapiAABToken)))
      val setupBookingRequest = SetupBookingRequest(
        correlationId = None,
        userContext = userContextWithAABToken,
        paymentRequest = Some(paymentRequest),
        customerInfo = None,
        productsRequest = productsRequest
      )
      when(customerApiTokenUtil.isAgentAssistantBooking(mockCapiAABToken)).thenReturn(true)
      val actualConfirmPriceRequest = getFlightRequest(setupBookingRequest)
      actualConfirmPriceRequest.get.left.get.context.isAgentAssistedBooking shouldBe Some(true)
    }

    "return correct flight request with isAgentAssistedBooking = false for non-AAB token" in {
      clearInvocations(loggerMock)
      val mockCapiToken           = "~token~"
      val userContextWithAABToken = mockUserContextOpt.map(_.copy(capiToken = Some(mockCapiToken)))
      val setupBookingRequest = SetupBookingRequest(
        correlationId = None,
        userContext = userContextWithAABToken,
        paymentRequest = Some(paymentRequest),
        customerInfo = None,
        productsRequest = productsRequest
      )
      when(customerApiTokenUtil.isAgentAssistantBooking(mockCapiToken)).thenReturn(false)
      val actualConfirmPriceRequest = getFlightRequest(setupBookingRequest)
      actualConfirmPriceRequest.get.left.get.context.isAgentAssistedBooking shouldBe Some(false)
    }
  }

  "retrieveFlightItineraryDetails" should {
    "returns flight details successfully" in {
      clearInvocations(loggerMock)
      val contextMarkerCapture: ArgumentCaptor[RequestContextMarker] =
        ArgumentCaptor.forClass(classOf[RequestContextMarker])
      val setupBookingRequest = SetupBookingRequest(
        correlationId = None,
        userContext = mockUserContextOpt,
        paymentRequest = Some(paymentRequest),
        customerInfo = None,
        productsRequest = productsRequest
      )

      val content = StringContext treatEscapes flightsData.head.content

      val itineraryDetailsResponse =
        Serialization.jacksonMapper.readValue[ItineraryDetailsResponse](content)

      val confirmPriceResponse = ConfirmPriceResponse(
        itineraryDetailsResponse.isCompleted,
        itineraryDetailsResponse.itinerary,
        itineraryDetailsResponse.passengers,
        retry = itineraryDetailsResponse.retry
      )

      val jsonContent = Serialization.jacksonMapper.writeValueAsString(confirmPriceResponse)

      when(flapiClientProxyV2Mock.postFlightItineraryDetails(any(), any())(any()))
        .thenReturn(Future.successful(itineraryDetailsResponse, jsonContent))

      val confirmPriceRequest = getFlightRequest(setupBookingRequest)
      val expectedLog = FlapiSetupBookingServiceLog(
        Some("flight success"),
        Some(""),
        sessionId,
        BookingFlow.Package,
        mockUserContextOpt,
        setupBookingRequest.productsRequest.flightRequests.head,
        Some(confirmPriceResponse),
        FlightsRepository.itineraryDetailsEndpoint,
        setupBookingRequestContext.deviceContext,
        confirmPriceRequest
      )

      flightsRepository.retrieveFlightItineraryDetails(
        setupBookingRequest.productsRequest.flightRequests.head,
        setupBookingRequestContext.bookingFlowType,
        "THB",
        setupBookingRequestContext.whiteLabelInfo,
        setupBookingRequestContext.correlationId,
        setupBookingRequestContext.requestContext.locale,
        setupBookingRequestContext.sessionId,
        setupBookingRequest.userContext,
        setupBookingRequestContext.deviceContext,
        packagingToken = None,
        aabInfo = None
      ) map { response =>
        verify(loggerMock).info(
          contextMarkerCapture.capture(),
          MockitoMatchers.eq("[FlightAPI]"),
          MockitoMatchers.eq(null)
        )
        contextMarkerCapture.getValue.stringTags.asScala should contain theSameElementsAs expectedLog.stringTags.asScala
        contextMarkerCapture.getValue.eventData.asScala should contain theSameElementsAs expectedLog.eventData.asScala
        response.token shouldBe jsonContent
      }
    }

    "return error on invalid conditions" in {
      clearInvocations(loggerMock)

      val setupBookingRequest = SetupBookingRequest(
        correlationId = None,
        userContext = mockUserContextOpt,
        paymentRequest = Some(
          PaymentRequest(
            ccBin = None,
            selectedPaymentMethod = None
          )
        ),
        customerInfo = None,
        productsRequest = ProductsRequest(
          bookingToken = None,
          propertyRequests = Seq.empty,
          flightRequests = Seq(FlightRequestItem(None, None, None)),
          packageRequest = None
        )
      )

      flightsRepository
        .retrieveFlightItineraryDetails(
          setupBookingRequest.productsRequest.flightRequests.head,
          setupBookingRequestContext.bookingFlowType,
          "THB",
          setupBookingRequestContext.whiteLabelInfo,
          setupBookingRequestContext.correlationId,
          setupBookingRequestContext.requestContext.locale,
          setupBookingRequestContext.sessionId,
          setupBookingRequest.userContext,
          setupBookingRequestContext.deviceContext,
          packagingToken = None,
          aabInfo = None
        )
        .failed map { error =>
        error shouldBe an[IllegalArgumentException]
        error.getMessage shouldBe "No flight request item is defined"
      }
    }

    "returns paxNumberByType correctly (normal case)" in {
      clearInvocations(loggerMock)

      val content = StringContext treatEscapes flightsData.head.content

      val mockPassenger = Some(
        Seq(
          SearchResponsePassenger(3, Flight.PaxType.Adult.toString),
          SearchResponsePassenger(2, Flight.PaxType.Child.toString),
          SearchResponsePassenger(1, Flight.PaxType.Infant.toString)
        )
      )

      val itineraryDetailsResponse = ItineraryDetailsResponse(
        isCompleted = true,
        itinerary = None,
        passengers = mockPassenger,
        retry = Retry(None)
      )
      Serialization.jacksonMapper.readValue[ItineraryDetailsResponse](content)

      val confirmPriceResponse = ConfirmPriceResponse(
        itineraryDetailsResponse.isCompleted,
        itineraryDetailsResponse.itinerary,
        itineraryDetailsResponse.passengers,
        retry = itineraryDetailsResponse.retry
      )

      val jsonContent = Serialization.jacksonMapper.writeValueAsString(confirmPriceResponse)

      when(flapiClientProxyV2Mock.postFlightItineraryDetails(any(), any())(any()))
        .thenReturn(Future.successful(itineraryDetailsResponse, jsonContent))

      flightsRepository.retrieveFlightItineraryDetails(
        FlightRequestItem(Some("test"), Some(ConfirmPriceRequest("test", "test")), None),
        setupBookingRequestContext.bookingFlowType,
        "THB",
        setupBookingRequestContext.whiteLabelInfo,
        setupBookingRequestContext.correlationId,
        setupBookingRequestContext.requestContext.locale,
        setupBookingRequestContext.sessionId,
        Some(UserContextMock.value.copy(requestOrigin = "TH")),
        setupBookingRequestContext.deviceContext,
        packagingToken = None,
        aabInfo = None
      ) map { response =>
        response.paxNumberByType.getOrElse(Flight.PaxType.Adult, 0) shouldBe 3
        response.paxNumberByType.getOrElse(Flight.PaxType.Child, 0) shouldBe 2
        response.paxNumberByType.getOrElse(Flight.PaxType.Infant, 0) shouldBe 1
      }
    }

    "returns paxNumberByType correctly (duplicate and mismatch key case)" in {
      clearInvocations(loggerMock)

      val content = StringContext treatEscapes flightsData.head.content

      val mockPassenger = Some(
        Seq(
          SearchResponsePassenger(3, "DEAL"),
          SearchResponsePassenger(2, Flight.PaxType.Infant.toString),
          SearchResponsePassenger(2, Flight.PaxType.Infant.toString)
        )
      )

      val itineraryDetailsResponse = ItineraryDetailsResponse(
        isCompleted = true,
        itinerary = None,
        passengers = mockPassenger,
        retry = Retry(None)
      )
      Serialization.jacksonMapper.readValue[ItineraryDetailsResponse](content)

      val confirmPriceResponse = ConfirmPriceResponse(
        itineraryDetailsResponse.isCompleted,
        itineraryDetailsResponse.itinerary,
        itineraryDetailsResponse.passengers,
        retry = itineraryDetailsResponse.retry
      )

      val jsonContent = Serialization.jacksonMapper.writeValueAsString(confirmPriceResponse)

      when(flapiClientProxyV2Mock.postFlightItineraryDetails(any(), any())(any()))
        .thenReturn(Future.successful(itineraryDetailsResponse, jsonContent))

      flightsRepository.retrieveFlightItineraryDetails(
        FlightRequestItem(Some("test"), Some(ConfirmPriceRequest("test", "test")), None),
        setupBookingRequestContext.bookingFlowType,
        "THB",
        setupBookingRequestContext.whiteLabelInfo,
        setupBookingRequestContext.correlationId,
        setupBookingRequestContext.requestContext.locale,
        setupBookingRequestContext.sessionId,
        mockUserContextOpt,
        setupBookingRequestContext.deviceContext,
        packagingToken = None,
        aabInfo = None
      ) map { response =>
        response.paxNumberByType.getOrElse(Flight.PaxType.Adult, 0) shouldBe 3
        response.paxNumberByType.getOrElse(Flight.PaxType.Child, 0) shouldBe 0
        response.paxNumberByType.getOrElse(Flight.PaxType.Infant, 0) shouldBe 4
      }
    }

    "with chargeablePaymentMethods" in {
      clearInvocations(loggerMock)

      val setupBookingRequest = SetupBookingRequest(
        correlationId = None,
        userContext = userContextOpt,
        paymentRequest = Some(paymentRequest),
        customerInfo = None,
        productsRequest = productsRequest
      )

      val content = StringContext treatEscapes flightsDataWithPaymentFee.head.content

      val confirmPriceResponse =
        Serialization.jacksonMapper.readValue[ConfirmPriceResponse](content)

      val jsonContent = Serialization.jacksonMapper.writeValueAsString(confirmPriceResponse)

      when(flapiClientProxyV2Mock.postFlightsConfirmPrice(any(), any())(any()))
        .thenReturn(Future.successful(confirmPriceResponse, jsonContent))
      when(requestContextIndian.featureAware).thenReturn(None)
      when(setupBookingContextIndian.requestContext).thenReturn(requestContextIndian)

      val confirmPriceRequest = getFlightRequest(setupBookingRequest)

      flightsRepository.retrieveFlightConfirmationData(
        request = setupBookingRequest.productsRequest.flightRequests.head,
        bookingFlowType = setupBookingRequestContext.bookingFlowType,
        chargeCurrency = "THB",
        whiteLabelInfo = setupBookingRequestContext.whiteLabelInfo,
        correlationId = setupBookingRequestContext.correlationId,
        locale = setupBookingRequestContext.requestContext.locale,
        sessionId = setupBookingRequestContext.sessionId,
        userContext = setupBookingRequest.userContext,
        deviceContext = setupBookingRequestContext.deviceContext,
        packagingToken = None,
        selectedCreditCardTypeId = None,
        campaignInfo = None,
        enabledFeatures = None,
        loyaltyRequestOpt = None,
        aabInfo = None
      )(setupBookingContextIndian) map (response =>
        response.chargeablePaymentMethods shouldBe Some(List(ChargeablePaymentMethod(1), ChargeablePaymentMethod(2)))
      )
    }
  }

  "isReturnPartialResult" should {
    val response  = mock[ConfirmPriceResponse]
    val itinerary = mock[SearchResponseItinerary]
    val requests =
      Table(
        (
          "response",
          "isCompleted",
          "itinerary",
          "expected"
        ),
        (response, false, None, false),
        (response, true, None, false),
        (response, false, Some(itinerary), true),
        (response, true, Some(itinerary), false)
      )

    "Test isReturnPartialResult" in
      forEvery(requests) {
        (
            response,
            isCompleted,
            itinerary,
            expected
        ) =>
          when(response.isCompleted).thenReturn(isCompleted)
          when(response.itinerary).thenReturn(itinerary)
          FlightsRepository.isReturnPartialResult(response) should equal(expected)
      }

  }

  "with add ons" should {
    case class IsAddOnsEqual(addOns: Option[FLAPIAddOns]) extends ArgumentMatcher[FlapiConfirmPriceRequest] {
      override def matches(argument: FlapiConfirmPriceRequest): Boolean = argument.addOns == addOns
    }

    "seat selection" should {
      "returns postFlightsConfirmprice successfully with seat selection ..." in {

        val confirmPriceResp = ConfirmPriceResponse(isCompleted = true, retry = Retry(None))
        val jsonContent      = Serialization.jacksonMapper.writeValueAsString(confirmPriceResp)
        when(flapiClientProxyV2Mock.postFlightsConfirmPrice(any(), any())(any()))
          .thenReturn(Future.successful(confirmPriceResp, jsonContent))

        val addOn = FlightAddOn(
          passengerId = 1,
          segmentId = Some(1),
          seat = Some(FlightSeatSelection("A", "25", seatToken = None))
        )
        val passenger = AncillaryPassengerDetails(1, "ADT")
        val confirmPriceRequest =
          ConfirmPriceRequest(
            itineraryId = "test",
            searchToken = "test",
            addOns = Some(Seq(addOn)),
            passengers = Some(Vector(passenger))
          )

        flightsRepository.retrieveFlightConfirmationData(
          FlightRequestItem(Some("test"), Some(confirmPriceRequest), None),
          setupBookingRequestContext.bookingFlowType,
          "THB",
          setupBookingRequestContext.whiteLabelInfo,
          setupBookingRequestContext.correlationId,
          setupBookingRequestContext.requestContext.locale,
          setupBookingRequestContext.sessionId,
          userContextOpt,
          setupBookingRequestContext.deviceContext,
          packagingToken = None,
          selectedCreditCardTypeId = None,
          campaignInfo = None,
          enabledFeatures = None,
          loyaltyRequestOpt = None,
          aabInfo = None
        )(setupBookingContextIndian) map (response => {
          response.isCompleted shouldBe true
          response.token shouldBe jsonContent
        })
      }

      "returns postFlightsConfirmprice successfully with seat selection using new seats field ..." in {

        val confirmPriceResp = ConfirmPriceResponse(isCompleted = true, retry = Retry(None))
        val jsonContent      = Serialization.jacksonMapper.writeValueAsString(confirmPriceResp)
        when(flapiClientProxyV2Mock.postFlightsConfirmPrice(any(), any())(any()))
          .thenReturn(Future.successful(confirmPriceResp, jsonContent))

        val addOn = FlightAddOn(
          passengerId = 1,
          seats = Some(Vector(FlightSeatSelection(column = "A", row = "25", segmentId = 1, seatToken = None)))
        )
        val passenger = AncillaryPassengerDetails(1, "ADT")
        val confirmPriceRequest =
          ConfirmPriceRequest(
            itineraryId = "test",
            searchToken = "test",
            addOns = Some(Seq(addOn)),
            passengers = Some(Vector(passenger))
          )

        flightsRepository.retrieveFlightConfirmationData(
          FlightRequestItem(Some("test"), Some(confirmPriceRequest), None),
          setupBookingRequestContext.bookingFlowType,
          "THB",
          setupBookingRequestContext.whiteLabelInfo,
          setupBookingRequestContext.correlationId,
          setupBookingRequestContext.requestContext.locale,
          setupBookingRequestContext.sessionId,
          userContextOpt,
          setupBookingRequestContext.deviceContext,
          packagingToken = None,
          selectedCreditCardTypeId = None,
          campaignInfo = None,
          enabledFeatures = None,
          loyaltyRequestOpt = None,
          aabInfo = None
        )(setupBookingContextIndian) map (response => {
          response.isCompleted shouldBe true
          response.token shouldBe jsonContent
        })
      }

      "returns postFlightsConfirmprice successfully with seat selection both old and new fields ..." in {

        val confirmPriceResp = ConfirmPriceResponse(isCompleted = true, retry = Retry(None))
        val jsonContent      = Serialization.jacksonMapper.writeValueAsString(confirmPriceResp)
        when(flapiClientProxyV2Mock.postFlightsConfirmPrice(any(), any())(any()))
          .thenReturn(Future.successful(confirmPriceResp, jsonContent))

        val addOn = FlightAddOn(
          passengerId = 1,
          segmentId = Some(1),
          seat = Some(FlightSeatSelection("B", "30", seatToken = None)),
          seats = Some(Vector(FlightSeatSelection(column = "A", row = "25", segmentId = 1, seatToken = None)))
        )
        val passenger = AncillaryPassengerDetails(1, "ADT")
        val confirmPriceRequest =
          ConfirmPriceRequest(
            itineraryId = "test",
            searchToken = "test",
            addOns = Some(Seq(addOn)),
            passengers = Some(Vector(passenger))
          )

        flightsRepository
          .retrieveFlightConfirmationData(
            FlightRequestItem(Some("test"), Some(confirmPriceRequest), None),
            setupBookingRequestContext.bookingFlowType,
            "THB",
            setupBookingRequestContext.whiteLabelInfo,
            setupBookingRequestContext.correlationId,
            setupBookingRequestContext.requestContext.locale,
            setupBookingRequestContext.sessionId,
            userContextOpt,
            setupBookingRequestContext.deviceContext,
            packagingToken = None,
            selectedCreditCardTypeId = None,
            campaignInfo = None,
            enabledFeatures = None,
            loyaltyRequestOpt = None,
            aabInfo = None
          )(setupBookingContextIndian) map (response => {
          response.isCompleted shouldBe true
          response.token shouldBe jsonContent
        })
      }
    }

    "mapAddOn correctly" should {
      val testCases = Table(
        ("caseName", "passengers", "requestedAddOns", "expectedAddOns"),
        (
          "2 passengers, selecting 2 baggage options",
          Vector(AncillaryPassengerDetails(1, "ADT"), AncillaryPassengerDetails(2, "ADT")),
          Seq(
            FlightAddOn(
              passengerId = 1,
              baggage = Some(
                Seq(
                  BaggageSelection(
                    sliceId = Some(1),
                    baggageTokens = Vector("baggageTokens-1-1-1", "baggageTokens-1-1-2"),
                    scope = None,
                    scopeRefId = None
                  ),
                  BaggageSelection(
                    sliceId = Some(2),
                    baggageTokens = Vector("baggageTokens-1-2-1", "baggageTokens-1-2-2"),
                    scope = None,
                    scopeRefId = None
                  )
                )
              )
            ),
            FlightAddOn(
              passengerId = 2,
              baggage = Some(
                Seq(
                  BaggageSelection(
                    sliceId = Some(1),
                    baggageTokens = Vector("baggageTokens-2-1-1"),
                    scope = None,
                    scopeRefId = None
                  )
                )
              )
            )
          ),
          Some(
            FLAPIAddOns(
              seatSelection = None,
              passengers = Some(
                Seq(
                  AddonRequestPassenger(id = 1, `type` = "ADT", frequentFlyer = None),
                  AddonRequestPassenger(id = 2, `type` = "ADT", frequentFlyer = None)
                )
              ),
              baggage = Some(
                Seq(
                  BaggageDetail(
                    passengerRefId = 1,
                    sliceId = Some(1),
                    baggageTokens = Seq("baggageTokens-1-1-1", "baggageTokens-1-1-2"),
                    scope = None,
                    scopeRefId = None
                  ),
                  BaggageDetail(
                    passengerRefId = 1,
                    sliceId = Some(2),
                    baggageTokens = Seq("baggageTokens-1-2-1", "baggageTokens-1-2-2"),
                    scope = None,
                    scopeRefId = None
                  ),
                  BaggageDetail(
                    passengerRefId = 2,
                    sliceId = Some(1),
                    baggageTokens = Seq("baggageTokens-2-1-1"),
                    scope = None,
                    scopeRefId = None
                  )
                )
              ),
              seats = None
            )
          )
        ),
        (
          "1 passenger, selecting 1 baggage with seats",
          Vector(AncillaryPassengerDetails(1, "ADT")),
          Seq(
            FlightAddOn(
              passengerId = 1,
              baggage = Some(
                Seq(
                  BaggageSelection(
                    sliceId = Some(1),
                    baggageTokens = Vector("baggageTokens-1-1-1"),
                    scope = None,
                    scopeRefId = None
                  )
                )
              ),
              seats = Some(
                Seq(
                  FlightSeatSelection(
                    column = "A",
                    row = "1",
                    segmentId = 1,
                    seatToken = Some("seatToken")
                  )
                )
              )
            )
          ),
          Some(
            FLAPIAddOns(
              seatSelection = None,
              passengers = Some(
                Seq(
                  AddonRequestPassenger(id = 1, `type` = "ADT", frequentFlyer = None)
                )
              ),
              baggage = Some(
                Seq(
                  BaggageDetail(
                    passengerRefId = 1,
                    sliceId = Some(1),
                    baggageTokens = Seq("baggageTokens-1-1-1"),
                    scope = None,
                    scopeRefId = None
                  )
                )
              ),
              seats = Some(
                Seq(
                  SeatDetail(
                    segmentId = 1,
                    row = "1",
                    column = "A",
                    passengerRefId = 1,
                    seatToken = Some("seatToken")
                  )
                )
              )
            )
          )
        ),
        (
          "2 passengers, selecting 2 baggage options, using scope and scopeRefId",
          Vector(AncillaryPassengerDetails(1, "ADT"), AncillaryPassengerDetails(2, "ADT")),
          Seq(
            FlightAddOn(
              passengerId = 1,
              baggage = Some(
                Seq(
                  BaggageSelection(
                    sliceId = None,
                    baggageTokens = Vector("baggageTokens-1-1-1", "baggageTokens-1-1-2"),
                    scope = Some(Scope.SLICE.toString),
                    scopeRefId = Some(1)
                  ),
                  BaggageSelection(
                    sliceId = None,
                    baggageTokens = Vector("baggageTokens-1-2-1", "baggageTokens-1-2-2"),
                    scope = Some(Scope.SLICE.toString),
                    scopeRefId = Some(2)
                  )
                )
              )
            ),
            FlightAddOn(
              passengerId = 2,
              baggage = Some(
                Seq(
                  BaggageSelection(
                    sliceId = None,
                    baggageTokens = Vector("baggageTokens-2-1-1"),
                    scope = Some(Scope.SLICE.toString),
                    scopeRefId = Some(1)
                  )
                )
              )
            )
          ),
          Some(
            FLAPIAddOns(
              seatSelection = None,
              passengers = Some(
                Seq(
                  AddonRequestPassenger(id = 1, `type` = "ADT", frequentFlyer = None),
                  AddonRequestPassenger(id = 2, `type` = "ADT", frequentFlyer = None)
                )
              ),
              baggage = Some(
                Seq(
                  BaggageDetail(
                    passengerRefId = 1,
                    sliceId = None,
                    baggageTokens = Seq("baggageTokens-1-1-1", "baggageTokens-1-1-2"),
                    scope = Some(Scope.SLICE.toString),
                    scopeRefId = Some(1)
                  ),
                  BaggageDetail(
                    passengerRefId = 1,
                    sliceId = None,
                    baggageTokens = Seq("baggageTokens-1-2-1", "baggageTokens-1-2-2"),
                    scope = Some(Scope.SLICE.toString),
                    scopeRefId = Some(2)
                  ),
                  BaggageDetail(
                    passengerRefId = 2,
                    sliceId = None,
                    baggageTokens = Seq("baggageTokens-2-1-1"),
                    scope = Some(Scope.SLICE.toString),
                    scopeRefId = Some(1)
                  )
                )
              ),
              seats = None
            )
          )
        ),
        (
          "1 passenger, selecting 1 baggage with seats, using scope and scopeRefId",
          Vector(AncillaryPassengerDetails(1, "ADT")),
          Seq(
            FlightAddOn(
              passengerId = 1,
              baggage = Some(
                Seq(
                  BaggageSelection(
                    sliceId = None,
                    baggageTokens = Vector("baggageTokens-1-1-1"),
                    scope = Some(Scope.SLICE.toString),
                    scopeRefId = Some(1)
                  )
                )
              ),
              seats = Some(
                Seq(
                  FlightSeatSelection(
                    column = "A",
                    row = "1",
                    segmentId = 1,
                    seatToken = Some("seatToken")
                  )
                )
              )
            )
          ),
          Some(
            FLAPIAddOns(
              seatSelection = None,
              passengers = Some(
                Seq(
                  AddonRequestPassenger(id = 1, `type` = "ADT", frequentFlyer = None)
                )
              ),
              baggage = Some(
                Seq(
                  BaggageDetail(
                    passengerRefId = 1,
                    sliceId = None,
                    baggageTokens = Seq("baggageTokens-1-1-1"),
                    scope = Some(Scope.SLICE.toString),
                    scopeRefId = Some(1)
                  )
                )
              ),
              seats = Some(
                Seq(
                  SeatDetail(
                    segmentId = 1,
                    row = "1",
                    column = "A",
                    passengerRefId = 1,
                    seatToken = Some("seatToken")
                  )
                )
              )
            )
          )
        ),
        (
          "1 passenger, without selecting anything (None case)",
          Vector(AncillaryPassengerDetails(1, "ADT")),
          Seq(
            FlightAddOn(
              passengerId = 1,
              baggage = None,
              seats = None
            )
          ),
          Some(
            FLAPIAddOns(
              seatSelection = None,
              passengers = Some(
                Seq(
                  AddonRequestPassenger(id = 1, `type` = "ADT", frequentFlyer = None)
                )
              ),
              baggage = None,
              seats = None
            )
          )
        ),
        (
          "1 passenger, without selecting anything (Seq.empty case)",
          Vector(AncillaryPassengerDetails(1, "ADT")),
          Seq(
            FlightAddOn(
              passengerId = 1,
              baggage = Some(Seq.empty),
              seats = Some(Seq.empty)
            )
          ),
          Some(
            FLAPIAddOns(
              seatSelection = None,
              passengers = Some(
                Seq(
                  AddonRequestPassenger(id = 1, `type` = "ADT", frequentFlyer = None)
                )
              ),
              baggage = None,
              seats = None
            )
          )
        )
      )

      forEvery(testCases) { (caseName, passengers, requestedAddOns, expectedAddOns) =>
        caseName in {

          val confirmPriceResp = ConfirmPriceResponse(isCompleted = true, retry = Retry(None))
          val jsonContent      = Serialization.jacksonMapper.writeValueAsString(confirmPriceResp)
          when(flapiClientProxyV2Mock.postFlightsConfirmPrice(any(), any())(any()))
            .thenReturn(Future.successful(confirmPriceResp, jsonContent))

          val confirmPriceRequest =
            ConfirmPriceRequest(
              itineraryId = "test",
              searchToken = "test",
              addOns = Some(requestedAddOns),
              passengers = Some(passengers)
            )

          flightsRepository
            .retrieveFlightConfirmationData(
              FlightRequestItem(Some("test"), Some(confirmPriceRequest), None),
              setupBookingRequestContext.bookingFlowType,
              "USD",
              setupBookingRequestContext.whiteLabelInfo,
              setupBookingRequestContext.correlationId,
              setupBookingRequestContext.requestContext.locale,
              setupBookingRequestContext.sessionId,
              userContextOpt,
              setupBookingRequestContext.deviceContext,
              packagingToken = None,
              selectedCreditCardTypeId = None,
              campaignInfo = None,
              enabledFeatures = None,
              loyaltyRequestOpt = None,
              aabInfo = None
            )(setupBookingContextIndian) map (response => {
            verify(flapiClientProxyV2Mock).postFlightsConfirmPrice(argThat(IsAddOnsEqual(expectedAddOns)), *)(any())
            response.isCompleted shouldBe true
            response.token shouldBe jsonContent
          })
        }
      }
    }
  }

  "with brands" should {
    "return postFlightConfirmprice successfully with brand selection" in {

      val confirmPriceResp = ConfirmPriceResponse(isCompleted = true, retry = Retry(None))
      val jsonContent      = Serialization.jacksonMapper.writeValueAsString(confirmPriceResp)

      val brands = Seq(
        FlightBrand(
          scope = "ITINERARY",
          scopeRefId = 1,
          brandId = "brandId1"
        )
      )
      val passenger = AncillaryPassengerDetails(1, "ADT")
      val confirmPriceRequest =
        ConfirmPriceRequest(
          itineraryId = "test",
          searchToken = "test",
          brands = Some(brands),
          passengers = Some(Vector(passenger))
        )

      when(flapiClientProxyV2Mock.postFlightsConfirmPrice(any(), any())(any()))
        .thenAnswer { (invocation: InvocationOnMock) =>
          val confirmPriceRequest: FlapiConfirmPriceRequest = invocation.getArgument(0)
          confirmPriceRequest.brands.isDefined shouldBe true
          confirmPriceRequest.brands.get.size shouldBe brands.size
          confirmPriceRequest.brands.get.zip(brands).map {
            case (actual, expect) =>
              actual.scope shouldBe expect.scope
              actual.scopeRefId shouldBe expect.scopeRefId
              actual.brandId shouldBe expect.brandId
          }

          Future.successful(confirmPriceResp, jsonContent)
        }

      flightsRepository
        .retrieveFlightConfirmationData(
          FlightRequestItem(Some("test"), Some(confirmPriceRequest), None),
          setupBookingRequestContext.bookingFlowType,
          "USD",
          setupBookingRequestContext.whiteLabelInfo,
          setupBookingRequestContext.correlationId,
          setupBookingRequestContext.requestContext.locale,
          setupBookingRequestContext.sessionId,
          userContextOpt,
          setupBookingRequestContext.deviceContext,
          packagingToken = None,
          selectedCreditCardTypeId = None,
          campaignInfo = None,
          enabledFeatures = None,
          loyaltyRequestOpt = None,
          aabInfo = None
        )(setupBookingContextIndian) map (response => {
        response.isCompleted shouldBe true
        response.token shouldBe jsonContent
      })
    }
  }

  "with loyalty request" should {
    "return postFlightConfirmprice successfully with loyalty request" in {

      val partnerClaimToken       = Some("partner-claim-0")
      val selectedOfferIdentifier = Some("identifier-0")
      val points                  = Some(1000d)
      val loyaltySearchType       = Some("BURN")

      val mockLoyaltyRequest = LoyaltyRequest(
        partnerClaimToken = partnerClaimToken,
        selectedOfferIdentifier = selectedOfferIdentifier,
        points = points,
        loyaltySearchType = loyaltySearchType
      )

      val confirmPriceResp = ConfirmPriceResponse(isCompleted = true, retry = Retry(None))
      val jsonContent      = Serialization.jacksonMapper.writeValueAsString(confirmPriceResp)

      val passenger = AncillaryPassengerDetails(1, "ADT")
      val confirmPriceRequest =
        ConfirmPriceRequest(
          itineraryId = "test",
          searchToken = "test",
          passengers = Some(Vector(passenger))
        )

      when(flapiClientProxyV2Mock.postFlightsConfirmPrice(any(), any())(any()))
        .thenAnswer { (invocation: InvocationOnMock) =>
          val confirmPriceRequest: FlapiConfirmPriceRequest = invocation.getArgument(0)
          confirmPriceRequest.paymentInfo.get.externalLoyalty shouldBe Some(
            ExternalLoyaltyRequest(
              partnerClaimToken = partnerClaimToken,
              loyaltySearchType = loyaltySearchType,
              points = points,
              selectedOfferIdentifier = selectedOfferIdentifier
            )
          )
          Future.successful(confirmPriceResp, jsonContent)
        }

      flightsRepository
        .retrieveFlightConfirmationData(
          FlightRequestItem(Some("test"), Some(confirmPriceRequest), None),
          setupBookingRequestContext.bookingFlowType,
          "USD",
          setupBookingRequestContext.whiteLabelInfo,
          setupBookingRequestContext.correlationId,
          setupBookingRequestContext.requestContext.locale,
          setupBookingRequestContext.sessionId,
          userContextOpt,
          setupBookingRequestContext.deviceContext,
          packagingToken = None,
          selectedCreditCardTypeId = None,
          campaignInfo = None,
          enabledFeatures = None,
          loyaltyRequestOpt = Some(mockLoyaltyRequest),
          aabInfo = None
        )(setupBookingContextIndian) map (response => {
        response.isCompleted shouldBe true
        response.token shouldBe jsonContent
      })
    }
  }

  "with brands + requestSource" should {
    "return postFlightConfirmprice successfully with requestSource" in {

      val confirmPriceResp = ConfirmPriceResponse(isCompleted = true, retry = Retry(None))
      val jsonContent      = Serialization.jacksonMapper.writeValueAsString(confirmPriceResp)

      val brands = Seq(
        FlightBrand(
          scope = "ITINERARY",
          scopeRefId = 1,
          brandId = "brandId1"
        )
      )
      val passenger = AncillaryPassengerDetails(1, "ADT")
      val confirmPriceRequest =
        ConfirmPriceRequest(
          itineraryId = "test",
          searchToken = "test",
          brands = Some(brands),
          passengers = Some(Vector(passenger)),
          requestSource = Some("ReattemptPriceChange")
        )

      when(flapiClientProxyV2Mock.postFlightsConfirmPrice(any(), any())(any()))
        .thenAnswer { (invocation: InvocationOnMock) =>
          val confirmPriceRequest: FlapiConfirmPriceRequest = invocation.getArgument(0)
          confirmPriceRequest.brands.isDefined shouldBe true
          confirmPriceRequest.brands.get.size shouldBe brands.size
          confirmPriceRequest.brands.get.zip(brands).map {
            case (actual, expect) =>
              actual.scope shouldBe expect.scope
              actual.scopeRefId shouldBe expect.scopeRefId
              actual.brandId shouldBe expect.brandId
          }
          confirmPriceRequest.requestSource shouldBe Some("ReattemptPriceChange")
          Future.successful(confirmPriceResp, jsonContent)
        }

      flightsRepository
        .retrieveFlightConfirmationData(
          FlightRequestItem(Some("test"), Some(confirmPriceRequest), None),
          setupBookingRequestContext.bookingFlowType,
          "USD",
          setupBookingRequestContext.whiteLabelInfo,
          setupBookingRequestContext.correlationId,
          setupBookingRequestContext.requestContext.locale,
          setupBookingRequestContext.sessionId,
          userContextOpt,
          setupBookingRequestContext.deviceContext,
          packagingToken = None,
          selectedCreditCardTypeId = None,
          campaignInfo = None,
          enabledFeatures = None,
          loyaltyRequestOpt = None,
          aabInfo = None
        )(setupBookingContextIndian) map (response => {
        response.isCompleted shouldBe true
        response.token shouldBe jsonContent
      })
    }
  }

  "mapInstantPriceConfirmRequest" should {

    val instantSearchRequestTrip = InstantSearchRequestTrip(
      2,
      200.45,
      "ABC",
      "Roundtrip",
      Vector(
        InstantSearchRequestSlice(
          4,
          Vector(
            InstantSearchRequestSegment(3, "123", 345, "ECO", "XYZ", LocalDateTime.now().plusDays(1), "ABC", "XYZ")
          )
        )
      )
    )

    val instantPriceConfirmRequest = InstantPriceConfirmRequest(
      Vector(SearchRequestPassengers(1, "ADT")),
      Vector(instantSearchRequestTrip)
    )

    "should map experiment force map even though force by variant is empty" in {
      val forcedExp = Map("test" -> "B")
      val context = userContextOpt.map { user =>
        val experimentData = user.experimentData.map { experiment =>
          experiment.addForce(forcedExp)
        }
        user.copy(experimentData = experimentData)
      }
      val flapiInstantPriceConfirmRequest: FlapiInstantPriceConfirmRequest =
        flightsRepository.mapInstantPriceConfirmRequest(
          instantPriceConfirmRequest = instantPriceConfirmRequest,
          userContext = context,
          deviceContext = Some(DeviceContext(deviceId = Some("test"), deviceTypeId = DevicePlatform.AppIPad)),
          packageRequest = None,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          requestId = "",
          locale = "",
          chargeCurrency = "USD",
          enabledFeatures = None,
          paymentInfo = None,
          loyaltyRequestOpt = None,
          pollingId = None,
          aabInfo = None
        )
      flapiInstantPriceConfirmRequest.context.experiments.nonEmpty shouldBe true
      val firstExperiment =
        flapiInstantPriceConfirmRequest.context.experiments.flatMap(_.forceByExperiment).flatMap(_.headOption)
      firstExperiment.map(_.id) shouldBe Some("test")
      firstExperiment.map(_.variant) shouldBe Some("B")
      flapiInstantPriceConfirmRequest.context.experiments.flatMap(_.forceByVariant) shouldBe None
    }

    "should map forceByVariant even though force experiment map is empty" in {
      val variant = "B"
      val context = userContextOpt.map { user =>
        val experimentData = user.experimentData.map { experiment =>
          experiment.copy(forceByVariant = Some(variant))
        }
        user.copy(experimentData = experimentData)
      }
      val flapiInstantPriceConfirmRequest: FlapiInstantPriceConfirmRequest =
        flightsRepository.mapInstantPriceConfirmRequest(
          instantPriceConfirmRequest = instantPriceConfirmRequest,
          userContext = context,
          deviceContext = Some(DeviceContext(deviceId = Some("test"), deviceTypeId = DevicePlatform.AppIPad)),
          packageRequest = None,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          requestId = "",
          locale = "",
          chargeCurrency = "USD",
          enabledFeatures = None,
          paymentInfo = None,
          loyaltyRequestOpt = None,
          pollingId = None,
          aabInfo = None
        )
      flapiInstantPriceConfirmRequest.context.experiments.nonEmpty shouldBe true
      flapiInstantPriceConfirmRequest.context.experiments.flatMap(_.forceByExperiment) shouldBe None
      flapiInstantPriceConfirmRequest.context.experiments.flatMap(_.forceByVariant) shouldBe Some(variant)
    }

    "should validate the enableFeature mapping" in {

      val flapiInstantPriceConfirmRequest: FlapiInstantPriceConfirmRequest =
        flightsRepository.mapInstantPriceConfirmRequest(
          instantPriceConfirmRequest = instantPriceConfirmRequest,
          userContext = userContextOpt,
          deviceContext = Some(DeviceContext(deviceId = Some("test"), deviceTypeId = DevicePlatform.AppIPad)),
          packageRequest = None,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          requestId = "",
          locale = "",
          chargeCurrency = "USD",
          enabledFeatures = Some(Seq("QuickSort")),
          paymentInfo = None,
          loyaltyRequestOpt = None,
          pollingId = None,
          aabInfo = None
        )
      flapiInstantPriceConfirmRequest.enabledFeatures shouldBe Some(Seq("QuickSort"))
    }

    "should correctly map paymentInfo with addConvenienceFee flag when not present" in {

      val flapiInstantPriceConfirmRequest: FlapiInstantPriceConfirmRequest =
        flightsRepository.mapInstantPriceConfirmRequest(
          instantPriceConfirmRequest = instantPriceConfirmRequest,
          userContext = userContextOpt,
          deviceContext = Some(DeviceContext(deviceId = Some("test"), deviceTypeId = DevicePlatform.AppIPad)),
          packageRequest = None,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          requestId = "",
          locale = "",
          chargeCurrency = "USD",
          enabledFeatures = None,
          paymentInfo = None,
          loyaltyRequestOpt = None,
          pollingId = None,
          aabInfo = None
        )

      val expectedPaymentInfo = PaymentInfo(
        addConvenienceFee = true,
        addFacilitationFee = Some(true)
      )
      flapiInstantPriceConfirmRequest.paymentInfo shouldBe Some(expectedPaymentInfo)
    }

    "should correctly map paymentInfo with addConvenienceFee flag when present" in {

      val flapiInstantPriceConfirmRequest: FlapiInstantPriceConfirmRequest =
        flightsRepository.mapInstantPriceConfirmRequest(
          instantPriceConfirmRequest = instantPriceConfirmRequest,
          userContext = userContextOpt,
          deviceContext = Some(DeviceContext(deviceId = Some("test"), deviceTypeId = DevicePlatform.AppIPad)),
          packageRequest = None,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          requestId = "",
          locale = "",
          chargeCurrency = "USD",
          enabledFeatures = None,
          paymentInfo = Some(FlightsPaymentInfo()),
          loyaltyRequestOpt = None,
          pollingId = None,
          aabInfo = None
        )

      val expectedPaymentInfo = PaymentInfo(
        addConvenienceFee = true,
        addFacilitationFee = Some(true)
      )
      flapiInstantPriceConfirmRequest.paymentInfo shouldBe Some(expectedPaymentInfo)
    }

    "should correctly map Some displayedCurrency from InstantSearchRequestTrip when present" in {

      val testDisplayedCurrency = "THB"
      val testInstantSearchRequestTrip =
        instantSearchRequestTrip.copy(displayedCurrency = Some(testDisplayedCurrency))
      val testInstantPriceConfirmRequest = instantPriceConfirmRequest.copy(
        trips = Vector(testInstantSearchRequestTrip)
      )

      val flapiInstantPriceConfirmRequest: FlapiInstantPriceConfirmRequest =
        flightsRepository.mapInstantPriceConfirmRequest(
          instantPriceConfirmRequest = testInstantPriceConfirmRequest,
          userContext = userContextOpt,
          deviceContext = Some(DeviceContext(deviceId = Some("test"), deviceTypeId = DevicePlatform.AppIPad)),
          packageRequest = None,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          requestId = "",
          locale = "",
          chargeCurrency = "USD",
          enabledFeatures = Some(Seq("QuickSort")),
          paymentInfo = None,
          loyaltyRequestOpt = None,
          pollingId = None,
          aabInfo = None
        )
      flapiInstantPriceConfirmRequest.trips.flatMap(_.displayedCurrency) shouldBe Seq(testDisplayedCurrency)
    }

    "should correctly map None as displayedCurrency from InstantSearchRequestTrip when not present" in {
      val testDisplayedCurrency = None
      val testInstantSearchRequestTrip =
        instantSearchRequestTrip.copy(displayedCurrency = testDisplayedCurrency)
      val testInstantPriceConfirmRequest = instantPriceConfirmRequest.copy(
        trips = Vector(testInstantSearchRequestTrip)
      )

      val flapiInstantPriceConfirmRequest: FlapiInstantPriceConfirmRequest =
        flightsRepository.mapInstantPriceConfirmRequest(
          instantPriceConfirmRequest = testInstantPriceConfirmRequest,
          userContext = userContextOpt,
          deviceContext = Some(DeviceContext(deviceId = Some("test"), deviceTypeId = DevicePlatform.AppIPad)),
          packageRequest = None,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          requestId = "",
          locale = "",
          chargeCurrency = "USD",
          enabledFeatures = Some(Seq("QuickSort")),
          paymentInfo = None,
          loyaltyRequestOpt = None,
          pollingId = None,
          aabInfo = None
        )
      flapiInstantPriceConfirmRequest.trips.flatMap(_.displayedCurrency) shouldBe Seq.empty
    }

    "should correctly map paymentInfo with loyalty request" in {

      val flapiInstantPriceConfirmRequest: FlapiInstantPriceConfirmRequest =
        flightsRepository.mapInstantPriceConfirmRequest(
          instantPriceConfirmRequest = instantPriceConfirmRequest,
          userContext = userContextOpt,
          deviceContext = Some(DeviceContext(deviceId = Some("test"), deviceTypeId = DevicePlatform.AppIPad)),
          packageRequest = None,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          requestId = "",
          locale = "",
          chargeCurrency = "USD",
          enabledFeatures = None,
          paymentInfo = Some(FlightsPaymentInfo()),
          loyaltyRequestOpt = Some(mockLoyaltyRequest),
          pollingId = None,
          aabInfo = None
        )

      val expectedPaymentInfo = PaymentInfo(
        addConvenienceFee = true,
        externalLoyalty = Some(mockExternalLoyaltyRequest),
        addFacilitationFee = Some(true)
      )
      flapiInstantPriceConfirmRequest.paymentInfo shouldBe Some(expectedPaymentInfo)
    }

    "should correctly map externalTrackingId when externalTrackingId is not empty" in {
      val forcedExp = Map("test" -> "B")
      val context = userContextOpt.map { user =>
        val experimentData = user.experimentData.map { experiment =>
          experiment.addForce(forcedExp)
        }
        user.copy(experimentData = experimentData)
      }
      val request = instantPriceConfirmRequest.copy(externalTrackingId = Option("nut_123"))
      val flapiInstantPriceConfirmRequest: FlapiInstantPriceConfirmRequest =
        flightsRepository.mapInstantPriceConfirmRequest(
          instantPriceConfirmRequest = request,
          userContext = context,
          deviceContext = Some(DeviceContext(deviceId = Some("test"), deviceTypeId = DevicePlatform.AppIPad)),
          packageRequest = None,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          requestId = "",
          locale = "",
          chargeCurrency = "USD",
          enabledFeatures = None,
          paymentInfo = None,
          loyaltyRequestOpt = None,
          pollingId = None,
          aabInfo = None
        )
      flapiInstantPriceConfirmRequest.externalTrackingId shouldBe Option("nut_123")
    }

    "should correctly map externalTrackingId when externalTrackingId is empty" in {
      val forcedExp = Map("test" -> "B")
      val context = userContextOpt.map { user =>
        val experimentData = user.experimentData.map { experiment =>
          experiment.addForce(forcedExp)
        }
        user.copy(experimentData = experimentData)
      }
      val request = instantPriceConfirmRequest.copy(externalTrackingId = None)
      val flapiInstantPriceConfirmRequest: FlapiInstantPriceConfirmRequest =
        flightsRepository.mapInstantPriceConfirmRequest(
          instantPriceConfirmRequest = request,
          userContext = context,
          deviceContext = Some(DeviceContext(deviceId = Some("test"), deviceTypeId = DevicePlatform.AppIPad)),
          packageRequest = None,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          requestId = "",
          locale = "",
          chargeCurrency = "USD",
          enabledFeatures = None,
          paymentInfo = None,
          loyaltyRequestOpt = None,
          pollingId = None,
          aabInfo = None
        )
      flapiInstantPriceConfirmRequest.externalTrackingId shouldBe None
    }

    "should correctly map paymentInfo with addFacilitationFee when flightFacilitationFeeWaiverReasonId is present" in {
      val mockAabInfo = AabInfo(flight = Some(FlightAabInfo(facilitationFeeWaiverReasonId = Some(1))))

      val flapiInstantPriceConfirmRequest: FlapiInstantPriceConfirmRequest =
        flightsRepository.mapInstantPriceConfirmRequest(
          instantPriceConfirmRequest = instantPriceConfirmRequest,
          userContext = userContextOpt,
          deviceContext = Some(DeviceContext(deviceId = Some("test"), deviceTypeId = DevicePlatform.AppIPad)),
          packageRequest = None,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          requestId = "",
          locale = "",
          chargeCurrency = "USD",
          enabledFeatures = None,
          paymentInfo = Some(FlightsPaymentInfo()),
          loyaltyRequestOpt = None,
          pollingId = None,
          aabInfo = Some(mockAabInfo)
        )

      val expectedPaymentInfo = PaymentInfo(
        addConvenienceFee = true,
        addFacilitationFee = Some(false)
      )
      flapiInstantPriceConfirmRequest.paymentInfo shouldBe Some(expectedPaymentInfo)
    }

    "should correctly map paymentInfo with addFacilitationFee when flight is empty" in {
      val mockAabInfo = AabInfo(flight = None)

      val flapiInstantPriceConfirmRequest: FlapiInstantPriceConfirmRequest =
        flightsRepository.mapInstantPriceConfirmRequest(
          instantPriceConfirmRequest = instantPriceConfirmRequest,
          userContext = userContextOpt,
          deviceContext = Some(DeviceContext(deviceId = Some("test"), deviceTypeId = DevicePlatform.AppIPad)),
          packageRequest = None,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          requestId = "",
          locale = "",
          chargeCurrency = "USD",
          enabledFeatures = None,
          paymentInfo = Some(FlightsPaymentInfo()),
          loyaltyRequestOpt = None,
          pollingId = None,
          aabInfo = Some(mockAabInfo)
        )

      val expectedPaymentInfo = PaymentInfo(
        addConvenienceFee = true,
        addFacilitationFee = Some(true)
      )
      flapiInstantPriceConfirmRequest.paymentInfo shouldBe Some(expectedPaymentInfo)
    }

    "should correctly map paymentInfo with addFacilitationFee when aabInfo is empty" in {
      val mockAabInfo = None

      val flapiInstantPriceConfirmRequest: FlapiInstantPriceConfirmRequest =
        flightsRepository.mapInstantPriceConfirmRequest(
          instantPriceConfirmRequest = instantPriceConfirmRequest,
          userContext = userContextOpt,
          deviceContext = Some(DeviceContext(deviceId = Some("test"), deviceTypeId = DevicePlatform.AppIPad)),
          packageRequest = None,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          requestId = "",
          locale = "",
          chargeCurrency = "USD",
          enabledFeatures = None,
          paymentInfo = Some(FlightsPaymentInfo()),
          loyaltyRequestOpt = None,
          pollingId = None,
          aabInfo = mockAabInfo
        )

      val expectedPaymentInfo = PaymentInfo(
        addConvenienceFee = true,
        addFacilitationFee = Some(true)
      )
      flapiInstantPriceConfirmRequest.paymentInfo shouldBe Some(expectedPaymentInfo)
    }
  }

  "mapConfirmPriceRequest" should {

    val mockLocale                   = "en-us"
    val mockRequestId                = "mock-request-id"
    val mockChargeCurrency           = "USD"
    val mockSelectedCreditCardTypeId = 1
    val mockConfirmPriceRequest = ConfirmPriceRequest(
      "dummy-itinerary-id",
      "dummy-itinerary-token"
    )
    val mockDeviceContext  = DeviceContext(deviceId = Some("test"), deviceTypeId = DevicePlatform.AppIPad)
    val mockWhiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration())

    val mockCid = Some("9999")
    val dummyUserContext = userContext.copy(
      experimentData = userContext.experimentData.map(_.copy(cId = mockCid))
    )

    "should correctly map paymentInfo when addConvenience fee is absent" in {

      val result = flightsRepository.mapConfirmPriceRequest(
        confirmPriceRequest = mockConfirmPriceRequest,
        userContext = Some(dummyUserContext),
        deviceContext = Some(mockDeviceContext),
        packageRequest = None,
        whiteLabelInfo = mockWhiteLabelInfo,
        requestId = mockRequestId,
        locale = mockLocale,
        chargeCurrency = mockChargeCurrency,
        selectedCreditCardTypeId = Some(mockSelectedCreditCardTypeId),
        campaignInfo = Some(MockFlightsPricingData.campaignInfoRequest),
        paymentInfo = None,
        campaignsIds = Some(MockFlightsPricingData.campaignIds),
        loyaltyRequestOpt = None,
        selectedPaymentMethodId = None,
        cartRequest = None,
        pollingId = None,
        aabInfo = None
      )

      val expectedPromotionInfoRequest = PromotionInfoRequest(
        id = MockFlightsPricingData.campaignInfoRequest.id,
        cid = mockCid.get.toInt,
        promotionCode = MockFlightsPricingData.campaignInfoRequest.promotionCode,
        campaignIds = Some(MockFlightsPricingData.campaignIds)
      )
      val expectedPaymentInfo = PaymentInfo(
        creditCardTypeId = Some(mockSelectedCreditCardTypeId),
        promotionInfo = Some(expectedPromotionInfoRequest),
        addConvenienceFee = true,
        addFacilitationFee = Some(true)
      )

      result.itineraryId shouldBe mockConfirmPriceRequest.itineraryId
      result.paymentInfo shouldBe Some(expectedPaymentInfo)
    }

    "should correctly map paymentInfo when addConvenience fee is present" in {

      val mockFlightsPaymentInfo = FlightsPaymentInfo()

      val result = flightsRepository.mapConfirmPriceRequest(
        confirmPriceRequest = mockConfirmPriceRequest,
        userContext = Some(dummyUserContext),
        deviceContext = Some(mockDeviceContext),
        packageRequest = None,
        whiteLabelInfo = mockWhiteLabelInfo,
        requestId = mockRequestId,
        locale = mockLocale,
        chargeCurrency = mockChargeCurrency,
        selectedCreditCardTypeId = Some(mockSelectedCreditCardTypeId),
        campaignInfo = Some(MockFlightsPricingData.campaignInfoRequest),
        paymentInfo = Some(mockFlightsPaymentInfo),
        campaignsIds = Some(MockFlightsPricingData.campaignIds),
        loyaltyRequestOpt = None,
        selectedPaymentMethodId = None,
        cartRequest = None,
        pollingId = None,
        aabInfo = None
      )

      val expectedPromotionInfoRequest = PromotionInfoRequest(
        id = MockFlightsPricingData.campaignInfoRequest.id,
        cid = mockCid.get.toInt,
        promotionCode = MockFlightsPricingData.campaignInfoRequest.promotionCode,
        campaignIds = Some(MockFlightsPricingData.campaignIds)
      )
      val expectedPaymentInfo = PaymentInfo(
        creditCardTypeId = Some(mockSelectedCreditCardTypeId),
        promotionInfo = Some(expectedPromotionInfoRequest),
        addConvenienceFee = true,
        addFacilitationFee = Some(true)
      )

      result.itineraryId shouldBe mockConfirmPriceRequest.itineraryId
      result.paymentInfo shouldBe Some(expectedPaymentInfo)
    }

    "should correctly map promotionInfo when promotionRequest is absent but CC Campaigns are present" in {

      val mockFlightsPaymentInfo = FlightsPaymentInfo()

      val result = flightsRepository.mapConfirmPriceRequest(
        confirmPriceRequest = mockConfirmPriceRequest,
        userContext = Some(dummyUserContext),
        deviceContext = Some(mockDeviceContext),
        packageRequest = None,
        whiteLabelInfo = mockWhiteLabelInfo,
        requestId = mockRequestId,
        locale = mockLocale,
        chargeCurrency = mockChargeCurrency,
        selectedCreditCardTypeId = Some(mockSelectedCreditCardTypeId),
        campaignInfo = None,
        paymentInfo = Some(mockFlightsPaymentInfo),
        campaignsIds = Some(MockFlightsPricingData.campaignIds),
        loyaltyRequestOpt = None,
        selectedPaymentMethodId = None,
        cartRequest = None,
        pollingId = None,
        aabInfo = None
      )

      val expectedPromotionInfoRequest = PromotionInfoRequest(
        id = None,
        cid = mockCid.get.toInt,
        promotionCode = "",
        campaignIds = Some(MockFlightsPricingData.campaignIds)
      )
      val expectedPaymentInfo = PaymentInfo(
        creditCardTypeId = Some(mockSelectedCreditCardTypeId),
        promotionInfo = Some(expectedPromotionInfoRequest),
        addConvenienceFee = true,
        addFacilitationFee = Some(true)
      )

      result.itineraryId shouldBe mockConfirmPriceRequest.itineraryId
      result.paymentInfo shouldBe Some(expectedPaymentInfo)
    }

    "should correctly map promotionInfo when loyalty request is present" in {

      val result = flightsRepository.mapConfirmPriceRequest(
        confirmPriceRequest = mockConfirmPriceRequest,
        userContext = Some(dummyUserContext),
        deviceContext = Some(mockDeviceContext),
        packageRequest = None,
        whiteLabelInfo = mockWhiteLabelInfo,
        requestId = mockRequestId,
        locale = mockLocale,
        chargeCurrency = mockChargeCurrency,
        selectedCreditCardTypeId = Some(mockSelectedCreditCardTypeId),
        campaignInfo = None,
        paymentInfo = None,
        campaignsIds = Some(MockFlightsPricingData.campaignIds),
        loyaltyRequestOpt = Some(mockLoyaltyRequest),
        selectedPaymentMethodId = None,
        cartRequest = None,
        pollingId = None,
        aabInfo = None
      )

      val expectedPromotionInfoRequest = PromotionInfoRequest(
        id = None,
        cid = mockCid.get.toInt,
        promotionCode = "",
        campaignIds = Some(MockFlightsPricingData.campaignIds)
      )

      val expectedPaymentInfo = PaymentInfo(
        creditCardTypeId = Some(mockSelectedCreditCardTypeId),
        promotionInfo = Some(expectedPromotionInfoRequest),
        addConvenienceFee = true,
        externalLoyalty = Some(mockExternalLoyaltyRequest),
        addFacilitationFee = Some(true)
      )

      result.itineraryId shouldBe mockConfirmPriceRequest.itineraryId
      result.paymentInfo shouldBe Some(expectedPaymentInfo)
    }

    "should correctly map BundleContext when CartRequest is present" in {
      val result = flightsRepository.mapConfirmPriceRequest(
        confirmPriceRequest = mockConfirmPriceRequest,
        userContext = Some(dummyUserContext),
        deviceContext = Some(mockDeviceContext),
        packageRequest = None,
        whiteLabelInfo = mockWhiteLabelInfo,
        requestId = mockRequestId,
        locale = mockLocale,
        chargeCurrency = mockChargeCurrency,
        selectedCreditCardTypeId = Some(mockSelectedCreditCardTypeId),
        campaignInfo = None,
        paymentInfo = None,
        campaignsIds = None,
        loyaltyRequestOpt = None,
        cartRequest = Some(cartBaseRequest),
        aabInfo = None
      )

      result.bundleContext shouldBe Some(BundleContext(Some(expectedFlapiCartBaseRequest)))
    }

    "should correctly map pollingId when pollingId is present" in {
      val mockPollingId = Some("mockPollingId")

      val result = flightsRepository.mapConfirmPriceRequest(
        confirmPriceRequest = mockConfirmPriceRequest,
        userContext = None,
        deviceContext = None,
        packageRequest = None,
        whiteLabelInfo = mockWhiteLabelInfo,
        requestId = mockRequestId,
        locale = mockLocale,
        chargeCurrency = mockChargeCurrency,
        selectedCreditCardTypeId = None,
        campaignInfo = None,
        paymentInfo = None,
        campaignsIds = None,
        loyaltyRequestOpt = None,
        pollingId = mockPollingId,
        aabInfo = None
      )

      result.context.pollingId shouldBe mockPollingId
    }

    "should correctly map aabInfo when flightFacilitationFeeWaiverReasonId is present" in {
      val mockAabInfo = AabInfo(flight = Some(FlightAabInfo(facilitationFeeWaiverReasonId = Some(1))))

      val result = flightsRepository.mapConfirmPriceRequest(
        confirmPriceRequest = mockConfirmPriceRequest,
        userContext = None,
        deviceContext = None,
        packageRequest = None,
        whiteLabelInfo = mockWhiteLabelInfo,
        requestId = mockRequestId,
        locale = mockLocale,
        chargeCurrency = mockChargeCurrency,
        selectedCreditCardTypeId = None,
        campaignInfo = None,
        paymentInfo = None,
        campaignsIds = None,
        loyaltyRequestOpt = None,
        pollingId = None,
        aabInfo = Some(mockAabInfo)
      )

      result.paymentInfo.flatMap(_.addFacilitationFee) shouldBe Some(false)
    }

    "should correctly map aabInfo when flight is empty" in {
      val mockAabInfo = AabInfo(flight = None)

      val result = flightsRepository.mapConfirmPriceRequest(
        confirmPriceRequest = mockConfirmPriceRequest,
        userContext = None,
        deviceContext = None,
        packageRequest = None,
        whiteLabelInfo = mockWhiteLabelInfo,
        requestId = mockRequestId,
        locale = mockLocale,
        chargeCurrency = mockChargeCurrency,
        selectedCreditCardTypeId = None,
        campaignInfo = None,
        paymentInfo = None,
        campaignsIds = None,
        loyaltyRequestOpt = None,
        pollingId = None,
        aabInfo = Some(mockAabInfo)
      )

      result.paymentInfo.flatMap(_.addFacilitationFee) shouldBe Some(true)
    }

    "should correctly map aabInfo when aabInfo is empty" in {
      val mockAabInfo = None

      val result = flightsRepository.mapConfirmPriceRequest(
        confirmPriceRequest = mockConfirmPriceRequest,
        userContext = None,
        deviceContext = None,
        packageRequest = None,
        whiteLabelInfo = mockWhiteLabelInfo,
        requestId = mockRequestId,
        locale = mockLocale,
        chargeCurrency = mockChargeCurrency,
        selectedCreditCardTypeId = None,
        campaignInfo = None,
        paymentInfo = None,
        campaignsIds = None,
        loyaltyRequestOpt = None,
        pollingId = None,
        aabInfo = mockAabInfo
      )

      result.paymentInfo.flatMap(_.addFacilitationFee) shouldBe Some(true)
    }

    "should correctly map correlationId and bookingSessionId when present in context for mapConfirmPriceRequest" in {
      val mockCorrelationId    = "test-correlation-id"
      val mockBookingSessionId = "test-booking-session-id"

      // Create a mock context with the test values
      val mockContext = mock[SetupBookingContext]
      when(mockContext.correlationId).thenReturn(mockCorrelationId)
      when(mockContext.bookingSessionId).thenReturn(mockBookingSessionId)

      val result = flightsRepository.mapConfirmPriceRequest(
        confirmPriceRequest = mockConfirmPriceRequest,
        userContext = None,
        deviceContext = None,
        packageRequest = None,
        whiteLabelInfo = mockWhiteLabelInfo,
        requestId = mockRequestId,
        locale = mockLocale,
        chargeCurrency = mockChargeCurrency,
        selectedCreditCardTypeId = None,
        campaignInfo = None,
        paymentInfo = None,
        campaignsIds = None,
        loyaltyRequestOpt = None,
        pollingId = None,
        aabInfo = None
      )(mockContext)

      result.context.correlationId shouldBe Some(mockCorrelationId)
      result.context.bookingSessionId shouldBe Some(mockBookingSessionId)
    }
  }

  "mapInstantPriceConfirmRequest" should {

    val mockLocale         = "en-us"
    val mockRequestId      = "mock-request-id"
    val mockChargeCurrency = "USD"
    val mockWhiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration())

    val instantPriceConfirmRequest = InstantPriceConfirmRequest(
      Vector(SearchRequestPassengers(1, "ADT")),
      Vector(
        InstantSearchRequestTrip(
          2,
          200.45,
          "ABC",
          "Roundtrip",
          Vector(
            InstantSearchRequestSlice(
              4,
              Vector(
                InstantSearchRequestSegment(
                  3,
                  "123",
                  345,
                  "ECO",
                  "XYZ",
                  LocalDateTime.now().plusDays(1),
                  "ABC",
                  "XYZ"
                )
              )
            )
          )
        )
      ),
      externalTrackingId = None
    )

    "should correctly map correlationId and bookingSessionId when present in context for mapInstantPriceConfirmRequest" in {
      val mockCorrelationId    = "test-correlation-id"
      val mockBookingSessionId = "test-booking-session-id"

      // Create a mock context with the test values
      val mockContext = mock[SetupBookingContext]
      when(mockContext.correlationId).thenReturn(mockCorrelationId)
      when(mockContext.bookingSessionId).thenReturn(mockBookingSessionId)

      val result = flightsRepository.mapInstantPriceConfirmRequest(
        instantPriceConfirmRequest = instantPriceConfirmRequest,
        userContext = None,
        deviceContext = None,
        packageRequest = None,
        whiteLabelInfo = mockWhiteLabelInfo,
        requestId = mockRequestId,
        locale = mockLocale,
        chargeCurrency = mockChargeCurrency,
        enabledFeatures = None,
        paymentInfo = None,
        loyaltyRequestOpt = None,
        pollingId = None,
        aabInfo = None
      )(mockContext)

      result.context.correlationId shouldBe Some(mockCorrelationId)
      result.context.bookingSessionId shouldBe Some(mockBookingSessionId)
    }
  }
}
