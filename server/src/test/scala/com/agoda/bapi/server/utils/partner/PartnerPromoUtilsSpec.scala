package com.agoda.bapi.server.utils.partner

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.model.{WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.service.FeatureAware
import org.mockito.Mockito.when
import org.scalatest.BeforeAndAfterEach
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatestplus.mockito.MockitoSugar

class PartnerPromoUtilsSpec extends AnyWordSpec with MockitoSugar with Matchers with BeforeAndAfterEach {
  implicit var requestContext: RequestContext = _
  var featureFlag: FeatureAware               = _
  var whiteLabelInfo: WhiteLabelInfo          = _

  override def beforeEach(): Unit = {
    requestContext = mock[RequestContext]
    featureFlag = mock[FeatureAware]
    whiteLabelInfo = mock[WhiteLabelInfo]
  }

  "apply" should {
    "return DefaultPartnerPromoUtils for WestJet Whitelabel when CustomPartnerPromoUtils is disabled" in {
      when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.WestJet)
      when(requestContext.featureAware).thenReturn(Some(featureFlag))
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.CustomPartnerPromoUtils)).thenReturn(false)
      PartnerPromoUtils(whiteLabelInfo) shouldBe DefaultPartnerPromoUtils
    }

    "return DefaultPartnerPromoUtils for WestJetUat1 Whitelabel when CustomPartnerPromoUtils is disabled" in {
      when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.WestJetUat1)
      when(requestContext.featureAware).thenReturn(Some(featureFlag))
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.CustomPartnerPromoUtils)).thenReturn(false)
      PartnerPromoUtils(whiteLabelInfo) shouldBe DefaultPartnerPromoUtils
    }

    "return PropertiesPromoUtils for CitiUS Whitelabel when CustomPartnerPromoUtils is enabled" in {
      when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.CitiUS)
      when(requestContext.featureAware).thenReturn(Some(featureFlag))
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.CustomPartnerPromoUtils)).thenReturn(true)
      PartnerPromoUtils(whiteLabelInfo) shouldBe PropertiesPromoUtils
    }

    "return PropertiesPromoUtils for CitiUSUat1 Whitelabel when CustomPartnerPromoUtils is enabled" in {
      when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.CitiUSUat1)
      when(requestContext.featureAware).thenReturn(Some(featureFlag))
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.CustomPartnerPromoUtils)).thenReturn(true)
      PartnerPromoUtils(whiteLabelInfo) shouldBe PropertiesPromoUtils
    }

    "return PropertiesPromoUtils for CitiUSUat2 Whitelabel when CustomPartnerPromoUtils is enabled" in {
      when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.CitiUSUat2)
      when(requestContext.featureAware).thenReturn(Some(featureFlag))
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.CustomPartnerPromoUtils)).thenReturn(true)
      PartnerPromoUtils(whiteLabelInfo) shouldBe PropertiesPromoUtils
    }

    "return PropertiesPromoUtils for CitiUSUat3 Whitelabel when CustomPartnerPromoUtils is enabled" in {
      when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.CitiUSUat3)
      when(requestContext.featureAware).thenReturn(Some(featureFlag))
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.CustomPartnerPromoUtils)).thenReturn(true)
      PartnerPromoUtils(whiteLabelInfo) shouldBe PropertiesPromoUtils
    }

    "return PropertiesPromoUtils for WestJet Whitelabel when CustomPartnerPromoUtils is enabled" in {
      when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.WestJet)
      when(requestContext.featureAware).thenReturn(Some(featureFlag))
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.CustomPartnerPromoUtils)).thenReturn(true)
      PartnerPromoUtils(whiteLabelInfo) shouldBe PropertiesPromoUtils
    }

    "return PropertiesPromoUtils for WestJetUat1 Whitelabel when CustomPartnerPromoUtils is enabled" in {
      when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.WestJetUat1)
      when(requestContext.featureAware).thenReturn(Some(featureFlag))
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.CustomPartnerPromoUtils)).thenReturn(true)
      PartnerPromoUtils(whiteLabelInfo) shouldBe PropertiesPromoUtils
    }

    "return AgodaPromoUtils for Agoda Whitelabel" in {
      when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
      when(requestContext.featureAware).thenReturn(Some(featureFlag))
      PartnerPromoUtils(whiteLabelInfo) shouldBe AgodaPromoUtils
    }

    "return DefaultPartnerPromoUtils for Other Whitelabels" in {
      when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Rurubu)
      when(requestContext.featureAware).thenReturn(Some(featureFlag))
      PartnerPromoUtils(whiteLabelInfo) shouldBe DefaultPartnerPromoUtils
    }
  }
}
