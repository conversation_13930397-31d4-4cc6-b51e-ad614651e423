package com.agoda.bapi.server.utils

import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.mpb.common.MultiProductType
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

class CommonUtilsSpec extends AnyWordSpec with Matchers {

  private val defaultMasterBookingAction = BookingWorkflowAction(
    actionId = 123,
    itineraryId = 1234,
    bookingType = None,
    bookingId = None,
    memberId = 0,
    actionTypeId = 0,
    correlationId = "",
    requestId = "",
    workflowId = 0,
    workflowStateId = 0,
    productTypeId = None,
    stateSchemaVersion = 0,
    state = "",
    storefrontId = None,
    languageId = None
  )

  private val defaultPropertyBookingAction = BookingWorkflowAction(
    actionId = 123,
    itineraryId = 1234,
    bookingType = None,
    bookingId = Some(11111),
    memberId = 0,
    actionTypeId = 0,
    correlationId = "",
    requestId = "",
    workflowId = 0,
    workflowStateId = 0,
    productTypeId = Some(MultiProductType.SingleProperty.id),
    stateSchemaVersion = 0,
    state = "",
    storefrontId = None,
    languageId = None
  )

  private val defaultFlightBookingAction = BookingWorkflowAction(
    actionId = 123,
    itineraryId = 1234,
    bookingType = None,
    bookingId = Some(22222),
    memberId = 0,
    actionTypeId = 0,
    correlationId = "",
    requestId = "",
    workflowId = 0,
    workflowStateId = 0,
    productTypeId = Some(MultiProductType.SingleFlight.id),
    stateSchemaVersion = 0,
    state = "",
    storefrontId = None,
    languageId = None
  )

  "getBookingActionsForOperation" should {
    "return booking actions from booking action sequence if they are from the same operation" in {
      val bookingActions = Seq(
        defaultMasterBookingAction.copy(operationId = Some(1)),
        defaultPropertyBookingAction.copy(operationId = Some(1)),
        defaultFlightBookingAction.copy(operationId = Some(0))
      )
      val actual = CommonUtils.getBookingActionsForOperation(bookingActions, 1)

      actual shouldBe Seq(
        defaultMasterBookingAction.copy(operationId = Some(1)),
        defaultPropertyBookingAction.copy(operationId = Some(1))
      )
    }

    "return no booking actions from booking action sequence if there are none from the same operation" in {
      val bookingActions = Seq(
        defaultPropertyBookingAction.copy(operationId = Some(1)),
        defaultFlightBookingAction.copy(operationId = Some(1)),
        defaultMasterBookingAction.copy(operationId = Some(1))
      )
      val actual = CommonUtils.getBookingActionsForOperation(bookingActions, 2)

      actual shouldBe Seq.empty
    }
    "filter out booking actions with no operationId" in {
      val bookingActions = Seq(
        defaultPropertyBookingAction.copy(operationId = Some(1)),
        defaultFlightBookingAction.copy(operationId = None),
        defaultMasterBookingAction.copy(operationId = None)
      )
      val actual = CommonUtils.getBookingActionsForOperation(bookingActions, 1)

      actual shouldBe Seq(defaultPropertyBookingAction.copy(operationId = Some(1)))
    }
  }

  "getMasterBookingAction" should {
    "return master booking action from booking action sequence" in {
      val bookingActions = Seq(defaultMasterBookingAction, defaultPropertyBookingAction, defaultFlightBookingAction)
      val actual         = CommonUtils.getMasterBookingAction(bookingActions)

      actual shouldBe Some(defaultMasterBookingAction)
    }

    "return correct master booking action from booking action sequence" in {
      val bookingActions = Seq(defaultPropertyBookingAction, defaultFlightBookingAction, defaultMasterBookingAction)
      val actual         = CommonUtils.getMasterBookingAction(bookingActions)

      actual shouldBe Some(defaultMasterBookingAction)
    }

    "return none when master booking action doesn't exists" in {
      val bookingActions = Seq(defaultPropertyBookingAction, defaultFlightBookingAction)
      val actual         = CommonUtils.getMasterBookingAction(bookingActions)

      actual shouldBe None
    }

  }

  "getPropertyBookingActions" should {
    "return 1 property booking action" in {
      val bookingActions = Seq(defaultMasterBookingAction, defaultPropertyBookingAction, defaultFlightBookingAction)
      val actual         = CommonUtils.getPropertyBookingActions(bookingActions)

      actual shouldBe Seq(defaultPropertyBookingAction)
    }

    "return 2 property booking action" in {
      val bookingActions = Seq(defaultMasterBookingAction, defaultPropertyBookingAction, defaultPropertyBookingAction)
      val actual         = CommonUtils.getPropertyBookingActions(bookingActions)

      actual shouldBe Seq(defaultPropertyBookingAction, defaultPropertyBookingAction)
    }

    "return Seq.empty when property booking action doesn't exists" in {
      val bookingActions = Seq(defaultMasterBookingAction, defaultFlightBookingAction)
      val actual         = CommonUtils.getPropertyBookingActions(bookingActions)

      actual shouldBe Seq.empty
    }
  }

  "getBookingSessionIdFromBookingAction" should {
    "return bookingSessionId from booking action state" in {
      val bookingActionState       = s"""{"request":{"bookingSessionId": "bookingSession"}}"""
      val masterBookingAction      = defaultMasterBookingAction.copy(state = bookingActionState)
      val masterBookingActionState = CommonUtils.getBookingActionState(masterBookingAction)
      val actual = CommonUtils.getBookingSessionId(
        masterBookingActionState = masterBookingActionState,
        fallbackBookingSessionId = None
      )

      actual shouldBe Some("bookingSession")
    }

    "return bookingSessionId from fallbackBookingSessionId when bookingSessionId in booking action state is null" in {
      val bookingActionState       = s"""{"request":{"bookingSessionId": null}}"""
      val fallbackBookingSessionId = "fallbackBookingSessionId"
      val masterBookingAction      = defaultMasterBookingAction.copy(state = bookingActionState)
      val masterBookingActionState = CommonUtils.getBookingActionState(masterBookingAction)
      val actual = CommonUtils.getBookingSessionId(
        masterBookingActionState = masterBookingActionState,
        fallbackBookingSessionId = Some(fallbackBookingSessionId)
      )

      actual shouldBe Some("fallbackBookingSessionId")
    }

    "return bookingSessionId from fallbackBookingSessionId when booking action state can't deserialize" in {
      val bookingActionState       = s"""{"request":{incorrectWorkflowState}}"""
      val fallbackBookingSessionId = "fallbackBookingSessionId"
      val masterBookingAction      = defaultMasterBookingAction.copy(state = bookingActionState)
      val masterBookingActionState = CommonUtils.getBookingActionState(masterBookingAction)
      val actual = CommonUtils.getBookingSessionId(
        masterBookingActionState = masterBookingActionState,
        fallbackBookingSessionId = Some(fallbackBookingSessionId)
      )

      actual shouldBe Some("fallbackBookingSessionId")
    }

    "return bookingSessionId from fallbackBookingSessionId when booking action is not exists" in {
      val fallbackBookingSessionId = "fallbackBookingSessionId"
      val actual = CommonUtils.getBookingSessionId(
        masterBookingActionState = None,
        fallbackBookingSessionId = Some(fallbackBookingSessionId)
      )

      actual shouldBe Some("fallbackBookingSessionId")
    }

    "return None when bookingSessionId from booking action state is null" in {
      val bookingActionState       = s"""{"request":{"bookingSessionId": null}}"""
      val masterBookingAction      = defaultMasterBookingAction.copy(state = bookingActionState)
      val masterBookingActionState = CommonUtils.getBookingActionState(masterBookingAction)
      val actual = CommonUtils.getBookingSessionId(
        masterBookingActionState = masterBookingActionState,
        fallbackBookingSessionId = None
      )

      actual shouldBe None
    }

    "return None when incorrect booking action state" in {
      val bookingActionState = s"""{"request":{"bookingSession}}"""

      val masterBookingAction      = defaultMasterBookingAction.copy(state = bookingActionState)
      val masterBookingActionState = CommonUtils.getBookingActionState(masterBookingAction)
      val actual = CommonUtils.getBookingSessionId(
        masterBookingActionState,
        fallbackBookingSessionId = None
      )

      actual shouldBe None
    }

    "return None when no state" in {
      val bookingActionState       = "{}"
      val masterBookingAction      = defaultMasterBookingAction.copy(state = bookingActionState)
      val masterBookingActionState = CommonUtils.getBookingActionState(masterBookingAction)
      val actual = CommonUtils.getBookingSessionId(
        masterBookingActionState,
        fallbackBookingSessionId = None
      )

      actual shouldBe None
    }
  }

  "getUserId" should {
    "return userId from request in master action state" in {
      val uuid                     = java.util.UUID.randomUUID().toString
      val bookingActionState       = s"""{"request":{"userId": "$uuid"}}"""
      val masterBookingAction      = defaultMasterBookingAction.copy(state = bookingActionState)
      val masterBookingActionState = CommonUtils.getBookingActionState(masterBookingAction)
      CommonUtils.getUserId(masterBookingActionState.get) shouldBe Some(uuid)
    }

    "return None when request in master action state is null" in {
      val bookingActionState       = s"""{}"""
      val masterBookingAction      = defaultMasterBookingAction.copy(state = bookingActionState)
      val masterBookingActionState = CommonUtils.getBookingActionState(masterBookingAction)
      CommonUtils.getUserId(masterBookingActionState.get) shouldBe None
    }

    "return None when request.userId in master action state is null" in {
      val bookingActionState       = s"""{"request":{}}"""
      val masterBookingAction      = defaultMasterBookingAction.copy(state = bookingActionState)
      val masterBookingActionState = CommonUtils.getBookingActionState(masterBookingAction)
      CommonUtils.getUserId(masterBookingActionState.get) shouldBe None
    }
  }
}
