package com.agoda.bapi.server.service.payment.feature

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.model.WhiteLabelInfo
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.ProductData
import com.agoda.bapi.server.service.payment.PaymentTestHelpers
import com.agoda.bapi.server.service.payment.model.PaymentMethodFeatureComposite
import com.agoda.mpbe.state.common.enums.PaymentMethod.{PaymentMethod => MPBPaymentMethod}
import mocks.RequestContextMock
import org.mockito.ArgumentMatchers.{any, eq => eqTo}
import org.mockito.Mockito.when
import org.scalatest.BeforeAndAfter
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future

class WhiteLabelPaymentFeatureSpec
    extends AsyncWordSpec
    with Matchers
    with MockitoSugar
    with BeforeAndAfter
    with ScalaFutures
    with RequestContextMock
    with PaymentTestHelpers {

  "process" should {
    "should get payment feature correctly" in
      testProcess().map(result => result shouldBe PaymentMethodFeatureComposite())
    "should get payment feature correctly for Diner" in {
      testProcess(
        enablePropertyPaymentMethodsConfig = true
      ).map(result =>
        result shouldBe PaymentMethodFeatureComposite(
          excludedPaymentMethodIds = Set(MPBPaymentMethod.Diners.value)
        )
      )
      testProcess(
        enablePropertyPaymentMethodsConfig = true,
        enableDinerPaymentMethod = true
      ).map(result => result shouldBe PaymentMethodFeatureComposite())
    }
  }

  def testProcess(
      enablePropertyPaymentMethodsConfig: Boolean = false,
      enableDinerPaymentMethod: Boolean = false,
      bookingFlow: BookingFlow.BookingFlow = BookingFlow.SingleProperty
  ): Future[PaymentMethodFeatureComposite] = {
    implicit val setupBookingContext: SetupBookingContext = getSetupBookingContext
    val setupBookingRequest                               = getSetupBookingRequest
    val service                                           = new WhiteLabelPaymentFeature()
    val mockProductData                                   = mock[ProductData]
    val requestContext                                    = mock[RequestContext]
    val whiteLabelInfo                                    = mock[WhiteLabelInfo]

    // WL config
    when(whiteLabelInfo.isFeatureEnabled(eqTo(WhiteLabelFeatureName.PropertyPaymentMethodsConfig), any(), any(), any()))
      .thenReturn(enablePropertyPaymentMethodsConfig)
    when(whiteLabelInfo.isFeatureEnabled(eqTo(WhiteLabelFeatureName.DinerPaymentMethod), any(), any(), any()))
      .thenReturn(enableDinerPaymentMethod)

    when(setupBookingContext.bookingFlowType).thenReturn(bookingFlow)
    when(setupBookingContext.requestContext).thenReturn(requestContext)
    when(requestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)

    val result = service.process(mockProductData, setupBookingRequest)
    result
  }
}
