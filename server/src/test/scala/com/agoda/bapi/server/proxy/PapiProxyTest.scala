package com.agoda.bapi.server.proxy

import akka.actor.{ActorSystem, Scheduler}
import akka.http.javadsl.model.headers.RawHeader
import com.agoda.bapi.common.config.{ApiCallSettings, Configuration}
import com.agoda.bapi.common.model.UserContext
import com.agoda.bapi.common.{BcreMetricReporter, WithProxyMessageTestMock}
import com.agoda.bapi.creation.mapper.ebe.ActivityRiskInfoMapperImpl
import com.agoda.bapi.server.module.features.{BookingModule, CommonModule}
import com.agoda.commons.http.api.v2.exceptions.HttpException
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.mpb.common.header.{AgHeaderKey, AgHttpHeader}
import com.agoda.property.client.{PAPISearchAgHttpClient, ResponseWithJson}
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.util.Modules
import com.google.inject.{AbstractModule, G<PERSON>ce, Module}
import mocks.PropertyMock
import net.codingwell.scalaguice.ScalaModule
import org.mockito.ArgumentMatchers.{any, eq => eqTo}
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.funsuite.AsyncFunSuite
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar
import request.{PropertyContext, PropertyRequest}
import transformers.Properties

import scala.concurrent.{Future, TimeoutException}

class PapiProxyTest
    extends AsyncFunSuite
    with BeforeAndAfter
    with MockitoSugar
    with Matchers
    with PropertyMock
    with WithProxyMessageTestMock {
  implicit private val actorSystem: ActorSystem = mock[ActorSystem]
  private val scheduler: Scheduler              = mock[Scheduler]
  when(actorSystem.scheduler).thenReturn(scheduler)
  when(requestContext.userContext).thenReturn(
    Some(
      UserContext(
        languageId = 1,
        requestOrigin = "th",
        currency = "thb",
        nationalityId = 0,
        experimentData = None
      )
    )
  )
  when(requestContext.agHeaders).thenReturn(
    Seq(
      AgHttpHeader("ag-env", "UAT"),
      AgHttpHeader("AG-AID", "123"),
      AgHttpHeader(
        "AG-MSE-PRICING-TOKEN",
        """[{"cid":"123","cin":"2023-10-10","los":"3", "hid":"1", "mpt":"msePricingToken"}]"""
      )
    )
  )

  private val mockContext = PropertyContext(
    propertyIds = List.empty,
    searchId = "",
    locale = "en-us",
    userId = Some(java.util.UUID.randomUUID().toString),
    cid = 1,
    memberId = 1,
    origin = "",
    platform = 1,
    correlationId = Some("id")
  )
  private val mockPropertyRequest = PropertyRequest(mockContext)

  private val expectedHeaders = collection.immutable.Seq(
    RawHeader.create("ag-env", "UAT"),
    RawHeader.create(
      "AG-MSE-PRICING-TOKEN",
      """[{"cid":"123","cin":"2023-10-10","los":"3", "hid":"1", "mpt":"msePricingToken"}]"""
    )
  )

  private var papiProxy: PapiProxy = _
  private val mockPapi             = mock[PAPISearchAgHttpClient[Future]]
  private val configuration        = mock[Configuration]
  val reporter: MetricsReporter    = mock[MetricsReporter]
  val activityRiskInfoMapper       = mock[ActivityRiskInfoMapperImpl]
  val testModule: Module = Modules
    .`override`(new CommonModule, new BookingModule)
    .`with`(new AbstractModule with ScalaModule {
      override def configure(): Unit = {
        bind[MetricsReporter].toInstance(reporter)
        binder().requestInjection(BcreMetricReporter)
      }
    })

  Guice.createInjector(testModule)

  before {
    reset(mockPapi, configuration)
    when(configuration.apiCallSettings)
      .thenReturn(
        ApiCallSettings(papiMaxRetries = 2, paymentApiV2MaxRetries = 1, gandalfMaxRetries = 2)
      )
    when(mockPapi.mapper.asInstanceOf[ObjectMapper])
      .thenReturn(new ObjectMapper)
    papiProxy = new PapiProxyImpl(mockPapi, configuration, messagingService)
  }

  test("getPropertyForBookingResponse should call PAPIHttpClient.getPropertyForBookingResponseWithJson") {
    when(mockPapi.getPropertyForBookingResponseWithJson(any(), any()))
      .thenReturn(Future.successful(ResponseWithJson(Properties(Seq.empty, None), "{}")))
    when(requestContext.path).thenReturn("/get-papi-tags")
    when(requestContext.clientId).thenReturn(123)

    def expectedOnSuccess[T] = (a: T) =>
      Map[String, String](
        "masterRoomExists"   -> "false",
        "roomSwappingExists" -> "false"
      )

    def expectedTags = Map[String, String](
      "client"               -> "123",
      "endpoint"             -> "/get-papi-tags",
      "method"               -> "mock-method-name",
      "dmc-id"               -> "-1",
      "isPriceFreezeBooking" -> "false"
    )

    val spyPapiProxy = spy(papiProxy)

    spyPapiProxy.getPropertyForBooking(mockPropertyRequest).map { result =>
      verify(spyPapiProxy).withMeasureLogAndExtractTags(
        metricNameSuffix = any(),
        extractTagsOnSuccess = eqTo(expectedOnSuccess),
        extractTagsOnError = any(),
        additionalTags = eqTo(expectedTags)
      )(_)

      result shouldEqual ResponseWithJson(Properties(Seq.empty, None), "{}")
    }
  }

  test("Health check should get successful response") {
    when(mockPapi.getHealthCheck)
      .thenReturn(Future.successful("OK"))
    papiProxy.getHealth.map { result =>
      verify(mockPapi).getHealthCheck
      result shouldEqual Some(true)
    }
  }

  test("toHeaders should parse correctly") {
    val headersMap = Map(
      "ag-env"               -> "UAT",                                                                                    // valid header
      "AG-MSE-PRICING-TOKEN" -> """[{"cid":"123","cin":"2023-10-10","los":"3", "hid":"1", "mpt":"msePricingToken"}]""",   // valid header
      "AG-AID"               -> "123",                                                                                    // valid header
      "AG-DOMAIN"            -> s"""{"Domain":"www.agoda.com","PciDomain":"secure.agoda.com","CdnDomain":"agoda.net"}""", // valid header
      "Content-Length"       -> "invalid value",                                                                          // simulate header with invalid value
      "invalid-key;"         -> "value"                                                                                   // simulate header with invalid key
    )

    val papiProxyImpl: PapiProxyImpl = papiProxy.asInstanceOf[PapiProxyImpl]

    val result = papiProxyImpl.toHeaders(headersMap)
    result should have length 4
    result.exists { header =>
      header.name() == "ag-env" &&
      header.value() == "UAT"
    } shouldBe true
    result.exists { header =>
      header.name() == "AG-MSE-PRICING-TOKEN" &&
      header.value() == """[{"cid":"123","cin":"2023-10-10","los":"3", "hid":"1", "mpt":"msePricingToken"}]"""
    } shouldBe true
    result.exists { header =>
      header.name() == "AG-AID" &&
      header.value() == "123"
    } shouldBe true
    result.exists { header =>
      header.name() == AgHeaderKey.AG_DOMAIN.toString &&
      header.value() == s"""{"Domain":"www.agoda.com","PciDomain":"secure.agoda.com","CdnDomain":"agoda.net"}"""
    } shouldBe true
  }
}
