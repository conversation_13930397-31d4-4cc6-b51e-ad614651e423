package com.agoda.bapi.server.service.payment.feature

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.setupBooking.{CartPricingContext, FlightRequestItem, ProductsRequest, SetupBookingRequest}
import com.agoda.bapi.common.model.WhiteLabelInfo
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.product.BookingFlow.BookingFlow
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.ProductData
import com.agoda.bapi.server.service.payment.model.PaymentFeature.PaymentFeature
import com.agoda.bapi.server.service.payment.model.{PaymentFeature, PaymentMethodFeatureComposite}
import com.agoda.common.itineraryContext.ItineraryContext
import mocks.RequestContextMock
import org.mockito.ArgumentMatchers.{any, eq => eqTo}
import org.mockito.Mockito.when
import org.scalatest.BeforeAndAfter
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future

class CartPaymentFeatureSpec
    extends AsyncWordSpec
    with Matchers
    with MockitoSugar
    with BeforeAndAfter
    with ScalaFutures
    with RequestContextMock {

  "process" should {
    "should get payment feature correctly" in
      testProcess().map(result =>
        result shouldBe PaymentMethodFeatureComposite(paymentFeatures = Set(PaymentFeature.DisableAPM))
      )
    "should get payment feature correctly for ANA WhiteLabel" in
      testProcess(enablePayPalForANA = true).map(result =>
        result shouldBe PaymentMethodFeatureComposite(paymentFeatures = Set.empty[PaymentFeature])
      )
    "should get DisableAPM when enableAutoRefundForCart is A" in
      testProcess(enableAutoRefundForCart = false).map(result =>
        result shouldBe PaymentMethodFeatureComposite(paymentFeatures = Set(PaymentFeature.DisableAPM))
      )
    "should get RequiredAutoRefundSupport when enableAutoRefundForCart is B" in
      testProcess(enableAutoRefundForCart = true).map(result =>
        result shouldBe PaymentMethodFeatureComposite(paymentFeatures = Set(PaymentFeature.RequiredAutoRefundSupport))
      )
  }

  "shouldApplyFeature" should {
    "only apply feature to bookingFlow = Cart" in {
      testApplyFeature(bookingFlow = BookingFlow.SingleProperty) shouldBe false
      testApplyFeature(bookingFlow = BookingFlow.Package) shouldBe false
      testApplyFeature(bookingFlow = BookingFlow.Cart) shouldBe true
      testApplyFeature(
        bookingFlow = BookingFlow.Cart,
        enableMigrateFlightToCartFlow = true,
        isCartBf = true
      ) shouldBe true
      testApplyFeature(
        bookingFlow = BookingFlow.Cart,
        enableMigrateFlightToCartFlow = true,
        enableFlightsSoldInCartBF = true,
        isCartBf = true
      ) shouldBe false
      testApplyFeature(
        bookingFlow = BookingFlow.Cart,
        enableMigrateFlightToCartFlow = true,
        isCartBf = false
      ) shouldBe false
      testApplyFeature(
        bookingFlow = BookingFlow.Cart,
        enableHackerFareToCartFlow = true
      ) shouldBe false
      testApplyFeature(
        bookingFlow = BookingFlow.Cart,
        enableMigrateFlightToCartFlow = true,
        enableHackerFareToCartFlow = true
      ) shouldBe false
      testApplyFeature(
        bookingFlow = BookingFlow.Cart,
        enableMigrateFlightToCartFlow = true,
        enableHackerFareToCartFlow = true,
        isCartBf = true
      ) shouldBe true
    }
  }

  "processIfApplicable" should {
    "return correct payment feature" in {
      testProcessIfApplicable(bookingFlow = BookingFlow.SingleProperty).map(result =>
        result shouldBe PaymentMethodFeatureComposite()
      )
      testProcessIfApplicable(bookingFlow = BookingFlow.Cart).map(result =>
        result shouldBe PaymentMethodFeatureComposite(paymentFeatures = Set(PaymentFeature.DisableAPM))
      )
    }
  }

  def testProcess(
      enablePayPalForANA: Boolean = false,
      enableAutoRefundForCart: Boolean = false,
      featureAware: FeatureAware = mock[FeatureAware],
      setupBookingRequest: SetupBookingRequest = getSetupBookingRequest()
  ): Future[PaymentMethodFeatureComposite] = {
    implicit val setupBookingContext: SetupBookingContext =
      getSetupBookingContext(enablePayPalForANA = enablePayPalForANA, enableAutoRefundForCart = enableAutoRefundForCart)
    val service         = new CartPaymentFeature()
    val mockProductData = mock[ProductData]
    val result          = service.process(mockProductData, setupBookingRequest)
    result
  }

  def testApplyFeature(
      bookingFlow: BookingFlow,
      enableMigrateFlightToCartFlow: Boolean = false,
      isCartBf: Boolean = false,
      enableFlightsSoldInCartBF: Boolean = false,
      enableHackerFareToCartFlow: Boolean = false
  ): Boolean = {
    implicit val setupBookingContext: SetupBookingContext =
      getSetupBookingContext(
        bookingFlow,
        enableMigrateFlightToCartFlow = enableMigrateFlightToCartFlow,
        enableFlightsSoldInCartBF = enableFlightsSoldInCartBF,
        enableHackerFareToCartFlow = enableHackerFareToCartFlow
      )

    val setupBookingRequest = getSetupBookingRequest(populateCartPricingContext = isCartBf)
    val service             = new CartPaymentFeature()
    val mockProductData     = mock[ProductData]
    val result              = service.shouldApplyFeature(mockProductData, setupBookingRequest)
    result
  }

  def testProcessIfApplicable(bookingFlow: BookingFlow): Future[PaymentMethodFeatureComposite] = {
    implicit val setupBookingContext: SetupBookingContext = getSetupBookingContext(bookingFlow)
    implicit val itineraryContext: ItineraryContext       = implicitly(new ItineraryContext())
    val setupBookingRequest                               = getSetupBookingRequest()
    val service                                           = new CartPaymentFeature()
    val mockProductData                                   = mock[ProductData]
    service.processIfApplicable(mockProductData, setupBookingRequest)
  }

  def getSetupBookingContext(
      bookingFlow: BookingFlow = BookingFlow.Cart,
      enablePayPalForANA: Boolean = false,
      enableAutoRefundForCart: Boolean = false,
      enableMigrateFlightToCartFlow: Boolean = false,
      enableFlightsSoldInCartBF: Boolean = false,
      enableHackerFareToCartFlow: Boolean = false
  ): SetupBookingContext = {
    val context        = mock[SetupBookingContext]
    val requestContext = mock[RequestContext]
    val whiteLabelInfo = mock[WhiteLabelInfo]
    val featureAware   = mock[FeatureAware]

    when(featureAware.enableAutoRefundForCart).thenReturn(enableAutoRefundForCart)
    when(featureAware.migrateFlightToCartFlow(any())).thenReturn(enableMigrateFlightToCartFlow)
    when(featureAware.migrateFlightToCartBF).thenReturn(enableFlightsSoldInCartBF)
    when(featureAware.migrateHackerFareToCartFlow(any())).thenReturn(enableHackerFareToCartFlow)
    when(requestContext.featureAware).thenReturn(Some(featureAware))
    when(requestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
    when(whiteLabelInfo.isFeatureEnabled(eqTo(WhiteLabelFeatureName.EnablePayPalForCart), any(), any(), any()))
      .thenReturn(enablePayPalForANA)
    when(context.bookingFlowType).thenReturn(bookingFlow)
    when(context.requestContext).thenReturn(requestContext)

    context
  }

  def getSetupBookingRequest(populateCartPricingContext: Boolean = false): SetupBookingRequest = {
    val setupBookingRequest: SetupBookingRequest = mock[SetupBookingRequest]
    val productsRequest                          = mock[ProductsRequest]
    val flightRequestItem                        = mock[FlightRequestItem]
    when(productsRequest.flightRequests).thenReturn(Seq(flightRequestItem))
    when(productsRequest.isOnlyFlightRequest).thenReturn(true)
    when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
    if (populateCartPricingContext) {
      when(productsRequest.cartPricingContext).thenReturn(Some(mock[CartPricingContext]))
    } else {
      when(productsRequest.cartPricingContext).thenReturn(None)
    }
    setupBookingRequest
  }
}
