package com.agoda.bapi.server.validator

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.pricebreakdown.{PriceBreakdownNode, PriceBreakdownResponse, PriceBreakdownType}
import com.agoda.bapi.common.message.setupBooking.LoyaltyRequest
import com.agoda.bapi.common.model.activity.{ActivityBookingPriceInfo, ActivityConfirmationData, BookingPayment, PaymentPoint}
import com.agoda.bapi.common.model.car._
import com.agoda.bapi.common.model.externalloyalty.ExternalLoyaltyAdditionalInfo
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.model.{WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.token.Money
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.{BookingPropertiesData, ExternalLoyaltyPointsValidationResult, FlightConfirmationData, ProductData}
import com.agoda.externalloyalty.client.v2.model.DistributePointsRequest.PointsOfferType
import com.agoda.externalloyalty.client.v2.model.DistributePointsResponse.RedemptionType
import com.agoda.externalloyalty.client.v2.model.OfferV3Request.SearchType
import com.agoda.flights.client.v2.model.{BurnOffer, ExternalLoyaltyPricing, ItineraryOffers, LoyaltyOffer, SearchResponseCurrencyPricing}
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import com.agoda.winterfell.output.{ExternalLoyaltyUserProfileResponse, LoyaltyProfile, SubLoyaltyPrograms}
import generated.model.{PartnerLoyaltyConfiguration, PartnerLoyaltyConfigurationPointsRequirements}
import models.starfruit.BookingExternalLoyaltyPayment
import org.mockito.Mockito.{reset, when}
import org.scalatest.matchers.must.Matchers._
import org.scalatest.prop.TableDrivenPropertyChecks
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{BeforeAndAfterEach, OptionValues}
import org.scalatestplus.mockito.MockitoSugar
import transformers.{EnrichedEBEBooking, EnrichedEBECreditCard, EnrichedEBEPayment}

import scala.language.postfixOps

class ExternalLoyaltyPointsValidatorImplTest
    extends AsyncWordSpec
    with MockitoSugar
    with OptionValues
    with BeforeAndAfterEach
    with TableDrivenPropertyChecks {
  private lazy val pointsRequirements            = mock[PartnerLoyaltyConfigurationPointsRequirements]
  private lazy val partnerLoyaltyConfig          = mock[PartnerLoyaltyConfiguration]
  private lazy val featureConfiguration          = mock[FeaturesConfiguration]
  private lazy val whiteLabelInfo                = mock[WhiteLabelInfo]
  private lazy val requestContext                = mock[RequestContext]
  private lazy val subLoyaltyProgram             = mock[SubLoyaltyPrograms]
  private lazy val externalLoyaltyProfileInfo    = mock[ExternalLoyaltyUserProfileResponse]
  private lazy val loyaltyProfile                = mock[LoyaltyProfile]
  private lazy val featureAware                  = mock[FeatureAware]
  private lazy val externalLoyaltyAdditionalInfo = mock[ExternalLoyaltyAdditionalInfo]

  implicit private lazy val setupBookingContext: SetupBookingContext = mock[SetupBookingContext]

  private val service = new ExternalLoyaltyPointsValidatorImpl()

  private val BURN = Some("BURN")
  private val EARN = Some("EARN")

  override def beforeEach(): Unit = {
    reset(
      pointsRequirements,
      partnerLoyaltyConfig,
      featureConfiguration,
      whiteLabelInfo,
      setupBookingContext,
      requestContext,
      externalLoyaltyProfileInfo,
      loyaltyProfile,
      featureAware,
      externalLoyaltyAdditionalInfo
    )

    when(setupBookingContext.requestContext).thenReturn(requestContext)
    when(setupBookingContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
    when(requestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
    when(requestContext.featureAware).thenReturn(Some(featureAware))
    when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
    when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Unknown)
  }

  "validate must" should {
    "return correct InsufficientPointsBalance" when {
      "validatePointsBalanceByWhitelabelConfig returns false" when {
        val testCase = Table(
          ("enableExternalLoyaltyPointsErrorV2", "ExternalLoyaltyPointsErrorV2Feature", "expected"),
          (true, true, ExternalLoyaltyPointsValidationResult.HardInsufficientPointsBalance),
          (true, false, ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance),
          (false, true, ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance),
          (false, false, ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance)
        )

        forAll(testCase) { (enableExternalLoyaltyPointsErrorV2, externalLoyaltyPointsErrorV2Feature, expected) =>
          s"on enableExternalLoyaltyPointsErrorV2 exp = $enableExternalLoyaltyPointsErrorV2" +
            s"and ExternalLoyaltyPointsErrorV2Feature feature = $externalLoyaltyPointsErrorV2Feature" +
            s"Should return $expected" in {
              setupPointsRequirementsConfig(
                hasMinimumBalance = true,
                externalLoyaltyPointsErrorV2 = externalLoyaltyPointsErrorV2Feature
              )
              setupLoyaltyProfile(pointBalance = Some(20.0), minimumPointsToRedeem = Some(1000.0))
              setupExperiment(enableExternalLoyaltyPointsErrorV2 = enableExternalLoyaltyPointsErrorV2)
              val loyaltyRequest = getLoyaltyRequest(redeemingPoints = Some(500.0))
              val productData = getProductData(
                propertyRedeemedPoints = 10.0,
                flightRedeemedPoints = 10.0,
                activityRedeemedPoints = 10.0,
                vehicleRedeemedPoints = 10.0
              )

              val result =
                service.validate(
                  Some(loyaltyProfile),
                  Some(loyaltyRequest),
                  productData
                )(
                  setupBookingContext
                )

              result mustBe expected
            }
        }
      }

      "validatePointsBalanceByWhitelabelConfig and validatePointsRedemption return false" when {
        val testCase = Table(
          ("enableExternalLoyaltyPointsErrorV2", "ExternalLoyaltyPointsErrorV2Feature", "expected"),
          (true, true, ExternalLoyaltyPointsValidationResult.HardInsufficientPointsBalance),
          (true, false, ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance),
          (false, true, ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance),
          (false, false, ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance)
        )

        forAll(testCase) { (enableExternalLoyaltyPointsErrorV2, externalLoyaltyPointsErrorV2Feature, expected) =>
          s"on enableExternalLoyaltyPointsErrorV2 exp = $enableExternalLoyaltyPointsErrorV2" +
            s"and ExternalLoyaltyPointsErrorV2Feature feature = $externalLoyaltyPointsErrorV2Feature" +
            s"Should return $expected" in {
              setupPointsRequirementsConfig(
                hasMinimumBalance = true,
                hasMinimumRedeemable = true,
                externalLoyaltyPointsErrorV2 = externalLoyaltyPointsErrorV2Feature
              )
              setupLoyaltyProfile(pointBalance = Some(20.0), minimumPointsToRedeem = Some(1000.0))
              setupExperiment(enableExternalLoyaltyPointsErrorV2 = enableExternalLoyaltyPointsErrorV2)
              val loyaltyRequest =
                getLoyaltyRequest(loyaltySearchType = BURN, redeemingPoints = Some(50.0))
              val productData = getProductData(
                propertyRedeemedPoints = 10.0,
                flightRedeemedPoints = 10.0,
                activityRedeemedPoints = 10.0,
                vehicleRedeemedPoints = 10.0
              )

              val result =
                service.validate(
                  Some(loyaltyProfile),
                  Some(loyaltyRequest),
                  productData
                )(
                  setupBookingContext
                )

              result mustBe expected
            }
        }
      }

      "validatePointsForAllPointsRequest returns false" when {
        val testCase = Table(
          ("enableExternalLoyaltyPointsErrorV2", "ExternalLoyaltyPointsErrorV2Feature", "expected"),
          (true, true, ExternalLoyaltyPointsValidationResult.SoftInsufficientPointsBalance),
          (true, false, ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance),
          (false, true, ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance),
          (false, false, ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance)
        )

        forAll(testCase) { (enableExternalLoyaltyPointsErrorV2, externalLoyaltyPointsErrorV2Feature, expected) =>
          s"on enableExternalLoyaltyPointsErrorV2 exp = $enableExternalLoyaltyPointsErrorV2" +
            s"and ExternalLoyaltyPointsErrorV2Feature feature = $externalLoyaltyPointsErrorV2Feature" +
            s"Should return $expected" in {
              setupPointsRequirementsConfig(externalLoyaltyPointsErrorV2 = externalLoyaltyPointsErrorV2Feature)
              setupLoyaltyProfile(pointBalance = Some(2000.0), minimumPointsToRedeem = Some(100.0))
              setupExperiment(enableExternalLoyaltyPointsErrorV2 = enableExternalLoyaltyPointsErrorV2)
              when(
                whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.UserBalanceValidationWithTotalItemPriceInPoints)
              )
                .thenReturn(true)
              val loyaltyRequest = LoyaltyRequest(pointsOfferType = Some(PointsOfferType.ALLPOINTS), points = Some(101))

              val result =
                service.validate(
                  Some(loyaltyProfile),
                  Some(loyaltyRequest),
                  getProductData(value = 0, totalItemPriceInPoints = 2001)
                )(
                  setupBookingContext
                )

              result mustBe expected
            }
        }
      }
      "validatePointsBalanceByRedemptionType returns false" when {
        val testCase = Table(
          ("enableExternalLoyaltyPointsErrorV2", "ExternalLoyaltyPointsErrorV2Feature", "expected"),
          (true, true, ExternalLoyaltyPointsValidationResult.HardInsufficientPointsBalance),
          (true, false, ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance),
          (false, true, ExternalLoyaltyPointsValidationResult.Success),
          (false, false, ExternalLoyaltyPointsValidationResult.Success)
        )

        forAll(testCase) { (enableExternalLoyaltyPointsErrorV2, externalLoyaltyPointsErrorV2Feature, expected) =>
          s"on enableExternalLoyaltyPointsErrorV2 exp = $enableExternalLoyaltyPointsErrorV2" +
            s"and ExternalLoyaltyPointsErrorV2Feature feature = $externalLoyaltyPointsErrorV2Feature" +
            s"Should return $expected" in {
              setupPointsRequirementsConfig(externalLoyaltyPointsErrorV2 = externalLoyaltyPointsErrorV2Feature)
              setupLoyaltyProfile(pointBalance = Some(20.0), minimumPointsToRedeem = Some(1000.0))
              setupExperiment(enableExternalLoyaltyPointsErrorV2 = enableExternalLoyaltyPointsErrorV2)
              val loyaltyRequest = getLoyaltyRequest(redeemingPoints = Some(500.0))
              val productData = getProductData(
                propertyRedeemedPoints = 10.0,
                flightRedeemedPoints = 10.0,
                activityRedeemedPoints = 10.0,
                vehicleRedeemedPoints = 10.0,
                redemptionType = Some(RedemptionType.POINTSORPARTIALCASH)
              )

              val result =
                service.validate(
                  Some(loyaltyProfile),
                  Some(loyaltyRequest),
                  productData
                )(
                  setupBookingContext
                )

              result mustBe expected
            }
        }
      }
    }
    "return InsufficientPointsRedemption" when {
      "validatePointsRedemption returns false" in {
        setupPointsRequirementsConfig(hasMinimumRedeemable = true)
        setupLoyaltyProfile(pointBalance = Some(1000.0), minimumPointsToRedeem = Some(1000.0))
        val loyaltyRequest =
          getLoyaltyRequest(loyaltySearchType = BURN, redeemingPoints = Some(50.0))
        val productData = getProductData(
          propertyRedeemedPoints = 10.0,
          flightRedeemedPoints = 10.0,
          activityRedeemedPoints = 10.0,
          vehicleRedeemedPoints = 10.0
        )

        val result =
          service.validate(
            Some(loyaltyProfile),
            Some(loyaltyRequest),
            productData
          )(
            setupBookingContext
          )

        result mustBe ExternalLoyaltyPointsValidationResult.InsufficientPointsRedemption
      }
    }
    "return Success" should {
      "validatePointsBalanceByWhitelabelConfig and validatePointsRedemption return true" in {
        setupPointsRequirementsConfig(hasMinimumRedeemable = true)
        setupLoyaltyProfile(pointBalance = Some(1000.0), minimumPointsToRedeem = Some(1000.0))
        val loyaltyRequest =
          getLoyaltyRequest(loyaltySearchType = BURN, redeemingPoints = Some(1000.0))
        val productData = getProductData(
          propertyRedeemedPoints = 300.0,
          flightRedeemedPoints = 200.0,
          activityRedeemedPoints = 100.0,
          vehicleRedeemedPoints = 400.0
        )

        val result =
          service.validate(
            Some(loyaltyProfile),
            Some(loyaltyRequest),
            productData
          )(
            setupBookingContext
          )

        result mustBe ExternalLoyaltyPointsValidationResult.Success
      }

      "return Success when validatePointsBalanceByWhitelabelConfig is true and loyaltySearchType is EARN" in {
        setupPointsRequirementsConfig(hasMinimumBalance = true, hasMinimumRedeemable = true)
        setupLoyaltyProfile(pointBalance = Some(0.0), minimumPointsToRedeem = Some(1000.0))
        val loyaltyRequest =
          getLoyaltyRequest(loyaltySearchType = EARN, redeemingPoints = None)
        val productData = getProductData()

        val result =
          service.validate(
            Some(loyaltyProfile),
            Some(loyaltyRequest),
            productData
          )(
            setupBookingContext
          )

        result mustBe ExternalLoyaltyPointsValidationResult.Success
      }
    }
  }

  "validatePointsBalanceByWhitelabelConfig " should {
    "return true" when {
      "isAllCash returns true" in {
        val loyaltyRequest =
          getLoyaltyRequest(pointsOfferType = Some(PointsOfferType.ALLCASH), redeemingPoints = Some(500.0))

        val result = service.validatePointsBalanceByWhitelabelConfig(Some(loyaltyProfile), Some(loyaltyRequest))(
          setupBookingContext
        )

        result mustBe true
      }

      "WhitelabelUtils.hasMinimumPointsBalanceRequirement returns false" in {
        setupPointsRequirementsConfig(hasMinimumBalance = false)
        val loyaltyRequest =
          getLoyaltyRequest(loyaltySearchType = BURN, redeemingPoints = Some(50.0))

        val result = service.validatePointsBalanceByWhitelabelConfig(Some(loyaltyProfile), Some(loyaltyRequest))(
          setupBookingContext
        )

        result mustBe true
      }

      "WhitelabelUtils.hasMinimumPointsBalanceRequirement returns true" should {
        "and hasMinimumConstraints is false" in {
          setupPointsRequirementsConfig(hasMinimumBalance = true)
          setupLoyaltyProfile(pointBalance = Some(50.0), minimumPointsToRedeem = None)
          val loyaltyRequest =
            getLoyaltyRequest(loyaltySearchType = BURN, redeemingPoints = Some(50.0))

          val result =
            service.validatePointsBalanceByWhitelabelConfig(Some(loyaltyProfile), Some(loyaltyRequest))(
              setupBookingContext
            )

          result mustBe true
        }

        "and hasMinimumConstraints is true" when {
          "and balance is at least constraints" in {
            setupPointsRequirementsConfig(hasMinimumBalance = true)
            setupLoyaltyProfile(pointBalance = Some(1000.0), minimumPointsToRedeem = Some(1000.0))
            val loyaltyRequest =
              getLoyaltyRequest(loyaltySearchType = BURN, redeemingPoints = Some(50.0))

            val result =
              service.validatePointsBalanceByWhitelabelConfig(Some(loyaltyProfile), Some(loyaltyRequest))(
                setupBookingContext
              )

            result mustBe true
          }
        }
      }
    }

    "return false when points balance is less than constraints" in {
      setupPointsRequirementsConfig(hasMinimumBalance = true)
      setupLoyaltyProfile(pointBalance = Some(500.0), minimumPointsToRedeem = Some(1000.0))
      val loyaltyRequest =
        getLoyaltyRequest(loyaltySearchType = BURN, redeemingPoints = Some(50.0))

      val result =
        service.validatePointsBalanceByWhitelabelConfig(Some(loyaltyProfile), Some(loyaltyRequest))(setupBookingContext)

      result mustBe false
    }
  }

  "validatePointsRedemption" should {
    "return true" when {
      "isAllCash returns true" in {
        setupPointsRequirementsConfig(hasMinimumRedeemable = false)
        val productData = getProductData(
          propertyRedeemedPoints = 300.0,
          flightRedeemedPoints = 200.0,
          activityRedeemedPoints = 100.0,
          vehicleRedeemedPoints = 400.0
        )
        val loyaltyRequest =
          getLoyaltyRequest(pointsOfferType = Some(PointsOfferType.ALLCASH), redeemingPoints = Some(500.0))

        val result =
          service.validatePointsRedemption(Some(loyaltyProfile), Some(loyaltyRequest), productData)(setupBookingContext)

        result mustBe true
      }

      "WhitelabelUtils.hasMinimumPointsRedemptionRequirement returns false" in {
        setupPointsRequirementsConfig(hasMinimumRedeemable = false)
        val loyaltyRequest = getLoyaltyRequest(redeemingPoints = Some(500.0))
        val productData = getProductData(
          propertyRedeemedPoints = 300.0,
          flightRedeemedPoints = 200.0,
          activityRedeemedPoints = 100.0,
          vehicleRedeemedPoints = 400.0
        )
        val result =
          service.validatePointsRedemption(Some(loyaltyProfile), Some(loyaltyRequest), productData)(setupBookingContext)

        result mustBe true
      }
      "minimum points to redeem is 0" in {
        when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.MinimumPointsRedemptionValidation)).thenReturn(true)
        setupPointsRequirementsConfig()
        setupLoyaltyProfile(pointBalance = Some(1000.0), minimumPointsToRedeem = Some(0.0))
        val loyaltyRequest = getLoyaltyRequest(redeemingPoints = Some(500.0))
        val productData = getProductData(
          propertyRedeemedPoints = 300.0,
          flightRedeemedPoints = 200.0,
          activityRedeemedPoints = 100.0,
          vehicleRedeemedPoints = 400.0
        )
        val result =
          service.validatePointsRedemption(Some(loyaltyProfile), Some(loyaltyRequest), productData)(setupBookingContext)

        result mustBe true
      }

      "WhitelabelUtils.isMinimumPointsRedemptionValidation returns false" in {
        when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.MinimumPointsRedemptionValidation)).thenReturn(false)
        setupPointsRequirementsConfig()
        val loyaltyRequest = getLoyaltyRequest(redeemingPoints = Some(500.0))
        val productData = getProductData(
          propertyRedeemedPoints = 300.0,
          flightRedeemedPoints = 200.0,
          activityRedeemedPoints = 100.0,
          vehicleRedeemedPoints = 400.0
        )
        val result =
          service.validatePointsRedemption(Some(loyaltyProfile), Some(loyaltyRequest), productData)(setupBookingContext)

        result mustBe true
      }

      "WhitelabelUtils.hasMinimumPointsRedemptionRequirement returns true" should {
        "and hasMinimumPointsConstraints returns false" in {
          setupPointsRequirementsConfig(hasMinimumRedeemable = true)
          setupLoyaltyProfile(pointBalance = Some(1000.0), minimumPointsToRedeem = None)
          val loyaltyRequest =
            getLoyaltyRequest(loyaltySearchType = EARN, redeemingPoints = Some(50.0))
          val productData = getProductData(
            propertyRedeemedPoints = 300.0,
            flightRedeemedPoints = 200.0,
            activityRedeemedPoints = 100.0,
            vehicleRedeemedPoints = 400.0
          )

          val result = service.validatePointsRedemption(Some(loyaltyProfile), Some(loyaltyRequest), productData)(
            setupBookingContext
          )

          result mustBe true
        }

        "and hasMinimumPointsConstraints is true" should {
          "and getIsBurnFlow returns false" in {
            setupPointsRequirementsConfig(hasMinimumRedeemable = true)
            setupLoyaltyProfile(pointBalance = Some(2000.0), minimumPointsToRedeem = Some(50.0))
            val loyaltyRequest =
              getLoyaltyRequest(loyaltySearchType = EARN, redeemingPoints = Some(100.0))
            val productData = getProductData(
              propertyRedeemedPoints = 300.0,
              flightRedeemedPoints = 200.0,
              activityRedeemedPoints = 100.0,
              vehicleRedeemedPoints = 400.0
            )

            val result =
              service.validatePointsRedemption(Some(loyaltyProfile), Some(loyaltyRequest), productData)(
                setupBookingContext
              )

            result mustBe true
          }

          "and getIsBurnFlow returns true" should {

            "and redeeming points is None" in {
              setupPointsRequirementsConfig(hasMinimumRedeemable = true)
              setupLoyaltyProfile(pointBalance = Some(1000.0), minimumPointsToRedeem = Some(1500.0))
              val loyaltyRequest =
                getLoyaltyRequest(loyaltySearchType = BURN, redeemingPoints = None)
              val productData = getProductData(
                propertyRedeemedPoints = 300.0,
                flightRedeemedPoints = 200.0,
                activityRedeemedPoints = 100.0,
                vehicleRedeemedPoints = 400.0
              )

              val result =
                service.validatePointsRedemption(Some(loyaltyProfile), Some(loyaltyRequest), productData)(
                  setupBookingContext
                )

              result mustBe true
            }

            "and redeeming point is at least constraints" in {
              setupPointsRequirementsConfig(hasMinimumRedeemable = true)
              setupLoyaltyProfile(pointBalance = Some(1000.0), minimumPointsToRedeem = Some(1000.0))
              val loyaltyRequest =
                getLoyaltyRequest(loyaltySearchType = BURN, redeemingPoints = Some(1000.0))
              val productData = getProductData(
                propertyRedeemedPoints = 300.0,
                flightRedeemedPoints = 200.0,
                activityRedeemedPoints = 100.0,
                vehicleRedeemedPoints = 400.0
              )

              val result =
                service.validatePointsRedemption(Some(loyaltyProfile), Some(loyaltyRequest), productData)(
                  setupBookingContext
                )

              result mustBe true
            }
          }
        }
      }
    }

    "return false when redeeming point is less than constraints" in {
      setupPointsRequirementsConfig(hasMinimumRedeemable = true)
      setupLoyaltyProfile(pointBalance = Some(1000.0), minimumPointsToRedeem = Some(1000.0))
      val loyaltyRequest =
        getLoyaltyRequest(loyaltySearchType = BURN, redeemingPoints = Some(20.0))
      val productData = getProductData(
        propertyRedeemedPoints = 100.0,
        flightRedeemedPoints = 100.0,
        activityRedeemedPoints = 100.0,
        vehicleRedeemedPoints = 100.0
      )

      val result =
        service.validatePointsRedemption(Some(loyaltyProfile), Some(loyaltyRequest), productData)(setupBookingContext)

      result mustBe false
    }
    "return false when minimum points to redeem greater than 0 and feature enabled" in {
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.MinimumPointsRedemptionValidation)).thenReturn(true)
      setupPointsRequirementsConfig(hasMinimumRedeemable = true)
      setupLoyaltyProfile(pointBalance = Some(1000.0), minimumPointsToRedeem = Some(1000.0))
      val loyaltyRequest =
        getLoyaltyRequest(loyaltySearchType = BURN, redeemingPoints = Some(20.0))
      val productData = getProductData(
        propertyRedeemedPoints = 100.0,
        flightRedeemedPoints = 100.0,
        activityRedeemedPoints = 100.0,
        vehicleRedeemedPoints = 100.0
      )
      val result =
        service.validatePointsRedemption(Some(loyaltyProfile), Some(loyaltyRequest), productData)(setupBookingContext)

      result mustBe false
    }

    "return false when less than minimum required irrespective of loyalty search type" in {
      setupPointsRequirementsConfig(hasMinimumRedeemable = true)
      setupLoyaltyProfile(pointBalance = Some(20000.0), minimumPointsToRedeem = Some(5000.0))
      val loyaltyRequest =
        getLoyaltyRequest(loyaltySearchType = EARN, redeemingPoints = Some(100.0))
      val productData = getProductData(
        propertyRedeemedPoints = 300.0,
        flightRedeemedPoints = 200.0,
        activityRedeemedPoints = 100.0,
        vehicleRedeemedPoints = 400.0
      )

      val result =
        service.validatePointsRedemption(Some(loyaltyProfile), Some(loyaltyRequest), productData)(
          setupBookingContext
        )

      result mustBe false
    }
  }

  "getMinimumPointsToRedeem" should {
    "return None" when {
      "no subloyalty program is found" in {
        when(externalLoyaltyProfileInfo.subLoyaltyPrograms).thenReturn(Seq.empty)
        when(loyaltyProfile.externalLoyaltyProfileInfo).thenReturn(Some(externalLoyaltyProfileInfo))

        val result = service.getMinimumPointsToRedeem(loyaltyProfile)

        result mustBe None
      }

      "minPointsToRedeem is None" in {
        when(subLoyaltyProgram.minimumPointsToRedeem).thenReturn(None)
        when(externalLoyaltyProfileInfo.subLoyaltyPrograms).thenReturn(Seq(subLoyaltyProgram))
        when(loyaltyProfile.externalLoyaltyProfileInfo).thenReturn(Some(externalLoyaltyProfileInfo))

        val result = service.getMinimumPointsToRedeem(loyaltyProfile)

        result mustBe None
      }
    }

    "return correct value" should {
      "subloyalty profile has minimumPointsToRedeem" in {
        when(subLoyaltyProgram.minimumPointsToRedeem).thenReturn(Some(1000.0))
        when(externalLoyaltyProfileInfo.subLoyaltyPrograms).thenReturn(Seq(subLoyaltyProgram))
        when(loyaltyProfile.externalLoyaltyProfileInfo).thenReturn(Some(externalLoyaltyProfileInfo))

        val result = service.getMinimumPointsToRedeem(loyaltyProfile)

        result.value mustBe 1000.0
      }
    }
  }

  "hasMinimumPointsToRedeem" should {
    "return true" when {
      "subloyalty profile has minimumPointsToRedeem" in {
        when(subLoyaltyProgram.minimumPointsToRedeem).thenReturn(Some(1000.0))
        when(externalLoyaltyProfileInfo.subLoyaltyPrograms).thenReturn(Seq(subLoyaltyProgram))
        when(loyaltyProfile.externalLoyaltyProfileInfo).thenReturn(Some(externalLoyaltyProfileInfo))

        val result = service.hasMinimumPointsToRedeem(loyaltyProfile)

        result mustBe true
      }
    }

    "return false" should {
      "no subloyalty program is found" in {
        when(externalLoyaltyProfileInfo.subLoyaltyPrograms).thenReturn(Seq.empty)
        when(loyaltyProfile.externalLoyaltyProfileInfo).thenReturn(Some(externalLoyaltyProfileInfo))

        val result = service.hasMinimumPointsToRedeem(loyaltyProfile)

        result mustBe false
      }

      "minPointsToRedeem is None" in {
        when(subLoyaltyProgram.minimumPointsToRedeem).thenReturn(None)
        when(externalLoyaltyProfileInfo.subLoyaltyPrograms).thenReturn(Seq(subLoyaltyProgram))
        when(loyaltyProfile.externalLoyaltyProfileInfo).thenReturn(Some(externalLoyaltyProfileInfo))

        val result = service.hasMinimumPointsToRedeem(loyaltyProfile)

        result mustBe false
      }
    }
  }

  "getPointsBalance" should {
    "return 0.0" when {
      "externalLoyaltyProfileInfo is None" in {
        when(loyaltyProfile.externalLoyaltyProfileInfo).thenReturn(None)

        val result = service.getPointsBalance(loyaltyProfile)

        result mustBe 0.0
      }

      "externalLoyaltyProfileInfo has value" should {
        "but subLoyaltyProgram is empty" in {
          when(externalLoyaltyProfileInfo.subLoyaltyPrograms).thenReturn(Seq.empty)
          when(loyaltyProfile.externalLoyaltyProfileInfo).thenReturn(Some(externalLoyaltyProfileInfo))

          val result = service.getPointsBalance(loyaltyProfile)

          result mustBe 0.0
        }

        "and loyaltyPrograms is not empty" should {
          "but pointsBalance is None" in {
            when(subLoyaltyProgram.pointsBalance).thenReturn(None)
            when(externalLoyaltyProfileInfo.subLoyaltyPrograms).thenReturn(Seq(subLoyaltyProgram))
            when(loyaltyProfile.externalLoyaltyProfileInfo).thenReturn(Some(externalLoyaltyProfileInfo))

            val result = service.getPointsBalance(loyaltyProfile)

            result mustBe 0.0
          }
        }
      }
    }

    "return correct value" should {
      "pointsBalance has value" in {
        when(subLoyaltyProgram.pointsBalance).thenReturn(Some(100.0))
        when(externalLoyaltyProfileInfo.subLoyaltyPrograms).thenReturn(Seq(subLoyaltyProgram))
        when(loyaltyProfile.externalLoyaltyProfileInfo).thenReturn(Some(externalLoyaltyProfileInfo))

        val result = service.getPointsBalance(loyaltyProfile)

        result mustBe 100.0
      }
    }
  }

  "getIsBurnFlow" should {
    "return false" when {
      "loyaltySearchType is None" in {
        val loyaltyRequest = getLoyaltyRequest(loyaltySearchType = None, redeemingPoints = Some(500.0))
        when(loyaltyRequest.loyaltySearchType).thenReturn(None)

        val result = service.getIsBurnFlow(loyaltyRequest)

        result mustBe false
      }

      "loyaltySearchType has value" should {
        "but the value not BURN" in {
          val loyaltyRequest = getLoyaltyRequest(loyaltySearchType = EARN, redeemingPoints = Some(500.0))

          val result = service.getIsBurnFlow(loyaltyRequest)

          result mustBe false
        }
      }
    }

    "return true when" should {
      "loyaltySearchType has value BURN" in {
        val loyaltyRequest = getLoyaltyRequest(loyaltySearchType = BURN, redeemingPoints = Some(500.0))

        val result = service.getIsBurnFlow(loyaltyRequest)

        result mustBe true
      }
    }
  }

  "hasRedeemAmount" should {
    "return true" when {
      "points has value" in {
        val loyaltyRequest = getLoyaltyRequest(redeemingPoints = Some(100.0))

        val result = service.hasRedeemAmount(loyaltyRequest)

        result mustBe true
      }
    }

    "return false" should {
      "points is None" in {
        val loyaltyRequest = getLoyaltyRequest(redeemingPoints = None)
        when(loyaltyRequest.points).thenReturn(None)

        val result = service.hasRedeemAmount(loyaltyRequest)

        result mustBe false
      }
    }
  }

  "getRedeemedAmount" should {
    "return the correct value" when {
      "points has value" in {
        val productData = getProductData(
          propertyRedeemedPoints = 300.0,
          flightRedeemedPoints = 200.0,
          activityRedeemedPoints = 100.0,
          vehicleRedeemedPoints = 400.0
        )

        val result = service.getRedeemedAmount(productData)

        result mustBe 1000.0
      }
    }
  }

  "getPropertiesRedeemedAmount must" should {
    "return the correct amount of points" in {
      val bookingPropertiesData1 = getBookingPropertiesDataWithRedeemedPoints(redeemedPoints = 500.0)
      val bookingPropertiesData2 = getBookingPropertiesDataWithRedeemedPoints(redeemedPoints = 800.0)

      val result = service.getPropertiesRedeemedAmount(Seq(bookingPropertiesData1, bookingPropertiesData2))

      result mustBe 1300.0
    }
  }

  "getFlightsRedeemedAmount must" should {
    "return the correct amount of points" in {
      val flightConfirmationData1 = getFlightConfirmationDataWithRedeemedPoints(redeemedPoints = 500.0)
      val flightConfirmationData2 = getFlightConfirmationDataWithRedeemedPoints(redeemedPoints = 800.0)

      val result = service.getFlightsRedeemedAmount(Seq(flightConfirmationData1, flightConfirmationData2))

      result mustBe 1300.0
    }
  }

  "getActivitiesRedeemedAmount must" should {
    "return the correct amount of points" in {
      val activityConfirmationData1 = getActivityConfirmationDataWithRedeemedPoints(redeemedPoints = 500.0)
      val activityConfirmationData2 = getActivityConfirmationDataWithRedeemedPoints(redeemedPoints = 800.0)

      val result = service.getActivitiesRedeemedAmount(Seq(activityConfirmationData1, activityConfirmationData2))

      result mustBe 1300.0
    }
  }

  "getVehiclesRedeemedAmount must" should {
    "return the correct amount of points" in {
      val carConfirmationData1 = getCarConfirmationDataWithRedeemedPoints(redeemedPoints = 500.0)
      val carConfirmationData2 = getCarConfirmationDataWithRedeemedPoints(redeemedPoints = 800.0)

      val result = service.getVehiclesRedeemedAmount(Seq(carConfirmationData1, carConfirmationData2))

      result mustBe 1300.0
    }
  }

  "hasCashPortion" should {
    "return true when there is a cash portion" in {
      val productData = getProductData(value = 0)
      service.hasCashPortion(productData) mustBe false
    }

    "return false when there is no cash portion" in {
      val productData = getProductData(value = 100)
      service.hasCashPortion(productData) mustBe true
    }
  }

  "isAllPointsRequest" should {
    "return true when pointsOfferType is ALLPOINTS" in {
      val loyaltyRequest = LoyaltyRequest(pointsOfferType = Some(PointsOfferType.ALLPOINTS))
      service.isAllPointsRequest(loyaltyRequest) mustBe true
    }

    "return false when pointsOfferType is not ALLPOINTS" in {
      val loyaltyRequest = LoyaltyRequest(pointsOfferType = Some(PointsOfferType.ALLCASH))
      service.isAllPointsRequest(loyaltyRequest) mustBe false
    }
  }

  "validateNoCashPortionForAllPoints" should {
    "return true when isAllPoint is true and isCashPortion is true" in {
      val loyaltyRequest = LoyaltyRequest(pointsOfferType = Some(PointsOfferType.ALLPOINTS))
      val productData    = getProductData(value = 0)
      service.validateNoCashPortionForAllPoints(Some(loyaltyRequest), productData)(
        setupBookingContext
      ) mustBe true
    }

    "return false when isAllPoint is true and isCashPortion is false" in {
      val loyaltyRequest = LoyaltyRequest(pointsOfferType = Some(PointsOfferType.ALLPOINTS))
      val productData    = getProductData(value = 10)
      service.validateNoCashPortionForAllPoints(Some(loyaltyRequest), productData)(
        setupBookingContext
      ) mustBe false
    }

    "return true when isAllPoint is false and isCashPortion is true" in {
      val loyaltyRequest = LoyaltyRequest(pointsOfferType = Some(PointsOfferType.ALLCASH))
      val productData    = getProductData(value = 0)
      service.validateNoCashPortionForAllPoints(Some(loyaltyRequest), productData)(
        setupBookingContext
      ) mustBe true
    }

    "return true when isAllPoint is false and isCashPortion is false" in {
      val loyaltyRequest = LoyaltyRequest(pointsOfferType = Some(PointsOfferType.ALLCASH))
      val productData    = getProductData(value = 10)
      service.validateNoCashPortionForAllPoints(Some(loyaltyRequest), productData)(
        setupBookingContext
      ) mustBe true
    }

    "return false when no loyalty request" in {
      val productData = getProductData(value = 10)
      service.validateNoCashPortionForAllPoints(None, productData)(setupBookingContext) mustBe true
    }

    "return false when price break down not exist" in {
      val loyaltyRequest = LoyaltyRequest(pointsOfferType = Some(PointsOfferType.ALLPOINTS))
      val productData    = getProductData(value = 10)
      when(productData.totalPriceDisplay).thenReturn(None)

      service.validateNoCashPortionForAllPoints(Some(loyaltyRequest), productData)(setupBookingContext) mustBe true
    }
  }

  "validatePointsBalanceForAllPointsRequest" should {

    "return true when whitelabel feature is disabled" in {
      setupLoyaltyProfile(pointBalance = Some(2000.0), minimumPointsToRedeem = None)
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.UserBalanceValidationWithTotalItemPriceInPoints))
        .thenReturn(false)
      val result = service.validatePointsBalanceForAllPoints(
        loyaltyProfileOpt = Some(loyaltyProfile),
        loyaltyRequestOpt = None,
        productData = getProductData(value = 0, totalItemPriceInPoints = 2000)
      )

      result mustBe true
    }

    "return true when loyalty request is empty" in {
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.UserBalanceValidationWithTotalItemPriceInPoints))
        .thenReturn(true)
      setupLoyaltyProfile(pointBalance = Some(2000.0), minimumPointsToRedeem = None)
      val result = service.validatePointsBalanceForAllPoints(
        loyaltyProfileOpt = Some(loyaltyProfile),
        loyaltyRequestOpt = None,
        productData = getProductData(value = 0, totalItemPriceInPoints = 2000)
      )

      result mustBe true
    }

    "return true when points offer type do not exists in loyalty request" in {
      setupLoyaltyProfile(pointBalance = Some(2000.0), minimumPointsToRedeem = None)
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.UserBalanceValidationWithTotalItemPriceInPoints))
        .thenReturn(true)
      val loyaltyRequest = LoyaltyRequest(pointsOfferType = None)
      val result = service.validatePointsBalanceForAllPoints(
        loyaltyProfileOpt = Some(loyaltyProfile),
        loyaltyRequestOpt = Some(loyaltyRequest),
        productData = getProductData(value = 0, totalItemPriceInPoints = 2000)
      )

      result mustBe true
    }

    "return true when loyalty profile is empty" in {
      setupLoyaltyProfile(pointBalance = Some(2000.0), minimumPointsToRedeem = None)
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.UserBalanceValidationWithTotalItemPriceInPoints))
        .thenReturn(true)
      val loyaltyRequest = LoyaltyRequest(pointsOfferType = Some(PointsOfferType.ALLPOINTS))
      val result = service.validatePointsBalanceForAllPoints(
        loyaltyProfileOpt = None,
        loyaltyRequestOpt = Some(loyaltyRequest),
        productData = getProductData(value = 0, totalItemPriceInPoints = 2000)
      )

      result mustBe true
    }

    "return true when isAllPoint is false" in {
      val loyaltyRequest = LoyaltyRequest(pointsOfferType = Some(PointsOfferType.ALLCASH))
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.UserBalanceValidationWithTotalItemPriceInPoints))
        .thenReturn(true)
      setupLoyaltyProfile(pointBalance = Some(2000.0), minimumPointsToRedeem = None)
      val result = service.validatePointsBalanceForAllPoints(
        loyaltyProfileOpt = Some(loyaltyProfile),
        loyaltyRequestOpt = Some(loyaltyRequest),
        productData = getProductData(value = 0, totalItemPriceInPoints = 2000)
      )

      result mustBe true
    }

    "return false when isAllPoint is true and user do not have sufficient balance" in {
      val loyaltyRequest = LoyaltyRequest(pointsOfferType = Some(PointsOfferType.ALLPOINTS))
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.UserBalanceValidationWithTotalItemPriceInPoints))
        .thenReturn(true)
      setupLoyaltyProfile(pointBalance = Some(2000.0), minimumPointsToRedeem = None)
      val result = service.validatePointsBalanceForAllPoints(
        loyaltyProfileOpt = Some(loyaltyProfile),
        loyaltyRequestOpt = Some(loyaltyRequest),
        productData = getProductData(value = 0, totalItemPriceInPoints = 2001)
      )

      result mustBe false
    }

    "return true when isAllPoint is true and user have sufficient balance" in {
      val loyaltyRequest = LoyaltyRequest(pointsOfferType = Some(PointsOfferType.ALLPOINTS))
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.UserBalanceValidationWithTotalItemPriceInPoints))
        .thenReturn(true)
      setupLoyaltyProfile(pointBalance = Some(2001.0), minimumPointsToRedeem = None)
      val result = service.validatePointsBalanceForAllPoints(
        loyaltyProfileOpt = Some(loyaltyProfile),
        loyaltyRequestOpt = Some(loyaltyRequest),
        productData = getProductData(value = 0, totalItemPriceInPoints = 2001)
      )

      result mustBe true
    }
  }

  "resolveInsufficientPointsBalanceError" should {
    "resolve correctly on isUseExternalLoyaltyPointV2 enabled" when {
      val testCase = Table(
        (
          "isPointsBalanceValidByWhitelabelConfig",
          "isPointsBalanceValidForAllPoints",
          "isPointsBalanceValidByRedemptionType",
          "expected"
        ),
        (true, true, true, ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance),
        (true, false, true, ExternalLoyaltyPointsValidationResult.SoftInsufficientPointsBalance),
        (false, true, true, ExternalLoyaltyPointsValidationResult.HardInsufficientPointsBalance),
        (false, false, true, ExternalLoyaltyPointsValidationResult.HardInsufficientPointsBalance),
        (true, true, false, ExternalLoyaltyPointsValidationResult.HardInsufficientPointsBalance),
        (true, false, false, ExternalLoyaltyPointsValidationResult.HardInsufficientPointsBalance),
        (false, true, false, ExternalLoyaltyPointsValidationResult.HardInsufficientPointsBalance),
        (false, false, false, ExternalLoyaltyPointsValidationResult.HardInsufficientPointsBalance)
      )
      forAll(testCase) {
        (
            isPointsBalanceValidByWhitelabelConfig,
            isPointsBalanceValidForAllPoints,
            isPointsBalanceValidByRedemptionType,
            expected
        ) =>
          s"isPointsBalanceValidByWhitelabelConfig=$isPointsBalanceValidByWhitelabelConfig, isPointsBalanceValidForAllPoints=$isPointsBalanceValidForAllPoints, isPointsBalanceValidByRedemptionType=$isPointsBalanceValidByRedemptionType" in {
            setupPointsRequirementsConfig(externalLoyaltyPointsErrorV2 = true)
            val result = service.resolveInsufficientPointsBalanceError(
              isPointsBalanceValidByWhitelabelConfig,
              isPointsBalanceValidForAllPoints,
              isPointsBalanceValidByRedemptionType
            )
            result mustBe expected
          }
      }
    }
    "resolve correctly to InsufficientPointsBalance on isUseExternalLoyaltyPointV2 enabled" when {
      val testCase = Table(
        (
          "isPointsBalanceValidByWhitelabelConfig",
          "isPointsBalanceValidForAllPoints",
          "isPointsBalanceValidByRedemptionType"
        ),
        (true, true, true),
        (true, false, true),
        (false, true, true),
        (false, false, true),
        (true, true, false),
        (true, false, false),
        (false, true, false),
        (false, false, false)
      )
      forAll(testCase) {
        (
            isPointsBalanceValidByWhitelabelConfig,
            isPointsBalanceValidForAllPoints,
            isPointsBalanceValidByRedemptionType
        ) =>
          s"isPointsBalanceValidByWhitelabelConfig=$isPointsBalanceValidByWhitelabelConfig, isPointsBalanceValidForAllPoints=$isPointsBalanceValidForAllPoints, isPointsBalanceValidByRedemptionType=$isPointsBalanceValidByRedemptionType" in {
            setupPointsRequirementsConfig(externalLoyaltyPointsErrorV2 = false)
            val result = service.resolveInsufficientPointsBalanceError(
              isPointsBalanceValidByWhitelabelConfig,
              isPointsBalanceValidForAllPoints,
              isPointsBalanceValidByRedemptionType
            )
            result mustBe ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance
          }
      }
    }
  }

  "validatePointsBalanceByRedemptionType " should {
    "return true" when {
      "isAllCash returns true" in {
        setupLoyaltyProfile(pointBalance = Some(500.0), minimumPointsToRedeem = Some(1000.0))
        val loyaltyRequest =
          getLoyaltyRequest(pointsOfferType = Some(PointsOfferType.ALLCASH), redeemingPoints = Some(500.0))
        val productData = getProductData(redemptionType = Some(RedemptionType.ONLYPOINTS))

        val result = service.validatePointsBalanceByRedemptionType(
          Some(loyaltyProfile),
          Some(loyaltyRequest),
          productData = productData
        )(setupBookingContext)

        result mustBe true
      }

      "no redemptionType in productData" in {
        setupLoyaltyProfile(pointBalance = Some(500.0), minimumPointsToRedeem = Some(1000.0))
        val loyaltyRequest =
          getLoyaltyRequest(redeemingPoints = Some(500.0))
        val productData = getProductData(redemptionType = None)

        val result =
          service.validatePointsBalanceByRedemptionType(Some(loyaltyProfile), Some(loyaltyRequest), productData)(
            setupBookingContext
          )

        result mustBe true
      }

      "no redemptionType is not in PointMandatoryRedemptionTypeSet" in {
        setupLoyaltyProfile(pointBalance = Some(500.0), minimumPointsToRedeem = Some(1000.0))
        val loyaltyRequest =
          getLoyaltyRequest(redeemingPoints = Some(500.0))
        val productData = getProductData(redemptionType = Some(RedemptionType.FLEXIBLE))

        val result =
          service.validatePointsBalanceByRedemptionType(Some(loyaltyProfile), Some(loyaltyRequest), productData)(
            setupBookingContext
          )

        result mustBe true
      }

      "no minimumPointsToRedeem in LoyaltyProfile" in {
        setupLoyaltyProfile(pointBalance = Some(500.0), minimumPointsToRedeem = None)
        val loyaltyRequest =
          getLoyaltyRequest(redeemingPoints = Some(500.0))
        val productData = getProductData(redemptionType = Some(RedemptionType.ONLYPOINTS))

        val result =
          service.validatePointsBalanceByRedemptionType(Some(loyaltyProfile), Some(loyaltyRequest), productData)(
            setupBookingContext
          )

        result mustBe true
      }
      "user has enough point in LoyaltyProfile" in {
        setupLoyaltyProfile(pointBalance = Some(1000.0), minimumPointsToRedeem = Some(1000.0))
        val loyaltyRequest =
          getLoyaltyRequest(redeemingPoints = Some(500.0))
        val productData = getProductData(redemptionType = Some(RedemptionType.ONLYPOINTS))

        val result =
          service.validatePointsBalanceByRedemptionType(Some(loyaltyProfile), Some(loyaltyRequest), productData)(
            setupBookingContext
          )

        result mustBe true
      }
    }

    "return false " when {
      "points balance is less than constraints" in {
        setupLoyaltyProfile(pointBalance = Some(500.0), minimumPointsToRedeem = Some(1000.0))
        val loyaltyRequest =
          getLoyaltyRequest(redeemingPoints = Some(500.0))
        val productData = getProductData(redemptionType = Some(RedemptionType.ONLYPOINTS))

        val result =
          service.validatePointsBalanceByRedemptionType(Some(loyaltyProfile), Some(loyaltyRequest), productData)(
            setupBookingContext
          )

        result mustBe false
      }
      "no points balance is loyaltyProfile" in {
        setupLoyaltyProfile(pointBalance = None, minimumPointsToRedeem = Some(1000.0))
        val loyaltyRequest =
          getLoyaltyRequest(redeemingPoints = Some(500.0))
        val productData = getProductData(redemptionType = Some(RedemptionType.ONLYPOINTS))

        val result =
          service.validatePointsBalanceByRedemptionType(Some(loyaltyProfile), Some(loyaltyRequest), productData)(
            setupBookingContext
          )

        result mustBe false
      }
    }
  }

  private def setupPointsRequirementsConfig(
      hasMinimumBalance: Boolean = false,
      hasMinimumRedeemable: Boolean = false,
      externalLoyaltyPointsErrorV2: Boolean = false
  ): Unit = {
    when(pointsRequirements.hasMinimumBalance).thenReturn(Some(hasMinimumBalance))
    when(partnerLoyaltyConfig.pointsRequirements).thenReturn(Some(pointsRequirements))
    when(featureConfiguration.partnerLoyaltyConfiguration).thenReturn(partnerLoyaltyConfig)
    when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.MinimumPointsRedemptionValidation))
      .thenReturn(hasMinimumRedeemable)
    when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.ExternalLoyaltyPointsErrorV2))
      .thenReturn(externalLoyaltyPointsErrorV2)
    when(whiteLabelInfo.feature).thenReturn(featureConfiguration)
    when(setupBookingContext.whiteLabelInfo).thenReturn(whiteLabelInfo)

    ()
  }

  private def setupLoyaltyProfile(pointBalance: Option[Double], minimumPointsToRedeem: Option[Double]): Unit = {
    when(subLoyaltyProgram.pointsBalance).thenReturn(pointBalance)
    when(subLoyaltyProgram.minimumPointsToRedeem).thenReturn(minimumPointsToRedeem)
    when(externalLoyaltyProfileInfo.subLoyaltyPrograms).thenReturn(Seq(subLoyaltyProgram))
    when(loyaltyProfile.externalLoyaltyProfileInfo).thenReturn(Some(externalLoyaltyProfileInfo))
  }

  private def setupExperiment(enableExternalLoyaltyPointsErrorV2: Boolean) = {
    when(featureAware.enableExternalLoyaltyPointsErrorV2).thenReturn(enableExternalLoyaltyPointsErrorV2)
  }

  private def getLoyaltyRequest(
      loyaltySearchType: Option[String] = None,
      pointsOfferType: Option[PointsOfferType] = None,
      redeemingPoints: Option[Double]
  ): LoyaltyRequest = {
    val loyaltyRequest = mock[LoyaltyRequest]

    when(loyaltyRequest.loyaltySearchType).thenReturn(loyaltySearchType)
    when(loyaltyRequest.pointsOfferType).thenReturn(pointsOfferType)
    when(loyaltyRequest.points).thenReturn(redeemingPoints)

    loyaltyRequest
  }

  private def getProductData(
      propertyRedeemedPoints: Double = 0,
      flightRedeemedPoints: Double = 0,
      activityRedeemedPoints: Double = 0,
      vehicleRedeemedPoints: Double = 0,
      totalItemPriceInPoints: BigDecimal = 0,
      value: BigDecimal = 0,
      redemptionType: Option[RedemptionType] = None
  ): ProductData = {
    val productData = mock[ProductData]

    val bookingPropertiesData    = getBookingPropertiesDataWithRedeemedPoints(propertyRedeemedPoints)
    val flightConfirmationData   = getFlightConfirmationDataWithRedeemedPoints(flightRedeemedPoints)
    val activityConfirmationData = getActivityConfirmationDataWithRedeemedPoints(activityRedeemedPoints)
    val carConfirmationData      = getCarConfirmationDataWithRedeemedPoints(vehicleRedeemedPoints)

    val pricePayAgoda = PriceBreakdownNode(
      value = Some(PriceBreakdownResponse(`type` = PriceBreakdownType.PayAgoda, amount = Money(value, "USD")))
    )
    val BundleSummaryBreakdown = PriceBreakdownNode(
      value = Some(PriceBreakdownResponse(`type` = PriceBreakdownType.BundledSummary, amount = Money(10, "USD"))),
      breakdowns = Some(Seq(pricePayAgoda))
    )
    val priceBreakdownResponse = PriceBreakdownResponse(
      `type` = PriceBreakdownType.PriceSummary,
      amount = Money(10, "USD"),
      itemPriceInPoints = Some(totalItemPriceInPoints)
    )
    val priceBreakDown =
      PriceBreakdownNode(
        value = Some(priceBreakdownResponse),
        breakdowns = Some(Seq(BundleSummaryBreakdown))
      )

    when(productData.properties).thenReturn(Seq(bookingPropertiesData))
    when(productData.flights).thenReturn(Seq(flightConfirmationData))
    when(productData.activities).thenReturn(Seq(activityConfirmationData))
    when(productData.cars).thenReturn(Seq(carConfirmationData))
    when(productData.totalPriceDisplay).thenReturn(Some(priceBreakDown))
    when(productData.externalLoyaltyAdditionalInfo).thenReturn(Some(externalLoyaltyAdditionalInfo))
    when(externalLoyaltyAdditionalInfo.redemptionType).thenReturn(redemptionType)

    productData
  }

  private def getBookingPropertiesDataWithRedeemedPoints(redeemedPoints: Double): BookingPropertiesData = {
    val bookingExternalLoyaltyPayment = mock[BookingExternalLoyaltyPayment]
    val enrichedEbePayment            = mock[EnrichedEBEPayment]
    val enrichedEbeCreditCard         = mock[EnrichedEBECreditCard]
    val enrichedEbeBooking            = mock[EnrichedEBEBooking]
    val bookingPropertyData           = mock[BookingPropertiesData]

    when(bookingExternalLoyaltyPayment.points).thenReturn(redeemedPoints)
    when(enrichedEbePayment.externalLoyalty).thenReturn(Some(bookingExternalLoyaltyPayment))
    when(enrichedEbeCreditCard.payment).thenReturn(enrichedEbePayment)
    when(enrichedEbeBooking.creditCard).thenReturn(enrichedEbeCreditCard)
    when(bookingPropertyData.getEbeBookingData()).thenReturn(Some(enrichedEbeBooking))

    bookingPropertyData
  }

  private def getFlightConfirmationDataWithRedeemedPoints(redeemedPoints: Double): FlightConfirmationData = {
    val burnOffer                     = mock[BurnOffer]
    val loyaltyOffer                  = mock[LoyaltyOffer]
    val itineraryOffer                = mock[ItineraryOffers]
    val externalLoyaltyPricing        = mock[ExternalLoyaltyPricing]
    val searchResponseCurrencyPricing = mock[SearchResponseCurrencyPricing]
    val flightConfirmationData        = mock[FlightConfirmationData]

    when(burnOffer.points).thenReturn(Some(redeemedPoints))
    when(loyaltyOffer.burn).thenReturn(Some(burnOffer))
    when(itineraryOffer.items).thenReturn(Seq(loyaltyOffer))
    when(externalLoyaltyPricing.offers).thenReturn(Seq(itineraryOffer))
    when(searchResponseCurrencyPricing.externalLoyalty).thenReturn(Some(externalLoyaltyPricing))
    when(flightConfirmationData.flightPricing).thenReturn(Some(Map("THB" -> searchResponseCurrencyPricing)))

    flightConfirmationData
  }

  private def getActivityConfirmationDataWithRedeemedPoints(redeemedPoints: Double): ActivityConfirmationData = {
    val paymentPoint             = mock[PaymentPoint]
    val bookingPayment           = mock[BookingPayment]
    val activityBookingPriceInfo = mock[ActivityBookingPriceInfo]
    val activityConfirmationData = mock[ActivityConfirmationData]

    when(paymentPoint.noOfPoints).thenReturn(redeemedPoints)
    when(bookingPayment.point).thenReturn(Some(paymentPoint))
    when(activityBookingPriceInfo.payment).thenReturn(bookingPayment)
    when(activityConfirmationData.bookingInfo).thenReturn(Some(activityBookingPriceInfo))

    activityConfirmationData
  }

  private def getCarConfirmationDataWithRedeemedPoints(redeemedPoints: Double): CarConfirmationData = {
    val bookingLoyaltyBurnOffer = mock[BookingLoyaltyBurnOffer]
    val bookingPaymentPoint     = mock[BookingPaymentPoint]
    val bookingDetailsPayment   = mock[BookingDetailsPayment]
    val carBookingDetails       = mock[CarBookingDetails]
    val carConfirmationData     = mock[CarConfirmationData]

    when(bookingLoyaltyBurnOffer.noOfPoints).thenReturn(BookingPointsValue(value = redeemedPoints))
    when(bookingPaymentPoint.burnOffer).thenReturn(Some(bookingLoyaltyBurnOffer))
    when(bookingDetailsPayment.point).thenReturn(Some(bookingPaymentPoint))
    when(carBookingDetails.payment).thenReturn(bookingDetailsPayment)
    when(carConfirmationData.carBookingPricing).thenReturn(Some(carBookingDetails))

    carConfirmationData
  }
}
