package com.agoda.bapi.server.handler

import com.agoda.bapi.common.NoOpMetricsReporter
import com.agoda.bapi.common.config.KillSwitches
import com.agoda.bapi.common.directive.HttpHeaderBase
import com.agoda.bapi.common.handler.{RequestContext, RequestContextBuilder}
import com.agoda.bapi.common.localization.{CmsContextFactory, LocaleContext, LocaleContextFactory}
import com.agoda.bapi.common.message._
import com.agoda.bapi.common.message.creation.{BookingServerStatus, CreatedBookingStatus, FlightBooking, GetStatusRequest, GetStatusResponse, HotelBooking, Itinerary}
import com.agoda.bapi.common.message.flightBookingReplicateByItineraryID.{FlightBookingReplicateByItineraryIDRequest, FlightBookingReplicateByItineraryIDResponse}
import com.agoda.bapi.common.message.multi.product._
import com.agoda.bapi.common.model.StatusToken._
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.common.model.car.VehicleBookingState
import com.agoda.bapi.common.model.car.VehicleBookingStateModel._
import com.agoda.bapi.common.model.flight.flightModel.{Breakdown, FlightModelInternal, MultiProductItinerary}
import com.agoda.bapi.common.model.flight.history.ActionType
import com.agoda.bapi.common.model.multiproduct.MultiProductBookingGroupDBModel
import com.agoda.mpb.common.models.state.ProductType
import com.agoda.bapi.common.provider.ExperimentManagerProvider
import com.agoda.bapi.common.repository.CountriesRepository
import com.agoda.bapi.common.service.{FeatureAware, MessagesBag}
import com.agoda.bapi.common.util.ServerUtils
import com.agoda.bapi.creation.service._
import com.agoda.bapi.server.addon.{CegFastTrackBookingService, GenericAddOnStateService}
import com.agoda.bapi.server.proxy._
import com.agoda.bapi.server.repository._
import com.agoda.bapi.server.service._
import com.agoda.bapi.server.service.vehicle.VehicleBookingService
import com.agoda.bapi.server.validator.BookingsRequestValidator
import com.agoda.bapi.common.MessageService
import com.agoda.bapi.common.model.base.BaseBookingRelationshipInternal
import com.agoda.bapi.common.model.relationship.{RelationshipStatuses, RelationshipTypes}
import com.agoda.commons.agprotobuf.scalapb.utils.ProtoConverter
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.mpbe.state.product.property.{EbePropertyBooking, PropertyProductModel}
import mocks.MeasurementStubs.{logBookingCreationLogMessageBaseStub, measureStub}
import mocks._
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers.{eq => meq, _}
import org.mockito.Mockito._
import org.scalatest._
import org.scalatest.funsuite.AsyncFunSuite
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.{ExecutionContext, Future}

class BookingsHandlerTests
    extends AsyncFunSuite
    with MockitoSugar
    with BeforeAndAfter
    with RequestContextMock
    with Matchers
    with FlightModelMock
    with ActivityModelMock
    with VehicleModelMock
    with DBBookingModelHelper {
  private val contextBuilder                = mock[RequestContextBuilder]
  private val localeContextFactory          = mock[LocaleContextFactory]
  private val localContext                  = mock[LocaleContext]
  private val messagesBag                   = mock[MessagesBag]
  private val validator                     = mock[BookingsRequestValidator]
  private val service                       = mock[BookingsService]
  private val commonService                 = mock[CommonBookingsService]
  private val bcreProxy                     = mock[BookingCreationByDcProxyImpl]
  private val flightBookingsService         = mock[FlightBookingsService]
  private val experimentManagerProviderMock = mock[ExperimentManagerProvider]
  private val killSwitches                  = mock[KillSwitches]
  private val messageService                = mock[MessageService]

  val countryRepo = mock[CountriesRepository]

  val hotelRepo               = mock[HotelRepository]
  val dbProxy                 = mock[ServerBFDBProxy]
  val configurationRepository = mock[ConfigurationRepository]
  val cmsFactory              = mock[CmsContextFactory]
  val repopulateFilter        = mock[Filter]

  private val hadoopMessaging             = mock[HadoopMessagingService]
  private val vehicleBookingService       = mock[VehicleBookingService]
  private val activityService             = mock[ActivityService]
  private val propertyService             = mock[PropertyService]
  private val cegFastTrackBookingService  = mock[CegFastTrackBookingService]
  private val addOnStateService           = mock[GenericAddOnStateService]
  private val pendingBookingChangeService = mock[PendingBookingChangeService]

  implicit val context = requestContext(messagesBag)

  private val bcreEndpointMessageService = new BcreEndpointMessageServiceImpl(messageService, killSwitches)
  private val multiProductBookingGroup = MultiProductBookingGroup(
    bookingId = -1,
    itineraryId = -2,
    cartId = -3,
    packageId = Some(-4)
  )

  private val expectedMultiProductBookingGroups = Seq(
    MultiProductBookingGroupDBModel(
      bookingId = -1,
      itineraryId = -2,
      cartId = -3,
      packageId = Some(-4)
    )
  )

  private val uuid = java.util.UUID.randomUUID().toString
  private val masterBookingAction = BookingWorkflowAction(
    actionId = 1,
    itineraryId = 1,
    bookingType = Some(1),
    bookingId = None,
    memberId = 1,
    actionTypeId = 1,
    correlationId = "",
    requestId = "",
    workflowId = 1,
    workflowStateId = 1,
    productTypeId = Some(1),
    stateSchemaVersion = 1,
    state = s"""{"request":{"userId":"$uuid","bookingSessionId":"testBookingSessionId"}}""",
    storefrontId = Some(1),
    languageId = Some(1)
  )
  private val productBookingAction = masterBookingAction.copy(bookingId = Some(1))

  def withHandler[T]()(block: BookingsHandler => T): T = {
    val handler = new BookingsHandlerImpl(
      contextBuilder,
      validator,
      service,
      commonService,
      bcreProxy,
      hadoopMessaging,
      flightBookingsService,
      vehicleBookingService,
      activityService,
      propertyService,
      cegFastTrackBookingService,
      addOnStateService,
      pendingBookingChangeService,
      bcreEndpointMessageService,
      killSwitches
    ) {
      override val reporter: MetricsReporter = new NoOpMetricsReporter

      override def withMeasureReqRes(
          httpHeader: HttpHeaderBase,
          start: Long,
          success: Boolean,
          errorCode: Option[String],
          subErrorCode: Option[Int] = None,
          additionalTags: Map[String, String] = Map.empty
      ): Future[Unit] =
        Future.successful()

      override def withReqReslog[
          Req <: AnyRef {
            def correlationId: Option[String]
          },
          Res <: ResponseBase
      ](
          httpHeader: HttpHeaderBase,
          ip: String,
          request: Req,
          errorMessage: String,
          createJson: Option[AnyRef => String] = None,
          createAdditionalTags: Option[(Req, Option[Res]) => Map[String, String]] = None,
          loggerTags: Option[Map[String, String]] = None,
          isLogResponse: Boolean = false
      )(f: Future[Res]): Future[Res] = f
    }

    block(handler)
  }

  def withCommonserviceHandler[T]()(block: BookingsHandler => T): T = {
    val handler = new BookingsHandlerImpl(
      contextBuilder,
      validator,
      service,
      commonService,
      bcreProxy,
      hadoopMessaging,
      flightBookingsService,
      vehicleBookingService,
      activityService,
      propertyService,
      cegFastTrackBookingService,
      addOnStateService,
      pendingBookingChangeService,
      bcreEndpointMessageService,
      killSwitches
    ) {
      override val reporter: MetricsReporter = new NoOpMetricsReporter

      override def withMeasureReqRes(
          httpHeader: HttpHeaderBase,
          start: Long,
          success: Boolean,
          errorCode: Option[String],
          subErrorCode: Option[Int] = None,
          additionalTags: Map[String, String] = Map.empty
      ): Future[Unit] =
        Future.successful()

      override def withReqReslog[
          Req <: AnyRef {
            def correlationId: Option[String]
          },
          Res <: ResponseBase
      ](
          httpHeader: HttpHeaderBase,
          ip: String,
          request: Req,
          errorMessage: String,
          createJson: Option[AnyRef => String] = None,
          createAdditionalTags: Option[(Req, Option[Res]) => Map[String, String]] = None,
          loggerTags: Option[Map[String, String]] = None,
          isLogResponse: Boolean = false
      )(f: Future[Res]): Future[Res] = f
    }

    block(handler)
  }

  before {
    reset(
      localeContextFactory,
      localContext,
      messagesBag,
      validator,
      hadoopMessaging,
      service,
      experimentManagerProviderMock,
      vehicleBookingService,
      activityService,
      commonService,
      killSwitches,
      flightBookingsService
    )
    when(contextBuilder.build(any[RequestBase], any[HttpHeaderBase])).thenReturn(Future.successful(context))
    when(contextBuilder.build(any[Request], any[HttpHeaderBase])).thenReturn(Future.successful(context))
    when(contextBuilder.build(any[BookingCreationFlowContext], any[HttpHeaderBase], any[Option[String]], any(), any()))
      .thenReturn(Future.successful(context))
    when(localContext.getLocale(anyInt)).thenReturn("en-us")
    when(localeContextFactory.get).thenReturn(Future.successful(localContext))

    when(hadoopMessaging.sendBookingGetStatusLogMessage(any(), any(), any(), any(), any(), any(), any()))
      .thenReturn(Future.successful())

    when(hadoopMessaging.sendBookingGetStatusResponseLogMessage(any(), any(), any(), any(), any()))
      .thenReturn(Future.successful())
  }

  val mockedItineraryModel = defaultFlightModel.copy(
    actionType = ActionType.Confirmed,
    flights = Seq(),
    slices = Seq(),
    segments = Seq(),
    passengers = Seq(),
    payments = Seq(activityDefaultPayment),
    breakdown = Seq(),
    breakdownPerPax = Seq(),
    baggageAllowance = Seq(),
    history = Seq(activityDefaultHistory),
    paxTickets = Seq(),
    itinerary = defaultFlightItinerary,
    summary = Seq(),
    userAgent = Some(defaultUserAgent),
    bookingAttribution = Seq()
  )

  val mockedPropertyBookingStateWithItinerary = PropertyBookingStateWithItinerary(
    itinerary = defaultFlightItinerary,
    properties = Seq(
      PropertyBookingStateModel(
        bookingId = 123,
        stateId = 2,
        propertyProductModel = Some(
          PropertyProductModel(
            booking = EbePropertyBooking(
              bookingId = 123
            )
          )
        )
      )
    )
  )

  test("replicateFlightBookingByItineraryId returns a successful result") {
    withHandler() { handler =>
      val request = FlightBookingReplicateByItineraryIDRequest(
        itineraryIds = Seq(1L)
      )

      val response = FlightBookingReplicateByItineraryIDResponse(success = true)

      when(validator.validateFlightBookingReplicateByItineraryIDRequest(request)).thenReturn(None)

      when(
        flightBookingsService.replicateFlightBookingByItineraryId(meq(httpHeader), meq(""), meq(request))(
          any[ExecutionContext]
        )
      ).thenReturn(Future.successful(response))

      handler.replicateFlightBookingStateByItineraryId(httpHeader, "", request).map(result => result shouldBe response)
    }
  }

  test("replicateFlightBookingByItineraryId returns a validation error") {
    withHandler() { handler =>
      val request = FlightBookingReplicateByItineraryIDRequest(
        itineraryIds = Seq(1L)
      )

      val response = FlightBookingReplicateByItineraryIDResponse(success = false)

      when(validator.validateFlightBookingReplicateByItineraryIDRequest(request)).thenReturn(Some(response))
      handler
        .replicateFlightBookingStateByItineraryId(httpHeader, "", request)
        .map(result => result shouldBe response)
    }
  }

  test("replicateNonPropertyBookingStateByItineraryId returns a validation error") {
    withHandler() { handler =>
      val request  = ReplicateByItineraryIdRequest(itineraryIds = Seq(100), productType = "Activity")
      val response = ReplicateByItineraryIdResponse(success = false)
      when(validator.validateNonPropertyBookingReplicateByItineraryIdRequest(request)).thenReturn(Some(response))
      handler.replicateNonPropertyBookingStateByItineraryId(httpHeader, "host", request).map { result =>
        result shouldBe response
      }
    }
  }

  test("replicateNonPropertyBookingStateByItineraryId is being replicated for flights") {
    withHandler() { handler =>
      val request  = ReplicateByItineraryIdRequest(itineraryIds = Seq(100), productType = "Flight")
      val response = ReplicateByItineraryIdResponse(success = true)
      when(validator.validateNonPropertyBookingReplicateByItineraryIdRequest(request)).thenReturn(None)
      when(
        flightBookingsService.replicateFlightBookingByItineraryId(any(), any(), any())(any())
      ).thenReturn(Future.successful(FlightBookingReplicateByItineraryIDResponse(success = true)))
      handler.replicateNonPropertyBookingStateByItineraryId(httpHeader, "host", request).map { result =>
        verify(flightBookingsService, times(1)).replicateFlightBookingByItineraryId(any(), any(), any())(any())
        result shouldBe response
      }
    }
  }

  test("replicateNonPropertyBookingStateByItineraryId recovers in case of technical error") {
    withHandler() { handler =>
      val request  = ReplicateByItineraryIdRequest(itineraryIds = Seq(100), productType = "Activity")
      val response = ReplicateByItineraryIdResponse.withError(ResponseErrorCode.TechnicalError, "test-exception")
      when(validator.validateNonPropertyBookingReplicateByItineraryIdRequest(request)).thenReturn(None)
      when(activityService.replicateBookingsByItineraryId(request))
        .thenReturn(Future.failed(new RuntimeException("test-exception")))
      handler.replicateNonPropertyBookingStateByItineraryId(httpHeader, "host", request).map { result =>
        result shouldBe response
      }
    }
  }

  test("replicateNonPropertyBookingStateByItineraryId is being replicated for Activity") {
    withHandler() { handler =>
      val request  = ReplicateByItineraryIdRequest(itineraryIds = Seq(100), productType = "Activity")
      val response = ReplicateByItineraryIdResponse.withSuccess()
      when(validator.validateNonPropertyBookingReplicateByItineraryIdRequest(request)).thenReturn(None)
      when(activityService.replicateBookingsByItineraryId(request))
        .thenReturn(Future.successful(ReplicateByItineraryIdResponse.withSuccess()))
      handler.replicateNonPropertyBookingStateByItineraryId(httpHeader, "host", request).map { result =>
        verify(activityService, times(1)).replicateBookingsByItineraryId(request)
        result shouldBe response
      }
    }
  }

  test("replicateNonPropertyBookingStateByItineraryId is being replicated for Vehicles") {
    withHandler() { handler =>
      val request  = ReplicateByItineraryIdRequest(itineraryIds = Seq(100), productType = "Vehicle")
      val response = ReplicateByItineraryIdResponse.withSuccess()
      when(validator.validateNonPropertyBookingReplicateByItineraryIdRequest(request)).thenReturn(None)
      when(vehicleBookingService.replicateBookingsByItineraryId(request))
        .thenReturn(Future.successful(ReplicateByItineraryIdResponse.withSuccess()))
      handler.replicateNonPropertyBookingStateByItineraryId(httpHeader, "host", request).map { result =>
        verify(vehicleBookingService, times(1)).replicateBookingsByItineraryId(request)
        result shouldBe response
      }
    }
  }

  test("replicateNonPropertyBookingStateByItineraryId is being replicated for AddOn") {
    withHandler() { handler =>
      val request  = ReplicateByItineraryIdRequest(itineraryIds = Seq(100), productType = "AddOn")
      val response = ReplicateByItineraryIdResponse.withSuccess()
      when(validator.validateNonPropertyBookingReplicateByItineraryIdRequest(request)).thenReturn(None)
      when(addOnStateService.replicateBookingsByItineraryId(request))
        .thenReturn(Future.successful(ReplicateByItineraryIdResponse.withSuccess()))
      handler.replicateNonPropertyBookingStateByItineraryId(httpHeader, "host", request).map { result =>
        verify(addOnStateService, times(1)).replicateBookingsByItineraryId(request)
        result shouldBe response
      }
    }
  }

  test("replicateNonPropertyBookingStateByItineraryId is being replicated for CegFastTrack") {
    withHandler() { handler =>
      val request  = ReplicateByItineraryIdRequest(itineraryIds = Seq(100), productType = "CegFastTrack")
      val response = ReplicateByItineraryIdResponse.withSuccess()
      when(validator.validateNonPropertyBookingReplicateByItineraryIdRequest(request)).thenReturn(None)
      when(cegFastTrackBookingService.replicateBookingsByItineraryId(request))
        .thenReturn(Future.successful(ReplicateByItineraryIdResponse.withSuccess()))
      handler.replicateNonPropertyBookingStateByItineraryId(httpHeader, "host", request).map { result =>
        verify(cegFastTrackBookingService, times(1)).replicateBookingsByItineraryId(request)
        result shouldBe response
      }
    }
  }

  test(
    "replicateFlightBookingStateByItineraryId process PropertyBookingState only when there's property but no flight/vehicle/activity in request"
  ) {
    withHandler() { handler =>
      val propertyBookingState = PropertyBookingState(123, 2)
      val request              = mock[SetStateRequest]
      when(request.properties).thenReturn(Some(Seq(propertyBookingState)))
      when(request.flights).thenReturn(Seq.empty)
      when(request.vehicle).thenReturn(Some(Seq.empty))
      when(request.activities).thenReturn(None)
      when(request.cegFastTracks).thenReturn(None)
      when(request.addOns).thenReturn(None)
      when(request.protectionModels).thenReturn(None)
      when(request.bookingId).thenReturn(1)
      when(request.itinerary).thenReturn(MultiProductItinerary(itineraryId = 1, memberId = 1))

      val expectedResponse = SetStateResponse(
        success = true,
        bookingState = Some(mockedItineraryModel),
        propertyBookings =
          Some(List(PropertyBookingState(123, 2, Some("EisIe7oFJgokMDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAw"))))
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(service.setPropertyBookingState(propertyBookingState))
        .thenReturn(Future.successful(SetStateResponse(success = true)))
      when(
        flightBookingsService.setOnlyItineraryBookingState(meq(request), meq(true))(
          any[ExecutionContext],
          any[RequestContext]
        )
      )
        .thenReturn(Future.successful(mockedItineraryModel))
      when(propertyService.saveAndReplicatePropertyBookingState(any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(mockedPropertyBookingStateWithItinerary))

      handler
        .replicateItineraryBookingState(httpHeader, "", request)
        .map(result => {
          verify(flightBookingsService).setOnlyItineraryBookingState(meq(request), meq(true))(
            any[ExecutionContext],
            any[RequestContext]
          )
          result shouldBe expectedResponse
        })
    }
  }

  test(
    "replicateFlightBookingStateByItineraryId process PropertyBookingState only when there're multiple properties and no flight/vehicle/activity in request"
  ) {
    withHandler() { handler =>
      val propertyBookingState1 = PropertyBookingState(123, 2)
      val propertyBookingState2 = PropertyBookingState(456, 2)
      val request               = mock[SetStateRequest]
      when(request.properties).thenReturn(Some(Seq(propertyBookingState1, propertyBookingState2)))
      when(request.flights).thenReturn(Seq.empty)
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.cegFastTracks).thenReturn(None)
      when(request.addOns).thenReturn(None)
      when(request.protectionModels).thenReturn(None)
      when(request.bookingId).thenReturn(1)
      when(request.itinerary).thenReturn(MultiProductItinerary(itineraryId = 1, memberId = 1))

      val expectedResponse = SetStateResponse(
        success = true,
        bookingState = Some(mockedItineraryModel),
        propertyBookings =
          Some(List(PropertyBookingState(123, 2, Some("EisIe7oFJgokMDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAw"))))
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(service.setPropertyBookingState(propertyBookingState1))
        .thenReturn(Future.successful(SetStateResponse(success = true)))
      when(service.setPropertyBookingState(propertyBookingState2))
        .thenReturn(Future.successful(SetStateResponse(success = true)))

      when(
        flightBookingsService.setOnlyItineraryBookingState(meq(request), meq(true))(
          any[ExecutionContext],
          any[RequestContext]
        )
      )
        .thenReturn(Future.successful(mockedItineraryModel))

      handler
        .replicateItineraryBookingState(httpHeader, "", request)
        .map(result => {
          verify(flightBookingsService).setOnlyItineraryBookingState(meq(request), meq(true))(
            any[ExecutionContext],
            any[RequestContext]
          )
          result shouldBe expectedResponse
        })
    }
  }

  test(
    "replicateFlightBookingStateByItineraryId process FlightBookingState only when there's flight but no property in request"
  ) {
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq(defaultFlightBooking))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.cegFastTracks).thenReturn(None)
      when(request.addOns).thenReturn(None)
      when(request.protectionModels).thenReturn(None)
      when(request.bookingId).thenReturn(1)
      when(request.itinerary).thenReturn(MultiProductItinerary(itineraryId = 1, memberId = 1))

      val expectedResponse = SetStateResponse(success = true, bookingState = Some(defaultFlightModel))

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(
        flightBookingsService.setFlightBookingState(meq(httpHeader), meq(""), meq(request), meq(true))(
          any[ExecutionContext],
          any[RequestContext]
        )
      ).thenReturn(Future.successful(SetStateResponse(success = true, bookingState = Some(defaultFlightModel))))

      handler
        .replicateItineraryBookingState(httpHeader, "", request)
        .map(result => {
          verify(flightBookingsService).setFlightBookingState(meq(httpHeader), meq(""), meq(request), meq(true))(
            any[ExecutionContext],
            any[RequestContext]
          )
          result shouldBe expectedResponse
        })
    }
  }

  test(
    "replicateFlightBookingStateByItineraryId process FlightBookingState only when there's flight but empty property sequence in request"
  ) {
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.properties).thenReturn(Some(Seq()))
      when(request.flights).thenReturn(Seq(defaultFlightBooking))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.cegFastTracks).thenReturn(None)
      when(request.addOns).thenReturn(None)
      when(request.protectionModels).thenReturn(None)
      when(request.bookingId).thenReturn(1)
      when(request.itinerary).thenReturn(MultiProductItinerary(itineraryId = 1, memberId = 1))

      val expectedResponse = SetStateResponse(success = true, bookingState = Some(defaultFlightModel))

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(
        flightBookingsService.setFlightBookingState(meq(httpHeader), meq(""), meq(request), meq(true))(
          any[ExecutionContext],
          any[RequestContext]
        )
      ).thenReturn(Future.successful(SetStateResponse(success = true, bookingState = Some(defaultFlightModel))))

      handler
        .replicateItineraryBookingState(httpHeader, "", request)
        .map(result => {
          verify(flightBookingsService).setFlightBookingState(meq(httpHeader), meq(""), meq(request), meq(true))(
            any[ExecutionContext],
            any[RequestContext]
          )
          result shouldBe expectedResponse
        })
    }
  }

  test("replicateFlightBookingStateByItineraryId return error when request doesn't pass validation") {
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.properties)
        .thenReturn(Some(Seq(PropertyBookingState(0, CreatedBookingStatus.BookingProcessing.id.toShort))))
      when(request.flights).thenReturn(Seq.empty)
      when(request.bookingId).thenReturn(1)
      when(request.itinerary).thenReturn(MultiProductItinerary(itineraryId = 1, memberId = 1))

      val expectedResponse = SetStateResponse(
        success = false,
        errorCode = Some("InvalidRequestData"),
        errorMessage = Some("property booking id in List(0) is invalid")
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(
        Some(SetStateResponse(false, Some("InvalidRequestData"), Some("property booking id in List(0) is invalid")))
      )

      handler
        .replicateItineraryBookingState(httpHeader, "", request)
        .map(result => result shouldBe expectedResponse)
    }
  }

  test(
    "replicateFlightBookingStateByItineraryId return success when both property and flight response are success and keep flight warning message if any"
  ) {
    withHandler() { handler =>
      val propertyBookingState = PropertyBookingState(123, 2)
      val request              = mock[SetStateRequest]
      when(request.properties).thenReturn(Some(Seq(propertyBookingState)))
      when(request.flights).thenReturn(Seq(defaultFlightBooking))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.cegFastTracks).thenReturn(None)
      when(request.addOns).thenReturn(None)
      when(request.userContext).thenReturn(None)
      when(request.protectionModels).thenReturn(None)
      when(request.bookingId).thenReturn(1)
      when(request.itinerary).thenReturn(MultiProductItinerary(itineraryId = 1, memberId = 1))

      val expectedResponse = SetStateResponse(
        success = true,
        bookingState = Some(defaultFlightModel),
        errorCode = Some("Flight ErrorCode: BOOKING_NOT_FOUND"),
        errorMessage = Some("Flight ErrorMessage: WARNING: Can't find flights booking with id 11111 and version < 2"),
        propertyBookings = Some(
          Seq(
            propertyBookingState.copy(propertyState =
              Some("EisIe7oFJgokMDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAw")
            )
          )
        )
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(service.setPropertyBookingState(propertyBookingState))
        .thenReturn(Future.successful(SetStateResponse(success = true)))
      when(
        flightBookingsService.setOnlyItineraryBookingState(meq(request), meq(true))(
          any[ExecutionContext],
          any[RequestContext]
        )
      )
        .thenReturn(Future.successful(defaultFlightModel))
      when(
        flightBookingsService.setFlightBookingState(meq(httpHeader), meq(""), meq(request), meq(true))(
          any[ExecutionContext],
          any[RequestContext]
        )
      ).thenReturn(
        Future.successful(
          SetStateResponse(
            success = true,
            bookingState = Some(defaultFlightModel),
            errorCode = Some("BOOKING_NOT_FOUND"),
            errorMessage = Some("WARNING: Can't find flights booking with id 11111 and version < 2")
          )
        )
      )

      handler
        .replicateItineraryBookingState(httpHeader, "", request)
        .map(result => result shouldBe expectedResponse)
    }
  }

  test(
    "replicateFlightBookingStateByItineraryId return success with warn message when flight response is success but property is not"
  ) {
    withHandler() { handler =>
      val propertyBookingState = PropertyBookingState(123, 2)
      val request              = mock[SetStateRequest]
      when(request.properties).thenReturn(Some(Seq(propertyBookingState)))
      when(request.flights).thenReturn(Seq(defaultFlightBooking))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.cegFastTracks).thenReturn(None)
      when(request.addOns).thenReturn(None)
      when(request.userContext).thenReturn(None)
      when(request.protectionModels).thenReturn(None)
      when(request.bookingId).thenReturn(1)
      when(request.itinerary).thenReturn(MultiProductItinerary(itineraryId = 1, memberId = 1))

      val expectedResponse = SetStateResponse(
        success = true,
        errorCode = Some("PARTIAL_UPDATE"),
        errorMessage = Some(
          "Properties ErrorMessage: PropertyBookingState was not updated & Flight ErrorMessage: Flight ErrorMessage: WARNING: Can't find flights booking with id 11111 and version < 2"
        ),
        bookingState = Some(defaultFlightModel)
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(service.setPropertyBookingState(propertyBookingState)).thenReturn(
        Future.successful(
          SetStateResponse(
            success = false,
            errorCode = Some("InternalError"),
            errorMessage = Some("PropertyBookingState was not updated")
          )
        )
      )

      when(
        flightBookingsService.setOnlyItineraryBookingState(meq(request), meq(true))(
          any[ExecutionContext],
          any[RequestContext]
        )
      )
        .thenReturn(Future.successful(defaultFlightModel))

      when(
        flightBookingsService.setFlightBookingState(meq(httpHeader), meq(""), meq(request), meq(true))(
          any[ExecutionContext],
          any[RequestContext]
        )
      ).thenReturn(
        Future.successful(
          SetStateResponse(
            success = true,
            bookingState = Some(defaultFlightModel),
            errorCode = Some("Flight ErrorCode: BOOKING_NOT_FOUND"),
            errorMessage =
              Some("Flight ErrorMessage: WARNING: Can't find flights booking with id 11111 and version < 2")
          )
        )
      )

      handler
        .replicateItineraryBookingState(httpHeader, "", request)
        .map(result => result shouldBe expectedResponse)
    }
  }

  test(
    "replicateFlightBookingStateByItineraryId return success with warn message when property response is success but flight is not"
  ) {
    withHandler() { handler =>
      val propertyBookingState = PropertyBookingState(123, 2)
      val request              = mock[SetStateRequest]
      when(request.properties).thenReturn(Some(Seq(propertyBookingState)))
      when(request.flights).thenReturn(Seq(defaultFlightBooking))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.cegFastTracks).thenReturn(None)
      when(request.addOns).thenReturn(None)
      when(request.userContext).thenReturn(None)
      when(request.protectionModels).thenReturn(None)
      when(request.bookingId).thenReturn(1)
      when(request.itinerary).thenReturn(MultiProductItinerary(itineraryId = 1, memberId = 1))

      val expectedResponse = SetStateResponse(
        success = true,
        errorCode = Some("PARTIAL_UPDATE"),
        errorMessage = Some("Flight ErrorMessage: Collision Found! new version is 1 but existed version is 3"),
        propertyBookings = Some(
          Seq(
            propertyBookingState.copy(propertyState =
              Some("EisIe7oFJgokMDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAw")
            )
          )
        )
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(service.setPropertyBookingState(propertyBookingState))
        .thenReturn(Future.successful(SetStateResponse(success = true)))

      when(
        flightBookingsService.setOnlyItineraryBookingState(meq(request), meq(true))(
          any[ExecutionContext],
          any[RequestContext]
        )
      )
        .thenReturn(Future.successful(defaultFlightModel))

      when(
        flightBookingsService.setFlightBookingState(meq(httpHeader), meq(""), meq(request), meq(true))(
          any[ExecutionContext],
          any[RequestContext]
        )
      ).thenReturn(
        Future.successful(
          SetStateResponse(
            success = false,
            errorCode = Some("InternalError"),
            errorMessage = Some("Collision Found! new version is 1 but existed version is 3")
          )
        )
      )

      handler
        .replicateItineraryBookingState(httpHeader, "", request)
        .map(result => result shouldBe expectedResponse)
    }
  }

  test(
    "replicateFlightBookingStateByItineraryId return error when both property and flight response are failed with error code and message combined"
  ) {
    withHandler() { handler =>
      val propertyBookingState = PropertyBookingState(123, 2)
      val request              = mock[SetStateRequest]
      when(request.properties).thenReturn(Some(Seq(propertyBookingState)))
      when(request.flights).thenReturn(Seq(defaultFlightBooking))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.cegFastTracks).thenReturn(None)
      when(request.addOns).thenReturn(None)
      when(request.userContext).thenReturn(None)
      when(request.protectionModels).thenReturn(None)
      when(request.bookingId).thenReturn(1)
      when(request.itinerary).thenReturn(MultiProductItinerary(itineraryId = 1, memberId = 1))

      val expectedResponse = SetStateResponse(
        success = false,
        errorCode = Some("Properties ErrorCode: InternalError & Flight ErrorCode: InternalError"),
        errorMessage = Some(
          "Properties ErrorMessage: PropertyBookingState was not updated & Flight ErrorMessage: Collision Found! new version is 1 but existed version is 3"
        )
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(service.setPropertyBookingState(propertyBookingState)).thenReturn(
        Future.successful(
          SetStateResponse(
            success = false,
            errorCode = Some("InternalError"),
            errorMessage = Some("PropertyBookingState was not updated")
          )
        )
      )

      when(
        flightBookingsService.setFlightBookingState(meq(httpHeader), meq(""), meq(request), meq(true))(
          any[ExecutionContext],
          any[RequestContext]
        )
      ).thenReturn(
        Future.successful(
          SetStateResponse(
            success = false,
            errorCode = Some("InternalError"),
            errorMessage = Some("Collision Found! new version is 1 but existed version is 3")
          )
        )
      )

      handler
        .replicateItineraryBookingState(httpHeader, "", request)
        .map(result => result shouldBe expectedResponse)
    }
  }

  test(
    "multiProductReplication return partial success when some product are failed with error code and message combined"
  ) {
    withHandler() { handler =>
      val propertyBookingState = PropertyBookingState(123, 2)
      val activityBookingStateWithItinerary = ActivityBookingStateWithItinerary(
        itinerary = mockItineraryInternalModel.itinerary,
        itineraryHistories = mockItineraryInternalModel.history,
        payments = mockItineraryInternalModel.payments,
        bookingPayments = mockItineraryInternalModel.bookingPayments,
        activities = Seq(mockActivityProductModel),
        multiProductBookingGroups = expectedMultiProductBookingGroups,
        relationships = Nil
      )
      val activityBookingState = ActivityBookingState(
        ProtoConverter.protoToString(mockActivityProductModel),
        mockActivityProductModel.product.booking.bookingId
      )
      val request = mock[SetStateRequest]
      when(request.protectionModels).thenReturn(None)
      when(request.properties).thenReturn(Some(Seq(propertyBookingState)))
      when(request.flights).thenReturn(Seq(defaultFlightBooking))
      when(request.vehicle).thenReturn(Some(Seq(defaultMockVehicleModel)))
      when(request.activities).thenReturn(
        Some(
          Seq(
            activityBookingState
          )
        )
      )
      when(request.bookingRelationships).thenReturn(None)
      when(request.cegFastTracks).thenReturn(None)
      when(request.addOns).thenReturn(None)
      when(request.actionType).thenReturn(ActionType.Created)
      when(request.actionId).thenReturn(0L)
      when(request.bookingType).thenReturn(Some(1))
      when(request.payments).thenReturn(Seq(defaultPayment))
      when(request.bookingPayments).thenReturn(Some(Seq(defaultBookingPayment)))
      when(request.history).thenReturn(Seq(defaultHistory))
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductBookingGroups).thenReturn(None)
      when(request.multiProductInfos).thenReturn(None)
      when(request.userContext).thenReturn(None)

      val bypassItineraryDate = DateTime.parse("2019-08-02T16:01")
      // expected FlightInternalModelFromVehicle since FlightSetStateResponse is failed
      val expectedFlightInternalModel = FlightModelInternal(
        actionType = request.actionType,
        actionId = request.actionId,
        bookingType = request.bookingType,
        bookingId = 0,
        schemaVersion = FlightModelInternal.SCHEMA_VERSION,
        itinerary = defaultVehicleBookingState.itinerary,
        payments = defaultVehicleBookingState.payments,
        history = defaultVehicleBookingState.itineraryHistories,
        itineraryDate = bypassItineraryDate
      )
      val expectedResponse = SetStateResponse(
        success = true,
        errorCode = Some("PARTIAL_UPDATE"),
        errorMessage = Some(
          "Properties ErrorMessage: PropertyBookingState was not updated & Flight ErrorMessage: Collision Found! new version is 1 but existed version is 3"
        ),
        activityBookings = Some(Seq(activityBookingState)),
        vehicleBookings = Some(Seq(defaultMockVehicleModel)),
        bookingState = Some(expectedFlightInternalModel)
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(propertyService.saveAndReplicatePropertyBookingState(any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(mockedPropertyBookingStateWithItinerary))
      when(service.setPropertyBookingState(propertyBookingState)).thenReturn(
        Future.successful(
          SetStateResponse(
            success = false,
            errorCode = Some("InternalError"),
            errorMessage = Some("PropertyBookingState was not updated")
          )
        )
      )

      when(
        flightBookingsService.setFlightBookingState(meq(httpHeader), meq(""), meq(request), meq(true))(
          any[ExecutionContext],
          any[RequestContext]
        )
      ).thenReturn(
        Future.successful(
          SetStateResponse(
            success = false,
            errorCode = Some("InternalError"),
            errorMessage = Some("Collision Found! new version is 1 but existed version is 3")
          )
        )
      )

      when(
        vehicleBookingService.saveAndReplicateVehicleBookingState(
          any(),
          any(),
          any(),
          any[VehicleBookingState],
          any(),
          any()
        )
      )
        .thenReturn(
          Future.successful(defaultVehicleBookingState)
        )

      when(
        activityService.saveAndReplicateActivityBookingState(
          any(),
          any(),
          any(),
          any[ActivityBookingStateWithItinerary],
          any()
        )
      )
        .thenReturn(
          Future.successful(activityBookingStateWithItinerary)
        )

      handler
        .replicateItineraryBookingState(httpHeader, "", request)
        .map(result => {
          // Need to bypass ItineraryDate since the default value is DateTime.now() and we retrieve that data to assert
          val actualResponseWithBypassItineraryDate =
            result.copy(bookingState = result.bookingState.map(_.copy(itineraryDate = bypassItineraryDate)))
          actualResponseWithBypassItineraryDate shouldBe expectedResponse
        })
    }
  }

  test(
    "multiProductReplication return success when product are all success with warning code and message combined"
  ) {
    withHandler() { handler =>
      val propertyBookingState = PropertyBookingState(123, 2)
      val activityBookingStateWithItinerary = ActivityBookingStateWithItinerary(
        itinerary = mockItineraryInternalModel.itinerary,
        itineraryHistories = mockItineraryInternalModel.history,
        payments = mockItineraryInternalModel.payments,
        bookingPayments = mockItineraryInternalModel.bookingPayments,
        activities = Seq(mockActivityProductModel),
        multiProductBookingGroups = expectedMultiProductBookingGroups,
        relationships = Nil
      )
      val activityBookingState = ActivityBookingState(
        ProtoConverter.protoToString(mockActivityProductModel),
        mockActivityProductModel.product.booking.bookingId
      )
      val request = mock[SetStateRequest]
      when(request.protectionModels).thenReturn(None)
      when(request.bookingRelationships).thenReturn(None)
      when(request.properties).thenReturn(Some(Seq(propertyBookingState)))
      when(request.flights).thenReturn(Seq(defaultFlightBooking))
      when(request.vehicle).thenReturn(Some(Seq(defaultMockVehicleModel)))
      when(request.activities).thenReturn(
        Some(
          Seq(
            activityBookingState
          )
        )
      )
      when(request.cegFastTracks).thenReturn(None)
      when(request.addOns).thenReturn(None)
      when(request.actionType).thenReturn(ActionType.Created)
      when(request.actionId).thenReturn(0L)
      when(request.bookingType).thenReturn(Some(1))
      when(request.payments).thenReturn(Seq(defaultPayment))
      when(request.bookingPayments).thenReturn(Some(Seq(defaultBookingPayment)))
      when(request.history).thenReturn(Seq(defaultHistory))
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductBookingGroups).thenReturn(None)
      when(request.multiProductInfos).thenReturn(None)
      when(request.userContext).thenReturn(None)

      val expectedResponse = SetStateResponse(
        success = true,
        bookingState = Some(defaultFlightModel),
        errorCode = Some("Flight ErrorCode: BOOKING_NOT_FOUND"),
        errorMessage = Some("Flight ErrorMessage: WARNING: Can't find flights booking with id 11111 and version < 2"),
        activityBookings = Some(Seq(activityBookingState)),
        vehicleBookings = Some(Seq(defaultMockVehicleModel)),
        propertyBookings = Some(
          Seq(
            propertyBookingState.copy(propertyState =
              Some("EisIe7oFJgokMDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAw")
            )
          )
        )
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(service.setPropertyBookingState(propertyBookingState))
        .thenReturn(Future.successful(SetStateResponse(success = true)))

      when(
        flightBookingsService.setFlightBookingState(meq(httpHeader), meq(""), meq(request), meq(true))(
          any[ExecutionContext],
          any[RequestContext]
        )
      ).thenReturn(
        Future.successful(
          SetStateResponse(
            success = true,
            bookingState = Some(defaultFlightModel),
            errorCode = Some("BOOKING_NOT_FOUND"),
            errorMessage = Some("WARNING: Can't find flights booking with id 11111 and version < 2")
          )
        )
      )

      when(
        vehicleBookingService.saveAndReplicateVehicleBookingState(
          any(),
          any(),
          any(),
          any[VehicleBookingState],
          any(),
          any()
        )
      )
        .thenReturn(
          Future.successful(defaultVehicleBookingState)
        )

      when(
        activityService.saveAndReplicateActivityBookingState(
          any(),
          any(),
          any(),
          any[ActivityBookingStateWithItinerary],
          any()
        )
      )
        .thenReturn(
          Future.successful(activityBookingStateWithItinerary)
        )

      handler
        .replicateItineraryBookingState(httpHeader, "", request)
        .map(result => result shouldBe expectedResponse)
    }
  }

  test("replicateFlightBookingStateByItineraryId return error when there's no property and flight in the request") {
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.protectionModels).thenReturn(None)
      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq.empty)
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.cegFastTracks).thenReturn(None)
      when(request.addOns).thenReturn(None)
      when(request.userContext).thenReturn(None)
      when(request.bookingId).thenReturn(1)
      when(request.itinerary).thenReturn(MultiProductItinerary(itineraryId = 1, memberId = 1))

      val expectedResponse = SetStateResponse(
        success = false,
        errorCode = Some("InternalError"),
        errorMessage = Some(
          "Neither property, flight, vehicle, activity, priceFreeze nor cegFastTrack states are updated because they are not existed in the request"
        )
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      handler
        .replicateItineraryBookingState(httpHeader, "", request)
        .map(result => result shouldBe expectedResponse)
    }
  }

  test("setBookingState return error when there's no flights and itinerary data in the request") {
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq.empty)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq.empty)
      when(request.userContext).thenReturn(None)
      when(request.crossProductIsolatedFeature).thenReturn(None)
      when(request.bookingId).thenReturn(1)
      when(request.itinerary).thenReturn(MultiProductItinerary(itineraryId = 1, memberId = 1))

      val expectedResponse =
        SetStateResponse.withError(ResponseErrorCode.InvalidRequest, "flight and property are empty")

      when(validator.validateSetItineraryBookingState(request)).thenReturn(Some(expectedResponse))

      handler
        .setBookingState(httpHeader, "", request)
        .map(result => result shouldBe expectedResponse)
    }
  }

  val vehicleModelInternalMock = VehicleModelInternal(
    vehicleBooking = random[VehicleModelBooking],
    vehicleBookingLocation = VehicleModelBookingPickUpDropOffLocation(
      pickUp = random[VehicleModelBookingLocation],
      dropOff = random[VehicleModelBookingLocation]
    ),
    vehicleBookingSummary = random[VehicleModelBookingSummary],
    vehicleBookingTrip = random[VehicleModelBookingTrip],
    vehicleFinancialBreakdowns = Seq(random[Breakdown]),
    vehicleBookingCancellation = Some(random[VehicleModelBookingCancellation]),
    vehicleInfo = Some(random[VehicleInfo]),
    baseBooking = None
  )

  test("setBookingState with Vehicle") {
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.actionType).thenReturn(ActionType.Created)
      when(request.actionId).thenReturn(123)
      when(request.bookingType).thenReturn(Some(10))
      when(request.schemaVersion).thenReturn("schemaVersion")
      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq.empty)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq.empty)
      when(request.bookingPayments).thenReturn(Some(Seq.empty))
      when(request.vehicle).thenReturn(Some(Seq(vehicleModelInternalMock)))
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductBookingGroups).thenReturn(Some(Seq(multiProductBookingGroup)))
      when(request.userContext).thenReturn(None)
      when(request.crossProductIsolatedFeature).thenReturn(None)

      val vehicleBookingState = VehicleBookingState(
        Seq(vehicleModelInternalMock),
        Seq.empty,
        Seq.empty,
        Seq.empty,
        defaultFlightItinerary,
        expectedMultiProductBookingGroups
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(vehicleBookingService.saveVehicleBookingState(vehicleBookingState))
        .thenReturn(Future.successful(vehicleBookingState))

      val now = DateTime.now

      handler.setBookingState(httpHeader, "", request).map { result =>
        verify(vehicleBookingService).saveVehicleBookingState(vehicleBookingState)
        result.copy(bookingState = result.bookingState.map(_.copy(itineraryDate = now))) shouldBe SetStateResponse(
          success = true,
          bookingState = Some(
            FlightModelInternal(
              actionType = ActionType.Created,
              actionId = 123,
              bookingType = Some(10),
              bookingId = 0,
              schemaVersion = "2",
              itinerary = vehicleBookingState.itinerary,
              payments = vehicleBookingState.payments,
              history = vehicleBookingState.itineraryHistories,
              itineraryDate = now
            )
          ),
          vehicleBookings = Some(vehicleBookingState.vehicleModelsInternal)
        )
      }
    }
  }

  test("saveVehicleBookingState with empty Vehicle in SetStateRequest") {
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.bookingId).thenReturn(10)
      when(request.vehicle).thenReturn(None)
      when(request.itinerary).thenReturn(defaultFlightItinerary)

      handler.asInstanceOf[BookingsHandlerImpl].saveVehicleBookingState(request).failed.map { result =>
        result.getMessage shouldEqual "VehicleModelInternal is not defined for bookingId: 10"
      }
    }
  }

  test("setBookingState with Vehicle failed") {
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq.empty)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq.empty)
      when(request.bookingPayments).thenReturn(Some(Seq.empty))
      when(request.vehicle).thenReturn(Some(Seq(vehicleModelInternalMock)))
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductBookingGroups).thenReturn(Some(Seq(multiProductBookingGroup)))
      when(request.crossProductIsolatedFeature).thenReturn(None)

      val vehicleBookingState = VehicleBookingState(
        Seq(vehicleModelInternalMock),
        Seq.empty,
        Seq.empty,
        Seq.empty,
        defaultFlightItinerary,
        expectedMultiProductBookingGroups
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(vehicleBookingService.saveVehicleBookingState(vehicleBookingState))
        .thenReturn(Future.failed(new Exception("I'm failing")))

      handler.setBookingState(httpHeader, "", request).map { result =>
        verify(vehicleBookingService).saveVehicleBookingState(vehicleBookingState)
        result shouldBe SetStateResponse.withError(ResponseErrorCode.InternalError, "I'm failing")
      }
    }
  }

  test("setBookingState with for rebook and cancel relationship") {
    withHandler() { handler =>
      val request = mock[SetStateRequest]

      val expectedBookingRelationship = BaseBookingRelationshipInternal(
        sourceBookingId = 1,
        targetBookingId = 2,
        relationshipStatusId = RelationshipStatuses.Confirmed.id,
        relationshipTypeId = RelationshipTypes.RebookAndCancel.id,
        recStatus = 5,
        recCreatedWhen = dateTime,
        recCreatedBy = "ABC",
        recModifiedWhen = Some(dateTime.plusDays(1)),
        recModifiedBy = Some("XYZ"),
        relationshipId = 6
      )
      val mockedItineraryModelForRebookAndCancel = defaultFlightModel.copy(
        bookingRelationships = Some(Seq(expectedBookingRelationship)),
        actionId = 123,
        bookingType = Some(10),
        bookingId = 2,
        schemaVersion = "schemaVersion",
        flights = Seq.empty,
        history = Seq.empty,
        payments = Seq.empty,
        slices = Seq.empty,
        segments = Seq.empty,
        passengers = Seq.empty,
        breakdown = Seq.empty,
        breakdownPerPax = Seq.empty,
        baggageAllowance = Seq.empty,
        baggage = Seq.empty,
        segmentInfoByPaxType = Seq.empty,
        paxTickets = Seq.empty,
        summary = Seq.empty,
        userAgent = None,
        bookingAttribution = Seq.empty,
        seatSelections = Seq.empty,
        postBookingFee = Seq.empty
      )

      when(request.actionType).thenReturn(ActionType.Created)
      when(request.actionId).thenReturn(123)
      when(request.bookingType).thenReturn(Some(10))
      when(request.schemaVersion).thenReturn("schemaVersion")
      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq.empty)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq.empty)
      when(request.bookingPayments).thenReturn(Some(Seq.empty))
      when(request.vehicle).thenReturn(Some(Seq.empty))
      when(request.activities).thenReturn(Some(Seq.empty))
      when(request.cegFastTracks).thenReturn(Some(Seq.empty))
      when(request.addOns).thenReturn(Some(Seq.empty))
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductBookingGroups).thenReturn(Some(Seq(multiProductBookingGroup)))
      when(request.userContext).thenReturn(None)
      when(request.crossProductIsolatedFeature).thenReturn(None)
      when(request.bookingRelationships).thenReturn(Some(Seq(expectedBookingRelationship)))
      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)
      when(
        flightBookingsService.setOnlyItineraryBookingState(meq(request), meq(false))(
          any[ExecutionContext],
          any[RequestContext]
        )
      )
        .thenReturn(Future.successful(mockedItineraryModelForRebookAndCancel))

      val now = DateTime.now

      handler.setBookingState(httpHeader, "", request).map { result =>
        val expected = SetStateResponse(
          success = true,
          bookingState = Some(
            FlightModelInternal(
              actionType = ActionType.Created,
              actionId = 123,
              bookingType = Some(10),
              bookingId = 2,
              schemaVersion = "schemaVersion",
              itinerary = defaultFlightItinerary,
              payments = Seq.empty,
              history = Seq.empty,
              itineraryDate = now,
              bookingRelationships = Some(Seq(expectedBookingRelationship))
            )
          )
        )

        val actual = result.copy(bookingState = result.bookingState.map(_.copy(itineraryDate = now)))

        actual shouldBe expected
      }
    }
  }

  val mockPropertyBookingState = PropertyBookingState(123, 2)

  test("setBookingState with Property") {
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.actionType).thenReturn(ActionType.Created)
      when(request.actionId).thenReturn(123)
      when(request.bookingType).thenReturn(Some(1))
      when(request.schemaVersion).thenReturn("schemaVersion")
      when(request.properties).thenReturn(Some(Seq(mockPropertyBookingState)))
      when(request.flights).thenReturn(Seq.empty)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq.empty)
      when(request.activities).thenReturn(Some(Seq.empty))
      when(request.bookingPayments).thenReturn(Some(Seq.empty))
      when(request.vehicle).thenReturn(Some(Seq.empty))
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductBookingGroups).thenReturn(Some(Seq(multiProductBookingGroup)))
      when(request.userContext).thenReturn(None)
      when(request.crossProductIsolatedFeature).thenReturn(None)

      val propertyBookingState = PropertyBookingStateWithItinerary(
        itinerary = defaultFlightItinerary,
        properties = Seq(
          PropertyBookingStateModel(
            bookingId = 123,
            stateId = 2,
            propertyProductModel = Some(
              PropertyProductModel(
                booking = EbePropertyBooking(
                  bookingId = 123
                )
              )
            )
          )
        )
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(propertyService.savePropertyBookingState(any()))
        .thenReturn(Future.successful(propertyBookingState))

      val now = DateTime.now

      handler.setBookingState(httpHeader, "", request).map { result =>
        verify(propertyService).savePropertyBookingState(any())
        result.copy(bookingState = result.bookingState.map(_.copy(itineraryDate = now))) shouldBe SetStateResponse(
          success = true,
          bookingState = Some(
            FlightModelInternal(
              actionType = ActionType.Created,
              actionId = 123,
              bookingType = Some(1),
              bookingId = 0,
              schemaVersion = "2",
              itinerary = propertyBookingState.itinerary,
              payments = Seq.empty,
              history = Seq.empty,
              bookingPayments = Seq.empty,
              itineraryDate = now
            )
          ),
          propertyBookings = Some(
            Seq(
              PropertyBookingState(
                bookingId = mockPropertyBookingState.bookingId,
                stateId = mockPropertyBookingState.stateId,
                propertyState =
                  Some(ProtoConverter.protoToString(propertyBookingState.properties.head.propertyProductModel.get))
              )
            )
          )
        )
      }
    }
  }

  test("savePropertyBookingState with empty Property in SetStateRequest") {
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.bookingId).thenReturn(18)
      when(request.properties).thenReturn(None)
      when(request.itinerary).thenReturn(defaultFlightItinerary)

      handler.asInstanceOf[BookingsHandlerImpl].savePropertyBookingState(request).failed.map { result =>
        result.getMessage shouldEqual "PropertyModelInternal is not defined for bookingId: 18"
      }
    }
  }

  test("setBookingState with Property failed") {
    reset(propertyService)
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.properties).thenReturn(Some(Seq(mockPropertyBookingState)))
      when(request.flights).thenReturn(Seq.empty)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq.empty)
      when(request.bookingPayments).thenReturn(Some(Seq.empty))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(Some(Seq.empty))
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductInfos).thenReturn(Some(Seq(mockMultiProductInfoSetState)))
      when(request.multiProductBookingGroups).thenReturn(Some(Seq(multiProductBookingGroup)))
      when(request.userContext).thenReturn(None)
      when(request.crossProductIsolatedFeature).thenReturn(None)

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(propertyService.savePropertyBookingState(any()))
        .thenReturn(Future.failed(new Exception("I'm failing")))

      handler.setBookingState(httpHeader, "", request).map { result =>
        verify(propertyService).savePropertyBookingState(any())
        result shouldBe SetStateResponse.withError(ResponseErrorCode.InternalError, "I'm failing")
      }
    }
  }

  val mockActivityBookingState = ActivityBookingState("CgQKAgh7", 1)

  test("setBookingState with Activity") {
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.actionType).thenReturn(ActionType.Created)
      when(request.actionId).thenReturn(123)
      when(request.bookingType).thenReturn(Some(10))
      when(request.schemaVersion).thenReturn("schemaVersion")
      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq.empty)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq.empty)
      when(request.bookingPayments).thenReturn(Some(Seq.empty))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(Some(Seq(mockActivityBookingState)))
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductInfos).thenReturn(Some(Seq(mockMultiProductInfoSetState)))
      when(request.multiProductBookingGroups).thenReturn(Some(Seq(multiProductBookingGroup)))
      when(request.crossProductIsolatedFeature).thenReturn(None)
      when(request.bookingRelationships).thenReturn(None)

      val activityBookingState = ActivityBookingStateWithItinerary(
        itinerary = mockItineraryInternalModel.itinerary,
        itineraryHistories = mockItineraryInternalModel.history,
        payments = mockItineraryInternalModel.payments,
        bookingPayments = mockItineraryInternalModel.bookingPayments,
        activities = Seq(mockActivityProductModel),
        multiProductBookingGroups = expectedMultiProductBookingGroups,
        relationships = Nil
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(activityService.saveActivityBookingState(any(), any()))
        .thenReturn(Future.successful(activityBookingState))

      val now = DateTime.now

      handler.setBookingState(httpHeader, "", request).map { result =>
        verify(activityService).saveActivityBookingState(any(), any())
        result.copy(bookingState = result.bookingState.map(_.copy(itineraryDate = now))) shouldBe SetStateResponse(
          success = true,
          bookingState = Some(
            FlightModelInternal(
              actionType = ActionType.Created,
              actionId = 123,
              bookingType = Some(10),
              bookingId = 0,
              schemaVersion = "2",
              itinerary = activityBookingState.itinerary,
              history = activityBookingState.itineraryHistories,
              payments = activityBookingState.payments,
              bookingPayments = activityBookingState.bookingPayments,
              itineraryDate = now
            )
          ),
          activityBookings = Some(
            Seq(
              ActivityBookingState(
                ProtoConverter.protoToString(mockActivityProductModel),
                mockActivityProductModel.product.booking.bookingId
              )
            )
          )
        )
      }
    }
  }

  test("setBookingState with PendingBookingChange") {
    withHandler() { handler =>
      val request                     = mock[SetStateRequest]
      val pendingBookingChangeReqMock = mock[PendingBookingChange]
      val crossProductIsolatedFeatureReqMock = CrossProductIsolatedFeature(
        pendingBookingChange = Some(pendingBookingChangeReqMock)
      )
      val pendingBookingChangeRespMock = mock[PendingBookingChange]

      when(request.actionType).thenReturn(ActionType.flightAggregatedChange)
      when(request.actionId).thenReturn(123)
      when(request.bookingType).thenReturn(None)
      when(request.schemaVersion).thenReturn("schemaVersion")
      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq.empty)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq.empty)
      when(request.bookingPayments).thenReturn(Some(Seq.empty))
      when(request.bookingRelationships).thenReturn(Some(Seq.empty))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.cegFastTracks).thenReturn(None)
      when(request.crossProductIsolatedFeature).thenReturn(Some(crossProductIsolatedFeatureReqMock))
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductInfos).thenReturn(Some(Seq(mockMultiProductInfoSetState)))
      when(request.multiProductBookingGroups).thenReturn(Some(Seq(multiProductBookingGroup)))

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(pendingBookingChangeService.upsertPendingBookingChange(any()))
        .thenReturn(Future.successful(pendingBookingChangeRespMock))

      val now = DateTime.now

      handler.setBookingState(httpHeader, "", request).map { result =>
        verify(pendingBookingChangeService).upsertPendingBookingChange(any())
        result.success shouldBe true
        result.crossProductIsolatedFeature.nonEmpty shouldBe true
        result.crossProductIsolatedFeature.get.pendingBookingChange shouldBe Some(pendingBookingChangeRespMock)
      }
    }
  }

  test("setBookingState with PendingBookingChange failed") {
    withHandler() { handler =>
      val request                     = mock[SetStateRequest]
      val pendingBookingChangeReqMock = mock[PendingBookingChange]
      val crossProductIsolatedFeatureMock = CrossProductIsolatedFeature(
        pendingBookingChange = Some(pendingBookingChangeReqMock)
      )

      when(request.actionType).thenReturn(ActionType.flightAggregatedChange)
      when(request.actionId).thenReturn(123)
      when(request.bookingType).thenReturn(None)
      when(request.schemaVersion).thenReturn("schemaVersion")
      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq.empty)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq.empty)
      when(request.bookingPayments).thenReturn(Some(Seq.empty))
      when(request.bookingRelationships).thenReturn(Some(Seq.empty))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.cegFastTracks).thenReturn(None)
      when(request.crossProductIsolatedFeature).thenReturn(Some(crossProductIsolatedFeatureMock))
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductInfos).thenReturn(Some(Seq(mockMultiProductInfoSetState)))
      when(request.multiProductBookingGroups).thenReturn(Some(Seq(multiProductBookingGroup)))

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(pendingBookingChangeService.upsertPendingBookingChange(any()))
        .thenReturn(Future.failed(new Exception("I'm failing")))

      handler.setBookingState(httpHeader, "", request).map { result =>
        result shouldBe SetStateResponse.withError(
          ResponseErrorCode.InternalError,
          "Failed to save pendingBookingChange with booking: 0 for error: I'm failing caused by: "
        )
      }

    }
  }

  test("setBookingState with CegFastTrack") {
    withHandler() { handler =>
      val request                      = mock[SetStateRequest]
      val mockCegFastTrackBookingState = CegFastTrackBookingState("CgQKAgh7", 1)

      when(request.actionType).thenReturn(ActionType.Created)
      when(request.actionId).thenReturn(123)
      when(request.bookingType).thenReturn(Some(10))
      when(request.schemaVersion).thenReturn("schemaVersion")
      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq.empty)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq.empty)
      when(request.bookingPayments).thenReturn(Some(Seq.empty))
      when(request.bookingRelationships).thenReturn(Some(Seq.empty))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.addOns).thenReturn(None)
      when(request.cegFastTracks).thenReturn(Some(Seq(mockCegFastTrackBookingState)))
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductInfos).thenReturn(Some(Seq(mockMultiProductInfoSetState)))
      when(request.multiProductBookingGroups).thenReturn(Some(Seq(multiProductBookingGroup)))
      when(request.crossProductIsolatedFeature).thenReturn(None)

      val bookingState = CegFastTrackBookingStateWithItinerary(
        itinerary = mockItineraryInternalModel.itinerary,
        itineraryHistories = mockItineraryInternalModel.history,
        payments = mockItineraryInternalModel.payments,
        bookingPayments = mockItineraryInternalModel.bookingPayments,
        relationships = mockItineraryInternalModel.relationships,
        cegFastTracks = Seq(mockCegFastTrackProductModel),
        multiProductBookingGroups = expectedMultiProductBookingGroups
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(cegFastTrackBookingService.saveCegFastTrackBookingState(any(), any()))
        .thenReturn(Future.successful(bookingState))

      val now = DateTime.now

      handler.setBookingState(httpHeader, "", request).map { result =>
        verify(cegFastTrackBookingService).saveCegFastTrackBookingState(any(), any())
        result.copy(bookingState = result.bookingState.map(_.copy(itineraryDate = now))) shouldBe SetStateResponse(
          success = true,
          bookingState = Some(
            FlightModelInternal(
              actionType = ActionType.Created,
              actionId = 123,
              bookingType = Some(10),
              bookingId = 0,
              schemaVersion = "2",
              itinerary = bookingState.itinerary,
              history = bookingState.itineraryHistories,
              payments = bookingState.payments,
              bookingPayments = bookingState.bookingPayments,
              itineraryDate = now,
              bookingRelationships = Some(bookingState.relationships)
            )
          ),
          cegFastTrackBookings = Some(
            Seq(
              CegFastTrackBookingState(
                ProtoConverter.protoToString(mockCegFastTrackProductModel),
                mockCegFastTrackProductModel.product.booking.bookingId
              )
            )
          )
        )
      }
    }
  }

  test("setBookingState with AddOn") {
    withHandler() { handler =>
      val request               = mock[SetStateRequest]
      val mockAddOnBookingState = AddOnBookingState("CgQKAgh7", 1, ProductType.TripProtection.id)

      when(request.actionType).thenReturn(ActionType.Created)
      when(request.actionId).thenReturn(123)
      when(request.bookingType).thenReturn(Some(10))
      when(request.schemaVersion).thenReturn("schemaVersion")
      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq.empty)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq.empty)
      when(request.bookingPayments).thenReturn(Some(Seq.empty))
      when(request.bookingRelationships).thenReturn(Some(Seq.empty))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.cegFastTracks).thenReturn(None)
      when(request.addOns).thenReturn(Some(Seq(mockAddOnBookingState)))
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductInfos).thenReturn(Some(Seq(mockMultiProductInfoSetState)))
      when(request.multiProductBookingGroups).thenReturn(Some(Seq(multiProductBookingGroup)))
      when(request.crossProductIsolatedFeature).thenReturn(None)

      val bookingState = AddOnBookingStateWithItinerary(
        itinerary = mockItineraryInternalModel.itinerary,
        itineraryHistories = mockItineraryInternalModel.history,
        payments = mockItineraryInternalModel.payments,
        bookingPayments = mockItineraryInternalModel.bookingPayments,
        relationships = mockItineraryInternalModel.relationships,
        addOns = Seq(mockProtectionAddOnProductModel),
        multiProductBookingGroups = expectedMultiProductBookingGroups
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(addOnStateService.saveAddOnBookingState(any(), any()))
        .thenReturn(Future.successful(bookingState))

      val now = DateTime.now

      handler.setBookingState(httpHeader, "", request).map { result =>
        verify(addOnStateService).saveAddOnBookingState(any(), any())
        result.copy(bookingState = result.bookingState.map(_.copy(itineraryDate = now))) shouldBe SetStateResponse(
          success = true,
          bookingState = Some(
            FlightModelInternal(
              actionType = ActionType.Created,
              actionId = 123,
              bookingType = Some(10),
              bookingId = 0,
              schemaVersion = "2",
              itinerary = bookingState.itinerary,
              history = bookingState.itineraryHistories,
              payments = bookingState.payments,
              bookingPayments = bookingState.bookingPayments,
              itineraryDate = now,
              bookingRelationships = Some(bookingState.relationships)
            )
          ),
          addOnBookings = Some(
            Seq(
              AddOnBookingState(
                ProtoConverter.protoToString(mockProtectionAddOnProductModel),
                mockProtectionAddOnProductModel.product.booking.bookingId,
                mockProtectionAddOnProductModel.product.booking.productTypeId
              )
            )
          )
        )
      }
    }
  }

  test("setBookingState with CegFastTrack failed") {
    withHandler() { handler =>
      val request                      = mock[SetStateRequest]
      val mockCegFastTrackBookingState = CegFastTrackBookingState("CgQKAgh7", 1)

      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq.empty)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq.empty)
      when(request.bookingPayments).thenReturn(Some(Seq.empty))
      when(request.bookingRelationships).thenReturn(Some(Seq.empty))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.properties).thenReturn(None)
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductInfos).thenReturn(Some(Seq(mockMultiProductInfoSetState)))
      when(request.multiProductBookingGroups).thenReturn(Some(Seq(multiProductBookingGroup)))
      when(request.cegFastTracks).thenReturn(Some(Seq(mockCegFastTrackBookingState)))
      when(request.crossProductIsolatedFeature).thenReturn(None)

      val bookingState = CegFastTrackBookingStateWithItinerary(
        itinerary = mockItineraryInternalModel.itinerary,
        itineraryHistories = mockItineraryInternalModel.history,
        payments = mockItineraryInternalModel.payments,
        bookingPayments = mockItineraryInternalModel.bookingPayments,
        relationships = mockItineraryInternalModel.relationships,
        cegFastTracks = Seq(mockCegFastTrackProductModel),
        multiProductBookingGroups = expectedMultiProductBookingGroups
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)
      when(cegFastTrackBookingService.saveAndReplicateCegFastTrackBookingState(any(), any(), any(), any(), any()))
        .thenReturn(Future.successful(bookingState))
      when(cegFastTrackBookingService.saveCegFastTrackBookingState(any(), any()))
        .thenReturn(Future.failed(new Exception("I'm failing")))

      handler.setBookingState(httpHeader, "", request).map { result =>
        result shouldBe SetStateResponse.withError(ResponseErrorCode.InternalError, "I'm failing")
      }
    }
  }

  test("setBookingState with AddOn failed") {
    withHandler() { handler =>
      val request               = mock[SetStateRequest]
      val mockAddOnBookingState = AddOnBookingState("CgQKAgh7", 1, ProductType.TripProtection.id)

      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq.empty)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq.empty)
      when(request.bookingPayments).thenReturn(Some(Seq.empty))
      when(request.bookingRelationships).thenReturn(Some(Seq.empty))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.properties).thenReturn(None)
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductInfos).thenReturn(Some(Seq(mockMultiProductInfoSetState)))
      when(request.multiProductBookingGroups).thenReturn(Some(Seq(multiProductBookingGroup)))
      when(request.cegFastTracks).thenReturn(None)
      when(request.addOns).thenReturn(Some(Seq(mockAddOnBookingState)))
      when(request.crossProductIsolatedFeature).thenReturn(None)

      val bookingState = AddOnBookingStateWithItinerary(
        itinerary = mockItineraryInternalModel.itinerary,
        itineraryHistories = mockItineraryInternalModel.history,
        payments = mockItineraryInternalModel.payments,
        bookingPayments = mockItineraryInternalModel.bookingPayments,
        relationships = mockItineraryInternalModel.relationships,
        addOns = Seq(mockProtectionAddOnProductModel),
        multiProductBookingGroups = expectedMultiProductBookingGroups
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)
      when(addOnStateService.saveAndReplicateAddOnBookingState(any(), any(), any(), any(), any()))
        .thenReturn(Future.successful(bookingState))
      when(addOnStateService.saveAddOnBookingState(any(), any()))
        .thenReturn(Future.failed(new Exception("I'm failing")))

      handler.setBookingState(httpHeader, "", request).map { result =>
        result shouldBe SetStateResponse.withError(ResponseErrorCode.InternalError, "I'm failing")
      }
    }
  }

  test("replicateBookingState with Activity") {
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.protectionModels).thenReturn(None)
      when(request.actionType).thenReturn(ActionType.Created)
      when(request.actionId).thenReturn(123)
      when(request.bookingType).thenReturn(Some(10))
      when(request.schemaVersion).thenReturn("schemaVersion")
      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq.empty)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq.empty)
      when(request.bookingPayments).thenReturn(Some(Seq.empty))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(Some(Seq(mockActivityBookingState)))
      when(request.cegFastTracks).thenReturn(None)
      when(request.addOns).thenReturn(None)
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductInfos).thenReturn(Some(Seq(mockMultiProductInfoSetState)))
      when(request.multiProductBookingGroups).thenReturn(Some(Seq(multiProductBookingGroup)))
      when(request.userContext).thenReturn(None)
      when(request.bookingRelationships).thenReturn(Some(Seq.empty))

      val activityBookingState = ActivityBookingStateWithItinerary(
        itinerary = mockItineraryInternalModel.itinerary,
        itineraryHistories = mockItineraryInternalModel.history,
        payments = mockItineraryInternalModel.payments,
        bookingPayments = mockItineraryInternalModel.bookingPayments,
        activities = Seq(mockActivityProductModel),
        multiProductBookingGroups = expectedMultiProductBookingGroups,
        relationships = Nil
      )

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(activityService.saveAndReplicateActivityBookingState(any(), any(), any(), any(), any()))
        .thenReturn(Future.successful(activityBookingState))

      val now = DateTime.now

      handler.replicateItineraryBookingState(httpHeader, "", request).map { result =>
        verify(activityService).saveAndReplicateActivityBookingState(any(), any(), any(), any(), any())
        result.copy(bookingState = result.bookingState.map(_.copy(itineraryDate = now))) shouldBe SetStateResponse(
          success = true,
          bookingState = Some(
            FlightModelInternal(
              actionType = ActionType.Created,
              actionId = 123,
              bookingType = Some(10),
              bookingId = 0,
              schemaVersion = "2",
              itinerary = activityBookingState.itinerary,
              history = activityBookingState.itineraryHistories,
              payments = activityBookingState.payments,
              bookingPayments = activityBookingState.bookingPayments,
              itineraryDate = now
            )
          ),
          activityBookings = Some(
            Seq(
              ActivityBookingState(
                ProtoConverter.protoToString(mockActivityProductModel),
                mockActivityProductModel.product.booking.bookingId
              )
            )
          )
        )
      }
    }
  }

  test("saveActivityBookingState with empty activity in SetStateRequest") {
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.bookingId).thenReturn(10)
      when(request.activities).thenReturn(None)
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductInfos).thenReturn(Some(Seq(mockMultiProductInfoSetState)))
      when(request.userContext).thenReturn(None)

      handler.asInstanceOf[BookingsHandlerImpl].saveActivityBookingState(request).failed.map { result =>
        result.getMessage shouldEqual "ActivityBookingState is not defined for bookingId: 10"
      }
    }
  }

  test("saveAddOnBookingState with empty addons in SetStateRequest") {
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.bookingId).thenReturn(10)
      when(request.addOns).thenReturn(None)
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductInfos).thenReturn(Some(Seq(mockMultiProductInfoSetState)))
      when(request.userContext).thenReturn(None)

      handler.asInstanceOf[BookingsHandlerImpl].saveAddOnBookingState(request).failed.map { result =>
        result.getMessage shouldEqual "AddOnBookingState is not defined"
      }
    }
  }

  test("setBookingState with Activity failed") {
    reset(activityService)
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq.empty)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq.empty)
      when(request.bookingPayments).thenReturn(Some(Seq.empty))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(Some(Seq(mockActivityBookingState)))
      when(request.cegFastTracks).thenReturn(None)
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.multiProductInfos).thenReturn(Some(Seq(mockMultiProductInfoSetState)))
      when(request.multiProductBookingGroups).thenReturn(Some(Seq(multiProductBookingGroup)))
      when(request.userContext).thenReturn(None)
      when(request.crossProductIsolatedFeature).thenReturn(None)
      when(request.bookingRelationships).thenReturn(None)

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)

      when(activityService.saveActivityBookingState(any(), any()))
        .thenReturn(Future.failed(new Exception("I'm failing")))

      handler.setBookingState(httpHeader, "", request).map { result =>
        verify(activityService).saveActivityBookingState(any(), any())
        result shouldBe SetStateResponse.withError(ResponseErrorCode.InternalError, "I'm failing")
      }
    }
  }

  List(None, Some(List.empty[VehicleModelInternal])).foreach { vehicles =>
    test(
      s"setBookingState return success when there's no flights, vehicles, activities pricefreeze, and cegFastTracks is ${vehicles} but has itinerary data in the request"
    ) {
      withHandler() { handler =>
        val request = mock[SetStateRequest]
        when(request.properties).thenReturn(None)
        when(request.flights).thenReturn(Seq.empty)
        when(request.vehicle).thenReturn(vehicles)
        when(request.itinerary).thenReturn(defaultFlightItinerary)
        when(request.history).thenReturn(Seq(activityDefaultHistory))
        when(request.payments).thenReturn(Seq(activityDefaultPayment))
        when(request.activities).thenReturn(None)
        when(request.cegFastTracks).thenReturn(None)
        when(request.addOns).thenReturn(None)
        when(request.userContext).thenReturn(None)
        when(request.crossProductIsolatedFeature).thenReturn(None)

        val expectedResponse = SetStateResponse.withSuccess(mockedItineraryModel)

        when(validator.validateSetItineraryBookingState(request)).thenReturn(None)
        when(
          flightBookingsService.setOnlyItineraryBookingState(meq(request), meq(false))(
            any[ExecutionContext],
            any[RequestContext]
          )
        )
          .thenReturn(Future.successful(mockedItineraryModel))

        handler
          .setBookingState(httpHeader, "", request)
          .map(result => result shouldBe expectedResponse)
      }
    }
  }

  test("setBookingState return success when there are flights data in the request") {
    withHandler() { handler =>
      val request = mock[SetStateRequest]
      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq(defaultFlightBooking))
      when(request.itinerary).thenReturn(defaultFlightItinerary)
      when(request.history).thenReturn(Seq(activityDefaultHistory))
      when(request.payments).thenReturn(Seq(activityDefaultPayment))
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.cegFastTracks).thenReturn(None)
      when(request.addOns).thenReturn(None)
      when(request.crossProductIsolatedFeature).thenReturn(None)
      when(request.userContext).thenReturn(None)

      val expectedResponse = SetStateResponse.withSuccess(mockedItineraryModel)

      when(validator.validateSetItineraryBookingState(request)).thenReturn(None)
      when(
        flightBookingsService.setFlightBookingState(meq(httpHeader), meq(""), meq(request), meq(false))(
          any[ExecutionContext],
          any[RequestContext]
        )
      ).thenReturn(Future.successful(expectedResponse))

      handler
        .setBookingState(httpHeader, "", request)
        .map(result => result shouldBe expectedResponse)
    }
  }

  // Section: getItineraryStatus

  test("getItineraryStatus should return a Status response") {
    withHandler() { handler =>
      val token = StatusToken(
        itineraryId = 1,
        actionId = 1,
        productType = Set(),
        dc = ServerUtils.serverDc()
      )

      val request = GetStatusRequest(
        statusToken = token.serialize(logBookingCreationLogMessageBaseStub, measureStub),
        correlationId = Option("Correlation-ID"),
        userContext = None,
        bookingContext = None
      )

      val response = GetStatusResponse(
        success = true,
        itinerary = Option(
          Itinerary(
            itineraryId = 1,
            flights = Seq(
              FlightBooking(
                bookingStatus = CreatedBookingStatus.BookingConfirmed
              )
            ),
            bookings = Seq(
              HotelBooking(
                bookingStatus = CreatedBookingStatus.BookingRejected
              )
            )
          )
        )
      )

      val bookingActions = Seq(masterBookingAction, productBookingAction)

      when(commonService.getBookingActionsByItineraryId(token.itineraryId))
        .thenReturn(Future.successful(bookingActions))
      when(commonService.getItineraryStatus(request, bookingActions))
        .thenReturn(Future.successful(response))

      handler
        .getItineraryStatus(httpHeader, "", request)
        .map(result => {
          result shouldBe response
          verify(commonService, times(1)).getBookingActionsByItineraryId(token.itineraryId)
          verifyNoInteractions(bcreProxy)
          succeed
        })

    }
  }

  test("getItineraryStatus should return a Status response and get the bookingSessionId from BookingActionState") {
    withHandler() { handler =>
      val token = StatusToken(
        itineraryId = 1,
        actionId = 1,
        productType = Set(),
        dc = ServerUtils.serverDc()
      )

      val request = GetStatusRequest(
        statusToken = token.serialize(logBookingCreationLogMessageBaseStub, measureStub),
        correlationId = Option("Correlation-ID"),
        userContext = None,
        bookingContext = None
      )

      val response = GetStatusResponse(
        success = true,
        itinerary = Option(
          Itinerary(
            itineraryId = 1,
            flights = Seq(
              FlightBooking(
                bookingStatus = CreatedBookingStatus.BookingConfirmed
              )
            ),
            bookings = Seq(
              HotelBooking(
                bookingStatus = CreatedBookingStatus.BookingRejected
              )
            )
          )
        )
      )

      val bookingActions = Seq(masterBookingAction, productBookingAction)

      when(commonService.getItineraryStatus(request, bookingActions)).thenReturn(Future.successful(response))
      when(commonService.getBookingActionsByItineraryId(token.itineraryId))
        .thenReturn(Future.successful(bookingActions))

      handler
        .getItineraryStatus(httpHeader, "", request)
        .map(result => {
          result shouldBe response
          verifyNoInteractions(bcreProxy)
          succeed
        })

    }
  }

  test(
    "getItineraryStatus should return a Status response and get the bookingSessionId from the BookingContext"
  ) {
    withHandler() { handler =>
      val token = StatusToken(
        itineraryId = 1,
        actionId = 1,
        productType = Set(),
        dc = ServerUtils.serverDc()
      )

      val request = GetStatusRequest(
        statusToken = token.serialize(logBookingCreationLogMessageBaseStub, measureStub),
        correlationId = Option("Correlation-ID"),
        userContext = None,
        bookingContext = None
      )

      val response = GetStatusResponse(
        success = true,
        itinerary = Option(
          Itinerary(
            itineraryId = 1,
            flights = Seq(
              FlightBooking(
                bookingStatus = CreatedBookingStatus.BookingConfirmed
              )
            ),
            bookings = Seq(
              HotelBooking(
                bookingStatus = CreatedBookingStatus.BookingRejected
              )
            )
          )
        )
      )

      val bookingActions = Seq.empty
      when(commonService.getBookingActionsByItineraryId(token.itineraryId))
        .thenReturn(Future.successful(bookingActions))
      when(commonService.getItineraryStatus(request, bookingActions)).thenReturn(Future.successful(response))

      handler
        .getItineraryStatus(httpHeader, "", request)
        .map(result => {
          result shouldBe response
          verify(bcreProxy, times(0)).getItineraryStatus(any(), any())(any())
          succeed
        })

    }
  }

  test("getItineraryStatus should call hadoop logging") {
    withHandler() { handler =>
      val token = StatusToken(
        itineraryId = 1,
        actionId = 1,
        productType = Set(),
        dc = ServerUtils.serverDc()
      )

      val featureAwareMock = mock[FeatureAware]
      val clientId         = 61
      val correlationId    = Option("Correlation-ID")
      val updatedContext =
        context.copy(
          featureAware = Some(featureAwareMock),
          clientId = clientId,
          correlationId = correlationId,
          pollingId = Some("test-polling-id")
        )
      when(
        contextBuilder.build(any[BookingCreationFlowContext], any[HttpHeaderBase], any[Option[String]], any(), any())
      )
        .thenReturn(Future.successful(updatedContext))
      val updatedHeader = httpHeader.copy(clientId = clientId)

      val request = GetStatusRequest(
        statusToken = token.serialize(logBookingCreationLogMessageBaseStub, measureStub),
        correlationId = correlationId,
        userContext = None,
        bookingContext = None,
        pollingId = Some("test-polling-id")
      )

      val response = GetStatusResponse(
        success = true,
        itinerary = Option(
          Itinerary(
            itineraryId = 1,
            flights = Seq(
              FlightBooking(
                bookingStatus = CreatedBookingStatus.BookingConfirmed
              )
            ),
            bookings = Seq(
              HotelBooking(
                bookingStatus = CreatedBookingStatus.BookingRejected
              )
            )
          )
        )
      )
      val bookingActions = Seq.empty
      when(commonService.getItineraryStatus(request, bookingActions)(updatedContext))
        .thenReturn(Future.successful(response))
      when(commonService.getBookingActionsByItineraryId(token.itineraryId))
        .thenReturn(Future.successful(bookingActions))
      handler
        .getItineraryStatus(updatedHeader, "", request)
        .map(result => {
          result shouldBe response
          verify(bcreProxy, times(0)).getItineraryStatus(any(), any())(any())
          verify(hadoopMessaging, times(1)).sendBookingGetStatusLogMessage(
            processName = HadoopMessageProcessName.GetItineraryStatusRequest,
            correlationId = correlationId.get,
            clientId = clientId,
            request = request,
            prebookingId = None,
            bookingSessionId = None,
            pollingId = Some("test-polling-id")
          )

          verify(hadoopMessaging, times(1)).sendBookingGetStatusResponseLogMessage(
            correlationId = correlationId.get,
            clientId = clientId,
            request = result,
            bookingSessionId = None,
            pollingId = Some("test-polling-id")
          )
          succeed
        })

    }
  }

  test("getItineraryStatus should call bapi localproxy if the token was created in a different dc") {
    withHandler() { handler =>
      val token = StatusToken(
        itineraryId = 1,
        actionId = 1,
        productType = Set(),
        dc = "xyz"
      )

      val request = GetStatusRequest(
        statusToken = token.serialize(logBookingCreationLogMessageBaseStub, measureStub),
        correlationId = Option("Correlation-ID"),
        userContext = None,
        bookingContext = None
      )

      val response = GetStatusResponse(
        success = true,
        itinerary = Option(
          Itinerary(
            itineraryId = 1,
            flights = Seq(
              FlightBooking(
                bookingStatus = CreatedBookingStatus.BookingConfirmed
              )
            ),
            bookings = Seq(
              HotelBooking(
                bookingStatus = CreatedBookingStatus.BookingRejected
              )
            )
          )
        )
      )

      when(bcreProxy.getItineraryStatus(any(), any())(any())).thenReturn(Future.successful(response))

      handler
        .getItineraryStatus(httpHeader, "", request)
        .map(result => {
          result shouldBe response
          verify(commonService, times(0)).getBookingActionsByItineraryId(any())
          verify(bcreProxy, times(1)).getItineraryStatus(request, "xyz")(context)
          succeed
        })

    }
  }

  test("getItineraryStatus should return a bad request if the request is malformed") {
    withHandler() { handler =>
      val token = StatusToken(
        itineraryId = 1,
        actionId = 1,
        productType = Set(),
        dc = ServerUtils.serverDc()
      )

      val statusToken = token.serialize(logBookingCreationLogMessageBaseStub, measureStub)

      val expectedItinerary = Itinerary(
        statusToken = statusToken
      )

      val request = GetStatusRequest(
        statusToken = statusToken,
        correlationId = Option("Correlation-ID"),
        userContext = None,
        bookingContext = None
      )

      when(commonService.getBookingActionsByItineraryId(token.itineraryId)).thenReturn(Future.successful(Seq.empty))
      when(commonService.getItineraryStatus(request, Seq.empty)).thenReturn(Future.failed(new IllegalArgumentException))

      handler
        .getItineraryStatus(httpHeader, "", request)
        .map(result => {
          result.success shouldBe false
          result.status shouldBe BookingServerStatus.InvalidRequest
          result.itinerary shouldBe Some(expectedItinerary)
        })

    }
  }

  test("getItineraryStatus should return an internal error if there are any exceptions") {
    withHandler() { handler =>
      val token = StatusToken(
        itineraryId = 1,
        actionId = 1,
        productType = Set(),
        dc = ServerUtils.serverDc()
      )

      val statusToken = token.serialize(logBookingCreationLogMessageBaseStub, measureStub)

      val expectedItinerary = Itinerary(
        statusToken = statusToken
      )

      val request = GetStatusRequest(
        statusToken = statusToken,
        correlationId = Option("Correlation-ID"),
        userContext = None,
        bookingContext = None
      )

      when(commonService.getBookingActionsByItineraryId(token.itineraryId)).thenReturn(Future.successful(Seq.empty))
      when(commonService.getItineraryStatus(request, Seq.empty)).thenReturn(Future.failed(new Exception()))

      handler
        .getItineraryStatus(httpHeader, "", request)
        .map(result => {
          result.success shouldBe false
          result.status shouldBe BookingServerStatus.Error
          result.itinerary shouldBe Some(expectedItinerary)
        })
    }
  }

  test("getAdditionalTags with DevicePlatform correctly") {
    withHandler() { handler =>
      val token = StatusToken(
        itineraryId = 1,
        actionId = 1,
        productType = Set(),
        dc = ServerUtils.serverDc()
      )
      val request = GetStatusRequest(
        deviceContext = Some(DeviceContext(deviceTypeId = DevicePlatform.AppIPhone, deviceId = Some("deviceId"))),
        statusToken = token.serialize(logBookingCreationLogMessageBaseStub, measureStub),
        correlationId = Option("Correlation-ID"),
        userContext = None,
        bookingContext = None
      )

      val response = GetStatusResponse(true)

      val result = handler.asInstanceOf[BookingsHandlerImpl].getAdditionalTags(request, Some(response))
      assert(result.get("device_type") == Some("AppIPhone"))
      assert(result.get("statusToken_dc") == Some("dev"))
    }
  }

  test("getAdditionalTags is_same_dc should be true for same dc") {
    withHandler() { handler =>
      val token = StatusToken(
        itineraryId = 1,
        actionId = 1,
        productType = Set(),
        dc = ServerUtils.serverDc()
      )
      val request = GetStatusRequest(
        deviceContext = Some(DeviceContext(deviceTypeId = DevicePlatform.AppIPhone, deviceId = Some("deviceId"))),
        statusToken = token.serialize(logBookingCreationLogMessageBaseStub, measureStub),
        correlationId = Option("Correlation-ID"),
        userContext = None,
        bookingContext = None
      )

      val response = GetStatusResponse(true)

      val result = handler.asInstanceOf[BookingsHandlerImpl].getAdditionalTags(request, Some(response))
      assert(result.get("is_same_dc") == Some("true"))
    }
  }

  test("getAdditionalTags is_same_dc should be false for different dc") {
    withHandler() { handler =>
      val token = StatusToken(
        itineraryId = 1,
        actionId = 1,
        productType = Set(),
        dc = "sg"
      )
      val request = GetStatusRequest(
        deviceContext = Some(DeviceContext(deviceTypeId = DevicePlatform.AppIPhone, deviceId = Some("deviceId"))),
        statusToken = token.serialize(logBookingCreationLogMessageBaseStub, measureStub),
        correlationId = Option("Correlation-ID"),
        userContext = None,
        bookingContext = None
      )

      val response = GetStatusResponse(true)

      val result = handler.asInstanceOf[BookingsHandlerImpl].getAdditionalTags(request, Some(response))
      assert(result.get("is_same_dc") == Some("false"))
    }
  }
}
