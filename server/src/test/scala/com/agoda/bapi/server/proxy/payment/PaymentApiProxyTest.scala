package com.agoda.bapi.server.proxy.payment

import akka.actor.{ActorSystem, Scheduler}
import com.agoda.bapi.common.config.Configuration
import com.agoda.bapi.common.model.WhiteLabelInfo
import com.agoda.bapi.common.{BcreMetricReporter, WithProxyMessageTestMock}
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.paymentapiv2.client.v2.common.api.{GatewayApi, PaymentNonPciApi}
import com.agoda.paymentapiv2.client.v2.common.model._
import org.mockito.ArgumentMatchers.{any, argThat, notNull}
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future

class PaymentApiProxyTest extends AsyncWordSpec with WithProxyMessageTestMock with BeforeAndAfter with MockitoSugar {
  implicit private val actorSystem = mock[ActorSystem]
  val mockGatewayClient            = mock[GatewayApi[Future]]
  val mockPaymentNonPciClient      = mock[PaymentNonPciApi[Future]]
  var paymentApiProxy: PaymentApiProxy =
    new PaymentApiProxyImpl(mockGatewayClient, mockPaymentNonPciClient, 1, messagingService)

  val whiteLabelInfo            = mock[WhiteLabelInfo]
  private val scheduler         = mock[Scheduler]
  val reporter: MetricsReporter = mock[MetricsReporter]
  private val mockConfiguration = mock[Configuration]

  val mockWhitelabelToken = "mock-whitelabel-token"

  when(whiteLabelInfo.token).thenReturn(Some(mockWhitelabelToken))
  when(requestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
  when(actorSystem.scheduler).thenReturn(scheduler)

  before {
    reset(mockGatewayClient, mockConfiguration, reporter)
    BcreMetricReporter._reporter = reporter
  }

  "gatewayGetBinRange" should {
    "call GatewayClient.gatewayGetBinRangeAsync" in {
      val creditCardInfo = Some(
        CreditCardInfo(
          CCId = Some(123),
          CardTypeId = Some(1),
          CardHolderName = Some("Test User"),
          CardExpMonth = Some("10"),
          CardExpYear = Some("2018"),
          CardNumber = Some("123"),
          Cvv = Some("893")
        )
      )

      val request = GetBinRangeRequest(CreditCardInfo = creditCardInfo)

      val response = mock[BinRangeInfo]
      when(
        mockGatewayClient.gatewayGetBinRange(any[GetBinRangeRequest], any[Option[String]], any[Map[String, String]])(
          any()
        )
      )
        .thenReturn(Future.successful(response))
      paymentApiProxy.gatewayGetBinRange(request).map { _ =>
        verify(mockGatewayClient)
          .gatewayGetBinRange(any[GetBinRangeRequest], any[Option[String]], any[Map[String, String]])(any())
        succeed
      }
    }

    "call GatewayClient.gatewayGetBinRangeAsync with X-WhiteLabel-Token header" in {
      val creditCardInfo = Some(
        CreditCardInfo(
          CCId = Some(123),
          CardTypeId = Some(1),
          CardHolderName = Some("Test User"),
          CardExpMonth = Some("10"),
          CardExpYear = Some("2018"),
          CardNumber = Some("123"),
          Cvv = Some("893")
        )
      )

      val request = GetBinRangeRequest(CreditCardInfo = creditCardInfo)

      val response = mock[BinRangeInfo]
      when(
        mockGatewayClient.gatewayGetBinRange(any[GetBinRangeRequest], any[Option[String]], any[Map[String, String]])(
          any()
        )
      )
        .thenReturn(Future.successful(response))
      paymentApiProxy.gatewayGetBinRange(request).map { _ =>
        verify(mockGatewayClient).gatewayGetBinRange(
          any[GetBinRangeRequest],
          any[Option[String]],
          argThat { (map: Map[String, String]) =>
            map.contains("X-WhiteLabel-Token")
          }
        )(any())
        succeed
      }
    }
  }

  "getInstallmentData" should {
    "not call GatewayClient.gatewayGetInstallmentData when currency is empty" in {
      val request = GetInstallmentDataRequest(Amount = Some(10), Currency = None)

      paymentApiProxy.getInstallmentData(request).map { result =>
        verifyNoInteractions(mockGatewayClient)
        assertResult(result)(InstallmentDataResponse(Some(List.empty), Some(List.empty)))
      }
    }

    "not call GatewayClient.gatewayGetInstallmentData when amount is zero" in {
      val request = GetInstallmentDataRequest(Amount = Some(0.0), Currency = Some("USD"))

      paymentApiProxy.getInstallmentData(request).map { result =>
        verifyNoInteractions(mockGatewayClient)
        assertResult(result)(InstallmentDataResponse(Some(List.empty), Some(List.empty)))
      }
    }

    "call GatewayClient.gatewayGetInstallmentData" in {
      val request = GetInstallmentDataRequest(Amount = Some(10), Currency = Some("USD"))

      when(mockGatewayClient.gatewayGetInstallmentData(any[GetInstallmentDataRequest], any(), any())(any()))
        .thenReturn(
          Future.successful(
            InstallmentDataResponse(
              InstallmentPlans = Some(List.empty[InstallmentPlan]),
              InstallmentAvailableProviders = Some(List.empty[InstallmentAvailableProvider])
            )
          )
        )

      paymentApiProxy.getInstallmentData(request).map { result =>
        verify(mockGatewayClient).gatewayGetInstallmentData(any[GetInstallmentDataRequest], any(), any())(any())
        assertResult(result)(InstallmentDataResponse(Some(List.empty), Some(List.empty)))
      }
    }

    "return empty InstallmentDataResponse when GatewayClient.gatewayGetInstallmentData fails" in {
      val request = GetInstallmentDataRequest(Amount = Some(10), Currency = Some("USD"))

      when(mockGatewayClient.gatewayGetInstallmentData(any[GetInstallmentDataRequest], any(), any())(any()))
        .thenReturn(Future.failed(new Exception("error")))

      paymentApiProxy.getInstallmentData(request).map { result =>
        verify(mockGatewayClient, times(2))
          .gatewayGetInstallmentData(any[GetInstallmentDataRequest], any(), any())(any())
        assertResult(result)(InstallmentDataResponse(Some(List.empty), Some(List.empty)))
      }
    }

    "call GatewayClient.gatewayGetInstallmentData with headers when enableCapiSessionTokenSanitization is B" in {
      val request = GetInstallmentDataRequest(Amount = Some(10), Currency = Some("USD"))

      when(mockGatewayClient.gatewayGetInstallmentData(any[GetInstallmentDataRequest], any(), any())(any()))
        .thenReturn(
          Future.successful(
            InstallmentDataResponse(
              InstallmentPlans = Some(List.empty[InstallmentPlan]),
              InstallmentAvailableProviders = Some(List.empty[InstallmentAvailableProvider])
            )
          )
        )
      when(requestContext.featureAware.exists(_.enableCapiSessionTokenSanitization)).thenReturn(true)

      paymentApiProxy.getInstallmentData(request).map { result =>
        verify(mockGatewayClient).gatewayGetInstallmentData(any[GetInstallmentDataRequest], any(), notNull())(any())
        assertResult(result)(InstallmentDataResponse(Some(List.empty), Some(List.empty)))
      }
    }

    "call GatewayClient.gatewayGetInstallmentData with headers when enableCapiSessionTokenSanitization is A" in {
      val request = GetInstallmentDataRequest(Amount = Some(10), Currency = Some("USD"))

      when(mockGatewayClient.gatewayGetInstallmentData(any[GetInstallmentDataRequest], any(), any())(any()))
        .thenReturn(
          Future.successful(
            InstallmentDataResponse(
              InstallmentPlans = Some(List.empty[InstallmentPlan]),
              InstallmentAvailableProviders = Some(List.empty[InstallmentAvailableProvider])
            )
          )
        )
      when(requestContext.featureAware.exists(_.enableCapiSessionTokenSanitization)).thenReturn(false)

      paymentApiProxy.getInstallmentData(request).map { result =>
        verify(mockGatewayClient).gatewayGetInstallmentData(any[GetInstallmentDataRequest], any(), notNull())(any())
        assertResult(result)(InstallmentDataResponse(Some(List.empty), Some(List.empty)))
      }
    }
  }
}
