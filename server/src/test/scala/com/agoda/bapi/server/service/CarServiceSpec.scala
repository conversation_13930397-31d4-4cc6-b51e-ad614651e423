package com.agoda.bapi.server.service

import com.agoda.bapi.common.message.setupBooking.{CarConfirmPriceRequest, CarRequestItem}
import com.agoda.bapi.common.model.car.{CarConfirmationData, CarOptions}
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{UserContext, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.service.MessagesBag
import com.agoda.bapi.common.token.car.CarTokenService
import com.agoda.bapi.creation.service.observability.LogContext
import com.agoda.bapi.server.handler.context.{SetupBookingContext, SetupBookingSessionContext}
import com.agoda.bapi.server.proxy.VehicleSearchApiClientProxy
import com.agoda.mpb.common.header.AgHttpHeader
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import mocks.RequestContextMock
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec

class CarServiceSpec extends AsyncWordSpec with MockitoSugar with Matchers with BeforeAndAfter with RequestContextMock {

  val vehicleApiProxyMock = mock[VehicleSearchApiClientProxy]
  val carTokenServiceMock = mock[CarTokenService]
  val carServiceImpl      = new CarServiceImpl(vehicleApiProxyMock, carTokenServiceMock)

  val mockUserContext = UserContext(
    languageId = 1,
    requestOrigin = "TH",
    currency = "USD",
    nationalityId = 106
  )

  val mockWhiteLabelInfo = WhiteLabelInfo(
    WhiteLabel.Agoda,
    FeaturesConfiguration()
  )

  val mockCarConfirmRequest     = CarConfirmPriceRequest(identifier = None, searchToken = "searchToken", None)
  val mockCarRequestItem        = CarRequestItem("1", mockCarConfirmRequest)
  val mockEmptyCarRequestItem   = None
  val agEnvHeader: AgHttpHeader = AgHttpHeader("ag-env", "uat")
  var requestContextTest        = requestContext(mock[MessagesBag], Some(mockUserContext))
  val mockSetupBookingContext = SetupBookingContext(
    requestContextTest.copy(agHeaders = Seq(agEnvHeader)),
    "cid",
    mockWhiteLabelInfo,
    None,
    BookingFlow.SingleVehicle,
    SetupBookingSessionContext(),
    "sid",
    "",
    LogContext(),
    None
  )
  val mockCarOption = mock[CarOptions]

  val mockCarConfirmationData = CarConfirmationData(
    "1",
    "content",
    None,
    true,
    true,
    mockCarOption,
    None,
    None
  )

  before {
    reset(vehicleApiProxyMock)
  }

  "getCarConfirmationData" should {

    "Return data correctly" in {
      when(vehicleApiProxyMock.getCarConfirmationData(any(), any(), any())(any()))
        .thenReturn(Future.successful(mockCarConfirmationData))

      carServiceImpl
        .getCarConfirmationData(Some(mockCarRequestItem), "1", "USD", None)(mockSetupBookingContext)
        .map(response => {
          response.toCarProductItem.id shouldBe mockCarConfirmationData.id
          response.toCarProductItem.content shouldBe mockCarConfirmationData.content
        })

    }

    "throw exception when there is no car item" in {
      when(vehicleApiProxyMock.getCarConfirmationData(any(), any(), any())(any()))
        .thenReturn(Future.successful(mockCarConfirmationData))

      carServiceImpl
        .getCarConfirmationData(
          carRequestItem = mockEmptyCarRequestItem,
          requestId = "1",
          chargeCurrency = "USD",
          loyaltyRequestOpt = None
        )(mockSetupBookingContext)
        .failed
        .map(failure => failure shouldBe an[Exception])
    }
  }

  "composeVehicleRequestContext" should {

    "compose header correctly" in {
      val resultContext = carServiceImpl.composeVehicleRequestContext(mockSetupBookingContext).get

      resultContext.languageId shouldBe "1"
      resultContext.locale shouldBe "en-us"
      resultContext.whitelabelId shouldBe "1"
      resultContext.origin shouldBe "TH"
      resultContext.requestedCurrency shouldBe "USD"
      resultContext.sessionId shouldBe "sid"
      resultContext.correlationId shouldBe "cid"
      resultContext.environment shouldBe Some("uat")
    }
  }

}
