package com.agoda.bapi.server.service

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message._
import com.agoda.bapi.common.message.creation.CreatedBookingStatus.CreatedBookingStatus
import com.agoda.bapi.common.message.multi.product.{PropertyBookingState, SetStateResponse}
import com.agoda.bapi.common.message.pricebreakdown.{PriceBreakdownNode, PriceBreakdownResponse, PriceBreakdownType}
import com.agoda.bapi.common.message.setupBooking.{PaymentRequest, SetupBookingRequest}
import com.agoda.bapi.common.model.WhiteLabel.WhiteLabel
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{ChargeOption, CurrencyInfo, InstallmentDataResponse => InstallmentDataResponseCommon, InstallmentPlan => Installment<PERSON>lan<PERSON>ommon, LanguageID, WhiteLabel, _}
import com.agoda.bapi.common.repository.CountriesRepository
import com.agoda.bapi.common.service.{FeatureAware, MessagesBag}
import com.agoda.bapi.creation.repository.EbeLiteBookingRepository
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model._
import com.agoda.bapi.server.proxy.ServerBFDBProxy
import com.agoda.bapi.server.repository._
import com.agoda.bapi.server.service.payment.model.SelectedPaymentMethod
import com.agoda.paymentapiv2.client.v2.common.model.{GetInstallmentDataRequest, InstallmentAvailableProvider, InstallmentDataResponse, InstallmentPlan, PartnerSpecificParams, SetupPaymentResponse}
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import com.agoda.winterfell.common.ExternalResponseCode
import com.agoda.winterfell.unified.{PartnerClaim, SSOMemberDetail}
import generated.model.OverrideCreditCardPayment
import mocks.{CountriesMock, RequestContextMock}
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.funsuite.AsyncFunSuite
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.TableDrivenPropertyChecks._
import org.scalatestplus.mockito.MockitoSugar

import java.util.UUID
import scala.concurrent.Future

class BookingsServiceTests
    extends AsyncFunSuite
    with MockitoSugar
    with BeforeAndAfter
    with RequestContextMock
    with Matchers
    with CountriesMock {
  private val bookingInfoMessageService           = mock[MessagesBag]
  private val bfdbProxy                           = mock[ServerBFDBProxy]
  private val ebeliteRepo                         = mock[EbeLiteBookingRepository]
  private val paymentApiRepo                      = mock[PaymentApiRepository]
  private val currenciesRepo                      = mock[CurrenciesRepository]
  private val userContext                         = Some(UserContextMock.value.copy(requestOrigin = "TH"))
  private val messagesBag                         = mock[MessagesBag]
  private val featureAware                        = mock[FeatureAware]
  private val whiteLabelInfo                      = mock[WhiteLabelInfo]
  private val whiteLabelFeaturesConfiguration     = mock[FeaturesConfiguration]
  private val whiteLabelOverrideCreditCardPayment = mock[OverrideCreditCardPayment]
  private val countriesRepository                 = mock[CountriesRepository]

  private val chargeOption: Seq[CurrencyOption] = Seq(
    CurrencyOption(amount = 100d, currencyCode = "KrW"),
    CurrencyOption(amount = 200d, currencyCode = "MyR")
  )
  private val amount: Option[Double]                = Some(300d)
  private val chargeOptionNone: Seq[CurrencyOption] = Seq()
  implicit val setupContext: SetupBookingContext    = mock[SetupBookingContext](RETURNS_DEEP_STUBS)
  val context: RequestContext =
    requestContext(messagesBag, userContext).copy(featureAware = Some(featureAware))

  before {
    reset(bfdbProxy)
    reset(countriesRepository)
    reset(paymentApiRepo)

    when(countriesRepository.getCountries()).thenReturn(Future.successful(defaultCountries))

    when(setupContext.deviceContext).thenReturn(
      Some(DeviceContext(deviceTypeId = DevicePlatform.WebDesktop, deviceId = None))
    )
    when(setupContext.requestContext).thenReturn(context)

    when(whiteLabelInfo.feature).thenReturn(whiteLabelFeaturesConfiguration)
    when(whiteLabelFeaturesConfiguration.overrideCreditCardPayment).thenReturn(whiteLabelOverrideCreditCardPayment)

    when(setupContext.whiteLabelInfo).thenReturn(
      WhiteLabelInfo(whiteLabelId = WhiteLabel.Agoda, feature = null, token = None, instantBookingEnabled = false)
    )
  }

  private val InstallmentDataResponseForHKG =
    InstallmentDataResponse(
      InstallmentPlans = Some(List(InstallmentPlan(PlanCode = Some("PLAN-1")))),
      InstallmentAvailableProviders = Some(
        List(
          InstallmentAvailableProvider(ProviderId = Some(2))
        )
      )
    )

  test("getCreditCardInfo should work correctly with ccBin") {
    val service = createService
    val context = requestContext(bookingInfoMessageService)
    val ccBin   = Some("123456")
    val binRangeRequest =
      BinRangeRequest(languageId = LanguageID.English, correlationId = Some("correlationId"), bin = ccBin)
    val expectedCreditCardCurrency = Some(CurrencyInfo(10, "IND", 11))
    when(paymentApiRepo.getBinRange(binRangeRequest)(context)).thenReturn(
      Future.successful(
        Some(
          BinRangeInfo(
            countryCardIssuer = Some("IND")
          )
        )
      )
    )

    when(currenciesRepo.getCreditcardCurrency("IND")).thenReturn(Future.successful(expectedCreditCardCurrency))

    val ccInfoF =
      service.getCreditCardInfo(context, ccBin, None, None)
    ccInfoF.map { ccInfo =>
      assert(ccInfo.isDefined)
      assert(ccInfo.get.countryInfo.get.countryId === 35)
      assert(ccInfo.get.countryInfo.get === defaultCountries(3))
      assert(ccInfo.get.currencyInfo === expectedCreditCardCurrency)
    }
  }

  test("getCreditCardInfo should work correctly with ccId") {
    val service = createService
    val context = requestContext(bookingInfoMessageService)
    val ccId    = Some(123L)
    val binRangeRequest =
      BinRangeRequest(languageId = LanguageID.English, correlationId = Some("correlationId"), ccId = ccId)
    val expectedCreditCardCurrency = Some(CurrencyInfo(10, "IND", 11))
    when(paymentApiRepo.getBinRange(binRangeRequest)(context)).thenReturn(
      Future.successful(
        Some(
          BinRangeInfo(
            countryCardIssuer = Some("IND")
          )
        )
      )
    )

    when(currenciesRepo.getCreditcardCurrency("IND")).thenReturn(Future.successful(expectedCreditCardCurrency))

    val ccInfoF =
      service.getCreditCardInfo(context, None, ccId, None)
    ccInfoF.map { ccInfo =>
      assert(ccInfo.isDefined)
      assert(ccInfo.get.countryInfo.get.countryId === 35)
      assert(ccInfo.get.countryInfo.get === defaultCountries(3))
      assert(ccInfo.get.currencyInfo === expectedCreditCardCurrency)
    }
  }

  test("getCreditCardInfo should work correctly with CcToken") {
    val service = createService
    val context = requestContext(bookingInfoMessageService)
    val ccBin   = Some("123456")
    val ccToken = Some("ccToken")
    val binRangeRequest =
      BinRangeRequest(
        languageId = LanguageID.English,
        correlationId = Some("correlationId"),
        bin = ccBin,
        ccToken = ccToken
      )
    val expectedCreditCardCurrency = Some(CurrencyInfo(10, "IND", 11))
    when(paymentApiRepo.getBinRange(binRangeRequest)(context)).thenReturn(
      Future.successful(
        Some(
          BinRangeInfo(
            countryCardIssuer = Some("IND")
          )
        )
      )
    )

    when(currenciesRepo.getCreditcardCurrency("IND")).thenReturn(Future.successful(expectedCreditCardCurrency))

    val ccInfoF =
      service.getCreditCardInfo(context, ccBin, None, ccToken)
    ccInfoF.map { ccInfo =>
      assert(ccInfo.isDefined)
      assert(ccInfo.get.countryInfo.get.countryId === 35)
      assert(ccInfo.get.countryInfo.get === defaultCountries(3))
      assert(ccInfo.get.currencyInfo === expectedCreditCardCurrency)
    }
  }

  test("getCreditCardInfo should work correctly with ccId when paymentApi does NOT return countryCardIssuer") {
    val service = createService
    val context = requestContext(bookingInfoMessageService)
    val ccId    = Some(123L)
    val binRangeRequest =
      BinRangeRequest(languageId = LanguageID.English, correlationId = Some("correlationId"), ccId = ccId)
    val expectedCreditCardCurrency = Some(CurrencyInfo(10, "BVT", 11))
    when(paymentApiRepo.getBinRange(binRangeRequest)(context)).thenReturn(
      Future.successful(
        Some(
          BinRangeInfo(
            countryCardIssuer = None
          )
        )
      )
    )

    when(currenciesRepo.getCreditcardCurrency("BVT")).thenReturn(Future.successful(expectedCreditCardCurrency))

    val ccInfoF =
      service.getCreditCardInfo(context, None, ccId, None)
    ccInfoF.map { ccInfo =>
      assert(ccInfo.isDefined)
      assert(ccInfo.get.countryInfo.get.countryId === 329)
      assert(ccInfo.get.countryInfo.get === defaultCountries(0))
      assert(ccInfo.get.currencyInfo === expectedCreditCardCurrency)
    }
  }

  test("getCreditCardInfo should work when currencyInfo not found") {
    val service = createService
    val context = requestContext(bookingInfoMessageService)
    val ccId    = Some(123L)
    val binRangeRequest =
      BinRangeRequest(languageId = LanguageID.English, correlationId = Some("correlationId"), ccId = ccId)
    when(paymentApiRepo.getBinRange(binRangeRequest)(context)).thenReturn(
      Future.successful(
        Some(
          BinRangeInfo(
            countryCardIssuer = Some("IND")
          )
        )
      )
    )

    when(currenciesRepo.getCreditcardCurrency("IND")).thenReturn(Future.successful(None))

    val ccInfoF =
      service.getCreditCardInfo(context, None, ccId, None)
    ccInfoF.map { ccInfo =>
      assert(ccInfo.isDefined)
      assert(ccInfo.get.countryInfo.get.countryId === 35)
      assert(ccInfo.get.countryInfo.get === defaultCountries(3))
      assert(ccInfo.get.currencyInfo.isEmpty)
    }
  }

  test("getCreditCardInfo should work when countryInfo not found") {
    val service                    = createService
    val context                    = requestContext(bookingInfoMessageService)
    val ccId                       = Some(123L)
    val expectedCreditCardCurrency = Some(CurrencyInfo(10, "BVT", 11))
    val binRangeRequest =
      BinRangeRequest(languageId = LanguageID.English, correlationId = Some("correlationId"), ccId = ccId)
    when(paymentApiRepo.getBinRange(binRangeRequest)(context)).thenReturn(
      Future.successful(
        Some(
          BinRangeInfo(
            countryCardIssuer = Some("GBP")
          )
        )
      )
    )

    when(currenciesRepo.getCreditcardCurrency("GBP")).thenReturn(Future.successful(expectedCreditCardCurrency))

    val ccInfoF =
      service.getCreditCardInfo(context, None, ccId, None)
    ccInfoF.map { ccInfo =>
      assert(ccInfo.isDefined)
      assert(ccInfo.get.countryInfo.isEmpty)
      assert(ccInfo.get.currencyInfo === expectedCreditCardCurrency)
    }
  }

  test("getCreditCardInfo should return empty when ccId and ccBin are empty") {
    val service                    = createService
    val context                    = requestContext(bookingInfoMessageService)
    val binRangeRequest            = BinRangeRequest(languageId = LanguageID.English, correlationId = Some("correlationId"), None)
    val expectedCreditCardCurrency = Some(CurrencyInfo(10, "IND", 11))
    when(paymentApiRepo.getBinRange(binRangeRequest)(context)).thenReturn(
      Future.successful(
        Some(
          BinRangeInfo(
            countryCardIssuer = Some("IND")
          )
        )
      )
    )

    when(currenciesRepo.getCreditcardCurrency("IND")).thenReturn(Future.successful(expectedCreditCardCurrency))

    val ccInfoF =
      service.getCreditCardInfo(context, None, None, None)
    ccInfoF.map { ccInfo =>
      assert(ccInfo.isEmpty)
    }
  }

  test("getCreditCardInfo should work even when getBinRange throws an error") {
    val service = createService
    val context = requestContext(bookingInfoMessageService)
    val ccId    = Some(123L)
    val binRangeRequest =
      BinRangeRequest(languageId = LanguageID.English, correlationId = Some("correlationId"), ccId = ccId)
    val expectedCreditCardCurrency = Some(CurrencyInfo(10, "BVT", 11))
    when(paymentApiRepo.getBinRange(binRangeRequest)(context)).thenReturn(
      Future.failed(new Exception())
    )

    when(currenciesRepo.getCreditcardCurrency("BVT")).thenReturn(Future.successful(expectedCreditCardCurrency))

    val ccInfoF =
      service.getCreditCardInfo(context, None, ccId, None)
    ccInfoF.map { ccInfo =>
      assert(ccInfo.isDefined)
      assert(ccInfo.get.countryInfo.get.countryId === 329)
      assert(ccInfo.get.countryInfo.get === defaultCountries(0))
      assert(ccInfo.get.currencyInfo === expectedCreditCardCurrency)
    }
  }

  test(
    "getCreditCardCurrency should return override creditCardCurrency if WL service return currency"
  ) {
    val service = createService
    val context = requestContext(bookingInfoMessageService)
      .copy(featureAware = Some(featureAware), whiteLabelInfo = whiteLabelInfo)
    val INDCreditCardCurrency = Some(CurrencyInfo(10, "IND", 11))
    val JPYCreditCardCurrency = Some(CurrencyInfo(11, "JPY", 0))

    when(whiteLabelOverrideCreditCardPayment.creditCardCurrency).thenReturn(Some("JPY"))
    when(currenciesRepo.getCreditcardCurrency("IND")).thenReturn(Future.successful(INDCreditCardCurrency))
    when(ebeliteRepo.getCurrencyInfo("JPY")).thenReturn(Future.successful(JPYCreditCardCurrency))

    val ccCurrencyF = service.getCreditCardCurrency(context, "IND")
    ccCurrencyF.map { ccCurrency =>
      assert(ccCurrency.isDefined)
      assert(ccCurrency == JPYCreditCardCurrency)
    }
  }

  test(
    "getCreditCardCurrency should fallback to countryCardIssuer currency if WL service not return currency"
  ) {
    val service = createService
    val context = requestContext(bookingInfoMessageService)
      .copy(featureAware = Some(featureAware), whiteLabelInfo = whiteLabelInfo)
    val INDCreditCardCurrency = Some(CurrencyInfo(10, "IND", 11))
    val JPYCreditCardCurrency = Some(CurrencyInfo(11, "JPY", 0))

    when(whiteLabelOverrideCreditCardPayment.creditCardCurrency).thenReturn(None)
    when(currenciesRepo.getCreditcardCurrency("IND")).thenReturn(Future.successful(INDCreditCardCurrency))
    when(ebeliteRepo.getCurrencyInfo("JPY")).thenReturn(Future.successful(JPYCreditCardCurrency))

    val ccCurrencyF = service.getCreditCardCurrency(context, "IND")
    ccCurrencyF.map { ccCurrency =>
      assert(ccCurrency.isDefined)
      assert(ccCurrency == INDCreditCardCurrency)
    }
  }

  test(
    "get 2 supported currencies correctly when payment method support both display currency and credit card currency"
  ) {
    val service         = createService
    val cardCurrency    = CurrencyInfo(1, "GBP", 2)
    val displayCurrency = "EUR"
    val selectedPaymentMethod = SelectedPaymentMethod(
      id = 1,
      defaultCurrency = "USD",
      currencies = Set("EUR", "THB", "GBP"),
      redirect = false
    )

    val supportedCurrencies =
      service.getSupportedCurrencies(1, displayCurrency, Some(selectedPaymentMethod), Some(cardCurrency))
    assert(supportedCurrencies == Seq("EUR", "GBP"))
  }

  test("get only 1 currency when payment method support only credit card currency") {
    val service         = createService
    val cardCurrency    = CurrencyInfo(1, "GBP", 2)
    val displayCurrency = "EUR"
    val selectedPaymentMethod = Some(
      SelectedPaymentMethod(
        id = 1,
        defaultCurrency = "USD",
        currencies = Set("THB", "GBP"),
        redirect = false
      )
    )

    val supportedCurrencies =
      service.getSupportedCurrencies(1, displayCurrency, selectedPaymentMethod, Some(cardCurrency))
    assert(supportedCurrencies == Seq("GBP"))
  }

  test("get only 1 currency when credit card currency and display currency are the same and payment method supports") {
    val service         = createService
    val cardCurrency    = CurrencyInfo(1, "GBP", 2)
    val displayCurrency = "GBP"
    val selectedPaymentMethod = Some(
      SelectedPaymentMethod(
        id = 1,
        defaultCurrency = "USD",
        currencies = Set("THB", "GBP"),
        redirect = false
      )
    )

    val supportedCurrencies =
      service.getSupportedCurrencies(1, displayCurrency, selectedPaymentMethod, Some(cardCurrency))
    assert(supportedCurrencies == Seq("GBP"))
  }

  test("get display currency when payment method is not selected") {
    val service               = createService
    val displayCurrency       = "EUR"
    val selectedPaymentMethod = None
    val supportedCurrencies =
      service.getSupportedCurrencies(1, displayCurrency, selectedPaymentMethod, None)
    assert(supportedCurrencies == Seq("EUR"))
  }

  test("get display currency when payment method supports and can't get credit card currency") {
    val service         = createService
    val displayCurrency = "EUR"
    val selectedPaymentMethod = Some(
      SelectedPaymentMethod(
        id = 1,
        defaultCurrency = "USD",
        currencies = Set("EUR", "THB", "GBP"),
        redirect = false
      )
    )

    val supportedCurrencies =
      service.getSupportedCurrencies(1, displayCurrency, selectedPaymentMethod, None)
    assert(supportedCurrencies == Seq("EUR"))
  }

  test("get display currency when payment method supports and no ccbin in the request") {
    val service         = createService
    val displayCurrency = "EUR"
    val selectedPaymentMethod = Some(
      SelectedPaymentMethod(
        id = 1,
        defaultCurrency = "USD",
        currencies = Set("EUR", "THB", "GBP"),
        redirect = false
      )
    )

    val supportedCurrencies =
      service.getSupportedCurrencies(1, displayCurrency, selectedPaymentMethod, None)
    assert(supportedCurrencies == Seq("EUR"))
  }

  test(
    "get default currency of selected payment method when it doesn't support display currency and can't get credit card currency"
  ) {
    val service         = createService
    val displayCurrency = "EUR"
    val selectedPaymentMethod = Some(
      SelectedPaymentMethod(
        id = 1,
        defaultCurrency = "JPY",
        currencies = Set("USD"),
        redirect = false
      )
    )

    val supportedCurrencies =
      service.getSupportedCurrencies(
        1,
        displayCurrency,
        selectedPaymentMethod,
        Some(CurrencyInfo(6, "GBR", 2))
      )
    assert(supportedCurrencies == Seq("JPY"))
  }

  test(
    "get USD currency when payment method doesn't support display currency, can't get credit card currency and can't get default currency of payment method"
  ) {
    val service         = createService
    val displayCurrency = "EUR"
    val selectedPaymentMethod = Some(
      SelectedPaymentMethod(
        id = 1,
        currencies = Set("JPY"),
        defaultCurrency = "",
        redirect = false
      )
    )

    val supportedCurrencies =
      service.getSupportedCurrencies(1, displayCurrency, selectedPaymentMethod, Some(CurrencyInfo(1, "GBR", 2)))
    assert(supportedCurrencies == Seq("USD"))
  }

  test(
    "fallback to default currency of payment method when it doesn't support both display currency and credit card currency"
  ) {
    val service         = createService
    val cardCurrency    = CurrencyInfo(1, "GBP", 2)
    val displayCurrency = "EUR"
    val selectedPaymentMethod = Some(
      SelectedPaymentMethod(
        id = 1,
        currencies = Set(),
        defaultCurrency = "USD",
        redirect = false
      )
    )

    val supportedCurrencies =
      service.getSupportedCurrencies(1, displayCurrency, selectedPaymentMethod, Some(cardCurrency))
    assert(supportedCurrencies == Seq("USD"))

  }

  test("fallback to display currency when credit card currency is not offered") {
    val service         = createService
    val displayCurrency = "EUR"
    val selectedPaymentMethod = Some(
      SelectedPaymentMethod(
        id = 1,
        currencies = Set("USD", "THB", "MAD", "EUR"),
        defaultCurrency = "USD",
        redirect = false
      )
    )

    val supportedCurrencies =
      service.getSupportedCurrencies(1, displayCurrency, selectedPaymentMethod, None)
    assert(supportedCurrencies == Seq("EUR"))

  }

  test(
    "fallback to selected payment methods default currency if credit card currency is not offered and display currency is not supported by selected payment method"
  ) {
    val service         = createService
    val displayCurrency = "LOL"
    val selectedPaymentMethod = Some(
      SelectedPaymentMethod(
        id = 1,
        currencies = Set("USD", "THB", "MAD"),
        defaultCurrency = "USD",
        redirect = false
      )
    )

    val supportedCurrencies =
      service.getSupportedCurrencies(1, displayCurrency, selectedPaymentMethod, None)
    assert(supportedCurrencies == Seq("USD"))

  }

  test("isEmailMarketingOptedOutByDefault must return true if the origin is NZ") {
    val userCtx = userContext.map(_.copy(requestOrigin = "NZ"))
    val context = requestContext(bookingInfoMessageService, userCtx)
    assert(createService.isEmailMarketingOptedOutByDefault(context))
  }

  test("isEmailMarketingOptedOutByDefault must return true if the origin is BE") {
    val userCtx = userContext.map(_.copy(requestOrigin = "BE"))
    val context = requestContext(bookingInfoMessageService, userCtx)
    assert(createService.isEmailMarketingOptedOutByDefault(context))
  }

  test("isEmailMarketingOptedOutByDefault must return true if the origin is de (not capital on purpose)") {
    val userCtx = userContext.map(_.copy(requestOrigin = "de"))
    val context = requestContext(bookingInfoMessageService, userCtx)
    assert(createService.isEmailMarketingOptedOutByDefault(context))
  }

  test("isEmailMarketingOptedOutByDefault must return true if the origin is empty") {
    val userCtx = userContext.map(_.copy(requestOrigin = ""))
    val context = requestContext(bookingInfoMessageService, userCtx)
    assert(createService.isEmailMarketingOptedOutByDefault(context))
  }

  test("isEmailMarketingOptedOutByDefault must return true if the user context does not exist") {
    val context = requestContext(bookingInfoMessageService, None)
    assert(createService.isEmailMarketingOptedOutByDefault(context))
  }

  test("isEmailMarketingOptedOutByDefault must return false if the origin is ABC") {
    val userCtx = userContext.map(_.copy(requestOrigin = "ABC"))
    val context = requestContext(bookingInfoMessageService, userCtx)
    assert(!createService.isEmailMarketingOptedOutByDefault(context))
  }

  test("isEmailMarketingOptedOutByDefault must return true if the origin is VN") {
    val userCtx = userContext.map(_.copy(requestOrigin = "VN"))
    val context = requestContext(bookingInfoMessageService, userCtx)
    assert(createService.isEmailMarketingOptedOutByDefault(context))
  }

  test("isEmailMarketingOptedOutByDefault must return true if the origin is ID") {
    val userCtx = userContext.map(_.copy(requestOrigin = "ID"))
    val context = requestContext(bookingInfoMessageService, userCtx)
    assert(createService.isEmailMarketingOptedOutByDefault(context))
  }

  test("isEmailMarketingOptedOutByDefault must return false if the origin is TW") {
    val userCtx = userContext.map(_.copy(requestOrigin = "TW"))
    val context = requestContext(bookingInfoMessageService, userCtx)
    assert(!createService.isEmailMarketingOptedOutByDefault(context))
  }

  test("isEmailMarketingOptedOutByDefault must return false if the origin is SG") {
    val userCtx = userContext.map(_.copy(requestOrigin = "SG"))
    val context = requestContext(bookingInfoMessageService, userCtx)
    assert(!createService.isEmailMarketingOptedOutByDefault(context))
  }

  test("isEmailMarketingOptedOutByDefault must return true if the origin is TH") {
    val userCtx = userContext.map(_.copy(requestOrigin = "TH"))
    val context = requestContext(bookingInfoMessageService, userCtx)
    assert(createService.isEmailMarketingOptedOutByDefault(context))
  }

  test("isEmailMarketingOptedOutByDefault must return true if the origin is JP") {
    val userCtx = userContext.map(_.copy(requestOrigin = "JP"))
    val context = requestContext(bookingInfoMessageService, userCtx)
    assert(createService.isEmailMarketingOptedOutByDefault(context))
  }

  def createService =
    new BookingsServiceImpl(
      bfdbProxy,
      ebeliteRepo,
      paymentApiRepo,
      currenciesRepo,
      countriesRepository
    )

  test("get successful response when calling setPropertyBookingState") {
    val service = createService

    val request          = PropertyBookingState(123, 2)
    val expectedResponse = SetStateResponse(true)

    when(ebeliteRepo.setPropertyBookingState(any[Long], any[CreatedBookingStatus])).thenReturn(Future.successful(1))

    service.setPropertyBookingState(request).map { result =>
      assert(result == expectedResponse)
    }
  }

  test("get failed response when calling setPropertyBookingState because no booking is updated") {
    val service = createService

    val request          = PropertyBookingState(123, 1)
    val expectedResponse = SetStateResponse(false, Some("InternalError"), Some("PropertyBookingState was not updated"))

    when(ebeliteRepo.setPropertyBookingState(any[Long], any[CreatedBookingStatus])).thenReturn(Future.successful(0))

    service.setPropertyBookingState(request).map { result =>
      assert(result == expectedResponse)
    }
  }

  test(
    "getInstallmentData - should return a list of available installment plans when ccId is defined (non-agency, paynow)" +
      " and list of available providers"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("KrW"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)

    val productData = mock[ProductData]
    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanId = Some(1)))),
            InstallmentAvailableProviders = Some(List(InstallmentAvailableProvider(ProviderId = Some(1))))
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = productData,
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planId === Some(1))
        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders.head.providerId === Some(1))
      }
  }

  test(
    "getInstallmentData - should return getInstallmentDataRequest"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("KrW"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)

    val productData = mock[ProductData]
    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanId = Some(1)))),
            InstallmentAvailableProviders = Some(List(InstallmentAvailableProvider(ProviderId = Some(1))))
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = productData,
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        assert(result._2.nonEmpty)
        assert(result._2.flatMap(_.CcId).contains(1L))
        assert(result._2.flatMap(_.Amount).contains(300d))
        assert(result._2.flatMap(_.Currency).contains("KRW"))
        assert(result._2.flatMap(_.BusinessCountryIdList).contains(List.empty))
        assert(result._2.flatMap(_.PartnerClaimToken).isEmpty)
        assert(result._2.flatMap(_.CapiToken).isEmpty)
      }
  }

  test(
    "getInstallmentData - should return a list of available installment plans when ccId is defined and chargeOptionMask is not empty(non-agency, paynow)" +
      " and list of available providers"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(
      Some(Seq(ChargeOption.PayNow.id, ChargeOption.PayWithInstallment.id))
    )
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("KrW"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanId = Some(1)))),
            InstallmentAvailableProviders = Some(List(InstallmentAvailableProvider(ProviderId = Some(1))))
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = None,
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planId === Some(1))
        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders.head.providerId === Some(1))
      }
  }

  test(
    "getInstallmentData - should return a list of available installment plans when ccId is undefined but ccBin is defined (non-agency, paynow)" +
      " and list of available providers  for installment data response"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(None)
    when(paymentRequest.ccBin).thenReturn(Some("123"))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("myr"))

    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanId = Some(2)))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(1)),
                InstallmentAvailableProvider(ProviderId = Some(2))
              )
            )
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planId === Some(2))

        assert(installmentAvailableProviders.size === 2)
        assert(installmentAvailableProviders(0).providerId === Some(1))
        assert(installmentAvailableProviders(1).providerId === Some(2))
      }
  }

  test(
    "getInstallmentData - should not return any installment plans when both ccId and ccBin is undefined (non-agency, paynow)" +
      " and list of available providers  for installment data response"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.ccId).thenReturn(None)
    when(paymentRequest.ccBin).thenReturn(None)
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        assert(result._1.isEmpty)
      }
  }

  test(
    "getInstallmentData - should not return any installment plans when both ccId and ccBin is undefined (non-agency, paynow)" +
      " and list of available providers  for installment data response and charge option is none"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(None)
    when(paymentRequest.ccBin).thenReturn(None)
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)

    createService
      .getInstallmentData(
        request,
        false,
        productData = mock[ProductData],
        Some(ChargeOption.PayNow.id),
        chargeOptionNone,
        List.empty,
        None,
        amount = amount
      )
      .map { result =>
        assert(result._1.isEmpty)
      }
  }

  test(
    "getInstallmentData - should not return a list of available installment plans when ccId is defined (agency, payLater)" +
      " for installment data response"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanId = Some(2)))),
            InstallmentAvailableProviders = Some(List(InstallmentAvailableProvider(ProviderId = Some(2))))
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = true,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayLater.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        assert(result._1.isEmpty)
      }
  }

  test(
    "getInstallmentData - should not return a list of available installment plans when ccId is defined (non-agency, payLater)" +
      "  for installment data response"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanId = Some(2)))),
            InstallmentAvailableProviders = Some(List(InstallmentAvailableProvider(ProviderId = Some(2))))
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayLater.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        assert(result._1.isEmpty)
      }
  }

  test(
    "getInstallmentData - should return a list of available installment plans when ccId is defined and chargeOptionMask is not empty(non-agency, payLater)" +
      " and list of available providers"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(
      Some(Seq(ChargeOption.PayLater.id, ChargeOption.PayWithInstallment.id))
    )
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("KrW"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanId = Some(1)))),
            InstallmentAvailableProviders = Some(List(InstallmentAvailableProvider(ProviderId = Some(1))))
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayLater.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planId === Some(1))
        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders.head.providerId === Some(1))
      }
  }

  test(
    "getInstallmentData - should pass ccToken to paymentApi for installment data response"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.ccToken).thenReturn(Some("cctoken"))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("KrW"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanId = Some(2)))),
            InstallmentAvailableProviders = Some(List(InstallmentAvailableProvider(ProviderId = Some(2))))
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.CcToken == Some("cctoken"))
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.length === 1)
        assert(installmentAvailableProviders(0).providerId === Some(2))
        assert(installmentPlans.length === 1)
        assert(installmentPlans(0).planId === Some(2))
      }
  }

  test(
    "getInstallmentData - should pass IncludeThirdPartyInstallmentProviders=true for installment data response"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("KrW"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(Some(true))

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanId = Some(2)))),
            InstallmentAvailableProviders = Some(List(InstallmentAvailableProvider(ProviderId = Some(2))))
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == Some(true))
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders(0).providerId === Some(2))
        assert(installmentPlans.size === 1)
        assert(installmentPlans(0).planId === Some(2))
      }
  }

  test(
    "getInstallmentData - should pass IncludeThirdPartyInstallmentProviders=false for installment data response"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("KrW"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(Some(false))

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanId = Some(1)))),
            InstallmentAvailableProviders = Some(List(InstallmentAvailableProvider(ProviderId = Some(1))))
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == Some(false))
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders(0).providerId === Some(1))
        assert(installmentPlans.size === 1)
        assert(installmentPlans(0).planId === Some(1))
      }
  }

  test(
    "getInstallmentData - should pass IncludeThirdPartyInstallmentProviders=None for installment data response"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("KrW"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanId = Some(3)))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(1)),
                InstallmentAvailableProvider(ProviderId = Some(2))
              )
            )
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == None)
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 2)
        assert(installmentAvailableProviders(0).providerId === Some(1))
        assert(installmentAvailableProviders(1).providerId === Some(2))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planId === Some(3))
      }
  }

  test(
    "getInstallmentData - should pass BussinessCountryId correctly"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("INR"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanId = Some(3)))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(1)),
                InstallmentAvailableProvider(ProviderId = Some(2))
              )
            )
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List(35),
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.BusinessCountryIdList.contains(List(Integer.valueOf(35))))
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 2)
        assert(installmentAvailableProviders(0).providerId === Some(1))
        assert(installmentAvailableProviders(1).providerId === Some(2))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planId === Some(3))
      }
  }

  test(
    "getInstallmentData - should pass Amount for installment from finalProduct"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("KRW"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)

    val agodaBreakdownNode = PriceBreakdownNode(
      value = Some(
        PriceBreakdownResponse(
          `type` = PriceBreakdownType.PayAgoda,
          amount = com.agoda.bapi.common.token.Money(110, "USD")
        )
      )
    )
    val bundledSummaryNode = PriceBreakdownNode(
      value = Some(
        PriceBreakdownResponse(
          `type` = PriceBreakdownType.BundledSummary,
          amount = com.agoda.bapi.common.token.Money(110, "USD")
        )
      ),
      breakdowns = Some(Seq(agodaBreakdownNode))
    )
    val totalPriceNode = PriceBreakdownNode(
      value = Some(
        PriceBreakdownResponse(
          `type` = PriceBreakdownType.PriceSummary,
          amount = com.agoda.bapi.common.token.Money(110, "USD")
        )
      ),
      breakdowns = Some(Seq(bundledSummaryNode))
    )

    val productData = mock[ProductData]
    when(productData.totalPriceDisplay).thenReturn(Some(totalPriceNode))

    when(setupContext.whiteLabelInfo).thenReturn(
      WhiteLabelInfo(whiteLabelId = WhiteLabel.CitiUS, feature = null, token = None, instantBookingEnabled = false)
    )

    implicit val context: RequestContext =
      requestContext(bookingInfoMessageService)
        .copy(featureAware = Some(featureAware))
        .copy(whiteLabelInfo = whiteLabelInfo)

    when(setupContext.requestContext).thenReturn(context)

    val partnerClaimMock = PartnerClaim(
      userId = UUID.randomUUID(),
      partnerToken = Some("session-token"),
      memberDetail = Some(
        SSOMemberDetail(
          externalResponseCode = ExternalResponseCode.SUCCESS,
          loyaltyAccountNumber = Some("1234-**************"),
          partnerSpecificParameters = Map(
            "clientId"               -> "13",
            "environmentId"          -> "DEV",
            "flexPayEligibilityFlag" -> "true"
          ),
          externalMemberId = Some("1234"),
          firstName = Some("John"),
          lastName = Some("Doe"),
          emailAddress = None,
          tier = None,
          optionalPaymentInfo = None,
          loyaltyProfileId = None
        )
      )
    )

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanId = Some(3)))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(1)),
                InstallmentAvailableProvider(ProviderId = Some(2))
              )
            )
          )
        )
      )

    val expectedPartnerSpecificParams = PartnerSpecificParams(
      SessionToken = Some("session-token"),
      ClientId = Some("13"),
      CustomerId = Some("1234-**************"),
      EnvironmentId = Some("DEV")
    )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = productData,
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = Seq(),
        businessCountryIds = List(35),
        partnerClaim = Option(partnerClaimMock),
        amount = Some(100d)
      )
      .map { result =>
        verify(paymentApiRepo, times(1))
          .getInstallmentData(
            argThat[GetInstallmentDataRequest](r => r.Amount.contains(110d))
          )(argThat[RequestContext](r => r == setupContext.requestContext))

        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 2)
        assert(installmentAvailableProviders(0).providerId === Some(1))
        assert(installmentAvailableProviders(1).providerId === Some(2))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planId === Some(3))
      }
  }

  test(
    "getInstallmentData - should support Citi Installment"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq(ChargeOption.PayWithInstallment.id)))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("INR"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)

    when(setupContext.whiteLabelInfo).thenReturn(
      WhiteLabelInfo(whiteLabelId = WhiteLabel.CitiUS, feature = null, token = None, instantBookingEnabled = false)
    )

    implicit val context: RequestContext =
      requestContext(bookingInfoMessageService)
        .copy(featureAware = Some(featureAware))
        .copy(whiteLabelInfo = whiteLabelInfo)

    when(setupContext.requestContext).thenReturn(context)

    val partnerClaimMock = PartnerClaim(
      userId = UUID.randomUUID(),
      partnerToken = Some("session-token"),
      memberDetail = Some(
        SSOMemberDetail(
          externalResponseCode = ExternalResponseCode.SUCCESS,
          loyaltyAccountNumber = Some("1234-**************"),
          partnerSpecificParameters = Map(
            "clientId"               -> "13",
            "environmentId"          -> "DEV",
            "flexPayEligibilityFlag" -> "true"
          ),
          externalMemberId = Some("1234"),
          firstName = Some("John"),
          lastName = Some("Doe"),
          emailAddress = None,
          tier = None,
          optionalPaymentInfo = None,
          loyaltyProfileId = None
        )
      )
    )

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanId = Some(3)))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(1)),
                InstallmentAvailableProvider(ProviderId = Some(2))
              )
            )
          )
        )
      )

    val expectedPartnerSpecificParams = PartnerSpecificParams(
      SessionToken = Some("session-token"),
      ClientId = Some("13"),
      CustomerId = Some("1234-**************"),
      EnvironmentId = Some("DEV")
    )

    val agodaBreakdownNode = PriceBreakdownNode(
      value = Some(
        PriceBreakdownResponse(
          `type` = PriceBreakdownType.PayAgoda,
          amount = com.agoda.bapi.common.token.Money(110, "USD")
        )
      )
    )
    val bundledSummaryNode = PriceBreakdownNode(
      value = Some(
        PriceBreakdownResponse(
          `type` = PriceBreakdownType.BundledSummary,
          amount = com.agoda.bapi.common.token.Money(110, "USD")
        )
      ),
      breakdowns = Some(Seq(agodaBreakdownNode))
    )
    val totalPriceNode = PriceBreakdownNode(
      value = Some(
        PriceBreakdownResponse(
          `type` = PriceBreakdownType.PriceSummary,
          amount = com.agoda.bapi.common.token.Money(110, "USD")
        )
      ),
      breakdowns = Some(Seq(bundledSummaryNode))
    )

    val productData = mock[ProductData]
    when(productData.totalPriceDisplay).thenReturn(Some(totalPriceNode))

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = productData,
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List(35),
        partnerClaim = Option(partnerClaimMock),
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1))
          .getInstallmentData(
            argThat[GetInstallmentDataRequest](r => r.PartnerSpecificParams.contains(expectedPartnerSpecificParams))
          )(argThat[RequestContext](r => r == setupContext.requestContext))

        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 2)
        assert(installmentAvailableProviders(0).providerId === Some(1))
        assert(installmentAvailableProviders(1).providerId === Some(2))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planId === Some(3))
      }
  }

  test(
    "getInstallmentData - should support Citi Installment if FlexPayEligibility flag is enabled and chargeoption mask is empty"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("INR"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)

    when(setupContext.whiteLabelInfo).thenReturn(
      WhiteLabelInfo(whiteLabelId = WhiteLabel.CitiUS, feature = null, token = None, instantBookingEnabled = false)
    )

    implicit val context: RequestContext =
      requestContext(bookingInfoMessageService)
        .copy(featureAware = Some(featureAware))
        .copy(whiteLabelInfo = whiteLabelInfo)

    when(setupContext.requestContext).thenReturn(context)

    val partnerClaimMock = PartnerClaim(
      userId = UUID.randomUUID(),
      partnerToken = Some("session-token"),
      memberDetail = Some(
        SSOMemberDetail(
          externalResponseCode = ExternalResponseCode.SUCCESS,
          loyaltyAccountNumber = Some("1234-**************"),
          partnerSpecificParameters = Map(
            "clientId"               -> "13",
            "environmentId"          -> "DEV",
            "flexPayEligibilityFlag" -> "true"
          ),
          externalMemberId = Some("1234"),
          firstName = Some("John"),
          lastName = Some("Doe"),
          emailAddress = None,
          tier = None,
          optionalPaymentInfo = None,
          loyaltyProfileId = None
        )
      )
    )

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanId = Some(3)))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(1)),
                InstallmentAvailableProvider(ProviderId = Some(2))
              )
            )
          )
        )
      )

    val expectedPartnerSpecificParams = PartnerSpecificParams(
      SessionToken = Some("session-token"),
      ClientId = Some("13"),
      CustomerId = Some("1234-**************"),
      EnvironmentId = Some("DEV")
    )

    val agodaBreakdownNode = PriceBreakdownNode(
      value = Some(
        PriceBreakdownResponse(
          `type` = PriceBreakdownType.PayAgoda,
          amount = com.agoda.bapi.common.token.Money(110, "USD")
        )
      )
    )
    val bundledSummaryNode = PriceBreakdownNode(
      value = Some(
        PriceBreakdownResponse(
          `type` = PriceBreakdownType.BundledSummary,
          amount = com.agoda.bapi.common.token.Money(110, "USD")
        )
      ),
      breakdowns = Some(Seq(agodaBreakdownNode))
    )
    val totalPriceNode = PriceBreakdownNode(
      value = Some(
        PriceBreakdownResponse(
          `type` = PriceBreakdownType.PriceSummary,
          amount = com.agoda.bapi.common.token.Money(110, "USD")
        )
      ),
      breakdowns = Some(Seq(bundledSummaryNode))
    )

    val productData = mock[ProductData]
    when(productData.totalPriceDisplay).thenReturn(Some(totalPriceNode))

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = productData,
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List(35),
        partnerClaim = Option(partnerClaimMock),
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1))
          .getInstallmentData(
            argThat[GetInstallmentDataRequest](r => r.PartnerSpecificParams.contains(expectedPartnerSpecificParams))
          )(argThat[RequestContext](r => r == setupContext.requestContext))

        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 2)
        assert(installmentAvailableProviders(0).providerId === Some(1))
        assert(installmentAvailableProviders(1).providerId === Some(2))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planId === Some(3))
      }
  }
  test(
    "getInstallmentData - should return installmentData correctly based on White Label product"
  ) {
    val testCases = Table(
      ("whitelabel", "methodCallCount", "expected"),
      (WhiteLabel.Agoda, 1, true),
      (WhiteLabel.Jtb, 0, false)
    )
    forAll(testCases) { (whiteLabel: WhiteLabel, methodCallCount: Int, expected: Boolean) =>
      val request        = mock[SetupBookingRequest]
      val paymentRequest = mock[PaymentRequest]
      when(request.paymentRequest).thenReturn(Some(paymentRequest))
      when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
      when(paymentRequest.ccId).thenReturn(Some(1L))
      when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)
      when(paymentRequest.selectedChargeCurrency).thenReturn(Some("JPY"))
      when(setupContext.requestContext).thenReturn(context)
      when(setupContext.whiteLabelInfo).thenReturn(
        WhiteLabelInfo(whiteLabelId = whiteLabel, feature = null, token = None, instantBookingEnabled = false)
      )
      when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
        .thenReturn(
          Future.successful(
            InstallmentDataResponseForHKG
          )
        )

      createService
        .getInstallmentData(
          setupBookingRequest = request,
          isAgencyBooking = false,
          productData = mock[ProductData],
          chargeOption = Some(ChargeOption.PayNow.id),
          chargeCurrencyOption = chargeOption,
          businessCountryIds = List.empty,
          partnerClaim = None,
          amount = amount
        )
        .map { result =>
          verify(paymentApiRepo, Mockito.atLeast(methodCallCount)).getInstallmentData(
            argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == None)
          )(argThat[RequestContext](r => r == setupContext.requestContext))
          assert(result._1.nonEmpty == expected)
        }
    }
  }

  test(
    "getInstallmentData - should return installmentData correctly for Visa IOS installment"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("JPY"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)
    when(setupContext.deviceContext).thenReturn(
      Some(DeviceContext(deviceTypeId = DevicePlatform.AppIPhone, deviceId = None))
    )
    when(setupContext.requestContext).thenReturn(context)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanCode = Some("PLAN-1")))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(2))
              )
            )
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == None)
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders(0).providerId === Some(2))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planCode === Some("PLAN-1"))
      }
  }

  test(
    "getInstallmentData - should return installmentData correctly for Visa Android installment"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("JPY"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)
    when(setupContext.deviceContext).thenReturn(
      Some(DeviceContext(deviceTypeId = DevicePlatform.AppAndroidTablet, deviceId = None))
    )
    when(setupContext.requestContext).thenReturn(context)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanCode = Some("PLAN-1")))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(2))
              )
            )
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == None)
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders(0).providerId === Some(2))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planCode === Some("PLAN-1"))
      }
  }

  test(
    "getInstallmentData - should return installmentData correctly for JAPAN IOS installment"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("JPY"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)
    when(setupContext.deviceContext).thenReturn(
      Some(DeviceContext(deviceTypeId = DevicePlatform.AppIPhone, deviceId = None))
    )
    when(setupContext.requestContext).thenReturn(context)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanCode = Some("PLAN-1")))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(1))
              )
            )
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == None)
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders(0).providerId === Some(1))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planCode === Some("PLAN-1"))
      }
  }

  test(
    "getInstallmentData - should return installmentData correctly for JAPAN Android installment"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("JPY"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)
    when(setupContext.deviceContext).thenReturn(
      Some(DeviceContext(deviceTypeId = DevicePlatform.AppAndroidTablet, deviceId = None))
    )
    when(setupContext.requestContext).thenReturn(context)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanCode = Some("PLAN-1")))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(1))
              )
            )
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == None)
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders(0).providerId === Some(1))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planCode === Some("PLAN-1"))
      }
  }
  test(
    "getInstallmentData - should return installmentData correctly for JAPAN Web installment"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("JPY"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)
    when(setupContext.deviceContext).thenReturn(
      Some(DeviceContext(deviceTypeId = DevicePlatform.WebDesktop, deviceId = None))
    )
    when(setupContext.requestContext).thenReturn(context)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanCode = Some("PLAN-1")))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(1))
              )
            )
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == None)
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders(0).providerId === Some(1))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planCode === Some("PLAN-1"))
      }
  }

  test(
    "getInstallmentData - should return installmentData correctly for Thailand IOS installment"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("THB"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)
    when(setupContext.deviceContext).thenReturn(
      Some(DeviceContext(deviceTypeId = DevicePlatform.AppIPhone, deviceId = None))
    )
    when(setupContext.requestContext).thenReturn(context)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanCode = Some("PLAN-1")))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(1))
              )
            )
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == None)
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders(0).providerId === Some(1))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planCode === Some("PLAN-1"))
      }
  }

  test(
    "getInstallmentData - should return installmentData correctly for India IOS installment"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("INR"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)
    when(setupContext.deviceContext).thenReturn(
      Some(DeviceContext(deviceTypeId = DevicePlatform.AppIPhone, deviceId = None))
    )
    when(setupContext.requestContext).thenReturn(context)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanCode = Some("PLAN-1")))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(4))
              )
            )
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == None)
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders.head.providerId === Some(4))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planCode === Some("PLAN-1"))
      }
  }

  test(
    "getInstallmentData - should return installmentData correctly for India MSPA web installment"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("INR"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)
    when(setupContext.deviceContext).thenReturn(
      Some(DeviceContext(deviceTypeId = DevicePlatform.WebDesktop, deviceId = None))
    )
    when(setupContext.requestContext).thenReturn(context)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanCode = Some("PLAN-1")))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(4))
              )
            )
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == None)
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders.head.providerId === Some(4))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planCode === Some("PLAN-1"))
      }
  }

  test(
    "getInstallmentData - should return installmentData = None correctly when device platform is unknown"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("HKD"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)
    when(setupContext.deviceContext).thenReturn(
      Some(DeviceContext(deviceTypeId = DevicePlatform.Unknown, deviceId = None))
    )
    when(setupContext.requestContext).thenReturn(context)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponseForHKG
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == None)
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.isEmpty)
      }
  }

  test(
    "getInstallmentData - should return installmentData correctly when HongKong IOS Platform"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("HKD"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)
    when(setupContext.deviceContext).thenReturn(
      Some(DeviceContext(deviceTypeId = DevicePlatform.AppIPhone, deviceId = None))
    )
    when(setupContext.requestContext).thenReturn(context)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponseForHKG
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == None)
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders(0).providerId === Some(2))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planCode === Some("PLAN-1"))
      }
  }

  test(
    "getInstallmentData - should return installmentData correctly when HongKong ANDROID Platform"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("HKD"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)
    when(setupContext.deviceContext).thenReturn(
      Some(DeviceContext(deviceTypeId = DevicePlatform.AppAndroidPhone, deviceId = None))
    )
    when(setupContext.requestContext).thenReturn(context)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponseForHKG
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == None)
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders(0).providerId === Some(2))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planCode === Some("PLAN-1"))
      }
  }

  test(
    "getInstallmentData - should return empty installmentData when cart booking"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("HKD"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)
    when(setupContext.deviceContext).thenReturn(
      Some(DeviceContext(deviceTypeId = DevicePlatform.AppIPhone, deviceId = None))
    )
    when(setupContext.bookingFlowType).thenReturn(BookingFlow.Cart)
    when(setupContext.requestContext).thenReturn(context)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponseForHKG
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        // Assert that the result is None since it's a cart booking and the method should return None
        assert(result._1.isEmpty)
      }
  }

  test(
    "getInstallmentData - should return installmentData correctly for Thailand Android installment"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("thb"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)
    when(setupContext.deviceContext).thenReturn(
      Some(DeviceContext(deviceTypeId = DevicePlatform.AppAndroidTablet, deviceId = None))
    )
    when(setupContext.requestContext).thenReturn(context)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanCode = Some("PLAN-1")))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(1))
              )
            )
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == None)
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders(0).providerId === Some(1))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planCode === Some("PLAN-1"))
      }
  }

  test(
    "getInstallmentData - should return installmentData correctly for Thailand MSPA installment"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("thb"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)
    when(setupContext.deviceContext).thenReturn(
      Some(DeviceContext(deviceTypeId = DevicePlatform.WebDesktop, deviceId = None))
    )
    when(setupContext.requestContext).thenReturn(context)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanCode = Some("PLAN-1")))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(1))
              )
            )
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == None)
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders(0).providerId === Some(1))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planCode === Some("PLAN-1"))
      }
  }

  test(
    "getInstallmentData - should return installmentData correctly for India Android installment"
  ) {
    val request        = mock[SetupBookingRequest]
    val paymentRequest = mock[PaymentRequest]
    when(request.paymentRequest).thenReturn(Some(paymentRequest))
    when(paymentRequest.chargeOptionMask).thenReturn(Some(Seq()))
    when(paymentRequest.ccId).thenReturn(Some(1L))
    when(paymentRequest.selectedChargeCurrency).thenReturn(Some("inr"))
    when(paymentRequest.includeThirdPartyInstallmentProviders).thenReturn(None)
    when(setupContext.deviceContext).thenReturn(
      Some(DeviceContext(deviceTypeId = DevicePlatform.AppAndroidPhone, deviceId = None))
    )
    when(setupContext.requestContext).thenReturn(context)

    when(paymentApiRepo.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentPlans = Some(List(InstallmentPlan(PlanCode = Some("PLAN-1")))),
            InstallmentAvailableProviders = Some(
              List(
                InstallmentAvailableProvider(ProviderId = Some(4))
              )
            )
          )
        )
      )

    createService
      .getInstallmentData(
        setupBookingRequest = request,
        isAgencyBooking = false,
        productData = mock[ProductData],
        chargeOption = Some(ChargeOption.PayNow.id),
        chargeCurrencyOption = chargeOption,
        businessCountryIds = List.empty,
        partnerClaim = None,
        amount = amount
      )
      .map { result =>
        verify(paymentApiRepo, times(1)).getInstallmentData(
          argThat[GetInstallmentDataRequest](r => r.IncludeThirdPartyInstallmentProviders == None)
        )(argThat[RequestContext](r => r == setupContext.requestContext))
        assert(result._1.nonEmpty)

        val installmentAvailableProvidersOption = result._1.head.installmentAvailableProviders
        val installmentPlansOption              = result._1.head.installmentPlans

        assert(installmentAvailableProvidersOption.nonEmpty)
        assert(installmentPlansOption.nonEmpty)

        val installmentAvailableProviders = installmentAvailableProvidersOption.head
        val installmentPlans              = installmentPlansOption.head

        assert(installmentAvailableProviders.size === 1)
        assert(installmentAvailableProviders.head.providerId === Some(4))
        assert(installmentPlans.size === 1)
        assert(installmentPlans.head.planCode === Some("PLAN-1"))
      }
  }

  test("getInstallmentPlanCodeByInstallmentDataResponse - should return planCode correctly") {
    val planCode = createService.getInstallmentPlanCodeByInstallmentDataResponse(
      installmentDataResponseOpt = Some(
        InstallmentDataResponseCommon(
          installmentPlans = Some(
            List(
              InstallmentPlanCommon(
                planCode = Some("PLAN-1")
              ),
              InstallmentPlanCommon(
                planCode = Some("PLAN-3")
              )
            )
          ),
          installmentAvailableProviders = None
        )
      ),
      installmentPlanCodeReq = Some("PLAN-1")
    )
    assert(planCode === Some("PLAN-1"))
  }

  test("getInstallmentPlanCodeByInstallmentDataResponse - should return planCode = None when no plans") {
    val planCode = createService.getInstallmentPlanCodeByInstallmentDataResponse(
      installmentDataResponseOpt = Some(
        InstallmentDataResponseCommon(
          installmentPlans = None,
          installmentAvailableProviders = None
        )
      ),
      installmentPlanCodeReq = Some("PLAN-1")
    )
    assert(planCode === None)
  }

  test("getInstallmentPlanCodeByInstallmentDataResponse - should return planCode = None when no InstallmentData") {
    val planCode = createService.getInstallmentPlanCodeByInstallmentDataResponse(
      installmentDataResponseOpt = None,
      installmentPlanCodeReq = Some("PLAN-1")
    )
    assert(planCode === None)
  }

  test("getSelectedPlanTokenByInstallmentDataResponse - should return planCode correctly") {
    val planToken = createService.getSelectedPlanTokenByInstallmentDataResponse(
      installmentDataResponseOpt = Some(
        InstallmentDataResponseCommon(
          installmentPlans = Some(
            List(
              InstallmentPlanCommon(
                planCode = Some("PLAN-1"),
                planToken = Some("TOKEN-1")
              ),
              InstallmentPlanCommon(
                planCode = Some("PLAN-3"),
                planToken = Some("TOKEN-3")
              )
            )
          ),
          installmentAvailableProviders = None
        )
      ),
      installmentPlanCodeOpt = Some("PLAN-1")
    )
    assert(planToken === Some("TOKEN-1"))
  }

  test(
    "getSelectedPlanTokenByInstallmentDataResponse - should return planToken = None when no plans in InstallmentDataResponseCommon"
  ) {
    val planToken = createService.getSelectedPlanTokenByInstallmentDataResponse(
      installmentDataResponseOpt = Some(
        InstallmentDataResponseCommon(
          installmentPlans = None,
          installmentAvailableProviders = None
        )
      ),
      installmentPlanCodeOpt = Some("PLAN-1")
    )
    assert(planToken === None)
  }

  test(
    "getSelectedPlanTokenByInstallmentDataResponse - should return planToken = None when no InstallmentReponseData"
  ) {
    val planToken = createService.getSelectedPlanTokenByInstallmentDataResponse(
      installmentDataResponseOpt = None,
      installmentPlanCodeOpt = Some("PLAN-1")
    )
    assert(planToken === None)
  }
}
