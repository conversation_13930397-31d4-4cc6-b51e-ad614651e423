package com.agoda.bapi.server.service.pricebreakdown

import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.payment.PaymentModel
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.pricebreakdown.mapper.ConnectedTripSavingsMapper
import com.agoda.mpb.common.models.state.ProductType
import com.agoda.winterfell.output.LoyaltyProfile
import databuilder.PriceBreakdownTestDataBuilder
import models.starfruit.{BookingExternalLoyaltyPayment, DisplayBasis, DisplayPrice, DisplayPricePercentage, DisplaySummary, PseudoCoupon, SummaryElement}
import org.mockito.Mockito.when
import transformers.{AdditionalPriceDisplayBasis, CorBreakdown, EnrichedCampaign, EnrichedCharge, EnrichedNoPrePaymentRequired, EnrichedPayment, EnrichedPricing, PackagePriceAndSaving, PriceWithChannel}
import models.pricing.enums.{PaymentModels => SFPaymentModels}
import org.scalatest.matchers.should.Matchers

class PriceBreakdownParamComposerImplSpec
    extends PriceBreakdownServiceHelper
    with PriceBreakdownTestDataBuilder
    with Matchers {

  describe("compose") {
    val priceBreakdownParamComposer = new PriceBreakdownParamComposerImpl
    it("should compose price breakdown param for correctly") {
      val mockPricing                      = mock[EnrichedPricing]
      val mockCharges                      = Seq(mock[EnrichedCharge])
      val mockDisplaySummary               = mock[DisplaySummary]
      val mockSummaryElement               = mock[SummaryElement]
      val mockCrossedOut                   = mock[DisplayBasis]
      val pseudoCoupon                     = DisplayPrice(1, 2)
      val chargeTotalPrice                 = DisplayPrice(3, 4)
      val crossedOutPerBook                = DisplayPrice(5, 6)
      val cashbackTotalPrice               = DisplayPrice(7, 8)
      val displayAfterCashback             = DisplayPrice(9, 10)
      val mockPayment                      = mock[EnrichedPayment]
      val mockCorBreakdown                 = mock[CorBreakdown]
      val mockPseudoCoupon                 = mock[PseudoCoupon]
      val mockPrePaymentRequest            = Some(EnrichedNoPrePaymentRequired(true, "", "", ""))
      val mockCampaign                     = mock[EnrichedCampaign]
      val mockLoyaltyProfile               = mock[LoyaltyProfile]
      val mockExternalLoyalty              = mock[BookingExternalLoyaltyPayment]
      implicit val mockSetupBookingContext = mock[SetupBookingContext]

      val mockPricingCurrency: Map[String, EnrichedPricing] = Map("THB" -> mockPricing)
      when(childRoom.pricing).thenReturn(mockPricingCurrency)
      when(childRoom.payment).thenReturn(Some(mockPayment))
      when(childRoom.corBreakdown).thenReturn(mockCorBreakdown)
      when(childRoom.pseudoCoupon).thenReturn(Some(mockPseudoCoupon))
      when(childRoom.campaignPromotions).thenReturn(Some(List(mockCampaign)))
      when(mockPayment.paymentModel).thenReturn(SFPaymentModels.Merchant)
      when(mockPayment.noPrePaymentRequired).thenReturn(mockPrePaymentRequest)
      when(mockPricing.charges).thenReturn(mockCharges)
      when(mockPricing.displaySummary).thenReturn(mockDisplaySummary)
      when(mockDisplaySummary.perBook).thenReturn(mockSummaryElement)
      when(mockSummaryElement.pseudoCoupon).thenReturn(pseudoCoupon)
      when(mockSummaryElement.chargeTotal).thenReturn(chargeTotalPrice)
      when(mockSummaryElement.cashbackTotal).thenReturn(Some(cashbackTotalPrice))
      when(mockSummaryElement.displayAfterCashback).thenReturn(Some(displayAfterCashback))
      when(mockPricing.crossedOut).thenReturn(mockCrossedOut)
      when(mockCrossedOut.perBook).thenReturn(crossedOutPerBook)
      when(mockExternalLoyalty.itemPriceInPoints).thenReturn(Some(2000.0))

      val result = priceBreakdownParamComposer.compose(
        childRoom = childRoom,
        currency = Currency("THB", 5, Some(33)),
        numberOfRooms = 2,
        lengthOfStay = 3,
        includeVipDiscount = false,
        isSingleProperty = false,
        isEnableConnectedTrip = false,
        loyaltyProfile = Some(mockLoyaltyProfile),
        externalLoyaltyOpt = Some(mockExternalLoyalty),
        addOnPrices = Map(ProductType.CancelForAnyReason -> Seq.empty),
        isInclusivePaySupplier = false,
        fixDisplayAfterCashbackForAddOn = false,
        fixDoubleTaxesAndFeesPaySupplier = false
      )
      result shouldBe aValidPriceBreakdownBuilderParam
        .withCharges(mockCharges)
        .withChargeTotal(Some(chargeTotalPrice))
        .withOriginalAmount(crossedOutPerBook.allInclusive)
        .withPaymentModel(PaymentModel.Merchant)
        .withPseudoCouponAmount(Some(pseudoCoupon))
        .withPseudoCoupon(Some(mockPseudoCoupon))
        .withRequestedCurrency(Currency("THB", 5, Some(33)))
        .withNumberOfRooms(2)
        .withLengthOfStay(3)
        .withCorBreakdown(Some(mockCorBreakdown))
        .withIncludeVipDiscount(false)
        .withIsNoPrePaymentRequired(Some(true))
        .withIsSingleProperty(false)
        .withSavings(None)
        .withAdditionalRate(None)
        .withCampaignPromotions(Some(List(mockCampaign)))
        .withLoyaltyProfile(Some(mockLoyaltyProfile))
        .withExternalLoyalty(mockExternalLoyalty)
        .withItemPriceInPoints(Some(2000.0))
        .withCashbackTotal(Some(cashbackTotalPrice))
        .withAddOnPrices(Map(ProductType.CancelForAnyReason -> Seq.empty))
        .withDisplayAfterCashback(Some(displayAfterCashback))
        .withSetupBookingContext(Some(mockSetupBookingContext))
        .build
    }

    it("should compose price breakdown param with ConnectedTrip correctly") {
      val mockPackageDisplayBasisPrice     = mock[AdditionalPriceDisplayBasis]
      val mockPackageDisplayBasisSaving    = mock[AdditionalPriceDisplayBasis]
      val mockPackagePriceAndSaving        = mock[PackagePriceAndSaving]
      val mockPrice                        = mock[PriceWithChannel]
      val mockSaving                       = mock[PriceWithChannel]
      implicit val mockSetupBookingContext = mock[SetupBookingContext]
      when(pricing.packagePriceAndSaving).thenReturn(Some(mockPackagePriceAndSaving))
      when(mockPackagePriceAndSaving.packagePrice).thenReturn(mockPrice)
      when(mockPackagePriceAndSaving.savings).thenReturn(Some(mockSaving))
      when(mockPackagePriceAndSaving.savingPercent).thenReturn(Some(DisplayPricePercentage(10, 8)))
      when(mockPrice.price).thenReturn(mockPackageDisplayBasisPrice)
      when(mockSaving.price).thenReturn(mockPackageDisplayBasisSaving)
      when(mockPackageDisplayBasisPrice.perBook).thenReturn(DisplayPrice(120, 130))
      when(mockPackageDisplayBasisPrice.perPax).thenReturn(DisplayPrice(60, 65))
      when(mockPackageDisplayBasisSaving.perBook).thenReturn(DisplayPrice(12, 13))
      when(mockPackageDisplayBasisSaving.perPax).thenReturn(DisplayPrice(6, 6.5))
      val result = priceBreakdownParamComposer.compose(
        childRoom = childRoom,
        currency = Currency("THB", 5, Some(33)),
        numberOfRooms = 0,
        lengthOfStay = 0,
        includeVipDiscount = false,
        isSingleProperty = true,
        isEnableConnectedTrip = true,
        isInclusivePaySupplier = false,
        fixDisplayAfterCashbackForAddOn = false,
        fixDoubleTaxesAndFeesPaySupplier = false
      )

      result.additionalRate.map(_.perBook.bundle) shouldBe Some(DisplayPrice(120, 130))
      result.additionalRate.map(_.perPax.bundle) shouldBe Some(DisplayPrice(60, 65))
      result.savings shouldBe Some(ConnectedTripSavingsMapper(pricing))
    }
  }

}
