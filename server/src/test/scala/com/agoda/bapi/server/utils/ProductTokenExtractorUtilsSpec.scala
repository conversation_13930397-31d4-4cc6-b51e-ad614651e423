package com.agoda.bapi.server.utils

import com.agoda.bapi.common.model.CurrencyCode
import com.agoda.bapi.common.token.{BookingTokenEncryptionHelper, MultiProductRetryPaymentBookingToken}
import com.agoda.bapi.common.util.{TokenSerializer, TokenSerializers}
import com.agoda.bapi.server.facades.aggregator.MockProductUtils.createMockProduct
import com.agoda.bapi.server.model.FlightConfirmationData
import com.agoda.bapi.server.model.pricebreakdown.flights.MockFlightsPricingData
import com.agoda.flights.client.v2.model.ExternalLoyaltyPricing
import mocks.ProductTokenMockHelper
import org.scalatestplus.mockito.MockitoSugar
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

class ProductTokenExtractorUtilsSpec extends AnyWordSpec with MockitoSugar with Matchers with ProductTokenMockHelper {

  val hkdCurrency: CurrencyCode = "HKD"
  val overrideMockProduct       = createMockProduct()

  private def flightsDataWithLoyalty(id: String) =
    overrideMockProduct.flights.map(flight =>
      flight.copy(
        flightPricing = Some(
          flight.flightPricing.get
            .updated(
              hkdCurrency,
              flight.flightPricing
                .get(hkdCurrency)
                .copy(
                  externalLoyalty = Some(
                    ExternalLoyaltyPricing(
                      offers = Vector(MockFlightsPricingData.itineraryOffer(id).get)
                    )
                  )
                )
            )
        )
      )
    )

  "ProductTokenExtractorUtils" should {
    val utils = new ProductTokenCreatorUtilsImpl(encryptedHelper = BookingTokenEncryptionHelper)

    "extract Setup Booking Token" in {
      val result =
        utils.createMultiProductsToken(Some(defaultMultiProductSetupToken), Some(defaultMultiProductCreationToken))
      val extractor = new ProductTokenExtractorUtilsImpl(BookingTokenEncryptionHelper)

      val setupBookingSessionContext = extractor.extractSetupBookingToken(result.get)
      setupBookingSessionContext.get.properties.head._1 shouldBe "1"
      setupBookingSessionContext.get.protectionData shouldBe Some("protectionData")
      setupBookingSessionContext.get.addOnTokenData.get("combination-id") shouldBe Some("ancillary-cache-token")

      val property = setupBookingSessionContext.get.properties.head._2
      property.absRequestId shouldBe Some("AABBCC")
      property.allotmentResult shouldBe Some(1)
      property.productToken shouldBe Some("df-token")
    }

    "extract setup token from booking token with retry payment token" in {
      implicit val testFlightConfirmationDataSeqSerializer: TokenSerializer[Seq[FlightConfirmationData]] =
        new TokenSerializer[Seq[FlightConfirmationData]]
      val multiProductRetryPaymentBookingToken = MultiProductRetryPaymentBookingToken(
        flights = TokenSerializers[Seq[FlightConfirmationData]]
          .serialize(flightsDataWithLoyalty(""), Some(1571200505532L), None)
          .toOption
      )

      val result =
        utils.createMultiProductsToken(
          Some(defaultMultiProductSetupToken),
          Some(defaultMultiProductCreationToken),
          Some(multiProductRetryPaymentBookingToken)
        )
      val extractor = new ProductTokenExtractorUtilsImpl(BookingTokenEncryptionHelper)

      val setupBookingSessionContext = extractor.extractSetupBookingToken(result.get)
      setupBookingSessionContext.get.properties.head._1 shouldBe "1"
      setupBookingSessionContext.get.protectionData shouldBe Some("protectionData")
      setupBookingSessionContext.get.addOnTokenData.get("combination-id") shouldBe Some("ancillary-cache-token")

      val property = setupBookingSessionContext.get.properties.head._2
      property.absRequestId shouldBe Some("AABBCC")
      property.allotmentResult shouldBe Some(1)
      property.productToken shouldBe Some("df-token")
    }

    "extract retry token from booking token" in {
      implicit val testFlightConfirmationDataSeqSerializer: TokenSerializer[Seq[FlightConfirmationData]] =
        new TokenSerializer[Seq[FlightConfirmationData]]
      val multiProductRetryPaymentBookingToken = MultiProductRetryPaymentBookingToken(
        flights = TokenSerializers[Seq[FlightConfirmationData]]
          .serialize(flightsDataWithLoyalty(""), Some(1571200505532L), None)
          .toOption
      )

      val result =
        utils.createMultiProductsToken(
          Some(defaultMultiProductSetupToken),
          Some(defaultMultiProductCreationToken),
          Some(multiProductRetryPaymentBookingToken)
        )
      val extractor = new ProductTokenExtractorUtilsImpl(BookingTokenEncryptionHelper)

      val retryPaymentContext = extractor.extractRetryPaymentBookingToken(result.get)
      retryPaymentContext.get.flights.nonEmpty shouldBe true
      retryPaymentContext.get.flights shouldBe flightsDataWithLoyalty("")
    }
  }
}
