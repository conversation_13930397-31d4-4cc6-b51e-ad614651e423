package com.agoda.bapi.server.service

import com.agoda.bapi.agent.common.schema.{AgentBookingActionMessage, ProvisioningData, ProvisioningResult}
import com.agoda.bapi.common.MessageService
import com.agoda.bapi.common.config.{AutoLoginTokenConfig, Configuration, KillSwitches}
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.localization.CmsContextFactory
import com.agoda.bapi.common.message.creation
import com.agoda.bapi.common.message.creation.{BookingCreationContext, CreatedBookingStatus, GenericProductCreateResult, GetStatusRequest, InstantBookingStatus, ProtectionBooking}
import com.agoda.bapi.common.model.product.ProductTypeEnum
import com.agoda.bapi.common.model.protection.ProtectionBookingState
import com.agoda.bapi.common.model.{<PERSON><PERSON><PERSON>, <PERSON>Label, WhiteLabelInfo}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.model.db.ProtectionBookingInfo
import com.agoda.bapi.creation.repository.{EbeLiteBookingRepository, ProtectionBookingRepository, WorkflowRepository}
import com.agoda.bapi.creation.service._
import com.agoda.bapi.creation.service.processing.{ActivityInfoService, AddOnsInProcessingService}
import com.agoda.mpb.common.models.state.ProductType
import dispatch.Future
import mocks.RequestContextMock
import org.mockito.ArgumentMatchers.{any, anyString, eq => eqTo}
import org.mockito.Mockito.{reset, times, verify, verifyNoInteractions, when}
import org.scalatest.funsuite.AsyncFunSuite
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfter, OptionValues}
import org.scalatestplus.mockito.MockitoSugar
import scalapb.json4s.JsonFormat

class CommonBookingsServiceSpec
    extends AsyncFunSuite
    with MockitoSugar
    with BeforeAndAfter
    with Matchers
    with OptionValues
    with RequestContextMock {
  private val config                      = mock[Configuration]
  private val cmsContextFactory           = mock[CmsContextFactory]
  private val ebeLiteBookingRepository    = mock[EbeLiteBookingRepository]
  private val workflowRepository          = mock[WorkflowRepository]
  private val protectionRepository        = mock[ProtectionBookingRepository]
  private val flightInfoService           = mock[FlightInfoService]
  private val flightInfoServiceV2         = mock[FlightInfoServiceV2]
  private val activityInfoService         = mock[ActivityInfoService]
  private val vehicleInfoService          = mock[VehicleInfoService]
  private val bookingActionMessageService = mock[BookingActionMessageService]
  private val urlService                  = mock[UrlService]
  private val mockBookingActionMessage    = mock[BookingActionMessage]
  private val mockFeatureAware            = mock[FeatureAware]
  private val mockRequestContext          = mock[RequestContext]
  private val mockMessagingService        = mock[MessageService]
  private val mockAddOnInfoService        = mock[AddOnsInProcessingService]
  private val mockWhiteLabelInfo          = mock[WhiteLabelInfo]
  private val mockStatusTokenService      = mock[StatusTokenService]
  private val mockBookingCreationContext  = mock[BookingCreationContext]
  private val mockKillSwitch              = mock[KillSwitches]

  private var commonBookingsService: CommonBookingsServiceImpl = _

  before {
    reset(flightInfoService, flightInfoServiceV2, protectionRepository, mockAddOnInfoService)
    when(mockBookingActionMessage.actionId).thenReturn(123)
    when(mockRequestContext.featureAware).thenReturn(Some(mockFeatureAware))
    when(mockFeatureAware.enableGetStatusDataFromBkgDb).thenReturn(false)
    when(mockBookingCreationContext.bookingSessionId).thenReturn(Some("bookingSession123"))
    when(mockRequestContext.bookingCreationContext).thenReturn(Some(mockBookingCreationContext))
    when(mockWhiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
    when(mockRequestContext.whiteLabelInfo).thenReturn(mockWhiteLabelInfo)
    when(config.autoLoginTokenConfig).thenReturn(AutoLoginTokenConfig("testSecret", "testSalt"))
    when(mockKillSwitch.enableRebookAndCancelFlow).thenReturn(true)

    commonBookingsService = new CommonBookingsServiceImpl(
      ebeLiteBookingRepository = ebeLiteBookingRepository,
      workflowRepository = workflowRepository,
      protectionRepository = protectionRepository,
      flightInfoService = flightInfoService,
      flightInfoServiceV2 = flightInfoServiceV2,
      activityInfoService = activityInfoService,
      vehicleInfoService = vehicleInfoService,
      urlService = urlService,
      config = config,
      cmsContextFactory = cmsContextFactory,
      bookingActionMessageService = bookingActionMessageService,
      hadoopMessagingService = mockMessagingService,
      addOnInfoService = mockAddOnInfoService,
      statusTokenService = mockStatusTokenService,
      killSwitches = mockKillSwitch
    )
  }

  test(
    "mapExternalBookingId - return external booking id correctly if BAM have map bookingExternalReference with booking id"
  ) {
    val agentBAM = AgentBookingActionMessage().copy(
      provisioningResult = ProvisioningResult.ProvisioningConfirmed,
      provisioningData = Some(ProvisioningData(bookingExternalReference = Map(1234.toLong -> "5678")))
    )
    val content = JsonFormat.toJsonString(agentBAM)
    when(mockBookingActionMessage.content).thenReturn(content)
    assertResult(Some("5678"))(
      commonBookingsService.mapExternalBookingId(1234, Some(mockBookingActionMessage))
    )
  }

  test("mapExternalBookingId - return None if BAM not have bookingExternalReference map to booking id") {
    val agentBAM = AgentBookingActionMessage().copy(
      provisioningResult = ProvisioningResult.ProvisioningConfirmed,
      provisioningData = Some(ProvisioningData(bookingExternalReference = Map(1234.toLong -> "5678")))
    )
    val content = JsonFormat.toJsonString(agentBAM)
    when(mockBookingActionMessage.content).thenReturn(content)
    assertResult(None)(
      commonBookingsService.mapExternalBookingId(4321, Some(mockBookingActionMessage))
    )
  }

  test("mapExternalBookingId - return None if no BAM found") {
    assertResult(None)(
      commonBookingsService.mapExternalBookingId(1234, None)
    )
  }

  test("mapInstantBookingStatus - instant book confirmed") {
    val agentBAM = AgentBookingActionMessage().copy(provisioningResult = ProvisioningResult.ProvisioningConfirmed)
    val content  = JsonFormat.toJsonString(agentBAM)
    when(mockBookingActionMessage.content).thenReturn(content)
    assertResult(Some(InstantBookingStatus.Confirmed))(
      commonBookingsService.mapInstantBookingStatus(Some(mockBookingActionMessage))
    )
  }
  test("mapInstantBookingStatus - instant book failed") {
    val agentBAM = AgentBookingActionMessage().copy(provisioningResult = ProvisioningResult.ProvisioningFailed)
    val content  = JsonFormat.toJsonString(agentBAM)
    when(mockBookingActionMessage.content).thenReturn(content)
    assertResult(Some(InstantBookingStatus.Failed))(
      commonBookingsService.mapInstantBookingStatus(Some(mockBookingActionMessage))
    )
  }
  test("mapInstantBookingStatus - instant book timeout") {
    val agentBAM = AgentBookingActionMessage().copy(provisioningResult = ProvisioningResult.ProvisioningTimeout)
    val content  = JsonFormat.toJsonString(agentBAM)
    when(mockBookingActionMessage.content).thenReturn(content)
    assertResult(Some(InstantBookingStatus.Timeout))(
      commonBookingsService.mapInstantBookingStatus(Some(mockBookingActionMessage))
    )
  }
  test("mapInstantBookingStatus - instant book unconfirmed") {
    val agentBAM = AgentBookingActionMessage().copy(provisioningResult = ProvisioningResult.ProvisioningUnconfirmed)
    val content  = JsonFormat.toJsonString(agentBAM)
    when(mockBookingActionMessage.content).thenReturn(content)
    assertResult(Some(InstantBookingStatus.UnConfirmed))(
      commonBookingsService.mapInstantBookingStatus(Some(mockBookingActionMessage))
    )
  }
  test("mapInstantBookingStatus - instant book unconfirmed if there is no provisioning BAM") {
    assertResult(None)(
      commonBookingsService.mapInstantBookingStatus(None)
    )
  }

  test(
    "rejectReasonCodeResolver - should return None, if the booking status is ConfirmedBooking,"
  ) {
    val bookingStatus = CreatedBookingStatus.BookingConfirmed.id
    val expected      = None

    val rejectReason = commonBookingsService.rejectReasonCodeResolver(bookingStatus, Some(1), None)(mockRequestContext)

    rejectReason shouldBe expected
  }

  test(
    "rejectReasonCodeResolver - should return correct rejectReason from the masterRejectReason" +
      "if the property don't have Reject Reason and the booking status is NOT ConfirmedBooking"
  ) {
    val bookingStatus = CreatedBookingStatus.BookingRejected.id
    val expected      = 1

    val rejectReason = commonBookingsService.rejectReasonCodeResolver(bookingStatus, Some(1), None)(mockRequestContext)

    rejectReason.value.code shouldBe expected
    rejectReason.value.subErrorCode shouldBe Some(expected)
  }

  test(
    "rejectReasonCodeResolver - should return correct rejectReason from the masterRejectReason" +
      "even if there is propertyRejectReason and the booking status is NOT ConfirmedBooking"
  ) {
    val bookingStatus = CreatedBookingStatus.BookingRejected.id
    val expected      = 1

    val rejectReason =
      commonBookingsService.rejectReasonCodeResolver(bookingStatus, Some(1), Some(3))(mockRequestContext)

    rejectReason.value.code shouldBe expected
    rejectReason.value.subErrorCode shouldBe Some(expected)
  }

  test(
    "rejectReasonCodeResolver - should return correct rejectReason from the propertyRejectReason" +
      "if there is no masterRejectReason and the booking status is NOT ConfirmedBooking"
  ) {
    val bookingStatus = CreatedBookingStatus.BookingRejected.id
    val expected      = 2

    val rejectReason = commonBookingsService.rejectReasonCodeResolver(bookingStatus, None, Some(2))(mockRequestContext)

    rejectReason.value.code shouldBe expected
    rejectReason.value.subErrorCode shouldBe Some(expected)
  }

  test(
    "enableGetStatusDataFromBkgDb is A - getItineraryStatus Case: Flight + Trip Protection + Generic Addon"
  ) {
    val (itineraryId, mockRequest) =
      new WithVariantB().setupStatusRequestForFlightAndProtectionAndGenericAddon(isB = false)
    commonBookingsService.getItineraryStatus(mockRequest, Nil)(mockRequestContext).map { result =>
      verify(flightInfoService).getFlightsItineraryStatus(itineraryId)
      verifyNoInteractions(flightInfoServiceV2)
      verify(mockAddOnInfoService, times(1)).getGenericAddonStatuses(itineraryId, useBkgDb = false)

      verify(protectionRepository).getTripProtectionBookingInfoForItinerary(itineraryId)
      verify(protectionRepository, times(0)).getTripProtectionBookingInfoForItineraryV2(any())

      result.itinerary.value.itineraryId shouldBe itineraryId
      result.itinerary.value.flights.head.bookingStatus shouldBe CreatedBookingStatus.BookingCharged
      result.itinerary.value.addOns.head.bookingStatus shouldBe CreatedBookingStatus.BookingConfirmed
    }
  }

  test(
    s"enableGetStatusDataFromBkgDb is B - getItineraryStatus Case: Flight + Trip Protection + Generic Addon"
  ) {
    val (itineraryId, mockRequest) =
      new WithVariantB().setupStatusRequestForFlightAndProtectionAndGenericAddon(isB = true)
    commonBookingsService.getItineraryStatus(mockRequest, Nil)(mockRequestContext).map { result =>
      verify(flightInfoServiceV2).getFlightsItineraryStatus(itineraryId)
      verifyNoInteractions(flightInfoService)
      verify(mockAddOnInfoService, times(1)).getGenericAddonStatuses(itineraryId, useBkgDb = true)

      verify(protectionRepository, times(0)).getTripProtectionBookingInfoForItinerary(any())
      verify(protectionRepository).getTripProtectionBookingInfoForItineraryV2(itineraryId)

      result.itinerary.value.itineraryId shouldBe itineraryId
      result.itinerary.value.flights.head.bookingStatus shouldBe CreatedBookingStatus.BookingCharged
      result.itinerary.value.addOns.head.bookingStatus shouldBe CreatedBookingStatus.BookingConfirmed
    }
  }

  test("enableGetStatusDataFromBkgDb is A - getItineraryStatus Case: Flight + Generic Addon (Trip Protection)") {
    val (itineraryId, mockRequest) = new WithVariantB().setupStatusRequestForFlightAndGenericAddon(isB = false)
    commonBookingsService.getItineraryStatus(mockRequest, Nil)(mockRequestContext).map { result =>
      verify(flightInfoService).getFlightsItineraryStatus(itineraryId)
      verifyNoInteractions(flightInfoServiceV2, protectionRepository)
      verify(mockAddOnInfoService, times(1)).getGenericAddonStatuses(itineraryId, useBkgDb = false)

      result.itinerary.nonEmpty shouldBe true
      result.itinerary.get.itineraryId shouldBe itineraryId
      result.itinerary.get.protections.isEmpty shouldBe true
      result.itinerary.get.flights.head.bookingStatus shouldBe CreatedBookingStatus.BookingCharged
      result.itinerary.get.addOns.head.bookingStatus shouldBe CreatedBookingStatus.BookingConfirmed
    }

  }

  test("enableGetStatusDataFromBkgDb is B - getItineraryStatus Case: Flight + Generic Addon (Trip Protection)") {
    val (itineraryId, mockRequest) = new WithVariantB().setupStatusRequestForFlightAndGenericAddon(isB = true)
    commonBookingsService.getItineraryStatus(mockRequest, Nil)(mockRequestContext).map { result =>
      verify(flightInfoServiceV2).getFlightsItineraryStatus(itineraryId)
      verifyNoInteractions(flightInfoService, protectionRepository)
      verify(mockAddOnInfoService, times(1)).getGenericAddonStatuses(itineraryId, useBkgDb = true)

      result.itinerary.nonEmpty shouldBe true
      result.itinerary.get.itineraryId shouldBe itineraryId
      result.itinerary.get.protections.isEmpty shouldBe true
      result.itinerary.get.flights.head.bookingStatus shouldBe CreatedBookingStatus.BookingCharged
      result.itinerary.get.addOns.head.bookingStatus shouldBe CreatedBookingStatus.BookingConfirmed
    }

  }

  class WithVariantB {

    /**
      * for test getItineraryStatus - etItineraryStatus Case: Flight + Trip Protection + Generic Addon
      */
    def setupStatusRequestForFlightAndProtectionAndGenericAddon(isB: Boolean): (Long, GetStatusRequest) = {
      val mockRequest = mock[GetStatusRequest]
      val itineraryId = 16L
      val actionId    = 11L
      val mockProtectionBookingInfo = {
        val me = mock[ProtectionBookingInfo]
        when(me.protectionBookingId).thenReturn(112233)
        when(me.protectionBookingStateId).thenReturn(ProtectionBookingState.Purchased)
        when(me.supplierPolicyId).thenReturn(None)
        when(me.protectionTypeId).thenReturn(1)
        me
      }

      when(mockRequest.statusToken).thenReturn("Just a mock")
      when(mockFeatureAware.enableGetStatusDataFromBkgDb).thenReturn(isB)
      when(mockStatusTokenService.materializeToken(anyString())).thenReturn(
        Future.successful(
          StatusToken(
            itineraryId = itineraryId,
            actionId = actionId,
            productType = Set(ProductTypeEnum.Protection.toString, ProductTypeEnum.Flight.toString),
            dc = "BK"
          )
        )
      )
      when(workflowRepository.getBookingActionByItineraryId(itineraryId)).thenReturn(Future.successful(Seq.empty))
      when(flightInfoService.getFlightsItineraryStatus(any()))
        .thenReturn(Future.successful(Seq(creation.FlightBooking(bookingStatus = CreatedBookingStatus.BookingCharged))))
      when(flightInfoServiceV2.getFlightsItineraryStatus(any()))
        .thenReturn(Future.successful(Seq(creation.FlightBooking(bookingStatus = CreatedBookingStatus.BookingCharged))))
      when(protectionRepository.getTripProtectionBookingInfoForItinerary(any()))
        .thenReturn(Future.successful(Seq(mockProtectionBookingInfo)))
      when(protectionRepository.getTripProtectionBookingInfoForItineraryV2(any()))
        .thenReturn(Future.successful(Seq(mockProtectionBookingInfo)))
      when(bookingActionMessageService.getContinueBookingActionMessage(actionId)(mockRequestContext))
        .thenReturn(Future.successful(None))
      when(mockAddOnInfoService.getGenericAddonStatuses(itineraryId, isB)).thenReturn(
        Future.successful(
          Seq(
            GenericProductCreateResult(
              bookingId = 112244,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              productTypeId = ProductType.TripProtection.id
            )
          )
        )
      )
      (itineraryId, mockRequest)
    }

    def setupStatusRequestForFlightAndGenericAddon(isB: Boolean): (Long, GetStatusRequest) = {
      val mockRequest = mock[GetStatusRequest]
      when(mockRequest.statusToken).thenReturn("Just a mock")
      val itineraryId = 16L
      val actionId    = 11L
      when(mockStatusTokenService.materializeToken(anyString())).thenReturn(
        Future.successful(
          StatusToken(
            itineraryId = itineraryId,
            actionId = actionId,
            productType = Set(ProductType.TripProtection.toString, ProductTypeEnum.Flight.toString),
            dc = "BK"
          )
        )
      )
      when(mockFeatureAware.enableGetStatusDataFromBkgDb).thenReturn(isB)
      when(workflowRepository.getBookingActionByItineraryId(itineraryId)).thenReturn(Future.successful(Seq.empty))
      when(flightInfoService.getFlightsItineraryStatus(any()))
        .thenReturn(Future.successful(Seq(creation.FlightBooking(bookingStatus = CreatedBookingStatus.BookingCharged))))
      when(flightInfoServiceV2.getFlightsItineraryStatus(any()))
        .thenReturn(Future.successful(Seq(creation.FlightBooking(bookingStatus = CreatedBookingStatus.BookingCharged))))
      when(mockAddOnInfoService.getProtectionBookingStatus(itineraryId)).thenReturn(
        Future.successful(
          Seq(
            ProtectionBooking(
              bookingId = 112233,
              protectionTypeId = 1,
              bookingStatus = CreatedBookingStatus.BookingConfirmed
            )
          )
        )
      )
      when(bookingActionMessageService.getContinueBookingActionMessage(actionId)(mockRequestContext))
        .thenReturn(Future.successful(None))
      when(mockAddOnInfoService.getGenericAddonStatuses(itineraryId, isB)).thenReturn(
        Future.successful(
          Seq(
            GenericProductCreateResult(
              bookingId = 112244,
              bookingStatus = CreatedBookingStatus.BookingConfirmed,
              productTypeId = ProductType.TripProtection.id
            )
          )
        )
      )
      (itineraryId, mockRequest)
    }
  }

}
