package com.agoda.bapi.server.service.rebookAndCancel

import com.agoda.bapi.common.config.KillSwitches
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.model.WhiteLabelInfo
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.rebookAndCancel.RebookAndCancelRequirement
import com.agoda.bapi.common.model.{UserContextMock, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.service.{FeatureAware, MessagesBag}
import com.agoda.bapi.common.token.BookingTokenEncryptionHelper
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.service.RebookAndCancelServiceImpl
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import mocks.RequestContextMock
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar
import serializer.Serialization

import scala.util.Success

class RebookAndCancelServiceSpec
    extends AsyncWordSpec
    with Matchers
    with MockitoSugar
    with ScalaFutures
    with RequestContextMock {

  val messagesBag: MessagesBag       = mock[MessagesBag]
  val mockFeatureAware: FeatureAware = mock[FeatureAware]
  val mockKillSwitches: KillSwitches = mock[KillSwitches]

  implicit val context: RequestContext = requestContext(messagesBag).copy(featureAware = Some(mockFeatureAware))

  implicit val setupBookingContext: SetupBookingContext = SetupBookingContext(
    BookingFlow.SingleFlight,
    context,
    "",
    WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
    None,
    sessionId = "",
    bookingSessionId = ""
  )

  "When RebookAndCancel Enable " should {

    val mockPropertyItem = mock[PropertyRequestItem]

    val mockRebookAndCancelToken =
      "b0zgukNxTcp9hygq2+tJk4lSs2PoqpjNzuUFBGbhZEUwhIfvhl0Uchcw60RR9IzJMQ8AdkY1WrkPLgcznLhrRTx+uZxKAzSZ6DC69J2//+zOAEFARytV1xgKo/o0MhTeZdyqpDGf4SVJKDwuXmJJFyBGHDeSOXv+GLE0cAhubIauafzGEkvbbNszJPmaFvcmo3pQzywQmXAexwwwxiESR256ldDH2BXcV2ypG6qkT/E="

    val rebookAndCancelRequest = RebookAndCancelRequest(
      TokenMessage(mockRebookAndCancelToken, 1)
    )

    val mockSetupRequest = SetupBookingRequest(
      productsRequest = ProductsRequest(
        propertyRequests = Seq(mockPropertyItem)
      ),
      rebookAndCancelRequest = Some(rebookAndCancelRequest)
    )

    when(mockKillSwitches.enableRebookAndCancelFlow).thenReturn(true)

    "decrypt token and map to RebookAndCancelRequirement" in {
      val cancelAndRebookRequirement = RebookAndCancelRequirement(
        originalBookingId = 123,
        originalItineraryId = 456
      )

      val service = new RebookAndCancelServiceImpl(mockKillSwitches)
      val result  = service.getRebookAndCancelRequirement(mockSetupRequest)

      result shouldBe Some(cancelAndRebookRequirement)
    }

    "return None if token is empty" in {
      val mockTokenMessage           = mock[TokenMessage]
      val mockSetupBookingRequest    = mock[SetupBookingRequest]
      val mockCancelAndRebookRequest = mock[RebookAndCancelRequest]

      when(mockCancelAndRebookRequest.tokenMessage).thenReturn(mockTokenMessage)
      when(mockTokenMessage.token).thenReturn("")

      when(mockSetupBookingRequest.rebookAndCancelRequest).thenReturn(Some(mockCancelAndRebookRequest))

      val service = new RebookAndCancelServiceImpl(mockKillSwitches)
      val result  = service.getRebookAndCancelRequirement(mockSetupBookingRequest)

      result shouldBe None
    }

    "throw an exception if token decryption fails" in {

      val mockTokenMessage = mock[TokenMessage]
      when(mockTokenMessage.token).thenReturn("invalidToken")
      when(mockTokenMessage.version).thenReturn(1)

      val mockSetupBookingRequest    = mock[SetupBookingRequest]
      val mockCancelAndRebookRequest = mock[RebookAndCancelRequest]

      when(mockCancelAndRebookRequest.tokenMessage).thenReturn(mockTokenMessage)
      when(mockSetupBookingRequest.rebookAndCancelRequest).thenReturn(Some(mockCancelAndRebookRequest))
      when(mockCancelAndRebookRequest.tokenMessage).thenReturn(mockTokenMessage)

      val service = new RebookAndCancelServiceImpl(mockKillSwitches)

      assertThrows[Exception] {
        service.getRebookAndCancelRequirement(mockSetupBookingRequest)
      }
    }
  }

  "When RebookAndCancel Not Enable" should {

    "if kill switch disabled then return None" in {

      when(mockKillSwitches.enableRebookAndCancelFlow).thenReturn(false)

      val mockSetupRequest = SetupBookingRequest(
        productsRequest = ProductsRequest(
          propertyRequests = Seq.empty
        ),
        rebookAndCancelRequest = Some(mock[RebookAndCancelRequest])
      )

      val service = new RebookAndCancelServiceImpl(mockKillSwitches)
      val result  = service.getRebookAndCancelRequirement(mockSetupRequest)

      result shouldBe None
    }

    "if rebookAndCancel request is empty then return None" in {

      when(mockKillSwitches.enableRebookAndCancelFlow).thenReturn(true)

      val mockSetupRequest = SetupBookingRequest(
        productsRequest = ProductsRequest(
          propertyRequests = Seq.empty
        ),
        rebookAndCancelRequest = None
      )

      val service = new RebookAndCancelServiceImpl(mockKillSwitches)
      val result  = service.getRebookAndCancelRequirement(mockSetupRequest)

      result shouldBe None
    }
  }
}
