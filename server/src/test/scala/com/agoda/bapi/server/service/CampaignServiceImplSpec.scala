package com.agoda.bapi.server.service

import akka.actor.ActorSystem
import com.agoda.bapi.common.config.{ApiCallSettings, Configuration}
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message._
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.model.creation.DiscountType
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.model.{CampaignDiscountType, CampaignInfo, CampaignInfoInternal, CampaignMessage, CampaignPromotionInfo, CampaignStatusType, CampaignType, CampaignValidDateType, EligibleDiscount, PromoCmsData, PromotionCampaignTypes, PromotionCodeType, UserContextMock, WhiteLabelInfo}
import com.agoda.bapi.common.service.{FeatureAware, MessagesBag}
import com.agoda.bapi.server.addon.AddOnDefaults.requestContext
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.BookingPropertiesData
import com.agoda.bapi.server.proxy.GandalfProxy
import com.agoda.bapi.server.utils.PromotionUtils
import com.agoda.gandalf.common.CrgwCampaignProductType
import com.agoda.gandalf.response.{CreditCardPromotionResponse, Discount}
import com.agoda.papi.enums.campaign.InelegiblePromotionReasons.{InvalidPromocode, InvalidStateId}
import com.agoda.papi.enums.campaign.CampaignDiscountTypes
import com.agoda.papi.enums.campaign.{CampaignDiscountTypes => PAPICampaignDiscountTypes, CampaignStatusTypes => PAPICampaignStatusTypes, CampaignValidDateType => PAPICampaignValidDateType, PromotionCodeType => PAPIPromotionCodeType}
import com.softwaremill.quicklens._
import integration.mocks.ServerCDBProxyMock
import mocks.BookingMockHelper.{baseChildrenRoom, baseProperty}
import org.joda.time.{DateTime, LocalDate}
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.{any, eq => eqTo}
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar
import transformers.{EnrichedCampaign, EnrichedCampaignMessage, EnrichedPromoCmsData}

import scala.concurrent.{ExecutionContext, Future}

class CampaignServiceImplSpec
    extends AsyncWordSpec
    with ScalaFutures
    with BeforeAndAfter
    with Matchers
    with MockitoSugar {
  private val gandalfProxyMock = mock[GandalfProxy]
  private val serverCDBMock    = new ServerCDBProxyMock()
  private val configMock       = mock[Configuration]
  val featureAware             = mock[FeatureAware]
  val messagesBag              = mock[MessagesBag]
  val userContext              = Some(UserContextMock.value.copy(requestOrigin = "TH"))

  implicit val setupContext: SetupBookingContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)

  when(configMock.apiCallSettings).thenReturn(
    ApiCallSettings(papiMaxRetries = 1, paymentApiV2MaxRetries = 1, gandalfMaxRetries = 2)
  )
  private val campaignService = new CampaignServiceImpl(gandalfProxyMock, serverCDBMock, configMock) {
    override def withMeasure(metricName: String, value: Long = 1, tags: Map[String, String] = Map.empty): Future[Unit] =
      Future.unit
  }

  private val basePropertyProductItems = BookingPropertiesData(
    id = "1",
    content = "",
    papiProperties = Some(transformers.Properties(property = Seq(baseProperty()), debug = None)),
    packageRequest = None,
    papiPropertyStatus = PapiPropertyStatus.Ok,
    selectedChargeOption = None
  )

  val context: RequestContext =
    requestContext(messagesBag, userContext).copy(featureAware = Some(featureAware))

  implicit val ec: ExecutionContext = ActorSystem("CampaignServiceImplSpec").dispatcher
  val requestContextMock            = mock[RequestContext]
  when(requestContextMock.featureAware).thenReturn(None)

  before {
    reset(gandalfProxyMock)
  }

  "searchCampaignByCcBinV2" should {

    "map and return Flight CampaignInfoInternal correctly from gandalfProxyMock searchCampaignV2" in {
      val request = SearchCampaignRequestV2(
        siteId = Some(2302934),
        countryId = Some(23),
        paymentMethodId = Some(2),
        emailDomain = Some("@agoda.com"),
        bin = Some("411111"),
        exactBinMatch = Some(true),
        ccof = None,
        ccToken = None,
        overrideCampaigns = PromotionUtils.overrideCampaign
      )
      val mockCcCampaignResponse = CreditCardPromotionResponse(
        id = 1230913,
        campaignId = 23492,
        siteId = Some(20234),
        displayName = "cc contactless reduce COVID-19 chance",
        bin = Some("233333"),
        discount = Discount(
          discountType = "PERCENT",
          amount = 5.0,
          currency = Some("USD")
        ),
        membershipContentId = None,
        membershipContentCmsId = None,
        productType = CrgwCampaignProductType.Flight,
        isCashback = None
      )
      when(
        gandalfProxyMock.searchCampaignV2(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any(),
          any()
        )
      )
        .thenReturn(Future.successful(Vector(mockCcCampaignResponse)))

      val expectedResult = Seq(
        CampaignInfoInternal(
          campaignInfo = CampaignInfo(
            id = mockCcCampaignResponse.campaignId,
            cid = mockCcCampaignResponse.siteId.get,
            promotionCode = "",
            discountType = DiscountType.Percent,
            amount = 5.0,
            currency = Some("USD")
          ),
          promotionId = Some(mockCcCampaignResponse.id)
        )
      )

      val flightItem     = mock[FlightRequestItem]
      val protectionItem = mock[TripProtectionRequestItem]
      val productRequest =
        ProductsRequest(flightRequests = Seq(flightItem), tripProtectionRequests = Some(Seq(protectionItem)))

      campaignService
        .searchCampaignByCcBinV2(request, BookingFlow.FlightWithProtection, productRequest)(requestContextMock, ec)
        .map { actual =>
          verify(gandalfProxyMock, times(1)).searchCampaignV2(
            eqTo(request.siteId),
            eqTo(request.countryId),
            eqTo(request.paymentMethodId),
            eqTo(request.emailDomain),
            eqTo(request.bin),
            eqTo(request.exactBinMatch),
            eqTo(request.ccof),
            eqTo(request.ccToken),
            eqTo(request.overrideCampaigns),
            eqTo(CrgwCampaignProductType.Flight)
          )(any(), any())
          actual shouldBe expectedResult
        }
    }

    "return empty when problem with GandalfProxy searchCampaignV2" in {
      val request = SearchCampaignRequestV2(
        siteId = Some(2302934),
        countryId = Some(23),
        paymentMethodId = Some(2),
        emailDomain = Some("@agoda.com"),
        bin = Some("411111"),
        exactBinMatch = Some(true),
        ccof = None,
        ccToken = None
      )
      when(
        gandalfProxyMock.searchCampaignV2(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any(),
          any()
        )
      )
        .thenReturn(Future.failed(new Exception("Gandalf shall not pass")))

      val flightItem     = mock[FlightRequestItem]
      val productRequest = ProductsRequest(flightRequests = Seq(flightItem))
      campaignService
        .searchCampaignByCcBinV2(request, BookingFlow.SingleFlight, productRequest)(requestContextMock, ec)
        .map { actual =>
          actual shouldBe Seq.empty
        }
    }

    "return empty when productType property searchCampaignV2" in {
      val request = SearchCampaignRequestV2(
        siteId = Some(2302934),
        countryId = Some(23),
        paymentMethodId = Some(2),
        emailDomain = Some("@agoda.com"),
        bin = Some("411111"),
        exactBinMatch = Some(true),
        ccof = None,
        ccToken = None
      )
      when(
        gandalfProxyMock.searchCampaignV2(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any(),
          any()
        )
      )
        .thenReturn(Future.failed(new Exception("Gandalf shall not pass")))

      val propertyItem   = mock[PropertyRequestItem]
      val productRequest = ProductsRequest(propertyRequests = Seq(propertyItem))

      campaignService
        .searchCampaignByCcBinV2(request, BookingFlow.SingleProperty, productRequest)(requestContextMock, ec)
        .map { actual =>
          actual shouldBe Seq.empty
        }
    }

    "contain CrgwCampaignProductType.Flight in searchCampaignV2 when BookingFlow is Cart with flight related" in {
      val request = SearchCampaignRequestV2(
        siteId = Some(2302934),
        countryId = Some(23),
        paymentMethodId = Some(2),
        emailDomain = Some("@agoda.com"),
        bin = Some("411111"),
        exactBinMatch = Some(true),
        ccof = None,
        ccToken = None
      )
      when(
        gandalfProxyMock.searchCampaignV2(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any(),
          any()
        )
      )
        .thenReturn(Future.failed(new Exception("Gandalf shall not pass")))

      val flightItem     = mock[FlightRequestItem]
      val protectionItem = mock[TripProtectionRequestItem]
      val productRequest = ProductsRequest(
        flightRequests = Seq(flightItem, flightItem, flightItem),
        tripProtectionRequests = Some(Seq(protectionItem))
      )

      campaignService.searchCampaignByCcBinV2(request, BookingFlow.Cart, productRequest)(requestContextMock, ec).map {
        actual =>
          verify(gandalfProxyMock)
            .searchCampaignV2(
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              ArgumentMatchers.eq(CrgwCampaignProductType.Flight)
            )(
              any(),
              any()
            )
          actual shouldBe Seq.empty
      }
    }

    "contain CrgwCampaignProductType.Property in searchCampaignV2 when BookingFlow is Cart with non-flight related" in {
      val request = SearchCampaignRequestV2(
        siteId = Some(2302934),
        countryId = Some(23),
        paymentMethodId = Some(2),
        emailDomain = Some("@agoda.com"),
        bin = Some("411111"),
        exactBinMatch = Some(true),
        ccof = None,
        ccToken = None
      )
      when(
        gandalfProxyMock.searchCampaignV2(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any(),
          any()
        )
      )
        .thenReturn(Future.failed(new Exception("Gandalf shall not pass")))

      val flightItem     = mock[FlightRequestItem]
      val protectionItem = mock[TripProtectionRequestItem]
      val carItem        = mock[CarRequestItem]
      val productRequest = ProductsRequest(
        flightRequests = Seq(flightItem, flightItem, flightItem),
        tripProtectionRequests = Some(Seq(protectionItem)),
        carRequestsOpt = Some(Seq(carItem))
      )

      campaignService.searchCampaignByCcBinV2(request, BookingFlow.Cart, productRequest)(requestContextMock, ec).map {
        actual =>
          verify(gandalfProxyMock)
            .searchCampaignV2(
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              ArgumentMatchers.eq(CrgwCampaignProductType.Property)
            )(
              any(),
              any()
            )
          actual shouldBe Seq.empty
      }
    }
  }

  "getCampaignInfo" should {
    val campaignInfoRequest = CampaignInfoRequest(Some(1), 1, "TEST")

    val propertyRequest = PropertyRequestItem(
      id = "",
      propertySearchCriteria = PropertySearchCriteria(
        propertyId = Some(1),
        roomIdentifier = "",
        occupancyRequest = com.agoda.bapi.common.message.OccupancyRequest(1, 0, 1),
        durationRequest = com.agoda.bapi.common.message.DurationRequest(LocalDate.now(), 1),
        pricingRequest = Some(
          PricingRequest(
            isMse = false,
            requiredPrice = "",
            requiredBasis = "",
            isRPM2Included = false,
            selectedPointMaxId = None,
            isIncludeUsdAndLocalCurrency = false,
            allowOverrideOccupancy = false,
            enableOpaqueChannel = false,
            isAllowRoomTypeNotGuarantee = false,
            synchronous = true,
            partnerLoyaltyProgramId = Some(0)
          )
        ),
        papiContextRequest =
          Some(PapiContextRequest(true, trafficTypeId = Some(1), rawBotProfile = "", showCms = false)),
        roomSelectionRequest = None,
        propertyRequest = None,
        simplifiedRoomSelectionRequest = None,
        partnerRequest = None
      )
    )
    "map and return campaign list correctly" in {
      val mockWhiteLabelInfo = mock[WhiteLabelInfo]
      when(requestContextMock.whiteLabelInfo).thenReturn(mockWhiteLabelInfo)
      when(mockWhiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.CampaignInfoCheck)).thenReturn(true)

      val expected = Seq(CampaignInfoInternal(CampaignInfo(1, 1, "TEST", DiscountType.Percent, 0.0, None), None))

      campaignService
        .getCampaignInfo(Some("0"), Some(campaignInfoRequest), Some(propertyRequest))(requestContextMock, ec)
        .map { actual =>
          actual shouldBe expected
        }
    }

    "return campaign list when campaign info is empty" in
      campaignService.getCampaignInfo(None, None, Some(propertyRequest))(requestContextMock, ec).map { actual =>
        actual shouldBe Seq.empty
      }

    "return empty campaign list when cid is empty" in
      campaignService.getCampaignInfo(None, None, Some(propertyRequest))(requestContextMock, ec).map { actual =>
        actual shouldBe Seq.empty
      }

    "return empty campaign list when propertyRequests is empty" in
      campaignService.getCampaignInfo(Some("0"), Some(campaignInfoRequest), None)(requestContextMock, ec).map { actual =>
        actual shouldBe Seq.empty
      }

    "return empty campaign list when campaignInfoRequest and propertyRequests is empty" in
      campaignService.getCampaignInfo(Some("0"), None, None)(requestContextMock, ec).map { actual =>
        actual shouldBe Seq.empty
      }

    "return empty campaign list when CampaignInfoCheck feature is false" in {
      val mockWhiteLabelInfo = mock[WhiteLabelInfo]
      val mockFeatureAware   = mock[FeatureAware]
      when(requestContextMock.whiteLabelInfo).thenReturn(mockWhiteLabelInfo)
      when(requestContextMock.featureAware).thenReturn(Some(mockFeatureAware))
      when(mockWhiteLabelInfo.token).thenReturn(Some("token"))
      when(mockWhiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.CampaignInfoCheck)).thenReturn(false)

      campaignService
        .getCampaignInfo(Some("0"), Some(campaignInfoRequest), Some(propertyRequest))(requestContextMock, ec)
        .map { actual =>
          actual shouldBe Seq.empty
        }
    }
  }

  "getPromotionInfo" should {
    "return empty maximumCreditCardDiscount if ccBinCampaigns is empty" in {
      val ccBinCampaigns = Seq.empty
      val expected       = None
      val result         = campaignService.getPromotionInfo(Seq.empty, Seq.empty, ccBinCampaigns)
      result.maximumCreditCardDiscount shouldBe expected
    }

    "return maximumCreditCardDiscount if ccBinCampaigns is not empty" in {
      val ccBinCampaigns = Seq(
        CampaignInfoInternal(
          campaignInfo = CampaignInfo(
            id = 1,
            cid = 1,
            promotionCode = "AGODA20",
            discountType = DiscountType.Percent,
            amount = 5.0,
            currency = None
          ),
          promotionId = Some(1)
        )
      )
      val expected = Some(EligibleDiscount(DiscountType.Percent, 5.0, None))
      val result   = campaignService.getPromotionInfo(Seq.empty, Seq.empty, ccBinCampaigns)
      result.maximumCreditCardDiscount shouldBe expected
    }

    "return maximumCreditCardDiscount with maximum discount rate if ccBinCampaigns contains more than one element" in {
      val ccBinCampaigns = Seq(
        CampaignInfoInternal(
          campaignInfo = CampaignInfo(
            id = 1,
            cid = 1,
            promotionCode = "",
            discountType = DiscountType.Percent,
            amount = 5.0,
            currency = None
          ),
          promotionId = Some(1)
        ),
        CampaignInfoInternal(
          campaignInfo = CampaignInfo(
            id = 2,
            cid = 1,
            promotionCode = "",
            discountType = DiscountType.Percent,
            amount = 10.0,
            currency = None
          ),
          promotionId = Some(2)
        )
      )
      val expected = Some(EligibleDiscount(DiscountType.Percent, 10.0, None))
      val result   = campaignService.getPromotionInfo(Seq.empty, Seq.empty, ccBinCampaigns)
      result.maximumCreditCardDiscount shouldBe expected
    }

    "return maximumPromotionCodeDiscount promo eligible is true on B-Side of PMC-5507" in {

      val mockRequestContext = mock[RequestContext]
      val mockFeatureAware   = mock[FeatureAware]

      when(mockFeatureAware.removeSpBAPIGetPMCCampaignPromotion).thenReturn(true)
      when(mockRequestContext.featureAware).thenReturn(Some(mockFeatureAware))
      when(setupContext.requestContext).thenReturn(context.copy(featureAware = Some(mockFeatureAware)))

      val propertyPromotionEligible =
        basePropertyProductItems
          .modify(_.papiProperties.each.property.each.masterRooms.each.childrenRooms.each.promotionEligible)
          .setTo(Some(true))
      val promoCodeCampaigns = Seq(
        CampaignInfoInternal(
          campaignInfo = CampaignInfo(
            id = 1,
            cid = 1,
            promotionCode = "AGODA20",
            discountType = DiscountType.Percent,
            amount = 5.0,
            currency = None
          ),
          promotionId = Some(1)
        )
      )
      val expected = Some(EligibleDiscount(DiscountType.Percent, 5.0, None))
      val result =
        campaignService.getPromotionInfo(Seq(propertyPromotionEligible), promoCodeCampaigns, Seq.empty)(setupContext)
      result.maximumPromotionCodeDiscount shouldBe None
    }

    "return maximumPromotionCodeDiscount promo eligible is true on A-Side of PMC-5507" in {
      val mockRequestContext = mock[RequestContext]
      val mockFeatureAware   = mock[FeatureAware]

      when(mockFeatureAware.removeSpBAPIGetPMCCampaignPromotion).thenReturn(false)
      when(mockRequestContext.featureAware).thenReturn(Some(mockFeatureAware))
      when(setupContext.requestContext).thenReturn(context.copy(featureAware = Some(mockFeatureAware)))

      val propertyPromotionEligible =
        basePropertyProductItems
          .modify(_.papiProperties.each.property.each.masterRooms.each.childrenRooms.each.promotionEligible)
          .setTo(Some(true))
      val promoCodeCampaigns = Seq(
        CampaignInfoInternal(
          campaignInfo = CampaignInfo(
            id = 1,
            cid = 1,
            promotionCode = "AGODA20",
            discountType = DiscountType.Percent,
            amount = 5.0,
            currency = None
          ),
          promotionId = Some(1)
        )
      )
      val expected = Some(EligibleDiscount(DiscountType.Percent, 5.0, None))
      val result =
        campaignService.getPromotionInfo(Seq(propertyPromotionEligible), promoCodeCampaigns, Seq.empty)(setupContext)
      result.maximumPromotionCodeDiscount shouldBe expected
    }

    "return None for maximumPromotionCodeDiscount promo eligible is false" in {
      val propertyPromotionEligible =
        basePropertyProductItems
          .modify(_.papiProperties.each.property.each.masterRooms.each.childrenRooms.each.promotionEligible)
          .setTo(Some(false))
      val promoCodeCampaigns = Seq(
        CampaignInfoInternal(
          campaignInfo = CampaignInfo(
            id = 1,
            cid = 1,
            promotionCode = "AGODA20",
            discountType = DiscountType.Percent,
            amount = 5.0,
            currency = None
          ),
          promotionId = Some(1)
        )
      )
      val expected = None
      val result   = campaignService.getPromotionInfo(Seq(propertyPromotionEligible), promoCodeCampaigns, Seq.empty)
      result.maximumPromotionCodeDiscount shouldBe expected
    }

    "return appliedCampaignTypes.propertyCampaignType correctly" in {
      val enrichedCampaign = EnrichedCampaign(
        campaignId = 1,
        cid = 1,
        promotionCode = "AGODA",
        description = "",
        isMarkUse = true,
        isSelected = true,
        inapplicableReasonString = None,
        inapplicableReason = None,
        campaignType = enumerations.CampaignTypes.CreditCard,
        campaignDiscountType = None,
        originalDiscountPercentage = None,
        isUnderCap = false,
        promotionCap = None,
        messages = None
      )

      val propertyWithAppliedCreditCardPromo =
        basePropertyProductItems
          .modify(_.papiProperties.each.property.each.masterRooms.each.childrenRooms.each.campaignPromotions)
          .setTo(Some(List(enrichedCampaign)))

      val expected = Some(PromotionCampaignTypes(propertyCampaignType = Some(CampaignType.PropertyCreditCard)))
      val result   = campaignService.getPromotionInfo(Seq(propertyWithAppliedCreditCardPromo), Seq.empty, Seq.empty)
      result.appliedCampaignTypes shouldBe expected
    }

    "return appliedCampaignTypes = None and appliedCampaignInfo = None when there is no used promotion campaign" in {
      val propertyWithoutAppliedCreditCardPromo =
        basePropertyProductItems
          .modify(_.papiProperties.each.property.each.masterRooms.each.childrenRooms.each.campaignPromotions)
          .setTo(Some(List.empty[EnrichedCampaign]))

      val result = campaignService.getPromotionInfo(Seq(propertyWithoutAppliedCreditCardPromo), Seq.empty, Seq.empty)
      result.appliedCampaignTypes shouldBe None
      result.appliedCampaignInfo shouldBe None
    }

    "return appliedCampaignInfo correctly for percentage discount" in {
      val enrichedCampaign = EnrichedCampaign(
        campaignId = 123,
        cid = 456,
        promotionCode = "AGODA",
        description = "AGODASALE Discount",
        isMarkUse = true,
        isSelected = true,
        inapplicableReasonString = None,
        inapplicableReason = None,
        campaignType = enumerations.CampaignTypes.PromotionCode,
        campaignDiscountType = Some(CampaignDiscountTypes.Percentage),
        originalDiscountPercentage = Some(15),
        isUnderCap = false,
        promotionCap = None,
        messages = None,
        originalDiscountAmount = None,
        originalDiscountCurrencyCode = None,
        validDateType = Some(PAPICampaignValidDateType.BookingDate),
        dateValidFrom = Some(DateTime.parse("2014-01-02T00:00:00.000+07:00")),
        dateValidUntil = Some(DateTime.parse("2025-11-02T23:59:59.999+07:00")),
        isAutoApply = true,
        isAutoApplyBookingForm = false
      )

      val propertyWithSelectedPromo =
        basePropertyProductItems
          .modify(_.papiProperties.each.property.each.masterRooms.each.childrenRooms.each.campaignPromotions)
          .setTo(Some(List(enrichedCampaign)))

      val expected = Some(PromotionCampaignTypes(propertyCampaignType = Some(CampaignType.PropertyPromotionCode)))
      val result   = campaignService.getPromotionInfo(Seq(propertyWithSelectedPromo), Seq.empty, Seq.empty)

      // Deprecated
      result.appliedCampaignTypes shouldBe expected
      result.appliedCampaignInfo shouldNot be(None)
      result.appliedCampaignInfo.get.campaignId shouldBe enrichedCampaign.campaignId
      result.appliedCampaignInfo.get.cid shouldBe enrichedCampaign.cid
      result.appliedCampaignInfo.get.promotionCode shouldBe enrichedCampaign.promotionCode
      result.appliedCampaignInfo.get.campaignName shouldBe enrichedCampaign.description
      result.appliedCampaignInfo.get.campaignDiscountType shouldBe Some(CampaignDiscountType.Percent)
      result.appliedCampaignInfo.get.originalDiscountPercentage shouldBe enrichedCampaign.originalDiscountPercentage
      result.appliedCampaignInfo.get.originalDiscountAmount shouldBe None
      result.appliedCampaignInfo.get.originalDiscountCurrencyCode shouldBe None
      result.appliedCampaignInfo.get.validDateType shouldBe Some(CampaignValidDateType.BookingDate)
      result.appliedCampaignInfo.get.dateValidFrom shouldBe enrichedCampaign.dateValidFrom
      result.appliedCampaignInfo.get.dateValidUntil shouldBe enrichedCampaign.dateValidUntil
      result.appliedCampaignInfo.get.isAutoApply shouldBe Some(enrichedCampaign.isAutoApply)
      result.appliedCampaignInfo.get.isAutoApplyBookingForm shouldBe Some(enrichedCampaign.isAutoApplyBookingForm)
      result.appliedCampaignInfo.get.inapplicableReason shouldBe None
      result.appliedCampaignInfo.get.inapplicableReasonString shouldBe None

      result.appliedCampaigns.flatMap(_.headOption) shouldNot be(None)
      val appliedCampaign = result.appliedCampaigns.get.head
      appliedCampaign.campaignType shouldBe Some(CampaignType.PropertyPromotionCode)
      appliedCampaign.campaignId shouldBe enrichedCampaign.campaignId
      appliedCampaign.cid shouldBe enrichedCampaign.cid
      appliedCampaign.promotionCode shouldBe enrichedCampaign.promotionCode
      appliedCampaign.campaignName shouldBe enrichedCampaign.description
      appliedCampaign.campaignDiscountType shouldBe Some(CampaignDiscountType.Percent)
      appliedCampaign.originalDiscountPercentage shouldBe enrichedCampaign.originalDiscountPercentage
      appliedCampaign.originalDiscountAmount shouldBe None
      appliedCampaign.originalDiscountCurrencyCode shouldBe None
      appliedCampaign.validDateType shouldBe Some(CampaignValidDateType.BookingDate)
      appliedCampaign.dateValidFrom shouldBe enrichedCampaign.dateValidFrom
      appliedCampaign.dateValidUntil shouldBe enrichedCampaign.dateValidUntil
      appliedCampaign.isAutoApply shouldBe Some(enrichedCampaign.isAutoApply)
      appliedCampaign.isAutoApplyBookingForm shouldBe Some(enrichedCampaign.isAutoApplyBookingForm)
      appliedCampaign.inapplicableReason shouldBe None
      appliedCampaign.inapplicableReasonString shouldBe None
    }

    "return appliedCampaignInfo correctly for amount discount" in {
      val enrichedCampaign = EnrichedCampaign(
        campaignId = 123,
        cid = 456,
        promotionCode = "AGODA",
        description = "AGODASALE Discount",
        isMarkUse = true,
        isSelected = true,
        inapplicableReasonString = None,
        inapplicableReason = None,
        campaignType = enumerations.CampaignTypes.PromotionCode,
        campaignDiscountType = Some(CampaignDiscountTypes.Amount),
        originalDiscountPercentage = None,
        isUnderCap = false,
        promotionCap = None,
        messages = None,
        originalDiscountAmount = Some(500),
        originalDiscountCurrencyCode = Some("THB"),
        validDateType = Some(PAPICampaignValidDateType.CheckInDate),
        dateValidFrom = Some(DateTime.parse("2014-01-02T00:00:00.000+07:00")),
        dateValidUntil = Some(DateTime.parse("2025-11-02T23:59:59.999+07:00")),
        isAutoApply = true,
        isAutoApplyBookingForm = false
      )

      val propertyWithSelectedPromo =
        basePropertyProductItems
          .modify(_.papiProperties.each.property.each.masterRooms.each.childrenRooms.each.campaignPromotions)
          .setTo(Some(List(enrichedCampaign)))

      val expected = Some(PromotionCampaignTypes(propertyCampaignType = Some(CampaignType.PropertyPromotionCode)))
      val result   = campaignService.getPromotionInfo(Seq(propertyWithSelectedPromo), Seq.empty, Seq.empty)

      // Deprecated
      result.appliedCampaignTypes shouldBe expected
      result.appliedCampaignInfo shouldNot be(None)
      result.appliedCampaignInfo.get.campaignId shouldBe enrichedCampaign.campaignId
      result.appliedCampaignInfo.get.cid shouldBe enrichedCampaign.cid
      result.appliedCampaignInfo.get.promotionCode shouldBe enrichedCampaign.promotionCode
      result.appliedCampaignInfo.get.campaignName shouldBe enrichedCampaign.description
      result.appliedCampaignInfo.get.campaignDiscountType shouldBe Some(CampaignDiscountType.Amount)
      result.appliedCampaignInfo.get.originalDiscountPercentage shouldBe None
      result.appliedCampaignInfo.get.originalDiscountAmount shouldBe enrichedCampaign.originalDiscountAmount
      result.appliedCampaignInfo.get.originalDiscountCurrencyCode shouldBe enrichedCampaign.originalDiscountCurrencyCode
      result.appliedCampaignInfo.get.validDateType shouldBe Some(CampaignValidDateType.CheckInDate)
      result.appliedCampaignInfo.get.dateValidFrom shouldBe enrichedCampaign.dateValidFrom
      result.appliedCampaignInfo.get.dateValidUntil shouldBe enrichedCampaign.dateValidUntil
      result.appliedCampaignInfo.get.isAutoApply shouldBe Some(enrichedCampaign.isAutoApply)
      result.appliedCampaignInfo.get.isAutoApplyBookingForm shouldBe Some(enrichedCampaign.isAutoApplyBookingForm)
      result.appliedCampaignInfo.get.inapplicableReason shouldBe None
      result.appliedCampaignInfo.get.inapplicableReasonString shouldBe None

      result.appliedCampaigns.flatMap(_.headOption) shouldNot be(None)
      val appliedCampaign = result.appliedCampaigns.get.head
      appliedCampaign.campaignType shouldBe Some(CampaignType.PropertyPromotionCode)
      appliedCampaign.campaignId shouldBe enrichedCampaign.campaignId
      appliedCampaign.cid shouldBe enrichedCampaign.cid
      appliedCampaign.promotionCode shouldBe enrichedCampaign.promotionCode
      appliedCampaign.campaignName shouldBe enrichedCampaign.description
      appliedCampaign.campaignDiscountType shouldBe Some(CampaignDiscountType.Amount)
      appliedCampaign.originalDiscountPercentage shouldBe None
      appliedCampaign.originalDiscountAmount shouldBe enrichedCampaign.originalDiscountAmount
      appliedCampaign.originalDiscountCurrencyCode shouldBe enrichedCampaign.originalDiscountCurrencyCode
      appliedCampaign.validDateType shouldBe Some(CampaignValidDateType.CheckInDate)
      appliedCampaign.dateValidFrom shouldBe enrichedCampaign.dateValidFrom
      appliedCampaign.dateValidUntil shouldBe enrichedCampaign.dateValidUntil
      appliedCampaign.isAutoApply shouldBe Some(enrichedCampaign.isAutoApply)
      appliedCampaign.isAutoApplyBookingForm shouldBe Some(enrichedCampaign.isAutoApplyBookingForm)
      appliedCampaign.inapplicableReason shouldBe None
      appliedCampaign.inapplicableReasonString shouldBe None
    }

    "return appliedCampaignInfo correctly for invalid coupon" in {
      val enrichedCampaign = EnrichedCampaign(
        campaignId = 123,
        cid = 456,
        promotionCode = "AGODA",
        description = "AGODASALE Discount",
        isMarkUse = true,
        isSelected = true,
        inapplicableReasonString = Some("InvalidPromocode"),
        inapplicableReason = Some(InvalidPromocode),
        campaignType = enumerations.CampaignTypes.PromotionCode,
        campaignDiscountType = Some(CampaignDiscountTypes.Amount),
        originalDiscountPercentage = None,
        isUnderCap = false,
        promotionCap = None,
        messages = None,
        originalDiscountAmount = Some(500),
        originalDiscountCurrencyCode = Some("THB"),
        validDateType = Some(PAPICampaignValidDateType.CheckInDate),
        dateValidFrom = Some(DateTime.parse("2014-01-02T00:00:00.000+07:00")),
        dateValidUntil = Some(DateTime.parse("2025-11-02T23:59:59.999+07:00")),
        isAutoApply = true,
        isAutoApplyBookingForm = false
      )

      val propertyWithSelectedPromo =
        basePropertyProductItems
          .modify(_.papiProperties.each.property.each.masterRooms.each.childrenRooms.each.campaignPromotions)
          .setTo(Some(List(enrichedCampaign)))

      val expected = Some(PromotionCampaignTypes(propertyCampaignType = Some(CampaignType.PropertyPromotionCode)))
      val result   = campaignService.getPromotionInfo(Seq(propertyWithSelectedPromo), Seq.empty, Seq.empty)

      // Deprecated
      result.appliedCampaignTypes shouldBe expected
      result.appliedCampaignInfo shouldNot be(None)
      result.appliedCampaignInfo.get.campaignId shouldBe enrichedCampaign.campaignId
      result.appliedCampaignInfo.get.cid shouldBe enrichedCampaign.cid
      result.appliedCampaignInfo.get.promotionCode shouldBe enrichedCampaign.promotionCode
      result.appliedCampaignInfo.get.campaignName shouldBe enrichedCampaign.description
      result.appliedCampaignInfo.get.campaignDiscountType shouldBe Some(CampaignDiscountType.Amount)
      result.appliedCampaignInfo.get.originalDiscountPercentage shouldBe None
      result.appliedCampaignInfo.get.originalDiscountAmount shouldBe enrichedCampaign.originalDiscountAmount
      result.appliedCampaignInfo.get.originalDiscountCurrencyCode shouldBe enrichedCampaign.originalDiscountCurrencyCode
      result.appliedCampaignInfo.get.validDateType shouldBe Some(CampaignValidDateType.CheckInDate)
      result.appliedCampaignInfo.get.dateValidFrom shouldBe enrichedCampaign.dateValidFrom
      result.appliedCampaignInfo.get.dateValidUntil shouldBe enrichedCampaign.dateValidUntil
      result.appliedCampaignInfo.get.isAutoApply shouldBe Some(enrichedCampaign.isAutoApply)
      result.appliedCampaignInfo.get.isAutoApplyBookingForm shouldBe Some(enrichedCampaign.isAutoApplyBookingForm)
      result.appliedCampaignInfo.get.inapplicableReason shouldBe Some(5)
      result.appliedCampaignInfo.get.inapplicableReasonString shouldBe Some("InvalidPromocode")

      result.appliedCampaigns.flatMap(_.headOption) shouldNot be(None)
      val appliedCampaign = result.appliedCampaigns.get.head
      appliedCampaign.campaignType shouldBe Some(CampaignType.PropertyPromotionCode)
      appliedCampaign.campaignId shouldBe enrichedCampaign.campaignId
      appliedCampaign.cid shouldBe enrichedCampaign.cid
      appliedCampaign.promotionCode shouldBe enrichedCampaign.promotionCode
      appliedCampaign.campaignName shouldBe enrichedCampaign.description
      appliedCampaign.campaignDiscountType shouldBe Some(CampaignDiscountType.Amount)
      appliedCampaign.originalDiscountPercentage shouldBe None
      appliedCampaign.originalDiscountAmount shouldBe enrichedCampaign.originalDiscountAmount
      appliedCampaign.originalDiscountCurrencyCode shouldBe enrichedCampaign.originalDiscountCurrencyCode
      appliedCampaign.validDateType shouldBe Some(CampaignValidDateType.CheckInDate)
      appliedCampaign.dateValidFrom shouldBe enrichedCampaign.dateValidFrom
      appliedCampaign.dateValidUntil shouldBe enrichedCampaign.dateValidUntil
      appliedCampaign.isAutoApply shouldBe Some(enrichedCampaign.isAutoApply)
      appliedCampaign.isAutoApplyBookingForm shouldBe Some(enrichedCampaign.isAutoApplyBookingForm)
      appliedCampaign.inapplicableReason shouldBe Some(5)
      appliedCampaign.inapplicableReasonString shouldBe Some("InvalidPromocode")
    }

    "return appliedCampaignInfo correctly for invalid inapplicableReason" in {
      val enrichedCampaign = EnrichedCampaign(
        campaignId = 123,
        cid = 456,
        promotionCode = "AGODA",
        description = "AGODASALE Discount",
        isMarkUse = true,
        isSelected = true,
        inapplicableReasonString = Some("InvalidPromocode"),
        inapplicableReason = Some(InvalidPromocode),
        campaignType = enumerations.CampaignTypes.PromotionCode,
        campaignDiscountType = Some(CampaignDiscountTypes.Amount),
        originalDiscountPercentage = None,
        isUnderCap = false,
        promotionCap = None,
        messages = None,
        originalDiscountAmount = Some(500),
        originalDiscountCurrencyCode = Some("THB"),
        validDateType = Some(PAPICampaignValidDateType.CheckInDate),
        dateValidFrom = Some(DateTime.parse("2014-01-02T00:00:00.000+07:00")),
        dateValidUntil = Some(DateTime.parse("2025-11-02T23:59:59.999+07:00")),
        isAutoApply = true,
        isAutoApplyBookingForm = false
      )

      val propertyWithSelectedPromo =
        basePropertyProductItems
          .modify(_.papiProperties.each.property.each.masterRooms.each.childrenRooms.each.campaignPromotions)
          .setTo(Some(List(enrichedCampaign)))

      val expected = Some(PromotionCampaignTypes(propertyCampaignType = Some(CampaignType.PropertyPromotionCode)))
      val result   = campaignService.getPromotionInfo(Seq(propertyWithSelectedPromo), Seq.empty, Seq.empty)

      // Deprecated
      result.appliedCampaignTypes shouldBe expected
      result.appliedCampaignInfo shouldNot be(None)
      result.appliedCampaignInfo.get.campaignId shouldBe enrichedCampaign.campaignId
      result.appliedCampaignInfo.get.cid shouldBe enrichedCampaign.cid
      result.appliedCampaignInfo.get.promotionCode shouldBe enrichedCampaign.promotionCode
      result.appliedCampaignInfo.get.campaignName shouldBe enrichedCampaign.description
      result.appliedCampaignInfo.get.campaignDiscountType shouldBe Some(CampaignDiscountType.Amount)
      result.appliedCampaignInfo.get.originalDiscountPercentage shouldBe None
      result.appliedCampaignInfo.get.originalDiscountAmount shouldBe enrichedCampaign.originalDiscountAmount
      result.appliedCampaignInfo.get.originalDiscountCurrencyCode shouldBe enrichedCampaign.originalDiscountCurrencyCode
      result.appliedCampaignInfo.get.validDateType shouldBe Some(CampaignValidDateType.CheckInDate)
      result.appliedCampaignInfo.get.dateValidFrom shouldBe enrichedCampaign.dateValidFrom
      result.appliedCampaignInfo.get.dateValidUntil shouldBe enrichedCampaign.dateValidUntil
      result.appliedCampaignInfo.get.isAutoApply shouldBe Some(enrichedCampaign.isAutoApply)
      result.appliedCampaignInfo.get.isAutoApplyBookingForm shouldBe Some(enrichedCampaign.isAutoApplyBookingForm)
      result.appliedCampaignInfo.get.inapplicableReason shouldBe Some(5)
      result.appliedCampaignInfo.get.inapplicableReasonString shouldBe Some("InvalidPromocode")

      result.appliedCampaigns.flatMap(_.headOption) shouldNot be(None)
      val appliedCampaign = result.appliedCampaigns.get.head
      appliedCampaign.campaignType shouldBe Some(CampaignType.PropertyPromotionCode)
      appliedCampaign.campaignId shouldBe enrichedCampaign.campaignId
      appliedCampaign.cid shouldBe enrichedCampaign.cid
      appliedCampaign.promotionCode shouldBe enrichedCampaign.promotionCode
      appliedCampaign.campaignName shouldBe enrichedCampaign.description
      appliedCampaign.campaignDiscountType shouldBe Some(CampaignDiscountType.Amount)
      appliedCampaign.originalDiscountPercentage shouldBe None
      appliedCampaign.originalDiscountAmount shouldBe enrichedCampaign.originalDiscountAmount
      appliedCampaign.originalDiscountCurrencyCode shouldBe enrichedCampaign.originalDiscountCurrencyCode
      appliedCampaign.validDateType shouldBe Some(CampaignValidDateType.CheckInDate)
      appliedCampaign.dateValidFrom shouldBe enrichedCampaign.dateValidFrom
      appliedCampaign.dateValidUntil shouldBe enrichedCampaign.dateValidUntil
      appliedCampaign.isAutoApply shouldBe Some(enrichedCampaign.isAutoApply)
      appliedCampaign.isAutoApplyBookingForm shouldBe Some(enrichedCampaign.isAutoApplyBookingForm)
      appliedCampaign.inapplicableReason shouldBe Some(5)
      appliedCampaign.inapplicableReasonString shouldBe Some("InvalidPromocode")
    }

    "return appliedCampaignInfo correctly for inapplicableReason invalidStateId" in {
      val enrichedCampaign = EnrichedCampaign(
        campaignId = 123,
        cid = 456,
        promotionCode = "PREFECTURE",
        description = "prefecture coupon",
        isMarkUse = true,
        isSelected = false,
        inapplicableReasonString = Some("InvalidStateId"),
        inapplicableReason = Some(InvalidStateId),
        campaignType = enumerations.CampaignTypes.PromotionCode,
        campaignDiscountType = Some(CampaignDiscountTypes.Amount),
        originalDiscountPercentage = None,
        isUnderCap = false,
        promotionCap = None,
        messages = None,
        originalDiscountAmount = Some(100),
        originalDiscountCurrencyCode = Some("JYP"),
        validDateType = Some(PAPICampaignValidDateType.CheckInDate),
        dateValidFrom = Some(DateTime.parse("2014-01-02T00:00:00.000+07:00")),
        dateValidUntil = Some(DateTime.parse("2025-11-02T23:59:59.999+07:00")),
        isAutoApply = true,
        isAutoApplyBookingForm = false,
        isStateIdRequired = Some(true)
      )

      val propertyWithSelectedPromo =
        basePropertyProductItems
          .modify(_.papiProperties.each.property.each.masterRooms.each.childrenRooms.each.campaignPromotions)
          .setTo(Some(List(enrichedCampaign)))

      val result =
        campaignService.getPromotionInfo(Seq(propertyWithSelectedPromo), Seq.empty, Seq.empty, Some("PREFECTURE"))

      // Deprecated
      result.appliedCampaignTypes shouldBe None
      result.appliedCampaignInfo shouldNot be(None)
      result.appliedCampaignInfo.get.campaignId shouldBe enrichedCampaign.campaignId
      result.appliedCampaignInfo.get.cid shouldBe enrichedCampaign.cid
      result.appliedCampaignInfo.get.promotionCode shouldBe enrichedCampaign.promotionCode
      result.appliedCampaignInfo.get.campaignName shouldBe enrichedCampaign.description
      result.appliedCampaignInfo.get.campaignDiscountType shouldBe Some(CampaignDiscountType.Amount)
      result.appliedCampaignInfo.get.originalDiscountPercentage shouldBe None
      result.appliedCampaignInfo.get.originalDiscountAmount shouldBe enrichedCampaign.originalDiscountAmount
      result.appliedCampaignInfo.get.originalDiscountCurrencyCode shouldBe enrichedCampaign.originalDiscountCurrencyCode
      result.appliedCampaignInfo.get.validDateType shouldBe Some(CampaignValidDateType.CheckInDate)
      result.appliedCampaignInfo.get.dateValidFrom shouldBe enrichedCampaign.dateValidFrom
      result.appliedCampaignInfo.get.dateValidUntil shouldBe enrichedCampaign.dateValidUntil
      result.appliedCampaignInfo.get.isAutoApply shouldBe Some(enrichedCampaign.isAutoApply)
      result.appliedCampaignInfo.get.isAutoApplyBookingForm shouldBe Some(enrichedCampaign.isAutoApplyBookingForm)
      result.appliedCampaignInfo.get.inapplicableReason shouldBe Some(12)
      result.appliedCampaignInfo.get.inapplicableReasonString shouldBe Some("InvalidStateId")
      result.appliedCampaignInfo.get.isStateIdRequired shouldBe Some(true)

      result.appliedCampaigns.flatMap(_.headOption) shouldNot be(None)
      val appliedCampaign = result.appliedCampaigns.get.head
      appliedCampaign.campaignType shouldBe Some(CampaignType.PropertyPromotionCode)
      appliedCampaign.campaignId shouldBe enrichedCampaign.campaignId
      appliedCampaign.cid shouldBe enrichedCampaign.cid
      appliedCampaign.promotionCode shouldBe enrichedCampaign.promotionCode
      appliedCampaign.campaignName shouldBe enrichedCampaign.description
      appliedCampaign.campaignDiscountType shouldBe Some(CampaignDiscountType.Amount)
      appliedCampaign.originalDiscountPercentage shouldBe None
      appliedCampaign.originalDiscountAmount shouldBe enrichedCampaign.originalDiscountAmount
      appliedCampaign.originalDiscountCurrencyCode shouldBe enrichedCampaign.originalDiscountCurrencyCode
      appliedCampaign.validDateType shouldBe Some(CampaignValidDateType.CheckInDate)
      appliedCampaign.dateValidFrom shouldBe enrichedCampaign.dateValidFrom
      appliedCampaign.dateValidUntil shouldBe enrichedCampaign.dateValidUntil
      appliedCampaign.isAutoApply shouldBe Some(enrichedCampaign.isAutoApply)
      appliedCampaign.isAutoApplyBookingForm shouldBe Some(enrichedCampaign.isAutoApplyBookingForm)
      appliedCampaign.inapplicableReason shouldBe Some(12)
      appliedCampaign.inapplicableReasonString shouldBe Some("InvalidStateId")
      appliedCampaign.isStateIdRequired shouldBe Some(true)
    }

    "return campaigns correctly" in {
      val aSelectedCampaign = EnrichedCampaign(
        campaignId = 1234,
        cid = 45678,
        promotionCode = "AGODA",
        description = "AGODASALE Discount",
        isMarkUse = true,
        isSelected = true,
        inapplicableReasonString = None,
        inapplicableReason = None,
        campaignType = enumerations.CampaignTypes.PromotionCode,
        campaignDiscountType = Some(PAPICampaignDiscountTypes.Percentage),
        originalDiscountPercentage = Some(15),
        isUnderCap = false,
        promotionCap = None,
        messages = None,
        originalDiscountAmount = None,
        originalDiscountCurrencyCode = None,
        validDateType = Some(PAPICampaignValidDateType.BookingDate),
        dateValidFrom = Some(DateTime.parse("2024-01-01T00:00:00.000+07:00")),
        dateValidUntil = Some(DateTime.parse("2025-11-30T23:59:59.999+07:00")),
        isAutoApply = true,
        isAutoApplyBookingForm = false,
        isStateIdRequired = None,
        hotelFundingText = Some("HFC"),
        campaignGroupId = None,
        isCashback = Some(false),
        promotionCodeType = Some(PAPIPromotionCodeType.MultiUse),
        status = Some(PAPICampaignStatusTypes.Selected),
        cms = Some(List(EnrichedPromoCmsData(1, Map("amount" -> "10"), 13))),
        campaignMessages = Some(
          Map(
            1 -> EnrichedCampaignMessage(message = "ABC", params = Map("urgencyType" -> "1")),
            2 -> EnrichedCampaignMessage(message = "DEF", params = Map("urgencyType" -> "2"))
          )
        )
      )

      val childRoom = baseChildrenRoom().copy(campaignPromotions = Some(List(aSelectedCampaign)))

      val result =
        campaignService.getPromotionInfo(Seq.empty, Seq.empty, Seq.empty, None, Some(childRoom))

      result.campaigns shouldNot be(Seq.empty)
      val campaignResult = result.campaigns.head
      campaignResult shouldBe CampaignPromotionInfo(
        campaignType = Some(CampaignType.PropertyPromotionCode),
        campaignId = 1234,
        cid = 45678,
        promotionCode = "AGODA",
        campaignName = "AGODASALE Discount",
        campaignDiscountType = Some(CampaignDiscountType.Percent),
        originalDiscountPercentage = Some(15),
        originalDiscountAmount = None,
        originalDiscountCurrencyCode = None,
        validDateType = Some(CampaignValidDateType.BookingDate),
        dateValidFrom = Some(DateTime.parse("2024-01-01T00:00:00.000+07:00")),
        dateValidUntil = Some(DateTime.parse("2025-11-30T23:59:59.999+07:00")),
        isAutoApply = Some(true),
        isAutoApplyBookingForm = Some(false),
        inapplicableReasonString = None,
        inapplicableReason = None,
        isStateIdRequired = None,
        promotionCodeType = Some(PromotionCodeType.MultiUse),
        status = Some(CampaignStatusType.Selected),
        cms = Some(List(PromoCmsData(1, Map("amount" -> "10"), 13))),
        campaignMessages = Some(
          Map(
            1 -> CampaignMessage(message = "ABC", params = Map("urgencyType" -> "1")),
            2 -> CampaignMessage(message = "DEF", params = Map("urgencyType" -> "2"))
          )
        )
      )
    }

    "return empty campaigns when roomToBook is None" in {
      val propertyProductItems   = Seq.empty[BookingPropertiesData]
      val promotionCodeCampaigns = Seq.empty[CampaignInfoInternal]
      val ccBinCampaigns         = Seq.empty[CampaignInfoInternal]
      val requestPromotionCode   = None
      val roomToBook             = None

      val result = campaignService.getPromotionInfo(
        propertyProductItems,
        promotionCodeCampaigns,
        ccBinCampaigns,
        requestPromotionCode,
        roomToBook
      )

      result.campaigns shouldBe Seq.empty
    }
  }

  "getSelectionCampaigns" should {
    "map campaigns correctly" in {
      val aSelectedCampaign = EnrichedCampaign(
        campaignId = 1234,
        cid = 45678,
        promotionCode = "AGODA",
        description = "AGODASALE Discount",
        isMarkUse = true,
        isSelected = true,
        inapplicableReasonString = None,
        inapplicableReason = None,
        campaignType = enumerations.CampaignTypes.PromotionCode,
        campaignDiscountType = Some(PAPICampaignDiscountTypes.Percentage),
        originalDiscountPercentage = Some(15),
        isUnderCap = false,
        promotionCap = None,
        messages = None,
        originalDiscountAmount = None,
        originalDiscountCurrencyCode = None,
        validDateType = Some(PAPICampaignValidDateType.BookingDate),
        dateValidFrom = Some(DateTime.parse("2024-01-01T00:00:00.000+07:00")),
        dateValidUntil = Some(DateTime.parse("2025-11-30T23:59:59.999+07:00")),
        isAutoApply = true,
        isAutoApplyBookingForm = false,
        isStateIdRequired = None,
        hotelFundingText = Some("HFC"),
        campaignGroupId = None,
        isCashback = Some(false),
        promotionCodeType = Some(PAPIPromotionCodeType.MultiUse),
        status = Some(PAPICampaignStatusTypes.Selected),
        cms = Some(List(EnrichedPromoCmsData(1, Map("amount" -> "10"), 13))),
        campaignMessages = Some(
          Map(
            1 -> EnrichedCampaignMessage(message = "ABC", params = Map("urgencyType" -> "1")),
            2 -> EnrichedCampaignMessage(message = "DEF", params = Map("urgencyType" -> "2"))
          )
        )
      )

      val room   = baseChildrenRoom().copy(campaignPromotions = Some(List(aSelectedCampaign)))
      val result = campaignService.getSelectionCampaigns(Some(room))

      result shouldBe Seq(
        CampaignPromotionInfo(
          campaignType = Some(CampaignType.PropertyPromotionCode),
          campaignId = 1234,
          cid = 45678,
          promotionCode = "AGODA",
          campaignName = "AGODASALE Discount",
          campaignDiscountType = Some(CampaignDiscountType.Percent),
          originalDiscountPercentage = Some(15),
          originalDiscountAmount = None,
          originalDiscountCurrencyCode = None,
          validDateType = Some(CampaignValidDateType.BookingDate),
          dateValidFrom = Some(DateTime.parse("2024-01-01T00:00:00.000+07:00")),
          dateValidUntil = Some(DateTime.parse("2025-11-30T23:59:59.999+07:00")),
          isAutoApply = Some(true),
          isAutoApplyBookingForm = Some(false),
          inapplicableReasonString = None,
          inapplicableReason = None,
          isStateIdRequired = None,
          promotionCodeType = Some(PromotionCodeType.MultiUse),
          status = Some(CampaignStatusType.Selected),
          cms = Some(List(PromoCmsData(1, Map("amount" -> "10"), 13))),
          campaignMessages = Some(
            Map(
              1 -> CampaignMessage(message = "ABC", params = Map("urgencyType" -> "1")),
              2 -> CampaignMessage(message = "DEF", params = Map("urgencyType" -> "2"))
            )
          )
        )
      )
    }

    "return empty when roomToBook is None" in {
      val result = campaignService.getSelectionCampaigns(None)

      result shouldBe Seq.empty
    }

    "return empty when campaignPromotions is None" in {
      val room   = baseChildrenRoom().copy(campaignPromotions = None)
      val result = campaignService.getSelectionCampaigns(Some(room))

      result shouldBe Seq.empty
    }
  }
}
