package com.agoda.bapi.server.validator

import com.agoda.bapi.agent.common.schema.BookingActionMessageTopic
import com.agoda.bapi.common.MockRequestContext
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.{ContinuePaymentRequest, FraudResult, FraudResultStatusEnum, GetStatusResponse}
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.model.{StatusToken, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.service.FeatureAware
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfterEach
import org.mockito.ArgumentMatchers.{eq => eqTo, _}
import org.scalatestplus.mockito.MockitoSugar
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

class ContinuePaymentRequestValidatorTest extends AnyWordSpec with Matchers with BeforeAndAfterEach with MockitoSugar {

  val validator = mock[ContinuePaymentRequestValidatorImpl]

  override def beforeEach(): Unit = {
    reset(validator)
  }

  "validateContinuePaymentRequest" should {
    val request = mock[ContinuePaymentRequest]
    when(request.statusToken).thenReturn("mockToken")
    "can't deserialize token return response" in {
      val response = mock[GetStatusResponse]
      when(validator.validateContinuePaymentRequest(any())).thenCallRealMethod()
      when(validator.validateStatusToken(any())).thenReturn(Left(response))
      validator.validateContinuePaymentRequest(request) shouldBe Some(response)
    }

    "token redirect topic should call correct validate function" in {
      val statusToken = mock[StatusToken]
      val topic       = Some(BookingActionMessageTopic.BAM_Topic_Redirect.value)
      when(statusToken.topic).thenReturn(topic)
      when(validator.validateContinuePaymentRequest(any())).thenCallRealMethod()
      when(validator.commonValidate(any(), any())).thenReturn(Nil)
      when(validator.validate3DS(any())).thenReturn(Nil)
      when(validator.validateAsyncRedirect(any())).thenReturn(Nil)
      when(validator.validateStatusToken(any())).thenReturn(Right(statusToken))

      validator.validateContinuePaymentRequest(request)

      verify(validator, times(1)).commonValidate(eqTo(request), eqTo(topic))
      verify(validator, times(1)).validateAsyncRedirect(eqTo(request))
      verify(validator, never).validate3DS(eqTo(request))
    }

    "token 3DS topic should call correct validate function" in {
      val statusToken = mock[StatusToken]
      val topic       = Some(BookingActionMessageTopic.BAM_Topic_PreAuth3dsRequired.value)
      when(statusToken.topic).thenReturn(topic)
      when(validator.validateContinuePaymentRequest(any())).thenCallRealMethod()
      when(validator.commonValidate(any(), any())).thenReturn(Nil)
      when(validator.validate3DS(any())).thenReturn(Nil)
      when(validator.validateAsyncRedirect(any())).thenReturn(Nil)
      when(validator.validateStatusToken(any())).thenReturn(Right(statusToken))

      validator.validateContinuePaymentRequest(request)

      verify(validator, times(1)).commonValidate(eqTo(request), eqTo(topic))
      verify(validator, times(1)).validate3DS(eqTo(request))
      verify(validator, never).validateAsyncRedirect(eqTo(request))
    }

    "By Pass all validations  for topic ticketing" in {
      val statusToken = mock[StatusToken]
      when(validator.validateStatusToken(any())).thenReturn(Right(statusToken))

      when(validator.validateContinuePaymentRequest(any())).thenCallRealMethod()

      val request = mock[ContinuePaymentRequest]
      val result  = validator.validateContinuePaymentRequest(request)
      result shouldBe None
    }
  }

  "validateJtbWhitelabelId" should {
    "validate pass (false) for same both request and context JTB whitelabel" in {
      val featureAware   = mock[FeatureAware]
      val whiteLabelInfo = mock[WhiteLabelInfo]
      implicit val requestContext: RequestContext = MockRequestContext
        .create()
        .copy(
          featureAware = Some(featureAware),
          whiteLabelInfo = whiteLabelInfo
        )
      when(validator.validateJtbWhitelabelId(any(), any())(any())).thenCallRealMethod()
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsJtbWl)).thenReturn(true)
      validator.validateJtbWhitelabelId(WhiteLabel.Jtb, WhiteLabel.Jtb) shouldBe false
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsRurubuWl)).thenReturn(true)
      validator.validateJtbWhitelabelId(WhiteLabel.Rurubu, WhiteLabel.Rurubu) shouldBe false
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsJapanicanWl)).thenReturn(true)
      validator.validateJtbWhitelabelId(WhiteLabel.Japanican, WhiteLabel.Japanican) shouldBe false
    }

    "validate fail (true) for different request and context whitelabel" in {
      val featureAware   = mock[FeatureAware]
      val whiteLabelInfo = mock[WhiteLabelInfo]
      implicit val requestContext: RequestContext = MockRequestContext
        .create()
        .copy(
          featureAware = Some(featureAware),
          whiteLabelInfo = whiteLabelInfo
        )

      when(validator.validateJtbWhitelabelId(any(), any())(any())).thenCallRealMethod()

      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsJapanicanWl)).thenReturn(false)
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsRurubuWl)).thenReturn(false)
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsJtbWl)).thenReturn(true)
      validator.validateJtbWhitelabelId(WhiteLabel.Jtb, WhiteLabel.Rurubu) shouldBe true
      validator.validateJtbWhitelabelId(WhiteLabel.Jtb, WhiteLabel.Japanican) shouldBe true
      validator.validateJtbWhitelabelId(WhiteLabel.Jtb, WhiteLabel.Agoda) shouldBe true

      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsJtbWl)).thenReturn(false)
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsRurubuWl)).thenReturn(false)
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsJapanicanWl)).thenReturn(true)
      validator.validateJtbWhitelabelId(WhiteLabel.Japanican, WhiteLabel.Rurubu) shouldBe true
      validator.validateJtbWhitelabelId(WhiteLabel.Japanican, WhiteLabel.Jtb) shouldBe true
      validator.validateJtbWhitelabelId(WhiteLabel.Japanican, WhiteLabel.Agoda) shouldBe true

      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsJapanicanWl)).thenReturn(false)
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsJtbWl)).thenReturn(false)
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsRurubuWl)).thenReturn(true)
      validator.validateJtbWhitelabelId(WhiteLabel.Rurubu, WhiteLabel.Agoda) shouldBe true
      validator.validateJtbWhitelabelId(WhiteLabel.Rurubu, WhiteLabel.Jtb) shouldBe true
      validator.validateJtbWhitelabelId(WhiteLabel.Rurubu, WhiteLabel.Japanican) shouldBe true
    }

    "validate skip (false) for any whitelabel that not JTB whitelabel" in {
      implicit val requestContext: RequestContext = MockRequestContext.create()
      when(validator.validateJtbWhitelabelId(any(), any())(any())).thenCallRealMethod()
      validator.validateJtbWhitelabelId(WhiteLabel.Agoda, WhiteLabel.Agoda) shouldBe false
      validator.validateJtbWhitelabelId(WhiteLabel.Priceline, WhiteLabel.Priceline) shouldBe false
      validator.validateJtbWhitelabelId(WhiteLabel.Agoda, WhiteLabel.Priceline) shouldBe false
    }
  }

  "validate function" should {
    val validator = new ContinuePaymentRequestValidatorImpl()

    "validateStatusToken" should {
      "failed if deserialize failed" in {
        val result = validator.validateStatusToken("")
        result.isLeft shouldBe true
      }
    }
    "validateAsyncRedirect" should {
      "no fields required" in {
        val request = mock[ContinuePaymentRequest]
        val result  = validator.validateAsyncRedirect(request)
        result shouldBe Nil
      }
    }
    "validate3DS" should {
      "required fields" in {
        val request = mock[ContinuePaymentRequest]
        when(request.postBackFields).thenReturn(None)
        val result = validator.validate3DS(request)

        assert(result.map(_.getMessage).contains("post back fields required"))
      }
    }

    "commonValidate" should {
      "redirect topic not required field" in {
        val request = mock[ContinuePaymentRequest]
        val topic   = Some(1)
        when(request.fraudResult).thenReturn(None)
        when(request.postBackFields).thenReturn(None)
        val result = validator.commonValidate(request, topic)
        result shouldBe Nil
      }

      "postback and fraud result should not empty at the same time" in {
        val request = mock[ContinuePaymentRequest]
        when(request.fraudResult).thenReturn(None)
        when(request.postBackFields).thenReturn(Some(Map.empty[String, String]))
        val result = validator.commonValidate(request, None)
        assert(result.map(_.getMessage).contains("Both fraud result and post back fields cannot be absent"))
      }

      "postback and fraud result should not have data at the same time" in {
        val request     = mock[ContinuePaymentRequest]
        val fraudResult = mock[FraudResult]
        val postback    = Map("a" -> "a")
        when(request.fraudResult).thenReturn(Some(fraudResult))
        when(request.postBackFields).thenReturn(Some(postback))
        val result = validator.commonValidate(request, None)
        assert(result.map(_.getMessage).contains("Both fraud result and post back fields cannot be present"))
      }
    }

  }
}
