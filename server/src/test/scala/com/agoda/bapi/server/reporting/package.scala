package com.agoda.bapi.server

import com.agoda.abspnx.client.http.response.PrecheckResponse
import com.agoda.abspnx.client.models.abs.Response.RequestProcessing
import com.agoda.abspnx.client.models.abs.ResultInfo
import com.agoda.abspnx.client.models.abs.Status.Success
import com.agoda.content.models.db.features._
import com.agoda.content.models.db.highlights._
import com.agoda.content.models.db.image.{ImageResponse, ImagesResponse, VideoResponse}
import com.agoda.content.models.db.information._
import com.agoda.content.models.db.policy._
import com.agoda.core.search.models.CancellationTypes.NonRefundable
import com.agoda.papi.enums.room.{ApplyType, SubChargeType}
import enumerations.AvailabilityTypes.Realtime
import enumerations.SpecialRequestIds._
import meta.CheapestRoomWithSuggestion
import models.pricing.AccountingEntity
import models.pricing.enums.ApplyTypes.{PB, PRPB, PRPN}
import models.pricing.enums.ChargeOptions.Mandatory
import models.pricing.enums.ChargeTypes.{Room, Surcharge, Tax, TaxAndFee}
import models.pricing.enums.CorTypeFlags.NoCOR
import models.pricing.enums.PaxTypes.One
import models.pricing.enums.PaymentModels.Agency
import models.pricing.enums.TaxTypes.SimpleTax
import models.starfruit.PayTypes.Hotel
import models.starfruit.RatePlanStatusTypes.Dispatched
import models.starfruit.SuggestedPrice.Exclusive
import models.starfruit.{BookingItemBreakdown, BookingMaxOccupancy, BookingPayment, BookingPrice, BookingReferencePrice, BookingSummaryPrice, BookingSummaryPrices, BookingSurchargesItem, BookingSurchargesItems, Cancellation, CreditCard, Daily, Display, DisplayBasis, DisplayPrice, DisplaySummary, ExtraInformation, HotelAggregatedOptions, HotelAggregatedPayment, HotelFeatures, PartnerPaxOption, PayAtHotel, PayLater, PrePayment, Price, PseudoCoupon, RateModel, StayOccupancy, SummaryElement, TaxReceipt}
import org.joda.time.format.DateTimeFormat
import transformers._

import scala.collection.mutable

package object reporting {

  val formatter = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZZ")

  val richPropertyResponse = Property(
    searchId = "3f74acf8-d557-4733-9ef1-669d2a09d579",
    propertyId = 114558,
    cityId = 14562,
    productType = List(-1, 1),
    activeHotelsInCity = Some(95),
    countryId = 153,
    areaId = 18172,
    cheapestRoom = Some(
      EnrichedCheapestRoom(
        description = "Most popular choice!",
        dmcRoomId = "c3e10b7d-850f-5bd7-f92e-a462e52e6bea",
        uid = "08064e41-955b-7e44-ff89-a7f5dc349eb4"
      )
    ),
    needOccupancySearch = false,
    isReady = true,
    supplierId = 3038,
    info = Some(
      InformationResponse(
        propertyId = 114558,
        description = Some(
          Description(
            long = Some(
              "Included in all rooms are bathtub, air conditioning, non smoking rooms.\r\nThe hotel boasts shops, business center, 24hr room service as part of its superior facilities and services.\r\nGuests will find this service-oriented hotel with superb facilities and amenities provides excellent value.\r\nPlease enter your dates on our secure online booking form to make a reservation at Hotel Icare Toulouse.\r\n"
            ),
            short = Some("Included in all rooms are bathtub, air conditioning, non smoking rooms.")
          )
        ),
        usefulInfoGroups = None,
        notes = Some(
          Notes(
            publicNotes = None,
            importantNotes = None,
            transportationNotes = None,
            criticalNotes = None
          )
        ),
        entertainment = None,
        numberOfRoom = None,
        policies = Some(
          HotelPolicies(
            children = None,
            minAge = None,
            adult = None,
            extraBed = Some(
              Vector(
                "Extra beds are dependent on the room you choose. Please check the individual room capacity for more details."
              )
            ),
            additional =
              Some(Vector("When booking more than 5 rooms, different policies and additional supplements may apply.")),
            hotelAgePolicy = Some(
              HotelAgePolicy(
                infantAges = Some(
                  AgeRange(
                    min = None,
                    max = None
                  )
                ),
                childAges = Some(
                  AgeRange(
                    min = None,
                    max = None
                  )
                ),
                minGuestAge = None,
                isChildStayFree = Some(false)
              )
            )
          )
        ),
        spokenLanguages = Some(
          Vector(
            LanguageInfo(
              id = 2,
              localeName = "French",
              locale = None
            )
          )
        ),
        messaging = Some(
          Messaging(
            hostName = None,
            isAllowedPreBooking = None,
            isAllowedPostBooking = None,
            isAllowedWithBooking = None,
            isAllowedInHouseFeedback = None,
            isAllowedInHouseRequest = None,
            isAllowedMessagingPlatform = None,
            isShowCustomerInfoInVoucher = None,
            emailDisplaySetting = None,
            responsiveRate = None,
            totalNumberOfInquiries = None,
            totalResponseRateInLast3Months = None,
            avgResponseTimeMin = None
          )
        ),
        reception = Some(
          Reception(
            isMystay = false
          )
        ),
        blockedNationalities = None,
        contact = Some(
          Contact(
            phone = Some(""),
            email = None,
            phones = Vector("026737896", "026737897"),
            ycsEmail = Some("<EMAIL>")
          )
        ),
        local = Some(
          Local(
            name = Some("Hôtel Icare"),
            language = Some(
              LanguageInfo(
                id = 2,
                localeName = "Français",
                locale = Some("fr-fr")
              )
            ),
            address = Some(
              Address(
                country = Some("France"),
                city = Some("Toulouse"),
                state = Some("Midi-Pyrénées"),
                area = Some("Centre-ville de Toulouse"),
                address1 = None,
                address2 = None,
                postalCode = None,
                GmtOffset = Some(2),
                utcOffset = None,
                countryId = None,
                cityId = None,
                areaId = None,
                countryCode = None,
                stateId = None
              )
            )
          )
        ),
        chainId = Some(0),
        childrenStayFreeTypeId = None,
        formerlyName = Some(""),
        localizations = None,
        isExtraBedAvailable = Some(false),
        contractInfo = None,
        breakfastInfo = Some(
          BreakfastInfo(
            cuisines = None
          )
        ),
        isAgodaVerified = Some(false),
        restaurantOnSite = None,
        coffeeInfo = None,
        certificate = None,
        staffVaccinationInfo = None,
        characteristicTopics = None,
        sustainabilityInfo = None,
        nightStayInfo = None,
        dsaComplianceInfo = None,
        companyTraceabilityInfo = None
      )
    ),
    features = None,
    reviews = None,
    images = Some(
      ImagesResponse(
        propertyId = 114558,
        totalHotelImages = 0,
        mainImage = Some(
          ImageResponse(
            id = 4565997,
            caption = "Exterior view",
            typeId = 6,
            locations = Map(
              "original" -> "http://qa.pix.agoda.local/hotelImages/114/114558/114558_1111241135004565997.jpg"
            ),
            providerId = 3038,
            group = "Property views",
            groupId = Some("property"),
            captionId = 13,
            localizations = None
          )
        ),
        hotelImages = Vector(),
        cityImage = Some(
          ImageResponse(
            id = 1025,
            caption = "",
            typeId = 8,
            locations = Map(
              "original" -> "http://qa.pix.agoda.local/city/14562/14562-7x3.jpg?s=762x328"
            ),
            providerId = 332,
            group = "Other",
            groupId = Some("other"),
            captionId = 0,
            localizations = None
          )
        ),
        categories = Some(Vector()),
        videos = Some(Vector()),
        matterports = Some(Vector()),
        ugcImages = Some(Vector()),
        ugcMosaicImages = Some(Vector())
      )
    ),
    highlights = None,
    nonHotelAccommodation = None,
    isDisplayAsAmount = Some(false),
    suggestedBasis = Some(PRPN),
    suggestedPrice = Some(Exclusive),
    recommendations = List(),
    personalizedRecommendations = List(),
    pastBookingsRecommendations = List(),
    recommendationHeader = None,
    tooltip = PropertyTooltip(
      taxAndServiceFeeText = Some(
        "Change your price display to include or not include taxes and service fees by clicking on the currency icon at the top of the page."
      ),
      extraBedTexts = Some(
        List(
          "<strong>Extra bed per room per night = [extrabedPrice]</strong><br>(charge applied when making your payment)",
          "",
          "Children aged 0 and over must use an extra bed."
        )
      )
    ),
    requestedChannelIds = List(1, 2, 7),
    availableFilters = Some(
      PropertyFilters(
        title = "Filter room options by:",
        tags = List(
          FilterTag(
            id = "no-cc-required",
            title = "Book without credit card",
            symbol = "payment-option-no-credit-card",
            subTags = None
          ),
          FilterTag(
            id = "pay-at-the-place",
            title = "Pay at the hotel",
            symbol = "pay-at-the-place",
            subTags = None
          )
        )
      )
    ),
    enableAPS = false,
    masterRooms = List(
      EnrichedMasterRoom(
        maxOccupancy = 2,
        maxExtraBeds = 0,
        typeId = 373685,
        englishName = Some("Standard Double Room with Shower"),
        name = Some("Standard Double Room with Shower"),
        images = Vector(
          ImageResponse(
            id = 5276160,
            caption = "",
            typeId = 7,
            locations = Map(
              "original" -> "http://qa.pix.agoda.local/hotelImages/114/114558/114558_1112211012005276160.jpg"
            ),
            providerId = 3038,
            group = "Rooms",
            groupId = Some("room"),
            captionId = 0,
            localizations = None
          )
        ),
        facilities = Vector(),
        bedConfiguration2 = None,
        childrenRooms = List(
          EnrichedChildRoom(
            uid = Some("08064e41-955b-7e44-ff89-a7f5dc349eb4"),
            typeId = Some(373685),
            dmcRoomId = Some("c3e10b7d-850f-5bd7-f92e-a462e52e6bea"),
            rateModel = Some(2),
            ratePlan = Some(
              EnrichedRatePlan(
                id = 0,
                englishName = "",
                name = "",
                promotionTypeId = None,
                promotionTypeName = None,
                localizedName = None
              )
            ),
            benefits = List(),
            filterTags = List("pay-at-the-place", "no-cc-required"),
            isBreakfastIncluded = Some(false),
            channel = Some(
              EnrichedChannel(
                id = 1,
                description = "",
                symbol = ""
              )
            ),
            supplierId = Some(3038),
            subSupplierId = Some(3038),
            availableRooms = Some(999),
            capacity = Some(
              EnrichedCapacity(
                adults = 2,
                children = 0,
                extraBed = 0,
                occupancy = 2,
                symbol = "max-occupancy-override",
                maxExtraBed = 0,
                numberOfGuestsWithoutRoom = 0,
                description = Some("2 adults"),
                allowedFreeChildrenAndInfants = Some(0)
              )
            ),
            localCurrencyCode = Some("EUR"),
            requestedCurrencyCode = Some("USD"),
            pricing = Map(
              "EUR" -> EnrichedPricing(
                DisplayBasis(DisplayPrice(100.0, 200.0), DisplayPrice(50.0, 100.0), DisplayPrice(50.0, 100.0)),
                DisplayBasis(DisplayPrice(0.0, 0.0), DisplayPrice(0.0, 0.0), DisplayPrice(0.0, 0.0)),
                List(),
                List(
                  EnrichedCharge(
                    "Extra charges",
                    "Extra charges [AMOUNT]",
                    Surcharge,
                    List(
                      EnrichedChargeBreakdown(
                        "City Tax",
                        "City Tax (Pay at the property) [AMOUNT]",
                        Surcharge,
                        228,
                        Hotel,
                        PB,
                        1,
                        0.0,
                        Price(40.0, 40.0),
                        Price(40.0, 40.0),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PB,
                            1,
                            Price(40.0, 40.0),
                            None,
                            Price(40.0, 40.0),
                            None,
                            SubChargeType.None
                          )
                        ),
                        false,
                        Some(Display(PB, 40.0, 40.0)),
                        0,
                        Some(Display(PB, 0.0, 0.0)),
                        Price(0.0, 0.0)
                      ),
                      EnrichedChargeBreakdown(
                        "Speed Boat Transfer",
                        "Speed Boat Transfer [AMOUNT]",
                        Surcharge,
                        51,
                        Hotel,
                        PB,
                        1,
                        0.0,
                        Price(30.0, 30.0),
                        Price(30.0, 30.0),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PB,
                            1,
                            Price(30.0, 30.0),
                            None,
                            Price(30.0, 30.0),
                            None,
                            SubChargeType.None
                          )
                        ),
                        true,
                        Some(Display(PB, 30.0, 30.0)),
                        0,
                        Some(Display(PB, 0.0, 0.0)),
                        Price(0.0, 0.0)
                      )
                    ),
                    Price(70.0, 70.0),
                    Price(0.0, 0.0),
                    Price(70.0, 70.0)
                  ),
                  EnrichedCharge(
                    "1 room X 2 nights",
                    "1 room X 2 nights [AMOUNT]",
                    Room,
                    List(
                      EnrichedChargeBreakdown(
                        "",
                        "",
                        Room,
                        -1,
                        Hotel,
                        PRPB,
                        1,
                        0.0,
                        Price(100.0, 110.0),
                        Price(100.0, 110.0),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PRPN,
                            1,
                            Price(50.0, 55.0),
                            None,
                            Price(41.7, 46.7),
                            None,
                            SubChargeType.None
                          ),
                          Daily(
                            formatter.parseDateTime("2019-12-13T00:00:00.000+07:00"),
                            0.0,
                            PRPN,
                            1,
                            Price(50.0, 55.0),
                            None,
                            Price(41.7, 46.7),
                            None,
                            SubChargeType.None
                          )
                        ),
                        true,
                        Some(Display(PRPN, 50.0, 55.0)),
                        0,
                        Some(Display(PRPN, 41.7, 46.7)),
                        Price(83.4, 93.4)
                      )
                    ),
                    Price(100.0, 110.0),
                    Price(0.0, 0.0),
                    Price(100.0, 110.0)
                  ),
                  EnrichedCharge(
                    "Hotel tax and service fees ",
                    "Hotel tax and service fees  [AMOUNT]",
                    TaxAndFee,
                    List(
                      EnrichedChargeBreakdown(
                        "Tax",
                        "Unknown [AMOUNT]",
                        Tax,
                        253,
                        Hotel,
                        PRPB,
                        1,
                        0.0,
                        Price(10.0, 10.0),
                        Price(10.0, 10.0),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PRPB,
                            1,
                            Price(5.0, 5.0),
                            None,
                            Price(0.0, 0.0),
                            None,
                            SubChargeType.None
                          ),
                          Daily(
                            formatter.parseDateTime("2019-12-13T00:00:00.000+07:00"),
                            0.0,
                            PRPB,
                            1,
                            Price(5.0, 5.0),
                            None,
                            Price(0.0, 0.0),
                            None,
                            SubChargeType.None
                          )
                        ),
                        true,
                        Some(Display(PRPN, 5.0, 5.0)),
                        0,
                        Some(Display(PRPN, 0.0, 0.0)),
                        Price(0.0, 0.0)
                      ),
                      EnrichedChargeBreakdown(
                        "Tax",
                        "Unknown (Pay at the property) [AMOUNT]",
                        Tax,
                        1794,
                        Hotel,
                        PRPB,
                        1,
                        0.0,
                        Price(20.0, 20.0),
                        Price(20.0, 20.0),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PRPB,
                            1,
                            Price(10.0, 10.0),
                            None,
                            Price(0.0, 0.0),
                            None,
                            SubChargeType.None
                          ),
                          Daily(
                            formatter.parseDateTime("2019-12-13T00:00:00.000+07:00"),
                            0.0,
                            PRPB,
                            1,
                            Price(10.0, 10.0),
                            None,
                            Price(0.0, 0.0),
                            None,
                            SubChargeType.None
                          )
                        ),
                        false,
                        Some(Display(PRPN, 10.0, 10.0)),
                        0,
                        Some(Display(PRPN, 0.0, 0.0)),
                        Price(0.0, 0.0)
                      )
                    ),
                    Price(30.0, 30.0),
                    Price(0.0, 0.0),
                    Price(30.0, 30.0)
                  )
                ),
                Some(ExtraInformation(5.0, 5.0, 30.0, 30.0, 18.49376114081997, 100.0, 100.0, 100.0)),
                DisplaySummary(
                  SummaryElement(
                    DisplayPrice(100.0, 200.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(100.0, 200.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(100.0, 200.0),
                    Some(DisplayPrice(0.0, 0.0)),
                    Some(DisplayPrice(170.0, 180.0))
                  ),
                  SummaryElement(
                    DisplayPrice(100.0, 200.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(100.0, 200.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(100.0, 200.0),
                    Some(DisplayPrice(0.0, 0.0)),
                    Some(DisplayPrice(170.0, 180.0))
                  ),
                  SummaryElement(
                    DisplayPrice(50.0, 100.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(50.0, 100.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(50.0, 100.0),
                    Some(DisplayPrice(0.0, 0.0)),
                    Some(DisplayPrice(85.0, 90.0))
                  ),
                  SummaryElement(
                    DisplayPrice(50.0, 100.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(50.0, 100.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(50.0, 100.0),
                    Some(DisplayPrice(0.0, 0.0)),
                    Some(DisplayPrice(85.0, 90.0))
                  )
                ),
                None,
                Some(List()),
                None,
                None,
                0,
                None,
                None
              ),
              "USD" -> EnrichedPricing(
                DisplayBasis(DisplayPrice(111.4, 222.8), DisplayPrice(55.7, 111.4), DisplayPrice(55.7, 111.4)),
                DisplayBasis(DisplayPrice(0.0, 0.0), DisplayPrice(0.0, 0.0), DisplayPrice(0.0, 0.0)),
                List(),
                List(
                  EnrichedCharge(
                    "Extra charges",
                    "Extra charges [AMOUNT]",
                    Surcharge,
                    List(
                      EnrichedChargeBreakdown(
                        "City Tax",
                        "City Tax (Pay at the property) [AMOUNT]",
                        Surcharge,
                        228,
                        Hotel,
                        PB,
                        1,
                        0.0,
                        Price(44.56, 44.56),
                        Price(44.56, 44.56),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PB,
                            1,
                            Price(44.56, 44.56),
                            None,
                            Price(44.56, 44.56),
                            None,
                            SubChargeType.None
                          )
                        ),
                        false,
                        Some(Display(PB, 44.56, 44.56)),
                        0,
                        Some(Display(PB, 0.0, 0.0)),
                        Price(0.0, 0.0)
                      ),
                      EnrichedChargeBreakdown(
                        "Speed Boat Transfer",
                        "Speed Boat Transfer [AMOUNT]",
                        Surcharge,
                        51,
                        Hotel,
                        PB,
                        1,
                        0.0,
                        Price(33.42, 33.42),
                        Price(33.42, 33.42),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PB,
                            1,
                            Price(33.42, 33.42),
                            None,
                            Price(33.42, 33.42),
                            None,
                            SubChargeType.None
                          )
                        ),
                        true,
                        Some(Display(PB, 33.42, 33.42)),
                        0,
                        Some(Display(PB, 0.0, 0.0)),
                        Price(0.0, 0.0)
                      )
                    ),
                    Price(77.98, 77.98),
                    Price(0.0, 0.0),
                    Price(77.98, 77.98)
                  ),
                  EnrichedCharge(
                    "1 room X 2 nights",
                    "1 room X 2 nights [AMOUNT]",
                    Room,
                    List(
                      EnrichedChargeBreakdown(
                        "",
                        "",
                        Room,
                        -1,
                        Hotel,
                        PRPB,
                        1,
                        0.0,
                        Price(111.4, 122.54),
                        Price(111.4, 122.54),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PRPN,
                            1,
                            Price(55.7, 61.27),
                            None,
                            Price(46.46, 52.03),
                            None,
                            SubChargeType.None
                          ),
                          Daily(
                            formatter.parseDateTime("2019-12-13T00:00:00.000+07:00"),
                            0.0,
                            PRPN,
                            1,
                            Price(55.7, 61.27),
                            None,
                            Price(46.46, 52.03),
                            None,
                            SubChargeType.None
                          )
                        ),
                        true,
                        Some(Display(PRPN, 55.7, 61.27)),
                        0,
                        Some(Display(PRPN, 46.46, 52.03)),
                        Price(92.92, 104.06)
                      )
                    ),
                    Price(111.4, 122.54),
                    Price(0.0, 0.0),
                    Price(111.4, 122.54)
                  ),
                  EnrichedCharge(
                    "Hotel tax and service fees ",
                    "Hotel tax and service fees  [AMOUNT]",
                    TaxAndFee,
                    List(
                      EnrichedChargeBreakdown(
                        "Tax",
                        "Unknown [AMOUNT]",
                        Tax,
                        253,
                        Hotel,
                        PRPB,
                        1,
                        0.0,
                        Price(11.14, 11.14),
                        Price(11.14, 11.14),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PRPB,
                            1,
                            Price(5.57, 5.57),
                            None,
                            Price(0.0, 0.0),
                            None,
                            SubChargeType.None
                          ),
                          Daily(
                            formatter.parseDateTime("2019-12-13T00:00:00.000+07:00"),
                            0.0,
                            PRPB,
                            1,
                            Price(5.57, 5.57),
                            None,
                            Price(0.0, 0.0),
                            None,
                            SubChargeType.None
                          )
                        ),
                        true,
                        Some(Display(PRPN, 5.57, 5.57)),
                        0,
                        Some(Display(PRPN, 0.0, 0.0)),
                        Price(0.0, 0.0)
                      ),
                      EnrichedChargeBreakdown(
                        "Tax",
                        "Unknown (Pay at the property) [AMOUNT]",
                        Tax,
                        1794,
                        Hotel,
                        PRPB,
                        1,
                        0.0,
                        Price(22.28, 22.28),
                        Price(22.28, 22.28),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PRPB,
                            1,
                            Price(11.14, 11.14),
                            None,
                            Price(0.0, 0.0),
                            None,
                            SubChargeType.None
                          ),
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PRPB,
                            1,
                            Price(11.14, 11.14),
                            None,
                            Price(0.0, 0.0),
                            None,
                            SubChargeType.None
                          )
                        ),
                        false,
                        Some(Display(PRPN, 11.14, 11.14)),
                        0,
                        Some(Display(PRPN, 0.0, 0.0)),
                        Price(0.0, 0.0)
                      )
                    ),
                    Price(33.42, 33.42),
                    Price(0.0, 0.0),
                    Price(33.42, 33.42)
                  )
                ),
                Some(ExtraInformation(5.57, 5.57, 33.42, 33.42, 18.49376114081997, 111.4, 111.4, 111.4)),
                DisplaySummary(
                  SummaryElement(
                    DisplayPrice(111.4, 222.8),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(111.4, 222.8),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(111.4, 222.8),
                    Some(DisplayPrice(0.0, 0.0)),
                    Some(DisplayPrice(189.38, 200.52))
                  ),
                  SummaryElement(
                    DisplayPrice(111.4, 222.8),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(111.4, 222.8),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(111.4, 222.8),
                    Some(DisplayPrice(0.0, 0.0)),
                    Some(DisplayPrice(189.38, 200.52))
                  ),
                  SummaryElement(
                    DisplayPrice(55.7, 111.4),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(55.7, 111.4),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(55.7, 111.4),
                    Some(DisplayPrice(0.0, 0.0)),
                    Some(DisplayPrice(94.69, 100.26))
                  ),
                  SummaryElement(
                    DisplayPrice(55.7, 111.4),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(55.7, 111.4),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(55.7, 111.4),
                    Some(DisplayPrice(0.0, 0.0)),
                    Some(DisplayPrice(94.69, 100.26))
                  )
                ),
                None,
                Some(List()),
                None,
                None,
                0,
                None,
                None
              )
            ),
            promotions = None,
            pointsMax = None,
            payment = Some(
              EnrichedPayment(
                taxReceipt = EnrichedTaxReceipt(
                  isEligible = false,
                  isDomestic = false,
                  title = "Tax receipt available",
                  description =
                    "Bookings chosen to be paid at the hotel can ask for the local tax invoices from the hotel directly.\nAs for the pre-paid bookings made on the Agoda app for hotels in China (mainland), most of them can be issued local tax invoices. All bookings can get Agoda receipts via the \"My bookings\" page.",
                  symbol = "tax-receipt-available",
                  detail = Some(
                    EnrichedTaxReceiptDetail(
                      title = "Tax Receipts",
                      description =
                        "Bookings chosen to be paid at the hotel can ask for the local tax invoices from the hotel directly.\nAs for the pre-paid bookings made on the Agoda app for hotels in China (mainland), most of them can be issued local tax invoices. All bookings can get Agoda receipts via the \"My bookings\" page."
                    )
                  )
                ),
                payLater = EnrichedPayLater(
                  isEligible = false,
                  authDate = formatter.parseDateTime("2019-09-16T00:00:00.000+07:00"),
                  chargeDate = formatter.parseDateTime("2019-09-16T00:00:00.000+07:00"),
                  title = "",
                  description = "",
                  symbol = ""
                ),
                cancellation = EnrichedCancellation(
                  code = "365D100P_100P",
                  noShowPolicy = "If you fail to arrive or cancel the booking, no refund will be given.",
                  defaultNoShowPolicy = "If you fail to arrive or cancel the booking, no refund will be given.",
                  policies = List("This booking is Non-Refundable and cannot be amended or modified."),
                  defaultPolicies = List("This booking is Non-Refundable and cannot be amended or modified."),
                  cancellationType = NonRefundable,
                  description =
                    "This special offer includes an extra-low price, but cannot be amended or cancelled. In case of a no-show, the property will not refund the booking.<br><br>If you're sure of your travel dates, you can take advantage of this special offer!",
                  defaultDescription =
                    "This special offer includes an extra-low price, but cannot be amended or cancelled. In case of a no-show, the property will not refund the booking.<br><br>If you're sure of your travel dates, you can take advantage of this special offer!",
                  freeCancellationDate = None,
                  title = "Extra low price! (non-refundable)",
                  symbol = "cancellation-policy-non-refund-special-condition",
                  cancellationPolicies = None
                ),
                noCreditCard = EnrichedNoCreditCard(
                  isEligible = true,
                  title = "Book without credit card",
                  description = "",
                  symbol = "payment-option-no-credit-card"
                ),
                payAtHotel = EnrichedPayAtHotel(
                  isEligible = true,
                  title = "Pay at the hotel",
                  description = "",
                  symbol = "pay-at-the-place"
                ),
                paymentModel = Agency,
                payAtCheckIn = EnrichedPayAtCheckIn(
                  isEligible = false,
                  authDate = formatter.parseDateTime("2019-09-16T00:00:00.000+07:00"),
                  chargeDate = formatter.parseDateTime("2019-09-16T00:00:00.000+07:00"),
                  title = "",
                  description = "",
                  symbol = ""
                ),
                m150 = Some(
                  M150Info(
                    isEligible = false,
                    text = None
                  )
                ),
                noPrePaymentRequired = Some(
                  EnrichedNoPrePaymentRequired(
                    isEligible = true,
                    title = "No payment until check-in",
                    description = "",
                    symbol = ".ficon-prepayment"
                  )
                ),
                installmentDetails = None
              )
            ),
            dmcPolicyText = Some(
              EnrichedDMCPolicyText(
                externalData =
                  "<ns1:ExternalData xmlns:ns1=\"http://DmcExternalData.Agoda.com\"><ns1:DMCResponse type=\"SearchResponse\"><ns1:getBlockAvailability><ns1:result><ns1:block><source>SUPPLY_CDS</source><supplySource>Push</supplySource><ns1:is_deal>0</ns1:is_deal><ns1:source_update>SUPPLY_CDS</ns1:source_update><ns1:block_id>5494201_83346541_2_2</ns1:block_id><ns1:incremental_price><ns1:currency>EUR</ns1:currency><ns1:price>140.00</ns1:price></ns1:incremental_price><ns1:is_nocc>0</ns1:is_nocc><ns1:generic_nocc>false</ns1:generic_nocc><ns1:domestic_nocc>false</ns1:domestic_nocc><ns1:lastmin_nocc>false</ns1:lastmin_nocc><ns1:georate>1</ns1:georate><ns1:origin>A1</ns1:origin><ns1:is_bmp>0</ns1:is_bmp><ns1:display_price>140.00</ns1:display_price></ns1:block><ns1:hotel_id>54942</ns1:hotel_id></ns1:result></ns1:getBlockAvailability></ns1:DMCResponse></ns1:ExternalData>",
                blockId = Some("5494201_83346541_2_2"),
                cancellationText = ""
              )
            ),
            isFit = Some(true),
            pricingRoomOption = None,
            tooltip = RoomTooltip(
              corText = None,
              capacityText = Some("Max 2 Adults"),
              availableRoomsText = Some("Limited availability")
            ),
            gmtOffset = Some(2),
            needOccupancySearch = Some(false),
            giftCard = None,
            offerId = Some(0),
            priceOfferId = Some("3f74acf8-d557-4733-9ef1-669d2a09d57908064e41-955b-7e44-ff89-a7f5dc349eb4"),
            pseudoCoupon = Some(
              PseudoCoupon(
                showBadge = false
              )
            ),
            promotionEligible = Some(false),
            packageEligible = Some(false),
            rateRepurposeInfos = Some(List()),
            isRepurposed = Some(false),
            isAveragePrice = Some(false),
            pointsMaxBadge = Some(
              PointsMaxBadge(
                showBadge = true,
                badgeText = "Earn airline miles"
              )
            ),
            npclnChannel = None,
            priceInfos = Map(
            ),
            isRoomTypeNotGuarantee = Some(false),
            mseGroupId = None,
            campaignPromotion = None,
            roomLevelBOR = None,
            campaignPromotions = Some(List()),
            loyaltyResponse = None,
            paymentFeature = Some(
              Map(
              )
            ),
            morErpAdminId = Some(0),
            hasChildBreakfastSurcharge = Some(false),
            corBreakdown = CorBreakdown(
              perRoomPerNightTaxExclusive = List(
                CorBreakdownItem(
                  id = 1,
                  text = "Price per night",
                  price = 56.0,
                  icon = None,
                  isDiscount = false
                )
              ),
              perRoomPerNightTaxInclusive = List(
                CorBreakdownItem(
                  id = 1,
                  text = "Total price per night",
                  price = 111.0,
                  icon = None,
                  isDiscount = false
                )
              ),
              perNightTaxExclusive = List(
                CorBreakdownItem(
                  id = 1,
                  text = "Price per night",
                  price = 56.0,
                  icon = None,
                  isDiscount = false
                )
              ),
              perNightTaxInclusive = List(
                CorBreakdownItem(
                  id = 1,
                  text = "Total price per night",
                  price = 111.0,
                  icon = None,
                  isDiscount = false
                )
              )
            ),
            promotionUrgencyMessage = None,
            roomIdentifiers = Some(
              "CmkI6s4tEAQgAjAESg0zNjVEMTAwUF8xMDBQULwvektTb21lKDU0OTQyKXwzNzM2ODV8MXw1NDk0MjAxXzgzMzQ2NTQxXzJfMl8wfDJ8MnxMaXN0KCl8MzY1RDEwMFBfMTAwUHxTb21lKCkSAggB"
            ),
            corSummary = Some(
              CorSummary(
                hasCor = false,
                corType = NoCOR,
                corText = None,
                isOriginal = false,
                hasOwnCOR = false,
                isBlacklistedCor = false
              )
            ),
            isMultiRoomPromotion = Some(false),
            pricingMessages = List(),
            roomStatus = Some(Dispatched),
            fencedRate = Some(
              FencedRate(
                origin = None
              )
            ),
            priceChange = None,
            supplierFinancialData = None,
            masterTypeId = Some(373685),
            soldOutPredictionTime = None,
            rewardOptions = Map.empty
          )
        ),
        facilityGroups = Vector(),
        features = Vector(
          RoomFeatureResponse(
            name = "size",
            text = "14 m²/151 ft²",
            symbol = "sqm"
          ),
          RoomFeatureResponse(
            name = "room-size-sqm",
            text = "14",
            symbol = "room-size-sqm"
          ),
          RoomFeatureResponse(
            name = "room-size-sqft",
            text = "151",
            symbol = "room-size-sqft"
          )
        ),
        groupMseRooms = List(),
        isAllowChildren = Some(false),
        isYcsMaster = Some(false),
        suitabilityType = None,
        hotelRoomTypeAlternateName = None,
        numberOfRoom = Some(0),
        roomSizeInclTerrace = Some(false),
        extrabedChildAge = Some(0),
        maxInfantInRoom = Some(0),
        localizations = None,
        isDormitory = Some(false),
        roomReviewInformation = Some(
          MasterRoomReviewInformation(
            score = 7.0,
            scoreText = "Very good",
            valueForMoneyScore = None,
            cleanliness = None,
            roomComfort = None,
            location = None,
            staffPerformance = None,
            noOfReviewPositiveMentioned = None,
            noOfReviewNonPositiveMentioned = None
          )
        ),
        suitabilityTypes = List(),
        alternativeDate = None,
        styleName = None,
        isRoomDayUsed = None,
        topFacilities = None,
        roomLicenseId = None,
        customizableRoomGridOptions = None,
        isRecommended = None,
        roomDescriptionAI = None,
        videos = Vector(
          VideoResponse(123L, "ABC")
        )
      )
    ),
    rooms = List(),
    infoSummary = Some(
      InformationSummaryResponse(
        propertyId = 114558,
        isPreferredPartner = false,
        awardYear = None,
        localeName = "Hotel Icare",
        defaultName = "Hotel Icare",
        displayName = "Hotel Icare",
        singleRoomNonHotelAccommodation = false,
        hasHostExperience = false,
        accommodationType = "Hotel",
        isNonHotelAccommodationType = false,
        remarks = None,
        geoInfo = Some(
          GeoInfo(
            latitude = 43.610841826476,
            longitude = 1.45251274108887,
            obfuscatedLat = 43.610841826476,
            obfuscatedLong = 1.45251274108887
          )
        ),
        address = Some(
          Address(
            country = Some("France"),
            city = Some("Toulouse"),
            state = Some("Midi-Pyrenees"),
            area = Some("Toulouse City Center"),
            address1 = Some("11 Boulevard Bonrepos"),
            address2 = None,
            postalCode = None,
            GmtOffset = Some(2),
            utcOffset = None,
            cityId = None,
            countryId = None,
            areaId = None,
            countryCode = None,
            stateId = None
          )
        ),
        highlightedFeatures = Vector(
          Feature(
            id = Some(80),
            featureName = Some("Car park"),
            featureNameLocalizations = None,
            featureNameLocalizationList = None,
            symbol = Some("car-park"),
            emphasis = None,
            available = Some(true),
            tags = None,
            order = Some(10),
            highlighted = Some(true),
            images = None,
            featureTags = None,
            providerIds = None
          )
        ),
        rating = StarRating(
          value = 2.0,
          symbol = "star-2",
          text =
            "Gold star ratings are provided by the property to reflect the comfort, facilities, and amenities you can expect.",
          color = Color(
            name = "orange-yellow",
            hex = "#F99E00"
          )
        ),
        propertyType = "Hotel",
        sellingPoints = Vector(),
        generalRoomInformation = Some(
          GeneralRoomInformationResponse(
            features = Vector()
          )
        ),
        spokenLanguages = Vector(
          LanguageInfo(
            id = 2,
            localeName = "French",
            locale = None
          )
        ),
        awardsAndAccolades = AwardsAndAccolades(
          text = "Awards and Accolades\n",
          goldCircleAward = None,
          advanceGuaranteeProgram = None
        ),
        localizations = None,
        isAllowPostBooking = None,
        themes = List(
          HotelTheme(
            themeId = 0,
            name = "Business hotels",
            typeId = 2
          ),
          HotelTheme(
            themeId = 1,
            name = "Airport hotels",
            typeId = 2
          ),
          HotelTheme(
            themeId = 3,
            name = "Gay friendly hotels",
            typeId = 2
          ),
          HotelTheme(
            themeId = 4,
            name = "Pet friendly hotels",
            typeId = 2
          )
        ),
        chainId = 0,
        hasChannelManager = false,
        nhaSummary = None,
        propertyStatus = None,
        requiredGuestContact = false,
        asqType = None,
        asqInfos = Vector.empty
      )
    ),
    paxSetting = Some(
      PartnerPaxOption(
        paxSubmit = One,
        childAge = 0
      )
    ),
    localInformation = None,
    checkInOut = None,
    synopsis = None,
    engagement = None,
    experience = None,
    distance = None,
    pseudoCouponMessage = None,
    dfFeatures = Some(
      HotelFeatures(
        isMyStaySupported = false,
        bookOnRequest = false,
        canApplyPromotionEligible = false,
        options = Some(
          HotelAggregatedOptions(
            benefits = List(),
            cancellationCodes = Map(
              Agency -> Set(Cancellation("365D100P_100P"))
            ),
            maxPointMax = None,
            channels = Some(Set(1)),
            payment = HotelAggregatedPayment(
              taxReceipt = TaxReceipt(
                available = false,
                isDomestic = false,
                isCommission = false
              ),
              payLater = PayLater(
                payLater = false
              ),
              creditCard = CreditCard(
                required = false
              ),
              prePayment = PrePayment(
                required = false
              ),
              payAtHotel = PayAtHotel(
                isEligible = false
              )
            ),
            yscAvailableRooms = Some(0),
            hasAPS = Some(false)
          )
        ),
        bookNowPayAtCheckIn = false,
        isPackageEligible = false
      )
    ),
    priceMessaging = None,
    promotionEligible = Some(false),
    packageEligible = Some(false),
    booking = Some(
      EnrichedBookingItem(
        rooms = List(
          EnrichedBookingRoom(
            uid = "08064e41-955b-7e44-ff89-a7f5dc349eb4",
            accountingEntity = Some(
              AccountingEntity(
                merchantOfRecord = 114558,
                revenue = 5632,
                rateContract = 3038,
                argument =
                  "{\"ContractTypeId\":0,\"FapiaoInfo\":{\"IsDigitalGeneralEnabled\":true,\"IsPhysicalSpecialEnabled\":true},\"IsDomesticTaxReceiptEnabled\":false,\"MerchantOfRecord\":114558,\"MerchantOfRecordType\":2,\"RateContract\":3038,\"RateContractType\":3,\"Revenue\":5632,\"RevenueType\":1}"
              )
            ),
            availabilityType = Realtime,
            breakfastIncluded = false,
            breakfastInfo = "",
            cancellation = EnrichedBookingCancellation(
              code = "365D100P_100P",
              fallbackCode = "",
              noShowPolicy = "If you fail to arrive or cancel the booking, no refund will be given.",
              defaultNoShowPolicy = "If you fail to arrive or cancel the booking, no refund will be given.",
              policies = List("This booking is Non-Refundable and cannot be amended or modified."),
              defaultPolicies = List("This booking is Non-Refundable and cannot be amended or modified.")
            ),
            cancellationPolicy = "This booking is Non-Refundable and cannot be amended or modified.",
            cancellationPolicyCode = "365D100P_100P",
            cancellationChargeType = None,
            pricing = BookingPrice(
              charge = List(
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 2,
                  localAmount = 5.0,
                  usdAmount = 5.57,
                  reqAmount = 5.57,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(253),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 2,
                  localAmount = 5.0,
                  usdAmount = 5.57,
                  reqAmount = 5.57,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(253),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 12,
                  localAmount = 30.0,
                  usdAmount = 33.42,
                  reqAmount = 33.42,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 48,
                  localAmount = 0.0,
                  usdAmount = 0.0,
                  reqAmount = 0.0,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 48,
                  localAmount = 5.0,
                  usdAmount = 5.57,
                  reqAmount = 5.57,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 11,
                  localAmount = 50.0,
                  usdAmount = 55.7,
                  reqAmount = 55.7,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 3,
                  localAmount = 0.0,
                  usdAmount = 0.0,
                  reqAmount = 0.0,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 1,
                  localAmount = 41.7,
                  usdAmount = 46.46,
                  reqAmount = 46.46,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 3,
                  localAmount = 8.3,
                  usdAmount = 9.24,
                  reqAmount = 9.24,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 10,
                  localAmount = 46.7,
                  usdAmount = 52.03,
                  reqAmount = 52.03,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 49,
                  localAmount = 0.0,
                  usdAmount = 0.0,
                  reqAmount = 0.0,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 12,
                  localAmount = 55.0,
                  usdAmount = 61.27,
                  reqAmount = 61.27,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 48,
                  localAmount = 5.0,
                  usdAmount = 5.57,
                  reqAmount = 5.57,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 5,
                  localAmount = 0.0,
                  usdAmount = 0.0,
                  reqAmount = 0.0,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 49,
                  localAmount = 0.0,
                  usdAmount = 0.0,
                  reqAmount = 0.0,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 5,
                  localAmount = 0.0,
                  usdAmount = 0.0,
                  reqAmount = 0.0,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 1,
                  localAmount = 30.0,
                  usdAmount = 33.42,
                  reqAmount = 33.42,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 11,
                  localAmount = 50.0,
                  usdAmount = 55.7,
                  reqAmount = 55.7,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 12,
                  localAmount = 55.0,
                  usdAmount = 61.27,
                  reqAmount = 61.27,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 3,
                  localAmount = 8.3,
                  usdAmount = 9.24,
                  reqAmount = 9.24,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 49,
                  localAmount = 0.0,
                  usdAmount = 0.0,
                  reqAmount = 0.0,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 10,
                  localAmount = 30.0,
                  usdAmount = 33.42,
                  reqAmount = 33.42,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 1,
                  localAmount = 41.7,
                  usdAmount = 46.46,
                  reqAmount = 46.46,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 11,
                  localAmount = 30.0,
                  usdAmount = 33.42,
                  reqAmount = 33.42,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 41,
                  localAmount = 55.0,
                  usdAmount = 61.27,
                  reqAmount = 61.27,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 41,
                  localAmount = 30.0,
                  usdAmount = 33.42,
                  reqAmount = 33.42,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 10,
                  localAmount = 46.7,
                  usdAmount = 52.03,
                  reqAmount = 52.03,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 5,
                  localAmount = 0.0,
                  usdAmount = 0.0,
                  reqAmount = 0.0,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 41,
                  localAmount = 55.0,
                  usdAmount = 61.27,
                  reqAmount = 61.27,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                )
              ),
              summary = Some(
                BookingSummaryPrices(
                  roomAndExtrabed = BookingSummaryPrice(
                    perBook = Some(
                      BookingReferencePrice(
                        netInclusive = 104.06,
                        sellInclusive = 122.54
                      )
                    ),
                    perRoom = None,
                    perNight = None,
                    perRoomPerNight = None
                  ),
                  surcharges = BookingSurchargesItems(
                    perBook = List(
                      BookingSurchargesItem(
                        id = 51,
                        charge = BookingReferencePrice(
                          netInclusive = 33.42,
                          sellInclusive = 33.42
                        ),
                        chargeOption = Mandatory
                      )
                    ),
                    perRoom = List(),
                    perNight = List(),
                    perRoomPerNight = List()
                  )
                )
              )
            ),
            dmcId = Some(3038),
            dmcSpecificData =
              "<ns1:ExternalData xmlns:ns1=\"http://DmcExternalData.Agoda.com\"><ns1:DMCResponse type=\"SearchResponse\"><ns1:getBlockAvailability><ns1:result><ns1:block><source>SUPPLY_CDS</source><supplySource>Push</supplySource><ns1:is_deal>0</ns1:is_deal><ns1:source_update>SUPPLY_CDS</ns1:source_update><ns1:block_id>5494201_83346541_2_2</ns1:block_id><ns1:incremental_price><ns1:currency>EUR</ns1:currency><ns1:price>140.00</ns1:price></ns1:incremental_price><ns1:is_nocc>0</ns1:is_nocc><ns1:generic_nocc>false</ns1:generic_nocc><ns1:domestic_nocc>false</ns1:domestic_nocc><ns1:lastmin_nocc>false</ns1:lastmin_nocc><ns1:georate>1</ns1:georate><ns1:origin>A1</ns1:origin><ns1:is_bmp>0</ns1:is_bmp><ns1:display_price>140.00</ns1:display_price></ns1:block><ns1:hotel_id>54942</ns1:hotel_id></ns1:result></ns1:getBlockAvailability></ns1:DMCResponse></ns1:ExternalData>",
            excluded = "",
            giftCardEarning = None,
            hotelId = 114558,
            included = "",
            isAgodaReception = false,
            isNHA = false,
            isNotCcRequired = true,
            isPrepay = false,
            capacity = EnrichedCapacity(
              adults = 2,
              children = 0,
              extraBed = 0,
              occupancy = 2,
              symbol = "max-occupancy-override",
              maxExtraBed = 0,
              numberOfGuestsWithoutRoom = 0,
              description = Some("2 adults"),
              allowedFreeChildrenAndInfants = Some(0)
            ),
            noOfAdults = 2,
            noOfChildren = 0,
            noOfExtrabeds = 0,
            numberOfRoom = 1,
            occupancy = 2,
            partnerLoyaltyPoint = None,
            paymentModels = Agency,
            rateCategory = EnrichedBookingRateCategory(
              id = 0,
              Code = "",
              applyTo = "",
              Amount = 0.0,
              benefits = List()
            ),
            rateModel = RateModel(
              id = 2
            ),
            totalSaving = Some(0.0),
            rateModelType = Some(2),
            roomTypeId = 373685,
            roomTypeName = "Standard Double Room with Shower",
            ycsRatePlanId = 1,
            sellInfo = EnrichedBookingSellInfo(
              downlift = 0.0,
              downliftUsd = 0.0,
              priceTemplatedId = Some(-1),
              searchId = "3f74acf8-d557-4733-9ef1-669d2a09d579",
              sellTagId = 0,
              isAdvanceGuarantee = false,
              offerId = Some(0),
              priceOfferId = Some("3f74acf8-d557-4733-9ef1-669d2a09d57908064e41-955b-7e44-ff89-a7f5dc349eb4"),
              fireDrillContract = None
            ),
            taxSurchargeInfo =
              "Included : Taxes and fees USD 11.14, Speed Boat Transfer USD 33.42<br>Not Included : Taxes and fees USD 22.28 (Pay at the property), City Tax (Pay at the property) USD 44.56",
            rateChannel = Some(
              EnrichedChannel(
                id = 1,
                description = "",
                symbol = ""
              )
            ),
            correlationId = "3f74acf8-d557-4733-9ef1-669d2a09d579",
            promotion = None,
            ycsForeignPromotionText = "",
            ycsPromotionID = 0,
            ycsPromotionText = "",
            availableRooms = 999,
            chargeDiscount = 0.0,
            displayAmount = 0.0,
            displayCurrency = "",
            benefit = List(),
            exchange = BookingPayment(
              upliftExchangeRate = 1.0,
              siteExchangeRate = 1.0,
              exchangeRate = 1.0,
              localDecimalPlace = 2,
              requestDecimalPlace = 2,
              usdDecimalPlace = 2,
              localCurrency = "EUR",
              requestToUSDExchangeRate = 1.0,
              requestToLocalExchangeRate = 0.8976,
              upliftAmount = None,
              paymentCurrency = None,
              paymentAmount = Some(155.96),
              paymentUsdAmount = Some(155.96)
            ),
            npclnChannel = None,
            borSupplier = None,
            specialRequestOptions = List(
              AdditionalNotes,
              LateCheckIn,
              EarlyCheckIn,
              HighFloor,
              LargeBed,
              TwinBed,
              NonSmokingRoom,
              SmokingRoom
            ),
            hotelRemark = Some(""),
            lineItemId = Some(1),
            pointMultiply = Some(0.0),
            roomRemark = Some(""),
            numberOfBeds = Some(0),
            isAgodaAgency = false,
            paymentChannels = List.empty
          )
        ),
        los = 2,
        booking = List(
          EnrichedEBEBooking(
            creditCard = EnrichedEBECreditCard(
              payment = EnrichedEBEPayment(
                exchangeRate = 1.0,
                giftCardAmount = 0.0,
                giftCardAmountInUSD = 0.0,
                paymentAmount = 155.96,
                paymentAmountInUSD = 155.96,
                paymentCurrency = "USD",
                rewardsRedeemedPoint = 0,
                rewardsSaving = 0.0,
                rewardsSavingInUSD = 0.0,
                siteExchangeRate = 1.0,
                upliftAmount = 0.0,
                upliftExchangeRate = 1.0,
                exchangeRateOption = Some(0),
                destinationCurrency = None,
                destinationExchangeRate = 1.0,
                rateQuoteID = 0
              ),
              chargeOption = None,
              fullyAuthDate = Some(formatter.parseDateTime("2019-09-16T00:00:00.000+07:00")),
              fullyChargeDate = Some(formatter.parseDateTime("2019-09-16T00:00:00.000+07:00"))
            ),
            discount = None,
            hotel = List(
              EnrichedEBEHotel(
                checkIn = formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                checkOut = formatter.parseDateTime("2019-12-14T00:00:00.000+07:00"),
                numberOfAdults = 2,
                numberOfChildren = 0,
                numberOfRooms = 1,
                occFreeSearch = false,
                room = List(
                  EnrichedBookingRoom(
                    uid = "08064e41-955b-7e44-ff89-a7f5dc349eb4",
                    accountingEntity = Some(
                      AccountingEntity(
                        merchantOfRecord = 114558,
                        revenue = 5632,
                        rateContract = 3038,
                        argument =
                          "{\"ContractTypeId\":0,\"FapiaoInfo\":{\"IsDigitalGeneralEnabled\":true,\"IsPhysicalSpecialEnabled\":true},\"IsDomesticTaxReceiptEnabled\":false,\"MerchantOfRecord\":114558,\"MerchantOfRecordType\":2,\"RateContract\":3038,\"RateContractType\":3,\"Revenue\":5632,\"RevenueType\":1}"
                      )
                    ),
                    availabilityType = Realtime,
                    breakfastIncluded = false,
                    breakfastInfo = "",
                    cancellation = EnrichedBookingCancellation(
                      code = "365D100P_100P",
                      fallbackCode = "",
                      noShowPolicy = "If you fail to arrive or cancel the booking, no refund will be given.",
                      defaultNoShowPolicy = "If you fail to arrive or cancel the booking, no refund will be given.",
                      policies = List("This booking is Non-Refundable and cannot be amended or modified."),
                      defaultPolicies = List("This booking is Non-Refundable and cannot be amended or modified.")
                    ),
                    cancellationPolicy = "This booking is Non-Refundable and cannot be amended or modified.",
                    cancellationPolicyCode = "365D100P_100P",
                    cancellationChargeType = None,
                    pricing = BookingPrice(
                      charge = List(
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 2,
                          localAmount = 5.0,
                          usdAmount = 5.57,
                          reqAmount = 5.57,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(253),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 2,
                          localAmount = 5.0,
                          usdAmount = 5.57,
                          reqAmount = 5.57,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(253),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 12,
                          localAmount = 30.0,
                          usdAmount = 33.42,
                          reqAmount = 33.42,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 48,
                          localAmount = 0.0,
                          usdAmount = 0.0,
                          reqAmount = 0.0,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 48,
                          localAmount = 5.0,
                          usdAmount = 5.57,
                          reqAmount = 5.57,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 11,
                          localAmount = 50.0,
                          usdAmount = 55.7,
                          reqAmount = 55.7,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 3,
                          localAmount = 0.0,
                          usdAmount = 0.0,
                          reqAmount = 0.0,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 1,
                          localAmount = 41.7,
                          usdAmount = 46.46,
                          reqAmount = 46.46,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 3,
                          localAmount = 8.3,
                          usdAmount = 9.24,
                          reqAmount = 9.24,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 10,
                          localAmount = 46.7,
                          usdAmount = 52.03,
                          reqAmount = 52.03,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 49,
                          localAmount = 0.0,
                          usdAmount = 0.0,
                          reqAmount = 0.0,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 12,
                          localAmount = 55.0,
                          usdAmount = 61.27,
                          reqAmount = 61.27,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 48,
                          localAmount = 5.0,
                          usdAmount = 5.57,
                          reqAmount = 5.57,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 5,
                          localAmount = 0.0,
                          usdAmount = 0.0,
                          reqAmount = 0.0,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 49,
                          localAmount = 0.0,
                          usdAmount = 0.0,
                          reqAmount = 0.0,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 5,
                          localAmount = 0.0,
                          usdAmount = 0.0,
                          reqAmount = 0.0,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 1,
                          localAmount = 30.0,
                          usdAmount = 33.42,
                          reqAmount = 33.42,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 11,
                          localAmount = 50.0,
                          usdAmount = 55.7,
                          reqAmount = 55.7,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 12,
                          localAmount = 55.0,
                          usdAmount = 61.27,
                          reqAmount = 61.27,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 3,
                          localAmount = 8.3,
                          usdAmount = 9.24,
                          reqAmount = 9.24,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 49,
                          localAmount = 0.0,
                          usdAmount = 0.0,
                          reqAmount = 0.0,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 10,
                          localAmount = 30.0,
                          usdAmount = 33.42,
                          reqAmount = 33.42,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 1,
                          localAmount = 41.7,
                          usdAmount = 46.46,
                          reqAmount = 46.46,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 11,
                          localAmount = 30.0,
                          usdAmount = 33.42,
                          reqAmount = 33.42,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 41,
                          localAmount = 55.0,
                          usdAmount = 61.27,
                          reqAmount = 61.27,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 41,
                          localAmount = 30.0,
                          usdAmount = 33.42,
                          reqAmount = 33.42,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 10,
                          localAmount = 46.7,
                          usdAmount = 52.03,
                          reqAmount = 52.03,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 5,
                          localAmount = 0.0,
                          usdAmount = 0.0,
                          reqAmount = 0.0,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 41,
                          localAmount = 55.0,
                          usdAmount = 61.27,
                          reqAmount = 61.27,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        )
                      ),
                      summary = Some(
                        BookingSummaryPrices(
                          roomAndExtrabed = BookingSummaryPrice(
                            perBook = Some(
                              BookingReferencePrice(
                                netInclusive = 104.06,
                                sellInclusive = 122.54
                              )
                            ),
                            perRoom = None,
                            perNight = None,
                            perRoomPerNight = None
                          ),
                          surcharges = BookingSurchargesItems(
                            perBook = List(
                              BookingSurchargesItem(
                                id = 51,
                                charge = BookingReferencePrice(
                                  netInclusive = 33.42,
                                  sellInclusive = 33.42
                                ),
                                chargeOption = Mandatory
                              )
                            ),
                            perRoom = List(),
                            perNight = List(),
                            perRoomPerNight = List()
                          )
                        )
                      )
                    ),
                    dmcId = Some(3038),
                    dmcSpecificData =
                      "<ns1:ExternalData xmlns:ns1=\"http://DmcExternalData.Agoda.com\"><ns1:DMCResponse type=\"SearchResponse\"><ns1:getBlockAvailability><ns1:result><ns1:block><source>SUPPLY_CDS</source><supplySource>Push</supplySource><ns1:is_deal>0</ns1:is_deal><ns1:source_update>SUPPLY_CDS</ns1:source_update><ns1:block_id>5494201_83346541_2_2</ns1:block_id><ns1:incremental_price><ns1:currency>EUR</ns1:currency><ns1:price>140.00</ns1:price></ns1:incremental_price><ns1:is_nocc>0</ns1:is_nocc><ns1:generic_nocc>false</ns1:generic_nocc><ns1:domestic_nocc>false</ns1:domestic_nocc><ns1:lastmin_nocc>false</ns1:lastmin_nocc><ns1:georate>1</ns1:georate><ns1:origin>A1</ns1:origin><ns1:is_bmp>0</ns1:is_bmp><ns1:display_price>140.00</ns1:display_price></ns1:block><ns1:hotel_id>54942</ns1:hotel_id></ns1:result></ns1:getBlockAvailability></ns1:DMCResponse></ns1:ExternalData>",
                    excluded = "",
                    giftCardEarning = None,
                    hotelId = 114558,
                    included = "",
                    isAgodaReception = false,
                    isNHA = false,
                    isNotCcRequired = true,
                    isPrepay = false,
                    capacity = EnrichedCapacity(
                      adults = 2,
                      children = 0,
                      extraBed = 0,
                      occupancy = 2,
                      symbol = "max-occupancy-override",
                      maxExtraBed = 0,
                      numberOfGuestsWithoutRoom = 0,
                      description = Some("2 adults"),
                      allowedFreeChildrenAndInfants = Some(0)
                    ),
                    noOfAdults = 2,
                    noOfChildren = 0,
                    noOfExtrabeds = 0,
                    numberOfRoom = 1,
                    occupancy = 2,
                    partnerLoyaltyPoint = None,
                    paymentModels = Agency,
                    rateCategory = EnrichedBookingRateCategory(
                      id = 0,
                      Code = "",
                      applyTo = "",
                      Amount = 0.0,
                      benefits = List()
                    ),
                    rateModel = RateModel(
                      id = 2
                    ),
                    totalSaving = Some(0.0),
                    rateModelType = Some(2),
                    roomTypeId = 373685,
                    roomTypeName = "Standard Double Room with Shower",
                    ycsRatePlanId = 1,
                    sellInfo = EnrichedBookingSellInfo(
                      downlift = 0.0,
                      downliftUsd = 0.0,
                      priceTemplatedId = Some(-1),
                      searchId = "3f74acf8-d557-4733-9ef1-669d2a09d579",
                      sellTagId = 0,
                      isAdvanceGuarantee = false,
                      offerId = Some(0),
                      priceOfferId = Some("3f74acf8-d557-4733-9ef1-669d2a09d57908064e41-955b-7e44-ff89-a7f5dc349eb4"),
                      fireDrillContract = None
                    ),
                    taxSurchargeInfo =
                      "Included : Taxes and fees USD 11.14, Speed Boat Transfer USD 33.42<br>Not Included : Taxes and fees USD 22.28 (Pay at the property), City Tax (Pay at the property) USD 44.56",
                    rateChannel = Some(
                      EnrichedChannel(
                        id = 1,
                        description = "",
                        symbol = ""
                      )
                    ),
                    correlationId = "3f74acf8-d557-4733-9ef1-669d2a09d579",
                    promotion = None,
                    ycsForeignPromotionText = "",
                    ycsPromotionID = 0,
                    ycsPromotionText = "",
                    availableRooms = 999,
                    chargeDiscount = 0.0,
                    displayAmount = 0.0,
                    displayCurrency = "",
                    benefit = List(),
                    exchange = BookingPayment(
                      upliftExchangeRate = 1.0,
                      siteExchangeRate = 1.0,
                      exchangeRate = 1.0,
                      localDecimalPlace = 2,
                      requestDecimalPlace = 2,
                      usdDecimalPlace = 2,
                      localCurrency = "EUR",
                      requestToUSDExchangeRate = 1.0,
                      requestToLocalExchangeRate = 0.8976,
                      upliftAmount = None,
                      paymentCurrency = None,
                      paymentAmount = Some(155.96),
                      paymentUsdAmount = Some(155.96)
                    ),
                    npclnChannel = None,
                    borSupplier = None,
                    specialRequestOptions = List(
                      AdditionalNotes,
                      LateCheckIn,
                      EarlyCheckIn,
                      HighFloor,
                      LargeBed,
                      TwinBed,
                      NonSmokingRoom,
                      SmokingRoom
                    ),
                    hotelRemark = Some(""),
                    lineItemId = Some(1),
                    pointMultiply = Some(0.0),
                    roomRemark = Some(""),
                    numberOfBeds = Some(0),
                    isAgodaAgency = false,
                    paymentChannels = List.empty
                  )
                )
              )
            )
          )
        )
      )
    ),
    taxType = Some(SimpleTax),
    supplierSummaries = Map(
      3038L -> EnrichedSupplierSummary(
        supplierId = 3038,
        isReady = true
      )
    ),
    isMseProperty = Some(false),
    mseCheapestRoom = None,
    usp = None,
    uspRanks = None,
    suggestedRooms = List(),
    roomSwapping = List(),
    suggestedChildRooms = List(),
    maxPointsMax = None,
    maxPointsMaxBadge = Some(
      PointsMaxBadge(
        showBadge = true,
        badgeText = "Earn airline miles"
      )
    ),
    taxMetaData = List(),
    nearestLandmarkDistanceMessage = None,
    nearestLandmarkDistance = None,
    distanceFromSelectedLandmark = None,
    selectedLandmarkName = None,
    isFavorite = None,
    friends = List(),
    uniqueSellingPoints = None,
    supportUserLocale = Some(false),
    supportUserLocaleBadge = None,
    hotelAggInfo = None,
    soldOutPrice = None,
    soldOutRooms = None,
    bookingHistory = None,
    bookingFormHistory = None,
    topSellingPoints = List(
      TopSellingPoint(
        id = 9,
        text = None,
        icon = None,
        value = Some(10.0)
      )
    ),
    personalizedInformation = None,
    sponsored = false,
    sponsoredType = None,
    showSponsoredTag = None,
    sponsoredTrackingData = None,
    rankingType = 0,
    rankingExplanationMessages = List(),
    continentId = Some(4),
    isWhiteListHotel = false,
    internalUtilityData = InternalUtilityData(),
    closestBeachLandmarkInfo = List(),
    supportedSuppliers = Map(
      3038L -> EnrichedSupplierSummary(
        supplierId = 3038,
        isReady = true
      )
    ),
    areaBookingCountLastWeek = Some(
      AreaBookingCountModel(
        text = "{0} bookings confirmed so far this week in the Toulouse City Center area in Toulouse",
        value = 49
      )
    ),
    alternateDates = List(),
    stayOccupancy = Some(
      StayOccupancy(
        stayNoOfAdults = 2,
        stayNoOfChildren = 0,
        stayListOfChildAge = List(),
        stayNoOfRoom = 1
      )
    ),
    bookingMaxOccupancy = Some(
      BookingMaxOccupancy(
        adults = 2,
        children = 0
      )
    ),
    rateCategory = None,
    propertyRankingInfo = None,
    metaEngagement = None,
    multiRoomSuggestions = List(),
    isBoutiqueHotel = -1,
    multiHotelEligible = None,
    cartEligible = None
  )
}
