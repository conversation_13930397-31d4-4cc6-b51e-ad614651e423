package com.agoda.bapi.server.proxy

import akka.actor.ActorSystem
import com.agoda.bapi.common.WithProxyMessageTestMock
import com.agoda.bapi.server.utils.PromotionUtils
import com.agoda.gandalf.client.api.{CallResult, PromotionApi}
import com.agoda.gandalf.common.CrgwCampaignProductType
import com.agoda.gandalf.response.{CreditCardPromotionResponse, Discount}
import org.joda.time.LocalDate
import org.mockito.ArgumentMatchers.{any, eq => eqTo}
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.{ExecutionContext, Future}

class GandalfProxyTest
    extends AsyncWordSpec
    with WithProxyMessageTestMock
    with ScalaFutures
    with BeforeAndAfter
    with Matchers
    with MockitoSugar {
  private val clientMock         = mock[PromotionApi]
  private val expectedMetricName = "external.service.gandalf-api"

  class GandalfProxyImplMock(client: PromotionApi, expectedTags: Map[String, String])
      extends GandalfProxyImpl(client, messagingService) {
    override def withMeasureAndLog[T](metricName: String, tags: Map[String, String] = Map())(
        f: Future[T]
    ): Future[T] = {
      metricName shouldBe expectedMetricName
      tags shouldBe expectedTags
      f // skip measurement send and only execute f
    }
  }

  implicit val ec: ExecutionContext = ActorSystem("CampaignServiceImplSpec").dispatcher

  before {
    reset(clientMock)
  }

  "searchCampaignV2" should {
    "proxy and return correct response" in {
      val fixture = new fixtureV2 {}
      import fixture._
      val expectedTags = Map("method" -> "search-campaignV2")
      val proxy        = new GandalfProxyImplMock(clientMock, expectedTags)
      proxy
        .searchCampaignV2(
          siteId,
          countryId,
          paymentMethodId,
          emailDomain,
          bin,
          exactBinMatch,
          ccof,
          ccToken,
          PromotionUtils.overrideCampaign,
          productType
        )
        .map { actual =>
          verify(clientMock, times(1)).getCreditCardOrEmailCampaignsV2(
            eqTo(siteId),
            eqTo(countryId),
            eqTo(paymentMethodId),
            eqTo(emailDomain),
            eqTo(bin),
            eqTo(exactBinMatch),
            eqTo(ccof),
            eqTo(ccToken),
            eqTo(PromotionUtils.gandalfOverrideCampaign),
            eqTo(productType)
          )
          actual shouldBe mockResponse
        }
    }
  }
  trait fixtureV2 {
    val siteId          = Some(2302934)
    val countryId       = Some(23)
    val paymentMethodId = Some(2)
    val emailDomain     = Some("@agoda.com")
    val bin             = Some("411111")
    val exactBinMatch   = Some(true)
    val ccof            = Some(1959)
    val ccToken         = Some("token")
    val productType     = CrgwCampaignProductType.Flight
    val callResultMock  = mock[CallResult[Vector[CreditCardPromotionResponse]]]
    val mockResponse = Vector(
      CreditCardPromotionResponse(
        id = 1230913,
        campaignId = PromotionUtils.gandalfCampaignId,
        siteId = Some(PromotionUtils.gandalfSiteId),
        displayName = "cc contactless reduce COVID-19 chance",
        bin = Some("233333"),
        discount = Discount(
          discountType = "cc bin",
          amount = 5.0,
          currency = Some("USD")
        ),
        membershipContentId = None,
        membershipContentCmsId = None,
        productType = productType,
        isCashback = None
      )
    )
    when(callResultMock.runSync).thenReturn(mockResponse)
    when(
      clientMock.getCreditCardOrEmailCampaignsV2(
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any()
      )
    ).thenReturn(callResultMock)
  }
}
