package com.agoda.bapi.server.utils.partner

import com.agoda.bapi.common.message.PropertySearchCriteria
import com.agoda.bapi.common.message.creation.PaymentAmount
import com.agoda.bapi.common.message.setupBooking.{CampaignInfoRequest, ProductPaymentRequest, ProductsRequest, PropertyProductItem, PropertyRequestItem}
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.model.{CampaignInfo, CampaignInfoInternal, ChargeOption, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.model.creation.{BAPIBooking, DiscountType => CampaignDiscountType}
import com.agoda.bapi.creation.BookingTokenHelper
import com.agoda.bapi.server.addon.AddOnDefaults.{createMockPropertyWithRoomUid, defaultChildRoom}
import com.agoda.bapi.server.facades.helpers.{SetupBookingContextFixture, SetupBookingRequestFixture}
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.BookingPropertiesData
import com.agoda.bapi.server.repository.dto.papi.DiscountRequest
import com.agoda.bapi.server.utils.partner.PropertiesPromoUtils.{getMostExpensiveBessieProductTokenKey, getPartnerDiscountRequest, getPartnerPromoEligiblePropertyId, getTravelCreditLeftAmount, isBenefitEligible, isPartnerPromotionCodeEligible, isPropertyEligibleForPartnerPromo}
import com.agoda.winterfell.output.{Campaign, DiscountType, ExternalLoyaltyUserProfileResponse, LoyaltyProfile, ProductType, SubLoyaltyPrograms}
import com.fasterxml.jackson.databind.node.ObjectNode
import mocks.{BookingMockHelper, ProductTokenMockHelper}
import models.starfruit.{DisplayBasis, DisplaySummary}
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.when
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatestplus.mockito.MockitoSugar
import transformers.{EnrichedChildRoom, EnrichedMasterRoom, EnrichedPricing}

class PropertiesPromoUtilsSpec
    extends AnyWordSpec
    with MockitoSugar
    with Matchers
    with BookingTokenHelper
    with ProductTokenMockHelper {
  val setupBookingContextFixture = new SetupBookingContextFixture {}

  val setupBookingRequestFixture = new SetupBookingRequestFixture {}
  import setupBookingRequestFixture._

  val mockPropertyRequest = mock[PropertyRequestItem]
  when(mockPropertyRequest.payment).thenReturn(Some(ProductPaymentRequest(ChargeOption.PayNow)))
  when(mockPropertyRequest.id).thenReturn("123")

  val searchCriteriaMock = mock[PropertySearchCriteria]
  when(searchCriteriaMock.simplifiedRoomSelectionRequest).thenReturn(None)
  when(mockPropertyRequest.propertySearchCriteria).thenReturn(searchCriteriaMock)
  val expectedToken = TokenMessage(defaultMultiBookingTokenString, 1)

  "getBessiePromoEligiblePropertyId" should {
    val bessieCampaign              = CampaignInfoRequest(id = Some(1), cid = 1, promotionCode = "CITI4NF")
    val productRequest              = mock[ProductsRequest]
    val mockCitiSetupBookingContext = mock[SetupBookingContext]
    val mockWhiteLabelInfo          = mock[WhiteLabelInfo]

    when(mockCitiSetupBookingContext.whiteLabelInfo).thenReturn(mockWhiteLabelInfo)
    when(mockWhiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.CitiUS)

    "apply promo for no booking token with single hotel" in {
      when(setupBookingRequest.productsRequest).thenReturn(productRequest)
      when(setupBookingRequest.productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
      when(setupBookingRequest.productsRequest.bookingToken).thenReturn(None)
      when(setupBookingRequest.campaignInfo).thenReturn(
        Some(CampaignInfoRequest(id = Some(1), cid = 51, promotionCode = "CITI4THNIGHT"))
      )

      val result =
        getPartnerPromoEligiblePropertyId(setupBookingRequest, mockCitiSetupBookingContext)
      result shouldBe Some("123")
    }

    "do not apply promo for no booking token with multi hotel" in {
      when(setupBookingRequest.productsRequest.propertyRequests)
        .thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))
      when(setupBookingRequest.productsRequest.bookingToken).thenReturn(None)
      val result =
        getPartnerPromoEligiblePropertyId(setupBookingRequest, mockCitiSetupBookingContext)
      result shouldBe None
    }

    "do not apply promo for booking token present with no campaign with multi hotel" in {
      when(setupBookingRequest.productsRequest.propertyRequests)
        .thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))
      when(setupBookingRequest.productsRequest.bookingToken).thenReturn(Some(expectedToken))
      when(setupBookingRequest.campaignInfo).thenReturn(None)
      val result =
        getPartnerPromoEligiblePropertyId(
          setupBookingRequest,
          mockCitiSetupBookingContext
        )
      result shouldBe None
    }

    "apply promo for booking token present with campaign and single hotel" in {
      when(setupBookingRequest.productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
      when(setupBookingRequest.productsRequest.bookingToken).thenReturn(Some(expectedToken))
      when(setupBookingRequest.campaignInfo).thenReturn(Some(bessieCampaign))
      val result =
        getPartnerPromoEligiblePropertyId(
          setupBookingRequest,
          mockCitiSetupBookingContext
        )
      result shouldBe Some("123")
    }

    "apply promo for booking token present with campaign and most expensive multi hotel" in {
      when(setupBookingRequest.productsRequest.propertyRequests)
        .thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))
      when(setupBookingRequest.productsRequest.bookingToken).thenReturn(Some(expectedToken))
      when(setupBookingRequest.campaignInfo).thenReturn(Some(bessieCampaign))

      val mockCitiSetupBookingContext = mock[SetupBookingContext]
      val mockWhiteLabelInfo          = mock[WhiteLabelInfo]

      when(mockCitiSetupBookingContext.whiteLabelInfo).thenReturn(mockWhiteLabelInfo)
      when(mockWhiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.CitiUS)
      val lessExpensivePaymentAmount: PaymentAmount =
        PaymentAmount("THB", 1150, 60, 0.1, 0.1, 0.1, 0.1, Some("string"), 0, 0, 0, 0, 0, 0, 0, Some("string"))
      val moreExpensivePaymentAmount: PaymentAmount =
        PaymentAmount("THB", 2150, 60, 0.1, 0.1, 0.1, 0.1, Some("string"), 0, 0, 0, 0, 0, 0, 0, Some("string"))
      val bapiBooking1: BAPIBooking =
        getMockBapiBooking.copy(productTokenKey = Some("234"), paymentAmount = Some(lessExpensivePaymentAmount))
      val bapiBooking2: BAPIBooking =
        getMockBapiBooking.copy(productTokenKey = Some("123"), paymentAmount = Some(moreExpensivePaymentAmount))
      val bookingToken = genBookingToken(bapiBookingToOption = Some(Seq(bapiBooking1, bapiBooking2)))
      when(setupBookingRequest.productsRequest.bookingToken)
        .thenReturn(Some(bookingToken))

      val result =
        getPartnerPromoEligiblePropertyId(
          setupBookingRequest,
          mockCitiSetupBookingContext
        )
      result shouldBe Some("123")
    }

    "do not apply promo for booking token present with campaign and most expensive multi hotel" in {
      when(setupBookingRequest.productsRequest.propertyRequests)
        .thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))
      when(setupBookingRequest.productsRequest.bookingToken).thenReturn(Some(expectedToken))
      when(setupBookingRequest.campaignInfo).thenReturn(Some(bessieCampaign))
      val result =
        getPartnerPromoEligiblePropertyId(
          setupBookingRequest,
          mockCitiSetupBookingContext
        )
      result shouldBe None
    }
  }

  "getBessieDiscountRequest" should {
    val bessieCampaign = CampaignInfoRequest(id = Some(1), cid = 1, promotionCode = "CITI4NF")
    val memberEmail    = Some("<EMAIL>")

    "get discount request when campaign info is present" in {
      when(setupBookingRequest.campaignInfo).thenReturn(Some(bessieCampaign))
      val result =
        getPartnerDiscountRequest(setupBookingRequest, memberEmail)
      result shouldBe Some(
        DiscountRequest(
          Some("<EMAIL>"),
          Some(List(CampaignInfoInternal(CampaignInfo(1, 1, "CITI4NF", CampaignDiscountType.None, 0, None), None)))
        )
      )
    }

    "no discount request when campaign info is absent" in {
      when(setupBookingRequest.campaignInfo).thenReturn(None)
      val result =
        getPartnerDiscountRequest(setupBookingRequest, memberEmail)
      result shouldBe None
    }
  }

  "getMostExpensiveBessieProductTokenKey" should {
    val mockCitiSetupBookingContext = mock[SetupBookingContext]
    val mockWhiteLabelInfo          = mock[WhiteLabelInfo]

    when(mockCitiSetupBookingContext.whiteLabelInfo).thenReturn(mockWhiteLabelInfo)
    when(mockWhiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.CitiUS)

    "return most expensive property during multi property call" in {
      val lessExpensivePaymentAmount: PaymentAmount =
        PaymentAmount("THB", 1150, 60, 0.1, 0.1, 0.1, 0.1, Some("string"), 0, 0, 0, 0, 0, 0, 0, Some("string"))
      val moreExpensivePaymentAmount: PaymentAmount =
        PaymentAmount("THB", 2150, 60, 0.1, 0.1, 0.1, 0.1, Some("string"), 0, 0, 0, 0, 0, 0, 0, Some("string"))
      val bapiBooking1: BAPIBooking =
        getMockBapiBooking.copy(productTokenKey = Some("1"), paymentAmount = Some(lessExpensivePaymentAmount))
      val bapiBooking2: BAPIBooking =
        getMockBapiBooking.copy(productTokenKey = Some("2"), paymentAmount = Some(moreExpensivePaymentAmount))
      val bookingToken = genBookingToken(bapiBookingToOption = Some(Seq(bapiBooking1, bapiBooking2)))
      val result =
        getMostExpensiveBessieProductTokenKey(Some(bookingToken))

      result shouldBe Some("2")
    }

    "return the only property during single property call" in {
      val lessExpensivePaymentAmount: PaymentAmount =
        PaymentAmount("THB", 1150, 60, 0.1, 0.1, 0.1, 0.1, Some("string"), 0, 0, 0, 0, 0, 0, 0, Some("string"))
      val bapiBooking: BAPIBooking =
        getMockBapiBooking.copy(productTokenKey = Some("1"), paymentAmount = Some(lessExpensivePaymentAmount))
      val bookingToken = genBookingToken(bapiBookingToOption = Some(Seq(bapiBooking)))
      val result =
        getMostExpensiveBessieProductTokenKey(Some(bookingToken))

      result shouldBe Some("1")
    }
  }

  "isPartnerPromotionCodeEligible" should {
    "return true when partnerDiscountRequest and partnerPromoEligiblePropertyId are defined" in {
      val mockSetupBookingContext        = mock[SetupBookingContext]
      val partnerDiscountRequest         = Some(mock[DiscountRequest])
      val partnerPromoEligiblePropertyId = Some("1")
      val propertyProductItems           = Seq.empty
      val campaignList                   = Seq.empty
      val flightConfirmationData         = Seq.empty
      isPartnerPromotionCodeEligible(
        partnerDiscountRequest,
        partnerPromoEligiblePropertyId,
        propertyProductItems,
        campaignList,
        flightConfirmationData,
        ProductsRequest(),
        None
      )(mockSetupBookingContext) shouldBe true
    }

    "return false when partnerDiscountRequest is defined and partnerPromoEligiblePropertyId is not defined" in {
      val mockSetupBookingContext        = mock[SetupBookingContext]
      val partnerDiscountRequest         = Some(mock[DiscountRequest])
      val partnerPromoEligiblePropertyId = None
      val propertyProductItems           = Seq.empty
      val campaignList                   = Seq.empty
      val flightConfirmationData         = Seq.empty
      isPartnerPromotionCodeEligible(
        partnerDiscountRequest,
        partnerPromoEligiblePropertyId,
        propertyProductItems,
        campaignList,
        flightConfirmationData,
        ProductsRequest(),
        None
      )(mockSetupBookingContext) shouldBe false
    }

    "return false when partnerDiscountRequest is not defined and partnerPromoEligiblePropertyId is defined" in {
      val mockSetupBookingContext        = mock[SetupBookingContext]
      val partnerDiscountRequest         = None
      val partnerPromoEligiblePropertyId = Some("1")
      val propertyProductItems           = Seq.empty
      val campaignList                   = Seq.empty
      val flightConfirmationData         = Seq.empty
      isPartnerPromotionCodeEligible(
        partnerDiscountRequest,
        partnerPromoEligiblePropertyId,
        propertyProductItems,
        campaignList,
        flightConfirmationData,
        ProductsRequest(),
        None
      )(mockSetupBookingContext) shouldBe false
    }

    "return false when partnerDiscountRequest is not defined and partnerPromoEligiblePropertyId is not defined" in {
      val mockSetupBookingContext        = mock[SetupBookingContext]
      val partnerDiscountRequest         = None
      val partnerPromoEligiblePropertyId = None
      val propertyProductItems           = Seq.empty
      val campaignList                   = Seq.empty
      val flightConfirmationData         = Seq.empty
      isPartnerPromotionCodeEligible(
        partnerDiscountRequest,
        partnerPromoEligiblePropertyId,
        propertyProductItems,
        campaignList,
        flightConfirmationData,
        ProductsRequest(),
        None
      )(mockSetupBookingContext) shouldBe false
    }
  }

  "isPartnerPromoEligible" should {
    "always return true" in {
      val result = ClubTravelPromoUtils.isPartnerPromoEligible
      result shouldBe true
    }
  }

  "isPropertyEligibleForPartnerPromo" should {
    val campaign = Campaign(
      "CITIONYX",
      DiscountType.HotelCredit,
      100.0,
      Some("USD"),
      Some(1),
      Some(1),
      Some("2099-12-31T00:00:00Z"),
      Some("2099-12-31T00:00:00Z"),
      ProductType.HOTELS,
      Some(100),
      Some(300)
    )
    val subLoyaltyProgram = SubLoyaltyPrograms(
      pointsBalance = Some(10.0),
      campaigns = Some(Seq(campaign))
    )
    val externalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
      None,
      Seq(
        subLoyaltyProgram
      )
    )
    val loyaltyProfile =
      LoyaltyProfile(
        None,
        Vector.empty[ObjectNode],
        None,
        None,
        None,
        Some(
          externalLoyaltyUserProfileResponse
        )
      )
    "return always true when property is eligible for partner promo" in {
      isPropertyEligibleForPartnerPromo(
        None,
        true
      ) shouldBe true
    }

    "return always false when property is not eligible for partner promo" in {
      isPropertyEligibleForPartnerPromo(
        None,
        false
      ) shouldBe false
    }

    "return true when promocode discount type is travel credit" in {
      isPropertyEligibleForPartnerPromo(
        Some(loyaltyProfile),
        false
      ) shouldBe true
    }

    "return false when promocode discount type is not travel credit" in {
      val modifiedLoyaltyProfile = loyaltyProfile.copy(
        externalLoyaltyProfileInfo = Some(
          externalLoyaltyUserProfileResponse.copy(
            subLoyaltyPrograms = Seq(
              subLoyaltyProgram.copy(
                campaigns = Some(
                  Seq(
                    campaign.copy(discountType = DiscountType.FourthNightFree)
                  )
                )
              )
            )
          )
        )
      )

      isPropertyEligibleForPartnerPromo(
        Some(modifiedLoyaltyProfile),
        false
      ) shouldBe false
    }

    "return false when no loyalty profile" in {
      isPropertyEligibleForPartnerPromo(
        None,
        false
      ) shouldBe false
    }

    "return false when no external loyalty profile" in {
      val modifiedLoyaltyProfile = loyaltyProfile.copy(
        externalLoyaltyProfileInfo = None
      )
      isPropertyEligibleForPartnerPromo(
        Some(modifiedLoyaltyProfile),
        false
      ) shouldBe false
    }

    "return false when no campaign" in {
      val modifiedLoyaltyProfile = loyaltyProfile.copy(
        externalLoyaltyProfileInfo = Some(
          externalLoyaltyUserProfileResponse.copy(
            subLoyaltyPrograms = Seq(
              subLoyaltyProgram.copy(
                campaigns = None
              )
            )
          )
        )
      )
      isPropertyEligibleForPartnerPromo(
        Some(modifiedLoyaltyProfile),
        false
      ) shouldBe false
    }
  }

  "getTravelCreditLeftAmount" should {
    val mockMasterRoom1                   = mock[EnrichedMasterRoom]
    val mockMasterRoom2                   = mock[EnrichedMasterRoom]
    val mockMasterRoom3                   = mock[EnrichedMasterRoom]
    val mockChildRoom1: EnrichedChildRoom = defaultChildRoom()
    val mockChildRoom2: EnrichedChildRoom = defaultChildRoom()
    val properties                        = mock[BookingPropertiesData]
    val papiProperties                    = mock[transformers.Properties]

    val papiProperty1 = createMockPropertyWithRoomUid(1, Some("room-uid"), Some("identity"))
    val papiProperty2 = createMockPropertyWithRoomUid(2, Some("room-uid2"), Some("identity2"))
    val papiProperty3 = createMockPropertyWithRoomUid(3, Some("room-uid3"), Some("identity3"))
    when(papiProperties.property) thenReturn Seq(papiProperty1, papiProperty2)
    when(properties.papiProperties) thenReturn Some(papiProperties)
    when(papiProperty1.masterRooms) thenReturn Seq(mockMasterRoom1)
    when(papiProperty2.masterRooms) thenReturn Seq(mockMasterRoom2)
    when(papiProperty3.masterRooms) thenReturn Seq(mockMasterRoom3)
    when(mockMasterRoom1.childrenRooms) thenReturn List(mockChildRoom1)
    when(mockMasterRoom2.childrenRooms) thenReturn List(mockChildRoom2)
    when(mockMasterRoom3.childrenRooms) thenReturn List(mockChildRoom2)
    when(mockChildRoom1.pricing) thenReturn Map(
      "THB" -> EnrichedPricing(
        mock[DisplayBasis],
        mock[DisplayBasis],
        Seq.empty,
        Nil,
        displaySummary = mock[DisplaySummary],
        promotionDiscountAmount = Some(-6000)
      ),
      "USD" -> EnrichedPricing(
        mock[DisplayBasis],
        mock[DisplayBasis],
        Seq.empty,
        Nil,
        displaySummary = mock[DisplaySummary],
        promotionDiscountAmount = Some(-200)
      )
    )
    when(mockChildRoom2.pricing) thenReturn Map(
      "THB" -> EnrichedPricing(
        mock[DisplayBasis],
        mock[DisplayBasis],
        Seq.empty,
        Nil,
        displaySummary = mock[DisplaySummary],
        promotionDiscountAmount = Some(-3000)
      ),
      "USD" -> EnrichedPricing(
        mock[DisplayBasis],
        mock[DisplayBasis],
        Seq.empty,
        Nil,
        displaySummary = mock[DisplaySummary],
        promotionDiscountAmount = Some(-100)
      )
    )
    val campaign = Campaign(
      "CITIONYX",
      DiscountType.HotelCredit,
      100.0,
      Some("USD"),
      Some(1),
      Some(1),
      Some("2099-12-31T00:00:00Z"),
      Some("2099-12-31T00:00:00Z"),
      ProductType.HOTELS,
      Some(300),
      Some(300)
    )
    val subLoyaltyProgram = SubLoyaltyPrograms(
      pointsBalance = Some(10.0),
      campaigns = Some(Seq(campaign))
    )
    val externalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
      None,
      Seq(
        subLoyaltyProgram
      )
    )
    val loyaltyProfile =
      LoyaltyProfile(
        None,
        Vector.empty[ObjectNode],
        None,
        None,
        None,
        Some(
          externalLoyaltyUserProfileResponse
        )
      )

    "should return correct amount after deduction from all previous properties" in {
      val result = getTravelCreditLeftAmount(Some(loyaltyProfile), Seq(properties))
      result shouldBe Some(0.0)
    }

    "should not send negative total amount" in {
      when(papiProperties.property) thenReturn Seq(papiProperty1, papiProperty2, papiProperty3)
      val result = getTravelCreditLeftAmount(Some(loyaltyProfile), Seq(properties))
      result shouldBe Some(0.0)
    }
  }

  "isBenefitEligible" should {
    val campaign = Campaign(
      "CITIONYX",
      DiscountType.HotelCredit,
      100.0,
      Some("USD"),
      Some(1),
      Some(1),
      Some("2099-12-31T00:00:00Z"),
      Some("2099-12-31T00:00:00Z"),
      ProductType.HOTELS,
      Some(300),
      Some(300)
    )
    "should return true when booking token is null" in {
      val productRequest = mock[ProductsRequest]
      when(setupBookingRequest.productsRequest).thenReturn(productRequest)
      when(setupBookingRequest.productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
      when(setupBookingRequest.productsRequest.bookingToken).thenReturn(None)
      val result = isBenefitEligible(campaign, setupBookingRequest)
      result shouldBe true
    }

    "should return false when booking token is not null" in {
      val productRequest = mock[ProductsRequest]
      when(setupBookingRequest.productsRequest).thenReturn(productRequest)
      when(setupBookingRequest.productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
      when(setupBookingRequest.productsRequest.bookingToken).thenReturn(Some(expectedToken))
      val result = isBenefitEligible(campaign, setupBookingRequest)
      result shouldBe false
    }
  }
}
