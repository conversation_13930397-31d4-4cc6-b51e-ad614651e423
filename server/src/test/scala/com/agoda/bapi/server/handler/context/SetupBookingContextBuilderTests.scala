package com.agoda.bapi.server.handler.context

import com.agoda.bapi.common.directive.HttpHeaderBase
import com.agoda.bapi.common.handler.RequestContextBuilder
import com.agoda.bapi.common.message.{BookingCreationFlowContext, DeviceContext, DevicePlatform, Request}
import com.agoda.bapi.common.message.setupBooking.{PackageRequest, ProductsRequest, SetupBookingRequest}
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.service.MessagesBag
import com.agoda.bapi.common.token.PropertySetupBookingToken
import com.agoda.bapi.server.utils.ProductTokenExtractorUtils
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import mocks.RequestContextMock
import org.scalatest.BeforeAndAfter
import org.mockito.Mockito._
import org.mockito.ArgumentMatchers._

import scala.concurrent.Future
import scala.util.{Success, Try}
import org.scalatestplus.mockito.MockitoSugar
import org.scalatest.funspec.AsyncFunSpec
import org.scalatest.matchers.should.Matchers

class SetupBookingContextBuilderTests
    extends AsyncFunSpec
    with Matchers
    with MockitoSugar
    with BeforeAndAfter
    with RequestContextMock {
  private val requestContextBuilder         = mock[RequestContextBuilder]
  private val header                        = mock[HttpHeaderBase]
  private val productTokenUtils             = mock[ProductTokenExtractorUtils]
  private val expectedTokenizedPackageToken = PackageRequest("client-in-token", Some("interSystem-in-token"))
  private val sessionFromToken = SetupBookingSessionContext(
    properties = Map("A" -> PropertySetupBookingToken("A", None, None, None, Some(1L))),
    packages = Some(expectedTokenizedPackageToken),
    timestamp = Some(44L),
    sessionExpired = false
  )

  describe("RequestContext") {
    before {
      reset(requestContextBuilder)
      reset(header)

      when(header.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()))
      when(productTokenUtils.extractSetupBookingToken(any())).thenReturn(Try(sessionFromToken))
    }

    describe("build") {
      it("should build SetupBookingContext with no booking token") {
        when(requestContextBuilder.build(any[BookingCreationFlowContext](), any(), any(), any(), any()))
          .thenReturn(
            Future.successful(
              requestContext(mock[MessagesBag])
                .copy(whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()))
            )
          )

        val deviceContext = DeviceContext(DevicePlatform.WebDesktop, Some("AAA"))
        val sbContextBuilder =
          new SetupBookingContextBuilderImpl(requestContextBuilder, productTokenUtils = productTokenUtils)

        val result = sbContextBuilder.build(
          SetupBookingRequest(
            ProductsRequest(),
            deviceContext = Some(deviceContext)
          ),
          header
        )

        result.map { r =>
          r.deviceContext.get.deviceTypeId shouldBe deviceContext.deviceTypeId
          r.deviceContext.get.deviceId shouldBe deviceContext.deviceId

          r.whiteLabelInfo.whiteLabelId shouldBe header.whiteLabelInfo.whiteLabelId
          r.session shouldNot be(sessionFromToken)
        }
      }

      it("should build SetupBookingContext with booking token") {
        when(requestContextBuilder.build(any[BookingCreationFlowContext](), any(), any(), any(), any()))
          .thenReturn(
            Future.successful(
              requestContext(mock[MessagesBag])
                .copy(whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()))
            )
          )

        val deviceContext = DeviceContext(DevicePlatform.WebDesktop, Some("AAA"))
        val sbContextBuilder =
          new SetupBookingContextBuilderImpl(requestContextBuilder, productTokenUtils = productTokenUtils)

        val result = sbContextBuilder.build(
          SetupBookingRequest(
            ProductsRequest(bookingToken = Some(TokenMessage("token", 1))),
            deviceContext = Some(deviceContext)
          ),
          header
        )

        result.map { r =>
          r.deviceContext.get.deviceTypeId shouldBe deviceContext.deviceTypeId
          r.deviceContext.get.deviceId shouldBe deviceContext.deviceId
          r.whiteLabelInfo.whiteLabelId shouldBe header.whiteLabelInfo.whiteLabelId
          r.session shouldBe sessionFromToken
        }
      }

      it("should create new session for request with no booking token") {
        when(requestContextBuilder.build(any[BookingCreationFlowContext](), any(), any(), any(), any()))
          .thenReturn(
            Future.successful(
              requestContext(mock[MessagesBag])
                .copy(whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()))
            )
          )

        val deviceContext = DeviceContext(DevicePlatform.WebDesktop, Some("AAA"))
        val sbContextBuilder =
          new SetupBookingContextBuilderImpl(requestContextBuilder, productTokenUtils = productTokenUtils)
        val expectedPackageToken = PackageRequest("client", Some("interSystem"))

        val result = sbContextBuilder.build(
          SetupBookingRequest(
            ProductsRequest(packageRequest = Some(expectedPackageToken)),
            deviceContext = Some(deviceContext)
          ),
          header
        )

        result.map { r =>
          r.session.sessionExpired shouldBe false
          r.session.timestamp.isDefined shouldBe true
          r.session.packages shouldBe Some(expectedPackageToken)
        }
      }
    }

    describe("build [packages]") {
      it("should with no booking token use package token from request") {
        when(requestContextBuilder.build(any[BookingCreationFlowContext](), any(), any(), any(), any()))
          .thenReturn(
            Future.successful(
              requestContext(mock[MessagesBag])
                .copy(whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()))
            )
          )

        val deviceContext = DeviceContext(DevicePlatform.WebDesktop, Some("AAA"))
        val sbContextBuilder =
          new SetupBookingContextBuilderImpl(requestContextBuilder, productTokenUtils = productTokenUtils)
        val expectedPackageToken = PackageRequest("client", Some("interSystem"))

        val result = sbContextBuilder.build(
          SetupBookingRequest(
            ProductsRequest(packageRequest = Some(expectedPackageToken)),
            deviceContext = Some(deviceContext)
          ),
          header
        )

        result.map { r =>
          r.session.packages shouldBe Some(expectedPackageToken)
          r.bookingFlowType shouldBe BookingFlow.Package
        }
      }

      it("should with booking token use package token from booking token") {
        when(requestContextBuilder.build(any[BookingCreationFlowContext](), any(), any(), any(), any()))
          .thenReturn(
            Future.successful(
              requestContext(mock[MessagesBag])
                .copy(whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()))
            )
          )

        val expectedPackageToken = PackageRequest("client", Some("interSystem"))
        when(productTokenUtils.extractSetupBookingToken(any()))
          .thenReturn(Try(SetupBookingSessionContext(packages = Some(expectedPackageToken))))
        val deviceContext = DeviceContext(DevicePlatform.WebDesktop, Some("AAA"))
        val sbContextBuilder =
          new SetupBookingContextBuilderImpl(requestContextBuilder, productTokenUtils = productTokenUtils)

        val result = sbContextBuilder.build(
          SetupBookingRequest(
            ProductsRequest(
              packageRequest = Some(expectedPackageToken.copy(clientToken = "not expected")),
              bookingToken = Some(mock[TokenMessage])
            ),
            deviceContext = Some(deviceContext)
          ),
          header
        )

        result.map { r =>
          r.session.packages shouldBe Some(expectedPackageToken)
          r.bookingFlowType shouldBe BookingFlow.Package
        }
      }
    }
  }

}
