package com.agoda.bapi.server.facades.properties

import com.agoda.bapi.common.message.pricebreakdown.{PriceBreakdownNode, PriceBreakdownResponse, PriceBreakdownType}
import com.agoda.bapi.common.message._
import com.agoda.bapi.common.model.CurrencyCode
import com.agoda.mpb.common.models.state.ProductType
import com.agoda.bapi.common.token.Money
import com.agoda.bapi.server.addon.AddOnDefaults
import com.agoda.bapi.server.addon.AddOnDefaults.defaultPriceBreakdown
import com.agoda.bapi.server.facades.helpers.SetupBookingContextFixture
import com.agoda.bapi.server.model.BookingPropertiesData
import com.agoda.bapi.server.service.PapiPropertyStatus
import com.agoda.bapi.server.service.pricebreakdown.PriceBreakdownService
import mocks.MockAddOnV2Data.mockBreakdown
import mocks.{MockAddOnV2Data, PropertyMock}
import models.pricing.enums.SwapRoomTypes
import models.starfruit.AlternativeRoom
import org.joda.time.{DateTime, LocalDate}
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.{any, argThat}
import org.mockito.Mockito.{reset, verify, when}
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar
import transformers.{EnrichedChildRoom, EnrichedPricing}

import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, Future}

class PropertiesPriceBreakdownCalculatorMixinSpec
    extends AsyncWordSpec
    with MockitoSugar
    with Matchers
    with PropertyMock
    with SetupBookingContextFixture
    with PropertiesPriceBreakdownCalculatorMixin {

  override val priceBreakdownService: PriceBreakdownService = mock[PriceBreakdownService]

  val mockPriceBreakDownNode: PriceBreakdownNode = PriceBreakdownNode(
    Some(
      PriceBreakdownResponse(
        `type` = PriceBreakdownType.TotalPrice,
        amount = Money(30, "SAR"),
        originalAmount = Some(Money(30, "SAR")),
        title = Some("Total Price"),
        date = Some(DateTime.now()),
        averageAmountPerUnit = Some(Money(30, "SAR")),
        productType = Some(ProductType.Hotel),
        productId = Some("123")
      )
    )
  )

  "extractAddOnData" should {
    "should return all selected addOn pricebreakdown items" in {
      val addOnPrices = extractAddOnData("Property_1", Some(AddOnDefaults.defaultAddOnDataWithCegFastTrack))
      addOnPrices shouldBe AddOnDefaults.defaultAddOnPrices
    }

    "should return empty when no choices are selected" in {
      val addOnDataWithNoSelection = AddOnDefaults.defaultAddOnDataWithCegFastTrack.copy(products =
        Seq(
          AddOnDefaults.defaultAddOnProductData.copy(choices =
            Seq(AddOnDefaults.defaultAddOnChoiceInternal.copy(isSelected = false))
          )
        )
      )
      val addOnPrices = extractAddOnData("Property_1", Some(addOnDataWithNoSelection))
      addOnPrices shouldBe Map.empty
    }

    "should return empty when addOn data is associated with another property" in {
      val addOnPrices = extractAddOnData("Property_2", Some(AddOnDefaults.defaultAddOnDataWithCegFastTrack))
      addOnPrices shouldBe Map.empty
    }
  }

  "MultiHotelBreakDown" should {
    "MultiHotelBreakDown return sum of multi properties total price" in {

      val searchCriteria1 = PropertySearchCriteria(
        None,
        "",
        OccupancyRequest(1, 1, 0, Nil, None),
        DurationRequest(LocalDate.now(), 1),
        None,
        None,
        None,
        None,
        None,
        None
      )
      val property1 = createTopLevelMockProperties(
        12345,
        Seq(createMockMasterRoom(12345, None, "", None, List(defaultChildRoom(false, 2)))),
        None
      )
      val searchCriteria2 = PropertySearchCriteria(
        None,
        "",
        OccupancyRequest(1, 1, 0, Nil, None),
        DurationRequest(LocalDate.now(), 1),
        None,
        None,
        None,
        None,
        None,
        None
      )
      val property2 = createTopLevelMockProperties(
        12345,
        Seq(createMockMasterRoom(54321, None, "", None, List(defaultChildRoom(false, 2)))),
        None
      )
      val propertiesData = Seq(
        BookingPropertiesData("123", "", Some(property1), None, PapiPropertyStatus.Ok, None, Some(searchCriteria1)),
        BookingPropertiesData("321", "", Some(property2), None, PapiPropertyStatus.Ok, None, Some(searchCriteria2))
      )
      mockPriceBreakdownService()
      val f = multiHotelPriceBreakdown(
        propertiesData,
        requestContext = requestContext,
        isDisableShowBookingFee = false,
        isInclusivePaySupplier = false,
        fixDoubleTaxesAndFeesPaySupplier = false
      )
      val result = Await.result(f, 20.seconds)
      assert(result == Seq(Some(mockPriceBreakDownNode), Some(mockPriceBreakDownNode)))
    }
    "MultiHotelBreakDown return property total price in case of single property" in {
      val searchCriteria = PropertySearchCriteria(
        None,
        "",
        OccupancyRequest(1, 1, 0, Nil, None),
        DurationRequest(LocalDate.now(), 1),
        None,
        None,
        None,
        None,
        None,
        None
      )

      val property = createTopLevelMockProperties(
        12345,
        Seq(createMockMasterRoom(12345, None, "", None, List(defaultChildRoom(false, 2)))),
        None
      )
      val propertiesData = Seq(
        BookingPropertiesData("123", "", Some(property), None, PapiPropertyStatus.Ok, None, Some(searchCriteria))
      )
      mockPriceBreakdownService()
      val f = multiHotelPriceBreakdown(
        propertiesData,
        requestContext = requestContext,
        isDisableShowBookingFee = false,
        isInclusivePaySupplier = false,
        fixDoubleTaxesAndFeesPaySupplier = false
      )
      val result = Await.result(f, 20.seconds)
      assert(result == Seq(Some(mockPriceBreakDownNode)))
    }
    "MultiHotelBreakDown return property total price in case of single property with alternatives enabled" in {
      val selectedRoomUid = "selectedRoom"
      val altRoomUid      = "altRoom"
      val searchCriteria = PropertySearchCriteria(
        None,
        selectedRoomUid,
        OccupancyRequest(1, 1, 0, Nil, None),
        DurationRequest(LocalDate.now(), 1),
        None,
        None,
        None,
        None,
        Some(
          SimplifiedRoomSelectionRequest(
            selectedRoomUid,
            alternativeOptIn = Some(AlternativeRoomOptIn(Some(SwapRoomTypes.BreakfastUpSell.i)))
          )
        ),
        None
      )

      val expectedChildRoom = defaultChildRoom(false, 2, Some(selectedRoomUid))
      val optInRoom         = defaultChildRoom(false, 1, Some(altRoomUid))
      val rooms = List(
        optInRoom,
        expectedChildRoom
      )
      val property = createTopLevelMockProperties(
        12345,
        Seq(createMockMasterRoom(12345, None, "", None, rooms)),
        None
      )
      val p = property.property.head
      when(p.roomSwapping).thenReturn(List(AlternativeRoom(selectedRoomUid, altRoomUid, SwapRoomTypes.BreakfastUpSell)))
      when(p.getAllRooms).thenReturn(rooms)
      val propertiesData = Seq(
        BookingPropertiesData("123", "", Some(property), None, PapiPropertyStatus.Ok, None, Some(searchCriteria))
      )

      mockPriceBreakdownService(argThat[EnrichedChildRoom](r => r.uid == Some(altRoomUid)))

      val f = multiHotelPriceBreakdown(
        propertiesData,
        isSingleProperty = true,
        requestContext = requestContext,
        isDisableShowBookingFee = false,
        isInclusivePaySupplier = false,
        fixDoubleTaxesAndFeesPaySupplier = false
      )
      val result = Await.result(f, 20.seconds)
      assert(result == Seq(Some(mockPriceBreakDownNode)))
    }

    "MultiHotelBreakDown return price breakdown based on overridden price" in {
      val selectedRoomUid        = "selectedRoom"
      val rateChannelSwapRoomUid = "RateChannelSwapRoom"
      val searchCriteria = PropertySearchCriteria(
        None,
        selectedRoomUid,
        OccupancyRequest(1, 1, 0, Nil, None),
        DurationRequest(LocalDate.now(), 1),
        None,
        None,
        None,
        None,
        Some(SimplifiedRoomSelectionRequest(selectedRoomUid)),
        None
      )

      val expectedChildRoom   = defaultChildRoom(false, 0, Some(selectedRoomUid))
      val rateChannelSwapRoom = defaultChildRoom(false, 0, Some(rateChannelSwapRoomUid))
      val rooms               = List(rateChannelSwapRoom, expectedChildRoom)
      val property = createTopLevelMockProperties(
        12345,
        Seq(createMockMasterRoom(12345, None, "", None, rooms)),
        None
      )
      val mockPriceMap = mock[Map[CurrencyCode, EnrichedPricing]]
      val p            = property.property.head
      when(p.roomSwapping).thenReturn(
        List(AlternativeRoom(selectedRoomUid, rateChannelSwapRoomUid, SwapRoomTypes.RateChannelSwap))
      )
      when(p.getAllRooms).thenReturn(rooms)
      val propertiesData = Seq(
        BookingPropertiesData("123", "", Some(property), None, PapiPropertyStatus.Ok, None, Some(searchCriteria))
      )

      reset(priceBreakdownService)
      mockPriceBreakdownService(
        childRoomMock = argThat[EnrichedChildRoom](r => r.uid.contains(rateChannelSwapRoomUid))
      )

      val f = multiHotelPriceBreakdown(
        propertiesData,
        isSingleProperty = true,
        requestContext = requestContext,
        isDisableShowBookingFee = false,
        overridePriceForDisplay = Some(mockPriceMap),
        isInclusivePaySupplier = false,
        fixDoubleTaxesAndFeesPaySupplier = false
      )
      val result = Await.result(f, 20.seconds)
      assert(result == Seq(Some(mockPriceBreakDownNode)))
    }

    "MultiHotelBreakDown calls getPriceBreakdown with correct addOn v1 + v2 arguments" in {
      val selectedRoomUid        = "selectedRoom"
      val rateChannelSwapRoomUid = "RateChannelSwapRoom"
      val searchCriteria = PropertySearchCriteria(
        None,
        selectedRoomUid,
        OccupancyRequest(1, 1, 0, Nil, None),
        DurationRequest(LocalDate.now(), 1),
        None,
        None,
        None,
        None,
        Some(SimplifiedRoomSelectionRequest(selectedRoomUid)),
        None
      )

      val expectedChildRoom   = defaultChildRoom(isNoCreditCardEligible = false, 0, Some(selectedRoomUid))
      val rateChannelSwapRoom = defaultChildRoom(isNoCreditCardEligible = false, 0, Some(rateChannelSwapRoomUid))
      val rooms               = List(rateChannelSwapRoom, expectedChildRoom)
      val property = createTopLevelMockProperties(
        12345,
        Seq(createMockMasterRoom(12345, None, "", None, rooms)),
        None
      )
      val mockPriceMap = mock[Map[CurrencyCode, EnrichedPricing]]
      val p            = property.property.head
      when(p.roomSwapping).thenReturn(
        List(AlternativeRoom(selectedRoomUid, rateChannelSwapRoomUid, SwapRoomTypes.RateChannelSwap))
      )
      when(p.getAllRooms).thenReturn(rooms)
      val propertiesData = Seq(
        BookingPropertiesData("Property_1", "", Some(property), None, PapiPropertyStatus.Ok, None, Some(searchCriteria))
      )
      val mockSaleInclusiveBreakdown = mockBreakdown(
        itemId = 12,
        typeId = 401,
        requestedAmount = 101.00,
        usdExchangeRate = 1.0
      )
      val mockConfirmationData =
        Some(MockAddOnV2Data.mockConfirmationData(saleInclusiveBreakdown = mockSaleInclusiveBreakdown))

      val fakeAddOnV2Data = Seq(
        MockAddOnV2Data
          .sampleAddOn(productType = ProductType.CancelForAnyReason, confirmationData = mockConfirmationData),
        MockAddOnV2Data.sampleAddOn(productType = ProductType.TripProtection, confirmationData = mockConfirmationData)
      )

      reset(priceBreakdownService)
      mockPriceBreakdownService()
      multiHotelPriceBreakdown(
        propertiesData,
        isSingleProperty = true,
        requestContext = requestContext,
        isDisableShowBookingFee = false,
        overridePriceForDisplay = Some(mockPriceMap),
        addOnData = Some(AddOnDefaults.defaultAddOnDataWithCegFastTrack),
        addOnDataV2 = fakeAddOnV2Data,
        isInclusivePaySupplier = false,
        fixDoubleTaxesAndFeesPaySupplier = false
      ).map { result =>
        verify(priceBreakdownService).getPriceBreakdown(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          ArgumentMatchers.eq(
            Map(
              ProductType.CEGFastTrack       -> Seq(defaultPriceBreakdown),
              ProductType.CancelForAnyReason -> mockConfirmationData.value.priceBreakdown,
              ProductType.TripProtection     -> mockConfirmationData.value.priceBreakdown
            )
          ),
          ArgumentMatchers.eq(
            Map(
              ProductType.CancelForAnyReason -> mockConfirmationData.value.priceBreakdownDisplayContentOpt,
              ProductType.TripProtection     -> mockConfirmationData.value.priceBreakdownDisplayContentOpt
            )
          ),
          any(),
          any(),
          any()
        )(any())
        assert(result == Seq(Some(mockPriceBreakDownNode)))
      }
    }
  }

  private def mockPriceBreakdownService(
      childRoomMock: EnrichedChildRoom = any(),
      overridePriceForDisplay: Option[Map[String, EnrichedPricing]] = any(),
      mockPriceBreakDownNode: PriceBreakdownNode = mockPriceBreakDownNode
  ): Unit = {
    when(
      priceBreakdownService.getPriceBreakdown(
        childRoomMock,
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        overridePriceForDisplay,
        any(),
        any(),
        any(),
        any(),
        any()
      )(
        any()
      )
    ).thenReturn(Future.successful(mockPriceBreakDownNode))
  }
}
