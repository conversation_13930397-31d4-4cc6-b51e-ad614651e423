package com.agoda.bapi.server.service

import com.agoda.bapi.common.constants.PropertyFeatureFlags
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message._
import com.agoda.bapi.common.message.creation.{CreditCardExpiration, HotelGuest}
import com.agoda.bapi.common.message.pricebreakdown.{PriceBreakdownNode, PriceBreakdownResponse, PriceBreakdownType}
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.model.activity.ActivityConfirmationData
import com.agoda.bapi.common.model.booking.{CreditCardOnFile, NonCardOnFile, PaymentMethodIcon}
import com.agoda.bapi.common.model.car.{CarConfirmationData, CarOptions}
import com.agoda.bapi.common.model.creation.DiscountType
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.model.{CampaignDiscountType, CampaignInfo, CampaignPromotionInfo, CampaignStatusType, CampaignType, ChargeOption, EligibleDiscount, Icon, PromoCmsData, PromotionCodeType, PromotionInfo, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.service.{FeatureAware, MessagesBag}
import com.agoda.bapi.common.token.Money
import com.agoda.bapi.common.token.flight.FlightToken
import com.agoda.bapi.creation.validation.WhiteLabelFeatureMock
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.{BookingPropertiesData, ExternalLoyaltyPointsValidationResult, FlightConfirmationData, ProductData}
import com.agoda.bapi.server.service.SetupBookingStatusProvider.{createRemark, createRemarkWithMeasurement}
import com.agoda.bapi.server.service.allotment.{AllotmentPreCheckStatus, AllotmentStatus}
import com.agoda.bapi.server.utils.SetupBookingMock
import com.agoda.mpb.common.CrossSellReasonType
import com.agoda.papi.enums.campaign.CampaignDiscountTypes
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import com.agoda.winterfell.output.MemberDetails
import com.softwaremill.quicklens._
import mocks.BookingMockHelper.{baseChildrenRoom, baseProperty, childrenRoomWithLoyaltyResponse, masterRoomWithChildRoomWithLoyaltyResponse}
import mocks.{PropertyMock, RequestContextMock, SetupBookingRequestMock, SetupBookingWithAlternativesMock}
import models.pricing.enums.{PaymentModel, PaymentModels}
import models.starfruit.{LoyaltyPaymentBoundaries, LoyaltyReasons}
import org.mockito.Mockito.{reset, when}
import org.scalatest.BeforeAndAfterEach
import org.scalatest.wordspec.AnyWordSpec
import org.scalatestplus.mockito.MockitoSugar
import transformers.{DFMetaResult, EnrichedCampaignDiscount, Properties}

class SetupBookingStatusProviderTest
    extends AnyWordSpec
    with MockitoSugar
    with SetupBookingRequestMock
    with SetupBookingMock
    with PropertyMock
    with SetupBookingWithAlternativesMock
    with RequestContextMock
    with BeforeAndAfterEach
    with WhiteLabelFeatureMock {

  private val roomIdentifier = "roomIdentifier"
  private def mockPropertyProductItem(
      paymentModels: PaymentModel = PaymentModels.Merchant,
      isPayLater: Boolean = false,
      isPayAtHotel: Boolean = false,
      loyaltyResponse: Option[LoyaltyPaymentBoundaries] = Some(
        LoyaltyPaymentBoundaries(
          min = 0,
          max = 100,
          maxBeforeGatewayLimit = 99.5,
          minTotalAfterRedeem = Some(0.5),
          canStayForFree = Some(false),
          reason = LoyaltyReasons.Success
        )
      ),
      cashbackRedemptionBoundaries: Option[LoyaltyPaymentBoundaries] = Some(
        LoyaltyPaymentBoundaries(
          min = 0,
          max = 100,
          maxBeforeGatewayLimit = 99.5,
          minTotalAfterRedeem = Some(0.5),
          canStayForFree = Some(false),
          reason = LoyaltyReasons.Success
        )
      ),
      dfFeatureFlag: Option[Int] = None,
      elapiErrorCode: Option[Int] = None
  ) =
    BookingPropertiesData(
      id = "1",
      content = "",
      papiProperties = Some(
        Properties(
          Seq(
            createMockPropertyWithRoomUid(
              propertyId = 1,
              uid = Some("uid"),
              roomIdentifier = Some(roomIdentifier),
              paymentModels,
              isPayLater,
              isPayAtHotel,
              loyaltyResponse,
              cashbackRedemptionBoundaries = cashbackRedemptionBoundaries,
              elapiErrorCode = elapiErrorCode,
              priceAdjustmentId = None
            )
          ),
          None
        )
      ),
      packageRequest = None,
      papiPropertyStatus = PapiPropertyStatus.Ok,
      selectedChargeOption = None,
      propertySearchCriteria = Some(
        PropertySearchCriteriaMock.value
          .copy(
            propertyId = Some(1),
            roomIdentifier = roomIdentifier,
            simplifiedRoomSelectionRequest = Some(SimplifiedRoomSelectionRequest(roomIdentifier)),
            pricingRequest = PropertySearchCriteriaMock.value.pricingRequest.map(request =>
              request.copy(dfFeatureFlags = request.dfFeatureFlags ++ Seq(dfFeatureFlag.getOrElse(-1)))
            )
          )
      )
    )
  private def mockFlightProductItem(isComplete: Boolean, hasFlight: Boolean, hasContent: Boolean) =
    FlightConfirmationData(
      id = "",
      token = "",
      submitToken = FlightToken(None, None),
      isCompleted = isComplete,
      hasFlight = hasFlight,
      hasContent = hasContent,
      isHackerFare = false,
      None,
      None,
      None,
      None,
      Map.empty
    )

  private def mockCreditCardOnFile() = {
    val iconType        = Seq(Icon(1, 1, "agoda.com/enabled.png"), Icon(1, 2, "agoda.com/disabled.png"))
    val paymentIconType = iconType.map(i => PaymentMethodIcon(i.iconType, i.iconUrl))
    val ccof = Seq(
      CreditCardOnFile(123, "1234", 1, paymentIconType, true, Some(CreditCardExpiration(2040, 1)))
    )
    ccof
  }

  private def mockNonCardOnFile() = {
    val iconType        = Seq(Icon(1, 1, "agoda.com/enabled.png"), Icon(1, 2, "agoda.com/disabled.png"))
    val paymentIconType = iconType.map(i => PaymentMethodIcon(i.iconType, i.iconUrl))
    val ncof = Seq(
      NonCardOnFile(123, 1, "aaa", paymentIconType, true)
    )
    ncof
  }

  private def mockCarConfirmationData() = CarConfirmationData(
    "1",
    "content",
    None,
    isCompleted = true,
    hasContent = true,
    mock[CarOptions],
    None,
    None
  )

  implicit val requestContext    = mock[RequestContext]
  private val mockWhiteLabelInfo = mock[WhiteLabelInfo]

  override def beforeEach(): Unit = {
    reset(requestContext)
    when(requestContext.whiteLabelInfo).thenReturn(mockWhiteLabelInfo)
    when(mockWhiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.MatchedRoomIdentifiersCheck)).thenReturn(true)
    when(requestContext.featureAware).thenReturn(None)
    when(requestContext.getExternalPartnerId()).thenReturn(None)
  }

  "SetupBookingStatusProvider" when {
    "BookingFlow is Property and No property return" should {
      "return ProductNotFound" in {
        val productItems = ProductData(
          properties = Seq.empty,
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          totalPriceDisplay = None,
          priceDisplayType = None,
          packageToken = None,
          priceChange = None,
          priceConfirmed = false
        )
        val productRequest = ProductsRequest(None, Seq.empty, Seq.empty, None)
        val context        = setupBookingContext(BookingFlow.SingleProperty)

        assertResult((InitializeBookingStatus.ProductNotFound, Some("No properties found"))) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is SingleProperty and No Room with request RoomIdentifier return" should {
      "return ProductItemNotFound" in {
        val productItem = bookingPropertyProduct()
        val productItems =
          ProductData(
            properties = Seq(productItem),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )

        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("id-1", "NotMatchRoomId")), Seq.empty, None)

        val context = setupBookingContext(BookingFlow.SingleProperty).copy(requestContext = requestContext)

        assertResult(
          (
            InitializeBookingStatus.ProductItemNotFound,
            Some("Property Id:1 papiResponseRoomIdentifier:matchedroomidentifiers requestRoomIdentifier:NotMatchRoomId")
          )
        ) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }

      "return success when roomIdentifier not match but shouldSkipRoomIdentifier is true" in {
        val productItem = bookingPropertyProduct()
        val productItems =
          ProductData(
            properties = Seq(productItem),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )

        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("id-1", "NotMatchRoomId")), Seq.empty, None)

        val mockFeatureAware = mock[FeatureAware]
        when(requestContext.whiteLabelInfo).thenReturn(mockWhiteLabelInfo)
        when(mockWhiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.MatchedRoomIdentifiersCheck)).thenReturn(false)
        when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))
        val context = setupBookingContext(BookingFlow.SingleProperty).copy(requestContext = requestContext)

        assertResult(
          (InitializeBookingStatus.Ok, None)
        ) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is SingleProperty and No Room with request RoomIdentifier return" when {
      "number of extra beds has not changed" should {
        "return ProductItemNotFound" in {
          val productItem = bookingPropertyProduct()
          val productItems =
            ProductData(
              properties = Seq(productItem),
              flights = Seq.empty,
              cars = Seq.empty,
              protections = Seq.empty,
              activities = Seq.empty,
              totalPriceDisplay = None,
              priceDisplayType = None,
              packageToken = None,
              priceChange = None,
              priceConfirmed = false
            )
          val propertyItemRequest =
            defaultPropertyItemRequest("id-1", "NotMatchRoomId")
          val requestItem = propertyItemRequest.copy(
            propertySearchCriteria = propertyItemRequest.propertySearchCriteria.copy(
              roomSelectionRequest = Some(
                defaultRoomSelectionRequest()
              )
            )
          )
          val productRequest =
            ProductsRequest(None, Seq(requestItem), Seq.empty, None)

          val context = setupBookingContext(BookingFlow.SingleProperty).copy(requestContext = requestContext)

          assertResult(
            (
              InitializeBookingStatus.ProductItemNotFound,
              Some(
                "Property Id:1 papiResponseRoomIdentifier:matchedroomidentifiers requestRoomIdentifier:NotMatchRoomId"
              )
            )
          ) {
            SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(
              context
            )
          }
        }
      }

      "number of extra beds changed and valid number of rooms in PAPI response" should {
        "return OK" in {
          val productItem = bookingPropertyProduct()
          val productItems =
            ProductData(
              properties = Seq(productItem),
              flights = Seq.empty,
              cars = Seq.empty,
              protections = Seq.empty,
              activities = Seq.empty,
              totalPriceDisplay = None,
              priceDisplayType = None,
              packageToken = None,
              priceChange = None,
              priceConfirmed = false
            )
          val propertyItemRequest =
            defaultPropertyItemRequest("id-1", "NotMatchRoomId")
          val requestItem = propertyItemRequest.copy(
            propertySearchCriteria = propertyItemRequest.propertySearchCriteria.copy(
              roomSelectionRequest = Some(
                defaultRoomSelectionRequest(0, Some(List(1)))
              )
            )
          )
          val productRequest =
            ProductsRequest(None, Seq(requestItem), Seq.empty, None)
          val context = setupBookingContext(BookingFlow.SingleProperty)

          assertResult((InitializeBookingStatus.Ok, None)) {
            SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(
              context
            )
          }
        }
      }

      "number of extra beds changed and invalid number of rooms in PAPI response" should {
        "return ProductItemNotFound" in {
          val productItem = bookingPropertyProduct()
          val productItems =
            ProductData(
              properties = Seq(productItem),
              flights = Seq.empty,
              cars = Seq.empty,
              protections = Seq.empty,
              activities = Seq.empty,
              totalPriceDisplay = None,
              priceDisplayType = None,
              packageToken = None,
              priceChange = None,
              priceConfirmed = false
            )
          val propertyItemRequest =
            defaultPropertyItemRequest("id-1", "NotMatchRoomId")
          val requestItem = propertyItemRequest.copy(
            propertySearchCriteria = propertyItemRequest.propertySearchCriteria.copy(
              roomSelectionRequest = Some(
                defaultRoomSelectionRequest()
              )
            )
          )
          val productRequest =
            ProductsRequest(None, Seq(requestItem, requestItem), Seq.empty, None)
          val context = setupBookingContext(BookingFlow.SingleProperty)

          assertResult(
            (
              InitializeBookingStatus.ProductItemNotFound,
              Some(
                "Property Id:1 papiResponseRoomIdentifier:matchedroomidentifiers requestRoomIdentifier:NotMatchRoomId"
              )
            )
          ) {
            SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(
              context
            )
          }
        }
      }
    }

    "BookingFlow is SingleProperty and isOnlyAlternativeRoom == true is returned" should {
      "return ProductItemNotFound" in {
        val productItemOriginal = bookingPropertyProduct()
        val productItem = productItemOriginal.copy(
          papiProperties = productItemOriginal.papiProperties.map { pps =>
            pps.copy(property = pps.property.map(p => p.copy(isOnlyAlternativeRoom = Some(true))))
          }
        )
        val productItems =
          ProductData(
            properties = Seq(productItem),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("1", "matchedroomidentifiers")), Seq.empty, None)
        val context = setupBookingContext(BookingFlow.SingleProperty)

        assertResult(
          (
            InitializeBookingStatus.ProductItemNotFound,
            Some(
              "Contains property with alternative room: Property Id:1 papiResponseRoomIdentifier:matchedroomidentifiers requestRoomIdentifier:matchedroomidentifiers"
            )
          )
        ) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is SingleProperty and isOnlyAlternativeRoom == false is returned" should {
      "return Ok" in {
        val propertyOriginal = bookingPropertyProduct()
        val property = propertyOriginal.copy(
          papiProperties = propertyOriginal.papiProperties.map { pps =>
            pps.copy(property = pps.property.map(p => p.copy(isOnlyAlternativeRoom = Some(false))))
          }
        )
        val productItems =
          ProductData(
            properties = Seq(
              property
            ),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )

        val productRequest =
          ProductsRequest(
            None,
            Seq(
              defaultPropertyItemRequest("1", "matchedroomidentifiers")
                .copy(payment = Some(ProductPaymentRequest(ChargeOption.None)))
            ),
            Seq.empty,
            None
          )
        val context = setupBookingContext(BookingFlow.SingleProperty)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is SingleProperty and maximum booking amount exceeded is returned" should {
      "return MaximumBookingAmountExceeded" in {
        val productItems =
          ProductData(
            properties =
              Seq(mockPropertyProductItem().copy(papiPropertyStatus = PapiPropertyStatus.MaximumBookingAmountExceeded)),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("id-1", roomIdentifier)), Seq.empty, None)
        val context = setupBookingContext(BookingFlow.SingleProperty)

        assertResult((InitializeBookingStatus.MaximumBookingAmountExceeded, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is Property and Room with request RoomIdentifier is returned" should {
      "return Ok" in {
        val productItems = ProductData(
          properties = Seq(mockPropertyProductItem()),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          totalPriceDisplay = None,
          priceDisplayType = None,
          packageToken = None,
          priceChange = None,
          priceConfirmed = false
        )
        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("id-1", roomIdentifier)), Seq.empty, None)
        val context = setupBookingContext(BookingFlow.SingleProperty)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is Property and Room with request RoomIdentifier is returned but isPollingRequiredDueToSamePrice is true" should {
      "return ProductNotReady if isPollingRequiredDueToSamePrice is true and precheck empty " in {
        val productItems = ProductData(
          properties = Seq(mockPropertyProductItem()),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          totalPriceDisplay = None,
          priceDisplayType = None,
          packageToken = None,
          priceChange = None,
          priceConfirmed = false
        )
        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("id-1", roomIdentifier)), Seq.empty, None)
        val context = setupBookingContext(BookingFlow.SingleProperty)

        assertResult((InitializeBookingStatus.ProductNotReady, None)) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItems,
            CustomerRiskStatus.Safe,
            isPollingRequiredDueToSamePrice = true
          )(context)
        }
      }
      "return normal ok if isPollingRequiredDueToSamePrice is true but has precheck result " in {
        val productItems =
          ProductData(
            properties = Seq(mockPropertyProductItem()),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("id-1", roomIdentifier)), Seq.empty, None)
        val context = setupBookingContext(BookingFlow.SingleProperty)
        val precheckResult =
          Seq(AllotmentPreCheckStatus(productKey = "1", roomIdentifier = None, requestId = None, status = 2))

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItems,
            CustomerRiskStatus.Safe,
            isPollingRequiredDueToSamePrice = true,
            allotmentPreCheckResults = precheckResult
          )(context)
        }
      }
    }

    "BookingFlow is MultiHotel and 2nd Room return allotmentReject with request RoomIdentifier is returned" should {
      "return Allotment Not Available with message" in {
        val productItems =
          ProductData(
            properties = Seq(mockPropertyProductItem()),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("i", roomIdentifier)), Seq.empty, None)
        val context = setupBookingContext(BookingFlow.SingleProperty)

        val allotmentStatus = Seq(
          AllotmentPreCheckStatus("1", None, None, AllotmentStatus.Available),
          AllotmentPreCheckStatus("2", None, None, AllotmentStatus.NotAvailable)
        )
        assertResult((InitializeBookingStatus.AllotmentNotAvailable, Some("Allotment Not Available for ProductId 2"))) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItems,
            CustomerRiskStatus.Safe,
            allotmentStatus
          )(context)
        }
      }
    }
    "BookingFlow is MultiHotel and Both Room return allotmentReject with request RoomIdentifier is returned" should {
      "return Allotment Not Available with message" in {
        val productItems =
          ProductData(
            properties = Seq(mockPropertyProductItem()),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("i", roomIdentifier)), Seq.empty, None)
        val context = setupBookingContext(BookingFlow.SingleProperty)

        val allotmentStatus = Seq(
          AllotmentPreCheckStatus("1", None, None, AllotmentStatus.NotAvailable),
          AllotmentPreCheckStatus("2", None, None, AllotmentStatus.NotAvailable)
        )
        assertResult(
          (
            InitializeBookingStatus.AllotmentNotAvailable,
            Some("Allotment Not Available for ProductId 1, Allotment Not Available for ProductId 2")
          )
        ) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItems,
            CustomerRiskStatus.Safe,
            allotmentStatus
          )(context)
        }
      }
    }

    "BookingFlow is MultiHotel and 1st Property returned no MasterRoom and second returned different Room" should {
      "return technical message with ProductItemNotFound" in {
        val noMasterRoomProperty0 = bookingPropertyProduct()
          .modify(_.papiProperties.each.property.each.masterRooms)
          .setTo(Nil)
          .modify(_.id)
          .setTo("0")
        val propertyRoom1 = bookingPropertyProduct.modify(_.id).setTo("1")
        val productItems =
          ProductData(
            properties = Seq(noMasterRoomProperty0, propertyRoom1),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val productRequest =
          ProductsRequest(
            None,
            Seq(
              defaultPropertyItemRequest("0", roomIdentifier),
              defaultPropertyItemRequest("1", s"$roomIdentifier second")
            ),
            Seq.empty,
            None
          )
        val context = setupBookingContext(BookingFlow.SingleProperty)

        assertResult(
          (
            InitializeBookingStatus.ProductItemNotFound,
            Some(
              "Property Id:0 papiResponseRoomIdentifier:[Not Found] requestRoomIdentifier:roomIdentifier, " +
                "Property Id:1 papiResponseRoomIdentifier:matchedroomidentifiers requestRoomIdentifier:roomIdentifier second"
            )
          )
        ) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItems,
            CustomerRiskStatus.Safe
          )(context)
        }
      }
    }

    "Campaign is requested and promo is applied for Bessie CitiWhitelabel" should {
      val bookingItemMock = createMockEnrichedBookingItem()
      val hotelMock       = bookingItemMock.booking.head.hotel.head
      val room1           = hotelMock.room.head
      when(room1.uid).thenReturn(childrenRoomWithLoyaltyResponse.uid.get)
      val booking1 = bookingItemMock.booking.head
      when(hotelMock.room).thenReturn(List(room1))
      when(booking1.hotel).thenReturn(List(hotelMock))
      when(booking1.discount)
        .thenReturn(Some(EnrichedCampaignDiscount(1.1, CampaignDiscountTypes.Amount, 11, "promo-code", "promoText")))
      when(bookingItemMock.booking).thenReturn(List(booking1))
      val propertyMock = baseProperty()
        .copy(
          masterRooms = Seq(
            masterRoomWithChildRoomWithLoyaltyResponse.copy(
              childrenRooms =
                List(baseChildrenRoom(), childrenRoomWithLoyaltyResponse.copy(roomIdentifiers = Some(roomIdentifier)))
            )
          ),
          rooms = Seq(baseChildrenRoom(), childrenRoomWithLoyaltyResponse.copy(roomIdentifiers = Some(roomIdentifier))),
          booking = Some(bookingItemMock)
        )
      val property = mockPropertyProductItem(dfFeatureFlag = Some(PropertyFeatureFlags.BreakfastUpSell.id))
        .copy(papiProperties = Some(transformers.Properties(property = Seq(propertyMock), debug = None)))

      val productItems =
        ProductData(
          properties = Seq(property),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          totalPriceDisplay = None,
          priceDisplayType = None,
          packageToken = None,
          priceChange = None,
          priceConfirmed = false
        )

      val productItemsWithPriceConfirmed =
        ProductData(
          properties = Seq(property),
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          totalPriceDisplay = None,
          priceDisplayType = None,
          packageToken = None,
          priceChange = None,
          priceConfirmed = true
        )

      val pricingRequest = Some(
        PricingRequest(
          isMse = false,
          requiredPrice = "",
          requiredBasis = "",
          isRPM2Included = false,
          selectedPointMaxId = None,
          isIncludeUsdAndLocalCurrency = false,
          allowOverrideOccupancy = false,
          enableOpaqueChannel = false,
          isAllowRoomTypeNotGuarantee = false,
          synchronous = true,
          partnerLoyaltyProgramId = Some(0),
          dfFeatureFlags = Seq(PropertyFeatureFlags.BreakfastUpSell.id)
        )
      )

      val searchRequestWithCorrectGuestName = defaultPropertyItemRequest("id-1", roomIdentifier, pricingRequest)
        .copy(guests = Some(Seq(HotelGuest(firstname = "firstname", lastname = "lastname"))))

      val correctMemberInfo = mock[MemberDetails]
      when(correctMemberInfo.firstName).thenReturn("firstname")
      when(correctMemberInfo.lastName).thenReturn("lastname")

      "return BessieCitiWhitelabelInvalidGuestName when guest and cardholder does not match" in {
        val productRequest =
          ProductsRequest(None, Seq(searchRequestWithCorrectGuestName), Seq.empty, None)
        val featureAware = mock[FeatureAware]
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(requestContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.CitiUS, FeaturesConfiguration()))

        val context                   = setupBookingContext(BookingFlow.SingleProperty).copy(requestContext = requestContext)
        val memberInfo: MemberDetails = mock[MemberDetails]
        when(memberInfo.firstName).thenReturn("firstname")
        when(memberInfo.lastName).thenReturn("wronglastname")

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItems,
            CustomerRiskStatus.Safe,
            isCampaignRequested = true,
            memberInfo = Some(memberInfo)
          )(context)
        }
      }

      "return BessieCitiWhitelabelInvalidGuestName when not all primary guest matches cardholder" in {
        val searchRequestIncorrectGuestNames =
          defaultPropertyItemRequest("id-1", roomIdentifier).copy(guests =
            Some(Seq(HotelGuest(firstname = "firstnameWrong", lastname = "lastname")))
          )
        val productRequest =
          ProductsRequest(
            None,
            Seq(searchRequestWithCorrectGuestName, searchRequestIncorrectGuestNames),
            Seq.empty,
            None
          )
        val featureAware = mock[FeatureAware]
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(requestContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.CitiUS, FeaturesConfiguration()))

        val context = setupBookingContext(BookingFlow.Cart).copy(requestContext = requestContext)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItemsWithPriceConfirmed,
            CustomerRiskStatus.Safe,
            isCampaignRequested = true,
            memberInfo = Some(correctMemberInfo)
          )(context)
        }
      }

      "return BessieCitiWhitelabelInvalidGuestName when cardholder name is missing" in {
        val productRequest =
          ProductsRequest(None, Seq(searchRequestWithCorrectGuestName), Seq.empty, None)
        val featureAware = mock[FeatureAware]
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(requestContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.CitiUS, FeaturesConfiguration()))

        val context = setupBookingContext(BookingFlow.SingleProperty).copy(requestContext = requestContext)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItems,
            CustomerRiskStatus.Safe,
            isCampaignRequested = true,
            memberInfo = None
          )(context)
        }
      }

      "return ok when guest and cardholder match" in {
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val productRequest =
          ProductsRequest(None, Seq(searchRequestWithCorrectGuestName), Seq.empty, None)
        val featureAware = mock[FeatureAware]
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(requestContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.CitiUS, FeaturesConfiguration()))

        val context = setupBookingContext(BookingFlow.SingleProperty).copy(requestContext = requestContext)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItems,
            CustomerRiskStatus.Safe,
            isCampaignRequested = true,
            memberInfo = Some(correctMemberInfo)
          )(context)
        }
      }

      "return ok when all primary guest names matches cardholder" in {
        val productRequest =
          ProductsRequest(
            None,
            Seq(searchRequestWithCorrectGuestName, searchRequestWithCorrectGuestName),
            Seq.empty,
            None
          )
        val featureAware = mock[FeatureAware]
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(requestContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.CitiUS, FeaturesConfiguration()))

        val context = setupBookingContext(BookingFlow.Cart).copy(requestContext = requestContext)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItemsWithPriceConfirmed,
            CustomerRiskStatus.Safe,
            isCampaignRequested = true,
            memberInfo = Some(correctMemberInfo)
          )(context)
        }
      }

      "return ok when case mismatch or contain spaces" in {
        val productRequest =
          ProductsRequest(None, Seq(searchRequestWithCorrectGuestName), Seq.empty, None)
        val featureAware = mock[FeatureAware]
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(requestContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.CitiUS, FeaturesConfiguration()))

        val context                   = setupBookingContext(BookingFlow.SingleProperty).copy(requestContext = requestContext)
        val memberInfo: MemberDetails = mock[MemberDetails]
        when(memberInfo.firstName).thenReturn(" firstname")
        when(memberInfo.lastName).thenReturn("Lastname ")

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItems,
            CustomerRiskStatus.Safe,
            isCampaignRequested = true,
            memberInfo = Some(memberInfo)
          )(context)
        }
      }

      "return ok when no guest info" in {
        val searchRequest = defaultPropertyItemRequest("id-1", roomIdentifier)
        val productRequest =
          ProductsRequest(None, Seq(searchRequest), Seq.empty, None)
        val featureAware = mock[FeatureAware]
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(requestContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.CitiUS, FeaturesConfiguration()))

        val context = setupBookingContext(BookingFlow.SingleProperty).copy(requestContext = requestContext)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItems,
            CustomerRiskStatus.Safe,
            isCampaignRequested = true,
            memberInfo = Some(correctMemberInfo)
          )(context)
        }
      }
    }

    "BookingFlow is SingleProperty and Campaign is included but DF failed to apply" should {
      "return CampaignNotApplied" in {
        val property = mockPropertyProductItem()
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("id-1", roomIdentifier)), Seq.empty, None)
        val context = setupBookingContext(BookingFlow.SingleProperty)

        assertResult((InitializeBookingStatus.CampaignNotApplied, None)) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItems,
            CustomerRiskStatus.Safe,
            isCampaignRequested = true
          )(context)
        }
      }

      "return CampaignNotApplied with alternative rooms" in {
        val searchRequest   = defaultPropertyItemRequest("id-1", roomIdentifier)
        val bookingItemMock = createMockEnrichedBookingItem()
        val hotelMock       = bookingItemMock.booking.head.hotel.head
        val room1           = hotelMock.room.head
        when(room1.uid).thenReturn(childrenRoomWithLoyaltyResponse.uid.get)
        val booking1 = bookingItemMock.booking.head
        when(hotelMock.room).thenReturn(List(room1))
        when(booking1.hotel).thenReturn(List(hotelMock))
        // for debugging: uncomment for no validation error
        // when(booking1.discount)
        //  .thenReturn(Some(EnrichedCampaignDiscount(1.1, CampaignDiscountTypes.Amount, 11, "promo-code", "promoText")))
        when(booking1.discount).thenReturn(None)
        when(bookingItemMock.booking).thenReturn(List(booking1))
        val propertyMock = baseProperty()
          .copy(
            masterRooms = Seq(
              masterRoomWithChildRoomWithLoyaltyResponse.copy(
                childrenRooms =
                  List(baseChildrenRoom(), childrenRoomWithLoyaltyResponse.copy(roomIdentifiers = Some(roomIdentifier)))
              )
            ),
            rooms =
              Seq(baseChildrenRoom(), childrenRoomWithLoyaltyResponse.copy(roomIdentifiers = Some(roomIdentifier))),
            booking = Some(bookingItemMock)
          )
        val property = mockPropertyProductItem(dfFeatureFlag = Some(PropertyFeatureFlags.BreakfastUpSell.id))
          .copy(papiProperties = Some(transformers.Properties(property = Seq(propertyMock), debug = None)))
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val productRequest =
          ProductsRequest(None, Seq(searchRequest), Seq.empty, None)
        val featureAware = mock[FeatureAware]
        when(requestContext.featureAware).thenReturn(Some(featureAware))

        val context = setupBookingContext(BookingFlow.SingleProperty).copy(requestContext = requestContext)

        assertResult((InitializeBookingStatus.CampaignNotApplied, None)) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItems,
            CustomerRiskStatus.Safe,
            isCampaignRequested = true
          )(context)
        }
      }
    }

    "BookingFlow is SingleProperty and paymentRequest is None" should {
      "return Ok" in {
        val property = mockPropertyProductItem()
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )

        val productRequest =
          ProductsRequest(
            None,
            Seq(
              defaultPropertyItemRequest("1", roomIdentifier)
                .copy(payment = Some(ProductPaymentRequest(ChargeOption.None)))
            ),
            Seq.empty,
            None
          )
        val context = setupBookingContext(BookingFlow.SingleProperty)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is SingleProperty and paymentRequest is payNow with property that is payNow" should {
      "return Ok" in {
        val property = mockPropertyProductItem()
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )

        val productRequest =
          ProductsRequest(
            None,
            Seq(
              defaultPropertyItemRequest("1", roomIdentifier)
                .copy(payment = Some(ProductPaymentRequest(ChargeOption.PayNow)))
            ),
            Seq.empty,
            None
          )
        val context = setupBookingContext(BookingFlow.SingleProperty)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is SingleProperty and paymentRequest is payLater with property that is payLater" should {
      "return Ok" in {
        val property = mockPropertyProductItem(paymentModels = PaymentModels.Merchant, isPayLater = true)
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )

        val productRequest =
          ProductsRequest(
            None,
            Seq(
              defaultPropertyItemRequest("1", roomIdentifier)
                .copy(payment = Some(ProductPaymentRequest(ChargeOption.PayNow)))
            ),
            Seq.empty,
            None
          )
        val context = setupBookingContext(BookingFlow.SingleProperty)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is SingleProperty and paymentRequest is payLater" should {
      "for property that not support payLater return ChargeOptionNotApplied" in {
        val property = mockPropertyProductItem(paymentModels = PaymentModels.Merchant, isPayLater = false)
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )

        val productRequest =
          ProductsRequest(
            None,
            Seq(
              defaultPropertyItemRequest("1", roomIdentifier)
                .copy(payment = Some(ProductPaymentRequest(ChargeOption.PayLater)))
            ),
            Seq.empty,
            None
          )
        val context = setupBookingContext(BookingFlow.SingleProperty)

        assertResult((InitializeBookingStatus.ChargeOptionNotApplied, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }

      "for property with alternative rooms that not support payLater return ChargeOptionNotApplied" in {
        val featureAwareMock = mock[FeatureAware]
        when(requestContext.featureAware).thenReturn(Some(featureAwareMock))
        val pricingRequest = Some(
          PricingRequest(
            isMse = false,
            requiredPrice = "",
            requiredBasis = "",
            isRPM2Included = false,
            selectedPointMaxId = None,
            isIncludeUsdAndLocalCurrency = false,
            allowOverrideOccupancy = false,
            enableOpaqueChannel = false,
            isAllowRoomTypeNotGuarantee = false,
            synchronous = true,
            partnerLoyaltyProgramId = Some(0),
            dfFeatureFlags = Seq(PropertyFeatureFlags.BreakfastUpSell.id)
          )
        )
        val propertyItemRequest =
          defaultPropertyItemRequest("1", "matchedroomidentifiers", pricingRequest)
            .copy(payment = Some(ProductPaymentRequest(ChargeOption.PayLater)))
        val propertySearchCriteria = propertyItemRequest.propertySearchCriteria.copy(
          roomSelectionRequest = Some(defaultRoomSelectionRequest(1, Some(List(1)))),
          simplifiedRoomSelectionRequest = Some(
            SimplifiedRoomSelectionRequest(
              "matchedroomidentifiers",
              Some(1),
              requestExtraBedForRoomNumbers = Some(List(1))
            )
          )
        )
        val requestItem = propertyItemRequest.copy(
          propertySearchCriteria = propertySearchCriteria
        )
        val productRequest =
          ProductsRequest(None, Seq(requestItem), Seq.empty, None)

        val context     = setupBookingContext(BookingFlow.SingleProperty).copy(requestContext = requestContext)
        val productItem = bookingPropertyProduct().copy(propertySearchCriteria = Some(propertySearchCriteria))
        val productItems =
          ProductData(
            properties = Seq(productItem, productItem.copy(id = "2")),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        assertResult((InitializeBookingStatus.ChargeOptionNotApplied, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }

      "for property with alternative rooms cross sell case return Ok" in {

        val featureAwareMock = mock[FeatureAware]
        when(requestContext.featureAware).thenReturn(Some(featureAwareMock))
        val pricingRequest = Some(
          PricingRequest(
            isMse = false,
            requiredPrice = "",
            requiredBasis = "",
            isRPM2Included = false,
            selectedPointMaxId = None,
            isIncludeUsdAndLocalCurrency = false,
            allowOverrideOccupancy = false,
            enableOpaqueChannel = false,
            isAllowRoomTypeNotGuarantee = false,
            synchronous = true,
            partnerLoyaltyProgramId = Some(0),
            dfFeatureFlags = Seq(PropertyFeatureFlags.BreakfastUpSell.id)
          )
        )

        val propertyItemRequest =
          defaultPropertyItemRequest("1", "roomiden-original", pricingRequest)
            .copy(payment = Some(ProductPaymentRequest(ChargeOption.PayLater)))
        val propertySearchCriteria = propertyItemRequest.propertySearchCriteria.copy(
          roomSelectionRequest = Some(defaultRoomSelectionRequest(1, Some(List(1)))),
          simplifiedRoomSelectionRequest = Some(
            SimplifiedRoomSelectionRequest(
              roomIdentifier = "roomiden-original",
              requestedRoomNumbers = Some(1),
              alternativeOptIn = Some(
                AlternativeRoomOptIn(crossSellOptIn =
                  Some(CrossSellOptIn(reason = Some(CrossSellReasonType.preAuth.id)))
                )
              )
            )
          )
        )
        val requestItem = propertyItemRequest.copy(
          propertySearchCriteria = propertySearchCriteria
        )
        val productRequest =
          ProductsRequest(None, Seq(requestItem), Seq.empty, None)

        val context = setupBookingContext(BookingFlow.SingleProperty).copy(requestContext = requestContext)
        val productItem = bookingPropertyProduct().copy(
          propertySearchCriteria = Some(propertySearchCriteria),
          papiProperties = Some(transformers.Properties(property = Seq(propertyWithAlternatives), debug = None))
        )

        val productItems =
          ProductData(
            properties = Seq(productItem, productItem.copy(id = "2")),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is SingleProperty and loyaltyResponse is not success" should {
      "return InvalidGiftCardRedeem" in {
        val property = mockPropertyProductItem(
          loyaltyResponse =
            Some(LoyaltyPaymentBoundaries(0, 100, 99, Some(0.5), Some(false), LoyaltyReasons.GatewayLimit))
        )
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )

        val productRequest =
          ProductsRequest(
            None,
            Seq(
              defaultPropertyItemRequest("1", roomIdentifier)
                .copy(payment = Some(ProductPaymentRequest(ChargeOption.PayNow)))
            ),
            Seq.empty,
            None
          )
        val context = setupBookingContext(BookingFlow.SingleProperty)

        assertResult((InitializeBookingStatus.InvalidGiftCardRedeem, None)) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            Some(RedeemRequest(99.5, Some(0))),
            productItems,
            CustomerRiskStatus.Safe
          )(context)
        }
      }
    }

    "BookingFlow is SingleProperty and cashback redemption is not success" should {
      "return InvalidCashbackRedeem" in {
        val property = mockPropertyProductItem(
          cashbackRedemptionBoundaries =
            Some(LoyaltyPaymentBoundaries(0, 100, 99, Some(0.5), Some(false), LoyaltyReasons.GatewayLimit))
        )
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )

        val productRequest =
          ProductsRequest(
            None,
            Seq(
              defaultPropertyItemRequest("1", roomIdentifier)
                .copy(payment = Some(ProductPaymentRequest(ChargeOption.PayNow)))
            ),
            Seq.empty,
            None
          )
        val context = setupBookingContext(BookingFlow.SingleProperty)

        assertResult((InitializeBookingStatus.InvalidCashbackRedeem, None)) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            Some(RedeemRequest(0, Some(10))),
            productItems,
            CustomerRiskStatus.Safe
          )(context)
        }
      }
    }

    "BookingFlow is SingleProperty and loyaltyResponse is None" should {
      "return Ok" in {
        val property = mockPropertyProductItem(loyaltyResponse = None)
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )

        val productRequest =
          ProductsRequest(
            None,
            Seq(
              defaultPropertyItemRequest("1", roomIdentifier)
                .copy(payment = Some(ProductPaymentRequest(ChargeOption.PayNow)))
            ),
            Seq.empty,
            None
          )
        val context = setupBookingContext(BookingFlow.SingleProperty)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is SingleProperty and allotmentPrecheckResult is PriceChanged" should {
      "return ProductNotReady when PriceChangePolling is enabled" in {
        val property = mockPropertyProductItem(loyaltyResponse = None)
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val allotmentPreCheckResults = Seq(AllotmentPreCheckStatus("1", None, Some("1"), AllotmentStatus.PriceChanged))

        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("1", roomIdentifier)), Seq.empty, None)

        val featureAware = mock[FeatureAware]
        when(featureAware.isPriceChangePollingEnabled) thenReturn true
        val requestCxt = requestContext(mock[MessagesBag]).copy(featureAware = Some(featureAware))
        val context    = setupBookingContext(BookingFlow.SingleProperty).copy(requestContext = requestCxt)

        assertResult((InitializeBookingStatus.ProductNotReady, Some("Allotment Price Changed for ProductId 1"))) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItems,
            CustomerRiskStatus.Safe,
            allotmentPreCheckResults
          )(context)
        }
      }

      "return AllotmentNotAvailable when PriceChangePolling is not enabled" in {
        val property = mockPropertyProductItem(loyaltyResponse = None)
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val allotmentPreCheckResults = Seq(AllotmentPreCheckStatus("1", None, Some("1"), AllotmentStatus.PriceChanged))
        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("1", roomIdentifier)), Seq.empty, None)

        val context = setupBookingContext(BookingFlow.SingleProperty)

        assertResult((InitializeBookingStatus.ProductNotReady, Some("Allotment Price Changed for ProductId 1"))) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItems,
            CustomerRiskStatus.Safe,
            allotmentPreCheckResults
          )(context)
        }
      }
    }

    "BookingFlow is SingleProperty and request is External Partner request" should {
      "return ExternalPartnerSuspended when property is blocked and externalPartnerId is valid" in {
        val productItem = bookingPropertyProduct()
          .copy(papiProperties =
            Some(
              transformers.Properties(
                property = Seq.empty,
                debug = None,
                dfMetaResult = DFMetaResult(None, Some(true))
              )
            )
          )
        val productItems =
          ProductData(
            properties = Seq(productItem),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )

        val productRequest =
          ProductsRequest(
            None,
            Seq(
              defaultPropertyItemRequest("1", "matchedroomidentifiers")
                .copy(payment = Some(ProductPaymentRequest(ChargeOption.PayNow)))
            ),
            Seq.empty,
            None
          )

        val context      = setupBookingContext(BookingFlow.SingleProperty)
        val featureAware = mock[FeatureAware]
        val requestContext = context.requestContext.copy(
          featureAware = Some(featureAware),
          userContext = Some(
            userContext.copy(experimentData =
              Some(
                ExperimentData(
                  "1",
                  "1",
                  Some("memberId"),
                  Some("trafficGroup"),
                  Some("cId"),
                  Some("aId"),
                  Some("serverName"),
                  externalPartnerId = Some("ExternalPartnerId")
                )
              )
            )
          )
        )
        val actualContext = context.copy(requestContext = requestContext)

        assertResult(
          (InitializeBookingStatus.ExternalPartnerSuspended, Some("External Partner is suspended for this booking"))
        ) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(
            actualContext
          )
        }
      }

      "return ProductItemNotFound when property is blocked and externalPartnerId is empty" in {
        val productItem = bookingPropertyProduct()
          .copy(papiProperties =
            Some(
              transformers.Properties(
                property = Seq.empty,
                debug = None,
                dfMetaResult = DFMetaResult(None, Some(true))
              )
            )
          )
        val productItems =
          ProductData(
            properties = Seq(productItem),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )

        val productRequest =
          ProductsRequest(
            None,
            Seq(
              defaultPropertyItemRequest("1", "matchedroomidentifiers")
                .copy(payment = Some(ProductPaymentRequest(ChargeOption.PayNow)))
            ),
            Seq.empty,
            None
          )

        val context      = setupBookingContext(BookingFlow.SingleProperty)
        val featureAware = mock[FeatureAware]
        val requestContext = context.requestContext.copy(
          featureAware = Some(featureAware)
        )
        val actualContext = context.copy(requestContext = requestContext)

        val (actual, _) =
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(
            actualContext
          )
        assertResult(InitializeBookingStatus.ProductItemNotFound)(actual)
      }
    }

    "BookingFlow is SingleProperty and priceAdjustmentId is valid" should {
      def createCwsProductData(productItem: BookingPropertiesData) = ProductData(
        properties = Seq(productItem),
        flights = Seq.empty,
        cars = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        totalPriceDisplay = None,
        priceDisplayType = None,
        packageToken = None,
        priceChange = None,
        priceConfirmed = false
      )
      def createCwsProductRequest(propertyRequestItem: PropertyRequestItem, priceAdjustmentId: Option[Long]) =
        ProductsRequest(
          None,
          Seq(
            propertyRequestItem
              .copy(
                propertySearchCriteria =
                  propertyRequestItem.propertySearchCriteria.copy(priceAdjustmentId = priceAdjustmentId),
                payment = Some(ProductPaymentRequest(ChargeOption.PayNow))
              )
          ),
          Seq.empty,
          None
        )
      def createCwsRequestContext(): SetupBookingContext = {
        val context      = setupBookingContext(BookingFlow.SingleProperty)
        val featureAware = mock[FeatureAware]
        val requestContext = context.requestContext.copy(
          featureAware = Some(featureAware),
          userContext = Some(
            userContext.copy(experimentData =
              Some(
                ExperimentData(
                  "1",
                  "1",
                  Some("memberId"),
                  Some("trafficGroup"),
                  Some("cId"),
                  Some("aId"),
                  Some("serverName"),
                  externalPartnerId = Some("ExternalPartnerId")
                )
              )
            )
          )
        )
        context.copy(requestContext = requestContext)
      }

      "return Ok when priceAdjustmentId matched and externalPartnerId not exist" in {
        val priceAdjustmentId   = 10001L
        val productItem         = bookingPropertyProductForCWS(Some(priceAdjustmentId))
        val productItems        = createCwsProductData(productItem)
        val propertyRequestItem = defaultPropertyItemRequest("1", "matchedroomidentifiers")
        val productRequest      = createCwsProductRequest(propertyRequestItem, Some(priceAdjustmentId))

        val context      = setupBookingContext(BookingFlow.SingleProperty)
        val featureAware = mock[FeatureAware]
        val requestContext = context.requestContext.copy(
          featureAware = Some(featureAware)
        )
        val actualContext = context.copy(requestContext = requestContext)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(
            actualContext
          )
        }
      }

      "return Ok when priceAdjustmentId matched and externalPartnerId exist" in {
        val priceAdjustmentId   = 10001L
        val productItem         = bookingPropertyProductForCWS(Some(priceAdjustmentId))
        val productItems        = createCwsProductData(productItem)
        val propertyRequestItem = defaultPropertyItemRequest("1", "matchedroomidentifiers")
        val productRequest      = createCwsProductRequest(propertyRequestItem, Some(priceAdjustmentId))
        val context             = createCwsRequestContext()

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }

      "return Ok when priceAdjustmentId not match and externalPartnerId not exist" in {
        val productItem         = bookingPropertyProductForCWS(Some(10001L))
        val productItems        = createCwsProductData(productItem)
        val propertyRequestItem = defaultPropertyItemRequest("1", "matchedroomidentifiers")
        val productRequest      = createCwsProductRequest(propertyRequestItem, Some(10002L))

        val context      = setupBookingContext(BookingFlow.SingleProperty)
        val featureAware = mock[FeatureAware]
        val requestContext = context.requestContext.copy(
          featureAware = Some(featureAware)
        )
        val actualContext = context.copy(requestContext = requestContext)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(
            actualContext
          )
        }
      }

      "return Ok when request priceAdjustmentId and product priceAdjustmentId is empty" in {
        val productItem         = bookingPropertyProductForCWS(None)
        val productItems        = createCwsProductData(productItem)
        val propertyRequestItem = defaultPropertyItemRequest("1", "matchedroomidentifiers")
        val productRequest      = createCwsProductRequest(propertyRequestItem, None)
        val context             = createCwsRequestContext()

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }

      "return Ok when request priceAdjustmentId is default and product priceAdjustmentId is empty" in {
        val productItem         = bookingPropertyProductForCWS(None)
        val productItems        = createCwsProductData(productItem)
        val propertyRequestItem = defaultPropertyItemRequest("1", "matchedroomidentifiers")
        val productRequest      = createCwsProductRequest(propertyRequestItem, Some(0L))
        val context             = createCwsRequestContext()

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }

      "return Ok when request priceAdjustmentId is empty and product priceAdjustmentId is default" in {
        val productItem         = bookingPropertyProductForCWS(Some(0L))
        val productItems        = createCwsProductData(productItem)
        val propertyRequestItem = defaultPropertyItemRequest("1", "matchedroomidentifiers")
        val productRequest      = createCwsProductRequest(propertyRequestItem, None)
        val context             = createCwsRequestContext()

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }

      "return Ok when request priceAdjustmentId and product priceAdjustmentId is default" in {
        val productItem         = bookingPropertyProductForCWS(Some(0L))
        val productItems        = createCwsProductData(productItem)
        val propertyRequestItem = defaultPropertyItemRequest("1", "matchedroomidentifiers")
        val productRequest      = createCwsProductRequest(propertyRequestItem, Some(0L))
        val context             = createCwsRequestContext()

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }

      "return PriceAdjustmentIdMismatch when priceAdjustmentId not match and externalPartnerId exist" in {
        val productItem         = bookingPropertyProductForCWS(Some(10001L))
        val productItems        = createCwsProductData(productItem)
        val propertyRequestItem = defaultPropertyItemRequest("1", "matchedroomidentifiers")
        val productRequest      = createCwsProductRequest(propertyRequestItem, Some(10002L))
        val context             = createCwsRequestContext()

        assertResult((InitializeBookingStatus.PriceAdjustmentIdMismatch, Some("PriceAdjustmentId mismatch"))) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(
            context
          )
        }
      }

      "return PriceAdjustmentIdMismatch when priceAdjustmentId not match on default and externalPartnerId exist" in {
        val productItem         = bookingPropertyProductForCWS(Some(0L))
        val productItems        = createCwsProductData(productItem)
        val propertyRequestItem = defaultPropertyItemRequest("1", "matchedroomidentifiers")
        val productRequest      = createCwsProductRequest(propertyRequestItem, Some(10001L))
        val context             = createCwsRequestContext()

        assertResult((InitializeBookingStatus.PriceAdjustmentIdMismatch, Some("PriceAdjustmentId mismatch"))) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(
            context
          )
        }
      }

      "return PriceAdjustmentIdMismatch when priceAdjustmentId not match on empty and externalPartnerId exist" in {
        val productItem         = bookingPropertyProductForCWS(None)
        val productItems        = createCwsProductData(productItem)
        val propertyRequestItem = defaultPropertyItemRequest("1", "matchedroomidentifiers")
        val productRequest      = createCwsProductRequest(propertyRequestItem, Some(10001L))
        val context             = createCwsRequestContext()

        assertResult((InitializeBookingStatus.PriceAdjustmentIdMismatch, Some("PriceAdjustmentId mismatch"))) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(
            context
          )
        }
      }
    }
  }

  "SetupBookingStatusProvider" when {
    "BookingFlow is Flight and no flight return" should {
      "return ProductNotFound" in {
        val productItems = ProductData(
          properties = Seq.empty,
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          totalPriceDisplay = None,
          priceDisplayType = None,
          packageToken = None,
          priceChange = None,
          priceConfirmed = false
        )
        val productRequest = ProductsRequest(None, Seq.empty, Seq.empty, None)
        val context        = setupBookingContext(BookingFlow.SingleFlight)

        assertResult((InitializeBookingStatus.ProductNotFound, Some("No flights in ProductData"))) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is Flight and PriceConfirm is complete but no flight with itineraryId" should {
      "return ProductNotFound" in {
        val notFoundFlight = mockFlightProductItem(true, false, false)
        val productItems =
          ProductData(
            properties = Seq.empty,
            flights = Seq(notFoundFlight),
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val productRequest = ProductsRequest(None, Seq.empty, Seq.empty, None)
        val context        = setupBookingContext(BookingFlow.SingleFlight)

        assertResult((InitializeBookingStatus.ProductNotFound, Some("Does not have requested flight"))) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is Flight and PriceConfirm is not complete" should {
      "return ProductNotReady" in {
        val notReadyFlight = mockFlightProductItem(false, true, true)
        val productItems =
          ProductData(
            properties = Seq.empty,
            flights = Seq(notReadyFlight),
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val productRequest = ProductsRequest(None, Seq.empty, Seq.empty, None)
        val context        = setupBookingContext(BookingFlow.SingleFlight)

        assertResult((InitializeBookingStatus.ProductNotReady, Some("Flight Request is not completed"))) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is Flight and PriceConfirm is complete and there is flight with itineraryId" should {
      "return Ok" in {
        val readyAndFoundFlight = mockFlightProductItem(true, true, true)
        val productItems =
          ProductData(
            properties = Seq.empty,
            flights = Seq(readyAndFoundFlight),
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )

        val flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None))
        val productRequest = ProductsRequest(None, Seq.empty, flightRequests, None)
        val context        = setupBookingContext(BookingFlow.SingleFlight)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is Package and PriceConfirm is not complete" should {
      "return ProductNotReady" in {
        val flight = mockFlightProductItem(false, true, true)
        val productItems =
          ProductData(
            properties = Seq.empty,
            flights = Seq(flight),
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )

        val productRequest = ProductsRequest(None, Seq.empty, Seq.empty, None)
        val context        = setupBookingContext(BookingFlow.Package)

        assertResult((InitializeBookingStatus.ProductNotReady, Some("Flight Request is not completed"))) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is Package and PriceConfirm but there is no flight with itineraryId" should {
      "return ProductNotFound" in {
        val flight = mockFlightProductItem(true, false, false)
        val productItems = ProductData(
          properties = Seq.empty,
          flights = Seq(flight),
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          totalPriceDisplay = None,
          priceDisplayType = None,
          packageToken = None,
          priceChange = None,
          priceConfirmed = false
        )
        val productRequest = ProductsRequest(None, Seq.empty, Seq.empty, None)
        val context        = setupBookingContext(BookingFlow.Package)

        assertResult((InitializeBookingStatus.ProductNotFound, Some("Does not have requested flight"))) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is Package and PriceConfirm is complete but no hotel return" should {
      "return ProductNotFound" in {
        val flight = mockFlightProductItem(true, true, true)
        val productItems = ProductData(
          properties = Seq.empty,
          flights = Seq(flight),
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          totalPriceDisplay = None,
          priceDisplayType = None,
          packageToken = None,
          priceChange = None,
          priceConfirmed = false
        )
        val flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None))
        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("id-1", "NotMatchRoomId")), flightRequests, None)

        val context = setupBookingContext(BookingFlow.Package).copy(requestContext = requestContext)

        assertResult((InitializeBookingStatus.ProductNotFound, Some("No properties found"))) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is Package and PriceConfirm is complete but no room with request RoomIdentifier is returned" should {
      "return ProductItemNotFound" in {
        val flight   = mockFlightProductItem(true, true, true)
        val property = bookingPropertyProduct()
        val priceBreakDownNode = Some(
          PriceBreakdownNode(
            value = Option(
              PriceBreakdownResponse(
                `type` = PriceBreakdownType.TotalPrice,
                amount = Money(amount = 2000d, currencyCode = "THB")
              )
            )
          )
        )
        val flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None))
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq(flight),
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = priceBreakDownNode,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("id-1", "NotMatchRoomId")), flightRequests, None)

        val context = setupBookingContext(BookingFlow.Package).copy(requestContext = requestContext)

        assertResult(
          (
            InitializeBookingStatus.ProductItemNotFound,
            Some(
              "Property Id:1 papiResponseRoomIdentifier:matchedroomidentifiers requestRoomIdentifier:NotMatchRoomId"
            )
          )
        ) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is Package and PriceConfirm is complete and room with request RoomIdentifier is returned" should {
      "return Ok" in {
        val flight   = mockFlightProductItem(true, true, true)
        val property = mockPropertyProductItem()
        val priceBreakDownNode = Some(
          PriceBreakdownNode(
            value = Option(
              PriceBreakdownResponse(
                `type` = PriceBreakdownType.TotalPrice,
                amount = Money(amount = 2000d, currencyCode = "THB")
              )
            )
          )
        )
        val flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None))
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq(flight),
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = priceBreakDownNode,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )

        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("id-1", roomIdentifier)), flightRequests, None)
        val context = setupBookingContext(BookingFlow.Package)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is Package and everything is complete but property loyalty response is not success" should {
      "return InvalidGiftCardRedeem" in {
        val flight = mockFlightProductItem(true, true, true)
        val property = mockPropertyProductItem(
          loyaltyResponse =
            Some(LoyaltyPaymentBoundaries(0, 100, 99, Some(0.5), Some(false), LoyaltyReasons.InsufficientBalance))
        )
        val priceBreakDownNode = Some(
          PriceBreakdownNode(
            value = Option(
              PriceBreakdownResponse(
                `type` = PriceBreakdownType.TotalPrice,
                amount = Money(amount = 2000d, currencyCode = "THB")
              )
            )
          )
        )
        val flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None))
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq(flight),
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = priceBreakDownNode,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("id-1", roomIdentifier)), flightRequests, None)
        val context = setupBookingContext(BookingFlow.Package)

        assertResult((InitializeBookingStatus.InvalidGiftCardRedeem, None)) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            Some(RedeemRequest(99.5, Some(0))),
            productItems,
            CustomerRiskStatus.Safe
          )(context)
        }
      }
    }

    "BookingFlow is Package and PriceConfirm is not complete and room with request RoomIdentifier is returned" should {
      "return ProductNotReady" in {
        val flight             = mockFlightProductItem(false, true, true)
        val property           = mockPropertyProductItem()
        val priceBreakDownNode = None
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq(flight),
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = priceBreakDownNode,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("id-1", roomIdentifier)), Seq.empty, None)
        val context = setupBookingContext(BookingFlow.Package)

        assertResult(
          (InitializeBookingStatus.ProductNotReady, Some("Flight and Property exist but Price is not confirmed"))
        ) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is Package and Campaign is included but DF failed to apply" should {
      "return CampaignNotApplied" in {
        val flight   = mockFlightProductItem(true, true, true)
        val property = mockPropertyProductItem()
        val priceBreakDownNode = Some(
          PriceBreakdownNode(
            value = Option(
              PriceBreakdownResponse(
                `type` = PriceBreakdownType.TotalPrice,
                amount = Money(amount = 2000d, currencyCode = "THB")
              )
            )
          )
        )
        val flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None))
        val productItems =
          ProductData(
            properties = Seq(property),
            flights = Seq(flight),
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = priceBreakDownNode,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        val productRequest =
          ProductsRequest(None, Seq(defaultPropertyItemRequest("id-1", roomIdentifier)), flightRequests, None)
        val context = setupBookingContext(BookingFlow.Package)

        assertResult((InitializeBookingStatus.CampaignNotApplied, None)) {
          SetupBookingStatusProvider.createStatus(
            productRequest,
            None,
            productItems,
            CustomerRiskStatus.Safe,
            isCampaignRequested = true
          )(context)
        }
      }
    }

    "BookingFlow is Vehicle and No Vehicle Option return" should {
      "return ProductNotFound" in {
        val productItems = ProductData(
          properties = Seq.empty,
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          totalPriceDisplay = None,
          priceDisplayType = None,
          packageToken = None,
          priceChange = None,
          priceConfirmed = false
        )
        val productRequest = ProductsRequest(None, Seq.empty, Seq.empty, None)
        val context        = setupBookingContext(BookingFlow.SingleVehicle)

        assertResult((InitializeBookingStatus.ProductNotFound, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }
    }

    "BookingFlow is Activity" should {
      "return ProductNotFound when no activity return" in {
        val productItems = ProductData(
          properties = Seq.empty,
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          totalPriceDisplay = None,
          priceDisplayType = None,
          packageToken = None,
          priceChange = None,
          priceConfirmed = false
        )
        val productRequest = ProductsRequest(None, Seq.empty, Seq.empty, None)
        val context        = setupBookingContext(BookingFlow.SingleActivity)

        assertResult((InitializeBookingStatus.ProductNotFound, None)) {
          SetupBookingStatusProvider.createStatus(productRequest, None, productItems, CustomerRiskStatus.Safe)(context)
        }
      }

      val activityItem = ActivityConfirmationData("some id", "some content", true, None, None)
      val activityRequests = Some(
        Seq(
          ActivityRequestItem("some id", ActivityConfirmPriceRequest("some", "some"))
        )
      )
      val productItems =
        ProductData(
          properties = Seq.empty,
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq(activityItem),
          totalPriceDisplay = None,
          priceDisplayType = None,
          packageToken = None,
          priceChange = None,
          priceConfirmed = false
        )
      val productRequest = ProductsRequest(
        bookingToken = None,
        propertyRequests = Seq.empty,
        flightRequests = Seq.empty,
        activityRequests = activityRequests
      )

      def campaignPromotionInfo(
          campaignId: Int,
          promotionCode: String,
          status: CampaignStatusType.CampaignStatusType
      ) = CampaignPromotionInfo(
        campaignType = Some(CampaignType.ActivitiesPromotionCode),
        campaignId = campaignId,
        cid = 1,
        promotionCode = promotionCode,
        campaignName = promotionCode,
        campaignDiscountType = Some(CampaignDiscountType.Amount),
        originalDiscountPercentage = None,
        originalDiscountAmount = Some(1.0),
        originalDiscountCurrencyCode = Some("THB"),
        validDateType = None,
        dateValidFrom = None,
        dateValidUntil = None,
        isAutoApply = Some(true),
        isAutoApplyBookingForm = None,
        inapplicableReasonString = None,
        inapplicableReason = None,
        isStateIdRequired = None,
        promotionCodeType = Some(PromotionCodeType.Wallet),
        status = Some(status),
        cms = Some(Seq(PromoCmsData(id = 289345, params = Map("amount" -> "1.0"), targetField = 13)))
      )

      "ACTB-PMC-WIDGET experiment is A" should {
        "return OK when there's an activities data" in {
          val context = setupBookingContext(BookingFlow.SingleActivity)

          assertResult((InitializeBookingStatus.Ok, None)) {
            SetupBookingStatusProvider.createStatus(
              productRequest,
              None,
              productItems,
              CustomerRiskStatus.Safe,
              isCampaignRequested = true
            )(context)
          }
        }
      }

      "ACTB-PMD-WIDGET experiment is B" should {
        "return OK when there is no campaignPromotionInfo object" in {
          val featureAware = mock[FeatureAware]
          when(featureAware.isPmcWidgetExperiment).thenReturn(true)
          when(requestContext.featureAware).thenReturn(Some(featureAware))
          val context = setupBookingContext(BookingFlow.SingleActivity).copy(requestContext = requestContext)

          assertResult((InitializeBookingStatus.Ok, None)) {
            SetupBookingStatusProvider.createStatus(
              productRequest,
              None,
              productItems,
              CustomerRiskStatus.Safe,
              isCampaignRequested = true
            )(context)
          }
        }

        "return OK when there is an applied promocode" in {
          val featureAware = mock[FeatureAware]
          when(featureAware.isPmcWidgetExperiment).thenReturn(true)
          when(requestContext.featureAware).thenReturn(Some(featureAware))
          val context = setupBookingContext(BookingFlow.SingleActivity).copy(requestContext = requestContext)

          val productItemsWithPromocode = productItems.copy(
            campaignPromotionInfo = Some(
              PromotionInfo(
                maximumPromotionCodeDiscount = Some(
                  EligibleDiscount(
                    CampaignInfo(
                      id = 101,
                      cid = 1,
                      promotionCode = "COUPON",
                      discountType = DiscountType.Amount,
                      amount = -1.0,
                      currency = Some("THB")
                    )
                  )
                ),
                maximumCreditCardDiscount = None,
                appliedCampaigns = Some(Seq(campaignPromotionInfo(101, "COUPON", CampaignStatusType.Selected))),
                campaigns = Seq(
                  campaignPromotionInfo(101, "COUPON", CampaignStatusType.Selected)
                )
              )
            )
          )

          assertResult((InitializeBookingStatus.Ok, None)) {
            SetupBookingStatusProvider.createStatus(
              productRequest,
              None,
              productItemsWithPromocode,
              CustomerRiskStatus.Safe,
              isCampaignRequested = true
            )(context)
          }
        }

        "return CampaignNotApplied when there is no applied promocode - Some with empty Sequence" in {
          val featureAware = mock[FeatureAware]
          when(featureAware.isPmcWidgetExperiment).thenReturn(true)
          when(requestContext.featureAware).thenReturn(Some(featureAware))
          val context = setupBookingContext(BookingFlow.SingleActivity).copy(requestContext = requestContext)

          val productItemsWithoutPromocode = productItems.copy(
            campaignPromotionInfo = Some(
              PromotionInfo(
                maximumPromotionCodeDiscount = None,
                maximumCreditCardDiscount = None,
                appliedCampaigns = Some(Seq.empty),
                campaigns = Seq.empty
              )
            )
          )
          assertResult((InitializeBookingStatus.CampaignNotApplied, None)) {
            SetupBookingStatusProvider.createStatus(
              productRequest,
              None,
              productItemsWithoutPromocode,
              CustomerRiskStatus.Safe,
              isCampaignRequested = true
            )(context)
          }
        }

        "return CampaignNotApplied when there is no applied promocode - None" in {
          val featureAware = mock[FeatureAware]
          when(featureAware.isPmcWidgetExperiment).thenReturn(true)
          when(requestContext.featureAware).thenReturn(Some(featureAware))
          val context = setupBookingContext(BookingFlow.SingleActivity).copy(requestContext = requestContext)

          val productItemsWithoutPromocode = productItems.copy(
            campaignPromotionInfo = Some(
              PromotionInfo(
                maximumPromotionCodeDiscount = None,
                maximumCreditCardDiscount = None,
                appliedCampaigns = None,
                campaigns = Seq.empty
              )
            )
          )
          assertResult((InitializeBookingStatus.CampaignNotApplied, None)) {
            SetupBookingStatusProvider.createStatus(
              productRequest,
              None,
              productItemsWithoutPromocode,
              CustomerRiskStatus.Safe,
              isCampaignRequested = true
            )(context)
          }
        }
      }
    }

    "hasRequestedRooms" should {
      "return true when match roomIdentifiers with empty roomSelection and simplifiedRoomSelection" in {
        val productItem = bookingPropertyProduct()
        val productItems =
          ProductData(
            properties = Seq(productItem),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )

        val propertyItemRequest =
          defaultPropertyItemRequest("id-1", "matchedroomidentifiers")
        val requestItem = propertyItemRequest.copy(
          propertySearchCriteria = propertyItemRequest.propertySearchCriteria.copy(
            roomSelectionRequest = None,
            simplifiedRoomSelectionRequest = None
          )
        )
        val productRequest =
          ProductsRequest(None, Seq(requestItem), Seq.empty, None)
        assertResult(true) {
          SetupBookingStatusProvider.hasRequestedRooms(productRequest, productItems)
        }
      }
      "return false when match not a property has a missing masterRoom " in {
        val productItem = bookingPropertyProduct()
        val productItemWithNoMasterRoom =
          bookingPropertyProduct().modify(_.papiProperties.each.property.each.masterRooms).setTo(Nil)
        val productItems =
          ProductData(
            properties = Seq(productItem, productItemWithNoMasterRoom),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val propertyItemRequest =
          defaultPropertyItemRequest("id-1", "matchedroomidentifiers")
        val requestItem = propertyItemRequest.copy(
          propertySearchCriteria = propertyItemRequest.propertySearchCriteria.copy(
            roomSelectionRequest = None,
            simplifiedRoomSelectionRequest = None
          )
        )
        val productRequest =
          ProductsRequest(None, Seq(requestItem, requestItem), Seq.empty, None)
        assertResult(false) {
          SetupBookingStatusProvider.hasRequestedRooms(productRequest, productItems)
        }
      }

      "return true when match roomIdentifiers is not matched with change requestExtraBedForRoomNumbers in RoomSelection" in {
        val productItem = bookingPropertyProduct()
        val productItems =
          ProductData(
            properties = Seq(productItem),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val propertyItemRequest =
          defaultPropertyItemRequest("id-1", "notmatchedroomidentifiers")
        val requestItem = propertyItemRequest.copy(
          propertySearchCriteria = propertyItemRequest.propertySearchCriteria.copy(
            roomSelectionRequest = Some(defaultRoomSelectionRequest(0, Some(List(1)))),
            simplifiedRoomSelectionRequest =
              Some(SimplifiedRoomSelectionRequest("id", Some(1), requestExtraBedForRoomNumbers = None))
          )
        )
        val productRequest =
          ProductsRequest(None, Seq(requestItem), Seq.empty, None)
        assertResult(true) {
          SetupBookingStatusProvider.hasRequestedRooms(productRequest, productItems)
        }
      }

      "return true when match roomIdentifiers is not matched with change requestExtraBedForRoomNumbers in SimplifiedRoomSelection" in {
        val productItem = bookingPropertyProduct()
        val productItems =
          ProductData(
            properties = Seq(productItem),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val propertyItemRequest =
          defaultPropertyItemRequest("id-1", "notmatchedroomidentifiers")
        val requestItem = propertyItemRequest.copy(
          propertySearchCriteria = propertyItemRequest.propertySearchCriteria.copy(
            roomSelectionRequest = Some(defaultRoomSelectionRequest(1, Some(List(1)))),
            simplifiedRoomSelectionRequest =
              Some(SimplifiedRoomSelectionRequest("id", Some(1), requestExtraBedForRoomNumbers = Some(List(1))))
          )
        )
        val productRequest =
          ProductsRequest(None, Seq(requestItem), Seq.empty, None)
        assertResult(true) {
          SetupBookingStatusProvider.hasRequestedRooms(productRequest, productItems)
        }
      }

      "return true when match roomIdentifiers is not matched with change, but have packaging Request" in {
        val productItem1 = bookingPropertyProductWithPackagePricing(Seq("unmatch roomIdentifier"))
        val productItem2 = bookingPropertyProductWithPackagePricing(Seq("rid2"))
        val productItems =
          ProductData(
            properties = Seq(productItem1, productItem2),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val propertyItemRequest1 = defaultPropertyItemRequest("id-1", "rid1")
        val propertyItemRequest2 = defaultPropertyItemRequest("id-2", "rid2")
        val productRequest =
          ProductsRequest(
            None,
            Seq(propertyItemRequest1, propertyItemRequest2),
            Seq.empty,
            None,
            packageRequest = Some(PackageRequest("", None))
          )

        assertResult(true) {
          SetupBookingStatusProvider.hasRequestedRooms(productRequest, productItems)
        }
      }

      "return false when match roomIdentifiers is not matched with change have packaging Request, but child room missing on one property" in {
        val productItem1 = bookingPropertyProductWithPackagePricing(Seq.empty)
        val productItem2 = bookingPropertyProductWithPackagePricing(Seq("rid2"))
        val productItems =
          ProductData(
            properties = Seq(productItem1, productItem2),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val propertyItemRequest1 = defaultPropertyItemRequest("id-1", "rid1")
        val propertyItemRequest2 = defaultPropertyItemRequest("id-2", "rid2")
        val productRequest =
          ProductsRequest(
            None,
            Seq(propertyItemRequest1, propertyItemRequest2),
            Seq.empty,
            None,
            packageRequest = Some(PackageRequest("", None))
          )

        assertResult(false) {
          SetupBookingStatusProvider.hasRequestedRooms(productRequest, productItems)
        }
      }

      "return false when not match roomIdentifiers is not matched with no change in requestExtraBedForRoomNumbers" in {
        val productItem = bookingPropertyProduct()
        val productItems =
          ProductData(
            properties = Seq(productItem),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val propertyItemRequest =
          defaultPropertyItemRequest("id-1", "notmatchedroomidentifiers")
        val requestItem = propertyItemRequest.copy(
          propertySearchCriteria = propertyItemRequest.propertySearchCriteria.copy(
            roomSelectionRequest = Some(defaultRoomSelectionRequest(1, Some(List(1)))),
            simplifiedRoomSelectionRequest =
              Some(SimplifiedRoomSelectionRequest("id", Some(1), requestExtraBedForRoomNumbers = None))
          )
        )
        val productRequest =
          ProductsRequest(None, Seq(requestItem), Seq.empty, None)
        assertResult(false) {
          SetupBookingStatusProvider.hasRequestedRooms(productRequest, productItems)
        }
      }

      "UpXSell case: return true when not match roomIdentifiers is not matched with no change in requestExtraBedForRoomNumbers" in {
        val featureAwareMock = mock[FeatureAware]
        when(requestContext.featureAware).thenReturn(Some(featureAwareMock))
        val pricingRequest = Some(
          PricingRequest(
            isMse = false,
            requiredPrice = "",
            requiredBasis = "",
            isRPM2Included = false,
            selectedPointMaxId = None,
            isIncludeUsdAndLocalCurrency = false,
            allowOverrideOccupancy = false,
            enableOpaqueChannel = false,
            isAllowRoomTypeNotGuarantee = false,
            synchronous = true,
            partnerLoyaltyProgramId = Some(0),
            dfFeatureFlags = Seq(PropertyFeatureFlags.BreakfastUpSell.id)
          )
        )
        val propertyItemRequest =
          defaultPropertyItemRequest("id-1", "matchedroomidentifiers", pricingRequest)
        val requestItem = propertyItemRequest.copy(
          propertySearchCriteria = propertyItemRequest.propertySearchCriteria.copy(
            roomSelectionRequest = Some(defaultRoomSelectionRequest(1, Some(List(1)))),
            simplifiedRoomSelectionRequest = Some(
              SimplifiedRoomSelectionRequest(
                "matchedroomidentifiers",
                Some(1),
                requestExtraBedForRoomNumbers = Some(List(1))
              )
            )
          )
        )
        val productItem =
          bookingPropertyProduct.copy(propertySearchCriteria = Some(propertyItemRequest.propertySearchCriteria))
        val productItems =
          ProductData(
            properties = Seq(productItem, productItem.copy(id = "2")),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val productRequest =
          ProductsRequest(None, Seq(requestItem), Seq.empty, None)
        assertResult(true) {
          SetupBookingStatusProvider.hasRequestedRooms(productRequest, productItems)
        }
      }
    }

    "BookingFlow is Cart" should {
      "return Ok when all rooms are available" in {
        val productItems =
          ProductData(
            properties = Seq(
              mockPropertyProductItem(),
              mockPropertyProductItem().copy(id = "2"),
              mockPropertyProductItem().copy(id = "3")
            ),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        val productRequest =
          ProductsRequest(
            bookingToken = None,
            propertyRequests = Seq(
              defaultPropertyItemRequest("id-1", roomIdentifier),
              defaultPropertyItemRequest("id-2", roomIdentifier),
              defaultPropertyItemRequest("id-3", roomIdentifier)
            )
          )
        val ccof    = mockCreditCardOnFile()
        val context = setupBookingContext(BookingFlow.Cart)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(
            request = productRequest,
            redeemRequest = None,
            products = productItems,
            tprmCheckResult = CustomerRiskStatus.Safe,
            availableCreditCardsOnFile = ccof
          )(context)
        }
      }

      "return Ok when there multiple product in cart" in {
        val flight         = mockFlightProductItem(true, true, true)
        val vehicle        = mockCarConfirmationData()
        val activityItem   = ActivityConfirmationData("some id", "some content", true, None, None)
        val flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None))
        val carRequests    = Some(Seq(mock[CarRequestItem]))
        val activityRequests = Some(
          Seq(
            mock[ActivityRequestItem]
          )
        )

        val productItems =
          ProductData(
            properties = Seq(
              mockPropertyProductItem(),
              mockPropertyProductItem().copy(id = "2"),
              mockPropertyProductItem().copy(id = "3")
            ),
            flights = Seq(flight),
            cars = Seq(vehicle),
            protections = Seq.empty,
            activities = Seq(activityItem),
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        val productRequest =
          ProductsRequest(
            propertyRequests = Seq(
              defaultPropertyItemRequest("id-1", roomIdentifier),
              defaultPropertyItemRequest("id-2", roomIdentifier),
              defaultPropertyItemRequest("id-3", roomIdentifier)
            ),
            flightRequests = flightRequests,
            carRequestsOpt = carRequests,
            activityRequests = activityRequests
          )
        val ccof    = mockCreditCardOnFile()
        val context = setupBookingContext(BookingFlow.Cart)

        assertResult((InitializeBookingStatus.Ok, None)) {
          SetupBookingStatusProvider.createStatus(
            request = productRequest,
            redeemRequest = None,
            products = productItems,
            tprmCheckResult = CustomerRiskStatus.Safe,
            availableCreditCardsOnFile = ccof
          )(context)
        }
      }

      "return ProductItemNotFound when the requested flight is not found" in {
        val flight       = mockFlightProductItem(true, true, true).copy(id = "flightId1")
        val vehicle      = mockCarConfirmationData()
        val activityItem = ActivityConfirmationData("some id", "some content", true, None, None)
        val flightRequests = Seq(
          FlightRequestItem(Some("flightId1"), Some(ConfirmPriceRequest("", "")), None),
          FlightRequestItem(Some("flightId2"), Some(ConfirmPriceRequest("", "")), None)
        )
        val carRequests = Some(Seq(mock[CarRequestItem]))
        val activityRequests = Some(
          Seq(
            mock[ActivityRequestItem]
          )
        )

        val productItems =
          ProductData(
            properties = Seq(
              mockPropertyProductItem(),
              mockPropertyProductItem().copy(id = "2"),
              mockPropertyProductItem().copy(id = "3")
            ),
            flights = Seq(flight),
            cars = Seq(vehicle),
            protections = Seq.empty,
            activities = Seq(activityItem),
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        val productRequest =
          ProductsRequest(
            propertyRequests = Seq(
              defaultPropertyItemRequest("id-1", roomIdentifier),
              defaultPropertyItemRequest("id-2", roomIdentifier),
              defaultPropertyItemRequest("id-3", roomIdentifier)
            ),
            flightRequests = flightRequests,
            carRequestsOpt = carRequests,
            activityRequests = activityRequests
          )
        val ccof    = mockCreditCardOnFile()
        val context = setupBookingContext(BookingFlow.Cart)

        assertResult((InitializeBookingStatus.ProductItemNotFound, Some("[Flight not found] id:flightId2"))) {
          SetupBookingStatusProvider.createStatus(
            request = productRequest,
            redeemRequest = None,
            products = productItems,
            tprmCheckResult = CustomerRiskStatus.Safe,
            availableCreditCardsOnFile = ccof
          )(context)
        }
      }

      "return ProductItemNotFound when the requested car is not found" in {
        val flight         = mockFlightProductItem(true, true, true)
        val vehicle        = mockCarConfirmationData().copy(id = "carId1")
        val activityItem   = ActivityConfirmationData("some id", "some content", true, None, None)
        val flightRequests = Seq(FlightRequestItem(Some("flightId"), Some(ConfirmPriceRequest("", "")), None))
        val carRequests = Some(
          Seq(
            CarRequestItem(
              id = "carId1",
              confirmPriceRequest = CarConfirmPriceRequest(
                identifier = None,
                searchToken = "token"
              )
            ),
            CarRequestItem(
              id = "carId2",
              confirmPriceRequest = CarConfirmPriceRequest(
                identifier = None,
                searchToken = "token"
              )
            )
          )
        )
        val activityRequests = Some(
          Seq(
            mock[ActivityRequestItem]
          )
        )

        val productItems =
          ProductData(
            properties = Seq(
              mockPropertyProductItem(),
              mockPropertyProductItem().copy(id = "2"),
              mockPropertyProductItem().copy(id = "3")
            ),
            flights = Seq(flight),
            cars = Seq(vehicle),
            protections = Seq.empty,
            activities = Seq(activityItem),
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        val productRequest =
          ProductsRequest(
            propertyRequests = Seq(
              defaultPropertyItemRequest("id-1", roomIdentifier),
              defaultPropertyItemRequest("id-2", roomIdentifier),
              defaultPropertyItemRequest("id-3", roomIdentifier)
            ),
            flightRequests = flightRequests,
            carRequestsOpt = carRequests,
            activityRequests = activityRequests
          )
        val ccof    = mockCreditCardOnFile()
        val context = setupBookingContext(BookingFlow.Cart)

        assertResult((InitializeBookingStatus.ProductItemNotFound, Some("[Car not found] id:carId2"))) {
          SetupBookingStatusProvider.createStatus(
            request = productRequest,
            redeemRequest = None,
            products = productItems,
            tprmCheckResult = CustomerRiskStatus.Safe,
            availableCreditCardsOnFile = ccof
          )(context)
        }
      }

      "return ProductItemNotFound when the requested activity is not found" in {
        val flight         = mockFlightProductItem(true, true, true)
        val vehicle        = mockCarConfirmationData()
        val activityItem   = ActivityConfirmationData("activityId1", "some content", true, None, None)
        val flightRequests = Seq(FlightRequestItem(Some("flightId"), Some(ConfirmPriceRequest("", "")), None))
        val carRequests    = Some(Seq(mock[CarRequestItem]))
        val activityRequests = Some(
          Seq(
            ActivityRequestItem("activityId1", ActivityConfirmPriceRequest("some", "some")),
            ActivityRequestItem("activityId2", ActivityConfirmPriceRequest("some", "some"))
          )
        )

        val productItems =
          ProductData(
            properties = Seq(
              mockPropertyProductItem(),
              mockPropertyProductItem().copy(id = "2"),
              mockPropertyProductItem().copy(id = "3")
            ),
            flights = Seq(flight),
            cars = Seq(vehicle),
            protections = Seq.empty,
            activities = Seq(activityItem),
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        val productRequest =
          ProductsRequest(
            propertyRequests = Seq(
              defaultPropertyItemRequest("id-1", roomIdentifier),
              defaultPropertyItemRequest("id-2", roomIdentifier),
              defaultPropertyItemRequest("id-3", roomIdentifier)
            ),
            flightRequests = flightRequests,
            carRequestsOpt = carRequests,
            activityRequests = activityRequests
          )
        val ccof    = mockCreditCardOnFile()
        val context = setupBookingContext(BookingFlow.Cart)

        assertResult((InitializeBookingStatus.ProductItemNotFound, Some("[Activity not found] id:activityId2"))) {
          SetupBookingStatusProvider.createStatus(
            request = productRequest,
            redeemRequest = None,
            products = productItems,
            tprmCheckResult = CustomerRiskStatus.Safe,
            availableCreditCardsOnFile = ccof
          )(context)
        }
      }

      "return ProductNotFound when there is no product in cart and request" in {
        val productItems =
          ProductData(
            properties = Seq.empty,
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        val productRequest =
          ProductsRequest(
            None,
            Seq.empty,
            Seq.empty,
            None
          )
        val ccof    = mockCreditCardOnFile()
        val context = setupBookingContext(BookingFlow.Cart)

        assertResult((InitializeBookingStatus.ProductNotFound, Some("There is no product in cart"))) {
          SetupBookingStatusProvider.createStatus(
            request = productRequest,
            redeemRequest = None,
            products = productItems,
            tprmCheckResult = CustomerRiskStatus.Safe,
            availableCreditCardsOnFile = ccof
          )(context)
        }
      }

      "return Allotment Not Available when one of the room is not available" in {
        val productItems =
          ProductData(
            properties = Seq(
              mockPropertyProductItem(),
              mockPropertyProductItem().copy(id = "2"),
              mockPropertyProductItem().copy(id = "3")
            ),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        val productRequest =
          ProductsRequest(
            None,
            Seq(
              defaultPropertyItemRequest("id-1", roomIdentifier),
              defaultPropertyItemRequest("id-2", roomIdentifier),
              defaultPropertyItemRequest("id-3", roomIdentifier)
            ),
            Seq.empty,
            None
          )
        val ncof    = mockNonCardOnFile()
        val context = setupBookingContext(BookingFlow.Cart)

        val allotmentStatus = Seq(
          AllotmentPreCheckStatus("1", None, None, AllotmentStatus.Available),
          AllotmentPreCheckStatus("2", None, None, AllotmentStatus.NotAvailable),
          AllotmentPreCheckStatus("3", None, None, AllotmentStatus.Available)
        )
        assertResult((InitializeBookingStatus.AllotmentNotAvailable, Some("Allotment Not Available for ProductId 2"))) {
          SetupBookingStatusProvider.createStatus(
            request = productRequest,
            redeemRequest = None,
            products = productItems,
            tprmCheckResult = CustomerRiskStatus.Safe,
            allotmentPreCheckResults = allotmentStatus,
            availableNonCardsOnFile = ncof
          )(context)
        }
      }

      "return ProductItemNotFound when one of the item is not found" in {
        val productItems =
          ProductData(
            properties = Seq(
              mockPropertyProductItem(),
              mockPropertyProductItem().copy(id = "2"),
              mockPropertyProductItem().copy(id = "3")
            ),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        val productRequest =
          ProductsRequest(
            None,
            Seq(
              defaultPropertyItemRequest("id-1", "NotMatchRoomId"),
              defaultPropertyItemRequest("id-2", roomIdentifier),
              defaultPropertyItemRequest("id-3", roomIdentifier)
            ),
            Seq.empty,
            None
          )

        val context = setupBookingContext(BookingFlow.Cart).copy(requestContext = requestContext)

        assertResult(
          (
            InitializeBookingStatus.ProductItemNotFound,
            Some(
              "Property Id:1 papiResponseRoomIdentifier:roomIdentifier requestRoomIdentifier:NotMatchRoomId, Property Id:2 papiResponseRoomIdentifier:roomIdentifier requestRoomIdentifier:roomIdentifier, Property Id:3 papiResponseRoomIdentifier:roomIdentifier requestRoomIdentifier:roomIdentifier"
            )
          )
        ) {
          SetupBookingStatusProvider.createStatus(
            request = productRequest,
            redeemRequest = None,
            products = productItems,
            tprmCheckResult = CustomerRiskStatus.Safe,
            paymentMethods = mockPaymentMethods
          )(context)
        }
      }

      "return ProductNotReady when the price is not confirm" in {
        val productItems =
          ProductData(
            properties = Seq(
              mockPropertyProductItem(),
              mockPropertyProductItem().copy(id = "2"),
              mockPropertyProductItem().copy(id = "3")
            ),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val productRequest =
          ProductsRequest(
            None,
            Seq(
              defaultPropertyItemRequest("id-1", "NotMatchRoomId"),
              defaultPropertyItemRequest("id-2", roomIdentifier),
              defaultPropertyItemRequest("id-3", roomIdentifier)
            ),
            Seq.empty,
            None
          )
        val ccof = mockCreditCardOnFile()

        val context = setupBookingContext(BookingFlow.Cart).copy(requestContext = requestContext)

        assertResult(
          (
            InitializeBookingStatus.ProductItemNotFound,
            Some(
              createRemark(
                properties = productItems.properties,
                propertyRequests = productRequest.propertyRequests
              )
            )
          )
        ) {
          SetupBookingStatusProvider.createStatus(
            request = productRequest,
            redeemRequest = None,
            products = productItems,
            tprmCheckResult = CustomerRiskStatus.Safe,
            availableCreditCardsOnFile = ccof
          )(context)
        }
      }

      "return ProductItemNotFound when the price is not confirm" in {
        val productItems =
          ProductData(
            properties = Seq(
              mockPropertyProductItem(),
              mockPropertyProductItem().copy(id = "2"),
              mockPropertyProductItem().copy(id = "3")
            ),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val productRequest =
          ProductsRequest(
            None,
            Seq(
              defaultPropertyItemRequest("id-1", "NotMatchRoomId"),
              defaultPropertyItemRequest("id-2", roomIdentifier),
              defaultPropertyItemRequest("id-3", roomIdentifier)
            ),
            Seq.empty,
            None
          )
        val ccof = mockCreditCardOnFile()

        val context = setupBookingContext(BookingFlow.Cart).copy(requestContext = requestContext)

        assertResult(
          (
            InitializeBookingStatus.ProductItemNotFound,
            Some(
              createRemarkWithMeasurement(
                properties = productItems.properties,
                propertyRequests = productRequest.propertyRequests
              )
            )
          )
        ) {
          SetupBookingStatusProvider.createStatus(
            request = productRequest,
            redeemRequest = None,
            products = productItems,
            tprmCheckResult = CustomerRiskStatus.Safe,
            availableCreditCardsOnFile = ccof
          )(context)
        }
      }

      "When booking flow is Cart and properties are confirmed but any of the flights is not complete" should {
        "return ProductNotReady" in {
          val productItems =
            ProductData(
              properties = Seq(
                mockPropertyProductItem()
              ),
              flights = Seq(
                mockFlightProductItem(isComplete = true, hasFlight = true, hasContent = true).copy(id = "3"),
                mockFlightProductItem(isComplete = false, hasFlight = true, hasContent = true).copy(id = "4")
              ),
              cars = Seq.empty,
              protections = Seq.empty,
              activities = Seq.empty,
              totalPriceDisplay = None,
              priceDisplayType = None,
              packageToken = None,
              priceChange = None,
              priceConfirmed = true
            )
          val productRequest =
            ProductsRequest(
              bookingToken = None,
              propertyRequests = Seq(
                defaultPropertyItemRequest("id-1", roomIdentifier)
              ),
              flightRequests = Seq(
                FlightRequestItem(Some("id-3"), Some(ConfirmPriceRequest("", "")), None),
                FlightRequestItem(Some("id-4"), Some(ConfirmPriceRequest("", "")), None)
              )
            )
          val ccof    = mockCreditCardOnFile()
          val context = setupBookingContext(BookingFlow.Cart)

          assertResult((InitializeBookingStatus.ProductNotReady, Some("Flight Request is not completed"))) {
            SetupBookingStatusProvider.createStatus(
              request = productRequest,
              redeemRequest = None,
              products = productItems,
              tprmCheckResult = CustomerRiskStatus.Safe,
              availableCreditCardsOnFile = ccof
            )(context)
          }
        }
      }

      "When booking flow is Cart and properties are confirmed but any of the vehicles is not complete" should {
        "return ProductNotReady" in {
          val flight         = mockFlightProductItem(isComplete = true, hasFlight = true, hasContent = true)
          val vehicle        = mockCarConfirmationData().copy(isCompleted = false)
          val activityItem   = ActivityConfirmationData("some id", "some content", true, None, None)
          val flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None))
          val carRequests    = Some(Seq(mock[CarRequestItem]))
          val activityRequests = Some(
            Seq(
              mock[ActivityRequestItem]
            )
          )

          val productItems =
            ProductData(
              properties = Seq(
                mockPropertyProductItem().copy(id = "1")
              ),
              flights = Seq(flight),
              cars = Seq(vehicle),
              protections = Seq.empty,
              activities = Seq(activityItem),
              totalPriceDisplay = None,
              priceDisplayType = None,
              packageToken = None,
              priceChange = None,
              priceConfirmed = true
            )

          val productRequest =
            ProductsRequest(
              propertyRequests = Seq(
                defaultPropertyItemRequest("id-1", roomIdentifier)
              ),
              flightRequests = flightRequests,
              carRequestsOpt = carRequests,
              activityRequests = activityRequests
            )

          val ccof    = mockCreditCardOnFile()
          val context = setupBookingContext(BookingFlow.Cart)

          assertResult((InitializeBookingStatus.ProductNotReady, None)) {
            SetupBookingStatusProvider.createStatus(
              request = productRequest,
              redeemRequest = None,
              products = productItems,
              tprmCheckResult = CustomerRiskStatus.Safe,
              availableCreditCardsOnFile = ccof
            )(context)
          }
        }
      }

      "return NoPaymentMethodAvailable when there is no payment method available and wl id is Citi" in {
        val productItems =
          ProductData(
            properties = Seq(
              mockPropertyProductItem(),
              mockPropertyProductItem().copy(id = "2"),
              mockPropertyProductItem().copy(id = "3")
            ),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        val productRequest =
          ProductsRequest(
            bookingToken = None,
            propertyRequests = Seq(
              defaultPropertyItemRequest("id-1", "NotMatchRoomId"),
              defaultPropertyItemRequest("id-2", roomIdentifier),
              defaultPropertyItemRequest("id-3", roomIdentifier)
            ),
            flightRequests = Seq.empty,
            carRequestsOpt = None
          )
        val configuration              = featuresConfiguration(hideAvailablePaymentMethods = Some(true))
        val featureAware               = mock[FeatureAware]
        val setupBookingRequestContext = setupBookingContext(BookingFlow.Cart)
        val requestContext             = setupBookingRequestContext.requestContext.copy(featureAware = Some(featureAware))
        val context = setupBookingRequestContext
          .copy(
            whiteLabelInfo = WhiteLabelInfo(WhiteLabel.CitiUS, configuration, None),
            requestContext = requestContext
          )

        assertResult(
          (
            InitializeBookingStatus.NoPaymentMethodAvailable,
            Some("There is no Payment Method available")
          )
        ) {
          SetupBookingStatusProvider.createStatus(
            request = productRequest,
            redeemRequest = None,
            products = productItems,
            tprmCheckResult = CustomerRiskStatus.Safe,
            paymentMethods = Seq.empty,
            availableCreditCardsOnFile = Seq.empty,
            availableNonCardsOnFile = Seq.empty
          )(context)
        }
      }

      "not return NoPaymentMethodAvailable when there is no payment method available and wl id is not Citi" in {
        val productItems =
          ProductData(
            properties = Seq(
              mockPropertyProductItem(),
              mockPropertyProductItem().copy(id = "2"),
              mockPropertyProductItem().copy(id = "3")
            ),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        val productRequest =
          ProductsRequest(
            bookingToken = None,
            propertyRequests = Seq(
              defaultPropertyItemRequest("id-1", "NotMatchRoomId"),
              defaultPropertyItemRequest("id-2", roomIdentifier),
              defaultPropertyItemRequest("id-3", roomIdentifier)
            ),
            flightRequests = Seq.empty,
            carRequestsOpt = None
          )

        val context = setupBookingContext(BookingFlow.Cart).copy(requestContext = requestContext)

        assertResult(
          (
            InitializeBookingStatus.ProductItemNotFound,
            Some(
              "Property Id:1 papiResponseRoomIdentifier:roomIdentifier requestRoomIdentifier:NotMatchRoomId, Property Id:2 papiResponseRoomIdentifier:roomIdentifier requestRoomIdentifier:roomIdentifier, Property Id:3 papiResponseRoomIdentifier:roomIdentifier requestRoomIdentifier:roomIdentifier"
            )
          )
        ) {
          SetupBookingStatusProvider.createStatus(
            request = productRequest,
            redeemRequest = None,
            products = productItems,
            tprmCheckResult = CustomerRiskStatus.Safe,
            paymentMethods = Seq.empty,
            availableCreditCardsOnFile = Seq.empty,
            availableNonCardsOnFile = Seq.empty
          )(context)
        }
      }

      "return ExternalLoyaltyPointsError when status is OK but externalLoyaltyValidationResult is not Success" in {
        val flight         = mockFlightProductItem(true, true, true)
        val vehicle        = mockCarConfirmationData()
        val activityItem   = ActivityConfirmationData("some id", "some content", true, None, None)
        val flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None))
        val carRequests    = Some(Seq(mock[CarRequestItem]))
        val activityRequests = Some(
          Seq(
            mock[ActivityRequestItem]
          )
        )

        val productItems =
          ProductData(
            properties = Seq(
              mockPropertyProductItem(),
              mockPropertyProductItem().copy(id = "2"),
              mockPropertyProductItem().copy(id = "3")
            ),
            flights = Seq(flight),
            cars = Seq(vehicle),
            protections = Seq.empty,
            activities = Seq(activityItem),
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        val productRequest =
          ProductsRequest(
            propertyRequests = Seq(
              defaultPropertyItemRequest("id-1", roomIdentifier),
              defaultPropertyItemRequest("id-2", roomIdentifier),
              defaultPropertyItemRequest("id-3", roomIdentifier)
            ),
            flightRequests = flightRequests,
            carRequestsOpt = carRequests,
            activityRequests = activityRequests
          )
        val ccof         = mockCreditCardOnFile()
        val featureAware = mock[FeatureAware]
        when(featureAware.isPreCheckOnAlternatives).thenReturn(true)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(requestContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.KrisFlyer, FeaturesConfiguration()))

        val context = setupBookingContext(BookingFlow.Cart).copy(requestContext = requestContext)

        assertResult((InitializeBookingStatus.ExternalLoyaltyPointsError, None)) {
          SetupBookingStatusProvider.createStatus(
            request = productRequest,
            redeemRequest = None,
            products = productItems,
            tprmCheckResult = CustomerRiskStatus.Safe,
            availableCreditCardsOnFile = ccof,
            externalLoyaltyPointsValidationResult = ExternalLoyaltyPointsValidationResult.InsufficientPointsRedemption
          )(context)
        }
      }

      "return ExternalLoyaltyPointsError when feature enabled and externalLoyaltyValidationResult is not Success" in {
        val flight         = mockFlightProductItem(true, true, true)
        val vehicle        = mockCarConfirmationData()
        val activityItem   = ActivityConfirmationData("some id", "some content", true, None, None)
        val flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None))
        val carRequests    = Some(Seq(mock[CarRequestItem]))
        val activityRequests = Some(
          Seq(
            mock[ActivityRequestItem]
          )
        )

        val productItems =
          ProductData(
            properties = Seq(
              mockPropertyProductItem(elapiErrorCode =
                Some(ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance.id)
              ),
              mockPropertyProductItem(elapiErrorCode =
                Some(ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance.id)
              ).copy(id = "2"),
              mockPropertyProductItem(elapiErrorCode =
                Some(ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance.id)
              ).copy(id = "3")
            ),
            flights = Seq(flight),
            cars = Seq(vehicle),
            protections = Seq.empty,
            activities = Seq(activityItem),
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        val productRequest =
          ProductsRequest(
            propertyRequests = Seq(
              defaultPropertyItemRequest("id-1", roomIdentifier),
              defaultPropertyItemRequest("id-2", roomIdentifier),
              defaultPropertyItemRequest("id-3", roomIdentifier)
            ),
            flightRequests = flightRequests,
            carRequestsOpt = carRequests,
            activityRequests = activityRequests
          )
        val ccof         = mockCreditCardOnFile()
        val featureAware = mock[FeatureAware]
        when(featureAware.isPreCheckOnAlternatives).thenReturn(true)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(mockWhiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.MapELAPIErrorWithBookingSubStatus))
          .thenReturn(true)
        val context = setupBookingContext(BookingFlow.Cart).copy(requestContext = requestContext)
        assertResult((InitializeBookingStatus.ExternalLoyaltyPointsError, None)) {
          SetupBookingStatusProvider.createStatus(
            request = productRequest,
            redeemRequest = None,
            products = productItems,
            tprmCheckResult = CustomerRiskStatus.Safe,
            availableCreditCardsOnFile = ccof,
            externalLoyaltyPointsValidationResult = ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance
          )(context)
        }
      }
      "return InvalidExternalLoyaltyPointRedeem when feature disabled and externalLoyaltyValidationResult is not Success" in {
        val flight         = mockFlightProductItem(true, true, true)
        val vehicle        = mockCarConfirmationData()
        val activityItem   = ActivityConfirmationData("some id", "some content", true, None, None)
        val flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None))
        val carRequests    = Some(Seq(mock[CarRequestItem]))
        val activityRequests = Some(
          Seq(
            mock[ActivityRequestItem]
          )
        )

        val productItems =
          ProductData(
            properties = Seq(
              mockPropertyProductItem(elapiErrorCode =
                Some(ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance.id)
              ),
              mockPropertyProductItem(elapiErrorCode =
                Some(ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance.id)
              ).copy(id = "2"),
              mockPropertyProductItem(elapiErrorCode =
                Some(ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance.id)
              ).copy(id = "3")
            ),
            flights = Seq(flight),
            cars = Seq(vehicle),
            protections = Seq.empty,
            activities = Seq(activityItem),
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        val productRequest =
          ProductsRequest(
            propertyRequests = Seq(
              defaultPropertyItemRequest("id-1", roomIdentifier),
              defaultPropertyItemRequest("id-2", roomIdentifier),
              defaultPropertyItemRequest("id-3", roomIdentifier)
            ),
            flightRequests = flightRequests,
            carRequestsOpt = carRequests,
            activityRequests = activityRequests
          )
        val ccof         = mockCreditCardOnFile()
        val featureAware = mock[FeatureAware]
        when(featureAware.isPreCheckOnAlternatives).thenReturn(true)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(mockWhiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.MapELAPIErrorWithBookingSubStatus))
          .thenReturn(false)
        val context = setupBookingContext(BookingFlow.Cart).copy(requestContext = requestContext)
        assertResult((InitializeBookingStatus.InvalidExternalLoyaltyPointRedeem, None)) {
          SetupBookingStatusProvider.createStatus(
            request = productRequest,
            redeemRequest = None,
            products = productItems,
            tprmCheckResult = CustomerRiskStatus.Safe,
            availableCreditCardsOnFile = ccof,
            externalLoyaltyPointsValidationResult = ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance
          )(context)
        }
      }

      "Activities' ACTB-PMD-WIDGET experiment is B" should {
        val activityItem = ActivityConfirmationData("some id", "some content", true, None, None)
        val activityRequests = Some(
          Seq(
            ActivityRequestItem("some id", ActivityConfirmPriceRequest("some", "some"))
          )
        )
        val productItems =
          ProductData(
            properties = Seq.empty,
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq(activityItem),
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        val productRequest = ProductsRequest(
          bookingToken = None,
          propertyRequests = Seq.empty,
          flightRequests = Seq.empty,
          activityRequests = activityRequests
        )

        def campaignPromotionInfo(
            campaignId: Int,
            promotionCode: String,
            status: CampaignStatusType.CampaignStatusType
        ) = CampaignPromotionInfo(
          campaignType = Some(CampaignType.ActivitiesPromotionCode),
          campaignId = campaignId,
          cid = 1,
          promotionCode = promotionCode,
          campaignName = promotionCode,
          campaignDiscountType = Some(CampaignDiscountType.Amount),
          originalDiscountPercentage = None,
          originalDiscountAmount = Some(1.0),
          originalDiscountCurrencyCode = Some("THB"),
          validDateType = None,
          dateValidFrom = None,
          dateValidUntil = None,
          isAutoApply = Some(true),
          isAutoApplyBookingForm = None,
          inapplicableReasonString = None,
          inapplicableReason = None,
          isStateIdRequired = None,
          promotionCodeType = Some(PromotionCodeType.Wallet),
          status = Some(status),
          cms = Some(Seq(PromoCmsData(id = 289345, params = Map("amount" -> "1.0"), targetField = 13)))
        )

        "return OK when there is no campaignPromotionInfo object" in {
          val featureAware = mock[FeatureAware]
          when(featureAware.isPmcWidgetExperiment).thenReturn(true)
          when(requestContext.featureAware).thenReturn(Some(featureAware))
          val context = setupBookingContext(BookingFlow.Cart).copy(requestContext = requestContext)

          assertResult((InitializeBookingStatus.Ok, None)) {
            SetupBookingStatusProvider.createStatus(
              productRequest,
              None,
              productItems,
              CustomerRiskStatus.Safe,
              isCampaignRequested = true
            )(context)
          }
        }

        "return OK when there is an applied promocode" in {
          val featureAware = mock[FeatureAware]
          when(featureAware.isPmcWidgetExperiment).thenReturn(true)
          when(requestContext.featureAware).thenReturn(Some(featureAware))
          val context = setupBookingContext(BookingFlow.Cart).copy(requestContext = requestContext)

          val productItemsWithPromocode = productItems.copy(
            campaignPromotionInfo = Some(
              PromotionInfo(
                maximumPromotionCodeDiscount = Some(
                  EligibleDiscount(
                    CampaignInfo(
                      id = 101,
                      cid = 1,
                      promotionCode = "COUPON",
                      discountType = DiscountType.Amount,
                      amount = -1.0,
                      currency = Some("THB")
                    )
                  )
                ),
                maximumCreditCardDiscount = None,
                appliedCampaigns = Some(Seq(campaignPromotionInfo(101, "COUPON", CampaignStatusType.Selected))),
                campaigns = Seq(
                  campaignPromotionInfo(101, "COUPON", CampaignStatusType.Selected)
                )
              )
            )
          )

          assertResult((InitializeBookingStatus.Ok, None)) {
            SetupBookingStatusProvider.createStatus(
              productRequest,
              None,
              productItemsWithPromocode,
              CustomerRiskStatus.Safe,
              isCampaignRequested = true
            )(context)
          }
        }

        "return CampaignNotApplied when there is no applied promocode - Some with empty Sequence" in {
          val featureAware = mock[FeatureAware]
          when(featureAware.isPmcWidgetExperiment).thenReturn(true)
          when(requestContext.featureAware).thenReturn(Some(featureAware))
          val context = setupBookingContext(BookingFlow.Cart).copy(requestContext = requestContext)

          val productItemsWithoutPromocode = productItems.copy(
            campaignPromotionInfo = Some(
              PromotionInfo(
                maximumPromotionCodeDiscount = None,
                maximumCreditCardDiscount = None,
                appliedCampaigns = Some(Seq.empty),
                campaigns = Seq.empty
              )
            )
          )
          assertResult((InitializeBookingStatus.CampaignNotApplied, None)) {
            SetupBookingStatusProvider.createStatus(
              productRequest,
              None,
              productItemsWithoutPromocode,
              CustomerRiskStatus.Safe,
              isCampaignRequested = true
            )(context)
          }
        }

        "return CampaignNotApplied when there is no applied promocode - None" in {
          val featureAware = mock[FeatureAware]
          when(featureAware.isPmcWidgetExperiment).thenReturn(true)
          when(requestContext.featureAware).thenReturn(Some(featureAware))
          val context = setupBookingContext(BookingFlow.Cart).copy(requestContext = requestContext)

          val productItemsWithoutPromocode = productItems.copy(
            campaignPromotionInfo = Some(
              PromotionInfo(
                maximumPromotionCodeDiscount = None,
                maximumCreditCardDiscount = None,
                appliedCampaigns = None,
                campaigns = Seq.empty
              )
            )
          )
          assertResult((InitializeBookingStatus.CampaignNotApplied, None)) {
            SetupBookingStatusProvider.createStatus(
              productRequest,
              None,
              productItemsWithoutPromocode,
              CustomerRiskStatus.Safe,
              isCampaignRequested = true
            )(context)
          }
        }

      }
    }
  }

  "isInvalidAgodaCashRedeem" should {
    val propertyItemRequest = defaultPropertyItemRequest("1", "roomiden-original")

    val productRequest = ProductsRequest(
      bookingToken = None,
      propertyRequests = Seq(propertyItemRequest),
      flightRequests = Seq.empty,
      carRequestsOpt = None
    )

    val productItems =
      ProductData(
        properties = Seq(mockPropertyProductItem()),
        flights = Seq.empty,
        cars = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        totalPriceDisplay = None,
        priceDisplayType = None,
        packageToken = None,
        priceChange = None,
        priceConfirmed = false
      )

    val insufficientLoyaltyPaymentBoundaries = LoyaltyPaymentBoundaries(
      min = 0,
      max = 100,
      maxBeforeGatewayLimit = 99.5,
      minTotalAfterRedeem = Some(0.5),
      canStayForFree = Some(false),
      reason = LoyaltyReasons.InsufficientBalance
    )

    val insufficientPropertyProductItem =
      mockPropertyProductItem(loyaltyResponse = Some(insufficientLoyaltyPaymentBoundaries))

    val redeemRequest = RedeemRequest(redeemAmount = 99.5, cashbackRedeemAmount = Some(0))

    "return true when invalid redeem" in {
      val propertyProductItem = insufficientPropertyProductItem
      val productItem         = productItems.copy(properties = Seq(propertyProductItem))

      assertResult(true) {
        SetupBookingStatusProvider.isInvalidAgodaCashRedeem(productRequest, productItem, Some(redeemRequest))
      }
    }

    "return false when invalid redeem but got crossSell room" in {

      val propertySearchCriteria = PropertySearchCriteriaMock.value
        .copy(
          propertyId = Some(1),
          roomIdentifier = roomIdentifier,
          simplifiedRoomSelectionRequest = Some(
            SimplifiedRoomSelectionRequest(
              roomIdentifier = roomIdentifier,
              alternativeOptIn = Some(
                AlternativeRoomOptIn(
                  swapRoomType = None,
                  crossSellOptIn = Some(CrossSellOptIn(reason = Some(CrossSellReasonType.preAuth.id)))
                )
              )
            )
          )
        )
      val requestItem = propertyItemRequest.copy(propertySearchCriteria = propertySearchCriteria)
      val productRequest = ProductsRequest(
        bookingToken = None,
        propertyRequests = Seq(requestItem),
        flightRequests = Seq.empty,
        carRequestsOpt = None
      )
      val propertyProductItem =
        insufficientPropertyProductItem.copy(propertySearchCriteria = Some(propertySearchCriteria))
      val productItem = productItems.copy(properties = Seq(propertyProductItem))

      assertResult(false) {
        SetupBookingStatusProvider.isInvalidAgodaCashRedeem(productRequest, productItem, Some(redeemRequest))
      }
    }

    "return false when valid redeem and loyalty reason is success" in {
      val productItem = productItems

      assertResult(false) {
        SetupBookingStatusProvider.isInvalidAgodaCashRedeem(productRequest, productItem, Some(redeemRequest))
      }
    }

    "return false when no redeem amount" in
      assertResult(false) {
        SetupBookingStatusProvider.isInvalidAgodaCashRedeem(productRequest, productItems, None)
      }

  }
}
