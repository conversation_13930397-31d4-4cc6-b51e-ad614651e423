package com.agoda.bapi.server.repository

import api.request.FeatureFlag._
import com.agoda.bapi.common.MockRequestContext
import com.agoda.bapi.common.config.KillSwitches
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.{BookingCreationContext, UserAgent}
import com.agoda.bapi.common.message.{DevicePlatform, ExperimentData, OccupancyRequest}
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.consumerFintech.ConsumerFintechRequirement
import com.agoda.bapi.common.model.consumerFintech.products.cancelAndRebookV3.CancelAndRebookRequirement
import com.agoda.bapi.common.reporting.logs.RequestContextMarker
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.token.Money
import com.agoda.bapi.creation.service.HadoopMessagingService
import com.agoda.bapi.server.proxy.PapiProxy
import com.agoda.bapi.server.reporting.logs.{PapiRepositoryLog, PapiRepositoryLogParams}
import com.agoda.bapi.server.repository.dto.papi.{PapiRequestDto, PriceParameter}
import com.agoda.property.client.ResponseWithJson
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import com.typesafe.scalalogging.Logger
import integration.mocks.DbProxyMock
import mocks.PropertyMock
import models.starfruit.PropertyPricingJsonProtocol.ResponseStateToken
import models.starfruit.{BookingRequest, PriceAdjustment, ReBookingRequest}
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers.{any, eq => eqTo}
import org.mockito.Mockito._
import org.mockito.{ArgumentCaptor, ArgumentMatchers => MockitoMatchers}
import org.scalatest.BeforeAndAfter
import org.scalatest.funsuite.AsyncFunSuite
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.{Logger => Underlying}
import request.{FeatureFlagRequest, PropertyContext, PropertyPricing, PropertyRequest}
import transformers._

import scala.collection.JavaConverters._
import scala.concurrent.Future
import scala.concurrent.duration._

class PapiRepositorySpec extends AsyncFunSuite with Matchers with MockitoSugar with BeforeAndAfter with PropertyMock {
  implicit private val requestContext: RequestContext = mock[RequestContext]
  implicit private val sessionId: String =
    "3nqw0gnbprc24oxeqrlkvzb4opqewfjkq3p0j"
  implicit private val bookingSessionId: String =
    "3nqw0gnbprc24oxeqrlkvzb4opqewfjkq3p0j-bookingSessionId"
  val papiRequestDto   = mock[PapiRequestDto]
  val priceParameter   = mock[PriceParameter]
  val featureAware     = mock[FeatureAware]
  val occupancyRequest = mock[OccupancyRequest]

  val dbProxy = new DbProxyMock {
    override def getMasterHotelIds(hotelIds: Seq[Int]): Future[Map[Int, Int]] =
      Future.successful(hotelIds.map(h => h -> (if (h % 2 == 0) h else h + 10)).toMap)
  }

  val propertyRequestContext = PropertyContext(
    searchId = "",
    locale = "",
    cid = 0,
    memberId = 0L,
    origin = "",
    platform = 0
  )
  val propertyRequest = PropertyRequest(propertyRequestContext)

  val propertyPricing = PropertyPricing(
    checkIn = DateTime.now(),
    lengthOfStay = 0,
    bookingDate = DateTime.now(),
    numberOfRooms = 0,
    numberOfAdults = 0,
    numberOfChildren = 0,
    isUserLoggedIn = false,
    enableOpaqueChannel = false,
    allowOverrideOccupancy = false,
    partnerLoyaltyProgramId = 0,
    storeFrontId = None,
    currency = "",
    ratePlanId = List.empty,
    isRPM2Included = false,
    isIncludeUsdAndLocalCurrency = false
  )

  when(requestContext.userContext).thenReturn(
    Some(
      UserContext(
        languageId = 1,
        requestOrigin = "th",
        currency = "thb",
        nationalityId = 0,
        experimentData = Some(
          ExperimentData(
            userId = "trackingCookieId",
            deviceTypeId = "WebDesktop",
            memberId = None,
            trafficGroup = None,
            cId = Some("100"),
            aId = Some("101"),
            serverName = None
          )
        ),
        memberId = Some(1)
      )
    )
  )

  when(requestContext.correlationId).thenReturn(Some("1234"))
  when(requestContext.clientId).thenReturn(20)
  when(requestContext.path).thenReturn("/create/booking")
  when(requestContext.getCorrelationId()).thenCallRealMethod()
  when(requestContext.getSiteId()).thenCallRealMethod()
  when(requestContext.getBookingSessionId()).thenCallRealMethod()
  when(requestContext.featureAware).thenReturn(Some(featureAware))
  when(requestContext.bookingCreationContext).thenReturn(
    Some(
      BookingCreationContext(
        bookingSessionId = Some(bookingSessionId),
        sessionId = sessionId,
        userAgent = UserAgent(
          origin = "origin",
          osName = "osName",
          osVersion = "osVersion",
          browserName = "browserName",
          browserLanguage = "browserLanguage",
          browserVersion = "browserVersion",
          browserSubVersion = "browserSubVersion",
          browserBuildNumber = "browserBuildNumber",
          deviceBrand = "deviceBrand",
          deviceModel = "deviceModel",
          deviceTypeId = 1,
          isMobile = Some(true),
          isTouch = Some(true),
          additionalInfo = Option("additionalInfo")
        )
      )
    )
  )

  val timeout                    = 2.second
  val papiProxyMock              = mock[PapiProxy]
  val loggerMock                 = mock[Underlying]
  val hadoopMessagingServiceMock = mock[HadoopMessagingService]
  val mockKillSwitches           = mock[KillSwitches]

  before {
    reset(papiProxyMock, papiRequestDto)
    when(papiRequestDto.to(any())) thenReturn propertyRequest
    when(papiRequestDto.priceParameter) thenReturn priceParameter
    when(priceParameter.currency) thenReturn "EUR"
    when(requestContext.whiteLabelInfo).thenReturn(
      WhiteLabelInfo(
        whiteLabelId = WhiteLabel.Agoda,
        feature = FeaturesConfiguration(),
        token = Some("RequestContext.whiteLabelInfo.token")
      )
    )
    when(loggerMock.isInfoEnabled(any())).thenReturn(true)
    when(featureAware.isAffiliateFeatureFlagEnabled).thenReturn(false)
    when(
      hadoopMessagingServiceMock.sendPapiRequestResponse(
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any()
      )
    ).thenReturn(Future.successful())
    when(mockKillSwitches.stopLoggingBapiPapiMessage).thenReturn(false)
  }

  test(
    "getPropertyForBooking should not override propertyRequest for dictatorLike when affiliate experiment is enabled"
  ) {
    val uid = java.util.UUID.randomUUID.toString
    val propertyRequestArgCaptor: ArgumentCaptor[PropertyRequest] =
      ArgumentCaptor.forClass(classOf[PropertyRequest])

    val pricing = propertyPricing.copy(featureFlag = Some(List.empty))

    when(featureAware.isAffiliateFeatureFlagEnabled).thenReturn(true)
    when(papiRequestDto.to(any())) thenReturn propertyRequest.copy(
      featureFlag = Some(FeatureFlagRequest(allowFilterMasterRoom = None)),
      pricing = Some(pricing)
    )

    withPapiRepository { papiRepository =>
      val properties = createMockProperties(
        createMockPropertyWithRoomUid(10, Some(uid))
      )

      when(papiProxyMock.getPropertyForBooking(propertyRequestArgCaptor.capture())(any()))
        .thenReturn(Future.successful(ResponseWithJson(properties, "")))

      papiRepository
        .getPropertyForBooking(
          papiRequestDto,
          Some(
            PapiRepositoryLogParams(
              sessionId = sessionId,
              deviceTypeId = Some(DevicePlatform.Unknown),
              hasPackageToken = false,
              hasCartToken = false
            )
          ),
          occupancyRequest
        )(
          requestContext
        )
        .map { property =>
          val featureFlag = propertyRequestArgCaptor.getValue.featureFlag
          val pricingFeatureFlag =
            propertyRequestArgCaptor.getValue.pricing.head.featureFlag.head
          assert(featureFlag.head.allowFilterMasterRoom.isEmpty)
          assert(pricingFeatureFlag.isEmpty)
        }
    }
  }

  test("getPropertyForBooking should return property") {
    val uid = java.util.UUID.randomUUID.toString
    when(papiRequestDto.roomIdentifier).thenReturn(uid)
    withPapiRepository { papiRepository =>
      val properties = createMockProperties(
        createMockPropertyWithRoomUid(10, Some(uid))
      )

      when(papiProxyMock.getPropertyForBooking(PapiRepository.dictatorLike(propertyRequest))(requestContext))
        .thenReturn(Future.successful(ResponseWithJson(properties, "")))

      papiRepository
        .getPropertyForBooking(
          papiRequestDto,
          Some(
            PapiRepositoryLogParams(
              sessionId = sessionId,
              deviceTypeId = Some(DevicePlatform.Unknown),
              hasPackageToken = false,
              hasCartToken = false
            )
          ),
          occupancyRequest
        )(
          requestContext
        )
        .map { property =>
          property.head.properties.head.property
          assert(property.head.propertyId == 10)
          assert(property.head.priceGuaranteeToken.isDefined)
        }
    }
  }

  test("getPropertyForBooking should call hadoop logging") {
    val uid = java.util.UUID.randomUUID.toString
    when(papiRequestDto.roomIdentifier).thenReturn(uid)
    withPapiRepository { papiRepository =>
      val properties = createMockProperties(
        createMockPropertyWithRoomUid(10, Some(uid))
      )

      val pricing = PropertyPricing(
        checkIn = DateTime.now(),
        lengthOfStay = 9,
        bookingDate = DateTime.now(),
        numberOfRooms = 8,
        numberOfAdults = 7,
        numberOfChildren = 6,
        isUserLoggedIn = false,
        enableOpaqueChannel = false,
        allowOverrideOccupancy = false,
        partnerLoyaltyProgramId = 5,
        currency = "",
        ratePlanId = List.empty,
        isRPM2Included = false,
        isIncludeUsdAndLocalCurrency = false,
        priceAdjustmentRequest = Some(
          List(
            PriceAdjustment(
              roomId = "roomUid",
              requestedPrice = 1.2,
              chargeType = 1,
              rateType = 2,
              applyType = "ApplyType",
              chargeOption = 3,
              surchargeId = None,
              requestCurrency = "USD"
            )
          )
        )
      )

      val testRequest = PropertyRequest(
        context = PropertyContext(searchId = "", locale = "", cid = 0, memberId = 0, origin = "", platform = 0),
        pricing = Some(pricing)
      )
      val enhancedPapiRequest = PapiRepository.dictatorLike(testRequest)

      when(papiRequestDto.to(any())) thenReturn testRequest
      when(papiProxyMock.getPropertyForBooking(enhancedPapiRequest)(requestContext))
        .thenReturn(Future.successful(ResponseWithJson(properties, "")))

      papiRepository
        .getPropertyForBooking(
          papiRequestDto,
          Some(
            PapiRepositoryLogParams(
              sessionId = sessionId,
              deviceTypeId = Some(DevicePlatform.Unknown),
              hasPackageToken = false,
              hasCartToken = false
            )
          ),
          occupancyRequest
        )(
          requestContext
        )
        .map { property =>
          verify(hadoopMessagingServiceMock)
            .sendPapiRequestResponse(
              eqTo("1234"),
              eqTo(Some(sessionId)),
              eqTo(bookingSessionId),
              eqTo(20),
              eqTo("/create/booking"),
              eqTo(Some(100)),
              eqTo(Some("101")),
              eqTo(Some("trackingCookieId")),
              eqTo("PapiBooking"),
              eqTo(Some(1.2)),
              eqTo(enhancedPapiRequest),
              any[Option[Property]]
            )
          succeed
        }
    }
  }

  test("getPropertyForBooking should return None with no properties from PAPI") {
    val uid = java.util.UUID.randomUUID.toString
    when(papiRequestDto.roomIdentifier).thenReturn(uid)
    withPapiRepository { papiRepository =>
      when(papiProxyMock.getPropertyForBooking(PapiRepository.dictatorLike(propertyRequest))(requestContext))
        .thenReturn(Future.successful(ResponseWithJson(Properties(Seq.empty, None), "")))

      papiRepository
        .getPropertyForBooking(
          papiRequestDto,
          Some(
            PapiRepositoryLogParams(
              sessionId = sessionId,
              deviceTypeId = Some(DevicePlatform.Unknown),
              hasPackageToken = false,
              hasCartToken = false
            )
          ),
          occupancyRequest
        )(
          requestContext
        )
        .map { property =>
          assert(property.isEmpty)
        }
    }
  }

  test("getPropertyForBooking should recover as None when PapiProxy failedI") {
    val uid = java.util.UUID.randomUUID.toString
    when(papiRequestDto.roomIdentifier).thenReturn(uid)
    withPapiRepository { papiRepository =>
      when(papiProxyMock.getPropertyForBooking(PapiRepository.dictatorLike(propertyRequest))(requestContext))
        .thenReturn(Future.failed(new Exception("Why does Papi not expose error models????")))

      papiRepository
        .getPropertyForBooking(
          papiRequestDto,
          Some(
            PapiRepositoryLogParams(
              sessionId = sessionId,
              deviceTypeId = Some(DevicePlatform.Unknown),
              hasPackageToken = false,
              hasCartToken = false
            )
          ),
          occupancyRequest
        )(
          requestContext
        )
        .map { property =>
          assert(property.isEmpty)
        }
    }
  }

  test("dictatorLike should return whiteLabelKey value from request context") {
    import request.PropertyContext

    when(requestContext.whiteLabelInfo).thenReturn(
      WhiteLabelInfo(
        whiteLabelId = WhiteLabel.Agoda,
        feature = FeaturesConfiguration(),
        token = Some("RequestContext.whiteLabelInfo.token")
      )
    )

    val testRequest = PropertyRequest(
      context = PropertyContext(searchId = "", locale = "", cid = 0, memberId = 0, origin = "", platform = 0)
    )
    val actualRequest = PapiRepository.dictatorLike(testRequest)
    assert(actualRequest.context.whiteLabelKey == Some("RequestContext.whiteLabelInfo.token"))
  }

  test("dictatorLike should return whiteLabelKey value from property request") {
    import request.PropertyContext

    when(requestContext.whiteLabelInfo).thenReturn(
      WhiteLabelInfo(
        whiteLabelId = WhiteLabel.Agoda,
        feature = FeaturesConfiguration(),
        token = Some("RequestContext.whiteLabelInfo.token")
      )
    )

    val testRequest = PropertyRequest(
      context = PropertyContext(
        searchId = "",
        locale = "",
        cid = 0,
        memberId = 0,
        origin = "",
        platform = 0,
        whiteLabelKey = Some("PropertyRequest.context.whiteLabelKey")
      )
    )
    val actualRequest = PapiRepository.dictatorLike(testRequest)
    assert(actualRequest.context.whiteLabelKey == Some("PropertyRequest.context.whiteLabelKey"))
  }

  test("dictatorLike should return allowFilterMasterRoom true when no featureFlag") {
    import request.PropertyContext

    val testRequest =
      PropertyRequest(
        context = PropertyContext(searchId = "", locale = "", cid = 0, memberId = 0, origin = "", platform = 0),
        featureFlag = None
      )
    val actualRequest = PapiRepository.dictatorLike(testRequest)
    assert(actualRequest.featureFlag.get.allowFilterMasterRoom == Some(true))
  }

  test("dictatorLike should return allowFilterMasterRoom true when allowFilterMasterRoom false") {
    import request.PropertyContext

    val testRequest = PropertyRequest(
      context = PropertyContext(searchId = "", locale = "", cid = 0, memberId = 0, origin = "", platform = 0),
      featureFlag = Some(FeatureFlagRequest(allowFilterMasterRoom = Some(false)))
    )
    val actualRequest = PapiRepository.dictatorLike(testRequest)
    assert(actualRequest.featureFlag.get.allowFilterMasterRoom == Some(true))
  }

  test("dictatorLike should return allowFilterMasterRoom true when no allowFilterMasterRoom") {
    import request.PropertyContext

    val testRequest = PropertyRequest(
      context = PropertyContext(searchId = "", locale = "", cid = 0, memberId = 0, origin = "", platform = 0),
      featureFlag = Some(FeatureFlagRequest(allowFilterMasterRoom = None))
    )
    val actualRequest = PapiRepository.dictatorLike(testRequest)
    assert(actualRequest.featureFlag.get.allowFilterMasterRoom == Some(true))
  }

  test("dictatorLike should return pricing with MultipleExtraBeds when no featureFlag") {
    import request.PropertyContext
    val pricing = propertyPricing

    val testRequest = PropertyRequest(
      context = PropertyContext(searchId = "", locale = "", cid = 0, memberId = 0, origin = "", platform = 0),
      pricing = Some(pricing)
    )
    val actualRequest = PapiRepository.dictatorLike(testRequest)
    assert(actualRequest.pricing.get.featureFlag.get.contains(MultipleExtraBeds))
  }

  test("enrichFeatureFlagsForPriceFreeze should return pricing with AlternativeRoom flag") {
    import request.PropertyContext

    val pricing = propertyPricing
    val testRequest = PropertyRequest(
      context = PropertyContext(searchId = "", locale = "", cid = 0, memberId = 0, origin = "", platform = 0),
      pricing = Some(pricing)
    )
    val actualRequest = PapiRepository.enrichFeatureFlagsForPriceFreeze(testRequest)

    actualRequest.pricing.get.featureFlag.get shouldBe Seq(AlternativeRoom, PriceFreezeExercise)
  }

  test("enrichFeatureFlagsForPriceFreeze should return PropertyRequest without upsell and cross-sell flags") {
    val pricing = propertyPricing.copy(featureFlag =
      Some(
        List(
          Standard,
          MobileGiftcard,
          Coupon,
          ClientDiscount,
          CrossSell,
          APSPeek,
          AutoApplyPromos,
          ChildAge,
          DomesticTaxReceipt,
          MixAndSave,
          MixAndSaveSoldOutOnPP,
          VipPlatinum,
          StackChannelDiscount,
          PromotionEligibleFromClient,
          EnableCashback,
          BreakfastUpsell
        )
      )
    )
    val expectedFeatureFlag = List(
      Standard,
      MobileGiftcard,
      Coupon,
      ClientDiscount,
      APSPeek,
      AutoApplyPromos,
      ChildAge,
      DomesticTaxReceipt,
      MixAndSave,
      MixAndSaveSoldOutOnPP,
      VipPlatinum,
      StackChannelDiscount,
      PromotionEligibleFromClient,
      EnableCashback
    )
    val testRequest = PropertyRequest(
      context = propertyRequestContext,
      pricing = Some(pricing)
    )

    val actualRequest = PapiRepository.enrichFeatureFlagsForPriceFreeze(testRequest)
    actualRequest.pricing.get.featureFlag.get.containsSlice(expectedFeatureFlag) shouldBe true
    actualRequest.pricing.get.featureFlag.get.contains(CrossSell) shouldBe false
    actualRequest.pricing.get.featureFlag.get.contains(BreakfastUpsell) shouldBe false
    actualRequest.pricing.get.featureFlag.get.contains(UpsellRoomUpgrade) shouldBe false
  }

  test(
    "dictatorLike should return pricing with 1 MultipleExtraBeds and 1 Coupon when featureFlag contains MultipleExtraBeds and Coupon"
  ) {
    import request.PropertyContext

    val pricing = propertyPricing.copy(featureFlag = Some(List(MultipleExtraBeds, Coupon)))

    val testRequest = PropertyRequest(
      context = PropertyContext(searchId = "", locale = "", cid = 0, memberId = 0, origin = "", platform = 0),
      pricing = Some(pricing)
    )
    val actualRequest = PapiRepository.dictatorLike(testRequest)
    assert(
      actualRequest.pricing.get.featureFlag.get.count(f => f.i == MultipleExtraBeds.i) == 1
    )
    assert(
      actualRequest.pricing.get.featureFlag.get.count(f => f.i == Coupon.i) == 1
    )
  }

  test("PapiRepositorySpec should log when papi return property") {
    clearInvocations(loggerMock)
    val uid = java.util.UUID.randomUUID.toString
    when(papiRequestDto.roomIdentifier).thenReturn(uid)
    val contextMarkerCapture: ArgumentCaptor[RequestContextMarker] =
      ArgumentCaptor.forClass(classOf[RequestContextMarker])
    withPapiRepository { papiRepository =>
      val properties = createMockProperties(
        createMockPropertyWithRoomUid(10, Some(uid), Some(uid))
      )

      when(papiProxyMock.getPropertyForBooking(PapiRepository.dictatorLike(propertyRequest))(requestContext))
        .thenReturn(Future.successful(ResponseWithJson(properties, "")))

      val expectedPropertyRequestLog =
        PapiRepository.dictatorLike(propertyRequest)

      val expectedLog = PapiRepositoryLog(
        responseStatus = Some("Papi success"),
        correlationId = Some("1234"),
        sessionId = sessionId,
        bookingFlowType = None,
        request = expectedPropertyRequestLog,
        response = Some(properties),
        deviceType = DevicePlatform.WebPhone,
        hasPackageToken = false,
        hasCartToken = false,
        masterRoomExists = true,
        roomSwappingExists = false
      )

      papiRepository
        .getPropertyForBooking(
          papiRequestDto,
          Some(
            PapiRepositoryLogParams(
              sessionId = sessionId,
              deviceTypeId = Some(DevicePlatform.WebPhone),
              hasPackageToken = false,
              hasCartToken = false
            )
          ),
          occupancyRequest
        )(
          requestContext
        )
        .map { property =>
          verify(loggerMock).info(
            contextMarkerCapture.capture(),
            MockitoMatchers.eq("[PropertyAPI]"),
            MockitoMatchers.eq(null)
          )
          expectedLog.stringTags.asScala should contain theSameElementsAs contextMarkerCapture.getValue.stringTags.asScala
          assert(property.head.propertyId == 10)
          assert(property.head.priceGuaranteeToken.isDefined)

        }
    }
  }

  test("PapiRepositorySpec should log when papi return property with no master room") {
    clearInvocations(loggerMock)
    val uid = java.util.UUID.randomUUID.toString
    val contextMarkerCapture: ArgumentCaptor[RequestContextMarker] =
      ArgumentCaptor.forClass(classOf[RequestContextMarker])
    when(papiRequestDto.roomIdentifier).thenReturn(uid)

    withPapiRepository { papiRepository =>
      val properties = mock[Properties]
      val property   = mock[Property]

      when(papiProxyMock.getPropertyForBooking(PapiRepository.dictatorLike(propertyRequest))(requestContext))
        .thenReturn(Future.successful(ResponseWithJson(properties, "")))

      when(properties.dfMetaResult).thenReturn(DFMetaResult(Option(ResponseStateToken("lakjdf;oa84", false))))
      when(properties.property).thenReturn(Seq(property))
      when(property.masterRooms).thenReturn(Seq.empty)
      when(property.roomSwapping).thenReturn(List.empty)

      val expectedPropertyRequestLog =
        PapiRepository.dictatorLike(propertyRequest)

      val expectedLog =
        PapiRepositoryLog(
          responseStatus = Some("No Master Rooms"),
          correlationId = Some("1234"),
          sessionId = sessionId,
          bookingFlowType = None,
          request = expectedPropertyRequestLog,
          response = Some(properties),
          deviceType = DevicePlatform.WebPhone,
          hasPackageToken = false,
          hasCartToken = false,
          masterRoomExists = false,
          roomSwappingExists = false
        )

      papiRepository
        .getPropertyForBooking(
          papiRequestDto,
          Some(
            PapiRepositoryLogParams(
              sessionId = sessionId,
              deviceTypeId = Some(DevicePlatform.WebPhone),
              hasPackageToken = false,
              hasCartToken = false
            )
          ),
          occupancyRequest
        )(
          requestContext
        )
        .map { property =>
          verify(loggerMock).info(
            contextMarkerCapture.capture(),
            MockitoMatchers.eq("[PropertyAPI]"),
            MockitoMatchers.eq(null)
          )
          expectedLog.stringTags.asScala should contain theSameElementsAs contextMarkerCapture.getValue.stringTags.asScala
          assert(property.head.properties.get.property.head.masterRooms.isEmpty)
        }
    }
  }

  test("PapiRepositorySpec should log when papi return property with no child room") {
    clearInvocations(loggerMock)
    val uid = java.util.UUID.randomUUID.toString
    val contextMarkerCapture: ArgumentCaptor[RequestContextMarker] =
      ArgumentCaptor.forClass(classOf[RequestContextMarker])
    when(papiRequestDto.roomIdentifier).thenReturn(uid)

    withPapiRepository { papiRepository =>
      val properties = mock[Properties]
      val property   = mock[Property]
      val masterRoom = mock[EnrichedMasterRoom]

      when(papiProxyMock.getPropertyForBooking(PapiRepository.dictatorLike(propertyRequest))(requestContext))
        .thenReturn(Future.successful(ResponseWithJson(properties, "")))

      when(properties.dfMetaResult).thenReturn(DFMetaResult(Option(ResponseStateToken("lakjdf;oa84", false))))
      when(properties.property).thenReturn(Seq(property))
      when(property.masterRooms).thenReturn(Seq(masterRoom))
      when(property.roomSwapping).thenReturn(List.empty)
      when(masterRoom.childrenRooms).thenReturn(List.empty)

      val expectedPropertyRequestLog =
        PapiRepository.dictatorLike(propertyRequest)

      val expectedLog = PapiRepositoryLog(
        responseStatus = Some("No Child Room"),
        correlationId = Some("1234"),
        sessionId = sessionId,
        bookingFlowType = None,
        request = expectedPropertyRequestLog,
        response = Some(properties),
        deviceType = DevicePlatform.WebPhone,
        hasPackageToken = false,
        hasCartToken = false,
        masterRoomExists = true,
        roomSwappingExists = false
      )

      papiRepository
        .getPropertyForBooking(
          papiRequestDto,
          Some(
            PapiRepositoryLogParams(
              sessionId = sessionId,
              deviceTypeId = Some(DevicePlatform.WebPhone),
              hasPackageToken = false,
              hasCartToken = false
            )
          ),
          occupancyRequest
        )(
          requestContext
        )
        .map { property =>
          verify(loggerMock).info(
            contextMarkerCapture.capture(),
            MockitoMatchers.eq("[PropertyAPI]"),
            MockitoMatchers.eq(null)
          )
          expectedLog.stringTags.asScala should contain theSameElementsAs contextMarkerCapture.getValue.stringTags.asScala
          assert(property.head.properties.get.property.head.masterRooms.head.childrenRooms.isEmpty)
        }
    }
  }

  test("PapiRepositorySpec should log when papi there is no property") {
    clearInvocations(loggerMock)
    val uid = java.util.UUID.randomUUID.toString
    val contextMarkerCapture: ArgumentCaptor[RequestContextMarker] =
      ArgumentCaptor.forClass(classOf[RequestContextMarker])
    when(papiRequestDto.roomIdentifier).thenReturn(uid)

    withPapiRepository { papiRepository =>
      when(papiProxyMock.getPropertyForBooking(PapiRepository.dictatorLike(propertyRequest))(requestContext))
        .thenReturn(Future.successful(ResponseWithJson(Properties(Seq.empty, None), "")))

      val expectedPropertyRequestLog =
        PapiRepository.dictatorLike(propertyRequest)

      val expectedLog = PapiRepositoryLog(
        responseStatus = Some(s"No Property for master hotel id of 0"),
        correlationId = Some("1234"),
        sessionId = sessionId,
        bookingFlowType = None,
        request = expectedPropertyRequestLog,
        response = Some(Properties(Seq.empty, None)),
        deviceType = DevicePlatform.WebPhone,
        hasPackageToken = false,
        hasCartToken = false,
        masterRoomExists = false,
        roomSwappingExists = false
      )

      papiRepository
        .getPropertyForBooking(
          papiRequestDto,
          Some(
            PapiRepositoryLogParams(
              sessionId = sessionId,
              deviceTypeId = Some(DevicePlatform.WebPhone),
              hasPackageToken = false,
              hasCartToken = false
            )
          ),
          occupancyRequest
        )(
          requestContext
        )
        .map { property =>
          verify(loggerMock).info(
            contextMarkerCapture.capture(),
            MockitoMatchers.eq("[PropertyAPI]"),
            MockitoMatchers.eq(null)
          )
          expectedLog.stringTags.asScala should contain theSameElementsAs contextMarkerCapture.getValue.stringTags.asScala
          assert(property.isEmpty)
        }
    }
  }

  test("dictatorLike should return ClientDiscount true") {
    import request.PropertyContext

    val testRequest =
      PropertyRequest(
        context = PropertyContext(searchId = "", locale = "", cid = 0, memberId = 0, origin = "", platform = 0),
        featureFlag = None,
        pricing = Some(propertyPricing)
      )
    val actualRequest = PapiRepository.dictatorLike(testRequest)
    assert(actualRequest.pricing.get.featureFlag.get.contains(ClientDiscount))
  }

  test("getPapiFeatureFlags should return NONE when payment option is none") {
    val testRequest =
      PropertyRequest(
        context = PropertyContext(searchId = "", locale = "", cid = 0, memberId = 0, origin = "", platform = 0),
        featureFlag = None,
        pricing = Some(
          propertyPricing.copy(paymentRequest =
            Some(request.PaymentRequest(paymentOption = Some(ChargeOption.None.id)))
          )
        )
      )
    val actual = PapiRepository.getPapiFeatureFlags(testRequest)
    assert(actual == None)
  }

  test("getPapiFeatureFlags should return None when payment is payNow") {
    val testRequest =
      PropertyRequest(
        context = PropertyContext(searchId = "", locale = "", cid = 0, memberId = 0, origin = "", platform = 0),
        featureFlag = None,
        pricing = Some(
          propertyPricing.copy(paymentRequest =
            Some(request.PaymentRequest(paymentOption = Some(ChargeOption.PayNow.id)))
          )
        )
      )
    val actual = PapiRepository.getPapiFeatureFlags(testRequest)
    assert(actual == None)
  }

  test("getPapiFeatureFlags should return M150 feature flag when payment is payLater") {
    val testRequest =
      PropertyRequest(
        context = PropertyContext(searchId = "", locale = "", cid = 0, memberId = 0, origin = "", platform = 0),
        featureFlag = None,
        pricing = Some(
          propertyPricing.copy(paymentRequest =
            Some(request.PaymentRequest(paymentOption = Some(ChargeOption.PayLater.id)))
          )
        )
      )
    val actual = PapiRepository.getPapiFeatureFlags(testRequest)
    assert(actual == Some(Map("M150" -> true)))
  }

  test("enrichSalePriceForCancelAndRebookV3 should return PropertyRequest with new ReBookingRequest") {
    import request.PropertyContext

    val consumerFintechRequirement = Some(
      ConsumerFintechRequirement(
        cancelAndRebookRequirement = Some(
          CancelAndRebookRequirement(
            originalBookingId = 11111,
            originalItineraryId = 22222,
            saleInclusive = Money(BigDecimal(100), "USD"),
            roomTypeId = 1,
            originalAgodaCash = Some(1.2f),
            originalCashback = Some(1.3f),
            originalPromoAmount = Some(1.4f),
            originalSellIn = None,
            originalUsdToRequestExchangeRate = Some(20.0f),
            originalSellInUsd = Some(1.5f)
          )
        )
      )
    )
    val reBookingRequest = ReBookingRequest(
      roomTypeId = 1,
      masterRoomTypeId = None,
      customerPaidPrice = 80,
      originalNetIn = Some(40),
      originalCashback = None,
      originalPromoAmount = Some(1.4f),
      originalUsdToRequestExchangeRate = Some(20.0f),
      originalSellInUsd = None
    )
    val testRequest = PropertyRequest(
      context = PropertyContext(searchId = "", locale = "", cid = 0, memberId = 0, origin = "", platform = 0),
      booking = Some(BookingRequest(reBookingRequest = Some(reBookingRequest))),
      pricing = Some(propertyPricing)
    )

    val expectedReBookingRequest = ReBookingRequest(
      roomTypeId = 1,
      masterRoomTypeId = None,
      customerPaidPrice = 100,
      originalNetIn = None,
      originalCashback = Some(1.3f),
      originalPromoAmount = Some(1.4f),
      originalUsdToRequestExchangeRate = Some(20.0f),
      originalSellInUsd = Some(1.5f)
    )
    val expectedRequest =
      testRequest.copy(
        booking = testRequest.booking.map(_.copy(reBookingRequest = Some(expectedReBookingRequest))),
        pricing = Some(propertyPricing.copy(currency = "USD"))
      )
    val actualRequest = PapiRepository.enrichSalePriceForCancelAndRebookV3(testRequest, consumerFintechRequirement)

    actualRequest.booking.get.reBookingRequest.get shouldBe expectedReBookingRequest
    actualRequest shouldBe expectedRequest
  }

  test("enrichSalePriceForCancelAndRebookV3 should return PropertyRequest with ReBookingRequest if not exists") {
    import request.PropertyContext

    val consumerFintechRequirement =
      Some(
        ConsumerFintechRequirement(
          Some(
            CancelAndRebookRequirement(
              originalBookingId = 11111,
              originalItineraryId = 22222,
              saleInclusive = Money(100, "USD"),
              roomTypeId = 1,
              originalAgodaCash = Some(1.2f),
              originalCashback = Some(1.3f),
              originalPromoAmount = Some(1.4f),
              originalSellIn = None,
              originalUsdToRequestExchangeRate = None,
              originalSellInUsd = Some(1.5f)
            )
          )
        )
      )
    val testRequest = PropertyRequest(
      context = PropertyContext(searchId = "", locale = "", cid = 0, memberId = 0, origin = "", platform = 0),
      booking = Some(BookingRequest(reBookingRequest = None)),
      pricing = Some(propertyPricing)
    )

    val expectedReBookingRequest =
      ReBookingRequest(
        roomTypeId = 1,
        masterRoomTypeId = None,
        customerPaidPrice = 100,
        originalNetIn = None,
        originalCashback = Some(1.3f),
        originalPromoAmount = Some(1.4f),
        originalUsdToRequestExchangeRate = None,
        originalSellInUsd = Some(1.5f)
      )
    val expectedRequest =
      testRequest.copy(
        booking = testRequest.booking.map(_.copy(reBookingRequest = Some(expectedReBookingRequest))),
        pricing = Some(propertyPricing.copy(currency = "USD"))
      )
    val actualRequest = PapiRepository.enrichSalePriceForCancelAndRebookV3(testRequest, consumerFintechRequirement)

    actualRequest.booking.get.reBookingRequest.get shouldBe expectedReBookingRequest
    actualRequest shouldBe expectedRequest
  }

  test(
    "enrichSalePriceForCancelAndRebookV3 should return same PropertyRequest if no consumerFintechRequirement present"
  ) {
    import request.PropertyContext

    val consumerFintechRequirement = None
    val testRequest = PropertyRequest(
      context = PropertyContext(searchId = "", locale = "", cid = 0, memberId = 0, origin = "", platform = 0)
    )

    val actualRequest = PapiRepository.enrichSalePriceForCancelAndRebookV3(testRequest, consumerFintechRequirement)

    actualRequest shouldBe testRequest
  }

  test("enrichSalePriceForCancelAndRebookV3 should return same PropertyRequest if no PricingRequest present") {
    import request.PropertyContext

    val consumerFintechRequirement =
      Some(
        ConsumerFintechRequirement(
          Some(
            CancelAndRebookRequirement(
              originalBookingId = 11111,
              originalItineraryId = 22222,
              saleInclusive = Money(100, "USD"),
              roomTypeId = 1,
              originalAgodaCash = Some(1.2f),
              originalCashback = Some(1.3f),
              originalPromoAmount = Some(1.4f),
              originalSellIn = None,
              originalUsdToRequestExchangeRate = None,
              originalSellInUsd = None
            )
          )
        )
      )
    val reBookingRequest = ReBookingRequest(
      roomTypeId = 1,
      masterRoomTypeId = None,
      customerPaidPrice = 80,
      originalNetIn = Some(40),
      originalCashback = None,
      originalPromoAmount = None,
      originalUsdToRequestExchangeRate = None,
      originalSellInUsd = None
    )
    val testRequest = PropertyRequest(
      context = PropertyContext(searchId = "", locale = "", cid = 0, memberId = 0, origin = "", platform = 0),
      booking = Some(BookingRequest(reBookingRequest = Some(reBookingRequest))),
      pricing = None
    )

    val actualRequest = PapiRepository.enrichSalePriceForCancelAndRebookV3(testRequest, consumerFintechRequirement)

    actualRequest shouldBe testRequest
  }

  def withPapiRepository[T](block: PapiRepository => T): T = {

    val papiRepository = new PapiRepositoryImpl(
      papiProxyMock,
      dbProxy,
      hadoopMessagingServiceMock,
      mockKillSwitches
    ) {
      override protected def updatePapiRequestDtoWithMasterHotelId(
          papiRequestDto: PapiRequestDto,
          masterHotelId: Long
      ): PapiRequestDto = papiRequestDto

      override def logger: Logger =
        com.typesafe.scalalogging.Logger(loggerMock)
    }
    block(papiRepository)
  }

  def createMockProperties(propertyList: Property*): Properties = {
    val properties = mock[Properties]
    when(properties.dfMetaResult).thenReturn(DFMetaResult(Option(ResponseStateToken("lakjdf;oa84", false))))
    when(properties.property).thenReturn(propertyList)

    properties
  }
}
