package com.agoda.bapi.server.proxy.protection

import com.agoda.ancillary.client.AncillaryApiClient
import com.agoda.ancillary.client.config.{AncillaryApiConfig => AncillaryClientConfig}
import com.agoda.ancillary.clientV2.api.AncillaryApi
import com.agoda.ancillary.clientV2.model._
import com.agoda.bapi.common.WithProxyMessageTestMock
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.message.{DevicePlatform, ExperimentDataMock}
import com.agoda.bapi.common.model.car.{BookingDetailsPayment, BookingDetailsPriceBreakdowns, CarBookingDetails, CarConfirmationData}
import com.agoda.bapi.common.model.flight.Flight.PaxType
import com.agoda.bapi.common.model.product.{BookingFlow, ProductTypeEnum}
import com.agoda.bapi.common.model.protection.ProtectionRequestItemOptInValue
import com.agoda.bapi.common.model.{CurrencyOption, UserContextMock, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.proxy.DependencyNames
import com.agoda.bapi.common.service.{FeatureAware, MessagesBag}
import com.agoda.bapi.common.token.flight.FlightToken
import com.agoda.bapi.creation.service.observability.LogContext
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.pricebreakdown.cars.MockCarPricingData
import com.agoda.bapi.server.model.pricebreakdown.flights.MockFlightsPricingData.chargeTotal
import com.agoda.bapi.server.model.{FlightConfirmationData, FlightItinerary}
import com.agoda.bapi.server.proxy.ancillary.request.{AncillaryCarMapper, AncillaryFlightMapper}
import com.agoda.bapi.server.proxy.protection.request.ProtectionQuoteRequestV1ToV2
import com.agoda.bapi.server.proxy.protection.response.ProtectionQuoteResponseV2ToV1
import com.agoda.bapi.server.service.AncillaryApiV2Provider
import com.agoda.flights.awo.repository.DefaultEncryptionKeyRepository
import com.agoda.flights.awo.{FlightsTokenService, SecureFlightTokenizer}
import com.agoda.flights.client.v2.model.{SearchResponseCurrencyPricing, SearchResponseGenericSeatAssignCost, SearchResponseSegment, SearchResponseSlice}
import com.agoda.flights.proto.workflow.{FlightInfo => FlightInfoAWO, FlightItemBreakdown => FlightItemBreakdownAWO, FlightToken => FlightTokenAWO, PaymentModel => PaymentModelAWO, Segment => SegmentAWO, Slice => SliceAWO}
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import com.typesafe.config.ConfigFactory
import com.typesafe.scalalogging.Logger
import io.netty.handler.ssl.SslContext
import mocks.RequestContextMock
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{clearInvocations, reset, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.TableDrivenPropertyChecks._
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.{Logger => Underlying}

import java.time.format.DateTimeFormatter
import java.time.{LocalDate, LocalDateTime, ZoneOffset}
import scala.concurrent.Future

class ProtectionProxyV2Test
    extends AsyncWordSpec
    with WithProxyMessageTestMock
    with Matchers
    with MockitoSugar
    with BeforeAndAfter
    with RequestContextMock {

  val featureAwareMock: FeatureAware = mock[FeatureAware]
  val messagesBag                    = mock[MessagesBag]
  val ancillaryClientMock            = mock[AncillaryApi[Future]]

  val clientV2 = new AncillaryApiV2Provider(
    ConfigFactory.parseString(
      """
        |ag-observability.tracing.features {
        |  enabled = false
        |  included = []
        |  excluded = []
        |  exporters = []
        |}
        |
        |ag-http-client.client {
        |  services {
        |    ancillary-api {
        |      individual-timeout = 5 seconds
        |    }
        |  }
        |}
        |
        |ag-http-client.mesh {
        |  default {}
        |  services {
        |    ancillary-api {
        |      discovery {
        |        method = "static"
        |        static {
        |          hosts = ["ancillary"]
        |        }
        |      }
        |      request {
        |        max-attempts = 1 // no retry
        |      }
        |      round-robin {
        |        method = "simple"
        |      }
        |    }
        |  }
        |}
        |""".stripMargin
    )
  ).get()

  val clientConfig = new AncillaryClientConfig(
    maxRetries = 0,
    backOffMillis = 0
  )
  val sslContext      = mock[SslContext]
  val ancillaryClient = new AncillaryApiClient(List(""), sslContext, Some(clientConfig))
  val protectionProxy = new ProtectionProxyImpl(ancillaryClient, messagingService) {
    override def logger: Logger = com.typesafe.scalalogging.Logger(loggerMock)
  }

  val protectionProxyV2 = new MockProtectionProxyV2(Map.empty, "", clientV2)

  val loggerMock = mock[Underlying]
  val userContext = Some(
    UserContextMock.value.copy(requestOrigin = "TH", experimentData = Option(ExperimentDataMock.protectionValue))
  )

  implicit val context = requestContext(messagesBag, userContext).copy(featureAware = Some(featureAwareMock))

  implicit val setupBookingContext = SetupBookingContext(
    BookingFlow.SingleFlight,
    context,
    "contextCorrelationId",
    WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration()),
    None,
    sessionId = "",
    bookingSessionId = "",
    logContext = LogContext()
  )

  class MockProtectionProxyV2(
      expectedTags: Map[String, String],
      expectedMetricName: String,
      client: AncillaryApi[Future]
  ) extends ProtectionProxyV2Impl(client, messagingService) {
    override def logger: Logger = com.typesafe.scalalogging.Logger(loggerMock)

    override def withMeasureAndLog[T](metricName: String, tags: Map[String, String])(f: Future[T]): Future[T] = {
      metricName shouldBe expectedMetricName
      tags shouldBe expectedTags
      f
    }

    override def withMeasureAndIncrement(tags: Map[String, String], suffix: Option[String]): Future[Unit] = {
      suffix shouldBe Some("error")
      tags shouldBe expectedTags
      Future.successful()
    }
  }

  class MockProtectionMappingProxy extends ProtectionMappingProxyImpl(AncillaryFlightMapper, AncillaryCarMapper)

  val protectionMappingProxy = new MockProtectionMappingProxy
  val secureTokenizer        = new SecureFlightTokenizer()
  val keyRepository          = new DefaultEncryptionKeyRepository()
  val tokenizer              = new FlightsTokenService(secureTokenizer, keyRepository)

  before {
    reset(messagesBag)
    reset(ancillaryClientMock)
    when(loggerMock.isInfoEnabled).thenReturn(true)
    clearInvocations(loggerMock)
  }

  "ProtectionProxy" should {
    "map protection request correctly for Vehicle" in {
      val protectionProxy     = new MockProtectionProxyV2(Map.empty, "", ancillaryClientMock)
      val setupBookingRequest = getSetupBookingRequestForVehicle()
      val quoteRequestResult = protectionMappingProxy
        .mapProtectionRequest(setupBookingRequest, None, Option(mockCarConfirmationData), "THB")
        .map(
          ProtectionQuoteRequestV1ToV2.mapQuoteRequestV1ToV2
        )

      verifyQuoteRequestForVehicle(quoteRequestResult.head)
    }

    "map protection response with non-empty tripProtections correctly" in {
      val testCases = Table(
        ("requestedOptInVal", "expectedSelectedOptInVal"),
        (ProtectionRequestItemOptInValue.None, ProtectionRequestItemOptInValue.None),
        (ProtectionRequestItemOptInValue.Purchase, ProtectionRequestItemOptInValue.Purchase),
        (ProtectionRequestItemOptInValue.Decline, ProtectionRequestItemOptInValue.Decline)
      )
      forAll(testCases) { (requestedOptInVal, expectedSelectedOptInVal) =>
        val tripProtectionTypes = Table("type", Some(1), Some(2))
        forAll(tripProtectionTypes) { tripProtectionType =>
          {
            val protectionProxy   = new MockProtectionProxyV2(Map.empty, "", ancillaryClientMock)
            val mockQuoteResponse = getMockQuoteResponse(tripProtectionType)
            val tripProtectionProductItemResult =
              protectionMappingProxy.mapProtectionResponse(
                request = getSetupBookingRequest(
                  requestedOptInValue = requestedOptInVal
                ),
                tp = mockQuoteResponse.tripProtections.get.head,
                resp = mockQuoteResponse,
                protectionBookingToken = ""
              )

            assert(tripProtectionProductItemResult.tripProtectionData.nonEmpty)
            assert(tripProtectionProductItemResult.content == "Success")
            assert(tripProtectionProductItemResult.tripProtectionData.get.protectionBookingToken.isEmpty)
            assert(tripProtectionProductItemResult.enabledFeatures == Seq("Feature"))

            tripProtectionProductItemResult.tripProtectionData.map { t =>
              {
                t.disclaimer shouldBe mockQuoteResponse.tripProtections.get.head.tripProtectionData.disclaimer
                t.tripProtectionType shouldBe mockQuoteResponse.tripProtections.get.head.tripProtectionType.get
                t.solicitation.optionButtons.get.accept shouldBe mockQuoteResponse.tripProtections.get.head.optionButton.accept
                t.solicitation.optionButtons.get.decline shouldBe mockQuoteResponse.tripProtections.get.head.optionButton.decline
                t.solicitation.breakDown.map(
                  _.header
                ) shouldBe mockQuoteResponse.tripProtections.get.head.tripProtectionData.solicitation.header
                t.solicitation.breakDown.map(
                  _.subHeader
                ) shouldBe mockQuoteResponse.tripProtections.get.head.tripProtectionData.solicitation.subHeader
                t.solicitation.breakDown.map(
                  _.body
                ) shouldBe mockQuoteResponse.tripProtections.get.head.tripProtectionData.solicitation.body
                t.solicitation.breakDown.map(
                  _.footer
                ) shouldBe mockQuoteResponse.tripProtections.get.head.tripProtectionData.solicitation.footer
                t.solicitation.breakDown.map(
                  _.callToAction
                ) shouldBe mockQuoteResponse.tripProtections.get.head.tripProtectionData.solicitation.callToAction
                t.solicitation.breakDown.get.options.purchase shouldBe mockQuoteResponse.tripProtections.get.head.tripProtectionData.solicitation.options.get.purchase
                t.solicitation.breakDown.get.options.decline shouldBe mockQuoteResponse.tripProtections.get.head.tripProtectionData.solicitation.options.get.decline
                t.solicitation.breakDown
                  .map(_.terms.get.text)
                  .getOrElse(
                    ""
                  ) shouldBe mockQuoteResponse.tripProtections.get.head.tripProtectionData.solicitation.terms.get.text
                t.solicitation.breakDown
                  .map(_.terms.get.url.text)
                  .getOrElse(
                    ""
                  ) shouldBe mockQuoteResponse.tripProtections.get.head.tripProtectionData.solicitation.terms.get.url.text
                t.solicitation.breakDown
                  .map(_.terms.get.url.link)
                  .getOrElse(
                    ""
                  ) shouldBe mockQuoteResponse.tripProtections.get.head.tripProtectionData.solicitation.terms.get.url.link
                t.solicitation.contactNumber shouldBe mockQuoteResponse.tripProtections.get.head.tripProtectionData.solicitation.contactNumber
                t.price.get.amount shouldBe mockQuoteResponse.tripProtections.get.head.tripProtectionData.price.amount
                t.price.get.currencyCode shouldBe mockQuoteResponse.tripProtections.get.head.tripProtectionData.price.currencyCode
                t.solicitation.offerHtml shouldBe mockQuoteResponse.tripProtections.get.head.tripProtectionData.solicitation.offerHtml
                t.subSupplierId shouldBe 40002
                t.paymentModel shouldBe PaymentModel.Agency.id
                t.supplierAmount shouldBe None
                t.solicitation.breakDown.flatMap(_.shortTerms) shouldBe mockQuoteResponse.tripProtections.flatMap(
                  _.headOption.flatMap(_.tripProtectionData.solicitation.shortTerms)
                )
                t.solicitation.breakDown.flatMap(_.options.purchaseInfo) shouldBe mockQuoteResponse.tripProtections
                  .flatMap(
                    _.headOption.flatMap(_.tripProtectionData.solicitation.options.flatMap(_.purchaseInfo))
                  )
                t.solicitation.breakDown.flatMap(_.options.declineInfo) shouldBe mockQuoteResponse.tripProtections
                  .flatMap(
                    _.headOption.flatMap(_.tripProtectionData.solicitation.options.flatMap(_.declineInfo))
                  )
                t.agePolicy.minDate shouldBe mockQuoteResponse.tripProtections.get.head.agePolicy.minAgePolicyInfo.dateOfBirth
                t.agePolicy.maxDate shouldBe mockQuoteResponse.tripProtections.get.head.agePolicy.maxAgePolicyInfo.dateOfBirth
                t.selectedOptInValue shouldBe expectedSelectedOptInVal
                t.displayPriceBreakdowns should not be empty
                t.displayPriceBreakdowns should have size 2
                t.displayPriceBreakdowns.head.price shouldBe CurrencyOption(100, "USD")
                t.displayPriceBreakdowns.head.breakdowns.map(_ should have size 1).get
                t.displayPriceBreakdowns(1).price shouldBe CurrencyOption(200, "USD")
                t.displayPriceBreakdowns(1).breakdowns.map(_ shouldBe empty).get
              }
            }.get
          }
        }
      }
    }

    "map protection response solicitation version correctly" in {
      val protectionProxy = new MockProtectionProxyV2(Map.empty, "", ancillaryClientMock)
      val testCases = Table(
        ("latestSupportedVersion", "expected"),
        (None, None),
        (Some("1"), Some("test")),
        (Some("2"), Some("test"))
      )
      forAll(testCases) { (latestSupportedVersion, expected) =>
        val mockQuoteResponse = getMockQuoteResponse()
        val setupBookingRequest = getSetupBookingRequest(
          requestedOptInValue = ProtectionRequestItemOptInValue.None
        )
        val tripProtectionRequestItem =
          TripProtectionRequestItem(
            "123",
            ProductTypeEnum.Flight,
            ProtectionRequestItemOptInValue.None,
            latestSupportedVersion
          )
        val productsRequest =
          setupBookingRequest.productsRequest.copy(tripProtectionRequests = Some(Seq(tripProtectionRequestItem)))
        val tripProtectionProductItemResult =
          protectionMappingProxy.mapProtectionResponse(
            setupBookingRequest
              .copy(productsRequest = productsRequest),
            mockQuoteResponse.tripProtections.get.head,
            mockQuoteResponse,
            ""
          )

        assert(tripProtectionProductItemResult.tripProtectionData.nonEmpty)
        assert(tripProtectionProductItemResult.content == "Success")
        assert(tripProtectionProductItemResult.tripProtectionData.get.protectionBookingToken.isEmpty)
        assert(tripProtectionProductItemResult.tripProtectionData.head.solicitation.version == expected)
      }
    }

    "map protection response with empty Body in Solicitation correctly" in {
      val tripProtectionProductItemResult =
        protectionMappingProxy.mapProtectionResponse(
          getSetupBookingRequest(),
          ProtectionQuoteResponseV2ToV1.mapTripProtectionV2ToV1(
            mockQuoteResponseWithEmptyBody.tripProtections.get.head
          ),
          ProtectionQuoteResponseV2ToV1.mapQuoteResponseV2ToV1(mockQuoteResponseWithEmptyBody),
          "token"
        )
      assert(tripProtectionProductItemResult.content == "Success")
      assert(tripProtectionProductItemResult.tripProtectionData.map(_.protectionBookingToken).nonEmpty)
      assert(tripProtectionProductItemResult.tripProtectionData.map(_.solicitation.breakDown).get.isEmpty)
      assert(tripProtectionProductItemResult.tripProtectionData.map(_.solicitation.offerHtml).get.isEmpty)
      assert(tripProtectionProductItemResult.tripProtectionData.map(_.solicitation.contactNumber).get.isEmpty)
    }

    "map FlightConfirmation to FlightProductItem correctly" in
      assert(
        mockFlightConfirmation.toFlightProductItem == FlightProductItem("~unique_id~", "", FlightToken(None, None))
      )

    "fail gracefully if Ancillary API is throwing exception" in {
      val protectionProxy =
        new MockProtectionProxyV2(
          Map("method" -> "quoteTripProtectionV2"),
          s"external.service.${DependencyNames.Protection}",
          ancillaryClientMock
        )
      when(ancillaryClientMock.httpClient).thenThrow(new IndexOutOfBoundsException("Ancillary Client throws"))
      when(ancillaryClientMock.quoteTripProtection(any(), any())(any()))
        .thenThrow(new IndexOutOfBoundsException("Get Protection throws"))
      protectionProxy.callProtectionQuote(mockQuoteRequest).map { result =>
        result shouldBe None
      }
    }

    "create AncillaryApiClient success with wrong invalid server on call" in {
      val expectedJsonString =
        """{"slices":[{"segments":[{"originAirport":"RDM","destinationAirport":"PHX","departDateTime":"0001-01-01T01:01:00Z","arrivalDateTime":"0001-01-01T01:01:00Z","operatingCarrier":"AA","marketingCarrier":"AA","validatingCarrier":"AA","cabinClassCode":"ECO","fareBasisCode":"S0FWZNN1","flightNumber":"3503","bookingClass":"S"}],"subSupplierId":30023,"paymentModel":1}]}""".stripMargin

      val actualJsonString = protectionProxyV2.getSupplierSpecificData(
        Some(
          SliceDetail(
            slices = Seq(
              Slice(
                segments = Seq(
                  Segment(
                    originAirport = "RDM",
                    destinationAirport = "PHX",
                    departDateTime = LocalDateTime.of(1, 1, 1, 1, 1).atOffset(ZoneOffset.UTC),
                    arrivalDateTime = LocalDateTime.of(1, 1, 1, 1, 1).atOffset(ZoneOffset.UTC),
                    operatingCarrier = "AA",
                    marketingCarrier = Some("AA"),
                    validatingCarrier = Some("AA"),
                    cabinClassCode = "ECO",
                    fareBasisCode = Some("S0FWZNN1"),
                    flightNumber = "3503",
                    bookingClass = "S"
                  )
                ),
                subSupplierId = Some(30023),
                paymentModel = Some(1)
              )
            )
          )
        )
      )

      expectedJsonString shouldEqual actualJsonString.get
    }

    "ancillaryProxy.getSupplierSpecificDataForDrive should serialize correctly" in {
      val expectedJsonString =
        """{"id":"1","reservations":[{"pickUpDate":"0001-01-01T01:01:00Z","dropOffDate":"0001-01-01T01:01:00Z","pickUpAddress":{"street1":"street1","street2":"street2","airportCode":null,"cityId":1,"countryId":1},"dropOffAddress":{"street1":"street1","street2":"street2","airportCode":null,"cityId":1,"countryId":1},"drivers":[],"quantity":0,"tripCost":{"amount":230.7,"currencyCode":"USD"},"payLater":false,"carClassCode":"J Class","agency":null}]}"""

      val actualJsonString = protectionProxyV2.getSupplierSpecificDataForDrive(
        Some(
          Drive(
            id = "1",
            reservations = Seq(
              Reservation(
                pickUpDate = LocalDateTime.of(1, 1, 1, 1, 1).atOffset(ZoneOffset.UTC),
                dropOffDate = LocalDateTime.of(1, 1, 1, 1, 1).atOffset(ZoneOffset.UTC),
                tripCost = Price(230.7, "USD"),
                carClassCode = "J Class",
                pickUpAddress = DriveAddress(
                  street1 = Some("street1"),
                  street2 = Some("street2"),
                  airportCode = None,
                  cityId = Some(1),
                  countryId = Some(1)
                ),
                dropOffAddress = DriveAddress(
                  street1 = Some("street1"),
                  street2 = Some("street2"),
                  airportCode = None,
                  cityId = Some(1),
                  countryId = Some(1)
                ),
                agency = None,
                drivers = Seq(),
                quantity = 0,
                payLater = false
              )
            )
          )
        )
      )
      actualJsonString.get shouldEqual expectedJsonString
    }

    "map DisplayPriceBreakdowns when tripProtectionData has NONE breakdown" in {
      val tripProtectionData = getTripProtectionData.copy(displayPriceBreakdown = None)
      val actualDisplayPriceBreakdowns = protectionMappingProxy.getDisplayPriceBreakdowns(
        ProtectionQuoteResponseV2ToV1.mapTripProtectionDataV2ToV1(tripProtectionData)
      )
      assert(actualDisplayPriceBreakdowns.isEmpty)
    }

    "map DisplayPriceBreakdowns when tripProtectionData has empty breakdown" in {
      val tripProtectionData = getTripProtectionData.copy(displayPriceBreakdown = Some(Seq.empty))
      val actualDisplayPriceBreakdowns = protectionMappingProxy.getDisplayPriceBreakdowns(
        ProtectionQuoteResponseV2ToV1.mapTripProtectionDataV2ToV1(tripProtectionData)
      )
      assert(actualDisplayPriceBreakdowns.isEmpty)
    }

  }

  private val mockFlightItinerary = FlightItinerary(
    id = "test",
    slices = Seq(
      SearchResponseSlice(
        id = 1,
        duration = 1,
        overnightFlight = true,
        segments = Seq(
          SearchResponseSegment(
            1,
            "2019-07-24T21:55:00",
            None,
            "2019-07-24T18:30:00",
            "SIN",
            145,
            "333",
            "979",
            "SQ",
            "BKK",
            "V",
            "ECO",
            SearchResponseGenericSeatAssignCost(0.0, "USD"),
            false,
            3,
            true,
            true,
            true,
            true,
            true,
            true,
            0.0,
            None,
            None,
            None,
            None,
            "SQ",
            None,
            false,
            Vector.empty,
            Vector.empty,
            Vector.empty,
            None,
            segmentInfoByPaxTypes = Vector.empty,
            None,
            None,
            stops = Vector.empty
          )
        ),
        freeBags = Seq.empty,
        cancellationPolicies = Seq.empty,
        exchangePolicies = Seq.empty,
        voidableWithinHours = 0
      )
    ),
    paymentModel = 0
  )

  private val mockFlightConfirmation = FlightConfirmationData(
    id = "~unique_id~",
    token = "",
    submitToken = FlightToken(None, None),
    isCompleted = true,
    hasFlight = true,
    hasContent = true,
    isHackerFare = false,
    packageRequest = None,
    flightPricing = Some(
      Map(
        "THB" -> SearchResponseCurrencyPricing(
          charges = Seq.empty,
          display = chargeTotal,
          discount = None,
          promotionDiscount = None,
          crossedOutDisplay = None,
          discountItem = None,
          totalDiscount = None,
          paymentModel = -1,
          acceptedCreditCards = None
        )
      )
    ),
    priceChange = None,
    flightItinerary = Some(mockFlightItinerary),
    paxNumberByType = Map(PaxType.Adult -> 3, PaxType.Child -> 2, PaxType.LapInfant -> 1)
  )

  val mockCarConfirmationData = CarConfirmationData(
    "1",
    CarProductItem.rawMockResponse,
    isCompleted = true,
    hasContent = true,
    carInfo = MockCarPricingData.MockCarOptions,
    carBookingPricing = Some(
      CarBookingDetails(
        payment = BookingDetailsPayment(
          paymentAmount = 100,
          paymentAmountInUSD = 30,
          paymentCurrency = "INR",
          milesAmountInUSD = 30,
          vendorExchangeRate = 32,
          paymentExchangeRate = 1,
          vendorAmount = 1,
          vendorCurrency = "THB",
          milesAmountInPaymentCurrency = 100,
          point = None
        ),
        priceBreakdowns = Seq(
          BookingDetailsPriceBreakdowns(
            itemId = 1,
            date = "2021-10-10",
            typeId = 211,
            quantity = 1,
            usdAmount = 30,
            vendorExchangeRate = 1,
            applyType = 4,
            taxFeeId = 0,
            optionId = 1,
            vendorCurrency = "THB",
            vendorAmount = 90,
            requestAmount = 100,
            requestedCurrency = "INR",
            requestedExchangeRate = 1
          )
        ),
        driverAge = 25
      )
    ),
    customerPolicyInfo = None
  )

  def getMockQuoteResponse(tripProtectionType: Option[Int] = Some(1)) =
    ProtectionQuoteResponseV2ToV1.mapQuoteResponseV2ToV1(
      QuoteResponse(
        id = "123",
        isCompleted = true,
        resultCode = 1,
        resultMessage = "success",
        tripProtections = Some(
          Seq(
            TripProtection(
              tripProtectionType = tripProtectionType,
              supplierId = SupplierId(40001),
              subSupplierId = SubSupplierId(40002),
              paymentModel = PaymentModel.Agency,
              tripProtectionData = getTripProtectionData,
              optionButton = OptionButton("", ""),
              agePolicy = AgePolicy(
                minAgePolicyInfo = AgePolicyInfo(age = Age(0, 3, 0), dateOfBirth = LocalDate.parse("2021-09-01")),
                maxAgePolicyInfo = AgePolicyInfo(age = Age(65, 0, 0), dateOfBirth = LocalDate.parse("2100-12-01"))
              )
            )
          )
        ),
        enabledFeatures = Seq("Feature"),
        errors = Seq.empty
      )
    )
  def getTripProtectionData: TripProtectionData =
    TripProtectionData(
      supplierData = "",
      supplierSearchId = "",
      price = Price(100, "USD"),
      usdPrice = 100,
      margin = Price(20, "USD"),
      marginPercentage = 20,
      usdMargin = 20,
      disclaimer = "",
      solicitation = Solicitation(
        offerHtml = Some("test"),
        header = Some("test"),
        subHeader = Some("test"),
        callToAction = Some("test"),
        body = Some("test"),
        footer = Some("test"),
        terms = Some(Terms("test", SolicitationUrl("text", "link"))),
        options = Some(Options("test", "test", Some("purchaseInfo detail"), None)),
        contactNumber = Some("+1-234567890"),
        shortTerms = Some("shortTerms text"),
        benefits = Some(Seq(Benefit("test"))),
        version = "test"
      ),
      displayPriceBreakdown = getDisplayPriceBreakdown,
      financialBreakdowns = Seq.empty
    )

  def getDisplayPriceBreakdown: Option[Seq[DisplayPriceBreakdownItem]] =
    Some(
      Seq(
        DisplayPriceBreakdownItem(
          title = "test",
          price = Price(100, "USD"),
          breakdowns = Some(
            Seq(
              DisplayPriceBreakdownItem(
                title = "test",
                price = Price(100, "USD"),
                breakdowns = None,
                typeId = Option(82)
              )
            )
          ),
          typeId = Option(80)
        ),
        DisplayPriceBreakdownItem(
          title = "test1",
          price = Price(200, "USD"),
          breakdowns = Some(Seq.empty),
          typeId = Some(86)
        )
      )
    )

  private val mockQuoteResponseWithEmptyBody = QuoteResponse(
    id = "123",
    isCompleted = true,
    resultCode = 1,
    resultMessage = "success",
    tripProtections = Some(
      Seq(
        TripProtection(
          tripProtectionType = Some(1),
          supplierId = SupplierId(1),
          subSupplierId = SubSupplierId(1),
          paymentModel = PaymentModel.Agency,
          tripProtectionData = TripProtectionData(
            supplierData = "",
            supplierSearchId = "",
            price = Price(100, "USD"),
            usdPrice = 100,
            margin = Price(20, "USD"),
            marginPercentage = 20,
            usdMargin = 20,
            disclaimer = "",
            solicitation = Solicitation(None, None, None, None, None, None, None, None, version = "test"),
            financialBreakdowns = Seq.empty
          ),
          optionButton = OptionButton("", ""),
          agePolicy = AgePolicy(
            minAgePolicyInfo = AgePolicyInfo(Age(0, 3, 0), dateOfBirth = LocalDate.MIN),
            maxAgePolicyInfo = AgePolicyInfo(Age(65, 0, 0), dateOfBirth = LocalDate.MAX)
          )
        )
      )
    ),
    enabledFeatures = Seq.empty,
    errors = Seq.empty
  )

  private val mockQuoteRequest = QuoteRequest(
    context = RequestContext(
      requestId = "123",
      locale = Some("en-us"),
      languageId = 1,
      deviceTypeId = 1,
      deviceType = "desktop",
      userId = "user_001",
      clientIp = "clientIp_127.0.0.1",
      origin = "US",
      currency = "USD",
      correlationId = "correlationId_001",
      whitelabelId = 5,
      browserType = Some("IE"),
      osType = Some("Ubuntu")
    ),
    passenger = RequestPassenger(
      numberOfAdults = 3,
      numberOfChildren = 2,
      numberOfInfants = 1
    ),
    supplierQuoteRequestData = QuoteRequestData(),
    enabledFeatures = Seq.empty
  )

  val flightBookingToken = Seq(
    FlightTokenAWO(
      supplierId = 30001,
      subSupplierId = 0,
      supplierData =
        """{"priceToken":"R_BKK201909100850RGN201909100940MPG701~RGN201909101735SIN201909102210YSQ5019_2_USD981.24_145239933000704-145239933000614-8","groupId":"145239933000704","refId":"145239933000614","pricedConfirmationId":"*********","price":{"pointOfSale":{"totalBaseWithTaxesAndFees":1962.9,"totalBasePrice":1841.6,"totalTaxes":121.3,"totalFees":0.0,"fees":[],"currencyCode":"USD"},"transaction":{"totalBaseWithTaxesAndFees":1962.9,"totalBasePrice":1841.6,"totalTaxes":121.3,"totalFees":0.0,"fees":[],"currencyCode":"USD"}}}""",
      info = Some(
        FlightInfoAWO(
          2,
          0,
          "SQ",
          None,
          PaymentModelAWO.UNKNOWN,
          Vector(
            SliceAWO(
              List(),
              Seq(
                SegmentAWO(
                  "BKK",
                  "RGN",
                  "319",
                  "PG",
                  "",
                  "701",
                  None,
                  None,
                  "M",
                  "ECO",
                  0,
                  Seq.empty,
                  "",
                  "80",
                  ""
                ),
                SegmentAWO(
                  "RGN",
                  "SIN",
                  "738",
                  "SQ",
                  "",
                  "5019",
                  None,
                  None,
                  "Y",
                  "ECO",
                  0,
                  Seq.empty,
                  "",
                  "185",
                  ""
                )
              )
            )
          ),
          List(
            FlightItemBreakdownAWO(None, 1, 101, 0, 1, "USD", 1841.6, 0.0, 1841.6, 1841.6, 1),
            FlightItemBreakdownAWO(None, 11, 101, 0, 1, "USD", 1841.6, 0.0, 1841.6, 1841.6, 1),
            FlightItemBreakdownAWO(None, 10, 101, 0, 1, "USD", 1962.9, 0.0, 1962.9, 1962.9, 1),
            FlightItemBreakdownAWO(None, 12, 101, 0, 1, "USD", 1962.9, 0.0, 1962.9, 1962.9, 1),
            FlightItemBreakdownAWO(None, 3, 101, 0, 1, "USD", 0.0, 0.0, 0.0, 0.0, 1),
            FlightItemBreakdownAWO(None, 1, 101, 0, 1, "USD", 121.3, 0.0, 121.3, 121.3, 1),
            FlightItemBreakdownAWO(None, 16, 101, 0, 1, "USD", 0.0, 0.0, 0.0, 0.0, 1)
          ),
          1,
          false,
          false,
          None,
          List(
            FlightItemBreakdownAWO(None, 1, 101, 0, 1, "USD", 1841.6, 0.0, 1841.6, 1841.6, 1),
            FlightItemBreakdownAWO(None, 11, 101, 0, 1, "USD", 1841.6, 0.0, 1841.6, 1841.6, 1),
            FlightItemBreakdownAWO(None, 10, 101, 0, 1, "USD", 1962.9, 0.0, 1962.9, 1962.9, 1),
            FlightItemBreakdownAWO(None, 12, 101, 0, 1, "USD", 1962.9, 0.0, 1962.9, 1962.9, 1),
            FlightItemBreakdownAWO(None, 3, 101, 0, 1, "USD", 0.0, 0.0, 0.0, 0.0, 1),
            FlightItemBreakdownAWO(None, 1, 101, 0, 1, "USD", 121.3, 0.0, 121.3, 121.3, 1),
            FlightItemBreakdownAWO(None, 16, 101, 0, 1, "USD", 0.0, 0.0, 0.0, 0.0, 1)
          )
        )
      ),
      searchId = "8463d781-6c32-4416-a253-1c9cc0c4cec2",
      itineraryId = "1154072730",
      pointOfSale = "US",
      experimentVariant = ""
    )
  )

  private def getSetupBookingRequest(
      enabledFeatures: Option[Seq[String]] = None,
      requestedOptInValue: Int = ProtectionRequestItemOptInValue.None
  ): SetupBookingRequest = {
    val tripProtectionRequestItem =
      TripProtectionRequestItem(
        "123",
        ProductTypeEnum.Flight,
        requestedOptInValue
      )
    val flightRequest = FlightRequestItem(Some("test"), Some(ConfirmPriceRequest("test", "test")), None)
    SetupBookingRequest(
      correlationId = Some("correlationId"),
      userContext = Some(
        UserContextMock.value.copy(requestOrigin = "TH", experimentData = Option(ExperimentDataMock.protectionValue))
      ),
      paymentRequest = Some(
        PaymentRequest(
          ccBin = None,
          ccId = None,
          selectedPaymentMethod = None
        )
      ),
      customerInfo = None,
      productsRequest = ProductsRequest(
        bookingToken = None,
        propertyRequests = Seq.empty,
        flightRequests = Seq(flightRequest),
        tripProtectionRequests = Some(Seq(tripProtectionRequestItem)),
        packageRequest = None
      ),
      enabledFeatures = enabledFeatures
    )
  }

  private def getSetupBookingRequestForVehicle(
      enabledFeatures: Option[Seq[String]] = None,
      requestedOptInValue: Int = ProtectionRequestItemOptInValue.None
  ): SetupBookingRequest = {
    val tripProtectionRequestItem =
      TripProtectionRequestItem(
        "123",
        ProductTypeEnum.Car,
        requestedOptInValue
      )
    SetupBookingRequest(
      correlationId = Some("correlationId"),
      userContext = Some(
        UserContextMock.value.copy(requestOrigin = "TH", experimentData = Option(ExperimentDataMock.protectionValue))
      ),
      paymentRequest = Some(
        PaymentRequest(
          ccBin = None,
          ccId = None,
          selectedPaymentMethod = None
        )
      ),
      customerInfo = None,
      productsRequest = ProductsRequest(
        bookingToken = None,
        propertyRequests = Seq.empty,
        carRequestsOpt = Option(
          Seq(
            CarRequestItem(
              id = "1",
              confirmPriceRequest = CarConfirmPriceRequest(
                identifier = None,
                searchToken =
                  "AAAA7/AdeyJ2IjoxLCJ0T2JqIjp7ImlkIjoiQ0FSLTkwYWwiLCJkYSI6MzUsInBpY2sgACBjbyIAoDEzNDg3OSwiY3QOAMA5OTk5LCJsb2MiOiIMABAiDQD2GHQiOjIsImR0IjoiMjAyMC0wMy0yM1QxODoyNDowMFoifSwiZHJvcFQARDc3NzdSAFQxOTI4MlMAAQ0AD1QABho1VAARYVIA8ApwIjozODkwLjg4LCJjdXIiOiJUSEIifX19"
              ),
              tripProtectionRequest = Some(tripProtectionRequestItem)
            )
          )
        ),
        packageRequest = None
      ),
      enabledFeatures = enabledFeatures
    )
  }

  private def verifyQuoteRequest(
      quoteRequest: QuoteRequest,
      expectedEnabledFeatures: Seq[String] = Seq.empty,
      expectedPassengers: Option[Seq[Passenger]] = None
  ) = {
    quoteRequest.context.requestId shouldBe "123"
    quoteRequest.context.deviceTypeId shouldBe 0
    quoteRequest.context.deviceType shouldBe DevicePlatform.Unknown.toString
    quoteRequest.context.userId shouldBe UserContextMock.value.experimentData.get.userId
    quoteRequest.context.clientIp shouldBe "0.0.0.0"
    quoteRequest.context.origin shouldBe "TH"
    quoteRequest.context.currency shouldBe "THB"
    quoteRequest.context.locale shouldBe Some("en-us")
    quoteRequest.context.correlationId shouldBe "correlationId"
    quoteRequest.context.whitelabelId shouldBe WhiteLabel.Agoda.id
    quoteRequest.context.experiments shouldBe Some(
      Experiments(
        forceByVariant = None,
        forceByExperiment = Seq(
          ForcedExperiment(id = "expId", "B")
        )
      )
    )
    quoteRequest.context.cid shouldBe Option(1)
    quoteRequest.context.aid shouldBe Option(123)
    quoteRequest.context.trafficGroup shouldBe Option("trafficGroup")
    val flyResult = quoteRequest.supplierQuoteRequestData.fly
    flyResult.get.id shouldBe mockFlightConfirmation.id
    val totalNumberPassenger =
      mockFlightConfirmation.paxNumberByType.getOrElse(PaxType.Adult, 0) + mockFlightConfirmation.paxNumberByType
        .getOrElse(PaxType.Child, 0) + mockFlightConfirmation.paxNumberByType.getOrElse(PaxType.LapInfant, 0)

    flyResult.get.sliceDetail.slices(0).segments(0).flightNumber shouldBe
      mockFlightItinerary.slices(0).segments(0).flightNumber
    flyResult.get.sliceDetail.slices(0).segments(0).bookingClass shouldBe
      mockFlightItinerary.slices(0).segments(0).bkgClass
    quoteRequest.passenger.numberOfAdults shouldBe mockFlightConfirmation.paxNumberByType
      .getOrElse(PaxType.Adult, 0)
    quoteRequest.passenger.numberOfChildren shouldBe mockFlightConfirmation.paxNumberByType
      .getOrElse(PaxType.Child, 0)
    quoteRequest.passenger.numberOfInfants shouldBe mockFlightConfirmation.paxNumberByType
      .getOrElse(PaxType.LapInfant, 0)

    quoteRequest.supplierQuoteRequestData.fly.get.numberOfPassenger shouldBe expectedPassengers
    quoteRequest.supplierQuoteRequestData.fly.get.tripCost.amount shouldBe mockFlightConfirmation.flightPricing
      .get("THB")
      .display
      .perBook
      .allInclusive
    quoteRequest.supplierQuoteRequestData.fly.get.tripCost.currencyCode shouldBe "THB"
    quoteRequest.enabledFeatures shouldBe expectedEnabledFeatures

  }

  private def verifyQuoteRequestForVehicle(
      quoteRequest: QuoteRequest,
      expectedEnabledFeatures: Seq[String] = Seq.empty
  ) = {
    quoteRequest.context.requestId shouldBe "contextCorrelationId"
    quoteRequest.context.deviceTypeId shouldBe 0
    quoteRequest.context.deviceType shouldBe DevicePlatform.Unknown.toString
    quoteRequest.context.userId shouldBe UserContextMock.value.experimentData.get.userId
    quoteRequest.context.clientIp shouldBe "0.0.0.0"
    quoteRequest.context.origin shouldBe "TH"
    quoteRequest.context.currency shouldBe "THB"
    quoteRequest.context.locale shouldBe Some("en-us")
    quoteRequest.context.correlationId shouldBe "correlationId"
    quoteRequest.context.whitelabelId shouldBe WhiteLabel.Agoda.id
    quoteRequest.context.experiments shouldBe Some(
      Experiments(
        forceByVariant = None,
        forceByExperiment = Seq(
          ForcedExperiment(id = "expId", "B")
        )
      )
    )
    quoteRequest.context.cid shouldBe Option(1)
    quoteRequest.context.aid shouldBe Option(123)
    quoteRequest.context.trafficGroup shouldBe Option("trafficGroup")
    quoteRequest.passenger.numberOfAdults shouldBe 0
    quoteRequest.passenger.numberOfChildren shouldBe 0
    quoteRequest.passenger.numberOfInfants shouldBe 0

    val vehicleResult = quoteRequest.supplierQuoteRequestData.drive
    vehicleResult.get.id shouldBe mockCarConfirmationData.id

    vehicleResult.get.reservations(0).pickUpDate shouldBe LocalDateTime
      .parse(
        MockCarPricingData.MockLocationInfo.dateTime.toLocalDateTime.toString,
        DateTimeFormatter.ISO_LOCAL_DATE_TIME
      )
      .atOffset(ZoneOffset.UTC)
    vehicleResult.get.reservations(0).dropOffDate shouldBe LocalDateTime
      .parse(
        MockCarPricingData.MockLocationInfo.dateTime.toLocalDateTime.toString,
        DateTimeFormatter.ISO_LOCAL_DATE_TIME
      )
      .atOffset(ZoneOffset.UTC)
    vehicleResult.get.reservations(0).pickUpAddress.street1 shouldBe Some(
      MockCarPricingData.MockLocationInfo.addressLine
    )
    vehicleResult.get.reservations(0).pickUpAddress.street2 shouldBe Option("")
    vehicleResult.get.reservations(0).pickUpAddress.airportCode shouldBe None
    vehicleResult.get.reservations(0).pickUpAddress.cityId shouldBe Some(MockCarPricingData.MockLocationInfo.cityId)
    vehicleResult.get.reservations(0).pickUpAddress.countryId shouldBe Some(
      MockCarPricingData.MockLocationInfo.countryId
    )
    vehicleResult.get.reservations(0).dropOffAddress.street1 shouldBe Some(
      MockCarPricingData.MockLocationInfo.addressLine
    )
    vehicleResult.get.reservations(0).dropOffAddress.street2 shouldBe Option("")
    vehicleResult.get.reservations(0).dropOffAddress.airportCode shouldBe None
    vehicleResult.get.reservations(0).dropOffAddress.cityId shouldBe Some(MockCarPricingData.MockLocationInfo.cityId)
    vehicleResult.get.reservations(0).dropOffAddress.countryId shouldBe Some(
      MockCarPricingData.MockLocationInfo.countryId
    )

    vehicleResult.get.reservations(0).tripCost.amount shouldBe mockCarConfirmationData.carBookingPricing
      .map(_.payment.paymentAmount)
      .get
    vehicleResult.get.reservations(0).tripCost.currencyCode shouldBe mockCarConfirmationData.carBookingPricing
      .map(_.payment.paymentCurrency)
      .get
    vehicleResult.get
      .reservations(0)
      .payLater shouldBe MockCarPricingData.MockCarOptions.paymentModel == PaymentModel.Agency
    vehicleResult.get.reservations(0).carClassCode shouldBe MockCarPricingData.MockVehicleInfo.classification
    vehicleResult.get.reservations(0).agency shouldBe Option(MockCarPricingData.MockVehicleSupplierInfo.providerCode)
    quoteRequest.enabledFeatures shouldBe expectedEnabledFeatures
  }
}
