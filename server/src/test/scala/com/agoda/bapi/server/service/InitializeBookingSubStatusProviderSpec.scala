package com.agoda.bapi.server.service

import com.agoda.bapi.common.constants.UserPointBalanceErrorCode
import com.agoda.bapi.common.message.{InitializeBookingStatus, InitializeBookingSubStatus}
import com.agoda.bapi.server.model.ExternalLoyaltyPointsValidationResult
import com.agoda.bapi.server.service.allotment.AllotmentPreCheckStatus
import org.scalatest.OptionValues
import org.scalatest.matchers.must.Matchers.convertToAnyMustWrapper
import org.scalatest.wordspec.AnyWordSpec
import org.scalatestplus.mockito.MockitoSugar

class InitializeBookingSubStatusProviderSpec extends AnyWordSpec with MockitoSugar with OptionValues {
  "SetupBookingSubStatusProvider" when {
    "InitializeBookingStatus is AllotmentNotAvailable" when {
      "error code is not empty" when
        Seq(
          (220030, InitializeBookingSubStatus.OccupancyGenderNotAllowed),
          (220031, InitializeBookingSubStatus.OccupancyGenderNotAllowed),
          (220049, InitializeBookingSubStatus.AllotmentNotAvailable),
          (220053, InitializeBookingSubStatus.AllotmentNotAvailable),
          (220010, InitializeBookingSubStatus.NumberOfRoomNotAllowed),
          (220027, InitializeBookingSubStatus.StayPeriodNotAllowed),
          (220040, InitializeBookingSubStatus.NumberOfOccupancyNotAllowed),
          (220181, InitializeBookingSubStatus.NumberOfOccupancyNotAllowed),
          (999999, InitializeBookingSubStatus.Unknown)
        ).foreach { testCase =>
          s"contains ${testCase._1} error code" should {
            s"return InitializeBookingSubStatus.${testCase._2}" in {
              val allotmentPreCheckStatuses = Seq(
                AllotmentPreCheckStatus(
                  productKey = "0",
                  roomIdentifier = None,
                  requestId = None,
                  status = 1,
                  errorCode = Some(testCase._1)
                )
              )

              val result = InitializeBookingSubStatusProvider.create(
                InitializeBookingStatus.AllotmentNotAvailable,
                allotmentPreCheckStatuses
              )
              result.value mustBe testCase._2
            }
          }
        }
      "error code is empty" should {
        "return None" in {
          val allotmentPreCheckStatuses = Seq(
            AllotmentPreCheckStatus(
              productKey = "0",
              roomIdentifier = None,
              requestId = None,
              status = 1,
              errorCode = None
            )
          )

          val result = InitializeBookingSubStatusProvider.create(
            InitializeBookingStatus.AllotmentNotAvailable,
            allotmentPreCheckStatuses
          )
          result.isEmpty mustBe true
        }
      }
    }

    "InitializeBookingStatus is ExternalLoyaltyPointsError" when {
      "ExternalLoyaltyValidationResult is InsufficientPointBalance" should {
        "return InsufficientPointBalance" in {
          val result = InitializeBookingSubStatusProvider.create(
            InitializeBookingStatus.ExternalLoyaltyPointsError,
            Seq.empty,
            ExternalLoyaltyPointsValidationResult.InsufficientPointsBalance
          )

          result.value mustBe InitializeBookingSubStatus.InsufficientPointsBalance
        }
      }

      "ExternalLoyaltyValidationResult is InsufficientPointsRedemption" should {
        "return InsufficientPointsRedemption" in {
          val result = InitializeBookingSubStatusProvider.create(
            InitializeBookingStatus.ExternalLoyaltyPointsError,
            Seq.empty,
            ExternalLoyaltyPointsValidationResult.InsufficientPointsRedemption
          )

          result.value mustBe InitializeBookingSubStatus.InsufficientPointsRedemption
        }
      }

      "ExternalLoyaltyValidationResult is HardInsufficientPointsBalance" should {
        "return HardInsufficientPointsBalance" in {
          val result = InitializeBookingSubStatusProvider.create(
            InitializeBookingStatus.ExternalLoyaltyPointsError,
            Seq.empty,
            ExternalLoyaltyPointsValidationResult.HardInsufficientPointsBalance
          )

          result.value mustBe InitializeBookingSubStatus.HardInsufficientPointsBalance
        }
      }

      "ExternalLoyaltyValidationResult is SoftInsufficientPointsBalance" should {
        "return SoftInsufficientPointBalance" in {
          val result = InitializeBookingSubStatusProvider.create(
            InitializeBookingStatus.ExternalLoyaltyPointsError,
            Seq.empty,
            ExternalLoyaltyPointsValidationResult.SoftInsufficientPointsBalance
          )

          result.value mustBe InitializeBookingSubStatus.SoftInsufficientPointsBalance
        }
      }
    }

    "InitializeBookingStatus is other" should {
      "return None" in {
        val allotmentPreCheckStatuses = Seq.empty

        val result = InitializeBookingSubStatusProvider.create(InitializeBookingStatus.Ok, allotmentPreCheckStatuses)
        result.isEmpty mustBe true
      }
    }

    "fromUserPointBalanceErrorCode" should {
      "return HardInsufficientPointsBalance Substatus when error code is HardInsufficientPointsBalance" in {
        val result = InitializeBookingSubStatusProvider.fromUserPointBalanceErrorCode(
          Some(UserPointBalanceErrorCode.HardInsufficientPointsBalance)
        )
        result.value mustBe InitializeBookingSubStatus.HardInsufficientPointsBalance
      }

      "return SoftInsufficientPointsBalance Substatus when error code is SoftInsufficientPointsBalance" in {
        val result = InitializeBookingSubStatusProvider.fromUserPointBalanceErrorCode(
          Some(UserPointBalanceErrorCode.SoftInsufficientPointsBalance)
        )
        result.value mustBe InitializeBookingSubStatus.SoftInsufficientPointsBalance
      }

      "return InsufficientPointsBalance Substatus when error code is InsufficientPointsBalance" in {
        val result = InitializeBookingSubStatusProvider.fromUserPointBalanceErrorCode(
          Some(UserPointBalanceErrorCode.InsufficientPointsBalance)
        )
        result.value mustBe InitializeBookingSubStatus.InsufficientPointsBalance
      }

      "return None when error code is None" in {
        val result = InitializeBookingSubStatusProvider.fromUserPointBalanceErrorCode(None)
        result mustBe None
      }
    }
  }
}
