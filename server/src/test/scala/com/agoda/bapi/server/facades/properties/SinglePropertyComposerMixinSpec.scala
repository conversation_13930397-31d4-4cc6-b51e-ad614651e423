package com.agoda.bapi.server.facades.properties

import com.agoda.bapi.common.constants.PropertyFeatureFlags
import com.agoda.bapi.common.message.{PricingRequest, PropertySearchCriteria}
import com.agoda.bapi.common.message.setupBooking.{PackageRequest, PaymentChargeOption, PriceChange, ProductsRequest, PropertyRequestItem, SetupBookingRequest}
import com.agoda.bapi.common.model.creation
import com.agoda.bapi.common.model.creation.BookingRateCategory
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.token.Money
import com.agoda.bapi.creation.{Booking<PERSON><PERSON><PERSON>elper, CreateBookingHelper}
import com.agoda.bapi.server.addon.{AddOnContentData, AddOnData, AddOnDataV2, AddOnFacade, AddOnProductData, AddOnService}
import com.agoda.bapi.server.facades.ProductsFacadeImpl
import com.agoda.bapi.server.facades.addons.AddOnsComposerMixin
import com.agoda.bapi.server.facades.helpers.SetupBookingContextFixture
import com.agoda.bapi.server.service.TotalSavingsService
import com.agoda.mpb.common.models.state.ProductType
import models.pricing.AccountingEntity
import models.starfruit.{DisplayPrice, PaymentBreakdown, PaymentFeature, PerBookDifference, PriceDifference}
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{never, reset, times, verify, when}
import org.scalatest.BeforeAndAfterEach
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar
import transformers.{EnrichedChildRoom, EnrichedPricing}

class SinglePropertyComposerMixinSpec
    extends AsyncWordSpec
    with MockitoSugar
    with Matchers
    with BeforeAndAfterEach
    with CreateBookingHelper
    with BookingTokenHelper
    with SetupBookingContextFixture
    with SinglePropertyComposerMixin
    with AddOnsComposerMixin {

  override val totalSavingsService: TotalSavingsService = mock[TotalSavingsService]
  override val addOnFacade: AddOnFacade                 = mock[AddOnFacade]
  override val addOnsService: AddOnService              = mock[AddOnService]

  override def beforeEach(): Unit = {
    reset(featureAware)
  }

  val usdCurrency = "USD"
  val eurCurrency = "EUR"

  "getPaymentChargeOptions" should {
    "return an empty list if childRoom is None" in {
      val actual = getPaymentChargeOptions(None, Map.empty)
      actual shouldBe empty
    }

    "return an empty list if paymentFeature is None" in {
      val childRoom = Some(EnrichedChildRoom(paymentFeature = None))
      val actual    = getPaymentChargeOptions(childRoom, Map.empty)
      actual shouldBe empty
    }

    "map payment feature and currency with add-on price" in {
      val childRoom = Some(
        EnrichedChildRoom(paymentFeature =
          Some(
            Map(
              usdCurrency -> PaymentFeature(
                upliftPayment = PaymentBreakdown(payToHotel = 100, payToAgoda = 50, total = 150),
                upliftAmount = 10
              ),
              eurCurrency -> PaymentFeature(
                upliftPayment = PaymentBreakdown(payToHotel = 200, payToAgoda = 75, total = 275),
                upliftAmount = 20
              )
            )
          )
        )
      )

      val addOnPrices = Map(
        usdCurrency -> Money(BigDecimal(30), usdCurrency),
        eurCurrency -> Money(BigDecimal(25), eurCurrency)
      )

      val actual = getPaymentChargeOptions(childRoom, addOnPrices)
      val expected = Seq(
        PaymentChargeOption(
          currencyCode = usdCurrency,
          payToSupplierAmount = 100.0,
          payToAgodaAmount = 80.0,
          totalAmount = 180.0
        ),
        PaymentChargeOption(
          currencyCode = eurCurrency,
          payToSupplierAmount = 200.0,
          payToAgodaAmount = 100.0,
          totalAmount = 300.0
        )
      )
      actual shouldBe expected
    }
  }

  "getRateCategoryIds" should {
    "return rateCategoryIds correctly" in {
      val bapiBooking =
        defaultRoom(
          creation.PaymentModel.Merchant,
          rateCategory = Some(BookingRateCategory(id = 12, Amount = 3000))
        ).head._2.bapiBooking

      val token = genBookingToken(bapiBookingToOption = Some(Seq(bapiBooking)))

      val result = getRateCategoryIds(Some(token))

      result.head shouldBe 12
      result.length shouldBe 1
    }

    "return Seq.empty when booking token is None" in {
      val result = getRateCategoryIds(None)

      result.length shouldBe 0
    }

    "return Seq.empty when room's Rate Category ids is None" in {
      val bapiBooking =
        defaultRoom(
          creation.PaymentModel.Merchant,
          rateCategory = None
        ).head._2.bapiBooking

      val token = genBookingToken(bapiBookingToOption = Some(Seq(bapiBooking)))

      val result = getRateCategoryIds(Some(token))

      result.length shouldBe 0
    }
  }

  "getPriceChangeForSingleProperty" should {
    val displayCurrency       = usdCurrency
    val notDisplayCurrency    = "XXX"
    val mockEnrichedChildRoom = mock[EnrichedChildRoom]
    val mockPricing           = mock[EnrichedPricing]
    val basePriceDiff = PriceDifference(
      PerBookDifference(
        priceDifference = DisplayPrice(100, 100),
        previousPrice = DisplayPrice(200, 250),
        currentPrice = DisplayPrice(300, 350)
      )
    )
    "return price change correctly when price diff return from papi and match with display currency" in {
      when(mockEnrichedChildRoom.pricing)
        .thenReturn(Map[String, EnrichedPricing]((displayCurrency, mockPricing)))
      when(mockPricing.priceDifferenceWithPreviousCall).thenReturn(Some(basePriceDiff))
      val res = getPriceChangeForSingleProperty(
        Some(mockEnrichedChildRoom),
        displayCurrency
      )
      val expected =
        Some(PriceChange(Money(250.0, usdCurrency), Money(350.0, usdCurrency), Money(100.0, usdCurrency)))
      res shouldBe expected
    }

    "return price change correctly when price diff return from papi and not match with display currency" in {
      when(mockEnrichedChildRoom.pricing)
        .thenReturn(Map[String, EnrichedPricing]((notDisplayCurrency, mockPricing)))
      when(mockPricing.priceDifferenceWithPreviousCall).thenReturn(Some(basePriceDiff))
      val res = getPriceChangeForSingleProperty(
        Some(mockEnrichedChildRoom),
        displayCurrency
      )
      val expected = None
      res shouldBe expected
    }
  }

  "isCalculateTaxAndFeeWithMarginTaxAndFee" should {
    val mockSetupBookingRequest    = mock[SetupBookingRequest]
    val mockProductsRequest        = mock[ProductsRequest]
    val mockPropertyRequestItem    = mock[PropertyRequestItem]
    val mockPropertySearchCriteria = mock[PropertySearchCriteria]
    val mockPricingRequest         = mock[PricingRequest]

    when(mockSetupBookingRequest.productsRequest).thenReturn(mockProductsRequest)
    when(mockProductsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequestItem))
    when(mockPropertyRequestItem.propertySearchCriteria).thenReturn(mockPropertySearchCriteria)
    when(mockPropertySearchCriteria.pricingRequest).thenReturn(Some(mockPricingRequest))

    "return true when dfFeatureFlags contains CalculateTaxAndFeeWithMarginTaxAndFee" in {
      when(mockPricingRequest.dfFeatureFlags).thenReturn(
        Seq(PropertyFeatureFlags.CalculateTaxAndFeeWithMarginTaxAndFee.id)
      )
      val result = isCalculateTaxAndFeeWithMarginTaxAndFee(mockSetupBookingRequest)
      result shouldBe true
    }

    "return false when dfFeatureFlags does not contains CalculateTaxAndFeeWithMarginTaxAndFee" in {
      when(mockPricingRequest.dfFeatureFlags).thenReturn(Seq.empty)
      val result = isCalculateTaxAndFeeWithMarginTaxAndFee(mockSetupBookingRequest)
      result shouldBe false
    }
  }

  "filteredCegFastTrackAddOnData" should {
    val mockAccountingEntity = AccountingEntity(1, 2, 3, "test")

    val cegFastTrackAddOn = AddOnProductData(
      productType = ProductType.CEGFastTrack,
      isMultipleChoiceSupported = false,
      choices = Seq.empty,
      coreProductItems = Seq.empty,
      isActive = true,
      accountingEntity = mockAccountingEntity
    )

    val otherAddOn = AddOnProductData(
      productType = ProductType.TripProtection,
      isMultipleChoiceSupported = false,
      choices = Seq.empty,
      coreProductItems = Seq.empty,
      isActive = true,
      accountingEntity = mockAccountingEntity
    )

    val cfarAddOnV2 = AddOnDataV2(
      id = "cfar-addon",
      mainProductId = "main-product-1",
      productType = ProductType.CancelForAnyReason,
      productRefIds = Seq("product-ref-1"),
      selectedOptionIds = Seq.empty,
      choices = None,
      content = None,
      confirmationData = None,
      displayContent = None,
      token = None
    )

    val otherAddOnV2 = AddOnDataV2(
      id = "other-addon",
      mainProductId = "main-product-1",
      productType = ProductType.TripProtection,
      productRefIds = Seq("product-ref-2"),
      selectedOptionIds = Seq.empty,
      choices = None,
      content = None,
      confirmationData = None,
      displayContent = None,
      token = None
    )

    "return original addOnData when no CEGFastTrack product exists - CegFastTrackMigration never called" in {
      val addOnData = AddOnData(
        products = Seq(otherAddOn),
        content = Seq.empty
      )
      val addOnDataV2 = Seq(otherAddOnV2)

      val result = filteredCegFastTrackAddOnData(addOnData, addOnDataV2, Some(featureAware))

      result shouldBe addOnData
      verify(featureAware, never()).CegFastTrackMigration
      succeed
    }

    "filter out CEGFastTrack when CFAR exists - CegFastTrackMigration never called" in {

      val addOnData = AddOnData(
        products = Seq(cegFastTrackAddOn, otherAddOn),
        content = Seq.empty
      )
      val addOnDataV2 = Seq(cfarAddOnV2, otherAddOnV2)

      val result = filteredCegFastTrackAddOnData(addOnData, addOnDataV2, Some(featureAware))

      result.products should contain only otherAddOn
      result.products should not contain cegFastTrackAddOn
      verify(featureAware, never()).CegFastTrackMigration
      succeed
    }

    "filter out CEGFastTrack when CegFastTrackMigration is true and no CFAR exists - CegFastTrackMigration called 1 time" in {
      when(featureAware.CegFastTrackMigration).thenReturn(true)

      val addOnData = AddOnData(
        products = Seq(cegFastTrackAddOn, otherAddOn),
        content = Seq.empty
      )
      val addOnDataV2 = Seq(otherAddOnV2)

      val result = filteredCegFastTrackAddOnData(addOnData, addOnDataV2, Some(featureAware))

      result.products should contain only otherAddOn
      result.products should not contain cegFastTrackAddOn
      verify(featureAware, times(1)).CegFastTrackMigration
      succeed
    }

    "return original addOnData when CegFastTrackMigration is false and no CFAR exists - CegFastTrackMigration called 1 time" in {
      when(featureAware.CegFastTrackMigration).thenReturn(false)

      val addOnData = AddOnData(
        products = Seq(cegFastTrackAddOn, otherAddOn),
        content = Seq.empty
      )
      val addOnDataV2 = Seq(otherAddOnV2)

      val result = filteredCegFastTrackAddOnData(addOnData, addOnDataV2, Some(featureAware))

      result shouldBe addOnData
      verify(featureAware, times(1)).CegFastTrackMigration
      succeed
    }

    "preserve content and only filter products when CFAR blocking is enabled - CegFastTrackMigration never called" in {
      val mockContent = Seq(mock[AddOnContentData])
      val addOnData = AddOnData(
        products = Seq(cegFastTrackAddOn, otherAddOn),
        content = mockContent
      )
      val addOnDataV2 = Seq(cfarAddOnV2)

      val result = filteredCegFastTrackAddOnData(addOnData, addOnDataV2, Some(featureAware))

      result.content shouldBe mockContent
      result.products should contain only otherAddOn
      verify(featureAware, never()).CegFastTrackMigration
      succeed
    }

    "handle empty products list correctly - CegFastTrackMigration never called" in {
      val addOnData = AddOnData(
        products = Seq.empty,
        content = Seq.empty
      )
      val addOnDataV2 = Seq(cfarAddOnV2)

      val result = filteredCegFastTrackAddOnData(addOnData, addOnDataV2, Some(featureAware))

      result shouldBe addOnData
      verify(featureAware, never()).CegFastTrackMigration
      succeed
    }

    "handle multiple CEGFastTrack products correctly when CFAR blocking is enabled - CegFastTrackMigration never called" in {
      val anotherCegFastTrackAddOn = cegFastTrackAddOn.copy(coreProductItems = Seq("different"))
      val addOnData = AddOnData(
        products = Seq(cegFastTrackAddOn, anotherCegFastTrackAddOn, otherAddOn),
        content = Seq.empty
      )
      val addOnDataV2 = Seq(cfarAddOnV2)

      val result = filteredCegFastTrackAddOnData(addOnData, addOnDataV2, Some(featureAware))

      result.products should contain only otherAddOn
      result.products should have size 1
      verify(featureAware, never()).CegFastTrackMigration
      succeed
    }
  }

  "filteredAncillaryAddOns" should {
    "return all add-ons when CEGFastTrack exists and CegFastTrackMigration feature is enabled" in {
      val cegFastTrackAddOn = AddOnDataV2(
        id = "ceg-fast-track-1",
        mainProductId = "main-product-1",
        productType = ProductType.CEGFastTrack,
        productRefIds = Seq("product-ref-1"),
        selectedOptionIds = Seq.empty,
        choices = None,
        content = None,
        confirmationData = None,
        displayContent = None,
        token = None
      )
      val otherAddOn = AddOnDataV2(
        id = "other-addon-1",
        mainProductId = "main-product-1",
        productType = ProductType.CancelForAnyReason,
        productRefIds = Seq("product-ref-2"),
        selectedOptionIds = Seq.empty,
        choices = None,
        content = None,
        confirmationData = None,
        displayContent = None,
        token = None
      )
      val addOns = Seq(cegFastTrackAddOn, otherAddOn)

      when(featureAware.CegFastTrackMigration).thenReturn(true)

      val result = filteredAncillaryAddOns(addOns)

      result should contain theSameElementsAs addOns
    }

    "filter out CEGFastTrack when CegFastTrackMigration feature is disabled" in {
      val cegFastTrackAddOn = AddOnDataV2(
        id = "ceg-fast-track-1",
        mainProductId = "main-product-1",
        productType = ProductType.CEGFastTrack,
        productRefIds = Seq("product-ref-1"),
        selectedOptionIds = Seq.empty,
        choices = None,
        content = None,
        confirmationData = None,
        displayContent = None,
        token = None
      )
      val otherAddOn = AddOnDataV2(
        id = "other-addon-1",
        mainProductId = "main-product-1",
        productType = ProductType.CancelForAnyReason,
        productRefIds = Seq("product-ref-2"),
        selectedOptionIds = Seq.empty,
        choices = None,
        content = None,
        confirmationData = None,
        displayContent = None,
        token = None
      )
      val addOns = Seq(cegFastTrackAddOn, otherAddOn)

      when(featureAware.CegFastTrackMigration).thenReturn(false)

      val result = filteredAncillaryAddOns(addOns)

      result should contain only otherAddOn
      result should not contain cegFastTrackAddOn
    }
  }

}
