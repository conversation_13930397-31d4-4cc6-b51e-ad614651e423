package com.agoda.bapi.server.facades

import com.agoda.abspnx.client.http.response.PrecheckResponse
import com.agoda.abspnx.client.models.abs.Response.RequestProcessing
import com.agoda.abspnx.client.models.abs.Status.Success
import com.agoda.abspnx.client.models.abs.{Response => AbsResponse, ResultInfo, Status => AbsStatus}
import com.agoda.adp.messaging.message.Message
import com.agoda.bapi.common.config.{OverrideOccupancyConfig, PriceChangeSupplierBlacklistConfig}
import com.agoda.bapi.common.constants.PropertyFeatureFlags
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.localization.{CmsContext, CmsContextFactory, CmsItem, CmsItems}
import com.agoda.bapi.common.message._
import com.agoda.bapi.common.message.consumerFinTech.BcreEssInfoMessage
import com.agoda.bapi.common.message.creation.{CreditCardExpiration, CustomerV2}
import com.agoda.bapi.common.message.externalLoyalty.{PointsRange => SetupResponseELEPointRange, _}
import com.agoda.bapi.common.message.loyalty.LoyaltyPaymentBoundaries
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.activity.{ActivityConfirmationData, ActivityPaymentMethodAndCharges}
import com.agoda.bapi.common.model.booking._
import com.agoda.bapi.common.model.cart.CartContext
import com.agoda.bapi.common.model.creation.LoyaltyDiscountType
import com.agoda.bapi.common.model.db.CountryInfo
import com.agoda.bapi.common.model.ess.EssTaxCountryResult
import com.agoda.bapi.common.model.payment.{PaymentModel, RequireBillingAddress}
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.product.BookingFlow.BookingFlow
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.proxy.{FraudApiClientProxy, FraudHttpClientProxy}
import com.agoda.bapi.common.repository.{CountriesRepository, TPRMRepository}
import com.agoda.bapi.common.service.{CreditCardOnFileService, FeatureAware, MessagesBag}
import com.agoda.bapi.common.token.flight.FlightToken
import com.agoda.bapi.common.token.{BookingTokenEncryptionHelper, PropertySetupBookingToken}
import com.agoda.bapi.common.util.{BessieUtils, PaymentUtils}
import com.agoda.bapi.common.{ExternalLoyaltyCampaign, MessageService, MockRequestContext}
import com.agoda.bapi.creation.model.db.{CardBinRangeInfo, CardClass}
import com.agoda.bapi.creation.model.{CreditCardInfo, PaymentLimitationInfo}
import com.agoda.bapi.creation.repository.{EbeLiteBookingRepository, PaymentLimitationRepository}
import com.agoda.bapi.server.addon.FinanceCalculator
import com.agoda.bapi.server.facades.SetupBookingFacadeImpl.requiredBankNameProductTypesList
import com.agoda.bapi.server.handler.context.{SetupBookingContext, SetupBookingSessionContext}
import com.agoda.bapi.server.mapper.ItineraryContextMapper
import com.agoda.bapi.server.model._
import com.agoda.bapi.server.reporting.BapiSetupV3DebugLogMessage
import com.agoda.bapi.server.repository.dto.papi.{CardPaymentRequestParameter, CashbackRedemptionParameter}
import com.agoda.bapi.server.repository.{AllotmentRequest, PrecheckRepository}
import com.agoda.bapi.server.service._
import com.agoda.bapi.server.service.allotment.{AllotmentPreCheckHelperImpl, AllotmentPreCheckStatus, AllotmentStatus}
import com.agoda.bapi.server.service.bookingFormRegularExpression.BookingFormRegularExpressionServiceImpl
import com.agoda.bapi.server.service.payment.PaymentService
import com.agoda.bapi.server.service.payment.model.{AvailablePaymentMethodsResult, SelectedPaymentMethod}
import com.agoda.bapi.server.utils.{FlightComplianceServiceImpl, SetupBookingMock}
import com.agoda.bapi.server.validator.ExternalLoyaltyPointsValidator
import com.agoda.externalloyalty.client.v2.model.DistributePointsResponse.RedemptionType
import com.agoda.finance.tax.models.BookingCountryIndicator
import com.agoda.flights.client.v2.model.{BillingAddressRequirements, ChargeablePaymentMethod, SearchResponseSupplierAcceptedCreditCard}
import com.agoda.fraud.proto.{WhitelistCCTokenRequest => NewWhitelistCCTokenRequest, WhitelistCCTokenResponse}
import com.agoda.mpb.common.CrossSellReasonType
import com.agoda.mpb.common.models.state.{ProductType => BapiProductType}
import com.agoda.mpbe.state.common.enums.PaymentMethod.{PaymentMethod => MPBPaymentMethod}
import com.agoda.paymentapiv2.client.v2.common.model.SetupPaymentRequestV2
import com.agoda.terracotta.clients.rest.models.WhitelistCreditCard
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import com.agoda.winterfell.output.{Campaign, _}
import com.fasterxml.jackson.databind.node.ObjectNode
import com.github.nscala_time.time.Imports.DateTime
import com.softwaremill.quicklens._
import enumerations.SpecialRequestIds
import generated.model.{BookingChargeCurrency, UserProfileV4}
import mocks.AlternativePreCheckBookingTokenMockHelper._
import mocks.PollingBookingTokenMockHelper.{multiBookingTokenString, multiBookingTokenStringTwo}
import mocks._
import models.pricing.enums.SwapRoomTypes
import models.starfruit.PropertyPricingJsonProtocol.ResponseStateToken
import models.starfruit.{AlternativeRoom, DisplayBasis, DisplayPrice, LoyaltyReasons}
import org.joda.time.LocalDate
import org.mockito.ArgumentMatchers.{any, anyBoolean, argThat, eq => mockitoEq}
import org.mockito.Mockito._
import org.mockito.{ArgumentCaptor, ArgumentMatcher, ArgumentMatchers, Mockito}
import org.scalatest
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.{TableDrivenPropertyChecks, TableFor2}
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{BeforeAndAfter, Inspectors}
import transformers.{Property, _}

import java.time.{LocalDate => JavaLocalDate}
import scala.concurrent.Future
import scala.language.postfixOps
import scala.util.Try

class SetupBookingFacadeSpec
    extends AsyncWordSpec
    with Matchers
    with BeforeAndAfter
    with ScalaFutures
    with RequestContextMock
    with PropertyMock
    with ProductTokenMockHelper
    with TableDrivenPropertyChecks
    with SetupBookingMock {

  val bookingsService                         = mock[BookingsService]
  val precheckRepository                      = mock[PrecheckRepository]
  val cmsContextFactory                       = mock[CmsContextFactory]
  val productService                          = mock[ProductsFacade]
  val messagesBag                             = mock[MessagesBag]
  val setupPaymentService                     = mock[SetupPaymentService]
  val ccofService                             = mock[CreditCardOnFileService]
  val tprmRepository                          = mock[TPRMRepository]
  val userContext                             = Some(UserContextMock.value.copy(requestOrigin = "TH"))
  val featureAware                            = mock[FeatureAware]
  val customerService                         = mock[CustomerService]
  val paymentUtil                             = mock[PaymentUtils]
  val cmsContext                              = mock[CmsContext]
  val messageService                          = mock[MessageService]
  val bookingConsentService                   = mock[BookingConsentService]
  val mockEbeRepository                       = mock[EbeLiteBookingRepository]
  val mockFraudHttpClientProxy                = mock[FraudHttpClientProxy]
  val mockFraudApiClientProxy                 = mock[FraudApiClientProxy]
  val mockCreditCardInfo                      = mock[CreditCardInfo]
  val paymentLimitationRepository             = mock[PaymentLimitationRepository]
  val externalLoyaltyService                  = mock[ExternalLoyaltyService]
  val customerMarketingDisplaySettingsService = mock[CustomerMarketingDisplaySettingsService]
  val saveCcofService                         = mock[SaveCcofService]
  val externalLoyaltyPointsValidator          = mock[ExternalLoyaltyPointsValidator]
  val mockCountriesRepository                 = mock[CountriesRepository]
  val mockFinanceCalculator                   = mock[FinanceCalculator]
  val overrideOccupancyConfig                 = Mockito.mock(classOf[OverrideOccupancyConfig], Mockito.RETURNS_DEEP_STUBS)
  val mockPaymentService                      = mock[PaymentService]
  val mockCegWlService                        = mock[CegWlService]
  val flightComplianceServiceImpl             = mock[FlightComplianceServiceImpl]
  val itineraryContextMapper                  = mock[ItineraryContextMapper]
  val cartContext                             = CartContext("referenceId")

  private val correlationId = "00000000-0000-0000-0000-000000000000"
  private val customerEmail = Some("<EMAIL>")

  val context: RequestContext =
    requestContext(messagesBag, userContext).copy(featureAware = Some(featureAware))
  implicit val setupContext: SetupBookingContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)
  val priceChangeSupplierBlackListConfig =
    Mockito.mock(classOf[PriceChangeSupplierBlacklistConfig], Mockito.RETURNS_DEEP_STUBS)
  val setupBookingRequest = SetupBookingRequest(
    correlationId = None,
    userContext = userContext,
    paymentRequest = Some(
      PaymentRequest(
        ccBin = None,
        selectedPaymentMethod = Some(1)
      )
    ),
    customerInfo = None,
    productsRequest = ProductsRequest(
      bookingToken = None,
      propertyRequests = Seq.empty,
      flightRequests = Seq.empty,
      packageRequest = None
    )
  )

  private def setupBookingRequestWithCartContext(baseRequest: Option[SetupBookingRequest] = None) = {
    val request = baseRequest.getOrElse(setupBookingRequest)
    request.copy(
      productsRequest = request.productsRequest.copy(cartContext = Some(cartContext))
    )
  }
  val chargeCurrOptions: Seq[CurrencyCode] = Seq("USD", "EUR", "THB")
  val requestContext: RequestContext       = MockRequestContext.create()
  implicit val wlInfo: WhiteLabelInfo      = mock[WhiteLabelInfo]

  val visa = PaymentMethodDetailsV2(
    id = 1,
    name = "Visa",
    paymentFlow = PaymentFlow.CreditCard,
    paymentGroupCategory = PaymentGroupCategory.CreditDebitCard,
    timeout = Some(0),
    gatewayName = None,
    icons = Seq(PaymentMethodIcon(1, "https://cdn{0}.agoda.net/images/mvc/default/ic_visa.png")),
    remarks = Seq(),
    chargeDateTypes = ChargeOption.PayNow,
    chargeOptions = Set(ChargeOption.PayNow, ChargeOption.PayLater),
    isRecommended = false,
    ranking = 1,
    defaultCurrency = "USD",
    cardNumRegEx = Some("^[0-9]{16}$"),
    cvcRegEx = Some("^[0-9]{3}$"),
    isLuhnCheckRecommended = Some(false),
    requiredFields = Some(Map()),
    isFapiaoEligible = Some(false),
    isTokenEnabled = None
  )

  val mastercard = PaymentMethodDetailsV2(
    id = 2,
    name = "MasterCard",
    paymentFlow = PaymentFlow.CreditCard,
    paymentGroupCategory = PaymentGroupCategory.CreditDebitCard,
    timeout = Some(0),
    gatewayName = None,
    icons = Seq(PaymentMethodIcon(1, "https://cdn{0}.agoda.net/images/mvc/default/ic_master.png")),
    remarks = Seq(),
    chargeDateTypes = ChargeOption.PayNow,
    chargeOptions = Set(ChargeOption.PayNow, ChargeOption.PayLater),
    isRecommended = false,
    ranking = 1,
    defaultCurrency = "USD",
    cardNumRegEx = Some("^[0-9]{16}$"),
    cvcRegEx = Some("^[0-9]{3}$"),
    isLuhnCheckRecommended = Some(false),
    requiredFields = Some(Map()),
    isFapiaoEligible = Some(false),
    isTokenEnabled = None
  )

  val jcb = PaymentMethodDetailsV2(
    id = 3,
    name = "JCB",
    paymentFlow = PaymentFlow.CreditCard,
    paymentGroupCategory = PaymentGroupCategory.CreditDebitCard,
    timeout = Some(0),
    gatewayName = None,
    icons = Seq(PaymentMethodIcon(1, "https://cdn{0}.agoda.net/images/mvc/default/ic_jcb.png")),
    remarks = Seq(),
    chargeDateTypes = ChargeOption.PayNow,
    chargeOptions = Set(ChargeOption.PayNow),
    isRecommended = false,
    ranking = 1,
    defaultCurrency = "USD",
    cardNumRegEx = Some("^[0-9]{16}$"),
    cvcRegEx = Some("^[0-9]{3}$"),
    isLuhnCheckRecommended = Some(false),
    requiredFields = Some(Map()),
    isFapiaoEligible = Some(false),
    isTokenEnabled = None
  )

  val amex = PaymentMethodDetailsV2(
    id = 4,
    name = "American Express",
    paymentFlow = PaymentFlow.CreditCard,
    paymentGroupCategory = PaymentGroupCategory.CreditDebitCard,
    timeout = Some(0),
    gatewayName = None,
    icons = Seq(PaymentMethodIcon(1, "https://cdn{0}.agoda.net/images/mvc/default/ic_americanexpress.png")),
    remarks = Seq(),
    chargeDateTypes = ChargeOption.PayNow,
    chargeOptions = Set(ChargeOption.PayNow, ChargeOption.PayLater),
    isRecommended = false,
    ranking = 1,
    defaultCurrency = "USD",
    cardNumRegEx = Some("^[0-9]{16}$"),
    cvcRegEx = Some("^[0-9]{3}$"),
    isLuhnCheckRecommended = Some(false),
    requiredFields = Some(Map()),
    isFapiaoEligible = Some(false),
    isTokenEnabled = None
  )

  val paymentMethodDetailsV2 = Seq(mastercard)

  val flightPaymentMethodDetailsV2 = Seq(visa, mastercard, amex)

  val propertyPaymentMethodDetailsV2 = Seq(visa, mastercard, jcb, amex)

  val activityPaymentMethodDetailsV2 = Seq(mastercard, visa, amex)

  val iconType1        = Seq(Icon(1, 1, "agoda.com/enabled.png"), Icon(1, 2, "agoda.com/disabled.png"))
  val iconType2        = Seq(Icon(2, 1, "agoda.com/enabled.png"), Icon(2, 2, "agoda.com/disabled.png"))
  val iconType3        = Seq(Icon(3, 1, "agoda.com/enabled.png"), Icon(1, 2, "agoda.com/disabled.png"))
  val paymentIconType1 = iconType1.map(i => PaymentMethodIcon(i.iconType, i.iconUrl))
  val paymentIconType2 = iconType2.map(i => PaymentMethodIcon(i.iconType, i.iconUrl))
  val paymentIconType3 = iconType3.map(i => PaymentMethodIcon(i.iconType, i.iconUrl))

  val ccofList = Seq(
    CreditCardOnFile(123, "1234", 1, paymentIconType1, true, Some(CreditCardExpiration(2040, 1))),
    CreditCardOnFile(456, "4567", 1, paymentIconType1, true, Some(CreditCardExpiration(2041, 1))),
    CreditCardOnFile(789, "7890", 2, paymentIconType2, true, Some(CreditCardExpiration(2042, 1)))
  )
  val creditCardInfo = Some(
    CreditCardInfo(
      Some(CurrencyInfo(1, "GBP", 2)),
      Some(CountryInfo(1, "United Kingdom", "United Kingdom", "GB", "GBR")),
      None
    )
  )

  val ncofList = Seq(
    NonCardOnFile(123, 1, "aaa", paymentIconType1, true),
    NonCardOnFile(456, 1, "bbb", paymentIconType1, true),
    NonCardOnFile(789, 2, "bbb", paymentIconType2, true)
  )

  private def getSetupBookingFacade: SetupBookingFacadeImpl =
    new SetupBookingFacadeImpl(
      bookingsService,
      cmsContextFactory,
      productService,
      setupPaymentService,
      ccofService,
      customerService,
      new CustomerRiskServiceImp(tprmRepository),
      new AllotmentPreCheckHelperImpl(precheckRepository, priceChangeSupplierBlackListConfig),
      paymentUtil,
      mockEbeRepository,
      mockFraudHttpClientProxy,
      mockFraudApiClientProxy,
      messageService,
      bookingConsentService,
      paymentLimitationRepository,
      externalLoyaltyService,
      new BookingFormRegularExpressionServiceImpl,
      customerMarketingDisplaySettingsService,
      saveCcofService,
      overrideOccupancyConfig,
      externalLoyaltyPointsValidator,
      mockCountriesRepository,
      mockFinanceCalculator,
      mockPaymentService,
      mockCegWlService,
      flightComplianceServiceImpl,
      itineraryContextMapper
    )

  val childRoom = defaultChildRoom(isNoCreditCardEligible = true, extraBedCount = 3)
  when(childRoom.uid).thenReturn(Some("room-uid"))
  when(childRoom.roomIdentifiers).thenReturn(Some("room-uid"))
  when(childRoom.loyaltyResponse).thenReturn(None)
  when(childRoom.originalRoomDetail).thenReturn(None)
  when(childRoom.campaignPromotions).thenReturn(None)
  when(childRoom.cashbackRedemptionBoundaries).thenReturn(None)
  when(childRoom.priceAdjustmentId).thenReturn(None)

  val property =
    createMockProperty(
      propertyId = 1L,
      masterRooms = Seq(createMockMasterRoom(typeId = 3L, name = None, imageUrl = "", childRooms = List(childRoom))),
      getAllRooms = List(childRoom)
    )
  val propertyNoMasterRoom =
    createMockProperty(
      propertyId = 1L,
      masterRooms = Nil
    )

  val SinglePropertyProductData = ProductData(
    properties = Seq(
      BookingPropertiesData(
        id = "1",
        content = "1",
        papiProperties = Some(
          transformers.Properties(
            Seq(property),
            None
          )
        ),
        packageRequest = None,
        papiPropertyStatus = PapiPropertyStatus.Ok,
        selectedChargeOption = None
      )
    ),
    flights = Seq.empty,
    cars = Seq.empty,
    protections = Seq.empty,
    activities = Seq.empty,
    totalPriceDisplay = None,
    priceDisplayType = None,
    packageToken = None,
    priceChange = None,
    priceConfirmed = true
  )

  val SingleFlightData = FlightConfirmationData(
    id = "",
    token = "",
    submitToken = FlightToken(data = None),
    isCompleted = true,
    hasFlight = true,
    hasContent = true,
    isHackerFare = false,
    packageRequest = None,
    flightPricing = None,
    priceChange = None,
    flightItinerary = Some(
      FlightItinerary(
        id = "",
        slices = Seq.empty,
        paymentModel = PaymentModel.Merchant.id,
        acceptedCreditCards = Nil
      )
    ),
    paxNumberByType = Map.empty,
    billingAddressRequirements = Some(
      BillingAddressRequirements(postCode = true, country = true, streetAddress = false, city = true, state = true)
    )
  )

  val SingleActivityData = ActivityConfirmationData("sample id", "sample content", true, None, None, None)

  val PackageProductData = ProductData(
    properties = Seq(
      BookingPropertiesData(
        id = "1",
        content = "1",
        papiProperties = Some(
          transformers.Properties(
            Seq(property),
            None
          )
        ),
        packageRequest = None,
        papiPropertyStatus = PapiPropertyStatus.Ok,
        selectedChargeOption = None
      )
    ),
    flights = Seq(SingleFlightData),
    cars = Seq.empty,
    protections = Seq.empty,
    activities = Seq.empty,
    totalPriceDisplay = None,
    priceDisplayType = None,
    packageToken = None,
    priceChange = None,
    priceConfirmed = true
  )

  val SingleAgencyFlightProductData = ProductData(
    properties = Seq.empty,
    flights = Seq(
      FlightConfirmationData(
        id = "",
        token = "",
        submitToken = FlightToken(data = None),
        isCompleted = true,
        hasFlight = true,
        hasContent = true,
        isHackerFare = false,
        packageRequest = None,
        flightPricing = None,
        priceChange = None,
        flightItinerary = Some(
          FlightItinerary(
            id = "",
            slices = Seq.empty,
            paymentModel = PaymentModel.Agency.id,
            acceptedCreditCards = Seq(
              SearchResponseSupplierAcceptedCreditCard(
                creditCardTypeId = 1,
                creditCardFeeTypeId = 1,
                creditCardFeeAmount = 0.0
              ),
              SearchResponseSupplierAcceptedCreditCard(
                creditCardTypeId = 2,
                creditCardFeeTypeId = 1,
                creditCardFeeAmount = 0.0
              )
            )
          )
        ),
        paxNumberByType = Map.empty,
        billingAddressRequirements = Some(
          BillingAddressRequirements(postCode = true, country = true, streetAddress = false, city = true, state = true)
        )
      )
    ),
    cars = Seq.empty,
    protections = Seq.empty,
    activities = Seq.empty,
    totalPriceDisplay = None,
    priceDisplayType = None,
    packageToken = None,
    priceChange = None,
    priceConfirmed = true
  )
  val multipleAgencyFlightProductData = ProductData(
    properties = Seq.empty,
    flights = Seq(
      FlightConfirmationData(
        id = "",
        token = "",
        submitToken = FlightToken(data = None),
        isCompleted = true,
        hasFlight = true,
        hasContent = true,
        isHackerFare = false,
        packageRequest = None,
        flightPricing = None,
        priceChange = None,
        flightItinerary = Some(
          FlightItinerary(
            id = "",
            slices = Seq.empty,
            paymentModel = PaymentModel.Agency.id,
            acceptedCreditCards = Seq(
              SearchResponseSupplierAcceptedCreditCard(
                creditCardTypeId = 1,
                creditCardFeeTypeId = 1,
                creditCardFeeAmount = 0.0
              ),
              SearchResponseSupplierAcceptedCreditCard(
                creditCardTypeId = 2,
                creditCardFeeTypeId = 1,
                creditCardFeeAmount = 0.0
              )
            )
          )
        ),
        paxNumberByType = Map.empty,
        billingAddressRequirements = Some(
          BillingAddressRequirements(postCode = true, country = true, streetAddress = false, city = true, state = true)
        )
      ),
      FlightConfirmationData(
        id = "1",
        token = "",
        submitToken = FlightToken(data = None),
        isCompleted = true,
        hasFlight = true,
        hasContent = true,
        isHackerFare = false,
        packageRequest = None,
        flightPricing = None,
        priceChange = None,
        flightItinerary = Some(
          FlightItinerary(
            id = "",
            slices = Seq.empty,
            paymentModel = PaymentModel.Agency.id,
            acceptedCreditCards = Seq(
              SearchResponseSupplierAcceptedCreditCard(
                creditCardTypeId = 1,
                creditCardFeeTypeId = 1,
                creditCardFeeAmount = 0.0
              ),
              SearchResponseSupplierAcceptedCreditCard(
                creditCardTypeId = 2,
                creditCardFeeTypeId = 1,
                creditCardFeeAmount = 0.0
              ),
              SearchResponseSupplierAcceptedCreditCard(
                creditCardTypeId = 3,
                creditCardFeeTypeId = 1,
                creditCardFeeAmount = 0.0
              )
            )
          )
        ),
        paxNumberByType = Map.empty,
        billingAddressRequirements = Some(
          BillingAddressRequirements(postCode = true, country = true, streetAddress = true, city = true, state = false)
        )
      )
    ),
    cars = Seq.empty,
    protections = Seq.empty,
    activities = Seq.empty,
    totalPriceDisplay = None,
    priceDisplayType = None,
    packageToken = None,
    priceChange = None,
    priceConfirmed = true
  )

  val paymentLimitationInfoTemplate = PaymentLimitationInfo(
    siteId = 0,
    paymentMethodIds = Some(Seq(2)),
    paymentMethodNames = Some(Seq("MasterCard")),
    bookingStartDate = DateTime.now.toString,
    bookingEndDate = DateTime.now.toString,
    errorCMSId = 333333,
    alternatePaymentCMSId = None,
    platformIds = Some(Seq(1, 1007)),
    binList = Some(Seq(111111, 222222))
  )

  val partnerConfigResponse =
    Set(
      Partner(
        partnerCode = Some("KTC"),
        name = Some("KTC"),
        logoImageUrl = Some("https://cdn{0}.agoda.net/images/mvc/default/ex.png"),
        loginRedirectUrl = Some("http://hk-ulqa-2001.agoda.local:9000/start/KTC/direct"),
        cmsId = Some(13777)
      )
    )

  val externalLoyaltyMemberBalanceResponse =
    Seq(
      SubLoyaltyProgram(
        pointsBalance = Some(1700.43),
        disabled = Some(false),
        programId = Some("1"),
        programName = Some("KTC"),
        minimumPointsToRedeem = Some(100.20),
        programLogoUrl = Some("\"https://cdn{0}.agoda.net/images/mvc/default/ex.png\"")
      )
    )

  val externalLoyaltyRedemption = RedemptionResponse(
    points = 1L,
    pointsValueInUsd = 2L,
    pointsRange = Some(SetupResponseELEPointRange(maxPointsApplicable = Some(3L), minPointsApplicable = Some(4L))),
    redemptionType = Some(RedemptionType.FLEXIBLE)
  )

  val externalLoyaltyPaymentLimitation = PaymentLimitations(
    partnerCode = "1",
    paymentMethodIds = Some(Seq(1, 2, 3, 4)),
    paymentMethodNames = Some(Seq("Master", "Visa")),
    binRanges = Some(
      Seq(
        BinRange(
          bookingStartDate = Some(java.time.LocalDate.parse("2000-01-01")),
          bookingEndDate = None,
          binList = Some(Seq(BinListRange(from = 111, to = 222)))
        )
      )
    ),
    errorCMSId = 1,
    alternatePaymentCMSId = Some(2)
  )

  val externalLoyaltyPartnerConfigResponse = PartnerConfigResponse(
    paymentLimitations = externalLoyaltyPaymentLimitation
  )

  val roomIdentifier = "identity"

  val pricingRequest = PricingRequest(
    isMse = false,
    requiredPrice = "",
    requiredBasis = "",
    isRPM2Included = false,
    selectedPointMaxId = None,
    isIncludeUsdAndLocalCurrency = false,
    allowOverrideOccupancy = false,
    enableOpaqueChannel = false,
    isAllowRoomTypeNotGuarantee = false,
    synchronous = true,
    partnerLoyaltyProgramId = Some(0)
  )

  val propertySearchCriteria = PropertySearchCriteria(
    propertyId = Some(1),
    roomIdentifier = roomIdentifier,
    occupancyRequest = com.agoda.bapi.common.message.OccupancyRequest(1, 0, 1),
    durationRequest = com.agoda.bapi.common.message.DurationRequest(LocalDate.now(), 1),
    pricingRequest = Some(pricingRequest),
    papiContextRequest = Some(PapiContextRequest(true, trafficTypeId = Some(1), rawBotProfile = "", showCms = false)),
    roomSelectionRequest = None,
    propertyRequest = None,
    simplifiedRoomSelectionRequest = None,
    partnerRequest = None
  )

  val citiBenefitPromoCampaign = Campaign(
    "CITI4NF",
    DiscountType.Amount,
    100.0,
    Some("USD"),
    Some(1),
    Some(1),
    Some("2099-12-31T00:00:00Z"),
    Some("2099-12-31T00:00:00Z"),
    ProductType.HOTELS
  )

  val citiOnyxBenefitCampaign = Campaign(
    "CITIONYX",
    DiscountType.HotelCredit,
    100.0,
    Some("USD"),
    Some(1),
    Some(1),
    Some("2099-12-31T00:00:00Z"),
    Some("2099-12-31T00:00:00Z"),
    ProductType.HOTELS,
    Some(100),
    Some(300)
  )

  private val subLoyaltyProgram = SubLoyaltyPrograms(
    pointsBalance = Some(10.0),
    campaigns = Some(Seq(citiBenefitPromoCampaign)),
    creditCardDetail = Some(com.agoda.winterfell.output.CreditCardInfo())
  )

  private val thankYouPartnerSpecificParams    = Some(Map(BessieUtils.ChannelId -> BessieUtils.CorporateCardChannelId))
  private val nonThankYouPartnerSpecificParams = Some(Map(BessieUtils.ChannelId -> "non thank you"))
  private val externalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
    userProfileInfo = None,
    subLoyaltyPrograms = Seq(
      subLoyaltyProgram
    ),
    partnerSpecificParams = thankYouPartnerSpecificParams
  )

  private val nonTYExternalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
    userProfileInfo = None,
    subLoyaltyPrograms = Seq(
      subLoyaltyProgram
    ),
    partnerSpecificParams = nonThankYouPartnerSpecificParams
  )

  private val loyaltyProfile = Some(
    LoyaltyProfile(
      None,
      Vector.empty[ObjectNode],
      None,
      None,
      None,
      Some(
        externalLoyaltyUserProfileResponse
      )
    )
  )

  private val nonTYLoyaltyProfile = Some(
    LoyaltyProfile(
      None,
      Vector.empty[ObjectNode],
      None,
      None,
      None,
      Some(
        nonTYExternalLoyaltyUserProfileResponse
      )
    )
  )

  private val mockAvailablePaymentMethodsResult = mock[AvailablePaymentMethodsResult]

  before {
    reset(bookingsService)
    reset(precheckRepository)
    reset(cmsContextFactory)
    reset(productService)
    reset(messagesBag)
    reset(setupPaymentService)
    reset(ccofService)
    reset(customerService)
    reset(tprmRepository)
    reset(featureAware)
    reset(paymentUtil)
    reset(messageService)
    reset(mockEbeRepository)
    reset(mockCreditCardInfo)
    reset(externalLoyaltyService)
    reset(mockFinanceCalculator)
    reset(wlInfo)
    reset(mockPaymentService)
    reset(mockCegWlService)
    reset(flightComplianceServiceImpl)
    reset(mockFraudHttpClientProxy)
    reset(mockFraudApiClientProxy)
    when(cmsContextFactory.get(any(), any())).thenReturn(Future.successful(cmsContext))
    when(
      productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
        any()
      )
    )
      .thenReturn(
        Future.successful(
          ProductData(
            properties = Seq.empty,
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = false
          )
        )
      )
    when(
      productService
        .createProductToken(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(
          any()
        )
    ).thenReturn(Try {
      TokenMessage("mockToken", 1)
    })
    when(bookingsService.getSupportedCurrencies(any(), any(), any(), any())).thenReturn(chargeCurrOptions)
    when(bookingsService.getCreditCardInfo(any(), any(), any(), any()))
      .thenReturn(Future.successful(creditCardInfo))
    when(customerService.getGiftCardBalance(any(), any())(any()))
      .thenReturn(Future.successful(0.0))
    when(customerService.getMemberInfo(any(), any())(any()))
      .thenReturn(Future.successful(None))
    when(customerService.getMemberLoyaltyProfile(any(), any(), any(), any(), any(), any())(any()))
      .thenReturn(Future.successful(None))
    when(customerService.getPartnerClaim(any(), any(), any())(any()))
      .thenReturn(Future.successful(None))

    when(
      ccofService.getCreditCardAndNonCardOnFileList(any(), any(), any(), any(), any(), any(), any(), any())(
        any(),
        any()
      )
    )
      .thenReturn(Future.successful((Seq.empty, Seq.empty)))

    when(bookingsService.isEmailMarketingOptedOutByDefault(any()))
      .thenReturn(false)
    when(
      bookingsService.getInstallmentData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
        any()
      )
    )
      .thenReturn(Future.successful(None, None))

    when(mockPaymentService.getPaymentMethodDetails(any(), any(), any())(any()))
      .thenReturn(Future.successful(Some(SelectedPaymentMethod(1, Set("USD", "OMR", "EUR", "THB"), "USD", false))))
    when(paymentUtil.isNoCvc(any(), any(), any(), any(), any(), any())).thenReturn(true)
    when(setupContext.requestContext).thenReturn(context)
    when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
    when(setupContext.session).thenReturn(SetupBookingSessionContext())
    when(setupContext.whiteLabelInfo).thenReturn(
      WhiteLabelInfo(
        WhiteLabel.Agoda,
        FeaturesConfiguration(userProfile =
          UserProfileV4(
            isEmailSubscriptionEnabled = None,
            isSocialEnabled = None,
            isPaymentEnabled = Some(true),
            isPromotionEmailsEnabled = None,
            isBookingAssistRemindersEnabled = None,
            newsletterFrequency = None,
            isUpcomingTripOfferEmailsEnabled = None,
            preferenceId = None
          )
        )
      )
    )
    when(mockEbeRepository.isRedirectCard(any())).thenReturn(Future.successful(false))
    when(mockEbeRepository.isPaymentTokenEnabled(any(), any(), any())).thenReturn(Future.successful(false))
    when(mockEbeRepository.isCurrencyOffered(any())).thenReturn(Future.successful(true))
    when(externalLoyaltyService.applyExternalLoyaltyPaymentLimitation(any(), any()))
      .thenReturn(ExternalLoyaltyPaymentLimitationError(None, None))
    when(externalLoyaltyService.createExternalLoyalty(any(), any(), any(), any())(any()))
      .thenReturn(Some(ExternalLoyalty(Some(partnerConfigResponse), Some(externalLoyaltyMemberBalanceResponse))))
    when(externalLoyaltyService.getExternalLoyaltyResponse(any(), any()))
      .thenReturn(
        Future.successful(
          ExternalLoyaltyResponse(
            Some(partnerConfigResponse),
            Some(externalLoyaltyPartnerConfigResponse),
            Some(externalLoyaltyMemberBalanceResponse),
            None
          )
        )
      )
    when(externalLoyaltyService.getPaymentLimitationCMSIds(any()))
      .thenReturn(Seq(1, 2))
    when(mockCreditCardInfo.countryInfo).thenReturn(Some(CountryInfo(1, "Monaco", "", "", "")))
    val countryInfos = Seq(
      CountryInfo(1111, "country-name", "country-nationality", "country-iso", "country-iso2"),
      CountryInfo(198, "Malaysia", "Malaysia", "MY", "MY"),
      CountryInfo(2, "Abc", "Abc", "AB", "AB"),
      CountryInfo(118, "Belgium", "Belgium", "BEL", "BEL"),
      CountryInfo(120, "Nigeria", "Nigerian", "NGA", "NG")
    )
    when(mockCountriesRepository.getCountries()).thenReturn(
      Future.successful(countryInfos)
    )
    when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
      .thenReturn(
        Future.successful(
          AvailablePaymentMethodsResult(paymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)
        )
      )
    when(mockCegWlService.getProductWaiverReason(any())(any()))
      .thenReturn(Future.successful(None))
    when(mockFinanceCalculator.determineCountryOfBooker(any[BookingCountryIndicator]))
      .thenReturn(EssTaxCountryResult(None, None))
  }

  def getFirstChildRoomBookingData(property: Property): Option[EnrichedBookingRoom] =
    property.booking.flatMap(_.rooms.headOption)

  "SetupBookingFacade" should {
    implicit val wlInfo: WhiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration())
    "return response successfully with SetupHotel Migration is A" in {
      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])

      service.setupBooking(setupBookingRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]

          verify(bookingConsentService, times(1)).getBookingConsent(any(), any(), any())(any())
          response.success shouldBe true
          response.bookingResponse
            .map(_.chargeCurrencyOptions.foreach(c => chargeCurrOptions.contains(c.currencyCode) shouldBe true))
          response.bookingResponse.isDefined shouldBe true
          response.bookingResponse.map(_.paymentMethods) shouldBe Some(mockPaymentMethods)
          response.bookingResponse.map(_.products.tripProtections) shouldBe Some(Seq.empty)
        }
      }
    }

    "return response successfully when calling paymentService" in {
      val service                                  = getSetupBookingFacade
      val newPaymentMethod                         = mockPaymentMethods.map(_.copy(id = 2))
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])

      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(AvailablePaymentMethodsResult(newPaymentMethod, SetupPaymentRequestV2(), Seq.empty, None))
        )
      service.setupBooking(setupBookingRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]

          verify(bookingConsentService, times(2)).getBookingConsent(any(), any(), any())(any())
          response.success shouldBe true
          response.bookingResponse
            .map(_.chargeCurrencyOptions.foreach(c => chargeCurrOptions.contains(c.currencyCode) shouldBe true))
          response.bookingResponse.isDefined shouldBe true
          response.bookingResponse.map(_.paymentMethods) shouldBe Some(mockPaymentMethods)
          response.bookingResponse.map(_.products.tripProtections) shouldBe Some(Seq.empty)
        }
      }
    }

    "return response successfully with newPaymentMethod" in {
      val service                                  = getSetupBookingFacade
      val newPaymentMethod                         = mockPaymentMethods.map(_.copy(id = 1))
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])

      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(AvailablePaymentMethodsResult(newPaymentMethod, SetupPaymentRequestV2(), Seq.empty, None))
        )
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleFlight)

      service.setupBooking(setupBookingRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]

          response.success shouldBe true
          response.bookingResponse
            .map(_.chargeCurrencyOptions.foreach(c => chargeCurrOptions.contains(c.currencyCode) shouldBe true))
          response.bookingResponse.isDefined shouldBe true
          response.bookingResponse.map(_.paymentMethods) shouldBe Some(newPaymentMethod)
          response.bookingResponse.map(_.products.tripProtections) shouldBe Some(Seq.empty)
        }
      }
    }

    "return response successfully when selected payment method not provided" in {
      val setupBookingRequestWithPaymentMethod = setupBookingRequest.copy(paymentRequest =
        Some(
          PaymentRequest(
            ccBin = None,
            ccId = None,
            selectedPaymentMethod = None,
            selectedChargeCurrency = None
          )
        )
      )
      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])

      service.setupBooking(setupBookingRequestWithPaymentMethod).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]

          response.success shouldBe true
          response.bookingResponse
            .map(_.chargeCurrencyOptions.foreach(c => chargeCurrOptions.contains(c.currencyCode) shouldBe true))
          response.bookingResponse.isDefined shouldBe true
          response.bookingResponse.map(_.paymentMethods) shouldBe Some(mockPaymentMethods)
          response.bookingResponse.map(_.products.tripProtections) shouldBe Some(Seq.empty)
        }
      }
    }

    "return response successfully when aabInfo is provided" in {
      val setupBookingRequestWithAabInfo = spy(
        setupBookingRequest.copy(aabInfo =
          Some(
            AabInfo(
              flight = Some(FlightAabInfo(facilitationFeeWaiverReasonId = Some(3)))
            )
          )
        )
      )
      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])

      service.setupBooking(setupBookingRequestWithAabInfo).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]

          verify(setupBookingRequestWithAabInfo, times(1)).aabInfo
          response.success shouldBe true
          response.bookingResponse.isDefined shouldBe true
        }
      }
    }

    "return aabData correctly" in {
      val service      = getSetupBookingFacade
      val waiverReason = Seq(WaiverReason(id = Some(1), title = Some("title"), description = Some("Description")))
      when(mockCegWlService.getProductWaiverReason(any())(any()))
        .thenReturn(
          Future.successful(
            Some(
              AabData(flight =
                Some(
                  FlightAabData(waiverReason)
                )
              )
            )
          )
        )
      service.setupBooking(setupBookingRequest).map { response =>
        {
          verify(mockCegWlService, times(1)).getProductWaiverReason(any())(any())
          response.success shouldBe true
          response.bookingResponse.isDefined shouldBe true
          response.bookingResponse.flatMap(_.aabData).flatMap(_.flight) shouldBe Some(FlightAabData(waiverReason))
        }
      }
    }

    "return empty response when aabData is not present" in {
      val service = getSetupBookingFacade
      when(mockCegWlService.getProductWaiverReason(any())(any()))
        .thenReturn(
          Future.successful(
            None
          )
        )
      service.setupBooking(setupBookingRequest).map { response =>
        {
          verify(mockCegWlService, times(1)).getProductWaiverReason(any())(any())
          response.success shouldBe true
          response.bookingResponse.isDefined shouldBe true
          response.bookingResponse.flatMap(_.aabData) shouldBe None
        }
      }
    }

    "return property payment method with corrected isRecommended" in {
      reset(setupPaymentService)
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(propertyPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)
          )
        )

      val service = getSetupBookingFacade

      val token = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenString)
      val productRequestWithProperty = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        ),
        bookingToken = token.toOption
      )
      val setupBookingRequestProperty = setupBookingRequest.copy(productsRequest = productRequestWithProperty)
      val expectedPaymentMethod       = propertyPaymentMethodDetailsV2
      service.setupBooking(setupBookingRequestProperty).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse
            .map(_.chargeCurrencyOptions.foreach(c => chargeCurrOptions.contains(c.currencyCode) shouldBe true))
          response.bookingResponse.isDefined shouldBe true
          response.bookingResponse.map(_.paymentMethods) shouldBe Some(expectedPaymentMethod)
          response.bookingResponse.map(_.products.tripProtections) shouldBe Some(Seq.empty)
        }
      }
    }

    "return loyalty partners successfully" in {
      val service = getSetupBookingFacade
      val productRequestWithProperty = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = PropertySearchCriteria(
              propertyId = Some(1),
              roomIdentifier = "",
              occupancyRequest = com.agoda.bapi.common.message.OccupancyRequest(1, 0, 1),
              durationRequest = com.agoda.bapi.common.message.DurationRequest(LocalDate.now(), 1),
              pricingRequest = Some(
                PricingRequest(
                  isMse = false,
                  requiredPrice = "",
                  requiredBasis = "",
                  isRPM2Included = false,
                  selectedPointMaxId = None,
                  isIncludeUsdAndLocalCurrency = false,
                  allowOverrideOccupancy = false,
                  enableOpaqueChannel = false,
                  isAllowRoomTypeNotGuarantee = false,
                  synchronous = true,
                  partnerLoyaltyProgramId = Some(0)
                )
              ),
              papiContextRequest =
                Some(PapiContextRequest(true, trafficTypeId = Some(1), rawBotProfile = "", showCms = false)),
              roomSelectionRequest = None,
              propertyRequest = None,
              simplifiedRoomSelectionRequest = None,
              partnerRequest = None
            )
          )
        )
      )
      val requestWithCampaignInfo =
        setupBookingRequest.copy(
          productsRequest = productRequestWithProperty,
          campaignInfo = Some(CampaignInfoRequest(Some(85), 1, "EmailCreditCard"))
        )

      service.setupBooking(requestWithCampaignInfo).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse.isDefined shouldBe true
          response.bookingResponse.flatMap(_.externalLoyalty).flatMap(_.loyaltyPartners) shouldBe Some(
            partnerConfigResponse
          )
        }
      }
    }

    "return campaigns in externalLoyalty for Citi Whitelabel" in {
      val someCapiToken     = Some("SomeCapiToken")
      val partnerClaimToken = "citipartnerclaimtoken"
      val currency          = "USD"
      val loyaltyProfile = Some(
        LoyaltyProfile(
          None,
          Vector.empty[ObjectNode],
          None,
          None,
          None,
          Some(
            ExternalLoyaltyUserProfileResponse(
              None,
              Seq(
                SubLoyaltyPrograms(
                  pointsBalance = Some(10.0),
                  campaigns = Some(
                    Seq(
                      Campaign(
                        "CITI4NF",
                        DiscountType.Amount,
                        100.0,
                        Some(currency),
                        Some(1),
                        None,
                        None,
                        None,
                        ProductType.HOTELS
                      )
                    )
                  )
                )
              )
            )
          )
        )
      )
      val externalLoyaltyCampaign = ExternalLoyaltyCampaign(
        "CITI4NF",
        Some(LoyaltyDiscountType.fromCapiDiscountType(DiscountType.Amount.toString)),
        Some(100.0),
        Some(currency),
        Some(1),
        None,
        None,
        None,
        None,
        None
      )
      when(
        customerService.getMemberLoyaltyProfile(
          WhiteLabel.CitiUS,
          someCapiToken,
          Some(partnerClaimToken),
          Some(currency)
        )(setupContext)
      )
        .thenReturn(Future.successful(loyaltyProfile))
      when(externalLoyaltyService.createExternalLoyalty(any(), any(), any(), any())(any()))
        .thenReturn(
          Some(
            ExternalLoyalty(
              Some(partnerConfigResponse),
              Some(externalLoyaltyMemberBalanceResponse),
              None,
              None,
              None,
              Some(List(externalLoyaltyCampaign))
            )
          )
        )
      val mockLoyaltyRequest = mock[LoyaltyRequest]
      when(mockLoyaltyRequest.partnerClaimToken).thenReturn(Some(partnerClaimToken))

      val bessieSetupBookingRequest =
        setupBookingRequest.copy(
          userContext = userContext.map(_.copy(capiToken = someCapiToken, currency = currency)),
          loyaltyRequest = Some(mockLoyaltyRequest),
          paymentRequest = setupBookingRequest.paymentRequest.map(
            _.copy(
              selectedChargeCurrency = Some(currency)
            )
          )
        )
      when(setupContext.whiteLabelInfo).thenReturn(
        WhiteLabelInfo(
          WhiteLabel.CitiUS,
          FeaturesConfiguration(userProfile =
            UserProfileV4(
              isEmailSubscriptionEnabled = None,
              isSocialEnabled = None,
              isPaymentEnabled = Some(true),
              isPromotionEmailsEnabled = None,
              isBookingAssistRemindersEnabled = None,
              newsletterFrequency = None,
              isUpcomingTripOfferEmailsEnabled = None,
              preferenceId = None
            )
          )
        )
      )
      val service = getSetupBookingFacade
      service.setupBooking(bessieSetupBookingRequest).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse.isDefined shouldBe true
          response.bookingResponse.flatMap(_.externalLoyalty).flatMap(_.loyaltyPartners) shouldBe Some(
            partnerConfigResponse
          )
          verify(customerService)
            .getMemberLoyaltyProfile(WhiteLabel.CitiUS, someCapiToken, Some(partnerClaimToken), Some(currency))
          response.bookingResponse.flatMap(_.externalLoyalty).flatMap(_.campaigns).map(_.head) shouldBe Some(
            externalLoyaltyCampaign
          )
        }
      }
    }

    "return CITI300BENEF campaigns in externalLoyalty for Citi Whitelabel" in {
      val someCapiToken     = Some("SomeCapiToken")
      val partnerClaimToken = "citipartnerclaimtoken"
      val currency          = "USD"
      val loyaltyProfile = Some(
        LoyaltyProfile(
          None,
          Vector.empty[ObjectNode],
          None,
          None,
          None,
          Some(
            ExternalLoyaltyUserProfileResponse(
              None,
              Seq(
                SubLoyaltyPrograms(
                  pointsBalance = Some(10.0),
                  campaigns = Some(
                    Seq(
                      Campaign(
                        "CITI300BENEF",
                        DiscountType.HotelCredit,
                        300.0,
                        Some(currency),
                        Some(1),
                        None,
                        None,
                        None,
                        ProductType.HOTELS,
                        Some(300),
                        Some(300)
                      )
                    )
                  )
                )
              )
            )
          )
        )
      )
      val externalLoyaltyCampaign = ExternalLoyaltyCampaign(
        "CITI300BENEF",
        Some(LoyaltyDiscountType.fromCapiDiscountType(DiscountType.Amount.toString)),
        Some(300.0),
        Some(currency),
        Some(1),
        None,
        None,
        None,
        Some(300),
        Some(300)
      )
      when(
        customerService.getMemberLoyaltyProfile(
          WhiteLabel.CitiUS,
          someCapiToken,
          Some(partnerClaimToken),
          Some(currency)
        )(setupContext)
      )
        .thenReturn(Future.successful(loyaltyProfile))
      when(externalLoyaltyService.createExternalLoyalty(any(), any(), any(), any())(any()))
        .thenReturn(
          Some(
            ExternalLoyalty(
              Some(partnerConfigResponse),
              Some(externalLoyaltyMemberBalanceResponse),
              None,
              None,
              None,
              Some(List(externalLoyaltyCampaign))
            )
          )
        )
      val mockLoyaltyRequest = mock[LoyaltyRequest]
      when(mockLoyaltyRequest.partnerClaimToken).thenReturn(Some(partnerClaimToken))

      val bessieSetupBookingRequest =
        setupBookingRequest.copy(
          userContext = userContext.map(_.copy(capiToken = someCapiToken, currency = currency)),
          loyaltyRequest = Some(mockLoyaltyRequest),
          paymentRequest = setupBookingRequest.paymentRequest.map(
            _.copy(
              selectedChargeCurrency = Some(currency)
            )
          )
        )
      when(setupContext.whiteLabelInfo).thenReturn(
        WhiteLabelInfo(
          WhiteLabel.CitiUS,
          FeaturesConfiguration(userProfile =
            UserProfileV4(
              isEmailSubscriptionEnabled = None,
              isSocialEnabled = None,
              isPaymentEnabled = Some(true),
              isPromotionEmailsEnabled = None,
              isBookingAssistRemindersEnabled = None,
              newsletterFrequency = None,
              isUpcomingTripOfferEmailsEnabled = None,
              preferenceId = None
            )
          )
        )
      )
      val service = getSetupBookingFacade
      service.setupBooking(bessieSetupBookingRequest).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse.isDefined shouldBe true
          response.bookingResponse.flatMap(_.externalLoyalty).flatMap(_.loyaltyPartners) shouldBe Some(
            partnerConfigResponse
          )
          verify(customerService)
            .getMemberLoyaltyProfile(WhiteLabel.CitiUS, someCapiToken, Some(partnerClaimToken), Some(currency))
          response.bookingResponse.flatMap(_.externalLoyalty).flatMap(_.campaigns).map(_.head) shouldBe Some(
            externalLoyaltyCampaign
          )
        }
      }
    }

    "return externalLoyalty correctly based on externalLoyalty service response" in {
      when(externalLoyaltyService.createExternalLoyalty(any(), any(), any(), any())(any()))
        .thenReturn(
          Some(
            ExternalLoyalty(
              Some(partnerConfigResponse),
              Some(externalLoyaltyMemberBalanceResponse),
              redemption = Some(externalLoyaltyRedemption),
              isCashPlusPointsSupported = Some(true)
            )
          )
        )
      val service = getSetupBookingFacade
      service.setupBooking(setupBookingRequest).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse.isDefined shouldBe true
          val externalLoyalty = response.bookingResponse.flatMap(_.externalLoyalty)
          externalLoyalty.flatMap(_.isCashPlusPointsSupported) shouldBe Some(true)
          externalLoyalty.flatMap(_.loyaltyPartners) shouldBe Some(partnerConfigResponse)
          externalLoyalty.flatMap(_.memberBalances) shouldBe Some(externalLoyaltyMemberBalanceResponse)
          externalLoyalty.flatMap(_.redemption) shouldBe Some(externalLoyaltyRedemption)
        }
      }
    }

    "return property payment method successfully for payment limitation : EnablePaymentLimitation" in {
      reset(setupPaymentService)
      reset(paymentLimitationRepository)

      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(propertyPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)
          )
        )

      when(paymentLimitationRepository.getPaymentLimitation(any())).thenReturn(Future.successful(None))

      val service = getSetupBookingFacade

      val productRequestWithProperty = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        )
      )
      val enabledFeature = Some(Seq("EnablePaymentLimitation"))
      val setupBookingRequestProperty =
        setupBookingRequest.copy(productsRequest = productRequestWithProperty, enabledFeatures = enabledFeature)
      val expectedPaymentMethod = propertyPaymentMethodDetailsV2
      service.setupBooking(setupBookingRequestProperty).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse
            .foreach(_.chargeCurrencyOptions.foreach(c => chargeCurrOptions.contains(c.currencyCode) shouldBe true))
          response.bookingResponse.isDefined shouldBe true
          verify(paymentLimitationRepository, times(1)).getPaymentLimitation(any())
          response.bookingResponse.map(_.paymentMethods) shouldBe Some(expectedPaymentMethod)
          response.bookingResponse.map(_.products.tripProtections) shouldBe Some(Seq.empty)
        }
      }
    }

    "return property payment method successfully when B side of PMC-5033 for payment limitation: EnablePaymentLimitation" in {
      reset(setupPaymentService)
      reset(paymentLimitationRepository)

      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(propertyPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)
          )
        )

      val mockRequestContext = mock[RequestContext]
      val mockFeatureAware   = mock[FeatureAware]

      when(mockFeatureAware.RemoveReadingPMCCampaignDataFromBFDB).thenReturn(true)
      when(mockRequestContext.featureAware).thenReturn(Some(mockFeatureAware))
      when(setupContext.requestContext).thenReturn(context.copy(featureAware = Some(mockFeatureAware)))

      val service = getSetupBookingFacade

      // Create a request with the EnablePaymentLimitation feature enabled
      val productRequestWithProperty = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        )
      )
      val enabledFeature = Some(Seq("EnablePaymentLimitation"))
      val setupBookingRequestProperty =
        setupBookingRequest.copy(productsRequest = productRequestWithProperty, enabledFeatures = enabledFeature)

      val expectedPaymentMethod = propertyPaymentMethodDetailsV2

      // Call the service and verify the response
      service.setupBooking(setupBookingRequestProperty).map { response =>
        response.success shouldBe true
        response.bookingResponse.isDefined shouldBe true
        verifyNoInteractions(paymentLimitationRepository)
        response.bookingResponse.map(_.paymentMethods) shouldBe Some(expectedPaymentMethod)
        response.bookingResponse.map(_.products.tripProtections) shouldBe Some(Seq.empty)
      }
    }

    "return flight payment method with corrected isRecommended" in {
      reset(setupPaymentService)
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(
              flightPaymentMethodDetailsV2.intersect(propertyPaymentMethodDetailsV2),
              SetupPaymentRequestV2(),
              Seq.empty,
              None
            )
          )
        )

      val service = getSetupBookingFacade

      val productRequestWithFlight =
        ProductsRequest(flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None)))
      val setupBookingRequestFlight = setupBookingRequest.copy(productsRequest = productRequestWithFlight)
      val expectedPaymentMethod     = flightPaymentMethodDetailsV2
      service.setupBooking(setupBookingRequestFlight).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse
            .map(_.chargeCurrencyOptions.foreach(c => chargeCurrOptions.contains(c.currencyCode) shouldBe true))
          response.bookingResponse.isDefined shouldBe true
          response.bookingResponse.map(_.paymentMethods) shouldBe Some(expectedPaymentMethod)
          response.bookingResponse.map(_.products.tripProtections) shouldBe Some(Seq.empty)
        }
      }
    }

    "return flight billingAddressRequirements successfully with agency model" in {
      reset(setupPaymentService)
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(
              flightPaymentMethodDetailsV2.intersect(propertyPaymentMethodDetailsV2),
              SetupPaymentRequestV2(),
              Seq.empty,
              None
            )
          )
        )

      val productData = ProductData(
        properties = Seq.empty,
        flights = Seq(
          FlightConfirmationData(
            id = "",
            token = "",
            submitToken = FlightToken(data = None),
            isCompleted = true,
            hasFlight = true,
            hasContent = true,
            isHackerFare = false,
            packageRequest = None,
            flightPricing = None,
            priceChange = None,
            flightItinerary = Some(
              FlightItinerary(
                id = "",
                slices = Seq.empty,
                paymentModel = PaymentModel.Agency.id,
                acceptedCreditCards = Seq(
                  SearchResponseSupplierAcceptedCreditCard(
                    creditCardTypeId = 1,
                    creditCardFeeTypeId = 1,
                    creditCardFeeAmount = 0.0
                  ),
                  SearchResponseSupplierAcceptedCreditCard(
                    creditCardTypeId = 2,
                    creditCardFeeTypeId = 1,
                    creditCardFeeAmount = 0.0
                  )
                )
              )
            ),
            paxNumberByType = Map.empty,
            billingAddressRequirements = Some(
              BillingAddressRequirements(
                postCode = true,
                country = true,
                streetAddress = true,
                city = true,
                state = true
              )
            )
          )
        ),
        cars = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        totalPriceDisplay = None,
        priceDisplayType = None,
        packageToken = None,
        priceChange = None,
        priceConfirmed = true
      )
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      )
        .thenReturn(Future.successful(productData))

      val service = getSetupBookingFacade

      val productRequestWithFlight =
        ProductsRequest(flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None)))
      val setupBookingRequestFlight = setupBookingRequest.copy(productsRequest = productRequestWithFlight)
      val expectedBillingAddressRequirements =
        RequireBillingAddress(streetAddress = true, city = true, state = true, country = true, postalCode = true)
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleFlight)
      service.setupBooking(setupBookingRequestFlight).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse
            .map(_.chargeCurrencyOptions.foreach(c => chargeCurrOptions.contains(c.currencyCode) shouldBe true))
          response.bookingResponse.isDefined shouldBe true
          response.bookingResponse.map(_.products.tripProtections) shouldBe Some(Seq.empty)
          response.bookingResponse.flatMap(_.requireBillingAddress) shouldBe Some(expectedBillingAddressRequirements)
        }
      }
    }

    "return property and flight payment method with corrected isRecommended " in {
      reset(setupPaymentService)
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(flightPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)
          )
        )

      val service = getSetupBookingFacade

      val productsRequest = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        ),
        flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None))
      )
      val setupBookingRequestMultiProduct = setupBookingRequest.copy(productsRequest = productsRequest)
      val expectedPaymentMethod           = flightPaymentMethodDetailsV2

      when(setupContext.bookingFlowType).thenReturn(BookingFlow.Package)
      service.setupBooking(setupBookingRequestMultiProduct).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse
            .map(_.chargeCurrencyOptions.foreach(c => chargeCurrOptions.contains(c.currencyCode) shouldBe true))
          response.bookingResponse.isDefined shouldBe true
          response.bookingResponse.map(_.paymentMethods) shouldBe Some(expectedPaymentMethod)
        }
      }
    }

    "return wallet promotions successfully" in {
      val walletPromotion = WalletPromotion(
        campaignId = 12345,
        cid = 999,
        promotionCode = "CODE",
        value = 10,
        discountType = 1,
        currencyCode = None,
        displayName = "Promotion",
        cms = None,
        ineligibleReason = None,
        promoValidationType = None,
        campaignType = Some(CampaignType.PropertyPromotionCode),
        isSelected = Some(true)
      )
      val service = getSetupBookingFacade
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      )
        .thenReturn(
          Future.successful(
            ProductData(
              properties = Seq.empty,
              flights = Seq.empty,
              cars = Seq.empty,
              protections = Seq.empty,
              activities = Seq.empty,
              totalPriceDisplay = None,
              priceDisplayType = None,
              packageToken = None,
              priceChange = None,
              priceConfirmed = false,
              walletPromotions = Seq(walletPromotion)
            )
          )
        )

      service.setupBooking(setupBookingRequest).map { response =>
        {
          response.bookingResponse.map(_.walletPromotions) shouldBe Some(Seq(walletPromotion))
        }
      }
    }

    "return selection campaigns successfully" in {
      val campaign = CampaignPromotionInfo(
        campaignType = Some(CampaignType.PropertyPromotionCode),
        campaignId = 12345,
        cid = 999,
        promotionCode = "CODE",
        campaignName = "Promotion",
        campaignDiscountType = Some(CampaignDiscountType.Percent),
        originalDiscountPercentage = Some(10),
        originalDiscountAmount = None,
        originalDiscountCurrencyCode = None,
        validDateType = Some(CampaignValidDateType.BookingDate),
        dateValidFrom = Some(DateTime.parse("2024-01-01")),
        dateValidUntil = Some(DateTime.parse("2024-01-30")),
        isAutoApply = Some(true),
        isAutoApplyBookingForm = Some(false),
        inapplicableReasonString = None,
        inapplicableReason = None,
        isStateIdRequired = None,
        promotionCodeType = Some(PromotionCodeType.Wallet),
        status = Some(CampaignStatusType.Selected),
        cms = Some(List(PromoCmsData(1, Map("amount" -> "10"), 13))),
        messages = Some(BookingMockHelper.basePromotionInfoMessage()),
        campaignMessages = Some(
          Map(
            1 -> CampaignMessage(message = "ABC", params = Map("urgencyType" -> "1")),
            2 -> CampaignMessage(message = "DEF", params = Map("urgencyType" -> "2"))
          )
        )
      )
      val service = getSetupBookingFacade
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      )
        .thenReturn(
          Future.successful(
            ProductData(
              properties = Seq.empty,
              flights = Seq.empty,
              cars = Seq.empty,
              protections = Seq.empty,
              activities = Seq.empty,
              totalPriceDisplay = None,
              priceDisplayType = None,
              packageToken = None,
              priceChange = None,
              priceConfirmed = false,
              campaignPromotionInfo = Some(
                PromotionInfo(
                  maximumPromotionCodeDiscount = None,
                  maximumCreditCardDiscount = None,
                  campaigns = Seq(campaign)
                )
              )
            )
          )
        )

      service.setupBooking(setupBookingRequest).map { response =>
        {
          response.bookingResponse.flatMap(_.campaignPromotionInfo).map(_.campaigns) shouldBe Some(Seq(campaign))
        }
      }
    }

    "return property with credit card on file successfully" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(propertyPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)
          )
        )
      when(
        ccofService.getCreditCardAndNonCardOnFileList(any(), any(), any(), any(), any(), any(), any(), any())(
          any(),
          any()
        )
      ) thenReturn Future
        .successful(
          (ccofList, Seq.empty)
        )
      val service = getSetupBookingFacade

      val productRequestWithProperty = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        )
      )
      val setupBookingRequestProperty = setupBookingRequest.copy(productsRequest = productRequestWithProperty)
      service.setupBooking(setupBookingRequestProperty).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse.isDefined shouldBe true
          val availableCcof = response.bookingResponse.map(_.availableCreditCardsOnFile).get
          availableCcof shouldBe ccofList
        }
      }
    }

    "return flight with credit card on file successfully" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(
              flightPaymentMethodDetailsV2.intersect(propertyPaymentMethodDetailsV2),
              SetupPaymentRequestV2(),
              Seq.empty,
              None
            )
          )
        )
      when(
        ccofService.getCreditCardAndNonCardOnFileList(any(), any(), any(), any(), any(), any(), any(), any())(
          any(),
          any()
        )
      ) thenReturn Future
        .successful(
          (ccofList, Seq.empty)
        )
      val service = getSetupBookingFacade

      val productRequestWithFlight =
        ProductsRequest(flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None)))
      val setupBookingRequestFlight = setupBookingRequest.copy(productsRequest = productRequestWithFlight)

      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleFlight)
      service.setupBooking(setupBookingRequestFlight).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse.isDefined shouldBe true
          val availableCcof = response.bookingResponse.map(_.availableCreditCardsOnFile).get
          availableCcof shouldBe ccofList
        }
      }
    }

    "return property and flight with credit card on file successfully" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(flightPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)
          )
        )
      when(
        ccofService.getCreditCardAndNonCardOnFileList(any(), any(), any(), any(), any(), any(), any(), any())(
          any(),
          any()
        )
      ) thenReturn Future
        .successful(
          (ccofList, Seq.empty)
        )
      val service = getSetupBookingFacade

      val productsRequest = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        ),
        flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None))
      )
      val setupBookingRequestMultiProduct = setupBookingRequest.copy(productsRequest = productsRequest)

      when(setupContext.bookingFlowType).thenReturn(BookingFlow.Package)
      service.setupBooking(setupBookingRequestMultiProduct).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse.isDefined shouldBe true
          val availableCcof = response.bookingResponse.map(_.availableCreditCardsOnFile).get
          availableCcof shouldBe ccofList
        }
      }
    }

    "return non-card on file successfully" in {
      val service = getSetupBookingFacade

      when(
        ccofService.getCreditCardAndNonCardOnFileList(any(), any(), any(), any(), any(), any(), any(), any())(
          any(),
          any()
        )
      ) thenReturn Future
        .successful(
          (Seq.empty, ncofList)
        )

      service.setupBooking(setupBookingRequest).map { response =>
        response.success shouldBe true
        response.bookingResponse.map(_.availableNonCardsOnFile) shouldBe Some(ncofList)
      }
    }

    "return credit card and non-card on file successfully" in {
      val service = getSetupBookingFacade

      when(
        ccofService.getCreditCardAndNonCardOnFileList(any(), any(), any(), any(), any(), any(), any(), any())(
          any(),
          any()
        )
      ) thenReturn Future
        .successful(
          (ccofList, ncofList)
        )

      service.setupBooking(setupBookingRequest).map { response =>
        response.success shouldBe true
        response.bookingResponse.map(_.availableCreditCardsOnFile) shouldBe Some(ccofList)
        response.bookingResponse.map(_.availableNonCardsOnFile) shouldBe Some(ncofList)
      }
    }

    "return correct email regulated marketing flag for regulated country" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(
              flightPaymentMethodDetailsV2.intersect(propertyPaymentMethodDetailsV2),
              SetupPaymentRequestV2(),
              Seq.empty,
              None
            )
          )
        )
      when(bookingsService.isEmailMarketingOptedOutByDefault(any()))
        .thenReturn(true)

      val service = getSetupBookingFacade
      service.setupBooking(setupBookingRequest).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse.map(_.emailMarketingRegulated) shouldBe Some(true)
        }
      }
    }

    "return correct email regulated marketing flag for not regulated country" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(
              flightPaymentMethodDetailsV2.intersect(propertyPaymentMethodDetailsV2),
              SetupPaymentRequestV2(),
              Seq.empty,
              None
            )
          )
        )
      when(bookingsService.isEmailMarketingOptedOutByDefault(any()))
        .thenReturn(false)

      val service = getSetupBookingFacade
      service.setupBooking(setupBookingRequest).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse.map(_.emailMarketingRegulated) shouldBe Some(false)
        }
      }
    }

    "return customer risk status correctly" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(flightPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)
          )
        )
      when(tprmRepository.checkSuspicious(any())(any())) thenReturn Future.successful(CustomerRiskStatus.AskMoreInfo)

      val properties = mock[BookingPropertiesData]
      when(properties.getLoyaltyPaymentBoundaries()(any())).thenReturn(None)
      when(properties.getCashbackRedemptionPaymentBoundaries()(any())).thenReturn(None)
      val papiProperties = mock[transformers.Properties]
      val papiProperty   = createMockPropertyWithRoomUid(1, Some("room-uid"), Some(roomIdentifier))
      when(papiProperties.property) thenReturn Seq(papiProperty)
      when(papiProperties.dfMetaResult).thenReturn(DFMetaResult())
      when(properties.papiProperties) thenReturn Some(papiProperties)
      when(properties.toPropertyProductItem(any())) thenReturn mock[PropertyProductItem]
      when(properties.getEbeBookingData()).thenReturn(None)
      when(properties.getChildRoomToBook()).thenReturn(Some(BookingMockHelper.baseChildrenRoom))
      when(properties.getPropertyToBook).thenReturn(Some(papiProperty))
      when(properties.childRooms) thenReturn Seq(BookingMockHelper.baseChildrenRoom)
      val childRoomBookingOpt = getFirstChildRoomBookingData(papiProperty)
      when(properties.getChildRoomBookingData).thenReturn(childRoomBookingOpt)
      when(properties.propertySearchCriteria).thenReturn(Some(propertySearchCriteria))
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn Future
        .successful(
          ProductData(
            properties = Seq(properties),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        )
      val whiteLabelInfo = mock[WhiteLabelInfo]
      when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
      when(whiteLabelInfo.feature).thenReturn(FeaturesConfiguration())
      when(whiteLabelInfo.isFeatureEnabled(mockitoEq(WhiteLabelFeatureName.TPRMCheck), any(), any(), any()))
        .thenReturn(true)
      when(setupContext.requestContext).thenReturn(context.copy(whiteLabelInfo = whiteLabelInfo))
      val service = getSetupBookingFacade

      val productsRequest = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "1",
            propertySearchCriteria = propertySearchCriteria
          )
        )
      )
      val setupBookingRequestMultiProduct =
        setupBookingRequest.copy(productsRequest = productsRequest, customerInfo = Some(CustomerV2("I", "L", "", 0)))
      service.setupBooking(setupBookingRequestMultiProduct).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse.map { booking =>
            {
              booking.serverStatus.status shouldBe InitializeBookingStatus.VerifyCustomer
            }
          }.isDefined shouldBe true
        }
      }
    }

    "return allotment check status correctly for first call" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(flightPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)
          )
        )
      when(tprmRepository.checkSuspicious(any())(any())) thenReturn Future.successful(CustomerRiskStatus.Safe)
      when(precheckRepository.getAllotmentForBooking(any(), ArgumentMatchers.eq(true))(any(), any())) thenReturn Future
        .successful(PrecheckResponse(ResultInfo(AbsStatus.Success, AbsResponse.AllotmentNotAvailable, "")))

      val properties = mock[BookingPropertiesData]
      when(properties.getLoyaltyPaymentBoundaries()(any())).thenReturn(None)
      when(properties.getCashbackRedemptionPaymentBoundaries()(any())).thenReturn(None)
      val papiProperties = mock[transformers.Properties]
      val papiProperty   = createMockPropertyWithRoomUid(1, Some("room-uid"), Some(roomIdentifier))
      val dfMetaResult = DFMetaResult(
        propertyToken = Some(ResponseStateToken(isNew = false, token = ""))
      )
      when(papiProperties.property) thenReturn Seq(papiProperty)
      when(papiProperties.dfMetaResult) thenReturn dfMetaResult
      when(properties.id) thenReturn "1"
      when(properties.papiProperties) thenReturn Some(papiProperties)
      when(properties.getChildRoomToBook()).thenReturn(Some(BookingMockHelper.baseChildrenRoom))
      when(properties.getPropertyToBook).thenReturn(Some(papiProperty))
      when(properties.toPropertyProductItem(any())) thenReturn mock[PropertyProductItem]
      when(properties.getEbeBookingData()).thenReturn(None)
      when(properties.childRooms) thenReturn Seq(BookingMockHelper.baseChildrenRoom)
      when(properties.propertySearchCriteria).thenReturn(Some(propertySearchCriteria))
      val childRoomBookingOpt = getFirstChildRoomBookingData(papiProperty)
      when(properties.getChildRoomBookingData).thenReturn(childRoomBookingOpt)
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn Future
        .successful(
          ProductData(
            properties = Seq(properties),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        )
      val service = getSetupBookingFacade

      val productsRequest = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "1",
            propertySearchCriteria = propertySearchCriteria
          )
        )
      )
      val setupBookingRequestMultiProduct =
        setupBookingRequest.copy(productsRequest = productsRequest, customerInfo = Some(CustomerV2("I", "L", "", 0)))
      service.setupBooking(setupBookingRequestMultiProduct).map { response =>
        response.success shouldBe true
        verify(precheckRepository, times(1))
          .getAllotmentForBooking(any(), ArgumentMatchers.eq(true))(any[RequestContext], any[String])
        response.bookingResponse.map { booking =>
          {
            booking.serverStatus.status shouldBe InitializeBookingStatus.Ok
          }
        }.isDefined shouldBe true
      }
    }

    "return ProductNotReady status when allotment precheck status is AllotmentPriceChangedIncrease and PriceChangePolling is enabled" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(flightPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)
          )
        )
      when(tprmRepository.checkSuspicious(any())(any())) thenReturn Future.successful(CustomerRiskStatus.Safe)
      when(precheckRepository.getAllotmentForBooking(any(), anyBoolean())(any(), any())) thenReturn Future
        .successful(PrecheckResponse(ResultInfo(AbsStatus.Failed, AbsResponse.AllotmentPriceChangedIncrease, "")))

      val properties = mock[BookingPropertiesData]
      when(properties.getLoyaltyPaymentBoundaries()(any())).thenReturn(None)
      when(properties.getCashbackRedemptionPaymentBoundaries()(any())).thenReturn(None)
      val papiProperties = mock[transformers.Properties]
      val papiProperty   = createMockPropertyWithRoomUid(1, Some("room-uid"), Some(roomIdentifier))
      val dfMetaResult = DFMetaResult(
        propertyToken = Some(ResponseStateToken(isNew = false, token = ""))
      )
      when(properties.getChildRoomToBook()).thenReturn(Some(BookingMockHelper.baseChildrenRoom))
      when(properties.getPropertyToBook).thenReturn(Some(papiProperty))
      when(papiProperties.property) thenReturn Seq(papiProperty)
      when(papiProperties.dfMetaResult) thenReturn dfMetaResult
      when(properties.id) thenReturn "1"
      when(properties.papiProperties) thenReturn Some(papiProperties)
      when(properties.toPropertyProductItem(any())) thenReturn mock[PropertyProductItem]
      when(properties.getEbeBookingData()).thenReturn(None)
      when(properties.childRooms) thenReturn Seq(BookingMockHelper.baseChildrenRoom)
      when(properties.propertySearchCriteria).thenReturn(Some(propertySearchCriteria))
      val childRoomBookingOpt = getFirstChildRoomBookingData(papiProperty)
      when(properties.getChildRoomBookingData).thenReturn(childRoomBookingOpt)
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn Future
        .successful(
          ProductData(
            properties = Seq(properties),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        )
      val service = getSetupBookingFacade

      val productsRequest = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "1",
            propertySearchCriteria = propertySearchCriteria
          )
        )
      )
      val setupBookingRequestMultiProduct =
        setupBookingRequest.copy(productsRequest = productsRequest, customerInfo = Some(CustomerV2("I", "L", "", 0)))

      when(setupContext.session).thenReturn(
        SetupBookingSessionContext(properties =
          Map("1" -> PropertySetupBookingToken("1", Some("111"), Some(AllotmentStatus.Processing), None, Some(1L)))
        )
      )

      when(featureAware.isPriceChangePollingEnabled) thenReturn true

      service.setupBooking(setupBookingRequestMultiProduct).map { response =>
        response.success shouldBe true
        verify(precheckRepository, times(1))
          .getAllotmentForBooking(any(), ArgumentMatchers.eq(false))(any(), any())
        response.bookingResponse.map { booking =>
          {
            booking.serverStatus.status shouldBe InitializeBookingStatus.ProductNotReady
          }
        }.isDefined shouldBe true
      }
    }

    "return allotment status in supplierResponse" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(flightPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)
          )
        )
      when(tprmRepository.checkSuspicious(any())(any())) thenReturn Future.successful(CustomerRiskStatus.Safe)
      when(precheckRepository.getAllotmentForBooking(any(), anyBoolean())(any(), any())) thenReturn Future
        .successful(PrecheckResponse(ResultInfo(AbsStatus.UnknownStatus, AbsResponse.RequestProcessing, "")))

      val properties = mock[BookingPropertiesData]
      when(properties.getLoyaltyPaymentBoundaries()(any())).thenReturn(None)
      when(properties.getCashbackRedemptionPaymentBoundaries()(any())).thenReturn(None)
      val papiProperties = mock[transformers.Properties]
      val papiProperty   = createMockPropertyWithRoomUid(1, Some("room-uid"), Some(roomIdentifier))
      val dfMetaResult = DFMetaResult(
        propertyToken = Some(ResponseStateToken(isNew = false, token = ""))
      )
      when(properties.getChildRoomToBook()).thenReturn(Some(BookingMockHelper.baseChildrenRoom))
      when(properties.getPropertyToBook).thenReturn(Some(papiProperty))
      when(papiProperties.property) thenReturn Seq(papiProperty)
      when(papiProperties.dfMetaResult) thenReturn dfMetaResult
      when(properties.id) thenReturn "1"
      when(properties.papiProperties) thenReturn Some(papiProperties)
      when(properties.toPropertyProductItem(any())) thenReturn mock[PropertyProductItem]
      when(properties.getEbeBookingData()).thenReturn(None)
      when(properties.childRooms) thenReturn Seq(BookingMockHelper.baseChildrenRoom)
      when(properties.propertySearchCriteria).thenReturn(Some(propertySearchCriteria))
      val childRoomBookingOpt = getFirstChildRoomBookingData(papiProperty)
      when(properties.getChildRoomBookingData).thenReturn(childRoomBookingOpt)
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn Future
        .successful(
          ProductData(
            properties = Seq(properties),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        )
      val service = getSetupBookingFacade

      val productsRequest = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "1",
            propertySearchCriteria = propertySearchCriteria
          )
        )
      )
      val setupBookingRequestMultiProduct =
        setupBookingRequest.copy(productsRequest = productsRequest, customerInfo = Some(CustomerV2("I", "L", "", 0)))

      when(setupContext.session).thenReturn(
        SetupBookingSessionContext(properties =
          Map("1" -> PropertySetupBookingToken("1", Some("111"), Some(AllotmentStatus.Processing), None, Some(1L)))
        )
      )

      when(featureAware.isPriceChangePollingEnabled) thenReturn true

      service.setupBooking(setupBookingRequestMultiProduct).map { response =>
        response.success shouldBe true
        verify(precheckRepository, times(1))
          .getAllotmentForBooking(any(), ArgumentMatchers.eq(false))(any(), any())
        response.bookingResponse.map { booking =>
          {
            booking.supplierResponse.get.headOption.map(_.allotmentStatus).get shouldBe Some(AllotmentStatus.Processing)
          }
        }.isDefined shouldBe true
      }
    }

    "return ProductNotReady status when allotment precheck status is AllotmentPriceChangedDecrease and PriceChangePolling is enabled" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(flightPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)
          )
        )
      when(tprmRepository.checkSuspicious(any())(any())) thenReturn Future.successful(CustomerRiskStatus.Safe)
      when(precheckRepository.getAllotmentForBooking(any(), anyBoolean())(any(), any())) thenReturn Future
        .successful(PrecheckResponse(ResultInfo(AbsStatus.Failed, AbsResponse.AllotmentPriceChangedDecrease, "")))

      val properties = mock[BookingPropertiesData]
      when(properties.getLoyaltyPaymentBoundaries()(any())).thenReturn(None)
      when(properties.getCashbackRedemptionPaymentBoundaries()(any())).thenReturn(None)
      val papiProperties = mock[transformers.Properties]
      val papiProperty   = createMockPropertyWithRoomUid(1, Some("room-uid"), Some(roomIdentifier))
      val dfMetaResult = DFMetaResult(
        propertyToken = Some(ResponseStateToken(isNew = false, token = ""))
      )
      when(papiProperties.property) thenReturn Seq(papiProperty)
      when(papiProperties.dfMetaResult) thenReturn dfMetaResult
      when(properties.getChildRoomToBook()).thenReturn(Some(BookingMockHelper.baseChildrenRoom))
      when(properties.getPropertyToBook).thenReturn(Some(papiProperty))
      when(properties.id) thenReturn "1"
      when(properties.papiProperties) thenReturn Some(papiProperties)
      when(properties.toPropertyProductItem(any())) thenReturn mock[PropertyProductItem]
      when(properties.getEbeBookingData()).thenReturn(None)
      when(properties.childRooms) thenReturn Seq(BookingMockHelper.baseChildrenRoom)
      when(properties.propertySearchCriteria).thenReturn(Some(propertySearchCriteria))
      val childRoomBookingOpt = getFirstChildRoomBookingData(papiProperty)
      when(properties.getChildRoomBookingData).thenReturn(childRoomBookingOpt)
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn Future
        .successful(
          ProductData(
            properties = Seq(properties),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        )
      val service = getSetupBookingFacade

      val productsRequest = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "1",
            propertySearchCriteria = propertySearchCriteria
          )
        )
      )
      val setupBookingRequestMultiProduct =
        setupBookingRequest.copy(productsRequest = productsRequest, customerInfo = Some(CustomerV2("I", "L", "", 0)))

      when(setupContext.session).thenReturn(
        SetupBookingSessionContext(properties =
          Map("1" -> PropertySetupBookingToken("1", Some("111"), Some(AllotmentStatus.Processing), None, Some(1L)))
        )
      )

      when(featureAware.isPriceChangePollingEnabled) thenReturn true

      service.setupBooking(setupBookingRequestMultiProduct).map { response =>
        response.success shouldBe true
        verify(precheckRepository, times(1))
          .getAllotmentForBooking(any(), ArgumentMatchers.eq(false))(any[RequestContext], any[String])
        response.bookingResponse.map { booking =>
          {
            booking.serverStatus.status shouldBe InitializeBookingStatus.ProductNotReady
          }
        }.isDefined shouldBe true
      }
    }

    "return AllotmenNotAvailable status when allotment precheck status is AllotmentPriceChangedIncrease and PriceChangePolling is not enabled" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(flightPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)
          )
        )
      when(tprmRepository.checkSuspicious(any())(any())) thenReturn Future.successful(CustomerRiskStatus.Safe)
      when(precheckRepository.getAllotmentForBooking(any(), anyBoolean())(any(), any())) thenReturn Future
        .successful(PrecheckResponse(ResultInfo(AbsStatus.Failed, AbsResponse.AllotmentPriceChangedIncrease, "")))

      val properties = mock[BookingPropertiesData]
      when(properties.getLoyaltyPaymentBoundaries()(any())).thenReturn(None)
      when(properties.getCashbackRedemptionPaymentBoundaries()(any())).thenReturn(None)
      val papiProperties = mock[transformers.Properties]
      val papiProperty   = createMockPropertyWithRoomUid(1, Some("room-uid"), Some(roomIdentifier))
      val dfMetaResult = DFMetaResult(
        propertyToken = Some(ResponseStateToken(isNew = false, token = ""))
      )
      when(papiProperties.property) thenReturn Seq(papiProperty)
      when(papiProperties.dfMetaResult) thenReturn dfMetaResult
      when(properties.getChildRoomToBook()).thenReturn(Some(BookingMockHelper.baseChildrenRoom))
      when(properties.getPropertyToBook).thenReturn(Some(papiProperty))
      when(properties.id) thenReturn "1"
      when(properties.papiProperties) thenReturn Some(papiProperties)
      when(properties.getEbeBookingData()).thenReturn(None)
      when(properties.toPropertyProductItem(any())) thenReturn mock[PropertyProductItem]
      when(properties.childRooms) thenReturn Seq(BookingMockHelper.baseChildrenRoom)
      when(properties.propertySearchCriteria).thenReturn(Some(propertySearchCriteria))
      val childRoomBookingOpt = getFirstChildRoomBookingData(papiProperty)
      when(properties.getChildRoomBookingData).thenReturn(childRoomBookingOpt)
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn Future
        .successful(
          ProductData(
            properties = Seq(properties),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        )
      val service = getSetupBookingFacade

      val productsRequest = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "1",
            propertySearchCriteria = propertySearchCriteria
          )
        )
      )
      val setupBookingRequestMultiProduct =
        setupBookingRequest.copy(productsRequest = productsRequest, customerInfo = Some(CustomerV2("I", "L", "", 0)))

      when(setupContext.session).thenReturn(
        SetupBookingSessionContext(properties =
          Map("1" -> PropertySetupBookingToken("1", Some("111"), Some(AllotmentStatus.Processing), None, Some(1L)))
        )
      )

      service.setupBooking(setupBookingRequestMultiProduct).map { response =>
        response.success shouldBe true
        verify(precheckRepository, times(1))
          .getAllotmentForBooking(any(), ArgumentMatchers.eq(false))(any(), any())
        response.bookingResponse.map { booking =>
          {
            booking.serverStatus.status shouldBe InitializeBookingStatus.AllotmentNotAvailable
          }
        }.isDefined shouldBe true
      }
    }

    "return AllotmenNotAvailable status when allotment precheck status is AllotmentPriceChangedDecrease and PriceChangePolling is not enabled" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(flightPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)
          )
        )
      when(tprmRepository.checkSuspicious(any())(any())) thenReturn Future.successful(CustomerRiskStatus.Safe)
      when(precheckRepository.getAllotmentForBooking(any(), anyBoolean())(any(), any())) thenReturn Future
        .successful(PrecheckResponse(ResultInfo(AbsStatus.Failed, AbsResponse.AllotmentPriceChangedDecrease, "")))

      val properties = mock[BookingPropertiesData]
      when(properties.getLoyaltyPaymentBoundaries()(any())).thenReturn(None)
      when(properties.getCashbackRedemptionPaymentBoundaries()(any())).thenReturn(None)
      val papiProperties = mock[transformers.Properties]
      val papiProperty   = createMockPropertyWithRoomUid(1, Some("room-uid"), Some(roomIdentifier))
      val dfMetaResult = DFMetaResult(
        propertyToken = Some(ResponseStateToken(isNew = false, token = ""))
      )
      when(papiProperties.property) thenReturn Seq(papiProperty)
      when(papiProperties.dfMetaResult) thenReturn dfMetaResult
      when(properties.getChildRoomToBook()).thenReturn(Some(BookingMockHelper.baseChildrenRoom))
      when(properties.getPropertyToBook).thenReturn(Some(papiProperty))
      when(properties.id) thenReturn "1"
      when(properties.papiProperties) thenReturn Some(papiProperties)
      when(properties.toPropertyProductItem(any())) thenReturn mock[PropertyProductItem]
      when(properties.getEbeBookingData()).thenReturn(None)
      when(properties.childRooms) thenReturn Seq(BookingMockHelper.baseChildrenRoom)
      when(properties.propertySearchCriteria).thenReturn(Some(propertySearchCriteria))
      val childRoomBookingOpt = getFirstChildRoomBookingData(papiProperty)
      when(properties.getChildRoomBookingData).thenReturn(childRoomBookingOpt)
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn Future
        .successful(
          ProductData(
            properties = Seq(properties),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        )
      val service = getSetupBookingFacade

      val productsRequest = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "1",
            propertySearchCriteria = propertySearchCriteria
          )
        )
      )
      val setupBookingRequestMultiProduct =
        setupBookingRequest.copy(productsRequest = productsRequest, customerInfo = Some(CustomerV2("I", "L", "", 0)))

      when(setupContext.session).thenReturn(
        SetupBookingSessionContext(properties =
          Map("1" -> PropertySetupBookingToken("1", Some("111"), Some(AllotmentStatus.Processing), None, Some(1L)))
        )
      )

      service.setupBooking(setupBookingRequestMultiProduct).map { response =>
        response.success shouldBe true
        verify(precheckRepository, times(1))
          .getAllotmentForBooking(any(), ArgumentMatchers.eq(false))(any[RequestContext], any[String])
        response.bookingResponse.map { booking =>
          {
            booking.serverStatus.status shouldBe InitializeBookingStatus.AllotmentNotAvailable
          }
        }.isDefined shouldBe true
      }
    }

    "return ProductNotReady status when papiproperties has upsell room and allotment precheck status is AllotmentPriceChangedIncrease and PriceChangePolling is enabled" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(flightPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)
          )
        )
      when(tprmRepository.checkSuspicious(any())(any())) thenReturn Future.successful(CustomerRiskStatus.Safe)
      when(precheckRepository.getAllotmentForBooking(any(), anyBoolean())(any(), any())) thenReturn Future
        .successful(PrecheckResponse(ResultInfo(AbsStatus.Failed, AbsResponse.AllotmentPriceChangedIncrease, "")))

      val properties = mock[BookingPropertiesData]
      when(properties.getLoyaltyPaymentBoundaries()(any())).thenReturn(None)
      when(properties.getCashbackRedemptionPaymentBoundaries()(any())).thenReturn(None)
      val papiProperties       = mock[transformers.Properties]
      val roomIdentifier       = "identity-1"
      val upSellRoomIdentifier = "identity-2"
      val upSellPricingRequest = pricingRequest.copy(dfFeatureFlags = Seq(PropertyFeatureFlags.BreakfastUpSell.id))
      val papiProperty =
        createMockPropertyWithUpsellWithRoomUid(
          propertyId = 1,
          uid = Some("room-uid"),
          roomIdentifier = Some(roomIdentifier),
          upsellPropertyId = 2,
          upsellUid = Some("upsellroom-uid"),
          upsellRoomIdentifier = Some(upSellRoomIdentifier)
        )
      val dfMetaResult = DFMetaResult(
        propertyToken = Some(ResponseStateToken(isNew = false, token = ""))
      )

      when(papiProperties.property) thenReturn Seq(papiProperty)
      when(papiProperties.dfMetaResult) thenReturn dfMetaResult
      when(properties.getChildRoomToBook()).thenReturn(Some(BookingMockHelper.baseChildrenRoom))
      when(properties.getPropertyToBook).thenReturn(Some(papiProperty))
      when(properties.id) thenReturn "1"
      when(properties.papiProperties) thenReturn Some(papiProperties)
      when(properties.toPropertyProductItem(any())) thenReturn mock[PropertyProductItem]
      when(properties.getEbeBookingData()).thenReturn(None)
      when(properties.childRooms) thenReturn Seq(BookingMockHelper.baseChildrenRoom)
      when(properties.propertySearchCriteria).thenReturn(
        Some(
          propertySearchCriteria.copy(
            roomIdentifier = upSellRoomIdentifier,
            pricingRequest = Some(upSellPricingRequest)
          )
        )
      )
      val childRoomBookingOpt = getFirstChildRoomBookingData(papiProperty)
      when(properties.getChildRoomBookingData).thenReturn(childRoomBookingOpt)
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn Future
        .successful(
          ProductData(
            properties = Seq(properties),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        )
      val service = getSetupBookingFacade

      val productsRequest = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "1",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = roomIdentifier)
          ),
          PropertyRequestItem(
            id = "2",
            propertySearchCriteria = propertySearchCriteria.copy(
              roomIdentifier = upSellRoomIdentifier,
              pricingRequest = Some(upSellPricingRequest)
            )
          )
        )
      )

      val setupBookingRequestMultiProduct =
        setupBookingRequest.copy(productsRequest = productsRequest, customerInfo = Some(CustomerV2("I", "L", "", 0)))

      when(setupContext.session).thenReturn(
        SetupBookingSessionContext(properties =
          Map("1" -> PropertySetupBookingToken("1", Some("111"), Some(AllotmentStatus.Processing), None, Some(1L)))
        )
      )

      when(featureAware.isPriceChangePollingEnabled) thenReturn true

      service.setupBooking(setupBookingRequestMultiProduct).map { response =>
        response.success shouldBe true
        verify(precheckRepository, times(1))
          .getAllotmentForBooking(any(), ArgumentMatchers.eq(false))(any(), any())
        response.bookingResponse.map { booking =>
          {
            booking.serverStatus.status shouldBe InitializeBookingStatus.ProductNotReady
          }
        }.isDefined shouldBe true
      }
    }

    "return allotment check status correctly for 1+n call" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(flightPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)
          )
        )
      when(tprmRepository.checkSuspicious(any())(any())) thenReturn Future.successful(CustomerRiskStatus.Safe)
      when(precheckRepository.getAllotmentForBooking(any(), any[Boolean]())(any(), any())) thenReturn Future.successful(
        PrecheckResponse(ResultInfo(AbsStatus.Failed, AbsResponse.AllotmentNotAvailable, ""))
      )

      val properties = mock[BookingPropertiesData]
      when(properties.getLoyaltyPaymentBoundaries()(any())).thenReturn(None)
      when(properties.getCashbackRedemptionPaymentBoundaries()(any())).thenReturn(None)
      val papiProperties = mock[transformers.Properties]
      val papiProperty   = createMockPropertyWithRoomUid(1, Some("room-uid"), Some(roomIdentifier))
      val dfMetaResult = DFMetaResult(
        propertyToken = Some(ResponseStateToken(isNew = false, token = ""))
      )
      when(papiProperties.property) thenReturn Seq(papiProperty)
      when(papiProperties.dfMetaResult) thenReturn dfMetaResult
      when(properties.getChildRoomToBook()).thenReturn(Some(BookingMockHelper.baseChildrenRoom))
      when(properties.getPropertyToBook).thenReturn(Some(papiProperty))
      when(properties.id) thenReturn "1"
      when(properties.papiProperties) thenReturn Some(papiProperties)
      when(properties.toPropertyProductItem(any())) thenReturn mock[PropertyProductItem]
      when(properties.getEbeBookingData()).thenReturn(None)
      when(properties.childRooms) thenReturn Seq(BookingMockHelper.baseChildrenRoom)
      when(properties.propertySearchCriteria).thenReturn(Some(propertySearchCriteria))
      val childRoomBookingOpt = getFirstChildRoomBookingData(papiProperty)
      when(properties.getChildRoomBookingData).thenReturn(childRoomBookingOpt)
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn Future
        .successful(
          ProductData(
            properties = Seq(properties),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        )
      val service = getSetupBookingFacade

      val productsRequest = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "1",
            propertySearchCriteria = propertySearchCriteria
          )
        )
      )
      val setupBookingRequestMultiProduct =
        setupBookingRequest.copy(productsRequest = productsRequest, customerInfo = Some(CustomerV2("I", "L", "", 0)))

      when(setupContext.session).thenReturn(
        SetupBookingSessionContext(properties =
          Map("1" -> PropertySetupBookingToken("1", Some("111"), Some(AllotmentStatus.Processing), None, Some(1L)))
        )
      )
      service.setupBooking(setupBookingRequestMultiProduct).map { response =>
        response.success shouldBe true
        verify(precheckRepository, times(1))
          .getAllotmentForBooking(any(), ArgumentMatchers.eq(false))(any[RequestContext], any[String])
        response.bookingResponse.map { booking =>
          {
            booking.serverStatus.status shouldBe InitializeBookingStatus.AllotmentNotAvailable
          }
        }.isDefined shouldBe true
      }
    }

    "return cashback redemption boundaries in product item" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(
              flightPaymentMethodDetailsV2.intersect(propertyPaymentMethodDetailsV2),
              SetupPaymentRequestV2(),
              Seq.empty,
              None
            )
          )
        )
      when(bookingsService.isEmailMarketingOptedOutByDefault(any()))
        .thenReturn(false)

      val service    = getSetupBookingFacade
      val properties = mock[BookingPropertiesData]
      when(properties.getLoyaltyPaymentBoundaries()(any())).thenReturn(None)

      val cashbackRedemptionBoundaries = LoyaltyPaymentBoundaries(5, 5, 5, None, None, LoyaltyReasons.Success)

      when(properties.getCashbackRedemptionPaymentBoundaries()(any())) thenReturn Some(cashbackRedemptionBoundaries)
      val papiProperties = mock[transformers.Properties]
      val papiProperty   = createMockPropertyWithRoomUid(1, Some("room-uid"), Some(roomIdentifier))
      when(papiProperties.property) thenReturn Seq(papiProperty)
      when(papiProperties.dfMetaResult).thenReturn(DFMetaResult())
      when(properties.getChildRoomToBook()).thenReturn(Some(BookingMockHelper.baseChildrenRoom))
      when(properties.getPropertyToBook).thenReturn(Some(papiProperty))
      when(properties.papiProperties) thenReturn Some(papiProperties)
      when(properties.toPropertyProductItem(any())) thenReturn mock[PropertyProductItem]
      when(properties.getEbeBookingData()).thenReturn(None)
      when(properties.childRooms) thenReturn Seq(BookingMockHelper.baseChildrenRoom)
      when(properties.propertySearchCriteria).thenReturn(Some(propertySearchCriteria))
      val childRoomBookingOpt = getFirstChildRoomBookingData(papiProperty)
      when(properties.getChildRoomBookingData).thenReturn(childRoomBookingOpt)
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn Future
        .successful(
          ProductData(
            properties = Seq(properties),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        )
      when(
        customerService.getMemberLoyaltyProfile(mockitoEq(WhiteLabel.Agoda), any(), any(), any(), any(), any())(any())
      )
        .thenReturn(Future.successful(loyaltyProfile))

      val featuresConfiguration = mock[FeaturesConfiguration](RETURNS_DEEP_STUBS)
      when(featuresConfiguration.mpbe.isGetMemberLoyaltyProfile).thenReturn(Some(false))
      when(featuresConfiguration.bookingChargeCurrency).thenReturn(BookingChargeCurrency())
      when(setupContext.requestContext).thenReturn(
        requestContext.copy(
          whiteLabelInfo = WhiteLabelInfo(
            WhiteLabel.Agoda,
            featuresConfiguration,
            None,
            instantBookingEnabled = false,
            (feature, _, _, _, _) => feature == "CashbackRedemption"
          )
        )
      )

      val enabledFeatures = Some(Seq("cashbackRedemption"))
      val request = setupBookingRequest.copy(
        enabledFeatures = enabledFeatures,
        userContext = userContext.map(_.copy(capiToken = Some("token")))
      )

      service.setupBooking(request).map { response =>
        {
          verify(customerService, times(1)).getMemberLoyaltyProfile(
            mockitoEq(WhiteLabel.Agoda),
            any(),
            any(),
            any(),
            mockitoEq(Some(Seq("cashbackRedemption"))),
            any()
          )(any())

          verify(productService, times(1))
            .composeProductData(any(), any(), any(), any(), any(), any(), mockitoEq(None), any(), any(), any(), any())(
              any()
            )

          response.success shouldBe true
          response.bookingResponse.flatMap(_.products.cashbackRedeemBoundaries) shouldBe Some(
            RedeemBoundaries(
              cashbackRedemptionBoundaries.min,
              cashbackRedemptionBoundaries.max,
              cashbackRedemptionBoundaries.maxBeforeGatewayLimit,
              None,
              cashbackRedemptionBoundaries.canStayForFree
            )
          )
        }
      }
    }

    "return cashback redemption boundaries as None in product item when cashbackRedemption flag does not exist" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(
              flightPaymentMethodDetailsV2.intersect(propertyPaymentMethodDetailsV2),
              SetupPaymentRequestV2(),
              Seq.empty,
              None
            )
          )
        )
      when(bookingsService.isEmailMarketingOptedOutByDefault(any()))
        .thenReturn(false)

      val service    = getSetupBookingFacade
      val properties = mock[BookingPropertiesData]
      when(properties.getLoyaltyPaymentBoundaries()(any())).thenReturn(None)
      when(properties.getCashbackRedemptionPaymentBoundaries()(any())) thenReturn None
      val papiProperties = mock[transformers.Properties]
      val papiProperty   = createMockPropertyWithRoomUid(1, Some("room-uid"), Some(roomIdentifier))
      when(papiProperties.property) thenReturn Seq(papiProperty)
      when(papiProperties.dfMetaResult).thenReturn(DFMetaResult())
      when(properties.getChildRoomToBook()).thenReturn(Some(BookingMockHelper.baseChildrenRoom))
      when(properties.getPropertyToBook).thenReturn(Some(papiProperty))
      when(properties.papiProperties) thenReturn Some(papiProperties)
      when(properties.toPropertyProductItem(any())) thenReturn mock[PropertyProductItem]
      when(properties.getEbeBookingData()).thenReturn(None)
      when(properties.childRooms) thenReturn Seq(BookingMockHelper.baseChildrenRoom)
      when(properties.propertySearchCriteria).thenReturn(Some(propertySearchCriteria))
      val childRoomBookingOpt = getFirstChildRoomBookingData(papiProperty)
      when(properties.getChildRoomBookingData).thenReturn(childRoomBookingOpt)
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn Future
        .successful(
          ProductData(
            properties = Seq(properties),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        )
      when(customerService.getMemberLoyaltyProfile(any(), any(), any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(None))

      val featuresConfiguration = mock[FeaturesConfiguration](RETURNS_DEEP_STUBS)
      when(featuresConfiguration.mpbe.isGetMemberLoyaltyProfile).thenReturn(Some(false))
      when(featuresConfiguration.bookingChargeCurrency).thenReturn(BookingChargeCurrency())
      when(setupContext.requestContext).thenReturn(
        requestContext.copy(
          whiteLabelInfo = WhiteLabelInfo(
            WhiteLabel.Agoda,
            featuresConfiguration,
            None,
            instantBookingEnabled = false,
            (feature, _, _, _, _) => feature == "CashbackRedemption"
          )
        )
      )

      service.setupBooking(setupBookingRequest).map { response =>
        {
          verify(customerService, times(1)).getMemberLoyaltyProfile(
            any(),
            any(),
            any(),
            any(),
            mockitoEq(None),
            any()
          )(any())
          verify(productService, times(1))
            .composeProductData(any(), any(), any(), any(), any(), any(), mockitoEq(None), any(), any(), any(), any())(
              any()
            )

          response.success shouldBe true
          response.bookingResponse.flatMap(_.products.cashbackRedeemBoundaries) shouldBe None
        }
      }
    }

    "return cashback redemption boundaries as None in product item when cashbackRedemption flag exist but isGetMemberLoyaltyProfile is true" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(
              flightPaymentMethodDetailsV2.intersect(propertyPaymentMethodDetailsV2),
              SetupPaymentRequestV2(),
              Seq.empty,
              None
            )
          )
        )
      when(bookingsService.isEmailMarketingOptedOutByDefault(any()))
        .thenReturn(false)

      val service    = getSetupBookingFacade
      val properties = mock[BookingPropertiesData]
      when(properties.getLoyaltyPaymentBoundaries()(any())).thenReturn(None)
      when(properties.getCashbackRedemptionPaymentBoundaries()(any())) thenReturn None
      val papiProperties = mock[transformers.Properties]
      val papiProperty   = createMockPropertyWithRoomUid(1, Some("room-uid"), Some(roomIdentifier))
      when(papiProperties.property) thenReturn Seq(papiProperty)
      when(papiProperties.dfMetaResult).thenReturn(DFMetaResult())
      when(properties.getChildRoomToBook()).thenReturn(Some(BookingMockHelper.baseChildrenRoom))
      when(properties.getPropertyToBook).thenReturn(Some(papiProperty))
      when(properties.papiProperties) thenReturn Some(papiProperties)
      when(properties.toPropertyProductItem(any())) thenReturn mock[PropertyProductItem]
      when(properties.getEbeBookingData()).thenReturn(None)
      when(properties.childRooms) thenReturn Seq(BookingMockHelper.baseChildrenRoom)
      when(properties.propertySearchCriteria).thenReturn(Some(propertySearchCriteria))
      val childRoomBookingOpt = getFirstChildRoomBookingData(papiProperty)
      when(properties.getChildRoomBookingData).thenReturn(childRoomBookingOpt)
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn Future
        .successful(
          ProductData(
            properties = Seq(properties),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        )
      when(customerService.getMemberLoyaltyProfile(any(), any(), any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(loyaltyProfile))

      val featuresConfiguration = mock[FeaturesConfiguration](RETURNS_DEEP_STUBS)

      when(featuresConfiguration.mpbe.isGetMemberLoyaltyProfile).thenReturn(Some(true))
      when(featuresConfiguration.bookingChargeCurrency).thenReturn(BookingChargeCurrency())
      when(setupContext.requestContext).thenReturn(
        requestContext.copy(whiteLabelInfo =
          WhiteLabelInfo(
            WhiteLabel.Agoda,
            featuresConfiguration,
            None,
            instantBookingEnabled = true,
            (feature, _, _, _, _) => feature == "CashbackRedemption"
          )
        )
      )

      val enabledFeatures = Some(Seq("cashbackRedemption"))
      val request         = setupBookingRequest.copy(enabledFeatures = enabledFeatures)

      service.setupBooking(request).map { response =>
        {
          verify(customerService, times(1)).getMemberLoyaltyProfile(
            any(),
            any(),
            any(),
            any(),
            mockitoEq(Some(Seq("cashbackRedemption"))),
            any()
          )(any())
          verify(productService, times(1))
            .composeProductData(
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              mockitoEq(loyaltyProfile),
              any(),
              any(),
              any(),
              any()
            )(any())

          response.success shouldBe true
          response.bookingResponse.flatMap(_.products.cashbackRedeemBoundaries) shouldBe None
        }
      }
    }

    "return cashback redemption boundaries as None in product item when cashbackRedemption flag exist but isCashbackRedemptionEnabled is false" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(
              flightPaymentMethodDetailsV2.intersect(propertyPaymentMethodDetailsV2),
              SetupPaymentRequestV2(),
              Seq.empty,
              None
            )
          )
        )
      when(bookingsService.isEmailMarketingOptedOutByDefault(any()))
        .thenReturn(false)

      val service    = getSetupBookingFacade
      val properties = mock[BookingPropertiesData]
      when(properties.getLoyaltyPaymentBoundaries()(any())).thenReturn(None)
      when(properties.getCashbackRedemptionPaymentBoundaries()(any())) thenReturn None
      val papiProperties = mock[transformers.Properties]
      val papiProperty   = createMockPropertyWithRoomUid(1, Some("room-uid"), Some(roomIdentifier))
      when(papiProperties.property) thenReturn Seq(papiProperty)
      when(papiProperties.dfMetaResult).thenReturn(DFMetaResult())
      when(properties.getChildRoomToBook()).thenReturn(Some(BookingMockHelper.baseChildrenRoom))
      when(properties.getPropertyToBook).thenReturn(Some(papiProperty))
      when(properties.papiProperties) thenReturn Some(papiProperties)
      when(properties.toPropertyProductItem(any())) thenReturn mock[PropertyProductItem]
      when(properties.getEbeBookingData()).thenReturn(None)
      when(properties.childRooms) thenReturn Seq(BookingMockHelper.baseChildrenRoom)
      when(properties.propertySearchCriteria).thenReturn(Some(propertySearchCriteria))
      val childRoomBookingOpt = getFirstChildRoomBookingData(papiProperty)
      when(properties.getChildRoomBookingData).thenReturn(childRoomBookingOpt)
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn Future
        .successful(
          ProductData(
            properties = Seq(properties),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        )
      when(customerService.getMemberLoyaltyProfile(any(), any(), any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(loyaltyProfile))

      val featuresConfiguration = mock[FeaturesConfiguration](RETURNS_DEEP_STUBS)

      when(featuresConfiguration.mpbe.isGetMemberLoyaltyProfile).thenReturn(Some(false))
      when(featuresConfiguration.bookingChargeCurrency).thenReturn(BookingChargeCurrency())
      when(setupContext.requestContext).thenReturn(
        requestContext.copy(
          whiteLabelInfo = WhiteLabelInfo(
            WhiteLabel.Agoda,
            featuresConfiguration,
            None,
            instantBookingEnabled = true,
            (feature, _, _, _, _) => feature != "CashbackRedemption"
          )
        )
      )
      val enabledFeatures = Some(Seq("cashbackRedemption"))
      val request         = setupBookingRequest.copy(enabledFeatures = enabledFeatures)

      service.setupBooking(request).map { response =>
        {
          verify(customerService, times(1)).getMemberLoyaltyProfile(
            any(),
            any(),
            any(),
            any(),
            mockitoEq(Some(Seq("cashbackRedemption"))),
            any()
          )(any())
          verify(productService, times(1))
            .composeProductData(
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              mockitoEq(loyaltyProfile),
              any(),
              any(),
              any(),
              any()
            )(any())

          response.success shouldBe true
          response.bookingResponse.flatMap(_.products.cashbackRedeemBoundaries) shouldBe None
        }
      }
    }

    "return correct special requests options" in {
      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(
              flightPaymentMethodDetailsV2.intersect(propertyPaymentMethodDetailsV2),
              SetupPaymentRequestV2(),
              Seq.empty,
              None
            )
          )
        )
      when(bookingsService.isEmailMarketingOptedOutByDefault(any()))
        .thenReturn(false)

      val service    = getSetupBookingFacade
      val properties = mock[BookingPropertiesData]
      when(properties.getLoyaltyPaymentBoundaries()(any())).thenReturn(None)
      when(properties.getCashbackRedemptionPaymentBoundaries()(any())).thenReturn(None)
      val papiProperties = mock[transformers.Properties]
      val papiProperty   = createMockPropertyWithRoomUid(1, Some("room-uid"), Some(roomIdentifier))
      when(papiProperties.property) thenReturn Seq(papiProperty)
      when(papiProperties.dfMetaResult).thenReturn(DFMetaResult())
      when(properties.getChildRoomToBook()).thenReturn(Some(BookingMockHelper.baseChildrenRoom))
      when(properties.getPropertyToBook).thenReturn(Some(papiProperty))
      when(properties.getEbeBookingData()).thenReturn(None)
      when(properties.papiProperties) thenReturn Some(papiProperties)
      when(properties.toPropertyProductItem(any())) thenReturn mock[PropertyProductItem]
      when(properties.childRooms) thenReturn Seq(BookingMockHelper.baseChildrenRoom)
      when(properties.propertySearchCriteria).thenReturn(Some(propertySearchCriteria))
      val childRoomBookingOpt = getFirstChildRoomBookingData(papiProperty)
      when(properties.getChildRoomBookingData).thenReturn(childRoomBookingOpt)
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn Future
        .successful(
          ProductData(
            properties = Seq(properties),
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = None,
            priceDisplayType = None,
            packageToken = None,
            priceChange = None,
            priceConfirmed = true
          )
        )

      service.setupBooking(setupBookingRequest).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse.map(_.specialRequests) shouldBe Some(
            Seq(
              SpecialRequestIds.AdditionalNotes.i,
              SpecialRequestIds.NonSmokingRoom.i,
              SpecialRequestIds.AirportTransfer.i
            )
          )
        }
      }
    }

    "return SelectedPlanCode = None when no PlanCode Selected " in {
      val service = getSetupBookingFacade
      val setUpBookingRequestPlanCode = setupBookingRequest.copy(
        paymentRequest = setupBookingRequest.paymentRequest.map(
          _.copy(
            installmentPlanCode = None
          )
        )
      )
      when(setupContext.deviceContext).thenReturn(
        Some(DeviceContext(deviceTypeId = DevicePlatform.AppAndroidTablet, deviceId = None))
      )

      service.setupBooking(setUpBookingRequestPlanCode).map { response =>
        response.success shouldBe true
        response.bookingResponse.map { booking =>
          {
            booking.installmentPlanCode shouldBe null
          }
        }.isDefined shouldBe true
      }
    }

    "return SelectedPlanCode = None when no InstallmentPlan provided" in {
      val service = getSetupBookingFacade
      val setUpBookingRequestPlanCode = setupBookingRequest.copy(
        paymentRequest = setupBookingRequest.paymentRequest.map(
          _.copy(
            installmentPlanCode = Some("PLAN-1")
          )
        )
      )

      service.setupBooking(setUpBookingRequestPlanCode).map { response =>
        response.success shouldBe true
        response.bookingResponse.map { booking =>
          {
            booking.installmentPlanCode shouldBe null
          }
        }.isDefined shouldBe true
      }
    }

    "return SelectedPlanCode = None when no PlanCode in InstallmentPlan List" in {
      val service = getSetupBookingFacade
      val setUpBookingRequestPlanCode = setupBookingRequest.copy(
        paymentRequest = setupBookingRequest.paymentRequest.map(
          _.copy(
            installmentPlanCode = Some("PLAN-3")
          )
        )
      )

      when(setupContext.deviceContext).thenReturn(
        Some(DeviceContext(deviceTypeId = DevicePlatform.WebPhone, deviceId = None))
      )

      service.setupBooking(setUpBookingRequestPlanCode).map { response =>
        response.success shouldBe true
        response.bookingResponse.flatMap(_.installmentPlanCode) shouldBe null
      }
    }

    "return plan correctly when depecrate experiment turn on" in {
      val service = getSetupBookingFacade
      val setUpBookingRequestPlanCode = setupBookingRequest.copy(
        paymentRequest = setupBookingRequest.paymentRequest.map(
          _.copy(
            installmentPlanCode = Some("PLAN-3")
          )
        )
      )

      when(setupContext.deviceContext).thenReturn(
        Some(DeviceContext(deviceTypeId = DevicePlatform.WebPhone, deviceId = None))
      )

      when(
        bookingsService.getInstallmentData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      )
        .thenReturn(
          Future.successful(
            Some(
              InstallmentDataResponse(
                installmentPlans = Some(
                  List(
                    InstallmentPlan(
                      planCode = Some("PLAN-1")
                    ),
                    InstallmentPlan(
                      planCode = Some("PLAN-3")
                    )
                  )
                ),
                installmentAvailableProviders = Some(List(InstallmentAvailableProvider(providerId = Some(2))))
              )
            ),
            None
          )
        )
      when(bookingsService.getInstallmentPlanCodeByInstallmentDataResponse(any(), any())).thenReturn(Some("PLAN-3"))

      service.setupBooking(setUpBookingRequestPlanCode).map { response =>
        response.success shouldBe true
        response.bookingResponse.flatMap(_.installmentPlanCode) shouldBe Some("PLAN-3")
      }
    }

    "return plan correctly" in {
      val service = getSetupBookingFacade
      val setUpBookingRequestPlanCode = setupBookingRequest.copy(
        paymentRequest = setupBookingRequest.paymentRequest.map(
          _.copy(
            installmentPlanCode = Some("PLAN-3")
          )
        )
      )
      when(setupContext.deviceContext).thenReturn(
        Some(DeviceContext(deviceTypeId = DevicePlatform.WebPhone, deviceId = None))
      )

      when(
        bookingsService.getInstallmentData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      )
        .thenReturn(
          Future.successful(
            Some(
              InstallmentDataResponse(
                installmentPlans = Some(
                  List(
                    InstallmentPlan(
                      planCode = Some("PLAN-1")
                    ),
                    InstallmentPlan(
                      planCode = Some("PLAN-3")
                    )
                  )
                ),
                installmentAvailableProviders = Some(List(InstallmentAvailableProvider(providerId = Some(2))))
              )
            ),
            None
          )
        )
      when(bookingsService.getInstallmentPlanCodeByInstallmentDataResponse(any(), any())).thenReturn(Some("PLAN-3"))

      service.setupBooking(setUpBookingRequestPlanCode).map { response =>
        response.success shouldBe true
        response.bookingResponse.flatMap(_.installmentPlanCode) shouldBe Some("PLAN-3")
      }
    }

    "return SelectedPlanCode = None when no InstallmentDataPlan" in {
      val service = getSetupBookingFacade
      val setUpBookingRequestPlanCode = setupBookingRequest.copy(
        paymentRequest = setupBookingRequest.paymentRequest.map(
          _.copy(
            installmentPlanCode = Some("PLAN-3"),
            selectedChargeCurrency = Some("JPY")
          )
        )
      )

      when(setupContext.deviceContext).thenReturn(
        Some(DeviceContext(deviceTypeId = DevicePlatform.AppAndroidTablet, deviceId = None))
      )

      when(
        bookingsService.getInstallmentData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      )
        .thenReturn(
          Future.successful(
            Some(
              InstallmentDataResponse(
                installmentPlans = None,
                installmentAvailableProviders = Some(List(InstallmentAvailableProvider(providerId = Some(5))))
              )
            ),
            None
          )
        )

      service.setupBooking(setUpBookingRequestPlanCode).map { response =>
        response.success shouldBe true
        response.bookingResponse.flatMap(_.installmentPlanCode) shouldBe null
      }
    }

    "extractPropertyAmount should return the correct amount" in {

      val searchCriteria = PropertySearchCriteria(
        None,
        "",
        OccupancyRequest(1, 1, 0, Nil, None),
        DurationRequest(LocalDate.now(), 1),
        None,
        None,
        None,
        None,
        None,
        None
      )
      val property = createTopLevelMockProperties(
        12345,
        Seq(createMockMasterRoom(12345, None, "", None, List(defaultChildRoom(false, 2))))
      )
      when(property.property) thenReturn Seq(richPropertyResponse)

      val propertiesData = Seq(
        BookingPropertiesData("123", "", Some(property), None, PapiPropertyStatus.Ok, None, Some(searchCriteria))
      )
      val productData = SinglePropertyProductData.copy(
        properties = propertiesData
      )
      val service = getSetupBookingFacade

      // Call the method under test
      val amount = productData.extractPropertyAmount()

      // Verify the amount
      amount shouldEqual Some(155.96)
    }

    "map CcToken" in {
      val service = getSetupBookingFacade
      val setupBookingRequestWithCcToken = setupBookingRequest.copy(
        paymentRequest = Some(PaymentRequest(ccToken = Some("CreditCard Token Content")))
      )
      service.setupBooking(setupBookingRequestWithCcToken).map { response =>
        {
          val ccTokenCaptor = ArgumentCaptor.forClass(classOf[Option[String]])
          verify(bookingsService).getCreditCardInfo(any(), any(), any(), ccTokenCaptor.capture())
          response.success shouldBe true
          ccTokenCaptor.getValue().asInstanceOf[Option[String]] shouldEqual Some("CreditCard Token Content")
        }
      }
    }

    "pass correct cartContext to ProductsFacade.createProductToken" in {
      val service = getSetupBookingFacade

      val response = service.setupBooking(setupBookingRequestWithCartContext())
      response.map { r =>
        verify(productService, times(1)).createProductToken(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          ArgumentMatchers.eq(Some(cartContext)),
          any(),
          any(),
          addOnData = any(),
          any()
        )(any())
        r.success shouldBe true
      }
    }

    "pass correct selected Payment method to ProductsFacade.createProductToken when selectedPayment Method Match" in {
      val service = getSetupBookingFacade
      val setupBookingRequestWithPaymentMethod = setupBookingRequest.copy(paymentRequest =
        Some(
          PaymentRequest(
            ccBin = None,
            ccId = None,
            selectedPaymentMethod = Some(2),
            selectedChargeCurrency = None
          )
        )
      )

      val mastercardV2Details = mastercard

      val response =
        service.setupBooking(setupBookingRequestWithCartContext(Some(setupBookingRequestWithPaymentMethod)))
      response.map { r =>
        verify(productService, times(1)).createProductToken(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          ArgumentMatchers.eq(Some(mastercardV2Details)),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          addOnData = any(),
          any()
        )(any())
        r.success shouldBe true
      }
    }

    "pass correct selected Payment method to ProductsFacade.createProductToken when selectedPayment Method Mismatch" in {
      val service = getSetupBookingFacade
      val setupBookingRequestWithPaymentMethod = setupBookingRequest.copy(paymentRequest =
        Some(
          PaymentRequest(
            ccBin = None,
            ccId = None,
            selectedPaymentMethod = Some(1),
            selectedChargeCurrency = None
          )
        )
      )

      val response =
        service.setupBooking(setupBookingRequestWithCartContext(Some(setupBookingRequestWithPaymentMethod)))

      response.map { r =>
        verify(productService, times(1)).createProductToken(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          ArgumentMatchers.eq(None),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          addOnData = any(),
          any()
        )(any())
        r.success shouldBe true
      }
    }

    "pass correct selected Payment method to ProductsFacade.createProductToken when selectedPayment Method Empty" in {
      val service = getSetupBookingFacade
      val setupBookingRequestWithPaymentMethod = setupBookingRequest.copy(paymentRequest =
        Some(
          PaymentRequest(
            ccBin = None,
            ccId = None,
            selectedPaymentMethod = None,
            selectedChargeCurrency = None
          )
        )
      )

      val response =
        service.setupBooking(setupBookingRequestWithCartContext(Some(setupBookingRequestWithPaymentMethod)))
      response.map { r =>
        verify(productService, times(1)).createProductToken(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          ArgumentMatchers.eq(None),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          addOnData = any(),
          any()
        )(any())
        r.success shouldBe true
      }
    }

    "map suggested payment method when value return from setup payment service" in {
      val service = getSetupBookingFacade

      val newPaymentMethod = mockPaymentMethods.map(_.copy(id = 2))
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleActivity)

      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            AvailablePaymentMethodsResult(newPaymentMethod, SetupPaymentRequestV2(), Seq(1, 2, 3), None)
          )
        )
      service.setupBooking(setupBookingRequest).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse.nonEmpty shouldBe true
          response.bookingResponse.get.suggestedPaymentMethods shouldEqual Seq(1, 2, 3)
        }
      }
    }

    "map suggested payment method when empty sequence return from setup payment service" in {
      val service          = getSetupBookingFacade
      val newPaymentMethod = mockPaymentMethods.map(_.copy(id = 2))
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleActivity)

      when(mockPaymentService.getAvailablePaymentMethod(any(), any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(AvailablePaymentMethodsResult(newPaymentMethod, SetupPaymentRequestV2(), Seq.empty, None))
        )
      service.setupBooking(setupBookingRequest).map { response =>
        {
          response.success shouldBe true
          response.bookingResponse.nonEmpty shouldBe true
          response.bookingResponse.get.suggestedPaymentMethods.isEmpty shouldBe true
        }
      }
    }

    "map isExpiryDateRequired correctly when WLBC-1070:B" in {

      val service = getSetupBookingFacade
      when(featureAware.isCCExpiryDateRequiredForCiti).thenReturn(true)

      val creditCardInfoDisplays = service.getCreditCardInfoDisplay(nonTYLoyaltyProfile)

      creditCardInfoDisplays.map(_.isExpiryDateRequired) shouldBe List(Some(true))

    }

    "map isExpiryDateRequired correctly for thank you members when WLBC-1070:A" in {

      val service = getSetupBookingFacade
      when(featureAware.isCCExpiryDateRequiredForCiti).thenReturn(false)

      val creditCardInfoDisplays = service.getCreditCardInfoDisplay(loyaltyProfile)

      creditCardInfoDisplays.map(_.isExpiryDateRequired) shouldBe List(Some(true))

    }

    "map isExpiryDateRequired correctly for non-thank you members when WLBC-1070:A" in {

      val service = getSetupBookingFacade
      when(featureAware.isCCExpiryDateRequiredForCiti).thenReturn(false)

      val creditCardInfoDisplays = service.getCreditCardInfoDisplay(nonTYLoyaltyProfile)

      creditCardInfoDisplays.map(_.isExpiryDateRequired) shouldBe List(Some(false))

    }

    "map isExpiryDateRequired correctly when loyalty profile is empty" in {

      val service = getSetupBookingFacade
      when(featureAware.isCCExpiryDateRequiredForCiti).thenReturn(false)

      val creditCardInfoDisplays = service.getCreditCardInfoDisplay(None)

      creditCardInfoDisplays shouldBe Seq.empty

    }
  }

  "mapBillingAddressRequirements" should {
    "map billing address requirements successfully for single agency flight" in {
      val expected =
        RequireBillingAddress(postalCode = true, streetAddress = false, city = true, state = true, country = true)
      val result = getSetupBookingFacade.mapBillingAddressRequirements(SingleAgencyFlightProductData)
      result shouldBe expected
    }
    "map billing address requirements successfully for single merchant flight with none billingAddressRequired" in {
      val productData = SingleAgencyFlightProductData.copy(flights =
        Seq(
          SingleAgencyFlightProductData.flights.head.copy(
            flightItinerary = SingleAgencyFlightProductData.flights.head.flightItinerary.map(
              _.copy(
                paymentModel = PaymentModel.Merchant.id
              )
            ),
            billingAddressRequirements = None
          )
        )
      )
      val expected =
        RequireBillingAddress(postalCode = false, streetAddress = false, city = false, state = false, country = false)
      val result = getSetupBookingFacade.mapBillingAddressRequirements(productData)
      result shouldBe expected
    }
    "map billing address requirements successfully for two agency flights" in {
      val expected =
        RequireBillingAddress(postalCode = true, streetAddress = true, city = true, state = true, country = true)
      val result = getSetupBookingFacade.mapBillingAddressRequirements(multipleAgencyFlightProductData)
      result shouldBe expected
    }
    "map billing address requirements successfully for one agency one merchant flights" in {
      val productData = multipleAgencyFlightProductData.copy(flights =
        Seq(
          multipleAgencyFlightProductData.flights.head.copy(
            flightItinerary = multipleAgencyFlightProductData.flights.head.flightItinerary.map(
              _.copy(
                paymentModel = PaymentModel.Merchant.id
              )
            ),
            billingAddressRequirements = None
          ),
          multipleAgencyFlightProductData.flights.last
        )
      )
      val expected =
        RequireBillingAddress(postalCode = true, streetAddress = true, city = true, state = false, country = true)
      val result = getSetupBookingFacade.mapBillingAddressRequirements(productData)
      result shouldBe expected
    }
    "map billing address requirements successfully for two agency one merchant flights" in {
      val productData = multipleAgencyFlightProductData.copy(flights =
        Seq(
          multipleAgencyFlightProductData.flights.head.copy(
            flightItinerary = multipleAgencyFlightProductData.flights.head.flightItinerary.map(
              _.copy(
                paymentModel = PaymentModel.Merchant.id
              )
            ),
            billingAddressRequirements = None
          ),
          multipleAgencyFlightProductData.flights.head,
          multipleAgencyFlightProductData.flights.last
        )
      )
      val expected =
        RequireBillingAddress(postalCode = true, streetAddress = true, city = true, state = true, country = true)
      val result = getSetupBookingFacade.mapBillingAddressRequirements(productData)
      result shouldBe expected
    }

    "map billing address requirements correctly when flight is Merchant and TripProtection is Agency" in {
      val productData = SingleAgencyFlightProductData.copy(
        flights = Seq(
          SingleAgencyFlightProductData.flights.head.copy(
            flightItinerary = SingleAgencyFlightProductData.flights.head.flightItinerary.map(
              _.copy(
                paymentModel = PaymentModel.Merchant.id
              )
            ),
            billingAddressRequirements = None
          )
        ),
        addOnDataV2 = Seq(
          MockAddOnV2Data.sampleAddOn(
            productType = BapiProductType.TripProtection,
            productRefIds = Seq("productRefId"),
            price = 105,
            tax = 5,
            typeId = 310,
            currency = "USD",
            usdExchangeRate = 1,
            paymentModel = PaymentModel.Agency,
            requireBillingAddress = Some(
              BillingAddressRequirements(
                postCode = true,
                streetAddress = true,
                city = true,
                state = true,
                country = true
              )
            )
          )
        )
      )
      val expected =
        RequireBillingAddress(postalCode = true, streetAddress = true, city = true, state = true, country = true)
      val result = getSetupBookingFacade.mapBillingAddressRequirements(productData)
      result shouldBe expected
    }
    "map billing address requirements correctly when flight is Merchant and TripProtection is Merchant" in {
      val productData = SingleAgencyFlightProductData.copy(
        flights = Seq(
          SingleAgencyFlightProductData.flights.head.copy(
            flightItinerary = SingleAgencyFlightProductData.flights.head.flightItinerary.map(
              _.copy(
                paymentModel = PaymentModel.Merchant.id
              )
            ),
            billingAddressRequirements = None
          )
        ),
        addOnDataV2 = Seq(
          MockAddOnV2Data.sampleAddOn(
            productType = BapiProductType.TripProtection,
            productRefIds = Seq("productRefId"),
            price = 105,
            tax = 5,
            typeId = 310,
            currency = "USD",
            usdExchangeRate = 1,
            paymentModel = PaymentModel.Merchant,
            requireBillingAddress = None
          )
        )
      )
      val expected =
        RequireBillingAddress(postalCode = false, streetAddress = false, city = false, state = false, country = false)
      val result = getSetupBookingFacade.mapBillingAddressRequirements(productData)
      result shouldBe expected
    }
    "map billing address requirements correctly when flight Agency and TripProtection is Merchant" in {
      val productData = SingleAgencyFlightProductData.copy(
        flights = Seq(
          SingleAgencyFlightProductData.flights.head.copy(
            flightItinerary = SingleAgencyFlightProductData.flights.head.flightItinerary.map(
              _.copy(
                paymentModel = PaymentModel.Agency.id
              )
            )
          )
        ),
        addOnDataV2 = Seq(
          MockAddOnV2Data.sampleAddOn(
            productType = BapiProductType.TripProtection,
            productRefIds = Seq("productRefId"),
            price = 105,
            tax = 5,
            typeId = 310,
            currency = "USD",
            usdExchangeRate = 1,
            paymentModel = PaymentModel.Merchant,
            requireBillingAddress = None
          )
        )
      )
      val expected =
        RequireBillingAddress(postalCode = true, streetAddress = false, city = true, state = true, country = true)
      val result = getSetupBookingFacade.mapBillingAddressRequirements(productData)
      result shouldBe expected
    }

    "map billing address requirements correctly when flight Agency and TripProtection is Agency" in {
      val productData = SingleAgencyFlightProductData.copy(
        flights = Seq(
          SingleAgencyFlightProductData.flights.head.copy(
            flightItinerary = SingleAgencyFlightProductData.flights.head.flightItinerary.map(
              _.copy(
                paymentModel = PaymentModel.Agency.id
              )
            ),
            billingAddressRequirements = Some(
              BillingAddressRequirements(
                postCode = true,
                streetAddress = true,
                city = true,
                state = true,
                country = true
              )
            )
          )
        ),
        addOnDataV2 = Seq(
          MockAddOnV2Data.sampleAddOn(
            productType = BapiProductType.TripProtection,
            productRefIds = Seq("productRefId"),
            price = 105,
            tax = 5,
            typeId = 310,
            currency = "USD",
            usdExchangeRate = 1,
            paymentModel = PaymentModel.Agency,
            requireBillingAddress = Some(
              BillingAddressRequirements(
                postCode = true,
                streetAddress = true,
                city = true,
                state = true,
                country = true
              )
            )
          )
        )
      )
      val expected =
        RequireBillingAddress(postalCode = true, streetAddress = true, city = true, state = true, country = true)
      val result = getSetupBookingFacade.mapBillingAddressRequirements(productData)
      result shouldBe expected
    }

  }

  "getCreditCardInfoResponse" should {
    "return IsNoCvc = true if all conditions are met" in {
      val newSetupBookingRequest = SetupBookingRequest(
        correlationId = None,
        userContext = userContext,
        paymentRequest = Some(
          PaymentRequest(
            ccId = Some(1234),
            selectedPaymentMethod = Some(MPBPaymentMethod.Visa.index)
          )
        ),
        customerInfo = None,
        productsRequest = ProductsRequest(
          bookingToken = None,
          propertyRequests = Seq(
            PropertyRequestItem(
              id = "0",
              propertySearchCriteria = PropertySearchCriteria(
                propertyId = Some(1),
                roomIdentifier = "roomIdentifier",
                occupancyRequest = com.agoda.bapi.common.message.OccupancyRequest(1, 0, 1),
                durationRequest = com.agoda.bapi.common.message.DurationRequest(LocalDate.now(), 1),
                pricingRequest = Some(
                  PricingRequest(
                    isMse = false,
                    requiredPrice = "",
                    requiredBasis = "",
                    isRPM2Included = false,
                    selectedPointMaxId = None,
                    isIncludeUsdAndLocalCurrency = false,
                    allowOverrideOccupancy = false,
                    enableOpaqueChannel = false,
                    isAllowRoomTypeNotGuarantee = false,
                    synchronous = true,
                    partnerLoyaltyProgramId = Some(0)
                  )
                ),
                papiContextRequest =
                  Some(PapiContextRequest(true, trafficTypeId = Some(1), rawBotProfile = "", showCms = false)),
                roomSelectionRequest = None,
                propertyRequest = None,
                simplifiedRoomSelectionRequest = None,
                partnerRequest = Some(
                  PartnerRequest(
                    partnerRoomRateType = Some(3),
                    partnerSurchargeRateType = Some(1),
                    ratePartnerSummaries = Some(true),
                    discountType = Some(1),
                    isExcludedPfFromTax = Some(false),
                    returnDailyRates = Some(true)
                  )
                )
              ),
              Some(ProductPaymentRequest(ChargeOption.PayNow))
            )
          ),
          flightRequests = Seq.empty,
          packageRequest = None
        )
      )

      val mockPayment = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Merchant)

      val merchantRoom = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))

      val merchantMasterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(merchantRoom))

      val merchantProperty = BookingMockHelper.baseProperty.copy(masterRooms = Seq(merchantMasterRoom))

      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(merchantProperty), debug = None))
      )

      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))

      val result = getSetupBookingFacade.getCreditCardInfoResponse(
        setupBookingRequest = newSetupBookingRequest,
        productData = mockProductData,
        creditCardInfo = None,
        memberInfo = None,
        availablePaymentMethodsResult = mockAvailablePaymentMethodsResult
      )

      result.map { response =>
        verify(paymentUtil, times(1)).isNoCvc(
          ArgumentMatchers.eq(setupContext.bookingFlowType),
          ArgumentMatchers.eq(
            newSetupBookingRequest.productsRequest.propertyRequests.head.payment.get.selectedChargeOption
          ),
          ArgumentMatchers.eq(newSetupBookingRequest.paymentRequest.get.ccId),
          ArgumentMatchers.eq(Seq(false)),
          ArgumentMatchers.eq(Seq(com.agoda.bapi.common.model.creation.PaymentModel.Merchant)),
          ArgumentMatchers.eq(
            MPBPaymentMethod.fromValue(newSetupBookingRequest.paymentRequest.get.selectedPaymentMethod.get)
          )
        )

        response.isDefined shouldBe true
        response.map(_.isNoCvc) shouldBe Some(true)
      }
    }

    "return isBankNameRequired = false for Agency rooms and single property bookings" in {
      val mockPayment = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Agency)

      val setupBookingRequest = SetupBookingRequest(ProductsRequest())
      val childRoom           = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val masterRoom          = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(childRoom))
      val property            = BookingMockHelper.baseProperty.copy(masterRooms = Seq(masterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(property), debug = None))
      )

      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))

      val result = getSetupBookingFacade.getCreditCardInfoResponse(
        setupBookingRequest = setupBookingRequest,
        productData = mockProductData,
        creditCardInfo = None,
        memberInfo = None,
        availablePaymentMethodsResult = mockAvailablePaymentMethodsResult
      )

      result.map { response =>
        response.isDefined shouldBe true
        response.map(_.isBankNameRequired) shouldBe Some(false)
      }
    }

    "return true for isBankNameRequired when required and WL feature is not opted out" in {
      val mockPayment = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Merchant)
      val paymentRequest = Some(
        PaymentRequest(
          selectedPaymentMethod = Some(MPBPaymentMethod.Visa.value),
          ccToken = None,
          ccId = None,
          ccBin = Some("123456")
        )
      )

      val setupBookingRequest = SetupBookingRequest(
        ProductsRequest(),
        paymentRequest = paymentRequest
      )
      val creditCardInfo = Some(
        CreditCardInfo(
          binRangeInfo = Some(CardBinRangeInfo(CardClass.Credit)),
          countryInfo = Some(CountryInfo(1, "TestCounty", "TestCountry", "", "TC")),
          currencyInfo = Some(CurrencyInfo(1, "USD", 1))
        )
      )
      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      val childRoom  = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val masterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(childRoom))
      val property   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(masterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(property), debug = None))
      )

      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))

      when(wlInfo.isFeatureEnabled(WhiteLabelFeatureName.DisableIssuingBankDisplay)).thenReturn(false)
      when(wlInfo.whiteLabelId).thenReturn(WhiteLabel.USBank)
      when(setupContext.requestContext).thenReturn(
        requestContext.copy(
          whiteLabelInfo = wlInfo,
          featureAware = Some(featureAware)
        )
      )

      val result =
        getSetupBookingFacade.getCreditCardInfoResponse(
          setupBookingRequest = setupBookingRequest,
          productData = mockProductData,
          creditCardInfo = creditCardInfo,
          memberInfo = None,
          availablePaymentMethodsResult = mockAvailablePaymentMethodsResult
        )(
          setupContext
        )

      result.map { response =>
        response.isDefined shouldBe true
        response.map(_.isBankNameRequired) shouldBe Some(true)
      }
    }

    "return false for isBankNameRequired even when required and WL feature is opted out" in {
      val mockPayment = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Merchant)
      val paymentRequest = Some(
        PaymentRequest(
          selectedPaymentMethod = Some(MPBPaymentMethod.Visa.value),
          ccToken = None,
          ccId = None,
          ccBin = Some("123456")
        )
      )

      val setupBookingRequest = SetupBookingRequest(
        ProductsRequest(),
        paymentRequest = paymentRequest
      )
      val creditCardInfo = Some(
        CreditCardInfo(
          binRangeInfo = Some(CardBinRangeInfo(CardClass.Credit)),
          countryInfo = Some(CountryInfo(1, "TestCounty", "TestCountry", "", "TC")),
          currencyInfo = Some(CurrencyInfo(1, "USD", 1))
        )
      )
      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      val childRoom  = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val masterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(childRoom))
      val property   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(masterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(property), debug = None))
      )

      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))

      when(wlInfo.isFeatureEnabled(WhiteLabelFeatureName.DisableIssuingBankDisplay)).thenReturn(true)
      when(wlInfo.whiteLabelId).thenReturn(WhiteLabel.USBank)
      when(setupContext.requestContext).thenReturn(
        requestContext.copy(
          whiteLabelInfo = wlInfo,
          featureAware = Some(featureAware)
        )
      )

      val result =
        getSetupBookingFacade.getCreditCardInfoResponse(
          setupBookingRequest = setupBookingRequest,
          productData = mockProductData,
          creditCardInfo = creditCardInfo,
          memberInfo = None,
          availablePaymentMethodsResult = mockAvailablePaymentMethodsResult
        )(
          setupContext
        )

      result.map { response =>
        response.isDefined shouldBe true
        response.map(_.isBankNameRequired) shouldBe Some(false)
      }
    }

    "EnablePaymentLimitation return isPaymentLimitationApplied = true with error CMS if CID matches but BIN does not match" in {
      val errorCMSId            = 11111
      val cid                   = 55555
      val cmsText               = "Please enter Valid MasterCard"
      val paymentLimitationInfo = Some(paymentLimitationInfoTemplate.copy(siteId = cid, errorCMSId = errorCMSId))
      val mockPaymentReq = Some(
        PaymentRequest(
          ccBin = Some("333333"),
          ccId = None,
          selectedPaymentMethod = Some(1)
        )
      )
      val experimentData = Some(
        ExperimentData(
          userId = "cb2416c1-432c-4809-9414-7a08496a9076",
          deviceTypeId = "1",
          memberId = Some("********"),
          trafficGroup = Some("1"),
          cId = Some(cid.toString),
          aId = Some("130243"),
          serverName = None
        )
      )

      val enabledFeatures = Some(Seq("EnablePaymentLimitation"))
      val mockPayment     = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Agency)

      val setupBookingRequest = SetupBookingRequest(
        ProductsRequest(),
        paymentRequest = mockPaymentReq,
        userContext = Some(UserContextMock.value.copy(experimentData = experimentData)),
        enabledFeatures = enabledFeatures
      )
      val childRoom  = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val masterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(childRoom))
      val property   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(masterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(property), debug = None))
      )

      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))

      when(cmsContext.get(CmsItem(errorCMSId, ""))) thenReturn cmsText

      val result = getSetupBookingFacade.getCreditCardInfoResponse(
        setupBookingRequest = setupBookingRequest,
        productData = mockProductData,
        creditCardInfo = None,
        memberInfo = None,
        paymentLimitation = paymentLimitationInfo,
        cmsContext = Some(cmsContext),
        availablePaymentMethodsResult = mockAvailablePaymentMethodsResult
      )

      result.map { response =>
        response.isDefined shouldBe true
        response.flatMap(_.paymentLimitationResponse.map(_.isApplied)) shouldBe Some(true)
        response.flatMap(_.paymentLimitationResponse.flatMap(_.errorText)) shouldBe Some(cmsText)
      }
    }

    "EnablePaymentLimitation return isPaymentLimitationApplied = true with no error CMS if CID and BIN match" in {
      val errorCMSId            = 11111
      val cid                   = 55555
      val cmsText               = "Please enter Valid MasterCard"
      val paymentLimitationInfo = Some(paymentLimitationInfoTemplate.copy(siteId = cid, errorCMSId = errorCMSId))
      val mockPaymentReq = Some(
        PaymentRequest(
          ccBin = Some("222222"),
          ccId = None,
          selectedPaymentMethod = Some(1)
        )
      )
      val experimentData = Some(
        ExperimentData(
          userId = "cb2416c1-432c-4809-9414-7a08496a9076",
          deviceTypeId = "1",
          memberId = Some("********"),
          trafficGroup = Some("1"),
          cId = Some(cid.toString),
          aId = Some("130243"),
          serverName = None
        )
      )
      val enabledFeatures = Some(Seq("EnablePaymentLimitation"))
      val mockPayment     = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Agency)

      val setupBookingRequest = SetupBookingRequest(
        ProductsRequest(),
        paymentRequest = mockPaymentReq,
        userContext = Some(UserContextMock.value.copy(experimentData = experimentData)),
        enabledFeatures = enabledFeatures
      )
      val childRoom  = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val masterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(childRoom))
      val property   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(masterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(property), debug = None))
      )

      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))

      val result = getSetupBookingFacade.getCreditCardInfoResponse(
        setupBookingRequest = setupBookingRequest,
        productData = mockProductData,
        creditCardInfo = None,
        memberInfo = None,
        paymentLimitation = paymentLimitationInfo,
        cmsContext = Some(cmsContext),
        availablePaymentMethodsResult = mockAvailablePaymentMethodsResult
      )

      result.map { response =>
        response.isDefined shouldBe true
        response.flatMap(_.paymentLimitationResponse.map(_.isApplied)) shouldBe Some(true)
        response.flatMap(_.paymentLimitationResponse.flatMap(_.errorText)) shouldBe Some(null)
      }
    }

    "EnablePaymentLimitation return isPaymentLimitationApplied = false with no error CMS if CID does not match " in {
      val errorCMSId            = 11111
      val cid1                  = 55555
      val cid2                  = 666666
      val cmsText               = "Please enter Valid MasterCard"
      val paymentLimitationInfo = Some(paymentLimitationInfoTemplate.copy(siteId = cid1, errorCMSId = errorCMSId))
      val mockPaymentReq = Some(
        PaymentRequest(
          ccBin = Some("333333"),
          ccId = None,
          selectedPaymentMethod = Some(1)
        )
      )
      val experimentData = Some(
        ExperimentData(
          userId = "cb2416c1-432c-4809-9414-7a08496a9076",
          deviceTypeId = "1",
          memberId = Some("********"),
          trafficGroup = Some("1"),
          cId = Some(cid2.toString),
          aId = Some("130243"),
          serverName = None
        )
      )
      val enabledFeatures = Some(Seq("EnablePaymentLimitation"))
      val mockPayment     = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Agency)

      val setupBookingRequest = SetupBookingRequest(
        ProductsRequest(),
        paymentRequest = mockPaymentReq,
        userContext = Some(UserContextMock.value.copy(experimentData = experimentData)),
        enabledFeatures = enabledFeatures
      )
      val childRoom  = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val masterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(childRoom))
      val property   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(masterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(property), debug = None))
      )

      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))

      val result = getSetupBookingFacade.getCreditCardInfoResponse(
        setupBookingRequest = setupBookingRequest,
        productData = mockProductData,
        creditCardInfo = None,
        memberInfo = None,
        paymentLimitation = paymentLimitationInfo,
        cmsContext = Some(cmsContext),
        availablePaymentMethodsResult = mockAvailablePaymentMethodsResult
      )

      result.map { response =>
        response.isDefined shouldBe true
        response.flatMap(_.paymentLimitationResponse.map(_.isApplied)) shouldBe Some(false)
        response.flatMap(_.paymentLimitationResponse.flatMap(_.errorText)) shouldBe Some(null)
      }
    }

    "EnablePaymentLimitation return isPaymentLimitationApplied = true when payment service return true for shouldBlockOutboundIndianCardPayLater " in {
      val errorCMSId            = 356570
      val cid1                  = 55555
      val cid2                  = 666666
      val cmsText               = "Mastercard not supported. Please try a different card."
      val paymentLimitationInfo = Some(paymentLimitationInfoTemplate.copy(siteId = cid1, errorCMSId = errorCMSId))
      val mockPaymentReq = Some(
        PaymentRequest(
          ccBin = Some("333333"),
          ccId = None,
          selectedPaymentMethod = Some(1)
        )
      )
      val experimentData = Some(
        ExperimentData(
          userId = "cb2416c1-432c-4809-9414-7a08496a9076",
          deviceTypeId = "1",
          memberId = Some("********"),
          trafficGroup = Some("1"),
          cId = Some(cid2.toString),
          aId = Some("130243"),
          serverName = None
        )
      )
      val enabledFeatures = Some(Seq("EnablePaymentLimitation"))
      val mockPayment     = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Agency)
      when(cmsContext.get(CmsItem(errorCMSId, ""))) thenReturn cmsText

      val setupBookingRequest = SetupBookingRequest(
        ProductsRequest(),
        paymentRequest = mockPaymentReq,
        userContext = Some(UserContextMock.value.copy(experimentData = experimentData)),
        enabledFeatures = enabledFeatures
      )
      val childRoom  = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val masterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(childRoom))
      val property   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(masterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(property), debug = None))
      )

      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))
      when(setupPaymentService.shouldBlockOutboundIndianCardPayLater(any(), any(), any(), any()))
        .thenReturn(true)

      val result = getSetupBookingFacade.getCreditCardInfoResponse(
        setupBookingRequest = setupBookingRequest,
        productData = mockProductData,
        creditCardInfo = None,
        memberInfo = None,
        paymentLimitation = paymentLimitationInfo,
        cmsContext = Some(cmsContext),
        availablePaymentMethodsResult = mockAvailablePaymentMethodsResult
      )

      result.map { response =>
        response.isDefined shouldBe true
        response.flatMap(_.paymentLimitationResponse.map(_.isApplied)) shouldBe Some(true)
        response.flatMap(_.paymentLimitationResponse.flatMap(_.errorText)) shouldBe Some(cmsText)
      }
    }

    "EnablePaymentLimitation return isPaymentLimitationApplied = false when payment service return false for shouldBlockOutboundIndianCardPayLater " in {
      val errorCMSId            = 11111
      val cid1                  = 55555
      val cid2                  = 666666
      val cmsText               = "Please enter Valid MasterCard"
      val paymentLimitationInfo = Some(paymentLimitationInfoTemplate.copy(siteId = cid1, errorCMSId = errorCMSId))
      val mockPaymentReq = Some(
        PaymentRequest(
          ccBin = Some("333333"),
          ccId = None,
          selectedPaymentMethod = Some(1)
        )
      )
      val experimentData = Some(
        ExperimentData(
          userId = "cb2416c1-432c-4809-9414-7a08496a9076",
          deviceTypeId = "1",
          memberId = Some("********"),
          trafficGroup = Some("1"),
          cId = Some(cid2.toString),
          aId = Some("130243"),
          serverName = None
        )
      )
      val enabledFeatures = Some(Seq("EnablePaymentLimitation"))
      val mockPayment     = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Agency)

      val setupBookingRequest = SetupBookingRequest(
        ProductsRequest(),
        paymentRequest = mockPaymentReq,
        userContext = Some(UserContextMock.value.copy(experimentData = experimentData)),
        enabledFeatures = enabledFeatures
      )
      val childRoom  = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val masterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(childRoom))
      val property   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(masterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(property), debug = None))
      )

      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))
      when(setupPaymentService.shouldBlockOutboundIndianCardPayLater(any(), any(), any(), any()))
        .thenReturn(false)

      val result = getSetupBookingFacade.getCreditCardInfoResponse(
        setupBookingRequest = setupBookingRequest,
        productData = mockProductData,
        creditCardInfo = None,
        memberInfo = None,
        paymentLimitation = paymentLimitationInfo,
        cmsContext = Some(cmsContext),
        availablePaymentMethodsResult = mockAvailablePaymentMethodsResult
      )

      result.map { response =>
        response.isDefined shouldBe true
        response.flatMap(_.paymentLimitationResponse.map(_.isApplied)) shouldBe Some(false)
        response.flatMap(_.paymentLimitationResponse.flatMap(_.errorText)) shouldBe Some(null)
      }
    }

    "getCreditCardInfoResponse should create paymentLimitationResponse when PMC-5033 is A side" in {
      val enabledFeatures = Some(Seq("EnablePaymentLimitation"))
      val experimentData = Some(
        ExperimentData(
          userId = "cb2416c1-432c-4809-9414-7a08496a9076",
          deviceTypeId = "1",
          memberId = Some("********"),
          trafficGroup = Some("1"),
          cId = Some("55555"),
          aId = Some("130243"),
          serverName = None
        )
      )
      val mockPaymentReq = Some(
        PaymentRequest(
          ccBin = Some("333333"),
          ccId = None,
          selectedPaymentMethod = Some(1)
        )
      )
      val setupBookingRequest = SetupBookingRequest(
        ProductsRequest(),
        paymentRequest = mockPaymentReq,
        userContext = Some(UserContextMock.value.copy(experimentData = experimentData)),
        enabledFeatures = enabledFeatures
      )

      val mockProductData = mockProductsProduct.copy(properties = Seq.empty)

      val mockRequestContext = mock[RequestContext]
      val mockFeatureAware   = mock[FeatureAware]

      when(mockFeatureAware.RemoveReadingPMCCampaignDataFromBFDB).thenReturn(false)
      when(mockRequestContext.featureAware).thenReturn(Some(mockFeatureAware))
      when(setupContext.requestContext).thenReturn(context.copy(featureAware = Some(mockFeatureAware)))

      val result = getSetupBookingFacade.getCreditCardInfoResponse(
        setupBookingRequest = setupBookingRequest,
        productData = mockProductData,
        creditCardInfo = None,
        memberInfo = None,
        paymentLimitation = None,
        cmsContext = Some(cmsContext),
        availablePaymentMethodsResult = mockAvailablePaymentMethodsResult
      )(setupContext)

      result.map { response =>
        response.isDefined shouldBe true
        response.flatMap(_.paymentLimitationResponse.map(_.isApplied)) shouldBe Some(false)
        response.flatMap(_.paymentLimitationResponse.flatMap(_.errorText)) shouldBe Some(null)
      }
    }

    "getCreditCardInfoResponse should not create paymentLimitationResponse when PMC-5033 is B side" in {
      val enabledFeatures = Some(Seq("EnablePaymentLimitation"))
      val experimentData = Some(
        ExperimentData(
          userId = "cb2416c1-432c-4809-9414-7a08496a9076",
          deviceTypeId = "1",
          memberId = Some("********"),
          trafficGroup = Some("1"),
          cId = Some("55555"),
          aId = Some("130243"),
          serverName = None
        )
      )
      val mockPaymentReq = Some(
        PaymentRequest(
          ccBin = Some("333333"),
          ccId = None,
          selectedPaymentMethod = Some(1)
        )
      )
      val setupBookingRequest = SetupBookingRequest(
        ProductsRequest(),
        paymentRequest = mockPaymentReq,
        userContext = Some(UserContextMock.value.copy(experimentData = experimentData)),
        enabledFeatures = enabledFeatures
      )

      val mockProductData = mockProductsProduct.copy(properties = Seq.empty)

      val mockRequestContext = mock[RequestContext]
      val mockFeatureAware   = mock[FeatureAware]

      when(mockFeatureAware.RemoveReadingPMCCampaignDataFromBFDB).thenReturn(true)
      when(mockRequestContext.featureAware).thenReturn(Some(mockFeatureAware))
      when(setupContext.requestContext).thenReturn(context.copy(featureAware = Some(mockFeatureAware)))

      val result = getSetupBookingFacade.getCreditCardInfoResponse(
        setupBookingRequest = setupBookingRequest,
        productData = mockProductData,
        creditCardInfo = None,
        memberInfo = None,
        paymentLimitation = None,
        cmsContext = Some(cmsContext),
        availablePaymentMethodsResult = mockAvailablePaymentMethodsResult
      )(setupContext)

      result.map { response =>
        response.isDefined shouldBe true
        response.flatMap(_.paymentLimitationResponse) shouldBe None
      }
    }

    "shouldBlockOutboundBnplIndianCard" should {

      val shouldBlock: Boolean    = true
      val shouldNotBlock: Boolean = !shouldBlock

      val paymentLimitationTestCases = Table(
        (
          "description",
          "paymentMethod",
          "shouldBlockOutboundBnplIndianCard",
          "errorCMSId",
          "expectedIsApplied",
          "expectedErrorText"
        ),
        (
          "isPaymentLimitationApplied = false when method=amex",
          "amex",
          shouldNotBlock,
          0,
          false,
          null
        ),
        (
          "isPaymentLimitationApplied = true with Generic error when method=mastercard, shouldBlock=true",
          "mastercard",
          shouldBlock,
          356570,
          true,
          "{0} is not supported for Pay later transactions. Please try with a different card."
        ),
        (
          "isPaymentLimitationApplied = true with Generic error when method=amex, shouldBlock=true",
          "amex",
          shouldBlock,
          356570,
          true,
          "{0} is not supported for Pay later transactions. Please try with a different card."
        ),
        (
          "isPaymentLimitationApplied = false when method=visa, shouldBlock=false",
          "visa",
          shouldNotBlock,
          0,
          false,
          null
        )
      )

      forAll(paymentLimitationTestCases) {
        (
            description,
            selectedPaymentMethodName,
            shouldBlockOutboundBnplIndianCard,
            errorCMSId,
            expectedIsApplied,
            expectedErrorText
        ) =>
          description in {
            val cid1: WorkflowId                 = 55555
            val cid2: WorkflowId                 = 666666
            val randomCcBin: String              = "111111"
            val randomSelectedPaymentMethod: Int = 1
            val paymentLimitationInfo: Option[PaymentLimitationInfo] =
              Some(paymentLimitationInfoTemplate.copy(siteId = cid1, errorCMSId = errorCMSId))
            val mockPaymentReq: Option[PaymentRequest] = Some(
              PaymentRequest(
                ccBin = Some(randomCcBin),
                ccId = None,
                selectedPaymentMethod = Some(randomSelectedPaymentMethod)
              )
            )

            val mockPayment: EnrichedPayment = mock[EnrichedPayment]
            when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Agency)
            when(cmsContext.get(CmsItem(errorCMSId, ""))).thenReturn(expectedErrorText)

            implicit val setupBookingContext: SetupBookingContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)
            when(setupBookingContext.requestContext.featureAware).thenReturn(Some(featureAware))

            val setupBookingRequest: SetupBookingRequest = SetupBookingRequest(
              ProductsRequest(),
              paymentRequest = mockPaymentReq,
              userContext = Some(UserContextMock.value),
              enabledFeatures = Some(Seq("EnablePaymentLimitation"))
            )

            val childRoom: EnrichedChildRoom = BookingMockHelper.baseChildrenRoom().copy(payment = Some(mockPayment))
            val masterRoom: EnrichedMasterRoom =
              BookingMockHelper.baseMasterRoom().copy(childrenRooms = List(childRoom))
            val property: Property = BookingMockHelper.baseProperty().copy(masterRooms = Seq(masterRoom))
            val mockMerchantPropertyProduct: BookingPropertiesData = bookingPropertyProduct().copy(
              papiProperties = Some(transformers.Properties(property = Seq(property), debug = None))
            )
            val mockProductData: ProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))

            when(
              setupPaymentService.shouldBlockOutboundIndianCardPayLater(
                any(),
                any(),
                any(),
                any()
              )
            ).thenReturn(shouldBlockOutboundBnplIndianCard)

            val result: Future[Option[CreditCardInfoSetupResponse]] = getSetupBookingFacade.getCreditCardInfoResponse(
              setupBookingRequest = setupBookingRequest,
              productData = mockProductData,
              creditCardInfo = None,
              memberInfo = None,
              paymentLimitation = paymentLimitationInfo,
              cmsContext = Some(cmsContext),
              paymentMethodId = Some(randomSelectedPaymentMethod),
              paymentMethodName = Some(selectedPaymentMethodName),
              availablePaymentMethodsResult = mockAvailablePaymentMethodsResult
            )(setupBookingContext)

            result.map { response =>
              response.isDefined shouldBe true
              response.flatMap(_.paymentLimitationResponse.map(_.isApplied)) shouldBe Some(expectedIsApplied)
              response.flatMap(_.paymentLimitationResponse.flatMap(_.errorText)) shouldBe Some(
                if (expectedErrorText == null) null
                else expectedErrorText.replace("{0}", selectedPaymentMethodName)
              )
            }
          }
      }
    }

    "shouldBlockInboundNonIndiaCardForAdvancePay" should {

      val testCases: TableFor2[Boolean, CmsItem] = Table(
        ("shouldBlock", "cmsItem"),
        (true, CmsItems.PaymentGenericNonCompliantCard),
        (false, CmsItem(0, ""))
      )

      forAll(testCases) { (shouldBlock, cmsItem) =>
        s"shouldBlockInboundNonIndiaCardForAdvancePay = $shouldBlock" in {
          val cid1: WorkflowId = 55555
          val cid2: WorkflowId = 666666
          val paymentLimitationInfo: Option[PaymentLimitationInfo] =
            Some(paymentLimitationInfoTemplate.copy(siteId = cid1, errorCMSId = cmsItem.cmsId))
          val mockPaymentRequest: Option[PaymentRequest] = Some(
            PaymentRequest(
              ccBin = Some("400016"),
              ccId = None,
              selectedPaymentMethod = Some(visa.id)
            )
          )
          val experimentData: Option[ExperimentData] = Some(
            ExperimentData(
              userId = "cb2416c1-432c-4809-9414-7a08496a9076",
              deviceTypeId = "1",
              memberId = Some("********"),
              trafficGroup = Some("1"),
              cId = Some(cid2.toString),
              aId = Some("130243"),
              serverName = None
            )
          )
          when(cmsContext.get(CmsItem(cmsItem.cmsId, ""))).thenReturn(cmsItem.text)

          val featureAware: FeatureAware                        = mock[FeatureAware]
          implicit val setupBookingContext: SetupBookingContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)
          when(setupBookingContext.requestContext.featureAware).thenReturn(Some(featureAware))
          val setupBookingRequest: SetupBookingRequest = SetupBookingRequest(
            ProductsRequest(),
            paymentRequest = mockPaymentRequest,
            userContext = Some(UserContextMock.value.copy(experimentData = experimentData)),
            enabledFeatures = Some(Seq("EnablePaymentLimitation"))
          )

          when(setupPaymentService.shouldBlockInboundNonIndiaCardForAdvancePay(any(), any(), any(), any()))
            .thenReturn(shouldBlock)

          val result: Future[Option[CreditCardInfoSetupResponse]] = getSetupBookingFacade.getCreditCardInfoResponse(
            setupBookingRequest = setupBookingRequest,
            productData = mockProductsProduct,
            creditCardInfo = None,
            memberInfo = None,
            paymentLimitation = paymentLimitationInfo,
            cmsContext = Some(cmsContext),
            paymentMethodId = Some(visa.id),
            paymentMethodName = Some(visa.name),
            availablePaymentMethodsResult = mockAvailablePaymentMethodsResult
          )(setupBookingContext)

          result.map { response =>
            response.isDefined shouldBe true
            response.flatMap(_.paymentLimitationResponse.map(_.isApplied)) shouldBe Some(shouldBlock)
            response.flatMap(_.paymentLimitationResponse.flatMap(_.errorText)) shouldBe Some(cmsItem.text)
          }
        }
      }
    }

    "shouldBlockInboundOutBoundIndiaFlight" should {

      val testCases: TableFor2[Boolean, CmsItem] = Table(
        ("shouldBlock", "cmsItem"),
        (true, CmsItems.PaymentGenericNonCompliantCard),
        (false, CmsItem(0, ""))
      )

      forAll(testCases) { (shouldBlock, cmsItem) =>
        s"shouldBlockInboundOutBoundIndiaFlight = $shouldBlock" in {
          val mockPaymentRequest: Option[PaymentRequest] = Some(
            PaymentRequest(
              ccBin = Some("400016"),
              ccId = None,
              selectedPaymentMethod = Some(visa.id)
            )
          )
          val indiaCreditCard = mock[CreditCardInfo]
          when(indiaCreditCard.countryInfo).thenReturn(
            Some(CountryInfo(CountryId.India, "India", "India", "IN", "IND"))
          )
          when(indiaCreditCard.binRangeInfo).thenReturn(None)
          when(cmsContext.get(CmsItem(cmsItem.cmsId, ""))).thenReturn(cmsItem.text)

          val featureAware: FeatureAware = mock[FeatureAware]
          when(flightComplianceServiceImpl.shouldBlockIndiaFlight(any(), any(), any())(any()))
            .thenReturn(shouldBlock)
          implicit val setupBookingContext: SetupBookingContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)
          when(setupBookingContext.requestContext.featureAware).thenReturn(Some(featureAware))
          val setupBookingRequest: SetupBookingRequest = SetupBookingRequest(
            ProductsRequest(),
            paymentRequest = mockPaymentRequest
          )

          when(setupPaymentService.shouldBlockInboundNonIndiaCardForAdvancePay(any(), any(), any(), any()))
            .thenReturn(false)
          val result: Future[Option[CreditCardInfoSetupResponse]] = getSetupBookingFacade.getCreditCardInfoResponse(
            setupBookingRequest = setupBookingRequest,
            productData = mockProductsProduct,
            creditCardInfo = Some(indiaCreditCard),
            memberInfo = None,
            paymentLimitation = None,
            cmsContext = Some(cmsContext),
            paymentMethodId = Some(visa.id),
            paymentMethodName = Some(visa.name),
            availablePaymentMethodsResult = mockAvailablePaymentMethodsResult
          )(setupBookingContext)

          result.map { response =>
            response.isDefined shouldBe true
            response.flatMap(_.paymentLimitationResponse.map(_.isApplied)).getOrElse(false) shouldBe shouldBlock
            response.flatMap(_.paymentLimitationResponse.flatMap(_.errorText)).getOrElse("") shouldBe cmsItem.text
          }
        }
      }
    }
  }

  "getCustomerEmail" should {
    "use email from memberInfo as higher priority than from setupRequest" in {
      val mockMemberDetails = mock[MemberDetails]
      when(mockMemberDetails.email).thenReturn("<EMAIL>")
      val mockMemberInfo = Some(mockMemberDetails)
      val setupBookingRequestWithEmail = setupBookingRequest.copy(
        customerInfo = Some(CustomerV2("I", "L", "<EMAIL>", 0))
      )

      val result = getSetupBookingFacade.getCustomerEmail(setupBookingRequestWithEmail, mockMemberInfo)
      result shouldBe Some("<EMAIL>")
    }

    "return email when only exists in setup request" in {
      val setupBookingRequestWithEmail = setupBookingRequest.copy(
        customerInfo = Some(CustomerV2("I", "L", "<EMAIL>", 0))
      )

      val result = getSetupBookingFacade.getCustomerEmail(setupBookingRequestWithEmail, None)
      result shouldBe Some("<EMAIL>")
    }

    "return none when no memberIn and requested email ends with @agoda.com" in {
      val setupBookingRequestWithEmail = setupBookingRequest.copy(
        customerInfo = Some(CustomerV2("I", "L", "<EMAIL>", 0))
      )

      val result = getSetupBookingFacade.getCustomerEmail(setupBookingRequestWithEmail, None)
      result shouldBe None
    }
  }

  "getCashbackRedemptionParameter" should {
    "return CashbackRedemptionParameter correctly" in {
      val service             = getSetupBookingFacade
      val memberDetails       = mock[MemberDetails]
      val loyaltyProfile      = mock[LoyaltyProfile]
      val cashBackBalanceInfo = Some(CashBackBalanceInfo(12d, "USD", 12d))
      val whitelabelInfo      = mock[WhiteLabelInfo]

      when(whitelabelInfo.isFeatureEnabled(WhiteLabelFeatureName.CashbackRedemption)).thenReturn(true)
      when(loyaltyProfile.cashBackBalance).thenReturn(cashBackBalanceInfo)
      when(memberDetails.isCashbackRedemptionEligible).thenReturn(true)

      val request = setupBookingRequest.copy(
        enabledFeatures = Some(Seq("cashbackRedemption", "test")),
        redeemRequest = Some(RedeemRequest(0, Some(11d)))
      )
      val result =
        service.getCashbackRedemptionParameter(request, Some(memberDetails), Some(loyaltyProfile), whitelabelInfo)

      result shouldBe Some(
        CashbackRedemptionParameter(isCashbackRedemptionEligible = true, BigDecimal(12d), BigDecimal(11d))
      )
    }

    "return None if there is no feature flag" in {
      val service        = getSetupBookingFacade
      val memberDetails  = mock[MemberDetails]
      val loyaltyProfile = mock[LoyaltyProfile]
      val whitelabelInfo = mock[WhiteLabelInfo]
      val request        = setupBookingRequest.copy(enabledFeatures = Some(Seq("test")))
      val result =
        service.getCashbackRedemptionParameter(request, Some(memberDetails), Some(loyaltyProfile), whitelabelInfo)

      result shouldBe None
    }

    "return None if cashbackRedemption is disable from whitelabel" in {
      val service             = getSetupBookingFacade
      val memberDetails       = mock[MemberDetails]
      val loyaltyProfile      = mock[LoyaltyProfile]
      val cashBackBalanceInfo = Some(CashBackBalanceInfo(12d, "USD", 12d))
      val whitelabelInfo      = mock[WhiteLabelInfo]

      when(whitelabelInfo.isFeatureEnabled(WhiteLabelFeatureName.CashbackRedemption)).thenReturn(false)
      when(loyaltyProfile.cashBackBalance).thenReturn(cashBackBalanceInfo)
      when(memberDetails.isCashbackRedemptionEligible).thenReturn(true)

      val request = setupBookingRequest.copy(
        enabledFeatures = Some(Seq("cashbackRedemption", "test")),
        redeemRequest = Some(RedeemRequest(0, Some(11d)))
      )
      val result =
        service.getCashbackRedemptionParameter(request, Some(memberDetails), Some(loyaltyProfile), whitelabelInfo)

      result shouldBe None
    }
  }

  "isSaveCcofEligible" should {

    "return true when all conditions are matched" in {
      val mockMemberDetails = mock[MemberDetails]
      when(mockMemberDetails.isCreditCardOnFileOptOut).thenReturn(false)
      val mockMemberInfo = Some(mockMemberDetails)

      val mockPaymentReq = Some(
        PaymentRequest(
          ccBin = Some("1234"),
          ccId = None,
          selectedPaymentMethod = Some(1)
        )
      )

      val mockPayment = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Merchant)
      val merchantRoom       = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val merchantMasterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(merchantRoom))
      val merchantProperty   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(merchantMasterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(merchantProperty), debug = None))
      )
      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))

      val result = getSetupBookingFacade.isSaveCcofEligible(
        mockMemberInfo,
        mockPaymentReq,
        mockProductData,
        Some(mockCreditCardInfo)
      )
      result shouldBe true
    }

    "return true when payment method is PayPal" in {
      val mockMemberDetails = mock[MemberDetails]
      val mockMemberInfo    = Some(mockMemberDetails)

      val mockPaymentReq = Some(
        PaymentRequest(
          selectedPaymentMethod = Some(5)
        )
      )

      val mockPayment = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Merchant)
      val merchantRoom       = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val merchantMasterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(merchantRoom))
      val merchantProperty   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(merchantMasterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(merchantProperty), debug = None))
      )
      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))

      val result = getSetupBookingFacade.isSaveCcofEligible(
        mockMemberInfo,
        mockPaymentReq,
        mockProductData,
        Some(mockCreditCardInfo)
      )

      result shouldBe true
    }

    "return true when payment method is not VISA/MASTER/JCB/AMEX" in {

      val mockMemberDetails = mock[MemberDetails]
      val mockMemberInfo    = Some(mockMemberDetails)

      val mockPaymentReq = Seq(
        Some(
          PaymentRequest(
            selectedPaymentMethod = Some(EbeCreditCardType.UnionPayCreditCard)
          )
        ),
        Some(
          PaymentRequest(
            selectedPaymentMethod = Some(EbeCreditCardType.RuPay)
          )
        ),
        Some(
          PaymentRequest(
            selectedPaymentMethod = Some(EbeCreditCardType.Shinhan)
          )
        ),
        Some(
          PaymentRequest(
            selectedPaymentMethod = Some(EbeCreditCardType.KB)
          )
        ),
        Some(
          PaymentRequest(
            selectedPaymentMethod = Some(EbeCreditCardType.Hyundai)
          )
        ),
        Some(
          PaymentRequest(
            selectedPaymentMethod = Some(EbeCreditCardType.BC)
          )
        ),
        Some(
          PaymentRequest(
            selectedPaymentMethod = Some(EbeCreditCardType.Samsung)
          )
        ),
        Some(
          PaymentRequest(
            selectedPaymentMethod = Some(EbeCreditCardType.NH)
          )
        ),
        Some(
          PaymentRequest(
            selectedPaymentMethod = Some(EbeCreditCardType.Hana)
          )
        ),
        Some(
          PaymentRequest(
            selectedPaymentMethod = Some(EbeCreditCardType.Lotte)
          )
        ),
        Some(
          PaymentRequest(
            selectedPaymentMethod = Some(EbeCreditCardType.DiscoverCard)
          )
        ),
        Some(
          PaymentRequest(
            selectedPaymentMethod = Some(EbeCreditCardType.KoreaLocalCard)
          )
        ),
        Some(
          PaymentRequest(
            selectedPaymentMethod = Some(EbeCreditCardType.Diners)
          )
        )
      )

      val mockPayment = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Merchant)
      val merchantRoom       = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val merchantMasterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(merchantRoom))
      val merchantProperty   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(merchantMasterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(merchantProperty), debug = None))
      )
      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))

      val result = mockPaymentReq.map { paymentRequest =>
        getSetupBookingFacade.isSaveCcofEligible(
          mockMemberInfo,
          paymentRequest,
          mockProductData,
          Some(mockCreditCardInfo)
        )
      }

      result.forall(_ == true) shouldBe true
    }

    "return false when user is not logged in" in {
      val mockMemberInfo = None

      val mockPaymentReq = Some(
        PaymentRequest(
          ccBin = Some("1234"),
          ccId = None,
          selectedPaymentMethod = Some(1)
        )
      )

      val mockPayment = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Merchant)
      val merchantRoom       = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val merchantMasterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(merchantRoom))
      val merchantProperty   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(merchantMasterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(merchantProperty), debug = None))
      )
      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))

      val result = getSetupBookingFacade.isSaveCcofEligible(
        mockMemberInfo,
        mockPaymentReq,
        mockProductData,
        Some(mockCreditCardInfo)
      )
      result shouldBe false
    }

    "return false when user is opt out" in {
      val mockMemberDetails = mock[MemberDetails]
      when(mockMemberDetails.isCreditCardOnFileOptOut).thenReturn(true)
      val mockMemberInfo = Some(mockMemberDetails)

      val mockPaymentReq = Some(
        PaymentRequest(
          ccBin = Some("1234"),
          ccId = None,
          selectedPaymentMethod = Some(1)
        )
      )

      val mockPayment = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Merchant)
      val merchantRoom       = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val merchantMasterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(merchantRoom))
      val merchantProperty   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(merchantMasterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(merchantProperty), debug = None))
      )
      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))

      val result = getSetupBookingFacade.isSaveCcofEligible(
        mockMemberInfo,
        mockPaymentReq,
        mockProductData,
        Some(mockCreditCardInfo)
      )
      result shouldBe false
    }

    "return false when user select a saved card" in {
      val mockMemberDetails = mock[MemberDetails]
      when(mockMemberDetails.isCreditCardOnFileOptOut).thenReturn(false)
      val mockMemberInfo = Some(mockMemberDetails)

      val mockPaymentReq = Some(
        PaymentRequest(
          ccBin = Some("1234"),
          ccId = Some(987654321),
          selectedPaymentMethod = Some(1)
        )
      )

      val mockPayment = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Merchant)
      val merchantRoom       = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val merchantMasterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(merchantRoom))
      val merchantProperty   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(merchantMasterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(merchantProperty), debug = None))
      )
      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))

      val result = getSetupBookingFacade.isSaveCcofEligible(
        mockMemberInfo,
        mockPaymentReq,
        mockProductData,
        Some(mockCreditCardInfo)
      )
      result shouldBe false
    }

    "return false when there is no property booking" in {
      val mockMemberDetails = mock[MemberDetails]
      when(mockMemberDetails.isCreditCardOnFileOptOut).thenReturn(false)
      val mockMemberInfo = Some(mockMemberDetails)

      val mockPaymentReq = Some(
        PaymentRequest(
          ccBin = Some("1234"),
          ccId = None,
          selectedPaymentMethod = Some(1)
        )
      )

      val mockProductData = mockProductsProduct.copy(properties = Seq.empty)

      val result = getSetupBookingFacade.isSaveCcofEligible(
        mockMemberInfo,
        mockPaymentReq,
        mockProductData,
        Some(mockCreditCardInfo)
      )
      result shouldBe false
    }

    "return false when this is Agency booking" in {
      val mockMemberDetails = mock[MemberDetails]
      when(mockMemberDetails.isCreditCardOnFileOptOut).thenReturn(false)
      val mockMemberInfo = Some(mockMemberDetails)

      val mockPaymentReq = Some(
        PaymentRequest(
          ccBin = Some("1234"),
          ccId = None,
          selectedPaymentMethod = Some(1)
        )
      )

      val mockPayment = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Agency)
      val merchantRoom       = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val merchantMasterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(merchantRoom))
      val merchantProperty   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(merchantMasterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(merchantProperty), debug = None))
      )
      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))

      val result = getSetupBookingFacade.isSaveCcofEligible(
        mockMemberInfo,
        mockPaymentReq,
        mockProductData,
        Some(mockCreditCardInfo)
      )
      result shouldBe false
    }

    "process whitelabel CCOF config successfully" when {
      val testcases = Table(
        ("description", "isConfigCCOFEnabled", "expectedResult"),
        ("isCCOFEnabled is enabled", true, true),
        ("isCCOFEnabled is disabled", false, false)
      )

      forAll(testcases) { (description, isConfigCCOFEnabled, expectedResult) =>
        description in {
          implicit val setupContext: SetupBookingContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)
          when(setupContext.whiteLabelInfo.feature.userProfile.isPaymentEnabled).thenReturn(Some(isConfigCCOFEnabled))
          when(setupContext.requestContext.featureAware).thenReturn(Some(featureAware))

          val mockMemberDetails = mock[MemberDetails]
          when(mockMemberDetails.isCreditCardOnFileOptOut).thenReturn(false)
          val mockMemberInfo = Some(mockMemberDetails)
          val mockPaymentReq = Some(
            PaymentRequest(
              ccBin = Some("1234"),
              ccId = None,
              selectedPaymentMethod = Some(1)
            )
          )

          val result = getSetupBookingFacade.isSaveCcofEligible(
            mockMemberInfo,
            mockPaymentReq,
            mockProductsProduct,
            Some(mockCreditCardInfo)
          )
          result shouldBe expectedResult
        }
      }
    }

    "process card issuing bank for CCOF" when {
      val testcases = Table(
        ("description", "countryCardIssuingId", "countryCardIssuingName", "expectedResult"),
        ("return false when card issuing bank is India", "35", "India", false),
        ("return true when card issuing bank is not India", "3", "Japan", true),
        ("return true when countryInfo is None", "", "", true)
      )

      forAll(testcases) { (description, countryCardIssuingId, countryCardIssuingName, expectedResult) =>
        description in {
          val mockMemberDetails = mock[MemberDetails]
          when(mockMemberDetails.isCreditCardOnFileOptOut).thenReturn(false)
          val mockMemberInfo = Some(mockMemberDetails)

          val mockPaymentReq = Some(
            PaymentRequest(
              ccBin = Some("1234"),
              ccId = None,
              selectedPaymentMethod = Some(1)
            )
          )

          val mockPayment = mock[EnrichedPayment]
          when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Merchant)
          val merchantRoom       = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
          val merchantMasterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(merchantRoom))
          val merchantProperty   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(merchantMasterRoom))
          val mockMerchantPropertyProduct = bookingPropertyProduct.copy(
            papiProperties = Some(transformers.Properties(property = Seq(merchantProperty), debug = None))
          )
          val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))
          if (countryCardIssuingId == "")
            when(mockCreditCardInfo.countryInfo).thenReturn(None)
          else
            when(mockCreditCardInfo.countryInfo).thenReturn(
              Option(CountryInfo(countryCardIssuingId.toInt, countryCardIssuingName, "", "", ""))
            )
          val result = getSetupBookingFacade.isSaveCcofEligible(
            mockMemberInfo,
            mockPaymentReq,
            mockProductData,
            Some(mockCreditCardInfo)
          )
          result shouldBe expectedResult
        }
      }
    }

    "process successful when CreditCardInfo is nothing for CCOF" in {
      val mockMemberDetails = mock[MemberDetails]
      when(mockMemberDetails.isCreditCardOnFileOptOut).thenReturn(false)
      val mockMemberInfo = Some(mockMemberDetails)

      val mockPaymentReq = Some(
        PaymentRequest(
          ccBin = Some("1234"),
          ccId = None,
          selectedPaymentMethod = Some(1)
        )
      )

      val mockPayment = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Merchant)
      val merchantRoom       = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val merchantMasterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(merchantRoom))
      val merchantProperty   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(merchantMasterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(merchantProperty), debug = None))
      )
      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))
      val result = getSetupBookingFacade.isSaveCcofEligible(
        mockMemberInfo,
        mockPaymentReq,
        mockProductData,
        None
      )
      result shouldBe true
    }

    "process card issuing bank for CCOF PayLater" when {
      val testcases = Table(
        (
          "description",
          "countryCardIssuingId",
          "countryCardIssuingName",
          "chargeOption",
          "expectedResult"
        ),
        (
          "return false when card issuing bank is India and PayLater",
          "35",
          "India",
          ChargeOption.PayLater,
          false
        ),
        (
          "return false when card issuing bank is India and PayAtCheckIn",
          "35",
          "India",
          ChargeOption.PayAtCheckIn,
          false
        ),
        (
          "return false when card issuing bank is India and PayWithInstallment",
          "35",
          "India",
          ChargeOption.PayWithInstallment,
          false
        ),
        (
          "return false when card issuing bank is India and PayNow",
          "35",
          "India",
          ChargeOption.PayNow,
          false
        ),
        (
          "return true when card issuing bank is not India and PayLater",
          "3",
          "Japan",
          ChargeOption.PayLater,
          true
        ),
        (
          "return true when card issuing bank is not India and PayNow",
          "3",
          "Japan",
          ChargeOption.PayNow,
          true
        ),
        (
          "return true when card issuing bank is not India and PayAtCheckIn",
          "3",
          "Japan",
          ChargeOption.PayAtCheckIn,
          true
        ),
        (
          "return true when card issuing bank is not India and PayWithInstallment",
          "3",
          "Japan",
          ChargeOption.PayWithInstallment,
          true
        ),
        (
          "return true when countryInfo is None and PayLater",
          "",
          "",
          ChargeOption.PayLater,
          true
        )
      )

      forAll(testcases) {
        (
            description,
            countryCardIssuingId,
            countryCardIssuingName,
            chargeOption,
            expectedResult
        ) =>
          description in {
            val mockMemberDetails = mock[MemberDetails]
            when(mockMemberDetails.isCreditCardOnFileOptOut).thenReturn(false)
            val mockMemberInfo = Some(mockMemberDetails)

            val mockPaymentReq = Some(
              PaymentRequest(
                ccBin = Some("1234"),
                ccId = None,
                selectedPaymentMethod = Some(1)
              )
            )

            val mockPayment = mock[EnrichedPayment]
            when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Merchant)
            val merchantRoom       = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
            val merchantMasterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(merchantRoom))
            val merchantProperty   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(merchantMasterRoom))
            val mockMerchantPropertyProduct = bookingPropertyProduct.copy(
              papiProperties = Some(transformers.Properties(property = Seq(merchantProperty), debug = None)),
              selectedChargeOption = Some(chargeOption)
            )
            val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))
            if (countryCardIssuingId == "")
              when(mockCreditCardInfo.countryInfo).thenReturn(None)
            else
              when(mockCreditCardInfo.countryInfo).thenReturn(
                Option(CountryInfo(countryCardIssuingId.toInt, countryCardIssuingName, "", "", ""))
              )
            val result = getSetupBookingFacade.isSaveCcofEligible(
              mockMemberInfo,
              mockPaymentReq,
              mockProductData,
              Some(mockCreditCardInfo)
            )
            result shouldBe expectedResult
          }
      }
    }

    "process successful when CreditCardInfo is nothing for India CCOF PayNow" in {
      val mockMemberDetails = mock[MemberDetails]
      when(mockMemberDetails.isCreditCardOnFileOptOut).thenReturn(false)
      val mockMemberInfo = Some(mockMemberDetails)

      val mockPaymentReq = Some(
        PaymentRequest(
          ccBin = Some("1234"),
          ccId = None,
          selectedPaymentMethod = Some(1)
        )
      )

      val mockPayment = mock[EnrichedPayment]
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Merchant)
      val merchantRoom       = BookingMockHelper.baseChildrenRoom.copy(payment = Some(mockPayment))
      val merchantMasterRoom = BookingMockHelper.baseMasterRoom.copy(childrenRooms = List(merchantRoom))
      val merchantProperty   = BookingMockHelper.baseProperty.copy(masterRooms = Seq(merchantMasterRoom))
      val mockMerchantPropertyProduct = bookingPropertyProduct.copy(papiProperties =
        Some(transformers.Properties(property = Seq(merchantProperty), debug = None))
      )
      val mockProductData = mockProductsProduct.copy(properties = Seq(mockMerchantPropertyProduct))
      val result = getSetupBookingFacade.isSaveCcofEligible(
        mockMemberInfo,
        mockPaymentReq,
        mockProductData,
        None
      )
      result shouldBe true
    }
  }

  "chargeCurrencyFromOptions" should {
    val displayCurrency                            = "THB"
    val defaultCurrency                            = "CAN"
    implicit val setupContext: SetupBookingContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)
    when(setupContext.requestContext.displayCurrency).thenReturn(displayCurrency)

    "with default support chargeCCYOption" in {
      val result = getSetupBookingFacade.chargeCurrencyFromOptions(setupBookingRequest, Nil)
      result shouldBe RequestContext.DEFAULT_CURRENCY
    }

    "from selected CCY chargeCCYOption with supported CCY" in {
      val selectedCurrency = "IDR"
      val request = setupBookingRequest.copy(
        paymentRequest = setupBookingRequest.paymentRequest.map(
          _.copy(
            selectedChargeCurrency = Some(selectedCurrency)
          )
        )
      )
      val result =
        getSetupBookingFacade.chargeCurrencyFromOptions(request, Seq(selectedCurrency, defaultCurrency))
      result shouldBe selectedCurrency
    }

    "from selected CCY chargeCCYOption without supported CCY" in {
      val selectedCurrency = "IDR"
      val fallbackCurrency = "MYR"
      val request = setupBookingRequest.copy(
        paymentRequest = setupBookingRequest.paymentRequest.map(
          _.copy(
            selectedChargeCurrency = Some(selectedCurrency)
          )
        )
      )
      val result =
        getSetupBookingFacade.chargeCurrencyFromOptions(request, Seq(fallbackCurrency, defaultCurrency))
      result shouldBe fallbackCurrency
    }

    "from display CCY chargeCCYOption with supported CCY" in {
      val selectedCurrency = "IDR"
      when(setupContext.requestContext.displayCurrency).thenReturn(selectedCurrency)
      val result =
        getSetupBookingFacade.chargeCurrencyFromOptions(
          setupBookingRequest,
          Seq(selectedCurrency, defaultCurrency)
        )
      result shouldBe selectedCurrency
    }

    "from display CCY chargeCCYOption without supported CCY" in {
      val selectedCurrency = "IDR"
      val fallbackCurrency = "MYR"
      when(setupContext.requestContext.displayCurrency).thenReturn(selectedCurrency)
      val result =
        getSetupBookingFacade.chargeCurrencyFromOptions(
          setupBookingRequest,
          Seq(fallbackCurrency, defaultCurrency)
        )
      result shouldBe fallbackCurrency
    }
  }

  "Unit test for isBankNameRequired equals false" should {

    val requestContext = mock[RequestContext]
    val featureAware   = mock[FeatureAware]
    when(requestContext.featureAware).thenReturn(Some(featureAware))
    val whiteLabelInfo = mock[WhiteLabelInfo]
    when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
    when(requestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)

    "for agency single property bookings" in {
      val paymentRequest =
        PaymentRequest(ccBin = Some("411111"), selectedPaymentMethod = Option(MPBPaymentMethod.MasterCard.value))

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = true

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          None
        )(requestContext)
        .map(result => assert(!result))
    }

    "for Korean credit card" in {
      val paymentRequest =
        PaymentRequest(ccBin = Some("411111"), selectedPaymentMethod = Option(MPBPaymentMethod.Samsung.value))

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          None
        )(requestContext)
        .map(result => assert(!result))
    }

    "for Japan CC bank issue country id" in {
      val paymentRequest =
        PaymentRequest(ccBin = Some("411111"), selectedPaymentMethod = Option(MPBPaymentMethod.MasterCard.value))

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(3, "Japan", "", "", "")))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          None
        )(requestContext)
        .map(result => assert(!result))
    }

    "for Korean CC bank issue country id" in {
      val paymentRequest =
        PaymentRequest(ccBin = Some("411111"), selectedPaymentMethod = Option(MPBPaymentMethod.MasterCard.value))

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(212, "South Korea", "", "", "")))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          None
        )(requestContext)
        .map(result => assert(!result))
    }

    "for merchant AmericanExpress CC" in {
      val paymentRequest =
        PaymentRequest(ccBin = Some("411111"), selectedPaymentMethod = Option(MPBPaymentMethod.AmericanExpress.value))

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          None
        )(requestContext)
        .map(result => assert(!result))
    }

    "for Malaysian debit card" in {
      val paymentRequest =
        PaymentRequest(ccBin = Some("411111"), selectedPaymentMethod = Option(MPBPaymentMethod.MasterCard.value))

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Debit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(198, "Malaysia", "", "", "")))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          None
        )(requestContext)
        .map(result => assert(!result))
    }

    "for redirect payment" in {
      val paymentMethod  = MPBPaymentMethod.MasterCard
      val paymentRequest = PaymentRequest(ccBin = Some("411111"), selectedPaymentMethod = Option(paymentMethod.value))

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))
      when(mockEbeRepository.isRedirectCard(paymentMethod.value)).thenReturn(Future.successful(true))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          None
        )(requestContext)
        .map(result => assert(!result))
    }

    "for payment token enabled" in {
      val paymentMethod  = MPBPaymentMethod.MasterCard
      val paymentRequest = PaymentRequest(ccBin = Some("411111"), selectedPaymentMethod = Option(paymentMethod.value))

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))
      when(mockEbeRepository.isPaymentTokenEnabled(whiteLabel.id, paymentMethod.value, bookingFlow.id))
        .thenReturn(Future.successful(true))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          None
        )(requestContext)
        .map(result => assert(!result))
    }

    "for user hasn't put credit card information yet. Both CCToken and CCBin don't exist" in {
      val paymentRequest = PaymentRequest(selectedPaymentMethod = Option(MPBPaymentMethod.MasterCard.value))

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          None
        )(requestContext)
        .map(result => assert(!result))
    }

    "for Union Pay Debit payment method" in {
      val paymentRequest =
        PaymentRequest(ccBin = Some("411111"), selectedPaymentMethod = Option(MPBPaymentMethod.UnionPayNTT.value))

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          None
        )(requestContext)
        .map(result => assert(!result))
    }

    "isBankNameRequired should be false if WL feature DisableIssuingBankDisplay is true" in {
      val paymentRequest =
        PaymentRequest(ccBin = Some("411111"), selectedPaymentMethod = Option(MPBPaymentMethod.MasterCard.value))

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      val featureAware = mock[FeatureAware]

      val whiteLabelInfo = mock[WhiteLabelInfo]
      when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
      when(whiteLabelInfo.feature).thenReturn(FeaturesConfiguration())
      when(
        whiteLabelInfo.isFeatureEnabled(mockitoEq(WhiteLabelFeatureName.DisableIssuingBankDisplay), any(), any(), any())
      )
        .thenReturn(true)

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          None
        )(context.copy(whiteLabelInfo = whiteLabelInfo, featureAware = Some(featureAware)))
        .map(result => assert(result))
    }
  }

  "Unit test for isBankNameRequired equals true" should {
    "for merchant regular CC" in {
      val paymentRequest =
        PaymentRequest(ccBin = Some("411111"), selectedPaymentMethod = Option(MPBPaymentMethod.MasterCard.value))

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          None
        )(requestContext)
        .map(result => assert(result))
    }
  }

  private def validateIsBankNameRequired(
      bookingFlow: BookingFlow,
      expected: Boolean,
      useNewFraudClient: Boolean = false
  ): Future[scalatest.Assertion] = {
    val paymentRequest = PaymentRequest(
      selectedPaymentMethod = Option(MPBPaymentMethod.MasterCard.value),
      ccToken = Some("invalid-token")
    )

    val whiteLabel      = WhiteLabel.Agoda
    val isAgencyBooking = false

    val whiteLabelInfo = mock[WhiteLabelInfo]
    val requestContext = mock[RequestContext]
    val featureAware   = mock[FeatureAware]

    when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
    when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))
    if (useNewFraudClient) {
      when(featureAware.useNewFraudClient).thenReturn(true)
      when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
      when(requestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
      when(mockFraudApiClientProxy.checkWhitelistFromCCToken(any())(any(), any()))
        .thenReturn(Future.successful(WhitelistCCTokenResponse(false)))
    } else {
      when(mockFraudHttpClientProxy.checkWhitelistFromCCToken(any())(any(), any()))
        .thenReturn(Future.successful(WhitelistCreditCard(false)))
    }

    when(requestContext.featureAware).thenReturn(Some(featureAware))
    when(requestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
    when(
      whiteLabelInfo
        .isFeatureEnabled(mockitoEq(WhiteLabelFeatureName.DisableIssuingBankDisplay), any(), any(), any())
    ).thenReturn(false)

    getSetupBookingFacade
      .isBankNameRequired(
        Some(paymentRequest),
        Some(mockCreditCardInfo),
        whiteLabel,
        bookingFlow,
        isAgencyBooking,
        correlationId,
        customerEmail
      )(requestContext)
      .map(result => {
        if (useNewFraudClient) {
          verify(mockFraudApiClientProxy, atLeastOnce()).checkWhitelistFromCCToken(any())(any(), any())
          verify(mockFraudHttpClientProxy, never()).checkWhitelistFromCCToken(any())(any(), any())
        }
        assert(result == expected)
      })
  }

  "isBankNameRequired" should {

    val requestContext = mock[RequestContext]
    val featureAware   = mock[FeatureAware]
    when(requestContext.featureAware).thenReturn(Some(featureAware))
    val whiteLabelInfo = mock[WhiteLabelInfo]
    when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
    when(requestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
    when(
      whiteLabelInfo.isFeatureEnabled(mockitoEq(WhiteLabelFeatureName.DisableIssuingBankDisplay), any(), any(), any())
    )
      .thenReturn(false)

    "maintain the right booking flows in the list requiredBankNameProductTypesList" in {
      val expected = List(BookingFlow.SingleProperty, BookingFlow.MixAndSave, BookingFlow.MultiHotel, BookingFlow.Cart)
      requiredBankNameProductTypesList should contain theSameElementsAs expected
    }

    "returns true for all the booking flows in requiredBankNameProductTypesList" in
      Inspectors.forAll(requiredBankNameProductTypesList) { bookingFlow =>
        validateIsBankNameRequired(bookingFlow, true)
      }

    "return false for all the booking flows which are not in requiredBankNameProductTypesList" in {
      val notRequiredBankNameProductTypesList = BookingFlow.values.toList.diff(requiredBankNameProductTypesList)
      Inspectors.forAll(notRequiredBankNameProductTypesList) { bookingFlow =>
        validateIsBankNameRequired(bookingFlow, false)
      }
    }

    "false for whitelisted credit card" in {
      val paymentRequest = PaymentRequest(
        selectedPaymentMethod = Option(MPBPaymentMethod.MasterCard.value),
        ccToken = Some("invalid-token")
      )

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))
      when(mockFraudHttpClientProxy.checkWhitelistFromCCToken(any())(any(), any()))
        .thenReturn(Future.successful(WhitelistCreditCard(true)))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          customerEmail
        )(requestContext)
        .map(result => assert(!result))
    }

    "true for non-whitelisted credit card" in {
      val paymentRequest = PaymentRequest(
        selectedPaymentMethod = Option(MPBPaymentMethod.MasterCard.value),
        ccToken = Some("invalid-token")
      )

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))
      when(mockFraudHttpClientProxy.checkWhitelistFromCCToken(any())(any(), any()))
        .thenReturn(Future.successful(WhitelistCreditCard(false)))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          customerEmail
        )(requestContext)
        .map(result => assert(result))
    }

    "true if checkWhitelistFromCCToken fails" in {
      val paymentRequest = PaymentRequest(
        selectedPaymentMethod = Option(MPBPaymentMethod.MasterCard.value),
        ccToken = Some("invalid-token")
      )

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))
      when(mockFraudHttpClientProxy.checkWhitelistFromCCToken(any())(any(), any()))
        .thenReturn(Future.failed(new Exception("checkWhitelistFromCCToken - fails")))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          customerEmail
        )(requestContext)
        .map(result => assert(result))
    }

    "true if customerEmail is null" in {
      val paymentRequest = PaymentRequest(
        selectedPaymentMethod = Option(MPBPaymentMethod.MasterCard.value),
        ccToken = Some("invalid-token")
      )

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false
      val customerEmail   = Some(null)

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))
      when(mockFraudHttpClientProxy.checkWhitelistFromCCToken(any())(any(), any()))
        .thenReturn(Future.failed(new Exception("checkWhitelistFromCCToken - fails")))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          customerEmail
        )(requestContext)
        .map(result => assert(result))
    }

  }

  "isBankNameRequired when exp MPF-1742=B to use new fraud client" should {

    val requestContext = mock[RequestContext]
    val featureAware   = mock[FeatureAware]
    when(featureAware.useNewFraudClient).thenReturn(true)
    when(requestContext.featureAware).thenReturn(Some(featureAware))
    val whiteLabelInfo = mock[WhiteLabelInfo]
    when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
    when(
      whiteLabelInfo.isFeatureEnabled(mockitoEq(WhiteLabelFeatureName.DisableIssuingBankDisplay), any(), any(), any())
    )
      .thenReturn(false)
    when(requestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)

    "maintain the right booking flows in the list requiredBankNameProductTypesList" in {
      val expected = List(BookingFlow.SingleProperty, BookingFlow.MixAndSave, BookingFlow.MultiHotel, BookingFlow.Cart)
      requiredBankNameProductTypesList should contain theSameElementsAs expected
    }

    "returns true for all the booking flows in requiredBankNameProductTypesList" in
      Inspectors.forAll(requiredBankNameProductTypesList) { bookingFlow =>
        validateIsBankNameRequired(bookingFlow, true, useNewFraudClient = true)
      }

    "return false for all the booking flows which are not in requiredBankNameProductTypesList" in {
      val notRequiredBankNameProductTypesList = BookingFlow.values.toList.diff(requiredBankNameProductTypesList)
      Inspectors.forAll(notRequiredBankNameProductTypesList) { bookingFlow =>
        validateIsBankNameRequired(bookingFlow, false, useNewFraudClient = true)
      }
    }

    "false for whitelisted credit card" in {
      val paymentRequest = PaymentRequest(
        selectedPaymentMethod = Option(MPBPaymentMethod.MasterCard.value),
        ccToken = Some("invalid-token")
      )

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))
      when(mockFraudApiClientProxy.checkWhitelistFromCCToken(any())(any(), any()))
        .thenReturn(Future.successful(WhitelistCCTokenResponse(true)))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          customerEmail
        )(requestContext)
        .map(result => {
          verify(mockFraudApiClientProxy, times(1)).checkWhitelistFromCCToken(any())(any(), any())
          verify(mockFraudHttpClientProxy, never()).checkWhitelistFromCCToken(any())(any(), any())
          assert(!result)
        })
    }

    "true for non-whitelisted credit card" in {
      val paymentRequest = PaymentRequest(
        selectedPaymentMethod = Option(MPBPaymentMethod.MasterCard.value),
        ccToken = Some("invalid-token")
      )

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))
      when(mockFraudApiClientProxy.checkWhitelistFromCCToken(any())(any(), any()))
        .thenReturn(Future.successful(WhitelistCCTokenResponse(false)))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          customerEmail
        )(requestContext)
        .map(result => {
          verify(mockFraudApiClientProxy, times(1)).checkWhitelistFromCCToken(any())(any(), any())
          verify(mockFraudHttpClientProxy, never()).checkWhitelistFromCCToken(any())(any(), any())
          assert(result)
        })
    }

    "true if checkWhitelistFromCCToken fails" in {
      val paymentRequest = PaymentRequest(
        selectedPaymentMethod = Option(MPBPaymentMethod.MasterCard.value),
        ccToken = Some("invalid-token")
      )

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))
      when(mockFraudApiClientProxy.checkWhitelistFromCCToken(any())(any(), any()))
        .thenReturn(Future.failed(new Exception("checkWhitelistFromCCToken - fails")))

      println(s"EXp: ${requestContext.featureAware.exists(_.useNewFraudClient)}")
      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          customerEmail
        )(requestContext)
        .map(result => {
          verify(mockFraudApiClientProxy, times(1)).checkWhitelistFromCCToken(any())(any(), any())
          verify(mockFraudHttpClientProxy, never()).checkWhitelistFromCCToken(any())(any(), any())
          assert(result)
        })
    }

    "true if customerEmail is null" in {
      val paymentRequest = PaymentRequest(
        selectedPaymentMethod = Option(MPBPaymentMethod.MasterCard.value),
        ccToken = Some("invalid-token")
      )

      val whiteLabel      = WhiteLabel.Agoda
      val bookingFlow     = BookingFlow.SingleProperty
      val isAgencyBooking = false
      val customerEmail   = Some(null)

      when(mockCreditCardInfo.binRangeInfo).thenReturn(Option(CardBinRangeInfo(CardClass.Credit)))
      when(mockCreditCardInfo.countryInfo).thenReturn(Option(CountryInfo(106, "Thailand", "", "", "")))

      getSetupBookingFacade
        .isBankNameRequired(
          Some(paymentRequest),
          Some(mockCreditCardInfo),
          whiteLabel,
          bookingFlow,
          isAgencyBooking,
          correlationId,
          customerEmail
        )(requestContext)
        .map(result => assert(result))
    }

  }

  "CardPaymentRequestParams" should {
    "map credit card currency if credit card currency supported" in {
      val creditCardInfo = Some(
        CreditCardInfo(
          Some(CurrencyInfo(1, "EUR", 2)),
          Some(CountryInfo(1, "United Kingdom", "United Kingdom", "GB", "GBP")),
          None
        )
      )
      val chargeCurrency = "THB"
      val result =
        getSetupBookingFacade.getCardPaymentRequestParams(
          creditCardInfo,
          Some(SelectedPaymentMethod(1, Set("USD", "OMR", "EUR", "THB"), "USD", false)),
          Some(ChargeOption.PayNow.id),
          chargeCurrency
        )
      val expected = CardPaymentRequestParameter("THB", "EUR", Some(ChargeOption.PayNow.id), Some(1))
      result shouldBe Some(expected)
    }

    "map payment methods default currency if credit card currency is not supported" in {
      val creditCardInfo = Some(
        CreditCardInfo(
          Some(CurrencyInfo(58, "IDR", 2)),
          Some(CountryInfo(58, "Indonasia", "Indonasia", "ID", "IDR")),
          None
        )
      )
      val chargeCurrency = "THB"
      val result =
        getSetupBookingFacade.getCardPaymentRequestParams(
          creditCardInfo,
          Some(SelectedPaymentMethod(1, Set("USD", "OMR", "EUR", "THB"), "USD", false)),
          Some(ChargeOption.PayNow.id),
          chargeCurrency
        )
      val expected = CardPaymentRequestParameter("THB", "USD", Some(ChargeOption.PayNow.id), Some(58))
      result shouldBe Some(expected)
    }

    "Redirect payment: return CardPaymentRequestParameter with charge currency and default currency" in {
      val redirectSelectedPaymentMethod = mock[SelectedPaymentMethod]
      when(redirectSelectedPaymentMethod.redirect).thenReturn(true)
      when(redirectSelectedPaymentMethod.defaultCurrency).thenReturn("KRW")
      val chargeCurrency = "INR"
      val result =
        getSetupBookingFacade.getCardPaymentRequestParams(
          None,
          Some(redirectSelectedPaymentMethod),
          Some(ChargeOption.PayNow.id),
          chargeCurrency
        )
      val expected = CardPaymentRequestParameter("INR", "KRW", Some(ChargeOption.PayNow.id), None)
      result shouldBe Some(expected)
    }
  }

  "get payment charge option" should {
    "return empty list for agency booking" in {
      val service = getSetupBookingFacade
      val actual =
        service.getPaymentChargeOptionWithFallBack(Seq("THB", "USD"), productData = SingleAgencyFlightProductData)
      actual shouldBe Seq.empty
    }

    "return with only currency code if product data doesn't contains payment charge options for FXI" in {
      val service = getSetupBookingFacade
      val actual =
        service.getPaymentChargeOptionWithFallBack(Seq("THB", "USD"), productData = merchantPropertyProductItems(2000))
      val expected = Seq(PaymentChargeOption("THB"), PaymentChargeOption("USD"))
      actual shouldBe expected
    }

    "should return payment charge options from product data when present" in {
      val service               = getSetupBookingFacade
      val expectedChargeOptions = Seq(PaymentChargeOption("USD", 2.0, 3.0, 5.0))
      val productDataWithChargeOptions =
        merchantPropertyProductItems(2000).copy(paymentChargeOptions = expectedChargeOptions)
      val actual =
        service.getPaymentChargeOptionWithFallBack(Seq("THB", "USD"), productData = productDataWithChargeOptions)
      actual shouldBe expectedChargeOptions
    }

    "return payment charge options from product data for agency booking with Japanican whitelabel" in {
      val service                                    = getSetupBookingFacade
      implicit val setupContext: SetupBookingContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)
      when(setupContext.requestContext.whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Japanican)
      when(setupContext.requestContext.whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsJapanicanWl))
        .thenReturn(true)
      val expectedChargeOptions = Seq(PaymentChargeOption("USD", 2.0, 3.0, 5.0))
      val agencyProductData     = propertyProductItems(1000L, "").copy(paymentChargeOptions = expectedChargeOptions)
      val actual =
        service.getPaymentChargeOptionWithFallBack(Seq("THB", "USD"), productData = agencyProductData)
      actual shouldBe expectedChargeOptions
    }
  }

  "get charge currency options" should {
    "return empty for agency booking" in {
      val service              = getSetupBookingFacade
      val agencyProductData    = propertyProductItems(1000L, "")
      val paymentChargeOptions = Seq(PaymentChargeOption("USD", 100, 50, 150))
      val actual               = service.getChargeCurrencyOptions(Seq("USD"), paymentChargeOptions, agencyProductData)
      actual shouldBe Seq.empty
    }

    "return charge currency options with payToAgodaAmount mapped from paymentChargeOptions" in {
      val service              = getSetupBookingFacade
      val productData          = merchantPropertyProductItems(1000L)
      val paymentChargeOptions = Seq(PaymentChargeOption("USD", 100, 50, 150))
      val actual               = service.getChargeCurrencyOptions(Seq("USD"), paymentChargeOptions, productData)
      actual shouldBe Seq(CurrencyOption(50, "USD"))
    }

    "use Pay Now when it is cross sell request" in {
      val service = getSetupBookingFacade
      val productsRequest = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "1",
            propertySearchCriteria = PropertySearchCriteria(
              propertyId = Some(1),
              roomIdentifier = "roomIdentifier",
              occupancyRequest = com.agoda.bapi.common.message.OccupancyRequest(1, 0, 1),
              durationRequest = com.agoda.bapi.common.message.DurationRequest(LocalDate.now(), 1),
              pricingRequest = Some(
                PricingRequest(
                  isMse = false,
                  requiredPrice = "",
                  requiredBasis = "",
                  isRPM2Included = false,
                  selectedPointMaxId = None,
                  isIncludeUsdAndLocalCurrency = false,
                  allowOverrideOccupancy = false,
                  enableOpaqueChannel = false,
                  isAllowRoomTypeNotGuarantee = false,
                  synchronous = true,
                  partnerLoyaltyProgramId = Some(0)
                )
              ),
              papiContextRequest =
                Some(PapiContextRequest(true, trafficTypeId = Some(1), rawBotProfile = "", showCms = false)),
              roomSelectionRequest = None,
              propertyRequest = None,
              simplifiedRoomSelectionRequest = Some(
                SimplifiedRoomSelectionRequest(
                  roomIdentifier = "roomIdentifier",
                  requestedRoomNumbers = None,
                  requestExtraBedForRoomNumbers = None,
                  alternativeOptIn = Some(
                    AlternativeRoomOptIn(
                      None,
                      crossSellOptIn = Some(CrossSellOptIn(Some(CrossSellReasonType.fraud.id)))
                    )
                  )
                )
              ),
              partnerRequest = None
            ),
            payment = Some(ProductPaymentRequest(ChargeOption.PayLater))
          )
        )
      )
      val response = service.setupBooking(setupBookingRequest.copy(productsRequest = productsRequest))
      response.map { r =>
        verify(productService, times(1)).createProductToken(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          selectedChargeOption = ArgumentMatchers.eq(Some(ChargeOption.PayNow)),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          addOnData = any(),
          any()
        )(any())
        r.success shouldBe true
      }
    }

    "return expected currencies with respect to the whitelabel feature" in {
      val service                                    = getSetupBookingFacade
      implicit val setupContext: SetupBookingContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)

      val mockBookingChargeCurrencyFeature = mock[BookingChargeCurrency]

      when(setupContext.requestContext.whiteLabelInfo.feature.bookingChargeCurrency)
        .thenReturn(mockBookingChargeCurrencyFeature)

      when(mockBookingChargeCurrencyFeature.acceptedCurrencies)
        .thenReturn(Option(List("JPY")))

      when(mockBookingChargeCurrencyFeature.enabledForAgency)
        .thenReturn(Option(false))

      val productData = merchantPropertyProductItems(1000L)
      val paymentChargeOptions =
        Seq(PaymentChargeOption("USD", 100, 50, 150), PaymentChargeOption("JPY", 10000, 5000, 15000))
      val actual = service.getChargeCurrencyOptions(Seq("USD", "JPY"), paymentChargeOptions, productData)
      actual shouldBe Seq(CurrencyOption(5000, "JPY"))
    }

    "return expected currency for agency booking when enabled by the whitelabel feature" in {
      val service                                    = getSetupBookingFacade
      implicit val setupContext: SetupBookingContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)
      val mockBookingChargeCurrencyFeature           = mock[BookingChargeCurrency]

      when(setupContext.requestContext.whiteLabelInfo.feature.bookingChargeCurrency)
        .thenReturn(mockBookingChargeCurrencyFeature)

      when(mockBookingChargeCurrencyFeature.acceptedCurrencies)
        .thenReturn(Option(List("JPY")))

      when(mockBookingChargeCurrencyFeature.enabledForAgency)
        .thenReturn(Option(true))

      val agencyProductData = propertyProductItems(1000L, "")
      val paymentChargeOptions =
        Seq(PaymentChargeOption("USD", 100, 50, 150), PaymentChargeOption("JPY", 10000, 5000, 15000))
      val actual = service.getChargeCurrencyOptions(Seq("USD", "JPY"), paymentChargeOptions, agencyProductData)

      actual shouldBe Seq(CurrencyOption(5000, "JPY"))
    }
  }

  "isCreditCardCurrencyOffered" should {

    "call isCurrencyOffered in ebeLiteBookingRepository" in {
      val service = getSetupBookingFacade
      when(mockEbeRepository.isCurrencyOffered("MAD")).thenReturn(Future.successful(true))
      val creditCardInfo = CreditCardInfo(Some(CurrencyInfo(1, "MAD", 2)), None, None)
      service.isCreditCardCurrencyOffered(Some(creditCardInfo)) map { result =>
        verify(mockEbeRepository).isCurrencyOffered("MAD")
        result shouldBe true
      }
    }

    "return false if credit card currency is None" in {
      getSetupBookingFacade.isCreditCardCurrencyOffered(None) map { result =>
        verify(mockEbeRepository, never()).isCurrencyOffered("MAD")
        result shouldBe false
      }
    }
  }

  "SetupBookingFacade shouldCreateProductToken" should {
    val packageProduct = PackageProductData
    "not create Product Token when price is not confirmed " in {
      val priceNotConfirmedProduct = packageProduct.copy(priceConfirmed = false)
      val shouldCreateToken = getSetupBookingFacade.shouldCreateProductToken(
        priceNotConfirmedProduct,
        BookingFlow.Package
      )
      assert(!shouldCreateToken)
    }
    "not create Product Token when price is confirmed, hasFlight but no childRoom" in {
      val noChildRoomProduct =
        packageProduct.modify(_.properties.each.papiProperties.each.property).setTo(Seq(propertyNoMasterRoom))
      val shouldCreateToken =
        getSetupBookingFacade.shouldCreateProductToken(noChildRoomProduct, BookingFlow.Package)
      assert(!shouldCreateToken)
    }
  }

  "SetupBookingFacade For Price Change Polling should" should {
    val mockPropertyCriteria = mock[PropertySearchCriteria]
    when(mockPropertyCriteria.roomIdentifier).thenReturn("room-uid")
    when(mockPropertyCriteria.propertyId).thenReturn(Some(1L))
    when(mockPropertyCriteria.occupancyRequest).thenReturn(
      OccupancyRequest(1, 1)
    )
    when(mockPropertyCriteria.durationRequest).thenReturn(
      DurationRequest(new LocalDate("2025-11-18"), 2)
    )
    when(mockPropertyCriteria.pricingRequest).thenReturn(None)
    when(mockPropertyCriteria.papiContextRequest).thenReturn(None)
    when(mockPropertyCriteria.simplifiedRoomSelectionRequest).thenReturn(None)
    when(mockPropertyCriteria.isABSSync).thenReturn(None)
    when(mockPropertyCriteria.priceAdjustmentId).thenReturn(None)
    when(childRoom.priceAdjustmentId).thenReturn(None)
    val mockPropertyRequest = mock[PropertyRequestItem]
    when(mockPropertyRequest.propertySearchCriteria).thenReturn(mockPropertyCriteria)
    when(mockPropertyRequest.payment).thenReturn(None)
    when(mockPropertyRequest.id).thenReturn("1")

    "return ProductNotReady if price in bookingToken is same as papiResponse and precheck call is skipped" in {
      val token = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenString)
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = Seq(mockPropertyRequest))
      )
      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ).thenReturn(Future.successful(SinglePropertyProductData))
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(featureAware.isPriceChangePollingEnabled).thenReturn(true)
      service.setupBooking(updatedRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]
          verify(precheckRepository, times(0)).getAllotmentForBooking(any[AllotmentRequest], any())(any(), any())
          response.success shouldBe true
          response.bookingResponse.nonEmpty shouldBe true
          val bookingResponse = response.bookingResponse.head
          bookingResponse.serverStatus shouldBe InitializeBookingServerStatus(InitializeBookingStatus.ProductNotReady)
        }
      }
    }

    "Do not return productnoready if feature flag not found and precheck is called" in {
      val token = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenString)
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = Seq(mockPropertyRequest))
      )
      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ).thenReturn(Future.successful(SinglePropertyProductData))
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(precheckRepository.getAllotmentForBooking(any[AllotmentRequest], any())(any(), any())).thenReturn(
        Future.successful(
          PrecheckResponse(
            resultInfo = ResultInfo(
              status = Success,
              response = RequestProcessing,
              remark = "Request has been received and processing"
            ),
            supplierResponseInfo = None
          )
        )
      )
      service.setupBooking(updatedRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]
          verify(precheckRepository, times(1)).getAllotmentForBooking(any[AllotmentRequest], any())(any(), any())
          response.success shouldBe true
          response.bookingResponse.nonEmpty shouldBe true
          val bookingResponse = response.bookingResponse.head
          bookingResponse.serverStatus shouldBe InitializeBookingServerStatus(InitializeBookingStatus.Ok)
        }
      }
    }

    "Do not return productNotReady if all price are different between req and papi resp and precheck is called" in {
      val token = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringTwo)
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = Seq(mockPropertyRequest))
      )
      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ).thenReturn(Future.successful(SinglePropertyProductData))
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(featureAware.isPriceChangePollingEnabled).thenReturn(true)
      when(precheckRepository.getAllotmentForBooking(any[AllotmentRequest], any())(any(), any())).thenReturn(
        Future.successful(
          PrecheckResponse(
            resultInfo = ResultInfo(
              status = Success,
              response = RequestProcessing,
              remark = "Request has been received and processing"
            ),
            supplierResponseInfo = None
          )
        )
      )
      service.setupBooking(updatedRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]
          verify(precheckRepository, times(1)).getAllotmentForBooking(any[AllotmentRequest], any())(any(), any())
          response.success shouldBe true
          response.bookingResponse.nonEmpty shouldBe true
          val bookingResponse = response.bookingResponse.head
          bookingResponse.serverStatus shouldBe InitializeBookingServerStatus(InitializeBookingStatus.Ok)
        }
      }
    }

    "Do not return product not ready if price are different and different payment type between req/resp and precheck is called" in {
      val mockPayment = mock[EnrichedPayment]
      val mockCC      = mock[EnrichedNoCreditCard]
      when(mockCC.isEligible).thenReturn(false)
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Agency)
      when(mockPayment.noCreditCard).thenReturn(mockCC)
      when(childRoom.payment).thenReturn(Some(mockPayment))
      val token = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringTwo)
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = Seq(mockPropertyRequest))
      )
      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ).thenReturn(Future.successful(SinglePropertyProductData))
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(featureAware.isPriceChangePollingEnabled).thenReturn(true)
      when(precheckRepository.getAllotmentForBooking(any[AllotmentRequest], any())(any(), any())).thenReturn(
        Future.successful(
          PrecheckResponse(
            resultInfo = ResultInfo(
              status = Success,
              response = RequestProcessing,
              remark = "Request has been received and processing"
            ),
            supplierResponseInfo = None
          )
        )
      )
      service.setupBooking(updatedRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]
          verify(precheckRepository, times(1)).getAllotmentForBooking(any[AllotmentRequest], any())(any(), any())
          response.success shouldBe true
          response.bookingResponse.nonEmpty shouldBe true
          val bookingResponse = response.bookingResponse.head
          bookingResponse.serverStatus shouldBe InitializeBookingServerStatus(InitializeBookingStatus.Ok)
        }
      }
    }

    "Precheck is called price is diffweent between token(paynow) and papi response(paylater) and server status is ok" in {
      val mockPayment  = mock[EnrichedPayment]
      val mockCC       = mock[EnrichedNoCreditCard]
      val mockPayLater = mock[EnrichedPayLater]
      when(mockPayLater.isEligible).thenReturn(true)
      when(mockPayLater.authDate).thenReturn(DateTime.now())
      when(mockPayLater.chargeDate).thenReturn(DateTime.now())
      when(mockCC.isEligible).thenReturn(false)
      when(mockPayment.paymentModel).thenReturn(models.pricing.enums.PaymentModels.Merchant)
      when(mockPayment.payLater).thenReturn(mockPayLater)
      when(mockPayment.noCreditCard).thenReturn(mockCC)
      when(childRoom.payment).thenReturn(Some(mockPayment))
      val token = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringTwo)
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = Seq(mockPropertyRequest))
      )
      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])
      val updatedBookingProperties =
        SinglePropertyProductData.properties.map(_.copy(selectedChargeOption = Some(ChargeOption.PayLater)))
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ).thenReturn(Future.successful(SinglePropertyProductData.copy(properties = updatedBookingProperties)))
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(featureAware.isPriceChangePollingEnabled).thenReturn(true)
      when(precheckRepository.getAllotmentForBooking(any[AllotmentRequest], any())(any(), any())).thenReturn(
        Future.successful(
          PrecheckResponse(
            resultInfo = ResultInfo(
              status = Success,
              response = RequestProcessing,
              remark = "Request has been received and processing"
            ),
            supplierResponseInfo = None
          )
        )
      )
      service.setupBooking(updatedRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]
          verify(precheckRepository, times(1)).getAllotmentForBooking(any[AllotmentRequest], any())(any(), any())
          response.success shouldBe true
          response.bookingResponse.nonEmpty shouldBe true
          val bookingResponse = response.bookingResponse.head
          bookingResponse.serverStatus shouldBe InitializeBookingServerStatus(InitializeBookingStatus.Ok)
        }
      }
    }
  }

  "SetupBookingFace for RoomSwap Precheck" should {
    val propertyWithAlternativeRoom = createMockProperty(
      propertyId = 1L,
      masterRooms = Seq(createMockMasterRoom(typeId = 3L, name = None, imageUrl = "", childRooms = List(childRoom))),
      roomSwapping = List(AlternativeRoom("room-uid", "alternative-uid", SwapRoomTypes.AlternativeRoom))
    )
    when(propertyWithAlternativeRoom.isOnlyAlternativeRoom) thenReturn Some(true)
    val singlePropertyWithAlternativeRoomProductData = ProductData(
      properties = Seq(
        BookingPropertiesData(
          id = "1",
          content = "1",
          papiProperties = Some(
            transformers.Properties(
              Seq(propertyWithAlternativeRoom),
              None
            )
          ),
          packageRequest = None,
          papiPropertyStatus = PapiPropertyStatus.Ok,
          selectedChargeOption = None
        )
      ),
      flights = Seq.empty,
      cars = Seq.empty,
      protections = Seq.empty,
      activities = Seq.empty,
      totalPriceDisplay = None,
      priceDisplayType = None,
      packageToken = None,
      priceChange = None,
      priceConfirmed = true
    )

    val mockPropertyCriteria = mock[PropertySearchCriteria]
    when(mockPropertyCriteria.roomIdentifier).thenReturn("room-uid")
    when(mockPropertyCriteria.propertyId).thenReturn(Some(1L))
    when(mockPropertyCriteria.occupancyRequest).thenReturn(
      OccupancyRequest(1, 1)
    )
    when(mockPropertyCriteria.durationRequest).thenReturn(
      DurationRequest(new LocalDate("2025-11-18"), 2)
    )
    when(mockPropertyCriteria.pricingRequest).thenReturn(None)
    when(mockPropertyCriteria.papiContextRequest).thenReturn(None)
    when(mockPropertyCriteria.simplifiedRoomSelectionRequest).thenReturn(None)
    when(mockPropertyCriteria.isABSSync).thenReturn(None)
    val mockPropertyRequest = mock[PropertyRequestItem]
    when(mockPropertyRequest.propertySearchCriteria).thenReturn(mockPropertyCriteria)
    when(mockPropertyRequest.payment).thenReturn(None)
    when(mockPropertyRequest.id).thenReturn("1")
    "return ProductItemNotFound and call PAPI again when selected room got ABS PriceChanged/AllotmentNotAvailable" in {
      val token = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenString)
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = Seq(mockPropertyRequest))
      )
      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])
      when(
        productService.composeProductData(
          any(),
          any(),
          any(),
          any(),
          any(),
          mockitoEq(false),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
      )
        .thenReturn(Future.successful(SinglePropertyProductData))

      when(
        productService.composeProductData(
          any(),
          any(),
          any(),
          any(),
          any(),
          mockitoEq(true),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
      )
        .thenReturn(Future.successful(singlePropertyWithAlternativeRoomProductData))

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("room-uid")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Failed,
                response = AbsResponse.AllotmentPriceChangedIncrease,
                remark = "Allotment price has increated"
              ),
              supplierResponseInfo = None
            )
          )
        )
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(featureAware.isRoomSwapEnabled).thenReturn(true)
      service.setupBooking(updatedRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]
          verify(precheckRepository, times(1))
            .getAllotmentForBooking(any[AllotmentRequest], mockitoEq(true))(mockitoEq(context), any[String])
          verify(productService, times(1)).composeProductData(
            mockitoEq(updatedRequest),
            mockitoEq("THB"),
            mockitoEq(0.0),
            mockitoEq(None),
            mockitoEq(Some(CardPaymentRequestParameter("THB", "USD", None, Some(1)))),
            mockitoEq(false),
            any(),
            any(),
            any(),
            any(),
            any()
          )(
            mockitoEq(setupContext)
          )
          verify(productService, times(1)).composeProductData(
            mockitoEq(updatedRequest),
            mockitoEq("THB"),
            mockitoEq(0.0),
            mockitoEq(None),
            mockitoEq(Some(CardPaymentRequestParameter("THB", "USD", None, Some(1)))),
            mockitoEq(true),
            any(),
            any(),
            any(),
            any(),
            any()
          )(mockitoEq(setupContext))
          response.success shouldBe true
          response.bookingResponse.nonEmpty shouldBe true
          val bookingResponse = response.bookingResponse.head
          bookingResponse.serverStatus shouldBe new InitializeBookingServerStatus(
            InitializeBookingStatus.ProductItemNotFound,
            InitializeBookingStatusCategory.InvalidProductItem,
            Some(
              "Contains property with alternative room: Property Id:1 papiResponseRoomIdentifier:room-uid requestRoomIdentifier:room-uid"
            )
          )
        }
      }
    }
  }

  "isForceAlternativeRoomRequired" should {
    "not enable ForceAlternativeRoom if not SingleProperty flow" in {
      val service = getSetupBookingFacade
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleFlight)
      val requestContext = mock[RequestContext]
      when(setupContext.requestContext).thenReturn(requestContext)
      val featureAware = mock[FeatureAware]
      when(requestContext.featureAware).thenReturn(Some(featureAware))
      when(featureAware.isRoomSwapEnabled).thenReturn(true)
      when(setupContext.getIsRoomHasSwapped(any())).thenReturn(Some(false))
      val precheckResult = Seq(
        AllotmentPreCheckStatus(
          "productKey1",
          None,
          None,
          AllotmentStatus.NotAvailable
        )
      )
      val mockPropertySetupBookingToken = mock[PropertySetupBookingToken]
      when(setupContext.session)
        .thenReturn(
          SetupBookingSessionContext(
            Map("key" -> mockPropertySetupBookingToken),
            None,
            None
          )
        )
      when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))

      service.isForceAlternativeRoomRequired(setupContext, precheckResult) shouldEqual false
    }

    "not enable ForceAlternativeRoom if RoomSwap is not enabled" in {
      val service = getSetupBookingFacade
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      val requestContext = mock[RequestContext]
      when(setupContext.requestContext).thenReturn(requestContext)
      val featureAware = mock[FeatureAware]
      when(requestContext.featureAware).thenReturn(Some(featureAware))
      when(featureAware.isRoomSwapEnabled).thenReturn(false)
      val precheckResult = Seq(
        AllotmentPreCheckStatus(
          "productKey1",
          None,
          None,
          AllotmentStatus.NotAvailable
        )
      )
      val mockPropertySetupBookingToken = mock[PropertySetupBookingToken]
      when(setupContext.session)
        .thenReturn(
          SetupBookingSessionContext(
            Map("key" -> mockPropertySetupBookingToken),
            None,
            None
          )
        )
      when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))

      service.isForceAlternativeRoomRequired(setupContext, precheckResult) shouldEqual false
    }

    "not enable ForceAlternativeRoom if precheck result is Available" in {
      val service = getSetupBookingFacade
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      val requestContext = mock[RequestContext]
      when(setupContext.requestContext).thenReturn(requestContext)
      val featureAware = mock[FeatureAware]
      when(requestContext.featureAware).thenReturn(Some(featureAware))
      when(featureAware.isRoomSwapEnabled).thenReturn(true)
      val precheckResult = Seq(
        AllotmentPreCheckStatus(
          "productKey1",
          None,
          None,
          AllotmentStatus.Available
        )
      )
      val mockPropertySetupBookingToken = mock[PropertySetupBookingToken]
      when(setupContext.session)
        .thenReturn(
          SetupBookingSessionContext(
            Map("key" -> mockPropertySetupBookingToken),
            None,
            None
          )
        )
      when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))

      service.isForceAlternativeRoomRequired(setupContext, precheckResult) shouldEqual false
    }

    "not enable ForceAlternativeRoom if room has been swapped before" in {
      val service = getSetupBookingFacade
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      val requestContext = mock[RequestContext]
      when(setupContext.requestContext).thenReturn(requestContext)
      val featureAware = mock[FeatureAware]
      when(requestContext.featureAware).thenReturn(Some(featureAware))
      when(featureAware.isRoomSwapEnabled).thenReturn(true)
      val precheckResult = Seq(
        AllotmentPreCheckStatus(
          "productKey1",
          None,
          None,
          AllotmentStatus.NotAvailable
        )
      )
      val mockPropertySetupBookingToken = mock[PropertySetupBookingToken]
      when(setupContext.session)
        .thenReturn(
          SetupBookingSessionContext(
            Map("key" -> mockPropertySetupBookingToken),
            None,
            None
          )
        )
      when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(true))

      service.isForceAlternativeRoomRequired(setupContext, precheckResult) shouldEqual false
    }

    "enable ForceAlternativeRoom if SingleProperty flow, roomswap is enabled, precheck failed and room has NOT been swapped before" in {
      val service = getSetupBookingFacade
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      val requestContext = mock[RequestContext]
      when(setupContext.requestContext).thenReturn(requestContext)
      val featureAware = mock[FeatureAware]
      when(requestContext.featureAware).thenReturn(Some(featureAware))
      when(featureAware.isRoomSwapEnabled).thenReturn(true)
      val precheckResult = Seq(
        AllotmentPreCheckStatus(
          "productKey1",
          None,
          None,
          AllotmentStatus.NotAvailable
        )
      )
      val mockPropertySetupBookingToken = mock[PropertySetupBookingToken]
      when(setupContext.session)
        .thenReturn(
          SetupBookingSessionContext(
            Map("key" -> mockPropertySetupBookingToken),
            None,
            None
          )
        )
      when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))

      service.isForceAlternativeRoomRequired(setupContext, precheckResult) shouldEqual true
    }
  }
  "getPaymentLimitationCMSIds" should {
    "return CMS Ids if payment limitation is defined" in {
      val errorCMSId            = 11111
      val alternatePaymentCMSId = 22222
      val paymentLimitationInfo: Option[PaymentLimitationInfo] = Some(
        paymentLimitationInfoTemplate.copy(
          siteId = 55555,
          errorCMSId = errorCMSId,
          alternatePaymentCMSId = Some(alternatePaymentCMSId)
        )
      )

      val expected = Seq(errorCMSId, alternatePaymentCMSId)
      val res      = getSetupBookingFacade.getPaymentLimitationCMSIds(paymentLimitationInfo)
      res shouldBe expected
    }

    "do not return CMS Ids if payment limitation is not defined" in {
      val paymentLimitationInfo: Option[PaymentLimitationInfo] = None

      val res = getSetupBookingFacade.getPaymentLimitationCMSIds(paymentLimitationInfo)
      res shouldBe Seq.empty
    }
  }

  "SetupBookingFacade For PreCheck on Alternatives" should {
    val mockPropertyCriteria = mock[PropertySearchCriteria]
    when(mockPropertyCriteria.roomIdentifier).thenReturn("roomiden-original")
    when(mockPropertyCriteria.propertyId).thenReturn(Some(1L))
    when(mockPropertyCriteria.occupancyRequest).thenReturn(
      OccupancyRequest(1, 1)
    )
    when(mockPropertyCriteria.durationRequest).thenReturn(
      DurationRequest(new LocalDate("2025-11-18"), 2)
    )
    when(mockPropertyCriteria.pricingRequest).thenReturn(None)
    when(mockPropertyCriteria.papiContextRequest).thenReturn(None)
    when(mockPropertyCriteria.isFeatureOn(PropertyFeatureFlags.BreakfastUpSell)).thenReturn(true)
    when(mockPropertyCriteria.isABSSync).thenReturn(None)
    when(mockPropertyCriteria.priceAdjustmentId).thenReturn(None)
    when(childRoom.priceAdjustmentId).thenReturn(None)

    val mockPropertyRequest = mock[PropertyRequestItem]
    when(mockPropertyRequest.propertySearchCriteria).thenReturn(mockPropertyCriteria)
    when(mockPropertyRequest.payment).thenReturn(None)
    when(mockPropertyRequest.id).thenReturn("1")

    // Mock product data
    val displayPriceOriginal  = DisplayPrice(5, 6)
    val displayPriceBreakfast = DisplayPrice(15, 16)
    val displayPriceCrossSell = DisplayPrice(25, 26)
    val mockPricingOriginal   = mock[EnrichedPricing]
    when(mockPricingOriginal.display).thenReturn(
      DisplayBasis(displayPriceOriginal, displayPriceOriginal, displayPriceOriginal)
    )
    val mockPricingBreakfast = mock[EnrichedPricing]
    when(mockPricingBreakfast.display).thenReturn(
      DisplayBasis(displayPriceBreakfast, displayPriceBreakfast, displayPriceBreakfast)
    )
    val mockPricingCrossSell = mock[EnrichedPricing]
    when(mockPricingCrossSell.display).thenReturn(
      DisplayBasis(displayPriceCrossSell, displayPriceCrossSell, displayPriceCrossSell)
    )
    val childRoomOriginal = defaultChildRoom()
    when(childRoomOriginal.uid).thenReturn(Some("uid-original"))
    when(childRoomOriginal.roomIdentifiers).thenReturn(Some("roomiden-original"))
    when(childRoomOriginal.loyaltyResponse).thenReturn(None)
    when(childRoomOriginal.cashbackRedemptionBoundaries).thenReturn(None)
    when(childRoomOriginal.pricing).thenReturn(
      Map("USD" -> mockPricingBreakfast)
    )
    when(childRoomOriginal.campaignPromotions).thenReturn(None)
    when(childRoomOriginal.priceAdjustmentId).thenReturn(None)

    val childRoomBreakfast = defaultChildRoom()
    when(childRoomBreakfast.uid).thenReturn(Some("uid-breakfastupsell"))
    when(childRoomBreakfast.roomIdentifiers).thenReturn(Some("roomiden-breakfastupsell"))
    when(childRoomBreakfast.loyaltyResponse).thenReturn(None)
    when(childRoomBreakfast.cashbackRedemptionBoundaries).thenReturn(None)
    when(childRoomBreakfast.pricing).thenReturn(
      Map("USD" -> mockPricingBreakfast)
    )
    when(childRoomBreakfast.campaignPromotions).thenReturn(None)
    when(childRoomBreakfast.priceAdjustmentId).thenReturn(None)

    val childRoomCrossSell = defaultChildRoom()
    when(childRoomCrossSell.uid).thenReturn(Some("uid-crosssell"))
    when(childRoomCrossSell.roomIdentifiers).thenReturn(Some("roomiden-crosssell"))
    when(childRoomCrossSell.loyaltyResponse).thenReturn(None)
    when(childRoomCrossSell.cashbackRedemptionBoundaries).thenReturn(None)
    when(childRoomCrossSell.pricing).thenReturn(
      Map("USD" -> mockPricingCrossSell)
    )
    when(childRoomCrossSell.campaignPromotions).thenReturn(None)
    when(childRoomCrossSell.priceAdjustmentId).thenReturn(None)

    val baseRoomSwappingList = List(
      AlternativeRoom("roomiden-original", "roomiden-breakfastupsell", SwapRoomTypes.BreakfastUpSell),
      AlternativeRoom("roomiden-original", "roomiden-crosssell", SwapRoomTypes.NormalCrossSell)
    )

    val propertyWithAlternativeRooms =
      createMockProperty(
        propertyId = 1L,
        masterRooms = Seq(
          createMockMasterRoom(
            typeId = 3L,
            name = None,
            childRooms = List(childRoomOriginal, childRoomBreakfast, childRoomCrossSell)
          )
        ),
        roomSwapping = baseRoomSwappingList,
        getAllRooms = Seq(childRoomOriginal, childRoomBreakfast, childRoomCrossSell)
      )

    val productDataPropertyWtihAlternativeRooms = SinglePropertyProductData.copy(
      properties = Seq(
        BookingPropertiesData(
          id = "1",
          content = "1",
          papiProperties = Some(
            transformers.Properties(
              Seq(propertyWithAlternativeRooms),
              None
            )
          ),
          packageRequest = None,
          papiPropertyStatus = PapiPropertyStatus.Ok,
          selectedChargeOption = None,
          propertySearchCriteria = Some(mockPropertyCriteria)
        )
      )
    )

    // Mock breakfast upsell room that got price updated
    val displayPriceBreakfastUpdated = displayPriceBreakfast.copy(
      exclusive = displayPriceBreakfast.exclusive + 0.5,
      allInclusive = displayPriceBreakfast.allInclusive + 0.4
    )

    val mockPricingBreakfastUpdated = mock[EnrichedPricing]
    when(mockPricingBreakfastUpdated.display).thenReturn(
      DisplayBasis(displayPriceBreakfastUpdated, displayPriceBreakfastUpdated, displayPriceBreakfastUpdated)
    )

    val childRoomBreakfastPriceUpdated = defaultChildRoom()
    when(childRoomBreakfastPriceUpdated.uid).thenReturn(Some("uid-breakfastupsell"))
    when(childRoomBreakfastPriceUpdated.roomIdentifiers).thenReturn(Some("roomiden-breakfastupsell"))
    when(childRoomBreakfastPriceUpdated.loyaltyResponse).thenReturn(None)
    when(childRoomBreakfastPriceUpdated.cashbackRedemptionBoundaries).thenReturn(None)
    when(childRoomBreakfastPriceUpdated.pricing).thenReturn(
      Map("USD" -> mockPricingBreakfastUpdated)
    )
    when(childRoomBreakfastPriceUpdated.campaignPromotions).thenReturn(None)
    when(childRoomBreakfastPriceUpdated.priceAdjustmentId).thenReturn(None)

    val productDataPropertyWtihAlternativeRoomsPriceUpdated = productDataPropertyWtihAlternativeRooms.copy(
      properties = Seq(
        productDataPropertyWtihAlternativeRooms.properties.head.copy(
          papiProperties = Some(
            transformers.Properties(
              Seq(
                createMockProperty(
                  propertyId = 1L,
                  masterRooms = Seq(
                    createMockMasterRoom(
                      typeId = 3L,
                      name = None,
                      childRooms = List(childRoomOriginal, childRoomBreakfastPriceUpdated, childRoomCrossSell)
                    )
                  ),
                  roomSwapping = baseRoomSwappingList,
                  getAllRooms = Seq(childRoomOriginal, childRoomBreakfastPriceUpdated, childRoomCrossSell)
                )
              ),
              None
            )
          )
        )
      )
    )

    "return OK when AllotmentNotAvailable on NON-selected room" in {
      /* original - available - (selected) bf upsell - not available cross sell - not available return = Success */

      when(mockPropertyCriteria.simplifiedRoomSelectionRequest).thenReturn(
        Some(
          SimplifiedRoomSelectionRequest(
            roomIdentifier = "roomiden-original",
            alternativeOptIn = None // original room is selected
          )
        )
      )

      when(setupContext.session) thenReturn SetupBookingSessionContext(properties =
        Map("1" -> setupTokenWithNotAvailableOnAlternative)
      )

      val token = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringNotAvailableOnAlternative)
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = Seq(mockPropertyRequest))
      )

      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn (Future.successful(productDataPropertyWtihAlternativeRooms))
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(featureAware.isPreCheckOnAlternatives).thenReturn(true)

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-original")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Success,
                response = AbsResponse.AllotmentAvailable,
                remark = "Request has been received and processing"
              ),
              supplierResponseInfo = None
            )
          )
        )

      service.setupBooking(updatedRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]
          response.success shouldBe true
          response.bookingResponse.nonEmpty shouldBe true
          val bookingResponse = response.bookingResponse.head
          bookingResponse.serverStatus shouldBe InitializeBookingServerStatus(InitializeBookingStatus.Ok)
        }
      }
    }

    "return AllotmentNotAvailable when selected room allotment is not available" in {
      /* original - available bf upsell - not available (selected) cross sell - not available return =
       * AllotmentNotAvailable */

      when(mockPropertyCriteria.simplifiedRoomSelectionRequest).thenReturn(
        Some(
          SimplifiedRoomSelectionRequest(
            roomIdentifier = "roomiden-original",
            alternativeOptIn = Some(
              AlternativeRoomOptIn(swapRoomType = Some(SwapRoomTypes.BreakfastUpSell.i))
            ) // bf upsell room is selected
          )
        )
      )

      when(setupContext.session) thenReturn SetupBookingSessionContext(properties =
        Map("1" -> setupTokenWithNotAvailableOnAlternative)
      )

      val token = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringNotAvailableOnAlternative)
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = Seq(mockPropertyRequest))
      )

      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn (Future.successful(productDataPropertyWtihAlternativeRooms))
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(featureAware.isPreCheckOnAlternatives).thenReturn(true)

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-original")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Success,
                response = AbsResponse.AllotmentAvailable,
                remark = "Available"
              ),
              supplierResponseInfo = None
            )
          )
        )

      service.setupBooking(updatedRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]
          response.success shouldBe true
          response.bookingResponse.nonEmpty shouldBe true
          val bookingResponse = response.bookingResponse.head
          bookingResponse.serverStatus shouldBe InitializeBookingServerStatus.withDescription(
            InitializeBookingStatus.AllotmentNotAvailable,
            Some("Allotment Not Available for product: 1, room: roomiden-breakfastupsell")
          )
        }
      }
    }

    "return AllotmentNotAvailable when selected room is cross sell and allotment is not available" in {
      /* original - available bf upsell - not available cross sell - not available (selected) return =
       * AllotmentNotAvailable */
      when(mockPropertyCriteria.simplifiedRoomSelectionRequest).thenReturn(
        Some(
          SimplifiedRoomSelectionRequest(
            roomIdentifier = "roomiden-original",
            alternativeOptIn = Some(
              AlternativeRoomOptIn(crossSellOptIn = Some(CrossSellOptIn(Some(CrossSellReasonType.preAuth.id))))
            ) // cross sell room is selected
          )
        )
      )

      when(setupContext.session) thenReturn SetupBookingSessionContext(properties =
        Map("1" -> setupTokenWithNotAvailableOnAlternative)
      )

      val token = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringNotAvailableOnAlternative)
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = Seq(mockPropertyRequest))
      )

      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn (Future.successful(productDataPropertyWtihAlternativeRooms))
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(featureAware.isPreCheckOnAlternatives).thenReturn(true)

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-original")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Success,
                response = AbsResponse.AllotmentAvailable,
                remark = "Available"
              ),
              supplierResponseInfo = None
            )
          )
        )

      service.setupBooking(updatedRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]
          response.success shouldBe true
          response.bookingResponse.nonEmpty shouldBe true
          val bookingResponse = response.bookingResponse.head
          bookingResponse.serverStatus shouldBe InitializeBookingServerStatus.withDescription(
            InitializeBookingStatus.AllotmentNotAvailable,
            Some("Allotment Not Available for product: 1, room: roomiden-crosssell")
          )
        }
      }
    }

    "return AllotmentNotAvailable when selected room is original and is not available" in {
      /* original - not available (selected) bf upsell - available cross sell - available return = AllotmentNotAvailable */

      when(mockPropertyCriteria.simplifiedRoomSelectionRequest).thenReturn(
        Some(
          SimplifiedRoomSelectionRequest(
            roomIdentifier = "roomiden-original",
            alternativeOptIn = None // original room is selected
          )
        )
      )

      when(setupContext.session) thenReturn SetupBookingSessionContext(properties =
        Map("1" -> setupTokenAlternativeRoomsWithoutAllotResult)
      )

      val token = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringAlternativeWithoutAllotResult)
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = Seq(mockPropertyRequest))
      )

      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn (Future.successful(productDataPropertyWtihAlternativeRooms))
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(featureAware.isPreCheckOnAlternatives).thenReturn(true)

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-original")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Failed,
                response = AbsResponse.AllotmentNotAvailable,
                remark = "AllotmentNotAvailable"
              ),
              supplierResponseInfo = None
            )
          )
        )

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-breakfastupsell")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Success,
                response = AbsResponse.AllotmentAvailable,
                remark = "Available"
              ),
              supplierResponseInfo = None
            )
          )
        )

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-crosssell")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Success,
                response = AbsResponse.AllotmentAvailable,
                remark = "Available"
              ),
              supplierResponseInfo = None
            )
          )
        )

      service.setupBooking(updatedRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]
          response.success shouldBe true
          response.bookingResponse.nonEmpty shouldBe true
          val bookingResponse = response.bookingResponse.head
          bookingResponse.serverStatus shouldBe InitializeBookingServerStatus.withDescription(
            InitializeBookingStatus.AllotmentNotAvailable,
            Some("Allotment Not Available for product: 1, room: roomiden-original")
          )
        }
      }
    }

    // PreCheck on Alternatives + Price Change Polling
    "return OK + no call to pre-check on price changed room when NON-selected room got ABS PriceChanged + DF Price not yet updated" in {
      /* original - available (selected) bf upsell - price changed cross sell - available return = Success */
      when(mockPropertyCriteria.simplifiedRoomSelectionRequest).thenReturn(
        Some(
          SimplifiedRoomSelectionRequest(
            roomIdentifier = "roomiden-original" // original room is selected
          )
        )
      )

      when(setupContext.session) thenReturn SetupBookingSessionContext(properties =
        Map("1" -> setupTokenWithPriceChangedOnAlternative)
      )

      val token = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringPriceChangedOnAlternative)
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = Seq(mockPropertyRequest))
      )

      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn (Future.successful(productDataPropertyWtihAlternativeRooms))
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(featureAware.isPreCheckOnAlternatives).thenReturn(true)
      when(featureAware.isPriceChangePollingEnabled).thenReturn(true)

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-original")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Success,
                response = AbsResponse.AllotmentAvailable,
                remark = "Available"
              ),
              supplierResponseInfo = None
            )
          )
        )

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-crosssell")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Success,
                response = AbsResponse.AllotmentAvailable,
                remark = "Available"
              ),
              supplierResponseInfo = None
            )
          )
        )

      service.setupBooking(updatedRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]
          verify(precheckRepository, times(0)).getAllotmentForBooking(
            argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-breakfastupsell")),
            any()
          )(any(), any()) // No precheck call to price changed room since DF price not yet updated
          response.success shouldBe true
          response.bookingResponse.nonEmpty shouldBe true
          val bookingResponse = response.bookingResponse.head
          bookingResponse.serverStatus shouldBe InitializeBookingServerStatus(InitializeBookingStatus.Ok)
        }
      }
    }

    "return ProductNotReady + no call to pre-check for price changed room when selected room got ABS PriceChanged + DF Price is not yet updated" in {
      /* original - available bf upsell - price changed (selected) cross sell - available Note - DF price is not yet
       * updated return = ProductNotReady */
      when(mockPropertyCriteria.simplifiedRoomSelectionRequest).thenReturn(
        Some(
          SimplifiedRoomSelectionRequest(
            roomIdentifier = "roomiden-original",
            alternativeOptIn = Some(
              AlternativeRoomOptIn(swapRoomType = Some(SwapRoomTypes.BreakfastUpSell.i))
            ) // bf upsell room is selected
          )
        )
      )

      when(setupContext.session) thenReturn SetupBookingSessionContext(properties =
        Map("1" -> setupTokenWithPriceChangedOnAlternative)
      )

      val token = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringPriceChangedOnAlternative)
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = Seq(mockPropertyRequest))
      )

      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn (Future.successful(productDataPropertyWtihAlternativeRooms))
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(featureAware.isPreCheckOnAlternatives).thenReturn(true)
      when(featureAware.isPriceChangePollingEnabled).thenReturn(true)

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-original")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Success,
                response = AbsResponse.AllotmentAvailable,
                remark = "Available"
              ),
              supplierResponseInfo = None
            )
          )
        )

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-crosssell")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Success,
                response = AbsResponse.AllotmentAvailable,
                remark = "Available"
              ),
              supplierResponseInfo = None
            )
          )
        )

      service.setupBooking(updatedRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]
          verify(precheckRepository, times(0)).getAllotmentForBooking(
            argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-breakfastupsell")),
            any()
          )(any(), any()) // No precheck call to price changed room since DF price not yet updated
          response.success shouldBe true
          response.bookingResponse.nonEmpty shouldBe true
          val bookingResponse = response.bookingResponse.head
          bookingResponse.serverStatus shouldBe InitializeBookingServerStatus.withDescription(
            InitializeBookingStatus.ProductNotReady,
            Some("Allotment Price Changed for product: 1, room: roomiden-breakfastupsell")
          )
        }
      }
    }

    "return AllotmentNotAvailable when selected room got ABS PriceChanged but isCrossSell room" in {
      /* original - available bf upsell - available cross sell - price changed -> Not Available (selected) return =
       * Allotment Not Available */
      when(mockPropertyCriteria.simplifiedRoomSelectionRequest).thenReturn(
        Some(
          SimplifiedRoomSelectionRequest(
            roomIdentifier = "roomiden-original",
            alternativeOptIn =
              Some(AlternativeRoomOptIn(crossSellOptIn = Some(CrossSellOptIn(Some(CrossSellReasonType.preAuth.id)))))
          )
        )
      )

      when(setupContext.session) thenReturn SetupBookingSessionContext(properties =
        Map("1" -> setupTokenAlternativeRoomsWithoutAllotResult)
      )

      val token = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringAlternativeWithoutAllotResult)
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = Seq(mockPropertyRequest))
      )

      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn (Future.successful(productDataPropertyWtihAlternativeRooms))
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(featureAware.isPreCheckOnAlternatives).thenReturn(true)
      when(featureAware.isPriceChangePollingEnabled).thenReturn(true)

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-original")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Success,
                response = AbsResponse.AllotmentAvailable,
                remark = "Available"
              ),
              supplierResponseInfo = None
            )
          )
        )

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-breakfastupsell")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Success,
                response = AbsResponse.AllotmentAvailable,
                remark = "Available"
              ),
              supplierResponseInfo = None
            )
          )
        )

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-crosssell")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Failed,
                response = AbsResponse.AllotmentPriceChangedIncrease,
                remark = "AllotmentPriceChangedIncrease"
              ),
              supplierResponseInfo = None
            )
          )
        )

      service.setupBooking(updatedRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]
          response.success shouldBe true
          response.bookingResponse.nonEmpty shouldBe true
          val bookingResponse = response.bookingResponse.head
          bookingResponse.serverStatus shouldBe InitializeBookingServerStatus.withDescription(
            InitializeBookingStatus.AllotmentNotAvailable,
            Some("Allotment Not Available for product: 1, room: roomiden-crosssell")
          )
        }
      }
    }

    "return OK + call pre-check again when selected room got ABS PriceChanged + DF Price is already updated + Allotment now processing" in {
      /* original - available bf upsell - price changed (selected) cross sell - available Note - DF price is already
       * updated + next call to ABS now got processing return = Success */
      when(mockPropertyCriteria.simplifiedRoomSelectionRequest).thenReturn(
        Some(
          SimplifiedRoomSelectionRequest(
            roomIdentifier = "roomiden-original",
            alternativeOptIn = Some(
              AlternativeRoomOptIn(swapRoomType = Some(SwapRoomTypes.BreakfastUpSell.i))
            ) // bf upsell room is selected
          )
        )
      )

      when(setupContext.session) thenReturn SetupBookingSessionContext(properties =
        Map("1" -> setupTokenWithPriceChangedOnAlternative)
      )

      val token = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringPriceChangedOnAlternative)
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = Seq(mockPropertyRequest))
      )

      val service                                  = getSetupBookingFacade
      val messagingCaptor: ArgumentCaptor[Message] = ArgumentCaptor.forClass(classOf[Message])
      when(
        productService.composeProductData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(
          any()
        )
      ) thenReturn (Future.successful(productDataPropertyWtihAlternativeRoomsPriceUpdated))
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(featureAware.isPreCheckOnAlternatives).thenReturn(true)
      when(featureAware.isPriceChangePollingEnabled).thenReturn(true)

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-original")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Success,
                response = AbsResponse.AllotmentAvailable,
                remark = "Available"
              ),
              supplierResponseInfo = None
            )
          )
        )

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-breakfastupsell")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Success,
                response = AbsResponse.RequestProcessing,
                remark = "Request is now processing"
              ),
              supplierResponseInfo = None
            )
          )
        )

      when(
        precheckRepository.getAllotmentForBooking(
          argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-crosssell")),
          any()
        )(any(), any())
      )
        .thenReturn(
          Future.successful(
            PrecheckResponse(
              resultInfo = ResultInfo(
                status = AbsStatus.Success,
                response = AbsResponse.AllotmentAvailable,
                remark = "Available"
              ),
              supplierResponseInfo = None
            )
          )
        )

      service.setupBooking(updatedRequest).map { response =>
        {
          verify(messageService, times(2)).sendMessage(messagingCaptor.capture())
          messagingCaptor.getAllValues.get(1) shouldBe a[BapiSetupV3DebugLogMessage]
          messagingCaptor.getAllValues.get(0) shouldBe a[BcreEssInfoMessage]
          verify(precheckRepository, times(1)).getAllotmentForBooking(
            argThat(AllotmentRequestMatcherByRoomIdentifier("roomiden-breakfastupsell")),
            any()
          )(any(), any()) // 1 precheck call to price changed room since DF price has been updated
          response.success shouldBe true
          response.bookingResponse.nonEmpty shouldBe true
          val bookingResponse = response.bookingResponse.head
          bookingResponse.serverStatus shouldBe InitializeBookingServerStatus(InitializeBookingStatus.Ok)
        }
      }
    }

    "External Loyalty - Payment limitation" should {
      "getCreditCardInfoResponse - External Loyalty Flow error with CMSId" in {

        when(externalLoyaltyService.applyExternalLoyaltyPaymentLimitation(any(), any()))
          .thenReturn(ExternalLoyaltyPaymentLimitationError(Some(123), Some(245)))

        val service = getSetupBookingFacade
        val setupBookingRequestWithPaymentMethod = setupBookingRequest.copy(
          paymentRequest = Some(
            PaymentRequest(
              ccBin = None,
              ccId = None,
              selectedPaymentMethod = None,
              selectedChargeCurrency = None
            )
          )
        )
        val externalLoyaltyPaymentLimitation = Some(
          PaymentLimitations(
            partnerCode = "1",
            paymentMethodIds = Some(Seq(1, 2, 3)),
            paymentMethodNames = Some(Seq("Visa")),
            binRanges = Some(
              Seq(
                BinRange(
                  bookingStartDate = Some(java.time.LocalDate.parse("2000-01-01")),
                  bookingEndDate = None,
                  binList = Some(Seq(BinListRange(from = 111, to = 222)))
                )
              )
            ),
            errorCMSId = 1,
            alternatePaymentCMSId = Some(0)
          )
        )

        val response = service.getCreditCardInfoResponse(
          setupBookingRequest = setupBookingRequestWithPaymentMethod,
          productData = SinglePropertyProductData,
          creditCardInfo = None,
          memberInfo = None,
          paymentLimitation = None,
          externalLoyaltyPaymentLimitation = externalLoyaltyPaymentLimitation,
          cmsContext = None,
          availablePaymentMethodsResult = mockAvailablePaymentMethodsResult
        )
        response.map(result => result.flatMap(_.paymentLimitationResponse) shouldBe an[Some[PaymentLimitationResponse]])
      }
    }
  }

  "getPaymentCcToken" should {
    implicit val setupContext: SetupBookingContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)
    val featureAware                               = mock[FeatureAware]
    when(setupContext.requestContext.featureAware).thenReturn(Some(featureAware))

    "return ccToken" in {
      val setupBookingRequestWithCcToken = setupBookingRequest.copy(
        paymentRequest = Some(PaymentRequest(ccToken = Some("CreditCard Token Content")))
      )

      val result = getSetupBookingFacade.getPaymentCcToken(setupBookingRequestWithCcToken)
      result shouldBe Some("CreditCard Token Content")
    }

    "return ccToken with None" in {
      val setupBookingRequestWithCcToken = setupBookingRequest.copy(
        paymentRequest = Some(PaymentRequest(ccToken = None))
      )

      val result = getSetupBookingFacade.getPaymentCcToken(setupBookingRequestWithCcToken)
      result shouldBe None
    }
  }

  "injectBessiePromo" should {
    implicit val setupContext: SetupBookingContext = mock[SetupBookingContext](RETURNS_DEEP_STUBS)
    val requestContext                             = mock[RequestContext]
    when(setupContext.requestContext).thenReturn(requestContext)
    val featureAware = mock[FeatureAware]
    when(requestContext.featureAware).thenReturn(Some(featureAware))

    "inject Bessie promotion when it is the first single hotel call" in {
      val service = getSetupBookingFacade
      val productRequestWithProperty = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        )
      )
      val setupBookingRequestProperty = setupBookingRequest.copy(productsRequest = productRequestWithProperty)
      val result                      = service.injectBessiePromo(setupBookingRequestProperty, loyaltyProfile)
      result.campaignInfo shouldBe Some(CampaignInfoRequest(None, 1, "CITI4NF"))
    }

    "inject Onyx Travel Credit when it is the first single hotel call" in {
      val modifiedSubLoyaltyProgram = SubLoyaltyPrograms(
        pointsBalance = Some(10.0),
        campaigns = Some(Seq(citiOnyxBenefitCampaign))
      )
      val modifiedExternalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
        None,
        Seq(
          modifiedSubLoyaltyProgram
        )
      )
      val modifiedLoyaltyProfile = Some(
        LoyaltyProfile(
          None,
          Vector.empty[ObjectNode],
          None,
          None,
          None,
          Some(
            modifiedExternalLoyaltyUserProfileResponse
          )
        )
      )

      val service = getSetupBookingFacade
      val productRequestWithProperty = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        )
      )
      val setupBookingRequestProperty = setupBookingRequest.copy(productsRequest = productRequestWithProperty)
      val result                      = service.injectBessiePromo(setupBookingRequestProperty, modifiedLoyaltyProfile)
      result.campaignInfo shouldBe Some(CampaignInfoRequest(None, 1, "CITIONYX"))
    }

    "have no injection of promotion when it is the first multi hotel call" in {
      val service = getSetupBookingFacade
      val productRequestWithProperty = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          ),
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        )
      )
      val setupBookingRequestProperty = setupBookingRequest.copy(productsRequest = productRequestWithProperty)
      val result                      = service.injectBessiePromo(setupBookingRequestProperty, loyaltyProfile)
      result.campaignInfo shouldBe None
    }

    "have no injection of Onyx Travel Credit when it is the first multi hotel call" in {
      val modifiedSubLoyaltyProgram = SubLoyaltyPrograms(
        pointsBalance = Some(10.0),
        campaigns = Some(Seq(citiOnyxBenefitCampaign))
      )
      val modifiedExternalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
        None,
        Seq(
          modifiedSubLoyaltyProgram
        )
      )
      val modifiedLoyaltyProfile = Some(
        LoyaltyProfile(
          None,
          Vector.empty[ObjectNode],
          None,
          None,
          None,
          Some(
            modifiedExternalLoyaltyUserProfileResponse
          )
        )
      )
      val service = getSetupBookingFacade
      val productRequestWithProperty = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          ),
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        )
      )
      val setupBookingRequestProperty = setupBookingRequest.copy(productsRequest = productRequestWithProperty)
      val result                      = service.injectBessiePromo(setupBookingRequestProperty, modifiedLoyaltyProfile)
      result.campaignInfo shouldBe Some(CampaignInfoRequest(None, 1, "CITIONYX"))
    }

    "have no injection of promotion when it is not the first single hotel call" in {
      val service = getSetupBookingFacade
      val token   = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenString)
      val propertyRequests = Seq(
        PropertyRequestItem(
          id = "",
          propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
        )
      )
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = propertyRequests)
      )
      val result = service.injectBessiePromo(updatedRequest, loyaltyProfile)
      result.campaignInfo shouldBe None
    }

    "have no injection of Onyx Travel Credit when it is not the first single hotel call" in {
      val modifiedSubLoyaltyProgram = SubLoyaltyPrograms(
        pointsBalance = Some(10.0),
        campaigns = Some(Seq(citiOnyxBenefitCampaign))
      )
      val modifiedExternalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
        None,
        Seq(
          modifiedSubLoyaltyProgram
        )
      )
      val modifiedLoyaltyProfile = Some(
        LoyaltyProfile(
          None,
          Vector.empty[ObjectNode],
          None,
          None,
          None,
          Some(
            modifiedExternalLoyaltyUserProfileResponse
          )
        )
      )
      val service = getSetupBookingFacade
      val token   = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenString)
      val propertyRequests = Seq(
        PropertyRequestItem(
          id = "",
          propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
        )
      )
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = propertyRequests)
      )
      val result = service.injectBessiePromo(updatedRequest, modifiedLoyaltyProfile)
      result.campaignInfo shouldBe None
    }

    "have no injection of promotion when it is not the first multi hotel call" in {
      val service = getSetupBookingFacade
      val token   = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenString)
      val propertyRequests = Seq(
        PropertyRequestItem(
          id = "",
          propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
        ),
        PropertyRequestItem(
          id = "",
          propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
        )
      )
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = propertyRequests)
      )
      val result = service.injectBessiePromo(updatedRequest, loyaltyProfile)
      result.campaignInfo shouldBe None
    }

    "have no injection of Travel Credit when it is not the first multi hotel call" in {
      val modifiedSubLoyaltyProgram = SubLoyaltyPrograms(
        pointsBalance = Some(10.0),
        campaigns = Some(Seq(citiOnyxBenefitCampaign))
      )
      val modifiedExternalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
        None,
        Seq(
          modifiedSubLoyaltyProgram
        )
      )
      val modifiedLoyaltyProfile = Some(
        LoyaltyProfile(
          None,
          Vector.empty[ObjectNode],
          None,
          None,
          None,
          Some(
            modifiedExternalLoyaltyUserProfileResponse
          )
        )
      )
      val service = getSetupBookingFacade
      val token   = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenString)
      val propertyRequests = Seq(
        PropertyRequestItem(
          id = "",
          propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
        ),
        PropertyRequestItem(
          id = "",
          propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
        )
      )
      val updatedRequest = setupBookingRequest.copy(
        setupBookingRequest.productsRequest
          .copy(bookingToken = token.toOption, propertyRequests = propertyRequests)
      )
      val result = service.injectBessiePromo(updatedRequest, modifiedLoyaltyProfile)
      result.campaignInfo shouldBe None
    }

    "have no injection of promotion when promocode has expired" in {
      val modifiedCampaign = Campaign(
        "CITI4NF",
        DiscountType.Amount,
        100.0,
        Some("USD"),
        Some(1),
        Some(1),
        Some("2020-12-31T00:00:00Z"),
        Some("2022-12-31T00:00:00Z"),
        ProductType.HOTELS
      )
      val modifiedSubLoyaltyProgram = SubLoyaltyPrograms(
        pointsBalance = Some(10.0),
        campaigns = Some(Seq(modifiedCampaign))
      )
      val modifiedExternalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
        None,
        Seq(
          modifiedSubLoyaltyProgram
        )
      )
      val modifiedLoyaltyProfile = Some(
        LoyaltyProfile(
          None,
          Vector.empty[ObjectNode],
          None,
          None,
          None,
          Some(
            modifiedExternalLoyaltyUserProfileResponse
          )
        )
      )

      val service = getSetupBookingFacade
      val productRequestWithProperty = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        )
      )
      val setupBookingRequestProperty = setupBookingRequest.copy(productsRequest = productRequestWithProperty)
      val result                      = service.injectBessiePromo(setupBookingRequestProperty, modifiedLoyaltyProfile)
      result.campaignInfo shouldBe None
    }

    "have no injection of promotion when promocode has no more redemptionCounts left" in {
      val modifiedCampaign = Campaign(
        "CITI4NF",
        DiscountType.Amount,
        100.0,
        Some("USD"),
        Some(2),
        Some(0),
        Some("2099-12-31T00:00:00Z"),
        Some("2022-12-31T00:00:00Z"),
        ProductType.HOTELS
      )
      val modifiedSubLoyaltyProgram = SubLoyaltyPrograms(
        pointsBalance = Some(10.0),
        campaigns = Some(Seq(modifiedCampaign))
      )
      val modifiedExternalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
        None,
        Seq(
          modifiedSubLoyaltyProgram
        )
      )
      val modifiedLoyaltyProfile = Some(
        LoyaltyProfile(
          None,
          Vector.empty[ObjectNode],
          None,
          None,
          None,
          Some(
            modifiedExternalLoyaltyUserProfileResponse
          )
        )
      )
      val service = getSetupBookingFacade
      val productRequestWithProperty = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        )
      )
      val setupBookingRequestProperty = setupBookingRequest.copy(productsRequest = productRequestWithProperty)
      val result                      = service.injectBessiePromo(setupBookingRequestProperty, modifiedLoyaltyProfile)
      result.campaignInfo shouldBe None
    }

    "inject Bessie promotion for promoEndDate contain Date-only format" in {
      val modifiedCampaign = Campaign(
        "CITI4NF",
        DiscountType.Amount,
        100.0,
        Some("USD"),
        Some(1),
        Some(1),
        Some("2099-12-31"),
        Some("2022-12-31"),
        ProductType.HOTELS
      )
      val modifiedSubLoyaltyProgram = SubLoyaltyPrograms(
        pointsBalance = Some(10.0),
        campaigns = Some(Seq(modifiedCampaign))
      )
      val modifiedExternalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
        None,
        Seq(
          modifiedSubLoyaltyProgram
        )
      )
      val modifiedLoyaltyProfile = Some(
        LoyaltyProfile(
          None,
          Vector.empty[ObjectNode],
          None,
          None,
          None,
          Some(
            modifiedExternalLoyaltyUserProfileResponse
          )
        )
      )

      val service = getSetupBookingFacade
      val productRequestWithProperty = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        )
      )
      val setupBookingRequestProperty = setupBookingRequest.copy(productsRequest = productRequestWithProperty)
      val result                      = service.injectBessiePromo(setupBookingRequestProperty, modifiedLoyaltyProfile)
      result.campaignInfo shouldBe Some(CampaignInfoRequest(None, 1, "CITI4NF"))
    }

    "not inject Bessie promotion for promoEndDate contain Date-only format but the promoEndDate was expire" in {
      val modifiedCampaign = Campaign(
        "CITI4NF",
        DiscountType.Amount,
        100.0,
        Some("USD"),
        Some(1),
        Some(1),
        Some("2020-12-31"),
        Some("2022-12-31"),
        ProductType.HOTELS
      )
      val modifiedSubLoyaltyProgram = SubLoyaltyPrograms(
        pointsBalance = Some(10.0),
        campaigns = Some(Seq(modifiedCampaign))
      )
      val modifiedExternalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
        None,
        Seq(
          modifiedSubLoyaltyProgram
        )
      )
      val modifiedLoyaltyProfile = Some(
        LoyaltyProfile(
          None,
          Vector.empty[ObjectNode],
          None,
          None,
          None,
          Some(
            modifiedExternalLoyaltyUserProfileResponse
          )
        )
      )

      val service = getSetupBookingFacade
      val productRequestWithProperty = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        )
      )
      val setupBookingRequestProperty = setupBookingRequest.copy(productsRequest = productRequestWithProperty)
      val result                      = service.injectBessiePromo(setupBookingRequestProperty, modifiedLoyaltyProfile)
      result.campaignInfo shouldBe None
    }

    "inject Bessie promotion for promoEndDate contain Date-only format and the promoEndDate is today date " in {
      val currentDateString = JavaLocalDate.now().toString

      val modifiedCampaign = Campaign(
        "CITI4NF",
        DiscountType.Amount,
        100.0,
        Some("USD"),
        Some(1),
        Some(1),
        Some(currentDateString),
        Some("2022-12-31"),
        ProductType.HOTELS
      )
      val modifiedSubLoyaltyProgram = SubLoyaltyPrograms(
        pointsBalance = Some(10.0),
        campaigns = Some(Seq(modifiedCampaign))
      )
      val modifiedExternalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
        None,
        Seq(
          modifiedSubLoyaltyProgram
        )
      )
      val modifiedLoyaltyProfile = Some(
        LoyaltyProfile(
          None,
          Vector.empty[ObjectNode],
          None,
          None,
          None,
          Some(
            modifiedExternalLoyaltyUserProfileResponse
          )
        )
      )

      val service = getSetupBookingFacade
      val productRequestWithProperty = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        )
      )
      val setupBookingRequestProperty = setupBookingRequest.copy(productsRequest = productRequestWithProperty)
      val result                      = service.injectBessiePromo(setupBookingRequestProperty, modifiedLoyaltyProfile)
      result.campaignInfo shouldBe Some(CampaignInfoRequest(None, 1, "CITI4NF"))
    }

    "have no injection of Travel Credit when promocode has no benefit amount left" in {
      val modifiedSubLoyaltyProgram = SubLoyaltyPrograms(
        pointsBalance = Some(10.0),
        campaigns = Some(Seq(citiOnyxBenefitCampaign.copy(promoLeftAmount = Some(0.0))))
      )
      val modifiedExternalLoyaltyUserProfileResponse = ExternalLoyaltyUserProfileResponse(
        None,
        Seq(
          modifiedSubLoyaltyProgram
        )
      )
      val modifiedLoyaltyProfile = Some(
        LoyaltyProfile(
          None,
          Vector.empty[ObjectNode],
          None,
          None,
          None,
          Some(
            modifiedExternalLoyaltyUserProfileResponse
          )
        )
      )
      val service = getSetupBookingFacade
      val productRequestWithProperty = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        )
      )
      val setupBookingRequestProperty = setupBookingRequest.copy(productsRequest = productRequestWithProperty)
      val result                      = service.injectBessiePromo(setupBookingRequestProperty, modifiedLoyaltyProfile)
      result.campaignInfo shouldBe None
    }

    "have no injection of promotion when campaignInfo is exist" in {
      val service = getSetupBookingFacade
      val productRequestWithProperty = setupBookingRequest.productsRequest.copy(
        propertyRequests = Seq(
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          ),
          PropertyRequestItem(
            id = "",
            propertySearchCriteria = propertySearchCriteria.copy(roomIdentifier = "")
          )
        )
      )
      val mockCampaignInfo = CampaignInfoRequest(None, 1, "TESTPROMO")
      val setupBookingRequestProperty =
        setupBookingRequest.copy(productsRequest = productRequestWithProperty, campaignInfo = Some(mockCampaignInfo))
      val result = service.injectBessiePromo(setupBookingRequestProperty, loyaltyProfile)
      result.campaignInfo shouldBe Some(CampaignInfoRequest(None, 1, "TESTPROMO"))
    }
  }

  "updateFeeInPaymentMethods" should {
    "not map payment method fee if FLAPI returns NONE in CcOf Payment method" in {
      val productData = ProductData(
        properties = Seq.empty,
        flights = Seq(SingleFlightData),
        cars = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        totalPriceDisplay = None,
        priceDisplayType = None,
        packageToken = None,
        priceChange = None,
        priceConfirmed = true
      )

      val result = getSetupBookingFacade.updateFeeForCcofPaymentMethods(
        creditCardOnFiles = ccofList,
        finalProductData = productData
      )
      result shouldBe ccofList
    }

    "Map payment method fee if FLAPI returns method ids for Ccof Payment Method" in {
      val flightInfoWithFee = SingleFlightData.copy(chargeablePaymentMethods =
        Some(Seq(ChargeablePaymentMethod(paymentMethodId = 1), ChargeablePaymentMethod(paymentMethodId = 2)))
      ) // Visa & Master card chargeable , Amex is Free
      val productData = ProductData(
        properties = Seq.empty,
        flights = Seq(flightInfoWithFee),
        cars = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        totalPriceDisplay = None,
        priceDisplayType = None,
        packageToken = None,
        priceChange = None,
        priceConfirmed = true
      )

      val ccOnFilesWithFee = Seq(
        CreditCardOnFile(
          ccId = 123,
          lastFourDigits = "1234",
          cardTypeId = 1,
          icons = paymentIconType1,
          isPaymentMethodSupported = true,
          expiryDate = Some(CreditCardExpiration(2040, 1)),
          feeInfo = Some(FeeInfo(`type` = "CHARGEABLE"))
        ),
        CreditCardOnFile(
          ccId = 456,
          lastFourDigits = "4567",
          cardTypeId = 1,
          icons = paymentIconType1,
          isPaymentMethodSupported = true,
          expiryDate = Some(CreditCardExpiration(2041, 1)),
          feeInfo = Some(FeeInfo(`type` = "CHARGEABLE"))
        ),
        CreditCardOnFile(
          ccId = 789,
          lastFourDigits = "7890",
          cardTypeId = 2,
          icons = paymentIconType2,
          isPaymentMethodSupported = true,
          expiryDate = Some(CreditCardExpiration(2042, 1)),
          feeInfo = Some(FeeInfo(`type` = "CHARGEABLE"))
        )
      )

      val result = getSetupBookingFacade.updateFeeForCcofPaymentMethods(
        creditCardOnFiles = ccofList,
        finalProductData = productData
      )

      result shouldBe ccOnFilesWithFee
    }

    "Map all as Free if list is empty for Ccof Payment Methods" in {
      val flightInfoWithFee = SingleFlightData.copy(chargeablePaymentMethods = Some(Seq.empty))
      val productData = ProductData(
        properties = Seq.empty,
        flights = Seq(flightInfoWithFee),
        cars = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        totalPriceDisplay = None,
        priceDisplayType = None,
        packageToken = None,
        priceChange = None,
        priceConfirmed = true
      )

      val ccOnFilesWithFee = Seq(
        CreditCardOnFile(
          ccId = 123,
          lastFourDigits = "1234",
          cardTypeId = 1,
          icons = paymentIconType1,
          isPaymentMethodSupported = true,
          expiryDate = Some(CreditCardExpiration(2040, 1)),
          feeInfo = Some(FeeInfo(`type` = "FREE"))
        ),
        CreditCardOnFile(
          ccId = 456,
          lastFourDigits = "4567",
          cardTypeId = 1,
          icons = paymentIconType1,
          isPaymentMethodSupported = true,
          expiryDate = Some(CreditCardExpiration(2041, 1)),
          feeInfo = Some(FeeInfo(`type` = "FREE"))
        ),
        CreditCardOnFile(
          ccId = 789,
          lastFourDigits = "7890",
          cardTypeId = 2,
          icons = paymentIconType2,
          isPaymentMethodSupported = true,
          expiryDate = Some(CreditCardExpiration(2042, 1)),
          feeInfo = Some(FeeInfo(`type` = "FREE"))
        )
      )

      val result = getSetupBookingFacade.updateFeeForCcofPaymentMethods(
        creditCardOnFiles = ccofList,
        finalProductData = productData
      )

      result shouldBe ccOnFilesWithFee
    }

  }

  "UpdateAvailablePaymentMethods" should {

    "skip mapping payment methods when AFT-1356 is OFF" in {

      when(featureAware.isSendPaymentMethodAndChargesExperiment).thenReturn(false)

      val activityInfoWithFee = SingleActivityData.copy(paymentMethodAndCharges =
        Seq(
          ActivityPaymentMethodAndCharges(paymentMethodId = 1, paymentMethod = "", isChargeable = true),
          ActivityPaymentMethodAndCharges(paymentMethodId = 2, paymentMethod = "", isChargeable = true)
        )
      )

      val productData = ProductData(
        properties = Seq.empty,
        flights = Seq.empty,
        cars = Seq.empty,
        protections = Seq.empty,
        activities = Seq(activityInfoWithFee),
        totalPriceDisplay = None,
        packageToken = None,
        priceChange = None,
        priceConfirmed = true,
        priceDisplayType = None
      )

      val availablePaymentResult =
        AvailablePaymentMethodsResult(paymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)

      val result = getSetupBookingFacade.updateAvailablePaymentMethodsWithFeeForActivity(
        availablePaymentMethodsResult = availablePaymentResult,
        activityProductItems = productData.activities
      )
      result shouldBe availablePaymentResult
      result.paymentMethodDetailsList.find(_.id == 2).flatMap(_.feeInfo) shouldBe None
    }

    "Return correct updated payment methods when AFT-1356 is ON" in {

      when(featureAware.isSendPaymentMethodAndChargesExperiment).thenReturn(true)

      val activityInfoWithFee = SingleActivityData.copy(paymentMethodAndCharges =
        Seq(
          ActivityPaymentMethodAndCharges(
            paymentMethodId = 1,
            paymentMethod = "Visa",
            isChargeable = true,
            paymentCharge = Some(20),
            isRecommended = true
          ),
          ActivityPaymentMethodAndCharges(
            paymentMethodId = 2,
            paymentMethod = "MasterCard",
            paymentCharge = None,
            isRecommended = true
          ),
          ActivityPaymentMethodAndCharges(
            paymentMethodId = 3,
            paymentMethod = "JCB",
            isChargeable = true,
            paymentCharge = None
          ),
          ActivityPaymentMethodAndCharges(
            paymentMethodId = 4,
            paymentMethod = "AMEX",
            isChargeable = true,
            paymentCharge = Some(1)
          )
        )
      )

      val productData = ProductData(
        properties = Seq.empty,
        flights = Seq.empty,
        cars = Seq.empty,
        protections = Seq.empty,
        activities = Seq(activityInfoWithFee),
        totalPriceDisplay = None,
        packageToken = None,
        priceChange = None,
        priceConfirmed = true,
        priceDisplayType = None
      )

      val availablePaymentResult =
        AvailablePaymentMethodsResult(activityPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)

      val result = getSetupBookingFacade.updateAvailablePaymentMethodsWithFeeForActivity(
        availablePaymentMethodsResult = availablePaymentResult,
        activityProductItems = productData.activities
      )
      val mastercardResult = result.paymentMethodDetailsList.filter(_.id == 2)
      val visaResult       = result.paymentMethodDetailsList.filter(_.id == 1)
      val amex             = result.paymentMethodDetailsList.filter(_.id == 4)

      result.paymentMethodDetailsList.size shouldBe 3
      mastercardResult.map(_.isRecommended).headOption shouldBe Some(true)
      mastercardResult.map(_.feeInfo).headOption shouldBe Some(
        Some(FeeInfo(`type` = "FREE", isRecommended = true, paymentCharge = Some(0)))
      )

      visaResult.map(_.isRecommended).headOption shouldBe Some(false)
      visaResult.map(_.feeInfo).headOption shouldBe Some(
        Some(FeeInfo(`type` = "CHARGEABLE", isRecommended = false, paymentCharge = Some(20)))
      )

      amex.map(_.isRecommended).headOption shouldBe Some(false)
      amex.map(_.feeInfo).headOption shouldBe Some(
        Some(FeeInfo(`type` = "CHARGEABLE", isRecommended = false, paymentCharge = Some(1)))
      )
    }

    "Return correct updated payment methods when there is no recommended payment methods, it should select one with the CHEAPEST paymentCharge AFT-1356 is ON" in {

      when(featureAware.isSendPaymentMethodAndChargesExperiment).thenReturn(true)

      val activityInfoWithFee = SingleActivityData.copy(paymentMethodAndCharges =
        Seq(
          ActivityPaymentMethodAndCharges(
            paymentMethodId = 1,
            paymentMethod = "Visa",
            isChargeable = true,
            paymentCharge = Some(20)
          ),
          ActivityPaymentMethodAndCharges(
            paymentMethodId = 2,
            paymentMethod = "MasterCard",
            paymentCharge = None
          ),
          ActivityPaymentMethodAndCharges(
            paymentMethodId = 3,
            paymentMethod = "JCB",
            isChargeable = true,
            paymentCharge = None
          ),
          ActivityPaymentMethodAndCharges(
            paymentMethodId = 4,
            paymentMethod = "AMEX",
            isChargeable = true,
            paymentCharge = Some(1)
          )
        )
      )

      val productData = ProductData(
        properties = Seq.empty,
        flights = Seq.empty,
        cars = Seq.empty,
        protections = Seq.empty,
        activities = Seq(activityInfoWithFee),
        totalPriceDisplay = None,
        packageToken = None,
        priceChange = None,
        priceConfirmed = true,
        priceDisplayType = None
      )

      val availablePaymentResult =
        AvailablePaymentMethodsResult(activityPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)

      val result = getSetupBookingFacade.updateAvailablePaymentMethodsWithFeeForActivity(
        availablePaymentMethodsResult = availablePaymentResult,
        activityProductItems = productData.activities
      )
      val mastercardResult = result.paymentMethodDetailsList.filter(_.id == 2)
      val visaResult       = result.paymentMethodDetailsList.filter(_.id == 1)
      val amex             = result.paymentMethodDetailsList.filter(_.id == 4)

      result.paymentMethodDetailsList.size shouldBe 3
      mastercardResult.map(_.isRecommended).headOption shouldBe Some(true)
      mastercardResult.map(_.feeInfo).headOption shouldBe Some(
        Some(FeeInfo(`type` = "FREE", isRecommended = true, paymentCharge = Some(0)))
      )

      visaResult.map(_.isRecommended).headOption shouldBe Some(false)
      visaResult.map(_.feeInfo).headOption shouldBe Some(
        Some(FeeInfo(`type` = "CHARGEABLE", isRecommended = false, paymentCharge = Some(20)))
      )

      amex.map(_.isRecommended).headOption shouldBe Some(false)
      amex.map(_.feeInfo).headOption shouldBe Some(
        Some(FeeInfo(`type` = "CHARGEABLE", isRecommended = false, paymentCharge = Some(1)))
      )
    }

    "Return correct updated payment methods when there are 2 least payment charge and AFT-1356 is ON" in {

      when(featureAware.isSendPaymentMethodAndChargesExperiment).thenReturn(true)

      val activityInfoWithFee = SingleActivityData.copy(paymentMethodAndCharges =
        Seq(
          ActivityPaymentMethodAndCharges(
            paymentMethodId = 1,
            paymentMethod = "Visa",
            isChargeable = true,
            paymentCharge = Some(5)
          ),
          ActivityPaymentMethodAndCharges(
            paymentMethodId = 2,
            isChargeable = true,
            paymentMethod = "MasterCard",
            paymentCharge = Some(10)
          ),
          ActivityPaymentMethodAndCharges(
            paymentMethodId = 3,
            paymentMethod = "JCB",
            isChargeable = true,
            isRecommended = true,
            paymentCharge = Some(10)
          ),
          ActivityPaymentMethodAndCharges(
            paymentMethodId = 4,
            paymentMethod = "AMEX",
            isChargeable = true,
            paymentCharge = Some(10)
          )
        )
      )

      val productData = ProductData(
        properties = Seq.empty,
        flights = Seq.empty,
        cars = Seq.empty,
        protections = Seq.empty,
        activities = Seq(activityInfoWithFee),
        totalPriceDisplay = None,
        packageToken = None,
        priceChange = None,
        priceConfirmed = true,
        priceDisplayType = None
      )

      val availablePaymentResult =
        AvailablePaymentMethodsResult(activityPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)

      val result = getSetupBookingFacade.updateAvailablePaymentMethodsWithFeeForActivity(
        availablePaymentMethodsResult = availablePaymentResult,
        activityProductItems = productData.activities
      )
      val mastercardResult = result.paymentMethodDetailsList.filter(_.id == 2)
      val visaResult       = result.paymentMethodDetailsList.filter(_.id == 1)
      val amex             = result.paymentMethodDetailsList.filter(_.id == 4)

      result.paymentMethodDetailsList.size shouldBe 3
      mastercardResult.map(_.isRecommended).headOption shouldBe Some(false)
      mastercardResult.map(_.feeInfo).headOption shouldBe Some(
        Some(FeeInfo(`type` = "CHARGEABLE", isRecommended = false, paymentCharge = Some(10)))
      )

      visaResult.map(_.isRecommended).headOption shouldBe Some(true)
      visaResult.map(_.feeInfo).headOption shouldBe Some(
        Some(FeeInfo(`type` = "CHARGEABLE", isRecommended = true, paymentCharge = Some(5)))
      )

      amex.map(_.isRecommended).headOption shouldBe Some(false)
      amex.map(_.feeInfo).headOption shouldBe Some(
        Some(FeeInfo(`type` = "CHARGEABLE", isRecommended = false, paymentCharge = Some(10)))
      )
    }

    "skip mapping when product is not activity and AFT-1356 is ON" in {

      when(featureAware.isSendPaymentMethodAndChargesExperiment).thenReturn(true)

      val productData = ProductData(
        properties = Seq.empty,
        flights = Seq(SingleFlightData),
        cars = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        totalPriceDisplay = None,
        packageToken = None,
        priceChange = None,
        priceConfirmed = true,
        priceDisplayType = None
      )

      val availablePaymentResult =
        AvailablePaymentMethodsResult(activityPaymentMethodDetailsV2, SetupPaymentRequestV2(), Seq.empty, None)

      val result = getSetupBookingFacade.updateAvailablePaymentMethodsWithFeeForActivity(
        availablePaymentMethodsResult = availablePaymentResult,
        activityProductItems = productData.activities
      )
      val mastercardResult = result.paymentMethodDetailsList.filter(_.id == 2)
      val visaResult       = result.paymentMethodDetailsList.filter(_.id == 1)
      val amex             = result.paymentMethodDetailsList.filter(_.id == 4)

      result.paymentMethodDetailsList.size shouldBe 3
      mastercardResult.map(_.isRecommended).headOption shouldBe Some(false)
      mastercardResult.flatMap(_.feeInfo).headOption shouldBe None

      visaResult.map(_.isRecommended).headOption shouldBe Some(false)
      visaResult.flatMap(_.feeInfo).headOption shouldBe None

      amex.map(_.isRecommended).headOption shouldBe Some(false)
      amex.flatMap(_.feeInfo).headOption shouldBe None
    }

  }

  "getIsCampaignRequested" should {
    "return true when campaignInfo.promotionCode is non-empty" in {
      val request = setupBookingRequest.copy(
        campaignInfo = Some(CampaignInfoRequest(id = Some(1), cid = 1, promotionCode = "promotionCode"))
      )

      val result = getSetupBookingFacade.getIsCampaignRequested(request = request)(setupContext)

      result shouldBe true
    }

    "return false when campaignInfo.promotionCode is empty" in {
      val request = setupBookingRequest.copy(
        campaignInfo = Some(CampaignInfoRequest(id = Some(1), cid = 1, promotionCode = ""))
      )

      val result = getSetupBookingFacade.getIsCampaignRequested(request = request)(setupContext)

      result shouldBe false
    }
  }

  "getEssBookerOrigin" should {
    val mockMemberDetails = mock[MemberDetails]
    when(mockMemberDetails.nationalityId).thenReturn(198)
    val customerInfo = CustomerV2("I", "L", "", 2)
    val creditCardInfo =
      CreditCardInfo(
        Some(CurrencyInfo(1, "GBP", 2)),
        Some(CountryInfo(1, "United Kingdom", "United Kingdom", "GB", "GB")),
        None
      )

    "return essBookerOrigin correctly" in {
      val result = EssTaxCountryResult(Some("AB"), None)
      when(
        mockFinanceCalculator.determineCountryOfBooker(BookingCountryIndicator(Some("AB"), Some("GB"), Some("TH")))
      ) thenReturn result

      for {
        bookingCountryIndicatorOpt <- getSetupBookingFacade.getBookingCountryIndicator(
                                        customerInfo = Some(customerInfo),
                                        creditCardInfo = Some(creditCardInfo)
                                      )
        essBookerOrigin <-
          bookingCountryIndicatorOpt.map(getSetupBookingFacade.getEssBookerOrigin).getOrElse(Future.successful(None))
      } yield {
        verify(mockFinanceCalculator, times(1)).determineCountryOfBooker(
          BookingCountryIndicator(Some("AB"), Some("GB"), Some("TH"))
        )
        essBookerOrigin shouldBe Some(result)
      }
    }

    "call finance calculator to determine EssTaxCountryResult and log message correctly" in {
      val mockMemberDetails  = mock[MemberDetails]
      val mockCreditCardInfo = mock[CreditCardInfo]
      val mockCountryInfo    = mock[CountryInfo]
      val expectedEssBookerOriginResult = EssTaxCountryResult(
        countryCode = Some("BE"),
        errorMessage = None
      )
      when(mockCreditCardInfo.countryInfo).thenReturn(Some(mockCountryInfo))
      when(mockFinanceCalculator.determineCountryOfBooker(any())).thenReturn(expectedEssBookerOriginResult)
      when(mockCountryInfo.countryId).thenReturn(118)
      when(mockCountryInfo.countryISO2).thenReturn("BE")

      val resultF = for {
        bookingCountryIndicatorOpt <- getSetupBookingFacade.getBookingCountryIndicator(
                                        customerInfo = Some(customerInfo),
                                        creditCardInfo = Some(creditCardInfo)
                                      )
        essBookerOrigin <-
          bookingCountryIndicatorOpt.map(getSetupBookingFacade.getEssBookerOrigin).getOrElse(Future.successful(None))
      } yield essBookerOrigin

      resultF.map { result =>
        {
          verify(messageService).sendMessage(any[BcreEssInfoMessage])
          result shouldBe Some(expectedEssBookerOriginResult)
        }
      }
    }
  }

  "getCustomerRiskStatus" should {

    def setupMocks(
        bookingFlow: BookingFlow.Value = BookingFlow.SingleProperty,
        isTPRMCheckFeatureEnabled: Boolean = true
    ): Unit = {
      when(setupContext.bookingFlowType).thenReturn(bookingFlow)
      when(wlInfo.isFeatureEnabled(WhiteLabelFeatureName.TPRMCheck)).thenReturn(isTPRMCheckFeatureEnabled)
      when(setupContext.requestContext).thenReturn(
        requestContext.copy(
          whiteLabelInfo = wlInfo,
          featureAware = Some(featureAware)
        )
      )

      when(tprmRepository.checkSuspicious(any())(any()))
        .thenReturn(Future.successful(CustomerRiskStatus.AskMoreInfo))
      ()
    }

    "return CustomerRiskStatus correctly" in {
      setupMocks()
      val request = setupBookingRequest.copy(customerInfo = Some(CustomerV2("I", "L", "", 0)))
      for {
        result <- getSetupBookingFacade.getCustomerRiskStatus(request)
      } yield {
        verify(tprmRepository, times(1)).checkSuspicious(any())(any())
        result shouldBe CustomerRiskStatus.AskMoreInfo
      }
    }

    "return CustomerRiskStatus.Safe when flow is not Single Property" in {
      setupMocks(bookingFlow = BookingFlow.SingleFlight)
      val request = setupBookingRequest.copy(customerInfo = Some(CustomerV2("I", "L", "", 0)))
      for {
        result <- getSetupBookingFacade.getCustomerRiskStatus(request)
      } yield {
        verify(tprmRepository, times(0)).checkSuspicious(any())(any())
        result shouldBe CustomerRiskStatus.Safe
      }
    }

    "return CustomerRiskStatus.Unknown when TPRM Check feature is disabled" in {
      setupMocks(isTPRMCheckFeatureEnabled = false)
      val request = setupBookingRequest.copy(customerInfo = Some(CustomerV2("I", "L", "", 0)))
      for {
        result <- getSetupBookingFacade.getCustomerRiskStatus(request)
      } yield {
        verify(tprmRepository, times(0)).checkSuspicious(any())(any())
        result shouldBe CustomerRiskStatus.Unknown
      }
    }
  }

  "GetInstallmentData" should {
    "get called without capi-token, partner-claim-token and whitelabel-id if enableCapiSessionTokenSanitization is A" in {
      when(featureAware.enableCapiSessionTokenSanitization).thenReturn(false)
      val service = getSetupBookingFacade
      service.setupBooking(setupBookingRequest).map { response =>
        {
          verify(bookingsService, times(1)).getInstallmentData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            mockitoEq(null),
            mockitoEq(null)
          )(any())
          response.success shouldBe true
        }
      }
    }

    "get called with capi-token, partner-claim-token and whitelabel-id if enableCapiSessionTokenSanitization is B" in {
      when(featureAware.enableCapiSessionTokenSanitization).thenReturn(true)
      val service = getSetupBookingFacade
      service.setupBooking(setupBookingRequest).map { response =>
        {
          verify(bookingsService, times(1)).getInstallmentData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            mockitoEq(None),
            mockitoEq(None)
          )(any())
          response.success shouldBe true
        }
      }
    }
  }

  def mockBookingProperty = {
    val properties = mock[BookingPropertiesData]
    when(properties.getLoyaltyPaymentBoundaries()(any())).thenReturn(None)
    when(properties.getCashbackRedemptionPaymentBoundaries()(any())).thenReturn(None)
    val papiProperties = mock[transformers.Properties]
    val papiProperty =
      createMockPropertyWithRoomUid(1, Some("room-uid"), Some(roomIdentifier))
    val dfMetaResult = DFMetaResult(
      propertyToken = Some(ResponseStateToken(isNew = false, token = ""))
    )
    when(papiProperties.property) thenReturn Seq(papiProperty)
    when(papiProperties.dfMetaResult) thenReturn dfMetaResult
    when(properties.id) thenReturn "1"
    when(properties.papiProperties) thenReturn Some(papiProperties)
    when(properties.getChildRoomToBook()).thenReturn(Some(BookingMockHelper.baseChildrenRoom))
    when(properties.getPropertyToBook).thenReturn(Some(papiProperty))
    when(properties.toPropertyProductItem(any())) thenReturn mock[PropertyProductItem]
    when(properties.childRooms) thenReturn Seq(BookingMockHelper.baseChildrenRoom)
    when(properties.propertySearchCriteria).thenReturn(Some(propertySearchCriteria))
    val childRoomBookingOpt = getFirstChildRoomBookingData(papiProperty)
    when(properties.getChildRoomBookingData).thenReturn(childRoomBookingOpt)
    properties
  }
}

case class AllotmentRequestMatcherByRoomIdentifier(roomIden: String) extends ArgumentMatcher[AllotmentRequest] {
  def matches(argument: AllotmentRequest): Boolean =
    argument match {
      case a: AllotmentRequest if a.roomIdentifier == roomIden => true
      case _                                                   => false
    }
}
