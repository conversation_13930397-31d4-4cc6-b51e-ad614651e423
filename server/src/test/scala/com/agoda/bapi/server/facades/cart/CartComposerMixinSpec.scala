package com.agoda.bapi.server.facades.cart

import com.agoda.bapi.common.message.pricebreakdown.{PriceBreakdownNode, PriceBreakdownResponse, PriceBreakdownType, PriceDisplayType}
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.model.CurrencyCode
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName.SingleFlightPriceBDInCartBooking
import com.agoda.bapi.common.token.Money
import com.agoda.bapi.server.facades.helpers.SetupBookingContextFixture
import com.agoda.bapi.server.model.FlightConfirmationData
import com.agoda.bapi.server.repository.FlightsRepository
import com.agoda.bapi.server.service.pricebreakdown.PriceBreakdownService
import com.agoda.upi.models.cart.Cart
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{times, verify, when}
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.TableDrivenPropertyChecks
import org.scalatest.wordspec.AnyWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.{ExecutionContext, Future}

class CartComposerMixinSpec
    extends AnyWordSpec
    with MockitoSugar
    with SetupBookingContextFixture
    with Matchers
    with TableDrivenPropertyChecks
    with CartComposerMixin {
  override val flightsRepository: FlightsRepository         = mock[FlightsRepository]
  override val priceBreakdownService: PriceBreakdownService = mock[PriceBreakdownService]
  private val priceChangeForFlight                          = PriceChange(Money(125, "USD"), Money(150, "USD"), Money(25, "USD"))
  private val priceChangeForCartFromParam                   = PriceChange(Money(125, "USD"), Money(150, "USD"), Money(25, "USD"))
  private val priceChangeForCartFromToken                   = PriceChange(Money(125, "USD"), Money(150, "USD"), Money(25, "USD"))
  override def getPriceChangeForFlight(flight: FlightConfirmationData, currency: CurrencyCode): Option[PriceChange] =
    Some(priceChangeForFlight)

  override def getPriceChangeForCartFromParam(
      cart: Option[Cart],
      previousTotalPrice: Option[Money]
  ): Option[PriceChange] =
    Some(priceChangeForCartFromParam)

  override def getPriceChangeForCartFromToken(cart: Option[Cart], currency: CurrencyCode): Option[PriceChange] =
    Some(priceChangeForCartFromToken)

  override def totalSavingsForFlight(flight: FlightConfirmationData, currency: CurrencyCode): Option[TotalSavings] =
    Some(TotalSavings(Money(25, "USD")))

  val singleFlightRequest: ProductsRequest = ProductsRequest(
    flightRequests = Seq(mock[FlightRequestItem])
  )
  val singleFlightRequestCartPricingContext: ProductsRequest = ProductsRequest(
    flightRequests = Seq(mock[FlightRequestItem]),
    cartPricingContext = Some(mock[CartPricingContext])
  )

  "getPriceChange" should {
    val singleFlightRequestCartToken: ProductsRequest = ProductsRequest(
      flightRequests = Seq(mock[FlightRequestItem]),
      cartPricingContext = Some(CartPricingContext(token = Some(""), previousTotalPrice = None))
    )

    val singleFlightRequestWithPropertyCartPricingContext: ProductsRequest = ProductsRequest(
      flightRequests = Seq(mock[FlightRequestItem]),
      propertyRequests = Seq(mock[PropertyRequestItem]),
      cartPricingContext = Some(mock[CartPricingContext])
    )
    val singleFlightRequestWithPropertyCartToken: ProductsRequest = ProductsRequest(
      flightRequests = Seq(mock[FlightRequestItem]),
      propertyRequests = Seq(mock[PropertyRequestItem]),
      cartPricingContext = Some(CartPricingContext(token = Some(""), previousTotalPrice = None))
    )

    val multiFlightRequest: ProductsRequest = ProductsRequest(
      flightRequests = Seq(mock[FlightRequestItem], mock[FlightRequestItem])
    )

    val multiFlightRequestWithPropertyCartPricingContext: ProductsRequest = ProductsRequest(
      flightRequests = Seq(mock[FlightRequestItem], mock[FlightRequestItem]),
      propertyRequests = Seq(mock[PropertyRequestItem]),
      cartPricingContext = Some(mock[CartPricingContext])
    )
    val multiFlightRequestWithPropertyCartToken: ProductsRequest = ProductsRequest(
      flightRequests = Seq(mock[FlightRequestItem], mock[FlightRequestItem]),
      propertyRequests = Seq(mock[PropertyRequestItem]),
      cartPricingContext = Some(CartPricingContext(token = Some(""), previousTotalPrice = None))
    )

    val testCases = Table(
      ("description", "productRequest", "migrateFlightToCartFlow", "expected"),
      ("defaultProductsRequest", ProductsRequest(), true, Some(priceChangeForCartFromParam)),
      ("singleFlightRequest", singleFlightRequest, true, Some(priceChangeForFlight)),
      ("singleFlightRequest", singleFlightRequest, false, Some(priceChangeForCartFromParam)),
      (
        "singleFlightRequestCartPricingContext",
        singleFlightRequestCartPricingContext,
        true,
        Some(priceChangeForCartFromParam)
      ),
      ("singleFlightRequestCartToken", singleFlightRequestCartToken, true, Some(priceChangeForCartFromParam)),
      (
        "singleFlightRequestCartPricingContext",
        singleFlightRequestCartPricingContext,
        false,
        Some(priceChangeForCartFromParam)
      ),
      ("singleFlightRequestCartToken", singleFlightRequestCartToken, false, Some(priceChangeForCartFromToken)),
      (
        "singleFlightRequestWithPropertyCartPricingContext",
        singleFlightRequestWithPropertyCartPricingContext,
        true,
        Some(priceChangeForCartFromParam)
      ),
      (
        "singleFlightRequestWithPropertyCartToken",
        singleFlightRequestWithPropertyCartToken,
        true,
        Some(priceChangeForCartFromToken)
      ),
      ("multiFlightRequest", multiFlightRequest, true, Some(priceChangeForCartFromParam)),
      (
        "multiFlightRequestWithPropertyCartPricingContext",
        multiFlightRequestWithPropertyCartPricingContext,
        true,
        Some(priceChangeForCartFromParam)
      ),
      (
        "multiFlightRequestWithPropertyCartToken",
        multiFlightRequestWithPropertyCartToken,
        true,
        Some(priceChangeForCartFromToken)
      )
    )

    forAll(testCases) { (description, productsRequest, experimentEnabled, expected) =>
      s"$description with UNIBF-946 $experimentEnabled returns $expected" in {
        when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(experimentEnabled)
        getPriceChange(
          SetupBookingRequest(productsRequest),
          None, // UNDERLYING MOCKED
          Seq(mock[FlightConfirmationData]),
          "USD"
        ) shouldBe expected
      }
    }
  }

  "cartPriceBreakdown" should {
    val mockFlightPriceBreakdown: Option[PriceBreakdownNode] = Some(
      PriceBreakdownNode(
        value = Some(
          PriceBreakdownResponse(
            `type` = PriceBreakdownType.TotalPrice,
            amount = Money(300, "USD")
          )
        )
      )
    )

    val mockCartPriceBreakdown: Option[PriceBreakdownNode] = Some(
      PriceBreakdownNode(
        value = Some(
          PriceBreakdownResponse(
            `type` = PriceBreakdownType.PriceSummary,
            amount = Money(1500, "USD")
          )
        )
      )
    )
    when(
      priceBreakdownService.getPriceBreakdownForFlight(
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any()
      )
    ).thenReturn(Future.successful(mockFlightPriceBreakdown))
    when(
      priceBreakdownService.getPriceBreakdownForCart(
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any()
      )(any())
    ).thenReturn(Future.successful(mockCartPriceBreakdown))
    implicit val ec: ExecutionContext = ExecutionContext.global

    s"Should show cart price breakdown when cart pricing context is non empty and UNIBF-946 is disabled" in {
      val flightConfirmationData = mock[FlightConfirmationData]
      when(flightConfirmationData.cart).thenReturn(None)
      when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(false)
      val resultF = cartPriceBreakdown(
        SetupBookingRequest(
          ProductsRequest(
            flightRequests = Seq(mock[FlightRequestItem]),
            cartPricingContext = Some(mock[CartPricingContext])
          )
        ),
        Seq.empty, // UNDERLYING MOCKED
        Map(1 -> flightConfirmationData),
        currencyCode = "USD",
        isFlightCheckInEnabled = true,
        isInclusivePaySupplier = false,
        fixDoubleTaxesAndFeesPaySupplier = false
      )(setupBookingContext)
      resultF.map { result =>
        verify(priceBreakdownService, times(1)).getPriceBreakdownForCart(
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
        verify(priceBreakdownService, times(0)).getPriceBreakdownForFlight(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )
        result shouldBe mockCartPriceBreakdown
      }
    }
    s"Should show cart price breakdown when cart pricing context is non empty and UNIBF-946 is enabled" in {
      val flightConfirmationData = mock[FlightConfirmationData]
      when(flightConfirmationData.cart).thenReturn(None)
      when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(true)
      val resultF = cartPriceBreakdown(
        SetupBookingRequest(
          ProductsRequest(
            flightRequests = Seq(mock[FlightRequestItem]),
            cartPricingContext = Some(mock[CartPricingContext])
          )
        ),
        Seq.empty, // UNDERLYING MOCKED
        Map(1 -> flightConfirmationData),
        currencyCode = "USD",
        isFlightCheckInEnabled = true,
        isInclusivePaySupplier = false,
        fixDoubleTaxesAndFeesPaySupplier = false
      )(setupBookingContext)
      resultF.map { result =>
        verify(priceBreakdownService, times(1)).getPriceBreakdownForCart(
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
        verify(priceBreakdownService, times(0)).getPriceBreakdownForFlight(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )
        result shouldBe mockCartPriceBreakdown
      }
    }

    s"Should show cart price breakdown when cart pricing context is empty and itinerary has non flights products and UNIBF-946 is disabled" in {
      val flightConfirmationData = mock[FlightConfirmationData]
      when(flightConfirmationData.cart).thenReturn(None)
      when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(false)
      val resultF = cartPriceBreakdown(
        SetupBookingRequest(
          ProductsRequest(
            flightRequests = Seq(mock[FlightRequestItem]),
            propertyRequests = Seq(mock[PropertyRequestItem]),
            cartPricingContext = None
          )
        ),
        Seq.empty, // UNDERLYING MOCKED
        Map(1 -> flightConfirmationData),
        currencyCode = "USD",
        isFlightCheckInEnabled = true,
        isInclusivePaySupplier = false,
        fixDoubleTaxesAndFeesPaySupplier = false
      )(setupBookingContext)
      resultF.map { result =>
        verify(priceBreakdownService, times(1)).getPriceBreakdownForCart(
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
        verify(priceBreakdownService, times(0)).getPriceBreakdownForFlight(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )
        result shouldBe mockCartPriceBreakdown
      }
    }

    s"Should show cart price breakdown when cart pricing context is empty and itinerary has non flights products and UNIBF-946 is enabled" in {
      val flightConfirmationData = mock[FlightConfirmationData]
      when(flightConfirmationData.cart).thenReturn(None)
      when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(true)
      val resultF = cartPriceBreakdown(
        SetupBookingRequest(
          ProductsRequest(
            flightRequests = Seq(mock[FlightRequestItem]),
            propertyRequests = Seq(mock[PropertyRequestItem]),
            cartPricingContext = None
          )
        ),
        Seq.empty, // UNDERLYING MOCKED
        Map(1 -> flightConfirmationData),
        currencyCode = "USD",
        isFlightCheckInEnabled = true,
        isInclusivePaySupplier = false,
        fixDoubleTaxesAndFeesPaySupplier = false
      )(setupBookingContext)
      resultF.map { result =>
        verify(priceBreakdownService, times(1)).getPriceBreakdownForCart(
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
        verify(priceBreakdownService, times(0)).getPriceBreakdownForFlight(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )
        result shouldBe mockCartPriceBreakdown
      }
    }

    "Should show flight price breakdown when cart pricing context is empty and itinerary has only flights products and UNIBF-946 is enabled" in {
      val flightConfirmationData = mock[FlightConfirmationData]
      when(flightConfirmationData.cart).thenReturn(None)
      when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(true)
      val resultF = cartPriceBreakdown(
        SetupBookingRequest(
          ProductsRequest(
            flightRequests = Seq(mock[FlightRequestItem]),
            cartPricingContext = None
          )
        ),
        Seq.empty, // UNDERLYING MOCKED
        Map(1 -> flightConfirmationData),
        currencyCode = "USD",
        isFlightCheckInEnabled = true,
        isInclusivePaySupplier = false,
        fixDoubleTaxesAndFeesPaySupplier = false
      )(setupBookingContext)
      resultF.map { result =>
        verify(priceBreakdownService, times(0)).getPriceBreakdownForCart(
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
        verify(priceBreakdownService, times(1)).getPriceBreakdownForFlight(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )
        result shouldBe mockFlightPriceBreakdown
      }
    }

    "Should show empty flight price breakdown when cart pricing context is empty and itinerary has only flights products, no flight confirmation data and UNIBF-946 is enabled" in {
      val flightConfirmationData = mock[FlightConfirmationData]
      when(flightConfirmationData.cart).thenReturn(None)
      when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(true)
      val resultF = cartPriceBreakdown(
        SetupBookingRequest(
          ProductsRequest(
            flightRequests = Seq(mock[FlightRequestItem]),
            cartPricingContext = None
          )
        ),
        Seq.empty, // UNDERLYING MOCKED
        Map.empty, // Empty Flight Confirmation data
        currencyCode = "USD",
        isFlightCheckInEnabled = true,
        isInclusivePaySupplier = false,
        fixDoubleTaxesAndFeesPaySupplier = false
      )(setupBookingContext)
      resultF.map { result =>
        verify(priceBreakdownService, times(0)).getPriceBreakdownForCart(
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
        verify(priceBreakdownService, times(1)).getPriceBreakdownForFlight(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )
        result shouldBe None
      }
    }

    "Should show cart price breakdown when context is non empty and feature: useSingleFlightPriceInCart is enabled and UNIBF-F2C is disabled" in {
      val flightConfirmationData = mock[FlightConfirmationData]
      when(flightConfirmationData.cart).thenReturn(None)
      when(whiteLabelInfo.isFeatureEnabled(ArgumentMatchers.eq(SingleFlightPriceBDInCartBooking), any(), any(), any()))
        .thenReturn(true)
      when(featureAware.migrateFlightToCartBF).thenReturn(false)
      val resultF = cartPriceBreakdown(
        SetupBookingRequest(
          ProductsRequest(
            flightRequests = Seq(mock[FlightRequestItem]),
            cartPricingContext = Some(mock[CartPricingContext])
          )
        ),
        Seq.empty, // UNDERLYING MOCKED
        Map(1 -> flightConfirmationData),
        currencyCode = "USD",
        isFlightCheckInEnabled = true,
        isInclusivePaySupplier = false,
        fixDoubleTaxesAndFeesPaySupplier = false
      )(setupBookingContext)
      resultF.map { result =>
        verify(priceBreakdownService, times(1)).getPriceBreakdownForCart(
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
        verify(priceBreakdownService, times(0)).getPriceBreakdownForFlight(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )
        result shouldBe mockCartPriceBreakdown
      }
    }

    "Should show cart price breakdown when context is non empty and feature: useSingleFlightPriceInCart is disabled and UNIBF-F2C is enabled" in {
      val flightConfirmationData = mock[FlightConfirmationData]
      when(flightConfirmationData.cart).thenReturn(None)
      when(whiteLabelInfo.isFeatureEnabled(ArgumentMatchers.eq(SingleFlightPriceBDInCartBooking), any(), any(), any()))
        .thenReturn(false)
      when(featureAware.migrateFlightToCartBF).thenReturn(true)
      val resultF = cartPriceBreakdown(
        SetupBookingRequest(
          ProductsRequest(
            flightRequests = Seq(mock[FlightRequestItem]),
            cartPricingContext = Some(mock[CartPricingContext])
          )
        ),
        Seq.empty, // UNDERLYING MOCKED
        Map(1 -> flightConfirmationData),
        currencyCode = "USD",
        isFlightCheckInEnabled = true,
        isInclusivePaySupplier = false,
        fixDoubleTaxesAndFeesPaySupplier = false
      )(setupBookingContext)
      resultF.map { result =>
        verify(priceBreakdownService, times(1)).getPriceBreakdownForCart(
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
        verify(priceBreakdownService, times(0)).getPriceBreakdownForFlight(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )
        result shouldBe mockCartPriceBreakdown
      }
    }

    "Should show cart price breakdown when context is non empty and feature: useSingleFlightPriceInCart is enabled and UNIBF-F2C is enabled" in {
      val flightConfirmationData = mock[FlightConfirmationData]
      when(flightConfirmationData.cart).thenReturn(None)
      when(whiteLabelInfo.isFeatureEnabled(ArgumentMatchers.eq(SingleFlightPriceBDInCartBooking), any(), any(), any()))
        .thenReturn(true)
      when(featureAware.migrateFlightToCartBF).thenReturn(true)
      val resultF = cartPriceBreakdown(
        SetupBookingRequest(
          ProductsRequest(
            flightRequests = Seq(mock[FlightRequestItem]),
            cartPricingContext = Some(mock[CartPricingContext])
          )
        ),
        Seq.empty, // UNDERLYING MOCKED
        Map(1 -> flightConfirmationData),
        currencyCode = "USD",
        isFlightCheckInEnabled = true,
        isInclusivePaySupplier = false,
        fixDoubleTaxesAndFeesPaySupplier = false
      )(setupBookingContext)

      resultF.map { result =>
        verify(priceBreakdownService, times(0)).getPriceBreakdownForCart(
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
        verify(priceBreakdownService, times(1)).getPriceBreakdownForFlight(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )
        result shouldBe mockFlightPriceBreakdown
      }
    }
  }

  "getPriceDisplayTypeForCart" should {
    val testCases = Table(
      (
        "description",
        "productRequest",
        "migrateFlightToCartFlow",
        "migrateFlightToCartBF",
        "hf2CartEnabled",
        "expected"
      ),
      ("singleFlight", singleFlightRequest, true, false, false, PriceDisplayType.SingleFlight),
      ("singleFlight", singleFlightRequest, false, false, false, PriceDisplayType.MultiProduct),
      (
        "singleFlightWithCartPricingContext",
        singleFlightRequestCartPricingContext,
        true,
        false,
        false,
        PriceDisplayType.MultiProduct
      ),
      (
        "singleFlightWithCartPricingContext",
        singleFlightRequestCartPricingContext,
        false,
        false,
        false,
        PriceDisplayType.MultiProduct
      ),
      (
        "singleFlightWithCartPricingContext",
        singleFlightRequestCartPricingContext,
        true,
        true,
        false,
        PriceDisplayType.SingleFlight
      ),
      (
        "singleFlight",
        singleFlightRequest,
        false,
        false,
        true,
        PriceDisplayType.SingleFlight
      ),
      (
        "singleFlight",
        singleFlightRequest,
        true,
        false,
        true,
        PriceDisplayType.SingleFlight
      )
    )

    forAll(testCases) {
      (description, productsRequest, experimentEnabled, experimentF2CEnabled, hf2CartEnabled, expected) =>
        s"$description with UNIBF-946 $experimentEnabled and UNIBF-F2C $experimentF2CEnabled and UNIBF-2875 $hf2CartEnabled returns $expected" in {
          when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(experimentEnabled)
          when(featureAware.migrateFlightToCartBF).thenReturn(experimentF2CEnabled)
          when(featureAware.migrateHackerFareToCartFlow(whiteLabelInfo)).thenReturn(hf2CartEnabled)
          val request = SetupBookingRequest(
            productsRequest = productsRequest
          )
          getPriceDisplayTypeForCart(request) shouldBe expected
        }
    }
  }

  "totalSavingsF" should {
    val mockFlightConfirmationData = mock[FlightConfirmationData]
    val mockTotalSavings           = Some(TotalSavings(Money(25, "USD")))

    "return None when cartPricingContext is non-empty and migrateFlightToCartBF is false" in {
      when(featureAware.migrateFlightToCartBF).thenReturn(false)
      val request = SetupBookingRequest(
        ProductsRequest(
          flightRequests = Seq(mock[FlightRequestItem]),
          cartPricingContext = Some(mock[CartPricingContext])
        )
      )
      totalSavingsF(request, Seq(mockFlightConfirmationData), "USD") shouldBe None
    }

    "return flight total savings when cartPricingContext is empty and it's a flight-only request with migrateFlightToCartFlow enabled" in {
      when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(true)
      val request = SetupBookingRequest(
        ProductsRequest(
          flightRequests = Seq(mock[FlightRequestItem])
        )
      )
      totalSavingsF(request, Seq(mockFlightConfirmationData), "USD") shouldBe mockTotalSavings
    }

    "return flight total savings when cartPricingContext is non-empty with migrateFlightToCartBF enabled and it's a flight-only request with migrateFlightToCartFlow enabled" in {
      when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(true)
      when(featureAware.migrateFlightToCartBF).thenReturn(true)
      val request = SetupBookingRequest(
        ProductsRequest(
          flightRequests = Seq(mock[FlightRequestItem]),
          cartPricingContext = Some(mock[CartPricingContext])
        )
      )
      totalSavingsF(request, Seq(mockFlightConfirmationData), "USD") shouldBe mockTotalSavings
    }

    "return None for flight-only request with empty cartPricingContext if migrateFlightToCartFlow is disabled" in {
      when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(false)
      val request = SetupBookingRequest(
        ProductsRequest(
          flightRequests = Seq(mock[FlightRequestItem])
        )
      )
      totalSavingsF(request, Seq(mockFlightConfirmationData), "USD") shouldBe None
    }
  }
}
