package com.agoda.bapi.server.service.payment.mapper

import com.agoda.bapi.common.model.PaymentGroupCategory.PaymentGroupCategory
import com.agoda.bapi.common.model.booking.PaymentFlow.PaymentFlow
import com.agoda.bapi.common.model.booking._
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{ChargeOption, PaymentGroupCategory}
import com.agoda.bapi.server.service.payment.model._
import com.agoda.common.itineraryContext.ItineraryContext
import com.agoda.paymentapiv2.client.v2.common.model.{OptionItem => OptionItemClient, PaymentMethodDetailsResponse, PaymentMethodIcon => PaymentMethodIconClient, RequiredFieldMetadata => RequireFieldClient, SetupPaymentRequestV2, SetupPaymentResponseV2}
import mocks.RequestContextMock
import org.scalatest.BeforeAndAfter
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

class PaymentMapperSpec
    extends AsyncWordSpec
    with Matchers
    with MockitoSugar
    with BeforeAndAfter
    with ScalaFutures
    with RequestContextMock {

  "paymentMethodDetailsV2Mapper" should {
    "map paymentMethodDetailsV2 correctly" in {
      testPaymentMethodDetailsV2Mapper() shouldBe paymentMethod()
      testPaymentMethodDetailsV2Mapper(setupPaymentResponseMock(2)) shouldBe paymentMethod(PaymentFlow.AppPaymentWeChat)
      testPaymentMethodDetailsV2Mapper(setupPaymentResponseMock(20)) shouldBe paymentMethod(PaymentFlow.None)
      testPaymentMethodDetailsV2Mapper(setupPaymentResponseMock(paymentCategory = 2)) shouldBe paymentMethod(
        paymentGroupCategory = PaymentGroupCategory.OnlineBanking
      )
      testPaymentMethodDetailsV2Mapper(setupPaymentResponseMock(paymentCategory = 15)) shouldBe paymentMethod(
        paymentGroupCategory = PaymentGroupCategory.None
      )
    }
  }

  "setupPaymentRequestV2Mapper" should {
    "map correctly" in {
      val paymentMapper = new PaymentMapperImpl()
      val reqV1         = setupPaymentRequestMock()
      val reqV2         = paymentMapper.setupPaymentRequestV2Mapper(reqV1, true)

      // user context
      reqV2.UserContext.get.UserId.get shouldBe reqV1.userContext.userId
      reqV2.UserContext.get.CapiToken.get shouldBe reqV1.userContext.capiToken
      reqV2.UserContext.get.DeviceTypeId.get shouldBe reqV1.userContext.deviceTypeId
      reqV2.UserContext.get.LanguageId.get shouldBe reqV1.userContext.languageId
      reqV2.UserContext.get.MemberId.get shouldBe reqV1.userContext.memberId
      reqV2.UserContext.get.PartnerClaimToken.get shouldBe reqV1.userContext.partnerClaimToken
      reqV2.UserContext.get.RequestOrigin.get shouldBe reqV1.userContext.requestOrigin
      reqV2.UserContext.get.ResidentCountryId.get shouldBe reqV1.userContext.residentCountryId
      reqV2.UserContext.get.SiteId.get shouldBe reqV1.userContext.siteId

      // analytic context
      reqV2.AnalyticContext.get.UserAgent.get shouldBe reqV1.analyticContext.userAgent
      reqV2.AnalyticContext.get.SessionId.get shouldBe reqV1.analyticContext.sessionId
      reqV2.AnalyticContext.get.BookingFlow.get shouldBe reqV1.analyticContext.bookingFlow.toString
      reqV2.AnalyticContext.get.ClientIp.get shouldBe reqV1.analyticContext.clientIp
      reqV2.AnalyticContext.get.IsAffiliateUserLoggedIn.get shouldBe reqV1.analyticContext.isAffiliateUserLoggedIn
      reqV2.AnalyticContext.get.IsLoggedInUser.get shouldBe reqV1.analyticContext.isLoggedInUser
      reqV2.AnalyticContext.get.BookingSessionId shouldBe None
      reqV2.AnalyticContext.get.AppUserAgent shouldBe None
      reqV2.AnalyticContext.get.BrowserUserAgent shouldBe None
      reqV2.AnalyticContext.get.AcceptHeader shouldBe None

      // payment context
      reqV2.PaymentContext.get.PaymentAmount.get.Value.get shouldBe reqV1.paymentContext.paymentAmount.value
      reqV2.PaymentContext.get.PaymentAmount.get.Currency.get shouldBe reqV1.paymentContext.paymentAmount.currency
      reqV2.PaymentContext.get.CcToken.get shouldBe reqV1.paymentContext.ccToken
      reqV2.PaymentContext.get.CcBin.get shouldBe reqV1.paymentContext.ccBin
      reqV2.PaymentContext.get.IncludeThirdPartyInstallmentProviders.get shouldBe reqV1.paymentContext.includeThirdPartyInstallmentProviders
      reqV2.PaymentContext.get.SupportedChargeOptions.get shouldBe reqV1.paymentContext.supportedChargeOptions
      reqV2.PaymentContext.get.PaymentModel.get shouldBe reqV1.paymentContext.paymentModel
      reqV2.PaymentContext.get.SelectedPaymentMethodId.get shouldBe reqV1.paymentContext.selectedPaymentMethod
      reqV2.PaymentContext.get.SelectedChargeCurrency.get shouldBe reqV1.paymentContext.selectedChargeCurrency
      reqV2.PaymentContext.get.InstallmentPlanCode.get shouldBe reqV1.paymentContext.installmentPlanCode
      reqV2.PaymentContext.get.CcId.get shouldBe reqV1.paymentContext.ccId

      // product context
      reqV2.ProductContext.get.WhiteLabelId.get shouldBe reqV1.productContext.whiteLabelId
      reqV2.ProductContext.get.ProductList.get shouldBe reqV1.productContext.productList
      reqV2.ProductContext.get.ItineraryContextJson shouldBe Some("{}")

      // feature context
      reqV2.FeatureContext.get.FeatureList.get shouldBe reqV1.featureContext.paymentFeatures.toList
      reqV2.FeatureContext.get.IncludedPaymentMethod.get shouldBe reqV1.featureContext.includedPaymentMethod.toList
      reqV2.FeatureContext.get.ExcludedPaymentMethod.get shouldBe reqV1.featureContext.excludedPaymentMethod.toList
      reqV2.FeatureContext.get.FeeEnabledPaymentMethodIds.get shouldBe reqV1.featureContext.feeEnabledPaymentMethodIds.toList
      reqV2.FeatureContext.get.AdditionalFields.get shouldBe reqV1.featureContext.additionalFields

      // experiment context
      reqV2.ExperimentContext.get.SupportedFeatures.get shouldBe reqV1.experimentContext.supportedFeatures
      reqV2.ExperimentContext.get.ForcedExperiment.get shouldBe reqV1.experimentContext.forcedExperiment

      // request context
      reqV2.PartnerSpecificParams.isEmpty shouldBe true
    }
  }

  def testPaymentMethodDetailsV2Mapper(
      setupPaymentResponse: SetupPaymentResponseV2 = setupPaymentResponseMock()
  ): Seq[PaymentMethodDetailsV2] = {
    val paymentMapper = new PaymentMapperImpl()
    paymentMapper.paymentMethodDetailsV2Mapper(setupPaymentResponse)
  }

  def paymentMethod(
      paymentFlow: PaymentFlow = PaymentFlow.None,
      paymentGroupCategory: PaymentGroupCategory = PaymentGroupCategory.None
  ): Seq[PaymentMethodDetailsV2] = Seq(
    new PaymentMethodDetailsV2(
      id = 1,
      name = "Visa",
      paymentFlow = paymentFlow,
      paymentGroupCategory = paymentGroupCategory,
      icons = Seq(
        PaymentMethodIcon(
          `type` = 1,
          url = "cdn6/images/visa"
        )
      ),
      timeout = Some(1),
      gatewayName = None,
      remarks = Seq.empty,
      chargeDateTypes = ChargeOption.PayNow,
      chargeOptions = Set(ChargeOption.PayNow, ChargeOption.PayLater),
      isRecommended = false,
      ranking = 1,
      defaultCurrency = "USD",
      cardNumRegEx = Some("^[0-9]{16}$"),
      cvcRegEx = Some("^[0-9]{3}$"),
      isLuhnCheckRecommended = Some(true),
      requiredFields = Some(
        Map(
          "field1" -> RequiredFieldMetadata(
            regex = "^[a-zA-Z0-9]+$",
            nameCmsId = 123,
            validationCmsId = 456,
            placeholderCmsId = Some(789),
            descriptionCmsId = Some(101112),
            id = Some(1),
            maxLengthCmsId = Some(131415),
            maxLength = Some(50),
            fieldType = Some("text"),
            options = Some(
              Seq(
                OptionItem(
                  id = 1,
                  name = "Option1",
                  packageName = "Package1",
                  scheme = "Scheme1",
                  logo = "Logo1",
                  isIosEnabled = true,
                  isAndroidEnabled = true
                )
              )
            )
          )
        )
      ),
      isFapiaoEligible = Some(false),
      isTokenEnabled = Some(false)
    )
  )

  def setupPaymentResponseMock(
      paymentFlow: Int = 0,
      paymentCategory: Int = 0
  ): SetupPaymentResponseV2 = SetupPaymentResponseV2(
    SuggestedPaymentMethod = None,
    PaymentMethodDetails = Some(
      List(
        PaymentMethodDetailsResponse(
          PaymentMethodId = Some(1),
          PaymentMethodName = Some("Visa"),
          PaymentFlow = Some(paymentFlow),
          PaymentCategory = Some(paymentCategory),
          Timeout = Some(1),
          PaymentMethodIcons = Some(
            List(
              PaymentMethodIconClient(
                Type = Some(1),
                Url = Some("cdn6/images/visa")
              )
            )
          ),
          Remarks = None,
          RequiredField = Some(
            Map(
              "field1" -> RequireFieldClient(
                Id = Some(1),
                NameCmsId = Some(123),
                ValidationCmsId = Some(456),
                PlaceHolderCmsId = Some(789),
                DescriptionCmsId = Some(101112),
                MaxLengthCmsId = Some(131415),
                MaxLength = Some(50),
                FieldType = Some("text"),
                Options = Some(
                  List(
                    OptionItemClient(
                      Id = 1,
                      Name = "Option1",
                      PackageName = "Package1",
                      Scheme = "Scheme1",
                      Logo = "Logo1",
                      IsIosEnabled = true,
                      IsAndroidEnabled = true
                    )
                  )
                ),
                Regex = Some("^[a-zA-Z0-9]+$")
              )
            )
          ),
          ChargeType = Some(1),
          ChargeOptions = Some(List(1, 2)),
          IsRecommended = Some(false),
          Ranking = Some(1),
          DefaultCurrency = Some("USD"),
          CardNumRegex = Some("^[0-9]{16}$"),
          CvcRegex = Some("^[0-9]{3}$"),
          IsLuhnCheckRecommended = Some(true),
          IsTokenEnabled = Some(false),
          IsFapiaoEligible = Some(false),
          FeeInfo = None
        )
      )
    ),
    CreditCardOnFile = None,
    CreditCardInfoSetup = None,
    NonCardOnFile = None,
    InstallmentPlans = None,
    InstallmentAvailableProviders = None,
    InstallmentPlanCode = None,
    IsInstallmentEligible = None,
    RequestStatus = None,
    RequestMessage = None
  )

  def setupPaymentRequestMock() = {
    val setupPaymentRequest = SetupPaymentRequest(
      userContext = UserContext(
        requestOrigin = "A1",
        residentCountryId = 114,
        languageId = 112,
        userId = "U1",
        deviceTypeId = 1,
        memberId = "M1",
        siteId = 113,
        capiToken = "capi",
        partnerClaimToken = "partner"
      ),
      analyticContext = AnalyticContext(
        userAgent = "U1",
        sessionId = "S1",
        bookingFlow = BookingFlow.Cart,
        clientIp = "*******",
        isLoggedInUser = true,
        isAffiliateUserLoggedIn = true
      ),
      paymentContext = PaymentContext(
        paymentAmount = Amount(1.0, "USD"),
        ccToken = "ccToken",
        ccBin = "123456",
        includeThirdPartyInstallmentProviders = true,
        supportedChargeOptions = Seq(1, 2),
        paymentModel = 1,
        selectedPaymentMethod = 2,
        selectedChargeCurrency = "USD",
        installmentPlanCode = "Y",
        ccId = 111
      ),
      productContext = ProductContext(
        whiteLabelId = 1,
        productList = Seq.empty,
        itineraryContext = Some(ItineraryContext())
      ),
      featureContext = FeatureContext(
        includedPaymentMethod = Set(1, 5),
        excludedPaymentMethod = Set(3),
        paymentFeatures = Set("1", "2"),
        feeEnabledPaymentMethodIds = Set.empty,
        additionalFields = Map("field1" -> "field2")
      ),
      experimentContext = ExperimentContext(
        supportedFeatures = Seq.empty,
        forcedExperiment = Map.empty
      )
    )

    setupPaymentRequest
  }
}
