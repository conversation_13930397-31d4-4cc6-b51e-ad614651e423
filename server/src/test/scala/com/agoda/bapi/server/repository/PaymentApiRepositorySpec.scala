package com.agoda.bapi.server.repository

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.{BinRangeInfo, BinRangeRequest}
import com.agoda.bapi.server.proxy.payment.PaymentApiProxy
import com.agoda.paymentapiv2.client.v2.common.model.{BinRangeInfo => gatewayBinRangeInfo, GetBinRangeRequest => GatewayGetBinRangeRequest, GetInstallmentDataRequest, InstallmentAvailableProvider, InstallmentDataResponse, InstallmentPlan}
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.funsuite.AnyFunSuite
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.duration._
import scala.concurrent.{Await, Future}

class PaymentApiRepositorySpec extends AnyFunSuite with MockitoSugar with BeforeAndAfter {
  val paymentApiProxy         = mock[PaymentApiProxy]
  implicit val requestContext = mock[RequestContext]

  before {
    reset(paymentApiProxy)
  }

  test("getBinRange should return the valid bin range") {
    withPaymentApiV2Repository { repository =>
      val request = BinRangeRequest(
        languageId = 1,
        correlationId = None,
        bin = Some("2345")
      )

      val gateWayResponse =
        gatewayBinRangeInfo(
          BinRangeId = Some(123),
          BinLower = Some(234),
          BinHigher = Some(456),
          CardType = Some("MASTERCARD"),
          CardScheme = Some("MASTERCARD"),
          CardIssuer = Some("RANGE RESERVED FOR MASTERCARD"),
          CountryCardIssuer = Some("Thailand"),
          CardClass = Some("TestClass"),
          BankId = Some(1),
          BinLowerOrginal = Some(232310),
          BinLength = Some(6)
        )

      val response = BinRangeInfo(
        binRangeId = Some(123),
        binLower = Some(234),
        binHigher = Some(456),
        cardType = Some("MASTERCARD"),
        cardScheme = Some("MASTERCARD"),
        cardIssuer = Some("RANGE RESERVED FOR MASTERCARD"),
        countryCardIssuer = Some("Thailand"),
        cardClass = Some("TestClass"),
        bankId = Some(1),
        binLowerOrginal = Some(232310),
        binLength = Some(6)
      )
      when(paymentApiProxy.gatewayGetBinRange(any[GatewayGetBinRangeRequest])(any[RequestContext]))
        .thenReturn(Future.successful(gateWayResponse))
      val binRange      = Await.result(repository.getBinRange(request), 1.second)
      val binRangeValue = binRange.get

      assert(binRangeValue.binRangeId == response.binRangeId)
      assert(binRangeValue.binLower == response.binLower)
      assert(binRangeValue.binHigher == response.binHigher)
      assert(binRangeValue.cardType == response.cardType)
      assert(binRangeValue.cardScheme == response.cardScheme)
      assert(binRangeValue.cardIssuer == response.cardIssuer)
      assert(binRangeValue.countryCardIssuer == response.countryCardIssuer)
      assert(binRangeValue.bankId == response.bankId)
      assert(binRangeValue.binLowerOrginal == response.binLowerOrginal)
      assert(binRangeValue.binLength == response.binLength)

    }
  }

  test("getInstallmentData should return installmentDataResponse") {
    val installmentPlan = InstallmentPlan(PlanId = Some(1))
    val installmentAvailableProviders = List(
      InstallmentAvailableProvider(ProviderId = Some(1)),
      InstallmentAvailableProvider(ProviderId = Some(2))
    )

    when(paymentApiProxy.getInstallmentData(any[GetInstallmentDataRequest])(any[RequestContext]))
      .thenReturn(
        Future.successful(
          InstallmentDataResponse(
            InstallmentAvailableProviders = Some(installmentAvailableProviders),
            InstallmentPlans = Some(List(installmentPlan))
          )
        )
      )

    val request = GetInstallmentDataRequest()
    withPaymentApiV2Repository { repository =>
      val res = Await.result(repository.getInstallmentData(request), 1.second)

      assert(res.InstallmentPlans.nonEmpty)
      val paymentInstallmentPlans: List[InstallmentPlan] = res.InstallmentPlans.head
      assert(paymentInstallmentPlans(0).PlanId == installmentPlan.PlanId)

      assert(res.InstallmentAvailableProviders.nonEmpty)
      val paymentProviders: List[InstallmentAvailableProvider] = res.InstallmentAvailableProviders.head
      assert(paymentProviders.head.ProviderId == installmentAvailableProviders.head.ProviderId)
      assert(paymentProviders(1).ProviderId == installmentAvailableProviders(1).ProviderId)
    }
  }

  def withPaymentApiV2Repository[T](executor: PaymentApiRepository => T) = {
    val paymentApiV2Repository = new PaymentApiRepositoryImpl(paymentApiProxy)
    executor(paymentApiV2Repository)
  }
}
