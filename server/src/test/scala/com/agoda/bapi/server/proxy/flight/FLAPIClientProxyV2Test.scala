package com.agoda.bapi.server.proxy.flight

import akka.actor.ActorSystem
import com.agoda.bapi.common.WithProxyMessageTestMock
import com.agoda.bapi.common.config.FlightsApiConfig
import com.agoda.bapi.common.handler.MeasurementsContext
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.commons.serialization.v1.instances.CirceInstances
import com.agoda.flights.client.v2.api.FlightsApi
import com.agoda.flights.client.v2.model._
import com.agoda.mpb.common.header.AgHttpHeader
import mocks.MockFlightDataV2
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._
import org.mockito.{ArgumentCaptor, Mockito}
import org.scalatest.{BeforeAndAfter, OneInstancePerTest}
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future, Promise}

class FLAPIClientProxyV2Test
    extends AsyncWordSpec
    with WithProxyMessageTestMock
    with Matchers
    with MockitoSugar
    with BeforeAndAfter
    with MockFlightDataV2
    with CirceInstances
    with OneInstancePerTest {

  import FLAPIClientProxyV2Test._

  private val flapiClientConfig = FlightsApiConfig(
    timeout = None
  )

  private val flapiClientV2 = mock[FlightsApi[Future]](Mockito.RETURNS_DEEP_STUBS)

  private val featureAware = mock[FeatureAware]

  implicit private val setupBookingContext: SetupBookingContext = mock[SetupBookingContext]
  implicit private val measurementsContext: MeasurementsContext = mock[MeasurementsContext]

  private val AG_ATF_DEBUG_ID_KEY   = "AG-ATF-DEBUG-ID"
  private val AG_ATF_DEBUG_ID_VALUE = "test"
  private val AG_BOT_INFO           = "botprofile"
  private val AG_GK_RQ_PRIORITY     = "R2"

  private val defaultFlightHeader: Map[String, String] = Map(
    "ag-bot-info"       -> AG_BOT_INFO,
    "ag-gk-rq-priority" -> AG_GK_RQ_PRIORITY
  )

  before {
    reset(featureAware)
    reset(flapiClientV2)
    reset(setupBookingContext)
    reset(requestContext)

    when(measurementsContext.tags).thenReturn(Map[String, String]())
    when(requestContext.agHeaders).thenReturn(
      Seq(AgHttpHeader(AG_ATF_DEBUG_ID_KEY, AG_ATF_DEBUG_ID_VALUE))
    )
    when(setupBookingContext.requestContext).thenReturn(requestContext)

    when(requestContext.userContext).thenReturn(None)
    when(requestContext.bookingCreationContext).thenReturn(None)

    when(requestContext.featureAware).thenReturn(Some(featureAware))
  }

  "postFlightsConfirmPrice" should {
    "returns postFlightsConfirmprice successfully where" in {
      val argumentCaptor: ArgumentCaptor[ConfirmPriceRequest] = ArgumentCaptor.forClass(classOf[ConfirmPriceRequest])
      val agBotInfo: ArgumentCaptor[Option[String]]           = ArgumentCaptor.forClass(classOf[Option[String]])
      val agGkRqPriority: ArgumentCaptor[Option[String]]      = ArgumentCaptor.forClass(classOf[Option[String]])
      val agAtfDebugId: ArgumentCaptor[Option[String]]        = ArgumentCaptor.forClass(classOf[Option[String]])

      when(flapiClientV2.postFlightsConfirmprice(any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(defaultConfirmPriceResponseV2))

      when(flapiClientV2.circeSerializer[ConfirmPriceResponse](any()).serialize(any())).thenReturn("serializedResponse")

      val flapiProxyV2 = createFlapiProxyWithActorSystem()

      flapiProxyV2
        .postFlightsConfirmPrice(defaultConfirmPriceRequestV2, defaultFlightHeader)
        .map { response =>
          verify(flapiClientV2, times(1)).postFlightsConfirmprice(
            argumentCaptor.capture(),
            agBotInfo.capture(),
            agGkRqPriority.capture(),
            agAtfDebugId.capture()
          )

          argumentCaptor.getValue shouldEqual defaultConfirmPriceRequestV2
          agBotInfo.getValue shouldEqual Some(AG_BOT_INFO)
          agGkRqPriority.getValue shouldEqual Some(AG_GK_RQ_PRIORITY)
          agAtfDebugId.getValue shouldEqual Some(AG_ATF_DEBUG_ID_VALUE)
          response shouldEqual (defaultConfirmPriceResponseV2, "serializedResponse")
        }
        .andThen { case _ => flapiProxyV2.actorSystem.terminate() }
    }
    "returns postFlightsConfirmprice with error where  ..." in {
      when(flapiClientV2.postFlightsConfirmprice(any(), any(), any(), any())(any()))
        .thenReturn(Future.failed(new Exception("Flight exception")))

      val flapiProxyV2 = createFlapiProxyWithActorSystem()

      flapiProxyV2
        .postFlightsConfirmPrice(defaultConfirmPriceRequestV2, defaultFlightHeader)
        .map { _ =>
          fail("Expected an exception, but got a successful result")
        }
        .recover {
          case ex: Exception => succeed
        }
        .andThen { case _ => flapiProxyV2.actorSystem.terminate() }
    }
    "returns timeout exception successfully  ..." in {
      val flapiProxyV2 = createFlapiProxyWithActorSystem(
        flapiClientConfig.copy(timeout = Some(100))
      )

      when(flapiClientV2.postFlightsConfirmprice(any(), any(), any(), any())(any()))
        .thenReturn(
          simulateTimeout(200, defaultConfirmPriceResponseV2)(
            flapiProxyV2.actorSystem,
            flapiProxyV2.actorSystem.dispatcher
          )
        )

      flapiProxyV2
        .postFlightsConfirmPrice(defaultConfirmPriceRequestV2, defaultFlightHeader)
        .map { _ =>
          fail("Expected a timeout exception, but got a successful result")
        }
        .recover {
          case ex: FlightsApiTimeoutException => succeed
          case ex                             => fail(s"Expected a FlightsApiTimeoutException, but got an unexpected exception: $ex")
        }
        .andThen { case _ => flapiProxyV2.actorSystem.terminate() }
    }
  }
  "postFlightsInstantPriceConfirm" should {
    "returns postFlightsInstantPriceConfirm successfully" in {
      val argumentCaptor: ArgumentCaptor[InstantPriceConfirmRequest] =
        ArgumentCaptor.forClass(classOf[InstantPriceConfirmRequest])
      val agBotInfo: ArgumentCaptor[Option[String]]      = ArgumentCaptor.forClass(classOf[Option[String]])
      val agGkRqPriority: ArgumentCaptor[Option[String]] = ArgumentCaptor.forClass(classOf[Option[String]])
      val agAtfDebugId: ArgumentCaptor[Option[String]]   = ArgumentCaptor.forClass(classOf[Option[String]])

      when(flapiClientV2.postFlightsInstantpriceconfirm(any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(defaultConfirmPriceResponseV2))

      when(flapiClientV2.circeSerializer[ConfirmPriceResponse](any()).serialize(any())).thenReturn("serializedResponse")

      val flapiProxyV2 = createFlapiProxyWithActorSystem()

      flapiProxyV2
        .postFlightsInstantPriceConfirm(defaultInstantPriceRequestV2, defaultFlightHeader)
        .map { response =>
          verify(flapiClientV2, times(1)).postFlightsInstantpriceconfirm(
            argumentCaptor.capture(),
            agBotInfo.capture(),
            agGkRqPriority.capture(),
            agAtfDebugId.capture()
          )

          argumentCaptor.getValue shouldEqual defaultInstantPriceRequestV2
          agBotInfo.getValue shouldEqual Some(AG_BOT_INFO)
          agGkRqPriority.getValue shouldEqual Some(AG_GK_RQ_PRIORITY)
          agAtfDebugId.getValue shouldEqual Some(AG_ATF_DEBUG_ID_VALUE)
          response shouldEqual (defaultConfirmPriceResponseV2, "serializedResponse")
        }
        .andThen { case _ => flapiProxyV2.actorSystem.terminate() }
    }
    "returns postFlightsInstantPriceConfirm with error  ..." in {
      when(flapiClientV2.postFlightsInstantpriceconfirm(any(), any(), any(), any())(any()))
        .thenReturn(Future.failed(new Exception("Flight exception")))

      val flapiProxyV2 = createFlapiProxyWithActorSystem()

      flapiProxyV2
        .postFlightsInstantPriceConfirm(defaultInstantPriceRequestV2, defaultFlightHeader)
        .map { _ =>
          fail("Expected an exception, but got a successful result")
        }
        .recover {
          case ex: Exception => succeed
        }
        .andThen { case _ => flapiProxyV2.actorSystem.terminate() }
    }
    "returns timeout exception successfully for postFlightsInstantPriceConfirm ..." in {
      val flapiProxyV2 = createFlapiProxyWithActorSystem(
        flapiClientConfig.copy(timeout = Some(100))
      )

      when(flapiClientV2.postFlightsInstantpriceconfirm(any(), any(), any(), any())(any()))
        .thenReturn(
          simulateTimeout(200, defaultConfirmPriceResponseV2)(
            flapiProxyV2.actorSystem,
            flapiProxyV2.actorSystem.dispatcher
          )
        )

      flapiProxyV2
        .postFlightsInstantPriceConfirm(defaultInstantPriceRequestV2, defaultFlightHeader)
        .map { _ =>
          fail("Expected an FlightsApiTimeoutException, but got a successful result")
        }
        .recover {
          case ex: FlightsApiTimeoutException => succeed
          case ex                             => fail(s"Expected a FlightsApiTimeoutException, but got an unexpected exception: $ex")
        }
        .andThen { case _ => flapiProxyV2.actorSystem.terminate() }
    }
  }
  "postFlightItineraryDetails" should {
    "returns postFlightItineraryDetails successfully" in {
      val argumentCaptor: ArgumentCaptor[ConfirmPriceRequest] = ArgumentCaptor.forClass(classOf[ConfirmPriceRequest])
      val agBotInfo: ArgumentCaptor[Option[String]]           = ArgumentCaptor.forClass(classOf[Option[String]])
      val agGkRqPriority: ArgumentCaptor[Option[String]]      = ArgumentCaptor.forClass(classOf[Option[String]])
      val agAtfDebugId: ArgumentCaptor[Option[String]]        = ArgumentCaptor.forClass(classOf[Option[String]])

      when(flapiClientV2.postFlightsItinerary(any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(defaultItineraryDetailsResponseV2))

      when(flapiClientV2.circeSerializer[ConfirmPriceResponse](any()).serialize(any())).thenReturn("serializedResponse")

      val flapiProxyV2 = createFlapiProxyWithActorSystem()

      flapiProxyV2
        .postFlightItineraryDetails(defaultConfirmPriceRequestV2, defaultFlightHeader)
        .map { response =>
          verify(flapiClientV2, times(1)).postFlightsItinerary(
            argumentCaptor.capture(),
            agBotInfo.capture(),
            agGkRqPriority.capture(),
            agAtfDebugId.capture()
          )

          argumentCaptor.getValue shouldEqual defaultConfirmPriceRequestV2
          agBotInfo.getValue shouldEqual Some(AG_BOT_INFO)
          agGkRqPriority.getValue shouldEqual Some(AG_GK_RQ_PRIORITY)
          agAtfDebugId.getValue shouldEqual Some(AG_ATF_DEBUG_ID_VALUE)
          response shouldEqual (defaultItineraryDetailsResponseV2, "serializedResponse")
        }
        .andThen { case _ => flapiProxyV2.actorSystem.terminate() }
    }
    "returns postFlightItineraryDetails with enabledFeature successfully  ..." in {
      when(flapiClientV2.postFlightsItinerary(any(), any(), any(), any())(any()))
        .thenReturn(Future.failed(new Exception("Flight exception")))

      val flapiProxyV2 = createFlapiProxyWithActorSystem()

      flapiProxyV2
        .postFlightItineraryDetails(defaultConfirmPriceRequestV2, defaultFlightHeader)
        .map { _ =>
          fail("Expected an exception, but got a successful result")
        }
        .recover {
          case ex: Exception => succeed
        }
        .andThen { case _ => flapiProxyV2.actorSystem.terminate() }
    }

    "returns postFlightItineraryDetails with error  ..." in {
      val flapiProxyV2 = createFlapiProxyWithActorSystem(
        flapiClientConfig.copy(timeout = Some(100))
      )

      when(flapiClientV2.postFlightsItinerary(any(), any(), any(), any())(any()))
        .thenReturn(
          simulateTimeout(200, defaultItineraryDetailsResponseV2)(
            flapiProxyV2.actorSystem,
            flapiProxyV2.actorSystem.dispatcher
          )
        )

      flapiProxyV2
        .postFlightItineraryDetails(defaultConfirmPriceRequestV2, defaultFlightHeader)
        .map { _ =>
          fail("Expected an FlightsApiTimeoutException, but got a successful result")
        }
        .recover {
          case ex: FlightsApiTimeoutException => succeed
          case ex                             => fail(s"Expected a FlightsApiTimeoutException, but got an unexpected exception: $ex")
        }
        .andThen { case _ => flapiProxyV2.actorSystem.terminate() }
    }

  }

  private def createFlapiProxyWithActorSystem(
      flightsApiConfig: FlightsApiConfig = flapiClientConfig
  ) = {
    implicit val system: ActorSystem = ActorSystem("DelayedFutureSystem")
    import system.dispatcher // Use Akka's dispatcher as ExecutionContext

    new FLAPIClientProxyV2Impl(flapiClientV2, flightsApiConfig, system, messagingService)
  }

}

object FLAPIClientProxyV2Test {
  private def simulateTimeout[T](delay: Int, block: => T)(implicit
      system: ActorSystem,
      executionContext: ExecutionContext
  ) = {
    val promise = Promise[T]()
    system.scheduler.scheduleOnce(delay millis) {
      promise.success(block)
    }
    promise.future
  }
}
