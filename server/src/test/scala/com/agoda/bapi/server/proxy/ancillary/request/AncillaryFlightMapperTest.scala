package com.agoda.bapi.server.proxy.ancillary.request
import com.agoda.ancillary.models.response.Price
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.setupBooking.CartPricingContext
import com.agoda.bapi.common.model.{<PERSON><PERSON><PERSON>cyCode, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.service.MessagesBag
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.proxy.AncillaryProtectionFlightMockHelper
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import mocks.{MockFlightConfirmationData, RequestContextMock}
import org.mockito.Mockito.when
import org.scalatest.funspec.AsyncFunSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class AncillaryFlightMapperTest
    extends AsyncFunSpec
    with RequestContextMock
    with MockitoSugar
    with Matchers
    with AncillaryProtectionFlightMockHelper {
  val messagesBag: MessagesBag = mock[MessagesBag]

  implicit val context: RequestContext = requestContext(messagesBag)

  implicit val setupBookingContext: SetupBookingContext = SetupBookingContext(
    BookingFlow.SingleFlight,
    context,
    "",
    WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
    None,
    sessionId = "",
    bookingSessionId = ""
  )

  describe("AncillaryFlightMapper") {
    it("should correctly map FlightConfirmationData to Fly") {
      val result = AncillaryFlightMapper.map(MockFlightConfirmationData.mock, CurrencyCode.USD, None)

      result should not be empty
      val fly = result.get
      fly.id shouldBe "A:BKK_20240504_G:NRT_6-0-0_ECO___1625160513"
      fly.tripCost.amount shouldBe 344.0
      fly.tripCost.currencyCode shouldBe "USD"
      fly.sliceDetail.slices should have size 1
      val slice = fly.sliceDetail.slices.head
      slice.segments should have size 1
    }

    it("should return breakdown price") {
      val actualResult =
        AncillaryFlightMapper.map(mockFlightConfirmation1, CurrencyCode.USD, None)

      val expectedResult = Some(Price(1000.0, "USD"))

      actualResult.map(_.tripCost) shouldBe expectedResult
    }

    it("should return breakdown price and have multiple flights") {
      val actualResult =
        AncillaryFlightMapper.map(mockFlightConfirmation2, CurrencyCode.USD, None)

      val expectedResult = Some(Price(1500.0, "USD"))

      actualResult.map(_.tripCost) shouldBe expectedResult
    }

    it("should return breakdown price correctly when have different itemId and calculate on itemId = 12") {
      val actualResult =
        AncillaryFlightMapper.map(mockFlightConfirmation3, CurrencyCode.USD, None)

      val expectedResult = Some(Price(1000.0, "USD"))

      actualResult.map(_.tripCost) shouldBe expectedResult
    }

    it("should return breakdown price correctly when have different currencyCode and calculate on itemId = 12") {
      val actualResult = AncillaryFlightMapper.map(mockFlightConfirmation4, "THB", None)

      val expectedResult = Some(Price(2500.0, "THB"))

      actualResult.map(_.tripCost) shouldBe expectedResult
    }

    it("should return display price correctly when have different currencyCode and none has itemId = 12") {
      val actualResult = AncillaryFlightMapper.map(mockFlightConfirmation5, "THB", None)

      val expectedResult = Some(Price(1000.0, "THB"))

      actualResult.map(_.tripCost) shouldBe expectedResult
    }

    it("should return display price as None when breakdownPrice doesn't exist") {
      val actualResult = AncillaryFlightMapper.map(mockFlightConfirmation7, "THB", None)

      actualResult.map(_.tripCost) shouldBe None
    }

    it(
      "should return display price correctly when have different currencyCode and none has itemId = 12 in case of hackerfare"
    ) {
      val actualResult = AncillaryFlightMapper.map(mockFlightConfirmation8, "THB", None)

      val expectedResult = Some(Price(2000.0, "THB"))

      actualResult.map(_.tripCost) shouldBe expectedResult
    }

    it("should return isCart as true when cartPricingContext is defined") {
      val setupBookingContext = mock[SetupBookingContext]
      val requestContext      = mock[RequestContext]
      when(setupBookingContext.requestContext).thenReturn(requestContext)

      val actualResult =
        AncillaryFlightMapper.map(mockFlightConfirmation1, "THB", Some(mock[CartPricingContext]))(setupBookingContext)

      actualResult.map(_.isCart) shouldBe Some(true)
    }

    it("should return isCart as false when cartPricingContext is empty") {
      val setupBookingContext = mock[SetupBookingContext]
      val requestContext      = mock[RequestContext]
      when(setupBookingContext.requestContext).thenReturn(requestContext)

      val actualResult =
        AncillaryFlightMapper.map(mockFlightConfirmation1, "THB", None)(setupBookingContext)

      actualResult.map(_.isCart) shouldBe Some(false)
    }
  }
}
