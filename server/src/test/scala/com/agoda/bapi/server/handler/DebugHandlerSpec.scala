package com.agoda.bapi.server.handler

import akka.http.scaladsl.testkit.ScalatestRouteTest
import com.agoda.bapi.common.message.creation.PaymentAmount
import com.agoda.bapi.common.message.debug.DecryptTokenReadableResponse
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.token.{BookingTokenEncryptionHelper, MultiProductCreationBookingToken, MultiProductRetryPaymentBookingToken, MultiProductSetupBookingToken}
import com.agoda.bapi.server.facades.DebugFacade
import com.agoda.bapi.server.utils.GetConfigUtil
import mocks.{PropertyMock, RequestContextMock}
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito._
import org.scalatest._
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future
import scala.util.{Success, Try}

class DebugHandlerSpec
    extends AsyncWordSpec
    with Matchers
    with ScalatestRouteTest
    with MockitoSugar
    with BeforeAndAfterEach
    with RequestContextMock
    with PropertyMock {

  private val encryptionHelper = mock[BookingTokenEncryptionHelper]
  private val getConfigUtil    = mock[GetConfigUtil]
  private val debugFacade      = mock[DebugFacade]
  private val handler          = new DebugHandlerImpl(encryptionHelper, getConfigUtil, debugFacade)

  override def beforeEach: Unit = {
    reset(encryptionHelper)
  }

  "Encrypt token" should {
    "returns encrypted response" in {
      val response = TokenMessage(token = "{}", version = 1)

      when(encryptionHelper.encryptToken(any[String]())).thenReturn(Try(response))

      handler.encryptToken("").map {
        _ shouldBe response
      }
    }
  }

  "Decrypt token" should {
    "returns decrypted token model string" in {
      val response = "{}"

      when(encryptionHelper.decryptToken(any[TokenMessage]())).thenReturn(Try(response))

      handler.decryptToken(TokenMessage(token = "{}", version = 1)).map {
        _ shouldBe response
      }
    }
  }

  "GetAllConfigurations" should {
    "return strings of config" in {
      val response = "config result"
      when(getConfigUtil.getAllConfigurations).thenReturn(Success(response))

      handler.getAllConfigurations.map {
        _ shouldBe response
      }

      val result: Future[String] = handler.getAllConfigurations
      result.map { res =>
        res shouldEqual "config result"
      }
    }
  }

  "decryptTokenReadable" should {
    "return decrypted token model deserialized" in {
      val decryptTokenReadableResponse = DecryptTokenReadableResponse(
        setupBookingToken = MultiProductSetupBookingToken(),
        creationBookingToken = MultiProductCreationBookingToken(
          properties = None,
          flights = None,
          tripProtections = None,
          cars = None,
          activities = None,
          priceFreezes = None,
          cegFastTracks = None,
          addOns = None,
          payment = PaymentAmount(
            siteExchangeRate = 0,
            upliftExchangeRate = 0,
            destinationExchangeRate = 0
          ),
          bookingFlowType = BookingFlow.Cart,
          commonPayment = None
        ),
        retryPaymentBookingToken = Some(MultiProductRetryPaymentBookingToken(flights = None))
      )
      when(debugFacade.decryptToken(any[TokenMessage])).thenReturn(Future.successful(decryptTokenReadableResponse))

      handler.decryptTokenReadable(any[TokenMessage]).map {
        _ shouldBe decryptTokenReadableResponse
      }
    }
  }
}
