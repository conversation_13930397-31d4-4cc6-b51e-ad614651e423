package com.agoda.bapi.server.route

import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.model.{ContentTypes, HttpEntity, HttpMethods, HttpRequest}
import akka.http.scaladsl.testkit.ScalatestRouteTest
import akka.stream.{ActorMaterializer, ActorMaterializerSettings}
import com.agoda.bapi.common.config.{Configuration, KillSwitches}
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.{BookingServerStatus, CreateBookingResponse}
import com.agoda.bapi.common.proxy.WhiteLabelApiProxy
import com.agoda.bapi.creation.handler.CreationHandler
import com.agoda.bapi.creation.service.{HadoopMessageProcessName, HadoopMessagingService}
import com.agoda.bapi.server.route.pci.ItineraryPciRoutes
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.util.Modules
import com.google.inject.{AbstractModule, Guice}
import com.typesafe.scalalogging.Logger
import net.codingwell.scalaguice.ScalaModule
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfterEach
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatestplus.mockito.MockitoSugar

import java.util.concurrent.ConcurrentHashMap
import javax.inject.Singleton
import scala.concurrent.Future
import scala.io.Source

class ItineraryPciRoutesTest
    extends AnyWordSpec
    with MockitoSugar
    with BeforeAndAfterEach
    with ScalatestRouteTest
    with RoutesTimeoutSupport
    with Matchers {

  private val CLIENT_ID = "23"
  private val API_KEY   = "OGjOiYtdNEof2LgpsHzXRKbtuRoXwauwQb2hwYX5uz6H97neqn"

  private val actorMaterializer = ActorMaterializer(ActorMaterializerSettings(system))

  private val mockConfig: Configuration                        = mock[Configuration](Mockito.RETURNS_DEEP_STUBS)
  private val mockHandler: CreationHandler                     = mock[CreationHandler]
  private val mockHadoopMessageService: HadoopMessagingService = mock[HadoopMessagingService]
  private val mockKillSwitches: KillSwitches                   = mock[KillSwitches]
  private val wlClient                                         = mock[WhiteLabelApiProxy]
  private val mockRequestContext: RequestContext               = mock[RequestContext]
  private val testModule =
    Modules.combine(new AbstractModule with ScalaModule {
      override def configure(): Unit =
        bind[ObjectMapper].toProvider[ObjectMapperProvider].in[Singleton]
    })

  val mockLogger = mock[Logger]
  val underlying = mock[org.slf4j.Logger]
  when(mockLogger.underlying).thenReturn(underlying)
  when(underlying.isWarnEnabled()).thenReturn(true)

  private val injector = Guice.createInjector(testModule)

  private val itineraryRoute =
    new ItineraryPciRoutes(
      mockConfig,
      mockHandler,
      mockHadoopMessageService,
      actorMaterializer,
      mockKillSwitches
    ) {
      val state                                           = new ConcurrentHashMap[String, Map[String, String]]()
      override def defaultObjectMapper                    = injector.getInstance(classOf[ObjectMapper])
      override val whiteLabelApiProxy: WhiteLabelApiProxy = wlClient
      override def logger: Logger                         = mockLogger
      override val reporter: MetricsReporter = new MetricsReporter {
        override def report(metric: String, value: Long, tags: Map[String, String]): Unit =
          state.put(metric, tags)
      }
    }

  override def beforeEach(): Unit = {
    when(mockConfig.siteSettings.isAuthen).thenReturn(false)
    when(
      mockHadoopMessageService.sendBookingCreateLogMessage(
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any()
      )
    )
      .thenReturn(Future.successful())
    when(wlClient.getFeaturesById(any())).thenReturn(FeaturesConfiguration())
    reset(mockHadoopMessageService)
    super.beforeAll()
  }

  private def createBookingRequest(uri: String, body: String) =
    HttpRequest(
      method = HttpMethods.POST,
      headers = scala.collection.immutable.Seq(
        RawHeader("Client-ID", CLIENT_ID),
        RawHeader("API-Key", API_KEY),
        RawHeader("Request-ID", "mock_request_id")
      ),
      uri = uri,
      entity = HttpEntity(ContentTypes.`application/json`, body.getBytes())
    )

  "Deserialization of CreateBookingRequestV2" should {
    "log and sending measurement successfully" in {
      val body = """{"context"}"""
      val uri  = "/v1/itinerary/create"

      createBookingRequest(uri, body) ~> itineraryRoute.routes ~> check {
        val response = responseAs[String]

        itineraryRoute.state.contains("bapi_deserialize")
        itineraryRoute.state.contains("bapi_total_metrics")
        status.isSuccess() shouldEqual true
        response shouldBe """{"success":false,"errorMessage":"Unexpected request body","errorCode":"Bad request","subErrorCode":5,"duplicateBookings":[],"status":{"bookingStatus":13,"bookingStatusCategory":2}}"""

        verify(mockHadoopMessageService, times(1)).sendBookingCreateLogMessage(
          HadoopMessageProcessName.CreateBookingResponse,
          "mock_request_id",
          23,
          """Failed to deserialize booking create request: !!!NEXT JSON IS MALFORMED DUE TO MASKING OF SENSITIVE USER DATA!!!--->com.fasterxml.jackson.core.JsonParseException:Unexpectedcharacter('}'(code125)):wasexpectingacolontoseparatefieldnameandvalueat[Source:(String)"{"context"}";line:1,column:12]""",
          None,
          None
        )
      }
    }

    "log and return fail response if CreationHandler.createV2 failed" in {
      val testcaseFile = "/request/createbooking-v2-request/createbooking-v2-request.json"
      val testcase     = getClass.getResourceAsStream(testcaseFile)
      val uri          = "/v1/itinerary/create"

      val body = Source.fromInputStream(testcase, "utf8").getLines.mkString

      testcase.close()

      when(mockHandler.createV2(any(), any(), any(), any()))
        .thenReturn(Future.failed(new Throwable("Something inside createV2 failed")))

      createBookingRequest(uri, body) ~> itineraryRoute.routes ~> check {
        val response = responseAs[String]

        itineraryRoute.state.contains("bapi_deserialize")
        itineraryRoute.state.contains("bapi_total_metrics")
        status.isSuccess() shouldEqual true
        response shouldBe """{"success":false,"errorMessage":"Unexpected request body","errorCode":"Bad request","subErrorCode":5,"duplicateBookings":[],"status":{"bookingStatus":13,"bookingStatusCategory":2}}"""
        verify(mockHadoopMessageService, times(1)).sendBookingCreateLogMessage(
          HadoopMessageProcessName.CreateBookingResponse,
          "mock_request_id",
          23,
          """Failed to process CreationHandler.createV2 request: !!!NEXT JSON IS MALFORMED DUE TO MASKING OF SENSITIVE USER DATA!!!--->java.lang.Throwable:SomethinginsidecreateV2failed""",
          None,
          None
        )
      }
    }

    "deserialize request successfully" in {
      val testcaseFile = "/request/createbooking-v2-request/createbooking-v2-request.json"
      val testcase     = getClass.getResourceAsStream(testcaseFile)
      val uri          = "/v1/itinerary/create"

      val body = Source.fromInputStream(testcase, "utf8").getLines.mkString

      testcase.close()

      when(mockHandler.createV2(any(), any(), any(), any())).thenReturn(
        Future.successful(
          CreateBookingResponse(
            success = true,
            hotelRooms = None,
            itinerary = None,
            status = BookingServerStatus.Created
          )
        )
      )

      createBookingRequest(uri, body) ~> itineraryRoute.routes ~> check {
        status.isSuccess() shouldBe true
      }
    }
  }

}
