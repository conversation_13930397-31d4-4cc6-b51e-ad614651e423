package com.agoda.bapi.server.service

import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.token.Money
import com.agoda.bapi.common.model.consumerFintech.ConsumerFintechRequirement
import com.agoda.bapi.common.model.consumerFintech.products.cancelAndRebookV3.CancelAndRebookRequirement
import org.mockito.Mockito.when
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

class ConsumerFintechServiceSpec extends AsyncWordSpec with Matchers with MockitoSugar {
  "getConsumerFintechRequirement" should {
    "Return successful response" in {
      val consumerFintechRequest = ConsumerFintechRequest(
        product = ConsumerFintechProductRequest(
          cancelAndRebook = Some(
            ConsumerFintechCancelAndRebookProductRequest(
              TokenMessage(
                token =
                  "9tV5wb4y7sAhZofR4VcUAQnnxVUzm3+oclVH6TULK6ArqHscOD4JSLN+oHJkNwzfzMPgzYbrP0Kh1MsBEAD9TaOMTXnra0Rw26Hc/Gwv/NrlE6Pxgm8SD4ViGYd31tFJQ7V8qPJCFuNKhaOMTR1GEsIQAQyTQYSoWOCDQUw4oiPjOpKI0pxfNireSt4LMOoEMJcWPSlgOPxt4pzOO+mZtIm7VIv+tuyYOOssA+PjjhqcZB1hW4h9tZCZ0yujMYP7D2to+7/zGixptqcZNh8OY9FmGo/otrlI5dyBBUMIdHVioLj+pEnznsjsQQ0TY5nh3mMZf64otSDis0ZXl/Q8IGhWMdiM2kM4UIusoqDF5uyY57DTbMLzAqE91PSEsSZfFllkJtf217PRSK4l56/HggKBTowrJD4W1uHON86N4WaSy+gX28tRwjmEwktryG5Y+q6Y1ydl61UbEEnbseWi0mYvqC1YMXBxt/mLf5gmCxQ=",
                version = 1
              )
            )
          )
        )
      )

      val setupBookingRequest    = mockSetupBookingRequestWithConsumerFintechRequest(consumerFintechRequest)
      val consumerFintechService = new ConsumerFintechServiceImpl
      consumerFintechService.getConsumerFintechRequirement(setupBookingRequest).map { res =>
        res shouldBe Some(
          ConsumerFintechRequirement(
            cancelAndRebookRequirement = Some(
              CancelAndRebookRequirement(
                originalBookingId = 13234282,
                originalItineraryId = 49339483,
                saleInclusive = Money(1250.26, "USD"),
                roomTypeId = 1,
                originalAgodaCash = Some(1.2f),
                originalCashback = Some(1.3f),
                originalPromoAmount = Some(1.4f),
                originalSellIn = None,
                originalUsdToRequestExchangeRate = Some(20.0f),
                originalSellInUsd = Some(1.5f)
              )
            )
          )
        )
      }
    }

    "Return successful with None requirement if no encrypted rebook bookingId" in {
      val consumerFintechRequest = ConsumerFintechRequest(
        product = ConsumerFintechProductRequest(
          cancelAndRebook = None
        )
      )
      val setupBookingRequest    = mockSetupBookingRequestWithConsumerFintechRequest(consumerFintechRequest)
      val consumerFintechService = new ConsumerFintechServiceImpl
      consumerFintechService.getConsumerFintechRequirement(setupBookingRequest).map { res =>
        res shouldBe None
      }
    }

    "Return failure if token provided is incorrect" in {
      val consumerFintechRequest = ConsumerFintechRequest(
        product = ConsumerFintechProductRequest(
          cancelAndRebook = Some(
            ConsumerFintechCancelAndRebookProductRequest(
              TokenMessage(
                token = "I'm not the valid bookingIdToken and you knew that :P",
                version = 1
              )
            )
          )
        )
      )
      val setupBookingRequest    = mockSetupBookingRequestWithConsumerFintechRequest(consumerFintechRequest)
      val consumerFintechService = new ConsumerFintechServiceImpl
      consumerFintechService.getConsumerFintechRequirement(setupBookingRequest).failed.map { throwable =>
        throwable.getMessage shouldBe "token I'm not the valid bookingIdToken and you knew that :P with version 1 is invalid"
      }
    }

    def mockSetupBookingRequestWithConsumerFintechRequest(consumerFintechRequest: ConsumerFintechRequest) = {
      val mockPropertyRequestItem                            = mock[PropertyRequestItem]
      val mockPropertyRequestItems: Seq[PropertyRequestItem] = Seq(mockPropertyRequestItem)
      val mockProductsRequest: ProductsRequest               = mock[ProductsRequest]
      val setupBookingRequest: SetupBookingRequest           = mock[SetupBookingRequest]
      when(setupBookingRequest.productsRequest).thenReturn(mockProductsRequest)
      when(mockProductsRequest.propertyRequests).thenReturn(mockPropertyRequestItems)
      when(mockPropertyRequestItem.consumerFintechRequest).thenReturn(Some(consumerFintechRequest))
      setupBookingRequest
    }
  }
}
