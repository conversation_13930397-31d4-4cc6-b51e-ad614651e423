package com.agoda.bapi.server.facades.aggregator

import com.agoda.bapi.common.message.{PropertySearchCriteria, SimplifiedRoomSelectionRequest}
import com.agoda.bapi.common.message.setupBooking.{ProductsRequest, PropertyRequestItem}
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.model.creation.{PriceChangeOnPollingStatus, PriceChangePerRoomStatus}
import com.agoda.bapi.common.token.PropertySetupBookingToken
import com.agoda.bapi.creation.service.observability.LogContext
import com.agoda.bapi.server.facades.helpers.SetupBookingContextFixture
import com.agoda.bapi.server.handler.context.SetupBookingSessionContext
import com.agoda.bapi.server.model.{BookingPropertiesData, ProductData}
import com.agoda.bapi.server.service.PapiPropertyStatus
import com.agoda.bapi.server.service.allotment.{AllotmentPreCheckStatus, AllotmentStatus}
import mocks.{ProductToken<PERSON>ock<PERSON>el<PERSON>, PropertyMock, SetupBookingWithAlternativesMock}
import models.starfruit.PropertyPricingJsonProtocol.ResponseStateToken
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.when
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar
import transformers.DFMetaResult

class PropertyProductTokenAggregatorMixinSpec
    extends AsyncWordSpec
    with MockitoSugar
    with Matchers
    with PropertyMock
    with ProductTokenMockHelper
    with PropertyProductTokenAggregatorMixin {
  "toPropertySetupModel" should {
    val expectedToken       = TokenMessage(defaultMultiBookingTokenString, 1)
    val overrideMockProduct = MockProductUtils.createMockProduct()
    val mockPropertyRequest = mock[PropertyRequestItem]
    val mockProductRequest  = mock[ProductsRequest]
    val mockProductData     = mock[ProductData]
    val defaultPropertyId   = 1234567L
    val searchCriteriaMock  = mock[PropertySearchCriteria]
    when(searchCriteriaMock.simplifiedRoomSelectionRequest).thenReturn(None)
    when(mockProductRequest.bookingToken).thenReturn(None)
    when(mockPropertyRequest.propertySearchCriteria).thenReturn(searchCriteriaMock)
    when(mockProductRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
    "without PreCheck on Alternatives" should {
      new SetupBookingContextFixture {
        "should clear PriceGuaranteeToken when AllotmentStatus is PriceChanged" in {
          val input      = overrideMockProduct
          val propertyId = input.properties.head.id
          val allotmentStatus = Seq(
            AllotmentPreCheckStatus(
              productKey = input.properties.head.id,
              roomIdentifier = None,
              requestId = Some("absRequestId123"),
              status = AllotmentStatus.PriceChanged
            )
          )
          val result = toPropertySetupModel(overrideMockProduct, mockProductRequest, allotmentStatus, None)
          result.get(propertyId).get.productToken shouldBe None
          result.get(propertyId).get.absRequestId shouldBe Some("absRequestId123")
          result.get(propertyId).get.allotmentResult shouldBe Some(AllotmentStatus.PriceChanged)
          result.get(propertyId).get.isRoomHasSwapped shouldBe Some(false)
          result.get(propertyId).get.originalRoomIdentifier shouldBe None
        }
      }
      new SetupBookingContextFixture {
        "should insert isRoomHasSwapped and OriginalRoomIdentifier when original room swapped with AlternativeRoom at 1st papi response" in {
          val id                     = "x1"
          val originalRoomIdentifier = "room-identifier-original-1"
          val propertySearchCriteria = mock[PropertySearchCriteria]
          when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))
          when(propertySearchCriteria.roomIdentifier).thenReturn(originalRoomIdentifier)
          when(propertySearchCriteria.simplifiedRoomSelectionRequest).thenReturn(
            Some(SimplifiedRoomSelectionRequest(roomIdentifier = originalRoomIdentifier, alternativeOptIn = None))
          )

          when(mockProductRequest.propertyRequests).thenReturn(
            Seq(PropertyRequestItem(id, propertySearchCriteria, None))
          )
          when(mockProductRequest.bookingToken).thenReturn(None)
          val propertySession =
            Map(id -> PropertySetupBookingToken(id, Some("123"), Some(2), Some("df-token"), Some(1L), None, None))
          when(setupBookingContext.session)
            .thenReturn(
              SetupBookingSessionContext(
                propertySession,
                None,
                expectedTimeStamp
              )
            )
          val papiProperties = mock[transformers.Properties]
          val papiProperty = createMockPropertyWithRoomUid(
            1,
            Some("room-uid"),
            Some("alternative-room-identifier"),
            isOnlyAlternativeRoom = Some(true)
          )
          when(papiProperties.property) thenReturn Seq(papiProperty)
          when(papiProperties.dfMetaResult) thenReturn DFMetaResult(
            Some(ResponseStateToken("", true, Some("pricingRequestId-1")))
          )
          when(mockProductData.properties) thenReturn Seq(
            BookingPropertiesData(
              id,
              content = "",
              papiProperties = Some(papiProperties),
              packageRequest = None,
              papiPropertyStatus = PapiPropertyStatus.Ok,
              selectedChargeOption = None,
              propertySearchCriteria = Some(propertySearchCriteria)
            )
          )

          val allotmentStatus = Seq(
            AllotmentPreCheckStatus(
              productKey = id,
              roomIdentifier = None,
              requestId = Some("absRequestId123"),
              status = AllotmentStatus.Processing
            )
          )
          val result = toPropertySetupModel(mockProductData, mockProductRequest, allotmentStatus, None)
          result.get(id).get.productToken shouldNot be(None)
          result.get(id).get.absRequestId shouldBe Some("absRequestId123")
          result.get(id).get.allotmentResult shouldBe Some(AllotmentStatus.Processing)
          result.get(id).get.isRoomHasSwapped shouldBe Some(true)
          result.get(id).get.originalRoomIdentifier shouldBe Some(originalRoomIdentifier)
        }
      }
      new SetupBookingContextFixture {
        "should not overwrite isRoomHasSwapped and originalRoomIdentifier if already set" in {
          val id                       = "x1"
          val originalRoomIdentifier   = "room-identifier-original-1"
          val propertySearchCriteria   = mock[PropertySearchCriteria]
          val expectedIsRoomHasSwapped = Some(true)
          when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))
          when(propertySearchCriteria.roomIdentifier).thenReturn(originalRoomIdentifier)

          when(mockProductRequest.propertyRequests).thenReturn(
            Seq(PropertyRequestItem(id, propertySearchCriteria, None))
          )
          when(mockProductRequest.bookingToken).thenReturn(Some(expectedToken))
          val propertySession =
            Map(
              id -> PropertySetupBookingToken(
                id,
                Some("123"),
                Some(2),
                Some("df-token"),
                Some(1L),
                isRoomHasSwapped = expectedIsRoomHasSwapped,
                originalRoomIdentifier = Some(originalRoomIdentifier)
              )
            )
          when(setupBookingContext.session)
            .thenReturn(
              SetupBookingSessionContext(
                propertySession,
                None,
                expectedTimeStamp
              )
            )
          when(setupBookingContext.getIsRoomHasSwapped(any())).thenReturn(expectedIsRoomHasSwapped)
          when(setupBookingContext.getOriginalRoomIdentifier(any())).thenReturn(Some(originalRoomIdentifier))
          val papiProperties = mock[transformers.Properties]
          val papiProperty = createMockPropertyWithRoomUid(
            1,
            Some("room-uid"),
            Some(originalRoomIdentifier),
            isOnlyAlternativeRoom = Some(false)
          )
          when(papiProperties.property) thenReturn Seq(papiProperty)
          when(papiProperties.dfMetaResult) thenReturn DFMetaResult(
            Some(ResponseStateToken("", true, Some("pricingRequestId-1")))
          )
          when(mockProductData.properties) thenReturn Seq(
            BookingPropertiesData(
              id,
              content = "",
              papiProperties = Some(papiProperties),
              packageRequest = None,
              papiPropertyStatus = PapiPropertyStatus.Ok,
              selectedChargeOption = None
            )
          )

          val allotmentStatus = Seq(
            AllotmentPreCheckStatus(
              productKey = id,
              roomIdentifier = None,
              requestId = Some("absRequestId123"),
              status = AllotmentStatus.Processing
            )
          )
          val result = toPropertySetupModel(mockProductData, mockProductRequest, allotmentStatus, None)
          result.get(id).get.productToken shouldNot be(None)
          result.get(id).get.absRequestId shouldBe Some("absRequestId123")
          result.get(id).get.allotmentResult shouldBe Some(AllotmentStatus.Processing)
          result.get(id).get.isRoomHasSwapped shouldBe expectedIsRoomHasSwapped
          result.get(id).get.originalRoomIdentifier shouldBe Some(originalRoomIdentifier)
        }
      }
      new SetupBookingContextFixture {
        "should clear PriceGuaranteeToken when Property price is not changed" in {
          val input      = overrideMockProduct
          val propertyId = input.properties.head.id
          val isPriceChangedPropertyRequest =
            Some(PriceChangeOnPollingStatus(priceChangeStatus = Map(input.properties.head.id -> false)))
          val allotmentStatus = Seq(
            AllotmentPreCheckStatus(
              productKey = input.properties.head.id,
              roomIdentifier = None,
              requestId = Some("absRequestId123"),
              status = AllotmentStatus.Processing
            )
          )
          val result = toPropertySetupModel(
            overrideMockProduct,
            mockProductRequest,
            allotmentStatus,
            isPriceChangedPropertyRequest
          )
          result.get(propertyId).get.productToken shouldBe None
          result.get(propertyId).get.absRequestId shouldBe Some("absRequestId123")
          result.get(propertyId).get.allotmentResult shouldBe Some(AllotmentStatus.Processing)
        }
      }
      new SetupBookingContextFixture {
        "should not clear PriceGuaranteeToken when Property price is changed" in {
          val input      = overrideMockProduct
          val propertyId = input.properties.head.id
          val isPriceChangedPropertyRequest =
            Some(PriceChangeOnPollingStatus(priceChangeStatus = Map(input.properties.head.id -> true)))
          val allotmentStatus = Seq(
            AllotmentPreCheckStatus(
              productKey = input.properties.head.id,
              roomIdentifier = None,
              requestId = Some("absRequestId123"),
              status = AllotmentStatus.Processing
            )
          )
          val result = toPropertySetupModel(
            overrideMockProduct,
            mockProductRequest,
            allotmentStatus,
            isPriceChangedPropertyRequest
          )
          result.get(propertyId).get.productToken shouldNot be(None)
          result.get(propertyId).get.absRequestId shouldBe Some("absRequestId123")
          result.get(propertyId).get.allotmentResult shouldBe Some(AllotmentStatus.Processing)
        }
      }
      ()
    }
    "with PreCheck on Alternatives" should {

      new SetupBookingWithAlternativesMock {
        new SetupBookingContextFixture {
          "clear PriceGuaranteeToken when selected room got Price Changed" in {
            /* original - available bf upsell - price changed (selected) Note - DF price is not yet updated result =
             * clear price guarantee token */
            when(featureAware.isPreCheckOnAlternatives) thenReturn true

            val inputProductData   = productDataPropertyWithAlternativesSelectBFUpSell
            val propertyProductKey = inputProductData.properties.head.id
            val allotmentStatus = Seq(
              AllotmentPreCheckStatus(
                productKey = propertyProductKey,
                roomIdentifier = Some("roomiden-original"),
                requestId = Some("request-original"),
                status = AllotmentStatus.Available
              ),
              AllotmentPreCheckStatus(
                productKey = propertyProductKey,
                roomIdentifier = Some("roomiden-breakfastupsell"),
                requestId = Some("request-breakfastupsell"),
                status = AllotmentStatus.PriceChanged
              )
            )

            val priceChangeStatusOnRooms = Seq(
              PriceChangePerRoomStatus(propertyProductKey, "roomiden-breakfastupsell", isPriceChanged = false)
            )

            val result = toPropertySetupModel(
              inputProductData,
              mockProductRequest,
              allotmentStatus,
              None,
              priceChangeStatusOnRooms
            )
            result.get(propertyProductKey).get.productToken shouldBe None
            result.get(propertyProductKey).get.absRequestId shouldBe None
            result.get(propertyProductKey).get.allotmentResult shouldBe None
            result.get(propertyProductKey).get.roomAllotmentResults.size shouldBe 2
          }
        }
        new SetupBookingContextFixture {
          "not clear PriceGuaranteeToken when Price Changed is not on selected room" in {
            /* original - available (selected) bf upsell - price changed Note - DF price is not yet updated result = NOT
             * clear price guarantee token */
            when(featureAware.isPreCheckOnAlternatives) thenReturn true

            val inputProductData   = productDataPropertyWithAlternativesSelectOriginal
            val propertyProductKey = inputProductData.properties.head.id
            val allotmentStatus = Seq(
              AllotmentPreCheckStatus(
                productKey = propertyProductKey,
                roomIdentifier = Some("roomiden-original"),
                requestId = Some("request-original"),
                status = AllotmentStatus.Available
              ),
              AllotmentPreCheckStatus(
                productKey = propertyProductKey,
                roomIdentifier = Some("roomiden-breakfastupsell"),
                requestId = Some("request-breakfastupsell"),
                status = AllotmentStatus.PriceChanged
              )
            )

            val result = toPropertySetupModel(
              inputProductData,
              mockProductRequest,
              allotmentStatus,
              None,
              Seq.empty
            )
            result.get(propertyProductKey).get.productToken.nonEmpty shouldBe true
            result.get(propertyProductKey).get.absRequestId shouldBe None
            result.get(propertyProductKey).get.allotmentResult shouldBe None
            result.get(propertyProductKey).get.roomAllotmentResults.size shouldBe 2
          }
        }
        new SetupBookingContextFixture {
          "not clear PriceGuaranteeToken when selected room got Price Changed but DF price is updated" in {
            /* original - available bf upsell - price changed (selected) Note - DF price is already updated result = NOT
             * clear price guarantee token */
            when(featureAware.isPreCheckOnAlternatives) thenReturn true

            val inputProductData   = productDataPropertyWithAlternativesSelectBFUpSell
            val propertyProductKey = inputProductData.properties.head.id
            val allotmentStatus = Seq(
              AllotmentPreCheckStatus(
                productKey = propertyProductKey,
                roomIdentifier = Some("roomiden-original"),
                requestId = Some("request-original"),
                status = AllotmentStatus.Available
              ),
              AllotmentPreCheckStatus(
                productKey = propertyProductKey,
                roomIdentifier = Some("roomiden-breakfastupsell"),
                requestId = Some("request-breakfastupsell"),
                status = AllotmentStatus.PriceChanged
              )
            )

            val priceChangeStatusOnRooms = Seq(
              PriceChangePerRoomStatus(propertyProductKey, "roomiden-breakfastupsell", isPriceChanged = true)
            )

            val result = toPropertySetupModel(
              inputProductData,
              mockProductRequest,
              allotmentStatus,
              None,
              priceChangeStatusOnRooms
            )
            result.get(propertyProductKey).get.productToken.nonEmpty shouldBe true
            result.get(propertyProductKey).get.absRequestId shouldBe None
            result.get(propertyProductKey).get.allotmentResult shouldBe None
            result.get(propertyProductKey).get.roomAllotmentResults.size shouldBe 2
          }
        }
      }
      ()
    }
  }
}
