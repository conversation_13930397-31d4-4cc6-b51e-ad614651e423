package com.agoda.bapi.server.facades.flight

import com.agoda.bapi.common.message.setupBooking.{CampaignInfoRequest, ConfirmPriceRequest, FlightRequestItem}
import com.agoda.bapi.common.model.WhiteLabel
import com.agoda.bapi.server.facades.helpers.{SetupBookingContextFixture, SetupBookingRequestFixture}
import com.agoda.bapi.server.handler.context.RetryPaymentContext
import com.agoda.bapi.server.model.FlightConfirmationData
import com.agoda.bapi.server.repository.FlightsRepository
import com.agoda.upi.models.common.Arrangement
import com.agoda.upi.models.enums.{ArrangementEntries, ItemEntries, ProductEntries}
import com.agoda.upi.models.request.{CartBaseRequest, CartItemMetadata, CartMetadata}
import org.mockito.ArgumentMatchers.{any, endsWith, eq => eqTo}
import org.mockito.Mockito.{never, verify, when}
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future

class FlightsCartComposerMixinSpec extends AsyncWordSpec with MockitoSugar with Matchers with FlightsCartComposerMixin {

  override val flightsRepository: FlightsRepository = mock[FlightsRepository]

  val usdCurrency = "USD"

  "retrieveFlightsData" should {
    new SetupBookingRequestFixture with SetupBookingContextFixture {
      "when it's retry payment flow, should retrieve from retry payment context instead of calling FLAPI" in {
        val flightsRequest1             = FlightRequestItem(Some("test"), Some(ConfirmPriceRequest("test", "test")), None)
        val flightsRequest2             = FlightRequestItem(Some("test2"), Some(ConfirmPriceRequest("test", "test")), None)
        val flightsRequests             = Seq(flightsRequest1, flightsRequest2)
        val mockFlightConfirmationData1 = mock[FlightConfirmationData]
        val mockFlightConfirmationData2 = mock[FlightConfirmationData]
        when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
        when(setupBookingContext.retryPaymentContext).thenReturn(
          Some(
            RetryPaymentContext(
              flights = Seq(mockFlightConfirmationData1, mockFlightConfirmationData2)
            )
          )
        )
        val resultF = retrieveFlightsData(
          enabledFeatures = None,
          items = flightsRequests,
          chargeCurrency = usdCurrency,
          setupBookingRequest = setupBookingRequest,
          userContext = None,
          packageRequest = None,
          selectedCreditCardTypeId = None,
          campaignInfo = None,
          campaignsIds = None,
          loyaltyRequestOpt = None,
          distributePointsResponseOpt = None,
          aabInfo = None,
          isRetryPayment = Some(true)
        )(setupBookingContext)

        resultF.map { result =>
          verify(flightsRepository, never()).retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
          result shouldBe Map(0 -> mockFlightConfirmationData1, 1 -> mockFlightConfirmationData2)
        }
      }
    }

    new SetupBookingRequestFixture with SetupBookingContextFixture {
      "when it's retry payment flow, but no retryPaymentContext should throw error" in {
        val flightsRequest1 = FlightRequestItem(Some("test"), Some(ConfirmPriceRequest("test", "test")), None)
        val flightsRequest2 = FlightRequestItem(Some("test2"), Some(ConfirmPriceRequest("test", "test")), None)
        val flightsRequests = Seq(flightsRequest1, flightsRequest2)
        when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
        when(setupBookingContext.retryPaymentContext).thenReturn(None)

        val resultF = retrieveFlightsData(
          enabledFeatures = None,
          items = flightsRequests,
          chargeCurrency = usdCurrency,
          setupBookingRequest = setupBookingRequest,
          userContext = None,
          packageRequest = None,
          selectedCreditCardTypeId = None,
          campaignInfo = None,
          campaignsIds = None,
          loyaltyRequestOpt = None,
          distributePointsResponseOpt = None,
          aabInfo = None,
          isRetryPayment = Some(true)
        )(setupBookingContext)

        resultF.failed.map { e =>
          verify(flightsRepository, never()).retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())

          e shouldBe an[IllegalArgumentException]
          e.getMessage shouldBe "Retry payment flow - but no retry payment context found."
        }
      }
    }

    new SetupBookingRequestFixture with SetupBookingContextFixture {
      "when it's retry payment flow, but no flights in retry payment context should throw error" in {
        val flightsRequest1 = FlightRequestItem(Some("test"), Some(ConfirmPriceRequest("test", "test")), None)
        val flightsRequest2 = FlightRequestItem(Some("test2"), Some(ConfirmPriceRequest("test", "test")), None)
        val flightsRequests = Seq(flightsRequest1, flightsRequest2)
        when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
        when(setupBookingContext.retryPaymentContext).thenReturn(
          Some(
            RetryPaymentContext(
              flights = Seq.empty
            )
          )
        )

        val resultF = retrieveFlightsData(
          enabledFeatures = None,
          items = flightsRequests,
          chargeCurrency = usdCurrency,
          setupBookingRequest = setupBookingRequest,
          userContext = None,
          packageRequest = None,
          selectedCreditCardTypeId = None,
          campaignInfo = None,
          campaignsIds = None,
          loyaltyRequestOpt = None,
          distributePointsResponseOpt = None,
          aabInfo = None,
          isRetryPayment = Some(true)
        )(setupBookingContext)

        resultF.failed.map { e =>
          verify(flightsRepository, never()).retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())

          e shouldBe an[IllegalArgumentException]
          e.getMessage shouldBe "Retry payment flow - but no flights in retry payment context."
        }
      }
    }

    new SetupBookingRequestFixture with SetupBookingContextFixture {
      "return multi flights data with index correctly" in {
        val flightsRequest1             = FlightRequestItem(Some("test"), Some(ConfirmPriceRequest("test", "test")), None)
        val flightsRequest2             = FlightRequestItem(Some("test2"), Some(ConfirmPriceRequest("test", "test")), None)
        val flightsRequests             = Seq(flightsRequest1, flightsRequest2)
        val mockFlightConfirmationData1 = mock[FlightConfirmationData]
        val mockFlightConfirmationData2 = mock[FlightConfirmationData]
        when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.ClubTravel)
        when(
          flightsRepository
            .retrieveFlightConfirmationData(
              eqTo(flightsRequest1),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
        ).thenReturn(Future.successful(mockFlightConfirmationData1))
        when(
          flightsRepository
            .retrieveFlightConfirmationData(
              eqTo(flightsRequest2),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
        ).thenReturn(Future.successful(mockFlightConfirmationData2))
        val resultF = retrieveFlightsData(
          enabledFeatures = None,
          items = flightsRequests,
          chargeCurrency = usdCurrency,
          setupBookingRequest = setupBookingRequest,
          userContext = None,
          packageRequest = None,
          selectedCreditCardTypeId = None,
          campaignInfo = None,
          campaignsIds = None,
          loyaltyRequestOpt = None,
          distributePointsResponseOpt = None,
          aabInfo = None
        )(setupBookingContext)

        resultF.map { result =>
          result shouldBe Map(0 -> mockFlightConfirmationData1, 1 -> mockFlightConfirmationData2)
        }
      }
    }

    new SetupBookingRequestFixture with SetupBookingContextFixture {
      "return multi flights data with index and apply promo to first flight item only for ClubTravel campaigns correctly" in {
        val flightsRequest1             = FlightRequestItem(Some("test"), Some(ConfirmPriceRequest("test", "test")), None)
        val flightsRequest2             = FlightRequestItem(Some("test2"), Some(ConfirmPriceRequest("test", "test")), None)
        val flightsRequests             = Seq(flightsRequest1, flightsRequest2)
        val mockFlightConfirmationData1 = mock[FlightConfirmationData]
        val mockFlightConfirmationData2 = mock[FlightConfirmationData]
        when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.ClubTravel)
        when(
          flightsRepository
            .retrieveFlightConfirmationData(
              eqTo(flightsRequest1),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Some(CampaignInfoRequest(id = Some(1), cid = 45, promotionCode = "CLUBTRAVELPROMO"))),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
        ).thenReturn(Future.successful(mockFlightConfirmationData1))
        when(
          flightsRepository
            .retrieveFlightConfirmationData(
              eqTo(flightsRequest2),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
        ).thenReturn(Future.successful(mockFlightConfirmationData2))
        val resultF = retrieveFlightsData(
          enabledFeatures = None,
          items = flightsRequests,
          chargeCurrency = usdCurrency,
          setupBookingRequest = setupBookingRequest,
          userContext = None,
          packageRequest = None,
          selectedCreditCardTypeId = None,
          campaignInfo = Some(CampaignInfoRequest(id = Some(1), cid = 45, promotionCode = "CLUBTRAVELPROMO")),
          campaignsIds = None,
          loyaltyRequestOpt = None,
          distributePointsResponseOpt = None,
          aabInfo = None
        )(setupBookingContext)

        resultF.map { result =>
          result shouldBe Map(0 -> mockFlightConfirmationData1, 1 -> mockFlightConfirmationData2)
        }
      }
    }

    "send cartRequest from function parameter to confirm price" when {
      new SetupBookingRequestFixture with SetupBookingContextFixture {
        "it's the first confirm price call" in {
          val flightsRequest1             = FlightRequestItem(Some("test"), Some(ConfirmPriceRequest("test", "test")), None)
          val flightsRequests             = Seq(flightsRequest1)
          val mockFlightConfirmationData1 = mock[FlightConfirmationData]
          val cartRequest = CartBaseRequest(
            token = Some("cartToken"),
            arrangement = Some(
              Arrangement(
                `type` = ArrangementEntries.OrchestrationChange,
                refId = None
              )
            ),
            sourceId = None,
            srcId = Some("srcId"),
            meta = Some(
              CartMetadata(
                productEntry = Some(ProductEntries.CartProduct),
                itemMetadata = List(
                  CartItemMetadata(
                    item = ItemEntries.Property,
                    itemsSelected = 2
                  ),
                  CartItemMetadata(
                    item = ItemEntries.Flight,
                    itemsSelected = 1
                  )
                )
              )
            )
          )
          when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.ClubTravel)
          when(
            flightsRepository
              .retrieveFlightConfirmationData(
                eqTo(flightsRequest1),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(any())
          ).thenReturn(Future.successful(mockFlightConfirmationData1))
          val resultF = retrieveFlightsData(
            enabledFeatures = None,
            items = flightsRequests,
            chargeCurrency = usdCurrency,
            setupBookingRequest = setupBookingRequest,
            userContext = None,
            packageRequest = None,
            selectedCreditCardTypeId = None,
            campaignInfo = None,
            campaignsIds = None,
            loyaltyRequestOpt = None,
            distributePointsResponseOpt = None,
            cartRequest = Some(cartRequest),
            aabInfo = None
          )(setupBookingContext)

          resultF.map { result =>
            verify(flightsRepository).retrieveFlightConfirmationData(
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Some(cartRequest)),
              any()
            )(any())
            result shouldBe Map(0 -> mockFlightConfirmationData1)
          }
        }
      }
      ()
    }
  }
}
