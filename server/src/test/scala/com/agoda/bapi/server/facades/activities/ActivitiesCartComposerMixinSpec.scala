package com.agoda.bapi.server.facades.activities

import com.agoda.bapi.common.message.setupBooking.{ActivityConfirmPriceRequest, ActivityRequestItem}
import com.agoda.bapi.common.model.CurrencyCode
import com.agoda.bapi.common.model.activity.ActivityConfirmationData
import com.agoda.bapi.server.facades.helpers.SetupBookingContextFixture
import com.agoda.bapi.server.service.ActivityService
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.when
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar
import org.mockito.ArgumentMatchers.{eq => eqTo}

import scala.concurrent.Future

class ActivitiesCartComposerMixinSpec
    extends AsyncWordSpec
    with MockitoSugar
    with Matchers
    with SetupBookingContextFixture
    with ActivitiesCartComposerMixin {

  override val activityService: ActivityService = mock[ActivityService]

  "retrieveActivitiesData" should {
    "return multi activities data with index correctly" in {
      val activityRequest1                 = ActivityRequestItem("1", ActivityConfirmPriceRequest("", ""))
      val activityRequest2                 = ActivityRequestItem("2", ActivityConfirmPriceRequest("", ""))
      val activityRequests                 = Seq(activityRequest1, activityRequest2)
      val mockActivityConfirmationData1    = mock[ActivityConfirmationData]
      val mockActivityConfirmationData2    = mock[ActivityConfirmationData]
      val mockChargeCurrency: CurrencyCode = "USD"
      when(
        activityService.getActivityConfirmationData(
          eqTo(activityRequest1),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
      ).thenReturn(Future.successful(mockActivityConfirmationData1))
      when(
        activityService.getActivityConfirmationData(
          eqTo(activityRequest2),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
      ).thenReturn(Future.successful(mockActivityConfirmationData2))
      val resultF = retrieveActivitiesData(
        activityRequestItem = Some(activityRequests),
        loyaltyRequest = None,
        experimentData = None,
        distributePointsResponseOpt = None,
        campaignInfoRequest = None,
        chargeCurrency = mockChargeCurrency
      )

      resultF.map { result =>
        result shouldBe Map(0 -> mockActivityConfirmationData1, 1 -> mockActivityConfirmationData2)
      }
    }
  }
}
