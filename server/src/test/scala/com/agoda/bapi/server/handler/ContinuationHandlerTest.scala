package com.agoda.bapi.server.handler

import com.agoda.bapi.common.MessageService
import com.agoda.bapi.common.config.KillSwitches
import com.agoda.bapi.common.directive.{BookingAPIHttpHeader, HttpHeaderBase}
import com.agoda.bapi.common.handler.{RequestContext, RequestContextBuilder}
import com.agoda.bapi.common.message.BookingCreationFlowContext
import com.agoda.bapi.common.message.creation._
import com.agoda.bapi.common.model.{StatusToken, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.service.{FeatureAware, MessagesBag}
import com.agoda.bapi.common.util.ServerUtils
import com.agoda.bapi.creation.exception.DuplicatedContinueProcessingException
import com.agoda.bapi.creation.service._
import com.agoda.bapi.server.proxy.BookingCreationByDcProxyImpl
import com.agoda.bapi.server.service.CommonBookingsService
import com.agoda.bapi.server.validator.ContinuePaymentRequestValidator
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import mocks.{MeasurementStubs, RequestContextMock}
import org.mockito.ArgumentMatchers.{any, eq => meq}
import org.mockito.Mockito._
import org.scalatest.concurrent.Eventually
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{BeforeAndAfter, BeforeAndAfterEach}
import org.scalatestplus.mockito.MockitoSugar

import java.nio.charset.StandardCharsets
import java.util.Base64
import scala.concurrent.Future
import scala.concurrent.duration._

class ContinuationHandlerTest
    extends AsyncWordSpec
    with MockitoSugar
    with BeforeAndAfter
    with RequestContextMock
    with BeforeAndAfterEach
    with Eventually {

  private val requestValidator          = mock[ContinuePaymentRequestValidator]
  private val continueCreationService   = mock[ContinueCreationService]
  private val commonBookingsService     = mock[CommonBookingsService]
  private val hadoopMessagingServieMock = mock[HadoopMessagingService]
  private val contextBuilder            = mock[RequestContextBuilder]
  private val bookingCreationByDcProxy  = mock[BookingCreationByDcProxyImpl]
  private val featureAwareMock          = mock[FeatureAware]
  private val killSwitches              = mock[KillSwitches]
  private val messageService            = mock[MessageService]

  private val bcreEndpointMessageService = new BcreEndpointMessageServiceImpl(messageService, killSwitches)

  private val baseHeader = BookingAPIHttpHeader(
    "/create",
    1,
    "2",
    "qwe",
    WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration),
    "0.0.0.0"
  )

  when(contextBuilder.build(any[BookingCreationFlowContext], any[HttpHeaderBase], any[Option[String]], any(), any()))
    .thenReturn(Future.successful(newRequestContext()))

  private val successResponse = GetStatusResponse(
    success = true,
    itinerary = Some(Itinerary(2, Seq(), Seq(FlightBooking(0, CreatedBookingStatus.BookingProcessing))))
  )

  val handler = new ContinuationHandlerImpl(
    contextBuilder,
    continueCreationService,
    commonBookingsService,
    hadoopMessagingServieMock,
    requestValidator,
    bookingCreationByDcProxy,
    bcreEndpointMessageService
  )

  override def beforeEach(): Unit = {
    reset(
      bookingCreationByDcProxy,
      continueCreationService,
      commonBookingsService,
      hadoopMessagingServieMock,
      featureAwareMock,
      requestValidator
    )

    when(hadoopMessagingServieMock.sendBookingContinueLogMessage(any(), any(), any(), any(), any()))
      .thenReturn(Future.successful())
    when(requestValidator.validateJtbWhitelabelId(any(), any())(any())).thenReturn(false)
  }

  "Continue itinerary payment" should {

    "validation failed return response correctly" in {
      val expectedResponse = mock[GetStatusResponse]
      when(requestValidator.validateContinuePaymentRequest(any())).thenReturn(Some(expectedResponse))
      val request = mock[ContinuePaymentRequest]
      when(request.bookingContext).thenReturn(None)
      when(request.deviceContext).thenReturn(None)
      val header = mock[BookingAPIHttpHeader]
      handler
        .continueItineraryPayment(header, ip = "", request)
        .map { result =>
          verify(bookingCreationByDcProxy, never()).continueCreation(any(), any())(any())
          verify(continueCreationService, never()).continueCreation(meq(request))(any())
          verify(commonBookingsService, never()).getItineraryStatus(any(), meq(Seq.empty))(any())
          assert(result == expectedResponse)
        }
    }

    "same DC should call service correctly" in {
      val expectedResponse = mock[GetStatusResponse]
      when(continueCreationService.continueCreation(any())(any())).thenReturn(Future.successful())
      when(commonBookingsService.getItineraryStatus(any(), any())(any()))
        .thenReturn(Future.successful(expectedResponse))
      when(requestValidator.validateContinuePaymentRequest(any())).thenReturn(None)
      val dc = ServerUtils.serverDc()
      val request = ContinuePaymentRequest(
        "CAQSQEJyMjFZcVppalVOZzh2UUc0c2ZKK3kxR0FQdnZQTkVONU9jMmk4YVYweUYyN0I3d0lmWW1XdVczd2J6WGVLUE0=",
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None
      )
      val expectedGetStatusReq = GetStatusRequest(
        request.statusToken,
        request.correlationId,
        request.userContext,
        request.bookingContext
      )
      handler
        .continueItineraryPayment(baseHeader, ip = "", request)
        .map { result =>
          verify(bookingCreationByDcProxy, never()).continueCreation(any(), any())(any())
          verify(continueCreationService, times(1)).continueCreation(meq(request))(any())
          verify(commonBookingsService, times(1)).getItineraryStatus(meq(expectedGetStatusReq), meq(Seq.empty))(any())
          assert(result == expectedResponse)
        }
    }

    "DuplicatedContinueProcessingException should be handled as processing error" in {
      val statusToken      = "CAQSQEJyMjFZcVppalVOZzh2UUc0c2ZKK3kxR0FQdnZQTkVONU9jMmk4YVYweUYyN0I3d0lmWW1XdVczd2J6WGVLUE0="
      val failure          = new DuplicatedContinueProcessingException("i should fail")
      val expectedResponse = GetStatusResponse.processingError(exception = failure, statusToken = statusToken)
      when(continueCreationService.continueCreation(any())(any())).thenReturn(Future.successful())
      when(commonBookingsService.getItineraryStatus(any(), any())(any())).thenReturn(Future.failed(failure))
      when(requestValidator.validateContinuePaymentRequest(any())).thenReturn(None)

      val request = ContinuePaymentRequest(
        statusToken = statusToken,
        postBackFields = None,
        borBookingResult = None,
        fraudResult = None,
        internalToken = None,
        correlationId = None,
        userContext = None,
        bookingContext = None,
        flightData = None,
        securityCode = None
      )
      val expectedGetStatusReq = GetStatusRequest(
        request.statusToken,
        request.correlationId,
        request.userContext,
        request.bookingContext
      )
      handler
        .continueItineraryPayment(baseHeader, ip = "", request)
        .map { result =>
          verify(bookingCreationByDcProxy, never()).continueCreation(any(), any())(any())
          verify(continueCreationService, times(1)).continueCreation(meq(request))(any())
          verify(commonBookingsService, times(1)).getItineraryStatus(meq(expectedGetStatusReq), meq(Seq.empty))(any())
          assert(result == expectedResponse)
        }
    }

    "same DC should call hadoop service correctly" in {
      val clientId      = 61
      val correlationId = Option("Correlation-ID")
      val updatedContext =
        newRequestContext.copy(
          featureAware = Some(featureAwareMock),
          clientId = clientId,
          correlationId = correlationId
        )
      when(
        contextBuilder.build(any[BookingCreationFlowContext], any[HttpHeaderBase], any[Option[String]], any(), any())
      )
        .thenReturn(Future.successful(updatedContext))
      val expectedResponse = mock[GetStatusResponse]
      when(continueCreationService.continueCreation(any())(any())).thenReturn(Future.successful())
      when(commonBookingsService.getItineraryStatus(any(), any())(any()))
        .thenReturn(Future.successful(expectedResponse))
      when(requestValidator.validateContinuePaymentRequest(any())).thenReturn(None)
      val dc = ServerUtils.serverDc()
      val request = ContinuePaymentRequest(
        "CAQSQEJyMjFZcVppalVOZzh2UUc0c2ZKK3kxR0FQdnZQTkVONU9jMmk4YVYweUYyN0I3d0lmWW1XdVczd2J6WGVLUE0=",
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None
      )
      val header = BookingAPIHttpHeader(
        "/create",
        1,
        "2",
        "qwe",
        WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration),
        "0.0.0.0"
      )
      val expectedGetStatusReq = GetStatusRequest(
        request.statusToken,
        request.correlationId,
        request.userContext,
        request.bookingContext
      )
      handler
        .continueItineraryPayment(header, ip = "", request)
        .map { result =>
          eventually(timeout(5.seconds)) {
            verify(bookingCreationByDcProxy, never()).continueCreation(any(), any())(any())
            verify(continueCreationService, times(1)).continueCreation(meq(request))(any())
            verify(commonBookingsService, times(1)).getItineraryStatus(meq(expectedGetStatusReq), meq(Seq.empty))(any())
            verify(hadoopMessagingServieMock, times(1)).sendBookingContinueLogMessage(
              HadoopMessageProcessName.ContinueItineraryRequest,
              correlationId.get,
              clientId,
              request,
              None
            )
            assert(result == expectedResponse)
          }
        }
    }

    "Continue payment with proxy to original DC" in {
      when(requestValidator.validateContinuePaymentRequest(any())).thenReturn(None)
      when(bookingCreationByDcProxy.continueItinerary(any(), any())(any()))
        .thenReturn(Future.successful(successResponse))

      val request = ContinuePaymentRequest(
        "CAQSQEV2YjlhSjJIU3ZpWnIzTmNUTTdDb1VFaU9hZDdPWW1kVkFibkJZVHpxVk4yN0I3d0lmWW1XdVczd2J6WGVLUE0=",
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None
      )
      val header = BookingAPIHttpHeader(
        "/create",
        1,
        "2",
        "qwe",
        WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration),
        "0.0.0.0"
      )
      handler
        .continueItineraryPayment(header, ip = "", request)
        .map { result =>
          verify(continueCreationService, never()).continueCreation(any())(any())
          verify(commonBookingsService, never()).getItineraryStatus(any(), meq(Seq.empty))(any())
          verify(bookingCreationByDcProxy, times(1)).continueItinerary(meq(request), meq("zz"))(any())
          assert(result.success)
          assert(result.itinerary.isDefined)
          assert(result.itinerary.get.itineraryId === 2)
          assert(result.itinerary.get.flights.nonEmpty)
        }
    }

    "return failure when validate whitelabel is false" in {
      val wlValidateResponse = GetStatusResponse(
        success = false,
        errorMessage = Some("whitelabelId in create is JTB but in get status is Agoda")
      )

      when(requestValidator.validateContinuePaymentRequest(any())).thenReturn(None)
      when(requestValidator.validateJtbWhitelabelId(any(), any())(any())).thenReturn(true)
      when(commonBookingsService.getItineraryStatus(any(), any())(any()))
        .thenReturn(Future.successful(wlValidateResponse))

      val header = BookingAPIHttpHeader(
        "/create",
        1,
        "2",
        "qwe",
        WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration),
        "0.0.0.0"
      )

      handler
        .continueItineraryPayment(
          header,
          "",
          ContinuePaymentRequest(
            "CAQSQEV2YjlhSjJIU3ZpWnIzTmNUTTdDb1VFaU9hZDdPWW1kVkFibkJZVHpxVk4yN0I3d0lmWW1XdVczd2J6WGVLUE0=",
            None,
            None,
            Some(FraudResult(FraudResultStatusEnum.Pass)),
            None,
            None,
            None,
            None,
            None,
            None
          )
        )
        .map { result =>
          verify(continueCreationService, never()).continueCreation(any())(any())
          assert(!result.success)
          assertResult(Some("whitelabelId in create is JTB but in get status is Agoda"))(result.errorMessage)
        }
    }

    "return fail invalid status token" in {
      when(requestValidator.validateContinuePaymentRequest(any())).thenReturn(None)

      val header = BookingAPIHttpHeader(
        "/create",
        1,
        "2",
        "qwe",
        WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration),
        "0.0.0.0"
      )

      val request = ContinuePaymentRequest(
        Base64.getEncoder.encodeToString(
          "invalid".getBytes(StandardCharsets.UTF_8)
        ),
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None
      )

      handler
        .continueItineraryPayment(header, ip = "", request)
        .map { result =>
          verify(continueCreationService, never()).continueCreation(any())(any())
          verify(commonBookingsService, never()).getItineraryStatus(any(), meq(Seq.empty))(any())
          verify(requestValidator, never()).validateJtbWhitelabelId(any(), any())(any())
          verify(bookingCreationByDcProxy, never()).continueCreation(any(), any())(any())
          assert(!result.success)
          assertResult(Some("can't read status token"))(result.errorMessage)
        }
    }
  }

  "Get additional tags" should {
    "returns proper tags" in {
      val dc        = ServerUtils.serverDc()
      val response  = mock[GetStatusResponse]
      val itinerary = Itinerary(58090135, Seq(HotelBooking(bookingStatus = CreatedBookingStatus.BookingProcessing)))
      val expectedTags = Map(
        "topic_id"             -> "3",
        "product_type"         -> "SingleProperty",
        "device_type"          -> "Unknown",
        "hotel_booking_status" -> "BookingProcessing"
      )

      when(response.itinerary).thenReturn(Some(itinerary))
      when(response.status).thenReturn(None)

      val tags = handler
        .getAdditionalTags(
          ContinuePaymentRequest(
            "CAQSQEp2TEtBNWRRcm51b2Vka0RQcWlybFQxaWZVeVdMbUczbVhDZjNDZXhpMHMrWUlSZWcvQ1d1WGE0S3Z1MUF0UHg=",
            // CAQSQGxOWlNTSloyVkZ0NWtiaVk3OGpyMlVhWDVDd1pqL1p6NWl6Z3p6UDkyNjZMR0kwdjRwVGhSZ0RkRHp1QVlyZ0k=
            None,
            None,
            Some(FraudResult(FraudResultStatusEnum.Pass)),
            None,
            None,
            None,
            None,
            None,
            None
          ),
          Some(response)
        )

      assertResult(expectedTags)(tags)
    }
  }

  private def newRequestContext(): RequestContext =
    requestContext(mock[MessagesBag]).copy(correlationId = Some("test_correlation_id"), languageId = 42)

}
