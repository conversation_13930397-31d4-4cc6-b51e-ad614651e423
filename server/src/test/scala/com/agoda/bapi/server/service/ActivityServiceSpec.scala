package com.agoda.bapi.server.service

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.multi.product.{ReplicateByItineraryIdRequest, ReplicateByItineraryIdResponse}
import com.agoda.bapi.common.message.setupBooking.{ActivityConfirmPriceRequest, ActivityRequestItem, LoyaltyRequest}
import com.agoda.bapi.common.message.{ActivityBookingStateWithItinerary, ResponseErrorCode}
import com.agoda.bapi.common.model.activity.ActivityMockData.{activityBookingPriceInfo, activityDetails}
import com.agoda.bapi.common.model.activity.{ActivityConfirmationData, ActivityMockData}
import com.agoda.bapi.common.model.booking.{BookingStateMessage, MultiProductBookingGroupModelMessage}
import com.agoda.bapi.common.model.flight.history.ActionType
import com.agoda.bapi.common.model.flight.history.ActionType.ActionType
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{ActionId, UserContext, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.service.{FeatureAware, MessagesBag}
import com.agoda.bapi.common.token.activity.ActivityTokenService
import com.agoda.bapi.common.ProductTokenMockHelper
import com.agoda.bapi.creation.repository.{ActivityBookingRepository, MultiProductRepository}
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.proxy.ActivitySearchApiProxy
import com.agoda.bapi.common.MessageService
import com.agoda.mpb.common.{BookingType, PointsType}
import com.agoda.mpb.common.models.state.{AccountingEntity, PayNowProductPayment, PointsAttributes, ProductPayment}
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import mocks.{ActivityModelMock, DBBookingModelHelper, RequestContextMock}
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{clearInvocations, reset, verify, when}
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{BeforeAndAfterEach, OptionValues, TryValues}
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future
import scala.util.Success

class ActivityServiceSpec
    extends AsyncWordSpec
    with MockitoSugar
    with Matchers
    with BeforeAndAfterEach
    with RequestContextMock
    with ProductTokenMockHelper
    with OptionValues
    with TryValues
    with DBBookingModelHelper
    with ActivityModelMock {
  val activitySearchApiProxy                                 = mock[ActivitySearchApiProxy]
  val activityTokenService                                   = mock[ActivityTokenService]
  val mockActivityBookingRepository                          = mock[ActivityBookingRepository]
  val mockMessageService                                     = mock[MessageService]
  val mockMultiProductRepositoryMock: MultiProductRepository = mock[MultiProductRepository]
  val mockFeatureAware                                       = mock[FeatureAware]
  val activityService =
    new ActivityServiceImpl(
      activitySearchApiProxy,
      activityTokenService,
      mockActivityBookingRepository,
      mockMessageService,
      mockMultiProductRepositoryMock
    )
  implicit val requestContext = mock[RequestContext]

  when(activitySearchApiProxy.getActivityConfirmationData(any(), any(), any(), any(), any(), any(), any())(any()))
    .thenReturn(Future.successful(ActivityMockData.activityConfirmationData))

  when(activityTokenService.buildActivityBookingToken(any(), any(), any(), any()))
    .thenReturn(Success(defaultActivityToken))

  when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))

  override def beforeEach() = { reset(mockActivityBookingRepository) }

  "getActivityConfirmationData" should {
    "return confirmation data from activitySearchApiProxy" in {
      val request = ActivityRequestItem("some", ActivityConfirmPriceRequest("some", "some"))
      implicit val context: SetupBookingContext = SetupBookingContext(
        BookingFlow.SingleActivity,
        RequestContext(
          languageId = 0,
          locale = "",
          clientId = 0,
          path = "",
          platformId = 1,
          correlationId = Some(""),
          messagesBag = mock[MessagesBag],
          featureAware = Some(mock[FeatureAware]),
          experimentData = None,
          userContext = Some(UserContext(languageId = 0, requestOrigin = "TH", currency = "THB", nationalityId = 106)),
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, mock[FeaturesConfiguration]),
          xForwardedForIp = ""
        ),
        correlationId = "",
        whiteLabelInfo = null,
        sessionId = "",
        bookingSessionId = ""
      )
      val loyaltyReq = Some(LoyaltyRequest(Some("partnerClaimToken")))
      activityService
        .getActivityConfirmationData(
          activityRequestItem = request,
          loyaltyRequest = loyaltyReq,
          experimentData = None,
          campaignInfoRequest = None,
          chargeCurrency = "USD",
          distributePointsResponseOpt = None
        )
        .map(result => {
          result.id shouldBe "0"
          result.content.isEmpty shouldBe false
          result.activity.value shouldBe activityDetails

          result.bookingInfo.value shouldBe activityBookingPriceInfo
        })
    }
  }

  "toBookingToken" should {
    "return mock booking token based on confirmation data" in {
      val confirmationData = ActivityConfirmationData("sample id", "sample content", true, None, None, None)
      val result           = activityService.toBookingToken(Seq(confirmationData)).success.value
      result.keys.toSeq.contains(confirmationData.id) shouldBe true

      val bookingToken = result.get(confirmationData.id)

      val productPayment = bookingToken.flatMap(_.productPayment).value
      productPayment.agency shouldBe None
      productPayment.payLater shouldBe None
      productPayment.payNow.value shouldBe PayNowProductPayment(
        payment = ProductPayment(
          paymentAmount = 200,
          paymentAmountUsd = 100,
          paymentCurrency = "USD",
          siteExchangeRate = Some(0.5),
          upliftAmount = None,
          upliftExchangeRate = Some(1.0),
          exchangeRateOption = None
        ),
        isBundleCharge = true
      )
      productPayment.accountingEntity.value shouldBe AccountingEntity(
        merchantOfRecord = 5632,
        revenue = 5632,
        rateContract = 51001,
        argument = None
      )

      val point = productPayment.points.head
      point.pointType shouldBe PointsType.RMMiles
      point.pointAttributes shouldBe PointsAttributes(
        currency = "USD",
        amount = 10,
        localCurrency = Some("THB"),
        localAmount = Some(300)
      )

      val paymentAmount = bookingToken.flatMap(_.paymentAmount).value
      paymentAmount.paymentCurrency shouldBe "USD"
      paymentAmount.paymentAmount shouldBe 200.0
      paymentAmount.paymentAmountUSD shouldBe 100.0
      paymentAmount.exchangeRate shouldBe 0.5
      paymentAmount.siteExchangeRate shouldBe 0.5
      paymentAmount.upliftExchangeRate shouldBe 1.0
      paymentAmount.destinationCurrency.value shouldBe "THB"
      paymentAmount.destinationExchangeRate shouldBe 30
      paymentAmount.giftcardAmount shouldBe 100
      paymentAmount.giftcardAmountUSD shouldBe 3.33

    }
  }

  "saveActivityBookingState" should {
    "return result successfully" in {
      val bookingState = ActivityBookingStateWithItinerary(
        itinerary = mockItineraryInternalModel.itinerary,
        itineraryHistories = mockItineraryInternalModel.history,
        payments = mockItineraryInternalModel.payments,
        bookingPayments = mockItineraryInternalModel.bookingPayments,
        activities = Seq(mockActivityProductModel),
        multiProductBookingGroups = Seq.empty,
        relationships = Nil
      )
      when(mockActivityBookingRepository.saveBookingState(any()))
        .thenReturn(Future.successful(bookingState))
      when(mockMultiProductRepositoryMock.getMultiProductInfo(any()))
        .thenReturn(Future.successful(None))
      when(mockMultiProductRepositoryMock.insertMultiProductInfo(any()))
        .thenReturn(Future.successful(mockMultiProductInfo))
      activityService.saveActivityBookingState(bookingState, Seq(mockMultiProductInfo)).map { result =>
        verify(mockActivityBookingRepository).saveBookingState(bookingState)
        verify(mockMultiProductRepositoryMock).insertMultiProductInfo(mockMultiProductInfo)
        succeed
      }
    }
  }

  "saveAndReplicateActivityBookingState" should {
    "return result successfully" in {
      clearInvocations(mockActivityBookingRepository)
      val bookingState = ActivityBookingStateWithItinerary(
        itinerary = mockItineraryInternalModel.itinerary,
        itineraryHistories = mockItineraryInternalModel.history,
        payments = mockItineraryInternalModel.payments,
        bookingPayments = mockItineraryInternalModel.bookingPayments,
        activities = Seq(mockActivityProductModel),
        multiProductBookingGroups = Seq(mockMultiProductBookingGroup),
        relationships = Nil
      )
      val actionType: ActionType   = ActionType.Created
      val actionId: ActionId       = 1
      val bookingType: Option[Int] = Some(1)

      when(mockActivityBookingRepository.saveBookingState(any()))
        .thenReturn(Future.successful(bookingState))
      when(mockMessageService.sendMessage(any())).thenReturn(Future.unit)
      when(mockMultiProductRepositoryMock.insertMultiProductInfo(any()))
        .thenReturn(Future.successful(mockMultiProductInfo))

      activityService
        .saveAndReplicateActivityBookingState(
          actionType,
          actionId,
          bookingType,
          bookingState,
          Seq(mockMultiProductInfo)
        )
        .map { result =>
          verify(mockActivityBookingRepository).saveBookingState(bookingState)
          val messageToSend: ArgumentCaptor[BookingStateMessage] = ArgumentCaptor.forClass(classOf[BookingStateMessage])
          verify(mockMessageService).sendMessage(messageToSend.capture())
          messageToSend.getValue.multiProductBookingGroups shouldEqual Some(
            Seq(
              MultiProductBookingGroupModelMessage(
                bookingId = mockMultiProductBookingGroup.bookingId,
                itineraryId = mockMultiProductBookingGroup.itineraryId,
                cartId = mockMultiProductBookingGroup.cartId,
                packageId = mockMultiProductBookingGroup.packageId
              )
            )
          )
          result.multiProductBookingGroups.head shouldEqual mockMultiProductBookingGroup
        }
    }
  }

  "replicateBookingsByItineraryId" should {
    "return error response when activity booking repository cannot load activity state" in {
      when(mockActivityBookingRepository.getBookingState(1002))
        .thenReturn(Future.failed(new RuntimeException("test-exception")))
      when(mockActivityBookingRepository.getBookingState(1001))
        .thenReturn(
          Future.successful(
            ActivityBookingStateWithItinerary(
              itinerary = defaultActivityItinerary,
              itineraryHistories = Seq(activityDefaultHistory),
              payments = Seq(activityDefaultPayment),
              bookingPayments = Seq(activityDefaultBookingPayment),
              activities = Seq(defaultActivityProductModel),
              multiProductBookingGroups = Seq(defaultMultiProductBookingGroups),
              relationships = Nil
            )
          )
        )
      val request = ReplicateByItineraryIdRequest(itineraryIds = Seq(1001, 1002), productType = "Activity")
      activityService.replicateBookingsByItineraryId(request).map { res =>
        res shouldBe ReplicateByItineraryIdResponse(
          success = false,
          errorCode = Some(ResponseErrorCode.TechnicalError.toString),
          errorMessage = Some("Error during replication: test-exception")
        )
      }
    }

    "send replication message when booking state is correctly loaded" in {
      reset(mockMessageService)
      when(mockMessageService.sendMessage(any())).thenReturn(Future.unit)
      when(mockActivityBookingRepository.getBookingState(1001))
        .thenReturn(
          Future.successful(
            ActivityBookingStateWithItinerary(
              itinerary = defaultActivityItinerary,
              itineraryHistories = Seq(activityDefaultHistory),
              payments = Seq(activityDefaultPayment),
              bookingPayments = Seq(activityDefaultBookingPayment),
              activities = Seq(defaultActivityProductModel),
              multiProductBookingGroups = Seq(defaultMultiProductBookingGroups),
              relationships = Nil
            )
          )
        )
      val messageToSend: ArgumentCaptor[BookingStateMessage] = ArgumentCaptor.forClass(classOf[BookingStateMessage])
      val request                                            = ReplicateByItineraryIdRequest(itineraryIds = Seq(1001), productType = "Activity")
      activityService.replicateBookingsByItineraryId(request).map { res =>
        verify(mockMessageService).sendMessage(messageToSend.capture())
        val capturedMessage = messageToSend.getValue
        capturedMessage.actionId shouldBe 0
        capturedMessage.actionType shouldBe ActionType.Unknown.id
        capturedMessage.bookingType shouldBe Some(BookingType.Activity.id)
        res shouldBe ReplicateByItineraryIdResponse(success = true)
      }
    }
  }
}
