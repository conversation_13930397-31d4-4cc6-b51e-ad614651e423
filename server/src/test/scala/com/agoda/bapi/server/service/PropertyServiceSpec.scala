package com.agoda.bapi.server.service

import api.request.FeatureFlag
import com.agoda.bapi.common.MessageService
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.DevicePlatform
import com.agoda.bapi.common.message.DurationRequest
import com.agoda.bapi.common.message.OccupancyRequest
import com.agoda.bapi.common.message.PapiContextRequest
import com.agoda.bapi.common.message.PricingRequest
import com.agoda.bapi.common.message.PropertyBookingStateModel
import com.agoda.bapi.common.message.PropertyBookingStateWithItinerary
import com.agoda.bapi.common.message.PropertySearchCriteria
import com.agoda.bapi.common.message.loyalty
import com.agoda.bapi.common.message.setupBooking.PackageRequest
import com.agoda.bapi.common.message.setupBooking.ProductPaymentRequest
import com.agoda.bapi.common.message.setupBooking.PropertyRequestItem
import com.agoda.bapi.common.model.{Charge<PERSON><PERSON>, User<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, WhiteLabelInfo}
import com.agoda.bapi.common.model.booking.{BookingStateMessage, FlightItineraryForMessage, PropertyForMessage}
import com.agoda.bapi.common.model.cart.CartItemContext
import com.agoda.bapi.common.model.consumerFintech.ConsumerFintechDetail
import com.agoda.bapi.common.model.consumerFintech.products.ConsumerFintechProductDetail
import com.agoda.bapi.common.model.consumerFintech.products.smartFlex.{SmartFlexOfferDetail, SmartFlexProductDetail, SmartFlexReplacementDetail}
import com.agoda.bapi.common.model.consumerFintech.products.smartsaver.{SmartSaverOfferDetail, SmartSaverProductDetail}
import com.agoda.bapi.common.model.flight.flightModel.MultiProductItinerary
import com.agoda.bapi.common.model.flight.history.ActionType
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.service.MessagesBag
import com.agoda.bapi.common.token.PropertySetupBookingToken
import com.agoda.bapi.creation.repository.PropertyBookingRepository
import com.agoda.bapi.creation.service.observability.LogContext
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.handler.context.SetupBookingSessionContext
import com.agoda.bapi.server.model.property.BookingProperty
import com.agoda.bapi.server.repository.PapiRepository
import com.agoda.bapi.server.repository.dto.papi.GiftCardRedeemRequest
import com.agoda.bapi.server.repository.dto.papi.PapiRequestDto
import com.agoda.bapi.server.repository.dto.papi.RoomBundleHint
import com.agoda.content.models.db.image.VideoResponse
import com.agoda.mpbe.state.booking.BaseBookingEssInfo
import com.agoda.mpbe.state.product.property.EbePropertyBooking
import com.agoda.mpbe.state.product.property.PropertyProductModel
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import enumerations.SpecialRequestIds
import models.starfruit.{DisplayBasis, DisplayPrice, DisplaySummary, FinProducts, FinanceProductInfo, LoyaltyPaymentBoundaries, LoyaltyReasons, SmartFlexInfo, SmartSaverInfo, SummaryElement}
import org.joda.time.DateTime
import org.joda.time.LocalDate
import org.mockito.ArgumentMatchers.any
import org.mockito.ArgumentMatchers.argThat
import org.mockito.Mockito.{reset, times, verify, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.concurrent.ScalaFutures
import transformers._

import scala.concurrent.Future
import org.scalatestplus.mockito.MockitoSugar
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import com.softwaremill.quicklens._

import java.time.LocalDateTime

class PropertyServiceSpec extends AsyncWordSpec with Matchers with MockitoSugar with BeforeAndAfter with ScalaFutures {

  import mocks.BookingMockHelper._

  private val papiRepository            = mock[PapiRepository]
  private val messageService            = mock[MessageService]
  private val propertyBookingRepository = mock[PropertyBookingRepository]
  private val messageBag                = mock[MessagesBag]
  private val featureAware              = mock[FeatureAware]
  private val propertyKey1              = "p-key1"
  implicit val context                  = mock[RequestContext]
  when(context.featureAware).thenReturn(Some(featureAware))
  private val pricingRequest = PricingRequest(
    isMse = false,
    requiredPrice = "",
    requiredBasis = "",
    isRPM2Included = false,
    selectedPointMaxId = None,
    isIncludeUsdAndLocalCurrency = false,
    allowOverrideOccupancy = false,
    enableOpaqueChannel = false,
    isAllowRoomTypeNotGuarantee = false,
    synchronous = true,
    partnerLoyaltyProgramId = Some(0)
  )

  def makeRequestItem(
      testId: String,
      durationRequest: DurationRequest = DurationRequest(LocalDate.now(), 1),
      payment: Option[ProductPaymentRequest] = None
  ) =
    PropertyRequestItem(
      testId,
      PropertySearchCriteria(
        Some(1),
        "",
        occupancyRequest = com.agoda.bapi.common.message.OccupancyRequest(1, 0, 1),
        durationRequest = durationRequest,
        pricingRequest = Some(
          PricingRequest(
            isMse = false,
            requiredPrice = "",
            requiredBasis = "",
            isRPM2Included = false,
            selectedPointMaxId = None,
            isIncludeUsdAndLocalCurrency = false,
            allowOverrideOccupancy = false,
            enableOpaqueChannel = false,
            isAllowRoomTypeNotGuarantee = false,
            synchronous = true,
            partnerLoyaltyProgramId = Some(0)
          )
        ),
        papiContextRequest =
          Some(PapiContextRequest(true, trafficTypeId = Some(1), rawBotProfile = "", showCms = false)),
        None,
        None,
        None,
        None
      ),
      payment = payment
    )

  val baseBookingProperty =
    BookingProperty(
      1,
      None,
      None,
      properties = Some(
        Properties(
          property = Seq(
            baseProperty().copy(
              booking = basicBooking(),
              masterRooms = List(
                EnrichedMasterRoom(
                  maxOccupancy = 1,
                  maxExtraBeds = 0,
                  typeId = 1,
                  englishName = None,
                  name = None,
                  images = Vector.empty,
                  facilities = Vector.empty,
                  bedConfiguration2 = None,
                  facilityGroups = Vector.empty,
                  childrenRooms = List(EnrichedChildRoom()),
                  features = Vector.empty,
                  styleName = None,
                  isRoomDayUsed = None,
                  topFacilities = None,
                  roomLicenseId = None,
                  customizableRoomGridOptions = None,
                  isRecommended = None,
                  roomDescriptionAI = None,
                  videos = Vector(
                    VideoResponse(123L, "ABC")
                  )
                )
              )
            )
          ),
          debug = None
        )
      ),
      propertiesJson = "{}"
    )

  val bookingPropertyUnconventionalHours =
    BookingProperty(
      1,
      None,
      None,
      properties = Some(
        Properties(
          property = Seq(
            baseProperty().copy(
              booking = unconventionalTimingBooking,
              masterRooms = List(
                EnrichedMasterRoom(
                  maxOccupancy = 1,
                  maxExtraBeds = 0,
                  typeId = 1,
                  englishName = None,
                  name = None,
                  images = Vector.empty,
                  facilities = Vector.empty,
                  bedConfiguration2 = None,
                  facilityGroups = Vector.empty,
                  childrenRooms = List(EnrichedChildRoom()),
                  features = Vector.empty,
                  styleName = None,
                  isRoomDayUsed = None,
                  topFacilities = None,
                  roomLicenseId = None,
                  customizableRoomGridOptions = None,
                  isRecommended = None,
                  roomDescriptionAI = None,
                  videos = Vector(
                    VideoResponse(123L, "ABC")
                  )
                )
              )
            )
          ),
          debug = None
        )
      ),
      propertiesJson = "{}"
    )

  val bookingPropertyWithLoyaltyPaymentBoundaries =
    BookingProperty(
      1,
      None,
      None,
      properties = Some(
        Properties(
          property = Seq(
            baseProperty().copy(
              booking = basicBooking(),
              masterRooms = List(
                EnrichedMasterRoom(
                  maxOccupancy = 1,
                  maxExtraBeds = 0,
                  typeId = 1,
                  englishName = None,
                  name = None,
                  images = Vector.empty,
                  facilities = Vector.empty,
                  bedConfiguration2 = None,
                  facilityGroups = Vector.empty,
                  childrenRooms = List(
                    EnrichedChildRoom(
                      loyaltyResponse =
                        Option(LoyaltyPaymentBoundaries(0, 100, 100, Some(0), Some(false), LoyaltyReasons.Success))
                    )
                  ),
                  features = Vector.empty,
                  styleName = None,
                  isRoomDayUsed = None,
                  topFacilities = None,
                  roomLicenseId = None,
                  customizableRoomGridOptions = None,
                  isRecommended = None,
                  roomDescriptionAI = None,
                  videos = Vector(
                    VideoResponse(123L, "ABC")
                  )
                )
              )
            )
          ),
          debug = None
        )
      ),
      propertiesJson = "{}"
    )

  // format: off
  // error due to deeply nested codes https://scalameta.org/scalafmt/docs/known-issues.html#deeply-nested-code
  private def getPropertyForBookingChargeTotal(chargeTotal: DisplayPrice) = {
    val baseDisplayBasis = DisplayBasis(DisplayPrice(0, 0), DisplayPrice(0, 0), DisplayPrice(0, 0))
    val baseSummaryElement = SummaryElement(
      chargeTotal = DisplayPrice(0, 0),
      rebateTotal = DisplayPrice(0, 0),
      rebateExtraBed = DisplayPrice(0, 0),
      displayTotal = DisplayPrice(0, 0),
      pseudoCoupon = DisplayPrice(0, 0),
      originalTotal = DisplayPrice(0, 0)
    )

    baseBookingProperty.copy(
      properties = baseBookingProperty.properties.map(ps => ps.copy(
        property = ps.property.map(p => p.copy(
          masterRooms = p.masterRooms.map(mr => mr.copy(
            childrenRooms = mr.childrenRooms.map(cr => cr.copy(
              pricing =  Map("USD" -> EnrichedPricing(
                display = baseDisplayBasis,
                crossedOut = baseDisplayBasis,
                options = Nil,
                charges = Nil,
                displaySummary = DisplaySummary(
                  perBook = baseSummaryElement.copy(chargeTotal = chargeTotal),
                  perRoomPerBook = baseSummaryElement,
                  perRoomPerNight = baseSummaryElement,
                  perNight = baseSummaryElement
                )
              ))
            ))
          ))
        ))
      ))
    )
  }
  // format: on
  implicit val rc: SetupBookingContext =
    SetupBookingContext(
      BookingFlow.Package,
      RequestContext(
        0,
        "",
        0,
        "",
        1,
        Some(""),
        messageBag,
        Some(featureAware),
        None,
        Some(UserContext(0, "TH", "THB", 106)),
        whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, mock[FeaturesConfiguration]),
        xForwardedForIp = ""
      ),
      correlationId = "",
      whiteLabelInfo = null,
      sessionId = "",
      bookingSessionId = "",
      logContext = LogContext()
    )

  val rcWithSession = rc.copy(
    session = SetupBookingSessionContext(
      properties = Map(
        propertyKey1 -> PropertySetupBookingToken(propertyKey1, None, None, Some("<price guarantee token>"), Some(1L))
      ),
      packages = Some(PackageRequest("client", Some("system"))),
      timestamp = Some(101L)
    )
  )

  val rcWithSinglePropertyFlow: SetupBookingContext = rc.copy(bookingFlowType = BookingFlow.SingleProperty)
  val rcWithCartFlow: SetupBookingContext           = rc.copy(bookingFlowType = BookingFlow.Cart)

  before {
    reset(papiRepository)
    reset(featureAware)
    when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any())) thenReturn (Future
      .successful(None))
  }

  val testService = new PropertyServiceImpl(
    papiRepository = papiRepository,
    messaging = messageService,
    propertyBookingRepository = propertyBookingRepository
  )

  import org.mockito.ArgumentMatcher
  case class BundleMatcher(other: PapiRequestDto) extends ArgumentMatcher[PapiRequestDto] {
    override def matches(argument: PapiRequestDto): Boolean = {
      argument.roomBundleHint == other.roomBundleHint &&
      argument.priceParameter == other.priceParameter
    }
  }

  // it only validates the feature flags, for the rest of the fields we rely on other tests cases
  // in fact many of them are incorrect, it would be well to re-factor this spec
  case class FeatureFlagsMatcher(other: PapiRequestDto) extends ArgumentMatcher[PapiRequestDto] {
    override def matches(argument: PapiRequestDto): Boolean = {
      argument.priceParameter.dfFeatureFlags == other.priceParameter.dfFeatureFlags
    }
  }

  case class CnMatcher() extends ArgumentMatcher[PapiRequestDto] {
    override def matches(actual: PapiRequestDto): Boolean = {
      actual.priceParameter.dfFeatureFlags.contains(FeatureFlag.DomesticTaxReceipt.i) &&
      actual.priceParameter.dfFeatureFlags.contains(FeatureFlag.QuantumPaymentsEnabled.i)
    }
  }

  case class CnOriginMatcher() extends ArgumentMatcher[PapiRequestDto] {
    override def matches(actual: PapiRequestDto): Boolean = {
      actual.priceParameter.dfFeatureFlags.contains(FeatureFlag.DomesticTaxReceipt.i) &&
      actual.priceParameter.dfFeatureFlags.contains(FeatureFlag.QuantumPaymentsEnabled.i)
    }
  }

  "PropertyServiceImpl" should {
    "return room bundles" in {
      val occupancyRequest = OccupancyRequest(1, 0, 1)
      val today            = LocalDate.now()
      val tomorrow         = LocalDate.now().plusDays(1)
      val durationRequest  = DurationRequest(today, 3)

      val context = PapiContextRequest(true, trafficTypeId = Some(1), rawBotProfile = "", showCms = false)
      val searchCriteria = PropertySearchCriteria(
        Some(1L),
        "",
        occupancyRequest,
        durationRequest,
        Some(pricingRequest),
        Some(context),
        None,
        None,
        None,
        None
      )
      val requestContext = RequestContext(
        0,
        "",
        0,
        "",
        1,
        Some(""),
        messageBag,
        Some(featureAware),
        None,
        Some(UserContext(0, "TH", "THB", 106)),
        whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, mock[FeaturesConfiguration]),
        xForwardedForIp = ""
      )

      val dto = PapiRequestDto.apply(
        searchCriteria,
        DevicePlatform.WebDesktop,
        "USD",
        None,
        BookingFlow.SingleProperty,
        None,
        None,
        None,
        Some(Seq(RoomBundleHint("", today, 1), RoomBundleHint("", tomorrow, 2)))
      )(requestContext)

      when(
        papiRepository
          .getPropertyForBooking(argThat(BundleMatcher(dto)), any(), any(), any())(any(), any())
      ).thenReturn(Future.successful(Option(baseBookingProperty)))

      val room1 = makeRequestItem("Test 1", DurationRequest(today, 1))
      val room2 = makeRequestItem("Test 2", DurationRequest(tomorrow, 2))
      val response =
        testService.retrievePropertyBundles(Seq(room1, room2), DevicePlatform.WebDesktop, "USD", None)

      response.map { result =>
        result.isDefined shouldBe true
        result.get.propertyId shouldBe 1
      }
    }

    "savePropertyBookingState" should {
      "return the result from propertyBookingStateWithItinerary correctly" in {
        val multiProductItinerary = MultiProductItinerary(
          itineraryId = 973237L,
          memberId = 1,
          recStatus = Some(1),
          recCreatedWhen = Some(DateTime.parse("2019-08-02T16:01")),
          recModifiedWhen = Some(DateTime.parse("2019-08-02T16:01"))
        )
        val propertyBookingStateWithItinerary = PropertyBookingStateWithItinerary(
          itinerary = multiProductItinerary,
          properties = Seq(
            PropertyBookingStateModel(
              bookingId = 37298457,
              stateId = 2,
              propertyProductModel = Some(
                PropertyProductModel(
                  booking = EbePropertyBooking(
                    bookingId = 37298457
                  ),
                  essInfos = Seq(
                    BaseBookingEssInfo(
                      bookingEssInfoId = 0L,
                      bookingId = 37298457,
                      userTaxCountryId = 42,
                      recStatus = 1,
                      recCreatedWhen = LocalDateTime.parse("2019-08-02T16:01:00.00"),
                      recCreatedBy = "recCreatedBy"
                    )
                  )
                )
              )
            )
          )
        )
        val expectedPropertyBookingStateWithItinerary = PropertyBookingStateWithItinerary(
          itinerary = multiProductItinerary,
          properties = Seq(
            PropertyBookingStateModel(
              bookingId = 37298457,
              stateId = 2,
              propertyProductModel = Some(
                PropertyProductModel(
                  booking = EbePropertyBooking(
                    bookingId = 37298457
                  ),
                  essInfos = Seq(
                    BaseBookingEssInfo(
                      bookingEssInfoId = 2323L,
                      bookingId = 37298457,
                      userTaxCountryId = 42,
                      recStatus = 1,
                      recCreatedWhen = LocalDateTime.parse("2019-08-02T16:01:00.00"),
                      recCreatedBy = "recCreatedBy"
                    )
                  )
                )
              )
            )
          )
        )
        when(propertyBookingRepository.savePropertyBookingState(propertyBookingStateWithItinerary))
          .thenReturn(
            Future.successful(
              expectedPropertyBookingStateWithItinerary
            )
          )
        testService.savePropertyBookingState(propertyBookingStateWithItinerary).map { res =>
          res shouldBe expectedPropertyBookingStateWithItinerary
        }
      }
    }

    "saveAndReplicatePropertyBookingState" should {
      "calling savePropertyBookingState and process replication correctly" in {
        val actionType  = ActionType.Created
        val actionId    = 23982
        val bookingType = Some(1)
        val multiProductItinerary = MultiProductItinerary(
          itineraryId = 973237L,
          memberId = 1,
          recStatus = Some(1),
          recCreatedWhen = Some(DateTime.parse("2019-08-02T16:01")),
          recModifiedWhen = Some(DateTime.parse("2019-08-02T16:01"))
        )
        val propertyBookingStateWithItinerary = PropertyBookingStateWithItinerary(
          itinerary = multiProductItinerary,
          properties = Seq(
            PropertyBookingStateModel(
              bookingId = 37298457,
              stateId = 2,
              propertyProductModel = Some(
                PropertyProductModel(
                  booking = EbePropertyBooking(
                    bookingId = 37298457
                  ),
                  essInfos = Seq(
                    BaseBookingEssInfo(
                      bookingEssInfoId = 0L,
                      bookingId = 37298457,
                      userTaxCountryId = 42,
                      recStatus = 1,
                      recCreatedWhen = LocalDateTime.parse("2019-08-02T16:01:00.00"),
                      recCreatedBy = "recCreatedBy"
                    )
                  )
                )
              )
            )
          )
        )
        val expectedPropertyBookingStateWithItinerary = PropertyBookingStateWithItinerary(
          itinerary = multiProductItinerary,
          properties = Seq(
            PropertyBookingStateModel(
              bookingId = 37298457,
              stateId = 2,
              propertyProductModel = Some(
                PropertyProductModel(
                  booking = EbePropertyBooking(
                    bookingId = 37298457
                  ),
                  essInfos = Seq(
                    BaseBookingEssInfo(
                      bookingEssInfoId = 2323L,
                      bookingId = 37298457,
                      userTaxCountryId = 42,
                      recStatus = 1,
                      recCreatedWhen = LocalDateTime.parse("2019-08-02T16:01:00.00"),
                      recCreatedBy = "recCreatedBy"
                    )
                  )
                )
              )
            )
          )
        )
        val expectedBookingStateMessage = BookingStateMessage(
          actionType = actionType.id,
          actionId = actionId,
          bookingType = bookingType,
          bookingId = 0L,
          schemaVersion = "1",
          flights = Seq.empty,
          slices = Seq.empty,
          segments = Seq.empty,
          passengers = Seq.empty,
          payments = Seq.empty,
          bookingPayments = Seq.empty,
          bookingRelationships = Seq.empty,
          breakdown = Seq.empty,
          breakdownPerPax = Seq.empty,
          baggageAllowance = Seq.empty,
          baggage = Seq.empty,
          history = Seq.empty,
          summary = Seq.empty,
          paxTickets = Seq.empty,
          itinerary = FlightItineraryForMessage(
            itineraryId = 973237L,
            memberId = 1,
            recStatus = Some(1),
            recCreatedWhen = Some(DateTime.parse("2019-08-02T16:01").toDate),
            recModifiedWhen = Some(DateTime.parse("2019-08-02T16:01").toDate)
          ),
          userAgent = None,
          bookingAttribution = Seq.empty,
          itineraryDate = DateTime.parse("2019-08-02T16:01").toDate,
          protectionModels = None,
          multiProductInfos = None,
          flightSegmentInfoByPaxType = Seq.empty,
          segmentInfoByPaxType = Seq.empty,
          fareRulePolicies = None,
          flightSeatSelection = Seq.empty,
          vehicle = None,
          activities = None,
          properties = Some(
            Seq(
              PropertyForMessage(
                propertyState =
                  "Ei4ImcLkEboFJgokMDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAw6gIiCJMSEJnC5BEYKiABKgYIvLyR6gUyDHJlY0NyZWF0ZWRCeQ==",
                bookingId = 37298457,
                stateId = 2
              )
            )
          ),
          cegFastTracks = None,
          addOns = None,
          multiProductBookingGroups = None,
          flightBrandSelections = None,
          flightBrandAttributes = None,
          flightBrandAttributeParams = None,
          flightBaseBooking = None,
          flightBaseCancellationInfo = None,
          crossProductIsolatedFeature = None
        )
        when(propertyBookingRepository.savePropertyBookingState(propertyBookingStateWithItinerary))
          .thenReturn(
            Future.successful(
              expectedPropertyBookingStateWithItinerary
            )
          )
        when(messageService.sendMessage(any())).thenReturn(Future.successful(()))

        testService
          .saveAndReplicatePropertyBookingState(
            actionType = actionType,
            actionId = actionId,
            bookingType = bookingType,
            propertyBookingStateWithItinerary = propertyBookingStateWithItinerary
          )
          .map { res =>
            verify(messageService, times(1)).sendMessage(expectedBookingStateMessage)
            res shouldBe expectedPropertyBookingStateWithItinerary
          }
      }
    }

    "work with CN features" when {
      "origin is CN" in {
        when(papiRepository.getPropertyForBooking(argThat(CnOriginMatcher()), any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(Option(baseBookingProperty)))
        val requestContext = rc.copy(
          requestContext = rc.requestContext.copy(
            userContext = Some(
              rc.requestContext.userContext.get.copy(
                requestOrigin = "CN"
              )
            )
          )
        )

        testService
          .retrieveProperties(Seq(makeRequestItem("Test 2")), DevicePlatform.WebDesktop, "USD", None)(requestContext)
          .map { result =>
            result.seq.size shouldBe 1
            val propertyProductItem = result.seq(0).toPropertyProductItem
            propertyProductItem.id shouldBe "Test 2"
            propertyProductItem.content shouldBe "{}"
            propertyProductItem.displayPrice shouldBe None
            propertyProductItem.specialRequestOptions shouldBe Seq(
              SpecialRequestIds.AdditionalNotes.i,
              SpecialRequestIds.LateCheckIn.i,
              SpecialRequestIds.EarlyCheckIn.i,
              SpecialRequestIds.HighFloor.i,
              SpecialRequestIds.LargeBed.i,
              SpecialRequestIds.TwinBed.i,
              SpecialRequestIds.NonSmokingRoom.i,
              SpecialRequestIds.SmokingRoom.i
            )
            result.seq(0).papiProperties shouldBe baseBookingProperty.properties
          }
      }

      "origin is NOT CN" in {
        when(papiRepository.getPropertyForBooking(argThat(CnMatcher()), any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(Option(baseBookingProperty)))
        val requestContext = rc.copy(
          requestContext = rc.requestContext.copy(
            userContext = Some(
              rc.requestContext.userContext.get.copy(
                requestOrigin = "TH"
              )
            )
          )
        )

        testService
          .retrieveProperties(Seq(makeRequestItem("Test 2")), DevicePlatform.WebDesktop, "USD", None)(requestContext)
          .map { result =>
            result.seq.size shouldBe 1
            val propertyProductItem = result.seq(0).toPropertyProductItem
            propertyProductItem.id shouldBe "Test 2"
            propertyProductItem.content shouldBe "{}"
            propertyProductItem.displayPrice shouldBe None
            propertyProductItem.specialRequestOptions shouldBe Seq(
              SpecialRequestIds.AdditionalNotes.i,
              SpecialRequestIds.LateCheckIn.i,
              SpecialRequestIds.EarlyCheckIn.i,
              SpecialRequestIds.HighFloor.i,
              SpecialRequestIds.LargeBed.i,
              SpecialRequestIds.TwinBed.i,
              SpecialRequestIds.NonSmokingRoom.i,
              SpecialRequestIds.SmokingRoom.i
            )
            result.seq(0).papiProperties shouldBe baseBookingProperty.properties
          }
      }

    }

    "return empty result for search item when PAPI search returns None" in {
      val response =
        testService.retrieveProperties(Seq(makeRequestItem("Test 1")), DevicePlatform.WebDesktop, "USD", None)
      response.map { result =>
        result.seq.size shouldBe 1
        val propertyProductItem = result.seq(0).toPropertyProductItem
        propertyProductItem.id shouldBe "Test 1"
        propertyProductItem.content shouldBe ""
      }
    }

    "a PAPI exception gets thrown up" in {
      when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any())) thenReturn Future
        .failed(
          new Exception("PAPI timeout")
        )
      recoverToExceptionIf[Exception](
        testService.retrieveProperties(Seq(makeRequestItem("Test 1")), DevicePlatform.WebDesktop, "USD", None)
      ).map { result =>
        result shouldBe a[Exception]
      }
    }

    "return a property item result for search item when PAPI search returns a BookingProperty" in {
      when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any()))
        .thenReturn(Future.successful(Option(baseBookingProperty)))
      testService
        .retrieveProperties(Seq(makeRequestItem("Test 2")), DevicePlatform.WebDesktop, "USD", None)
        .map { result =>
          result.seq.size shouldBe 1
          val propertyProductItem = result.seq(0).toPropertyProductItem
          propertyProductItem.id shouldBe "Test 2"
          propertyProductItem.content shouldBe "{}"
          propertyProductItem.displayPrice shouldBe None
          propertyProductItem.specialRequestOptions shouldBe Seq(
            SpecialRequestIds.AdditionalNotes.i,
            SpecialRequestIds.LateCheckIn.i,
            SpecialRequestIds.EarlyCheckIn.i,
            SpecialRequestIds.HighFloor.i,
            SpecialRequestIds.LargeBed.i,
            SpecialRequestIds.TwinBed.i,
            SpecialRequestIds.NonSmokingRoom.i,
            SpecialRequestIds.SmokingRoom.i
          )
          result.seq(0).papiProperties shouldBe baseBookingProperty.properties
        }
    }

    "return a property item with consumer-fintech detail when PAPI search returns a BookingProperty with finProductInfo" in {
      when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            Some(
              baseBookingProperty
                .modify(_.properties.each.property.each.masterRooms.each.childrenRooms.each.finProductInfo)
                .setTo(
                  Some(
                    FinanceProductInfo(
                      products = FinProducts(
                        smartFlex = Some(
                          SmartFlexInfo(originalCxlCode = "14D50P_7D100P_100P", processReplacement = true)
                        ),
                        smartSaver = Some(SmartSaverInfo(originalCxlCode = "5D50P_100P"))
                      )
                    )
                  )
                )
            )
          )
        )
      testService
        .retrieveProperties(Seq(makeRequestItem("Test 2")), DevicePlatform.WebDesktop, "USD", None)
        .map { result =>
          result.seq.size shouldBe 1
          result.flatMap(_.consumerFintechDetail) shouldBe Seq(
            ConsumerFintechDetail(
              products = ConsumerFintechProductDetail(
                smartFlex = Some(
                  SmartFlexProductDetail(
                    offer = Some(SmartFlexOfferDetail(cxlCodeOriginal = "14D50P_7D100P_100P")),
                    replacement = Some(SmartFlexReplacementDetail(processReplacement = true))
                  )
                ),
                cancelAndRebookV3 = None,
                smartSaver = Some(
                  SmartSaverProductDetail(
                    offer = Some(SmartSaverOfferDetail(cxlCodeOriginal = "5D50P_100P"))
                  )
                )
              ),
              serviceTaxCountry = None
            )
          )
        }
    }

    "return a property item with No consumer-fintech detail when PAPI search returns a BookingProperty  consumer fintech info without any product" in {
      when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            Some(
              baseBookingProperty
                .modify(_.properties.each.property.each.masterRooms.each.childrenRooms.each.finProductInfo)
                .setTo(
                  Some(
                    FinanceProductInfo(
                      products = FinProducts(
                        smartFlex = None
                      )
                    )
                  )
                )
            )
          )
        )
      testService
        .retrieveProperties(Seq(makeRequestItem("Test 2")), DevicePlatform.WebDesktop, "USD", None)
        .map { result =>
          result.seq.size shouldBe 1
          result.flatMap(_.consumerFintechDetail) shouldBe Nil
        }
    }

    "return a property item with consumer-fintech serviceTaxCountry when CFB-1088 = A" in {
      when(featureAware.enableServiceTaxCountry).thenReturn(false)
      when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            Some(
              baseBookingProperty
                .modify(_.properties.each.property.each.masterRooms.each.childrenRooms.each.finProductInfo)
                .setTo(
                  Some(
                    FinanceProductInfo(
                      products = FinProducts(
                        smartFlex = Some(
                          SmartFlexInfo(originalCxlCode = "14D50P_7D100P_100P", processReplacement = true)
                        ),
                        smartSaver = Some(SmartSaverInfo(originalCxlCode = "5D50P_100P"))
                      ),
                      serviceTaxCountry = Some("TW")
                    )
                  )
                )
            )
          )
        )
      testService
        .retrieveProperties(Seq(makeRequestItem("Test 2")), DevicePlatform.WebDesktop, "USD", None)
        .map { result =>
          result.head.consumerFintechDetail.head.serviceTaxCountry shouldBe None
        }
    }

    "return a property item with consumer-fintech serviceTaxCountry when CFB-1088 = B" in {
      when(featureAware.enableServiceTaxCountry).thenReturn(true)
      when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any()))
        .thenReturn(
          Future.successful(
            Some(
              baseBookingProperty
                .modify(_.properties.each.property.each.masterRooms.each.childrenRooms.each.finProductInfo)
                .setTo(
                  Some(
                    FinanceProductInfo(
                      products = FinProducts(
                        smartFlex = Some(
                          SmartFlexInfo(originalCxlCode = "14D50P_7D100P_100P", processReplacement = true)
                        ),
                        smartSaver = Some(SmartSaverInfo(originalCxlCode = "5D50P_100P"))
                      ),
                      serviceTaxCountry = Some("TW")
                    )
                  )
                )
            )
          )
        )
      testService
        .retrieveProperties(Seq(makeRequestItem("Test 2")), DevicePlatform.WebDesktop, "USD", None)
        .map { result =>
          result.head.consumerFintechDetail.head.serviceTaxCountry shouldBe Some("TW")
        }
    }

    "package token is extracted correctly" in {
      val testBookingProperty = baseBookingProperty.copy(
        properties = Some(
          Properties(
            property = Seq(
              baseProperty.copy(
                booking = basicBooking(),
                masterRooms = List(
                  EnrichedMasterRoom(
                    maxOccupancy = 1,
                    maxExtraBeds = 0,
                    typeId = 1,
                    englishName = None,
                    name = None,
                    images = Vector.empty,
                    facilities = Vector.empty,
                    bedConfiguration2 = None,
                    facilityGroups = Vector.empty,
                    childrenRooms = List(
                      EnrichedChildRoom().copy(
                        packaging = Some(EnrichedPackaging(EnrichedPackagingToken("client-a", "system-b"), Map.empty))
                      )
                    ),
                    features = Vector.empty,
                    styleName = None,
                    isRoomDayUsed = None,
                    topFacilities = None,
                    roomLicenseId = None,
                    customizableRoomGridOptions = None,
                    isRecommended = None,
                    roomDescriptionAI = None,
                    videos = Vector(
                      VideoResponse(123L, "ABC")
                    )
                  )
                )
              )
            ),
            debug = None
          )
        )
      )
      when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any()))
        .thenReturn(Future.successful(Option(testBookingProperty)))
      testService
        .retrieveProperties(Seq(makeRequestItem(propertyKey1)), DevicePlatform.WebDesktop, "USD", None)(rc)
        .map { result =>
          result.seq(0).packageRequest shouldBe Some(PackageRequest("client-a", Some("system-b")))
          result.seq(0).toPropertyProductItem.packageRequest shouldBe Some(PackageRequest("client-a", Some("system-b")))
        }
    }

    "return LoyaltyPaymentBoundaries when PAPI search is issued with LoyaltyPaymentRequest while retrieving a BookingProperty" in {
      when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any()))
        .thenReturn(Future.successful(Option(bookingPropertyWithLoyaltyPaymentBoundaries)))
      testService
        .retrieveProperties(
          Seq(makeRequestItem("Test 3")),
          DevicePlatform.WebDesktop,
          "USD",
          None,
          Option(GiftCardRedeemRequest(0, 100))
        )
        .map { result =>
          result.seq.size shouldBe 1
          val loyaltyPaymentBoundaries =
            result.seq(0).getLoyaltyPaymentBoundaries()(rc.requestContext)
          loyaltyPaymentBoundaries shouldBe Some(
            loyalty.LoyaltyPaymentBoundaries(
              0,
              100,
              100,
              Some(0),
              Some(false),
              LoyaltyReasons.Success
            )
          )
          result
            .seq(0)
            .papiProperties shouldBe bookingPropertyWithLoyaltyPaymentBoundaries.properties
        }
    }

    "return proper PAPI checkin/checkout time" when {
      "return a property item result for search item when PAPI search returns a BookingProperty" in {
        when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(Option(bookingPropertyUnconventionalHours)))
        testService
          .retrieveProperties(Seq(makeRequestItem("Test 2")), DevicePlatform.WebDesktop, "USD", None)
          .map { result =>
            result.seq.size shouldBe 1
            val propertyProductItem = result.seq(0).toPropertyProductItem
            propertyProductItem.id shouldBe "Test 2"
            propertyProductItem.content shouldBe "{}"
            propertyProductItem.displayPrice shouldBe None
            propertyProductItem.specialRequestOptions shouldBe Seq(
              SpecialRequestIds.AdditionalNotes.i,
              SpecialRequestIds.LateCheckIn.i,
              SpecialRequestIds.EarlyCheckIn.i,
              SpecialRequestIds.HighFloor.i,
              SpecialRequestIds.LargeBed.i,
              SpecialRequestIds.TwinBed.i,
              SpecialRequestIds.NonSmokingRoom.i,
              SpecialRequestIds.SmokingRoom.i
            )
            result.seq(0).papiProperties shouldBe bookingPropertyUnconventionalHours.properties
            val resCheckIn = result.seq(0).papiProperties.get.property.head.booking.get.booking.head.hotel.head.checkIn
            val resCheckOut =
              result.seq(0).papiProperties.get.property.head.booking.get.booking.head.hotel.head.checkOut
            resCheckIn.isEqual(formatter.parseDateTime("2019-12-13T17:00:00.000+07:00")) shouldBe true
            resCheckOut.isEqual(formatter.parseDateTime("2019-12-14T11:00:00.000+07:00")) shouldBe true
          }
      }
    }

    "return proper PAPI property status" when {
      "when total booking amount over 100000 USD" in {
        val properties =
          getPropertyForBookingChargeTotal(DisplayPrice(0, 100001))
        when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(Option(properties)))

        testService
          .retrieveProperties(Seq(makeRequestItem("Test 2")), DevicePlatform.WebDesktop, "USD", None)(rc)
          .map { result =>
            result.seq.size shouldBe 1
            result.seq(0).papiProperties shouldBe properties.properties
            result.seq(0).papiPropertyStatus shouldBe PapiPropertyStatus.MaximumBookingAmountExceeded
          }
      }

      "when total booking amount under or equal 100000 USD" in {
        val properties =
          getPropertyForBookingChargeTotal(DisplayPrice(0, 100000))
        when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(Option(properties)))

        testService
          .retrieveProperties(Seq(makeRequestItem("Test 2")), DevicePlatform.WebDesktop, "USD", None)(rc)
          .map { result =>
            result.seq.size shouldBe 1
            result.seq(0).papiProperties shouldBe properties.properties
            result.seq(0).papiPropertyStatus shouldBe PapiPropertyStatus.Ok
          }
      }
    }

    "return proper PAPI property status from class methods" when {
      "when booking amount under or equal 100000 USD" in {
        val bookingProperty =
          getPropertyForBookingChargeTotal(DisplayPrice(0, 100000))
        val status = PapiPropertyService.getPapiPropertyStatus(bookingProperty.properties.get)
        status shouldBe PapiPropertyStatus.Ok
      }

      "when property is missing properties" in {
        val properties = Properties(property = Seq.empty, debug = None)
        val status     = PapiPropertyService.getPapiPropertyStatus(properties)
        status shouldBe PapiPropertyStatus.PropertyNotFound
      }

      "when property is missing master room" in {
        val properties = Properties(
          property = Seq(
            baseProperty.copy(masterRooms = Seq.empty)
          ),
          debug = None
        )
        val status = PapiPropertyService.getPapiPropertyStatus(properties)
        status shouldBe PapiPropertyStatus.PropertyNotFound
      }

      "when property is missing child rooms" in {
        val properties = Properties(
          property = Seq(
            baseProperty.copy(
              masterRooms = Seq(
                baseMasterRoom().copy(
                  childrenRooms = List.empty
                )
              )
            )
          ),
          debug = None
        )
        val status = PapiPropertyService.getPapiPropertyStatus(properties)
        status shouldBe PapiPropertyStatus.PropertyNotFound
      }

      "when booking amount over 100000 USD" in {
        val bookingProperty =
          getPropertyForBookingChargeTotal(DisplayPrice(0, 100001))
        val status = PapiPropertyService.getPapiPropertyStatus(bookingProperty.properties.get)
        status shouldBe PapiPropertyStatus.MaximumBookingAmountExceeded
      }
    }

    "return booking properties data with cart item context" in {
      val cartItemContext: CartItemContext = CartItemContext("e0049b61-a70a-40af-b8d8-a2a42bd2c076")
      val propertyRequestItemWithCartContext =
        makeRequestItem(propertyKey1).copy(cartItemContext = Some(cartItemContext))
      testService
        .retrieveProperties(
          Seq(propertyRequestItemWithCartContext),
          DevicePlatform.WebDesktop,
          "USD",
          None
        )(rc)
        .map { result =>
          result.seq(0).cartItemContext shouldBe Some(cartItemContext)
        }
    }

    "return booking properties data with cart item context as None" in {
      val propertyRequestItemWithoutCartContext = makeRequestItem(propertyKey1)
      testService
        .retrieveProperties(
          Seq(propertyRequestItemWithoutCartContext),
          DevicePlatform.WebDesktop,
          "USD",
          None
        )(rc)
        .map { result =>
          result.seq(0).cartItemContext shouldBe None
        }
    }

    "return booking properties data with cart item context if papi returns valid response" in {
      val cartItemContext: CartItemContext = CartItemContext("e0049b61-a70a-40af-b8d8-a2a42bd2c076")
      val propertyRequestItemWithCartContext =
        makeRequestItem(propertyKey1).copy(cartItemContext = Some(cartItemContext))
      when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any()))
        .thenReturn(Future.successful(Option(baseBookingProperty)))
      testService
        .retrieveProperties(
          Seq(propertyRequestItemWithCartContext),
          DevicePlatform.WebDesktop,
          "USD",
          None
        )(rc)
        .map { result =>
          result.seq(0).content shouldBe "{}"
          result.seq(0).cartItemContext shouldBe Some(cartItemContext)
        }
    }

    "return selected charge option as PayNow" when {
      "single property flow, selectedChargeOption is PayNow" in {
        when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(Option(baseBookingProperty)))
        testService
          .retrieveProperties(
            Seq(
              makeRequestItem(
                "selected charge option is pay now for single property flow",
                payment = Some(ProductPaymentRequest(selectedChargeOption = ChargeOption.PayNow))
              )
            ),
            DevicePlatform.WebDesktop,
            "USD",
            None
          )(rcWithSinglePropertyFlow)
          .map { result =>
            result.seq.size shouldBe 1
            result.seq.head.selectedChargeOption shouldBe Some(ChargeOption.PayNow)
          }
      }
      "cart flow, selectedChargeOption is PayNow" in {
        when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(Option(baseBookingProperty)))
        testService
          .retrieveProperties(
            Seq(
              makeRequestItem(
                "selected charge option is pay now for cart flow",
                payment = Some(ProductPaymentRequest(selectedChargeOption = ChargeOption.PayNow))
              )
            ),
            DevicePlatform.WebDesktop,
            "USD",
            None
          )(rcWithCartFlow)
          .map { result =>
            result.seq.size shouldBe 1
            result.seq.head.selectedChargeOption shouldBe Some(ChargeOption.PayNow)
          }
      }
    }
    "return selected charge option as PayLater" when {
      "single property flow, selectedChargeOption is PayLater" in {
        when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(Option(baseBookingProperty)))
        testService
          .retrieveProperties(
            Seq(
              makeRequestItem(
                "selected charge option is pay later for single property flow",
                payment = Some(ProductPaymentRequest(selectedChargeOption = ChargeOption.PayLater))
              )
            ),
            DevicePlatform.WebDesktop,
            "USD",
            None
          )(rcWithSinglePropertyFlow)
          .map { result =>
            result.seq.size shouldBe 1
            val propertyProductItem = result.seq.head.toPropertyProductItem
            result.seq.head.selectedChargeOption shouldBe Some(ChargeOption.PayLater)
          }
      }
      "cart flow, selectedChargeOption is PayLater" in {
        when(papiRepository.getPropertyForBooking(any(), any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(Option(baseBookingProperty)))
        testService
          .retrieveProperties(
            Seq(
              makeRequestItem(
                "selected charge option is pay later for cart flow",
                payment = Some(ProductPaymentRequest(selectedChargeOption = ChargeOption.PayLater))
              )
            ),
            DevicePlatform.WebDesktop,
            "USD",
            None
          )(rcWithCartFlow)
          .map { result =>
            result.seq.size shouldBe 1
            result.seq.head.selectedChargeOption shouldBe Some(ChargeOption.PayLater)
          }
      }
    }
  }
}
