package com.agoda.bapi.server.service

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.externalLoyalty._
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.message.{ExperimentData, PropertySearchCriteria}
import com.agoda.bapi.common.model.WhiteLabel.WhiteLabel
import com.agoda.bapi.common.model.creation.LoyaltyDiscountType.{Amount, HotelCredit}
import com.agoda.bapi.common.model.creation.PaymentModel
import com.agoda.bapi.common.model.externalloyalty.ExternalLoyaltyAdditionalInfo
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName.OverridePointsOfferType
import com.agoda.bapi.common.model.{User<PERSON>ontext<PERSON>ock, <PERSON><PERSON><PERSON><PERSON>, WhiteLabelInfo}
import com.agoda.bapi.common.provider.ExperimentManagerProvider
import com.agoda.bapi.common.proxy.ExternalLoyaltyProxy
import com.agoda.bapi.common.service.{FeatureAware, MessagesBag}
import com.agoda.bapi.common.token.BookingTokenEncryptionHelper
import com.agoda.bapi.creation.BookingTokenHelper
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.{BookingPropertiesData, ProductData}
import com.agoda.bapi.server.utils.SetupBookingMock
import com.agoda.experiments.ExperimentManager
import com.agoda.externalloyalty.client.v2.api.LoyaltyPlatformApi.PartnersV2InternalErrorException
import com.agoda.externalloyalty.client.v2.model.DistributePointsRequest.PointsOfferType
import com.agoda.externalloyalty.client.v2.model.DistributePointsResponse.RedemptionType
import com.agoda.externalloyalty.client.v2.model.{ApiErrorItem, CartItemReq, CartItemRes, DistributePointsRequest, DistributePointsResponse, ExperimentInfo, ForcedExperiment}
import com.agoda.flights.client.v2.model._
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import com.agoda.winterfell.output._
import com.fasterxml.jackson.databind.node.ObjectNode
import mocks.CartBookingTokenMockHelper.{multiBookingTokenStringForDistributePoints, multiBookingTokenStringForDistributePointsFullRedeem, multiBookingTokenStringWithPrecisionAmt, multiBookingTokenStringWithPriceBreakdown}
import mocks.PropertyMock
import com.agoda.papi.enums.room.AccrualPolicyStartFrom
import models.starfruit.{AccrualInfo => PapiAccrualInfo, AccrualPolicy => PapiAccrualPolicy, BookingExternalLoyaltyPayment, LoyaltyEarnInfo}
import org.mockito.ArgumentMatchers.{any, eq => eqTo}
import org.mockito.Mockito.{reset, times, verify, when}
import org.mockito.{ArgumentCaptor, ArgumentMatchers}
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.TableDrivenPropertyChecks
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{BeforeAndAfter, OptionValues}
import org.scalatestplus.mockito.MockitoSugar
import transformers.{Property, _}

import java.time.LocalDate
import scala.concurrent.Future

class ExternalLoyaltyServiceSpec
    extends AsyncWordSpec
    with SetupBookingMock
    with PropertyMock
    with Matchers
    with MockitoSugar
    with BeforeAndAfter
    with BookingTokenHelper
    with TableDrivenPropertyChecks
    with OptionValues {
  private val proxy                         = mock[ExternalLoyaltyProxy]
  private val mockExperimentManager         = mock[ExperimentManager]
  private val mockExperimentManagerProvider = mock[ExperimentManagerProvider]
  private val featureAware                  = mock[FeatureAware]

  val cId                                              = Some(1)
  val whiteLabelId                                     = Some(1)
  val origin                                           = Some("IN")
  val partnerClaim                                     = "dummy-token"
  val memberId                                         = Some(123)
  val loyaltyErrorResponse: List[LoyaltyErrorResponse] = List.empty[LoyaltyErrorResponse]
  val sessionId                                        = Some("sessionId-mock")
  val correlationId                                    = Some("correlationId-mock")
  val externalLoyaltyHeaders                           = ExternalLoyaltyHeaders(sessionId, correlationId)
  val experimentInfoOpt = Some(
    ExperimentInfo(
      forceUserVariant = Some("B"),
      forcedExperiments = Some(Seq(ForcedExperiment("ABC-123", "A"), ForcedExperiment("XYZ-456", "B"))),
      forceOnIntegrationRun = true,
      forceOnZeroTraffic = true
    )
  )

  private val service = new ExternalLoyaltyServiceImpl(proxy)

  val flightPricing = Some(
    Map(
      "pricing1" ->
        SearchResponseCurrencyPricing(
          charges = Seq.empty,
          display = SearchResponsePricingDisplay(
            perBook = SearchResponseDisplayPrice(exclusive = 1000.0, allInclusive = 1000.0),
            perPax = Seq.empty,
            averagePerPax = None
          ),
          paymentModel = PaymentModel.Agency.id,
          externalLoyalty = Some(
            ExternalLoyaltyPricing(
              offers = Seq(
                ItineraryOffers(
                  itineraryId = "itinerary-id",
                  items = Seq(
                    LoyaltyOffer(
                      isSelected = true,
                      burn = Some(
                        BurnOffer(
                          points = Some(1000.0d),
                          pointsInAmountUSD = Some(1000.0d)
                        )
                      )
                    )
                  )
                )
              )
            )
          )
        ),
      "pricing2" ->
        SearchResponseCurrencyPricing(
          charges = Seq.empty,
          display = SearchResponsePricingDisplay(
            perBook = SearchResponseDisplayPrice(exclusive = 1000.0, allInclusive = 1000.0),
            perPax = Seq.empty,
            averagePerPax = None
          ),
          paymentModel = PaymentModel.Agency.id,
          externalLoyalty = Some(
            ExternalLoyaltyPricing(
              offers = Seq(
                ItineraryOffers(
                  itineraryId = "itinerary-id",
                  items = Seq(
                    LoyaltyOffer(
                      isSelected = true,
                      burn = Some(
                        BurnOffer(
                          points = Some(500.0d),
                          pointsInAmountUSD = Some(500.0d)
                        )
                      )
                    )
                  )
                )
              )
            )
          )
        )
    )
  )

  val mockProduct = mockProductsProduct.copy(
    properties = mockProductsProduct.properties.map(
      _.copy(
        papiProperties = Some(
          Properties(
            Seq(
              createMockProperty(
                propertyId = 1L
              )
            ),
            debug = None
          )
        )
      )
    )
  )

  val mockProductData = mockProductsProduct.copy(
    properties = mockProductsProduct.properties.map(
      _.copy(
        papiProperties = Some(
          Properties(
            Seq(
              createMockProperty(
                propertyId = 1L
              )
            ),
            debug = None
          )
        )
      )
    ),
    flights = mockProductsProduct.flights.map(_.copy(flightPricing = flightPricing))
  )

  after {
    reset(proxy)
  }
  private val error = PartnersV2InternalErrorException(
    ApiErrorItem(
      errorName = "INTERNAL_ERROR",
      cmsId = Some(1234),
      cause = None,
      arguments = Seq()
    )
  )
  private val proxyResult = LoyaltyErrorResponse(error.error.cmsId, error.error.errorName)
  "External Loyalty service" should {
    "Return and Log correctly" should {
      val partner: Partner = Partner(
        partnerCode = Some("KTC"),
        name = Some("KTC"),
        logoImageUrl = Some("KTC"),
        loginRedirectUrl = Some("KTC"),
        cmsId = Some(13777)
      )
      val partnerExpectedRequest: PartnersRequest =
        PartnersRequest(cid = cId, whiteLabelId = whiteLabelId, origin = origin)
      val partnerExpectedResponse: Set[Partner] = Set(partner)
      val partnerConfigExpectedRequest: PartnerConfigRequest =
        PartnerConfigRequest(
          partnerClaim = partnerClaim,
          memberId = Some(123),
          whiteLabelId = Some(WhiteLabel.Agoda.id)
        )
      val dbshk = Partner(
        partnerCode = Some("dbshk"),
        name = Some("dbshk"),
        logoImageUrl = Some("dbshk"),
        loginRedirectUrl = Some("dbshk"),
        cmsId = Some(13777)
      )
      val dbssg = Partner(
        partnerCode = Some("dbssg"),
        name = Some("dbssg"),
        logoImageUrl = Some("dbssg"),
        loginRedirectUrl = Some("dbssg"),
        cmsId = Some(13777)
      )
      val partnersResponse = partnerExpectedResponse + dbshk + dbssg
      val expectedRequest: PartnersRequest =
        PartnersRequest(cid = Some(-1), whiteLabelId = whiteLabelId, origin = Some("HK"))
      val partnerConfigExpectedResponse: PartnerConfigResponse = PartnerConfigResponse(
        PaymentLimitations(
          partnerCode = "1",
          paymentMethodIds = Some(Seq(1, 2, 3)),
          paymentMethodNames = Some(Seq("Visa")),
          binRanges = Some(
            Seq(
              BinRange(
                bookingStartDate = Some(LocalDate.now()),
                bookingEndDate = None,
                binList = Some(Seq(BinListRange(from = 111, to = 222)))
              )
            )
          ),
          errorCMSId = 1,
          alternatePaymentCMSId = Some(0)
        )
      )
      val memberBalanceExpectedRequest: MemberBalanceRequest =
        MemberBalanceRequest(
          whiteLabelId = whiteLabelId.get,
          partnerClaim = partnerClaim,
          memberId = Some(123),
          experimentInfo = experimentInfoOpt
        )
      val memberBalanceExpectedResponse: Seq[SubLoyaltyProgram] = Seq(
        SubLoyaltyProgram(
          pointsBalance = Some(1700.40),
          disabled = Some(false),
          programId = Some("1"),
          programName = Some("KTC"),
          minimumPointsToRedeem = Some(100.20),
          programLogoUrl = Some("logourl")
        )
      )
      val setupBookingRequest: SetupBookingRequest   = mock[SetupBookingRequest]
      implicit val setupContext: SetupBookingContext = mock[SetupBookingContext]
      val messagesBag                                = mock[MessagesBag]
      val whitelabelInfo                             = getMockWhiteLabelInfo(externalLoyaltyBalanceEnabled = true, isDirectPartner = true)
      val requestContext = RequestContext(
        languageId = 1,
        locale = "en-us",
        clientId = 1,
        path = "",
        platformId = 4,
        userContext = Some(UserContextMock.value.copy(requestOrigin = "TH", memberId = memberId)),
        messagesBag = messagesBag,
        xForwardedForIp = "0.0.0.0",
        bookingCreationContext = None,
        whiteLabelInfo = whitelabelInfo
      )
      when(setupBookingRequest.enabledFeatures).thenReturn(Some(Seq(service.ExternalLoyaltyFeatureFlag)))
      when(setupBookingRequest.loyaltyRequest).thenReturn(None)
      when(setupBookingRequest.userContext).thenReturn(Some(UserContextMock.value))
      when(setupBookingRequest.correlationId).thenReturn(Some("mock-corid"))
      when(setupContext.requestContext).thenReturn(requestContext)
      when(setupContext.sessionId).thenReturn("mock-session")
      when(setupContext.whiteLabelInfo).thenReturn(whitelabelInfo)
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(setupContext.sessionId).thenReturn(sessionId.get)
      when(setupContext.correlationId).thenReturn(correlationId.get)
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(setupContext.sessionId).thenReturn(sessionId.get)
      when(setupContext.correlationId).thenReturn(correlationId.get)

      "getExternalLoyaltyResponse return error list" in {
        when(setupBookingRequest.loyaltyRequest).thenReturn(
          Some(LoyaltyRequest(partnerClaimToken = Some(partnerClaim)))
        )
        when(
          proxy
            .getMemberBalances(MemberBalanceRequest(whiteLabelId.get, partnerClaim, memberId), externalLoyaltyHeaders)(
              setupContext.requestContext
            )
        ).thenReturn(Future.successful(Left(proxyResult)))

        when(
          proxy
            .getPartnerConfig(
              PartnerConfigRequest(partnerClaim, memberId, Some(WhiteLabel.Agoda.id)),
              externalLoyaltyHeaders
            )(setupContext.requestContext)
        ).thenReturn(Future.successful(Left(proxyResult)))

        when(
          proxy.getPartners(any[PartnersRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Left(proxyResult)))

        val externalLoyaltyResponse = service.getExternalLoyaltyResponse(setupBookingRequest, setupContext)
        externalLoyaltyResponse.map { realResult =>
          realResult.externalLoyaltySubLoyalty shouldEqual None
          realResult.externalLoyaltyPartners shouldEqual None
          realResult.externalLoyaltyPartnerConfig shouldEqual None
          realResult.loyaltyErrorResponse.get.size shouldEqual 3
          realResult.loyaltyErrorResponse.get.head shouldEqual proxyResult
        }
      }
      "Return and Log correctly - Partner api" in {
        when(
          proxy
            .getPartners(any[PartnersRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Right(partnerExpectedResponse)))
        val partnerResponse = service.getPartners(cId, whiteLabelId, origin, externalLoyaltyHeaders, setupContext)
        partnerResponse.map { realResult =>
          verify(proxy).getPartners(eqTo(partnerExpectedRequest), eqTo(externalLoyaltyHeaders))(eqTo(requestContext))
          realResult shouldEqual Right(partnerExpectedResponse)
        }
      }
      "Return and Log correctly - Partner config api" in {
        when(
          proxy
            .getPartnerConfig(any[PartnerConfigRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Right(partnerConfigExpectedResponse)))
        val partnerConfigResponse =
          service.getPartnerConfig(partnerClaim, WhiteLabel.Agoda.id, Some(123), externalLoyaltyHeaders, setupContext)
        partnerConfigResponse.map { realResult =>
          verify(proxy).getPartnerConfig(eqTo(partnerConfigExpectedRequest), eqTo(externalLoyaltyHeaders))(
            eqTo(requestContext)
          )
          realResult shouldEqual Right(partnerConfigExpectedResponse)
        }
      }

      "Return and Log correctly - Member balance api" in {
        val experimentData = mock[ExperimentData]

        when(experimentData.force).thenReturn(Some(Map("ABC-123" -> "A", "XYZ-456" -> "B")))
        when(experimentData.forceByVariant).thenReturn(Some("B"))
        when(experimentData.forceOnIntegrationRun).thenReturn(Some(true))
        when(experimentData.forceOnZeroTraffic).thenReturn(Some(true))

        val mockRequestContext = mock[RequestContext]
        when(mockRequestContext.experimentData).thenReturn(Some(experimentData))

        when(setupContext.requestContext).thenReturn(mockRequestContext)
        when(mockRequestContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration))
        val whiteLabelInfo = getMockWhiteLabelInfo(externalLoyaltyBalanceEnabled = true)
        when(setupContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
        when(
          proxy
            .getMemberBalances(any[MemberBalanceRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Right(memberBalanceExpectedResponse)))
        val memberBalanceResponse =
          service.getMemberBalances(partnerClaim, WhiteLabel.Agoda.id, memberId, externalLoyaltyHeaders, setupContext)
        memberBalanceResponse.map { realResult =>
          verify(proxy).getMemberBalances(eqTo(memberBalanceExpectedRequest), eqTo(externalLoyaltyHeaders))(
            eqTo(mockRequestContext)
          )
          realResult shouldEqual Right(memberBalanceExpectedResponse)
        }
      }

      "Return error - Member balance api for non-agoda whitelabel id" in {
        val whiteLabelID   = WhiteLabel.ANA.id
        val whiteLabelInfo = getMockWhiteLabelInfo(externalLoyaltyBalanceEnabled = false)
        when(setupContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
        val errorRes = LoyaltyErrorResponse(
          errorName =
            s"Member balance workflow is only configured for Agoda and whitelabel id received is: $whiteLabelID"
        )
        when(
          proxy
            .getMemberBalances(any[MemberBalanceRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Right(memberBalanceExpectedResponse)))
        val memberBalanceResponse =
          service.getMemberBalances(partnerClaim, whiteLabelID, memberId, externalLoyaltyHeaders, setupContext)
        memberBalanceResponse.map { realResult =>
          realResult shouldEqual Left(errorRes)
        }
      }

      "Return and Log correctly - Get External Loyalty Response" in {
        val whiteLabelInfo = getMockWhiteLabelInfo(burnEnabled = true, externalLoyaltyBalanceEnabled = true)
        when(setupContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
        when(
          proxy
            .getPartners(any[PartnersRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Right(partnerExpectedResponse)))
        when(
          proxy
            .getPartnerConfig(any[PartnerConfigRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Right(partnerConfigExpectedResponse)))
        when(
          proxy
            .getMemberBalances(any[MemberBalanceRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Right(memberBalanceExpectedResponse)))
        when(setupContext.requestContext).thenReturn(requestContext)
        val externalLoyaltyResponse = service.getExternalLoyaltyResponse(setupBookingRequest, setupContext)
        externalLoyaltyResponse.map { realResult =>
          realResult.externalLoyaltyPartners.getOrElse(Seq()) shouldEqual partnerExpectedResponse
        }
      }
      "return partners only when partner claim is None" in {
        val whiteLabelInfo = getMockWhiteLabelInfo(burnEnabled = true, externalLoyaltyBalanceEnabled = true)
        when(setupContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
        when(
          proxy
            .getPartners(any[PartnersRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Right(partnerExpectedResponse)))
        when(setupBookingRequest.loyaltyRequest.flatMap(_.partnerClaimToken)).thenReturn(None)
        when(setupContext.requestContext).thenReturn(requestContext)
        val externalLoyaltyResponse = service.getExternalLoyaltyResponse(setupBookingRequest, setupContext)
        externalLoyaltyResponse.map { realResult =>
          realResult.externalLoyaltyPartners.getOrElse(Seq()) shouldEqual partnerExpectedResponse
          realResult.externalLoyaltySubLoyalty shouldEqual None
          realResult.externalLoyaltyPartnerConfig shouldEqual None
        }
      }

      "Return and Log correctly - Create External Loyalty" in {

        val promoCode            = "CITI4NF"
        val discountType         = DiscountType.Amount
        val expectedDiscountType = Amount
        val discountValue        = 100.0
        val currency             = "USD"
        val loyaltyProfile = LoyaltyProfile(
          None,
          Vector.empty[ObjectNode],
          None,
          None,
          None,
          Some(
            ExternalLoyaltyUserProfileResponse(
              None,
              Seq(
                SubLoyaltyPrograms(
                  pointsBalance = Some(10.0),
                  campaigns = Some(
                    Seq(
                      Campaign(
                        promoCode,
                        discountType,
                        discountValue,
                        Some(currency),
                        Some(1),
                        None,
                        None,
                        None,
                        ProductType.HOTELS
                      )
                    )
                  )
                )
              )
            )
          )
        )

        val createExternalLoyalty = service.createExternalLoyalty(
          setupBookingRequest,
          ExternalLoyaltyResponse(
            Some(partnerExpectedResponse),
            Some(partnerConfigExpectedResponse),
            Some(memberBalanceExpectedResponse),
            Some(loyaltyErrorResponse)
          ),
          mockProduct,
          Some(loyaltyProfile)
        )
        createExternalLoyalty.flatMap(_.loyaltyPartners).getOrElse() shouldEqual partnerExpectedResponse

        val actualExternalLoyaltyCampaign = createExternalLoyalty.flatMap(_.campaigns).get.head
        actualExternalLoyaltyCampaign.promoCode shouldEqual promoCode
        actualExternalLoyaltyCampaign.discountType.get shouldEqual expectedDiscountType
        actualExternalLoyaltyCampaign.value.get shouldEqual discountValue
        actualExternalLoyaltyCampaign.currency.get shouldEqual currency
      }

      "Map promoLeftAmount and promoTotalAmount correctly " in {

        val promoCode            = "CITI300BENEF"
        val discountType         = DiscountType.HotelCredit
        val expectedDiscountType = HotelCredit
        val discountValue        = 300.0
        val currency             = "USD"
        val loyaltyProfile = LoyaltyProfile(
          None,
          Vector.empty[ObjectNode],
          None,
          None,
          None,
          Some(
            ExternalLoyaltyUserProfileResponse(
              None,
              Seq(
                SubLoyaltyPrograms(
                  pointsBalance = Some(10.0),
                  campaigns = Some(
                    Seq(
                      Campaign(
                        promoCode,
                        discountType,
                        discountValue,
                        Some(currency),
                        Some(1),
                        None,
                        None,
                        None,
                        ProductType.HOTELS,
                        Some(300),
                        Some(300)
                      )
                    )
                  )
                )
              )
            )
          )
        )

        val createExternalLoyalty = service.createExternalLoyalty(
          setupBookingRequest,
          ExternalLoyaltyResponse(
            Some(partnerExpectedResponse),
            Some(partnerConfigExpectedResponse),
            Some(memberBalanceExpectedResponse),
            Some(loyaltyErrorResponse)
          ),
          mockProduct,
          Some(loyaltyProfile)
        )
        createExternalLoyalty.flatMap(_.loyaltyPartners).getOrElse() shouldEqual partnerExpectedResponse

        val actualExternalLoyaltyCampaign = createExternalLoyalty.flatMap(_.campaigns).get.head
        actualExternalLoyaltyCampaign.promoCode shouldEqual promoCode
        actualExternalLoyaltyCampaign.discountType.get shouldEqual expectedDiscountType
        actualExternalLoyaltyCampaign.value.get shouldEqual discountValue
        actualExternalLoyaltyCampaign.currency.get shouldEqual currency
        actualExternalLoyaltyCampaign.promoLeftAmount shouldEqual Some(300)
        actualExternalLoyaltyCampaign.promoTotalAmount shouldEqual Some(300)
      }

      "Create External Loyalty - should aggregate points correctly" in {
        val loyaltyProfile = LoyaltyProfile(None, Vector.empty[ObjectNode], None, None, None, None)
        val createExternalLoyalty = service.createExternalLoyalty(
          setupBookingRequest,
          ExternalLoyaltyResponse(
            Some(partnerExpectedResponse),
            Some(partnerConfigExpectedResponse),
            Some(memberBalanceExpectedResponse),
            Some(loyaltyErrorResponse)
          ),
          mockProductData,
          Some(loyaltyProfile)
        )
        val aggregate = createExternalLoyalty
          .flatMap(_.redemption)
          .getOrElse(RedemptionResponse(points = 0.0, pointsValueInUsd = 0.0))

        aggregate shouldBe RedemptionResponse(points = 2500.0, pointsValueInUsd = 2500.0, pointsRange = None)
      }

      "Create External Loyalty - should aggregate points and points range correctly" in {
        val loyaltyProfile        = LoyaltyProfile(None, Vector.empty[ObjectNode], None, None, None, None)
        val mockSingleProductData = mockProductData.copy(flights = Seq.empty, cars = Seq.empty)
        val createExternalLoyalty = service.createExternalLoyalty(
          setupBookingRequest,
          ExternalLoyaltyResponse(
            Some(partnerExpectedResponse),
            Some(partnerConfigExpectedResponse),
            Some(memberBalanceExpectedResponse),
            Some(loyaltyErrorResponse)
          ),
          mockSingleProductData,
          Some(loyaltyProfile)
        )

        createExternalLoyalty.flatMap(_.redemption) shouldBe Some(
          RedemptionResponse(
            points = 1000.0,
            pointsValueInUsd = 1000.0,
            pointsRange = Some(PointsRange(maxPointsApplicable = Some(100.0), minPointsApplicable = Some(0.0)))
          )
        )
      }

      "Product Data Null then None Redeem response - Create External Loyalty" in {
        val createExternalLoyalty = service.createExternalLoyalty(
          setupBookingRequest,
          ExternalLoyaltyResponse(
            Some(partnerExpectedResponse),
            Some(partnerConfigExpectedResponse),
            Some(memberBalanceExpectedResponse),
            Some(loyaltyErrorResponse)
          ),
          productData = null,
          None
        )
        createExternalLoyalty.flatMap(_.redemption) shouldBe None
      }

      "Product Data Null then None Earn response - Create External Loyalty" in {
        val createExternalLoyalty = service.createExternalLoyalty(
          setupBookingRequest,
          ExternalLoyaltyResponse(
            Some(partnerExpectedResponse),
            Some(partnerConfigExpectedResponse),
            Some(memberBalanceExpectedResponse)
          ),
          productData = null,
          None
        )
        createExternalLoyalty.flatMap(_.earn) shouldBe None
      }

      "createExternalLoyalty" should {
        "correctly map accrualInfo" in {
          val expectedAccrualPolicy = AccrualPolicy(
            startFrom = "BOOKING_DATE",
            duration = 7
          )

          val papiAccrualPolicy = PapiAccrualPolicy(
            accrualPolicyStartFrom = AccrualPolicyStartFrom.BookingDate,
            durationInDays = 7
          )

          val expectedAccrualInfo = AccrualInfo(
            accrualPolicy = expectedAccrualPolicy
          )

          val papiAccrualInfo = PapiAccrualInfo(
            eligibilityDate = "2025-01-01",
            accrualPolicy = papiAccrualPolicy
          )

          val expectedEarnResponse = EarnResponse(
            points = 0,
            accrualInfo = Some(expectedAccrualInfo)
          )

          val papiLoyaltyEarnInfo = LoyaltyEarnInfo(
            points = 100.0,
            accrualInfo = Some(papiAccrualInfo)
          )

          val mockExternalLoyaltyPayment = mock[ExternalLoyalty]
          when(mockExternalLoyaltyPayment.earn).thenReturn(Some(expectedEarnResponse))

          val mockBookingExternalLoyaltyPayment = mock[BookingExternalLoyaltyPayment]
          when(mockBookingExternalLoyaltyPayment.earn).thenReturn(Some(papiLoyaltyEarnInfo))

          val mockPapiPayment = mock[EnrichedEBEPayment]
          when(mockPapiPayment.externalLoyalty).thenReturn(Some(mockBookingExternalLoyaltyPayment))

          val mockCreditCard = mock[EnrichedEBECreditCard]
          when(mockCreditCard.payment).thenReturn(mockPapiPayment)

          val mockBooking = mock[EnrichedEBEBooking]
          when(mockBooking.creditCard).thenReturn(mockCreditCard)

          val enrichedMockBooking = mock[EnrichedBookingItem]
          when(enrichedMockBooking.booking).thenReturn(List(mockBooking))

          val mockProperty = mock[Property]
          when(mockProperty.booking).thenReturn(Some(enrichedMockBooking))

          val mockBookingPropertiesData = mock[BookingPropertiesData]
          when(mockBookingPropertiesData.getPropertyToBook).thenReturn(Some(mockProperty))

          val mockProductData = mock[ProductData]
          when(mockProductData.properties).thenReturn(Seq(mockBookingPropertiesData))

          val result = service.createExternalLoyalty(
            setupBookingRequest,
            ExternalLoyaltyResponse(
              Some(partnerExpectedResponse),
              Some(partnerConfigExpectedResponse),
              Some(memberBalanceExpectedResponse)
            ),
            productData = mockProductData,
            None
          )

          result.flatMap(_.earn) shouldBe Some(expectedEarnResponse)
        }

        "earnResponse should be None if papi earnResponse is None" in {
          val mockEarnResponse = EarnResponse(
            points = 0,
            accrualInfo = None
          )

          val mockExternalLoyaltyPayment = mock[ExternalLoyalty]
          when(mockExternalLoyaltyPayment.earn).thenReturn(Some(mockEarnResponse))

          val mockBookingExternalLoyaltyPayment = mock[BookingExternalLoyaltyPayment]
          when(mockBookingExternalLoyaltyPayment.earn).thenReturn(None)

          val mockPapiPayment = mock[EnrichedEBEPayment]
          when(mockPapiPayment.externalLoyalty).thenReturn(Some(mockBookingExternalLoyaltyPayment))

          val mockCreditCard = mock[EnrichedEBECreditCard]
          when(mockCreditCard.payment).thenReturn(mockPapiPayment)

          val mockBooking = mock[EnrichedEBEBooking]
          when(mockBooking.creditCard).thenReturn(mockCreditCard)

          val enrichedMockBooking = mock[EnrichedBookingItem]
          when(enrichedMockBooking.booking).thenReturn(List(mockBooking))

          val mockProperty = mock[Property]
          when(mockProperty.booking).thenReturn(Some(enrichedMockBooking))

          val mockBookingPropertiesData = mock[BookingPropertiesData]
          when(mockBookingPropertiesData.getPropertyToBook).thenReturn(Some(mockProperty))

          val mockProductData = mock[ProductData]
          when(mockProductData.properties).thenReturn(Seq(mockBookingPropertiesData))

          val result = service.createExternalLoyalty(
            setupBookingRequest,
            ExternalLoyaltyResponse(
              Some(partnerExpectedResponse),
              Some(partnerConfigExpectedResponse),
              Some(memberBalanceExpectedResponse)
            ),
            productData = mockProductData,
            None
          )

          result.flatMap(_.earn) shouldBe Some(mockEarnResponse)
        }
      }

      "Create External Loyalty - externalLoyaltyAdditionalInfo data mapping" in {
        val mockLoyaltyProfile = LoyaltyProfile(
          None,
          Vector.empty[ObjectNode],
          None,
          None,
          None,
          Some(
            ExternalLoyaltyUserProfileResponse(
              None,
              Seq(
                SubLoyaltyPrograms(
                  pointsBalance = Some(10.0),
                  campaigns = Some(
                    Seq(
                      Campaign(
                        "",
                        DiscountType.Amount,
                        100.0,
                        Some("USD"),
                        Some(1),
                        None,
                        None,
                        None,
                        ProductType.HOTELS
                      )
                    )
                  )
                )
              )
            )
          )
        )
        val mockNoneBoolean: Option[Boolean] = None
        val testCases =
          Table(
            ("statement", "productData", "expectedIsCashPlusPointsSupported", "expectedRedemptionType"),
            ("product data is Null", null, None, None),
            (
              "productData.externalLoyaltyAdditionalInfo is None",
              mockProductData.copy(externalLoyaltyAdditionalInfo = None),
              None,
              None
            ),
            (
              "productData.externalLoyaltyAdditionalInfo.isCashPlusPointsSupported is None without redemptionType",
              mockProductData.copy(externalLoyaltyAdditionalInfo =
                Some(
                  ExternalLoyaltyAdditionalInfo(
                    isCashPlusPointsSupported = mockNoneBoolean,
                    redemptionType = None
                  )
                )
              ),
              None,
              None
            ),
            (
              "productData.externalLoyaltyAdditionalInfo.isCashPlusPointsSupported is true with redemptionType",
              mockProductData.copy(externalLoyaltyAdditionalInfo =
                Some(
                  ExternalLoyaltyAdditionalInfo(
                    isCashPlusPointsSupported = Some(true),
                    redemptionType = Some(RedemptionType.POINTSORPARTIALCASH)
                  )
                )
              ),
              Some(true),
              Some(RedemptionType.POINTSORPARTIALCASH)
            ),
            (
              "productData.externalLoyaltyAdditionalInfo.isCashPlusPointsSupported is false without redemptionType",
              mockProductData.copy(externalLoyaltyAdditionalInfo =
                Some(
                  ExternalLoyaltyAdditionalInfo(
                    isCashPlusPointsSupported = Some(false),
                    redemptionType = None
                  )
                )
              ),
              Some(false),
              None
            )
          )

        forEvery(testCases) {
          (
              statement: String,
              mockProductData: ProductData,
              expectedIsCashPlusPointsSupported: Option[Boolean],
              expectedRedemptionType: Option[RedemptionType]
          ) =>
            val result = service.createExternalLoyalty(
              setupBookingRequest,
              ExternalLoyaltyResponse(
                Some(partnerExpectedResponse),
                Some(partnerConfigExpectedResponse),
                Some(memberBalanceExpectedResponse),
                Some(loyaltyErrorResponse)
              ),
              mockProductData,
              Some(mockLoyaltyProfile)
            )

            val isCashPlusPointsSupported = result.flatMap(_.isCashPlusPointsSupported)
            assert(
              isCashPlusPointsSupported == expectedIsCashPlusPointsSupported,
              s"should return $expectedIsCashPlusPointsSupported when $statement"
            )
            val redemptionType = result.flatMap(_.redemption.flatMap(_.redemptionType))
            assert(
              redemptionType == expectedRedemptionType,
              s"should return $expectedRedemptionType when $statement"
            )
        }
      }

      "Setup booking request" should {

        val defaultDistributePointsReq = DistributePointsRequest(
          whiteLabelId = WhiteLabel.CitiUS.id,
          partnerClaim = None,
          userEnteredPoints = 10000,
          totalBookingInclusivePrice = 0.0,
          totalBookingExclusivePrice = 0.0,
          memberId = Some(123),
          cartItems = Seq.empty
        )

        val usBankDistributePointsReq = defaultDistributePointsReq.copy(
          whiteLabelId = WhiteLabel.USBank.id
        )

        val propertySearchCriteria = mock[PropertySearchCriteria]
        val token                  = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringForDistributePoints).toOption
        val fullRedeemToken =
          BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringForDistributePointsFullRedeem).toOption
        val propertyRequestItem1 = PropertyRequestItem("1", propertySearchCriteria)
        val propertyRequestItem2 = PropertyRequestItem("2", propertySearchCriteria)
        val flightRequestItem1   = FlightRequestItem(Some("11"), None, None)
        val flightRequestItem2   = FlightRequestItem(Some("12"), None, None)
        val carRequestItem1      = CarRequestItem("21", CarConfirmPriceRequest(searchToken = ""))
        val carRequestItem2      = CarRequestItem("22", CarConfirmPriceRequest(searchToken = ""))
        val activityRequestItem  = ActivityRequestItem("32", ActivityConfirmPriceRequest("", ""))

        FlightRequestItem(Some("12"), None, None)
        val productsRequestWithToken = ProductsRequest(
          bookingToken = token
        )

        val productsRequestFullRedeemWithToken = ProductsRequest(
          bookingToken = fullRedeemToken
        )
        val productsRequest = productsRequestWithToken.copy(
          propertyRequests = Seq(propertyRequestItem1, propertyRequestItem2),
          flightRequests = Seq(flightRequestItem1, flightRequestItem2),
          carRequestsOpt = Some(Seq(carRequestItem1, carRequestItem2)),
          activityRequests = Some(Seq(activityRequestItem))
        )

        val productsRequestWithFullRedeem = productsRequestFullRedeemWithToken.copy(
          propertyRequests = Seq(propertyRequestItem1, propertyRequestItem2),
          flightRequests = Seq(flightRequestItem1, flightRequestItem2),
          carRequestsOpt = Some(Seq(carRequestItem1, carRequestItem2)),
          activityRequests = Some(Seq(activityRequestItem))
        )

        val propertyCartItemRes1 = CartItemRes(
          productIdentifier = Some("1"),
          productType = CartItemReq.ProductType.HOTELS.toString,
          inclusivePrice = 22000.0,
          exclusivePrice = 21000.0,
          inclusivePricePoints = 24000,
          exclusivePricePoints = 23000
        )
        val propertyCartItemRes2 = CartItemRes(
          productIdentifier = Some("2"),
          productType = CartItemReq.ProductType.HOTELS.toString,
          inclusivePrice = 1500.0,
          exclusivePrice = 1500.0,
          inclusivePricePoints = 1700.0,
          exclusivePricePoints = 1700.0
        )
        val flightCartItemRes1 = CartItemRes(
          productIdentifier = Some("11"),
          productType = CartItemReq.ProductType.FLIGHTS.toString,
          inclusivePrice = 2200.0,
          exclusivePrice = 2200.0,
          inclusivePricePoints = 2400.0,
          exclusivePricePoints = 2400.0
        )
        val flightCartItemRes2 = CartItemRes(
          productIdentifier = Some("12"),
          productType = CartItemReq.ProductType.FLIGHTS.toString,
          inclusivePrice = 2500.0,
          exclusivePrice = 2500.0,
          inclusivePricePoints = 2700.0,
          exclusivePricePoints = 2700.0
        )
        val vehicleCartItemRes1 = CartItemRes(
          productIdentifier = Some("21"),
          productType = CartItemReq.ProductType.CARS.toString,
          inclusivePrice = 3200.0,
          exclusivePrice = 3200.0,
          inclusivePricePoints = 3400.0,
          exclusivePricePoints = 3400.0
        )
        val vehicleCartItemRes2 = CartItemRes(
          productIdentifier = Some("22"),
          productType = CartItemReq.ProductType.CARS.toString,
          inclusivePrice = 3500.0,
          exclusivePrice = 3500.0,
          inclusivePricePoints = 3700.0,
          exclusivePricePoints = 3700
        )
        val activityCartItemRes = CartItemRes(
          productIdentifier = Some("31"),
          productType = CartItemReq.ProductType.ACTIVITIES.toString,
          inclusivePrice = 4200.0,
          exclusivePrice = 4200.0,
          inclusivePricePoints = 4400.0,
          exclusivePricePoints = 4400.0
        )
        val cartItemRes = Seq(
          propertyCartItemRes1,
          propertyCartItemRes2,
          flightCartItemRes1,
          flightCartItemRes2,
          vehicleCartItemRes1,
          vehicleCartItemRes2,
          activityCartItemRes
        )
        val experimentData = mock[ExperimentData]

        when(experimentData.force).thenReturn(Some(Map("ABC-123" -> "A", "XYZ-456" -> "B")))
        when(experimentData.forceByVariant).thenReturn(Some("B"))
        when(experimentData.forceOnIntegrationRun).thenReturn(Some(true))
        when(experimentData.forceOnZeroTraffic).thenReturn(Some(true))

        "not map distribute points request when no matching properties" in {

          val loyaltyRequest = LoyaltyRequest(points = Some(20000), pointsOfferType = Some(PointsOfferType.ALLPOINTS))
          val reqContext =
            requestContext.copy(whiteLabelInfo = WhiteLabelInfo(WhiteLabel.CitiUS, new FeaturesConfiguration))
          when(setupContext.requestContext).thenReturn(reqContext)
          when(setupContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(setupBookingRequest.loyaltyRequest).thenReturn(Some(loyaltyRequest))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequestWithToken)

          val actualRequest = service
            .prepareDistributePointsRequest(setupBookingRequest, setupContext)
            .getOrElse(None)
          actualRequest shouldBe None
        }
        "map distribute points request for property request when user entered point is 0" in {

          val productsRequest = productsRequestWithToken.copy(propertyRequests = Seq(propertyRequestItem1))
          val reqContext =
            requestContext.copy(
              whiteLabelInfo = WhiteLabelInfo(WhiteLabel.CitiUS, new FeaturesConfiguration),
              experimentData = Some(experimentData)
            )
          when(setupContext.requestContext).thenReturn(reqContext)
          when(setupContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
          when(setupBookingRequest.loyaltyRequest).thenReturn(
            Some(LoyaltyRequest(points = Some(0), pointsOfferType = Some(PointsOfferType.ALLPOINTS)))
          )

          val expectedRequest = defaultDistributePointsReq.copy(
            userEnteredPoints = 0,
            totalBookingInclusivePrice = 1200.0,
            totalBookingExclusivePrice = 1200.0,
            cartItems = Seq(CartItemReq(Some("1"), CartItemReq.ProductType.HOTELS, 1200.0, 1200.0)),
            pointsOfferType = Some(PointsOfferType.ALLPOINTS),
            experimentInfo = experimentInfoOpt
          )

          val actualRequest = service.prepareDistributePointsRequest(setupBookingRequest, setupContext).getOrElse(None)

          actualRequest shouldBe expectedRequest
        }
        "map distribute points request for property request" in {

          val productsRequest = productsRequestWithToken.copy(propertyRequests = Seq(propertyRequestItem1))
          val reqContext =
            requestContext.copy(
              whiteLabelInfo = WhiteLabelInfo(WhiteLabel.CitiUS, new FeaturesConfiguration),
              experimentData = Some(experimentData)
            )
          when(setupContext.requestContext).thenReturn(reqContext)
          when(setupContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
          when(setupBookingRequest.loyaltyRequest).thenReturn(
            Some(LoyaltyRequest(points = Some(10000), pointsOfferType = Some(PointsOfferType.ALLPOINTS)))
          )

          val expectedRequest = defaultDistributePointsReq.copy(
            userEnteredPoints = 10000,
            totalBookingInclusivePrice = 1200.0,
            totalBookingExclusivePrice = 1200.0,
            cartItems = Seq(CartItemReq(Some("1"), CartItemReq.ProductType.HOTELS, 1200.0, 1200.0)),
            pointsOfferType = Some(PointsOfferType.ALLPOINTS),
            experimentInfo = experimentInfoOpt
          )

          val actualRequest = service.prepareDistributePointsRequest(setupBookingRequest, setupContext).getOrElse(None)

          actualRequest shouldBe expectedRequest
        }

        "map distribute points request for property request with overriden pointsOfferType" in {

          val whiteLabelInfo           = mock[WhiteLabelInfo]
          val token                    = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringWithPriceBreakdown).toOption
          val productsRequestWithToken = ProductsRequest(bookingToken = token)
          val productsRequest          = productsRequestWithToken.copy(propertyRequests = Seq(propertyRequestItem1))
          val reqContext =
            requestContext.copy(
              whiteLabelInfo = whiteLabelInfo,
              experimentData = Some(experimentData)
            )
          val loyaltyRequest = LoyaltyRequest(points = Some(1000), pointsOfferType = None)

          when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.USBank)
          when(whiteLabelInfo.isFeatureEnabled(ArgumentMatchers.eq(OverridePointsOfferType), any(), any(), any()))
            .thenReturn(true)
          when(setupContext.requestContext).thenReturn(reqContext)
          when(setupContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
          when(setupBookingRequest.loyaltyRequest).thenReturn(Some(loyaltyRequest))

          val expectedRequest = usBankDistributePointsReq.copy(
            userEnteredPoints = 1000,
            totalBookingInclusivePrice = 1200.0,
            totalBookingExclusivePrice = 1200.0,
            cartItems = Seq(CartItemReq(Some("1"), CartItemReq.ProductType.HOTELS, 1200.0, 1200.0)),
            pointsOfferType = Some(PointsOfferType.ALLPOINTS),
            experimentInfo = experimentInfoOpt
          )

          val actualRequest = service.prepareDistributePointsRequest(setupBookingRequest, setupContext).getOrElse(None)

          actualRequest shouldBe expectedRequest
        }

        "map distribute points request for property request without overriden pointsOfferType when not full redeem" in {

          val whiteLabelInfo           = mock[WhiteLabelInfo]
          val token                    = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringWithPriceBreakdown).toOption
          val productsRequestWithToken = ProductsRequest(bookingToken = token)
          val productsRequest          = productsRequestWithToken.copy(propertyRequests = Seq(propertyRequestItem1))
          val reqContext =
            requestContext.copy(
              whiteLabelInfo = whiteLabelInfo,
              experimentData = Some(experimentData)
            )
          val loyaltyRequest = LoyaltyRequest(points = Some(800), pointsOfferType = None)

          when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.USBank)
          when(whiteLabelInfo.isFeatureEnabled(ArgumentMatchers.eq(OverridePointsOfferType), any(), any(), any()))
            .thenReturn(true)
          when(setupContext.requestContext).thenReturn(reqContext)
          when(setupContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
          when(setupBookingRequest.loyaltyRequest).thenReturn(Some(loyaltyRequest))

          val expectedRequest = usBankDistributePointsReq.copy(
            userEnteredPoints = 800,
            totalBookingInclusivePrice = 1200.0,
            totalBookingExclusivePrice = 1200.0,
            cartItems = Seq(CartItemReq(Some("1"), CartItemReq.ProductType.HOTELS, 1200.0, 1200.0)),
            pointsOfferType = None,
            experimentInfo = experimentInfoOpt
          )

          val actualRequest = service.prepareDistributePointsRequest(setupBookingRequest, setupContext).getOrElse(None)

          actualRequest shouldBe expectedRequest
        }

        "map distribute points request for property request without overriden pointsOfferType" in {

          val whiteLabelInfo           = mock[WhiteLabelInfo]
          val token                    = BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringWithPriceBreakdown).toOption
          val productsRequestWithToken = ProductsRequest(bookingToken = token)
          val productsRequest          = productsRequestWithToken.copy(propertyRequests = Seq(propertyRequestItem1))
          val reqContext =
            requestContext.copy(
              whiteLabelInfo = whiteLabelInfo,
              experimentData = Some(experimentData)
            )
          val loyaltyRequest = LoyaltyRequest(points = Some(1000), pointsOfferType = None)

          when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.USBank)
          when(whiteLabelInfo.isFeatureEnabled(ArgumentMatchers.eq(OverridePointsOfferType), any(), any(), any()))
            .thenReturn(false)
          when(setupContext.requestContext).thenReturn(reqContext)
          when(setupContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
          when(setupBookingRequest.loyaltyRequest).thenReturn(Some(loyaltyRequest))

          val expectedRequest = usBankDistributePointsReq.copy(
            userEnteredPoints = 1000,
            totalBookingInclusivePrice = 1200.0,
            totalBookingExclusivePrice = 1200.0,
            cartItems = Seq(CartItemReq(Some("1"), CartItemReq.ProductType.HOTELS, 1200.0, 1200.0)),
            pointsOfferType = None,
            experimentInfo = experimentInfoOpt
          )

          val actualRequest = service.prepareDistributePointsRequest(setupBookingRequest, setupContext).getOrElse(None)

          actualRequest shouldBe expectedRequest
        }

        "map distribute points request for property request with correct precision for payment amount" in {
          val tokenWithPrecisionAmt =
            BookingTokenEncryptionHelper.encryptToken(multiBookingTokenStringWithPrecisionAmt).toOption
          val productsRequestWithPrecisionAmt =
            productsRequestWithToken.copy(
              bookingToken = tokenWithPrecisionAmt,
              propertyRequests = Seq(propertyRequestItem1)
            )
          val reqContext =
            requestContext.copy(
              whiteLabelInfo = WhiteLabelInfo(WhiteLabel.CitiUS, new FeaturesConfiguration),
              experimentData = Some(experimentData)
            )
          when(setupContext.requestContext).thenReturn(reqContext)
          when(setupContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(setupBookingRequest.productsRequest).thenReturn(productsRequestWithPrecisionAmt)
          when(setupBookingRequest.loyaltyRequest).thenReturn(
            Some(LoyaltyRequest(points = Some(10000), pointsOfferType = Some(PointsOfferType.ALLCASH)))
          )

          val expectedRequest = defaultDistributePointsReq.copy(
            userEnteredPoints = 10000,
            totalBookingInclusivePrice = 1200.123456,
            totalBookingExclusivePrice = 1200.123456,
            cartItems = Seq(CartItemReq(Some("1"), CartItemReq.ProductType.HOTELS, 1200.12345678, 1200.12345678)),
            pointsOfferType = Some(PointsOfferType.ALLCASH),
            experimentInfo = experimentInfoOpt
          )

          val actualRequest = service.prepareDistributePointsRequest(setupBookingRequest, setupContext).getOrElse(None)

          actualRequest shouldBe expectedRequest
        }

        "map distribute points request for property, flight, car, and activity requests" in {

          val loyaltyRequest = LoyaltyRequest(points = Some(20000), pointsOfferType = None)
          val reqContext =
            requestContext.copy(
              whiteLabelInfo = WhiteLabelInfo(WhiteLabel.CitiUS, new FeaturesConfiguration),
              experimentData = Some(experimentData)
            )
          when(setupContext.requestContext).thenReturn(reqContext)
          when(setupContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(setupBookingRequest.loyaltyRequest).thenReturn(Some(loyaltyRequest))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)

          val expectedRequest = DistributePointsRequest(
            whiteLabelId = WhiteLabel.CitiUS.id,
            partnerClaim = None,
            userEnteredPoints = 20000,
            totalBookingInclusivePrice = 19400.0,
            totalBookingExclusivePrice = 19400.0,
            memberId = Some(123),
            cartItems = Seq(
              CartItemReq(Some("1"), CartItemReq.ProductType.HOTELS, 1200.0, 1200.0),
              CartItemReq(Some("2"), CartItemReq.ProductType.HOTELS, 1700.0, 1700.0),
              CartItemReq(Some("11"), CartItemReq.ProductType.FLIGHTS, 2200.0, 2200.0),
              CartItemReq(Some("12"), CartItemReq.ProductType.FLIGHTS, 2700.0, 2700.0),
              CartItemReq(Some("21"), CartItemReq.ProductType.CARS, 3200.0, 3200.0),
              CartItemReq(Some("22"), CartItemReq.ProductType.CARS, 3700.0, 3700.0),
              CartItemReq(Some("32"), CartItemReq.ProductType.ACTIVITIES, 4700.0, 4700.0)
            ),
            pointsOfferType = None,
            experimentInfo = experimentInfoOpt
          )

          val actualRequest = service
            .prepareDistributePointsRequest(setupBookingRequest, setupContext)
            .getOrElse(None)
          actualRequest shouldBe expectedRequest
        }

        "map distribute points request for property, flight, car, and activity requests correctly when price in booking token is 0 AND isCitiBugFix = true" in {

          val loyaltyRequest = LoyaltyRequest(points = Some(20000), pointsOfferType = Some(PointsOfferType.ALLPOINTS))
          val featureAware   = mock[FeatureAware]

          val reqContext =
            requestContext.copy(
              whiteLabelInfo = WhiteLabelInfo(WhiteLabel.CitiUS, new FeaturesConfiguration),
              featureAware = Some(featureAware),
              experimentData = Some(experimentData)
            )
          when(setupContext.requestContext).thenReturn(reqContext)
          when(setupContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(setupBookingRequest.loyaltyRequest).thenReturn(Some(loyaltyRequest))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequestWithFullRedeem)

          val expectedRequest = DistributePointsRequest(
            whiteLabelId = WhiteLabel.CitiUS.id,
            partnerClaim = None,
            userEnteredPoints = 20000,
            totalBookingInclusivePrice = 19400.0,
            totalBookingExclusivePrice = 19400.0,
            memberId = Some(123),
            cartItems = Seq(
              CartItemReq(Some("1"), CartItemReq.ProductType.HOTELS, 1200.0, 1200.0),
              CartItemReq(Some("2"), CartItemReq.ProductType.HOTELS, 1700.0, 1700.0),
              CartItemReq(Some("11"), CartItemReq.ProductType.FLIGHTS, 2200.0, 2200.0),
              CartItemReq(Some("12"), CartItemReq.ProductType.FLIGHTS, 2700.0, 2700.0),
              CartItemReq(Some("21"), CartItemReq.ProductType.CARS, 3200.0, 3200.0),
              CartItemReq(Some("22"), CartItemReq.ProductType.CARS, 3700.0, 3700.0),
              CartItemReq(Some("32"), CartItemReq.ProductType.ACTIVITIES, 4700.0, 4700.0)
            ),
            pointsOfferType = Some(PointsOfferType.ALLPOINTS),
            experimentInfo = experimentInfoOpt
          )

          val actualRequest = service
            .prepareDistributePointsRequest(setupBookingRequest, setupContext)
            .getOrElse(None)
          actualRequest shouldBe expectedRequest
        }

        "not distribute points when failure" in {

          val productsRequest = productsRequestWithToken.copy(
            flightRequests = Seq(FlightRequestItem(Some("11"), None, None)),
            activityRequests = Some(Seq(ActivityRequestItem("32", ActivityConfirmPriceRequest("", ""))))
          )

          val loyaltyRequest = LoyaltyRequest(points = Some(20000))
          when(setupBookingRequest.loyaltyRequest).thenReturn(Some(loyaltyRequest))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)

          when(proxy.distributePoints(any(), any())(any()))
            .thenReturn(Future.successful(Left(LoyaltyErrorResponse(errorName = "Distribute API call failed"))))

          service
            .distributePoints(setupBookingRequest, setupContext)
            .map { result =>
              result shouldBe None
            }
        }

        "not distribute points when booking token is empty" in {

          when(setupBookingRequest.productsRequest).thenReturn(ProductsRequest())

          val distributePointResponse = service.distributePoints(setupBookingRequest, setupContext)

          distributePointResponse.map {
            verify(proxy, times(0)).distributePoints(any(), any())(any())
            result => result shouldBe None
          }
        }

        "not distribute points when loyalty request is empty" in {

          val tokenMessage = genBookingToken(
            referenceTokenSerialized = getMockReferenceTokenSerializer,
            absData = Some(getMockAbsResponse),
            bapiBookingToOption = Some(Seq(getMockBapiBooking))
          )
          val productsRequest = ProductsRequest(bookingToken = Some(tokenMessage))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
          when(setupBookingRequest.loyaltyRequest).thenReturn(None)

          val distributePointResponse = service.distributePoints(setupBookingRequest, setupContext)

          distributePointResponse.map {
            verify(proxy, times(0)).distributePoints(any(), any())(any())
            result => result shouldBe None
          }
        }

        "not distribute points when loyalty request points is 0.0" in {

          val tokenMessage = genBookingToken(
            referenceTokenSerialized = getMockReferenceTokenSerializer,
            absData = Some(getMockAbsResponse),
            bapiBookingToOption = Some(Seq(getMockBapiBooking))
          )
          val productsRequest = ProductsRequest(bookingToken = Some(tokenMessage))
          val loyaltyRequest  = LoyaltyRequest(points = Some(0.0))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
          when(setupBookingRequest.loyaltyRequest).thenReturn(Some(loyaltyRequest))

          val distributePointResponse = service.distributePoints(setupBookingRequest, setupContext)

          distributePointResponse.map {
            verify(proxy, times(0)).distributePoints(any(), any())(any())
            result => result shouldBe None
          }
        }

        "not distribute points when no matching property request" in {

          val loyaltyRequest = LoyaltyRequest(points = Some(10000))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequestWithToken)
          when(setupBookingRequest.loyaltyRequest).thenReturn(Some(loyaltyRequest))

          val actualResponse = service.distributePoints(setupBookingRequest, setupContext)
          actualResponse
            .map {
              verify(proxy, times(0)).distributePoints(any(), any())(any())
              result => result shouldBe None
            }
        }

        "distribute points for property Request" in {

          val productsRequest = productsRequestWithToken.copy(propertyRequests = Seq(propertyRequestItem1))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
          when(setupBookingRequest.loyaltyRequest).thenReturn(Some(LoyaltyRequest(points = Some(10000))))

          val expectedResponse = DistributePointsResponse(
            userEnteredPoints = 10000,
            totalBookingInclusivePrice = 1200.0,
            totalBookingExclusivePrice = 1200.0,
            cartItems = Seq(
              CartItemRes(
                productIdentifier = Some("1"),
                productType = CartItemReq.ProductType.HOTELS.toString,
                inclusivePrice = 1200.0,
                exclusivePrice = 1200.0,
                inclusivePricePoints = 1400.0,
                exclusivePricePoints = 1400
              )
            )
          )
          when(proxy.distributePoints(any(), any())(any())).thenReturn(Future.successful(Right(expectedResponse)))
          val response = service.distributePoints(setupBookingRequest, setupContext)

          response.map { result =>
            result shouldBe Some(expectedResponse)
          }
        }

        "distribute points for property, flight, car, and activity Request" in {

          val loyaltyRequest = LoyaltyRequest(points = Some(20000))
          when(setupBookingRequest.loyaltyRequest).thenReturn(Some(loyaltyRequest))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)

          val expectedResponse = DistributePointsResponse(
            userEnteredPoints = 10000,
            totalBookingInclusivePrice = 19400.0,
            totalBookingExclusivePrice = 19400.0,
            cartItems = cartItemRes
          )

          when(proxy.distributePoints(any(), any())(any()))
            .thenReturn(Future.successful(Right(expectedResponse)))

          val response = service.distributePoints(setupBookingRequest, setupContext)
          response
            .map { result =>
              result shouldBe Some(expectedResponse)
            }
        }
      }
      "Return correct response in Partner api with some random cId" in {
        when(
          proxy
            .getPartners(any[PartnersRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Right(partnersResponse)))
        val partnerResponse =
          service.getPartners(Some(123), whiteLabelId, Some("HK"), externalLoyaltyHeaders, setupContext)
        partnerResponse.map { realResult =>
          verify(proxy).getPartners(eqTo(expectedRequest.copy(cid = Some(123))), eqTo(externalLoyaltyHeaders))(
            any()
          )
          realResult shouldEqual Right(partnersResponse)
        }
      }

      "Return correct response in Partner api with exp enableDBSHKOnAgoda A" in {
        when(
          proxy
            .getPartners(any[PartnersRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Right(Set(dbshk))))
        val partnerResponse =
          service.getPartners(Some(-1), whiteLabelId, Some("HK"), externalLoyaltyHeaders, setupContext)

        partnerResponse.map { realResult =>
          verify(proxy).getPartners(eqTo(expectedRequest), eqTo(externalLoyaltyHeaders))(any())
          realResult shouldEqual Right(Set.empty)
        }
      }

      "Return correct response in Partner api with exp enableDBSHKOnAgoda B" in {
        val featureAware       = mock[FeatureAware]
        val mockRequestContext = mock[RequestContext]
        when(setupContext.requestContext).thenReturn(mockRequestContext)
        when(mockRequestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.enableDBSHKOnAgoda).thenReturn(true)
        when(
          proxy
            .getPartners(any[PartnersRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Right(Set(dbshk))))
        val partnerResponse =
          service.getPartners(Some(-1), whiteLabelId, Some("HK"), externalLoyaltyHeaders, setupContext)
        partnerResponse.map { realResult =>
          verify(proxy).getPartners(eqTo(expectedRequest), eqTo(externalLoyaltyHeaders))(
            eqTo(mockRequestContext)
          )
          realResult shouldEqual Right(Set(dbshk))
        }
      }

      "Return correct response in Partner api with exp enableDBSSGOnAgoda A" in {
        when(
          proxy
            .getPartners(any[PartnersRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Right(Set(dbssg))))
        val partnerResponse =
          service.getPartners(Some(-1), whiteLabelId, Some("HK"), externalLoyaltyHeaders, setupContext)
        partnerResponse.map { realResult =>
          verify(proxy).getPartners(eqTo(expectedRequest), eqTo(externalLoyaltyHeaders))(any())
          realResult shouldEqual Right(Set.empty)
        }
      }

      "Return correct response in Partner api with exp enableDBSSGOnAgoda B" in {
        val featureAware       = mock[FeatureAware]
        val mockRequestContext = mock[RequestContext]
        when(setupContext.requestContext).thenReturn(mockRequestContext)
        when(mockRequestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.enableDBSSGOnAgoda).thenReturn(true)
        when(
          proxy
            .getPartners(any[PartnersRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Right(Set(dbssg))))
        val partnerResponse =
          service.getPartners(Some(-1), whiteLabelId, Some("HK"), externalLoyaltyHeaders, setupContext)
        partnerResponse.map { realResult =>
          verify(proxy).getPartners(eqTo(expectedRequest), eqTo(externalLoyaltyHeaders))(eqTo(mockRequestContext))
          realResult shouldEqual Right(Set(dbssg))
        }
      }

      "Return correct response in Partner api with exp enableDBSSGOnAgoda B and enableDBSHKOnAgoda B " in {
        val featureAware       = mock[FeatureAware]
        val mockRequestContext = mock[RequestContext]
        when(setupContext.requestContext).thenReturn(mockRequestContext)
        when(mockRequestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.enableDBSHKOnAgoda).thenReturn(true)
        when(featureAware.enableDBSSGOnAgoda).thenReturn(true)
        when(
          proxy
            .getPartners(any[PartnersRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Right(partnersResponse)))
        val partnerResponse =
          service.getPartners(Some(-1), whiteLabelId, Some("HK"), externalLoyaltyHeaders, setupContext)
        partnerResponse.map { realResult =>
          verify(proxy).getPartners(eqTo(expectedRequest), eqTo(externalLoyaltyHeaders))(eqTo(mockRequestContext))
          realResult shouldEqual Right(partnersResponse)
        }
      }
      "Return and Log correctly - Member balance api when feature is not enabled" in {
        val mockRequestContext = mock[RequestContext]
        val features           = mock[FeatureAware]
        when(setupContext.requestContext).thenReturn(mockRequestContext)
        when(mockRequestContext.featureAware).thenReturn(Some(features))
        val whiteLabelInfo = getMockWhiteLabelInfo(externalLoyaltyBalanceEnabled = false)
        when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.ANA)
        when(setupContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
        when(mockRequestContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.ANA, new FeaturesConfiguration))
        val whiteLabelID = WhiteLabel.ANA.id
        val errorRes = LoyaltyErrorResponse(
          errorName =
            s"Member balance workflow is only configured for Agoda and whitelabel id received is: $whiteLabelID"
        )
        when(
          proxy
            .getMemberBalances(any[MemberBalanceRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Right(memberBalanceExpectedResponse)))
        val memberBalanceResponse =
          service.getMemberBalances(partnerClaim, whiteLabelID, memberId, externalLoyaltyHeaders, setupContext)
        memberBalanceResponse.map { realResult =>
          realResult shouldEqual Left(errorRes)
        }
      }
    }

    "Return and Log - Not External Loyalty flow" should {
      val partner: Partner = Partner(
        partnerCode = Some("KTC"),
        name = Some("KTC"),
        logoImageUrl = Some("KTC"),
        loginRedirectUrl = Some("KTC"),
        cmsId = Some(13777)
      )
      val partnerExpectedRequest: PartnersRequest            = PartnersRequest(cId, whiteLabelId, origin)
      val partnerExpectedResponse: Set[Partner]              = Set(partner)
      val partnerConfigExpectedRequest: PartnerConfigRequest = PartnerConfigRequest(partnerClaim)
      val partnerConfigExpectedResponse: PartnerConfigResponse = PartnerConfigResponse(
        PaymentLimitations(
          partnerCode = "1",
          paymentMethodIds = Some(Seq(1, 2, 3)),
          paymentMethodNames = Some(Seq("Visa")),
          binRanges = Some(
            Seq(
              BinRange(
                bookingStartDate = Some(LocalDate.now()),
                bookingEndDate = None,
                binList = Some(Seq(BinListRange(from = 111, to = 222)))
              )
            )
          ),
          errorCMSId = 1,
          alternatePaymentCMSId = Some(0)
        )
      )
      val memberBalanceExpectedRequest: MemberBalanceRequest = MemberBalanceRequest(whiteLabelId.get, partnerClaim)
      val memberBalanceExpectedResponse: Seq[SubLoyaltyProgram] = Seq(
        SubLoyaltyProgram(
          pointsBalance = Some(1700.40),
          disabled = Some(false),
          programId = Some("1"),
          programName = Some("KTC"),
          minimumPointsToRedeem = Some(100.20),
          programLogoUrl = Some("logourl")
        )
      )
      val setupBookingRequest: SetupBookingRequest   = mock[SetupBookingRequest]
      implicit val setupContext: SetupBookingContext = mock[SetupBookingContext]
      val messagesBag                                = mock[MessagesBag]
      val requestContext = RequestContext(
        languageId = 1,
        locale = "en-us",
        clientId = 1,
        path = "",
        platformId = 4,
        userContext = Some(UserContextMock.value.copy(requestOrigin = "TH")),
        messagesBag = messagesBag,
        whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration),
        xForwardedForIp = "0.0.0.0",
        bookingCreationContext = None
      )

      when(setupBookingRequest.enabledFeatures).thenReturn(
        Some(Seq())
      ) // Removed Feature flag for Not External loyalty flow
      when(setupBookingRequest.loyaltyRequest).thenReturn(None)
      when(setupBookingRequest.userContext).thenReturn(Some(UserContextMock.value))
      when(setupContext.requestContext).thenReturn(requestContext)
      when(setupContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration))
      when(setupContext.bookingFlowType).thenReturn(BookingFlow.Unknown) // Not External Loyalty flow

      "Not External Loyalty flow - Create External Loyalty" in {
        val createExternalLoyalty = service.createExternalLoyalty(
          setupBookingRequest,
          ExternalLoyaltyResponse(
            Some(partnerExpectedResponse),
            Some(partnerConfigExpectedResponse),
            Some(memberBalanceExpectedResponse),
            Some(loyaltyErrorResponse)
          ),
          mockProduct,
          None
        )
        createExternalLoyalty.flatMap(_.loyaltyPartners) shouldEqual None
      }

      "Not External Loyalty flow - Get External Loyalty Response" in {
        val externalLoyaltyResponse = service.getExternalLoyaltyResponse(setupBookingRequest, setupContext)
        externalLoyaltyResponse.map { realResult =>
          realResult.externalLoyaltyPartners shouldEqual None
        }
      }
    }

    "log on failure" should {
      implicit val setupContext: SetupBookingContext = mock[SetupBookingContext]
      val features                                   = mock[FeatureAware]
      "Partner api failure" in {
        when(
          proxy.getPartners(any[PartnersRequest], any[ExternalLoyaltyHeaders])(any[RequestContext])
        ).thenReturn(Future.successful(Left(proxyResult)))
        service
          .getPartners(cId, whiteLabelId, origin, externalLoyaltyHeaders, setupContext)
          .map { result =>
            verify(proxy).getPartners(any(), any())(any())
            result shouldBe Left(
              LoyaltyErrorResponse(error.error.cmsId, error.error.errorName)
            )
          }
      }

      "Partner config api failure" in {
        val captorPartnerConfigRequest   = ArgumentCaptor.forClass(classOf[PartnerConfigRequest])
        val captorExternalLoyaltyHeaders = ArgumentCaptor.forClass(classOf[ExternalLoyaltyHeaders])
        when(
          proxy
            .getPartnerConfig(
              PartnerConfigRequest(partnerClaim, memberId, Some(WhiteLabel.Agoda.id)),
              externalLoyaltyHeaders
            )(setupContext.requestContext)
        ).thenReturn(Future.successful(Left(proxyResult)))
        service
          .getPartnerConfig(partnerClaim, WhiteLabel.Agoda.id, memberId, externalLoyaltyHeaders, setupContext)
          .map { result =>
            verify(proxy).getPartnerConfig(any(), any())(any())
            verify(proxy).getPartnerConfig(
              captorPartnerConfigRequest.capture(),
              captorExternalLoyaltyHeaders.capture()
            )(any())
            assert(captorPartnerConfigRequest.getValue.asInstanceOf[PartnerConfigRequest].memberId == memberId)
            assert(
              captorPartnerConfigRequest.getValue.asInstanceOf[PartnerConfigRequest].whiteLabelId == whiteLabelId
            )
            assert(
              captorPartnerConfigRequest.getValue.asInstanceOf[PartnerConfigRequest].partnerClaim == partnerClaim
            )
            assert(
              captorExternalLoyaltyHeaders.getValue
                .asInstanceOf[ExternalLoyaltyHeaders]
                .sessionId == externalLoyaltyHeaders.sessionId
            )
            assert(
              captorExternalLoyaltyHeaders.getValue
                .asInstanceOf[ExternalLoyaltyHeaders]
                .correlationId == externalLoyaltyHeaders.correlationId
            )
            result shouldBe Left(
              LoyaltyErrorResponse(error.error.cmsId, error.error.errorName)
            )
          }
      }

      "Member Balance api failure" in {
        val captor                       = ArgumentCaptor.forClass(classOf[MemberBalanceRequest])
        val captorExternalLoyaltyHeaders = ArgumentCaptor.forClass(classOf[ExternalLoyaltyHeaders])

        val experimentData = mock[ExperimentData]

        when(experimentData.force).thenReturn(Some(Map("ABC-123" -> "A", "XYZ-456" -> "B")))
        when(experimentData.forceByVariant).thenReturn(Some("B"))
        when(experimentData.forceOnIntegrationRun).thenReturn(Some(true))
        when(experimentData.forceOnZeroTraffic).thenReturn(Some(true))

        val mockRequestContext = mock[RequestContext]
        when(setupContext.requestContext).thenReturn(mockRequestContext)
        when(mockRequestContext.featureAware).thenReturn(Some(features))
        when(mockRequestContext.experimentData).thenReturn(Some(experimentData))

        val whiteLabelInfo = getMockWhiteLabelInfo(externalLoyaltyBalanceEnabled = true)
        when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
        when(setupContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
        when(
          proxy
            .getMemberBalances(
              MemberBalanceRequest(whiteLabelId.get, partnerClaim, memberId, experimentInfoOpt),
              externalLoyaltyHeaders
            )(
              mockRequestContext
            )
        ).thenReturn(Future.successful(Left(proxyResult)))
        service
          .getMemberBalances(partnerClaim, WhiteLabel.Agoda.id, Some(123), externalLoyaltyHeaders, setupContext)
          .map { result =>
            verify(proxy, times(1)).getMemberBalances(captor.capture(), captorExternalLoyaltyHeaders.capture())(any())
            assert(captor.getValue.asInstanceOf[MemberBalanceRequest].memberId == memberId)
            assert(captor.getValue.asInstanceOf[MemberBalanceRequest].whiteLabelId == whiteLabelId.get)
            assert(captor.getValue.asInstanceOf[MemberBalanceRequest].partnerClaim == partnerClaim)
            assert(captor.getValue.asInstanceOf[MemberBalanceRequest].experimentInfo == experimentInfoOpt)
            assert(
              captorExternalLoyaltyHeaders.getValue
                .asInstanceOf[ExternalLoyaltyHeaders]
                .sessionId == externalLoyaltyHeaders.sessionId
            )
            assert(
              captorExternalLoyaltyHeaders.getValue
                .asInstanceOf[ExternalLoyaltyHeaders]
                .correlationId == externalLoyaltyHeaders.correlationId
            )
            result shouldBe Left(
              LoyaltyErrorResponse(error.error.cmsId, error.error.errorName)
            )
          }
      }

      "External loyalty request" should {

        val setupBookingRequest: SetupBookingRequest   = mock[SetupBookingRequest]
        implicit val setupContext: SetupBookingContext = mock[SetupBookingContext]
        val messagesBag                                = mock[MessagesBag]

        val requestContext = RequestContext(
          languageId = 1,
          locale = "en-us",
          clientId = 1,
          path = "",
          userContext = Some(UserContextMock.value.copy(requestOrigin = "TH")),
          messagesBag = messagesBag,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration),
          xForwardedForIp = "0.0.0.0",
          bookingCreationContext = None
        )

        when(setupBookingRequest.enabledFeatures).thenReturn(Some(Seq(service.ExternalLoyaltyFeatureFlag)))
        when(setupContext.requestContext).thenReturn(requestContext)
        when(setupContext.whiteLabelInfo).thenReturn(WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration))

        "Is an External loyalty flow" in {
          val whiteLabelInfo = getMockWhiteLabelInfo(isDirectPartner = true)
          when(setupContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
          when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
          when(setupContext.requestContext).thenReturn(requestContext.copy(whiteLabelInfo = whiteLabelInfo))
          val result = service
            .isExternalLoyaltyFlow(setupBookingRequest, setupContext)
          result shouldBe true
        }

        "Is an External loyalty flow even when external loyalty feature is disabled in upstream" in {
          val whiteLabelInfo = getMockWhiteLabelInfo(isDirectPartner = true)
          when(setupContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
          when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
          when(setupBookingRequest.enabledFeatures).thenReturn(Some(Seq("some-other-feature")))

          val result = service
            .isExternalLoyaltyFlow(setupBookingRequest, setupContext)
          result shouldBe false
        }

        "Is an External loyalty flow even when external loyalty feature is enabled in upstream" in {
          val whiteLabelInfo = getMockWhiteLabelInfo(isDirectPartner = true)
          when(setupContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
          when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
          when(setupBookingRequest.enabledFeatures).thenReturn(Some(Seq("enable_external_loyalty")))

          val result = service
            .isExternalLoyaltyFlow(setupBookingRequest, setupContext)
          result shouldBe true
        }

        "Is an External loyalty flow for Non WebPhone Platform" in {
          val whiteLabelInfo = getMockWhiteLabelInfo(isDirectPartner = true)
          when(setupContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
          when(setupContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
          val result = service
            .isExternalLoyaltyFlow(setupBookingRequest, setupContext)
          result shouldBe true
        }

        "Not an External loyalty flow" in {
          when(setupContext.bookingFlowType).thenReturn(BookingFlow.Unknown)
          val result2 = service
            .isExternalLoyaltyFlow(setupBookingRequest, setupContext)
          result2 shouldBe false
        }
        "Not an External loyalty flow for Non Agoda WhiteLabel" in {
          when(setupContext.whiteLabelInfo).thenReturn(
            WhiteLabelInfo(WhiteLabel.RMTestarossa, new FeaturesConfiguration)
          )
          val result2 = service
            .isExternalLoyaltyFlow(setupBookingRequest, setupContext)
          result2 shouldBe false
        }
        "Not an External loyalty flow for ExternalLoyaltyFeatureFlag not enabled" in {
          when(setupBookingRequest.enabledFeatures).thenReturn(None)
          val result2 = service
            .isExternalLoyaltyFlow(setupBookingRequest, setupContext)
          result2 shouldBe false
        }
        "External loyalty flow for ANA when Experiment is enabled" in {
          when(mockExperimentManagerProvider.get()).thenReturn(mockExperimentManager)

          val mockRequestContext = mock[RequestContext]
          when(setupContext.requestContext).thenReturn(mockRequestContext)
          when(mockRequestContext.featureAware).thenReturn(Some(featureAware))
          val whiteLabelInfo = getMockWhiteLabelInfo(burnEnabled = true, earnEnabled = true)
          when(setupContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
          when(mockRequestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
          when(mockRequestContext.experimentData).thenReturn(None)

          val result2 = service
            .isExternalLoyaltyFlow(setupBookingRequest, setupContext)
          result2 shouldBe true
        }
        "External loyalty flow for ANA when Experiment is enabled and Burn feature flag enabled" in {
          when(mockExperimentManagerProvider.get()).thenReturn(mockExperimentManager)

          val mockRequestContext = mock[RequestContext]
          when(setupContext.requestContext).thenReturn(mockRequestContext)
          when(mockRequestContext.featureAware).thenReturn(Some(featureAware))
          val whiteLabelInfo = getMockWhiteLabelInfo(burnEnabled = true, earnEnabled = false)
          when(setupContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
          when(mockRequestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
          when(mockRequestContext.experimentData).thenReturn(None)

          val result2 = service
            .isExternalLoyaltyFlow(setupBookingRequest, setupContext)
          result2 shouldBe true
        }
        "External loyalty flow for ANA when Experiment is enabled and Earn feature flag enabled" in {
          when(mockExperimentManagerProvider.get()).thenReturn(mockExperimentManager)

          val mockRequestContext = mock[RequestContext]
          when(setupContext.requestContext).thenReturn(mockRequestContext)
          when(mockRequestContext.featureAware).thenReturn(Some(featureAware))
          val whiteLabelInfo = getMockWhiteLabelInfo(earnEnabled = true)
          when(setupContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
          when(mockRequestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
          when(mockRequestContext.experimentData).thenReturn(None)

          val result2 = service
            .isExternalLoyaltyFlow(setupBookingRequest, setupContext)
          result2 shouldBe true
        }

        "External loyalty flow for Clubtravel is disabled when earnwithburn feature is disabled" in {
          when(mockExperimentManagerProvider.get()).thenReturn(mockExperimentManager)

          val mockRequestContext = mock[RequestContext]
          when(setupContext.requestContext).thenReturn(mockRequestContext)
          when(mockRequestContext.featureAware).thenReturn(Some(featureAware))
          val whiteLabelInfo = getMockWhiteLabelInfo()
          when(setupContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
          when(mockRequestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
          when(mockRequestContext.experimentData).thenReturn(None)

          val result = service
            .isExternalLoyaltyFlow(setupBookingRequest, setupContext)
          result shouldBe false
        }

        "External loyalty flow for Clubtravel is enabled when PartnerLoyaltyEarnWithBurn is enabled" in {
          when(mockExperimentManagerProvider.get()).thenReturn(mockExperimentManager)

          val mockRequestContext = mock[RequestContext]
          when(mockRequestContext.featureAware).thenReturn(Some(featureAware))
          when(setupContext.requestContext).thenReturn(mockRequestContext)
          val whiteLabelInfo = getMockWhiteLabelInfo(earnBurnEnabled = true)
          when(setupContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
          when(mockRequestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
          when(mockRequestContext.experimentData).thenReturn(None)

          val result = service
            .isExternalLoyaltyFlow(setupBookingRequest, setupContext)
          result shouldBe true
        }

        "isExternalLoyaltyWhiteLabel" should {
          "return correctly" in {
            val requestContext = mock[RequestContext]
            when(requestContext.featureAware).thenReturn(Some(featureAware))
            val whiteLabelInfo = getMockWhiteLabelInfo(burnEnabled = false, earnEnabled = false, earnBurnEnabled = true)
            when(setupContext.whiteLabelInfo).thenReturn(whiteLabelInfo)

            val setupBookingContext = mock[SetupBookingContext]
            when(setupBookingContext.requestContext).thenReturn(requestContext)
            when(setupBookingContext.whiteLabelInfo).thenReturn(whiteLabelInfo)

            val result = service.isExternalLoyaltyWhiteLabel(setupBookingContext)

            verify(whiteLabelInfo, times(3)).isFeatureEnabled(any(), any(), any(), any())
            result shouldBe true
          }
        }
      }

      "Fetch Payment limitation CMSIds List" should {

        "With Both error and alternate CMS Id" in {
          val paymentLimitation: PaymentLimitations = PaymentLimitations(
            partnerCode = "1",
            errorCMSId = 1,
            alternatePaymentCMSId = Some(2)
          )
          val result = service.getPaymentLimitationCMSIds(Some(paymentLimitation))
          result.contains(paymentLimitation.errorCMSId) shouldBe true
          result.contains(paymentLimitation.alternatePaymentCMSId.getOrElse(0)) shouldBe true
          result.length shouldBe 2
        }

        "With only Error CMS Id" in {
          val paymentLimitation: PaymentLimitations = PaymentLimitations(
            partnerCode = "1",
            errorCMSId = 1,
            alternatePaymentCMSId = None
          )
          val result = service.getPaymentLimitationCMSIds(Some(paymentLimitation))
          result.contains(paymentLimitation.errorCMSId) shouldBe true
          result.contains(paymentLimitation.alternatePaymentCMSId.getOrElse(0)) shouldBe false
          result.length shouldBe 1
        }

        "With None Payment limitation" in {
          val optionPaymentLimitation: Option[PaymentLimitations] = None
          val result                                              = service.getPaymentLimitationCMSIds(optionPaymentLimitation)
          result.length shouldBe 0
        }
      }

      "External Loyalty Payment limitation check" should {

        val setupBookingRequest: SetupBookingRequest = mock[SetupBookingRequest]
        val paymentLimitation: PaymentLimitations = PaymentLimitations(
          partnerCode = "1",
          paymentMethodIds = Some(Seq(1, 2, 3, 4)),
          paymentMethodNames = Some(Seq("Master", "Visa")),
          binRanges = Some(
            Seq(
              BinRange(
                bookingStartDate = Some(LocalDate.now()),
                bookingEndDate = None,
                binList = Some(Seq(BinListRange(from = 111, to = 113), BinListRange(from = 122, to = 124)))
              )
            )
          ),
          errorCMSId = 1,
          alternatePaymentCMSId = Some(0)
        )
        "External Loyalty Payment limitation check - Correct" in {
          when(setupBookingRequest.paymentRequest).thenReturn(Some(PaymentRequest(ccBin = Some("123"))))

          val result = service.applyExternalLoyaltyPaymentLimitation(setupBookingRequest, Some(paymentLimitation))
          result.cmsId shouldBe None
        }
        "External Loyalty Payment limitation check - Incorrect" in {

          when(setupBookingRequest.paymentRequest).thenReturn(Some(PaymentRequest(ccBin = Some("333"))))

          val result = service.applyExternalLoyaltyPaymentLimitation(setupBookingRequest, Some(paymentLimitation))
          result.cmsId shouldBe Some(paymentLimitation.errorCMSId)
        }

        "External Loyalty Payment limitation check - Correct as No Payment limitation" in {
          when(setupBookingRequest.paymentRequest).thenReturn(Some(PaymentRequest(ccBin = Some("333"))))

          val result = service.applyExternalLoyaltyPaymentLimitation(setupBookingRequest, None)
          result.cmsId shouldBe None
        }

        "External Loyalty Payment limitation check - Correct as No Bin Range" in {
          val paymentLimitationWithoutBinRange: PaymentLimitations = PaymentLimitations(
            partnerCode = "1",
            paymentMethodIds = Some(Seq(1, 2, 3, 4)),
            paymentMethodNames = Some(Seq("Master", "Visa")),
            errorCMSId = 1,
            alternatePaymentCMSId = Some(0)
          )
          when(setupBookingRequest.paymentRequest).thenReturn(Some(PaymentRequest(ccBin = Some("333"))))
          val result =
            service.applyExternalLoyaltyPaymentLimitation(setupBookingRequest, Some(paymentLimitationWithoutBinRange))
          result.cmsId shouldBe None
        }

        "External Loyalty Payment limitation check - Correct as Empty list Bin Range" in {
          val paymentLimitationEmptyBinRange: PaymentLimitations = PaymentLimitations(
            partnerCode = "1",
            paymentMethodIds = Some(Seq()),
            paymentMethodNames = Some(Seq()),
            errorCMSId = 1,
            binRanges = Some(Seq()),
            alternatePaymentCMSId = Some(0)
          )
          when(setupBookingRequest.paymentRequest).thenReturn(Some(PaymentRequest(ccBin = Some("333"))))
          val result =
            service.applyExternalLoyaltyPaymentLimitation(setupBookingRequest, Some(paymentLimitationEmptyBinRange))
          result.cmsId shouldBe None
          result.alternateCmsId shouldBe None
        }

        "External Loyalty Payment limitation check - Correct as first bin is valid bin range" in {
          val paymentLimitationWithValidBinRange: PaymentLimitations = PaymentLimitations(
            partnerCode = "1",
            paymentMethodIds = Some(Seq(1, 2, 3, 4)),
            paymentMethodNames = Some(Seq("Master", "Visa")),
            binRanges = Some(
              Seq(
                BinRange(
                  bookingStartDate = Some(LocalDate.now()),
                  bookingEndDate = None,
                  binList = Some(Seq(BinListRange(from = 111, to = 111), BinListRange(from = 222, to = 222)))
                )
              )
            ),
            errorCMSId = 1,
            alternatePaymentCMSId = Some(0)
          )
          when(setupBookingRequest.paymentRequest).thenReturn(Some(PaymentRequest(ccBin = Some("111"))))
          val result =
            service.applyExternalLoyaltyPaymentLimitation(setupBookingRequest, Some(paymentLimitationWithValidBinRange))
          result.cmsId shouldBe None
        }

        "External Loyalty Payment limitation check - Correct as second bin is valid bin range" in {
          val paymentLimitationWithValidBinRange: PaymentLimitations = PaymentLimitations(
            partnerCode = "1",
            paymentMethodIds = Some(Seq(1, 2, 3, 4)),
            paymentMethodNames = Some(Seq("Master", "Visa")),
            binRanges = Some(
              Seq(
                BinRange(
                  bookingStartDate = Some(LocalDate.now()),
                  bookingEndDate = None,
                  binList = Some(Seq(BinListRange(from = 111, to = 111), BinListRange(from = 222, to = 222)))
                )
              )
            ),
            errorCMSId = 1,
            alternatePaymentCMSId = Some(0)
          )
          when(setupBookingRequest.paymentRequest).thenReturn(Some(PaymentRequest(ccBin = Some("222"))))
          val result =
            service.applyExternalLoyaltyPaymentLimitation(setupBookingRequest, Some(paymentLimitationWithValidBinRange))
          result.cmsId shouldBe None
        }

        "External Loyalty Payment limitation check - Correct as bin is valid bin range" in {
          val paymentLimitationWithValidBinRange: PaymentLimitations = PaymentLimitations(
            partnerCode = "1",
            paymentMethodIds = Some(Seq(1, 2, 3, 4)),
            paymentMethodNames = Some(Seq("Master", "Visa")),
            binRanges = Some(
              Seq(
                BinRange(
                  bookingStartDate = Some(LocalDate.now()),
                  bookingEndDate = None,
                  binList = Some(Seq(BinListRange(from = 111, to = 111)))
                )
              )
            ),
            errorCMSId = 1,
            alternatePaymentCMSId = Some(0)
          )
          when(setupBookingRequest.paymentRequest).thenReturn(Some(PaymentRequest(ccBin = Some("111"))))
          val result =
            service.applyExternalLoyaltyPaymentLimitation(setupBookingRequest, Some(paymentLimitationWithValidBinRange))
          result.cmsId shouldBe None
        }

        "External Loyalty Payment limitation check - Incorrect as bin is invalid bin range" in {
          val paymentLimitationWithInValidBinRange: PaymentLimitations = PaymentLimitations(
            partnerCode = "1",
            paymentMethodIds = Some(Seq(1, 2, 3, 4)),
            paymentMethodNames = Some(Seq("Master", "Visa")),
            binRanges = Some(
              Seq(
                BinRange(
                  bookingStartDate = Some(LocalDate.now()),
                  bookingEndDate = None,
                  binList = Some(Seq(BinListRange(from = 111, to = 111)))
                )
              )
            ),
            errorCMSId = 1,
            alternatePaymentCMSId = Some(0)
          )
          when(setupBookingRequest.paymentRequest).thenReturn(Some(PaymentRequest(ccBin = Some("222"))))
          val result =
            service.applyExternalLoyaltyPaymentLimitation(
              setupBookingRequest,
              Some(paymentLimitationWithInValidBinRange)
            )
          result.cmsId shouldBe Some(paymentLimitation.errorCMSId)
        }

        "External Loyalty Payment limitation check - Incorrect as CC Date is not Eligible" in {
          val paymentLimitationWithInValidBinRange: PaymentLimitations = PaymentLimitations(
            partnerCode = "1",
            paymentMethodIds = Some(Seq(1, 2, 3, 4)),
            paymentMethodNames = Some(Seq("Master", "Visa")),
            binRanges = Some(
              Seq(
                BinRange(
                  bookingStartDate = None,
                  bookingEndDate = Some(LocalDate.now().minusDays(10)), // Incorrect as End Date is before Current Date
                  binList = Some(Seq(BinListRange(from = 111, to = 111)))
                )
              )
            ),
            errorCMSId = 1,
            alternatePaymentCMSId = Some(0)
          )
          when(setupBookingRequest.paymentRequest).thenReturn(Some(PaymentRequest(ccBin = Some("111"))))
          val result =
            service.applyExternalLoyaltyPaymentLimitation(
              setupBookingRequest,
              Some(paymentLimitationWithInValidBinRange)
            )
          result.cmsId shouldBe Some(paymentLimitation.errorCMSId)
        }

      }

      "External Loyalty Payment Limitation - CC Date Eligibility check" should {
        "Start Date & End Date both provided with - Start Date is before than Current Date and End Date is after than Current Date" in {
          val startDate = Some(LocalDate.now().minusDays(10))
          val endDate   = Some(LocalDate.now().plusDays(10))
          val result    = service.checkCcDateEligibilty(startDate, endDate)
          result shouldBe true
        }
        "Start Date & End Date both provided with - Start Date is Current Date and End Date is after than Current Date" in {
          val startDate = Some(LocalDate.now())
          val endDate   = Some(LocalDate.now().plusDays(10))
          val result    = service.checkCcDateEligibilty(startDate, endDate)
          result shouldBe true
        }
        "Start Date & End Date both provided with - Start Date is before than Current Date and End Date is Current Date" in {
          val startDate = Some(LocalDate.now().minusDays(10))
          val endDate   = Some(LocalDate.now())
          val result    = service.checkCcDateEligibilty(startDate, endDate)
          result shouldBe true
        }
        "Start Date & End Date both provided with - Start Date is Current Date and End Date is Current Date" in {
          val startDate = Some(LocalDate.now())
          val endDate   = Some(LocalDate.now())
          val result    = service.checkCcDateEligibilty(startDate, endDate)
          result shouldBe true
        }
        "Start Date & End Date both provided with - Start Date is after Current Date and End Date is after Start Date" in {
          val startDate = Some(LocalDate.now().plusDays(10))
          val endDate   = Some(LocalDate.now().plusDays(20))
          val result    = service.checkCcDateEligibilty(startDate, endDate)
          result shouldBe false
        }
        "Start Date & End Date both provided with - Start Date is after Current Date and End Date is before Start Date" in {
          val startDate = Some(LocalDate.now().plusDays(10))
          val endDate   = Some(LocalDate.now().plusDays(5))
          val result    = service.checkCcDateEligibilty(startDate, endDate)
          result shouldBe false
        }
        "Start Date & End Date both provided with - Start Date is before End Date and End Date is before Current Date" in {
          val startDate = Some(LocalDate.now().minusDays(20))
          val endDate   = Some(LocalDate.now().minusDays(10))
          val result    = service.checkCcDateEligibilty(startDate, endDate)
          result shouldBe false
        }
        "Start Date & End Date both provided with - Start Date is after End Date and End Date is before Current Date" in {
          val startDate = Some(LocalDate.now().minusDays(5))
          val endDate   = Some(LocalDate.now().minusDays(10))
          val result    = service.checkCcDateEligibilty(startDate, endDate)
          result shouldBe false
        }
        "Only Start Date provided with - Start Date is Current Date" in {
          val startDate = Some(LocalDate.now())
          val result    = service.checkCcDateEligibilty(startDate, None)
          result shouldBe true
        }
        "Only Start Date provided with - Start Date is before Current Date" in {
          val startDate = Some(LocalDate.now().minusDays(10))
          val result    = service.checkCcDateEligibilty(startDate, None)
          result shouldBe true
        }
        "Only Start Date provided with - Start Date is after Current Date" in {
          val startDate = Some(LocalDate.now().plusDays(10))
          val result    = service.checkCcDateEligibilty(startDate, None)
          result shouldBe false
        }
        "Only End Date provided with - End Date is Current Date" in {
          val endDate = Some(LocalDate.now())
          val result  = service.checkCcDateEligibilty(None, endDate)
          result shouldBe true
        }
        "Only End Date provided with - End Date is after Current Date" in {
          val endDate = Some(LocalDate.now().plusDays(10))
          val result  = service.checkCcDateEligibilty(None, endDate)
          result shouldBe true
        }
        "Only End Date provided with - End Date is before Current Date" in {
          val endDate = Some(LocalDate.now().minusDays(10))
          val result  = service.checkCcDateEligibilty(None, endDate)
          result shouldBe false
        }
        "Both Start and End Date not provided" in {
          val result = service.checkCcDateEligibilty(None, None)
          result shouldBe true
        }
      }
    }

    "getExperimentInfo" should {
      "map correctly" in {
        val experimentData = mock[ExperimentData]

        when(experimentData.force).thenReturn(Some(Map("ABC-123" -> "A", "XYZ-456" -> "B")))
        when(experimentData.forceByVariant).thenReturn(Some("B"))
        when(experimentData.forceOnIntegrationRun).thenReturn(Some(true))
        when(experimentData.forceOnZeroTraffic).thenReturn(Some(true))

        val requestContext = mock[RequestContext]
        when(requestContext.experimentData).thenReturn(Some(experimentData))

        val result = service.getExperimentInfo(requestContext)

        result.value shouldBe ExperimentInfo(
          forceUserVariant = Some("B"),
          forcedExperiments = Some(Seq(ForcedExperiment("ABC-123", "A"), ForcedExperiment("XYZ-456", "B"))),
          forceOnIntegrationRun = true,
          forceOnZeroTraffic = true
        )
      }
    }
  }

  private def getMockWhiteLabelInfo(
      burnEnabled: Boolean = false,
      earnEnabled: Boolean = false,
      earnBurnEnabled: Boolean = false,
      externalLoyaltyBalanceEnabled: Boolean = false,
      isDirectPartner: Boolean = false,
      whitelabelId: WhiteLabel = WhiteLabel.Agoda
  ): WhiteLabelInfo = {
    val whiteLabelInfo = mock[WhiteLabelInfo]

    when(whiteLabelInfo.whiteLabelId)
      .thenReturn(
        whitelabelId
      )

    when(
      whiteLabelInfo.isFeatureEnabled(
        ArgumentMatchers.eq(WhiteLabelFeatureName.PartnerLoyaltyBurn),
        any(),
        any(),
        any()
      )
    )
      .thenReturn(burnEnabled)
    when(
      whiteLabelInfo.isFeatureEnabled(
        ArgumentMatchers.eq(WhiteLabelFeatureName.PartnerLoyaltyEarn),
        any(),
        any(),
        any()
      )
    )
      .thenReturn(earnEnabled)
    when(
      whiteLabelInfo.isFeatureEnabled(
        ArgumentMatchers.eq(WhiteLabelFeatureName.PartnerLoyaltyEarnWithBurn),
        any(),
        any(),
        any()
      )
    )
      .thenReturn(earnBurnEnabled)
    when(
      whiteLabelInfo.isFeatureEnabled(
        ArgumentMatchers.eq(WhiteLabelFeatureName.ExternalLoyaltyMemberBalance),
        any(),
        any(),
        any()
      )
    )
      .thenReturn(externalLoyaltyBalanceEnabled)

    when(
      whiteLabelInfo.isFeatureEnabled(
        ArgumentMatchers.eq(WhiteLabelFeatureName.DirectPartners),
        any(),
        any(),
        any()
      )
    ).thenReturn(isDirectPartner)

    whiteLabelInfo
  }
}
