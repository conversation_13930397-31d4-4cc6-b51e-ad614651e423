package com.agoda.bapi.server.proxy

import akka.actor.ActorSystem
import akka.testkit.TestKit
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.extensions.MFAResult
import com.agoda.bapi.common.message.creation.{ActivityData, BookingCreationContext, BorBookingResult, ConfirmedBookingData, ContinuePaymentRequest, FlightData, FraudResult, FraudResultStatusEnum, GetStatusRequest, ManualTicketingData, RejectedBookingData, ThirdPartyStatus, UserAgent}
import com.agoda.bapi.common.message.{DeviceContext, DevicePlatform, ExperimentData, creation}
import com.agoda.bapi.common.model.payment.{PaymentToken, PaymentTokenType}
import com.agoda.bapi.common.model.{UserContext, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.client.common.api.NonPciApi
import com.agoda.bapi.creation.client.common.model.{BorBookingResult => ClientBorBookingResult, CegFastTrackCreateResult, ContinuePaymentRequest => ClientContinuePaymentRequest, GenericProductCreateResult, GetStatusRequest => ClientGetStatusRequest, MFAResult => ClientMFAResult, PriceFreezeBaseBookingCreationResult, RejectedReason}
import com.agoda.commons.http.client.v2.RequestSettings
import com.agoda.mpb.common.BorBookingStatus
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import com.typesafe.config.ConfigFactory
import org.mockito.ArgumentMatchers.{any, eq => argsEq}
import org.mockito.Mockito._
import org.mockito.{ArgumentMatcher, ArgumentMatchers}
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpecLike
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future

class BookingCreationByDcProxySpec extends TestKit(ActorSystem("Test")) with AsyncWordSpecLike with Matchers {

  trait Fixture extends MockitoSugar {
    implicit val requestContext: RequestContext = mock[RequestContext]
    val mockFeatureAware                        = mock[FeatureAware]
    val mockBcreClient                          = mock[NonPciApi[Future]]
    val bcreByDcProxy =
      new BookingCreationByDcProxyImpl(Map("bk" -> mockBcreClient), ConfigFactory.load(), system)
    val mockDefaultRejectReason = Some(RejectedReason(code = 1, message = "mockMsg", subErrorCode = Some(2)))
    val invalidCardClassRejectReason = Some(
      RejectedReason(
        ErrorCode.InvalidCardClass.id,
        "invalid card class",
        Some(ErrorCode.InvalidCardClass.id)
      )
    )

    def initMockedBehaviours(
        extraMockParam: Map[String, String] = Map.empty,
        removePaymentResult: Boolean = false,
        isPartialSuccessAllowedResult: Boolean = false
    ): Unit = {
      val paymentResult =
        if (removePaymentResult) None
        else
          Some(
            com.agoda.bapi.creation.client.common.model.PaymentResult(
              com.agoda.bapi.creation.client.common.model.PaymentContinuation(1, Some(1), "123"),
              Some(
                com.agoda.bapi.creation.client.common.model
                  .Payment3DSResponse(Map("test" -> "test"), "test", Seq("test"), "test")
              ),
              Some(com.agoda.bapi.creation.client.common.model.PaymentRedirect("test"))
            )
          )

      val partnerPaymentResult = com.agoda.bapi.creation.client.common.model.PartnerPaymentResult(
        redirectPayment = Some(com.agoda.bapi.creation.client.common.model.PaymentRedirect("test")),
        thirdPartyStatus = Some(ThirdPartyStatus.Continue.id)
      )

      when(
        mockBcreClient.itineraryContinueBookingWorkflow(
          any[String],
          aPIKey = any[String],
          any[String],
          any[ClientContinuePaymentRequest],
          any[Option[String]]
        )(any[RequestSettings])
      )
        .thenReturn(
          Future.successful(
            com.agoda.bapi.creation.client.common.model.GetStatusResponse(
              success = true,
              errorCode = None,
              errorMessage = None,
              invalidRequestData =
                Some(Seq(com.agoda.bapi.creation.client.common.model.InvalidRequestDetail("test", true))),
              subErrorCode = None,
              itinerary = Some(
                com.agoda.bapi.creation.client.common.model.Itinerary(
                  itineraryId = 1,
                  flights = Seq(
                    com.agoda.bapi.creation.client.common.model
                      .FlightBooking(1, 1, rejectReason = mockDefaultRejectReason)
                  ),
                  cars = Seq(
                    com.agoda.bapi.creation.client.common.model
                      .VehicleBooking(1, 1, rejectReason = mockDefaultRejectReason)
                  ),
                  protections = Seq(com.agoda.bapi.creation.client.common.model.ProtectionBooking(1, 1, 1)),
                  activities = Seq(
                    com.agoda.bapi.creation.client.common.model
                      .ActivityBooking(1, 1, rejectReason = mockDefaultRejectReason)
                  ),
                  priceFreezes = Seq.empty[PriceFreezeBaseBookingCreationResult],
                  cegFastTracks = Seq.empty[CegFastTrackCreateResult],
                  addOns = Seq(GenericProductCreateResult(bookingId = 999001, bookingStatus = 0, productTypeId = 5)),
                  statusToken = "success",
                  itineraryAssociatedBookingsToken =
                    Some(com.agoda.bapi.creation.client.common.model.TokenMessage("success", 1)),
                  itineraryDate = None,
                  status = 1,
                  hotels = Seq(
                    com.agoda.bapi.creation.client.common.model.HotelBooking(
                      lineItemId = 1,
                      itineraryId = 1,
                      bookingId = 1,
                      productKey = Some("productKey1"),
                      bookingStatus = 1,
                      selfServiceUrl = "test",
                      rejectReason = invalidCardClassRejectReason
                    )
                  )
                )
              ),
              paymentResult = paymentResult,
              partnerPaymentResult = Some(partnerPaymentResult),
              isPartialSuccessAllowed = Some(isPartialSuccessAllowedResult),
              status = Option(com.agoda.bapi.creation.client.common.model.BookingServerStatus(1, 1))
            )
          )
        )

      when(
        mockBcreClient.getItineraryStatus(
          any[String],
          aPIKey = any[String],
          any[ClientGetStatusRequest],
          any[Option[String]],
          any[Option[String]]
        )(any[RequestSettings])
      )
        .thenReturn(
          Future.successful(
            com.agoda.bapi.creation.client.common.model.GetStatusResponse(
              success = true,
              errorCode = None,
              errorMessage = None,
              invalidRequestData =
                Some(Seq(com.agoda.bapi.creation.client.common.model.InvalidRequestDetail("test", true))),
              subErrorCode = None,
              itinerary = Some(
                com.agoda.bapi.creation.client.common.model.Itinerary(
                  1,
                  Seq(
                    com.agoda.bapi.creation.client.common.model
                      .FlightBooking(1, 1, None, rejectReason = mockDefaultRejectReason, Some("flightToken1"))
                  ),
                  Seq(
                    com.agoda.bapi.creation.client.common.model
                      .VehicleBooking(1, 1, rejectReason = mockDefaultRejectReason)
                  ),
                  Seq(com.agoda.bapi.creation.client.common.model.ProtectionBooking(1, 1, 1)),
                  Seq(
                    com.agoda.bapi.creation.client.common.model
                      .ActivityBooking(1, 1, rejectReason = mockDefaultRejectReason)
                  ),
                  Seq.empty[PriceFreezeBaseBookingCreationResult],
                  Seq.empty,
                  Seq(GenericProductCreateResult(bookingId = 999001, bookingStatus = 0, productTypeId = 5)),
                  "success",
                  Some(com.agoda.bapi.creation.client.common.model.TokenMessage("success", 1)),
                  None,
                  1,
                  Seq(
                    com.agoda.bapi.creation.client.common.model.HotelBooking(
                      lineItemId = 1,
                      itineraryId = 1,
                      bookingId = 1,
                      productKey = Some("productKey1"),
                      bookingStatus = 1,
                      selfServiceUrl = "test",
                      rejectReason = invalidCardClassRejectReason
                    )
                  )
                )
              ),
              paymentResult = paymentResult.map(
                _.copy(
                  requireCvv = Some(true),
                  thirdPartyStatus = Some(ThirdPartyStatus.Continue.id),
                  payment3ds2Type = extraMockParam
                    .get("payment3ds2Type")
                    .filter(_.forall(Character.isDigit))
                    .flatMap(p => Some(p.toInt))
                )
              ),
              partnerPaymentResult = Some(partnerPaymentResult),
              status = Option(com.agoda.bapi.creation.client.common.model.BookingServerStatus(1, 1)),
              borBookingResult = extraMockParam
                .get("borBookingResultStatus")
                .map(statusString => ClientBorBookingResult(Some(statusString.toInt))),
              mfaResult =
                extraMockParam.get("mfaResultRedirectUrl").map(url => ClientMFAResult(redirectUrl = Some(url))),
              isPartialSuccessAllowed = Some(isPartialSuccessAllowedResult)
            )
          )
        )

      when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))
    }
  }

  "getItineraryStatus" should {
    val whitelabelToken = Some("token")

    val request = GetStatusRequest(
      statusToken = "statusToken",
      correlationId = Option("correlationId"),
      userContext = Option(
        UserContext(
          languageId = 1,
          requestOrigin = "US",
          currency = "USD",
          nationalityId = 1,
          experimentData = Option(
            ExperimentData(
              userId = "1",
              deviceTypeId = "WEB",
              memberId = Option("member-1"),
              trafficGroup = Option("TG-1"),
              cId = Option("CID"),
              aId = Option("aID"),
              serverName = Option("Server")
            )
          )
        )
      ),
      bookingContext = Option(
        BookingCreationContext(
          sessionId = "session1",
          userAgent = UserAgent(
            origin = "origin",
            osName = "osName",
            osVersion = "osVersion",
            browserName = "browserName",
            browserLanguage = "browserLanguage",
            browserVersion = "browserVersion",
            browserSubVersion = "browserSubVersion",
            browserBuildNumber = "browserBuildNumber",
            deviceBrand = "deviceBrand",
            deviceModel = "deviceModel",
            deviceTypeId = 1,
            isMobile = Some(true),
            isTouch = Some(true),
            additionalInfo = Option("additionalInfo")
          )
        )
      ),
      deviceContext = Some(DeviceContext(DevicePlatform.Unknown))
    )

    val mappedRequest = com.agoda.bapi.creation.client.common.model
      .GetStatusRequest(
        statusToken = request.statusToken,
        correlationId = request.correlationId,
        userContext = request.userContext.map(e =>
          com.agoda.bapi.creation.client.common.model.UserContext(
            languageId = e.languageId,
            requestOrigin = e.requestOrigin,
            currency = e.currency,
            nationalityId = e.nationalityId,
            isLoggedInUser = e.isLoggedInUser,
            experimentData = e.experimentData.map(ed =>
              com.agoda.bapi.creation.client.common.model.ExperimentData(
                userId = ed.userId,
                deviceTypeId = ed.deviceTypeId,
                memberId = ed.memberId,
                trafficGroup = ed.trafficGroup,
                cId = ed.cId,
                aId = ed.aId,
                serverName = ed.serverName,
                force = ed.force,
                forceByVariant = ed.forceByVariant
              )
            ),
            memberId = None
          )
        ),
        bookingContext = request.bookingContext.map(bc =>
          com.agoda.bapi.creation.client.common.model.BookingCreationContext(
            sessionId = bc.sessionId,
            userAgent = com.agoda.bapi.creation.client.common.model.UserAgent(
              origin = bc.userAgent.origin,
              osName = bc.userAgent.osName,
              osVersion = bc.userAgent.osVersion,
              browserName = bc.userAgent.browserName,
              browserLanguage = bc.userAgent.browserLanguage,
              browserVersion = bc.userAgent.browserVersion,
              browserSubVersion = bc.userAgent.browserSubVersion,
              browserBuildNumber = bc.userAgent.browserBuildNumber,
              deviceBrand = bc.userAgent.deviceBrand,
              deviceModel = bc.userAgent.deviceModel,
              deviceTypeId = bc.userAgent.deviceTypeId,
              isMobile = bc.userAgent.isMobile,
              isTouch = bc.userAgent.isTouch,
              additionalInfo = bc.userAgent.additionalInfo
            )
          )
        ),
        deviceContext = request.deviceContext.map(dc =>
          com.agoda.bapi.creation.client.common.model.DeviceContext(
            deviceTypeId = com.agoda.bapi.creation.client.common.model.DeviceContext.DeviceTypeId(dc.deviceTypeId.id),
            deviceId = dc.deviceId
          )
        )
      )

    case class IsValidRequest() extends ArgumentMatcher[com.agoda.bapi.creation.client.common.model.GetStatusRequest] {
      override def matches(argument: com.agoda.bapi.creation.client.common.model.GetStatusRequest): Boolean = {
        argument == mappedRequest
      }
    }
    "output getStatus response for all products with requireCvv and thirdPartyStatus" in {
      val fixture = new Fixture {}
      import fixture._
      initMockedBehaviours()
      when(requestContext.whiteLabelInfo).thenReturn(
        WhiteLabelInfo(whiteLabelId = WhiteLabel.Agoda, feature = FeaturesConfiguration(), token = whitelabelToken)
      )

      val resultFut = bcreByDcProxy.getItineraryStatus(request, "bk")

      resultFut.map { result =>
        verify(mockBcreClient).getItineraryStatus(
          any(),
          any(),
          ArgumentMatchers.argThat(IsValidRequest()),
          any(),
          any()
        )(any())
        result.success shouldBe true
        result.itinerary.get.bookings.isEmpty shouldBe false
        result.itinerary.get.flights.isEmpty shouldBe false
        result.itinerary.get.flights.head.flightToken shouldBe Some("flightToken1")
        result.itinerary.get.cars.isEmpty shouldBe false
        result.itinerary.get.activities.isEmpty shouldBe false
        result.itinerary.get.addOns.isEmpty shouldBe false
        result.paymentResult.flatMap(_.requireCvv) shouldBe Some(true)
        result.paymentResult.flatMap(_.thirdPartyStatus) shouldBe Some(ThirdPartyStatus.Continue)
        result.partnerPaymentResult.flatMap(_.redirectPayment).map(_.url) shouldBe Some("test")
        result.partnerPaymentResult.flatMap(_.thirdPartyStatus) shouldBe Some(ThirdPartyStatus.Continue)
      }
    }

    "not call bcreClient.getItineraryStatus when 'dc' is not present" in {
      val fixture = new Fixture {}
      import fixture._

      val request   = GetStatusRequest(statusToken = "", correlationId = None, userContext = None, bookingContext = None)
      val resultFut = bcreByDcProxy.getItineraryStatus(request, "nonexist")

      verifyNoInteractions(mockBcreClient)
      resultFut.failed.map(ex => ex.getMessage should include("No BCRE local client was found"))
    }

    "output getStatus response for all products with mfaResult" in {
      val fixture = new Fixture {}
      import fixture._

      val extraMockParam = Map("mfaResultRedirectUrl" -> "MockRedirectUrl")
      initMockedBehaviours(extraMockParam, removePaymentResult = true)
      when(requestContext.whiteLabelInfo).thenReturn(
        WhiteLabelInfo(whiteLabelId = WhiteLabel.Agoda, feature = FeaturesConfiguration(), token = whitelabelToken)
      )

      val expectedMFAResult = MFAResult(redirectUrl = Some("MockRedirectUrl"))

      val resultFut = bcreByDcProxy.getItineraryStatus(request, "bk")

      resultFut.map { result =>
        verify(mockBcreClient).getItineraryStatus(
          any(),
          any(),
          ArgumentMatchers.argThat(IsValidRequest()),
          any(),
          argsEq(whitelabelToken)
        )(any())
        result.success shouldBe true
        result.itinerary.get.bookings.isEmpty shouldBe false
        result.itinerary.get.flights.isEmpty shouldBe false
        result.itinerary.get.flights.head.flightToken shouldBe Some("flightToken1")
        result.itinerary.get.cars.isEmpty shouldBe false
        result.itinerary.get.activities.isEmpty shouldBe false
        result.paymentResult.isEmpty shouldBe true
        result.mfaResult shouldBe Some(expectedMFAResult)
      }
    }

    "output getStatus response with borBookingResult" in {
      val fixture = new Fixture {}
      import fixture._

      val extraMockParam = Map("borBookingResultStatus" -> BorBookingStatus.WaitForApproved.id.toString)
      initMockedBehaviours(extraMockParam, removePaymentResult = true)
      when(requestContext.whiteLabelInfo).thenReturn(
        WhiteLabelInfo(whiteLabelId = WhiteLabel.Agoda, feature = FeaturesConfiguration(), token = whitelabelToken)
      )

      val expectedBorBookingResult = BorBookingResult(status = Some(BorBookingStatus.WaitForApproved.id))

      val resultFut = bcreByDcProxy.getItineraryStatus(request, "bk")

      resultFut.map { result =>
        verify(mockBcreClient).getItineraryStatus(
          any(),
          any(),
          ArgumentMatchers.argThat(IsValidRequest()),
          any(),
          any()
        )(any())
        result.success shouldBe true
        result.itinerary.get.bookings.isEmpty shouldBe false
        result.itinerary.get.flights.isEmpty shouldBe false
        result.itinerary.get.flights.head.flightToken shouldBe Some("flightToken1")
        result.itinerary.get.cars.isEmpty shouldBe false
        result.itinerary.get.activities.isEmpty shouldBe false
        result.itinerary.get.hotels.head.rejectReason.head.subErrorCode shouldBe Some(ErrorCode.InvalidCardClass.id)
        result.itinerary.get.hotels.head.rejectReason.head.code shouldBe ErrorCode.InvalidCardClass.id
        result.itinerary.get.hotels.head.rejectReason.head.message shouldBe "invalid card class"
        result.paymentResult.isEmpty shouldBe true
        result.borBookingResult shouldBe Some(expectedBorBookingResult)
      }
    }

    "output getStatus response with isPartialSuccessAllowed as true" in {
      val fixture = new Fixture {}
      import fixture._
      initMockedBehaviours(isPartialSuccessAllowedResult = true)
      when(requestContext.whiteLabelInfo).thenReturn(
        WhiteLabelInfo(whiteLabelId = WhiteLabel.Agoda, feature = FeaturesConfiguration(), token = whitelabelToken)
      )

      val resultFut = bcreByDcProxy.getItineraryStatus(request, "bk")

      resultFut.map { result =>
        verify(mockBcreClient).getItineraryStatus(
          any(),
          any(),
          ArgumentMatchers.argThat(IsValidRequest()),
          any(),
          any()
        )(any())
        result.success shouldBe true
        result.itinerary.get.bookings.isEmpty shouldBe false
        result.itinerary.get.flights.isEmpty shouldBe false
        result.itinerary.get.flights.head.flightToken shouldBe Some("flightToken1")
        result.itinerary.get.cars.isEmpty shouldBe false
        result.itinerary.get.activities.isEmpty shouldBe false
        result.isPartialSuccessAllowed shouldBe Some(true)
      }
    }

    "output getStatus response with isPartialSuccessAllowed as false" in {
      val fixture = new Fixture {}
      import fixture._
      initMockedBehaviours()
      when(requestContext.whiteLabelInfo).thenReturn(
        WhiteLabelInfo(whiteLabelId = WhiteLabel.Agoda, feature = FeaturesConfiguration(), token = whitelabelToken)
      )

      val resultFut = bcreByDcProxy.getItineraryStatus(request, "bk")

      resultFut.map { result =>
        verify(mockBcreClient).getItineraryStatus(
          any(),
          any(),
          ArgumentMatchers.argThat(IsValidRequest()),
          any(),
          any()
        )(any())
        result.success shouldBe true
        result.itinerary.get.bookings.isEmpty shouldBe false
        result.itinerary.get.flights.isEmpty shouldBe false
        result.itinerary.get.flights.head.flightToken shouldBe Some("flightToken1")
        result.itinerary.get.cars.isEmpty shouldBe false
        result.itinerary.get.activities.isEmpty shouldBe false
        result.isPartialSuccessAllowed shouldBe Some(false)
      }
    }

    "output getStatus response with bookingStatus as Challenge_3DS2 when payment3ds2Type is 2521" in {
      val fixture = new Fixture {}
      import fixture._
      val extraMockParam = Map("payment3ds2Type" -> "2521")
      initMockedBehaviours(extraMockParam, isPartialSuccessAllowedResult = true)
      when(requestContext.whiteLabelInfo).thenReturn(
        WhiteLabelInfo(whiteLabelId = WhiteLabel.Agoda, feature = FeaturesConfiguration(), token = whitelabelToken)
      )

      val resultFut = bcreByDcProxy.getItineraryStatus(request, "bk")

      resultFut.map { result =>
        verify(mockBcreClient).getItineraryStatus(
          any(),
          any(),
          ArgumentMatchers.argThat(IsValidRequest()),
          any(),
          any()
        )(any())
        result.success shouldBe true
        result.itinerary.get.bookings.isEmpty shouldBe false
        result.itinerary.get.flights.isEmpty shouldBe false
        result.itinerary.get.flights.head.flightToken shouldBe Some("flightToken1")
        result.itinerary.get.cars.isEmpty shouldBe false
        result.itinerary.get.activities.isEmpty shouldBe false
        result.isPartialSuccessAllowed shouldBe Some(true)
        result.status.get.bookingStatus shouldBe creation.BookingServerStatus.Challenge_3DS2.get.bookingStatus
      }
    }
    "mapped RejectReason correctly when WLBE-1730 = A" in {
      val fixture = new Fixture {}
      import fixture._
      initMockedBehaviours()
      when(requestContext.whiteLabelInfo).thenReturn(
        WhiteLabelInfo(whiteLabelId = WhiteLabel.Agoda, feature = FeaturesConfiguration(), token = whitelabelToken)
      )
      when(mockFeatureAware.enableStatusSubErrorCodePropagation).thenReturn(false)
      val resultFut = bcreByDcProxy.getItineraryStatus(request, "bk")

      resultFut.map { result =>
        verify(mockBcreClient).getItineraryStatus(
          any(),
          any(),
          ArgumentMatchers.argThat(IsValidRequest()),
          any(),
          any()
        )(any())

        result.itinerary.get.bookings.map(
          _.rejectReason.map(r => (r.code, r.message, r.subErrorCode))
        ) shouldBe Seq(invalidCardClassRejectReason.map(r => (r.code, r.message, r.subErrorCode)))

        val expectedDefaultRejectReasonTuple = mockDefaultRejectReason.map(r => (r.code, r.message, None))
        result.itinerary.get.flights.map(_.rejectReason.map(r => (r.code, r.message, r.subErrorCode))) shouldBe Seq(
          expectedDefaultRejectReasonTuple
        )
        result.itinerary.get.cars.map(_.rejectReason.map(r => (r.code, r.message, r.subErrorCode))) shouldBe Seq(
          expectedDefaultRejectReasonTuple
        )
        result.itinerary.get.activities.map(
          _.rejectReason.map(r => (r.code, r.message, r.subErrorCode))
        ) shouldBe Seq(expectedDefaultRejectReasonTuple)
      }
    }
    "mapped RejectReason correctly when WLBE-1730 = B" in {
      val fixture = new Fixture {}
      import fixture._
      initMockedBehaviours()
      when(requestContext.whiteLabelInfo).thenReturn(
        WhiteLabelInfo(whiteLabelId = WhiteLabel.Agoda, feature = FeaturesConfiguration(), token = whitelabelToken)
      )
      when(mockFeatureAware.enableStatusSubErrorCodePropagation).thenReturn(true)
      val resultFut = bcreByDcProxy.getItineraryStatus(request, "bk")

      resultFut.map { result =>
        verify(mockBcreClient).getItineraryStatus(
          any(),
          any(),
          ArgumentMatchers.argThat(IsValidRequest()),
          any(),
          any()
        )(any())

        result.itinerary.get.bookings.map(
          _.rejectReason.map(r => (r.code, r.message, r.subErrorCode))
        ) shouldBe Seq(invalidCardClassRejectReason.map(r => (r.code, r.message, r.subErrorCode)))

        val expectedDefaultRejectReasonTuple = mockDefaultRejectReason.map(r => (r.code, r.message, r.subErrorCode))
        result.itinerary.get.flights.map(_.rejectReason.map(r => (r.code, r.message, r.subErrorCode))) shouldBe Seq(
          expectedDefaultRejectReasonTuple
        )
        result.itinerary.get.cars.map(_.rejectReason.map(r => (r.code, r.message, r.subErrorCode))) shouldBe Seq(
          expectedDefaultRejectReasonTuple
        )
        result.itinerary.get.activities.map(
          _.rejectReason.map(r => (r.code, r.message, r.subErrorCode))
        ) shouldBe Seq(expectedDefaultRejectReasonTuple)
      }
    }
  }

  "continueItinerary" should {
    "call bcreByDcProxy itineraryContinueBookingWorkflow" in {
      val fixture = new Fixture {}
      import fixture._
      initMockedBehaviours()

      val paymentToken = PaymentToken(
        tokenType = PaymentTokenType.RedirectPayment,
        tokenValue = "token-value",
        additionalInfo = Some(Map("test" -> "test-value"))
      )
      val request = ContinuePaymentRequest(
        statusToken = "statusToken",
        correlationId = Option("correlationId"),
        fraudResult = Some(FraudResult(FraudResultStatusEnum.Pass)),
        flightData = Some(FlightData("1234", "bookingDetails")),
        userContext = Option(
          UserContext(
            languageId = 1,
            requestOrigin = "US",
            currency = "USD",
            nationalityId = 1,
            experimentData = Option(
              ExperimentData(
                userId = "1",
                deviceTypeId = "WEB",
                memberId = Option("member-1"),
                trafficGroup = Option("TG-1"),
                cId = Option("CID"),
                aId = Option("aID"),
                serverName = Option("Server")
              )
            ),
            isLoggedInUser = true,
            memberId = Some(123),
            clientIp = Some("IP"),
            botProfile = Some("bot"),
            locale = Some("locale"),
            isAffiliateUserLoggedIn = Some(true),
            capiToken = Some("capi token"),
            skipDownlift = Some(true)
          )
        ),
        bookingContext = Option(
          BookingCreationContext(
            sessionId = "session1",
            UserAgent(
              origin = "origin",
              osName = "osName",
              osVersion = "osVersion",
              browserName = "browserName",
              browserLanguage = "browserLanguage",
              browserVersion = "browserVersion",
              browserSubVersion = "browserSubVersion",
              browserBuildNumber = "browserBuildNumber",
              deviceBrand = "deviceBrand",
              deviceModel = "deviceModel",
              deviceTypeId = 1,
              isMobile = Some(true),
              isTouch = Some(true),
              additionalInfo = Option("additionalInfo")
            )
          )
        ),
        internalToken = Option("internal-token"),
        securityCode = Option("security-code"),
        manualTicketing = Option(
          ManualTicketingData(
            confirmedBookingData = Some(ConfirmedBookingData(carrierPnr = "CARIERPNR", gdsPnr = "GDSONR")),
            rejectedBookingData = Some(RejectedBookingData(isRejected = false))
          )
        ),
        borBookingResult = Some(BorBookingResult(status = Some(BorBookingStatus.Approved.id))),
        postBackFields = Some(Map("test" -> "test-value")),
        isCancel = Some(true),
        isConfirmPartnerBooking = Some(true),
        redirectPaymentPayload = Some(Map("test" -> "test-value")),
        thirdPartyStatus = Some(ThirdPartyStatus.Continue),
        paymentToken = Some(paymentToken),
        activityData = Some(ActivityData("1234", "bookingDetails"))
      )

      val mappedContinuePaymentRequest = com.agoda.bapi.creation.client.common.model
        .ContinuePaymentRequest(
          statusToken = request.statusToken,
          correlationId = request.correlationId.getOrElse(""),
          fraudResult = Some(
            com.agoda.bapi.creation.client.common.model.FraudResult(
              status = com.agoda.bapi.creation.client.common.model.FraudResult.Status.Pass
            )
          ),
          flightData = Some(
            com.agoda.bapi.creation.client.common.model.FlightData(
              supplierBookingId = "1234",
              bookingDetails = "bookingDetails"
            )
          ),
          userContext = request.userContext.map(userContext =>
            com.agoda.bapi.creation.client.common.model.UserContext(
              languageId = userContext.languageId,
              requestOrigin = userContext.requestOrigin,
              currency = userContext.currency,
              nationalityId = userContext.nationalityId,
              isLoggedInUser = userContext.isLoggedInUser,
              experimentData = userContext.experimentData.map(experimentData =>
                com.agoda.bapi.creation.client.common.model.ExperimentData(
                  userId = experimentData.userId,
                  deviceTypeId = experimentData.deviceTypeId,
                  memberId = experimentData.memberId,
                  trafficGroup = experimentData.trafficGroup,
                  cId = experimentData.cId,
                  aId = experimentData.aId,
                  serverName = experimentData.serverName,
                  force = experimentData.force,
                  forceByVariant = experimentData.forceByVariant
                )
              ),
              memberId = Some(123),
              clientIp = Some("IP"),
              botProfile = Some("bot"),
              locale = Some("locale"),
              isAffiliateUserLoggedIn = Some(true),
              capiToken = Some("capi token"),
              skipDownlift = Some(true)
            )
          ),
          bookingContext = request.bookingContext.map(context =>
            com.agoda.bapi.creation.client.common.model.BookingCreationContext(
              sessionId = context.sessionId,
              userAgent = com.agoda.bapi.creation.client.common.model.UserAgent(
                origin = context.userAgent.origin,
                osName = context.userAgent.osName,
                osVersion = context.userAgent.osVersion,
                browserName = context.userAgent.browserName,
                browserLanguage = context.userAgent.browserLanguage,
                browserVersion = context.userAgent.browserVersion,
                browserSubVersion = context.userAgent.browserSubVersion,
                browserBuildNumber = context.userAgent.browserBuildNumber,
                deviceBrand = context.userAgent.deviceBrand,
                deviceModel = context.userAgent.deviceModel,
                deviceTypeId = context.userAgent.deviceTypeId,
                isMobile = context.userAgent.isMobile,
                isTouch = context.userAgent.isTouch,
                additionalInfo = context.userAgent.additionalInfo
              )
            )
          ),
          deviceContext = request.deviceContext.map(deviceContext =>
            com.agoda.bapi.creation.client.common.model.DeviceContext(
              deviceTypeId = com.agoda.bapi.creation.client.common.model.DeviceContext
                .DeviceTypeId(deviceContext.deviceTypeId.id),
              deviceId = deviceContext.deviceId
            )
          ),
          internalToken = Option("internal-token"),
          manualTicketing = request.manualTicketing.map(data =>
            com.agoda.bapi.creation.client.common.model.ManualTicketingData(
              confirmedBookingData = data.confirmedBookingData.map(confirmedData =>
                com.agoda.bapi.creation.client.common.model.ConfirmedBookingData(
                  carrierPnr = confirmedData.carrierPnr,
                  gdsPnr = confirmedData.gdsPnr
                )
              ),
              rejectedBookingData = data.rejectedBookingData.map(rejectedData =>
                com.agoda.bapi.creation.client.common.model.RejectedBookingData(
                  isRejected = rejectedData.isRejected
                )
              )
            )
          ),
          borBookingResult = Some(ClientBorBookingResult(status = Some(BorBookingStatus.Approved.id))),
          postBackFields = request.postBackFields,
          isCancel = request.isCancel,
          isConfirmPartnerBooking = request.isConfirmPartnerBooking,
          redirectPaymentPayload = request.redirectPaymentPayload,
          thirdPartyStatus =
            request.thirdPartyStatus.map(status => ClientContinuePaymentRequest.ThirdPartyStatus(status.id)),
          paymentToken = request.paymentToken.map(token =>
            com.agoda.bapi.creation.client.common.model.PaymentToken(
              token.tokenType.id,
              token.tokenValue,
              token.additionalInfo
            )
          ),
          activityData = request.activityData.map(data =>
            com.agoda.bapi.creation.client.common.model.ActivityData(
              data.supplierBookingId,
              data.bookingDetails
            )
          ),
          securityCode = request.securityCode
        )

      val token = Some("Some token")
      when(requestContext.whiteLabelInfo).thenReturn(
        WhiteLabelInfo(whiteLabelId = WhiteLabel.CitiUS, feature = FeaturesConfiguration(), token = token)
      )
      val resultFut = bcreByDcProxy.continueItinerary(request, "bk")

      resultFut.map { result =>
        verify(mockBcreClient).itineraryContinueBookingWorkflow(
          any(),
          any(),
          any(),
          argsEq(mappedContinuePaymentRequest),
          argsEq(token)
        )(any())
        result.success shouldBe true
      }
    }

    "not call bcreByDcProxy itineraryContinueBookingWorkflow when 'dc' is not present" in {
      val fixture = new Fixture {}
      import fixture._

      val request = ContinuePaymentRequest(
        statusToken = "",
        correlationId = None,
        userContext = None,
        bookingContext = None,
        internalToken = None,
        securityCode = None
      )

      val resultFut = bcreByDcProxy.continueItinerary(request, "nonexist")

      verifyNoInteractions(mockBcreClient)
      resultFut.failed.map(ex => ex.getMessage should include("No BCRE local client was found"))
    }
  }

}
