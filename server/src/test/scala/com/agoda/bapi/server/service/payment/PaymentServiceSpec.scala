package com.agoda.bapi.server.service.payment

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.{BookingCreationContext, UserAgent}
import com.agoda.bapi.common.message.pricebreakdown.{PriceBreakdownNode, PriceBreakdownResponse, PriceBreakdownType}
import com.agoda.bapi.common.message.setupBooking.{LoyaltyRequest, PaymentRequest, SetupBookingRequest}
import com.agoda.bapi.common.message.{DeviceContext, DevicePlatform, ExperimentData}
import com.agoda.bapi.common.model.booking.{PaymentFlow, PaymentMethodDetailsV2, PaymentMethodIcon}
import com.agoda.bapi.common.model.payment.PaymentModel
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.product.BookingFlow.BookingFlow
import com.agoda.bapi.common.model.{ChargeOption, PaymentGroupCategory, User<PERSON><PERSON>x<PERSON>, <PERSON><PERSON><PERSON>l, WhiteLabelInfo}
import com.agoda.bapi.common.reporting.logs.RequestContextMarker
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.token.Money
import com.agoda.bapi.server.facades.aggregator.MockProductUtils.{flightsDataProduct, mockMemberDetails, mockProductsProduct}
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.{FlightItinerary, ProductData}
import com.agoda.bapi.server.repository.PaymentApiRepository
import com.agoda.bapi.server.service.payment.feature.PaymentFeatureProcessorComposite
import com.agoda.bapi.server.service.payment.funnel.FunnelProcessorComposite
import com.agoda.bapi.server.service.payment.mapper.PaymentMapperImpl
import com.agoda.bapi.server.service.payment.model.PaymentFeature.PaymentFeature
import com.agoda.bapi.server.service.payment.model._
import com.agoda.common.itineraryContext.ItineraryContext
import com.agoda.flights.client.v2.model.SearchResponseSlice
import com.agoda.common.itineraryContext.dsl._
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.agoda.paymentapiv2.client.v2.common.model.{Amount => AmountClient, AnalyticContext => AnalyticContextClient, ExperimentContext => ExperimentContextClient, FeatureContext => FeatureContextClient, GetInstallmentDataRequest, PaymentContext => PaymentContextClient, PaymentMethodDetailsResponse, PaymentMethodIcon => PaymentMethodIconClient, Product => ProductClient, ProductContext => ProductContextClient, SetupPaymentRequestV2, SetupPaymentResponseV2, UserContextV2 => UserContextClientV2}
import com.agoda.winterfell.output.MemberDetails
import com.agoda.winterfell.unified.PartnerClaim
import com.typesafe.scalalogging.Logger
import mocks.RequestContextMock
import org.mockito.ArgumentMatchers.{eq => eqTo, _}
import org.mockito.Mockito.{verify, when}
import org.mockito.{ArgumentCaptor, Mockito}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{BeforeAndAfter, OptionValues}
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.{Logger => Underlying}

import scala.collection.JavaConverters._
import scala.concurrent.Future

class PaymentServiceSpec
    extends AsyncWordSpec
    with Matchers
    with MockitoSugar
    with BeforeAndAfter
    with ScalaFutures
    with OptionValues
    with RequestContextMock
    with PaymentTestHelpers {

  val memberWithWalletScenarioIds: String =
    Set(76, 78, 84, 85).map(id => "WALLET-CORE-SIT-" + id).mkString(",")

  val memberWithoutWalletScenarioIds: String =
    Set(77, 79, 80, 81, 82, 83).map(id => "WALLET-CORE-SIT-" + id).mkString(",")

  private def verifyFirstProductInContext(
      productContext: ProductContextClient,
      expectedProductId: Int,
      expectedFirstBusinessCountryId: Int
  ): org.scalatest.compatible.Assertion = {
    productContext.ProductList should be(defined)
    val productList = productContext.ProductList.value
    productList should not be empty

    val firstProduct = productList.head
    firstProduct.ProductId should be(defined)
    firstProduct.ProductId.value shouldBe expectedProductId

    firstProduct.BusinessCountryIds should be(defined)
    val businessCountryIds = firstProduct.BusinessCountryIds.value
    businessCountryIds should not be empty
    businessCountryIds.head shouldBe expectedFirstBusinessCountryId
  }

  "getAvailablePaymentMethod" should {
    "should combine paymentFeature correctly" in {
      testGetPayment().map(result => result.getPaymentFeature shouldBe Set.empty[PaymentFeature])
      testGetPayment(
        productFeature = Set(PaymentFeature.DisableAPM)
      ).map(result => result.getPaymentFeature shouldBe Set(PaymentFeature.DisableAPM))
      testGetPayment(
        paymentFeature = Set(PaymentFeature.DisableAPM)
      ).map(result => result.getPaymentFeature shouldBe Set(PaymentFeature.DisableAPM))
      testGetPayment(
        productFeature = Set(PaymentFeature.DisableAPM),
        paymentFeature = Set(PaymentFeature.RequirePartialRefund)
      ).map(result =>
        result.getPaymentFeature shouldBe Set(PaymentFeature.DisableAPM, PaymentFeature.RequirePartialRefund)
      )
      testGetPayment(
        productFeature = Set(PaymentFeature.DisableAPM),
        paymentFeature = Set(PaymentFeature.DisableAPM)
      ).map(result => result.getPaymentFeature shouldBe Set(PaymentFeature.DisableAPM))

    }
    "should get userContext correctly" in
      testGetPayment(
        setupUserContext = getSetupUserContext()
      ).map(result =>
        result.getUserContext shouldBe UserContextClientV2(
          Some("userId"),
          Some("MemberId"),
          Some(3),
          Some("A1"),
          Some(2),
          Some(5),
          Some(-1),
          Some(""),
          Some("")
        )
      )

    "should get productContext paymentModel = Empty when sendingPaymentModelInProductContext is Off" in
      testGetPayment(
        productContext = Seq(getProduct(paymentModels = Set(1))),
        setupUserContext = getSetupUserContext()
      ).map(result =>
        result.getProductContext shouldBe ProductContextClient(
          Some(1),
          None,
          Some(List(ProductClient(Some(1), Some(1), Some(List()), None, Some(0), Some(0)))),
          new ItineraryContext().toJson.toOption
        )
      )

    "should get productContext paymentModel = Empty when sendingPaymentModelInProductContext is On" in
      testGetPayment(
        productContext = Seq(getProduct(paymentModels = Set(1))),
        sendingPaymentModelInProductContext = true
      ).map(result =>
        result.getProductContext shouldBe ProductContextClient(
          Some(1),
          None,
          Some(List(ProductClient(Some(1), Some(1), Some(List()), Some(List(1)), Some(0), Some(0)))),
          new ItineraryContext().toJson.toOption
        )
      )

    "should get walletContext paymentModel" in
      testGetPayment(
        productContext = Seq(getProduct(paymentModels = Set(1))),
        setupUserContext = getSetupUserContext(),
        memberInfo = Some(mockMemberDetails(isUserHasWallet = true, walletCountry = Some("US")))
      ).map(result => result.getExcludedPaymentMethod shouldBe List())

    "should not get walletContext paymentModel" in
      testGetPayment(
        productContext = Seq(getProduct(paymentModels = Set(1))),
        setupUserContext = getSetupUserContext(),
        memberInfo = Some(mockMemberDetails(isUserHasWallet = false, walletCountry = Some("UK")))
      ).map(result => result.getExcludedPaymentMethod shouldBe List(252, 253, 254))

    s"[$memberWithoutWalletScenarioIds] should not pass wallet context to payment API when member has no wallet" in
      testGetPayment(
        productContext = Seq(getProduct(paymentModels = Set(1), businessCountryIds = Set(107))),
        setupUserContext = getSetupUserContext()
      ).map(result => {
        result.getExcludedPaymentMethod shouldBe List(252, 253, 254)
        result.getAdditionalFields shouldBe Map.empty
        verifyFirstProductInContext(result.getProductContext, 1, 107)
      })

    s"[$memberWithWalletScenarioIds] should pass wallet context to payment API when member has wallet" in
      testGetPayment(
        productContext = Seq(getProduct(paymentModels = Set(1), businessCountryIds = Set(107))),
        setupUserContext = getSetupUserContext(),
        memberInfo = Some(mockMemberDetails(isUserHasWallet = true, walletCountry = Some("SG")))
      ).map(result => {
        result.getAdditionalFields shouldBe Map("BhfsWalletCountry" -> "SG")
        verifyFirstProductInContext(result.getProductContext, 1, 107)
      })

    "should get walletContext paymentModel with isUserHasWallet true and None WalletCountry" in
      testGetPayment(
        productContext = Seq(getProduct(paymentModels = Set(1))),
        setupUserContext = getSetupUserContext(),
        memberInfo = Some(mockMemberDetails(isUserHasWallet = true, walletCountry = None))
      ).map(result => result.getAdditionalFields shouldBe Map())

    "should get walletContext paymentModel with isUserHasWallet false and invalid WalletCountry" in
      testGetPayment(
        productContext = Seq(getProduct(paymentModels = Set(1))),
        setupUserContext = getSetupUserContext(),
        memberInfo = Some(mockMemberDetails(isUserHasWallet = false, walletCountry = None))
      ).map(result => result.getAdditionalFields shouldBe Map())

    "should map paymentResponse correctly" in
      testGetPayment().map(response =>
        response.result shouldBe AvailablePaymentMethodsResult(
          Seq(paymentMethod),
          setupRequestMock(),
          Seq(1, 2, 3),
          Some(true)
        )
      )

    "should map paymentResponse correctly with EnhancedRedirect" in {
      val paymentMethodEnhancedRedirect: PaymentMethodDetailsV2 =
        new PaymentMethodDetailsV2(
          id = 248,
          name = "UPI_PAY",
          paymentFlow = PaymentFlow.EnhancedRedirect,
          paymentGroupCategory = PaymentGroupCategory.None,
          icons = Seq(
            PaymentMethodIcon(
              `type` = 1,
              url = "cdn6/images/upi_pay"
            )
          ),
          timeout = Some(1),
          gatewayName = None,
          remarks = Seq.empty,
          chargeDateTypes = ChargeOption.PayNow,
          chargeOptions = Set(ChargeOption.PayNow, ChargeOption.PayLater),
          isRecommended = false,
          ranking = 1,
          defaultCurrency = "INR",
          cardNumRegEx = Some(""),
          cvcRegEx = Some(""),
          isLuhnCheckRecommended = Some(true),
          requiredFields = None,
          isFapiaoEligible = Some(false),
          isTokenEnabled = Some(false)
        )

      testGetPayment(
        setupPaymentResponse = SetupPaymentResponseV2(
          SuggestedPaymentMethod = Some(List(1, 2, 3)),
          PaymentMethodDetails = Some(
            List(
              PaymentMethodDetailsResponse(
                PaymentMethodId = Some(248),
                PaymentMethodName = Some("UPI_PAY"),
                PaymentFlow = Some(PaymentFlow.EnhancedRedirect.id),
                PaymentCategory = Some(PaymentGroupCategory.None.id),
                Timeout = Some(1),
                PaymentMethodIcons = Some(
                  List(
                    PaymentMethodIconClient(
                      Type = Some(1),
                      Url = Some("cdn6/images/upi_pay")
                    )
                  )
                ),
                Remarks = None,
                RequiredField = None,
                ChargeType = Some(1),
                ChargeOptions = Some(List(1, 2)),
                IsRecommended = Some(false),
                Ranking = Some(1),
                DefaultCurrency = Some("INR"),
                CardNumRegex = Some(""),
                CvcRegex = Some(""),
                IsLuhnCheckRecommended = Some(true),
                IsTokenEnabled = Some(false),
                IsFapiaoEligible = Some(false),
                FeeInfo = None
              )
            )
          ),
          CreditCardOnFile = None,
          CreditCardInfoSetup = None,
          NonCardOnFile = None,
          InstallmentPlans = None,
          InstallmentAvailableProviders = None,
          InstallmentPlanCode = None,
          IsInstallmentEligible = Some(true),
          RequestStatus = None,
          RequestMessage = None
        )
      ).map(response =>
        response.result shouldBe AvailablePaymentMethodsResult(
          Seq(paymentMethodEnhancedRedirect),
          setupRequestMock(),
          Seq(1, 2, 3),
          Some(true)
        )
      )
    }

    "getPaymentContext" should {
      val setupBookingRequest = getSetupBookingRequest
      val payToAgoda = PriceBreakdownNode(
        value = Option(
          PriceBreakdownResponse(
            `type` = PriceBreakdownType.PayAgoda,
            amount = Money(amount = 1000, currencyCode = "THB")
          )
        )
      )
      val bundleSummary = PriceBreakdownNode(
        value = Option(
          PriceBreakdownResponse(
            `type` = PriceBreakdownType.BundledSummary,
            amount = Money(amount = 1000, currencyCode = "THB")
          )
        ),
        breakdowns = Some(Seq(payToAgoda))
      )
      val priceBreakDownNode = Some(
        PriceBreakdownNode(
          value = Option(
            PriceBreakdownResponse(
              `type` = PriceBreakdownType.TotalPrice,
              amount = Money(amount = 2000d, currencyCode = "THB")
            )
          ),
          breakdowns = Some(Seq(bundleSummary))
        )
      )
      val productData = mockProductsProduct.copy(
        totalPriceDisplay = priceBreakDownNode
      )

      val paymentApiV2Repository    = mock[PaymentApiRepository]
      val funnelProcessorComposite  = mock[FunnelProcessorComposite]
      val featureProcessorComposite = mock[PaymentFeatureProcessorComposite]
      val paymentMapper             = new PaymentMapperImpl()

      "return paymentAmount = TotalPrice when enableSendingCorrectPaymentAmount = A" in {
        implicit val setupBookingContext: SetupBookingContext = getSetupBookingContext(getSetupUserContext())
        val service = new PaymentServiceImpl(
          paymentApiV2Repository,
          funnelProcessorComposite,
          featureProcessorComposite,
          paymentMapper
        )
        service.getPaymentContext(productData, setupBookingRequest).paymentAmount.value shouldBe 2000.0
      }
      "return paymentAmount = PayToAgoda when enableSendingCorrectPaymentAmount = B" in {
        implicit val setupBookingContext: SetupBookingContext =
          getSetupBookingContext(
            getSetupUserContext(),
            enableSendingCorrectPaymentAmount = true,
            compareProductPaymentInfoBuilderInSetupPayment = true
          )
        val service = new PaymentServiceImpl(
          paymentApiV2Repository,
          funnelProcessorComposite,
          featureProcessorComposite,
          paymentMapper
        )
        service.getPaymentContext(productData, setupBookingRequest).paymentAmount.value shouldBe 0
      }

      "should send comparison log when only enableSendingCorrectPaymentAmount = B" in {
        implicit val setupBookingContext: SetupBookingContext =
          getSetupBookingContext(
            getSetupUserContext(),
            enableSendingCorrectPaymentAmount = true,
            compareProductPaymentInfoBuilderInSetupPayment = false
          )

        // Mock
        val productContext = ProductContext(
          whiteLabelId = 1,
          productList = Seq(getProduct()),
          itineraryContext = None
        )
        val loggerMock = mock[Underlying]
        val contextMarkerCapture: ArgumentCaptor[RequestContextMarker] =
          ArgumentCaptor.forClass(classOf[RequestContextMarker])
        when(loggerMock.isWarnEnabled(any())).thenReturn(true)
        val installmentPlansRequest =
          Some(GetInstallmentDataRequest(CcId = Some(1111L), Currency = Some("THB"), CapiToken = Some("some-token")))

        val service = Mockito.spy[PaymentServiceImpl](
          new PaymentServiceImpl(
            paymentApiV2Repository,
            funnelProcessorComposite,
            featureProcessorComposite,
            paymentMapper
          ) {
            override def logger: Logger = Logger(loggerMock)
          }
        )
        service
          .getPaymentContext(
            productData,
            setupBookingRequest,
            Some(productContext),
            paymentFeature =
              Some(PaymentMethodFeatureComposite(paymentFeatures = Set(PaymentFeature.RequiredInstallment))),
            installmentPlansRequest = installmentPlansRequest
          )
          .paymentAmount
          .value shouldBe 0

        verify(loggerMock).warn(contextMarkerCapture.capture(), any(), any())
        contextMarkerCapture.getValue.stringTags shouldBe Map[String, String](
          "multiProductType"         -> "SingleFlight",
          "newAmount"                -> "0.0",
          "oldAmount"                -> "2000.0",
          "setupPaymentAmountLog"    -> "1",
          "whiteLabelId"             -> "1",
          "productCount"             -> "1",
          "productTypeIds"           -> "1",
          "paymentModels"            -> "",
          "isMatch"                  -> "false",
          "installmentAmount"        -> "155.96",
          "isMatchInstallmentAmount" -> "false",
          "isEligibleForInstallment" -> "true",
          "installmentCurrency"      -> "THB"
        ).asJava
      }

      "should return the correct Payment Model from flight requests for Flight bookings stamped as Cart and UNIBF-946=B" in {
        implicit val setupBookingContext: SetupBookingContext = getSetupBookingContext(
          getSetupUserContext(),
          flightToCartMigration = true,
          bookingFlow = BookingFlow.Cart
        )
        val updatedProductData = productData.copy(
          flights = productData.flights.map(
            _.copy(flightItinerary =
              Some(
                FlightItinerary(
                  id = "FlightId",
                  slices = Seq.empty,
                  paymentModel = PaymentModel.Merchant.id
                )
              )
            )
          ),
          properties = Seq.empty
        )
        val service = new PaymentServiceImpl(
          paymentApiV2Repository,
          funnelProcessorComposite,
          featureProcessorComposite,
          paymentMapper
        )
        service
          .getPaymentContext(updatedProductData, setupBookingRequest)
          .paymentModel
          .shouldBe(PaymentModel.Merchant.id)
      }

      "should return the Unknown Payment Model  for Hackerfare bookings stamped as Cart and UNIBF-946=B" in {
        implicit val setupBookingContext: SetupBookingContext = getSetupBookingContext(
          getSetupUserContext(),
          flightToCartMigration = true,
          bookingFlow = BookingFlow.Cart
        )
        val flightRequest = productData.flights
          .map(
            _.copy(flightItinerary =
              Some(
                FlightItinerary(
                  id = "FlightId",
                  slices = Seq.empty,
                  paymentModel = PaymentModel.Merchant.id
                )
              )
            )
          )
          .head

        val updatedProductData = productData.copy(
          flights = Seq(flightRequest, flightRequest),
          properties = Seq.empty
        )
        val service = new PaymentServiceImpl(
          paymentApiV2Repository,
          funnelProcessorComposite,
          featureProcessorComposite,
          paymentMapper
        )
        service
          .getPaymentContext(updatedProductData, setupBookingRequest)
          .paymentModel
          .shouldBe(PaymentModel.Unknown.id)
      }

      "should return the Unknown Payment Model  for Cart bookings stamped as Cart and UNIBF-946=B" in {
        implicit val setupBookingContext: SetupBookingContext = getSetupBookingContext(
          getSetupUserContext(),
          flightToCartMigration = true,
          bookingFlow = BookingFlow.Cart
        )
        val flightRequest = productData.flights.map(
          _.copy(flightItinerary =
            Some(
              FlightItinerary(
                id = "FlightId",
                slices = Seq.empty,
                paymentModel = PaymentModel.Merchant.id
              )
            )
          )
        )

        val updatedProductData = productData.copy(
          flights = flightRequest,
          properties = productData.properties
        )
        val service = new PaymentServiceImpl(
          paymentApiV2Repository,
          funnelProcessorComposite,
          featureProcessorComposite,
          paymentMapper
        )
        service
          .getPaymentContext(updatedProductData, setupBookingRequest)
          .paymentModel
          .shouldBe(PaymentModel.Unknown.id)
      }

      "should return  the Unknown Payment Model for Flight bookings stamped as Cart and UNIBF-946=A" in {
        implicit val setupBookingContext: SetupBookingContext = getSetupBookingContext(
          getSetupUserContext(),
          flightToCartMigration = false,
          bookingFlow = BookingFlow.Cart
        )
        val updatedProductData = productData.copy(
          flights = productData.flights.map(
            _.copy(flightItinerary =
              Some(
                FlightItinerary(
                  id = "FlightId",
                  slices = Seq.empty,
                  paymentModel = PaymentModel.Merchant.id
                )
              )
            )
          ),
          properties = Seq.empty
        )
        val service = new PaymentServiceImpl(
          paymentApiV2Repository,
          funnelProcessorComposite,
          featureProcessorComposite,
          paymentMapper
        )
        service
          .getPaymentContext(updatedProductData, setupBookingRequest)
          .paymentModel
          .shouldBe(PaymentModel.Unknown.id)
      }
    }

    "send measurement and log successfully" in {
      val result = testComparison(setupPaymentRequestV2 = setupRequestMock())
      verify(result.mockLogger).warn(result.requestContextMarkerCaptor.capture(), any(), any())
      result.requestContextMarkerCaptor.getValue.stringTags shouldBe Map[String, String](
        "isMatch"                       -> "true",
        "hasOldRequest"                 -> "false",
        "hasNewRequest"                 -> "false",
        "oldAmount"                     -> "0.0",
        "newAmount"                     -> "0.0",
        "oldCurrency"                   -> "",
        "newCurrency"                   -> "THB",
        "oldBusinessCountryId"          -> "",
        "newBusinessCountryId"          -> "",
        "oldCCId"                       -> "0",
        "newCCId"                       -> "1111",
        "oldCapiToken"                  -> "",
        "newCapiToken"                  -> "",
        "oldPartnerClaimToken"          -> "",
        "newPartnerClaimToken"          -> "",
        "bookingFlow"                   -> "SingleFlight",
        "whiteLabel"                    -> "1",
        "error"                         -> "",
        "selectedChargeCurrency"        -> "THB",
        "isMatchSelectedChargeCurrency" -> "false",
        "isMatchAmount"                 -> "true"
      ).asJava
      succeed
    }

    "send isMatch = false when GetInstallmentData not empty and some fields mismatch" in {
      val result = testComparison(
        installmentPlansRequest = Some(GetInstallmentDataRequest(CcId = Some(112L))),
        setupPaymentRequestV2 = setupRequestMock()
      )
      verify(result.mockLogger).warn(result.requestContextMarkerCaptor.capture(), any(), any())
      result.requestContextMarkerCaptor.getValue.stringTags shouldBe Map[String, String](
        "isMatch"                       -> "false",
        "hasOldRequest"                 -> "true",
        "hasNewRequest"                 -> "false",
        "oldAmount"                     -> "0.0",
        "newAmount"                     -> "0.0",
        "oldCurrency"                   -> "",
        "newCurrency"                   -> "THB",
        "oldBusinessCountryId"          -> "",
        "newBusinessCountryId"          -> "",
        "oldCCId"                       -> "112",
        "newCCId"                       -> "1111",
        "oldCapiToken"                  -> "",
        "newCapiToken"                  -> "",
        "oldPartnerClaimToken"          -> "",
        "newPartnerClaimToken"          -> "",
        "bookingFlow"                   -> "SingleFlight",
        "whiteLabel"                    -> "1",
        "error"                         -> "",
        "selectedChargeCurrency"        -> "THB",
        "isMatchSelectedChargeCurrency" -> "false",
        "isMatchAmount"                 -> "true"
      ).asJava
      succeed
    }

    "send isMatch = true when GetInstallmentData not empty and all fields match" in {
      val result = testComparison(
        installmentPlansRequest = Some(GetInstallmentDataRequest(CcId = Some(1111L), Currency = Some("THB"))),
        setupPaymentRequestV2 = setupRequestMock(List(PaymentFeature.RequiredInstallment))
      )
      verify(result.mockLogger).warn(result.requestContextMarkerCaptor.capture(), any(), any())
      result.requestContextMarkerCaptor.getValue.stringTags shouldBe Map[String, String](
        "isMatch"                       -> "true",
        "hasOldRequest"                 -> "true",
        "hasNewRequest"                 -> "true",
        "oldAmount"                     -> "0.0",
        "newAmount"                     -> "0.0",
        "oldCurrency"                   -> "THB",
        "newCurrency"                   -> "THB",
        "oldBusinessCountryId"          -> "",
        "newBusinessCountryId"          -> "",
        "oldCCId"                       -> "1111",
        "newCCId"                       -> "1111",
        "oldCapiToken"                  -> "",
        "newCapiToken"                  -> "",
        "oldPartnerClaimToken"          -> "",
        "newPartnerClaimToken"          -> "",
        "bookingFlow"                   -> "SingleFlight",
        "whiteLabel"                    -> "1",
        "error"                         -> "",
        "selectedChargeCurrency"        -> "THB",
        "isMatchSelectedChargeCurrency" -> "true",
        "isMatchAmount"                 -> "true"
      ).asJava
      succeed
    }

    "send isMatch = true when enableCapiSessionTokenSanitization is disable and capiToken mismatch" in {
      val result = testComparison(
        installmentPlansRequest =
          Some(GetInstallmentDataRequest(CcId = Some(1111L), Currency = Some("THB"), CapiToken = Some("some-token"))),
        setupPaymentRequestV2 = setupRequestMock(List(PaymentFeature.RequiredInstallment))
      )
      verify(result.mockLogger).warn(result.requestContextMarkerCaptor.capture(), any(), any())
      result.requestContextMarkerCaptor.getValue.stringTags shouldBe Map[String, String](
        "isMatch"                       -> "true",
        "hasOldRequest"                 -> "true",
        "hasNewRequest"                 -> "true",
        "oldAmount"                     -> "0.0",
        "newAmount"                     -> "0.0",
        "oldCurrency"                   -> "THB",
        "newCurrency"                   -> "THB",
        "oldBusinessCountryId"          -> "",
        "newBusinessCountryId"          -> "",
        "oldCCId"                       -> "1111",
        "newCCId"                       -> "1111",
        "oldCapiToken"                  -> "some-token",
        "newCapiToken"                  -> "",
        "oldPartnerClaimToken"          -> "",
        "newPartnerClaimToken"          -> "",
        "bookingFlow"                   -> "SingleFlight",
        "whiteLabel"                    -> "1",
        "error"                         -> "",
        "selectedChargeCurrency"        -> "THB",
        "isMatchSelectedChargeCurrency" -> "true",
        "isMatchAmount"                 -> "true"
      ).asJava
      succeed
    }

    "send isMatch = false when enableCapiSessionTokenSanitization is enable and capiToken mismatch" in {
      val result = testComparison(
        installmentPlansRequest =
          Some(GetInstallmentDataRequest(CcId = Some(1111L), Currency = Some("THB"), CapiToken = Some("some-token"))),
        setupPaymentRequestV2 = setupRequestMock(List(PaymentFeature.RequiredInstallment))
      )
      verify(result.mockLogger).warn(result.requestContextMarkerCaptor.capture(), any(), any())
      result.requestContextMarkerCaptor.getValue.stringTags shouldBe Map[String, String](
        "isMatch"                       -> "true",
        "hasOldRequest"                 -> "true",
        "hasNewRequest"                 -> "true",
        "oldAmount"                     -> "0.0",
        "newAmount"                     -> "0.0",
        "oldCurrency"                   -> "THB",
        "newCurrency"                   -> "THB",
        "oldBusinessCountryId"          -> "",
        "newBusinessCountryId"          -> "",
        "oldCCId"                       -> "1111",
        "newCCId"                       -> "1111",
        "oldCapiToken"                  -> "some-token",
        "newCapiToken"                  -> "",
        "oldPartnerClaimToken"          -> "",
        "newPartnerClaimToken"          -> "",
        "bookingFlow"                   -> "SingleFlight",
        "whiteLabel"                    -> "1",
        "error"                         -> "",
        "selectedChargeCurrency"        -> "THB",
        "isMatchSelectedChargeCurrency" -> "true",
        "isMatchAmount"                 -> "true"
      ).asJava
      succeed

    }
  }

  def testGetPayment(
      productContext: Seq[Product] = Seq(getProduct()),
      productFeature: Set[PaymentFeature] = Set.empty[PaymentFeature],
      paymentFeature: Set[PaymentFeature] = Set.empty[PaymentFeature],
      setupUserContext: UserContext = getSetupUserContext(),
      setupPaymentResponse: SetupPaymentResponseV2 = setupPaymentResponseMock,
      sendingPaymentModelInProductContext: Boolean = false,
      memberInfo: Option[MemberDetails] = None
  ): Future[PaymentServiceGetPaymentMethodsTestResult] = {
    implicit val setupBookingContext: SetupBookingContext = getSetupBookingContext(
      setupUserContext,
      sendingPaymentModelInProductContext = sendingPaymentModelInProductContext
    )
    implicit val itineraryContext: ItineraryContext = implicitly(new ItineraryContext())

    val setupBookingRequest = getSetupBookingRequest
    val productData         = getMockFlightProductData

    val paymentApiV2Repository    = mock[PaymentApiRepository]
    val funnelProcessorComposite  = mock[FunnelProcessorComposite]
    val featureProcessorComposite = mock[PaymentFeatureProcessorComposite]
    val paymentMapper             = new PaymentMapperImpl()
    val paymentReqCaptor: ArgumentCaptor[SetupPaymentRequestV2] =
      ArgumentCaptor.forClass(classOf[SetupPaymentRequestV2])

    when(funnelProcessorComposite.getProductContext(any(), any(), any())(any()))
      .thenReturn(
        Future.successful(
          FunnelCompositeResult(
            productContext = ProductContext(
              whiteLabelId = WhiteLabel.Agoda.id,
              productList = productContext,
              itineraryContext = Some(new ItineraryContext())
            ),
            paymentMethodFeatureComposite = PaymentMethodFeatureComposite(paymentFeatures = productFeature)
          )
        )
      )
    when(featureProcessorComposite.getPaymentFeature(any(), any())(any(), any()))
      .thenReturn(Future.successful(PaymentMethodFeatureComposite(paymentFeatures = productFeature)))
    when(featureProcessorComposite.getWalletContext(any())(any()))
      .thenReturn {
        val excludedPaymentMethodIds =
          if (
            !memberInfo.exists(_.isUserHasWallet)
          ) // If user does not have wallet, then exclude BHFS_Wallet from payment methods
            Set(
              PaymentMethod.BHFS_Wallet_US.value,
              PaymentMethod.BHFS_Wallet_SG.value,
              PaymentMethod.BHFS_Wallet_UK.value
            )
          else Set.empty[Int]

        Future.successful(
          PaymentMethodFeatureComposite(
            excludedPaymentMethodIds = excludedPaymentMethodIds
          )
        )
      }
    when(featureProcessorComposite.getAdditionalFieldsFeatureContext(any())(any()))
      .thenReturn {
        val userWalletCountry = memberInfo.flatMap(_.userWalletCountry).getOrElse("")
        if (memberInfo.exists(_.isUserHasWallet) && userWalletCountry.nonEmpty) {
          Future.successful(
            PaymentMethodFeatureComposite(
              additionalFields = Map("BhfsWalletCountry" -> userWalletCountry)
            )
          )
        } else {
          Future.successful(PaymentMethodFeatureComposite())
        }
      }
    when(paymentApiV2Repository.getSetupPaymentResponseV2(paymentReqCaptor.capture())(any()))
      .thenReturn(Future.successful(setupPaymentResponse))

    val service = new PaymentServiceImpl(
      paymentApiV2Repository,
      funnelProcessorComposite,
      featureProcessorComposite,
      paymentMapper
    )

    for {
      result <- service.getAvailablePaymentMethod(
                  productData,
                  setupBookingRequest
                )
    } yield PaymentServiceGetPaymentMethodsTestResult(
      result = result,
      paymentRequestCaptor = paymentReqCaptor
    )

  }

  def testComparison(
      installmentPlansRequest: Option[GetInstallmentDataRequest] = None,
      setupPaymentRequestV2: SetupPaymentRequestV2,
      partnerClaim: Option[PartnerClaim] = None
  ): PaymentServiceComparisonTestResult = {
    implicit val setupBookingContext: SetupBookingContext =
      getSetupBookingContext(getSetupUserContext())
    val paymentApiV2Repository    = mock[PaymentApiRepository]
    val funnelProcessorComposite  = mock[FunnelProcessorComposite]
    val featureProcessorComposite = mock[PaymentFeatureProcessorComposite]
    val loggerMock                = mock[Underlying]
    val paymentMapper             = new PaymentMapperImpl()
    val contextMarkerCapture: ArgumentCaptor[RequestContextMarker] =
      ArgumentCaptor.forClass(classOf[RequestContextMarker])

    when(loggerMock.isWarnEnabled(any())).thenReturn(true)
    val service = Mockito.spy[PaymentServiceImpl](
      new PaymentServiceImpl(
        paymentApiV2Repository,
        funnelProcessorComposite,
        featureProcessorComposite,
        paymentMapper
      ) {
        override def logger(): Logger = Logger(loggerMock)
      }
    )

    service.compareGetInstallmentData(installmentPlansRequest, setupPaymentRequestV2, partnerClaim)

    PaymentServiceComparisonTestResult(
      service,
      loggerMock,
      contextMarkerCapture
    )
  }

  def getSetupBookingContext(
      setupUserContext: UserContext,
      enableSendingCorrectPaymentAmount: Boolean = false,
      sendingPaymentModelInProductContext: Boolean = false,
      compareProductPaymentInfoBuilderInSetupPayment: Boolean = false,
      flightToCartMigration: Boolean = false,
      bookingFlow: BookingFlow = BookingFlow.SingleFlight
  ): SetupBookingContext = {
    val context              = mock[SetupBookingContext]
    val requestContext       = mock[RequestContext]
    val featureAware         = mock[FeatureAware]
    val userContext          = setupUserContext
    val whiteLabelInfo       = mock[WhiteLabelInfo]
    val deviceContext        = DeviceContext(DevicePlatform.AppIPhone, None)
    val bookingCreateContext = BookingCreationContext("mySession", UserAgent())

    when(featureAware.enableSendingCorrectPaymentAmount).thenReturn(enableSendingCorrectPaymentAmount)
    when(featureAware.sendingPaymentModelInProductContext).thenReturn(sendingPaymentModelInProductContext)
    when(featureAware.compareProductPaymentInfoBuilderInSetupPayment).thenReturn(
      compareProductPaymentInfoBuilderInSetupPayment
    )
    when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(flightToCartMigration)
    when(requestContext.userContext).thenReturn(Some(userContext))
    when(requestContext.bookingCreationContext).thenReturn(Some(bookingCreateContext))
    when(requestContext.experimentData).thenReturn(None)
    when(requestContext.languageId).thenReturn(3)
    when(requestContext.getLanguageId()).thenReturn(3)
    when(requestContext.getSiteId()).thenReturn(None)
    when(requestContext.getBookingSessionId()).thenReturn("6ef42e87-e17a-4b2c-9bd5-b397fa0f890a")
    when(requestContext.featureAware).thenReturn(Some(featureAware))
    when(context.requestContext).thenReturn(requestContext)
    when(context.whiteLabelInfo).thenReturn(whiteLabelInfo)
    when(context.deviceContext).thenReturn(Some(deviceContext))
    when(context.bookingFlowType).thenReturn(bookingFlow)
    context
  }

  override def getSetupBookingRequest: SetupBookingRequest = {
    val setupBookingRequest: SetupBookingRequest = mock[SetupBookingRequest]
    val paymentRequest                           = None
    when(setupBookingRequest.paymentRequest).thenReturn(Some(getPaymentRequest))
    when(setupBookingRequest.enabledFeatures).thenReturn(paymentRequest)
    when(setupBookingRequest.userContext).thenReturn(Some(getSetupBookingRequestUserContext))
    when(setupBookingRequest.loyaltyRequest).thenReturn(Some(getSetupBookingRequestLoyaltyRequest))
    setupBookingRequest
  }

  def getPaymentRequest: PaymentRequest = PaymentRequest(
    ccBin = None,
    ccId = Some(1111L),
    selectedPaymentMethod = None,
    selectedChargeCurrency = Some("THB")
  )

  def getSetupBookingRequestUserContext: UserContext =
    UserContext(
      languageId = 1,
      requestOrigin = "A1",
      currency = "THB",
      nationalityId = 0,
      experimentData = None,
      isLoggedInUser = true,
      capiToken = None
    )
  def getSetupBookingRequestLoyaltyRequest: LoyaltyRequest = LoyaltyRequest(points = Some(10000.0))

  def getSetupUserContext(
      languageId: Int = 1,
      requestOrigin: String = "A1",
      currency: String = "THB",
      nationalityId: Int = 2,
      experimentData: Option[ExperimentData] = Some(
        ExperimentData("userId", "DeviceType", Some("MemberId"), None, Some("cid"), Some("aid"), Some("server"))
      ),
      memberId: Option[Int] = None
  ): UserContext = UserContext(
    languageId = languageId,
    requestOrigin = requestOrigin,
    currency = currency,
    nationalityId = nationalityId,
    experimentData = experimentData,
    memberId = memberId
  )

  def getMockFlightProductData: ProductData = mockProductsProduct.copy(
    flights = Seq(
      flightsDataProduct.head.copy(
        flightItinerary = Some(
          FlightItinerary(
            id = "Flight-1",
            slices = Seq(
              SearchResponseSlice(
                id = 1,
                duration = 1,
                overnightFlight = true,
                segments = Seq.empty,
                freeBags = Seq.empty,
                cancellationPolicies = Seq.empty,
                exchangePolicies = Seq.empty,
                voidableWithinHours = 0
              )
            ),
            paymentModel = PaymentModel.Agency.id
          )
        )
      )
    )
  )

  def setupRequestMock(paymentFeature: List[String] = List.empty[String]): SetupPaymentRequestV2 =
    SetupPaymentRequestV2(
      UserContext = Some(
        UserContextClientV2(
          UserId = Some("userId"),
          MemberId = Some("MemberId"),
          LanguageId = Some(3),
          RequestOrigin = Some("A1"),
          ResidentCountryId = Some(2),
          DeviceTypeId = Some(5),
          SiteId = Some(-1),
          CapiToken = Some(""),
          PartnerClaimToken = Some("")
        )
      ),
      AnalyticContext = Some(
        AnalyticContextClient(
          PaymentAmount = None,
          UserAgent = Some(
            "{\"origin\":\"\",\"osName\":\"\",\"osVersion\":\"\",\"browserName\":\"\",\"browserLanguage\":\"\",\"browserVersion\":\"\",\"browserSubVersion\":\"\",\"browserBuildNumber\":\"\",\"deviceBrand\":\"\",\"deviceModel\":\"\",\"deviceTypeId\":0}"
          ),
          SessionId = Some("6ef42e87-e17a-4b2c-9bd5-b397fa0f890a"),
          AcceptHeader = None,
          BookingFlow = Some("SingleFlight"),
          ProductType = None,
          BookingSessionId = None,
          BrowserUserAgent = None,
          AppUserAgent = None,
          AnalyticsSessionId = None,
          ClientIp = Some(""),
          IsAffiliateUserLoggedIn = Some(false),
          IsLoggedInUser = Some(false)
        )
      ),
      PaymentContext = Some(
        PaymentContextClient(
          SelectedPaymentMethodId = Some(0),
          SelectedChargeCurrency = Some("THB"),
          CcToken = Some(""),
          CcBin = Some(""),
          InstallmentPlanCode = Some(""),
          IncludeThirdPartyInstallmentProviders = Some(false),
          SupportedChargeOptions = Some(List(1)),
          PaymentModel = Some(PaymentModel.Agency.id),
          PaymentAmount = Some(
            AmountClient(
              Value = Some(0.0),
              Currency = Some("THB")
            )
          ),
          CcId = Some(1111L)
        )
      ),
      ProductContext = Some(
        ProductContextClient(
          WhiteLabelId = Some(1),
          ProductList = Some(
            List(
              ProductClient(
                ProductId = Some(1),
                ProductCount = Some(1),
                BusinessCountryIds = Some(List()),
                MerchantOfRecord = Some(0),
                ProductReferenceId = Some(0)
              )
            )
          ),
          ItineraryContextJson = new ItineraryContext().toJson.toOption
        )
      ),
      FeatureContext = Some(
        FeatureContextClient(
          FeatureList = Some(paymentFeature),
          IncludedPaymentMethod = Some(List()),
          ExcludedPaymentMethod = Some(List(252, 253, 254)),
          FeeEnabledPaymentMethodIds = Some(List()),
          AdditionalFields = Some(Map())
        )
      ),
      ExperimentContext = Some(
        ExperimentContextClient(
          SupportedFeatures = Some(List()),
          ForcedExperiment = Some(Map())
        )
      )
    )

  val paymentMethod: PaymentMethodDetailsV2 =
    new PaymentMethodDetailsV2(
      id = 1,
      name = "Visa",
      paymentFlow = PaymentFlow.None,
      paymentGroupCategory = PaymentGroupCategory.None,
      icons = Seq(
        PaymentMethodIcon(
          `type` = 1,
          url = "cdn6/images/visa"
        )
      ),
      timeout = Some(1),
      gatewayName = None,
      remarks = Seq.empty,
      chargeDateTypes = ChargeOption.PayNow,
      chargeOptions = Set(ChargeOption.PayNow, ChargeOption.PayLater),
      isRecommended = false,
      ranking = 1,
      defaultCurrency = "USD",
      cardNumRegEx = Some("^[0-9]{16}$"),
      cvcRegEx = Some("^[0-9]{3}$"),
      isLuhnCheckRecommended = Some(true),
      requiredFields = None,
      isFapiaoEligible = Some(false),
      isTokenEnabled = Some(false)
    )

  val setupPaymentResponseMock: SetupPaymentResponseV2 = SetupPaymentResponseV2(
    SuggestedPaymentMethod = Some(List(1, 2, 3)),
    PaymentMethodDetails = Some(
      List(
        PaymentMethodDetailsResponse(
          PaymentMethodId = Some(1),
          PaymentMethodName = Some("Visa"),
          PaymentFlow = Some(PaymentFlow.None.id),
          PaymentCategory = Some(PaymentGroupCategory.None.id),
          Timeout = Some(1),
          PaymentMethodIcons = Some(
            List(
              PaymentMethodIconClient(
                Type = Some(1),
                Url = Some("cdn6/images/visa")
              )
            )
          ),
          Remarks = None,
          RequiredField = None,
          ChargeType = Some(1),
          ChargeOptions = Some(List(1, 2)),
          IsRecommended = Some(false),
          Ranking = Some(1),
          DefaultCurrency = Some("USD"),
          CardNumRegex = Some("^[0-9]{16}$"),
          CvcRegex = Some("^[0-9]{3}$"),
          IsLuhnCheckRecommended = Some(true),
          IsTokenEnabled = Some(false),
          IsFapiaoEligible = Some(false),
          FeeInfo = None
        )
      )
    ),
    CreditCardOnFile = None,
    CreditCardInfoSetup = None,
    NonCardOnFile = None,
    InstallmentPlans = None,
    InstallmentAvailableProviders = None,
    InstallmentPlanCode = None,
    IsInstallmentEligible = Some(true),
    RequestStatus = None,
    RequestMessage = None
  )
}

final case class PaymentServiceComparisonTestResult(
    service: PaymentServiceImpl,
    mockLogger: Underlying,
    requestContextMarkerCaptor: ArgumentCaptor[RequestContextMarker]
)

final case class PaymentServiceGetPaymentMethodsTestResult(
    result: AvailablePaymentMethodsResult,
    paymentRequestCaptor: ArgumentCaptor[SetupPaymentRequestV2]
) {
  def getIncludedPaymentMethod: Seq[Int] =
    paymentRequestCaptor.getValue.FeatureContext
      .flatMap(_.IncludedPaymentMethod)
      .getOrElse(Seq.empty[Integer])
      .map(_.intValue)

  def getExcludedPaymentMethod: Seq[Int] =
    paymentRequestCaptor.getValue.FeatureContext
      .flatMap(_.ExcludedPaymentMethod)
      .getOrElse(Seq.empty[Integer])
      .map(_.intValue)

  def getAdditionalFields: Map[String, String] =
    paymentRequestCaptor.getValue.FeatureContext
      .flatMap(_.AdditionalFields)
      .getOrElse(Map.empty[String, String])
      .map(x => (x._1, x._2))

  def getPaymentFeature: Set[PaymentFeature] =
    paymentRequestCaptor.getValue.FeatureContext.flatMap(_.FeatureList).getOrElse(List.empty[String]).toSet
  def getUserContext: UserContextClientV2 =
    paymentRequestCaptor.getValue.UserContext.getOrElse(UserContextClientV2())
  def getProductContext: ProductContextClient =
    paymentRequestCaptor.getValue.ProductContext.getOrElse(ProductContextClient())
}
