package com.agoda.bapi.server.facades

import api.request.FeatureFlag
import cats.implicits._
import com.agoda.bapi.common.BcreMetricReporter
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message._
import com.agoda.bapi.common.message.creation.{BookingCreationContext, HotelGuest, UserAgent}
import com.agoda.bapi.common.message.pricebreakdown.{PriceBreakdownNode, PriceBreakdownType, PriceDisplayType}
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.model.ChargeOption.PayNow
import com.agoda.bapi.common.model.activity.ActivityBookingToken
import com.agoda.bapi.common.model.addOn.UserTaxCountry
import com.agoda.bapi.common.model.addOn.cegFastTrack.AddOnBookingToken
import com.agoda.bapi.common.model.addOn.cegFastTrack.CEGFastTrackTiers.NormalHotelFastTrack
import com.agoda.bapi.common.model.addon.CEGFastTrackConfirmationData
import com.agoda.bapi.common.model.car._
import com.agoda.bapi.common.model.cart.CartContext
import com.agoda.bapi.common.model.consumerFintech.products.ConsumerFintechProductDetail
import com.agoda.bapi.common.model.consumerFintech.products.cancelAndRebookV3.{CancelAndRebookRequirement, CancelAndRebookV3ProductDetail}
import com.agoda.bapi.common.model.consumerFintech.{ConsumerFintechDetail, ConsumerFintechRequirement}
import com.agoda.bapi.common.model.creation.{DiscountType, PriceChangeOnPollingStatus}
import com.agoda.bapi.common.model.externalloyalty.ExternalLoyaltyAdditionalInfo
import com.agoda.bapi.common.model.multiproduct.AccountingEntities
import com.agoda.bapi.common.model.payment.PaymentModel
import com.agoda.bapi.common.model.pricefreeze._
import com.agoda.bapi.common.model.product.BookingFlow.SingleProperty
import com.agoda.bapi.common.model.product.{BookingFlow, ProductTypeEnum}
import com.agoda.bapi.common.model.protection.ProtectionRequestItemOptInValue
import com.agoda.bapi.common.model.rebookAndCancel.{RebookAndCancelData, RebookAndCancelRequirement}
import com.agoda.bapi.common.model.tripProtection._
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.model.{ChargeOption, CurrencyCode, Money => _, PaymentModel => _, UserContext, WhiteLabel, WhiteLabelInfo, _}
import com.agoda.bapi.common.token.common.ProductTokenKey
import com.agoda.bapi.common.token.flight.FlightToken
import com.agoda.bapi.common.token.{AddOnBookingModel, AgentAssistedBookingInfo => TokenAabInfo, ExternalLoyaltyRequestInfo, FlightAgentAssistedBookingInfo => TokenFlightAabInfo, GenericAddOnBookingModel, Money => TMoney, MultiProductBookingToken, MultiProductCreationBookingToken, MultiProductRetryPaymentBookingToken, MultiProductSetupBookingToken, PaymentRequestInfo, PropertySetupBookingToken}
import com.agoda.bapi.common.util.{TokenSerializer, TokenSerializers}
import com.agoda.bapi.common.util.TokenSerializers._
import com.agoda.bapi.creation.BookingTokenHelper
import com.agoda.bapi.creation.service.observability.LogContext
import com.agoda.bapi.creation.service.observability.setup.PropertyContentLog
import com.agoda.bapi.creation.validation.WhiteLabelFeatureMock
import com.agoda.bapi.server.addon.AddOnDefaults.defaultAddOnDataWithCegFastTrack
import com.agoda.bapi.server.addon._
import com.agoda.bapi.server.facades.aggregator.{FlightProductTokenAggregatorMixin, PropertyProductTokenAggregatorMixin, TripProtectionProductTokenAggregatorMixin}
import com.agoda.bapi.server.facades.helpers.{PapiPropertiesFixture, ProductsFacadeFixture, SetupBookingContextFixture, SetupBookingRequestFixture}
import com.agoda.bapi.server.handler.context.{RetryPaymentContext, SetupBookingContext, SetupBookingSessionContext}
import com.agoda.bapi.server.model._
import com.agoda.bapi.server.model.pricebreakdown.flights.MockFlightsPricingData
import com.agoda.bapi.server.model.property.BookingProperty
import com.agoda.bapi.server.reporting.CustomerContactInfo
import com.agoda.bapi.server.repository.dto.papi.{AdditionalPricingParameter, CardPaymentRequestParameter, DiscountRequest, GiftCardRedeemRequest}
import com.agoda.bapi.server.utils.SetupBookingMock
import com.agoda.bapi.server.utils.partner.{DefaultPartnerPromoUtils, PartnerPromoUtils, PropertiesPromoUtils}
import com.agoda.externalloyalty.client.v2.model.DistributePointsRequest.PointsOfferType
import com.agoda.externalloyalty.client.v2.model.DistributePointsResponse.RedemptionType
import com.agoda.externalloyalty.client.v2.model.{CartItemReq, CartItemRes, DistributePointsResponse}
import com.agoda.flights.client.v2.model.{ExternalLoyaltyPricing, PackagingToken, PricingDiscount, PromotionCampaignIds}
import com.agoda.gandalf.common.CrgwCampaignProductType
import com.agoda.gandalf.response.{CreditCardPromotionResponse, Discount}
import com.agoda.mpb.common.PointsType
import com.agoda.mpb.common.models.state.ProductType.ProductType
import com.agoda.mpb.common.models.state._
import com.agoda.mpbe.state.common.enums.PaymentMethod.{PaymentMethod => MPBPaymentMethod}
import com.agoda.upi.models.enums.ItemEntries
import com.agoda.winterfell.output.{ExternalLoyaltyUserProfileResponse, LoyaltyProfile, SubLoyaltyPrograms}
import com.softwaremill.quicklens._
import mocks.BookingMockHelper._
import mocks._
import models.pricing.enums.SwapRoomTypes
import models.starfruit.AlternativeRoom
import models.starfruit.PropertyPricingJsonProtocol.ResponseStateToken
import org.joda.time.{DateTime, LocalDate, LocalDateTime}
import org.mockito.ArgumentMatchers.{any, argThat, eq => eqTo}
import org.mockito.Mockito._
import org.mockito.invocation.InvocationOnMock
import org.mockito.{ArgumentCaptor, ArgumentMatcher, ArgumentMatchers, Mockito}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.TableDrivenPropertyChecks
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{BeforeAndAfter, TryValues}
import org.scalatestplus.mockito.MockitoSugar
import transformers._

import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter
import scala.concurrent.Future
import scala.language.postfixOps
import scala.util.{Random, Success, Try}

class ProductsFacadeSpec
    extends AsyncWordSpec
    with MockitoSugar
    with Matchers
    with SetupBookingMock
    with SetupBookingWithAlternativesMock
    with ScalaFutures
    with PropertyMock
    with ProductTokenMockHelper
    with BeforeAndAfter
    with TableDrivenPropertyChecks
    with TryValues
    with BookingTokenHelper
    with WhiteLabelFeatureMock
    with FlightProductTokenAggregatorMixin
    with PropertyProductTokenAggregatorMixin
    with TripProtectionProductTokenAggregatorMixin
    with ActivityMock
    with AddOnV2MockHelper {

  val productsFacadeFixture: ProductsFacadeFixture = new ProductsFacadeFixture {}

  import productsFacadeFixture._

  val setupBookingContextFixture = new SetupBookingContextFixture {}

  import setupBookingContextFixture._

  val setupBookingRequestFixture = new SetupBookingRequestFixture {}

  import setupBookingRequestFixture._

  val expectedPackagesToken = PackageRequest(clientToken = "client", interSystemToken = Some("system"))

  val cartToken          = "cart-token"
  val defaultPropertyId  = 1234567L
  val cartPricingContext = CartPricingContext(Some(cartToken), None)

  val authDate   = new DateTime("2019-06-03T00:00+07:00")
  val chargeDate = new DateTime("2019-06-04T00:00+07:00")

  val usdCurrency: CurrencyCode = "USD"
  val thbCurrency: CurrencyCode = "THB"
  val hkdCurrency: CurrencyCode = "HKD"

  val campaignInfo            = CampaignInfoRequest(id = Some(1), cid = 0, promotionCode = "AGODA20")
  val expectedToken           = TokenMessage(defaultMultiBookingTokenString, 1)
  val userContext             = mock[UserContext]
  val deviceContext           = mock[DeviceContext]
  val experimentData          = mock[ExperimentData]
  implicit val whiteLabelInfo = mock[WhiteLabelInfo]

  val papiPropertiesFixture = new PapiPropertiesFixture {}

  import papiPropertiesFixture._

  val mockPropertySetupBookingToken = mock[PropertySetupBookingToken]
  val overrideMockProduct           = createMockProduct()
  val protectionsMock               = Seq(mock[TripProtectionProductItem])
  val protectionDataMock            = Some(mock[TripProtectionData])

  val mockToken =
    "NwXm7b9LrYeOA58m28C/UmQAnSxHq8XA9mSbYyUPtuf4cAuULn2O3ak3lhwzQDeSe5ytJQB+0/4EZSFZItuGBGqhLNjAlcWfP05M2C05J4buTv0AmrtBd/0S6Q5Fk63G2tv1JCSPPVKOyzqIQ8YxeJoxtxK7nzJhRNbkHkEOcf8jIreT5nufM/uxxM5lrLMjPdThw0s7PmVw1ixumpMXjuVCVEBRBMQHWnKyD/spgnF0DNxtslqyf4KypJ09Ha0xaST4wjiIfME0m/zUOnIOFX+hPu36medgNUci5nbxQWppc3jEdn7Jm1NPxoz7NIbSdv8/6GNjxmzRgefbeStLgnI3HLnBopEYoPbga/olsqX/zrJ5sA9RxJ3uRYdLAWjAn+SxUdPoIe4mbdBtUebD2A+V4kOm+1aTtVBfzvxoyQV/1E8E/y44NZWTOTnj03Gac7O6QIngPGse6c4JHTmr5g5uayJcZywf24e0MSr85IZrvNOCBdGa/Zl0KJZ/0iwHj51bQ8Gr7Xdzi418K12IFw77OdQA3GCuHPmQCBrKMtEDH5mze8CglrRBM0tcmeTt"
  val protectionMockProduct = overrideMockProduct.copy(protections = protectionsMock)
  val tripProtectionToken = TripProtectionToken(
    protectionType = 1,
    products =
      Seq(ProtectionProductIds("1", ProductTypeEnum.Flight), ProtectionProductIds("2", ProductTypeEnum.Flight)),
    priceAmount = CurrencyOption(
      23.3,
      "EUR"
    ),
    priceAmountUSD = 27.7,
    marginAmount = CurrencyOption(
      13.3,
      "EUR"
    ),
    marginAmountUSD = 16.6,
    marginPercentage = 10,
    quantity = 2,
    tripCost = CurrencyOption(13.3, "EUR"),
    purchaseDate = OffsetDateTime.parse("2019-03-21T22:00:42.464Z", DateTimeFormatter.ISO_OFFSET_DATE_TIME),
    supplierId = 1,
    subSupplierId = 2,
    supplierData = SupplierData("supplierData"),
    supplierSearchId = "1",
    financialBreakdowns = Seq.empty,
    paymentModel = PaymentModel.Merchant,
    productPayment = Some(
      ProductPaymentInfo(
        agency = None,
        payNow = Some(
          PayNowProductPayment(
            ProductPayment(
              paymentAmount = 23.3,
              paymentAmountUsd = 27.7,
              paymentCurrency = "EUR"
            )
          )
        ),
        payLater = None,
        accountingEntity = Some(AccountingEntities.protectionEntity)
      )
    )
  )
  val packageTokenAfterPropertyCall = Some(expectedPackagesToken.copy(clientToken = "prop-client"))

  val activityMockProduct =
    overrideMockProduct.copy(
      properties = Seq.empty,
      flights = Seq.empty,
      activities = Seq(mockedActivityConfirmationData)
    )

  val cegFastTrackMockProduct: ProductData =
    overrideMockProduct.copy(properties = Seq.empty, flights = Seq.empty, addOnData = defaultAddOnDataWithCegFastTrack)

  when(setupBookingContext.requestContext).thenReturn(requestContext)
  when(setupBookingContext.requestContext.featureAware).thenReturn(Some(featureAware))
  when(experimentData.aId).thenReturn(Some(""))
  when(mockConsumerFintechService.getConsumerFintechRequirement(any())(any())).thenReturn(Future.successful(None))
  when(mockRebookAndCancelService.getRebookAndCancelRequirement(any())(any())).thenReturn(None)

  val mockPropertyRequest = mock[PropertyRequestItem]

  val mockProductRequest = mock[ProductsRequest]
  val mockProductData    = mock[ProductData]

  val mockPropertyData = mock[BookingProperty]

  def mockPriceBreakdownServiceGetPriceBreakdown(priceBreakdown: Option[PriceBreakdownNode] = priceBreakdown): Unit = {
    when(
      mockPriceBreakdownService.getPriceBreakdown(
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any()
      )(
        any()
      )
    )
      .thenReturn(Future.successful(priceBreakdown.get))
  }
  def mockPropertyServiceRetrieveProperties(
      mockPropertyResponses: Seq[BookingPropertiesData]*
  ): Unit = {
    when(
      propertyService.retrieveProperties(
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any()
      )(
        any()
      )
    ).thenReturn(
      Future.successful(mockPropertyResponses.head),
      mockPropertyResponses.drop(1).map(Future.successful): _*
    )
  }

  def createMockProduct(
      fullyAuthDate: Option[DateTime] = Some(new DateTime("2019-06-03T00:00+07:00")),
      fullyChargeDate: Option[DateTime] = Some(new DateTime("2019-06-04T00:00+07:00")),
      isCartMultiProperties: Boolean = false
  ): ProductData = {
    val mockProperty = Seq(
      createMockProperty(
        propertyId = 1L,
        booking = Some(
          createMockEnrichedBookingItem(
            fullyAuthDate = fullyAuthDate,
            fullyChargeDate = fullyChargeDate
          )
        )
      )
    )
    val mockProperties = if (isCartMultiProperties) {
      mockProperty ++ mockProperty
    } else {
      mockProperty
    }
    mockProductsProduct.copy(
      properties = mockProductsProduct.properties.map(
        _.copy(
          papiProperties = Some(
            Properties(
              property = mockProperties,
              debug = None,
              dfMetaResult = DFMetaResult(Some(ResponseStateToken("", true, Some("pricingRequestId-1"))))
            )
          ),
          packageRequest = Some(expectedPackagesToken)
        )
      ),
      totalPriceDisplay = priceBreakdown
    )
  }

  def createMockProductWithAlternatives(
      fullyAuthDate: Option[DateTime] = Some(new DateTime("2019-06-03T00:00+07:00")),
      fullyChargeDate: Option[DateTime] = Some(new DateTime("2019-06-04T00:00+07:00"))
  ) =
    mockProductsProduct.copy(
      properties = mockProductsProduct.properties.map(
        _.copy(
          papiProperties = Some(
            Properties(
              Seq(
                createMockProperty(
                  propertyId = 1L,
                  booking = Some(
                    createMockEnrichedBookingItem(
                      roomUidOpt = Some("uid-original"),
                      fullyAuthDate = fullyAuthDate,
                      fullyChargeDate = fullyChargeDate
                    )
                  ),
                  roomSwapping = List(
                    AlternativeRoom("roomiden-original", "roomiden-breakfastupsell", SwapRoomTypes.BreakfastUpSell),
                    AlternativeRoom("roomiden-original", "roomiden-crosssell", SwapRoomTypes.NormalCrossSell)
                  ),
                  masterRooms = Seq(masterRoomWithAlternatives),
                  getAllRooms = Seq(childRoomBreakFastUpSell, childRoomOriginal)
                )
              ),
              debug = None,
              dfMetaResult = DFMetaResult(Some(ResponseStateToken("", true, Some("pricingRequestId-1"))))
            )
          ),
          packageRequest = None,
          propertySearchCriteria = Some(propertySearchCriteriaBySelection("roomiden-original"))
        )
      ),
      totalPriceDisplay = priceBreakdown
    )

  private def flightsDataWithLoyalty(id: String) =
    overrideMockProduct.flights.map(
      _.copy(
        flightPricing = Some(
          overrideMockProduct.flights.head.flightPricing.get
            .updated(
              hkdCurrency,
              overrideMockProduct.flights.head.flightPricing
                .get(hkdCurrency)
                .copy(
                  externalLoyalty = Some(
                    ExternalLoyaltyPricing(
                      offers = Vector(MockFlightsPricingData.itineraryOffer(id).get)
                    )
                  )
                )
            )
        )
      )
    )

  val mockCcCampaignResponse = CreditCardPromotionResponse(
    id = 1230913,
    campaignId = 23492,
    siteId = Some(20234),
    displayName = "cc contactless reduce COVID-19 chance",
    bin = Some("233333"),
    discount = Discount(
      discountType = "PERCENT",
      amount = 5.0,
      currency = Some(chargeCurrency)
    ),
    membershipContentId = None,
    membershipContentCmsId = None,
    productType = CrgwCampaignProductType.Flight,
    isCashback = None
  )

  val ccCampaignMock = Seq(
    CampaignInfoInternal(
      campaignInfo = CampaignInfo(
        id = mockCcCampaignResponse.campaignId,
        cid = mockCcCampaignResponse.siteId.get,
        promotionCode = "",
        discountType = DiscountType.Percent,
        amount = 5.0,
        currency = Some(chargeCurrency)
      ),
      promotionId = Some(mockCcCampaignResponse.id)
    )
  )

  val addOnData = AddOnDefaults.defaultAddOnDataWithCegFastTrack

  val supportPriceDisplayTypesTestCases = Table(
    "isUNIBF2454",
    false,
    true
  )

  before {
    reset(papiProperties)
    reset(papiProperty)
    reset(roomBundle)
    reset(mockPropertyData)
    reset(mockPropertyRequest)
    reset(displayBasis)
    reset(savings)
    reset(price)
    reset(bookingItem)
    reset(room)
    reset(ebeRoom)
    reset(searchCriteria)
    reset(subSearch)
    reset(mockPricingTree)
    reset(flightRepository)
    reset(carService)
    reset(protectionService)
    reset(mockAddOnService)
    reset(setupBookingContext)
    reset(propertyService)
    reset(productTokenUtils)
    reset(requestContext)
    reset(userContext)
    reset(deviceContext)
    reset(mockReporter)
    reset(mockCampaignService)
    reset(mockMessageService)
    reset(mockBookingsService)
    reset(segment1)
    reset(segment2)
    reset(masterRooms1)
    reset(childRooms1)
    reset(masterRooms2)
    reset(childRooms2)
    reset(mockPropertySetupBookingToken)
    reset(mockActivityService)
    reset(featureAware)
    reset(mockTotalSavingsService)
    reset(mockBookingMessagingService)
    reset(whiteLabelInfo)
    reset(mockAddOnFacade)
    BcreMetricReporter._reporter = mockReporter

    when(subSearch.checkIn).thenReturn(DateTime.now())
    when(searchCriteria.searchCriteria).thenReturn(subSearch)
    when(bookingItem.rooms).thenReturn(List(room, room))
    when(bookingItem.booking).thenReturn(List(ebeRoom, ebeRoom))
    when(papiProperty.booking).thenReturn(Some(bookingItem))
    when(price.allInclusive).thenReturn(1234)
    when(displayBasis.perBook).thenReturn(price)
    when(roomBundle.bundleType).thenReturn(1)
    when(childRooms2.uid).thenReturn(Some("234"))
    when(masterRooms2.childrenRooms).thenReturn(List(childRooms2))
    when(childRooms1.uid).thenReturn(Some("123"))
    when(masterRooms1.childrenRooms).thenReturn(List(childRooms1))
    when(segment1.checkIn).thenReturn(DateTime.now())
    when(segment1.los).thenReturn(2)
    when(segment1.masterRooms).thenReturn(List(masterRooms1))
    when(segment2.checkIn).thenReturn(DateTime.now().plusDays(2))
    when(segment2.los).thenReturn(2)
    when(segment2.masterRooms).thenReturn(List(masterRooms2))
    when(roomBundle.segments).thenReturn(List(segment1, segment2))
    when(roomBundle.totalPrice).thenReturn(displayBasis)
    when(roomBundle.saveAmount).thenReturn(savings)
    when(papiProperty.roomBundles).thenReturn(List(roomBundle))
    when(mockPropertyData.properties).thenReturn(Some(papiProperties))
    when(papiProperties.property).thenReturn(Seq(papiProperty))
    when(papiProperties.searchStatusAndCriteria).thenReturn(Some(searchCriteria))
    when(mockPriceBreakdownService.getMixAndSavePriceBreakdown(any(), any(), any(), any(), any(), any(), any())(any()))
      .thenReturn(Future.successful(Some(mockPricingTree)))

    // TODO: delete these dup
    when(mockPropertySetupBookingToken.preBookingId).thenReturn(Some(1L))
    when(mockPropertySetupBookingToken.productToken).thenReturn(Some(""))

    when(setupBookingContext.session)
      .thenReturn(
        SetupBookingSessionContext(
          Map("key" -> mockPropertySetupBookingToken),
          Some(expectedPackagesToken),
          expectedTimeStamp
        )
      )
    when(setupBookingContext.getIsRoomHasSwapped(any())).thenReturn(None)
    when(setupBookingContext.getOriginalRoomIdentifier(any())).thenReturn(None)
    when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Package)
    when(setupBookingContext.requestContext).thenReturn(requestContext)
    when(setupBookingContext.requestContext.featureAware).thenReturn(Some(featureAware))

    when(requestContext.displayCurrency()).thenReturn(thbCurrency)
    when(requestContext.userContext).thenReturn(Some(userContext))

    when(requestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
    mockPropertyServiceRetrieveProperties(
      Seq(bookingPropertyProduct().copy(packageRequest = packageTokenAfterPropertyCall))
    )

    when(mockBookingMessagingService.mapIsMessageHostAvailableTo(any())(any())) thenAnswer ((i: InvocationOnMock) =>
      Future.successful(i.getArgument(0))
    )

    when(setupBookingRequest.paymentRequest).thenReturn(None)
    when(setupBookingRequest.campaignInfo).thenReturn(Some(campaignInfo))
    when(setupBookingRequest.userContext).thenReturn(Some(userContext))
    when(setupBookingRequest.isRetryPayment).thenReturn(None)
    when(setupBookingRequest.autoApplyCreditCardPromotionInfo).thenReturn(None)
    when(setupBookingRequest.deviceContext).thenReturn(None)
    when(requestContext.bookingCreationContext).thenReturn(None)
    when(productsRequest.propertyRequests).thenReturn(Seq.empty)
    when(productsRequest.flightRequests)
      .thenReturn(Seq(FlightRequestItem(Some("test"), Some(ConfirmPriceRequest("test", "test")), None)))
    when(productsRequest.tripProtectionRequests).thenReturn(None)
    when(productsRequest.bookingToken).thenReturn(None)
    when(mockPropertyRequest.confirmPriceRequest).thenReturn(None)
    when(productTokenUtils.createMultiProductsToken(any(), any(), any(), any())).thenReturn(Try(expectedToken))
    when(
      productTokenUtils.createMultiProductSetupBookingToken(
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any()
      )(
        any()
      )
    ).thenReturn(Try(defaultMultiProductSetupToken))
    when(
      productTokenUtils
        .createMultiProductCreationBookingToken(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
    ).thenReturn(Try(defaultMultiProductCreationToken))
    when(setupBookingContext.requestContext.featureAware).thenReturn(Some(featureAware))
    when(mockTotalSavingsService.getTotalSavingsForPackages(any(), any()))
      .thenReturn(Future.successful(totalSavings))

    when(userContext.memberId).thenReturn(Some(0))
    when(userContext.experimentData).thenReturn(Some(experimentData))
    when(experimentData.cId).thenReturn(Some("0"))
    when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
    when(mockCampaignService.getCampaignInfo(any(), any(), any())(any(), any()))
      .thenReturn(
        Future.successful(Seq(CampaignInfoInternal(CampaignInfo(5, 1, "TEST", DiscountType.Percent, 5.0, None), None)))
      )
    when(mockPropertyRequest.payment).thenReturn(Some(ProductPaymentRequest(ChargeOption.PayNow)))
    val searchCriteriaMock = mock[PropertySearchCriteria]
    when(searchCriteriaMock.simplifiedRoomSelectionRequest).thenReturn(None)
    when(mockPropertyRequest.propertySearchCriteria).thenReturn(searchCriteriaMock)
    when(mockProductRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
    when(mockBookingsService.getPreBookingId()).thenReturn(Future.successful(Random.nextLong()))
    when(mockAddOnService.getAddOns(any(), any(), any(), any())(any())).thenReturn(Future.successful(Seq.empty))
    when(mockActivityService.toBookingToken(any())(any()))
      .thenReturn(Success(Map.empty[ProductTokenKey, ActivityBookingToken]))
    when(mockPriceFreezeService.toBookingToken(any())(any()))
      .thenReturn(Success(Map.empty[ProductTokenKey, PriceFreezeBookingToken]))
    when(mockAddOnBookingTokenService.toBookingToken(any())(any()))
      .thenReturn(Success(Map.empty[ProductTokenKey, AddOnBookingToken]))

    when(productsRequest.carRequestsOpt)
      .thenReturn(None)
    when(productsRequest.activityRequests)
      .thenReturn(None)
    when(protectionService.toBookingToken(any())(any()))
      .thenReturn(Success(Map.empty[ProductTokenKey, TripProtectionToken]))

    when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
    when(whiteLabelInfo.feature).thenReturn(featuresConfiguration())
    when(setupBookingContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
    when(setupBookingContext.requestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
    when(setupBookingContext.requestContext.experimentData).thenReturn(Some(experimentData))

    when(mockAddOnBookingTokenService.getConfirmationData(any())).thenReturn(Seq.empty)
    when(mockAddOnBookingTokenService.mapConfirmationData(any())).thenReturn(None)
    when(mockAddOnFacade.getAddOn(any())(any[RequestContext])).thenReturn(
      Future.successful(addOnData)
    )
  }

  def getProtectionItem(optInValue: Int, token: String): TripProtectionProductItem =
    TripProtectionProductItem(
      id = "protectionId",
      content = "protectionContent",
      isCompleted = true,
      tripProtectionData = Some(
        TripProtectionData(
          tripProtectionType = 1,
          protectionBookingToken = token,
          price =
            Some(CurrencyOption(amount = MockFlightsPricingData.TripProtectionAmount, currencyCode = hkdCurrency)),
          disclaimer = "protection disclaimer",
          solicitation = Solicitation(version = Some("test")),
          subSupplierId = MockFlightsPricingData.TripProtectionSubSupplierId,
          paymentModel = PaymentModel.Merchant.id,
          supplierAmount = Some(CurrencyOption(amount = 20, currencyCode = hkdCurrency)),
          selectedOptInValue = optInValue
        )
      )
    )

  "getPreBookingId" should {
    "get call if preBookingIds in PropertySetupBookingToken is empty for single property setup" in {
      when(mockPropertySetupBookingToken.preBookingId).thenReturn(None)
      mockPropertyServiceRetrieveProperties(Seq[BookingPropertiesData](bookingPropertyProduct()))
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      mockPriceBreakdownServiceGetPriceBreakdown()
      when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(None))
      when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
      when(mockRebookAndCancelService.getRebookAndCancelRequirement(any())(any())).thenReturn(None)
      when(setupBookingContext.session)
        .thenReturn(
          SetupBookingSessionContext(
            Map("key" -> mockPropertySetupBookingToken),
            Some(expectedPackagesToken),
            expectedTimeStamp
          )
        )

      productsFacade.composeProductData(setupBookingRequest, chargeCurrency)(setupBookingContext).map { result =>
        verify(propertyService, times(1))
          .retrieveProperties(
            any(),
            any(),
            eqTo(chargeCurrency),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(
            any()
          )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)

        verify(mockBookingsService, times(1)).getPreBookingId()

        result.properties.length shouldBe 1
      }
    }

    "not get call if preBookingIds PropertySetupBookingToken is not empty" in {
      val input = overrideMockProduct
      when(mockAddOnBookingTokenService.getConfirmationData(any())).thenReturn(Seq.empty)
      when(mockAddOnBookingTokenService.mapConfirmationData(any())).thenReturn(None)
      when(mockProductRequest.cartContext).thenReturn(Some(CartContext("cartRefId")))
      when(mockProductRequest.cartPricingContext).thenReturn(Some(cartPricingContext))
      when(mockPropertySetupBookingToken.preBookingId).thenReturn(Some(Random.nextLong()))

      Future.fromTry(
        productsFacade
          .createProductToken(input, None, None, isNewsletterOptedIn = false, productsRequest = mockProductRequest)(
            setupBookingContext
          )
          .map { result =>
            verify(mockBookingsService, times(0)).getPreBookingId()
            result shouldBe expectedToken
          }
      )
    }

    "creation token with rebookAndCancel" in {

      val input = overrideMockProduct.copy(
        rebookAndCancelData = Some(
          RebookAndCancelData(
            originalBookingId = 1,
            originalItineraryId = 1
          )
        )
      )

      when(mockAddOnBookingTokenService.getConfirmationData(any())).thenReturn(Seq.empty)
      when(mockAddOnBookingTokenService.mapConfirmationData(any())).thenReturn(None)
      when(mockProductRequest.cartContext).thenReturn(Some(CartContext("cartRefId")))
      when(mockProductRequest.cartPricingContext).thenReturn(Some(cartPricingContext))
      when(mockPropertySetupBookingToken.preBookingId).thenReturn(Some(Random.nextLong()))
      when(mockRebookAndCancelService.getRebookAndCancelRequirement(any())(any()))
        .thenReturn(Some(RebookAndCancelRequirement(1, 1)))

      Future.fromTry(
        productsFacade
          .createProductToken(input, None, None, isNewsletterOptedIn = false, productsRequest = mockProductRequest)(
            setupBookingContext
          )
          .map { result =>
            verify(mockBookingsService, never).getPreBookingId()
            result shouldBe expectedToken
          }
      )
    }
  }

  "createProductToken" should {
    val flightCreatedTransform =
      toFlightBookingModel(overrideMockProduct.flights, None)(setupBookingContext).get
    when(setupBookingContext.requestContext.displayCurrency()).thenReturn(thbCurrency)
    val propertyCreatedTransform = toPropertyBookingModel(
      overrideMockProduct.properties,
      Seq.empty,
      None,
      whiteLabelInfo,
      setupBookingContext.requestContext,
      None,
      LogContext(),
      SingleProperty
    )

    val propertySession =
      Map("1" -> PropertySetupBookingToken("1", Some("123"), Some(2), Some("df-token"), Some(1L), None, None))

    when(setupBookingContext.session)
      .thenReturn(
        SetupBookingSessionContext(
          propertySession,
          timestamp = expectedTimeStamp
        )
      )
    val propertySearchCriteria = mock[PropertySearchCriteria]
    when(mockProductRequest.propertyRequests).thenReturn(Seq(PropertyRequestItem("x1", propertySearchCriteria, None)))
    when(mockProductRequest.bookingToken).thenReturn(None)
    when(mockProductRequest.cartContext).thenReturn(None)
    when(setupBookingContext.getIsRoomHasSwapped(any())).thenReturn(None)
    when(setupBookingContext.getOriginalRoomIdentifier(any())).thenReturn(None)

    val propertySetupTransform =
      toPropertySetupModel(overrideMockProduct, mockProductRequest, Seq.empty)(setupBookingContext)

    "create BapiBooking correctly for ProductItems with a valid RateChannelSwap room" in {
      when(featureAware.rateChannelSwapSwitch).thenReturn(true)
      val rateChannelSwapRoomUid        = "rateChannelSwapRoomUid"
      val rateChannelSwapRoomIdentifier = rateChannelSwapRoomUid
      val bookedRoomUid                 = "bookedRoomUid"
      val alternativeRoom               = AlternativeRoom(bookedRoomUid, rateChannelSwapRoomUid, SwapRoomTypes.RateChannelSwap)
      val defaultBookingProperties      = bookingPropertyProductWithMasterRoomNoChildRoomForPriceFreeze()
      val defaultChildRoom              = defaultBookingProperties.childRooms.head
      val childRoom = defaultChildRoom.copy(
        uid = Some(rateChannelSwapRoomUid),
        roomIdentifiers = Some(rateChannelSwapRoomIdentifier)
      )

      val defaultBookingO            = defaultBookingProperties.papiProperties.flatMap(_.property.headOption.flatMap(_.booking))
      val defaultRoomBooking         = defaultBookingO.flatMap(_.rooms.headOption).get
      val rateChannelSwapRoomBooking = defaultRoomBooking.copy(uid = rateChannelSwapRoomUid)
      val defaultBooking             = defaultBookingO.flatMap(_.booking.headOption).get
      val rateChannelSwapHotel       = defaultBooking.hotel.map(_.copy(room = List(rateChannelSwapRoomBooking)))
      val rateChannelSwapBooking     = defaultBooking.copy(hotel = rateChannelSwapHotel)

      val updatedBookedRooms = List(defaultRoomBooking, rateChannelSwapRoomBooking)
      val updatedBookings    = List(defaultBooking, rateChannelSwapBooking)

      val property = defaultBookingProperties.papiProperties
        .flatMap(_.property.headOption)
        .map(p =>
          p.copy(
            roomSwapping = List(alternativeRoom),
            rooms = Seq.empty,
            masterRooms = p.masterRooms.map(_.copy(childrenRooms = List(defaultChildRoom, childRoom))).toSeq,
            booking = p.booking.map(_.copy(rooms = updatedBookedRooms, booking = updatedBookings))
          )
        )
        .toSeq
      val updatedProperties = defaultBookingProperties.papiProperties.map(_.copy(property = property))
      val propertyCreatedTransform = toPropertyBookingModel(
        Seq(defaultBookingProperties.copy(papiProperties = updatedProperties)),
        Seq.empty,
        None,
        whiteLabelInfo,
        setupBookingContext.requestContext,
        None,
        LogContext(),
        SingleProperty
      )
      val bapiBookings = propertyCreatedTransform.values
      bapiBookings.size shouldBe 1
      val bapiBooking   = bapiBookings.head
      val childrenRooms = bapiBooking.getRooms()
      childrenRooms.size shouldBe 1
      childrenRooms.exists(_.uid.contains(rateChannelSwapRoomUid)) shouldBe true
      bapiBooking.alternativeRoomInfo.exists(_.isRateChannelSwap) shouldBe true
      childrenRooms.forall(_.uid == rateChannelSwapRoomUid) shouldBe true
    }

    "should get bookingToken correctly" in {
      when(mockAddOnBookingTokenService.getConfirmationData(any())).thenReturn(Seq.empty)
      when(mockAddOnBookingTokenService.mapConfirmationData(any())).thenReturn(None)
      when(mockProductRequest.cartContext).thenReturn(Some(CartContext("cartRefId")))
      when(mockProductRequest.cartPricingContext).thenReturn(Some(cartPricingContext))
      val input = overrideMockProduct

      flightCreatedTransform("1")
        .find(_.itineraryId == "")
        .flatMap(
          _.productPayment.flatMap(
            _.points
              .find(_.pointType == PointsType.ExternalLoyalty)
              .flatMap(_.pointAttributes.externalLoyaltyInfo.flatMap(_.partnerClaim))
          )
        ) shouldBe None

      Future.fromTry(
        productsFacade
          .createProductToken(input, None, None, isNewsletterOptedIn = false, productsRequest = mockProductRequest)
          .map { result =>
            verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
              eqTo(flightCreatedTransform),
              eqTo(propertyCreatedTransform),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(eqTo(setupBookingContext))

            verify(productTokenUtils, times(1)).createMultiProductSetupBookingToken(
              eqTo(propertySetupTransform),
              eqTo(Some(expectedPackagesToken)),
              eqTo(expectedTimeStamp),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())

            verify(productTokenUtils, times(1)).createMultiProductsToken(
              eqTo(Some(defaultMultiProductSetupToken)),
              eqTo(Some(defaultMultiProductCreationToken)),
              eqTo(None),
              any()
            )

            result shouldBe expectedToken
          }
      )
    }

    "should get bookingToken correctly when loyalty offers are present for flights" in {
      when(mockAddOnBookingTokenService.getConfirmationData(any())).thenReturn(Seq.empty)
      when(mockAddOnBookingTokenService.mapConfirmationData(any())).thenReturn(None)
      when(setupBookingRequest.userContext.get.currency).thenReturn(hkdCurrency)
      val input = overrideMockProduct.copy(
        properties = Vector.empty,
        flights = flightsDataWithLoyalty("")
      )

      val partnerClaim = Some("some-partner-claim")

      val loyaltyRequest = Some(
        LoyaltyRequest(
          partnerClaimToken = partnerClaim
        )
      )

      val flightCreatedTransformUpdated = toFlightBookingModel(input.flights, partnerClaim).get

      flightCreatedTransformUpdated("1")
        .find(_.itineraryId == "")
        .flatMap(
          _.productPayment.flatMap(
            _.points
              .find(_.pointType == PointsType.ExternalLoyalty)
              .flatMap(_.pointAttributes.externalLoyaltyInfo.flatMap(_.partnerClaim))
          )
        ) shouldBe partnerClaim

      Future.fromTry(
        productsFacade
          .createProductToken(
            input,
            None,
            None,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest,
            loyaltyRequest = loyaltyRequest
          )
          .map { result =>
            verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
              eqTo(flightCreatedTransformUpdated),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(eqTo(setupBookingContext))

            verify(productTokenUtils, times(1)).createMultiProductSetupBookingToken(
              any(),
              any(),
              eqTo(expectedTimeStamp),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())

            verify(productTokenUtils, times(1)).createMultiProductsToken(
              eqTo(Some(defaultMultiProductSetupToken)),
              eqTo(Some(defaultMultiProductCreationToken)),
              eqTo(None),
              any()
            )

            result shouldBe expectedToken
          }
      )
    }

    "should return retry payment bookingToken for flights when ABEX-548 is B" in {
      implicit val testFlightConfirmationDataSeqSerializer: TokenSerializer[Seq[FlightConfirmationData]] =
        new TokenSerializer[Seq[FlightConfirmationData]]
      val multiProductRetryPaymentBookingToken = MultiProductRetryPaymentBookingToken(
        flights = TokenSerializers[Seq[FlightConfirmationData]]
          .serialize(flightsDataWithLoyalty(""), Some(1571200505532L), None)
          .toOption
      )
      when(productTokenUtils.createMultiProductRetryPaymentBookingToken(any())(any()))
        .thenReturn(Try(multiProductRetryPaymentBookingToken))
      when(featureAware.encryptRetryPaymentBookingToken).thenReturn(true)

      val input = overrideMockProduct.copy(
        properties = Seq.empty,
        flights = flightsDataWithLoyalty("")
      )

      Future.fromTry(
        productsFacade
          .createProductToken(
            input,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest
          )
          .map { result =>
            verify(productTokenUtils, times(1)).createMultiProductRetryPaymentBookingToken(
              eqTo(input)
            )(eqTo(setupBookingContext))

            verify(productTokenUtils, times(1)).createMultiProductsToken(
              eqTo(Some(defaultMultiProductSetupToken)),
              eqTo(Some(defaultMultiProductCreationToken)),
              eqTo(Some(multiProductRetryPaymentBookingToken)),
              any()
            )

            result shouldBe expectedToken
          }
      )
    }

    "should clear PriceGuaranteeToken and return BookingToken when changedPrice found for properties" in {
      val input = overrideMockProduct
      val isPriceChangedPropertyRequest =
        Some(PriceChangeOnPollingStatus(priceChangeStatus = Map(input.properties.head.id -> false)))
      val expectedPropertySetupTransform =
        toPropertySetupModel(
          overrideMockProduct,
          mockProductRequest,
          Seq.empty,
          isPriceChangedPropertyRequest
        )
      assert(isPriceChangedPropertyRequest.head.isPollingRequired)
      assert(expectedPropertySetupTransform.get(input.properties.head.id).get.productToken == None)
      val updatedBookingCreationToken = propertyCreatedTransform.map {
        case (key, bapibooking) => key -> bapibooking.copy(isPriceGuaranteedTokenCleared = true)
      }
      when(mockProductRequest.cartPricingContext).thenReturn(Some(cartPricingContext))
      Future.fromTry(
        productsFacade
          .createProductToken(
            input,
            None,
            None,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest,
            isPriceChangedForRequiredProperty = isPriceChangedPropertyRequest
          )
          .map { result =>
            verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
              eqTo(flightCreatedTransform),
              eqTo(updatedBookingCreationToken),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(eqTo(setupBookingContext))
            verify(productTokenUtils, times(1)).createMultiProductSetupBookingToken(
              eqTo(expectedPropertySetupTransform),
              eqTo(Some(expectedPackagesToken)),
              eqTo(expectedTimeStamp),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
            verify(productTokenUtils, times(1)).createMultiProductsToken(
              eqTo(Some(defaultMultiProductSetupToken)),
              eqTo(Some(defaultMultiProductCreationToken)),
              eqTo(None),
              any()
            )

            result shouldBe expectedToken
          }
      )
    }

    "should not clear PriceGuaranteeToken and return BookingToken when changedPrice not found for properties" in {
      val input = overrideMockProduct
      val isPriceChangedPropertyRequest =
        Some(PriceChangeOnPollingStatus(priceChangeStatus = Map(input.properties.head.id -> true)))
      val expectedPropertySetupTransform =
        toPropertySetupModel(
          overrideMockProduct,
          mockProductRequest,
          Seq.empty,
          isPriceChangedPropertyRequest
        )
      assert(!isPriceChangedPropertyRequest.head.isPollingRequired)
      assert(expectedPropertySetupTransform.get(input.properties.head.id).get.productToken != None)

      when(mockProductRequest.cartPricingContext).thenReturn(Some(cartPricingContext))

      Future.fromTry(
        productsFacade
          .createProductToken(
            input,
            None,
            None,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest,
            isPriceChangedForRequiredProperty = isPriceChangedPropertyRequest
          )
          .map { result =>
            verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
              eqTo(flightCreatedTransform),
              eqTo(propertyCreatedTransform),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(eqTo(setupBookingContext))
            verify(productTokenUtils, times(1)).createMultiProductSetupBookingToken(
              eqTo(expectedPropertySetupTransform),
              eqTo(Some(expectedPackagesToken)),
              eqTo(expectedTimeStamp),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
            verify(productTokenUtils, times(1)).createMultiProductsToken(
              eqTo(Some(defaultMultiProductSetupToken)),
              eqTo(Some(defaultMultiProductCreationToken)),
              eqTo(None),
              any()
            )

            result shouldBe expectedToken
          }
      )
    }

    "should have correct packages token (price confirmed)" in {
      when(mockProductRequest.cartPricingContext).thenReturn(Some(cartPricingContext))
      val input = overrideMockProduct
      Future.fromTry(
        productsFacade
          .createProductToken(input, isNewsletterOptedIn = false, productsRequest = mockProductRequest)
          .map { result =>
            verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
              eqTo(flightCreatedTransform),
              eqTo(propertyCreatedTransform),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(eqTo(setupBookingContext))
            verify(productTokenUtils, times(1)).createMultiProductSetupBookingToken(
              eqTo(propertySetupTransform),
              eqTo(Some(expectedPackagesToken)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
            verify(productTokenUtils, times(1)).createMultiProductsToken(
              eqTo(Some(defaultMultiProductSetupToken)),
              eqTo(Some(defaultMultiProductCreationToken)),
              eqTo(None),
              any()
            )

            result shouldBe expectedToken
          }
      )
    }

    "should have correct protection token (setup only just now)" in {
      when(protectionsMock.head.id).thenReturn("123")
      when(protectionsMock.head.tripProtectionData).thenReturn(protectionDataMock)
      when(protectionDataMock.get.protectionBookingToken).thenReturn(mockToken)
      when(mockProductRequest.cartPricingContext).thenReturn(Some(cartPricingContext))
      val protectionSetupTransform = toTripProtectionModel(protectionMockProduct.protections)

      Future.fromTry(
        productsFacade
          .createProductToken(protectionMockProduct, isNewsletterOptedIn = false, productsRequest = mockProductRequest)
          .map { result =>
            verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
              eqTo(flightCreatedTransform),
              eqTo(propertyCreatedTransform),
              eqTo(protectionSetupTransform),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(eqTo(setupBookingContext))
            verify(productTokenUtils, times(1)).createMultiProductSetupBookingToken(
              eqTo(propertySetupTransform),
              eqTo(Some(expectedPackagesToken)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
            verify(productTokenUtils, times(1)).createMultiProductsToken(
              eqTo(Some(defaultMultiProductSetupToken)),
              eqTo(Some(defaultMultiProductCreationToken)),
              eqTo(None),
              any()
            )

            result shouldBe expectedToken
          }
      )
    }

    "should get bookingToken with payment request info correctly" in {
      val input = overrideMockProduct
      when(mockProductRequest.cartPricingContext).thenReturn(Some(cartPricingContext))
      val expectedPaymentRequestInfo = Some(
        PaymentRequestInfo(
          Some(ChargeOption.PayNow),
          Some(new DateTime("2019-06-03T00:00+07:00")),
          Some(new DateTime("2019-06-04T00:00+07:00"))
        )
      )

      when(
        productTokenUtils.createMultiProductCreationBookingToken(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          eqTo(expectedPaymentRequestInfo),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(eqTo(setupBookingContext))
      ).thenReturn(Try(defaultMultiProductCreationToken))
      when(mockPropertyRequest.payment).thenReturn(Some(ProductPaymentRequest(ChargeOption.PayNow)))
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)

      Future.fromTry(
        productsFacade
          .createProductToken(
            input,
            None,
            None,
            paymentRequest = None,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest,
            selectedChargeOption = Some(ChargeOption.PayNow)
          )
          .map { result =>
            verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
              eqTo(flightCreatedTransform),
              eqTo(propertyCreatedTransform),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(expectedPaymentRequestInfo),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(eqTo(setupBookingContext))
            verify(productTokenUtils, times(1)).createMultiProductSetupBookingToken(
              eqTo(propertySetupTransform),
              eqTo(Some(expectedPackagesToken)),
              eqTo(expectedTimeStamp),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
            verify(productTokenUtils, times(1)).createMultiProductsToken(
              eqTo(Some(defaultMultiProductSetupToken)),
              eqTo(Some(defaultMultiProductCreationToken)),
              eqTo(None),
              any()
            )

            result shouldBe expectedToken
          }
      )
    }

    "should get bookingToken with selectedPlanCode correctly" in {
      val input     = overrideMockProduct
      val planToken = Some("PLANTOKEN")
      val multiProductToken = MultiProductBookingToken(
        setupBookingToken = TokenSerializers[MultiProductSetupBookingToken]
          .serialize(defaultMultiProductSetupToken.copy(installmentPlanToken = planToken), Some(1571200505532L), None)
          .toOption,
        creationBookingToken = TokenSerializers[MultiProductCreationBookingToken]
          .serialize(
            defaultMultiProductCreationToken.copy(installmentPlanToken = planToken),
            Some(1571200505532L),
            None
          )
          .toOption,
        None
      )

      val multiBookingTokenString = TokenSerializers
        .toJsonString(
          TokenSerializers[MultiProductBookingToken].serialize(multiProductToken, Some(1571853316957L), None).get
        )
        .get

      val expectedTokenWithSelectedPlan = TokenMessage(multiBookingTokenString, 1)

      when(mockProductRequest.cartPricingContext).thenReturn(Some(cartPricingContext))
      when(
        productTokenUtils.createMultiProductSetupBookingToken(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(
          any()
        )
      ).thenReturn(Try(defaultMultiProductSetupToken.copy(installmentPlanToken = planToken)))
      when(
        productTokenUtils
          .createMultiProductCreationBookingToken(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
      ).thenReturn(Try(defaultMultiProductCreationToken.copy(installmentPlanToken = planToken)))

      when(productTokenUtils.createMultiProductsToken(any(), any(), any(), any()))
        .thenReturn(Try(expectedTokenWithSelectedPlan))

      Future.fromTry(
        productsFacade
          .createProductToken(
            input,
            None,
            None,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest,
            installmentPlanToken = planToken
          )
          .map { result =>
            verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
              eqTo(flightCreatedTransform),
              eqTo(propertyCreatedTransform),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(planToken),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              productsRequest = any(),
              any(),
              any()
            )(eqTo(setupBookingContext))

            verify(productTokenUtils, times(1)).createMultiProductSetupBookingToken(
              eqTo(propertySetupTransform),
              eqTo(Some(expectedPackagesToken)),
              eqTo(expectedTimeStamp),
              any(),
              any(),
              eqTo(planToken),
              any(),
              any(),
              any()
            )(any())

            verify(productTokenUtils, times(1)).createMultiProductsToken(
              eqTo(Some(defaultMultiProductSetupToken.copy(installmentPlanToken = planToken))),
              eqTo(Some(defaultMultiProductCreationToken.copy(installmentPlanToken = planToken))),
              eqTo(None),
              any()
            )

            result shouldBe expectedTokenWithSelectedPlan
          }
      )
    }

    "should have correct activity token" in {
      when(mockActivityService.toBookingToken(activityMockProduct.activities)(requestContext))
        .thenReturn(Success(Map("mock activity" -> defaultActivityBookingToken)))
      val activitySetupTransform =
        mockActivityService.toBookingToken(activityMockProduct.activities)(requestContext).success.value
      when(mockProductRequest.cartPricingContext).thenReturn(Some(cartPricingContext))

      Future.fromTry(
        productsFacade
          .createProductToken(activityMockProduct, isNewsletterOptedIn = false, productsRequest = mockProductRequest)
          .map { result =>
            verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
              eqTo(Map.empty),
              eqTo(Map.empty),
              eqTo(Map.empty),
              eqTo(null),
              eqTo(activitySetupTransform),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(eqTo(setupBookingContext))
            verify(productTokenUtils, times(1)).createMultiProductSetupBookingToken(
              eqTo(Map.empty),
              eqTo(Some(expectedPackagesToken)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
            verify(productTokenUtils, times(1)).createMultiProductsToken(
              eqTo(Some(defaultMultiProductSetupToken)),
              eqTo(Some(defaultMultiProductCreationToken)),
              eqTo(None),
              any()
            )

            result shouldBe expectedToken
          }
      )
    }

    "should have correct cegFastTrack token" in {
      val cegFastTrackNormalHotelProperty1ChoiceId = "CEGFastTrack#NormalHotelFastTrack#Property_1"
      val confirmationData = CEGFastTrackConfirmationData(
        productTokenKey = cegFastTrackNormalHotelProperty1ChoiceId,
        tier = NormalHotelFastTrack,
        refProductTokenKey = Seq("Property_1"),
        priceBreakdown = AddOnDefaults.defaultAddOnChoiceInternal.priceBreakdown,
        paymentAmount = com.agoda.bapi.common.token.Money(25.00, "THB"),
        paymentAmountUsd = 0.71,
        accountingEntity = None,
        metas = Nil,
        startDateTime = LocalDateTime.parse("2024-06-18T00:00:00"),
        endDateTime = LocalDateTime.parse("2024-07-20T00:00:00")
      )
      when(mockAddOnBookingTokenService.getConfirmationData(Seq(AddOnDefaults.defaultAddOnProductData)))
        .thenReturn(
          Seq(confirmationData)
        )
      when(mockAddOnBookingTokenService.toBookingToken(Seq(confirmationData))(requestContext)).thenReturn(
        Success(Map(cegFastTrackNormalHotelProperty1ChoiceId -> defaultAddOnBookingToken))
      )
      val addOnBookingToken =
        mockAddOnBookingTokenService.toBookingToken(Seq(confirmationData))(requestContext).success.value
      when(mockProductRequest.cartPricingContext).thenReturn(Some(cartPricingContext))

      Future.fromTry(
        productsFacade
          .createProductToken(
            cegFastTrackMockProduct,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest
          )
          .map { result =>
            verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
              eqTo(Map.empty),
              eqTo(Map.empty),
              eqTo(Map.empty),
              eqTo(null),
              eqTo(Map.empty),
              any(),
              eqTo(Map(cegFastTrackNormalHotelProperty1ChoiceId -> defaultAddOnBookingToken)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              productsRequest = any(),
              any(),
              any()
            )(eqTo(setupBookingContext))

            result shouldBe expectedToken
          }
      )
    }

    "should have correct cegFastTrack token with addOnV2" in {

      val mockCEGFastTrackConfirmationDataFromV1 = Seq(mock[CEGFastTrackConfirmationData])
      val mockAddOnProductDatas                  = Seq(mock[AddOnProductData])
      val mockAddOnContentDatas                  = Seq(mock[AddOnContentData])
      val mockAddOnData                          = AddOnData(mockAddOnProductDatas, mockAddOnContentDatas)
      when(mockAddOnBookingTokenService.getConfirmationData(mockAddOnProductDatas)).thenReturn(
        mockCEGFastTrackConfirmationDataFromV1
      )

      val confirmationDataMock = mock[AddOnConfirmationData]
      when(confirmationDataMock.paymentModel).thenReturn(PaymentModel.Unknown)
      when(confirmationDataMock.supplier).thenReturn(None)
      when(confirmationDataMock.metas).thenReturn(Nil)
      val mockAddOnConfirmationData          = Some(confirmationDataMock)
      val mockCEGFastTrackConfirmationDataV2 = Some(mock[CEGFastTrackConfirmationData])

      when(mockAddOnBookingTokenService.mapConfirmationData(mockAddOnConfirmationData)).thenReturn(
        mockCEGFastTrackConfirmationDataV2
      )

      val mockAddOnDataV2 = mock[AddOnDataV2]
      when(mockAddOnDataV2.confirmationData).thenReturn(mockAddOnConfirmationData)
      when(mockAddOnDataV2.productType).thenReturn(ProductType.CEGFastTrack)
      when(mockAddOnDataV2.productRefIds).thenReturn(Seq("Property_1"))

      val mockToken = mock[AddOnBookingModel]
      when(
        mockAddOnBookingTokenService.toBookingToken(
          mockCEGFastTrackConfirmationDataFromV1 ++ mockCEGFastTrackConfirmationDataV2
        )(requestContext)
      ).thenReturn(
        Try(mockToken)
      )

      when(mockProductRequest.cartPricingContext).thenReturn(Some(cartPricingContext))

      val cegFastTrackMockProduct: ProductData =
        overrideMockProduct.copy(
          properties = Seq.empty,
          flights = Seq.empty,
          addOnData = mockAddOnData,
          addOnDataV2 = Seq(mockAddOnDataV2)
        )

      Future.fromTry(
        productsFacade
          .createProductToken(
            cegFastTrackMockProduct,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest
          )
          .map { result =>
            verify(mockAddOnBookingTokenService, times(1))
              .toBookingToken(mockCEGFastTrackConfirmationDataFromV1 ++ mockCEGFastTrackConfirmationDataV2)(
                requestContext
              )
            verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
              eqTo(Map.empty),
              eqTo(Map.empty),
              eqTo(Map.empty),
              eqTo(null),
              eqTo(Map.empty),
              any(),
              eqTo(mockToken),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              productsRequest = any(),
              any(),
              any()
            )(eqTo(setupBookingContext))
            succeed
          }
      )
    }

    "should have correct cegFastTrack token with addOnV2 when addOnV2 exist" in {

      when(featureAware.CegFastTrackMigration).thenReturn(true)
      val productsRequest  = mockSetupBookingRequest.productsRequest
      val confirmationData = MockAddOnV2Data.mockConfirmationData()
      val genericAddons = Seq(
        AddOnDataV2(
          id = "testProtection",
          mainProductId = "main-product-id",
          productType = ProductType.CEGFastTrack,
          productRefIds = Seq("Property1"),
          selectedOptionIds = Seq("1"),
          choices = Some(
            Seq(AddOnChoiceDataV2("choice-1", PriceContent(102.04, "SGD"), None, Seq("badge"), isDefaultChoice = true))
          ),
          content = None,
          confirmationData = Some(confirmationData),
          displayContent = None,
          token = None
        )
      )
      val mockToken = mock[AddOnBookingModel]
      when(mockAddOnBookingTokenService.toBookingToken(any())(any())).thenReturn(Try(mockToken))
      val productWithAddons: ProductData =
        overrideMockProduct.copy(flights = Seq.empty, addOnDataV2 = genericAddons)

      Future.fromTry(
        productsFacade
          .createProductToken(
            productWithAddons,
            isNewsletterOptedIn = false,
            productsRequest = productsRequest
          )
          .map { result =>
            verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
              eqTo(Map.empty),
              any(),
              eqTo(Map.empty),
              eqTo(null),
              eqTo(Map.empty),
              any(),
              eqTo(Map.empty),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              argThat[GenericAddOnBookingModel](a =>
                (a.head._1, a.head._2) match {
                  case (key, token) =>
                    val addOn = genericAddons.head
                    (a.size == 1
                    && key == confirmationData.productTokenKey
                    && token.productTokenKey == confirmationData.productTokenKey
                    && token.characteristics.productTypeId == addOn.productType.id
                    // specific property mapping is in mapper test, here we
                    // just check if the token was added to creation part of booking token
                    )
                }
              ),
              any(),
              any(),
              any(),
              any()
            )(eqTo(setupBookingContext))
            succeed
          }
      )
    }

    "should have correct generic add on token" in {
      val productsRequest  = mockSetupBookingRequest.productsRequest
      val confirmationData = MockAddOnV2Data.mockConfirmationData()
      val genericAddons = Seq(
        AddOnDataV2(
          id = "testProtection",
          mainProductId = "main-product-id",
          productType = ProductType.TripProtection,
          productRefIds = Seq("Flight1"),
          selectedOptionIds = Seq("1"),
          choices = Some(
            Seq(AddOnChoiceDataV2("choice-1", PriceContent(102.04, "SGD"), None, Seq("badge"), isDefaultChoice = true))
          ),
          content = None,
          confirmationData = Some(confirmationData),
          displayContent = None,
          token = None
        )
      )
      val productWithAddons: ProductData =
        overrideMockProduct.copy(properties = Seq.empty, flights = Seq.empty, addOnDataV2 = genericAddons)

      Future.fromTry(
        productsFacade
          .createProductToken(
            productWithAddons,
            isNewsletterOptedIn = false,
            productsRequest = productsRequest
          )
          .map { result =>
            verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
              eqTo(Map.empty),
              eqTo(Map.empty),
              eqTo(Map.empty),
              eqTo(null),
              eqTo(Map.empty),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              argThat[GenericAddOnBookingModel](a =>
                (a.head._1, a.head._2) match {
                  case (key, token) =>
                    val addOn = genericAddons.head
                    (a.size == 1
                    && key == confirmationData.productTokenKey
                    && token.productTokenKey == confirmationData.productTokenKey
                    && token.characteristics.productTypeId == addOn.productType.id
                    // specific property mapping is in mapper test, here we
                    // just check if the token was added to creation part of booking token
                    )
                }
              ),
              any(),
              productsRequest = any(),
              any(),
              any()
            )(eqTo(setupBookingContext))
            succeed
          }
      )
    }

    "should have flight ceg upsell in cegFastTrack token when UNIBF-1068 = A" in {
      when(featureAware.isCEGUpsellAddOnV2Enabled).thenReturn(false)
      val productsRequest  = mockSetupBookingRequest.productsRequest
      val confirmationData = MockAddOnV2Data.mockConfirmationData()
      val genericAddons = Seq(
        AddOnDataV2(
          id = "testProtection",
          mainProductId = "main-product-id",
          productType = ProductType.CEGFastTrack,
          productRefIds = Seq("Flight1"),
          selectedOptionIds = Seq("1"),
          choices = Some(
            Seq(AddOnChoiceDataV2("choice-1", PriceContent(102.04, "SGD"), None, Seq("badge"), isDefaultChoice = true))
          ),
          content = None,
          confirmationData = Some(confirmationData),
          displayContent = None,
          token = None
        )
      )

      val mockToken = mock[AddOnBookingModel]
      when(mockAddOnBookingTokenService.toBookingToken(any())(any())).thenReturn(Try(mockToken))
      val productWithAddons: ProductData =
        overrideMockProduct.copy(
          properties = Seq.empty,
          flights = flightsDataWithLoyalty(""),
          addOnDataV2 = genericAddons
        )

      Future.fromTry(
        productsFacade
          .createProductToken(
            productWithAddons,
            isNewsletterOptedIn = false,
            productsRequest = productsRequest
          )
          .map { result =>
            verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
              any(),
              eqTo(Map.empty),
              eqTo(Map.empty),
              eqTo(null),
              eqTo(Map.empty),
              any(),
              eqTo(mockToken),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Map.empty),
              any(),
              any(),
              any(),
              any()
            )(eqTo(setupBookingContext))
            succeed
          }
      )
    }

    "should have flight ceg upsell in generic add on token when UNIBF-1068 = B" in {
      when(featureAware.isCEGUpsellAddOnV2Enabled).thenReturn(true)
      val productsRequest  = mockSetupBookingRequest.productsRequest
      val confirmationData = MockAddOnV2Data.mockConfirmationData()
      val genericAddons = Seq(
        AddOnDataV2(
          id = "testProtection",
          mainProductId = "main-product-id",
          productType = ProductType.CEGFastTrack,
          productRefIds = Seq("Flight1"),
          selectedOptionIds = Seq("1"),
          choices = Some(
            Seq(AddOnChoiceDataV2("choice-1", PriceContent(102.04, "SGD"), None, Seq("badge"), isDefaultChoice = true))
          ),
          content = None,
          confirmationData = Some(confirmationData),
          displayContent = None,
          token = None
        )
      )
      val mockToken = mock[AddOnBookingModel]
      when(mockAddOnBookingTokenService.toBookingToken(any())(any())).thenReturn(Try(mockToken))
      val productWithAddons: ProductData =
        overrideMockProduct.copy(
          properties = Seq.empty,
          flights = flightsDataWithLoyalty(""),
          addOnDataV2 = genericAddons
        )

      Future.fromTry(
        productsFacade
          .createProductToken(
            productWithAddons,
            isNewsletterOptedIn = false,
            productsRequest = productsRequest
          )
          .map { result =>
            verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
              any(),
              eqTo(Map.empty),
              eqTo(Map.empty),
              eqTo(null),
              eqTo(Map.empty),
              any(),
              eqTo(Map.empty),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              argThat[GenericAddOnBookingModel](a =>
                (a.head._1, a.head._2) match {
                  case (key, token) =>
                    val addOn = genericAddons.head
                    (a.size == 1
                    && key == confirmationData.productTokenKey
                    && token.productTokenKey == confirmationData.productTokenKey
                    && token.characteristics.productTypeId == addOn.productType.id
                    // specific property mapping is in mapper test, here we
                    // just check if the token was added to creation part of booking token
                    )
                }
              ),
              any(),
              any(),
              any(),
              any()
            )(eqTo(setupBookingContext))
            succeed
          }
      )
    }

    "append Payment logs" in {
      val input      = overrideMockProduct
      val logContext = LogContext()
      when(setupBookingContext.logContext).thenReturn(logContext)
      when(setupBookingContext.correlationId).thenReturn("111-555")
      Future.fromTry(
        productsFacade
          .createProductToken(input, None, None, isNewsletterOptedIn = false, productsRequest = mockProductRequest)(
            setupBookingContext
          )
          .map { _ =>
            val paymentLog = logContext.getPaymentLog(setupBookingContext.correlationId)
            paymentLog.isDefined shouldBe true
            paymentLog.get.totalChargeAmount.get should be > .0
            paymentLog.get.totalChargeAmountCurrencyCode shouldBe Some(thbCurrency)
          }
      )
    }

    "append Product logs" in {
      val input      = overrideMockProduct
      val logContext = LogContext()
      when(setupBookingContext.logContext).thenReturn(logContext)
      when(setupBookingContext.correlationId).thenReturn("111-555")
      when(setupBookingContext.requestContext.getCorrelationId()).thenReturn("111-555")
      when(mockProductRequest.propertyRequests.head.id).thenReturn("1")
      Future.fromTry(
        productsFacade
          .createProductToken(input, None, None, isNewsletterOptedIn = false, productsRequest = mockProductRequest)(
            setupBookingContext
          )
          .map { _ =>
            val productLog = logContext
              .getProductLog[PropertyContentLog](
                setupBookingContext.correlationId,
                mockProductRequest.propertyRequests.head.id
              )
            productLog.isDefined shouldBe true
            productLog.get.pricingSearchId shouldBe input.properties.head.papiProperties.head.dfMetaResult.propertyToken.get.pricingRequestId
            productLog.get.isNha shouldBe Some(true)
          }
      )
    }

    "return isPartialSuccessAllowed as false if enabledFeatures doesn't contain PartialSuccess" in {
      val input           = overrideMockProduct
      val enabledFeatures = Some(Seq.empty)
      Future
        .fromTry(
          productsFacade.createProductToken(
            input,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest,
            enabledFeatures = enabledFeatures
          )(setupBookingContext)
        )
        .map { _ =>
          verify(
            productTokenUtils,
            times(1)
          ).createMultiProductCreationBookingToken(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(false),
            any(),
            any(),
            any(),
            any(),
            any(),
            productsRequest = any(),
            any(),
            any()
          )(eqTo(setupBookingContext))
          succeed
        }
    }

    "return isPartialSuccessAllowed as true if enabledFeatures contain PartialSuccess" in {
      val input           = overrideMockProduct
      val enabledFeatures = Some(Seq("CrossOutPrice", "PartialSuccess"))
      when(whiteLabelInfo.feature).thenReturn(featuresConfiguration(isPartialSuccessAllowed = Some(true)))
      Future
        .fromTry(
          productsFacade.createProductToken(
            input,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest,
            enabledFeatures = enabledFeatures
          )(setupBookingContext)
        )
        .map { _ =>
          verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(true),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
          succeed
        }
    }

    "return isPartialSuccessAllowed as true if flight is hackerfare" in {
      val hackerFareFlight: FlightConfirmationData = flightsDataProduct.head.copy(isHackerFare = true)
      val input                                    = overrideMockProduct.copy(flights = Seq(hackerFareFlight))
      when(whiteLabelInfo.feature).thenReturn(featuresConfiguration(isPartialSuccessAllowed = Some(true)))

      Future
        .fromTry(
          productsFacade.createProductToken(
            input,
            None,
            None,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest
          )(setupBookingContext)
        )
        .map { _ =>
          verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            isPartialSuccessAllowed = eqTo(true),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
          succeed
        }
    }

    "return isPartialSuccessAllowed as false if flight is hackerfare with addon Protection" in {
      val hackerFareFlight: FlightConfirmationData = flightsDataProduct.head.copy(isHackerFare = true)
      val input = overrideMockProduct.copy(
        flights = Seq(hackerFareFlight),
        addOnDataV2 =
          Seq(MockAddOnV2Data.sampleAddOn(productType = ProductType.TripProtection, selectedOptionIds = Seq("1")))
      )

      Future
        .fromTry(
          productsFacade.createProductToken(
            input,
            None,
            None,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest
          )(setupBookingContext)
        )
        .map { _ =>
          verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            isPartialSuccessAllowed = eqTo(false),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
          succeed
        }
    }

    "return isPartialSuccessAllowed as true if flight is hackerfare with protection" in {
      when(whiteLabelInfo.feature).thenReturn(featuresConfiguration(isPartialSuccessAllowed = Some(true)))
      val hackerFareFlight: FlightConfirmationData = flightsDataProduct.head.copy(isHackerFare = true)
      val tripProtection: TripProtectionProductItem =
        getProtectionItem(optInValue = ProtectionRequestItemOptInValue.Purchase, token = "")
      val input = overrideMockProduct.copy(
        properties = Seq.empty,
        flights = Seq(hackerFareFlight),
        protections = Seq(tripProtection)
      )

      Future
        .fromTry(
          productsFacade.createProductToken(
            input,
            None,
            None,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest
          )(setupBookingContext)
        )
        .map { _ =>
          verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            isPartialSuccessAllowed = eqTo(true),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
          succeed
        }
    }

    "return isPartialSuccessAllowed as false for package with non-hackerfare flight" in {
      val hackerFareFlight: FlightConfirmationData = flightsDataProduct.head.copy(isHackerFare = false)
      val tripProtection: TripProtectionProductItem =
        getProtectionItem(optInValue = ProtectionRequestItemOptInValue.Purchase, token = "")
      val input = overrideMockProduct.copy(flights = Seq(hackerFareFlight), protections = Seq(tripProtection))

      Future
        .fromTry(
          productsFacade.createProductToken(
            input,
            None,
            None,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest
          )(setupBookingContext)
        )
        .map { _ =>
          verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            isPartialSuccessAllowed = eqTo(false),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
          succeed
        }
    }

    "return isPartialSuccessAllowed as true even if enabledFeatures contains PartialSuccess (case-insensitive)" in {
      val input = overrideMockProduct
      when(mockProductRequest.cartContext).thenReturn(None)
      when(whiteLabelInfo.feature).thenReturn(featuresConfiguration(isPartialSuccessAllowed = Some(true)))
      val enabledFeatures = Some(Seq("CrossOutPrice", "partialsuccess"))
      Future
        .fromTry(
          productsFacade.createProductToken(
            input,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest,
            enabledFeatures = enabledFeatures
          )(setupBookingContext)
        )
        .map { _ =>
          verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(true),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(eqTo(setupBookingContext))
          succeed
        }
    }

    "return userTaxCountry and userTaxCountryId" in {
      val input = overrideMockProduct
      when(mockProductRequest.cartContext).thenReturn(None)

      Future
        .fromTry(
          productsFacade.createProductToken(
            input,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest,
            userTaxCountry = Some(
              UserTaxCountry(
                userTaxCountryId = Some(111),
                bookerResidenceCountryId = Some(112),
                paymentInstrumentCountryId = Some(113),
                ipAddressCountryId = Some(114)
              )
            )
          )(setupBookingContext)
        )
        .map { _ =>
          verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(false),
            any(),
            userTaxCountry = eqTo(
              Some(
                UserTaxCountry(
                  userTaxCountryId = Some(111),
                  bookerResidenceCountryId = Some(112),
                  paymentInstrumentCountryId = Some(113),
                  ipAddressCountryId = Some(114)
                )
              )
            ),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(eqTo(setupBookingContext))
          succeed
        }
    }

    "return aabInfo" in {
      val input = overrideMockProduct
      when(mockProductRequest.cartContext).thenReturn(None)

      Future
        .fromTry(
          productsFacade.createProductToken(
            input,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest,
            aabInfo = Some(
              AabInfo(
                flight = Some(FlightAabInfo(facilitationFeeWaiverReasonId = Some(3)))
              )
            )
          )(setupBookingContext)
        )
        .map { _ =>
          verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(false),
            any(),
            any(),
            aabInfo = eqTo(
              Some(
                TokenAabInfo(
                  flight = Some(TokenFlightAabInfo(facilitationFeeWaiverReasonId = Some(3)))
                )
              )
            ),
            any(),
            any(),
            any(),
            any(),
            any()
          )(eqTo(setupBookingContext))
          succeed
        }
    }

    "return external loyalty info" in {
      val input = overrideMockProduct
      when(mockProductRequest.cartContext).thenReturn(None)

      val loyaltyRequestOpt = Some(
        LoyaltyRequest(
          pointsOfferType = Some(PointsOfferType.ALLPOINTS)
        )
      )
      val programs = SubLoyaltyPrograms(
        minimumPointsToRedeem = Some(1500.0),
        pointsBalance = Some(5000)
      )
      val profile = ExternalLoyaltyUserProfileResponse(
        subLoyaltyPrograms = Seq(programs)
      )
      val loyaltyProfile = LoyaltyProfile(
        loyaltyProfile = None,
        memberBalance = None,
        achievements = None,
        cashBackBalance = None,
        externalLoyaltyProfileInfo = Some(profile)
      )
      val loyaltyProfileOpt = Some(loyaltyProfile)
      Future
        .fromTry(
          productsFacade.createProductToken(
            input,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest,
            aabInfo = None,
            loyaltyRequest = loyaltyRequestOpt,
            loyaltyProfile = loyaltyProfileOpt
          )(setupBookingContext)
        )
        .map { _ =>
          val expectedExternalLoyaltyReqOpt = Some(
            ExternalLoyaltyRequestInfo(
              pointsOfferType = Some("ALL_POINTS"),
              minimumPointsToRedeem = Some(1500)
            )
          )
          verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(false),
            any(),
            any(),
            any(),
            any(),
            eqTo(expectedExternalLoyaltyReqOpt),
            any(),
            any(),
            any()
          )(eqTo(setupBookingContext))
          succeed
        }
    }

    "return external loyalty info only with PointsOfferType" in {
      val input = overrideMockProduct
      when(mockProductRequest.cartContext).thenReturn(None)

      val loyaltyRequestOpt = Some(
        LoyaltyRequest(
          pointsOfferType = Some(PointsOfferType.ALLPOINTS)
        )
      )
      val programs = SubLoyaltyPrograms(
        minimumPointsToRedeem = None,
        pointsBalance = Some(5000)
      )
      val profile = ExternalLoyaltyUserProfileResponse(
        subLoyaltyPrograms = Seq(programs)
      )
      val loyaltyProfile = LoyaltyProfile(
        loyaltyProfile = None,
        memberBalance = None,
        achievements = None,
        cashBackBalance = None,
        externalLoyaltyProfileInfo = Some(profile)
      )
      val loyaltyProfileOpt = Some(loyaltyProfile)
      Future
        .fromTry(
          productsFacade.createProductToken(
            input,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest,
            aabInfo = None,
            loyaltyRequest = loyaltyRequestOpt,
            loyaltyProfile = loyaltyProfileOpt
          )(setupBookingContext)
        )
        .map { _ =>
          val expectedExternalLoyaltyReqOpt = Some(
            ExternalLoyaltyRequestInfo(
              pointsOfferType = Some("ALL_POINTS"),
              minimumPointsToRedeem = None
            )
          )
          verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(false),
            any(),
            any(),
            any(),
            any(),
            eqTo(expectedExternalLoyaltyReqOpt),
            any(),
            any(),
            any()
          )(eqTo(setupBookingContext))
          succeed
        }
    }

    "return external loyalty info only with Min points to be redeemed" in {
      val input = overrideMockProduct
      when(mockProductRequest.cartContext).thenReturn(None)

      val loyaltyRequestOpt = Some(
        LoyaltyRequest(
          pointsOfferType = None
        )
      )
      val programs = SubLoyaltyPrograms(
        minimumPointsToRedeem = Some(1500),
        pointsBalance = Some(5000)
      )
      val profile = ExternalLoyaltyUserProfileResponse(
        subLoyaltyPrograms = Seq(programs)
      )
      val loyaltyProfile = LoyaltyProfile(
        loyaltyProfile = None,
        memberBalance = None,
        achievements = None,
        cashBackBalance = None,
        externalLoyaltyProfileInfo = Some(profile)
      )
      val loyaltyProfileOpt = Some(loyaltyProfile)
      Future
        .fromTry(
          productsFacade.createProductToken(
            input,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest,
            aabInfo = None,
            loyaltyRequest = loyaltyRequestOpt,
            loyaltyProfile = loyaltyProfileOpt
          )(setupBookingContext)
        )
        .map { _ =>
          val expectedExternalLoyaltyReqOpt = Some(
            ExternalLoyaltyRequestInfo(
              pointsOfferType = None,
              minimumPointsToRedeem = Some(1500)
            )
          )
          verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(false),
            any(),
            any(),
            any(),
            any(),
            eqTo(expectedExternalLoyaltyReqOpt),
            any(),
            any(),
            any()
          )(eqTo(setupBookingContext))
          succeed
        }
    }

    "not return external loyalty info when both min points to be redeemed and points offer type are empty" in {
      val input = overrideMockProduct
      when(mockProductRequest.cartContext).thenReturn(None)

      val loyaltyRequestOpt = Some(
        LoyaltyRequest(
          pointsOfferType = None
        )
      )
      val programs = SubLoyaltyPrograms(
        minimumPointsToRedeem = None,
        pointsBalance = Some(5000)
      )
      val profile = ExternalLoyaltyUserProfileResponse(
        subLoyaltyPrograms = Seq(programs)
      )
      val loyaltyProfile = LoyaltyProfile(
        loyaltyProfile = None,
        memberBalance = None,
        achievements = None,
        cashBackBalance = None,
        externalLoyaltyProfileInfo = Some(profile)
      )
      val loyaltyProfileOpt = Some(loyaltyProfile)
      Future
        .fromTry(
          productsFacade.createProductToken(
            input,
            isNewsletterOptedIn = false,
            productsRequest = mockProductRequest,
            loyaltyRequest = loyaltyRequestOpt,
            loyaltyProfile = loyaltyProfileOpt
          )(setupBookingContext)
        )
        .map { _ =>
          val expectedExternalLoyaltyReqOpt = None
          verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(false),
            any(),
            any(),
            any(),
            any(),
            eqTo(expectedExternalLoyaltyReqOpt),
            any(),
            any(),
            any()
          )(eqTo(setupBookingContext))
          succeed
        }
    }

    "return itineraryContext" in {
      val input = overrideMockProduct

      for {
        _ <- Future.fromTry(
               productsFacade.createProductToken(
                 input,
                 isNewsletterOptedIn = false,
                 productsRequest = mockProductRequest,
                 itineraryContext = Some(defaultItineraryContext)
               )(setupBookingContext)
             )
      } yield {
        verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          eqTo(false),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          eqTo(Some(defaultItineraryContext))
        )(eqTo(setupBookingContext))
        succeed
      }
    }
  }

  "getProductItems" should {
    forEvery(supportPriceDisplayTypesTestCases) { isUNIBF2454 =>
      {
        s"single flight flow work correctly when UNIBF-2454 is $isUNIBF2454" in {
          val mockPricingTree            = mock[PriceBreakdownNode]
          val mockFlightConfirmationData = mock[FlightConfirmationData]
          when(featureAware.supportPriceDisplayTypes).thenReturn(isUNIBF2454)
          when(mockFlightConfirmationData.flightPricing)
            .thenReturn(Some(MockFlightsPricingData.searchResponseCurrencyPricingUSD))
          when(mockFlightConfirmationData.priceChange).thenReturn(None)
          when(mockFlightConfirmationData.campaignInfo).thenReturn(Some(MockFlightsPricingData.promotionInfoResponse))
          when(
            flightRepository
              .retrieveFlightConfirmationData(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(any())
          ).thenReturn(Future.successful(mockFlightConfirmationData))
          when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleFlight)
          when(
            mockPriceBreakdownService.getPriceBreakdownForFlight(
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )
          )
            .thenReturn(Future.successful(Option(mockPricingTree)))
          when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
            .thenReturn(Future.successful(ccCampaignMock))

          productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
            verify(flightRepository, times(1))
              .retrieveFlightConfirmationData(
                any(),
                any(),
                eqTo(chargeCurrency),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(any())

            result.packageToken shouldBe None
            result.flights should have size 1
            result.totalPriceDisplay.isDefined shouldBe true
            result.totalPriceDisplay.get shouldBe mockPricingTree
            if (isUNIBF2454)
              result.priceDisplayType shouldBe Some(PriceDisplayType.SingleFlight)
            else
              result.priceDisplayType shouldBe None
            result.protections shouldBe Seq.empty
            result.totalSavings.get.totalSavingsAmount.amount shouldBe MockFlightsPricingData.totalDiscount
            result.totalSavings.get.totalSavingsAmount.currencyCode shouldBe chargeCurrency
            result.totalSavings.get.totalSavingsPercent shouldBe 6
            result.isPromotionCodeEligible shouldBe true
          }
        }
      }
    }

    "single flight with selected payment request flow work correctly" in {
      val mockPricingTree            = mock[PriceBreakdownNode]
      val mockFlightConfirmationData = mock[FlightConfirmationData]
      when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
      when(paymentRequest.selectedPaymentMethod).thenReturn(Some(MPBPaymentMethod.Visa.value))
      when(paymentRequest.ccId).thenReturn(None)
      when(mockFlightConfirmationData.flightPricing)
        .thenReturn(Some(MockFlightsPricingData.searchResponseCurrencyPricingUSD))
      when(mockFlightConfirmationData.priceChange).thenReturn(None)
      when(mockFlightConfirmationData.campaignInfo).thenReturn(None)
      when(
        flightRepository
          .retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
      ).thenReturn(Future.successful(mockFlightConfirmationData))
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleFlight)
      when(
        mockPriceBreakdownService.getPriceBreakdownForFlight(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )
      )
        .thenReturn(Future.successful(Option(mockPricingTree)))
      when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
        .thenReturn(Future.successful(ccCampaignMock))

      productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
        verify(flightRepository, times(1))
          .retrieveFlightConfirmationData(
            any(),
            any(),
            eqTo(chargeCurrency),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(Some(MPBPaymentMethod.Visa.value)),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())

        result.packageToken shouldBe None
        result.flights should have size 1
        result.totalPriceDisplay.isDefined shouldBe true
        result.totalPriceDisplay.get shouldBe mockPricingTree
        result.protections shouldBe Seq.empty
        result.totalSavings.get.totalSavingsAmount.amount shouldBe MockFlightsPricingData.totalDiscount
        result.totalSavings.get.totalSavingsAmount.currencyCode shouldBe chargeCurrency
        result.totalSavings.get.totalSavingsPercent shouldBe 6
      }
    }

    "single flight with selected payment request flow work correctly without saving data" in {

      val mockPricingTree            = mock[PriceBreakdownNode]
      val mockFlightConfirmationData = mock[FlightConfirmationData]
      when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
      when(paymentRequest.selectedPaymentMethod).thenReturn(Some(MPBPaymentMethod.Visa.value))
      when(paymentRequest.ccId).thenReturn(None)
      when(mockFlightConfirmationData.flightPricing)
        .thenReturn(Some(MockFlightsPricingData.searchResponseCurrencyPricingUSD_NoDiscount))
      when(mockFlightConfirmationData.priceChange).thenReturn(None)
      when(mockFlightConfirmationData.campaignInfo).thenReturn(None)
      when(
        flightRepository
          .retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
      ).thenReturn(Future.successful(mockFlightConfirmationData))
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleFlight)
      when(
        mockPriceBreakdownService.getPriceBreakdownForFlight(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )
      )
        .thenReturn(Future.successful(Option(mockPricingTree)))
      when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
        .thenReturn(Future.successful(ccCampaignMock))

      productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
        verify(flightRepository, times(1))
          .retrieveFlightConfirmationData(
            any(),
            any(),
            eqTo(chargeCurrency),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(Some(MPBPaymentMethod.Visa.value)),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())

        result.packageToken shouldBe None
        result.flights should have size 1
        result.totalPriceDisplay.isDefined shouldBe true
        result.totalPriceDisplay.get shouldBe mockPricingTree
        result.protections shouldBe Seq.empty
        result.totalSavings shouldBe None
      }
    }

    forEvery(supportPriceDisplayTypesTestCases) { isUNIBF2454 =>
      {
        s"mixandsave flow work correctly when UNIBF-2454 is $isUNIBF2454" in {
          val propertySearchCriteria1 = mock[PropertySearchCriteria]
          val durationRequest1        = DurationRequest(LocalDate.now(), 2)
          when(featureAware.supportPriceDisplayTypes).thenReturn(isUNIBF2454)
          when(propertySearchCriteria1.propertyId).thenReturn(Some(defaultPropertyId))
          when(propertySearchCriteria1.durationRequest).thenReturn(durationRequest1)
          when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria1)

          val product2                = mock[PropertyRequestItem]
          val propertySearchCriteria2 = mock[PropertySearchCriteria]
          val durationRequest2        = DurationRequest(LocalDate.now().plusDays(2), 2)
          when(propertySearchCriteria2.durationRequest).thenReturn(durationRequest2)
          when(product2.propertySearchCriteria).thenReturn(propertySearchCriteria2)

          when(setupBookingContext.requestContext).thenReturn(requestContext)
          when(requestContext.featureAware).thenReturn(Some(featureAware))

          val guest = mock[HotelGuest]
          when(guest.primary).thenReturn(true)
          when(guest.citizenshipId).thenReturn(Some(""))
          when(mockPropertyRequest.guests).thenReturn(None)
          when(product2.guests).thenReturn(None)

          val pricingRequest1 = mock[PricingRequest]
          when(propertySearchCriteria1.pricingRequest).thenReturn(Some(pricingRequest1))
          when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
          val pricingRequest2 = mock[PricingRequest]
          when(propertySearchCriteria2.pricingRequest).thenReturn(Some(pricingRequest2))
          when(pricingRequest2.dfFeatureFlags).thenReturn(Seq.empty)
          when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, product2))
          when(
            propertyService
              .retrievePropertyBundles(any(), any(), any(), any(), any(), any(), any())(
                any()
              )
          ).thenReturn(Future.successful(Some(mockPropertyData)))
          when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.MixAndSave)
          when(setupBookingContext.requestContext.displayCurrency()).thenReturn(CurrencyCode.USD)
          when(roomBundle.saveAmount).thenReturn(Option(displayBasis))
          when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(totalSavings))
          when(mockMixAndSaveService.generateBookingData(any(), any())).thenReturn(Seq(papiProperties, papiProperties))
          when(mockMixAndSaveService.toBookingPropertyData(any())(any()))
            .thenReturn(Seq(bookingPropertyProduct, bookingPropertyProduct))

          val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
          when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
          productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
            verify(propertyService, times(1))
              .retrievePropertyBundles(
                any(),
                any(),
                eqTo(chargeCurrency),
                any(),
                any(),
                any(),
                eqTo(Some(paymentRequest))
              )(any())
            result.properties should have size 2
            result.totalPriceDisplay.isDefined shouldBe true
            if (isUNIBF2454)
              result.priceDisplayType shouldBe Some(PriceDisplayType.Unknown)
            else
              result.priceDisplayType shouldBe None
            result.protections shouldBe Seq.empty
            result.priceConfirmed shouldBe true
          }
        }
      }
    }

    "mixandsave flow when no bundles are returned" in {
      when(featureAware.supportPriceDisplayTypes).thenReturn(true)
      when(productsRequest.propertyRequests).thenReturn(Seq.empty)
      when(
        propertyService
          .retrievePropertyBundles(eqTo(productsRequest.propertyRequests), any(), any(), any(), any(), any(), any())(
            any()
          )
      ).thenReturn(Future.successful(None))
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.MixAndSave)
      when(setupBookingContext.requestContext.displayCurrency()).thenReturn(CurrencyCode.USD)
      when(roomBundle.saveAmount).thenReturn(None)
      when(mockMixAndSaveService.generateBookingData(any(), any())).thenReturn(Seq.empty)
      when(mockMixAndSaveService.toBookingPropertyData(any())(any())).thenReturn(Seq.empty)

      productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
        result.properties should have size 0
        result.totalPriceDisplay.isDefined shouldBe true
        result.priceDisplayType shouldBe Some(PriceDisplayType.Unknown)
        result.protections shouldBe Seq.empty
        result.priceConfirmed shouldBe false
      }
    }

    "single flight return price change when flapi notify that price also changes" in {
      val flightProductItem =
        FlightConfirmationData(
          "",
          "",
          mock[FlightToken],
          true,
          true,
          true,
          false,
          None,
          Some(MockFlightsPricingData.searchResponseCurrencyPricing),
          Some(MockFlightsPricingData.priceChangeResponse),
          None,
          Map.empty
        )
      val mockPricingTree = mock[PriceBreakdownNode]
      val expectedDelta = BigDecimal(
        MockFlightsPricingData
          .searchResponseCurrencyPricing(hkdCurrency)
          .display
          .perBook
          .allInclusive
      ) - BigDecimal(MockFlightsPricingData.priceChangeResponse.oldPrice.perBook.allInclusive)
      when(
        flightRepository
          .retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
      ).thenReturn(Future.successful(flightProductItem))
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleFlight)
      when(
        mockPriceBreakdownService.getPriceBreakdownForFlight(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )
      )
        .thenReturn(Future.successful(Option(mockPricingTree)))
      when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
        .thenReturn(Future.successful(ccCampaignMock))
      val chargeCurrency: CurrencyCode = hkdCurrency
      productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
        verify(flightRepository, times(1))
          .retrieveFlightConfirmationData(
            any(),
            any(),
            eqTo(chargeCurrency),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())

        result.totalPriceDisplay.isDefined shouldBe true
        result.totalPriceDisplay.get shouldBe mockPricingTree
        result.priceChange.isDefined shouldBe true
        result.priceChange.get shouldBe PriceChange(
          previousPrice = TMoney(
            MockFlightsPricingData.priceChangeResponse.oldPrice.perBook.allInclusive,
            MockFlightsPricingData.Currency
          ),
          beforeDiscountPrice = TMoney(
            MockFlightsPricingData.searchResponseCurrencyPricing.values.head.display.perBook.allInclusive,
            MockFlightsPricingData.Currency
          ),
          delta = TMoney(expectedDelta, MockFlightsPricingData.Currency)
        )
      }
    }

    "should use correct package token for first flight request and return correct PackagesToken" in {
      val packageTokenAfterFlightCall = Some(expectedPackagesToken.copy(clientToken = "ret"))
      val flightProductItem =
        FlightConfirmationData(
          "",
          "",
          mock[FlightToken],
          true,
          true,
          true,
          false,
          packageTokenAfterFlightCall,
          Some(MockFlightsPricingData.searchResponseCurrencyPricing),
          Some(MockFlightsPricingData.priceChangeResponse),
          None,
          Map.empty
        )
      when(
        flightRepository
          .retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
      ).thenReturn(Future.successful(flightProductItem))
      when(
        flightRepository.retrieveFlightItineraryDetails(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
      ).thenReturn(Future.successful(flightProductItem))
      when(
        mockPriceBreakdownService.getPriceBreakdownFromPackagingPrice(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
      ).thenReturn(Future.successful(priceBreakdown))

      val packagingToken = PackagingToken(expectedPackagesToken.clientToken, expectedPackagesToken.interSystemToken)
      productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
        verify(flightRepository, times(1)).retrieveFlightConfirmationData(
          any(),
          any(),
          eqTo(chargeCurrency),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          eqTo(Some(packagingToken)),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())

        verify(propertyService, times(2))
          .retrieveProperties(
            any(),
            any(),
            any(),
            eqTo(packageTokenAfterFlightCall),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())

        result.packageToken shouldBe packageTokenAfterPropertyCall
      }
    }

    "should use correct package token for first flight request (price not confirmed)" in {
      val flightProductItem = FlightConfirmationData(
        "",
        "",
        mock[FlightToken],
        false,
        false,
        false,
        false,
        Some(expectedPackagesToken.copy(clientToken = "ret")),
        None,
        None,
        None,
        Map.empty
      )
      when(
        flightRepository
          .retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
      ).thenReturn(Future.successful(flightProductItem))
      when(
        flightRepository.retrieveFlightItineraryDetails(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
      ).thenReturn(Future.successful(flightProductItem))

      val packagingToken = PackagingToken(expectedPackagesToken.clientToken, expectedPackagesToken.interSystemToken)
      productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
        verify(flightRepository, times(1)).retrieveFlightConfirmationData(
          any(),
          any(),
          eqTo(chargeCurrency),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          eqTo(Some(packagingToken)),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())

        result.packageToken.head.interSystemToken shouldBe expectedPackagesToken.interSystemToken
        result.packageToken.head.clientToken shouldBe expectedPackagesToken.clientToken
      }
    }

    "should use correct package token for first property request (price confirmed) property returns correct token" in {
      val afterFlightCallPackageToken = expectedPackagesToken.copy(clientToken = "ret")
      val flightProductItem =
        FlightConfirmationData(
          "",
          "",
          mock[FlightToken],
          true,
          true,
          true,
          false,
          Some(expectedPackagesToken.copy(clientToken = "ret")),
          None,
          None,
          None,
          Map.empty
        )
      when(
        flightRepository
          .retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
      ).thenReturn(Future.successful(flightProductItem))
      when(
        flightRepository.retrieveFlightItineraryDetails(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
      ).thenReturn(Future.successful(flightProductItem))
      when(
        mockPriceBreakdownService.getPriceBreakdownFromPackagingPrice(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
      ).thenReturn(Future.successful(priceBreakdown))

      val packagingToken = PackagingToken(expectedPackagesToken.clientToken, expectedPackagesToken.interSystemToken)
      when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
      val propertySearchCriteria1 = mock[PropertySearchCriteria]
      val durationRequest1        = DurationRequest(LocalDate.now(), 2)
      when(propertySearchCriteria1.propertyId).thenReturn(Some(defaultPropertyId))
      when(propertySearchCriteria1.durationRequest).thenReturn(durationRequest1)
      when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria1)
      val guest = mock[HotelGuest]
      when(guest.primary).thenReturn(true)
      when(guest.citizenshipId).thenReturn(Some(""))
      when(mockPropertyRequest.guests).thenReturn(None)

      val pricingRequest1 = mock[PricingRequest]
      when(propertySearchCriteria1.pricingRequest).thenReturn(Some(pricingRequest1))
      when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)

      productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
        verify(flightRepository, times(1)).retrieveFlightConfirmationData(
          any(),
          any(),
          eqTo(chargeCurrency),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          eqTo(Some(packagingToken)),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
        verify(propertyService, times(2))
          .retrieveProperties(
            any(),
            any(),
            eqTo(chargeCurrency),
            eqTo(Some(afterFlightCallPackageToken)),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
        result.packageToken.head.interSystemToken shouldBe expectedPackagesToken.interSystemToken
        result.packageToken.head.clientToken shouldBe "prop-client"

        result.totalSavings.get.totalSavingsAmount.amount shouldBe 500d
        result.totalSavings.get.totalSavingsPercent shouldBe 20
        result.bundleSavings shouldBe None
      }
    }

    "single hotel flow with no data should have price not ready flag" in {

      mockPropertyServiceRetrieveProperties(
        Seq[BookingPropertiesData](bookingPropertyProduct().copy(papiProperties = None))
      )

      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)

      when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
      when(setupBookingContext.session)
        .thenReturn(
          SetupBookingSessionContext(
            Map("key" -> mockPropertySetupBookingToken),
            Some(expectedPackagesToken),
            expectedTimeStamp
          )
        )

      productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
        verify(propertyService, times(1))
          .retrieveProperties(
            any(),
            any(),
            eqTo(chargeCurrency),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(
            any()
          )
        verify(flightRepository, times(0))
          .retrieveFlightItineraryDetails(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(
            any()
          )

        result.priceConfirmed shouldBe false
        result.packageToken shouldBe None
        result.properties should have size 1
        result.flights should have size 0
        result.protections shouldBe Seq.empty
      }
    }

    forEvery(supportPriceDisplayTypesTestCases) { isUNIBF2454 =>
      {
        s"package flow with no price confirmation data should use detail response when UNIBF-2454 is $isUNIBF2454" in {
          when(featureAware.supportPriceDisplayTypes).thenReturn(isUNIBF2454)
          mockPropertyServiceRetrieveProperties(Seq[BookingPropertiesData](bookingPropertyProduct()))
          val flightProductItem =
            FlightConfirmationData(
              "",
              "",
              mock[FlightToken],
              false,
              false,
              true,
              false,
              None,
              Some(MockFlightsPricingData.searchResponseCurrencyPricing),
              Some(MockFlightsPricingData.priceChangeResponse),
              None,
              Map.empty
            )

          val flightDetailItem = flightProductItem.copy(
            isCompleted = false,
            hasFlight = true,
            hasContent = true,
            priceChange = None,
            token = "flightDetailToken"
          )
          when(
            flightRepository
              .retrieveFlightConfirmationData(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(any())
          ).thenReturn(Future.successful(flightProductItem))
          when(
            flightRepository.retrieveFlightItineraryDetails(
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
          ).thenReturn(Future.successful(flightDetailItem))
          when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Package)

          val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
          when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
          productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
            verify(propertyService, times(1))
              .retrieveProperties(
                any(),
                any(),
                eqTo(chargeCurrency),
                any(),
                any(),
                any(),
                any(),
                any(),
                eqTo(Some(paymentRequest)),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(any())
            verify(flightRepository, times(1))
              .retrieveFlightConfirmationData(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(any())
            verify(flightRepository, times(1))
              .retrieveFlightItineraryDetails(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(
                any()
              )

            result.priceConfirmed shouldBe false
            result.totalPriceDisplay shouldNot be(Seq.empty)
            if (isUNIBF2454)
              result.priceDisplayType shouldBe Some(PriceDisplayType.Package)
            else
              result.priceDisplayType shouldBe None
            result.flights should have size 1
            result.flights.head.token shouldBe "flightDetailToken"
            result.properties should have size 1
          }
        }
      }
    }

    "packages flow should call propertyService to retrieveProperties with GiftCardRedeemRequest" in {
      val packageTokenAfterFlightCall = Some(expectedPackagesToken.copy(clientToken = "ret"))
      val flightProductItem =
        FlightConfirmationData(
          "",
          "",
          mock[FlightToken],
          true,
          true,
          false,
          false,
          None,
          Some(MockFlightsPricingData.searchResponseCurrencyPricing),
          None,
          None,
          Map.empty
        )
      when(
        flightRepository
          .retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
      ).thenReturn(Future.successful(flightProductItem))
      when(
        flightRepository.retrieveFlightItineraryDetails(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
      ).thenReturn(Future.successful(flightProductItem))
      when(setupBookingRequest.redeemRequest).thenReturn(Option(RedeemRequest(2, Some(0))))

      val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
      when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
      productsFacade.composeProductData(setupBookingRequest, chargeCurrency, 10).map { result =>
        verify(propertyService, times(2))
          .retrieveProperties(
            any(),
            any(),
            any(),
            any(),
            eqTo(Some(GiftCardRedeemRequest(2, 10))),
            any(),
            any(),
            any(),
            eqTo(Some(paymentRequest)),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())

        result.packageToken shouldBe packageTokenAfterPropertyCall
      }
    }

    "packages flow should call propertyService to retrieveProperties with price change in " in {
      val packageTokenAfterFlightCall = Some(expectedPackagesToken.copy(clientToken = "ret"))
      val flightProductItem =
        FlightConfirmationData(
          "",
          "",
          mock[FlightToken],
          true,
          true,
          true,
          false,
          packageTokenAfterFlightCall,
          Some(MockFlightsPricingData.searchResponseCurrencyPricing),
          Some(MockFlightsPricingData.priceChangeResponse),
          None,
          Map.empty
        )
      when(
        flightRepository
          .retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
      ).thenReturn(Future.successful(flightProductItem))
      when(
        flightRepository.retrieveFlightItineraryDetails(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
      ).thenReturn(Future.successful(flightProductItem))

      mockPropertyServiceRetrieveProperties(
        Seq[BookingPropertiesData](
          bookingPropertyProductWithPackagePricing().copy(packageRequest = packageTokenAfterPropertyCall)
        )
      )

      val packagingToken = PackagingToken(expectedPackagesToken.clientToken, expectedPackagesToken.interSystemToken)
      productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
        verify(flightRepository, times(1)).retrieveFlightConfirmationData(
          any(),
          any(),
          eqTo(chargeCurrency),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          eqTo(Some(packagingToken)),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())

        verify(propertyService, times(2))
          .retrieveProperties(
            any(),
            any(),
            any(),
            eqTo(packageTokenAfterFlightCall),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())

        result.packageToken shouldBe packageTokenAfterPropertyCall
      }
    }

    "package flow with unconfirmed price enabled should have total savings" in {
      when(featureAware.supportPriceDisplayTypes).thenReturn(true)
      when(
        mockPriceBreakdownService.getPriceBreakdownFromPackagingPrice(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
      ).thenReturn(Future.successful(priceBreakdown))

      mockPropertyServiceRetrieveProperties(Seq[BookingPropertiesData](bookingPropertyProduct()))

      val flightProductItem =
        FlightConfirmationData(
          "",
          "",
          mock[FlightToken],
          false,
          false,
          true,
          false,
          None,
          Some(MockFlightsPricingData.searchResponseCurrencyPricing),
          Some(MockFlightsPricingData.priceChangeResponse),
          None,
          Map.empty
        )

      val flightDetailItem = flightProductItem.copy(
        isCompleted = false,
        hasFlight = true,
        hasContent = true,
        priceChange = None,
        token = "flightDetailToken"
      )
      when(
        flightRepository
          .retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
      ).thenReturn(Future.successful(flightProductItem))
      when(
        flightRepository.retrieveFlightItineraryDetails(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
      ).thenReturn(Future.successful(flightDetailItem))
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Package)

      when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
      val propertySearchCriteria1 = mock[PropertySearchCriteria]
      val durationRequest1        = DurationRequest(LocalDate.now(), 2)
      when(propertySearchCriteria1.propertyId).thenReturn(Some(defaultPropertyId))
      when(propertySearchCriteria1.durationRequest).thenReturn(durationRequest1)
      when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria1)
      val guest = mock[HotelGuest]
      when(guest.primary).thenReturn(true)
      when(guest.citizenshipId).thenReturn(Some(""))
      when(mockPropertyRequest.guests).thenReturn(None)

      val pricingRequest1 = mock[PricingRequest]
      when(propertySearchCriteria1.pricingRequest).thenReturn(Some(pricingRequest1))
      when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)

      productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
        verify(propertyService, times(1))
          .retrieveProperties(
            any(),
            any(),
            eqTo(chargeCurrency),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(
            any()
          )
        verify(flightRepository, times(1))
          .retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
        verify(flightRepository, times(1))
          .retrieveFlightItineraryDetails(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(
            any()
          )

        result.priceConfirmed shouldBe false
        result.totalPriceDisplay shouldNot be(Seq.empty)
        result.priceDisplayType shouldBe Some(PriceDisplayType.Package)
        result.flights should have size 1
        result.properties should have size 1
        result.totalSavings.isDefined should be(true)
        result.totalSavings.get.totalSavingsAmount.amount should be(500)
        result.totalSavings.get.totalSavingsAmount.currencyCode should be(thbCurrency)
        result.totalSavings.get.totalSavingsPercent should be(20)
      }
    }

  }

  "getProductItems for Protection cases" should {

    val flightProductItem = FlightConfirmationData(
      "",
      "",
      mock[FlightToken],
      true,
      true,
      true,
      false,
      Some(expectedPackagesToken.copy(clientToken = "ret")),
      None,
      None,
      Some(mock[FlightItinerary]),
      Map.empty
    )

    val flightProductItemWithHackerFare = FlightConfirmationData(
      "",
      "",
      mock[FlightToken],
      true,
      true,
      true,
      true,
      Some(expectedPackagesToken.copy(clientToken = "ret")),
      None,
      None,
      Some(mock[FlightItinerary]),
      Map.empty
    )
    val mockPricingTree = mock[PriceBreakdownNode]

    "flight flow with trip protection should succeed" in {
      when(featureAware.supportPriceDisplayTypes).thenReturn(true)
      when(productsRequest.tripProtectionRequests).thenReturn(Some(Seq(mock[TripProtectionRequestItem])))
      when(protectionService.setupProtection(any(), any(), any())(any()))
        .thenReturn(Future.successful(Seq(getProtectionItem(optInValue = 0, token = ""))))

      when(
        flightRepository
          .retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
      ).thenReturn(Future.successful(flightProductItem))
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.FlightWithProtection)
      when(mockAddOnService.getAddOns(any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(Seq.empty))
      when(
        mockPriceBreakdownService.getPriceBreakdownForFlight(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )
      )
        .thenReturn(Future.successful(Option(mockPricingTree)))
      when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
        .thenReturn(Future.successful(ccCampaignMock))

      productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
        verify(flightRepository, times(1))
          .retrieveFlightConfirmationData(
            any(),
            any(),
            eqTo(chargeCurrency),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
        verify(protectionService, times(1)).setupProtection(any(), any(), any())(any())

        result.packageToken shouldBe None
        result.flights should have size 1
        result.totalPriceDisplay.isDefined shouldBe true
        result.totalPriceDisplay.get shouldBe mockPricingTree
        result.priceDisplayType shouldBe Some(PriceDisplayType.SingleFlight)
        result.protections shouldBe Seq(getProtectionItem(optInValue = 0, token = ""))
      }
    }

    "flight flow with trip protection should be called for hackerfare" in {
      when(featureAware.supportPriceDisplayTypes).thenReturn(true)
      when(productsRequest.tripProtectionRequests).thenReturn(Some(Seq(mock[TripProtectionRequestItem])))
      when(protectionService.setupProtection(any(), any(), any())(any()))
        .thenReturn(Future.successful(Seq(getProtectionItem(optInValue = 1, token = ""))))

      when(
        flightRepository
          .retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
      ).thenReturn(Future.successful(flightProductItemWithHackerFare))
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.FlightWithProtection)
      when(mockAddOnService.getAddOns(any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(Seq.empty))
      when(
        mockPriceBreakdownService.getPriceBreakdownForFlight(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )
      )
        .thenReturn(Future.successful(Option(mockPricingTree)))
      when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
        .thenReturn(Future.successful(ccCampaignMock))

      productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
        verify(flightRepository, times(1))
          .retrieveFlightConfirmationData(
            any(),
            any(),
            eqTo(chargeCurrency),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
        verify(protectionService, times(1)).setupProtection(any(), any(), any())(any())

        result.packageToken shouldBe None
        result.flights should have size 1
        result.totalPriceDisplay.isDefined shouldBe true
        result.totalPriceDisplay.get shouldBe mockPricingTree
        result.priceDisplayType shouldBe Some(PriceDisplayType.SingleFlight)
        result.protections shouldBe Seq(getProtectionItem(optInValue = 1, token = ""))
      }
    }

    "flight flow with trip protection failure flight should succeed" in {
      when(featureAware.supportPriceDisplayTypes).thenReturn(true)
      when(productsRequest.tripProtectionRequests).thenReturn(Some(Seq(mock[TripProtectionRequestItem])))
      when(protectionService.setupProtection(any(), any(), any())(any()))
        .thenReturn(Future.successful(Seq.empty))

      when(
        flightRepository
          .retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
      ).thenReturn(Future.successful(flightProductItem))
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.FlightWithProtection)
      when(mockAddOnService.getAddOns(any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(Seq.empty))
      when(
        mockPriceBreakdownService.getPriceBreakdownForFlight(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )
      )
        .thenReturn(Future.successful(Option(mockPricingTree)))
      when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
        .thenReturn(Future.successful(ccCampaignMock))

      productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
        verify(flightRepository, times(1))
          .retrieveFlightConfirmationData(
            any(),
            any(),
            eqTo(chargeCurrency),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
        verify(protectionService, times(1)).setupProtection(any(), any(), any())(any())

        result.packageToken shouldBe None
        result.flights should have size 1
        result.totalPriceDisplay.isDefined shouldBe true
        result.totalPriceDisplay.get shouldBe mockPricingTree
        result.priceDisplayType shouldBe Some(PriceDisplayType.SingleFlight)
        result.protections shouldBe Seq.empty
      }
    }

    "package flow with empty price from papi should return empty price breakdown" in {
      mockPropertyServiceRetrieveProperties(
        Seq[BookingPropertiesData](bookingPropertyProduct().copy(papiProperties = None))
      )
      val flightProductItem =
        FlightConfirmationData(
          "",
          "",
          mock[FlightToken],
          false,
          false,
          true,
          false,
          None,
          Some(MockFlightsPricingData.searchResponseCurrencyPricing),
          Some(MockFlightsPricingData.priceChangeResponse),
          None,
          Map.empty
        )

      val flightDetailItem = flightProductItem.copy(
        isCompleted = false,
        hasFlight = true,
        hasContent = true,
        priceChange = None,
        token = "flightDetailToken"
      )
      when(featureAware.supportPriceDisplayTypes).thenReturn(true)
      when(
        flightRepository
          .retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
      ).thenReturn(Future.successful(flightProductItem))
      when(
        flightRepository.retrieveFlightItineraryDetails(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())
      ).thenReturn(Future.successful(flightDetailItem))
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Package)

      productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
        result.priceConfirmed shouldBe false
        result.totalPriceDisplay shouldBe None
        result.priceDisplayType shouldBe Some(PriceDisplayType.Package)
      }
    }
  }

  "composeProductData" when {
    val propertySearchCriteria = mock[PropertySearchCriteria]
    val occupancyRequest       = mock[OccupancyRequest]
    when(occupancyRequest.roomCount).thenReturn(1)
    when(propertySearchCriteria.occupancyRequest).thenReturn(occupancyRequest)

    def setupRoomSwapTest(
        roomSwapPropertySearchCriteria: PropertySearchCriteria,
        roomSwapPropertyRequestItem: PropertyRequestItem,
        bookingPropertiesData: BookingPropertiesData = bookingPropertyProduct(),
        isRateChannelSwap: Boolean = false
    ) = {
      when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(totalSavings))
      when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
      mockPropertyServiceRetrieveProperties(Seq[BookingPropertiesData](bookingPropertiesData.copy()))
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      mockPriceBreakdownServiceGetPriceBreakdown()
      when(productsRequest.propertyRequests).thenReturn(Seq(roomSwapPropertyRequestItem))
      when(mockPropertyRequest.propertySearchCriteria).thenReturn(roomSwapPropertySearchCriteria)
    }

    "single hotel flow with data" should {
      val defaultSetupBookingSessionContext = SetupBookingSessionContext(
        properties = Map("key" -> mockPropertySetupBookingToken),
        packages = Some(expectedPackagesToken),
        timestamp = expectedTimeStamp
      )

      forEvery(supportPriceDisplayTypesTestCases) { isUNIBF2454 =>
        {
          s"have price breakdown and total savings in Product Data when UNIBF-2454 is $isUNIBF2454" in {
            val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
            when(featureAware.supportPriceDisplayTypes).thenReturn(isUNIBF2454)
            when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
            mockPropertyServiceRetrieveProperties(Seq[BookingPropertiesData](bookingPropertyProduct()))
            when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
            when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
            when(setupBookingContext.session).thenReturn(defaultSetupBookingSessionContext)
            mockPriceBreakdownServiceGetPriceBreakdown()
            when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(totalSavings))

            productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
              verify(propertyService, times(1))
                .retrieveProperties(
                  any(),
                  any(),
                  eqTo(chargeCurrency),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  eqTo(Some(paymentRequest)),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any()
                )(any())

              result.properties.length shouldBe 1
              result.totalPriceDisplay.get.value.get.amount.amount shouldBe 2000d
              if (isUNIBF2454)
                result.priceDisplayType shouldBe Some(PriceDisplayType.SingleProperty)
              else
                result.priceDisplayType shouldBe None
            }
          }
        }
      }

      "call addOnV2 with correct currency" in {
        val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
        val childRoom      = baseChildrenRoom().copy(requestedCurrencyCode = Some("AAA"))
        val masterRoom     = baseMasterRoom().copy(childrenRooms = List(childRoom))
        val property       = baseProperty().copy(masterRooms = Seq(masterRoom))
        when(featureAware.CegFastTrackMigration).thenReturn(true)
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        mockPropertyServiceRetrieveProperties(
          Seq[BookingPropertiesData](
            bookingPropertyProduct().copy(
              papiProperties = Some(
                transformers.Properties(
                  property = Seq(property),
                  debug = None,
                  dfMetaResult = DFMetaResult(Some(ResponseStateToken("", true, Some("pricingRequestId-1"))))
                )
              )
            )
          )
        )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session).thenReturn(defaultSetupBookingSessionContext)
        mockPriceBreakdownServiceGetPriceBreakdown()
        when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(totalSavings))

        productsFacade.composeProductData(setupBookingRequest, "EUR").map { _ =>
          val addOnInputV2RequestCurrencyCaptor: ArgumentCaptor[String] = ArgumentCaptor.forClass(classOf[String])
          verify(mockAddOnService, times(1))
            .getAddOns(any(), any(), addOnInputV2RequestCurrencyCaptor.capture(), any())(any[SetupBookingContext])
          val chargeCurrency = addOnInputV2RequestCurrencyCaptor.getValue
          chargeCurrency shouldBe "AAA"
        }
      }

      "call addOnV2 with default currency" in {
        val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
        when(featureAware.CegFastTrackMigration).thenReturn(true)
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        mockPropertyServiceRetrieveProperties(
          Seq[BookingPropertiesData](
            bookingPropertyProduct()
          )
        )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session).thenReturn(defaultSetupBookingSessionContext)
        mockPriceBreakdownServiceGetPriceBreakdown()
        when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(totalSavings))

        productsFacade.composeProductData(setupBookingRequest, "EUR").map { _ =>
          val addOnInputV2RequestCurrencyCaptor: ArgumentCaptor[String] = ArgumentCaptor.forClass(classOf[String])
          verify(mockAddOnService, times(1))
            .getAddOns(any(), any(), addOnInputV2RequestCurrencyCaptor.capture(), any())(any[SetupBookingContext])
          val chargeCurrency = addOnInputV2RequestCurrencyCaptor.getValue
          chargeCurrency shouldBe "USD"
        }
      }

      "maps cxlRebook original bookingId correctly" in {
        val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        mockPropertyServiceRetrieveProperties(
          Seq[BookingPropertiesData](
            bookingPropertyProduct()
              .copy(propertySearchCriteria = Some(propertySearchCriteria))
          )
        )
        val propertyRequestItem = mock[PropertyRequestItem]
        val durationRequest     = mock[DurationRequest]
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session).thenReturn(defaultSetupBookingSessionContext)
        mockPriceBreakdownServiceGetPriceBreakdown()
        when(productsRequest.propertyRequests).thenReturn(Seq(propertyRequestItem))
        when(propertyRequestItem.copy(any(), any(), any(), any(), any(), any(), any())).thenReturn(propertyRequestItem)
        when(propertyRequestItem.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(propertyRequestItem.confirmPriceRequest).thenReturn(None)
        when(propertyRequestItem.consumerFintechRequest).thenReturn(
          Some(
            ConsumerFintechRequest(
              product = ConsumerFintechProductRequest(
                cancelAndRebook = Some(
                  ConsumerFintechCancelAndRebookProductRequest(
                    TokenMessage(
                      token = "/SZ5FiHCL9cYjRfdtFjGTw==",
                      version = 1
                    )
                  )
                )
              )
            )
          )
        )
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)
        val guest = mock[HotelGuest]
        when(guest.primary).thenReturn(true)
        when(guest.citizenshipId).thenReturn(Some(""))
        when(propertyRequestItem.guests).thenReturn(None)
        val occupancyRequest = com.agoda.bapi.common.message.OccupancyRequest(2, 2, 1)
        when(propertySearchCriteria.occupancyRequest).thenReturn(occupancyRequest)
        when(durationRequest.lengthOfStay).thenReturn(1)
        when(durationRequest.checkIn).thenReturn(new LocalDate("2020-12-15"))
        when(propertySearchCriteria.propertyId).thenReturn(Some(10L))
        when(propertySearchCriteria.pricingRequest).thenReturn(None)
        when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(totalSavings))
        when(mockPriceInfo.cxlCode).thenReturn("365D100_100P")
        when(mockPriceInfo.remainingRooms).thenReturn(4)
        when(mockPriceInfo.rateCategory).thenReturn(Some(mockRateCategory))
        val bookingContext: BookingCreationContext = mock[BookingCreationContext]
        when(setupBookingRequest.bookingContext).thenReturn(Some(bookingContext))
        when(mockRateCategory.id).thenReturn(4725365)
        when(userContext.memberId).thenReturn(Some(67354))
        when(setupBookingRequest.deviceContext).thenReturn(Some(deviceContext))
        when(bookingContext.userAgent).thenReturn(
          UserAgent(
            isMobile = Some(true),
            browserVersion = "2.1",
            browserName = "firefox",
            osName = "mac-os",
            osVersion = "catalina"
          )
        )
        when(deviceContext.deviceId).thenReturn(Some("856545"))
        when(deviceContext.deviceTypeId).thenReturn(DevicePlatform.WebTablet)
        when(mockConsumerFintechService.getConsumerFintechRequirement(any())(any()))
          .thenReturn(
            Future.successful(
              Some(
                ConsumerFintechRequirement(
                  cancelAndRebookRequirement = Some(
                    CancelAndRebookRequirement(
                      originalBookingId = 9384288,
                      originalItineraryId = 39273434,
                      saleInclusive = TMoney(42, "USD"),
                      roomTypeId = 1,
                      originalAgodaCash = Some(1.2f),
                      originalCashback = Some(1.3f),
                      originalPromoAmount = Some(1.4f),
                      originalSellIn = None,
                      originalUsdToRequestExchangeRate = Some(20.0f),
                      originalSellInUsd = Some(1.5f)
                    )
                  )
                )
              )
            )
          )

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              any(),
              any(),
              eqTo(chargeCurrency),
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Some(paymentRequest)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())

          result.properties.headOption.flatMap(_.consumerFintechDetail) shouldBe Some(
            ConsumerFintechDetail(
              products = ConsumerFintechProductDetail(
                smartFlex = None,
                cancelAndRebookV3 = Some(
                  CancelAndRebookV3ProductDetail(
                    originalBookingId = 9384288,
                    originalItineraryId = 39273434
                  )
                ),
                smartSaver = None
              ),
              serviceTaxCountry = None
            )
          )
        }
      }

      "have price breakdown and total savings in Product Data with alternatives" in {
        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        val durationRequest = DurationRequest(LocalDate.now(), 2)
        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)
        when(propertySearchCriteria.pricingRequest).thenReturn(None)
        when(propertySearchCriteria.roomIdentifier).thenReturn("matchedroomidentifiers")
        when(propertySearchCriteria.simplifiedRoomSelectionRequest).thenReturn(None)
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
        when(mockPropertyRequest.copy(any(), any(), any(), any(), any(), any(), any())).thenReturn(mockPropertyRequest)
        when(mockPropertyRequest.consumerFintechRequest).thenReturn(None)
        mockPropertyServiceRetrieveProperties(
          Seq[BookingPropertiesData](
            bookingPropertyProduct()
              .copy(propertySearchCriteria = Some(propertySearchCriteria))
          )
        )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session).thenReturn(defaultSetupBookingSessionContext)
        when(featureAware.isAffiliateFeatureFlagEnabled).thenReturn(false)
        when(mockPropertyRequest.guests).thenReturn(None)
        when(featureAware.isCrossSellOnUpSell).thenReturn(true)
        mockPriceBreakdownServiceGetPriceBreakdown()
        when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(totalSavings))

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              any(),
              any(),
              eqTo(chargeCurrency),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              any()
            )

          result.properties.length shouldBe 1
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 2000d
          result.priceDisplayType shouldBe Some(PriceDisplayType.SingleProperty)
        }
      }

      "send customer contact info message" in {
        val propertyRequestItem = mock[PropertyRequestItem]
        val durationRequest     = mock[DurationRequest]
        mockPropertyServiceRetrieveProperties(Seq[BookingPropertiesData](bookingPropertyProduct()))
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session).thenReturn(defaultSetupBookingSessionContext)
        when(featureAware.isAffiliateFeatureFlagEnabled).thenReturn(false)
        when(propertyRequestItem.guests).thenReturn(None)
        mockPriceBreakdownServiceGetPriceBreakdown()
        when(productsRequest.propertyRequests).thenReturn(Seq(propertyRequestItem))
        when(propertyRequestItem.copy(any(), any(), any(), any(), any(), any(), any())).thenReturn(propertyRequestItem)
        when(propertyRequestItem.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(propertyRequestItem.confirmPriceRequest).thenReturn(None)
        when(propertyRequestItem.consumerFintechRequest).thenReturn(None)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)
        when(durationRequest.lengthOfStay).thenReturn(1)
        when(durationRequest.checkIn).thenReturn(new LocalDate("2020-12-15"))
        when(propertySearchCriteria.propertyId).thenReturn(Some(1L))
        when(propertySearchCriteria.pricingRequest).thenReturn(None)
        when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(totalSavings))

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(mockMessageService, times(1)).sendMessage(any[CustomerContactInfo])
          result.properties.length shouldBe 1
        }
      }

      "call PAPI with correct parameters" in {
        mockPropertyServiceRetrieveProperties(Seq[BookingPropertiesData](bookingPropertyProduct()))
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session).thenReturn(defaultSetupBookingSessionContext)
        mockPriceBreakdownServiceGetPriceBreakdown()
        when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(totalSavings))

        val cardPaymentRequestParameter =
          Some(CardPaymentRequestParameter(usdCurrency, thbCurrency, Some(1), Some(101)))
        val cardPaymentRequestCaptor: ArgumentCaptor[Option[CardPaymentRequestParameter]] =
          ArgumentCaptor.forClass(classOf[Option[CardPaymentRequestParameter]])
        productsFacade
          .composeProductData(setupBookingRequest, chargeCurrency, cardPaymentParams = cardPaymentRequestParameter)
          .map { result =>
            verify(propertyService, times(1))
              .retrieveProperties(
                any(),
                any(),
                eqTo(chargeCurrency),
                any(),
                any(),
                any(),
                cardPaymentRequestCaptor.capture(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(any())
            val capturedCardPaymentRequestParameter = cardPaymentRequestCaptor.getValue
            capturedCardPaymentRequestParameter shouldBe cardPaymentRequestParameter
          }
      }

      "have message host flag" in {
        val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
        when(mockBookingMessagingService.mapIsMessageHostAvailableTo(any())(any())) thenAnswer (
          (i: InvocationOnMock) => {
            val bookingPropertiesData = i.getArgument[Seq[BookingPropertiesData]](0)
            Future.successful(bookingPropertiesData.map(p => p.copy(isMessageHostAvailable = Some(true))))
          }
        )
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        mockPropertyServiceRetrieveProperties(Seq[BookingPropertiesData](bookingPropertyProduct()))
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session).thenReturn(defaultSetupBookingSessionContext)
        mockPriceBreakdownServiceGetPriceBreakdown()
        when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(totalSavings))

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              any(),
              any(),
              eqTo(chargeCurrency),
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Some(paymentRequest)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())

          result.properties.head.isMessageHostAvailable shouldBe Some(true)
        }
      }

      val roomSwapOccupancyRequest = OccupancyRequest(roomCount = 1, adult = 2)
      val roomSwapDurationRequest  = DurationRequest(checkIn = new LocalDate("2020-12-15"), lengthOfStay = 1)
      val roomSwapPropertySearchCriteria = PropertySearchCriteria(
        propertyId = Some(1L),
        roomIdentifier = "",
        occupancyRequest = roomSwapOccupancyRequest,
        durationRequest = roomSwapDurationRequest,
        pricingRequest = Some(
          PricingRequest(
            isMse = false,
            requiredPrice = "",
            requiredBasis = "",
            isRPM2Included = false,
            selectedPointMaxId = None,
            isIncludeUsdAndLocalCurrency = false,
            dfFeatureFlags = Seq.empty,
            allowOverrideOccupancy = false,
            enableOpaqueChannel = false,
            isAllowRoomTypeNotGuarantee = false,
            synchronous = false,
            partnerLoyaltyProgramId = None
          )
        ),
        papiContextRequest = None,
        roomSelectionRequest = None,
        propertyRequest = None,
        simplifiedRoomSelectionRequest = None,
        partnerRequest = None
      )
      val roomSwapPropertyRequestItem =
        PropertyRequestItem(id = "", propertySearchCriteria = roomSwapPropertySearchCriteria)

      "not call PAPI with DF AlternativeRoom feature flag when experiment is not active" in {
        setupRoomSwapTest(roomSwapPropertySearchCriteria, roomSwapPropertyRequestItem)

        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.isRoomSwapEnabled).thenReturn(false)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session).thenReturn(defaultSetupBookingSessionContext)

        val requestItemCaptor: ArgumentCaptor[Seq[PropertyRequestItem]] =
          ArgumentCaptor.forClass(classOf[Seq[PropertyRequestItem]])

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              requestItemCaptor.capture(),
              eqTo(DevicePlatform.Unknown),
              eqTo(chargeCurrency),
              eqTo(None),
              eqTo(Some(GiftCardRedeemRequest(2.0, 0.0))),
              eqTo(
                Some(
                  DiscountRequest(
                    None,
                    Some(List(CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None), None)))
                  )
                )
              ),
              eqTo(None),
              eqTo(null),
              eqTo(None),
              eqTo(false),
              eqTo(None),
              eqTo(None),
              any(),
              eqTo(Seq.empty),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              eqTo(setupBookingContext)
            )
          requestItemCaptor.getValue.head.propertySearchCriteria.pricingRequest
            .map(_.dfFeatureFlags) shouldBe Some(Seq.empty)
          result.properties.length shouldBe 1
        }
      }

      "call PAPI with DF AlternativeRoom feature flag when experiment is active and isRoomHasSwapped is false" in {
        setupRoomSwapTest(roomSwapPropertySearchCriteria, roomSwapPropertyRequestItem)

        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.isRoomSwapEnabled).thenReturn(true)

        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session).thenReturn(defaultSetupBookingSessionContext)

        val requestItemCaptor: ArgumentCaptor[Seq[PropertyRequestItem]] =
          ArgumentCaptor.forClass(classOf[Seq[PropertyRequestItem]])

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              requestItemCaptor.capture(),
              eqTo(DevicePlatform.Unknown),
              eqTo(chargeCurrency),
              eqTo(None),
              eqTo(Some(GiftCardRedeemRequest(2.0, 0.0))),
              eqTo(
                Some(
                  DiscountRequest(
                    None,
                    Some(List(CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None), None)))
                  )
                )
              ),
              eqTo(None),
              eqTo(null),
              eqTo(None),
              eqTo(false),
              eqTo(None),
              eqTo(None),
              any(),
              eqTo(Seq.empty),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              eqTo(setupBookingContext)
            )
          requestItemCaptor.getValue.head.propertySearchCriteria.pricingRequest
            .map(_.dfFeatureFlags) shouldBe Some(Seq(49))
          result.properties.length shouldBe 1
        }
      }

      "not call PAPI with DF AlternativeRoom feature flag when experiment is active and isRoomHasSwapped is true" in {
        setupRoomSwapTest(roomSwapPropertySearchCriteria, roomSwapPropertyRequestItem)

        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.isRoomSwapEnabled).thenReturn(true)

        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(true))
        when(setupBookingContext.session).thenReturn(defaultSetupBookingSessionContext)

        val requestItemCaptor: ArgumentCaptor[Seq[PropertyRequestItem]] =
          ArgumentCaptor.forClass(classOf[Seq[PropertyRequestItem]])

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              requestItemCaptor.capture(),
              eqTo(DevicePlatform.Unknown),
              eqTo(chargeCurrency),
              eqTo(None),
              eqTo(Some(GiftCardRedeemRequest(2.0, 0.0))),
              eqTo(
                Some(
                  DiscountRequest(
                    None,
                    Some(List(CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None), None)))
                  )
                )
              ),
              eqTo(None),
              eqTo(null),
              eqTo(None),
              eqTo(false),
              eqTo(None),
              eqTo(None),
              any(),
              eqTo(Seq.empty),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              eqTo(setupBookingContext)
            )
          requestItemCaptor.getValue.head.propertySearchCriteria.pricingRequest
            .map(_.dfFeatureFlags) shouldBe Some(Seq.empty)
          result.properties.length shouldBe 1
        }
      }

      "call PAPI with DF ForceAlternativeRoom feature flag when isForceAlternativeRoom = true and isRoomHasSwapped = false" in {
        setupRoomSwapTest(roomSwapPropertySearchCriteria, roomSwapPropertyRequestItem)
        val isForceAlternativeRoom = true

        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.isRoomSwapEnabled).thenReturn(true)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session).thenReturn(defaultSetupBookingSessionContext)

        val requestItemCaptor: ArgumentCaptor[Seq[PropertyRequestItem]] =
          ArgumentCaptor.forClass(classOf[Seq[PropertyRequestItem]])
        productsFacade
          .composeProductData(setupBookingRequest, chargeCurrency, isForceAlternativeRoom = isForceAlternativeRoom)
          .map { result =>
            verify(propertyService, times(1))
              .retrieveProperties(
                requestItemCaptor.capture(),
                eqTo(DevicePlatform.Unknown),
                eqTo(chargeCurrency),
                eqTo(None),
                eqTo(Some(GiftCardRedeemRequest(2.0, 0.0))),
                eqTo(
                  Some(
                    DiscountRequest(
                      None,
                      Some(List(CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None), None)))
                    )
                  )
                ),
                eqTo(None),
                eqTo(null),
                eqTo(None),
                eqTo(true),
                eqTo(None),
                eqTo(None),
                any(),
                eqTo(Seq.empty),
                any(),
                any(),
                any(),
                any(),
                any()
              )(
                eqTo(setupBookingContext)
              )
            requestItemCaptor.getValue.head.propertySearchCriteria.pricingRequest
              .map(_.dfFeatureFlags) shouldBe Some(Seq(60))
            result.properties.length shouldBe 1
          }
      }
      "call PAPI without DF ForceAlternativeRoom feature flag when isForceAlternativeRoom = false and isRoomHasSwapped = false" in {
        setupRoomSwapTest(roomSwapPropertySearchCriteria, roomSwapPropertyRequestItem)

        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.isRoomSwapEnabled).thenReturn(false)
        val isForceAlternativeRoom = false
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session).thenReturn(defaultSetupBookingSessionContext)

        val requestItemCaptor: ArgumentCaptor[Seq[PropertyRequestItem]] =
          ArgumentCaptor.forClass(classOf[Seq[PropertyRequestItem]])
        productsFacade
          .composeProductData(setupBookingRequest, chargeCurrency, isForceAlternativeRoom = isForceAlternativeRoom)
          .map { result =>
            verify(propertyService, times(1))
              .retrieveProperties(
                requestItemCaptor.capture(),
                eqTo(DevicePlatform.Unknown),
                eqTo(chargeCurrency),
                eqTo(None),
                eqTo(Some(GiftCardRedeemRequest(2.0, 0.0))),
                eqTo(
                  Some(
                    DiscountRequest(
                      None,
                      Some(List(CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None), None)))
                    )
                  )
                ),
                eqTo(None),
                eqTo(null),
                eqTo(None),
                eqTo(false),
                eqTo(None),
                eqTo(None),
                any(),
                eqTo(Seq.empty),
                any(),
                any(),
                any(),
                any(),
                any()
              )(
                eqTo(setupBookingContext)
              )
            requestItemCaptor.getValue.head.propertySearchCriteria.pricingRequest
              .map(_.dfFeatureFlags) shouldBe Some(Seq.empty)
            result.properties.length shouldBe 1
          }
      }
      "call PAPI without DF ForceAlternativeRoom feature flag when isForceAlternativeRoom = false and isRoomHasSwapped = true" in {
        setupRoomSwapTest(roomSwapPropertySearchCriteria, roomSwapPropertyRequestItem)

        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.isRoomSwapEnabled).thenReturn(true)
        val isForceAlternativeRoom = false
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(true))
        when(setupBookingContext.session).thenReturn(defaultSetupBookingSessionContext)

        val requestItemCaptor: ArgumentCaptor[Seq[PropertyRequestItem]] =
          ArgumentCaptor.forClass(classOf[Seq[PropertyRequestItem]])
        productsFacade
          .composeProductData(setupBookingRequest, chargeCurrency, isForceAlternativeRoom = isForceAlternativeRoom)
          .map { result =>
            verify(propertyService, times(1))
              .retrieveProperties(
                requestItemCaptor.capture(),
                eqTo(DevicePlatform.Unknown),
                eqTo(chargeCurrency),
                eqTo(None),
                eqTo(Some(GiftCardRedeemRequest(2.0, 0.0))),
                eqTo(
                  Some(
                    DiscountRequest(
                      None,
                      Some(List(CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None), None)))
                    )
                  )
                ),
                eqTo(None),
                eqTo(null),
                eqTo(None),
                eqTo(false),
                eqTo(None),
                eqTo(None),
                any(),
                eqTo(Seq.empty),
                any(),
                any(),
                any(),
                any(),
                any()
              )(
                eqTo(setupBookingContext)
              )
            requestItemCaptor.getValue.head.propertySearchCriteria.pricingRequest
              .map(_.dfFeatureFlags) shouldBe Some(Seq.empty)
            result.properties.length shouldBe 1
          }
      }

      val propertyReqId          = "prop-req-id"
      val originalRoomIdentifier = propertySearchCriteriaSelectOriginal.roomIdentifier
      val rcsRoomIdentifier      = propertySearchCriteriaSelectRcs.roomIdentifier

      val rateChannelSwapPricingRequest = PricingRequest(
        isMse = false,
        requiredPrice = "",
        requiredBasis = "",
        isRPM2Included = false,
        selectedPointMaxId = None,
        isIncludeUsdAndLocalCurrency = false,
        dfFeatureFlags = Seq.empty,
        allowOverrideOccupancy = false,
        enableOpaqueChannel = false,
        isAllowRoomTypeNotGuarantee = false,
        synchronous = false,
        partnerLoyaltyProgramId = None
      )

      val rateChannelSwapOriginalRequestItem =
        PropertyRequestItem(
          id = propertyReqId,
          propertySearchCriteria =
            propertySearchCriteriaSelectOriginal.copy(pricingRequest = Some(rateChannelSwapPricingRequest))
        )

      val rateChannelSwapPostRequestItem =
        PropertyRequestItem(
          id = propertyReqId,
          propertySearchCriteria =
            propertySearchCriteriaSelectRcs.copy(pricingRequest = Some(rateChannelSwapPricingRequest))
        )

      "not call PAPI with DF RateChannelSwap feature flag when the experiment is not active" in {
        setupRoomSwapTest(propertySearchCriteriaSelectOriginal, rateChannelSwapOriginalRequestItem)

        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.rateChannelSwapSwitch).thenReturn(false)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session).thenReturn(
          defaultSetupBookingSessionContext.copy(properties = Map(propertyReqId -> mockPropertySetupBookingToken))
        )
        when(setupBookingContext.getOriginalRoomIdentifier(propertyReqId)).thenReturn(Some(originalRoomIdentifier))

        val requestItemCaptor: ArgumentCaptor[Seq[PropertyRequestItem]] =
          ArgumentCaptor.forClass(classOf[Seq[PropertyRequestItem]])
        clearInvocations(mockPriceBreakdownService)

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              requestItemCaptor.capture(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              eqTo(setupBookingContext)
            )

          verify(mockPriceBreakdownService, times(1)).getPriceBreakdown(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            argThat((priceMap: Option[Map[String, EnrichedPricing]]) => priceMap.isEmpty),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
          requestItemCaptor.getValue.headOption.map(_.propertySearchCriteria).foreach { searchCriteria =>
            searchCriteria.pricingRequest.map(_.dfFeatureFlags) shouldBe Some(Seq.empty)
            searchCriteria.roomIdentifier shouldBe originalRoomIdentifier
          }
          result.properties.length shouldBe 1
        }
      }

      "call PAPI with DF RateChannelSwap feature flag when the experiment is active on Booking Form page landing" in {
        setupRoomSwapTest(
          propertySearchCriteriaSelectOriginal,
          rateChannelSwapOriginalRequestItem,
          bookingPropertiesDataWithRcsRoom,
          isRateChannelSwap = true
        )

        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.rateChannelSwapSwitch).thenReturn(true)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session)
          .thenReturn(
            SetupBookingSessionContext(
              Map(propertyReqId -> mockPropertySetupBookingToken),
              Some(expectedPackagesToken),
              expectedTimeStamp
            )
          )
        when(setupBookingContext.getOriginalRoomIdentifier(propertyReqId)).thenReturn(Some(originalRoomIdentifier))

        val requestItemCaptor: ArgumentCaptor[Seq[PropertyRequestItem]] =
          ArgumentCaptor.forClass(classOf[Seq[PropertyRequestItem]])
        clearInvocations(mockPriceBreakdownService)

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              requestItemCaptor.capture(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              eqTo(setupBookingContext)
            )

          verify(mockPriceBreakdownService, times(1)).getPriceBreakdown(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            argThat((priceMap: Option[Map[String, EnrichedPricing]]) => priceMap.isDefined),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())

          requestItemCaptor.getValue.headOption.map(_.propertySearchCriteria).foreach { searchCriteria =>
            searchCriteria.pricingRequest.map(_.dfFeatureFlags) shouldBe Some(Seq(FeatureFlag.RateChannelSwap.i))
            searchCriteria.roomIdentifier shouldBe originalRoomIdentifier
          }
          result.properties.length shouldBe 1
          val childRooms = result.properties.flatMap(_.childRooms)
          childRooms.length shouldBe 1
          childRooms.headOption.flatMap(_.roomIdentifiers) shouldBe Some(rcsRoomIdentifier)

          result.totalPriceDisplay.nonEmpty shouldBe true
        }
      }

      "call PAPI with DF RateChannelSwap feature flag and revert room identifier when the experiment is active" in {
        setupRoomSwapTest(
          propertySearchCriteriaSelectRcs,
          rateChannelSwapPostRequestItem,
          bookingPropertiesDataWithRcsRoom,
          true
        )

        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.rateChannelSwapSwitch).thenReturn(true)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session)
          .thenReturn(
            SetupBookingSessionContext(
              Map(propertyReqId -> mockPropertySetupBookingToken),
              Some(expectedPackagesToken),
              expectedTimeStamp
            )
          )
        when(setupBookingContext.getOriginalRoomIdentifier(propertyReqId)).thenReturn(Some(originalRoomIdentifier))

        val requestItemCaptor: ArgumentCaptor[Seq[PropertyRequestItem]] =
          ArgumentCaptor.forClass(classOf[Seq[PropertyRequestItem]])
        clearInvocations(mockPriceBreakdownService)

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              requestItemCaptor.capture(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              eqTo(setupBookingContext)
            )

          verify(mockPriceBreakdownService, times(1)).getPriceBreakdown(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            argThat((priceMap: Option[Map[String, EnrichedPricing]]) => priceMap.isDefined),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())

          requestItemCaptor.getValue.headOption.map(_.propertySearchCriteria).foreach { searchCriteria =>
            searchCriteria.pricingRequest.map(_.dfFeatureFlags) shouldBe Some(Seq(FeatureFlag.RateChannelSwap.i))
            searchCriteria.roomIdentifier shouldBe originalRoomIdentifier
          }
          result.properties.length shouldBe 1
          val childRooms = result.properties.flatMap(_.childRooms)
          childRooms.length shouldBe 1
          childRooms.headOption.flatMap(_.roomIdentifiers) shouldBe Some(rcsRoomIdentifier)

          result.totalPriceDisplay.nonEmpty shouldBe true
        }
      }

      "not apply variable tax when have VariableTax feature, valid citizenshipId, allGuestSameNationality = true" in {
        val validCitizenshipId = "991119778899"
        setupRoomSwapTest(roomSwapPropertySearchCriteria, roomSwapPropertyRequestItem)

        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(setupBookingContext.bookingFlowType).thenReturn(SingleProperty)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.isRoomSwapEnabled).thenReturn(false)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session)
          .thenReturn(
            SetupBookingSessionContext(
              Map("key" -> mockPropertySetupBookingToken),
              Some(expectedPackagesToken),
              expectedTimeStamp
            )
          )
        val guest = mock[HotelGuest]
        val variableTaxPropertySearchCriteria = PropertySearchCriteria(
          propertyId = Some(1L),
          roomIdentifier = "",
          occupancyRequest = roomSwapOccupancyRequest,
          durationRequest = roomSwapDurationRequest,
          pricingRequest = Some(
            PricingRequest(
              isMse = false,
              requiredPrice = "",
              requiredBasis = "",
              isRPM2Included = false,
              selectedPointMaxId = None,
              isIncludeUsdAndLocalCurrency = false,
              dfFeatureFlags = Seq.empty,
              allowOverrideOccupancy = false,
              enableOpaqueChannel = false,
              isAllowRoomTypeNotGuarantee = false,
              synchronous = false,
              partnerLoyaltyProgramId = None
            )
          ),
          papiContextRequest = None,
          roomSelectionRequest = None,
          propertyRequest = None,
          simplifiedRoomSelectionRequest = None,
          partnerRequest = None
        )
        val variableTaxProduct = PropertyRequestItem(
          id = "",
          propertySearchCriteria = variableTaxPropertySearchCriteria,
          guests = Some(Seq(guest))
        )
        when(setupBookingRequest.enabledFeatures).thenReturn(Some(Seq("VariableTax")))
        when(guest.primary).thenReturn(true)
        when(guest.citizenshipId).thenReturn(Some(validCitizenshipId))
        when(guest.allGuestsSameNationality).thenReturn(Some(true))
        when(productsRequest.propertyRequests).thenReturn(Seq(variableTaxProduct))

        val requestItemCaptor: ArgumentCaptor[Seq[PropertyRequestItem]] =
          ArgumentCaptor.forClass(classOf[Seq[PropertyRequestItem]])

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              requestItemCaptor.capture(),
              eqTo(DevicePlatform.Unknown),
              eqTo(chargeCurrency),
              eqTo(None),
              eqTo(Some(GiftCardRedeemRequest(2.0, 0.0))),
              eqTo(
                Some(
                  DiscountRequest(
                    None,
                    Some(List(CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None), None)))
                  )
                )
              ),
              eqTo(None),
              eqTo(null),
              eqTo(None),
              eqTo(false),
              eqTo(None),
              eqTo(None),
              any(),
              eqTo(Seq.empty),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              eqTo(setupBookingContext)
            )
          requestItemCaptor.getValue.head.propertySearchCriteria.pricingRequest
            .map(_.dfFeatureFlags) shouldBe Some(Seq.empty)
          result.properties.length shouldBe 1
        }
      }

      "not apply variable tax when have VariableTax feature when allGuestSameNationality = true" in {
        val variableTaxDFFlag    = 78
        val invalidCitizenshipId = "991319778899"
        setupRoomSwapTest(roomSwapPropertySearchCriteria, roomSwapPropertyRequestItem)

        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(setupBookingContext.bookingFlowType).thenReturn(SingleProperty)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.isRoomSwapEnabled).thenReturn(false)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session)
          .thenReturn(
            SetupBookingSessionContext(
              Map("key" -> mockPropertySetupBookingToken),
              Some(expectedPackagesToken),
              expectedTimeStamp
            )
          )
        val guest = mock[HotelGuest]
        val variableTaxPropertySearchCriteria = PropertySearchCriteria(
          propertyId = Some(1L),
          roomIdentifier = "",
          occupancyRequest = roomSwapOccupancyRequest,
          durationRequest = roomSwapDurationRequest,
          pricingRequest = Some(
            PricingRequest(
              isMse = false,
              requiredPrice = "",
              requiredBasis = "",
              isRPM2Included = false,
              selectedPointMaxId = None,
              isIncludeUsdAndLocalCurrency = false,
              dfFeatureFlags = Seq.empty,
              allowOverrideOccupancy = false,
              enableOpaqueChannel = false,
              isAllowRoomTypeNotGuarantee = false,
              synchronous = false,
              partnerLoyaltyProgramId = None
            )
          ),
          papiContextRequest = None,
          roomSelectionRequest = None,
          propertyRequest = None,
          simplifiedRoomSelectionRequest = None,
          partnerRequest = None
        )
        val variableTaxProduct = PropertyRequestItem(
          id = "",
          propertySearchCriteria = variableTaxPropertySearchCriteria,
          guests = Some(Seq(guest))
        )
        when(setupBookingRequest.enabledFeatures).thenReturn(Some(Seq("VariableTax")))
        when(guest.primary).thenReturn(true)
        when(guest.citizenshipId).thenReturn(Some(invalidCitizenshipId))
        when(guest.allGuestsSameNationality).thenReturn(Some(true))
        when(productsRequest.propertyRequests).thenReturn(Seq(variableTaxProduct))

        val requestItemCaptor: ArgumentCaptor[Seq[PropertyRequestItem]] =
          ArgumentCaptor.forClass(classOf[Seq[PropertyRequestItem]])

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              requestItemCaptor.capture(),
              eqTo(DevicePlatform.Unknown),
              eqTo(chargeCurrency),
              eqTo(None),
              eqTo(Some(GiftCardRedeemRequest(2.0, 0.0))),
              eqTo(
                Some(
                  DiscountRequest(
                    None,
                    Some(List(CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None), None)))
                  )
                )
              ),
              eqTo(None),
              eqTo(null),
              eqTo(None),
              eqTo(false),
              eqTo(None),
              eqTo(None),
              any(),
              eqTo(Seq.empty),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              eqTo(setupBookingContext)
            )
          requestItemCaptor.getValue.head.propertySearchCriteria.pricingRequest
            .map(_.dfFeatureFlags) shouldBe Some(Seq.empty)
          result.properties.length shouldBe 1
        }
      }

      "apply variable tax when have VariableTax feature when allGuestSameNationality = false" in {
        val variableTaxDFFlag    = 78
        val invalidCitizenshipId = "991319778899"
        setupRoomSwapTest(roomSwapPropertySearchCriteria, roomSwapPropertyRequestItem)

        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(setupBookingContext.bookingFlowType).thenReturn(SingleProperty)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.isRoomSwapEnabled).thenReturn(false)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session)
          .thenReturn(
            SetupBookingSessionContext(
              Map("key" -> mockPropertySetupBookingToken),
              Some(expectedPackagesToken),
              expectedTimeStamp
            )
          )
        val guest = mock[HotelGuest]
        val variableTaxPropertySearchCriteria = PropertySearchCriteria(
          propertyId = Some(1L),
          roomIdentifier = "",
          occupancyRequest = roomSwapOccupancyRequest,
          durationRequest = roomSwapDurationRequest,
          pricingRequest = Some(
            PricingRequest(
              isMse = false,
              requiredPrice = "",
              requiredBasis = "",
              isRPM2Included = false,
              selectedPointMaxId = None,
              isIncludeUsdAndLocalCurrency = false,
              dfFeatureFlags = Seq.empty,
              allowOverrideOccupancy = false,
              enableOpaqueChannel = false,
              isAllowRoomTypeNotGuarantee = false,
              synchronous = false,
              partnerLoyaltyProgramId = None
            )
          ),
          papiContextRequest = None,
          roomSelectionRequest = None,
          propertyRequest = None,
          simplifiedRoomSelectionRequest = None,
          partnerRequest = None
        )
        val variableTaxProduct = PropertyRequestItem(
          id = "",
          propertySearchCriteria = variableTaxPropertySearchCriteria,
          guests = Some(Seq(guest))
        )
        when(setupBookingRequest.enabledFeatures).thenReturn(Some(Seq("VariableTax")))
        when(guest.primary).thenReturn(true)
        when(guest.citizenshipId).thenReturn(Some(invalidCitizenshipId))
        when(guest.allGuestsSameNationality).thenReturn(Some(false))
        when(productsRequest.propertyRequests).thenReturn(Seq(variableTaxProduct))

        val requestItemCaptor: ArgumentCaptor[Seq[PropertyRequestItem]] =
          ArgumentCaptor.forClass(classOf[Seq[PropertyRequestItem]])

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              requestItemCaptor.capture(),
              eqTo(DevicePlatform.Unknown),
              eqTo(chargeCurrency),
              eqTo(None),
              eqTo(Some(GiftCardRedeemRequest(2.0, 0.0))),
              eqTo(
                Some(
                  DiscountRequest(
                    None,
                    Some(List(CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None), None)))
                  )
                )
              ),
              eqTo(None),
              eqTo(null),
              eqTo(None),
              eqTo(false),
              eqTo(None),
              eqTo(None),
              any(),
              eqTo(Seq.empty),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              eqTo(setupBookingContext)
            )
          requestItemCaptor.getValue.head.propertySearchCriteria.pricingRequest
            .map(_.dfFeatureFlags) shouldBe Some(Seq(variableTaxDFFlag))
          result.properties.length shouldBe 1
        }
      }

      "apply variable tax when have VariableTax feature, valid citizenshipId, allGuestSameNationality = false" in {
        val variableTaxDFFlag  = 78
        val validCitizenshipId = "991119778899"
        setupRoomSwapTest(roomSwapPropertySearchCriteria, roomSwapPropertyRequestItem)

        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(setupBookingContext.bookingFlowType).thenReturn(SingleProperty)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.isRoomSwapEnabled).thenReturn(false)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session)
          .thenReturn(
            SetupBookingSessionContext(
              Map("key" -> mockPropertySetupBookingToken),
              Some(expectedPackagesToken),
              expectedTimeStamp
            )
          )
        val guest = mock[HotelGuest]
        val variableTaxPropertySearchCriteria = PropertySearchCriteria(
          propertyId = Some(1L),
          roomIdentifier = "",
          occupancyRequest = roomSwapOccupancyRequest,
          durationRequest = roomSwapDurationRequest,
          pricingRequest = Some(
            PricingRequest(
              isMse = false,
              requiredPrice = "",
              requiredBasis = "",
              isRPM2Included = false,
              selectedPointMaxId = None,
              isIncludeUsdAndLocalCurrency = false,
              dfFeatureFlags = Seq.empty,
              allowOverrideOccupancy = false,
              enableOpaqueChannel = false,
              isAllowRoomTypeNotGuarantee = false,
              synchronous = false,
              partnerLoyaltyProgramId = None
            )
          ),
          papiContextRequest = None,
          roomSelectionRequest = None,
          propertyRequest = None,
          simplifiedRoomSelectionRequest = None,
          partnerRequest = None
        )
        val variableTaxProduct = PropertyRequestItem(
          id = "",
          propertySearchCriteria = variableTaxPropertySearchCriteria,
          guests = Some(Seq(guest))
        )
        when(setupBookingRequest.enabledFeatures).thenReturn(Some(Seq("VariableTax")))
        when(guest.primary).thenReturn(true)
        when(guest.citizenshipId).thenReturn(Some(validCitizenshipId))
        when(guest.allGuestsSameNationality).thenReturn(Some(false))
        when(productsRequest.propertyRequests).thenReturn(Seq(variableTaxProduct))

        val requestItemCaptor: ArgumentCaptor[Seq[PropertyRequestItem]] =
          ArgumentCaptor.forClass(classOf[Seq[PropertyRequestItem]])

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              requestItemCaptor.capture(),
              eqTo(DevicePlatform.Unknown),
              eqTo(chargeCurrency),
              eqTo(None),
              eqTo(Some(GiftCardRedeemRequest(2.0, 0.0))),
              eqTo(
                Some(
                  DiscountRequest(
                    None,
                    Some(List(CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None), None)))
                  )
                )
              ),
              eqTo(None),
              eqTo(null),
              eqTo(None),
              eqTo(false),
              eqTo(None),
              eqTo(None),
              any(),
              eqTo(Seq.empty),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              eqTo(setupBookingContext)
            )
          requestItemCaptor.getValue.head.propertySearchCriteria.pricingRequest
            .map(_.dfFeatureFlags) shouldBe Some(Seq(variableTaxDFFlag))
          result.properties.length shouldBe 1
        }
      }

      "apply variable tax when have VariableTax feature, invalid citizenshipId, allGuestSameNationality = false" in {
        val variableTaxDFFlag    = 78
        val invalidCitizenshipId = "991319778899"
        setupRoomSwapTest(roomSwapPropertySearchCriteria, roomSwapPropertyRequestItem)

        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(setupBookingContext.bookingFlowType).thenReturn(SingleProperty)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.isRoomSwapEnabled).thenReturn(false)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session)
          .thenReturn(
            SetupBookingSessionContext(
              Map("key" -> mockPropertySetupBookingToken),
              Some(expectedPackagesToken),
              expectedTimeStamp
            )
          )
        val guest = mock[HotelGuest]
        val variableTaxPropertySearchCriteria = PropertySearchCriteria(
          propertyId = Some(1L),
          roomIdentifier = "",
          occupancyRequest = roomSwapOccupancyRequest,
          durationRequest = roomSwapDurationRequest,
          pricingRequest = Some(
            PricingRequest(
              isMse = false,
              requiredPrice = "",
              requiredBasis = "",
              isRPM2Included = false,
              selectedPointMaxId = None,
              isIncludeUsdAndLocalCurrency = false,
              dfFeatureFlags = Seq.empty,
              allowOverrideOccupancy = false,
              enableOpaqueChannel = false,
              isAllowRoomTypeNotGuarantee = false,
              synchronous = false,
              partnerLoyaltyProgramId = None
            )
          ),
          papiContextRequest = None,
          roomSelectionRequest = None,
          propertyRequest = None,
          simplifiedRoomSelectionRequest = None,
          partnerRequest = None
        )
        val variableTaxProduct = PropertyRequestItem(
          id = "",
          propertySearchCriteria = variableTaxPropertySearchCriteria,
          guests = Some(Seq(guest))
        )
        when(setupBookingRequest.enabledFeatures).thenReturn(Some(Seq("VariableTax")))
        when(guest.primary).thenReturn(true)
        when(guest.citizenshipId).thenReturn(Some(invalidCitizenshipId))
        when(guest.allGuestsSameNationality).thenReturn(None)
        when(productsRequest.propertyRequests).thenReturn(Seq(variableTaxProduct))

        val requestItemCaptor: ArgumentCaptor[Seq[PropertyRequestItem]] =
          ArgumentCaptor.forClass(classOf[Seq[PropertyRequestItem]])

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              requestItemCaptor.capture(),
              eqTo(DevicePlatform.Unknown),
              eqTo(chargeCurrency),
              eqTo(None),
              eqTo(Some(GiftCardRedeemRequest(2.0, 0.0))),
              eqTo(
                Some(
                  DiscountRequest(
                    None,
                    Some(List(CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None), None)))
                  )
                )
              ),
              eqTo(None),
              eqTo(null),
              eqTo(None),
              eqTo(false),
              eqTo(None),
              eqTo(None),
              any(),
              eqTo(Seq.empty),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              eqTo(setupBookingContext)
            )
          requestItemCaptor.getValue.head.propertySearchCriteria.pricingRequest
            .map(_.dfFeatureFlags) shouldBe Some(Seq(variableTaxDFFlag))
          result.properties.length shouldBe 1
        }
      }

      "not apply variable tax when have no VariableTax feature" in {
        setupRoomSwapTest(roomSwapPropertySearchCriteria, roomSwapPropertyRequestItem)

        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(setupBookingContext.bookingFlowType).thenReturn(SingleProperty)
        when(requestContext.featureAware).thenReturn(Some(featureAware))
        when(featureAware.isRoomSwapEnabled).thenReturn(false)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session)
          .thenReturn(
            SetupBookingSessionContext(
              Map("key" -> mockPropertySetupBookingToken),
              Some(expectedPackagesToken),
              expectedTimeStamp
            )
          )
        val variableTaxPropertySearchCriteria = PropertySearchCriteria(
          propertyId = Some(1L),
          roomIdentifier = "",
          occupancyRequest = roomSwapOccupancyRequest,
          durationRequest = roomSwapDurationRequest,
          pricingRequest = Some(
            PricingRequest(
              isMse = false,
              requiredPrice = "",
              requiredBasis = "",
              isRPM2Included = false,
              selectedPointMaxId = None,
              isIncludeUsdAndLocalCurrency = false,
              dfFeatureFlags = Seq.empty,
              allowOverrideOccupancy = false,
              enableOpaqueChannel = false,
              isAllowRoomTypeNotGuarantee = false,
              synchronous = false,
              partnerLoyaltyProgramId = None
            )
          ),
          papiContextRequest = None,
          roomSelectionRequest = None,
          propertyRequest = None,
          simplifiedRoomSelectionRequest = None,
          partnerRequest = None
        )
        val variableTaxProduct = PropertyRequestItem(
          id = "",
          propertySearchCriteria = variableTaxPropertySearchCriteria,
          guests = Some(Seq.empty)
        )
        when(setupBookingRequest.enabledFeatures).thenReturn(Some(Seq.empty))
        when(productsRequest.propertyRequests).thenReturn(Seq(variableTaxProduct))

        val requestItemCaptor: ArgumentCaptor[Seq[PropertyRequestItem]] =
          ArgumentCaptor.forClass(classOf[Seq[PropertyRequestItem]])

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              requestItemCaptor.capture(),
              eqTo(DevicePlatform.Unknown),
              eqTo(chargeCurrency),
              eqTo(None),
              eqTo(Some(GiftCardRedeemRequest(2.0, 0.0))),
              eqTo(
                Some(
                  DiscountRequest(
                    None,
                    Some(List(CampaignInfoInternal(CampaignInfo(1, 0, "AGODA20", DiscountType.None, 0, None), None)))
                  )
                )
              ),
              eqTo(None),
              eqTo(null),
              eqTo(None),
              eqTo(false),
              eqTo(None),
              eqTo(None),
              any(),
              eqTo(Seq.empty),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              eqTo(setupBookingContext)
            )
          requestItemCaptor.getValue.head.propertySearchCriteria.pricingRequest
            .map(_.dfFeatureFlags) shouldBe Some(Seq.empty)
          result.properties.length shouldBe 1
        }
      }

      "contain walletPromotions" in {
        mockPropertyServiceRetrieveProperties(Seq[BookingPropertiesData](bookingPropertyProduct()))
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session)
          .thenReturn(
            SetupBookingSessionContext(
              Map("key" -> mockPropertySetupBookingToken),
              Some(expectedPackagesToken),
              expectedTimeStamp
            )
          )
        when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(totalSavings))

        productsFacade.composeProductData(setupBookingRequest, usdCurrency).map { result =>
          val cmsData = PromoCmsData(id = 1, params = Map("abc" -> "test"), targetField = 13)
          result.walletPromotions shouldBe Seq(
            WalletPromotion(
              campaignId = 12345,
              cid = 9999,
              promotionCode = "CODE",
              value = 10.0,
              discountType = 1,
              currencyCode = None,
              displayName = "Promotion",
              cms = Some(List(cmsData)),
              ineligibleReason = Some(5),
              promoValidationType = Some(1),
              campaignType = Some(CampaignType.PropertyPromotionCode),
              isSelected = Some(true)
            )
          )
        }
      }

      "contain campaigns" in {
        val campaignPromotionInfo = CampaignPromotionInfo(
          campaignType = Some(CampaignType.PropertyPromotionCode),
          campaignId = 1234,
          cid = 45678,
          promotionCode = "AGODA",
          campaignName = "AGODASALE Discount",
          campaignDiscountType = Some(CampaignDiscountType.Percent),
          originalDiscountPercentage = Some(15),
          originalDiscountAmount = None,
          originalDiscountCurrencyCode = None,
          validDateType = Some(CampaignValidDateType.BookingDate),
          dateValidFrom = Some(DateTime.parse("2024-01-01T00:00:00.000+07:00")),
          dateValidUntil = Some(DateTime.parse("2025-11-30T23:59:59.999+07:00")),
          isAutoApply = Some(true),
          isAutoApplyBookingForm = Some(false),
          inapplicableReasonString = None,
          inapplicableReason = None,
          isStateIdRequired = None,
          promotionCodeType = Some(PromotionCodeType.MultiUse),
          status = Some(CampaignStatusType.Selected),
          cms = Some(List(PromoCmsData(1, Map("amount" -> "10"), 13))),
          messages = Some(basePromotionInfoMessage()),
          campaignMessages = Some(
            Map(
              1 -> CampaignMessage(message = "ABC", params = Map("urgencyType" -> "1")),
              2 -> CampaignMessage(message = "DEF", params = Map("urgencyType" -> "2"))
            )
          )
        )
        mockPropertyServiceRetrieveProperties(Seq[BookingPropertiesData](bookingPropertyProduct()))
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
        when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
        when(setupBookingContext.session)
          .thenReturn(
            SetupBookingSessionContext(
              Map("key" -> mockPropertySetupBookingToken),
              Some(expectedPackagesToken),
              expectedTimeStamp
            )
          )
        when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(totalSavings))
        mockPriceBreakdownServiceGetPriceBreakdown()

        when(
          mockCampaignService.getPromotionInfo(any(), any(), any(), any(), any())(any())
        ).thenReturn(
          PromotionInfo(
            maximumPromotionCodeDiscount = None,
            maximumCreditCardDiscount = None,
            campaigns = Seq(campaignPromotionInfo)
          )
        )

        productsFacade.composeProductData(setupBookingRequest, usdCurrency).map { result =>
          result.campaignPromotionInfo.map(_.campaigns) shouldBe Some(Seq(campaignPromotionInfo))
        }
      }

      val isPromotionCodeEligibleTestCases = Table(
        ("isPromotionCodeEligible", "HidePromoBoxWhenNoCouponAvailable", "isChildRoomPromotionCodeEligible"),
        (true, true, true),
        (false, true, false),
        (true, false, true),
        (false, false, false)
      )

      forEvery(isPromotionCodeEligibleTestCases) {
        (isPromotionCodeEligible, isHidePromoBoxWhenNoCouponAvailable, isChildRoomPromotionCodeEligible) =>
          {
            val featureText = if (isHidePromoBoxWhenNoCouponAvailable) "enabled" else "disabled"
            s"""
             | return isPromotionCodeEligible as $isPromotionCodeEligible
             |    when HidePromoBoxWhenNoCouponAvailable is $featureText
             |    and isChildRoomPromotionCodeEligible is $isChildRoomPromotionCodeEligible
             |""".stripMargin in {
              val properties = baseProperties().copy(property =
                Seq(
                  baseProperty().copy(masterRooms =
                    Seq(masterRoomWithChildRoomsWherePromotionEligible(isChildRoomPromotionCodeEligible))
                  )
                )
              )
              val product = bookingPropertyProduct().copy(papiProperties = Some(properties))
              mockPropertyServiceRetrieveProperties(Seq[BookingPropertiesData](product))
              when(mockCampaignService.getCampaignInfo(any(), any(), any())(any(), any()))
                .thenReturn(
                  Future.successful(
                    Seq(CampaignInfoInternal(CampaignInfo(5, 1, "TEST", DiscountType.Percent, 5.0, None), None))
                  )
                )
              when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
              when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
              when(setupBookingContext.session)
                .thenReturn(
                  SetupBookingSessionContext(
                    Map("key" -> mockPropertySetupBookingToken),
                    Some(expectedPackagesToken),
                    expectedTimeStamp
                  )
                )
              when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(totalSavings))
              when(setupBookingContext.requestContext).thenReturn(requestContext)
              when(requestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
              when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Rurubu)
              when(
                whiteLabelInfo.isFeatureEnabled(
                  eqTo(WhiteLabelFeatureName.HidePromoBoxWhenNoCouponAvailable),
                  any(),
                  any(),
                  any()
                )
              ).thenReturn(isHidePromoBoxWhenNoCouponAvailable)
              productsFacade.composeProductData(setupBookingRequest, "JPY").map { result =>
                result.isPromotionCodeEligible shouldBe isPromotionCodeEligible
              }
            }
          }
      }
    }

    "single vehicle flow" should {
      forEvery(supportPriceDisplayTypesTestCases) { isUNIBF2454 =>
        {
          s"compose car product data correct when UNIBF-2454 is $isUNIBF2454" in {
            val mockCarOption      = mock[CarOptions]
            val customerPolicyInfo = mock[VehicleCustomerPolicyInfo]
            val mockCarConfirmationData = CarConfirmationData(
              "1",
              "content",
              None,
              true,
              true,
              mockCarOption,
              None,
              Some(customerPolicyInfo),
              Some(CarCancellationPolicyType.NONREFUNDABLE)
            )
            val mockCurrencyPrice  = mock[CarCurrencyPrice]
            val mockCarRequestItem = mock[CarRequestItem]
            val chargeCurrency     = thbCurrency

            when(featureAware.supportPriceDisplayTypes).thenReturn(isUNIBF2454)
            when(mockCarOption.price).thenReturn(Seq(mockCurrencyPrice))
            when(mockCarOption.paymentModel).thenReturn(PaymentModel.MerchantCommission)
            when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleVehicle)
            when(setupBookingContext.requestContext).thenReturn(requestContext)
            when(requestContext.correlationId).thenReturn(Some("id"))
            when(mockPriceBreakdownService.getPriceBreakdownForCar(any(), any(), any()))
              .thenReturn(Future.successful(priceBreakdown))
            when(carService.getCarConfirmationData(any(), any(), any(), any())(any()))
              .thenReturn(Future.successful(mockCarConfirmationData))
            when(setupBookingRequest.productsRequest)
              .thenReturn(productsRequest)
            when(productsRequest.getCarRequestOpt)
              .thenReturn(Option(Seq(mockCarRequestItem)))
            when(productsRequest.activityRequests)
              .thenReturn(None)
            productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
              verify(carService, times(1))
                .getCarConfirmationData(any(), any(), any(), any())(any())
              verify(mockPriceBreakdownService, times(1))
                .getPriceBreakdownForCar(chargeCurrency, Seq(mockCurrencyPrice), PaymentModel.MerchantCommission)

              result.cars.length shouldBe 1
              result.totalPriceDisplay.isDefined shouldBe true
              if (isUNIBF2454)
                result.priceDisplayType shouldBe Some(PriceDisplayType.Unknown)
              else
                result.priceDisplayType shouldBe None
            }
          }
          s"compose car product data correct with cart breakdown experiment when UNIBF-2454 is $isUNIBF2454" in {
            val mockCarOption      = mock[CarOptions]
            val customerPolicyInfo = mock[VehicleCustomerPolicyInfo]
            val mockCarConfirmationData = CarConfirmationData(
              "1",
              "content",
              None,
              true,
              true,
              mockCarOption,
              None,
              Some(customerPolicyInfo),
              Some(CarCancellationPolicyType.NONREFUNDABLE)
            )
            val mockCurrencyPrice  = mock[CarCurrencyPrice]
            val mockCarRequestItem = mock[CarRequestItem]
            val chargeCurrency     = thbCurrency

            when(featureAware.supportPriceDisplayTypes).thenReturn(isUNIBF2454)
            when(mockCarOption.price).thenReturn(Seq(mockCurrencyPrice))
            when(mockCarOption.paymentModel).thenReturn(PaymentModel.MerchantCommission)
            when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleVehicle)
            when(setupBookingContext.requestContext).thenReturn(requestContext)
            when(requestContext.correlationId).thenReturn(Some("id"))
            when(requestContext.featureAware).thenReturn(Some(featureAware))
            when(featureAware.useCartBreakdownForSingleVehicle).thenReturn(true)
            when(
              mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(any())
            )
              .thenReturn(Future.successful(priceBreakdown))
            when(carService.getCarConfirmationData(any(), any(), any(), any())(any()))
              .thenReturn(Future.successful(mockCarConfirmationData))
            when(setupBookingRequest.productsRequest)
              .thenReturn(productsRequest)
            when(productsRequest.getCarRequestOpt)
              .thenReturn(Option(Seq(mockCarRequestItem)))
            when(productsRequest.activityRequests)
              .thenReturn(None)
            when(productsRequest.priceDisplayVersion)
              .thenReturn(None)
            clearInvocations(mockPriceBreakdownService)
            productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
              verify(carService, times(1))
                .getCarConfirmationData(any(), any(), any(), any())(any())
              verify(mockPriceBreakdownService, times(1))
                .getPriceBreakdownForCartFromNonUpi(any(), any(), any(), any(), any(), any(), any(), any(), any())(
                  any()
                )

              result.cars.length shouldBe 1
              result.totalPriceDisplay.isDefined shouldBe true
              if (isUNIBF2454)
                result.priceDisplayType shouldBe Some(PriceDisplayType.Unknown)
              else
                result.priceDisplayType shouldBe None
            }
          }
        }
      }
    }

    "single activity flow" should
      forEvery(supportPriceDisplayTypesTestCases) { isUNIBF2454 =>
        {
          s"compose car product data correct when UNIBF-2454 is $isUNIBF2454" in {
            val chargeCurrency = thbCurrency
            val activityRequestItem =
              ActivityRequestItem(
                "some id",
                ActivityConfirmPriceRequest("some token", "some redeem token")
              )

            when(featureAware.supportPriceDisplayTypes).thenReturn(isUNIBF2454)
            when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleActivity)
            when(setupBookingContext.requestContext).thenReturn(requestContext)
            when(requestContext.correlationId).thenReturn(Some("id"))
            when(mockActivityService.getActivityConfirmationData(any(), any(), any(), any(), any(), any())(any()))
              .thenReturn(Future.successful(mockedActivityConfirmationDataEmpty))
            when(
              mockPriceBreakdownService.getPriceBreakdownForActivity(chargeCurrency, None, isPmcWidgetEnabled = false)
            )
              .thenReturn(Future.successful(priceBreakdown))
            when(setupBookingRequest.productsRequest)
              .thenReturn(productsRequest)
            when(productsRequest.activityRequests)
              .thenReturn(Option(Seq(activityRequestItem)))
            clearInvocations(mockPriceBreakdownService)
            productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
              verify(mockActivityService, times(1)).getActivityConfirmationData(
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(
                any()
              )
              verify(mockPriceBreakdownService, times(1))
                .getPriceBreakdownForActivity(chargeCurrency, None, isPmcWidgetEnabled = false)

              result.activities.length shouldBe 1
              result.totalPriceDisplay.isDefined shouldBe true
              if (isUNIBF2454)
                result.priceDisplayType shouldBe Some(PriceDisplayType.Unknown)
              else
                result.priceDisplayType shouldBe None
            }
          }
        }
      }

    "Multi hotel flow with data" should {
      forEvery(supportPriceDisplayTypesTestCases) { isUNIBF2454 =>
        {
          s"have price breakdown and total savings in Product Data when UNIBF-2454 is $isUNIBF2454" in {
            val packageTokenWithFirstPropertyCall  = Some(PackageRequest("client1", Some("system1")))
            val packageTokenWithSecondPropertyCall = Some(PackageRequest("client2", Some("system2")))
            val paymentRequest                     = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
            when(featureAware.supportPriceDisplayTypes).thenReturn(isUNIBF2454)
            when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))

            mockPropertyServiceRetrieveProperties(
              Seq[BookingPropertiesData](
                bookingPropertyProduct().copy(packageRequest = packageTokenWithFirstPropertyCall)
              ),
              Seq[BookingPropertiesData](
                bookingPropertyProduct().copy(id = "2", packageRequest = packageTokenWithSecondPropertyCall)
              )
            )
            when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.MultiHotel)
            when(
              mockPriceBreakdownService.getPriceBreakdownFromPackagingPrice(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(any())
            ).thenReturn(Future.successful(priceBreakdown))
            when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
            when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))

            when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

            val durationRequest = mock[DurationRequest]
            when(durationRequest.lengthOfStay).thenReturn(2)
            when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

            when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
            when(mockPropertyRequest.guests).thenReturn(None)
            val pricingRequest1 = mock[PricingRequest]
            when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
            when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)

            productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
              verify(propertyService, times(2))
                .retrieveProperties(
                  any(),
                  any(),
                  eqTo(chargeCurrency),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  eqTo(Some(paymentRequest)),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any()
                )(any())

              result.properties.length shouldBe 2
              result.totalPriceDisplay.get.value.get.amount.amount shouldBe 2000d
              if (isUNIBF2454)
                result.priceDisplayType shouldBe Some(PriceDisplayType.Unknown)
              else
                result.priceDisplayType shouldBe None
              result.packageToken shouldBe packageTokenWithSecondPropertyCall
              result.properties.map(_.id) should contain theSameElementsAs Seq("1", "2")
            }
          }
        }
      }

      "Does not have price breakdown and total savings in Product Data if number of child room received dont match" in {
        val packageTokenWithFirstPropertyCall  = None
        val packageTokenWithSecondPropertyCall = Some(PackageRequest("client2", Some("system2")))
        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        mockPropertyServiceRetrieveProperties(
          Seq[BookingPropertiesData](
            bookingPropertyProductWithNoMasterRoom().copy(packageRequest = packageTokenWithFirstPropertyCall)
          ),
          Seq[BookingPropertiesData](
            bookingPropertyProduct().copy(id = "2", packageRequest = packageTokenWithSecondPropertyCall)
          )
        )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.MultiHotel)
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))

        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.guests).thenReturn(None)
        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(2))
            .retrieveProperties(
              any(),
              any(),
              eqTo(chargeCurrency),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              any()
            )

          result.properties.length shouldBe 2
          result.totalPriceDisplay shouldBe None
          result.priceDisplayType shouldBe Some(PriceDisplayType.Unknown)
          result.packageToken shouldBe packageTokenWithSecondPropertyCall
          result.properties.map(_.id) should contain theSameElementsAs Seq("1", "2")
        }
      }

      "Does not have price breakdown and total savings in Product Data if number of master room received dont match" in {
        val packageTokenWithFirstPropertyCall  = None
        val packageTokenWithSecondPropertyCall = Some(PackageRequest("client2", Some("system2")))
        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        mockPropertyServiceRetrieveProperties(
          Seq[BookingPropertiesData](
            bookingPropertyProductWithMasterRoomNoChildRoom()
              .copy(packageRequest = packageTokenWithFirstPropertyCall)
          ),
          Seq[BookingPropertiesData](
            bookingPropertyProduct().copy(id = "2", packageRequest = packageTokenWithSecondPropertyCall)
          )
        )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.MultiHotel)
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))

        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.guests).thenReturn(None)
        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(2))
            .retrieveProperties(
              any(),
              any(),
              eqTo(chargeCurrency),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              any()
            )

          result.properties.length shouldBe 2
          result.totalPriceDisplay shouldBe None
          result.priceDisplayType shouldBe Some(PriceDisplayType.Unknown)
          result.packageToken shouldBe packageTokenWithSecondPropertyCall
          result.properties.map(_.id) should contain theSameElementsAs Seq("1", "2")
        }
      }

    }

    "Cart flow with data" should {
      when(mockExternalLoyaltyService.distributePoints(any(), any())).thenReturn(Future.successful(None))
      when(mockPartnerPromoService.getPartnerPromoUtils(any[WhiteLabelInfo])(any[RequestContext]))
        .thenReturn(DefaultPartnerPromoUtils)

      case class VehicleIdMatcher(vehicleId: String) extends ArgumentMatcher[Option[CarRequestItem]] {
        override def matches(argument: Option[CarRequestItem]): Boolean =
          argument != null && argument.exists { carRequest =>
            carRequest.id == vehicleId
          }
      }

      def mockCarService(vehicleId: String = "", carConfirmationData: CarConfirmationData) =
        when(
          carService.getCarConfirmationData(
            ArgumentMatchers.argThat(VehicleIdMatcher(vehicleId)),
            any(),
            any(),
            any()
          )(any())
        ).thenReturn(Future.successful(carConfirmationData))

      val mockCarConfirmationData = CarConfirmationData(
        "1",
        "content",
        None,
        isCompleted = true,
        hasContent = true,
        mock[CarOptions],
        None,
        None
      )

      case class FlightIdMatcher(flightId: String) extends ArgumentMatcher[FlightRequestItem] {
        override def matches(argument: FlightRequestItem): Boolean =
          if (argument != null && argument.id.nonEmpty && argument.id.getOrElse("").equals(flightId)) true
          else false
      }

      def mockFlightRepository(flightId: String = "", flightProductItem1: FlightConfirmationData) = {
        when(
          flightRepository
            .retrieveFlightConfirmationData(
              ArgumentMatchers.argThat(FlightIdMatcher(flightId)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
        ).thenReturn(Future.successful(flightProductItem1))

        when(
          flightRepository
            .retrieveFlightItineraryDetails(
              ArgumentMatchers.argThat(FlightIdMatcher(flightId)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
        ).thenReturn(Future.successful(flightProductItem1))
      }

      def mockFlightData(
          flightConfirmationData: FlightConfirmationData,
          flightItineraryDetails: FlightConfirmationData
      ): Unit = {
        when(
          flightRepository
            .retrieveFlightConfirmationData(
              ArgumentMatchers.argThat(FlightIdMatcher(flightConfirmationData.id)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
        ).thenReturn(Future.successful(flightConfirmationData))

        when(
          flightRepository
            .retrieveFlightItineraryDetails(
              ArgumentMatchers.argThat(FlightIdMatcher(flightItineraryDetails.id)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
        ).thenReturn(Future.successful(flightItineraryDetails))
      }

      def mockMultiplePropertiesWithoutCart() = {
        val childrenRoomsWithoutCart: EnrichedChildRoom =
          baseChildrenRoom().modify(_.cart).setTo(None)
        val propertyProduct = bookingPropertyProduct()
          .modifyAll(
            _.papiProperties.each.property.each.masterRooms.each.childrenRooms.each,
            _.papiProperties.each.property.each.rooms.each
          )
          .setTo(childrenRoomsWithoutCart)
        mockPropertyServiceRetrieveProperties(
          Seq[BookingPropertiesData](
            propertyProduct.copy(id = "1")
          ),
          Seq[BookingPropertiesData](
            propertyProduct.copy(id = "2")
          )
        )
      }

      val flightConfirmationData =
        FlightConfirmationData(
          id = "3",
          token = "",
          submitToken = mock[FlightToken],
          isCompleted = false,
          hasFlight = true,
          hasContent = false,
          isHackerFare = false,
          packageRequest = None,
          flightPricing = Some(MockFlightsPricingData.searchResponseCurrencyPricing),
          priceChange = Some(MockFlightsPricingData.priceChangeResponse),
          flightItinerary = None,
          paxNumberByType = Map.empty
        )

      forEvery(supportPriceDisplayTypesTestCases) { isUNIBF2454 =>
        {
          s"have price breakdown and total savings in Product Data when UNIBF-2454 is $isUNIBF2454" in {
            val propertyCartProduct = BookingMockHelper.cartProduct.modify(_.info.`type`).setTo(ItemEntries.Property)
            val childrenRoomsWithPropertyCartProduct: EnrichedChildRoom =
              baseChildrenRoom().modify(_.cart.each.products).setTo(Seq(propertyCartProduct, propertyCartProduct))

            val propertyProduct = bookingPropertyProduct()
              .modifyAll(
                _.papiProperties.each.property.each.masterRooms.each.childrenRooms.each,
                _.papiProperties.each.property.each.rooms.each
              )
              .setTo(childrenRoomsWithPropertyCartProduct)
            val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
            when(featureAware.supportPriceDisplayTypes).thenReturn(isUNIBF2454)
            when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
            when(setupBookingRequest.productsRequest.activityRequests).thenReturn(Option.empty)
            when(setupBookingRequest.productsRequest.flightRequests).thenReturn(Seq.empty)
            when(setupBookingRequest.loyaltyRequest).thenReturn(None)
            mockPropertyServiceRetrieveProperties(
              Seq[BookingPropertiesData](
                propertyProduct
              ),
              Seq[BookingPropertiesData](
                propertyProduct.copy(id = "2")
              )
            )
            when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
            when(
              mockPriceBreakdownService.getPriceBreakdownForCartFromUpi(
                any(),
                any(),
                any(),
                any(),
                any()
              )
            ).thenReturn(Future.successful(cartPriceBreakdown))
            when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
            when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))
            when(productsRequest.carRequestsOpt).thenReturn(None)

            when(setupBookingRequest.productsRequest.getCartToken).thenReturn(Some(cartToken))
            when(setupBookingRequest.productsRequest.cartPricingContext).thenReturn(None)
            when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

            val durationRequest = mock[DurationRequest]
            when(durationRequest.lengthOfStay).thenReturn(2)
            when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

            when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
            when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
            when(mockPropertyRequest.guests).thenReturn(None)
            val pricingRequest1 = mock[PricingRequest]
            when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
            when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
            when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria))
              .thenReturn(mockPropertyRequest)
            when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
            when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)
            productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
              verify(propertyService, times(2))
                .retrieveProperties(
                  any(),
                  any(),
                  eqTo(chargeCurrency),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  eqTo(Some(paymentRequest)),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any()
                )(any())
              verify(mockReporter, times(1))
                .report(
                  any(),
                  any(),
                  eqTo(Map("exceeded" -> "false"))
                )

              result.properties.length shouldBe 2
              result.totalPriceDisplay.get.value.get.amount.amount shouldBe 1000d
              if (isUNIBF2454)
                result.priceDisplayType shouldBe Some(PriceDisplayType.MultiProduct)
              else
                result.priceDisplayType shouldBe None
              result.cart shouldBe Some(CartResponse(Some(cartToken)))
              result.properties.map(_.id) should contain theSameElementsAs Seq("1", "2")
            }
          }
        }
      }

      "have price breakdown in Product Data from Upi for properties,flight product and from NonUpi for activities product" in {
        reset(mockPriceBreakdownService)
        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        val durationRequest = mock[DurationRequest]
        val pricingRequest1 = mock[PricingRequest]
        // Prepare data
        val propertyCartProduct = BookingMockHelper.cartProduct.modify(_.info.`type`).setTo(ItemEntries.Property)
        val childrenRoomsWithPropertyCartProduct: EnrichedChildRoom =
          baseChildrenRoom().modify(_.cart.each.products).setTo(Seq(propertyCartProduct, propertyCartProduct))

        val propertyProductId1 = "1"
        val propertyProductId2 = "2"
        val propertyProduct = bookingPropertyProduct()
          .modifyAll(
            _.papiProperties.each.property.each.masterRooms.each.childrenRooms.each,
            _.papiProperties.each.property.each.rooms.each
          )
          .setTo(childrenRoomsWithPropertyCartProduct)
        val propertyProduct1 = propertyProduct.copy(id = propertyProductId1)
        val propertyProduct2 = propertyProduct.copy(id = propertyProductId2)

        val activityId = "activity-1"
        val activityConfirmPriceRequest = ActivityConfirmPriceRequest(
          activityToken = "some token",
          redeemLoyaltySplitTenderId = "some redeem token"
        )
        val activityRequestItem = ActivityRequestItem(
          id = activityId,
          confirmPriceRequest = activityConfirmPriceRequest
        )

        val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))

        // Setup booking context
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)

        // Product Request
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(setupBookingRequest.loyaltyRequest).thenReturn(None)

        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))
        when(productsRequest.flightRequests).thenReturn(Seq.empty)
        when(productsRequest.activityRequests).thenReturn(Option(Seq(activityRequestItem, activityRequestItem)))
        when(productsRequest.carRequestsOpt).thenReturn(None)

        when(productsRequest.getCartToken).thenReturn(Some(cartToken))
        when(productsRequest.cartPricingContext).thenReturn(None)
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))

        // Property Product
        when(mockPropertyRequest.guests).thenReturn(None)
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(mockPropertyRequest)

        mockPropertyServiceRetrieveProperties(
          Seq[BookingPropertiesData](propertyProduct1),
          Seq[BookingPropertiesData](propertyProduct2)
        )

        // Activity Service
        when(mockActivityService.getActivityConfirmationData(any(), any(), any(), any(), any(), any())(any()))
          .thenReturn(Future.successful(mockedActivityConfirmationData))

        // Campaign Service

        // PriceBreakdownService
        when(
          mockPriceBreakdownService.getPriceBreakdownForCart(any(), any(), any(), any(), any(), any(), any())(
            any()
          )
        )
          .thenReturn(
            Future.successful(cartPriceBreakdown)
          )

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          // Verify
          verify(mockReporter, times(1)).report(any(), any(), eqTo(Map("exceeded" -> "false")))

          verify(propertyService, times(2))
            .retrieveProperties(
              any(),
              any(),
              eqTo(chargeCurrency),
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Some(paymentRequest)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())

          verify(mockActivityService, times(2)).getActivityConfirmationData(any(), any(), any(), any(), any(), any())(
            any()
          )

          verify(mockPriceBreakdownService, times(1)).getPriceBreakdownForCart(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())

          verify(mockPriceBreakdownService, times(0)).getPriceBreakdownForCartFromUpi(
            any(),
            any(),
            any(),
            any(),
            any()
          )

          verify(mockPriceBreakdownService, times(0)).getPriceBreakdownForCartFromNonUpi(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())

          // Assertion
          result.totalPriceDisplay.isDefined shouldBe true
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 1000d
          result.priceDisplayType shouldBe Some(PriceDisplayType.MultiProduct)

          result.properties.length shouldBe 2
          result.flights.length shouldBe 0
          result.activities.length shouldBe 2
          result.cars.length shouldBe 0

          result.cart shouldBe Some(CartResponse(Some(cartToken)))
          result.properties.map(_.id) should contain theSameElementsAs Seq(propertyProductId1, propertyProductId2)
        }
      }

      "have price breakdown and total savings in Product Data for non upi cart only properties" in {
        val emptyCartToken = None
        val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))

        val bookingProperty = bookingPropertyProduct()
          .modifyAll(
            _.papiProperties.each.property.each.masterRooms.each.childrenRooms.each.cart,
            _.papiProperties.each.property.each.rooms.each.cart
          )
          .setTo(None)

        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        when(setupBookingRequest.productsRequest.activityRequests).thenReturn(Option.empty)
        when(setupBookingRequest.productsRequest.flightRequests).thenReturn(Seq.empty)
        mockPropertyServiceRetrieveProperties(
          Seq[BookingPropertiesData](
            bookingProperty
          ),
          Seq[BookingPropertiesData](
            bookingProperty.copy(id = "2")
          )
        )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(setupBookingContext.whiteLabelInfo).thenReturn(
          WhiteLabelInfo(whiteLabelId = WhiteLabel.CitiUS, feature = featuresConfiguration())
        )
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))
        when(productsRequest.carRequestsOpt).thenReturn(None)

        when(setupBookingRequest.productsRequest.getCartToken).thenReturn(emptyCartToken)

        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.guests).thenReturn(None)
        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(mockPropertyRequest)
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
        ).thenReturn(Future.successful(cartPriceBreakdown))

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(2))
            .retrieveProperties(
              any(),
              any(),
              eqTo(chargeCurrency),
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Some(paymentRequest)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
          verify(mockReporter, times(1))
            .report(
              any(),
              any(),
              eqTo(Map("exceeded" -> "false"))
            )

          result.properties.length shouldBe 2
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 1000d
          result.priceDisplayType shouldBe Some(PriceDisplayType.MultiProduct)
          result.cart shouldBe emptyCartToken
          result.properties.map(_.id) should contain theSameElementsAs Seq("1", "2")
        }
      }

      "have price breakdown and total savings in Product Data for non upi cart only activity" in {
        val activityRequestItem =
          ActivityRequestItem(
            "some id",
            ActivityConfirmPriceRequest("some token", "some redeem token")
          )
        val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))

        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(productsRequest.activityRequests).thenReturn(Option(Seq(activityRequestItem, activityRequestItem)))
        when(productsRequest.carRequestsOpt).thenReturn(Option(Seq()))
        when(productsRequest.flightRequests).thenReturn(Seq())

        when(mockActivityService.getActivityConfirmationData(any(), any(), any(), any(), any(), any())(any()))
          .thenReturn(Future.successful(mockedActivityConfirmationData))

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(mockActivityService, times(2)).getActivityConfirmationData(any(), any(), any(), any(), any(), any())(
            any()
          )
          verify(mockReporter, times(1)).report(any(), any(), eqTo(Map("exceeded" -> "false")))

          result.totalPriceDisplay.isDefined shouldBe true
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 1000d
          result.priceDisplayType shouldBe Some(PriceDisplayType.MultiProduct)
          result.cart shouldBe None
          result.activities.length shouldBe 2
          result.properties.length shouldBe 0
          result.cars.length shouldBe 0
          result.flights.length shouldBe 0
        }
      }

      "Does not have price breakdown and total savings in Product Data if number of child room received dont match" in {
        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        when(setupBookingRequest.loyaltyRequest).thenReturn(Some(LoyaltyRequest()))
        when(setupBookingRequest.productsRequest.activityRequests).thenReturn(Option.empty)
        when(setupBookingRequest.productsRequest.flightRequests).thenReturn(Seq.empty)
        mockPropertyServiceRetrieveProperties(
          Seq[BookingPropertiesData](
            bookingPropertyProductWithNoMasterRoom()
          ),
          Seq[BookingPropertiesData](
            bookingPropertyProductWithNoMasterRoom().copy(id = "2")
          )
        )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))
        when(productsRequest.carRequestsOpt).thenReturn(None)

        when(setupBookingRequest.productsRequest.getCartToken).thenReturn(Some(cartToken))

        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.guests).thenReturn(None)
        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(mockPropertyRequest)
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(2))
            .retrieveProperties(
              any(),
              any(),
              eqTo(chargeCurrency),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              any()
            )

          result.properties.length shouldBe 2
          result.totalPriceDisplay shouldBe None
          result.priceDisplayType shouldBe Some(PriceDisplayType.MultiProduct)
          result.cart shouldBe None
          result.properties.map(_.id) should contain theSameElementsAs Seq("1", "2")
        }
      }

      "Does not have price breakdown and total savings in Product Data if number of master room received dont match" in {
        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        when(setupBookingRequest.productsRequest.activityRequests).thenReturn(Option.empty)
        when(setupBookingRequest.productsRequest.flightRequests).thenReturn(Seq.empty)
        mockPropertyServiceRetrieveProperties(
          Seq[BookingPropertiesData](
            bookingPropertyProductWithMasterRoomNoChildRoom()
          ),
          Seq[BookingPropertiesData](
            bookingPropertyProductWithMasterRoomNoChildRoom().copy(id = "2")
          )
        )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))
        when(productsRequest.carRequestsOpt).thenReturn(Option(Seq.empty))
        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

        when(setupBookingRequest.productsRequest.getCartToken).thenReturn(Some(cartToken))

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.guests).thenReturn(None)
        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(mockPropertyRequest)
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(2))
            .retrieveProperties(
              any(),
              any(),
              eqTo(chargeCurrency),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              any()
            )

          result.properties.length shouldBe 2
          result.totalPriceDisplay shouldBe None
          result.priceDisplayType shouldBe Some(PriceDisplayType.MultiProduct)
          result.cart shouldBe None
          result.properties.map(_.id) should contain theSameElementsAs Seq("1", "2")
        }
      }

      "must report when isCartRestricted Items are more than 1" in {
        val propertyCartProduct = BookingMockHelper.cartProduct.modify(_.info.`type`).setTo(ItemEntries.Property)
        val childrenRoomsWithPropertyCartProduct: EnrichedChildRoom =
          childrenRoomWithIsCartRestricted
            .modify(_.cart.each.products)
            .setTo(Seq(propertyCartProduct, propertyCartProduct))

        val roomWithCorrectCartObj = masterRoomWithChildRoomsWithIsCartRestricted
          .modify(
            _.childrenRooms.each
          )
          .setTo(childrenRoomsWithPropertyCartProduct)

        val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
        when(setupBookingRequest.productsRequest.activityRequests).thenReturn(Option.empty)
        when(setupBookingRequest.productsRequest.flightRequests).thenReturn(Seq.empty)
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        mockPropertyServiceRetrieveProperties(
          Seq[BookingPropertiesData](
            bookingPropertyProduct().copy(
              papiProperties = Some(
                transformers.Properties(
                  property = Seq(
                    baseProperty().copy(masterRooms = Seq(masterRoomWithChildRoomsWithIsCartRestricted))
                  ),
                  debug = None,
                  dfMetaResult = DFMetaResult(Some(ResponseStateToken("", true, Some("pricingRequestId-1"))))
                )
              )
            )
          ),
          Seq[BookingPropertiesData](
            bookingPropertyProduct().copy(
              id = "2",
              papiProperties = Some(
                transformers.Properties(
                  property = Seq(
                    baseProperty()
                      .modifyAll(_.masterRooms)
                      .setTo(Seq(roomWithCorrectCartObj))
                  ),
                  debug = None,
                  dfMetaResult = DFMetaResult(Some(ResponseStateToken("", true, Some("pricingRequestId-1"))))
                )
              )
            )
          )
        )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromUpi(
            any(),
            any(),
            any(),
            any(),
            any()
          )
        ).thenReturn(Future.successful(cartPriceBreakdown))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))
        when(productsRequest.carRequestsOpt).thenReturn(Option(Seq.empty))

        when(setupBookingRequest.productsRequest.getCartToken).thenReturn(Some(cartToken))

        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.guests).thenReturn(None)
        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(mockPropertyRequest)
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(2))
            .retrieveProperties(
              any(),
              any(),
              eqTo(chargeCurrency),
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Some(paymentRequest)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
          verify(mockReporter, times(1))
            .report(
              any(),
              any(),
              eqTo(Map("exceeded" -> "true", "size" -> "2"))
            )

          result.properties.length shouldBe 2
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 1000d
          result.cart shouldBe Some(CartResponse(Some(cartToken)))
          result.properties.map(_.id) should contain theSameElementsAs Seq("1", "2")
        }
      }

      "return not None for child room" in {
        val propertyCartProduct = BookingMockHelper.cartProduct.modify(_.info.`type`).setTo(ItemEntries.Property)
        val childrenRoomsWithPropertyCartProduct: EnrichedChildRoom =
          baseChildrenRoom().modify(_.cart.each.products).setTo(Seq(propertyCartProduct, propertyCartProduct))

        val propertyProduct = bookingPropertyProduct()
          .modifyAll(
            _.papiProperties.each.property.each.masterRooms.each.childrenRooms.each,
            _.papiProperties.each.property.each.rooms.each
          )
          .setTo(childrenRoomsWithPropertyCartProduct)
        val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        when(setupBookingRequest.productsRequest.activityRequests).thenReturn(Option.empty)
        when(setupBookingRequest.productsRequest.flightRequests).thenReturn(Seq.empty)
        when(setupBookingRequest.loyaltyRequest).thenReturn(None)
        mockPropertyServiceRetrieveProperties(
          Seq[BookingPropertiesData](
            propertyProduct
          ),
          Seq[BookingPropertiesData](
            propertyProduct.copy(id = "2")
          )
        )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromUpi(
            any(),
            any(),
            any(),
            any(),
            any()
          )
        ).thenReturn(Future.successful(cartPriceBreakdown))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))
        when(productsRequest.carRequestsOpt).thenReturn(None)

        when(setupBookingRequest.productsRequest.getCartToken).thenReturn(Some(cartToken))
        when(setupBookingRequest.productsRequest.cartPricingContext).thenReturn(None)
        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.guests).thenReturn(None)
        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(mockPropertyRequest)
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(2))
            .retrieveProperties(
              any(),
              any(),
              eqTo(chargeCurrency),
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Some(paymentRequest)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
          verify(mockReporter, times(1))
            .report(
              any(),
              any(),
              eqTo(Map("exceeded" -> "false"))
            )

          val childRoom = productsFacade.getChildRoomForCart(setupBookingRequest, result.properties)
          childRoom should not be None
        }
      }

      "only contain Vehicle data Product Data" in {
        val vehicleDataItem1 = mockCarConfirmationData.copy(id = "5")
        val vehicleDataItem2 = mockCarConfirmationData.copy(id = "6")
        mockCarService("5", vehicleDataItem1)
        mockCarService("6", vehicleDataItem2)

        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.flightRequests).thenReturn(Seq.empty)
        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(requestContext.correlationId).thenReturn(Some("id"))

        when(productsRequest.propertyRequests).thenReturn(Seq.empty)
        when(productsRequest.activityRequests).thenReturn(Option(Seq.empty))
        when(productsRequest.carRequestsOpt).thenReturn(
          Some(
            Seq(
              CarRequestItem(
                id = "5",
                confirmPriceRequest = CarConfirmPriceRequest(identifier = None, searchToken = "")
              ),
              CarRequestItem(
                id = "6",
                confirmPriceRequest = CarConfirmPriceRequest(identifier = None, searchToken = "")
              )
            )
          )
        )
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
        ).thenReturn(Future.successful(cartPriceBreakdown))

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          result.properties.length shouldBe 0
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 1000d
          result.cars.length shouldBe 2
          result.cars.map(_.id) should contain theSameElementsAs Seq("5", "6")
        }
      }

      "contain Vehicle with protection in Product Data" in {
        val vehicleDataItem1 = mockCarConfirmationData.copy(id = "5")
        mockCarService("5", vehicleDataItem1)
        when(protectionService.setupProtectionForDrive(any(), any(), any())(any()))
          .thenReturn(Future.successful(Seq(getProtectionItem(optInValue = 0, token = ""))))
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.flightRequests).thenReturn(Seq.empty)
        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(requestContext.correlationId).thenReturn(Some("id"))

        when(productsRequest.propertyRequests).thenReturn(Seq.empty)
        when(productsRequest.hasVehicleWithProtection()).thenCallRealMethod()
        when(productsRequest.activityRequests).thenReturn(Option(Seq.empty))
        when(productsRequest.carRequestsOpt).thenReturn(
          Some(
            Seq(
              CarRequestItem(
                id = "5",
                confirmPriceRequest = CarConfirmPriceRequest(identifier = None, searchToken = ""),
                tripProtectionRequest = Some(TripProtectionRequestItem("", ProductTypeEnum.Car, 1))
              )
            )
          )
        )
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
        ).thenReturn(Future.successful(cartPriceBreakdown))

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 1000d
          result.cars.length shouldBe 1
          result.cars.map(_.id) should contain theSameElementsAs Seq("5")
          result.protections.length shouldBe 1
        }
      }

      "contain multiple Properties and multiple Vehicles in Product Data" in {
        when(setupBookingRequest.loyaltyRequest).thenReturn(Some(LoyaltyRequest()))
        mockMultiplePropertiesWithoutCart()
        val carOption1       = mock[CarOptions]
        val carOption2       = mock[CarOptions]
        val vehicleDataItem1 = mockCarConfirmationData.copy(id = "5", carInfo = carOption1)
        val vehicleDataItem2 = mockCarConfirmationData.copy(id = "6", carInfo = carOption2)
        mockCarService("5", vehicleDataItem1)
        mockCarService("6", vehicleDataItem2)
        when(productsRequest.activityRequests).thenReturn(Option(Seq.empty))
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.flightRequests).thenReturn(Seq.empty)
        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(requestContext.correlationId).thenReturn(Some("id"))

        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))
        when(productsRequest.carRequestsOpt).thenReturn(
          Some(
            Seq(
              CarRequestItem(
                id = "5",
                confirmPriceRequest = CarConfirmPriceRequest(identifier = None, searchToken = "")
              ),
              CarRequestItem(
                id = "6",
                confirmPriceRequest = CarConfirmPriceRequest(identifier = None, searchToken = "")
              )
            )
          )
        )
        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)
        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.guests).thenReturn(None)
        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(mockPropertyRequest)
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(mockPriceBreakdownService, times(1)).getPriceBreakdownForCartFromNonUpi(
            any(),
            any(),
            any(),
            any(),
            eqTo(Map(0 -> vehicleDataItem1.carInfo, 1 -> vehicleDataItem2.carInfo)),
            any(),
            any(),
            any(),
            any()
          )(any())
          result.properties.length shouldBe 2
          result.properties.map(_.id) should contain theSameElementsAs Seq("1", "2")
          result.cars.length shouldBe 2
          result.cars.map(_.id) should contain theSameElementsAs Seq("5", "6")

        }
      }

      "only contain Activities in Product Data" in {

        val activityRequestItem =
          ActivityRequestItem(
            "some id",
            ActivityConfirmPriceRequest("some token", "some redeem token")
          )

        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(setupBookingRequest.productsRequest.getCarRequestOpt).thenReturn(Option.empty)

        when(requestContext.correlationId).thenReturn(Some("id"))
        when(mockActivityService.getActivityConfirmationData(any(), any(), any(), any(), any(), any())(any()))
          .thenReturn(Future.successful(mockedActivityConfirmationData))
        when(setupBookingRequest.productsRequest)
          .thenReturn(productsRequest)
        when(productsRequest.activityRequests)
          .thenReturn(Option(Seq(activityRequestItem)))
        when(productsRequest.propertyRequests).thenReturn(Seq.empty)
        when(productsRequest.flightRequests).thenReturn(Seq.empty)

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 1000d
          result.activities.length shouldBe 1
          result.activities.map(_.id) should contain theSameElementsAs Seq("mock id")

        }
      }

      "only contain Flights in Product Data when all flights confirmed" in {

        val flightProductItem1 = flightConfirmationData.copy(id = "3", isCompleted = true)
        val flightProductItem2 = flightConfirmationData.copy(id = "4", isCompleted = true)
        mockFlightRepository("3", flightProductItem1)
        mockFlightRepository("4", flightProductItem2)

        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(ccCampaignMock))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq.empty)
        when(productsRequest.activityRequests).thenReturn(None)
        when(productsRequest.flightRequests)
          .thenReturn(Seq(FlightRequestItem(Some("3"), None, None), FlightRequestItem(Some("4"), None, None)))
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(Map(0 -> flightProductItem1, 1 -> flightProductItem2)),
            any(),
            any(),
            any()
          )(any())
        ).thenReturn(Future.successful(cartPriceBreakdown))
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(Seq.empty))

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          result.properties.length shouldBe 0
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 1000d
          result.flights.length shouldBe 2
          result.priceConfirmed shouldBe true
          result.flights.map(_.id) should contain theSameElementsAs Seq("3", "4")
        }
      }

      "only contain Flights in Product Data when partial flights confirmed" in {

        val flightProductItem1 = flightConfirmationData.copy(id = "3", isCompleted = true)
        val flightProductItem2 = flightConfirmationData.copy(id = "4", isCompleted = false)
        mockFlightRepository("3", flightProductItem1)
        mockFlightRepository("4", flightProductItem2)
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(Map(0 -> flightProductItem1, 1 -> flightProductItem2)),
            any(),
            any(),
            any()
          )(any())
        ).thenReturn(Future.successful(cartPriceBreakdown))

        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(ccCampaignMock))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.activityRequests).thenReturn(None)
        when(productsRequest.propertyRequests).thenReturn(Seq.empty)
        when(productsRequest.carRequestsOpt).thenReturn(Option(Seq.empty))
        when(productsRequest.flightRequests)
          .thenReturn(Seq(FlightRequestItem(Some("3"), None, None), FlightRequestItem(Some("4"), None, None)))

        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(Seq.empty))

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          result.properties.length shouldBe 0
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 1000d
          result.priceConfirmed shouldBe false
          result.flights.length shouldBe 2
          result.flights.map(_.id) should contain theSameElementsAs Seq("3", "4")
        }
      }

      "contain flight from confirm price" when {
        "WLBO-1595 = A and price breakdown return non-empty" in {
          val completedFlightForConfirmPrice =
            flightConfirmationData.copy(id = "98", token = "confirm_price_98", isCompleted = true)
          val incompletedFlightForConfirmPrice =
            flightConfirmationData.copy(id = "99", token = "confirm_price_99", isCompleted = false)
          val completedFlightForItineraryDetails =
            flightConfirmationData.copy(id = "98", token = "itinerary_details_98", isCompleted = true)
          val incompletedFlightForItineraryDetails =
            flightConfirmationData.copy(id = "99", token = "itinerary_details_99", isCompleted = false)

          mockFlightData(completedFlightForConfirmPrice, completedFlightForItineraryDetails)
          mockFlightData(incompletedFlightForConfirmPrice, incompletedFlightForItineraryDetails)

          when(
            mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Map(0 -> completedFlightForConfirmPrice, 1 -> incompletedFlightForConfirmPrice)),
              any(),
              any(),
              any()
            )(any())
          ).thenReturn(Future.successful(cartPriceBreakdown))

          when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
            .thenReturn(Future.successful(ccCampaignMock))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
          when(productsRequest.activityRequests).thenReturn(None)
          when(productsRequest.propertyRequests).thenReturn(Seq.empty)
          when(productsRequest.carRequestsOpt).thenReturn(Some(Seq.empty))
          when(productsRequest.flightRequests)
            .thenReturn(Seq(FlightRequestItem(Some("98"), None, None), FlightRequestItem(Some("99"), None, None)))
          when(productsRequest.cartPricingContext).thenReturn(Some(CartPricingContext(None, None)))
          when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
            .thenReturn(Future.successful(Seq.empty))

          when(featureAware.shouldReturnFlightItineraryDetailsWhenFlightConfirmPriceNotCompleted).thenReturn(false)

          productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
            result.flights.length shouldBe 2
            result.flights.map(_.id) should contain theSameElementsAs Seq("98", "99")
            result.flights.map(_.token) should contain theSameElementsAs Seq("confirm_price_98", "confirm_price_99")
          }
        }

        "WLBO-1595 = B and all confirm price is completed" in {
          val completedFlightForConfirmPrice =
            flightConfirmationData.copy(id = "98", token = "confirm_price_98", isCompleted = true)
          val completedFlightForConfirmPrice2 =
            flightConfirmationData.copy(id = "99", token = "confirm_price_99", isCompleted = true)
          val completedFlightForItineraryDetails =
            flightConfirmationData.copy(id = "98", token = "itinerary_details_98", isCompleted = true)
          val completedFlightForItineraryDetails2 =
            flightConfirmationData.copy(id = "99", token = "itinerary_details_99", isCompleted = true)

          mockFlightData(completedFlightForConfirmPrice, completedFlightForItineraryDetails)
          mockFlightData(completedFlightForConfirmPrice2, completedFlightForItineraryDetails2)

          when(
            mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Map(0 -> completedFlightForConfirmPrice, 1 -> completedFlightForConfirmPrice2)),
              any(),
              any(),
              any()
            )(any())
          ).thenReturn(Future.successful(cartPriceBreakdown))

          when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
            .thenReturn(Future.successful(ccCampaignMock))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
          when(productsRequest.activityRequests).thenReturn(None)
          when(productsRequest.propertyRequests).thenReturn(Seq.empty)
          when(productsRequest.carRequestsOpt).thenReturn(Some(Seq.empty))
          when(productsRequest.flightRequests)
            .thenReturn(Seq(FlightRequestItem(Some("98"), None, None), FlightRequestItem(Some("99"), None, None)))
          when(productsRequest.cartPricingContext).thenReturn(Some(CartPricingContext(None, None)))
          when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
            .thenReturn(Future.successful(Seq.empty))

          when(featureAware.shouldReturnFlightItineraryDetailsWhenFlightConfirmPriceNotCompleted).thenReturn(true)

          productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
            result.flights.length shouldBe 2
            result.flights.map(_.id) should contain theSameElementsAs Seq("98", "99")
            result.flights.map(_.token) should contain theSameElementsAs Seq("confirm_price_98", "confirm_price_99")
          }
        }
      }

      "contain flight from flights itinerary details" when {
        "WLBO-1595 = A and price breakdown return empty" in {
          val completedFlightForConfirmPrice =
            flightConfirmationData.copy(id = "98", token = "confirm_price_98", isCompleted = true)
          val incompletedFlightForConfirmPrice =
            flightConfirmationData.copy(id = "99", token = "confirm_price_99", isCompleted = false)
          val completedFlightForItineraryDetails =
            flightConfirmationData.copy(id = "98", token = "itinerary_details_98", isCompleted = true)
          val incompletedFlightForItineraryDetails =
            flightConfirmationData.copy(id = "99", token = "itinerary_details_99", isCompleted = false)

          mockFlightData(completedFlightForConfirmPrice, completedFlightForItineraryDetails)
          mockFlightData(incompletedFlightForConfirmPrice, incompletedFlightForItineraryDetails)

          when(
            mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Map(0 -> completedFlightForConfirmPrice, 1 -> incompletedFlightForConfirmPrice)),
              any(),
              any(),
              any()
            )(any())
          ).thenReturn(Future.successful(None))

          when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
            .thenReturn(Future.successful(ccCampaignMock))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
          when(productsRequest.activityRequests).thenReturn(None)
          when(productsRequest.propertyRequests).thenReturn(Seq.empty)
          when(productsRequest.carRequestsOpt).thenReturn(Some(Seq.empty))
          when(productsRequest.flightRequests)
            .thenReturn(Seq(FlightRequestItem(Some("98"), None, None), FlightRequestItem(Some("99"), None, None)))
          when(productsRequest.cartPricingContext).thenReturn(Some(CartPricingContext(None, None)))
          when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
            .thenReturn(Future.successful(Seq.empty))

          when(featureAware.shouldReturnFlightItineraryDetailsWhenFlightConfirmPriceNotCompleted).thenReturn(false)

          productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
            result.flights.length shouldBe 2
            result.flights.map(_.id) should contain theSameElementsAs Seq("98", "99")
            result.flights
              .map(_.token) should contain theSameElementsAs Seq("itinerary_details_98", "itinerary_details_99")
          }
        }

        "WLBO-1595 = B and some confirm price is incompleted" in {
          val completedFlightForConfirmPrice =
            flightConfirmationData.copy(id = "98", token = "confirm_price_98", isCompleted = true)
          val incompletedFlightForConfirmPrice =
            flightConfirmationData.copy(id = "99", token = "confirm_price_99", isCompleted = false)
          val completedFlightForItineraryDetails =
            flightConfirmationData.copy(id = "98", token = "itinerary_details_98", isCompleted = true)
          val incompletedFlightForItineraryDetails =
            flightConfirmationData.copy(id = "99", token = "itinerary_details_99", isCompleted = false)

          mockFlightData(completedFlightForConfirmPrice, completedFlightForItineraryDetails)
          mockFlightData(incompletedFlightForConfirmPrice, incompletedFlightForItineraryDetails)

          when(
            mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Map(0 -> completedFlightForConfirmPrice, 1 -> incompletedFlightForConfirmPrice)),
              any(),
              any(),
              any()
            )(any())
          ).thenReturn(Future.successful(cartPriceBreakdown))

          when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
            .thenReturn(Future.successful(ccCampaignMock))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
          when(productsRequest.activityRequests).thenReturn(None)
          when(productsRequest.propertyRequests).thenReturn(Seq.empty)
          when(productsRequest.carRequestsOpt).thenReturn(Some(Seq.empty))
          when(productsRequest.flightRequests)
            .thenReturn(Seq(FlightRequestItem(Some("98"), None, None), FlightRequestItem(Some("99"), None, None)))
          when(productsRequest.cartPricingContext).thenReturn(Some(CartPricingContext(None, None)))
          when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
            .thenReturn(Future.successful(Seq.empty))

          when(featureAware.shouldReturnFlightItineraryDetailsWhenFlightConfirmPriceNotCompleted).thenReturn(true)

          productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
            result.flights.length shouldBe 2
            result.flights.map(_.id) should contain theSameElementsAs Seq("98", "99")
            result.flights
              .map(_.token) should contain theSameElementsAs Seq("itinerary_details_98", "itinerary_details_99")
          }
        }
      }

      "contain multiple Properties and multiple Flights in Product Data" in {

        mockMultiplePropertiesWithoutCart()
        val flightProductItem1 = flightConfirmationData.copy(id = "3", isCompleted = true)
        val flightProductItem2 = flightConfirmationData.copy(id = "4", isCompleted = true)
        mockFlightRepository("3", flightProductItem1)
        mockFlightRepository("4", flightProductItem2)

        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.carRequestsOpt).thenReturn(None)
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))
        when(productsRequest.activityRequests).thenReturn(None)
        when(productsRequest.flightRequests)
          .thenReturn(Seq(FlightRequestItem(Some("3"), None, None), FlightRequestItem(Some("4"), None, None)))

        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)
        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.guests).thenReturn(None)
        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(mockPropertyRequest)
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          result.properties.length shouldBe 2
          result.totalPriceDisplay shouldBe cartPriceBreakdown
          result.properties.map(_.id) should contain theSameElementsAs Seq("1", "2")
          result.flights.length shouldBe 2
          result.priceConfirmed shouldBe true
          result.flights.map(_.id) should contain theSameElementsAs Seq("3", "4")
        }
      }

      "distribute points response" should {

        val propertyCartReqItem = CartItemRes(
          productIdentifier = Some("1"),
          productType = CartItemReq.ProductType.HOTELS.toString,
          inclusivePrice = 1100,
          exclusivePrice = 1000,
          inclusivePricePoints = 1500,
          exclusivePricePoints = 1400
        )

        val flightCartReqItem = CartItemRes(
          productIdentifier = Some("11"),
          productType = CartItemReq.ProductType.FLIGHTS.toString,
          inclusivePrice = 2200,
          exclusivePrice = 2100,
          inclusivePricePoints = 2500,
          exclusivePricePoints = 2400
        )

        val carCartReqItem = CartItemRes(
          productIdentifier = Some("21"),
          productType = CartItemReq.ProductType.CARS.toString,
          inclusivePrice = 3200,
          exclusivePrice = 3100,
          inclusivePricePoints = 3500,
          exclusivePricePoints = 3400
        )

        val activityCartReqItem = CartItemRes(
          productIdentifier = Some("31"),
          productType = CartItemReq.ProductType.ACTIVITIES.toString,
          inclusivePrice = 4200,
          exclusivePrice = 4100,
          inclusivePricePoints = 4500,
          exclusivePricePoints = 4400
        )

        "map to property product request" in {

          val distributePointsResponse = DistributePointsResponse(10000, 6000, 5000, Seq(propertyCartReqItem))
          when(mockExternalLoyaltyService.distributePoints(any(), any()))
            .thenReturn(Future.successful(Some(distributePointsResponse)))
          when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.CitiUS)
          when(setupBookingRequest.loyaltyRequest).thenReturn(Some(LoyaltyRequest()))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
          mockMultiplePropertiesWithoutCart()

          when(mockPropertyRequest.id).thenReturn("1")
          when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
          when(productsRequest.flightRequests).thenReturn(Seq.empty)

          when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))
          val durationRequest = mock[DurationRequest]
          when(durationRequest.checkIn).thenReturn(new LocalDate("2020-12-15"))
          when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)
          when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
          when(mockPropertyRequest.guests).thenReturn(None)
          val pricingRequest1 = mock[PricingRequest]
          when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
          when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
          when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria))
            .thenReturn(mockPropertyRequest)
          when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
          when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

          productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
            verify(propertyService, times(1))
              .retrieveProperties(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                eqTo(Some(LoyaltyRequest(points = Some(1500)))),
                any(),
                eqTo(false),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(
                eqTo(setupBookingContext)
              )

            result.properties.length shouldBe 1
          }
        }

        "map to property and flight product request" in {

          val distributePointsResponse =
            DistributePointsResponse(10000, 6000, 5000, Seq(propertyCartReqItem, flightCartReqItem))
          when(mockExternalLoyaltyService.distributePoints(any(), any()))
            .thenReturn(Future.successful(Some(distributePointsResponse)))
          when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.CitiUS)
          when(setupBookingRequest.loyaltyRequest).thenReturn(Some(LoyaltyRequest()))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)

          mockMultiplePropertiesWithoutCart()

          val flightProductItem = flightConfirmationData.copy(id = "11", isCompleted = true)
          mockFlightRepository("11", flightProductItem)

          when(mockPropertyRequest.id).thenReturn("1")
          when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
          when(productsRequest.activityRequests).thenReturn(None)
          when(productsRequest.carRequestsOpt).thenReturn(None)
          when(productsRequest.flightRequests)
            .thenReturn(Seq(FlightRequestItem(Some("11"), None, None))) /// , FlightRequestItem(Some("4"), None, None)))

          when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))
          val durationRequest = mock[DurationRequest]
          when(durationRequest.lengthOfStay).thenReturn(2)
          when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)
          when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
          when(mockPropertyRequest.guests).thenReturn(None)
          val pricingRequest1 = mock[PricingRequest]
          when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
          when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
          when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria))
            .thenReturn(mockPropertyRequest)
          when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
          when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

          productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
            verify(propertyService, times(1))
              .retrieveProperties(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                eqTo(Some(LoyaltyRequest(points = Some(1500)))),
                any(),
                eqTo(false),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(
                eqTo(setupBookingContext)
              )
            verify(flightRepository, times(1))
              .retrieveFlightConfirmationData(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                eqTo(Some(LoyaltyRequest(points = Some(2500)))),
                any(),
                any(),
                any()
              )(any())

            result.properties.length shouldBe 1
            result.flights.length shouldBe 1
          }
        }

        "map to property and HackerFare flight product request" in {

          val distributePointsResponse =
            DistributePointsResponse(10000, 6000, 5000, Seq(propertyCartReqItem, flightCartReqItem, flightCartReqItem))
          when(mockExternalLoyaltyService.distributePoints(any(), any()))
            .thenReturn(Future.successful(Some(distributePointsResponse)))
          when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.CitiUS)
          when(setupBookingRequest.loyaltyRequest).thenReturn(Some(LoyaltyRequest()))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)

          mockMultiplePropertiesWithoutCart()

          val flightProductItem = flightConfirmationData.copy(id = "11", isCompleted = true)
          mockFlightRepository("11", flightProductItem)

          when(mockPropertyRequest.id).thenReturn("1")
          when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
          when(productsRequest.activityRequests).thenReturn(None)
          when(productsRequest.carRequestsOpt).thenReturn(None)
          when(productsRequest.flightRequests)
            .thenReturn(Seq(FlightRequestItem(Some("11"), None, None))) /// , FlightRequestItem(Some("4"), None, None)))

          when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))
          val durationRequest = mock[DurationRequest]
          when(durationRequest.lengthOfStay).thenReturn(2)
          when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)
          when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
          when(mockPropertyRequest.guests).thenReturn(None)
          val pricingRequest1 = mock[PricingRequest]
          when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
          when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
          when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria))
            .thenReturn(mockPropertyRequest)
          when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
          when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

          productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
            verify(propertyService, times(1))
              .retrieveProperties(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                eqTo(Some(LoyaltyRequest(points = Some(1500)))),
                any(),
                eqTo(false),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(
                eqTo(setupBookingContext)
              )
            verify(flightRepository, times(1))
              .retrieveFlightConfirmationData(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                eqTo(Some(LoyaltyRequest(points = Some(5000)))),
                any(),
                any(),
                any()
              )(any())

            result.properties.length shouldBe 1
            result.flights.length shouldBe 1
          }
        }

        "map to property, flight, and vehicle product request" in {

          val cartReqItems             = Seq(propertyCartReqItem, flightCartReqItem, carCartReqItem)
          val distributePointsResponse = DistributePointsResponse(10000, 6000, 5000, cartReqItems)
          when(mockExternalLoyaltyService.distributePoints(any(), any()))
            .thenReturn(Future.successful(Some(distributePointsResponse)))
          when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.CitiUS)
          when(setupBookingRequest.loyaltyRequest).thenReturn(Some(LoyaltyRequest()))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)

          mockMultiplePropertiesWithoutCart()

          val flightProductItem = flightConfirmationData.copy(id = "11", isCompleted = true)
          mockFlightRepository("11", flightProductItem)
          val carConfirmationData = mockCarConfirmationData.copy(id = "21")
          mockCarService("21", carConfirmationData)

          when(mockPropertyRequest.id).thenReturn("1")
          when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
          when(productsRequest.activityRequests).thenReturn(None)
          when(productsRequest.flightRequests).thenReturn(Seq(FlightRequestItem(Some("11"), None, None)))
          when(productsRequest.carRequestsOpt).thenReturn(
            Some(Seq(CarRequestItem("21", CarConfirmPriceRequest(identifier = None, searchToken = ""))))
          )

          when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))
          when(setupBookingContext.requestContext.correlationId).thenReturn(Some("111-555"))
          val durationRequest = mock[DurationRequest]
          when(durationRequest.lengthOfStay).thenReturn(2)
          when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)
          when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
          when(mockPropertyRequest.guests).thenReturn(None)
          val pricingRequest1 = mock[PricingRequest]
          when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
          when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
          when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria))
            .thenReturn(mockPropertyRequest)
          when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
          when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

          productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
            verify(propertyService, times(1))
              .retrieveProperties(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                eqTo(Some(LoyaltyRequest(points = Some(1500)))),
                any(),
                eqTo(false),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(
                eqTo(setupBookingContext)
              )
            verify(flightRepository, times(1))
              .retrieveFlightConfirmationData(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                eqTo(Some(LoyaltyRequest(points = Some(2500)))),
                any(),
                any(),
                any()
              )(any())

            verify(carService).getCarConfirmationData(
              any(),
              any(),
              any(),
              eqTo(Some(LoyaltyRequest(points = Some(3500))))
            )(any())
            result.properties.length shouldBe 1
            result.flights.length shouldBe 1
            result.cars.length shouldBe 1
          }
        }

        "map to property, flight, vehicle, activity" in {

          val cartReqItems             = Seq(propertyCartReqItem, flightCartReqItem, carCartReqItem, activityCartReqItem)
          val distributePointsResponse = DistributePointsResponse(10000, 6000, 5000, cartReqItems)
          when(mockExternalLoyaltyService.distributePoints(any(), any()))
            .thenReturn(Future.successful(Some(distributePointsResponse)))
          when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
          when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.CitiUS)
          when(setupBookingRequest.loyaltyRequest).thenReturn(Some(LoyaltyRequest()))
          when(setupBookingRequest.productsRequest).thenReturn(productsRequest)

          mockMultiplePropertiesWithoutCart()

          val flightProductItem = flightConfirmationData.copy(id = "11", isCompleted = true)
          mockFlightRepository("11", flightProductItem)
          val carConfirmationData = mockCarConfirmationData.copy(id = "21")
          mockCarService("21", carConfirmationData)
          when(mockActivityService.getActivityConfirmationData(any(), any(), any(), any(), any(), any())(any()))
            .thenReturn(Future.successful(mockedActivityConfirmationData))

          when(mockPropertyRequest.id).thenReturn("1")
          when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
          when(productsRequest.activityRequests).thenReturn(None)
          when(productsRequest.flightRequests)
            .thenReturn(Seq(FlightRequestItem(Some("11"), None, None)))
          when(productsRequest.carRequestsOpt).thenReturn(
            Some(Seq(CarRequestItem("21", CarConfirmPriceRequest(identifier = None, searchToken = ""))))
          )
          when(productsRequest.activityRequests).thenReturn(
            Option(Seq(ActivityRequestItem("31", ActivityConfirmPriceRequest("some token", "some redeem token"))))
          )

          when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))
          when(setupBookingContext.requestContext.correlationId).thenReturn(Some("111-555"))
          val durationRequest = mock[DurationRequest]
          when(durationRequest.lengthOfStay).thenReturn(2)
          when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)
          when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
          when(mockPropertyRequest.guests).thenReturn(None)
          val pricingRequest1 = mock[PricingRequest]
          when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
          when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
          when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria))
            .thenReturn(mockPropertyRequest)
          when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
          when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

          productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
            verify(propertyService, times(1))
              .retrieveProperties(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                eqTo(Some(LoyaltyRequest(points = Some(1500)))),
                any(),
                eqTo(false),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(any())
            verify(flightRepository, times(1))
              .retrieveFlightConfirmationData(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                eqTo(Some(LoyaltyRequest(points = Some(2500)))),
                any(),
                any(),
                any()
              )(any())

            verify(carService).getCarConfirmationData(
              any(),
              any(),
              any(),
              eqTo(Some(LoyaltyRequest(points = Some(3500))))
            )(any())

            verify(mockActivityService).getActivityConfirmationData(
              any(),
              eqTo(Some(LoyaltyRequest(points = Some(4500)))),
              any(),
              any(),
              any(),
              any()
            )(any())

            result.properties.length shouldBe 1
            result.flights.length shouldBe 1
            result.cars.length shouldBe 1
            result.activities.length shouldBe 1
          }
        }

        "map ExternalLoyaltyAdditionalInfo correctly" in {
          val noneIsCashPlusPointsSupported: Option[Boolean] = None
          val testCases = Table(
            ("statement", "DistributeResponseOpt", "expected"),
            (
              "return ExternalLoyaltyAdditionalInfo with isCashPlusPointsSupported = true with RedemptionType",
              Some(
                DistributePointsResponse(
                  10000,
                  6000,
                  5000,
                  Seq(propertyCartReqItem),
                  isCashPlusPointsSupported = Some(true),
                  redemptionType = Some(RedemptionType.ONLYCASH)
                )
              ),
              Some(ExternalLoyaltyAdditionalInfo(Some(true), Some(RedemptionType.ONLYCASH)))
            ),
            (
              "return ExternalLoyaltyAdditionalInfo with isCashPlusPointsSupported = false without RedemptionType",
              Some(
                DistributePointsResponse(
                  10000,
                  6000,
                  5000,
                  Seq(propertyCartReqItem),
                  isCashPlusPointsSupported = Some(false),
                  redemptionType = None
                )
              ),
              Some(ExternalLoyaltyAdditionalInfo(Some(false), None))
            ),
            (
              "return ExternalLoyaltyAdditionalInfo with isCashPlusPointsSupported = None with RedemptionType",
              Some(
                DistributePointsResponse(
                  10000,
                  6000,
                  5000,
                  Seq(propertyCartReqItem),
                  isCashPlusPointsSupported = None,
                  redemptionType = Some(RedemptionType.POINTSORPARTIALCASH)
                )
              ),
              Some(
                ExternalLoyaltyAdditionalInfo(noneIsCashPlusPointsSupported, Some(RedemptionType.POINTSORPARTIALCASH))
              )
            ),
            (
              "return None when no DistributePointsResponse",
              None,
              None
            )
          )
          forEvery(testCases) {
            (
                statement: String,
                distributePointsResponseOpt: Option[DistributePointsResponse],
                expected: Option[ExternalLoyaltyAdditionalInfo]
            ) =>
              when(
                mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any(),
                  any()
                )(any())
              )
                .thenReturn(Future.successful(priceBreakdown))
              when(mockExternalLoyaltyService.distributePoints(any(), any()))
                .thenReturn(Future.successful(distributePointsResponseOpt))
              when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
              when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.CitiUS)
              when(setupBookingRequest.loyaltyRequest).thenReturn(Some(LoyaltyRequest()))
              when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
              mockMultiplePropertiesWithoutCart()

              when(mockPropertyRequest.id).thenReturn("1")
              when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
              when(productsRequest.flightRequests).thenReturn(Seq.empty)

              when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))
              val durationRequest = mock[DurationRequest]
              when(durationRequest.checkIn).thenReturn(new LocalDate("2020-12-15"))
              when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)
              when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
              when(mockPropertyRequest.guests).thenReturn(None)
              val pricingRequest1 = mock[PricingRequest]
              when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
              when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1)))
                .thenReturn(propertySearchCriteria)
              when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria))
                .thenReturn(mockPropertyRequest)
              when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
              when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

              productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
                withClue(statement)(result.externalLoyaltyAdditionalInfo shouldBe expected)
              }
          }
        }
      }

      "have price change in Product Data" in {
        val propertyCartProduct = BookingMockHelper.cartProduct.modify(_.info.`type`).setTo(ItemEntries.Property)
        val childrenRoomsWithPropertyCartProduct: EnrichedChildRoom =
          baseChildrenRoom().modify(_.cart.each.products).setTo(Seq(propertyCartProduct, propertyCartProduct))

        val propertyProduct = bookingPropertyProduct()
          .modifyAll(
            _.papiProperties.each.property.each.masterRooms.each.childrenRooms.each,
            _.papiProperties.each.property.each.rooms.each
          )
          .setTo(childrenRoomsWithPropertyCartProduct)
        val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        when(setupBookingRequest.productsRequest.activityRequests).thenReturn(Option.empty)
        when(setupBookingRequest.productsRequest.flightRequests).thenReturn(Seq.empty)
        mockPropertyServiceRetrieveProperties(
          Seq[BookingPropertiesData](propertyProduct),
          Seq[BookingPropertiesData](propertyProduct.copy(id = "2"))
        )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromUpi(
            any(),
            any(),
            any(),
            any(),
            any()
          )
        ).thenReturn(Future.successful(cartPriceBreakdown))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))
        when(productsRequest.carRequestsOpt).thenReturn(None)

        when(setupBookingRequest.productsRequest.getCartToken).thenReturn(Some(cartToken))

        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.guests).thenReturn(None)
        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(mockPropertyRequest)
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(2))
            .retrieveProperties(
              any(),
              any(),
              eqTo(chargeCurrency),
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Some(paymentRequest)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())
          verify(mockReporter, times(1))
            .report(
              any(),
              any(),
              eqTo(Map("exceeded" -> "false"))
            )

          result.priceChange.get.previousPrice.amount shouldBe 220d
          result.priceChange.get.beforeDiscountPrice.amount shouldBe 200d
          result.priceChange.get.delta.amount shouldBe 20d
        }
      }

      "apply promo to only 1 property in multi property booking with Bessie parameters" in {
        val partnerClaim = Some("some-partner-claim")
        val loyaltyRequest = Some(
          LoyaltyRequest(
            partnerClaimToken = partnerClaim
          )
        )
        when(setupBookingRequest.loyaltyRequest).thenReturn(loyaltyRequest)

        val partnerDiscountRequest = DiscountRequest(
          None,
          Some(List(CampaignInfoInternal(CampaignInfo(1, 0, "CITIMOCKPROMO", DiscountType.None, 0, None), None)))
        )
        val mockPartnerPromoUtils = mock[PartnerPromoUtils]
        when(mockPartnerPromoUtils.isPartnerPromoEligible).thenReturn(true)
        when(mockPartnerPromoUtils.getPartnerDiscountRequest(any(), any())).thenReturn(Some(partnerDiscountRequest))
        when(mockPartnerPromoUtils.getPartnerPromoEligiblePropertyId(any(), any())).thenReturn(Some("2"))
        when(mockPartnerPromoUtils.isPropertyEligibleForPartnerPromo(any(), eqTo(true))).thenReturn(true)
        when(mockPartnerPromoUtils.isPropertyEligibleForPartnerPromo(any(), eqTo(false))).thenReturn(false)

        when(mockPartnerPromoService.getPartnerPromoUtils(any[WhiteLabelInfo])(any[RequestContext]))
          .thenReturn(mockPartnerPromoUtils)
        val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        when(setupBookingRequest.productsRequest.activityRequests).thenReturn(Option.empty)
        when(setupBookingRequest.productsRequest.flightRequests).thenReturn(Seq.empty)
        when(setupBookingRequest.campaignInfo).thenReturn(Some(CampaignInfoRequest(Some(1), 51, "CITI4THNIGHT")))

        when(
          propertyService.retrieveProperties(
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(Some(partnerDiscountRequest)),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(
            any()
          )
        ).thenReturn(
          Future.successful(
            Seq[BookingPropertiesData](
              bookingPropertyProductWithCampaignPromotion()
            )
          )
        )

        when(
          propertyService.retrieveProperties(
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(None),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(
            any()
          )
        ).thenReturn(
          Future.successful(
            Seq[BookingPropertiesData](
              bookingPropertyProduct()
            )
          )
        )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.CitiUS)
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromUpi(
            any(),
            any(),
            any(),
            any(),
            any()
          )
        ).thenReturn(Future.successful(cartPriceBreakdown))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(mockPropertyRequest.id).thenReturn("1")

        val product2 = mock[PropertyRequestItem]
        when(product2.id).thenReturn("2")

        when(productsRequest.carRequestsOpt).thenReturn(None)
        when(setupBookingRequest.productsRequest.bookingToken).thenReturn(Some(expectedToken))

        when(setupBookingRequest.productsRequest.getCartToken).thenReturn(Some(cartToken))
        when(setupBookingRequest.enabledFeatures).thenReturn(Some(Seq("PaymentMethodFee")))

        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.guests).thenReturn(None)

        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(mockPropertyRequest)
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

        when(product2.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(product2.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(product2.guests).thenReturn(None)

        val pricingRequest2 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest2))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest2))).thenReturn(propertySearchCriteria)
        when(product2.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(product2)
        when(pricingRequest2.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest2.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest2)
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, product2))

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(2))
            .retrieveProperties(
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())

          result.properties.size shouldBe 2
          result.properties
            .filter(_.id == "1")
            .head
            .papiProperties
            .get
            .property
            .head
            .masterRooms
            .head
            .childrenRooms
            .head
            .campaignPromotions shouldBe None
          result.properties
            .filter(_.id == "2")
            .head
            .papiProperties
            .get
            .property
            .head
            .masterRooms
            .head
            .childrenRooms
            .head
            .campaignPromotions shouldBe Some(
            List(
              enrichedPartnerCampaign
            )
          )
        }
      }

      "apply travel credit to properties in multi property booking with Bessie parameters" in {
        val partnerClaim = Some("some-partner-claim")
        val loyaltyRequest = Some(
          LoyaltyRequest(
            partnerClaimToken = partnerClaim
          )
        )
        when(setupBookingRequest.loyaltyRequest).thenReturn(loyaltyRequest)

        val partnerDiscountRequest = DiscountRequest(
          None,
          Some(List(CampaignInfoInternal(CampaignInfo(1, 0, "ONYXTC", DiscountType.None, 0, None), None)))
        )
        val mockPartnerPromoUtils = mock[PartnerPromoUtils]
        when(mockPartnerPromoUtils.isPartnerPromoEligible).thenReturn(true)
        when(mockPartnerPromoUtils.getPartnerDiscountRequest(any(), any())).thenReturn(Some(partnerDiscountRequest))
        when(mockPartnerPromoUtils.getPartnerPromoEligiblePropertyId(any(), any())).thenReturn(Some("2"))
        when(mockPartnerPromoUtils.getTravelCreditLeftAmount(any(), any())).thenReturn(Some(300.00))
        when(mockPartnerPromoUtils.isPropertyEligibleForPartnerPromo(any(), eqTo(true))).thenReturn(true)
        when(mockPartnerPromoUtils.isPropertyEligibleForPartnerPromo(any(), eqTo(false))).thenReturn(false)

        when(mockPartnerPromoService.getPartnerPromoUtils(any[WhiteLabelInfo])(any[RequestContext]))
          .thenReturn(mockPartnerPromoUtils)
        val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        when(setupBookingRequest.productsRequest.activityRequests).thenReturn(Option.empty)
        when(setupBookingRequest.productsRequest.flightRequests).thenReturn(Seq.empty)
        when(setupBookingRequest.campaignInfo).thenReturn(Some(CampaignInfoRequest(Some(1), 51, "CITIONYX")))

        val additionalPricingParameter = AdditionalPricingParameter(travelCreditAmountLeft = Some(300.0))
        when(
          propertyService.retrieveProperties(
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(Some(partnerDiscountRequest)),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(
            any()
          )
        ).thenReturn(
          Future.successful(
            Seq[BookingPropertiesData](
              bookingPropertyProductWithCampaignPromotion()
            )
          )
        )

        when(
          propertyService.retrieveProperties(
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(None),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(
            any()
          )
        ).thenReturn(
          Future.successful(
            Seq[BookingPropertiesData](
              bookingPropertyProduct()
            )
          )
        )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.CitiUS)
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromUpi(
            any(),
            any(),
            any(),
            any(),
            any()
          )
        ).thenReturn(Future.successful(cartPriceBreakdown))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(mockPropertyRequest.id).thenReturn("1")

        val product2 = mock[PropertyRequestItem]
        when(product2.id).thenReturn("2")

        when(productsRequest.carRequestsOpt).thenReturn(None)
        when(setupBookingRequest.productsRequest.bookingToken).thenReturn(Some(expectedToken))

        when(setupBookingRequest.productsRequest.getCartToken).thenReturn(Some(cartToken))
        when(setupBookingRequest.enabledFeatures).thenReturn(Some(Seq("PaymentMethodFee")))

        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.guests).thenReturn(None)

        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(mockPropertyRequest)
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

        when(product2.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(product2.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(product2.guests).thenReturn(None)

        val pricingRequest2 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest2))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest2))).thenReturn(propertySearchCriteria)
        when(product2.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(product2)
        when(pricingRequest2.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest2.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest2)
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, product2))

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(2))
            .retrieveProperties(
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Some(additionalPricingParameter))
            )(any())

          result.properties.size shouldBe 2
          result.properties
            .filter(_.id == "1")
            .head
            .papiProperties
            .get
            .property
            .head
            .masterRooms
            .head
            .childrenRooms
            .head
            .campaignPromotions shouldBe None
          result.properties
            .filter(_.id == "2")
            .head
            .papiProperties
            .get
            .property
            .head
            .masterRooms
            .head
            .childrenRooms
            .head
            .campaignPromotions shouldBe Some(
            List(
              enrichedPartnerCampaign
            )
          )
        }
      }

      "return true for Bessie isPartnerPromotionCodeEligible with Bessie parameters" in {
        when(mockPartnerPromoService.getPartnerPromoUtils(any[WhiteLabelInfo])(any[RequestContext]))
          .thenReturn(PropertiesPromoUtils)
        val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        when(setupBookingRequest.productsRequest.activityRequests).thenReturn(Option.empty)
        when(setupBookingRequest.productsRequest.flightRequests).thenReturn(Seq.empty)
        when(setupBookingRequest.campaignInfo).thenReturn(Some(CampaignInfoRequest(Some(1), 51, "CITI4THNIGHT")))

        mockPropertyServiceRetrieveProperties(Seq[BookingPropertiesData](bookingPropertyProduct()))
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.CitiUS)
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromUpi(
            any(),
            any(),
            any(),
            any(),
            any()
          )
        ).thenReturn(Future.successful(cartPriceBreakdown))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(mockPropertyRequest.id).thenReturn("1")
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
        when(productsRequest.carRequestsOpt).thenReturn(None)
        when(setupBookingRequest.productsRequest.bookingToken).thenReturn(Some(expectedToken))

        when(setupBookingRequest.productsRequest.getCartToken).thenReturn(Some(cartToken))

        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.guests).thenReturn(None)
        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(mockPropertyRequest)
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              any(),
              any(),
              eqTo(chargeCurrency),
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Some(paymentRequest)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())

          result.isPromotionCodeEligible shouldBe true
        }
      }

      "return true for ClubTravel isPartnerPromotionCodeEligible with ClubTravel property parameters" in {
        val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))

        when(setupBookingContext.requestContext).thenReturn(requestContext)
        when(requestContext.featureAware).thenReturn(Some(featureAware))

        val partnerClaim = Some("some-partner-claim")
        val loyaltyRequest = Some(
          LoyaltyRequest(
            partnerClaimToken = partnerClaim
          )
        )
        when(setupBookingRequest.loyaltyRequest).thenReturn(loyaltyRequest)

        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        when(setupBookingRequest.productsRequest.activityRequests).thenReturn(Option.empty)
        when(setupBookingRequest.productsRequest.flightRequests).thenReturn(Seq.empty)
        when(setupBookingRequest.campaignInfo).thenReturn(Some(CampaignInfoRequest(Some(1), 51, "CITI4THNIGHT")))
        when(mockCampaignService.getCampaignInfo(any(), any(), any())(any(), any()))
          .thenReturn(
            Future.successful(
              Seq(CampaignInfoInternal(CampaignInfo(5, 1, "TEST", DiscountType.Percent, 5.0, None), None))
            )
          )
        val childRoomWithCampaign     = baseChildrenRoom().copy(promotionEligible = Some(true))
        val masterRoomWithCampaign    = baseMasterRoom().copy(childrenRooms = List(childRoomWithCampaign))
        val masterRoomWithCampaignSeq = Seq[EnrichedMasterRoom](masterRoomWithCampaign)
        val propertiesWithCampaign    = baseProperty().copy(masterRooms = masterRoomWithCampaignSeq)
        val bookingPropertyProductWithCampaign = bookingPropertyProduct().copy(papiProperties =
          Some(
            transformers.Properties(
              property = Seq(propertiesWithCampaign),
              debug = None,
              dfMetaResult = DFMetaResult(Some(ResponseStateToken("", true, Some("pricingRequestId-1"))))
            )
          )
        )

        mockPropertyServiceRetrieveProperties(
          Seq[BookingPropertiesData](bookingPropertyProductWithCampaign.copy(id = "1"))
        )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.ClubTravel)
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromUpi(
            any(),
            any(),
            any(),
            any(),
            any()
          )
        ).thenReturn(Future.successful(cartPriceBreakdown))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(mockPropertyRequest.id).thenReturn("23")
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
        when(productsRequest.carRequestsOpt).thenReturn(None)
        when(setupBookingRequest.productsRequest.bookingToken).thenReturn(Some(expectedToken))

        when(setupBookingRequest.productsRequest.getCartToken).thenReturn(Some(cartToken))

        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))
        when(mockPropertyRequest.guests).thenReturn(None)
        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(mockPropertyRequest)
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              any(),
              any(),
              eqTo(chargeCurrency),
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(Some(paymentRequest)),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())

          result.isPromotionCodeEligible shouldBe true
        }
      }

      "apply promo to only 1 property in multi property booking with Clubtravel parameters" in {
        val partnerClaim = Some("some-partner-claim")
        val loyaltyRequest = Some(
          LoyaltyRequest(
            partnerClaimToken = partnerClaim
          )
        )
        when(setupBookingRequest.loyaltyRequest).thenReturn(loyaltyRequest)

        val partnerDiscountRequest = DiscountRequest(
          None,
          Some(List(CampaignInfoInternal(CampaignInfo(1, 0, "PARTNERPROMO", DiscountType.None, 0, None), None)))
        )
        val mockPartnerPromoUtils = mock[PartnerPromoUtils]
        when(mockPartnerPromoUtils.isPartnerPromoEligible).thenReturn(true)
        when(mockPartnerPromoUtils.getPartnerDiscountRequest(any(), any())).thenReturn(Some(partnerDiscountRequest))
        when(mockPartnerPromoUtils.getPartnerPromoEligiblePropertyId(any(), any())).thenReturn(Some("2"))
        when(mockPartnerPromoUtils.isPropertyEligibleForPartnerPromo(any(), eqTo(true))).thenReturn(true)
        when(mockPartnerPromoUtils.isPropertyEligibleForPartnerPromo(any(), eqTo(false))).thenReturn(false)

        when(mockPartnerPromoService.getPartnerPromoUtils(any[WhiteLabelInfo])(any[RequestContext]))
          .thenReturn(mockPartnerPromoUtils)
        val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        when(setupBookingRequest.productsRequest.activityRequests).thenReturn(Option.empty)
        when(setupBookingRequest.productsRequest.flightRequests).thenReturn(Seq.empty)
        when(setupBookingRequest.campaignInfo).thenReturn(Some(CampaignInfoRequest(Some(1), 51, "PARTNERPROMO")))

        when(
          propertyService.retrieveProperties(
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(Some(partnerDiscountRequest)),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(
            any()
          )
        ).thenReturn(
          Future.successful(
            Seq[BookingPropertiesData](
              bookingPropertyProductWithCampaignPromotion()
            )
          )
        )

        when(
          propertyService.retrieveProperties(
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(None),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(
            any()
          )
        ).thenReturn(
          Future.successful(
            Seq[BookingPropertiesData](
              bookingPropertyProduct()
            )
          )
        )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.ClubTravel)
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromUpi(
            any(),
            any(),
            any(),
            any(),
            any()
          )
        ).thenReturn(Future.successful(cartPriceBreakdown))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(mockPropertyRequest.id).thenReturn("1")

        val product2 = mock[PropertyRequestItem]
        when(product2.id).thenReturn("2")

        when(productsRequest.carRequestsOpt).thenReturn(None)
        when(setupBookingRequest.productsRequest.bookingToken).thenReturn(Some(expectedToken))

        when(setupBookingRequest.productsRequest.getCartToken).thenReturn(Some(cartToken))
        when(setupBookingRequest.enabledFeatures).thenReturn(Some(Seq("PaymentMethodFee")))

        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.guests).thenReturn(None)

        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(mockPropertyRequest)
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

        when(product2.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(product2.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(product2.guests).thenReturn(None)

        val pricingRequest2 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest2))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest2))).thenReturn(propertySearchCriteria)
        when(product2.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(product2)
        when(pricingRequest2.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest2.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest2)
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, product2))

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(propertyService, times(2))
            .retrieveProperties(
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(any())

          result.properties.size shouldBe 2
          result.properties
            .filter(_.id == "1")
            .head
            .papiProperties
            .get
            .property
            .head
            .masterRooms
            .head
            .childrenRooms
            .head
            .campaignPromotions shouldBe None
          result.properties
            .filter(_.id == "2")
            .head
            .papiProperties
            .get
            .property
            .head
            .masterRooms
            .head
            .childrenRooms
            .head
            .campaignPromotions shouldBe Some(
            List(
              enrichedPartnerCampaign
            )
          )
        }
      }

      "return true for ClubTravel isPartnerPromotionCodeEligible with ClubTravel flight parameters" in {
        val mockPromotionInfoResponse = MockFlightsPricingData.promotionInfoResponse.copy(
          campaignId = PromotionCampaignIds(111, 222),
          isValid = true
        )
        val flightProductItem1 =
          flightConfirmationData.copy(id = "3", isCompleted = true, campaignInfo = Some(mockPromotionInfoResponse))
        val flightProductItem2 = flightConfirmationData.copy(id = "4", isCompleted = true)
        mockFlightRepository("3", flightProductItem1)
        mockFlightRepository("4", flightProductItem2)

        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.ClubTravel)
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(ccCampaignMock))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq.empty)
        when(productsRequest.activityRequests).thenReturn(None)
        when(productsRequest.flightRequests)
          .thenReturn(Seq(FlightRequestItem(Some("3"), None, None), FlightRequestItem(Some("4"), None, None)))
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(Map(0 -> flightProductItem1, 1 -> flightProductItem2)),
            any(),
            any(),
            any()
          )(any())
        ).thenReturn(Future.successful(cartPriceBreakdown))

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          result.properties.length shouldBe 0
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 1000d
          result.flights.length shouldBe 2
          result.priceConfirmed shouldBe true
          result.flights.map(_.id) should contain theSameElementsAs Seq("3", "4")
        }

      }

      "call PAPI with correct parameters" in {
        val propertyCartProduct = BookingMockHelper.cartProduct.modify(_.info.`type`).setTo(ItemEntries.Property)
        val childrenRoomsWithPropertyCartProduct: EnrichedChildRoom =
          baseChildrenRoom().modify(_.cart.each.products).setTo(Seq(propertyCartProduct, propertyCartProduct))

        val propertyProduct = bookingPropertyProduct()
          .modifyAll(
            _.papiProperties.each.property.each.masterRooms.each.childrenRooms.each,
            _.papiProperties.each.property.each.rooms.each
          )
          .setTo(childrenRoomsWithPropertyCartProduct)
        val paymentRequest = PaymentRequest(selectedPaymentMethod = Some(MPBPaymentMethod.MasterCard.value))
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        when(setupBookingRequest.productsRequest.activityRequests).thenReturn(Option.empty)
        when(setupBookingRequest.productsRequest.flightRequests).thenReturn(Seq.empty)
        when(setupBookingRequest.loyaltyRequest).thenReturn(None)
        mockPropertyServiceRetrieveProperties(
          Seq[BookingPropertiesData](propertyProduct),
          Seq[BookingPropertiesData](propertyProduct.copy(id = "2"))
        )
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromUpi(
            any(),
            any(),
            any(),
            any(),
            any()
          )
        ).thenReturn(Future.successful(cartPriceBreakdown))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest, mockPropertyRequest))
        when(productsRequest.carRequestsOpt).thenReturn(None)

        when(setupBookingRequest.productsRequest.getCartToken).thenReturn(Some(cartToken))
        when(setupBookingRequest.productsRequest.cartPricingContext).thenReturn(None)
        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.guests).thenReturn(None)
        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(mockPropertyRequest)
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

        val mockPartnerPromoUtils = mock[PartnerPromoUtils]
        when(mockPartnerPromoUtils.isPartnerPromoEligible).thenReturn(false)
        when(mockPartnerPromoUtils.getPartnerDiscountRequest(any(), any())).thenReturn(None)
        when(mockPartnerPromoUtils.getPartnerPromoEligiblePropertyId(any(), any())).thenReturn(None)

        when(mockPartnerPromoService.getPartnerPromoUtils(any[WhiteLabelInfo])(any[RequestContext]))
          .thenReturn(mockPartnerPromoUtils)

        val cardPaymentParams = Some(
          CardPaymentRequestParameter(
            paymentCurrency = "USD",
            creditCardCurrency = "THB",
            chargedDateOption = Some(2),
            countryIdOfIssuingBank = Some(101)
          )
        )

        val chargeCurrency: CurrencyCode = "USD"
        productsFacade
          .composeProductData(setupBookingRequest, chargeCurrency, cardPaymentParams = cardPaymentParams)
          .map { result =>
            verify(propertyService, times(2))
              .retrieveProperties(
                requestItems = any(),
                devicePlatform = any(),
                chargeCurrency = any(),
                packageRequest = any(),
                giftCardRedeemRequest = any(),
                discountRequest = any(),
                cardPaymentParams = eqTo(cardPaymentParams),
                externalLoyaltyRequest = any(),
                paymentRequest = any(),
                shouldClearPriceGuranteeToken = any(),
                cartRequest = any(),
                stateId = any(),
                partnerClaimToken = any(),
                rateCategoryIds = any(),
                capiToken = any(),
                consumerFintechRequirement = any(),
                userTaxCountryCode = any(),
                cashbackRedemptionParameter = any(),
                additionalPricingParameter = any()
              )(any())

            result.properties.length shouldBe 2
          }
      }
      "map Total Savings when single flight request with protection when UNIBF-946 B" in {

        val flightProductItem1 = flightConfirmationData
          .copy(id = "3", isCompleted = true)
          .modify(_.flightPricing.each)
          .setTo(MockFlightsPricingData.searchResponseCurrencyPricingUSD)
          .modify(_.flightPricing.each.each.totalDiscount)
          .setTo(Some(PricingDiscount(1, 1, 5d, 100, 50)))
        mockFlightRepository("3", flightProductItem1)

        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(ccCampaignMock))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq.empty)
        when(productsRequest.activityRequests).thenReturn(None)
        when(productsRequest.flightRequests)
          .thenReturn(Seq(FlightRequestItem(Some("3"), None, None)))
        when(productsRequest.tripProtectionRequests)
          .thenReturn(Some(Seq(mock[TripProtectionRequestItem])))
        when(productsRequest.cartPricingContext).thenReturn(None)
        when(productsRequest.getCartToken).thenReturn(None)
        Mockito.doCallRealMethod().when(productsRequest).hasFlight
        Mockito.doCallRealMethod().when(productsRequest).isOnlyFlightRequest
        when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(true)
        when(
          mockPriceBreakdownService.getPriceBreakdownForFlight(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )
        ).thenReturn(Future.successful(priceBreakdown))
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(Seq.empty))

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          result.properties.length shouldBe 0
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 2000d
          result.priceDisplayType shouldBe Some(PriceDisplayType.SingleFlight)
          result.flights.length shouldBe 1
          result.priceConfirmed shouldBe true
          result.totalSavings shouldBe Some(TotalSavings(com.agoda.bapi.common.token.Money(100.0, "USD"), 5))
          result.flights.map(_.id) should contain theSameElementsAs Seq("3")
        }
      }
      "do not map Total Savings when single flight request with protection when UNIBF-946 A" in {

        val flightProductItem1 = flightConfirmationData
          .copy(id = "3", isCompleted = true)
          .modify(_.flightPricing.each)
          .setTo(MockFlightsPricingData.searchResponseCurrencyPricingUSD)
          .modify(_.flightPricing.each.each.totalDiscount)
          .setTo(Some(PricingDiscount(1, 1, 5d, 100, 50)))
        mockFlightRepository("3", flightProductItem1)

        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(ccCampaignMock))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq.empty)
        when(productsRequest.activityRequests).thenReturn(None)
        when(productsRequest.flightRequests)
          .thenReturn(Seq(FlightRequestItem(Some("3"), None, None)))
        when(productsRequest.tripProtectionRequests)
          .thenReturn(Some(Seq(mock[TripProtectionRequestItem])))
        Mockito.doCallRealMethod().when(productsRequest).hasFlight
        Mockito.doCallRealMethod().when(productsRequest).isOnlyFlightRequest
        when(productsRequest.getCartToken).thenReturn(None)
        when(productsRequest.cartPricingContext).thenReturn(None)
        when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(false)
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(Map(0 -> flightProductItem1)),
            any(),
            any(),
            any()
          )(any())
        ).thenReturn(Future.successful(cartPriceBreakdown))
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(Seq.empty))

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          result.properties.length shouldBe 0
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 1000d
          result.priceDisplayType shouldBe Some(PriceDisplayType.MultiProduct)
          result.flights.length shouldBe 1
          result.priceConfirmed shouldBe true
          result.totalSavings shouldBe None
          result.flights.map(_.id) should contain theSameElementsAs Seq("3")
        }
      }
      "map Total Savings when single flight request with protection when UNIBF-946 B and single flight with no protection request" in {

        val flightProductItem1 = flightConfirmationData
          .copy(id = "3", isCompleted = true)
          .modify(_.flightPricing.each)
          .setTo(MockFlightsPricingData.searchResponseCurrencyPricingUSD)
          .modify(_.flightPricing.each.each.totalDiscount)
          .setTo(Some(PricingDiscount(1, 1, 5d, 100, 50)))
        mockFlightRepository("3", flightProductItem1)

        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(ccCampaignMock))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq.empty)
        when(productsRequest.activityRequests).thenReturn(None)
        when(productsRequest.flightRequests)
          .thenReturn(Seq(FlightRequestItem(Some("3"), None, None)))
        when(productsRequest.tripProtectionRequests)
          .thenReturn(None)
        when(productsRequest.getCartToken).thenReturn(None)
        Mockito.doCallRealMethod().when(productsRequest).hasFlight
        Mockito.doCallRealMethod().when(productsRequest).isOnlyFlightRequest
        when(productsRequest.cartPricingContext).thenReturn(None)

        when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(true)
        when(
          mockPriceBreakdownService.getPriceBreakdownForFlight(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )
        ).thenReturn(Future.successful(priceBreakdown))
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(Seq.empty))
        when(productsRequest.isOnlyFlightRequest).thenCallRealMethod()

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          result.properties.length shouldBe 0
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 2000d
          result.priceDisplayType shouldBe Some(PriceDisplayType.SingleFlight)
          result.flights.length shouldBe 1
          result.priceConfirmed shouldBe true
          result.totalSavings shouldBe Some(TotalSavings(com.agoda.bapi.common.token.Money(100.0, "USD"), 5))
          result.flights.map(_.id) should contain theSameElementsAs Seq("3")
        }
      }
      "do map Total Savings when single flight request with protection when UNIBF-946 B and single flight protection request and cartPricingContext nonEmpty " in {

        val flightProductItem1 = flightConfirmationData
          .copy(id = "3", isCompleted = true)
          .modify(_.flightPricing.each)
          .setTo(MockFlightsPricingData.searchResponseCurrencyPricingUSD)
          .modify(_.flightPricing.each.each.totalDiscount)
          .setTo(Some(PricingDiscount(1, 1, 5d, 100, 50)))
        mockFlightRepository("3", flightProductItem1)

        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(ccCampaignMock))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq.empty)
        when(productsRequest.activityRequests).thenReturn(None)
        when(productsRequest.flightRequests)
          .thenReturn(Seq(FlightRequestItem(Some("3"), None, None)))
        when(productsRequest.tripProtectionRequests)
          .thenReturn(None)
        when(productsRequest.getCartToken).thenReturn(None)
        when(productsRequest.cartPricingContext).thenReturn(Some(CartPricingContext(None, None)))
        Mockito.doCallRealMethod().when(productsRequest).hasFlight
        Mockito.doCallRealMethod().when(productsRequest).isOnlyFlightRequest
        when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(true)
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(Map(0 -> flightProductItem1)),
            any(),
            any(),
            any()
          )(any())
        ).thenReturn(Future.successful(cartPriceBreakdown))
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(Seq.empty))

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          result.properties.length shouldBe 0
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 1000d
          result.priceDisplayType shouldBe Some(PriceDisplayType.MultiProduct)
          result.flights.length shouldBe 1
          result.priceConfirmed shouldBe true
          result.totalSavings shouldBe None
          result.flights.map(_.id) should contain theSameElementsAs Seq("3")
        }
      }

      "map Cart price breakdown when UNIBF-946 A " in {
        reset(mockPriceBreakdownService, productsRequest)

        val flightProductItem1 = flightConfirmationData
          .copy(id = "3", isCompleted = true)

        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        mockFlightRepository("3", flightProductItem1)
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(ccCampaignMock))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq.empty)
        when(productsRequest.carRequestsOpt).thenReturn(None)
        when(productsRequest.activityRequests).thenReturn(None)
        when(productsRequest.flightRequests)
          .thenReturn(Seq(FlightRequestItem(Some("3"), None, None)))
        when(productsRequest.tripProtectionRequests)
          .thenReturn(None)
        when(productsRequest.getCartToken).thenReturn(None)
        when(productsRequest.cartPricingContext).thenReturn(None)
        when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(false)
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(Map(0 -> flightProductItem1)),
            any(),
            any(),
            any()
          )(any())
        ).thenReturn(Future.successful(cartPriceBreakdown))
        when(productsRequest.isOnlyFlightRequest).thenCallRealMethod()

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(mockPriceBreakdownService, times(0))
            .getPriceBreakdownForFlight(any(), any(), any(), any(), any(), any(), any(), any())
          verify(mockPriceBreakdownService, times(1))
            .getPriceBreakdownForCartFromNonUpi(any(), any(), any(), any(), any(), any(), any(), any(), any())(any())

          result.totalPriceDisplay.get.value.get.`type` shouldBe PriceBreakdownType.PriceSummary
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 1000d
          result.priceDisplayType shouldBe Some(PriceDisplayType.MultiProduct)
        }
      }
      "map Flight price breakdown when: single flight request with protection from Flight BF(no cart pricing context) with UNIBF-946 B " in {
        reset(mockPriceBreakdownService, productsRequest)

        mockFlightRepository("3", flightConfirmationData)

        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(ccCampaignMock))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq.empty)
        when(productsRequest.carRequestsOpt).thenReturn(None)
        when(productsRequest.activityRequests).thenReturn(None)
        when(productsRequest.flightRequests)
          .thenReturn(Seq(FlightRequestItem(Some("3"), None, None)))
        when(productsRequest.tripProtectionRequests)
          .thenReturn(None)
        when(productsRequest.getCartToken).thenReturn(None)
        when(productsRequest.cartPricingContext).thenReturn(None)
        when(productsRequest.isOnlyFlightRequest).thenCallRealMethod()
        when(productsRequest.hasFlight).thenCallRealMethod()
        when(productsRequest.hasProperty).thenCallRealMethod()
        when(productsRequest.hasVehicle).thenCallRealMethod()
        when(productsRequest.hasActivity).thenCallRealMethod()
        when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(true)

        when(
          mockPriceBreakdownService.getPriceBreakdownForFlight(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )
        ).thenReturn(Future.successful(priceBreakdown))

        productsFacade.composeProductData(setupBookingRequest, hkdCurrency).map { result =>
          verify(mockPriceBreakdownService, times(1))
            .getPriceBreakdownForFlight(any(), any(), any(), any(), any(), any(), any(), any())
          verify(mockPriceBreakdownService, times(0))
            .getPriceBreakdownForCart(any(), any(), any(), any(), any(), any(), any())(any())
          verify(mockPriceBreakdownService, times(0)).getPriceBreakdownForCartFromUpi(any(), any(), any(), any(), any())
          verify(mockPriceBreakdownService, times(0))
            .getPriceBreakdownForCartFromNonUpi(any(), any(), any(), any(), any(), any(), any(), any(), any())(any())

          result.totalPriceDisplay.get.value.get.`type` shouldBe PriceBreakdownType.TotalPrice
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 2000d
          result.priceDisplayType shouldBe Some(PriceDisplayType.SingleFlight)
        }
      }
      "map Cart price breakdown when: single flight request with protection from Cart BF(has cart pricing context) when UNIBF-946 B and single flight with no protection request" in {
        reset(mockPriceBreakdownService, productsRequest)

        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        mockFlightRepository("3", flightConfirmationData)
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(ccCampaignMock))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq.empty)
        when(productsRequest.carRequestsOpt).thenReturn(None)
        when(productsRequest.activityRequests).thenReturn(None)
        when(productsRequest.flightRequests)
          .thenReturn(Seq(FlightRequestItem(Some("3"), None, None)))
        when(productsRequest.tripProtectionRequests)
          .thenReturn(None)
        when(productsRequest.getCartToken).thenReturn(None)
        when(productsRequest.cartPricingContext).thenReturn(Some(CartPricingContext(None, None)))
        when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(true)
        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(Map(0 -> flightConfirmationData)),
            any(),
            any(),
            any()
          )(any())
        ).thenReturn(Future.successful(cartPriceBreakdown))
        when(productsRequest.isOnlyFlightRequest).thenCallRealMethod()

        productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { result =>
          verify(mockPriceBreakdownService, times(0))
            .getPriceBreakdownForFlight(any(), any(), any(), any(), any(), any(), any(), any())
          verify(mockPriceBreakdownService, times(1))
            .getPriceBreakdownForCartFromNonUpi(any(), any(), any(), any(), any(), any(), any(), any(), any())(any())

          result.totalPriceDisplay.get.value.get.`type` shouldBe PriceBreakdownType.PriceSummary
          result.totalPriceDisplay.get.value.get.amount.amount shouldBe 1000d
          result.priceDisplayType shouldBe Some(PriceDisplayType.MultiProduct)
        }
      }

      "RebookAndCancel - map Rebook And Cancel Data from Request for Single Property" in {

        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(setupBookingRequest.productsRequest.cartContext).thenReturn(Some(CartContext("")))
        when(setupBookingRequest.productsRequest.cartPricingContext).thenReturn(Some(CartPricingContext(None, None)))
        val rebookAndCancelRequest = RebookAndCancelRequest(
          tokenMessage = TokenMessage(token = "tolken", version = 1)
        )

        val mockRequirement = RebookAndCancelRequirement(
          originalBookingId = 1,
          originalItineraryId = 1
        )

        when(setupBookingRequest.rebookAndCancelRequest).thenReturn(Some(rebookAndCancelRequest))

        when(mockRebookAndCancelService.getRebookAndCancelRequirement(any())(any())).thenReturn(Some(mockRequirement))
        val mockFlightConfirmationData = mock[FlightConfirmationData]
        when(setupBookingRequest.productsRequest.flightRequests).thenReturn(Seq.empty)
        when(mockFlightConfirmationData.hasFlight).thenReturn(false)

        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(productsRequest.propertyRequests).thenReturn(Seq(mockPropertyRequest))
        when(productsRequest.carRequestsOpt).thenReturn(None)

        when(setupBookingRequest.productsRequest.getCartToken).thenReturn(None)
        when(setupBookingRequest.productsRequest.cartPricingContext).thenReturn(None)
        when(propertySearchCriteria.propertyId).thenReturn(Some(defaultPropertyId))

        val durationRequest = mock[DurationRequest]
        when(durationRequest.lengthOfStay).thenReturn(2)
        when(propertySearchCriteria.durationRequest).thenReturn(durationRequest)

        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.propertySearchCriteria).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.guests).thenReturn(None)
        val pricingRequest1 = mock[PricingRequest]
        when(propertySearchCriteria.pricingRequest).thenReturn(Some(pricingRequest1))
        when(propertySearchCriteria.copy(pricingRequest = Some(pricingRequest1))).thenReturn(propertySearchCriteria)
        when(mockPropertyRequest.copy(propertySearchCriteria = propertySearchCriteria)).thenReturn(mockPropertyRequest)
        when(pricingRequest1.dfFeatureFlags).thenReturn(Seq.empty)
        when(pricingRequest1.copy(dfFeatureFlags = Seq.empty)).thenReturn(pricingRequest1)

        when(
          flightRepository.retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
        ).thenReturn(Future.successful(mockFlightConfirmationData))

        when(setupBookingRequest.productsRequest.activityRequests).thenReturn(Option.empty)
        when(setupBookingRequest.productsRequest.flightRequests).thenReturn(Seq.empty)
        when(setupBookingRequest.loyaltyRequest).thenReturn(None)

        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
        )
          .thenReturn(Future.successful(priceBreakdown))

        productsFacade
          .composeProductData(
            request = setupBookingRequest,
            chargeCurrency = chargeCurrency,
            essCountryCode = Some("THB"),
            chargeOption = Some(PayNow)
          )(setupBookingContext)
          .map { result =>
            result.rebookAndCancelData shouldBe Some(
              RebookAndCancelData(
                originalBookingId = 1,
                originalItineraryId = 1
              )
            )

            verify(propertyService, times(1))
              .retrieveProperties(
                requestItems = any(),
                devicePlatform = any(),
                chargeCurrency = any(),
                packageRequest = any(),
                giftCardRedeemRequest = any(),
                discountRequest = any(),
                cardPaymentParams = any(),
                externalLoyaltyRequest = any(),
                paymentRequest = any(),
                shouldClearPriceGuranteeToken = any(),
                cartRequest = any(),
                stateId = any(),
                partnerClaimToken = any(),
                rateCategoryIds = any(),
                capiToken = any(),
                consumerFintechRequirement = any(),
                userTaxCountryCode = any(),
                cashbackRedemptionParameter = any(),
                additionalPricingParameter = any()
              )(any())

            result.cart shouldBe None
            result.properties.length shouldBe 1
            result.flights shouldBe empty
            result.activities shouldBe empty

            result.rebookAndCancelData shouldBe Some(
              RebookAndCancelData(
                originalBookingId = 1,
                originalItineraryId = 1
              )
            )
          }
      }
      "RebookAndCancel - create booking token with RebookAndCancel for single Property" in {

        val rebookAndCancel = RebookAndCancelData(1, 1)
        val input = overrideMockProduct.copy(
          priceConfirmed = true,
          rebookAndCancelData = Some(rebookAndCancel)
        )

        when(mockProductRequest.cartContext).thenReturn(Some(CartContext("")))

        Future
          .fromTry(
            productsFacade.createProductToken(
              input,
              isNewsletterOptedIn = false,
              productsRequest = mockProductRequest
            )(setupBookingContext)
          )
          .map { data =>
            verify(productTokenUtils, times(1)).createMultiProductCreationBookingToken(
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              eqTo(false),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              rebookAndCancelData = eqTo(Some(rebookAndCancel)),
              any()
            )(eqTo(setupBookingContext))
            print(data)
            succeed
          }
      }

      "should return selectedPaymentMethodId as None and map selectedCreditCardTypeId correctly when migrateFlightToCartFlow is false" in {
        reset(mockPriceBreakdownService, productsRequest)
        mockFlightRepository("3", flightConfirmationData)

        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(ccCampaignMock))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        when(paymentRequest.selectedPaymentMethod).thenReturn(Some(MPBPaymentMethod.Visa.value))
        when(paymentRequest.ccId).thenReturn(None)
        when(productsRequest.propertyRequests).thenReturn(Seq.empty)
        when(productsRequest.carRequestsOpt).thenReturn(None)
        when(productsRequest.activityRequests).thenReturn(None)
        when(productsRequest.flightRequests)
          .thenReturn(Seq(FlightRequestItem(Some("3"), None, None)))
        when(productsRequest.tripProtectionRequests)
          .thenReturn(None)
        when(productsRequest.getCartToken).thenReturn(None)
        when(productsRequest.cartPricingContext).thenReturn(None)
        when(productsRequest.isOnlyFlightRequest).thenCallRealMethod()
        when(productsRequest.hasFlight).thenCallRealMethod()
        when(productsRequest.hasProperty).thenCallRealMethod()
        when(productsRequest.hasVehicle).thenCallRealMethod()
        when(productsRequest.hasActivity).thenCallRealMethod()

        when(
          mockPriceBreakdownService.getPriceBreakdownForCartFromNonUpi(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
        ).thenReturn(Future.successful(cartPriceBreakdown))
        when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(false)
        productsFacade.composeProductData(setupBookingRequest, hkdCurrency).map { _ =>
          verify(flightRepository).retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(Some(MPBPaymentMethod.Visa.value)),
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(None),
            any(),
            any()
          )(any())
          succeed
        }
      }
      "should map selectedPaymentMethodId correctly and return selectedCreditCardTypeId as None when migrateFlightToCartFlow is true" in {
        reset(mockPriceBreakdownService, productsRequest)
        mockFlightRepository("3", flightConfirmationData)

        when(featureAware.supportPriceDisplayTypes).thenReturn(true)
        when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.Cart)
        when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
          .thenReturn(Future.successful(ccCampaignMock))
        when(setupBookingRequest.productsRequest).thenReturn(productsRequest)
        when(setupBookingRequest.paymentRequest).thenReturn(Some(paymentRequest))
        when(paymentRequest.selectedPaymentMethod).thenReturn(Some(MPBPaymentMethod.Visa.value))
        when(paymentRequest.ccId).thenReturn(None)
        when(productsRequest.propertyRequests).thenReturn(Seq.empty)
        when(productsRequest.carRequestsOpt).thenReturn(None)
        when(productsRequest.activityRequests).thenReturn(None)
        when(productsRequest.flightRequests)
          .thenReturn(Seq(FlightRequestItem(Some("3"), None, None)))
        when(productsRequest.tripProtectionRequests)
          .thenReturn(None)
        when(productsRequest.getCartToken).thenReturn(None)
        when(productsRequest.cartPricingContext).thenReturn(None)
        when(productsRequest.isOnlyFlightRequest).thenCallRealMethod()
        when(productsRequest.hasFlight).thenCallRealMethod()
        when(productsRequest.hasProperty).thenCallRealMethod()
        when(productsRequest.hasVehicle).thenCallRealMethod()
        when(productsRequest.hasActivity).thenCallRealMethod()

        when(
          mockPriceBreakdownService.getPriceBreakdownForFlight(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )
        ).thenReturn(Future.successful(priceBreakdown))

        when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(true)
        productsFacade.composeProductData(setupBookingRequest, hkdCurrency).map { _ =>
          verify(flightRepository).retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(None),
            any(),
            any(),
            any(),
            any(),
            any(),
            eqTo(Some(MPBPaymentMethod.Visa.value)),
            any(),
            any()
          )(any())
          succeed
        }
      }
    }
  }

  val requestedPartnerClaim = Some("partner-claim")

  val expectedPoints = Vector(
    Points(
      pointType = PointsType.ExternalLoyalty,
      pointAttributes = PointsAttributes(
        usdCurrency,
        BigDecimal.valueOf(50.0d),
        Some(thbCurrency),
        Some(BigDecimal.valueOf(255.0d)),
        Some(
          ExternalLoyaltyInfo(
            loyaltyToken = MockFlightsPricingData.loyaltyToken,
            fiatUserPayable = Some(50.0d),
            userPointsPayable = Some(100.0d),
            partnerClaim = requestedPartnerClaim,
            pointsToEarn = Some(5.0d)
          )
        )
      )
    )
  )

  "bookingFlow: FlightWithProtection with retry payment, should retrieve flights from retry context when available and not call flights api" in {
    val mockFlightConfirmationData = mock[FlightConfirmationData]
    when(mockFlightConfirmationData.flightPricing)
      .thenReturn(Some(MockFlightsPricingData.searchResponseCurrencyPricingUSD))
    when(mockFlightConfirmationData.priceChange).thenReturn(None)
    when(mockFlightConfirmationData.campaignInfo).thenReturn(None)
    when(mockFlightConfirmationData.flightItinerary).thenReturn(
      Some(
        FlightItinerary(
          id = "id",
          slices = Seq.empty,
          paymentModel = PaymentModel.Merchant.id,
          acceptedCreditCards = Seq.empty
        )
      )
    )

    when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.FlightWithProtection)
    when(setupBookingContext.retryPaymentContext).thenReturn(
      Some(RetryPaymentContext(flights = Seq(mockFlightConfirmationData)))
    )
    when(setupBookingRequest.isRetryPayment).thenReturn(Some(true))
    when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
      .thenReturn(Future.successful(ccCampaignMock))

    productsFacade.composeProductData(setupBookingRequest, usdCurrency)(setupBookingContext).map { result =>
      verify(flightRepository, never())
        .retrieveFlightConfirmationData(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )(any())

      result.flights shouldBe Seq(mockFlightConfirmationData)
    }
  }

  "bookingFlow: FlightWithProtection with retry payment, should throw error when flights is not available in retry context" in {
    val mockFlightConfirmationData = mock[FlightConfirmationData]
    when(mockFlightConfirmationData.flightPricing)
      .thenReturn(Some(MockFlightsPricingData.searchResponseCurrencyPricingUSD))
    when(mockFlightConfirmationData.priceChange).thenReturn(None)
    when(mockFlightConfirmationData.campaignInfo).thenReturn(None)
    when(mockFlightConfirmationData.flightItinerary).thenReturn(
      Some(
        FlightItinerary(
          id = "id",
          slices = Seq.empty,
          paymentModel = PaymentModel.Merchant.id,
          acceptedCreditCards = Seq.empty
        )
      )
    )

    when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.FlightWithProtection)
    when(setupBookingContext.retryPaymentContext).thenReturn(
      Some(RetryPaymentContext(flights = Seq.empty))
    )
    when(setupBookingRequest.isRetryPayment).thenReturn(Some(true))
    when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
      .thenReturn(Future.successful(ccCampaignMock))

    val result = productsFacade.composeProductData(setupBookingRequest, usdCurrency)(setupBookingContext)
    result.failed.map { e =>
      e shouldBe a[IllegalArgumentException]
      e.getMessage shouldBe "Retry payment flow - but no flights in retry payment context."
    }
  }

  "bookingFlow: FlightWithProtection" should {
    val mockFlightConfirmationData = mock[FlightConfirmationData]

    val testCases = Table(
      ("hasFlight", "hasContent", "flightPaymentModel", "isFlightHackerFare", "withTripProtection", "shouldCall"),
      (true, true, PaymentModel.Merchant.id, false, true, true),
      (false, true, PaymentModel.Merchant.id, true, true, false),
      (true, true, PaymentModel.Agency.id, true, true, false),
      (true, true, PaymentModel.Merchant.id, true, true, true),
      (true, true, PaymentModel.Merchant.id, true, false, true),
      (true, false, PaymentModel.Merchant.id, false, true, false),
      (false, false, PaymentModel.Merchant.id, true, true, false),
      (true, false, PaymentModel.Agency.id, true, true, false),
      (true, false, PaymentModel.Merchant.id, true, true, false),
      (true, false, PaymentModel.Merchant.id, true, false, false)
    )

    forEvery(testCases) {
      (hasFlight, hasContent, flightPaymentModel, isFlightHackerFare, withTripProtection, shouldCall) =>
        s"hasFlight: ${hasFlight}, hasContent: $hasContent, flightPaymentModel: ${flightPaymentModel}, isFlightHackerFare: ${isFlightHackerFare}, withTripProtection: ${withTripProtection} -> shouldCall: ${shouldCall}}" in {
          when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.FlightWithProtection)
          when(mockAddOnService.getAddOns(any(), any(), any(), any())(any()))
            .thenReturn(Future.successful(Seq.empty))
          when(mockFlightConfirmationData.flightPricing)
            .thenReturn(Some(MockFlightsPricingData.searchResponseCurrencyPricingUSD))
          when(
            mockPriceBreakdownService.getPriceBreakdownForFlight(
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )
          )
            .thenReturn(Future.successful(Option(mockPricingTree)))
          when(mockFlightConfirmationData.priceChange).thenReturn(None)
          when(mockFlightConfirmationData.hasFlight).thenReturn(hasFlight)
          when(mockFlightConfirmationData.hasContent).thenReturn(hasContent)
          when(mockFlightConfirmationData.isHackerFare).thenReturn(isFlightHackerFare)
          when(mockFlightConfirmationData.campaignInfo).thenReturn(None)
          when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
            .thenReturn(Future.successful(ccCampaignMock))
          when(mockFlightConfirmationData.flightItinerary).thenReturn(
            Some(
              FlightItinerary(
                id = "id",
                slices = Seq.empty,
                paymentModel = flightPaymentModel,
                acceptedCreditCards = Seq.empty
              )
            )
          )
          when(
            flightRepository
              .retrieveFlightConfirmationData(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
              )(any())
          ).thenReturn(Future.successful(mockFlightConfirmationData))
          when(setupBookingRequest.productsRequest).thenReturn(
            ProductsRequest(
              flightRequests = Seq(FlightRequestItem(Some("test"), Some(ConfirmPriceRequest("test", "test")), None)),
              tripProtectionRequests = Some(
                Seq(
                  TripProtectionRequestItem("123", ProductTypeEnum.Flight, ProtectionRequestItemOptInValue.Purchase)
                )
              )
            )
          )
          when(protectionService.setupProtection(any(), any(), any())(any()))
            .thenReturn(Future.successful(Seq.empty))

          val chargeCurrency = usdCurrency
          productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { _ =>
            if (shouldCall)
              verify(protectionService)
                .setupProtection(setupBookingRequest, mockFlightConfirmationData, chargeCurrency)
            else
              verify(protectionService, times(0)).setupProtection(any(), any(), any())(any())
            succeed
          }
        }
    }
  }

  "bookingFlow: AddOns" should {
    val mockFlightConfirmationData = mock[FlightConfirmationData]

    def setupBasedMock(mockFlightConfirmationData: FlightConfirmationData) = {
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.FlightWithProtection)
      when(mockAddOnService.getAddOns(any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(Seq.empty))
      when(setupBookingContext.requestContext.featureAware).thenReturn(Some(featureAware))
      when(mockFlightConfirmationData.flightPricing)
        .thenReturn(Some(MockFlightsPricingData.searchResponseCurrencyPricingUSD))
      when(
        mockPriceBreakdownService.getPriceBreakdownForFlight(
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any(),
          any()
        )
      )
        .thenReturn(Future.successful(Option(mockPricingTree)))
      when(mockFlightConfirmationData.priceChange).thenReturn(None)
      when(mockFlightConfirmationData.isHackerFare).thenReturn(false)
      when(mockFlightConfirmationData.campaignInfo).thenReturn(None)
      when(mockCampaignService.searchCampaignByCcBinV2(any(), any(), any())(any(), any()))
        .thenReturn(Future.successful(ccCampaignMock))
      when(mockFlightConfirmationData.flightItinerary).thenReturn(
        Some(
          FlightItinerary(
            id = "id",
            slices = Seq.empty,
            paymentModel = PaymentModel.Merchant.id,
            acceptedCreditCards = Seq.empty
          )
        )
      )
      when(
        flightRepository
          .retrieveFlightConfirmationData(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )(any())
      ).thenReturn(Future.successful(mockFlightConfirmationData))

    }

    "Invoking AddOns service" should {
      val testCases = Table(
        ("hasFlight", "hasContent", "isAgency", "shouldCall"),
        (true, true, false, true),
        (true, false, false, false),
        (false, true, false, false),
        (false, false, false, false),
        (true, true, true, false),
        (true, false, true, false),
        (false, true, true, false),
        (false, false, true, false)
      )

      forEvery(testCases) { (hasFlight, hasContent, isAgency, shouldCall) =>
        s"hasFlight: ${hasFlight}, hasContent: $hasContent, isAgency: ${isAgency} -> shouldCall: ${shouldCall}}" in {
          setupBasedMock(mockFlightConfirmationData)

          when(mockFlightConfirmationData.hasFlight).thenReturn(hasFlight)
          when(mockFlightConfirmationData.hasContent).thenReturn(hasContent)
          when(mockFlightConfirmationData.flightItinerary).thenReturn(
            Some(
              FlightItinerary(
                id = "id",
                slices = Seq.empty,
                paymentModel = if (isAgency) PaymentModel.Agency.id else PaymentModel.Merchant.id,
                acceptedCreditCards = Seq.empty
              )
            )
          )

          when(setupBookingRequest.productsRequest).thenReturn(
            ProductsRequest(
              flightRequests = Seq(FlightRequestItem(Some("test"), Some(ConfirmPriceRequest("test", "test")), None)),
              tripProtectionRequests = None,
              tripProtectionRequestsV2 = None
            )
          )
          when(setupBookingRequest.addOnRequestsV2).thenReturn(None)

          when(mockAddOnService.getAddOns(any(), any(), any(), any())(any()))
            .thenReturn(Future.successful(Seq.empty))

          val chargeCurrency = "USD"
          productsFacade.composeProductData(setupBookingRequest, chargeCurrency).map { _ =>
            if (shouldCall)
              verify(mockAddOnService)
                .getAddOns(
                  setupBookingRequest,
                  MainProductData(properties = Seq.empty, flights = Seq(mockFlightConfirmationData), cars = Seq.empty),
                  chargeCurrency
                )
            else
              verify(mockAddOnService, times(0)).getAddOns(any(), any(), any(), any())(any())
            succeed
          }
        }
      }
    }
  }

  "composeProductData with AddOn" should {
    when(mockPropertySetupBookingToken.preBookingId).thenReturn(Some(1L))
    mockPropertyServiceRetrieveProperties(Seq[BookingPropertiesData](bookingPropertyProduct()))

    "getAddOn" in {
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
      when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(None))
      mockPriceBreakdownServiceGetPriceBreakdown()
      productsFacade
        .composeProductData(
          request = setupBookingRequest,
          chargeCurrency = chargeCurrency,
          essCountryCode = Some("THB"),
          chargeOption = Some(PayNow)
        )(setupBookingContext)
        .map { result =>
          verify(propertyService, times(1))
            .retrieveProperties(
              any(),
              any(),
              eqTo(chargeCurrency),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any(),
              any()
            )(
              any()
            )
          val addOnInputArgumentCaptor: ArgumentCaptor[AddOnInput] = ArgumentCaptor.forClass(classOf[AddOnInput])
          verify(mockAddOnFacade, times(1)).getAddOn(addOnInputArgumentCaptor.capture())(any[RequestContext])
          val addOnInput = addOnInputArgumentCaptor.getValue
          addOnInput shouldBe AddOnDefaults.addOnInput
          result.addOnData shouldBe addOnData
        }
    }

    "getAddOnsForCart with CFAR" in {
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
      when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(None))
      when(mockPropertySetupBookingToken.preBookingId).thenReturn(None)

      mockPriceBreakdownServiceGetPriceBreakdown()
      def sampleAddOn = MockAddOnV2Data.sampleAddOn(productType = ProductType.CancelForAnyReason)
      reset(mockAddOnService)

      val bookingPropertyProductWithConfinDetails = bookingPropertyProduct()
      mockPropertyServiceRetrieveProperties(Seq(bookingPropertyProductWithConfinDetails))
      reset(mockPriceBreakdownService)
      mockPriceBreakdownServiceGetPriceBreakdown()
      when(mockAddOnService.getAddOns(any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(Seq(sampleAddOn)))
      productsFacade
        .composeProductData(
          request = setupBookingRequest,
          chargeCurrency = chargeCurrency,
          essCountryCode = Some("THB"),
          chargeOption = Some(PayNow)
        )(setupBookingContext)
        .map { result =>
          val addOnV2InputArgumentCaptor: ArgumentCaptor[Map[ProductType, Seq[PriceBreakdown]]] =
            ArgumentCaptor.forClass(classOf[Map[ProductType, Seq[PriceBreakdown]]])
          verify(mockAddOnService, times(1)).getAddOns(
            any(),
            any(),
            any(),
            any()
          )(any())

          verify(mockPriceBreakdownService, times(1)).getPriceBreakdown(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            addOnV2InputArgumentCaptor.capture(),
            any(),
            any(),
            any(),
            any()
          )(any())
          val addOnV2Value = addOnV2InputArgumentCaptor.getValue
          addOnV2Value.size shouldBe 1
          addOnV2Value.head._1 shouldBe sampleAddOn.productType
          addOnV2Value.head._2 shouldBe sampleAddOn.confirmationData.get.priceBreakdown
        }
    }

    "getAddOnsForCart with CegFastTrack" in {
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
      when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(None))
      when(mockPropertySetupBookingToken.preBookingId).thenReturn(None)
      when(featureAware.CegFastTrackMigration).thenReturn(true)

      mockPriceBreakdownServiceGetPriceBreakdown()
      def sampleAddOn = MockAddOnV2Data.sampleAddOn()
      reset(mockAddOnService)

      val bookingPropertyProductWithConfinDetails = bookingPropertyProduct()
      mockPropertyServiceRetrieveProperties(Seq(bookingPropertyProductWithConfinDetails))
      reset(mockPriceBreakdownService)
      mockPriceBreakdownServiceGetPriceBreakdown()
      when(mockAddOnService.getAddOns(any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(Seq(sampleAddOn)))
      productsFacade
        .composeProductData(
          request = setupBookingRequest,
          chargeCurrency = chargeCurrency,
          essCountryCode = Some("THB"),
          chargeOption = Some(PayNow)
        )(setupBookingContext)
        .map { result =>
          val addOnV2InputArgumentCaptor: ArgumentCaptor[Map[ProductType, Seq[PriceBreakdown]]] =
            ArgumentCaptor.forClass(classOf[Map[ProductType, Seq[PriceBreakdown]]])
          verify(mockAddOnService, times(1)).getAddOns(
            any(),
            any(),
            any(),
            any()
          )(any())

          verify(mockPriceBreakdownService, times(1)).getPriceBreakdown(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            addOnV2InputArgumentCaptor.capture(),
            any(),
            any(),
            any(),
            any()
          )(any())
          val addOnV2Value = addOnV2InputArgumentCaptor.getValue
          addOnV2Value.size shouldBe 1
          addOnV2Value.head._1 shouldBe sampleAddOn.productType
          addOnV2Value.head._2 shouldBe sampleAddOn.confirmationData.get.priceBreakdown
        }
    }

    "filter CEGFastTrack correctly when CegFastTrackMigration is true - should be in addOnDataV2 but not in addOnData" in {
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
      when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(None))
      when(mockPropertySetupBookingToken.preBookingId).thenReturn(None)
      when(featureAware.CegFastTrackMigration).thenReturn(true)

      mockPriceBreakdownServiceGetPriceBreakdown()

      // Create CEGFastTrack add-on V2
      val cegFastTrackAddOnV2 = MockAddOnV2Data.sampleAddOn(productType = ProductType.CEGFastTrack)
      val otherAddOnV2        = MockAddOnV2Data.sampleAddOn(productType = ProductType.TripProtection)
      val addOnsV2            = Seq(cegFastTrackAddOnV2, otherAddOnV2)

      // Create CEGFastTrack add-on V1 (legacy)
      val cegFastTrackAddOnV1 = AddOnProductData(
        productType = ProductType.CEGFastTrack,
        isMultipleChoiceSupported = false,
        choices = Seq.empty,
        coreProductItems = Seq.empty,
        isActive = true,
        accountingEntity = models.pricing.AccountingEntity(1, 2, 3, "test")
      )
      val otherAddOnV1 = AddOnProductData(
        productType = ProductType.TripProtection,
        isMultipleChoiceSupported = false,
        choices = Seq.empty,
        coreProductItems = Seq.empty,
        isActive = true,
        accountingEntity = models.pricing.AccountingEntity(1, 2, 3, "test")
      )
      val addOnDataV1 = AddOnData(products = Seq(cegFastTrackAddOnV1, otherAddOnV1), content = Seq.empty)

      reset(mockAddOnService, mockAddOnFacade)

      val bookingPropertyProductWithConfinDetails = bookingPropertyProduct()
      mockPropertyServiceRetrieveProperties(Seq(bookingPropertyProductWithConfinDetails))
      reset(mockPriceBreakdownService)
      mockPriceBreakdownServiceGetPriceBreakdown()

      when(mockAddOnService.getAddOns(any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(addOnsV2))
      when(mockAddOnFacade.getAddOn(any())(any()))
        .thenReturn(Future.successful(addOnDataV1))

      productsFacade
        .composeProductData(
          request = setupBookingRequest,
          chargeCurrency = chargeCurrency,
          essCountryCode = Some("THB"),
          chargeOption = Some(PayNow)
        )(setupBookingContext)
        .map { result =>
          // Verify that CEGFastTrack is present in addOnDataV2 (V2 add-ons)
          result.addOnDataV2.exists(_.productType == ProductType.CEGFastTrack) shouldBe true
          result.addOnDataV2.count(_.productType == ProductType.CEGFastTrack) shouldBe 1

          // Verify that CEGFastTrack is NOT present in addOnData (V1 add-ons) when migration is enabled
          result.addOnData.products.exists(_.productType == ProductType.CEGFastTrack) shouldBe false

          // Verify other add-ons are still present in both
          result.addOnDataV2.exists(_.productType == ProductType.TripProtection) shouldBe true
          result.addOnData.products.exists(_.productType == ProductType.TripProtection) shouldBe true
        }
    }

    "filter CEGFastTrack correctly when CegFastTrackMigration is false - should be in addOnData but not in addOnDataV2" in {
      when(setupBookingContext.bookingFlowType).thenReturn(BookingFlow.SingleProperty)
      when(mockPropertySetupBookingToken.isRoomHasSwapped).thenReturn(Some(false))
      when(mockTotalSavingsService.getTotalSavings(any(), any())).thenReturn(Future.successful(None))
      when(mockPropertySetupBookingToken.preBookingId).thenReturn(None)
      when(featureAware.CegFastTrackMigration).thenReturn(false)

      mockPriceBreakdownServiceGetPriceBreakdown()

      // Create CEGFastTrack add-on V2
      val cegFastTrackAddOnV2 = MockAddOnV2Data.sampleAddOn(productType = ProductType.CEGFastTrack)
      val otherAddOnV2        = MockAddOnV2Data.sampleAddOn(productType = ProductType.TripProtection)
      val addOnsV2            = Seq(cegFastTrackAddOnV2, otherAddOnV2)

      // Create CEGFastTrack add-on V1 (legacy)
      val cegFastTrackAddOnV1 = AddOnProductData(
        productType = ProductType.CEGFastTrack,
        isMultipleChoiceSupported = false,
        choices = Seq.empty,
        coreProductItems = Seq.empty,
        isActive = true,
        accountingEntity = models.pricing.AccountingEntity(1, 2, 3, "test")
      )
      val otherAddOnV1 = AddOnProductData(
        productType = ProductType.TripProtection,
        isMultipleChoiceSupported = false,
        choices = Seq.empty,
        coreProductItems = Seq.empty,
        isActive = true,
        accountingEntity = models.pricing.AccountingEntity(1, 2, 3, "test")
      )
      val addOnDataV1 = AddOnData(products = Seq(cegFastTrackAddOnV1, otherAddOnV1), content = Seq.empty)

      reset(mockAddOnService, mockAddOnFacade)

      val bookingPropertyProductWithConfinDetails = bookingPropertyProduct()
      mockPropertyServiceRetrieveProperties(Seq(bookingPropertyProductWithConfinDetails))
      reset(mockPriceBreakdownService)
      mockPriceBreakdownServiceGetPriceBreakdown()

      when(mockAddOnService.getAddOns(any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(addOnsV2))
      when(mockAddOnFacade.getAddOn(any())(any()))
        .thenReturn(Future.successful(addOnDataV1))

      productsFacade
        .composeProductData(
          request = setupBookingRequest,
          chargeCurrency = chargeCurrency,
          essCountryCode = Some("THB"),
          chargeOption = Some(PayNow)
        )(setupBookingContext)
        .map { result =>
          // Verify that CEGFastTrack is NOT present in addOnDataV2 (V2 add-ons) when migration is disabled
          result.addOnDataV2.exists(_.productType == ProductType.CEGFastTrack) shouldBe false

          // Verify that CEGFastTrack is present in addOnData (V1 add-ons)
          result.addOnData.products.exists(_.productType == ProductType.CEGFastTrack) shouldBe true
          result.addOnData.products.count(_.productType == ProductType.CEGFastTrack) shouldBe 1

          // Verify other add-ons are still present in both
          result.addOnDataV2.exists(_.productType == ProductType.TripProtection) shouldBe true
          result.addOnData.products.exists(_.productType == ProductType.TripProtection) shouldBe true
        }
    }
  }
}
