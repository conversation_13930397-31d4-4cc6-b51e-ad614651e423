package com.agoda.bapi.server.repository

import com.agoda.adp.messaging.message.Message
import com.agoda.bapi.common.message.{AddOnBookingStateWithItinerary, ProductStateWithItinerary}
import com.agoda.bapi.common.message.creation.BreakDownItemID
import com.agoda.bapi.common.model.ItineraryId
import com.agoda.bapi.common.model.booking.BookingStateMessage.mapBaseBookingRelationshipToBaseBookingRelationshipForMessage
import com.agoda.bapi.common.model.booking._
import com.agoda.bapi.common.model.flight.history.ActionType
import com.agoda.bapi.common.model.multiproduct.{MultiProductBookingGroupDBModel, MultiProductInfoDBModel}
import com.agoda.bapi.common.util.converters.CommonProductConverters
import com.agoda.bapi.common.util.converters.ItineraryConverters.toBaseBookingRelationshipInternal
import com.agoda.bapi.creation.config.ReplicateStateConfig
import com.agoda.bapi.creation.proxy.{BaseBookingDBProxy, GenericAddOnActionDbProxy, GenericAddOnDbProxy}
import com.agoda.bapi.creation.repository.{GenericAddOnRepositoryImpl, MultiProductRepository}
import com.agoda.bapi.common.MessageService
import com.agoda.bapi.common.exception.DbException
import com.agoda.bapi.creation.proxy.db.basebooking.BaseBookingDbReadProxy
import com.agoda.commons.agprotobuf.scalapb.utils.ProtoConverter
import com.agoda.commons.config.dynamic.DynamicObject
import com.agoda.mpb.common.MultiProductType
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpbe.state.booking.BaseBooking
import com.agoda.mpbe.state.product.common.FinancialBreakdown
import mocks.{AddOnModelMock, DBBookingModelHelper}
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito._
import org.scalatest.OptionValues
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar
import com.softwaremill.quicklens._
import org.joda.time.DateTime

import java.util.Date
import scala.concurrent.Future

class GenericAddOnRepositorySpec
    extends AsyncWordSpec
    with Matchers
    with MockitoSugar
    with DBBookingModelHelper
    with AddOnModelMock
    with OptionValues {

  val multiProductBookingGroupDBModel = MultiProductBookingGroupDBModel(
    bookingId = defaultBookingId,
    itineraryId = -2,
    cartId = -3,
    packageId = Some(-4)
  )
  private val datetime = DateTime.now()
  private val mockFinancialBreakdownDb = com.agoda.bapi.common.model.flight.flightModel.Breakdown(
    referenceId = 0,
    breakdownId = 1,
    itineraryId = 1002L,
    bookingType = Some(2),
    bookingId = Some(456),
    actionId = Some(ActionType.Created.id),
    eventDate = datetime,
    itemId = BreakDownItemID.SalesInclusive.id,
    typeId = 301,
    taxFeeId = Some(0),
    quantity = 1,
    localCurrency = "USD",
    localAmount = 100.1,
    exchangeRate = 1,
    usdAmount = 100.1,
    requestedAmount = Some(100.1),
    refBreakdownId = None,
    recStatus = Some(1),
    recCreatedWhen = Some(datetime),
    requestedCurrency = Some("INR")
  )

  val mockMultiProductInfos = Seq(MultiProductInfoDBModel(1, MultiProductType.SingleProtection))
  val mockAddOnProductModel = mockProtectionAddOnProductModel
    .modify(_.product.breakdowns)
    .setTo(Seq(CommonProductConverters.toFinancialBreakdown(mockFinancialBreakdownDb)))
  val mockProtectionAddOnState = AddOnBookingStateWithItinerary(
    itinerary = mockItineraryInternalModel.itinerary,
    itineraryHistories = mockItineraryInternalModel.history,
    payments = mockItineraryInternalModel.payments,
    bookingPayments = mockItineraryInternalModel.bookingPayments,
    relationships = mockItineraryInternalModel.relationships,
    addOns = Seq(mockAddOnProductModel),
    multiProductBookingGroups = Seq(multiProductBookingGroupDBModel)
  )

  "getBaseBooking" should {
    "return base bookings from bkgDbProxy" in {
      val fixture = new GenericAddOnRepositoryFixture {}
      import fixture._

      val itineraryId  = 1234
      val baseBookings = List(BaseBooking(bookingId = 1), BaseBooking(bookingId = 2))

      // Mock bkgDbProxy to return base bookings
      when(addOnsBkgDbProxyMock.getBaseBookingByItineraryId(itineraryId))
        .thenReturn(Future.successful(baseBookings))

      // Call the method and verify the result
      genericAddOnRepository.getBaseBooking(itineraryId).map { result =>
        verify(addOnsBkgDbProxyMock).getBaseBookingByItineraryId(itineraryId)
        verifyNoInteractions(addOnsLocalBkgDbProxyMock)
        result shouldEqual baseBookings
      }
    }

    "return base booking from localBkgProxy when bkgDbProxy throw db exception error" in {
      val fixture = new GenericAddOnRepositoryFixture {}
      import fixture._

      val itineraryId  = 1234
      val baseBookings = List(BaseBooking(bookingId = 1), BaseBooking(bookingId = 2))

      // Mock bkgDbProxy to throw DbException
      when(addOnsBkgDbProxyMock.getBaseBookingByItineraryId(itineraryId))
        .thenReturn(Future.failed(DbException(ErrorCode.UnexpectedDbError)))

      // Mock localBkgProxy to return base bookings
      when(addOnsLocalBkgDbProxyMock.getBaseBookingByItineraryId(itineraryId))
        .thenReturn(Future.successful(baseBookings))

      // Call the method and verify the fallback behavior
      genericAddOnRepository.getBaseBooking(itineraryId).map { result =>
        verify(addOnsBkgDbProxyMock).getBaseBookingByItineraryId(itineraryId)
        verify(addOnsLocalBkgDbProxyMock).getBaseBookingByItineraryId(itineraryId)
        result shouldEqual baseBookings
      }
    }

    "fail when both data sources fail" in {
      val fixture = new GenericAddOnRepositoryFixture {}
      import fixture._
      val itineraryId = 1234

      // Mock bkgDbProxy to throw DbException
      when(addOnsBkgDbProxyMock.getBaseBookingByItineraryId(itineraryId))
        .thenReturn(Future.failed(DbException(ErrorCode.UnexpectedDbError)))

      // Mock localBkgProxy to return base bookings
      when(addOnsLocalBkgDbProxyMock.getBaseBookingByItineraryId(itineraryId))
        .thenReturn(Future.failed(DbException(ErrorCode.UnexpectedDbError)))

      recoverToSucceededIf[DbException](genericAddOnRepository.getBaseBooking(itineraryId)).map { _ =>
        verify(addOnsBkgDbProxyMock).getBaseBookingByItineraryId(itineraryId)
        verify(addOnsLocalBkgDbProxyMock).getBaseBookingByItineraryId(itineraryId)
        succeed
      }
    }
  }

  "sendModelForReplication" should {
    "call messaging send" in {
      val fixture = new GenericAddOnRepositoryFixture {}
      import fixture._

      when(messagingMock.sendMessage(any[Message])).thenReturn(Future.successful(()))
      when(multiProductRepositoryMock.saveMultiProductBookingGroupIfNotExist(multiProductBookingGroupDBModel))
        .thenReturn(Future.successful(multiProductBookingGroupDBModel))

      val expected = BookingStateMessage(
        actionType = defaultProtectionAddOnBookingWorkflowAction.actionTypeId,
        actionId = defaultProtectionAddOnBookingWorkflowAction.actionId,
        bookingType = None,
        bookingId = 0,
        schemaVersion = "1",
        flights = Seq.empty,
        slices = Seq.empty,
        segments = Seq.empty,
        passengers = Seq.empty,
        payments = Seq(
          PaymentForMessage(
            referenceId = mockProtectionAddOnState.payments.head.referenceId,
            paymentId = mockProtectionAddOnState.payments.head.paymentId,
            itineraryId = mockProtectionAddOnState.payments.head.itineraryId,
            actionId = mockProtectionAddOnState.payments.head.actionId,
            creditCardId = mockProtectionAddOnState.payments.head.creditCardId,
            transactionDate = mockProtectionAddOnState.payments.head.transactionDate.toDate,
            transactionType = mockProtectionAddOnState.payments.head.transactionType,
            paymentState = mockProtectionAddOnState.payments.head.paymentState,
            referenceNo = mockProtectionAddOnState.payments.head.referenceNo,
            referenceType = mockProtectionAddOnState.payments.head.referenceType,
            last4Digits = mockProtectionAddOnState.payments.head.last4Digits,
            paymentMethodId = mockProtectionAddOnState.payments.head.paymentMethodId,
            gatewayId = mockProtectionAddOnState.payments.head.gatewayId,
            transactionId = mockProtectionAddOnState.payments.head.transactionId,
            paymentCurrency = mockProtectionAddOnState.payments.head.paymentCurrency,
            paymentAmount = mockProtectionAddOnState.payments.head.paymentAmount,
            amountUsd = mockProtectionAddOnState.payments.head.amountUsd,
            supplierCurrency = mockProtectionAddOnState.payments.head.supplierCurrency,
            supplierAmount = mockProtectionAddOnState.payments.head.supplierAmount,
            exchangeRateSupplierToPayment = mockProtectionAddOnState.payments.head.exchangeRateSupplierToPayment,
            creditCardCurrency = mockProtectionAddOnState.payments.head.creditCardCurrency,
            upliftAmount = mockProtectionAddOnState.payments.head.upliftAmount,
            siteExchangeRate = mockProtectionAddOnState.payments.head.siteExchangeRate,
            upliftExchangeRate = mockProtectionAddOnState.payments.head.upliftExchangeRate,
            remark = mockProtectionAddOnState.payments.head.remark,
            paymentTypeId = mockProtectionAddOnState.payments.head.paymentTypeId,
            token = mockProtectionAddOnState.payments.head.token,
            recStatus = mockProtectionAddOnState.payments.head.recStatus,
            recCreatedWhen = mockProtectionAddOnState.payments.head.recCreatedWhen.map(_.toDate),
            referencePaymentId = mockProtectionAddOnState.payments.head.referencePaymentId,
            points = mockProtectionAddOnState.payments.head.points
          )
        ),
        bookingPayments = Seq(
          BookingPaymentForMessage(
            paymentId = mockProtectionAddOnState.bookingPayments.head.paymentId,
            bookingId = mockProtectionAddOnState.bookingPayments.head.bookingId,
            paymentCurrency = mockProtectionAddOnState.bookingPayments.head.paymentCurrency,
            paymentAmount = mockProtectionAddOnState.bookingPayments.head.paymentAmount.toDouble,
            amountUsd = mockProtectionAddOnState.bookingPayments.head.amountUsd.toDouble,
            recStatus = mockProtectionAddOnState.bookingPayments.head.recStatus,
            recCreatedWhen = mockProtectionAddOnState.bookingPayments.head.recCreatedWhen.map(_.toDate),
            fxiUplift = mockProtectionAddOnState.bookingPayments.head.fxiUplift.map(_.toDouble),
            loyaltyPoints = mockProtectionAddOnState.bookingPayments.head.loyaltyPoints,
            supplierCurrency = mockProtectionAddOnState.bookingPayments.head.supplierCurrency,
            supplierExchangeRate = mockProtectionAddOnState.bookingPayments.head.supplierExchangeRate.map(_.toDouble),
            bookingPaymentId = mockProtectionAddOnState.bookingPayments.head.bookingPaymentId
          )
        ),
        bookingRelationships = mockProtectionAddOnState.relationships.headOption
          .map(head => mapBaseBookingRelationshipToBaseBookingRelationshipForMessage(Seq(head)))
          .get,
        breakdown = Seq.empty,
        breakdownPerPax = Seq.empty,
        baggageAllowance = Seq.empty,
        baggage = Seq.empty,
        history = Seq(
          ItineraryHistoryForMessage(
            actionId = mockProtectionAddOnState.itineraryHistories.head.actionId,
            itineraryId = mockProtectionAddOnState.itineraryHistories.head.itineraryId.toInt,
            bookingType = mockProtectionAddOnState.itineraryHistories.head.bookingType,
            bookingId = mockProtectionAddOnState.itineraryHistories.head.bookingId,
            actionType = mockProtectionAddOnState.itineraryHistories.head.actionType,
            version = mockProtectionAddOnState.itineraryHistories.head.version,
            actionDate = mockProtectionAddOnState.itineraryHistories.head.actionDate.toDate,
            parameters = mockProtectionAddOnState.itineraryHistories.head.parameters,
            description = mockProtectionAddOnState.itineraryHistories.head.description,
            recStatus = mockProtectionAddOnState.itineraryHistories.head.recStatus,
            recCreatedWhen = mockProtectionAddOnState.itineraryHistories.head.recCreatedWhen.map(_.toDate)
          )
        ),
        summary = Seq.empty,
        paxTickets = Seq.empty,
        itinerary = FlightItineraryForMessage(
          itineraryId = mockProtectionAddOnState.itinerary.itineraryId,
          memberId = mockProtectionAddOnState.itinerary.memberId,
          recStatus = mockProtectionAddOnState.itinerary.recStatus,
          recCreatedWhen = mockProtectionAddOnState.itinerary.recCreatedWhen.map(_.toDate),
          recModifiedWhen = mockProtectionAddOnState.itinerary.recModifiedWhen.map(_.toDate)
        ),
        userAgent = None,
        bookingAttribution = Seq.empty,
        itineraryDate = new Date(),
        protectionModels = None,
        multiProductInfos =
          Some(mockMultiProductInfos.map(pf => MultiProductInfoForMessage(pf.multiProductId, pf.multiProductType.id))),
        flightSegmentInfoByPaxType = Seq.empty,
        segmentInfoByPaxType = Seq.empty,
        fareRulePolicies = None,
        flightSeatSelection = Seq.empty,
        vehicle = None,
        activities = None,
        properties = None,
        cegFastTracks = None,
        addOns = Some(
          Seq(
            AddOnForMessage(
              ProtoConverter.protoToString(mockProtectionAddOnState.addOns.head),
              mockProtectionAddOnProductModel.product.booking.bookingId,
              mockProtectionAddOnProductModel.product.booking.productTypeId
            )
          )
        ),
        multiProductBookingGroups = Some(
          Seq(
            MultiProductBookingGroupModelMessage(
              bookingId = defaultBookingId,
              itineraryId = -2,
              cartId = -3,
              packageId = Some(-4)
            )
          )
        ),
        flightBrandSelections = None,
        flightBrandAttributes = None,
        flightBrandAttributeParams = None,
        flightBaseBooking = None,
        flightBaseCancellationInfo = None,
        crossProductIsolatedFeature = None
      )

      val bookingStateMessageCaptor: ArgumentCaptor[BookingStateMessage] =
        ArgumentCaptor.forClass(classOf[BookingStateMessage])

      genericAddOnRepository
        .sendModelForReplication(
          mockProtectionAddOnState,
          defaultProtectionAddOnBookingWorkflowAction,
          mockMultiProductInfos
        )
        .map { _ =>
          verify(messagingMock, times(1))
            .sendMessage(
              bookingStateMessageCaptor.capture()
            )
          val message = bookingStateMessageCaptor.getValue
          message shouldBe expected.copy(itineraryDate = message.itineraryDate)
        }
    }
  }

  "saveAddOnBookingState" should {

    val mockProtectionAddOnState = AddOnBookingStateWithItinerary(
      itinerary = mockItineraryInternalModel.itinerary,
      itineraryHistories = mockItineraryInternalModel.history.map(_.copy(actionId = 10, version = 0)),
      payments = mockItineraryInternalModel.payments,
      bookingPayments = mockItineraryInternalModel.bookingPayments,
      relationships = mockItineraryInternalModel.relationships,
      addOns = Seq(mockAddOnProductModel),
      multiProductBookingGroups = Seq(defaultMultiProductBookingGroups)
    )

    "save successfully with only new itineraryActionHistory, payment, bookingPayment and financialBreakdown" in {
      val fixture = new GenericAddOnRepositoryFixture {}
      import fixture._

      val existingItineraryHistories = mockProtectionAddOnState.itineraryHistories.zipWithIndex.map {
        case (itineraryHistory, index) =>
          itineraryHistory.copy(actionId = index, version = index)
      }

      val existingPaymentState = mockProtectionAddOnState.payments.zipWithIndex.map {
        case (paymentState, index) =>
          paymentState.copy(paymentId = index)
      }

      val existingBookingPaymentState = mockProtectionAddOnState.bookingPayments.map { bookingPaymentState =>
        bookingPaymentState.copy(paymentId = mockProtectionAddOnState.payments.head.paymentId)
      }

      val existingFinancialBreakdowns =
        mockProtectionAddOnState.addOns.flatMap(
          _.product.breakdowns.zipWithIndex
            .map { case (financialBreakDown, index) => financialBreakDown.copy(breakdownId = index, upcId = Some(3)) }
        )

      val existingBapiFinancialBreakdowns = existingFinancialBreakdowns.map(CommonProductConverters.toBapiBreakdown)

      val itineraryId = mockProtectionAddOnState.itinerary.itineraryId
      val bookingId   = itineraryProductModel.addOns.head.product.booking.bookingId

      when(genericAddOnDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
        .thenReturn(Future.successful(existingItineraryHistories))
      when(genericAddOnDbProxyMock.getItineraryPaymentByItineraryId(itineraryId))
        .thenReturn(Future.successful(existingPaymentState))
      when(genericAddOnDbProxyMock.getBookingPaymentsByPaymentIds(Seq(0, 9)))
        .thenReturn(Future.successful(existingBookingPaymentState))
      when(genericAddOnDbProxyMock.getFinancialBreakdown(bookingId))
        .thenReturn(Future.successful(existingBapiFinancialBreakdowns))

      val newItineraryHistory    = mockItineraryInternalModel.history.head.copy(actionId = 10, version = 2)
      val newPaymentState        = mockItineraryInternalModel.payments.head.copy(paymentId = 9)
      val newBookingPaymentState = mockItineraryInternalModel.bookingPayments.head.copy(paymentId = 9)
      val newFinancialBreakdowns = FinancialBreakdown.defaultInstance.copy(breakdownId = 10)
      val newRelationships       = mockItineraryInternalModel.relationships.head.copy(relationshipId = 11)

      val addOnBookingStateToSave = mockProtectionAddOnState.copy(
        itineraryHistories = existingItineraryHistories :+ newItineraryHistory,
        payments = existingPaymentState :+ newPaymentState,
        bookingPayments = existingBookingPaymentState :+ newBookingPaymentState,
        addOns = mockProtectionAddOnState.addOns
          .modify(_.each.product.breakdowns)
          .setTo(existingFinancialBreakdowns :+ newFinancialBreakdowns),
        relationships = mockProtectionAddOnState.relationships :+ newRelationships
      )

      val addOnBookingStateToSaveWithoutExistingRecord = mockProtectionAddOnState.copy(
        itineraryHistories = Seq(newItineraryHistory),
        payments = Seq(newPaymentState),
        bookingPayments = Seq(newBookingPaymentState),
        addOns = mockProtectionAddOnState.addOns
          .modify(_.each.product.breakdowns)
          .setTo(Seq(newFinancialBreakdowns)),
        relationships = Seq(newRelationships)
      )

      when(genericAddOnActionDbProxy.insertAddOn(any(), any()))
        .thenReturn(Future.successful(addOnBookingStateToSaveWithoutExistingRecord))
      when(multiProductRepositoryMock.saveMultiProductBookingGroupIfNotExist(defaultMultiProductBookingGroups))
        .thenReturn(Future.successful(defaultMultiProductBookingGroups))
      when(multiProductRepositoryMock.getMultiProductBookingGroupByItineraryId(itineraryId))
        .thenReturn(Future.successful(Seq(defaultMultiProductBookingGroups)))

      genericAddOnRepository.saveBookingState(addOnBookingStateToSave).map { result =>
        verify(genericAddOnDbProxyMock, times(2)).getItineraryActionHistoryByItineraryId(itineraryId)
        verify(genericAddOnDbProxyMock).getFinancialBreakdown(bookingId)
        result shouldEqual addOnBookingStateToSaveWithoutExistingRecord
      }
    }

    "save addOn with stale update" in {
      val fixture = new GenericAddOnRepositoryFixture {}
      import fixture._

      val newItineraryHistory = mockProtectionAddOnState.itineraryHistories.head.copy(version = 1)
      val itineraryId         = mockProtectionAddOnState.itinerary.itineraryId
      when(genericAddOnDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
        .thenReturn(Future.successful(Seq(newItineraryHistory)))

      val repository = new GenericAddOnRepositoryImpl(
        genericAddOnDbProxyMock,
        genericAddOnActionDbProxy,
        baseBookingDbProxyMock,
        messagingMock,
        multiProductRepositoryMock,
        replicateConfig,
        addOnsBkgDbProxyMock,
        addOnsLocalBkgDbProxyMock
      ) {
        override def getBookingState(
            itineraryId: ItineraryId
        ): Future[AddOnBookingStateWithItinerary] =
          Future.failed(new Exception("stale update was detected"))
      }

      when(multiProductRepositoryMock.getMultiProductBookingGroupByItineraryId(itineraryId))
        .thenReturn(Future.successful(Seq(defaultMultiProductBookingGroups)))

      repository.saveBookingState(mockProtectionAddOnState).failed.map { failure =>
        verify(genericAddOnDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
        failure.getMessage shouldEqual "stale update was detected"
      }
    }

    "save addOn with version collision" in {
      val fixture = new GenericAddOnRepositoryFixture {}
      import fixture._

      val mockProtectionAddOnStateWithStaleItinerary = AddOnBookingStateWithItinerary(
        itinerary = mockItineraryInternalModel.itinerary,
        itineraryHistories = mockItineraryInternalModel.history.map(_.copy(actionId = 10, version = 1)),
        payments = mockItineraryInternalModel.payments,
        bookingPayments = mockItineraryInternalModel.bookingPayments,
        relationships = Seq.empty,
        addOns = Seq(mockAddOnProductModel),
        multiProductBookingGroups = Seq.empty
      )

      val newItineraryHistory = mockProtectionAddOnState.itineraryHistories.head.copy(version = 1)
      val itineraryId         = mockProtectionAddOnState.itinerary.itineraryId
      when(genericAddOnDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
        .thenReturn(Future.successful(Seq(newItineraryHistory)))

      val repository = new GenericAddOnRepositoryImpl(
        genericAddOnDbProxyMock,
        genericAddOnActionDbProxy,
        baseBookingDbProxyMock,
        messagingMock,
        multiProductRepositoryMock,
        replicateConfig,
        addOnsBkgDbProxyMock,
        addOnsLocalBkgDbProxyMock
      ) {
        override protected def reportVersionCollision(
            addOnBookingStateWithItinerary: ProductStateWithItinerary
        ): Future[AddOnBookingStateWithItinerary] =
          Future.failed(new Exception("version collision detected"))
      }

      when(multiProductRepositoryMock.getMultiProductBookingGroupByItineraryId(itineraryId))
        .thenReturn(Future.successful(Seq(defaultMultiProductBookingGroups)))

      repository.saveBookingState(mockProtectionAddOnStateWithStaleItinerary).failed.map { failure =>
        verify(genericAddOnDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
        failure.getMessage shouldEqual "version collision detected"
      }
    }

    "successful getAddOnBookingState" in {
      val fixture = new GenericAddOnRepositoryFixture {}
      import fixture._

      val itineraryId                  = mockProtectionAddOnState.itinerary.itineraryId
      val bid                          = mockProtectionAddOnState.addOns.head.product.booking.bookingId
      val sourceBookingIds             = mockProtectionAddOnState.addOns.map(_.product.booking.bookingId)
      val paymentIds: Seq[ItineraryId] = mockProtectionAddOnState.payments.map(_.paymentId)

      when(genericAddOnDbProxyMock.getItineraryPaymentByItineraryId(itineraryId))
        .thenReturn(Future.successful(mockProtectionAddOnState.payments))
      when(genericAddOnDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
        .thenReturn(Future.successful(mockProtectionAddOnState.itineraryHistories))
      when(genericAddOnDbProxyMock.getMultiProductItinerary(itineraryId))
        .thenReturn(
          Future.successful(Some(mockProtectionAddOnState.itinerary))
        )
      when(genericAddOnDbProxyMock.getBookingPaymentsByPaymentIds(paymentIds))
        .thenReturn(Future.successful(mockProtectionAddOnState.bookingPayments))
      when(baseBookingDbProxyMock.getBaseBookingByItineraryId(itineraryId))
        .thenReturn(Future.successful(mockProtectionAddOnState.addOns.map(_.product.booking).toList))
      when(baseBookingDbProxyMock.getBaseBookingRelationshipsBySourceBookingIds(sourceBookingIds))
        .thenReturn(
          Future.successful(mockProtectionAddOnState.relationships.map(toBaseBookingRelationshipInternal))
        )
      when(genericAddOnDbProxyMock.getAddOnBookingByBookingId(bid))
        .thenReturn(Future.successful(mockAddOnProductModel))

      when(multiProductRepositoryMock.getMultiProductBookingGroup(bid))
        .thenReturn(Future.successful(Some(defaultMultiProductBookingGroups)))

      genericAddOnRepository.getBookingState(itineraryId).map { result =>
        verify(genericAddOnDbProxyMock).getItineraryPaymentByItineraryId(itineraryId)
        verify(genericAddOnDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
        verify(genericAddOnDbProxyMock).getBookingPaymentsByPaymentIds(paymentIds)
        verify(genericAddOnDbProxyMock).getMultiProductItinerary(itineraryId)
        verify(baseBookingDbProxyMock).getBaseBookingByItineraryId(itineraryId)
        verify(genericAddOnDbProxyMock).getAddOnBookingByBookingId(bid)
        verify(multiProductRepositoryMock).getMultiProductBookingGroup(bid)

        result shouldEqual mockProtectionAddOnState
      }
    }

    "Got an exception when multiProductItinerary not exists" in {
      val fixture = new GenericAddOnRepositoryFixture {}
      import fixture._

      val itineraryId      = mockProtectionAddOnState.itinerary.itineraryId
      val bid              = mockProtectionAddOnState.addOns.head.product.booking.bookingId
      val sourceBookingIds = mockProtectionAddOnState.addOns.map(_.product.booking.bookingId)

      val paymentIds: Seq[ItineraryId] = mockProtectionAddOnState.payments.map(_.paymentId)

      when(genericAddOnDbProxyMock.getItineraryPaymentByItineraryId(itineraryId))
        .thenReturn(Future.successful(mockProtectionAddOnState.payments))
      when(genericAddOnDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
        .thenReturn(Future.successful(mockProtectionAddOnState.itineraryHistories))
      when(genericAddOnDbProxyMock.getMultiProductItinerary(itineraryId))
        .thenReturn(
          Future.successful(None)
        )
      when(baseBookingDbProxyMock.getBaseBookingByItineraryId(itineraryId))
        .thenReturn(Future.successful(mockProtectionAddOnState.addOns.map(_.product.booking).toList))
      when(baseBookingDbProxyMock.getBaseBookingRelationshipsBySourceBookingIds(sourceBookingIds))
        .thenReturn(
          Future.successful(Seq.empty)
        )
      when(genericAddOnDbProxyMock.getBookingPaymentsByPaymentIds(paymentIds))
        .thenReturn(Future.successful(mockProtectionAddOnState.bookingPayments))

      genericAddOnRepository.getBookingState(itineraryId).failed.map { result =>
        verify(genericAddOnDbProxyMock).getItineraryPaymentByItineraryId(itineraryId)
        verify(genericAddOnDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
        verify(genericAddOnDbProxyMock).getBookingPaymentsByPaymentIds(paymentIds)
        verify(genericAddOnDbProxyMock).getMultiProductItinerary(itineraryId)

        result.getMessage shouldEqual s"Cannot itinerary by itineraryId $itineraryId"
      }
    }
  }

  trait GenericAddOnRepositoryFixture extends MockitoSugar {
    val baseBookingDbProxyMock: BaseBookingDBProxy           = mock[BaseBookingDBProxy]
    val multiProductRepositoryMock: MultiProductRepository   = mock[MultiProductRepository]
    val genericAddOnDbProxyMock: GenericAddOnDbProxy         = mock[GenericAddOnDbProxy]
    val genericAddOnActionDbProxy: GenericAddOnActionDbProxy = mock[GenericAddOnActionDbProxy]
    val messagingMock: MessageService                        = mock[MessageService]
    val mockKillSwitch: DynamicObject[Boolean]               = mock[DynamicObject[Boolean]]
    val replicateConfig: ReplicateStateConfig                = ReplicateStateConfig(mockKillSwitch)
    val addOnsBkgDbProxyMock: BaseBookingDbReadProxy         = mock[BaseBookingDbReadProxy]
    val addOnsLocalBkgDbProxyMock: BaseBookingDbReadProxy    = mock[BaseBookingDbReadProxy]

    val genericAddOnRepository = new GenericAddOnRepositoryImpl(
      genericAddOnDbProxyMock,
      genericAddOnActionDbProxy,
      baseBookingDbProxyMock,
      messagingMock,
      multiProductRepositoryMock,
      replicateConfig,
      addOnsBkgDbProxyMock,
      addOnsLocalBkgDbProxyMock
    )
  }
}
