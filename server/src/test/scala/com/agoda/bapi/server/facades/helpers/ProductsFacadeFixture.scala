package com.agoda.bapi.server.facades.helpers

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.setupBooking.{PaymentRequest, SetupBookingRequest}
import com.agoda.bapi.common.model.ChargeOption.ChargeOption
import com.agoda.bapi.common.model.CurrencyCode
import com.agoda.bapi.common.model.car.CarConfirmationData
import com.agoda.bapi.server.addon
import com.agoda.bapi.server.addon._
import com.agoda.bapi.server.facades.ProductsFacadeImpl
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.{BookingPropertiesData, FlightConfirmationData, MainProductData}
import com.agoda.bapi.server.repository.FlightsRepository
import com.agoda.bapi.server.service._
import com.agoda.bapi.server.service.pricebreakdown.PriceBreakdownService
import com.agoda.bapi.server.utils.ProductTokenCreatorUtils
import com.agoda.bapi.server.utils.partner.PartnerPromoService
import com.agoda.bapi.common.MessageService
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.finance.tax.models.BookingCountryIndicator
import dispatch.Future
import org.scalatestplus.mockito.MockitoSugar

trait ProductsFacadeFixture extends MockitoSugar {
  val propertyService              = mock[PropertyService]
  val flightRepository             = mock[FlightsRepository]
  val carService                   = mock[CarService]
  val protectionService            = mock[ProtectionService]
  val mockPriceBreakdownService    = mock[PriceBreakdownService]
  val productTokenUtils            = mock[ProductTokenCreatorUtils]
  val mockTotalSavingsService      = mock[TotalSavingsService]
  val mockMixAndSaveService        = mock[MixAndSaveService]
  val mockCampaignService          = mock[CampaignService]
  val mockMessageService           = mock[MessageService]
  val mockBookingsService          = mock[BookingsService]
  val mockActivityService          = mock[ActivityService]
  val mockPriceFreezeService       = mock[PriceFreezeService]
  val mockBookingMessagingService  = mock[BookingMessagingService]
  val mockExternalLoyaltyService   = mock[ExternalLoyaltyService]
  val mockPartnerPromoService      = mock[PartnerPromoService]
  val mockConsumerFintechService   = mock[ConsumerFintechService]
  val mockAddOnBookingTokenService = mock[AddOnBookingTokenService]
  val mockAddOnFacade              = mock[AddOnFacade]
  val mockAddOnService             = mock[AddOnService]
  val mockRebookAndCancelService   = mock[RebookAndCancelService]

  val mockReporter = mock[MetricsReporter]

  val paymentRequest = mock[PaymentRequest]

  val productsFacade = new ProductsFacadeImpl(
    propertyService,
    flightRepository,
    carService,
    protectionService,
    mockPriceBreakdownService,
    productTokenUtils,
    mockTotalSavingsService,
    mockMixAndSaveService,
    mockCampaignService,
    mockMessageService,
    mockBookingsService,
    mockActivityService,
    mockPriceFreezeService,
    mockBookingMessagingService,
    mockExternalLoyaltyService,
    mockPartnerPromoService,
    mockConsumerFintechService,
    mockAddOnBookingTokenService,
    mockAddOnFacade,
    mockAddOnService,
    mockRebookAndCancelService
  ) {
    override def withMeasure(metricName: String, value: Long = 1, tags: Map[String, String] = Map.empty): Future[Unit] =
      Future.unit

    override def createAddOnInput(
        request: SetupBookingRequest,
        properties: Seq[BookingPropertiesData],
        chargeCurrency: String,
        essCountryCode: Option[String],
        chargeOption: Option[ChargeOption],
        bookingCountryIndicator: Option[BookingCountryIndicator] = None
    )(requestContext: RequestContext): addon.AddOnInput =
      AddOnDefaults.addOnInput

    override def getAddOnsForCart(
        request: SetupBookingRequest,
        properties: Seq[BookingPropertiesData],
        flights: Seq[FlightConfirmationData],
        cars: Seq[CarConfirmationData],
        chargeCurrency: CurrencyCode,
        bookingCountryIndicatorOpt: Option[BookingCountryIndicator] = None
    )(implicit context: SetupBookingContext): Future[Seq[AddOnDataV2]] = mockAddOnService.getAddOns(
      request,
      MainProductData(properties = properties, flights = flights, cars = cars),
      chargeCurrency
    )
  }
}
