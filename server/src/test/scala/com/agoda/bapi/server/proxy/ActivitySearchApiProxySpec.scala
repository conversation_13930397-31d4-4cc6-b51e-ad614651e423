package com.agoda.bapi.server.proxy

import akka.actor.{ActorSystem, Scheduler}
import com.agoda.activity.client.ActivitySearchClient
import com.agoda.activity.client.model._
import com.agoda.adp.messaging.message.logging.LogLevel
import com.agoda.bapi.common.config.ActivitySearchApiConfig
import com.agoda.bapi.common.constants.UserPointBalanceErrorCode
import com.agoda.bapi.common.exception.{ActivityPrepareBookingException, BookingUserPointBalanceExceptionForResponse}
import com.agoda.bapi.common.handler.MeasurementsContext
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.model.{CurrencyCode, WhiteLabelInfo}
import com.agoda.bapi.common.model.cart.CartItemContext
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.{NoOpMetricsReporter, WithProxyMessageTestMock}
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.externalloyalty.client.v2.model.DistributePointsResponse
import com.agoda.externalloyalty.client.v2.model.DistributePointsResponse.RedemptionType
import com.agoda.mpb.common.header.{AgHeaderKey, AgHttpHeader}
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import generated.model.{PartnerLoyaltyConfiguration, PartnerLoyaltyConfigurationPointsRequirements}
import org.mockito.ArgumentMatchers.{any, eq => eqTo}
import org.mockito.Mockito.{reset, verify, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.TableDrivenPropertyChecks
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar
import sttp.client.SttpClientException.ReadException

import scala.concurrent.Future
import scala.concurrent.duration._

class ActivitySearchApiProxySpec
    extends AsyncWordSpec
    with WithProxyMessageTestMock
    with Matchers
    with MockitoSugar
    with BeforeAndAfter
    with TableDrivenPropertyChecks {
  val activitySearchClient                     = mock[ActivitySearchClient[Future]]
  val activitySearchApiConfig                  = mock[ActivitySearchApiConfig]
  val mockActorSystem: ActorSystem             = mock[ActorSystem]
  private val activityProductId                = "activity-product-id";
  @Override val mockFeatureAware: FeatureAware = mock[FeatureAware]

  implicit private val measurementsContext: MeasurementsContext = mock[MeasurementsContext]
  val agEnvHeader: AgHttpHeader                                 = AgHttpHeader("ag-env", "UAT")
  val activityRequestContext =
    ActivityRequestContext("1", "en-us", "1", "th", "USD", "cid", "sid", "uid", 1, "cid", "platformId", Some(true))
  val currencyCode: CurrencyCode = "USD"
  before {
    reset(activitySearchClient)
    reset(mockFeatureAware)
    when(measurementsContext.tags).thenReturn(Map[String, String]())
    when(mockActorSystem.scheduler).thenReturn(mock[Scheduler])
    when(activitySearchApiConfig.timeout).thenReturn(Some(30 seconds))
  }

  trait Fixture {
    val mockWhitelabelInfo   = mock[WhiteLabelInfo]
    val pointsRequirements   = mock[PartnerLoyaltyConfigurationPointsRequirements]
    val partnerLoyaltyConfig = mock[PartnerLoyaltyConfiguration]
    val featureConfiguration = mock[FeaturesConfiguration]
    val activitySearchClientProxy =
      new ActivitySearchApiProxyImpl(activitySearchClient, activitySearchApiConfig, mockActorSystem, messagingService) {
        override def reporter: MetricsReporter = new NoOpMetricsReporter

        override protected def log[Response](
            request: PrepareBookingRequest,
            context: RequestContext,
            response: Response,
            logLevel: LogLevel
        )(implicit responseSerializer: Response => String): Unit = super.log(request, context, response, logLevel)
      }
  }

  "activitySearchClientProxy" should {

    "getActivityConfirmationData successfully" in {
      val fixture = new Fixture {}

      val prepareBookingResponse =
        PrepareBookingResponse(
          true,
          Seq.empty,
          Some(PrepareBookingActivityResult(true, None, None))
        )
      when(activitySearchClient.prepareBooking(any(), any()))
        .thenReturn(Future.successful(prepareBookingResponse))

      val activityConfirmPriceRequest =
        ActivityConfirmPriceRequest("activity_token", "offer_token")
      val loyaltyReq          = Some(LoyaltyRequest(Some("partnerClaimToken")))
      val campaignInfoRequest = Some(CampaignInfoRequest(Some(123), 456, "COUPON"))
      val activityRequestItem = ActivityRequestItem(
        id = activityProductId,
        confirmPriceRequest = activityConfirmPriceRequest
      )

      fixture.activitySearchClientProxy
        .getActivityConfirmationData(
          activityRequestItem = activityRequestItem,
          activityRequestContext = activityRequestContext,
          loyaltyRequest = loyaltyReq,
          experimentData = None,
          campaignInfoRequest,
          chargeCurrency = currencyCode,
          distributePointsResponseOpt = None
        )
        .map(response => {
          response.isCompleted shouldBe true
          response.id shouldBe activityProductId
        })
    }

    "getActivityConfirmationData PriceNotConfirmed" in {
      val fixture = new Fixture {}

      val mockPrepareBookingActivity = mock[SelectedActivity]
      val mockBookingInfo            = mock[BookingInfo]
      val prepareBookingResponse =
        PrepareBookingResponse(
          true,
          Seq(
            SearchError(
              SearchErrorEnums.ErrorCode.INTERNALERROR,
              SearchErrorEnums.SubErrorCode.LOYALTYERROR,
              "price is not confirmed"
            )
          ),
          Some(PrepareBookingActivityResult(false, Some(mockPrepareBookingActivity), Some(mockBookingInfo)))
        )
      when(activitySearchClient.prepareBooking(any(), any()))
        .thenReturn(Future.successful(prepareBookingResponse))

      val activityConfirmPriceRequest =
        ActivityConfirmPriceRequest("activity_token", "offer_token")
      val loyaltyReq          = Some(LoyaltyRequest(Some("partnerClaimToken")))
      val campaignInfoRequest = Some(CampaignInfoRequest(Some(123), 456, "COUPON"))
      val activityRequestItem = ActivityRequestItem(
        id = activityProductId,
        confirmPriceRequest = activityConfirmPriceRequest
      )

      fixture.activitySearchClientProxy
        .getActivityConfirmationData(
          activityRequestItem = activityRequestItem,
          activityRequestContext = activityRequestContext,
          loyaltyRequest = loyaltyReq,
          experimentData = None,
          campaignInfoRequest,
          chargeCurrency = currencyCode,
          distributePointsResponseOpt = None
        )
        .failed
        .map(failure => failure.getMessage shouldBe "price is not confirmed")
    }

    "getActivityConfirmationData with correct Loyalty Error" when {
      val mockDistributePointsResponse = DistributePointsResponse(
        userEnteredPoints = 1.0,
        totalBookingInclusivePrice = 1.0,
        totalBookingExclusivePrice = 1.0,
        cartItems = Seq.empty,
        isCashPlusPointsSupported = None,
        redemptionType = None
      )

      val testcase = Table(
        (
          "enableExternalLoyaltyPointsErrorV2Exp",
          "distributeResponse",
          "isExternalLoyaltyPointsErrorV2Feature",
          "hasMinimumBalanceRequirement",
          "expectedUserPointBalanceErrorCode"
        ),
        (
          false,
          Some(mockDistributePointsResponse.copy(redemptionType = Some(RedemptionType.POINTSORPARTIALCASH))),
          true,
          true,
          None
        ),
        (
          true,
          Some(mockDistributePointsResponse.copy(redemptionType = Some(RedemptionType.POINTSORPARTIALCASH))),
          false,
          true,
          Some(UserPointBalanceErrorCode.InsufficientPointsBalance)
        ),
        (
          true,
          Some(mockDistributePointsResponse.copy(redemptionType = Some(RedemptionType.POINTSORPARTIALCASH))),
          true,
          false,
          Some(UserPointBalanceErrorCode.HardInsufficientPointsBalance)
        ),
        (
          true,
          Some(mockDistributePointsResponse.copy(redemptionType = Some(RedemptionType.FLEXIBLE))),
          true,
          true,
          Some(UserPointBalanceErrorCode.HardInsufficientPointsBalance)
        ),
        (
          true,
          Some(mockDistributePointsResponse),
          true,
          false,
          Some(UserPointBalanceErrorCode.SoftInsufficientPointsBalance)
        )
      )
      forAll(testcase) {
        (
            enableExternalLoyaltyPointsErrorV2Exp,
            distributeResponse,
            isExternalLoyaltyPointsErrorV2Feature,
            hasMinimumBalanceRequirement,
            expectedUserPointBalanceErrorCode
        ) =>
          s"enableExternalLoyaltyPointsErrorV2Exp = $enableExternalLoyaltyPointsErrorV2Exp, " +
            s"distributeResponse = $distributeResponse, " +
            s"isExternalLoyaltyPointsErrorV2Feature = $isExternalLoyaltyPointsErrorV2Feature, " +
            s"hasMinimumBalanceRequirement = $hasMinimumBalanceRequirement" in {
              val fixture = new Fixture {}

              import fixture._
              val mockPrepareBookingActivity = mock[SelectedActivity]
              val mockBookingInfo            = mock[BookingInfo]
              val prepareBookingResponse =
                PrepareBookingResponse(
                  true,
                  Seq(
                    SearchError(
                      SearchErrorEnums.ErrorCode.INTERNALERROR,
                      SearchErrorEnums.SubErrorCode.LOYALTYINSUFFICIENTBALANCEERROR,
                      "Insufficient User Balance"
                    )
                  ),
                  Some(PrepareBookingActivityResult(false, Some(mockPrepareBookingActivity), Some(mockBookingInfo)))
                )
              when(activitySearchClient.prepareBooking(any(), any()))
                .thenReturn(Future.successful(prepareBookingResponse))

              when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))
              when(mockFeatureAware.enableExternalLoyaltyPointsErrorV2).thenReturn(
                enableExternalLoyaltyPointsErrorV2Exp
              )
              when(requestContext.agHeaders).thenReturn(
                Seq(AgHttpHeader(AgHeaderKey.AG_ENVIRONMENT_HEADER.toString, "UAT"))
              )
              when(requestContext.userContext).thenReturn(None)
              when(requestContext.bookingCreationContext).thenReturn(None)
              when(requestContext.path).thenReturn("path")
              when(requestContext.getBookingSessionId()).thenReturn("bookingSessionId")

              when(requestContext.whiteLabelInfo).thenReturn(mockWhitelabelInfo)
              when(mockWhitelabelInfo.isFeatureEnabled(WhiteLabelFeatureName.ExternalLoyaltyPointsErrorV2))
                .thenReturn(isExternalLoyaltyPointsErrorV2Feature)
              when(pointsRequirements.hasMinimumBalance).thenReturn(Some(hasMinimumBalanceRequirement))
              when(partnerLoyaltyConfig.pointsRequirements).thenReturn(Some(pointsRequirements))
              when(featureConfiguration.partnerLoyaltyConfiguration).thenReturn(partnerLoyaltyConfig)
              when(mockWhitelabelInfo.feature).thenReturn(featureConfiguration)

              val activityConfirmPriceRequest =
                ActivityConfirmPriceRequest("activity_token", "offer_token")
              val loyaltyReq          = Some(LoyaltyRequest(Some("partnerClaimToken")))
              val campaignInfoRequest = Some(CampaignInfoRequest(Some(123), 456, "COUPON"))
              val activityRequestItem = ActivityRequestItem(
                id = activityProductId,
                confirmPriceRequest = activityConfirmPriceRequest
              )

              fixture.activitySearchClientProxy
                .getActivityConfirmationData(
                  activityRequestItem = activityRequestItem,
                  activityRequestContext = activityRequestContext,
                  loyaltyRequest = loyaltyReq,
                  experimentData = None,
                  campaignInfoRequest,
                  chargeCurrency = currencyCode,
                  distributePointsResponseOpt = distributeResponse
                )
                .failed
                .map { failure =>
                  failure shouldBe a[BookingUserPointBalanceExceptionForResponse]
                  val bookingUserPointBalanceException =
                    failure.asInstanceOf[BookingUserPointBalanceExceptionForResponse]
                  bookingUserPointBalanceException.getMessage shouldBe "Insufficient User Balance"
                  bookingUserPointBalanceException.userPointBalanceErrorCode shouldBe expectedUserPointBalanceErrorCode
                }
            }
      }
    }

    "getActivityConfirmationData with Empty Not completed result" in {
      val fixture = new Fixture {}

      val prepareBookingResponse =
        PrepareBookingResponse(false, Seq.empty, None)
      when(activitySearchClient.prepareBooking(any(), any()))
        .thenReturn(Future.successful(prepareBookingResponse))

      val activityConfirmPriceRequest =
        ActivityConfirmPriceRequest("activity_token", "offer_token")
      val loyaltyReq          = Some(LoyaltyRequest(Some("partnerClaimToken")))
      val campaignInfoRequest = Some(CampaignInfoRequest(Some(123), 456, "COUPON"))
      val activityRequestItem = ActivityRequestItem(
        id = activityProductId,
        confirmPriceRequest = activityConfirmPriceRequest
      )

      fixture.activitySearchClientProxy
        .getActivityConfirmationData(
          activityRequestItem = activityRequestItem,
          activityRequestContext = activityRequestContext,
          loyaltyRequest = loyaltyReq,
          experimentData = None,
          campaignInfoRequest,
          chargeCurrency = currencyCode,
          distributePointsResponseOpt = None
        )
        .map(response => {
          response.isCompleted shouldBe false
          response.id shouldBe ""
        })
    }

    "getActivityConfirmationData with error result" in {
      val fixture = new Fixture {}

      val exception = new ReadException(new Exception("test"))
      when(activitySearchClient.prepareBooking(any(), any()))
        .thenReturn(Future.failed(exception))

      val activityConfirmPriceRequest =
        ActivityConfirmPriceRequest("activity_token", "offer_token")
      val loyaltyReq          = Some(LoyaltyRequest(Some("partnerClaimToken")))
      val campaignInfoRequest = Some(CampaignInfoRequest(Some(123), 456, "COUPON"))

      val expectedException = new ActivityPrepareBookingException(
        s"UnexpectedError while calling prepareBooking\n${exception.getMessage}",
        Some(exception)
      )

      val activityRequestItem = ActivityRequestItem(
        id = activityProductId,
        confirmPriceRequest = activityConfirmPriceRequest
      )

      fixture.activitySearchClientProxy
        .getActivityConfirmationData(
          activityRequestItem = activityRequestItem,
          activityRequestContext = activityRequestContext,
          loyaltyRequest = loyaltyReq,
          experimentData = None,
          campaignInfoRequest,
          chargeCurrency = currencyCode,
          distributePointsResponseOpt = None
        )
        .failed
        .map(response => {
          response.getMessage shouldBe expectedException.getMessage
          response.getCause shouldBe expectedException.getCause
        })
    }

    "getActivityConfirmationData with cart item context" in {
      val fixture = new Fixture {}

      val prepareBookingResponse =
        PrepareBookingResponse(
          true,
          Seq.empty,
          Some(PrepareBookingActivityResult(true, None, None))
        )
      when(activitySearchClient.prepareBooking(any(), any()))
        .thenReturn(Future.successful(prepareBookingResponse))

      val activityConfirmPriceRequest =
        ActivityConfirmPriceRequest("activity_token", "offer_token")
      val cartItemContext: CartItemContext = CartItemContext("e0049b61-a70a-40af-b8d8-a2a42bd2c076")
      val activityRequestItem = ActivityRequestItem(
        id = activityProductId,
        confirmPriceRequest = activityConfirmPriceRequest,
        cartItemContext = Some(cartItemContext)
      )

      fixture.activitySearchClientProxy
        .getActivityConfirmationData(
          activityRequestItem = activityRequestItem,
          activityRequestContext = activityRequestContext,
          loyaltyRequest = None,
          experimentData = None,
          campaignInfoRequest = None,
          chargeCurrency = currencyCode,
          distributePointsResponseOpt = None
        )
        .map(response => {
          response.isCompleted shouldBe true
          response.id shouldBe activityProductId
          response.cartItemContext shouldBe Some(cartItemContext)
        })
    }

    "getActivityConfirmationData without cart item context" in {
      val fixture = new Fixture {}

      val prepareBookingResponse =
        PrepareBookingResponse(
          true,
          Seq.empty,
          Some(PrepareBookingActivityResult(true, None, None))
        )
      when(activitySearchClient.prepareBooking(any(), any()))
        .thenReturn(Future.successful(prepareBookingResponse))

      val activityConfirmPriceRequest =
        ActivityConfirmPriceRequest("activity_token", "offer_token")

      val activityRequestItem = ActivityRequestItem(
        id = activityProductId,
        confirmPriceRequest = activityConfirmPriceRequest
      )

      fixture.activitySearchClientProxy
        .getActivityConfirmationData(
          activityRequestItem = activityRequestItem,
          activityRequestContext = activityRequestContext,
          loyaltyRequest = None,
          experimentData = None,
          campaignInfoRequest = None,
          chargeCurrency = currencyCode,
          distributePointsResponseOpt = None
        )
        .map(response => {
          response.isCompleted shouldBe true
          response.id shouldBe activityProductId
          response.cartItemContext shouldBe None
        })
    }

    "pass the correct env value to prepareBooking" in {
      val fixture = new Fixture {}

      // Set up your mock responses
      val prepareBookingResponse = PrepareBookingResponse(
        true,
        Seq.empty,
        Some(PrepareBookingActivityResult(true, None, None))
      )
      when(activitySearchClient.prepareBooking(any(), any()))
        .thenReturn(Future.successful(prepareBookingResponse))
      val expectedAgMsePricingToken = "token"
      when(requestContext.agHeaders).thenReturn(
        Seq(
          AgHttpHeader(AgHeaderKey.AG_ENVIRONMENT_HEADER.toString, "UAT"),
          AgHttpHeader(AgHeaderKey.AG_MSE_PRICING_TOKEN.toString, expectedAgMsePricingToken)
        )
      )
      // Set up your test data
      val activityConfirmPriceRequest = ActivityConfirmPriceRequest("activity_token", "offer_token")

      val activityRequestItem = ActivityRequestItem(
        id = "activity-product-id",
        confirmPriceRequest = activityConfirmPriceRequest
      )
      // Call the method under test
      val futureResult = fixture.activitySearchClientProxy
        .getActivityConfirmationData(
          activityRequestItem = activityRequestItem,
          activityRequestContext = activityRequestContext,
          loyaltyRequest = None,
          experimentData = None,
          campaignInfoRequest = None,
          chargeCurrency = currencyCode,
          distributePointsResponseOpt = None
        )
      // Verify that the correct env value was passed to prepareBooking
      val expectedContext = RequestContext(
        languageId = activityRequestContext.languageId,
        languageGroupId = "",
        locale = activityRequestContext.locale,
        whitelabelId = activityRequestContext.whitelabelId,
        origin = activityRequestContext.origin,
        sessionId = activityRequestContext.sessionId,
        correlationId = activityRequestContext.correlationId,
        userId = activityRequestContext.userId,
        cid = activityRequestContext.cid,
        platformId = activityRequestContext.platformId,
        useMock = false,
        env = Some(agEnvHeader.value),
        agMsePricingToken = expectedAgMsePricingToken
      )

      futureResult.map { response =>
        verify(activitySearchClient).prepareBooking(any(), eqTo(expectedContext))
        response.isCompleted shouldBe true
        response.id shouldBe "activity-product-id"
      }
    }

    "pass the case insensitive ag-mse-pricing-token header value to prepareBooking" in {
      val fixture = new Fixture {}

      // Set up your mock responses
      val prepareBookingResponse = PrepareBookingResponse(
        true,
        Seq.empty,
        Some(PrepareBookingActivityResult(true, None, None))
      )
      when(activitySearchClient.prepareBooking(any(), any()))
        .thenReturn(Future.successful(prepareBookingResponse))
      val expectedAgMsePricingToken = "token"
      when(requestContext.agHeaders).thenReturn(
        Seq(
          AgHttpHeader(AgHeaderKey.AG_ENVIRONMENT_HEADER.toString, "UAT"),
          AgHttpHeader("ag-mse-pricing-token", expectedAgMsePricingToken)
        )
      )
      // Set up your test data
      val activityConfirmPriceRequest = ActivityConfirmPriceRequest("activity_token", "offer_token")

      val activityRequestItem = ActivityRequestItem(
        id = "activity-product-id",
        confirmPriceRequest = activityConfirmPriceRequest
      )
      // Call the method under test
      val futureResult = fixture.activitySearchClientProxy
        .getActivityConfirmationData(
          activityRequestItem = activityRequestItem,
          activityRequestContext = activityRequestContext,
          loyaltyRequest = None,
          experimentData = None,
          campaignInfoRequest = None,
          chargeCurrency = currencyCode,
          distributePointsResponseOpt = None
        )
      // Verify that the correct env value was passed to prepareBooking
      val expectedContext = RequestContext(
        languageId = activityRequestContext.languageId,
        languageGroupId = "",
        locale = activityRequestContext.locale,
        whitelabelId = activityRequestContext.whitelabelId,
        origin = activityRequestContext.origin,
        sessionId = activityRequestContext.sessionId,
        correlationId = activityRequestContext.correlationId,
        userId = activityRequestContext.userId,
        cid = activityRequestContext.cid,
        platformId = activityRequestContext.platformId,
        useMock = false,
        env = Some(agEnvHeader.value),
        agMsePricingToken = expectedAgMsePricingToken
      )

      futureResult.map { response =>
        verify(activitySearchClient).prepareBooking(any(), eqTo(expectedContext))
        response.isCompleted shouldBe true
        response.id shouldBe "activity-product-id"
      }
    }
  }
}
