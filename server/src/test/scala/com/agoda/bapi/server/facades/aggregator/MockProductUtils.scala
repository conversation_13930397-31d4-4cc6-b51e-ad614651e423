package com.agoda.bapi.server.facades.aggregator

import com.agoda.bapi.common.message.setupBooking.PackageRequest
import com.agoda.bapi.server.facades.helpers.PapiPropertiesFixture
import com.agoda.bapi.server.model.ProductData
import com.agoda.bapi.server.utils.SetupBookingMock
import mocks.{PropertyMock, SetupBookingWithAlternativesMock}
import models.pricing.enums.SwapRoomTypes
import models.starfruit.AlternativeRoom
import models.starfruit.PropertyPricingJsonProtocol.ResponseStateToken
import org.joda.time.DateTime
import transformers.{DFMetaResult, Properties}

object MockProductUtils
    extends PropertyMock
    with SetupBookingMock
    with SetupBookingWithAlternativesMock
    with PapiPropertiesFixture {
  private val expectedPackagesToken = PackageRequest(clientToken = "client", interSystemToken = Some("system"))

  def createMockProduct(
      fullyAuthDate: Option[DateTime] = Some(new DateTime("2019-06-03T00:00+07:00")),
      fullyChargeDate: Option[DateTime] = Some(new DateTime("2019-06-04T00:00+07:00")),
      isCartMultiProperties: Boolean = false
  ): ProductData = {
    val mockProperty = Seq(
      createMockProperty(
        propertyId = 1L,
        booking = Some(
          createMockEnrichedBookingItem(
            fullyAuthDate = fullyAuthDate,
            fullyChargeDate = fullyChargeDate
          )
        )
      )
    )
    val mockProperties = if (isCartMultiProperties) {
      mockProperty ++ mockProperty
    } else {
      mockProperty
    }
    mockProductsProduct.copy(
      properties = mockProductsProduct.properties.map(
        _.copy(
          papiProperties = Some(
            Properties(
              property = mockProperties,
              debug = None,
              dfMetaResult = DFMetaResult(Some(ResponseStateToken("", true, Some("pricingRequestId-1"))))
            )
          ),
          packageRequest = Some(expectedPackagesToken)
        )
      ),
      totalPriceDisplay = priceBreakdown
    )
  }

  def createMockProductWithAlternatives(
      fullyAuthDate: Option[DateTime] = Some(new DateTime("2019-06-03T00:00+07:00")),
      fullyChargeDate: Option[DateTime] = Some(new DateTime("2019-06-04T00:00+07:00"))
  ): ProductData =
    mockProductsProduct.copy(
      properties = mockProductsProduct.properties.map(
        _.copy(
          papiProperties = Some(
            Properties(
              Seq(
                createMockProperty(
                  propertyId = 1L,
                  booking = Some(
                    createMockEnrichedBookingItem(
                      roomUidOpt = Some("uid-original"),
                      fullyAuthDate = fullyAuthDate,
                      fullyChargeDate = fullyChargeDate
                    )
                  ),
                  roomSwapping = List(
                    AlternativeRoom("roomiden-original", "roomiden-breakfastupsell", SwapRoomTypes.BreakfastUpSell),
                    AlternativeRoom("roomiden-original", "roomiden-crosssell", SwapRoomTypes.NormalCrossSell)
                  ),
                  masterRooms = Seq(masterRoomWithAlternatives),
                  getAllRooms = Seq(childRoomBreakFastUpSell, childRoomOriginal)
                )
              ),
              debug = None,
              dfMetaResult = DFMetaResult(Some(ResponseStateToken("", true, Some("pricingRequestId-1"))))
            )
          ),
          packageRequest = None,
          propertySearchCriteria = Some(propertySearchCriteriaBySelection("roomiden-original"))
        )
      ),
      totalPriceDisplay = priceBreakdown
    )

}
