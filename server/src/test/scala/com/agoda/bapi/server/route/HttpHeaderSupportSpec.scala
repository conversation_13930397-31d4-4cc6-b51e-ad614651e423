package com.agoda.bapi.server.route

import akka.http.scaladsl.model._
import akka.http.scaladsl.model.headers._
import akka.http.scaladsl.server._
import akka.http.scaladsl.testkit.ScalatestRouteTest
import akka.http.scaladsl.unmarshalling.Unmarshaller._
import akka.stream.{ActorMaterializer, ActorMaterializerSettings}
import akka.testkit.TestKit
import com.agoda.bapi.common.ToolSet
import com.agoda.bapi.common.directive.BookingAPIHttpHeader
import com.agoda.bapi.common.model.WhiteLabel.WhiteLabel
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.model.{WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.proxy.WhiteLabelApiProxy
import com.agoda.mpb.common.header.AgHttpHeader
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{verify, when}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future

class HttpHeaderSupportSpec
    extends AnyFlatSpecLike
    with Matchers
    with ScalatestRouteTest
    with HttpHeaderSupport
    with Directives
    with RoutesTimeoutSupport
    with MockitoSugar {

  override def afterAll: Unit = {
    TestKit.shutdownActorSystem(system)
  }

  implicit val actorMaterializer: ActorMaterializer = ActorMaterializer(ActorMaterializerSettings(system))

  lazy val wlMapping: Map[String, Int] = Map(
    "F1A5905F-9620-45E5-9D91-D251C07E0B42"         -> 1,
    "46BFD421-3F38-45FD-A1CF-58766401AFDC"         -> 2,
    "A876A852-CA8F-4F2D-9A6B-718D3ED6C514"         -> 3,
    "EDF39F14-D569-4B22-882A-3BB9F6090F47"         -> 4,
    "7115A27D-A33D-4727-8DEC-B090096E7F51"         -> 5,
    "E1B2EF89-DC7B-47E3-A2F4-637BC8E069CD"         -> 6,
    "4B6B80CD-8620-4698-92DD-49F632B7717C"         -> 7,
    "512FD435-3755-44B5-91BD-471632456A08"         -> 32,
    "CED4FF54-5762-4072-B515-485C33DD9FD6"         -> 34,
    "9EEE3B1E-7207-4E72-9B9E-A0090D78403B"         -> 45,
    "FF4A9BF5-97F8-49D3-8447-2E2172B43DA5"         -> 47,
    "BB7D5528-D107-40CC-8BB0-C5DD5A4F3053"         -> 50,
    "5234834A-9346-47CE-BD5A-F2B754D3383E"         -> 51,
    "B699EA17-3A4F-4607-8091-DE4AFB3030B1"         -> 52,
    "F671D780-A199-4CBF-A3BB-D1FB4912DB97"         -> 53,
    "35ED911C-D764-4236-AA0D-9BCFBA8DB23F"         -> 55,
    "7EE0C9ED-1AF6-41D2-B1A0-31B95B871C77"         -> 5501,
    "D8641644-7E5D-4EEB-88FC-87C2E90FBCD4"         -> 58,
    "FC3D84FC-814B-4195-891D-86491C2C6135"         -> 59,
    "3B5CD422-7CEC-479E-8AEB-5574073072C6"         -> 60,
    "B65C8AB8-4B61-4FAD-905A-9E17616BC386"         -> 61,
    "3A2C6DF8-E746-4029-AA47-F17F11BF5BCC"         -> 62,
    "B65CC8BA-29C2-4521-99C1-86A27046D9BB"         -> 5801,
    "135911F4-7033-4FFB-BCA7-296E1B8035FA"         -> 5901,
    "98D8C3F3-326D-4157-BC8A-2E36A42A48EE"         -> 6001,
    "CDFA6791-33EA-4DF7-8B44-A88A14E865FD"         -> 6101,
    "B5A9F3DE-3937-4CEE-9A29-D2229CE31173"         -> 6201,
    "0CDCF21E-DB0E-426D-A525-81BE2D4368DA"         -> 999,
    "F3A9B7C2-4D8E-6F01-23AB-45CDEF789012"         -> 997,
    "ASDD-A3HGAHAS--1234-8DEC-B090096E7F51INVALID" -> -1,
    "35B8FAAF-FB4B-4B27-A5A8-F5B713814FF5"         -> 101
  )

  val mockWlClient                                    = mock[WhiteLabelApiProxy]
  override val whiteLabelApiProxy: WhiteLabelApiProxy = mockWlClient
  private val dummyIsFeatureEnabledFunction: (
      String,
      Option[String],
      Option[String],
      Option[Int],
      String => Boolean
  ) => Boolean = (_, _, _, _, _) => true

  when(mockWlClient.getWhiteLabelKeyToIdMap).thenReturn(wlMapping)

  val mockedToolSet = mock[ToolSet]

  override def withMeasure(metricName: String, value: Long, tags: Map[String, String]): Future[Unit] =
    mockedToolSet.withMeasure(metricName, value, tags)

  private val uri = "/test"
  "A HttpHeaderSupport" should "fail validateContentType" in
    Post(uri, HttpEntity(ContentTypes.`text/plain(UTF-8)`, "")) ~> validateContentType {
      complete("passed")
    } ~> check {
      rejection shouldBe UnsupportedRequestContentTypeRejection(Set(ContentTypeRange(ContentTypes.`application/json`)))
    }

  "A HttpHeaderSupport" should "successful extractHeader" in
    Post("test/", HttpEntity(ContentTypes.`application/json`, "")) ~> Accept(
      MediaRange(MediaTypes.`application/json`)
    ) ~> `User-Agent`("testAgent") ~> `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)) ~>
    RawHeader("x-opa-envoy-authz", "testOPA") ~>
    RawHeader("x-app-name", "testAppName") ~>
    RawHeader("API-Key", "testAPIKey") ~>
    RawHeader("Request-ID", "testRequestID") ~>
    RawHeader("X-WhiteLabel-Token", "testWhiteLabelToken") ~>
    RawHeader("x-mesh-target-cluster", "hk-int-2x") ~> extractHeader { header =>
      val expected = Seq(
        ContentTypes.`application/json`,
        Accept(MediaRange(MediaTypes.`application/json`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("API-Key", "testAPIKey"),
        RawHeader("Request-ID", "testRequestID"),
        RawHeader("X-WhiteLabel-Token", "testWhiteLabelToken"),
        RawHeader("x-opa-envoy-authz", "testOPA"),
        RawHeader("x-app-name", "testAppName"),
        RawHeader("x-mesh-target-cluster", "hk-int-2x")
      )
      complete(HttpResponse(entity = HttpEntity(ContentTypes.`application/json`, "passed")))

    } ~> check {
      responseAs[String] shouldEqual "passed"
    }

  "A HttpHeaderSupport" should "success validateMissingHeader" in
    Post(uri) ~> validateMissingHeader(
      true,
      Seq(
        Accept(MediaRange(MediaTypes.`application/json`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("API-Key", "testAPIKey"),
        RawHeader("Request-ID", "testRequestID"),
        RawHeader("Client-ID", "testClientID")
      )
    ) {
      complete(HttpResponse(entity = HttpEntity(ContentTypes.`application/json`, "passed")))
    } ~> check {
      responseAs[String] shouldEqual "passed"
    }

  "A HttpHeaderSupport" should "fail validateMissingHeader(API-Key)" in
    Post(uri) ~> validateMissingHeader(
      true,
      Seq(
        Accept(MediaRange(MediaTypes.`application/json`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("Request-ID", "testRequestID"),
        RawHeader("Client-ID", "testClientID")
      )
    ) {
      complete(HttpEntity.Empty)
    } ~> check {
      rejection shouldBe MissingHeaderRejection("api-key")
    }

  "A HttpHeaderSupport" should "fail validateMissingHeader(Client-ID)" in
    Post(uri) ~> validateMissingHeader(
      true,
      Seq(
        Accept(MediaRange(MediaTypes.`application/json`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("API-Key", "testAPIKey"),
        RawHeader("Request-ID", "testRequestID")
      )
    ) {
      complete(HttpEntity.Empty)
    } ~> check {
      rejection shouldBe MissingHeaderRejection("client-id")
    }

  "A HttpHeaderSupport" should "success validateMissingHeader(Request-ID)" in
    Post(uri) ~> validateMissingHeader(
      true,
      Seq(
        Accept(MediaRange(MediaTypes.`application/json`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("API-Key", "testAPIKey"),
        RawHeader("Client-ID", "testClientID")
      )
    ) {
      complete(HttpResponse(entity = HttpEntity(ContentTypes.`application/json`, "passed")))
    } ~> check {
      responseAs[String] shouldEqual "passed"
    }

  "A HttpHeaderSupport" should "success validateHeaderValue" in
    Post(uri) ~> validateHeaderValue(
      Seq(
        Accept(MediaRange(MediaTypes.`application/json`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("API-Key", "testAPIKey"),
        RawHeader("Request-ID", "testRequestID")
      )
    ) {
      complete(HttpResponse(entity = HttpEntity(ContentTypes.`application/json`, "passed")))
    } ~> check {
      responseAs[String] shouldEqual "passed"
    }

  "A HttpHeaderSupport" should "fail validateHeaderValue(Accept)" in
    Post(uri) ~> validateHeaderValue(
      Seq(
        Accept(MediaRange(MediaTypes.`application/javascript`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("API-Key", "testAPIKey"),
        RawHeader("Request-ID", "testRequestID")
      )
    ) {
      complete(HttpResponse(entity = HttpEntity(ContentTypes.`application/json`, "passed")))
    } ~> check {
      rejection shouldBe MalformedHeaderRejection(Accept.name, "")
    }

  "A HttpHeaderSupport" should "return header RequestId" in {
    val requestId = getRequestId(
      Seq(
        Accept(MediaRange(MediaTypes.`application/pdf`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("API-Key", "testAPIKey"),
        RawHeader("Request-ID", "testRequestID")
      )
    )
    assertResult("testRequestID")(requestId)
  }

  "A AgEnvironment" should "return header ag-env" in {
    val requestId = getAgHeaders(
      Seq(
        Accept(MediaRange(MediaTypes.`application/pdf`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("API-Key", "testAPIKey"),
        RawHeader("ag-env", "uat")
      )
    )
    assertResult(Seq(AgHttpHeader("ag-env", "uat")))(requestId)
  }

  "A HttpHeaderSupport" should "return header AG-MSE-PRICING-TOKEN" in {
    val requestId = getAgHeaders(
      Seq(
        Accept(MediaRange(MediaTypes.`application/pdf`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("API-Key", "testAPIKey"),
        RawHeader("AG-MSE-PRICING-TOKEN", "token")
      )
    )
    assertResult(Seq(AgHttpHeader("AG-MSE-PRICING-TOKEN", "token")))(requestId)
  }

  "A HttpHeaderSupport" should "return header AG-ATF-DEBUG-ID" in {
    val requestId = getAgHeaders(
      Seq(
        Accept(MediaRange(MediaTypes.`application/pdf`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("API-Key", "testAPIKey"),
        RawHeader("AG-ATF-DEBUG-ID", "test")
      )
    )
    assertResult(Seq(AgHttpHeader("AG-ATF-DEBUG-ID", "test")))(requestId)
  }

  "A HttpHeaderSupport" should "return header User-Agent" in {
    val userAgent = getUserAgent(
      Seq(
        Accept(MediaRange(MediaTypes.`application/json`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("API-Key", "testAPIKey"),
        RawHeader("Request-ID", "testRequestID")
      )
    )
    assertResult(Some("testAgent"))(userAgent)
  }

  "A HttpHeaderSupport" should "return header DeviceDetectionInfo" in {
    val deviceDetectionInfo = getDeviceDetectionInfo(
      Seq(
        Accept(MediaRange(MediaTypes.`application/json`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("API-Key", "testAPIKey"),
        RawHeader("Request-ID", "testRequestID"),
        RawHeader("ag-device-detection-info", "testDeviceDetectionInfo")
      )
    )
    assertResult(Some("testDeviceDetectionInfo"))(deviceDetectionInfo)
  }

  "A HttpHeaderSupport" should "return header ag-device-type-id" in {
    val deviceTypeId = getDeviceTypeId(
      Seq(
        Accept(MediaRange(MediaTypes.`application/json`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("API-Key", "testAPIKey"),
        RawHeader("Request-ID", "testRequestID"),
        RawHeader("AG-Device-Type-Id", "1")
      )
    )
    assertResult(Some(1))(deviceTypeId)
  }

  "A HttpHeaderSupport" should "return header x-mesh-target-cluster" in {
    val searchCLuster = getMeshTargetCluster(
      Seq(
        Accept(MediaRange(MediaTypes.`application/json`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("API-Key", "testAPIKey"),
        RawHeader("Request-ID", "testRequestID"),
        RawHeader("x-mesh-target-cluster", "hk-int-2x")
      )
    )
    assertResult(Some("hk-int-2x"))(searchCLuster)
  }

  "A HttpHeaderSupport" should "return header x-mesh-target-cluster in empty" in {
    val searchCLuster = getMeshTargetCluster(
      Seq(
        Accept(MediaRange(MediaTypes.`application/json`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("API-Key", "testAPIKey"),
        RawHeader("Request-ID", "testRequestID")
      )
    )
    assertResult(None)(searchCLuster)
  }

  "A HttpHeaderSupport" should "return header x-opa-envoy-authz" in {
    val opaResult = getOpaAuth(
      Seq(
        Accept(MediaRange(MediaTypes.`application/pdf`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("x-opa-envoy-authz", "testOPA")
      )
    )
    assertResult("testOPA")(opaResult)
  }

  "A HttpHeaderSupport" should "return header x-app-name" in {
    val appName = getClientAppName(
      Seq(
        Accept(MediaRange(MediaTypes.`application/pdf`)),
        `User-Agent`("testAgent"),
        `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
        RawHeader("x-app-name", "testAppName")
      )
    )
    assertResult("testAppName")(appName)
  }

  "A HttpHeaderSupport" should "no whiteLabel header should pass as agoda and contain no token" in {
    val (whiteLabelInfo, whiteLabelValidate) =
      getAndValidateWhiteLabelKey(
        Seq(
          Accept(MediaRange(MediaTypes.`application/pdf`)),
          `User-Agent`("testAgent"),
          `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip))
        ),
        uri = uri,
        clientId = 0
      )
    assertResult(whiteLabelValidate)(pass)
    assertResult(WhiteLabel.Agoda)(whiteLabelInfo.get.whiteLabelId)
    assertResult(None)(whiteLabelInfo.get.token)
  }

  "A HttpHeaderSupport" should "getAndValidateWhiteLabelKey: single whitelabel header should pass as correct Id and token" in {
    def validateSingleWhitelabelToken(whitelabel: WhiteLabel, whitelabelToken: String) = {
      val (whiteLabelInfo, whiteLabelValidate) = getAndValidateWhiteLabelKey(
        Seq(
          Accept(MediaRange(MediaTypes.`application/pdf`)),
          `User-Agent`("testAgent"),
          `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
          RawHeader("X-WhiteLabel-Token", whitelabelToken)
        ),
        uri = uri,
        clientId = 0
      )
      assertResult(whiteLabelValidate)(pass)
      assertResult(whitelabel)(whiteLabelInfo.get.whiteLabelId)
      assertResult(expected = Some(whitelabelToken))(whiteLabelInfo.get.token)
    }

    validateSingleWhitelabelToken(WhiteLabel.Agoda, "F1A5905F-9620-45E5-9D91-D251C07E0B42")
    validateSingleWhitelabelToken(WhiteLabel.Priceline, "7115A27D-A33D-4727-8DEC-B090096E7F51")
    validateSingleWhitelabelToken(WhiteLabel.Jtb, "46BFD421-3F38-45FD-A1CF-58766401AFDC")
    validateSingleWhitelabelToken(WhiteLabel.SPWL, "0CDCF21E-DB0E-426D-A525-81BE2D4368DA")
    validateSingleWhitelabelToken(WhiteLabel.ClubTravel, "9EEE3B1E-7207-4E72-9B9E-A0090D78403B")
    validateSingleWhitelabelToken(WhiteLabel.LotteDFS, "FF4A9BF5-97F8-49D3-8447-2E2172B43DA5")
    validateSingleWhitelabelToken(WhiteLabel.ANA, "BB7D5528-D107-40CC-8BB0-C5DD5A4F3053")
    validateSingleWhitelabelToken(WhiteLabel.CitiUS, "5234834A-9346-47CE-BD5A-F2B754D3383E")
    validateSingleWhitelabelToken(WhiteLabel.CitiMexico, "B699EA17-3A4F-4607-8091-DE4AFB3030B1")
    validateSingleWhitelabelToken(WhiteLabel.RMGofr, "E1B2EF89-DC7B-47E3-A2F4-637BC8E069CD")
    validateSingleWhitelabelToken(WhiteLabel.RMTestarossa, "4B6B80CD-8620-4698-92DD-49F632B7717C")
    validateSingleWhitelabelToken(WhiteLabel.RMTMobileTravel, "512FD435-3755-44B5-91BD-471632456A08")
    validateSingleWhitelabelToken(WhiteLabel.RMReserved1, "CED4FF54-5762-4072-B515-485C33DD9FD6")
    validateSingleWhitelabelToken(WhiteLabel.KrisFlyer, "F671D780-A199-4CBF-A3BB-D1FB4912DB97")
    validateSingleWhitelabelToken(WhiteLabel.LifeMiles, "35ED911C-D764-4236-AA0D-9BCFBA8DB23F")
    validateSingleWhitelabelToken(WhiteLabel.USBank, "D8641644-7E5D-4EEB-88FC-87C2E90FBCD4")
    validateSingleWhitelabelToken(WhiteLabel.Travel1MyRewardsAccess, "FC3D84FC-814B-4195-891D-86491C2C6135")
    validateSingleWhitelabelToken(WhiteLabel.Travel2MyRewardsAccess, "3B5CD422-7CEC-479E-8AEB-5574073072C6")
    validateSingleWhitelabelToken(WhiteLabel.Travel3MyRewardsAccess, "B65C8AB8-4B61-4FAD-905A-9E17616BC386")
    validateSingleWhitelabelToken(WhiteLabel.Travel4MyRewardsAccess, "3A2C6DF8-E746-4029-AA47-F17F11BF5BCC")
    validateSingleWhitelabelToken(WhiteLabel.USBankUat1, "B65CC8BA-29C2-4521-99C1-86A27046D9BB")
    validateSingleWhitelabelToken(WhiteLabel.Travel1MyRewardsAccessUat1, "135911F4-7033-4FFB-BCA7-296E1B8035FA")
    validateSingleWhitelabelToken(WhiteLabel.Travel2MyRewardsAccessUat1, "98D8C3F3-326D-4157-BC8A-2E36A42A48EE")
    validateSingleWhitelabelToken(WhiteLabel.Travel3MyRewardsAccessUat1, "CDFA6791-33EA-4DF7-8B44-A88A14E865FD")
    validateSingleWhitelabelToken(WhiteLabel.Travel4MyRewardsAccessUat1, "B5A9F3DE-3937-4CEE-9A29-D2229CE31173")
    validateSingleWhitelabelToken(WhiteLabel.LifeMilesUat, "7EE0C9ED-1AF6-41D2-B1A0-31B95B871C77")
    validateSingleWhitelabelToken(WhiteLabel.AffiliateSandbox, "F3A9B7C2-4D8E-6F01-23AB-45CDEF789012")
    validateSingleWhitelabelToken(WhiteLabel.AgodaUat, "35B8FAAF-FB4B-4B27-A5A8-F5B713814FF5")
  }

  "A HttpHeaderSupport" should "getAndValidateWhiteLabelKey: single whitelabel header should pass correct feature enabled func" in {
    def validateSingleWhitelabelToken(whitelabel: WhiteLabel, whitelabelToken: String, isFeatureEnabled: Boolean) = {
      when(mockWlClient.isFeatureEnabled(any(), any(), any(), any(), any())).thenReturn(isFeatureEnabled)
      val (whiteLabelInfo, whiteLabelValidate) = getAndValidateWhiteLabelKey(
        Seq(
          Accept(MediaRange(MediaTypes.`application/pdf`)),
          `User-Agent`("testAgent"),
          `Accept-Encoding`(HttpEncodingRange(HttpEncodings.gzip)),
          RawHeader("X-WhiteLabel-Token", whitelabelToken)
        ),
        uri = uri,
        clientId = 0
      )
      assertResult(whiteLabelValidate)(pass)
      assertResult(whitelabel)(whiteLabelInfo.get.whiteLabelId)
      assertResult(expected = Some(whitelabelToken))(whiteLabelInfo.get.token)
      assertResult(expected = isFeatureEnabled)(
        whiteLabelInfo.get.isFeatureEnabled(featureName = WhiteLabelFeatureName.TPRMCheck, None, None, _ => false)
      )
    }

    validateSingleWhitelabelToken(WhiteLabel.Agoda, "F1A5905F-9620-45E5-9D91-D251C07E0B42", isFeatureEnabled = true)
    validateSingleWhitelabelToken(WhiteLabel.Priceline, "7115A27D-A33D-4727-8DEC-B090096E7F51", isFeatureEnabled = true)
    validateSingleWhitelabelToken(WhiteLabel.Jtb, "46BFD421-3F38-45FD-A1CF-58766401AFDC", isFeatureEnabled = false)
    validateSingleWhitelabelToken(WhiteLabel.SPWL, "0CDCF21E-DB0E-426D-A525-81BE2D4368DA", isFeatureEnabled = true)

  }

  "A HttpHeaderSupport" should "call withMeasure when validateAPIKey" in
    Post("test/", HttpEntity(ContentTypes.`application/json`, "")) ~> validateAPIKey(
      isAuthen = false,
      BookingAPIHttpHeader(
        "/",
        1,
        "XXXapiKey",
        "foo",
        WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration, None, false, dummyIsFeatureEnabledFunction),
        "",
        "clientName",
        "opaAuth"
      )
    ) {
      complete(HttpResponse(entity = HttpEntity(ContentTypes.`application/json`, "passed")))
    } ~> check {
      responseAs[String] shouldEqual "passed"
      verify(mockedToolSet).withMeasure(
        "opa_auth_result",
        1,
        Map(
          "client_id"       -> "1",
          "client_name"     -> "clientName",
          "request_id"      -> "foo",
          "endpoint"        -> "/",
          "opa_auth_result" -> "opaAuth",
          "app_auth_result" -> "true",
          "masked_api_key"  -> "apiKey"
        )
      )
    }
}
