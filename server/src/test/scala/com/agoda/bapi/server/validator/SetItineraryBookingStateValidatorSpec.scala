package com.agoda.bapi.server.validator

import akka.http.scaladsl.testkit.ScalatestRouteTest
import com.agoda.bapi.common.config.Configuration
import com.agoda.bapi.common.message.ActivityBookingState
import com.agoda.bapi.common.message.multi.product.{PropertyBookingState, SetStateRequest, SetStateResponse}
import com.agoda.bapi.common.model.car.VehicleBookingStateModel.{VehicleInfo, VehicleModelBooking, VehicleModelBookingCancellation, VehicleModelBookingLocation, VehicleModelBookingPickUpDropOffLocation, VehicleModelBookingSummary, VehicleModelBookingTrip, VehicleModelInternal}
import com.agoda.bapi.common.model.flight.flightModel.Breakdown
import mocks.FlightModelMock
import org.joda.time.DateTime
import org.mockito.Mockito.when
import org.scalacheck.Arbitrary
import org.scalatest.BeforeAndAfter
import org.scalatestplus.mockito.MockitoSugar
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

class SetItineraryBookingStateValidatorSpec
    extends AnyWordSpec
    with Matchers
    with ScalatestRouteTest
    with MockitoSugar
    with BeforeAndAfter
    with FlightModelMock {

  val config    = mock[Configuration]
  val validator = new BookingsRequestValidatorImpl(config)

  "SetItineraryBookingStateValidator" should {
    "return error when flight, vehicle and property are empty" in {
      val request = mock[SetStateRequest]
      when(request.flights).thenReturn(Seq.empty)
      when(request.properties).thenReturn(None)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq.empty)
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      val response = validator.validateSetItineraryBookingState(request)
      val expectedResponse =
        Some(SetStateResponse(false, Some("InvalidRequestData"), Some("flight, vehicle and property are empty")))
      response shouldEqual expectedResponse
    }

    "return None when flight, vehicle and property are empty but has history" in {
      val request = mock[SetStateRequest]
      when(request.flights).thenReturn(Seq.empty)
      when(request.properties).thenReturn(None)
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.history).thenReturn(Seq(defaultHistory))
      when(request.payments).thenReturn(Seq.empty)
      val response         = validator.validateSetItineraryBookingState(request)
      val expectedResponse = None
      response shouldEqual expectedResponse
    }

    "return None when flight, vehicle and property are empty but has payments" in {
      val request = mock[SetStateRequest]
      when(request.flights).thenReturn(Seq.empty)
      when(request.properties).thenReturn(None)
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(None)
      when(request.history).thenReturn(Seq.empty)
      when(request.payments).thenReturn(Seq(defaultPayment))
      val response         = validator.validateSetItineraryBookingState(request)
      val expectedResponse = None
      response shouldEqual expectedResponse
    }

    "return error when property booking id = 0" in {
      val request = mock[SetStateRequest]
      when(request.flights).thenReturn(Seq.empty)
      when(request.properties).thenReturn(Some(Seq(PropertyBookingState(0, 2))))
      val response = validator.validateSetItineraryBookingState(request)
      val expectedResponse =
        Some(SetStateResponse(false, Some("InvalidRequestData"), Some("property booking id in List(0) is invalid")))
      response shouldEqual expectedResponse
    }

    "return error when property state id is invalid" in {
      val request = mock[SetStateRequest]
      when(request.flights).thenReturn(Seq.empty)
      when(request.properties).thenReturn(Some(Seq(PropertyBookingState(10, 2), PropertyBookingState(11, 12))))
      val response = validator.validateSetItineraryBookingState(request)
      val expectedResponse = Some(
        SetStateResponse(false, Some("InvalidRequestData"), Some("property booking state in List(2, 12) is invalid"))
      )
      response shouldEqual expectedResponse
    }

    "return None when property booking id > 0" in {
      val request = mock[SetStateRequest]
      when(request.flights).thenReturn(Seq.empty)
      when(request.properties).thenReturn(Some(Seq(PropertyBookingState(1L, 2))))
      val response         = validator.validateSetItineraryBookingState(request)
      val expectedResponse = None
      response shouldEqual expectedResponse
    }

    "call validateSetFlightBookingState when flight is existed in request" in {
      val request = mock[SetStateRequest]
      when(request.flights).thenReturn(Seq(defaultFlightBooking))
      when(request.bookingType).thenReturn(Some(1))
      when(request.slices).thenReturn(Seq(defaultFlightSlice))
      when(request.segments).thenReturn(Seq(defaultFlightSegment))
      when(request.passengers).thenReturn(Seq(defaultFlightPax))
      when(request.breakdown).thenReturn(Seq(defaultPriceBreakdown))
      val response         = validator.validateSetItineraryBookingState(request)
      val expectedResponse = None
      response shouldEqual expectedResponse
    }

    "return None when flight is validated by validateSetFlightBookingState and property booking id > 0" in {
      val request = mock[SetStateRequest]
      when(request.properties).thenReturn(Some(Seq(PropertyBookingState(10000, 2))))
      when(request.flights).thenReturn(Seq(defaultFlightBooking))
      when(request.bookingType).thenReturn(Some(1))
      when(request.slices).thenReturn(Seq(defaultFlightSlice))
      when(request.segments).thenReturn(Seq(defaultFlightSegment))
      when(request.passengers).thenReturn(Seq(defaultFlightPax))
      when(request.breakdown).thenReturn(Seq(defaultPriceBreakdown))
      val response         = validator.validateSetItineraryBookingState(request)
      val expectedResponse = None
      response shouldEqual expectedResponse
    }

    "return None when property state is manual processing" in {
      val request = mock[SetStateRequest]
      when(request.properties).thenReturn(Some(Seq(PropertyBookingState(10000, 13))))
      when(request.flights).thenReturn(Seq())
      val response         = validator.validateSetItineraryBookingState(request)
      val expectedResponse = None
      response shouldEqual expectedResponse
    }
  }

  "SetVehicleState" should {
    import com.danielasfregola.randomdatagenerator.RandomDataGenerator._
    implicit val arbitraryJodaDateTime = Arbitrary(DateTime.now)
    val mockVehicleModelInternal = VehicleModelInternal(
      vehicleBooking = random[VehicleModelBooking],
      vehicleBookingLocation = VehicleModelBookingPickUpDropOffLocation(
        pickUp = random[VehicleModelBookingLocation],
        dropOff = random[VehicleModelBookingLocation]
      ),
      vehicleBookingSummary = random[VehicleModelBookingSummary],
      vehicleBookingTrip = random[VehicleModelBookingTrip],
      vehicleFinancialBreakdowns = Seq(random[Breakdown]),
      vehicleBookingCancellation = Some(random[VehicleModelBookingCancellation]),
      vehicleInfo = Some(random[VehicleInfo]),
      baseBooking = None
    )

    "valid VehicleModelInternal" in {
      val request = mock[SetStateRequest]
      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq.empty)
      when(request.vehicle).thenReturn(Some(Seq(mockVehicleModelInternal)))
      val response = validator.validateSetItineraryBookingState(request)
      response shouldEqual None
    }
  }

  "SetFlightBookingStateValidator" should {
    "return None when got happy SetStateRequest" in {
      val request = mock[SetStateRequest]
      when(request.flights).thenReturn(Seq(defaultFlightBooking))
      when(request.bookingType).thenReturn(Some(1))
      when(request.slices).thenReturn(Seq(defaultFlightSlice))
      when(request.segments).thenReturn(Seq(defaultFlightSegment))
      when(request.passengers).thenReturn(Seq(defaultFlightPax))
      when(request.breakdown).thenReturn(Seq(defaultPriceBreakdown))
      val response         = validator.validateSetFlightBookingState(request)
      val expectedResponse = None
      response shouldEqual expectedResponse
    }

    "return None when SetStateRequest has bookingType = None" in {
      val request = mock[SetStateRequest]
      when(request.flights).thenReturn(Seq(defaultFlightBooking))
      when(request.bookingType).thenReturn(None)
      when(request.slices).thenReturn(Seq(defaultFlightSlice))
      when(request.segments).thenReturn(Seq(defaultFlightSegment))
      when(request.passengers).thenReturn(Seq(defaultFlightPax))
      when(request.breakdown).thenReturn(Seq(defaultPriceBreakdown))
      val response         = validator.validateSetFlightBookingState(request)
      val expectedResponse = None
      response shouldEqual expectedResponse
    }
  }

  "SetActivityBookingStateValidator" should {
    val mockActivityBookingState = ActivityBookingState("abc", 1)

    "valid ActivityModelInternal" in {
      val request = mock[SetStateRequest]
      when(request.properties).thenReturn(None)
      when(request.flights).thenReturn(Seq.empty)
      when(request.vehicle).thenReturn(None)
      when(request.activities).thenReturn(Some(Seq(mockActivityBookingState)))
      val response = validator.validateSetItineraryBookingState(request)
      response shouldEqual None
    }
  }
}
