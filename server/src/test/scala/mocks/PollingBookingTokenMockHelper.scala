package mocks

import com.agoda.bapi.common.message.creation.PaymentAmount
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.token._
import com.agoda.bapi.common.util.TokenSerializers
import com.agoda.bapi.common.util.TokenSerializers._
import com.agoda.mpb.common.models.state.{PayNowProductPayment, ProductPayment, ProductPaymentInfo}

object PollingBookingTokenMockHelper extends ProductTokenMockHelper {
  val bapiTokenWithProductPayment = defaultPropertyBookingToken.copy(
    productPayment = Some(
      ProductPaymentInfo(
        agency = None,
        payNow = Some(
          PayNowProductPayment(
            ProductPayment(
              paymentAmount = 23.3,
              paymentAmountUsd = 6.0,
              paymentCurrency = "EUR",
              siteExchangeRate = None
            ),
            false
          )
        ),
        payLater = None
      )
    ),
    isPriceGuaranteedTokenCleared = true
  )
  val bapiTokenWithProductPaymentTwo = defaultPropertyBookingToken.copy(
    productPayment = Some(
      ProductPaymentInfo(
        agency = None,
        payNow = Some(
          PayNowProductPayment(
            ProductPayment(
              paymentAmount = 23.3,
              paymentAmountUsd = 23.3,
              paymentCurrency = "EUR",
              siteExchangeRate = None
            ),
            false
          )
        ),
        payLater = None
      )
    ),
    isPriceGuaranteedTokenCleared = true
  )
  val setupTokenWithNoPGT = defaultPropertySetupToken.copy(productToken = None)
  val multiProductSetupToken = MultiProductSetupBookingToken(
    properties = TokenSerializers[PropertySetupModel]
      .serialize(Map(productKey -> setupTokenWithNoPGT), Some(1571200505532L), None)
      .toOption
  )
  val multiProductCreationToken = MultiProductCreationBookingToken(
    properties = TokenSerializers[PropertyBookingModel]
      .serialize(Map(productKey -> bapiTokenWithProductPayment), Some(1571199718191L), None)
      .toOption,
    flights = None,
    tripProtections = None,
    cars = None,
    activities = None,
    priceFreezes = None,
    cegFastTracks = None,
    addOns = None,
    payment = PaymentAmount("THB", 4300, 120, 0.1, 0, 0.1, 0.1, None, 0, 0, 0, 0, 0, 0, 0, None),
    bookingFlowType = BookingFlow.Package,
    commonPayment = None
  )
  val multiProductCreationTokenTwo = MultiProductCreationBookingToken(
    properties = TokenSerializers[PropertyBookingModel]
      .serialize(Map(productKey -> bapiTokenWithProductPaymentTwo), Some(1571199718191L), None)
      .toOption,
    flights = None,
    tripProtections = None,
    cars = None,
    activities = None,
    priceFreezes = None,
    cegFastTracks = None,
    addOns = None,
    payment = PaymentAmount("THB", 4300, 120, 0.1, 0, 0.1, 0.1, None, 0, 0, 0, 0, 0, 0, 0, None),
    bookingFlowType = BookingFlow.Package,
    commonPayment = None
  )
  val multiProductToken = MultiProductBookingToken(
    setupBookingToken = TokenSerializers[MultiProductSetupBookingToken]
      .serialize(multiProductSetupToken, Some(1571200505532L), None)
      .toOption,
    creationBookingToken = TokenSerializers[MultiProductCreationBookingToken]
      .serialize(multiProductCreationToken, Some(1571200505532L), None)
      .toOption,
    None
  )
  val multiProductTokenTwo = MultiProductBookingToken(
    setupBookingToken = TokenSerializers[MultiProductSetupBookingToken]
      .serialize(multiProductSetupToken, Some(1571200505532L), None)
      .toOption,
    creationBookingToken = TokenSerializers[MultiProductCreationBookingToken]
      .serialize(multiProductCreationTokenTwo, Some(1571200505532L), None)
      .toOption,
    None
  )
  val multiBookingTokenString = TokenSerializers
    .toJsonString(
      TokenSerializers[MultiProductBookingToken].serialize(multiProductToken, Some(1571853316957L), None).get
    )
    .get
  val multiBookingTokenStringTwo = TokenSerializers
    .toJsonString(
      TokenSerializers[MultiProductBookingToken].serialize(multiProductTokenTwo, Some(1571853316957L), None).get
    )
    .get
}
