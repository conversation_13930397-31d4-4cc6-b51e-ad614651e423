package mocks

import com.agoda.bapi.common.message.creation.PaymentAmount
import com.agoda.bapi.common.message.setupBooking.PackageRequest
import com.agoda.bapi.common.model.CurrencyOption
import com.agoda.bapi.common.model.activity.{ActivityBookingToken, ActivityMockData}
import com.agoda.bapi.common.model.addOn.cegFastTrack.AddOnBookingToken
import com.agoda.bapi.common.model.addOn.cegFastTrack.CEGFastTrackTiers.NormalHotelFastTrack
import com.agoda.bapi.common.model.car._
import com.agoda.bapi.common.model.creation.{BAPIBooking, BookingCancellation, BookingItem, BookingItemBreakdown, BookingPrice, BookingRoom, BookingRoomPayment, BookingSellInfo, CancellationChargeType, Capacity, EBEBooking, EBEHotel, InfoSummary, PaymentModel => PapiPaymentModel, RateModel, StayType}
import com.agoda.bapi.common.model.flight._
import com.agoda.bapi.common.model.payment.{PaymentModel, SupplierPaymentMethod}
import com.agoda.bapi.common.model.product.{BookingFlow, ProductTypeEnum}
import com.agoda.bapi.common.model.tripProtection.{ProtectionProductIds, SupplierData, TripProtectionItemBreakdown, TripProtectionToken}
import com.agoda.bapi.common.token._
import com.agoda.bapi.common.util.TokenSerializers
import com.agoda.bapi.common.util.TokenSerializers._
import com.agoda.common.itineraryContext.{ItineraryContext, ProductGroupItem, ProductGroupType}
import com.agoda.mpb.common.models.state.{PayNowProductPayment, ProductPayment, ProductPaymentInfo}
import org.joda.time.DateTime
import scalapb.json4s.JsonFormat

import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter

trait ProductTokenMockHelper {
  val productKey = "1"
  val defaultFlightBookingToken = FlightBookingToken(
    supplierId = 30001,
    subSupplierId = 0,
    supplierData =
      """{"priceToken":"R_BKK201909100850RGN201909100940MPG701~RGN201909101735SIN201909102210YSQ5019_2_USD981.24_145239933000704-145239933000614-8","groupId":"145239933000704","refId":"145239933000614","pricedConfirmationId":"*********","price":{"pointOfSale":{"totalBaseWithTaxesAndFees":1962.9,"totalBasePrice":1841.6,"totalTaxes":121.3,"totalFees":0.0,"fees":[],"currencyCode":"USD"},"transaction":{"totalBaseWithTaxesAndFees":1962.9,"totalBasePrice":1841.6,"totalTaxes":121.3,"totalFees":0.0,"fees":[],"currencyCode":"USD"}}}""",
    expiresAt = new DateTime(DateTime.parse("2019-06-07T01:29:34").getMillis),
    info = Some(
      FlightInfo(
        2,
        0,
        Some(0),
        "SQ",
        None,
        PaymentModel.Unknown,
        Vector(
          Slice(
            List(),
            Vector(
              Segment(
                "BKK",
                "RGN",
                "319",
                "PG",
                "",
                "701",
                new DateTime(DateTime.parse("2019-09-10T08:50:00.00").getMillis),
                new DateTime(DateTime.parse("2019-09-10T09:40:00.00").getMillis),
                "M",
                "ECO",
                "Economy Class",
                "MOW",
                "",
                80,
                List(),
                None,
                None,
                false,
                None,
                Some("TT1"),
                Some("TT2")
              ),
              Segment(
                "RGN",
                "SIN",
                "738",
                "SQ",
                "",
                "5019",
                new DateTime(DateTime.parse("2019-09-10T17:35:00.00").getMillis),
                new DateTime(DateTime.parse("2019-09-10T22:10:00.00").getMillis),
                "Y",
                "ECO",
                "Economy Class",
                "YIFSQ",
                "",
                185,
                List(),
                None,
                None,
                false,
                None,
                Some("TT1"),
                Some("TT2")
              )
            )
          )
        ),
        List(
          FlightItemBreakdown(
            new DateTime(0),
            1,
            101,
            Some(0),
            1,
            "USD",
            1841.6,
            0.0,
            1841.6,
            1841.6,
            1.0,
            Option("INR")
          ),
          FlightItemBreakdown(
            new DateTime(0),
            11,
            101,
            Some(0),
            1,
            "USD",
            1841.6,
            0.0,
            1841.6,
            1841.6,
            1.0,
            Option("INR")
          ),
          FlightItemBreakdown(
            new DateTime(0),
            10,
            101,
            Some(0),
            1,
            "USD",
            1962.9,
            0.0,
            1962.9,
            1962.9,
            1.0,
            Option("INR")
          ),
          FlightItemBreakdown(
            new DateTime(0),
            12,
            101,
            Some(0),
            1,
            "USD",
            1962.9,
            0.0,
            1962.9,
            1962.9,
            1.0,
            Option("INR")
          ),
          FlightItemBreakdown(new DateTime(0), 3, 101, Some(0), 1, "USD", 0.0, 0.0, 0.0, 0.0, 1.0, Option("INR")),
          FlightItemBreakdown(new DateTime(0), 1, 101, Some(0), 1, "USD", 121.3, 0.0, 121.3, 121.3, 1.0, Option("INR")),
          FlightItemBreakdown(new DateTime(0), 16, 101, Some(0), 1, "USD", 0.0, 0.0, 0.0, 0.0, 1.0, Option("INR"))
        ),
        isPassportRequired = false,
        isNationalityRequired = false,
        None,
        Some(
          List(
            FlightItemBreakdownPerPax(
              new DateTime(0),
              1,
              101,
              Some(0),
              1,
              "USD",
              1841.6,
              0.0,
              1841.6,
              1841.6,
              1.0,
              "THB",
              1
            ),
            FlightItemBreakdownPerPax(
              new DateTime(0),
              11,
              101,
              Some(0),
              1,
              "USD",
              1841.6,
              0.0,
              1841.6,
              1841.6,
              1.0,
              "THB",
              1
            ),
            FlightItemBreakdownPerPax(
              new DateTime(0),
              10,
              101,
              Some(0),
              1,
              "USD",
              1962.9,
              0.0,
              1962.9,
              1962.9,
              1.0,
              "THB",
              1
            ),
            FlightItemBreakdownPerPax(
              new DateTime(0),
              12,
              101,
              Some(0),
              1,
              "USD",
              1962.9,
              0.0,
              1962.9,
              1962.9,
              1.0,
              "THB",
              1
            ),
            FlightItemBreakdownPerPax(new DateTime(0), 3, 101, Some(0), 1, "USD", 0.0, 0.0, 0.0, 0.0, 1.0, "THB", 1),
            FlightItemBreakdownPerPax(
              new DateTime(0),
              1,
              101,
              Some(0),
              1,
              "USD",
              121.3,
              0.0,
              121.3,
              121.3,
              1.0,
              "THB",
              1
            ),
            FlightItemBreakdownPerPax(new DateTime(0), 16, 101, Some(0), 1, "USD", 0.0, 0.0, 0.0, 0.0, 1.0, "THB", 1)
          )
        ),
        passengerMinAge = Some(0),
        fareRulePolicies = None,
        supplierPaymentMethod = Some(SupplierPaymentMethod.None)
      )
    ),
    paymentAmount =
      Some(PaymentAmount("THB", 2150, 60, 0.1, 0.1, 0.1, 0.1, Some("string"), 0, 0, 0, 0, 0, 0, 0, Some("string"))),
    searchId = "8463d781-6c32-4416-a253-1c9cc0c4cec2",
    itineraryId = "1154072730",
    ancillaryAddOns = None,
    priceId = "30001"
  )

  val defaultTripProtectionToken = TripProtectionToken(
    protectionType = 1,
    products =
      Seq(ProtectionProductIds("1", ProductTypeEnum.Flight), ProtectionProductIds("2", ProductTypeEnum.Flight)),
    priceAmount = CurrencyOption(
      23.3,
      "EUR"
    ),
    priceAmountUSD = 27.7,
    marginAmount = CurrencyOption(
      13.3,
      "EUR"
    ),
    marginAmountUSD = 16.6,
    marginPercentage = 10,
    quantity = 2,
    tripCost = CurrencyOption(13.3, "EUR"),
    purchaseDate = OffsetDateTime.parse("2019-03-21T22:00:42.464Z", DateTimeFormatter.ISO_OFFSET_DATE_TIME),
    supplierId = 1,
    subSupplierId = 2,
    supplierData = SupplierData("supplierData"),
    supplierSearchId = "1",
    financialBreakdowns = Seq(
      TripProtectionItemBreakdown(
        eventDate = DateTime.parse("2020-02-02"),
        itemId = 10,
        typeId = 301,
        taxFeeId = 2,
        quantity = 3,
        localCurrency = "THB",
        localAmount = 62.84,
        exchangeRate = 31.42,
        usdAmount = 2,
        reqAmount = 2.5,
        vendorExchangeRate = 5.2,
        requestedCurrency = "INR"
      )
    ),
    paymentModel = PaymentModel.Merchant
  )

  val defaultPropertyBookingToken = BAPIBooking(
    searchId = "searchId",
    prebookingId = Some(111333),
    propertyId = 0,
    cityId = 0,
    countryId = 123,
    booking = Some(
      BookingItem(
        booking = List(
          EBEBooking(
            hotel = List(
              EBEHotel(
                checkIn = DateTime.parse("2018-11-16T11:37:10.645Z"),
                checkOut = DateTime.parse("2018-11-16T11:37:10.645Z"),
                numberOfAdults = 0,
                numberOfChildren = 0,
                numberOfRooms = 0,
                occFreeSearch = false,
                room = List(
                  BookingRoom(
                    uid = "1",
                    accountingEntity = None,
                    availabilityType = com.agoda.bapi.common.model.creation.AvailabilityType.Guarantee,
                    hotelId = 0,
                    capacity = Capacity(2),
                    noOfAdults = 0,
                    noOfChildren = 0,
                    noOfExtrabeds = 0,
                    numberOfRoom = 0,
                    breakfastIncluded = false,
                    breakfastInfo = "breakfast",
                    cancellation = BookingCancellation(
                      "test",
                      Some("cancellation"),
                      "test",
                      Option(Seq("test")),
                      Seq("test"),
                      Some(CancellationChargeType.PerBook)
                    ),
                    ycsRatePlanId = 0,
                    pricing = BookingPrice(
                      List(
                        BookingItemBreakdown(
                          chargeDate = None,
                          exchangeRate = 0.5,
                          itemId = 0,
                          localAmount = 0,
                          localCurrency = "RUB",
                          quantity = 0,
                          roomNo = 0,
                          usdAmount = 0,
                          surchargeId = None,
                          taxFeeId = None,
                          typeId = 0,
                          taxProtoTypeId = Some(0),
                          subTypeId = None
                        )
                      )
                    ),
                    exchange = BookingRoomPayment(None, None),
                    rateCategory = None,
                    borSupplier = None,
                    isNotCcRequired = false,
                    dmcId = Some(332),
                    dmcSpecificData = None,
                    excluded = None,
                    included = None,
                    roomTypeId = 0,
                    roomTypeName = "Name",
                    sellInfo = Some(
                      BookingSellInfo(
                        downliftUsd = None,
                        pricingRequestId = Some("pricingRequestId-1"),
                        isAdvanceGuarantee = false
                      )
                    ),
                    promotion = None,
                    displayCurrency = Some("USD"),
                    benefit = Seq(),
                    rateModel = RateModel(1),
                    taxSurchargeInfo = None,
                    paymentModels = PapiPaymentModel.Merchant,
                    npclnChannel = None,
                    isPrepaymentRequired = None,
                    searchId = None,
                    searchResultId = None,
                    isAgodaAgency = None
                  )
                ),
                stayType = Some(StayType.Nightly)
              )
            )
          )
        )
      )
    ),
    infoSummary = Some(InfoSummary(false, None)),
    masterRooms = Seq(),
    paymentAmount =
      Some(PaymentAmount("THB", 2150, 60, 0.1, 0.1, 0.1, 0.1, Some("string"), 0, 0, 0, 0, 0, 0, 0, Some("string"))),
    campaignInfo = None,
    productTokenKey = Some("1"),
    productPayment = None
  )

  val defaultPropertySetupToken = PropertySetupBookingToken(
    productTokenKey = "1",
    absRequestId = Some("AABBCC"),
    allotmentResult = Some(1),
    productToken = Some("df-token"),
    preBookingId = Some(1L)
  )

  val defaultItineraryContext: ItineraryContext = ItineraryContext(
    productGroups = Seq(
      ProductGroupItem(
        "product-1",
        productGroupId = "group-1",
        productGroupType = ProductGroupType.Package
      ),
      ProductGroupItem(
        "product-2",
        productGroupId = "group-1",
        productGroupType = ProductGroupType.Package
      )
    )
  )

  val defaultMultiProductCreationToken = MultiProductCreationBookingToken(
    properties = TokenSerializers[PropertyBookingModel]
      .serialize(Map(productKey -> defaultPropertyBookingToken), Some(1571199718191L), None)
      .toOption,
    flights = TokenSerializers[FlightBookingModel]
      .serialize(Map(productKey -> Seq(defaultFlightBookingToken)), Some(1571199718017L), None)
      .toOption,
    tripProtections = TokenSerializers[TripProtectionModel]
      .serialize(Map(productKey -> defaultTripProtectionToken), Some(1571199718017L), None)
      .toOption,
    cars = None,
    activities = None,
    priceFreezes = None,
    cegFastTracks = None,
    addOns = None,
    payment = PaymentAmount("THB", 4300, 120, 0.1, 0, 0.1, 0.1, None, 0, 0, 0, 0, 0, 0, 0, None),
    bookingFlowType = BookingFlow.Package,
    commonPayment = None,
    bookingSessionId = Some("booking-session-id"),
    itineraryContextStr = Some(JsonFormat.toJsonString(defaultItineraryContext))
  )

  val defaultMultiProductSetupToken = MultiProductSetupBookingToken(
    properties = TokenSerializers[PropertySetupModel]
      .serialize(Map(productKey -> defaultPropertySetupToken), Some(1571200505532L), None)
      .toOption,
    packageRequest = Some(
      PackageRequest("clientToken", Some("interSystemToken"))
    ),
    protectionData = Some("protectionData"),
    addOnTokenData = TokenSerializers[AncillarySetupModel]
      .serialize(Map("combination-id" -> "ancillary-cache-token"), Some(1571200505532L), None)
      .toOption
  )

  val defaultMultiProductToken = MultiProductBookingToken(
    setupBookingToken = TokenSerializers[MultiProductSetupBookingToken]
      .serialize(defaultMultiProductSetupToken, Some(1571200505532L), None)
      .toOption,
    creationBookingToken = TokenSerializers[MultiProductCreationBookingToken]
      .serialize(defaultMultiProductCreationToken, Some(1571200505532L), None)
      .toOption,
    None
  )

  val defaultMultiBookingTokenString = TokenSerializers
    .toJsonString(
      TokenSerializers[MultiProductBookingToken].serialize(defaultMultiProductToken, Some(1571853316957L), None).get
    )
    .get

  val defaultActivityBookingToken = ActivityBookingToken(
    activityInfo = ActivityMockData.activityBookingInfo,
    expiresAt = new DateTime(DateTime.parse("2020-10-10T01:29:34").getMillis),
    paymentAmount = Some(
      PaymentAmount(
        paymentCurrency = "THB",
        paymentAmount = 2150,
        paymentAmountUSD = 60,
        exchangeRate = 0.1,
        upliftAmount = 0.1,
        siteExchangeRate = 0.1,
        upliftExchangeRate = 0.1,
        destinationCurrency = Some("string"),
        destinationExchangeRate = 0,
        rateQuoteId = 0,
        rewardsRedeemedPoint = 0,
        rewardsSaving = 0,
        giftcardAmount = 0,
        giftcardAmountUSD = 0,
        exchangeRateOption = 0,
        paymentToken = Some("string")
      )
    ),
    productPayment = None
  )

  val defaultCarBookingToken =
    CarBookingToken(
      supplierId = 0,
      supplierData = "supplierData",
      providerCode = "providerCode",
      info = CarBookingInfo(
        paymentModel = PaymentModel.Merchant,
        carItemPriceSummary = CarPricingSummary(
          CarChargeDisplay(
            currency = "currency",
            baseFare = 100,
            taxAndFee = 20,
            baseDiscount = 10,
            campaignDiscount = 0,
            totalFare = 100,
            totalSurcharge = 0,
            surchargeDetails = "surchargeDetails",
            agodaFee = 5,
            policyChargesDetails = Some("policyChargeDetails"),
            paymentModel = 2,
            extraChargesDetails = Some("extraChargeDetails")
          )
        ),
        priceBreakdowns = Seq(
          CarItemBreakdown(
            eventDate = DateTime.now,
            itemId = 1,
            typeId = 1,
            taxFeeId = None,
            quantity = 1,
            localCurrency = "USD",
            localAmount = 100,
            exchangeRate = 1,
            usdAmount = 100,
            reqAmount = 100,
            vendorExchangeRate = 1,
            requestedCurrency = "INR"
          )
        ),
        pickUp = LocationInfo(
          airportInfo = None,
          cityId = 9395,
          countryId = 66,
          dateTime = DateTime.now,
          addressLine = "addressLine",
          postalCode = "postalCode",
          extraLocationInfo = "extraLocationInfo",
          locationName = "locationName",
          supplierLocationCode = "supplierLocationCode",
          locationType = Some("locationType"),
          phoneNumbers = Seq.empty
        ),
        dropOff = LocationInfo(
          airportInfo = None,
          cityId = 9395,
          countryId = 66,
          dateTime = DateTime.now,
          addressLine = "addressLine",
          postalCode = "postalCode",
          extraLocationInfo = "extraLocationInfo",
          locationName = "locationName",
          supplierLocationCode = "supplierLocationCode",
          locationType = Some("locationType"),
          phoneNumbers = Seq.empty
        ),
        vehicleCode = "vehicleCode",
        vehicleName = "vehicleName",
        classification = "classification",
        cancellationPolicy = "cancellationPolicy",
        vehicleExtraInfo = VehicleExtraInfo(
          vehicleDoors = Some(4),
          vehicleSeats = Some(1),
          vehicleSuitcases = Some(1),
          vehicleTransmission = Some("transmission"),
          vehicleIsAircon = Some(true),
          vehicleIsAirbag = Some(true),
          vehicleFuelType = Some("fuelType"),
          imageUrl = Some("imageUrl"),
          pickUpSupplierOperationOfHours =
            Some(Seq(OperationSchedule("Monday", Seq(SupplierOperationHour("06:00", "00:00"))))),
          dropOffSupplierOperationOfHours =
            Some(Seq(OperationSchedule("Monday", Seq(SupplierOperationHour("06:00", "00:00"))))),
          providerIconUrl = Some("iconUrl"),
          acrissCode = Some("acrissCode")
        ),
        vehicleMileagePolicy = Some(
          MileagePolicy(
            freeDistance = 0,
            code = "",
            description = "Mock Car Fuel Info",
            charge = None,
            isFreeCoverage = true
          )
        ),
        vehicleFuelPolicy = Some(
          FuelPolicy(
            coverageType = "Full_To_Full",
            code = "",
            description = "fuel policy",
            charge = None,
            isFreeCoverage = true
          )
        ),
        vehicleCustomerPolicyInfo = None
      ),
      expiresAt = DateTime.now,
      searchId = "123",
      itineraryId = "456",
      paymentAmount = None,
      productTokenKey = Some("1"),
      productPayment = None
    )

  val defaultAddOnBookingToken: AddOnBookingToken = {
    val requestedCurrency = "THB"
    val usdAmount         = 20
    val paymentAmount     = 600
    val exchangeRate      = 30 // paymentAmount/usdAmount

    AddOnBookingToken(
      tier = NormalHotelFastTrack,
      refProductItemId = Seq("Property_1"),
      productPayment = Some(
        ProductPaymentInfo(
          agency = None,
          payNow = Some(
            PayNowProductPayment(
              payment = ProductPayment(
                paymentAmount = paymentAmount,
                paymentAmountUsd = usdAmount,
                paymentCurrency = requestedCurrency
              )
            )
          ),
          payLater = None
        )
      ),
      paymentAmount = Some(
        PaymentAmount(
          paymentCurrency = requestedCurrency,
          paymentAmount = paymentAmount,
          paymentAmountUSD = usdAmount,
          siteExchangeRate = exchangeRate,
          exchangeRate = exchangeRate,
          upliftExchangeRate = exchangeRate,
          destinationExchangeRate = PaymentAmount.ZERO
        )
      ),
      priceBreakdown = Nil,
      startDateTime = DateTime.now(),
      endDateTime = DateTime.now(),
      productTokenKey = "CegFastTrack_1",
      metas = Nil
    )
  }

}
