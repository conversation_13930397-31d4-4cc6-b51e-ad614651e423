package mocks

import com.agoda.bapi.common.message.creation.PaymentAmount
import com.agoda.bapi.common.message.pricebreakdown.{PriceBreakdownNode, PriceBreakdownResponse, PriceBreakdownType}
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.token.{Money => TMoney, _}
import com.agoda.bapi.common.util.TokenSerializers
import com.agoda.bapi.common.util.TokenSerializers._
import com.agoda.mpb.common.PointsType
import com.agoda.mpb.common.models.state._

object CartBookingTokenMockHelper extends ProductTokenMockHelper {

  val propertyBookingToken1 = defaultPropertyBookingToken.copy(
    productPayment = Some(
      ProductPaymentInfo(
        payNow = Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 1200, paymentCurrency = "USD"))),
        agency = None,
        payLater = None
      )
    )
  )

  val propertyBookingTokenWithPrecisionAmt = defaultPropertyBookingToken.copy(
    productPayment = Some(
      ProductPaymentInfo(
        payNow =
          Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 1200.12345678, paymentCurrency = "USD"))),
        agency = None,
        payLater = None
      )
    )
  )

  val propertyBookingFullRedeemToken1 = defaultPropertyBookingToken.copy(
    productPayment = Some(
      ProductPaymentInfo(
        payNow = Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 0, paymentCurrency = "USD"))),
        agency = None,
        payLater = None,
        points = Vector(defaultExternalLoyaltyPoint(Some(1200)))
      )
    )
  )

  val propertyBookingToken2 = defaultPropertyBookingToken.copy(
    productPayment = Some(
      ProductPaymentInfo(
        payNow = Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 1700, paymentCurrency = "USD"))),
        agency = None,
        payLater = None
      )
    )
  )

  val propertyBookingFullRedeemToken2 = defaultPropertyBookingToken.copy(
    productPayment = Some(
      ProductPaymentInfo(
        payNow = Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 0, paymentCurrency = "USD"))),
        agency = None,
        payLater = None,
        points = Vector(defaultExternalLoyaltyPoint(Some(1700)))
      )
    )
  )

  val flightBookingToken1 = Seq(
    defaultFlightBookingToken.copy(
      productPayment = Some(
        ProductPaymentInfo(
          payNow = Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 2200, paymentCurrency = "USD"))),
          agency = None,
          payLater = None
        )
      )
    )
  )

  val flightBookingFullRedeemToken1 = Seq(
    defaultFlightBookingToken.copy(
      productPayment = Some(
        ProductPaymentInfo(
          payNow = Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 0, paymentCurrency = "USD"))),
          agency = None,
          payLater = None,
          points = Vector(defaultExternalLoyaltyPoint(Some(2200)))
        )
      )
    )
  )

  val flightBookingToken2 = Seq(
    defaultFlightBookingToken.copy(
      productPayment = Some(
        ProductPaymentInfo(
          payNow = Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 2700, paymentCurrency = "USD"))),
          agency = None,
          payLater = None
        )
      )
    )
  )

  val flightBookingFullRedeemToken2 = Seq(
    defaultFlightBookingToken.copy(
      productPayment = Some(
        ProductPaymentInfo(
          payNow = Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 0, paymentCurrency = "USD"))),
          agency = None,
          payLater = None,
          points = Vector(defaultExternalLoyaltyPoint(Some(2700)))
        )
      )
    )
  )

  val carBookingToken1 = defaultCarBookingToken.copy(
    productPayment = Some(
      ProductPaymentInfo(
        payNow = Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 3200, paymentCurrency = "USD"))),
        agency = None,
        payLater = None
      )
    )
  )

  val carBookingFullRedeemToken1 = defaultCarBookingToken.copy(
    productPayment = Some(
      ProductPaymentInfo(
        payNow = Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 0, paymentCurrency = "USD"))),
        agency = None,
        payLater = None,
        points = Vector(defaultExternalLoyaltyPoint(Some(3200)))
      )
    )
  )

  val carBookingToken2 = defaultCarBookingToken.copy(
    productPayment = Some(
      ProductPaymentInfo(
        payNow = Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 3700, paymentCurrency = "USD"))),
        agency = None,
        payLater = None
      )
    )
  )

  val carBookingFullRedeemToken2 = defaultCarBookingToken.copy(
    productPayment = Some(
      ProductPaymentInfo(
        payNow = Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 0, paymentCurrency = "USD"))),
        agency = None,
        payLater = None,
        points = Vector(defaultExternalLoyaltyPoint(Some(3700)))
      )
    )
  )

  val activityBookingToken1 = defaultActivityBookingToken.copy(
    productPayment = Some(
      ProductPaymentInfo(
        payNow = Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 4200, paymentCurrency = "USD"))),
        agency = None,
        payLater = None
      )
    )
  )

  val activityBookingFullRedeemToken1 = defaultActivityBookingToken.copy(
    productPayment = Some(
      ProductPaymentInfo(
        payNow = Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 0, paymentCurrency = "USD"))),
        agency = None,
        payLater = None,
        points = Vector(defaultExternalLoyaltyPoint(Some(4200)))
      )
    )
  )

  val activityBookingToken2 = defaultActivityBookingToken.copy(
    productPayment = Some(
      ProductPaymentInfo(
        payNow = Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 4700, paymentCurrency = "USD"))),
        agency = None,
        payLater = None
      )
    )
  )

  val activityBookingFullRedeemToken2 = defaultActivityBookingToken.copy(
    productPayment = Some(
      ProductPaymentInfo(
        payNow = Some(PayNowProductPayment(payment = ProductPayment(paymentAmount = 0, paymentCurrency = "USD"))),
        agency = None,
        payLater = None,
        points = Vector(defaultExternalLoyaltyPoint(Some(4700)))
      )
    )
  )

  def defaultExternalLoyaltyPoint(
      userPointsPayable: Option[Double] = None
  ): com.agoda.mpb.common.models.state.Points = {
    com.agoda.mpb.common.models.state.Points(
      PointsType.ExternalLoyalty,
      PointsAttributes(
        currency = "USD",
        amount = 30,
        localCurrency = Some("USD"),
        localAmount = userPointsPayable.map(BigDecimal.decimal),
        externalLoyaltyInfo = Some(
          ExternalLoyaltyInfo(
            loyaltyToken = Some("loyaltyToken"),
            fiatUserPayable = None,
            userPointsPayable = None,
            partnerClaim = Some("partnerClaimToken"),
            pointsToEarn = None
          )
        )
      )
    )
  }

  private val timeStamp = 1571199718191L
  def multiProductCreationTokenForCart(
      isPrecision: Boolean = false,
      priceBreakdownNode: Option[PriceBreakdownNode] = None
  ) = MultiProductCreationBookingToken(
    properties =
      if (isPrecision)
        TokenSerializers[PropertyBookingModel]
          .serialize(
            Map(
              "1" -> propertyBookingTokenWithPrecisionAmt,
              "2" -> propertyBookingToken2
            ),
            Some(timeStamp),
            None
          )
          .toOption
      else
        TokenSerializers[PropertyBookingModel]
          .serialize(
            Map(
              "1" -> propertyBookingToken1,
              "2" -> propertyBookingToken2
            ),
            Some(timeStamp),
            None
          )
          .toOption,
    flights = TokenSerializers[FlightBookingModel]
      .serialize(
        Map(
          "11" -> flightBookingToken1,
          "12" -> flightBookingToken2
        ),
        Some(timeStamp),
        None
      )
      .toOption,
    tripProtections = None,
    cars = TokenSerializers[CarBookingModel]
      .serialize(
        Map(
          "21" -> carBookingToken1,
          "22" -> carBookingToken2
        ),
        Some(timeStamp),
        None
      )
      .toOption,
    activities = TokenSerializers[ActivityBookingModel]
      .serialize(
        Map(
          "31" -> activityBookingToken1,
          "32" -> activityBookingToken2
        ),
        Some(timeStamp),
        None
      )
      .toOption,
    priceFreezes = None,
    cegFastTracks = None,
    addOns = None,
    payment = PaymentAmount(
      paymentCurrency = "THB",
      paymentAmount = 4300,
      paymentAmountUSD = 120,
      exchangeRate = 0.1,
      upliftAmount = 0,
      siteExchangeRate = 0.1,
      upliftExchangeRate = 0.1,
      destinationCurrency = None,
      destinationExchangeRate = 0,
      rateQuoteId = 0,
      rewardsRedeemedPoint = 0,
      rewardsSaving = 0,
      giftcardAmount = 0,
      giftcardAmountUSD = 0,
      exchangeRateOption = 0,
      paymentToken = None
    ),
    bookingFlowType = BookingFlow.Cart,
    commonPayment = None,
    priceBreakdown = priceBreakdownNode
  )

  val multiProductCreationTokenForCartFullRedeem = MultiProductCreationBookingToken(
    properties = TokenSerializers[PropertyBookingModel]
      .serialize(
        Map(
          "1" -> propertyBookingFullRedeemToken1,
          "2" -> propertyBookingFullRedeemToken2
        ),
        Some(timeStamp),
        None
      )
      .toOption,
    flights = TokenSerializers[FlightBookingModel]
      .serialize(
        Map(
          "11" -> flightBookingFullRedeemToken1,
          "12" -> flightBookingFullRedeemToken2
        ),
        Some(timeStamp),
        None
      )
      .toOption,
    tripProtections = None,
    cars = TokenSerializers[CarBookingModel]
      .serialize(
        Map(
          "21" -> carBookingFullRedeemToken1,
          "22" -> carBookingFullRedeemToken2
        ),
        Some(timeStamp),
        None
      )
      .toOption,
    activities = TokenSerializers[ActivityBookingModel]
      .serialize(
        Map(
          "31" -> activityBookingFullRedeemToken1,
          "32" -> activityBookingFullRedeemToken2
        ),
        Some(timeStamp),
        None
      )
      .toOption,
    priceFreezes = None,
    cegFastTracks = None,
    addOns = None,
    payment = PaymentAmount(
      paymentCurrency = "THB",
      paymentAmount = 4300,
      paymentAmountUSD = 120,
      exchangeRate = 0.1,
      upliftAmount = 0,
      siteExchangeRate = 0.1,
      upliftExchangeRate = 0.1,
      destinationCurrency = None,
      destinationExchangeRate = 0,
      rateQuoteId = 0,
      rewardsRedeemedPoint = 0,
      rewardsSaving = 0,
      giftcardAmount = 0,
      giftcardAmountUSD = 0,
      exchangeRateOption = 0,
      paymentToken = None
    ),
    bookingFlowType = BookingFlow.Cart,
    commonPayment = None
  )

  val multiProductTokenForDistributePoints = MultiProductBookingToken(
    setupBookingToken = None,
    creationBookingToken = TokenSerializers[MultiProductCreationBookingToken]
      .serialize(multiProductCreationTokenForCart(), Some(1571200505532L), None)
      .toOption,
    None
  )

  private val priceBreakdown = Option(
    PriceBreakdownNode(
      value = Option(
        PriceBreakdownResponse(
          `type` = PriceBreakdownType.TotalPrice,
          amount = TMoney(amount = 2000d, currencyCode = "THB"),
          amountBeforeDiscount = Some(TMoney(2300d, currencyCode = "THB")),
          discountType = Some(1),
          itemPriceInPoints = Some(1000)
        )
      ),
      breakdowns = Some(
        Seq(
          PriceBreakdownNode(
            value = Option(
              PriceBreakdownResponse(
                `type` = PriceBreakdownType.PayAgoda,
                amount = TMoney(amount = 2300d, currencyCode = "THB")
              )
            ),
            breakdowns = None
          ),
          PriceBreakdownNode(
            value = Option(
              PriceBreakdownResponse(
                `type` = PriceBreakdownType.TotalSavings,
                amount = TMoney(amount = 300d, currencyCode = "THB")
              )
            )
          )
        )
      )
    )
  )

  val multiProductTokenWithPriceBreakdown = MultiProductBookingToken(
    setupBookingToken = None,
    creationBookingToken = TokenSerializers[MultiProductCreationBookingToken]
      .serialize(multiProductCreationTokenForCart(priceBreakdownNode = priceBreakdown), Some(1571200505532L), None)
      .toOption,
    None
  )

  val multiProductTokenWithPrecisionAmt = MultiProductBookingToken(
    setupBookingToken = None,
    creationBookingToken = TokenSerializers[MultiProductCreationBookingToken]
      .serialize(multiProductCreationTokenForCart(true), Some(1571200505532L), None)
      .toOption,
    None
  )

  val multiProductTokenForDistributePointsFullRedeem = MultiProductBookingToken(
    setupBookingToken = None,
    creationBookingToken = TokenSerializers[MultiProductCreationBookingToken]
      .serialize(multiProductCreationTokenForCartFullRedeem, Some(1571200505532L), None)
      .toOption,
    None
  )

  val multiBookingTokenStringForDistributePoints = TokenSerializers
    .toJsonString(
      TokenSerializers[MultiProductBookingToken]
        .serialize(multiProductTokenForDistributePoints, Some(1571853316957L), None)
        .get
    )
    .get

  val multiBookingTokenStringWithPriceBreakdown = TokenSerializers
    .toJsonString(
      TokenSerializers[MultiProductBookingToken]
        .serialize(multiProductTokenWithPriceBreakdown, Some(1571853316957L), None)
        .get
    )
    .get

  val multiBookingTokenStringWithPrecisionAmt = TokenSerializers
    .toJsonString(
      TokenSerializers[MultiProductBookingToken]
        .serialize(multiProductTokenWithPrecisionAmt, Some(1571853316957L), None)
        .get
    )
    .get

  val multiBookingTokenStringForDistributePointsFullRedeem = TokenSerializers
    .toJsonString(
      TokenSerializers[MultiProductBookingToken]
        .serialize(multiProductTokenForDistributePointsFullRedeem, Some(1571853316957L), None)
        .get
    )
    .get
}
