package mocks

import com.agoda.bapi.common.model.ChargeOption
import com.agoda.content.models.db.features.Feature
import com.agoda.content.models.db.image.{ImageResponse, ImagesResponse}
import com.agoda.content.models.db.information._
import com.agoda.content.models.db.policy.{AgeRange, HotelAgePolicy, HotelPolicies}
import com.agoda.content.models.db.rateCategories.{RateCategoriesResponse, RateCategoryDetail, RateCategoryResponse}
import com.agoda.content.models.propertycontent.ContentGeoInfo
import com.agoda.core.search.models.CancellationTypes.NonRefundable
import com.agoda.papi.enums.room.CancellationChargeSettingType.{FirstNight, PerNight}
import com.agoda.papi.enums.room.{BenefitTargetTypes, SubChargeType}
import enumerations.AvailabilityTypes.Realtime
import enumerations.SpecialRequestIds.{AdditionalNotes, EarlyCheckIn, HighFloor, LargeBed, LateCheckIn, NonSmokingRoom, SmokingRoom, TwinBed}
import enumerations.{AvailabilityType, AvailabilityTypes, SpecialRequestIds}
import models.pricing.AccountingEntity
import models.pricing.enums.ApplyTypes.{PB, PRPB, PRPN}
import models.pricing.enums.ChargeOptions.Mandatory
import models.pricing.enums.ChargeTypes.{Room, Surcharge, Tax, TaxAndFee}
import models.pricing.enums.CorTypeFlags.NoCOR
import models.pricing.enums.PaxTypes.One
import models.pricing.enums.PaymentModels.Agency
import models.pricing.enums.TaxTypes.SimpleTax
import models.pricing.enums.{ApplyTypes, ChargeTypes, PaymentModel, PaymentModels}
import models.starfruit.PayTypes.Hotel
import models.starfruit.RatePlanStatusTypes.Dispatched
import models.starfruit.SuggestedPrice.Exclusive
import models.starfruit.{AlternativeRoom, BookingExternalLoyaltyPayment, BookingItemBreakdown, BookingMaxOccupancy, BookingPartnerLoyaltyPoint, BookingPayment, BookingPrice, BookingReferencePrice, BookingSummaryPrice, BookingSummaryPrices, BookingSurchargesItem, BookingSurchargesItems, Cancellation, CreditCard, Daily, Display, DisplayBasis, DisplayPrice, DisplaySummary, ExtraInformation, FinProducts, FinanceProductInfo, HotelAggregatedOptions, HotelAggregatedPayment, HotelFeatures, LoyaltyPaymentBoundaries, LoyaltyReasons, PartnerPaxOption, PayAtHotel, PayLater, PayTypes, PointsRange, PrePayment, Price, PseudoCoupon, RateModel, SmartFlexInfo, StayOccupancy, SummaryElement, TaxReceipt}
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import org.mockito.Mockito._
import org.scalatestplus.mockito.MockitoSugar
import transformers._

import scala.collection.mutable

trait PropertyMock extends MockitoSugar {
  val checkIn          = new DateTime("2019-02-02").toDateTimeISO
  val checkOut         = checkIn.plusDays(2).toDateTimeISO
  var propertyId: Long = 10L

  protected def createTopLevelMockProperties(
      propertyId: Long,
      masterRooms: Seq[EnrichedMasterRoom] = Seq.empty,
      booking: Option[transformers.EnrichedBookingItem] = Option(createMockEnrichedBookingItem())
  ) = {
    val properties = mock[Properties]

    val property = createMockProperty(
      propertyId,
      masterRooms,
      booking
    )
    when(properties.property) thenReturn Seq(property)
    properties
  }

  protected def createMockProperty(
      propertyId: Long,
      masterRooms: Seq[EnrichedMasterRoom] = Seq.empty,
      booking: Option[transformers.EnrichedBookingItem] = Option(createMockEnrichedBookingItem()),
      stayOccupancy: Option[StayOccupancy] = None,
      roomSwapping: List[AlternativeRoom] = List.empty,
      getAllRooms: Seq[EnrichedChildRoom] = Seq.empty
  ): Property = {
    val property = mock[Property]
    this.propertyId = propertyId

    when(property.getAllRooms) thenReturn getAllRooms
    when(property.masterRooms) thenReturn masterRooms
    when(property.roomSwapping) thenReturn roomSwapping
    val imageResponse = ImageResponse(
      id = 1L,
      caption = "",
      typeId = 1,
      locations = Map("first" -> "hotel-image-l.png", "second" -> "hotel-image-2.png"),
      providerId = 0,
      group = "",
      captionId = 0
    )
    val imagesResponse = ImagesResponse(
      propertyId = propertyId,
      totalHotelImages = 1,
      mainImage = Some(imageResponse),
      hotelImages = Vector.empty,
      cityImage = None,
      categories = None,
      videos = None,
      matterports = None,
      ugcImages = Some(Vector.empty),
      ugcMosaicImages = Some(Vector.empty)
    )
    val informationResponse = InformationResponse(
      propertyId = propertyId,
      description = None,
      usefulInfoGroups = None,
      notes = null,
      entertainment = None,
      numberOfRoom = None,
      policies = null,
      spokenLanguages = Some(Vector(LanguageInfo(1, "lang1", None), LanguageInfo(2, "lang2", None))),
      messaging = None,
      reception = None,
      blockedNationalities = None,
      contact = Some(
        Contact(
          phone = Some("026737896"),
          email = Some("<EMAIL>"),
          ycsEmail = Some("<EMAIL>"),
          phones = Vector("026737896", "026737897")
        )
      ),
      local = Some(
        Local(
          Some("เซนทารา"),
          Some(LanguageInfo(22, "", Some("th-th"))),
          Some(
            Address(
              Some("ประเทศไทย"),
              Some("กรุงเทพ"),
              Some("กรุงเทพ"),
              Some("ประตูน้ำ"),
              Some("เลขที่  222 ถนนราชปรารภ"),
              Some("เขตราชเทวี"),
              Some("10400"),
              Some(7),
              None,
              None,
              None,
              None,
              None,
              None
            )
          )
        )
      ),
      chainId = None,
      childrenStayFreeTypeId = None,
      isSendSmsForBooking = Some(true),
      isGlobalPartnerServiceBranding = Some(true),
      certificate = None,
      staffVaccinationInfo = None,
      characteristicTopics = None,
      sustainabilityInfo = None,
      nightStayInfo = None,
      dsaComplianceInfo = None,
      companyTraceabilityInfo = None
    )
    when(property.stayOccupancy).thenReturn(stayOccupancy)
    when(property.propertyId).thenReturn(propertyId)
    when(property.info).thenReturn(Some(informationResponse))
    when(property.images).thenReturn(Some(imagesResponse))
    when(property.infoSummary).thenReturn(
      Some(
        InformationSummaryResponse(
          propertyId = propertyId,
          isPreferredPartner = true,
          awardYear = None,
          localeName = "Centara Grand (Local)",
          defaultName = "Centara Grand (English)",
          displayName = "Centara Grand",
          singleRoomNonHotelAccommodation = true,
          accommodationType = "",
          geoInfo = Some(
            GeoInfo(
              latitude = 1d,
              longitude = 2d,
              obfuscatedLat = 1d,
              obfuscatedLong = 2d
            )
          ),
          address = Some(
            Address(
              country = Some("Thailand"),
              city = Some("Bangkok"),
              state = None,
              area = Some("Pratumwan"),
              address1 = Some("1/1"),
              address2 = Some("Central World"),
              postalCode = Some("10400"),
              GmtOffset = Some(7),
              None,
              None,
              None,
              None,
              None,
              None
            )
          ),
          highlightedFeatures = Vector.empty,
          rating = StarRating(value = 5.0, symbol = "star-5", text = "Great", color = Color("Green", "#00FF00")),
          isNonHotelAccommodationType = true,
          hasHostExperience = true,
          propertyType = "",
          remarks = None,
          sellingPoints = Vector.empty,
          generalRoomInformation = None,
          spokenLanguages = Vector.empty,
          awardsAndAccolades = AwardsAndAccolades(
            text = "",
            goldCircleAward = None,
            advanceGuaranteeProgram = None
          ),
          localizations = None,
          chainId = 0,
          hasChannelManager = true,
          contact = Some(
            Contact(
              phone = Some("026737896"),
              email = Some("<EMAIL>"),
              ycsEmail = Some("<EMAIL>"),
              phones = Vector("026737896", "026737897")
            )
          ),
          nhaSummary = None,
          propertyStatus = None,
          requiredGuestContact = false,
          asqType = None,
          asqInfos = Vector.empty
        )
      )
    )
    when(property.localInformation).thenReturn(
      Some(
        LocalInformation(
          propertyId = propertyId,
          nearbyProperties = Some(
            Vector(
              NearbyProperty(
                id = Some("test"),
                categoryName = Some("airports"),
                categorySymbol = Some("airports"),
                places = Some(
                  Vector(
                    Place(
                      name = Some("doun moung"),
                      abbr = Some("DMK"),
                      distance = Some(10d),
                      distanceUnit = None,
                      distanceDisplay = None,
                      distanceLongText = None,
                      distanceShortText = None,
                      duration = None,
                      durationIcon = None,
                      geoInfo = Some(ContentGeoInfo(5d, 5d, 1d, 1d)),
                      images = None
                    )
                  )
                )
              )
            )
          ),
          nearbyPlaces = None,
          topPlaces = None,
          cuisines = None,
          transportation = Some(
            Transportation(
              airports = None
            )
          )
        )
      )
    )
    when(property.countryId).thenReturn(106)
    when(property.cityId).thenReturn(107)
    when(property.searchId).thenReturn("searchId1")
    when(property.nonHotelAccommodation).thenReturn(None)
    when(property.booking).thenReturn(booking)

    val rateCategoriesResponse = RateCategoriesResponse(
      propertyId,
      Vector(
        RateCategoryResponse(
          rateCategoryId = 1,
          localizedRateCategoryName = Some("rcName"),
          detail = Some(
            RateCategoryDetail(
              supplierPlanCode = None,
              content = Some("content"),
              paymentNotice = None,
              notice = None,
              images = None,
              cancellationPolicies = None,
              rateCategoryContentToken = "1,2,3"
            )
          ),
          gender = Some("B")
        )
      ),
      Vector.empty
    )

    when(property.rateCategory).thenReturn(Some(rateCategoriesResponse))
    when(property.isOnlyAlternativeRoom).thenReturn(None)

    property
  }

  protected def createMockMasterRoom(
      typeId: Long,
      name: Option[String],
      imageUrl: String = "",
      englishName: Option[String] = None,
      childRooms: List[EnrichedChildRoom] = List.empty
  ): EnrichedMasterRoom = {
    val masterRoom = mock[EnrichedMasterRoom]
    val imageResponse = ImageResponse(
      id = 1,
      caption = "",
      typeId = 1,
      locations = Map("original" -> imageUrl),
      highResolutionSizes = None,
      providerId = 1,
      group = "",
      groupId = None,
      groupEntityId = None,
      captionId = 1
    )
    when(masterRoom.typeId).thenReturn(typeId)
    when(masterRoom.name).thenReturn(name)
    when(masterRoom.englishName).thenReturn(englishName)

    val images = Vector(imageResponse)
    when(masterRoom.images).thenReturn(images)

    when(masterRoom.childrenRooms) thenReturn childRooms

    masterRoom
  }

  def defaultChildRoom(
      isNoCreditCardEligible: Boolean = false,
      extraBedCount: Int = 2,
      roomUid: Option[String] = None,
      roomIdentifier: Option[String] = None,
      requestedCurrency: Option[String] = None
  ) = {
    val childRoom = mock[EnrichedChildRoom]

    val currency = "THB"

    when(childRoom.uid) thenReturn roomUid
    when(childRoom.roomIdentifiers) thenReturn roomIdentifier
    when(childRoom.typeId) thenReturn Some(2L)
    when(childRoom.supplierId) thenReturn Some(3L)
    when(childRoom.roomLevelBOR) thenReturn Some(mock[EnrichedBOR])
    when(childRoom.morErpAdminId) thenReturn Some(4)
    when(childRoom.ratePlan) thenReturn Some(
      EnrichedRatePlan(id = 5, englishName = "", name = "", localizedName = Some("test"))
    )
    when(childRoom.pointsMax) thenReturn Some(
      EnrichedPointMax(
        channelId = 6,
        partnerLoyaltyProgramId = 7,
        title = "",
        symbol = "",
        description = "",
        isEligible = false,
        point = 8L,
        value = 9L
      )
    )
    when(childRoom.channel) thenReturn Some(EnrichedChannel(id = 7, description = "", symbol = ""))

    val payment = mock[EnrichedPayment]
    when(payment.noCreditCard) thenReturn EnrichedNoCreditCard(
      isEligible = isNoCreditCardEligible,
      title = "",
      description = "",
      symbol = ""
    )
    when(payment.paymentModel) thenReturn PaymentModels.Merchant
    when(childRoom.payment) thenReturn Some(payment)

    when(childRoom.dmcPolicyText) thenReturn Some(
      EnrichedDMCPolicyText(
        externalData = "dmc policy text",
        blockId = None,
        cancellationText = ""
      )
    )

    val mockPricing = mock[EnrichedPricing]

    when(mockPricing.charges) thenReturn Seq(
      EnrichedCharge(
        name = "",
        description = "",
        chargeType = ChargeTypes.ExtraBed,
        breakdown = Seq(
          EnrichedChargeBreakdown(
            name = "",
            description = "",
            chargeType = ChargeTypes.ExtraBed,
            id = 2,
            pay = PayTypes.Agoda,
            basis = ApplyTypes.PB,
            quantity = extraBedCount,
            percentage = 9d,
            total = null,
            price = null,
            daily = Nil,
            isInclude = true,
            display = None,
            protoTypeID = 2,
            netDisplay = None,
            netPrice = null
          )
        ),
        totalToHotel = null,
        totalToAgoda = null,
        total = null
      )
    )
    when(childRoom.pricing) thenReturn Map(currency -> mockPricing)
    when(childRoom.originalRoomDetail) thenReturn None
    when(childRoom.campaignPromotions).thenReturn(None)
    when(childRoom.requestedCurrencyCode) thenReturn requestedCurrency
    when(childRoom.occupancyMessages) thenReturn Option(Seq(OccupancyMessage(1, "Price for 1 kid")))
    childRoom
  }

  def baseEbeBooking(hotels: List[EnrichedEBEHotel], ebeCreditCard: EnrichedEBECreditCard) = {
    val ebeBooking = mock[EnrichedEBEBooking]
    when(ebeBooking.discount).thenReturn(None)
    when(ebeBooking.hotel).thenReturn(hotels)
    when(ebeBooking.creditCard) thenReturn ebeCreditCard
    ebeBooking
  }

  def createMockEnrichedBookingItem(
      destinationCurrency: Option[String] = Some("USD"),
      roomUidOpt: Option[String] = None,
      fullyAuthDate: Option[DateTime] = Some(new DateTime("2019-06-03T00:00+07:00")),
      fullyChargeDate: Option[DateTime] = Some(new DateTime("2019-06-04T00:00+07:00")),
      availabilityType: AvailabilityType = AvailabilityTypes.Realtime,
      elapiErrorCode: Option[Int] = None
  ): transformers.EnrichedBookingItem = {
    val accountingEntity = mock[models.pricing.AccountingEntity]
    when(accountingEntity.merchantOfRecord).thenReturn(1)
    when(accountingEntity.rateContract).thenReturn(2)
    when(accountingEntity.revenue).thenReturn(3)
    when(accountingEntity.argument).thenReturn("argument")

    val room = mock[transformers.EnrichedBookingRoom]
    roomUidOpt match {
      case Some(roomUid) =>
        when(room.uid).thenReturn(roomUid)
      case None =>
        when(room.uid).thenReturn("room-uid")
    }
    when(room.accountingEntity).thenReturn(Option(accountingEntity))
    when(room.availabilityType).thenReturn(availabilityType)
    when(room.hotelId).thenReturn(propertyId)
    when(room.specialRequestOptions).thenReturn(
      Seq(SpecialRequestIds.AdditionalNotes, SpecialRequestIds.NonSmokingRoom, SpecialRequestIds.AirportTransfer)
    )

    val capacity = mock[transformers.EnrichedCapacity]
    when(capacity.occupancy).thenReturn(2)

    when(room.capacity).thenReturn(capacity)
    when(room.isPrepay).thenReturn(true)
    when(room.lineItemId).thenReturn(Option(111))
    when(room.noOfAdults).thenReturn(2)
    when(room.noOfChildren).thenReturn(1)
    when(room.noOfExtrabeds).thenReturn(2)
    when(room.numberOfRoom).thenReturn(2)

    val cancellation = mock[transformers.EnrichedBookingCancellation]
    when(cancellation.code).thenReturn("FCD100")
    when(cancellation.noShowPolicy).thenReturn("???")
    when(cancellation.defaultNoShowPolicy).thenReturn("Ok")
    when(cancellation.policies).thenReturn(Seq("policy 1", "policy 2"))
    when(cancellation.defaultPolicies).thenReturn(Seq.empty)

    when(room.cancellation).thenReturn(cancellation)
    when(room.cancellationChargeType).thenReturn(Some(FirstNight))
    when(room.ycsRatePlanId).thenReturn(301L)
    when(room.giftCardEarning)
      .thenReturn(Some(EnrichedBookingGiftCardEarning(32.15, "3dd3fa3f-1b2f-44cf-bc8c-69861fc9f78c", 1, 30)))
    when(room.partnerLoyaltyPoint)
      .thenReturn(Some(BookingPartnerLoyaltyPoint("USD", 10.00, 10.00, 12345, 10.00, 10.00, 45)))
    when(room.cashbackEarning)
      .thenReturn(Some(EnrichedBookingCashbackEarning(32.15, 2.0, "3dd3fa3f-1b2f-44cf-bc8c-69861fc9f78c", 60, 120)))
    when(room.promotionsBreakdown).thenReturn(None)
    when(room.channelDiscountSummary).thenReturn(None)
    when(room.finProductInfo).thenReturn(
      Some(
        FinanceProductInfo(
          products = FinProducts(
            smartFlex = Some(
              SmartFlexInfo(
                originalCxlCode = "10D100P_100P",
                processReplacement = true
              )
            )
          )
        )
      )
    )

    val charge = BookingItemBreakdown(
      chargeDate = Some(DateTime.now().plusDays(7)),
      exchangeRate = 31.3,
      itemId = 111L,
      localAmount = 2000.1,
      usdAmount = 100.01,
      reqAmount = 0.0,
      localCurrency = "THB",
      quantity = 4,
      surchargeId = Some(2),
      taxFeeId = Some(6),
      typeId = 7L,
      subTypeId = None,
      roomNo = 1,
      option = Mandatory,
      taxProtoTypeId = 8
    )

    val pricing = mock[models.starfruit.BookingPrice]
    when(pricing.charge).thenReturn(List(charge))

    when(room.pricing).thenReturn(pricing)

    val bookingPayment = mock[BookingPayment]
    when(bookingPayment.exchangeRate).thenReturn(31.3)
    when(bookingPayment.paymentCurrency).thenReturn(Some("USD"))
    when(room.exchange).thenReturn(bookingPayment)

    val rateCategory = mock[transformers.EnrichedBookingRateCategory]
    when(rateCategory.id).thenReturn(400)
    when(rateCategory.Code).thenReturn("RateCategory")
    when(rateCategory.applyTo).thenReturn("RateCategory.applyTo")
    when(rateCategory.Amount).thenReturn(1002.3)
    when(rateCategory.stayPackageType).thenReturn(Some(1))
    when(rateCategory.rateCategoryContentToken).thenReturn(Some("1,2,3"))
    when(rateCategory.inventoryType).thenReturn(Some(1))

    when(room.rateCategory).thenReturn(rateCategory)
    when(room.borSupplier).thenReturn(Option(mock[transformers.BORSupplier]))
    when(room.isNotCcRequired).thenReturn(false)
    when(room.dmcId).thenReturn(Option(234L))
    when(room.dmcSpecificData).thenReturn("dmc data")
    when(room.excluded).thenReturn("excluded")
    when(room.included).thenReturn("included")
    when(room.roomTypeId).thenReturn(107L)
    when(room.roomTypeName).thenReturn("NHA Single room")
    val sellInfoMock = mock[transformers.EnrichedBookingSellInfo]
    when(sellInfoMock.searchId).thenReturn("")
    when(sellInfoMock.fireDrillContract).thenReturn(None)
    when(room.sellInfo).thenReturn(sellInfoMock)
    when(room.promotion)
      .thenReturn(Option(mock[transformers.EnrichedBookingYCSPromotion]))
    when(room.displayCurrency)
      .thenReturn("") // dragon fruit hard codes this value to empty string

    val benefit = mock[transformers.EnrichedBenefit]
    when(benefit.value).thenReturn(33.2)
    when(benefit.id).thenReturn(88)
    when(benefit.remark).thenReturn("benefit Remark")
    when(benefit.structuredBenefitInfo).thenReturn(None)
    when(benefit.description).thenReturn("")
    when(benefit.targetType).thenReturn(BenefitTargetTypes.Regular)

    when(room.benefit).thenReturn(Seq(benefit))
    when(room.rateModel).thenReturn(models.starfruit.RateModel(201))
    when(room.taxSurchargeInfo).thenReturn("tax surcharge info")
    when(room.paymentModels)
      .thenReturn(models.pricing.enums.PaymentModels.Agency)
    when(room.npclnChannel).thenReturn(Some(1))
    when(room.pointMultiply).thenReturn(Some(1.1))

    val hotel = mock[transformers.EnrichedEBEHotel]
    when(hotel.checkIn).thenReturn(checkIn)
    when(hotel.checkOut).thenReturn(checkOut)
    when(hotel.numberOfRooms).thenReturn(2)
    when(hotel.numberOfAdults).thenReturn(2)
    when(hotel.numberOfChildren).thenReturn(1)
    when(hotel.room).thenReturn(List(room))

    val ebeCreditCard = mock[EnrichedEBECreditCard]
    val ebeBooking    = baseEbeBooking(List(hotel), ebeCreditCard)

    val paymentAmount = EnrichedEBEPayment(
      exchangeRate = 2.0,
      giftCardAmount = 3.0,
      giftCardAmountInUSD = 4.0,
      paymentAmount = 5.0,
      paymentAmountInUSD = 6.0,
      paymentCurrency = "THB",
      rewardsRedeemedPoint = 8,
      rewardsSaving = 9.0,
      rewardsSavingInUSD = 10.0,
      siteExchangeRate = 11.0,
      upliftAmount = 12.0,
      upliftExchangeRate = 13.0,
      exchangeRateOption = Some(14),
      destinationCurrency = destinationCurrency,
      destinationExchangeRate = 15.0,
      rateQuoteID = 16,
      externalLoyalty = Some(
        BookingExternalLoyaltyPayment(
          points = 1000,
          pointsToEarn = 0,
          pointsAmountInUSD = 1000,
          loyaltyToken = "loyaltyToken",
          partnerClaimToken = Some("partnerToken"),
          errorCode = elapiErrorCode,
          pointsRange = Some(PointsRange(maxPointsApplicable = Some(100.0), minPointsApplicable = Some(0.0)))
        )
      )
    )
    when(ebeCreditCard.payment) thenReturn paymentAmount
    when(ebeCreditCard.fullyAuthDate) thenReturn fullyAuthDate
    when(ebeCreditCard.fullyChargeDate) thenReturn fullyChargeDate
    when(ebeCreditCard.chargeOption) thenReturn Some(ChargeOption.PayNow.id)

    val bookingMock = mock[transformers.EnrichedBookingItem]
    when(bookingMock.booking).thenReturn(List(ebeBooking))
    when(bookingMock.rooms).thenReturn(List(room))
    bookingMock
  }

  def createMockPropertyWithRoomUid(
      propertyId: Long,
      uid: Option[String] = None,
      roomIdentifier: Option[String] = None,
      paymentModels: PaymentModel = PaymentModels.Merchant,
      isPayLater: Boolean = false,
      isPayAtHotel: Boolean = false,
      loyaltyResponse: Option[LoyaltyPaymentBoundaries] = Some(
        LoyaltyPaymentBoundaries(0, 100, 99.5, Some(0.5), Some(false), LoyaltyReasons.Success)
      ),
      isOnlyAlternativeRoom: Option[Boolean] = Some(false),
      cashbackRedemptionBoundaries: Option[LoyaltyPaymentBoundaries] = Some(
        LoyaltyPaymentBoundaries(5, 5, 5, Some(0.5), Some(false), LoyaltyReasons.Success)
      ),
      elapiErrorCode: Option[Int] = None,
      priceAdjustmentId: Option[Long] = None
  ): Property = {
    val property = mock[Property]
    when(property.propertyId).thenReturn(propertyId)
    val masterRoom = mock[EnrichedMasterRoom]
    val childRoom  = mock[EnrichedChildRoom]
    when(childRoom.priceAdjustmentId).thenReturn(priceAdjustmentId)
    val currency = "USD"
    val pricing  = mock[EnrichedPricing]

    val chargeTotal        = mock[DisplayPrice]
    val mockDiscountCharge = mock[EnrichedCharge]
    val price              = mock[Price]
    val mockPerBook        = mock[SummaryElement]
    val mockPricingSummary = mock[DisplaySummary]
    val payment            = mock[EnrichedPayment]

    val rateCategories = mock[RateCategoriesResponse]
    val rateCategory   = mock[RateCategoryResponse]

    when(price.inc).thenReturn(20)
    when(mockDiscountCharge.chargeType).thenReturn(ChargeTypes.CampaignDiscount)
    when(mockDiscountCharge.total).thenReturn(price)
    when(chargeTotal.exclusive).thenReturn(200)
    when(chargeTotal.allInclusive).thenReturn(220)
    when(mockPerBook.chargeTotal).thenReturn(chargeTotal)
    when(mockPricingSummary.perBook).thenReturn(mockPerBook)

    when(pricing.charges).thenReturn(Seq(mockDiscountCharge))
    when(pricing.displaySummary).thenReturn(mockPricingSummary)
    when(childRoom.uid).thenReturn(uid)
    when(childRoom.roomIdentifiers).thenReturn(roomIdentifier)
    when(masterRoom.childrenRooms).thenReturn(List(childRoom))
    when(property.masterRooms).thenReturn(List(masterRoom))
    when(childRoom.pricing).thenReturn(Map(currency -> pricing))
    when(childRoom.typeId).thenReturn(Some(101L))
    when(childRoom.roomLevelBOR).thenReturn(None)
    when(childRoom.payment).thenReturn(Some(payment))
    when(childRoom.morErpAdminId).thenReturn(None)
    when(childRoom.ratePlan).thenReturn(Some(EnrichedRatePlan(1, "", "")))
    when(childRoom.pointsMax)
      .thenReturn(Some(EnrichedPointMax(1, 1, "", "", "", true, 1L, 10)))
    when(childRoom.channel).thenReturn(Some(EnrichedChannel(10L, "", "")))
    when(childRoom.dmcPolicyText)
      .thenReturn(Some(EnrichedDMCPolicyText("", None, "C100N")))
    when(childRoom.supplierId).thenReturn(Some(112233L))
    when(childRoom.roomLevelBOR).thenReturn(None)
    when(childRoom.benefits).thenReturn(Seq.empty)
    when(childRoom.requestedCurrencyCode).thenReturn(None)
    when(payment.payLater).thenReturn(EnrichedPayLater(isPayLater, DateTime.now(), DateTime.now(), "", "", ""))
    when(payment.payAtHotel).thenReturn(EnrichedPayAtHotel(isPayAtHotel, "", "", ""))
    when(payment.paymentModel).thenReturn(paymentModels)
    when(payment.noCreditCard).thenReturn(EnrichedNoCreditCard(false, "", "", ""))

    when(childRoom.loyaltyResponse).thenReturn(loyaltyResponse)
    when(childRoom.cashbackRedemptionBoundaries).thenReturn(cashbackRedemptionBoundaries)
    when(childRoom.originalRoomDetail).thenReturn(None)
    when(childRoom.campaignPromotions).thenReturn(None)

    when(rateCategory.gender).thenReturn(Some("B"))
    when(rateCategories.rateCategories).thenReturn(Vector(rateCategory))

    val bookingItem = createMockEnrichedBookingItem(Some(currency), uid, elapiErrorCode = elapiErrorCode)
    when(property.booking).thenReturn(Some(bookingItem))
    when(property.rooms).thenReturn(Seq(childRoom))
    when(property.isOnlyAlternativeRoom).thenReturn(isOnlyAlternativeRoom)
    when(property.roomSwapping).thenReturn(List.empty)
    when(property.getAllRooms).thenReturn(Seq(childRoom))
    when(property.rateCategory).thenReturn(Some(rateCategories))

    property
  }

  def createMockPropertyWithUpsellWithRoomUid(
      propertyId: Long,
      uid: Option[String] = None,
      roomIdentifier: Option[String] = None,
      upsellPropertyId: Long,
      upsellUid: Option[String] = None,
      upsellRoomIdentifier: Option[String] = None,
      paymentModels: PaymentModel = PaymentModels.Merchant,
      isPayLater: Boolean = false,
      isPayAtHotel: Boolean = false,
      loyaltyResponse: Option[LoyaltyPaymentBoundaries] = Some(
        LoyaltyPaymentBoundaries(0, 100, 99.5, Some(0.5), Some(false), LoyaltyReasons.Success)
      ),
      cashbackRedemptionBoundaries: Option[LoyaltyPaymentBoundaries] = Some(
        LoyaltyPaymentBoundaries(5, 5, 5, Some(0.5), Some(false), LoyaltyReasons.Success)
      )
  ): Property = {
    val property = mock[Property]
    when(property.propertyId).thenReturn(propertyId)
    val masterRoom      = mock[EnrichedMasterRoom]
    val childRoom       = mock[EnrichedChildRoom]
    val upsellChildRoom = mock[EnrichedChildRoom]
    val currency        = "USD"
    val pricing         = mock[EnrichedPricing]

    val chargeTotal        = mock[DisplayPrice]
    val mockDiscountCharge = mock[EnrichedCharge]
    val price              = mock[Price]
    val mockPerBook        = mock[SummaryElement]
    val mockPricingSummary = mock[DisplaySummary]
    val payment            = mock[EnrichedPayment]

    val rateCategories = mock[RateCategoriesResponse]
    val rateCategory   = mock[RateCategoryResponse]

    when(price.inc).thenReturn(20)
    when(mockDiscountCharge.chargeType).thenReturn(ChargeTypes.CampaignDiscount)
    when(mockDiscountCharge.total).thenReturn(price)
    when(chargeTotal.exclusive).thenReturn(200)
    when(chargeTotal.allInclusive).thenReturn(220)
    when(mockPerBook.chargeTotal).thenReturn(chargeTotal)
    when(mockPricingSummary.perBook).thenReturn(mockPerBook)

    when(pricing.charges).thenReturn(Seq(mockDiscountCharge))
    when(pricing.displaySummary).thenReturn(mockPricingSummary)
    when(childRoom.uid).thenReturn(uid)
    when(childRoom.roomIdentifiers).thenReturn(roomIdentifier)
    when(upsellChildRoom.uid).thenReturn(uid)
    when(upsellChildRoom.roomIdentifiers).thenReturn(upsellRoomIdentifier)
    when(masterRoom.childrenRooms).thenReturn(List(childRoom, upsellChildRoom))
    when(property.masterRooms).thenReturn(List(masterRoom))
    when(childRoom.pricing).thenReturn(Map(currency -> pricing))
    when(upsellChildRoom.pricing).thenReturn(Map(currency -> pricing))
    when(childRoom.typeId).thenReturn(Some(101L))
    when(upsellChildRoom.typeId).thenReturn(Some(101L))
    when(childRoom.roomLevelBOR).thenReturn(None)
    when(upsellChildRoom.roomLevelBOR).thenReturn(None)
    when(childRoom.requestedCurrencyCode).thenReturn(None)
    when(childRoom.payment).thenReturn(Some(payment))
    when(upsellChildRoom.requestedCurrencyCode).thenReturn(None)
    when(upsellChildRoom.payment).thenReturn(Some(payment))
    when(childRoom.morErpAdminId).thenReturn(None)
    when(upsellChildRoom.morErpAdminId).thenReturn(None)
    when(childRoom.ratePlan).thenReturn(Some(EnrichedRatePlan(1, "", "")))
    when(upsellChildRoom.ratePlan).thenReturn(Some(EnrichedRatePlan(1, "", "")))
    when(childRoom.pointsMax)
      .thenReturn(Some(EnrichedPointMax(1, 1, "", "", "", true, 1L, 10)))
    when(upsellChildRoom.pointsMax)
      .thenReturn(Some(EnrichedPointMax(1, 1, "", "", "", true, 1L, 10)))
    when(childRoom.channel).thenReturn(Some(EnrichedChannel(10L, "", "")))
    when(upsellChildRoom.channel).thenReturn(Some(EnrichedChannel(10L, "", "")))
    when(childRoom.dmcPolicyText)
      .thenReturn(Some(EnrichedDMCPolicyText("", None, "C100N")))
    when(upsellChildRoom.dmcPolicyText)
      .thenReturn(Some(EnrichedDMCPolicyText("", None, "C100N")))
    when(childRoom.supplierId).thenReturn(Some(112233L))
    when(upsellChildRoom.supplierId).thenReturn(Some(112233L))
    when(childRoom.roomLevelBOR).thenReturn(None)
    when(childRoom.benefits).thenReturn(Seq.empty)
    when(upsellChildRoom.benefits).thenReturn(Seq.empty)
    when(payment.payLater).thenReturn(EnrichedPayLater(isPayLater, DateTime.now(), DateTime.now(), "", "", ""))
    when(payment.payAtHotel).thenReturn(EnrichedPayAtHotel(isPayAtHotel, "", "", ""))
    when(payment.paymentModel).thenReturn(paymentModels)
    when(payment.noCreditCard).thenReturn(EnrichedNoCreditCard(false, "", "", ""))

    when(childRoom.loyaltyResponse).thenReturn(loyaltyResponse)
    when(childRoom.cashbackRedemptionBoundaries).thenReturn(cashbackRedemptionBoundaries)
    when(childRoom.originalRoomDetail).thenReturn(None)
    when(childRoom.campaignPromotions).thenReturn(None)
    when(upsellChildRoom.loyaltyResponse).thenReturn(loyaltyResponse)
    when(upsellChildRoom.cashbackRedemptionBoundaries).thenReturn(cashbackRedemptionBoundaries)
    when(upsellChildRoom.originalRoomDetail).thenReturn(None)
    when(upsellChildRoom.campaignPromotions).thenReturn(None)

    when(rateCategory.gender).thenReturn(Some("B"))
    when(rateCategories.rateCategories).thenReturn(Vector(rateCategory))

    val bookingItem = createMockEnrichedBookingItem(Some(currency), uid)
    when(property.booking).thenReturn(Some(bookingItem))
    when(property.rooms).thenReturn(Seq(childRoom, upsellChildRoom))
    when(property.isOnlyAlternativeRoom).thenReturn(None)
    when(property.roomSwapping).thenReturn(List.empty)
    when(property.rateCategory).thenReturn(Some(rateCategories))
    property
  }

  val formatter = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZZ")
  val richPropertyResponse = Property(
    searchId = "3f74acf8-d557-4733-9ef1-669d2a09d579",
    propertyId = 114558,
    cityId = 14562,
    productType = List(-1, 1),
    activeHotelsInCity = Some(95),
    countryId = 153,
    areaId = 18172,
    cheapestRoom = Some(
      EnrichedCheapestRoom(
        description = "Most popular choice!",
        dmcRoomId = "c3e10b7d-850f-5bd7-f92e-a462e52e6bea",
        uid = "08064e41-955b-7e44-ff89-a7f5dc349eb4"
      )
    ),
    needOccupancySearch = false,
    isReady = true,
    supplierId = 3038,
    info = Some(
      InformationResponse(
        propertyId = 114558,
        description = Some(
          Description(
            long = Some(
              "Included in all rooms are bathtub, air conditioning, non smoking rooms.\r\nThe hotel boasts shops, business center, 24hr room service as part of its superior facilities and services.\r\nGuests will find this service-oriented hotel with superb facilities and amenities provides excellent value.\r\nPlease enter your dates on our secure online booking form to make a reservation at Hotel Icare Toulouse.\r\n"
            ),
            short = Some("Included in all rooms are bathtub, air conditioning, non smoking rooms.")
          )
        ),
        usefulInfoGroups = None,
        notes = Some(
          Notes(
            publicNotes = None,
            importantNotes = None,
            transportationNotes = None,
            criticalNotes = None
          )
        ),
        entertainment = None,
        numberOfRoom = None,
        policies = Some(
          HotelPolicies(
            children = None,
            minAge = None,
            adult = None,
            extraBed = Some(
              Vector(
                "Extra beds are dependent on the room you choose. Please check the individual room capacity for more details."
              )
            ),
            additional = Some(
              Vector("When booking more than 5 rooms, different policies and additional supplements may apply.")
            ),
            hotelAgePolicy = Some(
              HotelAgePolicy(
                infantAges = Some(
                  AgeRange(
                    min = Some(0),
                    max = Some(0)
                  )
                ),
                childAges = Some(
                  AgeRange(
                    min = Some(0),
                    max = Some(0)
                  )
                ),
                minGuestAge = None,
                isChildStayFree = Some(false)
              )
            )
          )
        ),
        spokenLanguages = Some(
          Vector(
            LanguageInfo(
              id = 2,
              localeName = "French",
              locale = None
            )
          )
        ),
        messaging = Some(
          Messaging(
            hostName = None,
            isAllowedPreBooking = None,
            isAllowedPostBooking = None,
            isAllowedWithBooking = None,
            isAllowedInHouseFeedback = None,
            isAllowedInHouseRequest = None,
            isAllowedMessagingPlatform = None,
            isShowCustomerInfoInVoucher = None,
            emailDisplaySetting = None,
            responsiveRate = None,
            totalNumberOfInquiries = None,
            totalResponseRateInLast3Months = None,
            avgResponseTimeMin = None
          )
        ),
        reception = Some(
          Reception(
            isMystay = false
          )
        ),
        blockedNationalities = None,
        contact = Some(
          Contact(
            phone = Some(""),
            email = None,
            ycsEmail = Some("<EMAIL>"),
            phones = Vector("1", "2")
          )
        ),
        local = Some(
          Local(
            name = Some("Hôtel Icare"),
            language = Some(
              LanguageInfo(
                id = 2,
                localeName = "Français",
                locale = Some("fr-fr")
              )
            ),
            address = Some(
              Address(
                country = Some("France"),
                city = Some("Toulouse"),
                state = Some("Midi-Pyrénées"),
                area = Some("Centre-ville de Toulouse"),
                address1 = None,
                address2 = None,
                postalCode = None,
                GmtOffset = Some(2),
                utcOffset = None,
                countryId = Some(7),
                cityId = Some(111),
                areaId = Some(1),
                stateId = None
              )
            )
          )
        ),
        chainId = Some(0),
        childrenStayFreeTypeId = None,
        formerlyName = Some(""),
        localizations = None,
        isExtraBedAvailable = Some(false),
        contractInfo = None,
        breakfastInfo = Some(
          BreakfastInfo(
            cuisines = None
          )
        ),
        isAgodaVerified = Some(false),
        restaurantOnSite = None,
        coffeeInfo = None,
        certificate = None,
        staffVaccinationInfo = None,
        characteristicTopics = None,
        sustainabilityInfo = None,
        nightStayInfo = None,
        dsaComplianceInfo = None,
        companyTraceabilityInfo = None
      )
    ),
    features = None,
    reviews = None,
    images = Some(
      ImagesResponse(
        propertyId = 114558,
        totalHotelImages = 0,
        mainImage = Some(
          ImageResponse(
            id = 4565997,
            caption = "Exterior view",
            typeId = 6,
            locations = Map(
              "original" -> "http://qa.pix.agoda.local/hotelImages/114/114558/114558_1111241135004565997.jpg"
            ),
            providerId = 3038,
            group = "Property views",
            groupId = Some("property"),
            captionId = 13,
            localizations = None
          )
        ),
        ugcImages = Some(Vector()),
        hotelImages = Vector(),
        cityImage = Some(
          ImageResponse(
            id = 1025,
            caption = "",
            typeId = 8,
            locations = Map(
              "original" -> "http://qa.pix.agoda.local/city/14562/14562-7x3.jpg?s=762x328"
            ),
            providerId = 332,
            group = "Other",
            groupId = Some("other"),
            captionId = 0,
            localizations = None
          )
        ),
        categories = Some(Vector()),
        videos = Some(Vector()),
        matterports = None,
        ugcMosaicImages = Some(Vector.empty)
      )
    ),
    highlights = None,
    nonHotelAccommodation = None,
    isDisplayAsAmount = Some(false),
    suggestedBasis = Some(PRPN),
    suggestedPrice = Some(Exclusive),
    recommendations = List(),
    personalizedRecommendations = List(),
    pastBookingsRecommendations = List(),
    recommendationHeader = None,
    tooltip = PropertyTooltip(
      taxAndServiceFeeText = Some(
        "Change your price display to include or not include taxes and service fees by clicking on the currency icon at the top of the page."
      ),
      extraBedTexts = Some(
        List(
          "<strong>Extra bed per room per night = [extrabedPrice]</strong><br>(charge applied when making your payment)",
          "",
          "Children aged 0 and over must use an extra bed."
        )
      )
    ),
    requestedChannelIds = List(1, 2, 7),
    availableFilters = Some(
      PropertyFilters(
        title = "Filter room options by:",
        tags = List(
          FilterTag(
            id = "no-cc-required",
            title = "Book without credit card",
            symbol = "payment-option-no-credit-card",
            subTags = None
          ),
          FilterTag(
            id = "pay-at-the-place",
            title = "Pay at the hotel",
            symbol = "pay-at-the-place",
            subTags = None
          )
        )
      )
    ),
    enableAPS = false,
    masterRooms = List(
      EnrichedMasterRoom(
        maxOccupancy = 2,
        maxExtraBeds = 0,
        typeId = 373685,
        englishName = Some("Standard Double Room with Shower"),
        name = Some("Standard Double Room with Shower"),
        images = Vector(
          ImageResponse(
            id = 5276160,
            caption = "",
            typeId = 7,
            locations = Map(
              "original" -> "http://qa.pix.agoda.local/hotelImages/114/114558/114558_1112211012005276160.jpg"
            ),
            providerId = 3038,
            group = "Rooms",
            groupId = Some("room"),
            captionId = 0,
            localizations = None
          )
        ),
        facilities = Vector(),
        bedConfiguration2 = None,
        childrenRooms = List(
          EnrichedChildRoom(
            uid = Some("08064e41-955b-7e44-ff89-a7f5dc349eb4"),
            typeId = Some(373685),
            dmcRoomId = Some("c3e10b7d-850f-5bd7-f92e-a462e52e6bea"),
            rateModel = Some(2),
            ratePlan = Some(
              EnrichedRatePlan(
                id = 0,
                englishName = "",
                name = "",
                promotionTypeId = None,
                promotionTypeName = None,
                localizedName = None
              )
            ),
            benefits = List(),
            filterTags = List("pay-at-the-place", "no-cc-required"),
            isBreakfastIncluded = Some(false),
            channel = Some(
              EnrichedChannel(
                id = 1,
                description = "",
                symbol = ""
              )
            ),
            supplierId = Some(3038),
            subSupplierId = Some(3038),
            availableRooms = Some(999),
            capacity = Some(
              EnrichedCapacity(
                adults = 2,
                children = 0,
                extraBed = 0,
                occupancy = 2,
                symbol = "max-occupancy-override",
                maxExtraBed = 0,
                numberOfGuestsWithoutRoom = 0,
                description = Some("2 adults"),
                allowedFreeChildrenAndInfants = Some(0)
              )
            ),
            localCurrencyCode = Some("EUR"),
            requestedCurrencyCode = Some("USD"),
            pricing = Map(
              "EUR" -> EnrichedPricing(
                DisplayBasis(DisplayPrice(100.0, 200.0), DisplayPrice(50.0, 100.0), DisplayPrice(50.0, 100.0)),
                DisplayBasis(DisplayPrice(0.0, 0.0), DisplayPrice(0.0, 0.0), DisplayPrice(0.0, 0.0)),
                List(),
                List(
                  EnrichedCharge(
                    "Extra charges",
                    "Extra charges [AMOUNT]",
                    Surcharge,
                    List(
                      EnrichedChargeBreakdown(
                        "City Tax",
                        "City Tax (Pay at the property) [AMOUNT]",
                        Surcharge,
                        228,
                        Hotel,
                        PB,
                        1,
                        0.0,
                        Price(40.0, 40.0),
                        Price(40.0, 40.0),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PB,
                            1,
                            Price(40.0, 40.0),
                            None,
                            Price(40.0, 40.0),
                            None,
                            SubChargeType.None
                          )
                        ),
                        false,
                        Some(Display(PB, 40.0, 40.0)),
                        0,
                        Some(Display(PB, 0.0, 0.0)),
                        Price(0.0, 0.0)
                      ),
                      EnrichedChargeBreakdown(
                        "Speed Boat Transfer",
                        "Speed Boat Transfer [AMOUNT]",
                        Surcharge,
                        51,
                        Hotel,
                        PB,
                        1,
                        0.0,
                        Price(30.0, 30.0),
                        Price(30.0, 30.0),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PB,
                            1,
                            Price(30.0, 30.0),
                            None,
                            Price(30.0, 30.0),
                            None,
                            SubChargeType.None
                          )
                        ),
                        true,
                        Some(Display(PB, 30.0, 30.0)),
                        0,
                        Some(Display(PB, 0.0, 0.0)),
                        Price(0.0, 0.0)
                      )
                    ),
                    Price(70.0, 70.0),
                    Price(0.0, 0.0),
                    Price(70.0, 70.0)
                  ),
                  EnrichedCharge(
                    "1 room X 2 nights",
                    "1 room X 2 nights [AMOUNT]",
                    Room,
                    List(
                      EnrichedChargeBreakdown(
                        "",
                        "",
                        Room,
                        -1,
                        Hotel,
                        PRPB,
                        1,
                        0.0,
                        Price(100.0, 110.0),
                        Price(100.0, 110.0),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PRPN,
                            1,
                            Price(50.0, 55.0),
                            None,
                            Price(41.7, 46.7),
                            None,
                            SubChargeType.None
                          ),
                          Daily(
                            formatter.parseDateTime("2019-12-13T00:00:00.000+07:00"),
                            0.0,
                            PRPN,
                            1,
                            Price(50.0, 55.0),
                            None,
                            Price(41.7, 46.7),
                            None,
                            SubChargeType.None
                          )
                        ),
                        true,
                        Some(Display(PRPN, 50.0, 55.0)),
                        0,
                        Some(Display(PRPN, 41.7, 46.7)),
                        Price(83.4, 93.4)
                      )
                    ),
                    Price(100.0, 110.0),
                    Price(0.0, 0.0),
                    Price(100.0, 110.0)
                  ),
                  EnrichedCharge(
                    "Hotel tax and service fees ",
                    "Hotel tax and service fees  [AMOUNT]",
                    TaxAndFee,
                    List(
                      EnrichedChargeBreakdown(
                        "Tax",
                        "Unknown [AMOUNT]",
                        Tax,
                        253,
                        Hotel,
                        PRPB,
                        1,
                        0.0,
                        Price(10.0, 10.0),
                        Price(10.0, 10.0),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PRPB,
                            1,
                            Price(5.0, 5.0),
                            None,
                            Price(0.0, 0.0),
                            None,
                            SubChargeType.None
                          ),
                          Daily(
                            formatter.parseDateTime("2019-12-13T00:00:00.000+07:00"),
                            0.0,
                            PRPB,
                            1,
                            Price(5.0, 5.0),
                            None,
                            Price(0.0, 0.0),
                            None,
                            SubChargeType.None
                          )
                        ),
                        true,
                        Some(Display(PRPN, 5.0, 5.0)),
                        0,
                        Some(Display(PRPN, 0.0, 0.0)),
                        Price(0.0, 0.0)
                      ),
                      EnrichedChargeBreakdown(
                        "Tax",
                        "Unknown (Pay at the property) [AMOUNT]",
                        Tax,
                        1794,
                        Hotel,
                        PRPB,
                        1,
                        0.0,
                        Price(20.0, 20.0),
                        Price(20.0, 20.0),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PRPB,
                            1,
                            Price(10.0, 10.0),
                            None,
                            Price(0.0, 0.0),
                            None,
                            SubChargeType.None
                          ),
                          Daily(
                            formatter.parseDateTime("2019-12-13T00:00:00.000+07:00"),
                            0.0,
                            PRPB,
                            1,
                            Price(10.0, 10.0),
                            None,
                            Price(0.0, 0.0),
                            None,
                            SubChargeType.None
                          )
                        ),
                        false,
                        Some(Display(PRPN, 10.0, 10.0)),
                        0,
                        Some(Display(PRPN, 0.0, 0.0)),
                        Price(0.0, 0.0)
                      )
                    ),
                    Price(30.0, 30.0),
                    Price(0.0, 0.0),
                    Price(30.0, 30.0)
                  )
                ),
                Some(ExtraInformation(5.0, 5.0, 30.0, 30.0, 18.49376114081997, 100.0, 100.0, 100.0)),
                DisplaySummary(
                  SummaryElement(
                    DisplayPrice(100.0, 200.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(100.0, 200.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(100.0, 200.0),
                    Some(DisplayPrice(0.0, 0.0)),
                    Some(DisplayPrice(170.0, 180.0))
                  ),
                  SummaryElement(
                    DisplayPrice(100.0, 200.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(100.0, 200.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(100.0, 200.0),
                    Some(DisplayPrice(0.0, 0.0)),
                    Some(DisplayPrice(170.0, 180.0))
                  ),
                  SummaryElement(
                    DisplayPrice(50.0, 100.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(50.0, 100.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(50.0, 100.0),
                    Some(DisplayPrice(0.0, 0.0)),
                    Some(DisplayPrice(85.0, 90.0))
                  ),
                  SummaryElement(
                    DisplayPrice(50.0, 100.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(50.0, 100.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(50.0, 100.0),
                    Some(DisplayPrice(0.0, 0.0)),
                    Some(DisplayPrice(85.0, 90.0))
                  )
                ),
                None,
                Some(List()),
                None,
                None,
                0,
                None,
                None
              ),
              "USD" -> EnrichedPricing(
                DisplayBasis(DisplayPrice(111.4, 222.8), DisplayPrice(55.7, 111.4), DisplayPrice(55.7, 111.4)),
                DisplayBasis(DisplayPrice(0.0, 0.0), DisplayPrice(0.0, 0.0), DisplayPrice(0.0, 0.0)),
                List(),
                List(
                  EnrichedCharge(
                    "Extra charges",
                    "Extra charges [AMOUNT]",
                    Surcharge,
                    List(
                      EnrichedChargeBreakdown(
                        "City Tax",
                        "City Tax (Pay at the property) [AMOUNT]",
                        Surcharge,
                        228,
                        Hotel,
                        PB,
                        1,
                        0.0,
                        Price(44.56, 44.56),
                        Price(44.56, 44.56),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PB,
                            1,
                            Price(44.56, 44.56),
                            None,
                            Price(44.56, 44.56),
                            None,
                            SubChargeType.None
                          )
                        ),
                        false,
                        Some(Display(PB, 44.56, 44.56)),
                        0,
                        Some(Display(PB, 0.0, 0.0)),
                        Price(0.0, 0.0)
                      ),
                      EnrichedChargeBreakdown(
                        "Speed Boat Transfer",
                        "Speed Boat Transfer [AMOUNT]",
                        Surcharge,
                        51,
                        Hotel,
                        PB,
                        1,
                        0.0,
                        Price(33.42, 33.42),
                        Price(33.42, 33.42),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PB,
                            1,
                            Price(33.42, 33.42),
                            None,
                            Price(33.42, 33.42),
                            None,
                            SubChargeType.None
                          )
                        ),
                        true,
                        Some(Display(PB, 33.42, 33.42)),
                        0,
                        Some(Display(PB, 0.0, 0.0)),
                        Price(0.0, 0.0)
                      )
                    ),
                    Price(77.98, 77.98),
                    Price(0.0, 0.0),
                    Price(77.98, 77.98)
                  ),
                  EnrichedCharge(
                    "1 room X 2 nights",
                    "1 room X 2 nights [AMOUNT]",
                    Room,
                    List(
                      EnrichedChargeBreakdown(
                        "",
                        "",
                        Room,
                        -1,
                        Hotel,
                        PRPB,
                        1,
                        0.0,
                        Price(111.4, 122.54),
                        Price(111.4, 122.54),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PRPN,
                            1,
                            Price(55.7, 61.27),
                            None,
                            Price(46.46, 52.03),
                            None,
                            SubChargeType.None
                          ),
                          Daily(
                            formatter.parseDateTime("2019-12-13T00:00:00.000+07:00"),
                            0.0,
                            PRPN,
                            1,
                            Price(55.7, 61.27),
                            None,
                            Price(46.46, 52.03),
                            None,
                            SubChargeType.None
                          )
                        ),
                        true,
                        Some(Display(PRPN, 55.7, 61.27)),
                        0,
                        Some(Display(PRPN, 46.46, 52.03)),
                        Price(92.92, 104.06)
                      )
                    ),
                    Price(111.4, 122.54),
                    Price(0.0, 0.0),
                    Price(111.4, 122.54)
                  ),
                  EnrichedCharge(
                    "Hotel tax and service fees ",
                    "Hotel tax and service fees  [AMOUNT]",
                    TaxAndFee,
                    List(
                      EnrichedChargeBreakdown(
                        "Tax",
                        "Unknown [AMOUNT]",
                        Tax,
                        253,
                        Hotel,
                        PRPB,
                        1,
                        0.0,
                        Price(11.14, 11.14),
                        Price(11.14, 11.14),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PRPB,
                            1,
                            Price(5.57, 5.57),
                            None,
                            Price(0.0, 0.0),
                            None,
                            SubChargeType.None
                          ),
                          Daily(
                            formatter.parseDateTime("2019-12-13T00:00:00.000+07:00"),
                            0.0,
                            PRPB,
                            1,
                            Price(5.57, 5.57),
                            None,
                            Price(0.0, 0.0),
                            None,
                            SubChargeType.None
                          )
                        ),
                        true,
                        Some(Display(PRPN, 5.57, 5.57)),
                        0,
                        Some(Display(PRPN, 0.0, 0.0)),
                        Price(0.0, 0.0)
                      ),
                      EnrichedChargeBreakdown(
                        "Tax",
                        "Unknown (Pay at the property) [AMOUNT]",
                        Tax,
                        1794,
                        Hotel,
                        PRPB,
                        1,
                        0.0,
                        Price(22.28, 22.28),
                        Price(22.28, 22.28),
                        List(
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PRPB,
                            1,
                            Price(11.14, 11.14),
                            None,
                            Price(0.0, 0.0),
                            None,
                            SubChargeType.None
                          ),
                          Daily(
                            formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                            0.0,
                            PRPB,
                            1,
                            Price(11.14, 11.14),
                            None,
                            Price(0.0, 0.0),
                            None,
                            SubChargeType.None
                          )
                        ),
                        false,
                        Some(Display(PRPN, 11.14, 11.14)),
                        0,
                        Some(Display(PRPN, 0.0, 0.0)),
                        Price(0.0, 0.0)
                      )
                    ),
                    Price(33.42, 33.42),
                    Price(0.0, 0.0),
                    Price(33.42, 33.42)
                  )
                ),
                Some(ExtraInformation(5.57, 5.57, 33.42, 33.42, 18.49376114081997, 111.4, 111.4, 111.4)),
                DisplaySummary(
                  SummaryElement(
                    DisplayPrice(111.4, 222.8),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(111.4, 222.8),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(111.4, 222.8),
                    Some(DisplayPrice(0.0, 0.0)),
                    Some(DisplayPrice(189.38, 200.52))
                  ),
                  SummaryElement(
                    DisplayPrice(111.4, 222.8),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(111.4, 222.8),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(111.4, 222.8),
                    Some(DisplayPrice(0.0, 0.0)),
                    Some(DisplayPrice(189.38, 200.52))
                  ),
                  SummaryElement(
                    DisplayPrice(55.7, 111.4),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(55.7, 111.4),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(55.7, 111.4),
                    Some(DisplayPrice(0.0, 0.0)),
                    Some(DisplayPrice(94.69, 100.26))
                  ),
                  SummaryElement(
                    DisplayPrice(55.7, 111.4),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(55.7, 111.4),
                    DisplayPrice(0.0, 0.0),
                    DisplayPrice(55.7, 111.4),
                    Some(DisplayPrice(0.0, 0.0)),
                    Some(DisplayPrice(94.69, 100.26))
                  )
                ),
                None,
                Some(List()),
                None,
                None,
                0,
                None,
                None
              )
            ),
            promotions = None,
            pointsMax = None,
            payment = Some(
              EnrichedPayment(
                taxReceipt = EnrichedTaxReceipt(
                  isEligible = false,
                  isDomestic = false,
                  title = "Tax receipt available",
                  description =
                    "Bookings chosen to be paid at the hotel can ask for the local tax invoices from the hotel directly.\nAs for the pre-paid bookings made on the Agoda app for hotels in China (mainland), most of them can be issued local tax invoices. All bookings can get Agoda receipts via the \"My bookings\" page.",
                  symbol = "tax-receipt-available",
                  detail = Some(
                    EnrichedTaxReceiptDetail(
                      title = "Tax Receipts",
                      description =
                        "Bookings chosen to be paid at the hotel can ask for the local tax invoices from the hotel directly.\nAs for the pre-paid bookings made on the Agoda app for hotels in China (mainland), most of them can be issued local tax invoices. All bookings can get Agoda receipts via the \"My bookings\" page."
                    )
                  )
                ),
                payLater = EnrichedPayLater(
                  isEligible = false,
                  authDate = formatter.parseDateTime("2019-09-16T00:00:00.000+07:00"),
                  chargeDate = formatter.parseDateTime("2019-09-16T00:00:00.000+07:00"),
                  title = "",
                  description = "",
                  symbol = ""
                ),
                cancellation = EnrichedCancellation(
                  code = "365D100P_100P",
                  noShowPolicy = "If you fail to arrive or cancel the booking, no refund will be given.",
                  defaultNoShowPolicy = "If you fail to arrive or cancel the booking, no refund will be given.",
                  policies = List("This booking is Non-Refundable and cannot be amended or modified."),
                  defaultPolicies = List("This booking is Non-Refundable and cannot be amended or modified."),
                  cancellationType = NonRefundable,
                  description =
                    "This special offer includes an extra-low price, but cannot be amended or cancelled. In case of a no-show, the property will not refund the booking.<br><br>If you're sure of your travel dates, you can take advantage of this special offer!",
                  defaultDescription =
                    "This special offer includes an extra-low price, but cannot be amended or cancelled. In case of a no-show, the property will not refund the booking.<br><br>If you're sure of your travel dates, you can take advantage of this special offer!",
                  freeCancellationDate = None,
                  title = "Extra low price! (non-refundable)",
                  symbol = "cancellation-policy-non-refund-special-condition",
                  cancellationPolicies = None
                ),
                noCreditCard = EnrichedNoCreditCard(
                  isEligible = true,
                  title = "Book without credit card",
                  description = "",
                  symbol = "payment-option-no-credit-card"
                ),
                payAtHotel = EnrichedPayAtHotel(
                  isEligible = true,
                  title = "Pay at the hotel",
                  description = "",
                  symbol = "pay-at-the-place"
                ),
                paymentModel = Agency,
                payAtCheckIn = EnrichedPayAtCheckIn(
                  isEligible = false,
                  authDate = formatter.parseDateTime("2019-09-16T00:00:00.000+07:00"),
                  chargeDate = formatter.parseDateTime("2019-09-16T00:00:00.000+07:00"),
                  title = "",
                  description = "",
                  symbol = ""
                ),
                m150 = Some(
                  M150Info(
                    isEligible = false,
                    text = None
                  )
                ),
                noPrePaymentRequired = Some(
                  EnrichedNoPrePaymentRequired(
                    isEligible = true,
                    title = "No payment until check-in",
                    description = "",
                    symbol = ".ficon-prepayment"
                  )
                ),
                installmentDetails = None
              )
            ),
            dmcPolicyText = Some(
              EnrichedDMCPolicyText(
                externalData =
                  "<ns1:ExternalData xmlns:ns1=\"http://DmcExternalData.Agoda.com\"><ns1:DMCResponse type=\"SearchResponse\"><ns1:getBlockAvailability><ns1:result><ns1:block><source>SUPPLY_CDS</source><supplySource>Push</supplySource><ns1:is_deal>0</ns1:is_deal><ns1:source_update>SUPPLY_CDS</ns1:source_update><ns1:block_id>5494201_83346541_2_2</ns1:block_id><ns1:incremental_price><ns1:currency>EUR</ns1:currency><ns1:price>140.00</ns1:price></ns1:incremental_price><ns1:is_nocc>0</ns1:is_nocc><ns1:generic_nocc>false</ns1:generic_nocc><ns1:domestic_nocc>false</ns1:domestic_nocc><ns1:lastmin_nocc>false</ns1:lastmin_nocc><ns1:georate>1</ns1:georate><ns1:origin>A1</ns1:origin><ns1:is_bmp>0</ns1:is_bmp><ns1:display_price>140.00</ns1:display_price></ns1:block><ns1:hotel_id>54942</ns1:hotel_id></ns1:result></ns1:getBlockAvailability></ns1:DMCResponse></ns1:ExternalData>",
                blockId = Some("5494201_83346541_2_2"),
                cancellationText = ""
              )
            ),
            isFit = Some(true),
            pricingRoomOption = None,
            tooltip = RoomTooltip(
              corText = None,
              capacityText = Some("Max 2 Adults"),
              availableRoomsText = Some("Limited availability")
            ),
            gmtOffset = Some(2),
            needOccupancySearch = Some(false),
            giftCard = None,
            offerId = Some(0),
            priceOfferId = Some("3f74acf8-d557-4733-9ef1-669d2a09d57908064e41-955b-7e44-ff89-a7f5dc349eb4"),
            pseudoCoupon = Some(
              PseudoCoupon(
                showBadge = false
              )
            ),
            promotionEligible = Some(false),
            packageEligible = Some(false),
            rateRepurposeInfos = Some(List()),
            isRepurposed = Some(false),
            isAveragePrice = Some(false),
            pointsMaxBadge = Some(
              PointsMaxBadge(
                showBadge = true,
                badgeText = "Earn airline miles"
              )
            ),
            npclnChannel = None,
            priceInfos = Map(
            ),
            isRoomTypeNotGuarantee = Some(false),
            mseGroupId = None,
            campaignPromotion = None,
            roomLevelBOR = None,
            campaignPromotions = Some(List()),
            loyaltyResponse = None,
            paymentFeature = Some(
              Map(
              )
            ),
            morErpAdminId = Some(0),
            hasChildBreakfastSurcharge = Some(false),
            corBreakdown = CorBreakdown(
              perRoomPerNightTaxExclusive = List(
                CorBreakdownItem(
                  id = 1,
                  text = "Price per night",
                  price = 56.0,
                  icon = None,
                  isDiscount = false
                )
              ),
              perRoomPerNightTaxInclusive = List(
                CorBreakdownItem(
                  id = 1,
                  text = "Total price per night",
                  price = 111.0,
                  icon = None,
                  isDiscount = false
                )
              ),
              perNightTaxExclusive = List(
                CorBreakdownItem(
                  id = 1,
                  text = "Price per night",
                  price = 56.0,
                  icon = None,
                  isDiscount = false
                )
              ),
              perNightTaxInclusive = List(
                CorBreakdownItem(
                  id = 1,
                  text = "Total price per night",
                  price = 111.0,
                  icon = None,
                  isDiscount = false
                )
              )
            ),
            promotionUrgencyMessage = None,
            roomIdentifiers = Some(
              "CmkI6s4tEAQgAjAESg0zNjVEMTAwUF8xMDBQULwvektTb21lKDU0OTQyKXwzNzM2ODV8MXw1NDk0MjAxXzgzMzQ2NTQxXzJfMl8wfDJ8MnxMaXN0KCl8MzY1RDEwMFBfMTAwUHxTb21lKCkSAggB"
            ),
            corSummary = Some(
              CorSummary(
                hasCor = false,
                corType = NoCOR,
                corText = None,
                isOriginal = false,
                hasOwnCOR = false,
                isBlacklistedCor = false
              )
            ),
            isMultiRoomPromotion = Some(false),
            pricingMessages = List(),
            roomStatus = Some(Dispatched),
            fencedRate = Some(
              FencedRate(
                origin = None
              )
            ),
            priceChange = None,
            supplierFinancialData = None,
            masterTypeId = Some(373685),
            soldOutPredictionTime = None,
            rewardOptions = Map.empty
          )
        ),
        facilityGroups = Vector(),
        features = Vector(
          RoomFeatureResponse(
            name = "size",
            text = "14 m²/151 ft²",
            symbol = "sqm"
          ),
          RoomFeatureResponse(
            name = "room-size-sqm",
            text = "14",
            symbol = "room-size-sqm"
          ),
          RoomFeatureResponse(
            name = "room-size-sqft",
            text = "151",
            symbol = "room-size-sqft"
          )
        ),
        groupMseRooms = List(),
        isAllowChildren = Some(false),
        isYcsMaster = Some(false),
        suitabilityType = None,
        hotelRoomTypeAlternateName = None,
        numberOfRoom = Some(0),
        roomSizeInclTerrace = Some(false),
        extrabedChildAge = Some(0),
        maxInfantInRoom = Some(0),
        localizations = None,
        isDormitory = Some(false),
        roomReviewInformation = Some(
          MasterRoomReviewInformation(
            score = 7.0,
            scoreText = "Very good",
            valueForMoneyScore = None,
            cleanliness = None,
            roomComfort = None,
            location = None,
            staffPerformance = None,
            noOfReviewPositiveMentioned = None,
            noOfReviewNonPositiveMentioned = None
          )
        ),
        suitabilityTypes = List(),
        alternativeDate = None,
        videos = Vector.empty
      )
    ),
    rooms = List(),
    infoSummary = Some(
      InformationSummaryResponse(
        propertyId = 114558,
        isPreferredPartner = false,
        awardYear = None,
        localeName = "Hotel Icare",
        defaultName = "Hotel Icare",
        displayName = "Hotel Icare",
        singleRoomNonHotelAccommodation = false,
        hasHostExperience = false,
        accommodationType = "Hotel",
        isNonHotelAccommodationType = false,
        remarks = None,
        geoInfo = Some(
          GeoInfo(
            latitude = 43.610841826476,
            longitude = 1.45251274108887,
            obfuscatedLat = 43.610841826476,
            obfuscatedLong = 1.45251274108887
          )
        ),
        address = Some(
          Address(
            country = Some("France"),
            city = Some("Toulouse"),
            state = Some("Midi-Pyrenees"),
            area = Some("Toulouse City Center"),
            address1 = Some("11 Boulevard Bonrepos"),
            address2 = None,
            postalCode = None,
            GmtOffset = Some(2),
            utcOffset = None,
            countryId = None,
            cityId = None,
            areaId = None,
            countryCode = None,
            stateId = None
          )
        ),
        highlightedFeatures = Vector(
          Feature(
            id = Some(80),
            featureName = Some("Car park"),
            featureNameLocalizations = None,
            symbol = Some("car-park"),
            emphasis = None,
            available = Some(true),
            tags = None,
            order = Some(10),
            highlighted = Some(true),
            images = None,
            featureTags = None,
            providerIds = None,
            featureNameLocalizationList = None
          )
        ),
        rating = StarRating(
          value = 2.0,
          symbol = "star-2",
          text =
            "Gold star ratings are provided by the property to reflect the comfort, facilities, and amenities you can expect.",
          color = Color(
            name = "orange-yellow",
            hex = "#F99E00"
          )
        ),
        propertyType = "Hotel",
        sellingPoints = Vector(),
        generalRoomInformation = Some(
          GeneralRoomInformationResponse(
            features = Vector()
          )
        ),
        spokenLanguages = Vector(
          LanguageInfo(
            id = 2,
            localeName = "French",
            locale = None
          )
        ),
        awardsAndAccolades = AwardsAndAccolades(
          text = "Awards and Accolades\n",
          goldCircleAward = None,
          advanceGuaranteeProgram = None
        ),
        localizations = None,
        isAllowPostBooking = None,
        themes = List(
          HotelTheme(
            themeId = 1,
            name = "Business hotels",
            typeId = 2
          ),
          HotelTheme(
            themeId = 1,
            name = "Airport hotels",
            typeId = 2
          ),
          HotelTheme(
            themeId = 1,
            name = "Gay friendly hotels",
            typeId = 2
          ),
          HotelTheme(
            themeId = 3,
            name = "Pet friendly hotels",
            typeId = 2
          )
        ),
        chainId = 0,
        hasChannelManager = false,
        nhaSummary = None,
        propertyStatus = None,
        requiredGuestContact = false,
        asqType = None,
        asqInfos = Vector.empty
      )
    ),
    paxSetting = Some(
      PartnerPaxOption(
        paxSubmit = One,
        childAge = 0
      )
    ),
    localInformation = None,
    checkInOut = None,
    synopsis = None,
    engagement = None,
    experience = None,
    distance = None,
    pseudoCouponMessage = None,
    dfFeatures = Some(
      HotelFeatures(
        isMyStaySupported = false,
        bookOnRequest = false,
        canApplyPromotionEligible = false,
        options = Some(
          HotelAggregatedOptions(
            benefits = List(),
            cancellationCodes = Map(
              Agency -> Set(Cancellation("365D100P_100P"))
            ),
            maxPointMax = None,
            channels = Some(Set(1)),
            payment = HotelAggregatedPayment(
              taxReceipt = TaxReceipt(
                available = false,
                isDomestic = false,
                isCommission = false
              ),
              payLater = PayLater(
                payLater = false
              ),
              creditCard = CreditCard(
                required = false
              ),
              prePayment = PrePayment(
                required = false
              ),
              payAtHotel = PayAtHotel(
                isEligible = false
              )
            ),
            yscAvailableRooms = Some(0),
            hasAPS = Some(false)
          )
        ),
        bookNowPayAtCheckIn = false,
        isPackageEligible = false
      )
    ),
    priceMessaging = None,
    promotionEligible = Some(false),
    packageEligible = Some(false),
    booking = Some(
      EnrichedBookingItem(
        rooms = List(
          EnrichedBookingRoom(
            uid = "08064e41-955b-7e44-ff89-a7f5dc349eb4",
            accountingEntity = Some(
              AccountingEntity(
                merchantOfRecord = 114558,
                revenue = 5632,
                rateContract = 3038,
                argument =
                  "{\"ContractTypeId\":0,\"FapiaoInfo\":{\"IsDigitalGeneralEnabled\":true,\"IsPhysicalSpecialEnabled\":true},\"IsDomesticTaxReceiptEnabled\":false,\"MerchantOfRecord\":114558,\"MerchantOfRecordType\":2,\"RateContract\":3038,\"RateContractType\":3,\"Revenue\":5632,\"RevenueType\":1}"
              )
            ),
            availabilityType = Realtime,
            breakfastIncluded = false,
            breakfastInfo = "",
            cancellation = EnrichedBookingCancellation(
              code = "365D100P_100P",
              fallbackCode = "",
              noShowPolicy = "If you fail to arrive or cancel the booking, no refund will be given.",
              defaultNoShowPolicy = "If you fail to arrive or cancel the booking, no refund will be given.",
              policies = List("This booking is Non-Refundable and cannot be amended or modified."),
              defaultPolicies = List("This booking is Non-Refundable and cannot be amended or modified.")
            ),
            cancellationPolicy = "This booking is Non-Refundable and cannot be amended or modified.",
            cancellationPolicyCode = "365D100P_100P",
            cancellationChargeType = None,
            pricing = BookingPrice(
              charge = List(
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 2,
                  localAmount = 5.0,
                  usdAmount = 5.57,
                  reqAmount = 5.57,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(253),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 2,
                  localAmount = 5.0,
                  usdAmount = 5.57,
                  reqAmount = 5.57,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(253),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 12,
                  localAmount = 30.0,
                  usdAmount = 33.42,
                  reqAmount = 33.42,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 48,
                  localAmount = 0.0,
                  usdAmount = 0.0,
                  reqAmount = 0.0,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 48,
                  localAmount = 5.0,
                  usdAmount = 5.57,
                  reqAmount = 5.57,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 11,
                  localAmount = 50.0,
                  usdAmount = 55.7,
                  reqAmount = 55.7,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 3,
                  localAmount = 0.0,
                  usdAmount = 0.0,
                  reqAmount = 0.0,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 1,
                  localAmount = 41.7,
                  usdAmount = 46.46,
                  reqAmount = 46.46,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 3,
                  localAmount = 8.3,
                  usdAmount = 9.24,
                  reqAmount = 9.24,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 10,
                  localAmount = 46.7,
                  usdAmount = 52.03,
                  reqAmount = 52.03,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 49,
                  localAmount = 0.0,
                  usdAmount = 0.0,
                  reqAmount = 0.0,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 12,
                  localAmount = 55.0,
                  usdAmount = 61.27,
                  reqAmount = 61.27,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 48,
                  localAmount = 5.0,
                  usdAmount = 5.57,
                  reqAmount = 5.57,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 5,
                  localAmount = 0.0,
                  usdAmount = 0.0,
                  reqAmount = 0.0,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 49,
                  localAmount = 0.0,
                  usdAmount = 0.0,
                  reqAmount = 0.0,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 5,
                  localAmount = 0.0,
                  usdAmount = 0.0,
                  reqAmount = 0.0,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 1,
                  localAmount = 30.0,
                  usdAmount = 33.42,
                  reqAmount = 33.42,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 11,
                  localAmount = 50.0,
                  usdAmount = 55.7,
                  reqAmount = 55.7,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 12,
                  localAmount = 55.0,
                  usdAmount = 61.27,
                  reqAmount = 61.27,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 3,
                  localAmount = 8.3,
                  usdAmount = 9.24,
                  reqAmount = 9.24,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 49,
                  localAmount = 0.0,
                  usdAmount = 0.0,
                  reqAmount = 0.0,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 10,
                  localAmount = 30.0,
                  usdAmount = 33.42,
                  reqAmount = 33.42,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 1,
                  localAmount = 41.7,
                  usdAmount = 46.46,
                  reqAmount = 46.46,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 11,
                  localAmount = 30.0,
                  usdAmount = 33.42,
                  reqAmount = 33.42,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 41,
                  localAmount = 55.0,
                  usdAmount = 61.27,
                  reqAmount = 61.27,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 41,
                  localAmount = 30.0,
                  usdAmount = 33.42,
                  reqAmount = 33.42,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(51),
                  taxFeeId = Some(0),
                  typeId = 2,
                  roomNo = 0,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 10,
                  localAmount = 46.7,
                  usdAmount = 52.03,
                  reqAmount = 52.03,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 5,
                  localAmount = 0.0,
                  usdAmount = 0.0,
                  reqAmount = 0.0,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                ),
                BookingItemBreakdown(
                  chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                  exchangeRate = 0.8975999999999998,
                  itemId = 41,
                  localAmount = 55.0,
                  usdAmount = 61.27,
                  reqAmount = 61.27,
                  localCurrency = "EUR",
                  quantity = 1,
                  surchargeId = Some(0),
                  taxFeeId = Some(0),
                  typeId = 1,
                  roomNo = 1,
                  option = Mandatory,
                  taxProtoTypeId = 0,
                  subTypeId = Some(0)
                )
              ),
              summary = Some(
                BookingSummaryPrices(
                  roomAndExtrabed = BookingSummaryPrice(
                    perBook = Some(
                      BookingReferencePrice(
                        netInclusive = 104.06,
                        sellInclusive = 122.54
                      )
                    ),
                    perRoom = None,
                    perNight = None,
                    perRoomPerNight = None
                  ),
                  surcharges = BookingSurchargesItems(
                    perBook = List(
                      BookingSurchargesItem(
                        id = 51,
                        charge = BookingReferencePrice(
                          netInclusive = 33.42,
                          sellInclusive = 33.42
                        ),
                        chargeOption = Mandatory
                      )
                    ),
                    perRoom = List(),
                    perNight = List(),
                    perRoomPerNight = List()
                  )
                )
              )
            ),
            dmcId = Some(3038),
            dmcSpecificData =
              "<ns1:ExternalData xmlns:ns1=\"http://DmcExternalData.Agoda.com\"><ns1:DMCResponse type=\"SearchResponse\"><ns1:getBlockAvailability><ns1:result><ns1:block><source>SUPPLY_CDS</source><supplySource>Push</supplySource><ns1:is_deal>0</ns1:is_deal><ns1:source_update>SUPPLY_CDS</ns1:source_update><ns1:block_id>5494201_83346541_2_2</ns1:block_id><ns1:incremental_price><ns1:currency>EUR</ns1:currency><ns1:price>140.00</ns1:price></ns1:incremental_price><ns1:is_nocc>0</ns1:is_nocc><ns1:generic_nocc>false</ns1:generic_nocc><ns1:domestic_nocc>false</ns1:domestic_nocc><ns1:lastmin_nocc>false</ns1:lastmin_nocc><ns1:georate>1</ns1:georate><ns1:origin>A1</ns1:origin><ns1:is_bmp>0</ns1:is_bmp><ns1:display_price>140.00</ns1:display_price></ns1:block><ns1:hotel_id>54942</ns1:hotel_id></ns1:result></ns1:getBlockAvailability></ns1:DMCResponse></ns1:ExternalData>",
            excluded = "",
            giftCardEarning = None,
            hotelId = 114558,
            included = "",
            isAgodaReception = false,
            isNHA = false,
            isNotCcRequired = true,
            isPrepay = false,
            capacity = EnrichedCapacity(
              adults = 2,
              children = 0,
              extraBed = 0,
              occupancy = 2,
              symbol = "max-occupancy-override",
              maxExtraBed = 0,
              numberOfGuestsWithoutRoom = 0,
              description = Some("2 adults"),
              allowedFreeChildrenAndInfants = Some(0)
            ),
            noOfAdults = 2,
            noOfChildren = 0,
            noOfExtrabeds = 0,
            numberOfRoom = 1,
            occupancy = 2,
            partnerLoyaltyPoint = None,
            paymentModels = Agency,
            rateCategory = EnrichedBookingRateCategory(
              id = 0,
              Code = "",
              applyTo = "",
              Amount = 0.0,
              benefits = List()
            ),
            rateModel = RateModel(
              id = 2
            ),
            totalSaving = Some(0.0),
            rateModelType = Some(2),
            roomTypeId = 373685,
            roomTypeName = "Standard Double Room with Shower",
            ycsRatePlanId = 1,
            sellInfo = EnrichedBookingSellInfo(
              downlift = 0.0,
              downliftUsd = 0.0,
              priceTemplatedId = Some(-1),
              searchId = "3f74acf8-d557-4733-9ef1-669d2a09d579",
              sellTagId = 0,
              isAdvanceGuarantee = false,
              offerId = Some(0),
              priceOfferId = Some("3f74acf8-d557-4733-9ef1-669d2a09d57908064e41-955b-7e44-ff89-a7f5dc349eb4"),
              fireDrillContract = None
            ),
            taxSurchargeInfo =
              "Included : Taxes and fees USD 11.14, Speed Boat Transfer USD 33.42<br>Not Included : Taxes and fees USD 22.28 (Pay at the property), City Tax (Pay at the property) USD 44.56",
            rateChannel = Some(
              EnrichedChannel(
                id = 1,
                description = "",
                symbol = ""
              )
            ),
            correlationId = "3f74acf8-d557-4733-9ef1-669d2a09d579",
            promotion = None,
            ycsForeignPromotionText = "",
            ycsPromotionID = 0,
            ycsPromotionText = "",
            availableRooms = 999,
            chargeDiscount = 0.0,
            displayAmount = 0.0,
            displayCurrency = "",
            benefit = List(),
            exchange = BookingPayment(
              upliftExchangeRate = 1.0,
              siteExchangeRate = 1.0,
              exchangeRate = 1.0,
              localDecimalPlace = 2,
              requestDecimalPlace = 2,
              usdDecimalPlace = 2,
              localCurrency = "EUR",
              requestToUSDExchangeRate = 1.0,
              requestToLocalExchangeRate = 0.8976,
              upliftAmount = None,
              paymentCurrency = None,
              paymentAmount = Some(155.96),
              paymentUsdAmount = Some(155.96)
            ),
            npclnChannel = None,
            borSupplier = None,
            specialRequestOptions = List(
              AdditionalNotes,
              LateCheckIn,
              EarlyCheckIn,
              HighFloor,
              LargeBed,
              TwinBed,
              NonSmokingRoom,
              SmokingRoom
            ),
            hotelRemark = Some(""),
            lineItemId = Some(1),
            pointMultiply = Some(0.0),
            roomRemark = Some(""),
            numberOfBeds = Some(0),
            isAgodaAgency = false,
            paymentChannels = List.empty
          )
        ),
        los = 2,
        booking = List(
          EnrichedEBEBooking(
            creditCard = EnrichedEBECreditCard(
              payment = EnrichedEBEPayment(
                exchangeRate = 1.0,
                giftCardAmount = 0.0,
                giftCardAmountInUSD = 0.0,
                paymentAmount = 155.96,
                paymentAmountInUSD = 155.96,
                paymentCurrency = "USD",
                rewardsRedeemedPoint = 0,
                rewardsSaving = 0.0,
                rewardsSavingInUSD = 0.0,
                siteExchangeRate = 1.0,
                upliftAmount = 0.0,
                upliftExchangeRate = 1.0,
                exchangeRateOption = Some(0),
                destinationCurrency = None,
                destinationExchangeRate = 1.0,
                rateQuoteID = 0
              ),
              chargeOption = None,
              fullyAuthDate = Some(formatter.parseDateTime("2019-09-16T00:00:00.000+07:00")),
              fullyChargeDate = Some(formatter.parseDateTime("2019-09-16T00:00:00.000+07:00"))
            ),
            discount = None,
            hotel = List(
              EnrichedEBEHotel(
                checkIn = formatter.parseDateTime("2019-12-12T00:00:00.000+07:00"),
                checkOut = formatter.parseDateTime("2019-12-14T00:00:00.000+07:00"),
                numberOfAdults = 2,
                numberOfChildren = 0,
                numberOfRooms = 1,
                occFreeSearch = false,
                room = List(
                  EnrichedBookingRoom(
                    uid = "08064e41-955b-7e44-ff89-a7f5dc349eb4",
                    accountingEntity = Some(
                      AccountingEntity(
                        merchantOfRecord = 114558,
                        revenue = 5632,
                        rateContract = 3038,
                        argument =
                          "{\"ContractTypeId\":0,\"FapiaoInfo\":{\"IsDigitalGeneralEnabled\":true,\"IsPhysicalSpecialEnabled\":true},\"IsDomesticTaxReceiptEnabled\":false,\"MerchantOfRecord\":114558,\"MerchantOfRecordType\":2,\"RateContract\":3038,\"RateContractType\":3,\"Revenue\":5632,\"RevenueType\":1}"
                      )
                    ),
                    availabilityType = Realtime,
                    breakfastIncluded = false,
                    breakfastInfo = "",
                    cancellation = EnrichedBookingCancellation(
                      code = "365D100P_100P",
                      fallbackCode = "",
                      noShowPolicy = "If you fail to arrive or cancel the booking, no refund will be given.",
                      defaultNoShowPolicy = "If you fail to arrive or cancel the booking, no refund will be given.",
                      policies = List("This booking is Non-Refundable and cannot be amended or modified."),
                      defaultPolicies = List("This booking is Non-Refundable and cannot be amended or modified.")
                    ),
                    cancellationPolicy = "This booking is Non-Refundable and cannot be amended or modified.",
                    cancellationPolicyCode = "365D100P_100P",
                    cancellationChargeType = Some(PerNight),
                    pricing = BookingPrice(
                      charge = List(
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 2,
                          localAmount = 5.0,
                          usdAmount = 5.57,
                          reqAmount = 5.57,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(253),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 2,
                          localAmount = 5.0,
                          usdAmount = 5.57,
                          reqAmount = 5.57,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(253),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 12,
                          localAmount = 30.0,
                          usdAmount = 33.42,
                          reqAmount = 33.42,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 48,
                          localAmount = 0.0,
                          usdAmount = 0.0,
                          reqAmount = 0.0,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 48,
                          localAmount = 5.0,
                          usdAmount = 5.57,
                          reqAmount = 5.57,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 11,
                          localAmount = 50.0,
                          usdAmount = 55.7,
                          reqAmount = 55.7,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 3,
                          localAmount = 0.0,
                          usdAmount = 0.0,
                          reqAmount = 0.0,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 1,
                          localAmount = 41.7,
                          usdAmount = 46.46,
                          reqAmount = 46.46,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 3,
                          localAmount = 8.3,
                          usdAmount = 9.24,
                          reqAmount = 9.24,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 10,
                          localAmount = 46.7,
                          usdAmount = 52.03,
                          reqAmount = 52.03,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 49,
                          localAmount = 0.0,
                          usdAmount = 0.0,
                          reqAmount = 0.0,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 12,
                          localAmount = 55.0,
                          usdAmount = 61.27,
                          reqAmount = 61.27,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 48,
                          localAmount = 5.0,
                          usdAmount = 5.57,
                          reqAmount = 5.57,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 5,
                          localAmount = 0.0,
                          usdAmount = 0.0,
                          reqAmount = 0.0,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 49,
                          localAmount = 0.0,
                          usdAmount = 0.0,
                          reqAmount = 0.0,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 5,
                          localAmount = 0.0,
                          usdAmount = 0.0,
                          reqAmount = 0.0,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 1,
                          localAmount = 30.0,
                          usdAmount = 33.42,
                          reqAmount = 33.42,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 11,
                          localAmount = 50.0,
                          usdAmount = 55.7,
                          reqAmount = 55.7,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 12,
                          localAmount = 55.0,
                          usdAmount = 61.27,
                          reqAmount = 61.27,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 3,
                          localAmount = 8.3,
                          usdAmount = 9.24,
                          reqAmount = 9.24,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 49,
                          localAmount = 0.0,
                          usdAmount = 0.0,
                          reqAmount = 0.0,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 10,
                          localAmount = 30.0,
                          usdAmount = 33.42,
                          reqAmount = 33.42,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 1,
                          localAmount = 41.7,
                          usdAmount = 46.46,
                          reqAmount = 46.46,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 11,
                          localAmount = 30.0,
                          usdAmount = 33.42,
                          reqAmount = 33.42,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 41,
                          localAmount = 55.0,
                          usdAmount = 61.27,
                          reqAmount = 61.27,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 41,
                          localAmount = 30.0,
                          usdAmount = 33.42,
                          reqAmount = 33.42,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(51),
                          taxFeeId = Some(0),
                          typeId = 2,
                          roomNo = 0,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 10,
                          localAmount = 46.7,
                          usdAmount = 52.03,
                          reqAmount = 52.03,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-13T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 5,
                          localAmount = 0.0,
                          usdAmount = 0.0,
                          reqAmount = 0.0,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        ),
                        BookingItemBreakdown(
                          chargeDate = Some(formatter.parseDateTime("2019-12-12T00:00:00.000+07:00")),
                          exchangeRate = 0.8975999999999998,
                          itemId = 41,
                          localAmount = 55.0,
                          usdAmount = 61.27,
                          reqAmount = 61.27,
                          localCurrency = "EUR",
                          quantity = 1,
                          surchargeId = Some(0),
                          taxFeeId = Some(0),
                          typeId = 1,
                          roomNo = 1,
                          option = Mandatory,
                          taxProtoTypeId = 0,
                          subTypeId = Some(0)
                        )
                      ),
                      summary = Some(
                        BookingSummaryPrices(
                          roomAndExtrabed = BookingSummaryPrice(
                            perBook = Some(
                              BookingReferencePrice(
                                netInclusive = 104.06,
                                sellInclusive = 122.54
                              )
                            ),
                            perRoom = None,
                            perNight = None,
                            perRoomPerNight = None
                          ),
                          surcharges = BookingSurchargesItems(
                            perBook = List(
                              BookingSurchargesItem(
                                id = 51,
                                charge = BookingReferencePrice(
                                  netInclusive = 33.42,
                                  sellInclusive = 33.42
                                ),
                                chargeOption = Mandatory
                              )
                            ),
                            perRoom = List(),
                            perNight = List(),
                            perRoomPerNight = List()
                          )
                        )
                      )
                    ),
                    dmcId = Some(3038),
                    dmcSpecificData =
                      "<ns1:ExternalData xmlns:ns1=\"http://DmcExternalData.Agoda.com\"><ns1:DMCResponse type=\"SearchResponse\"><ns1:getBlockAvailability><ns1:result><ns1:block><source>SUPPLY_CDS</source><supplySource>Push</supplySource><ns1:is_deal>0</ns1:is_deal><ns1:source_update>SUPPLY_CDS</ns1:source_update><ns1:block_id>5494201_83346541_2_2</ns1:block_id><ns1:incremental_price><ns1:currency>EUR</ns1:currency><ns1:price>140.00</ns1:price></ns1:incremental_price><ns1:is_nocc>0</ns1:is_nocc><ns1:generic_nocc>false</ns1:generic_nocc><ns1:domestic_nocc>false</ns1:domestic_nocc><ns1:lastmin_nocc>false</ns1:lastmin_nocc><ns1:georate>1</ns1:georate><ns1:origin>A1</ns1:origin><ns1:is_bmp>0</ns1:is_bmp><ns1:display_price>140.00</ns1:display_price></ns1:block><ns1:hotel_id>54942</ns1:hotel_id></ns1:result></ns1:getBlockAvailability></ns1:DMCResponse></ns1:ExternalData>",
                    excluded = "",
                    giftCardEarning = None,
                    hotelId = 114558,
                    included = "",
                    isAgodaReception = false,
                    isNHA = false,
                    isNotCcRequired = true,
                    isPrepay = false,
                    capacity = EnrichedCapacity(
                      adults = 2,
                      children = 0,
                      extraBed = 0,
                      occupancy = 2,
                      symbol = "max-occupancy-override",
                      maxExtraBed = 0,
                      numberOfGuestsWithoutRoom = 0,
                      description = Some("2 adults"),
                      allowedFreeChildrenAndInfants = Some(0)
                    ),
                    noOfAdults = 2,
                    noOfChildren = 0,
                    noOfExtrabeds = 0,
                    numberOfRoom = 1,
                    occupancy = 2,
                    partnerLoyaltyPoint = None,
                    paymentModels = Agency,
                    rateCategory = EnrichedBookingRateCategory(
                      id = 0,
                      Code = "",
                      applyTo = "",
                      Amount = 0.0,
                      benefits = List()
                    ),
                    rateModel = RateModel(
                      id = 2
                    ),
                    totalSaving = Some(0.0),
                    rateModelType = Some(2),
                    roomTypeId = 373685,
                    roomTypeName = "Standard Double Room with Shower",
                    ycsRatePlanId = 1,
                    sellInfo = EnrichedBookingSellInfo(
                      downlift = 0.0,
                      downliftUsd = 0.0,
                      priceTemplatedId = Some(-1),
                      searchId = "3f74acf8-d557-4733-9ef1-669d2a09d579",
                      sellTagId = 0,
                      isAdvanceGuarantee = false,
                      offerId = Some(0),
                      priceOfferId = Some("3f74acf8-d557-4733-9ef1-669d2a09d57908064e41-955b-7e44-ff89-a7f5dc349eb4"),
                      fireDrillContract = None
                    ),
                    taxSurchargeInfo =
                      "Included : Taxes and fees USD 11.14, Speed Boat Transfer USD 33.42<br>Not Included : Taxes and fees USD 22.28 (Pay at the property), City Tax (Pay at the property) USD 44.56",
                    rateChannel = Some(
                      EnrichedChannel(
                        id = 1,
                        description = "",
                        symbol = ""
                      )
                    ),
                    correlationId = "3f74acf8-d557-4733-9ef1-669d2a09d579",
                    promotion = None,
                    ycsForeignPromotionText = "",
                    ycsPromotionID = 0,
                    ycsPromotionText = "",
                    availableRooms = 999,
                    chargeDiscount = 0.0,
                    displayAmount = 0.0,
                    displayCurrency = "",
                    benefit = List(),
                    exchange = BookingPayment(
                      upliftExchangeRate = 1.0,
                      siteExchangeRate = 1.0,
                      exchangeRate = 1.0,
                      localDecimalPlace = 2,
                      requestDecimalPlace = 2,
                      usdDecimalPlace = 2,
                      localCurrency = "EUR",
                      requestToUSDExchangeRate = 1.0,
                      requestToLocalExchangeRate = 0.8976,
                      upliftAmount = None,
                      paymentCurrency = None,
                      paymentAmount = Some(155.96),
                      paymentUsdAmount = Some(155.96)
                    ),
                    npclnChannel = None,
                    borSupplier = None,
                    specialRequestOptions = List(
                      AdditionalNotes,
                      LateCheckIn,
                      EarlyCheckIn,
                      HighFloor,
                      LargeBed,
                      TwinBed,
                      NonSmokingRoom,
                      SmokingRoom
                    ),
                    hotelRemark = Some(""),
                    lineItemId = Some(1),
                    pointMultiply = Some(0.0),
                    roomRemark = Some(""),
                    numberOfBeds = Some(0),
                    isAgodaAgency = false,
                    paymentChannels = List.empty
                  )
                )
              )
            )
          )
        )
      )
    ),
    taxType = Some(SimpleTax),
    supplierSummaries = Map(
      3038L -> EnrichedSupplierSummary(
        supplierId = 3038,
        isReady = true
      )
    ),
    isMseProperty = Some(false),
    mseCheapestRoom = None,
    usp = None,
    uspRanks = None,
    suggestedRooms = List(),
    roomSwapping = List(),
    suggestedChildRooms = List(),
    maxPointsMax = None,
    maxPointsMaxBadge = Some(
      PointsMaxBadge(
        showBadge = true,
        badgeText = "Earn airline miles"
      )
    ),
    taxMetaData = List(),
    nearestLandmarkDistanceMessage = None,
    nearestLandmarkDistance = None,
    distanceFromSelectedLandmark = None,
    selectedLandmarkName = None,
    isFavorite = None,
    friends = List(),
    uniqueSellingPoints = None,
    supportUserLocale = Some(false),
    supportUserLocaleBadge = None,
    hotelAggInfo = None,
    soldOutPrice = None,
    soldOutRooms = None,
    bookingHistory = None,
    bookingFormHistory = None,
    topSellingPoints = List(
      TopSellingPoint(
        id = 9,
        text = None,
        icon = None,
        value = Some(10.0)
      )
    ),
    personalizedInformation = None,
    sponsored = false,
    sponsoredType = None,
    showSponsoredTag = None,
    sponsoredTrackingData = None,
    rankingType = 0,
    rankingExplanationMessages = List(),
    continentId = Some(4),
    isWhiteListHotel = false,
    internalUtilityData = null,
    closestBeachLandmarkInfo = List(),
    supportedSuppliers = Map(
      3038L -> EnrichedSupplierSummary(
        supplierId = 3038,
        isReady = true
      )
    ),
    areaBookingCountLastWeek = Some(
      AreaBookingCountModel(
        text = "{0} bookings confirmed so far this week in the Toulouse City Center area in Toulouse",
        value = 49
      )
    ),
    alternateDates = List(),
    stayOccupancy = Some(
      StayOccupancy(
        stayNoOfAdults = 2,
        stayNoOfChildren = 0,
        stayListOfChildAge = List(),
        stayNoOfRoom = 1
      )
    ),
    bookingMaxOccupancy = Some(
      BookingMaxOccupancy(
        adults = 2,
        children = 0
      )
    ),
    rateCategory = None,
    propertyRankingInfo = None,
    metaEngagement = None,
    multiRoomSuggestions = List(),
    isBoutiqueHotel = -1,
    multiHotelEligible = None,
    cartEligible = None
  )

}
