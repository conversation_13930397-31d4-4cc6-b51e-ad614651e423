package mocks

import com.agoda.bapi.common.message.creation.PaymentAmount
import com.agoda.bapi.common.model.creation.{ChildRoom, ChildRoomPricing, DisplayBasis, DisplayPrice, MasterRoom}
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.token._
import com.agoda.bapi.common.util.TokenSerializers
import com.agoda.bapi.common.util.TokenSerializers._
import com.agoda.bapi.server.service.allotment.AllotmentStatus
import com.agoda.mpb.common.models.state.{PayNowProductPayment, ProductPayment, ProductPaymentInfo}

object AlternativePreCheckBookingTokenMockHelper extends ProductTokenMockHelper {
  val bapiTokenWithAlternativeRooms = defaultPropertyBookingToken.copy(
    masterRooms = Seq(
      MasterRoom(
        typeId = 1234,
        childrenRooms = List(
          ChildRoom(
            uid = Some("uid-original"),
            roomIdentifiers = Some("roomiden-original"),
            pricingInUSD = Some(
              ChildRoomPricing(
                DisplayBasis(perBook = DisplayPrice(5, 6))
              )
            )
          ),
          ChildRoom(
            uid = Some("uid-breakfastupsell"),
            roomIdentifiers = Some("roomiden-breakfastupsell"),
            pricingInUSD = Some(
              ChildRoomPricing(
                DisplayBasis(perBook = DisplayPrice(15, 16))
              )
            )
          ),
          ChildRoom(
            uid = Some("uid-crosssell"),
            roomIdentifiers = Some("roomiden-crosssell"),
            pricingInUSD = Some(
              ChildRoomPricing(
                DisplayBasis(perBook = DisplayPrice(25, 26))
              )
            )
          )
        )
      )
    ),
    isPriceGuaranteedTokenCleared = true
  )

  val setupTokenWithNotAvailableOnAlternative = defaultPropertySetupToken.copy(
    roomAllotmentResults = Seq(
      RoomAllotmentResult("roomiden-original", Some("request-original"), AllotmentStatus.Available),
      RoomAllotmentResult(
        "roomiden-breakfastupsell",
        Some("request-breakfastupsell"),
        AllotmentStatus.NotAvailable
      ),
      RoomAllotmentResult("roomiden-crosssell", Some("request-crosssell"), AllotmentStatus.NotAvailable)
    )
  )

  val setupTokenWithPriceChangedOnAlternative = defaultPropertySetupToken.copy(
    roomAllotmentResults = Seq(
      RoomAllotmentResult("roomiden-original", Some("request-original"), AllotmentStatus.Available),
      RoomAllotmentResult(
        "roomiden-breakfastupsell",
        Some("request-breakfastupsell"),
        AllotmentStatus.PriceChanged
      ),
      RoomAllotmentResult("roomiden-crosssell", Some("request-crosssell"), AllotmentStatus.Available)
    )
  )

  val setupTokenAlternativeRoomsWithoutAllotResult = defaultPropertySetupToken.copy(
    roomAllotmentResults = Seq(
      RoomAllotmentResult("roomiden-original", Some("request-original"), AllotmentStatus.Processing),
      RoomAllotmentResult(
        "roomiden-breakfastupsell",
        Some("request-breakfastupsell"),
        AllotmentStatus.Processing
      ),
      RoomAllotmentResult("roomiden-crosssell", Some("request-crosssell"), AllotmentStatus.Processing)
    )
  )

  val multiProductSetupTokenNotAvailableOnAlternative = MultiProductSetupBookingToken(
    properties = TokenSerializers[PropertySetupModel]
      .serialize(Map(productKey -> setupTokenWithNotAvailableOnAlternative), Some(1571200505532L), None)
      .toOption
  )

  val multiProductSetupTokenPriceChangedOnAlternative = MultiProductSetupBookingToken(
    properties = TokenSerializers[PropertySetupModel]
      .serialize(Map(productKey -> setupTokenWithPriceChangedOnAlternative), Some(1571200505532L), None)
      .toOption
  )

  val multiProductSetupTokenAlternativeRoomsWithoutAllotResult = MultiProductSetupBookingToken(
    properties = TokenSerializers[PropertySetupModel]
      .serialize(Map(productKey -> setupTokenAlternativeRoomsWithoutAllotResult), Some(1571200505532L), None)
      .toOption
  )

  val multiProductCreationToken = MultiProductCreationBookingToken(
    properties = TokenSerializers[PropertyBookingModel]
      .serialize(Map(productKey -> bapiTokenWithAlternativeRooms), Some(1571199718191L), None)
      .toOption,
    flights = None,
    tripProtections = None,
    cars = None,
    activities = None,
    priceFreezes = None,
    cegFastTracks = None,
    addOns = None,
    payment = PaymentAmount("THB", 4300, 120, 0.1, 0, 0.1, 0.1, None, 0, 0, 0, 0, 0, 0, 0, None),
    bookingFlowType = BookingFlow.Package,
    commonPayment = None
  )

  val multiProductTokenNotAvailableOnAlternative = MultiProductBookingToken(
    setupBookingToken = TokenSerializers[MultiProductSetupBookingToken]
      .serialize(multiProductSetupTokenNotAvailableOnAlternative, Some(1571200505532L), None)
      .toOption,
    creationBookingToken = TokenSerializers[MultiProductCreationBookingToken]
      .serialize(multiProductCreationToken, Some(1571200505532L), None)
      .toOption,
    None
  )

  val multiProductTokenPriceChangedOnAlternative = MultiProductBookingToken(
    setupBookingToken = TokenSerializers[MultiProductSetupBookingToken]
      .serialize(multiProductSetupTokenPriceChangedOnAlternative, Some(1571200505532L), None)
      .toOption,
    creationBookingToken = TokenSerializers[MultiProductCreationBookingToken]
      .serialize(multiProductCreationToken, Some(1571200505532L), None)
      .toOption,
    None
  )

  val multiProductTokenAlternativeWithoutAllotResult = MultiProductBookingToken(
    setupBookingToken = TokenSerializers[MultiProductSetupBookingToken]
      .serialize(multiProductSetupTokenAlternativeRoomsWithoutAllotResult, Some(1571200505532L), None)
      .toOption,
    creationBookingToken = TokenSerializers[MultiProductCreationBookingToken]
      .serialize(multiProductCreationToken, Some(1571200505532L), None)
      .toOption,
    None
  )

  val multiBookingTokenStringNotAvailableOnAlternative = TokenSerializers
    .toJsonString(
      TokenSerializers[MultiProductBookingToken]
        .serialize(multiProductTokenNotAvailableOnAlternative, Some(1571853316957L), None)
        .get
    )
    .get

  val multiBookingTokenStringPriceChangedOnAlternative = TokenSerializers
    .toJsonString(
      TokenSerializers[MultiProductBookingToken]
        .serialize(multiProductTokenPriceChangedOnAlternative, Some(1571853316957L), None)
        .get
    )
    .get

  val multiBookingTokenStringAlternativeWithoutAllotResult = TokenSerializers
    .toJsonString(
      TokenSerializers[MultiProductBookingToken]
        .serialize(multiProductTokenAlternativeWithoutAllotResult, Some(1571853316957L), None)
        .get
    )
    .get
}
