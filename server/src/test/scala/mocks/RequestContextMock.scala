package mocks

import com.agoda.bapi.common.directive.BookingAPIHttpHeader
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.model.{UserContext, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.service.MessagesBag
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
trait RequestContextMock {
  val httpHeader =
    BookingAPIHttpHeader(
      "test",
      0,
      "test",
      "test",
      WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration),
      "0.0.0.0"
    )

  def requestContext(messagesBag: MessagesBag, userContext: Option[UserContext] = None): RequestContext =
    RequestContext(
      1,
      "en-us",
      1,
      "",
      correlationId = Some("correlationId"),
      userContext = userContext,
      messagesBag = messagesBag,
      whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, new FeaturesConfiguration),
      xForwardedForIp = "0.0.0.0",
      setupCorrelationId = None
    )
}
