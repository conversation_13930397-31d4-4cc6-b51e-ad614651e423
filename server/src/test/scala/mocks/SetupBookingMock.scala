package com.agoda.bapi.server.utils

import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.message.{InitializeBookingServerStatus, InitializeBookingStatus, PropertySearchCriteria, PublishPrice}
import com.agoda.bapi.common.model.booking.{PaymentFlow, PaymentMethodDetailsV2, PaymentMethodIcon}
import com.agoda.bapi.common.model.car.{CarConfirmationData, CarOptions}
import com.agoda.bapi.common.model.cart.CartItemContext
import com.agoda.bapi.common.model.{ChargeOption, CurrencyOption, PaymentGroupCategory, WalletPromotion}
import com.agoda.bapi.common.token.flight.FlightToken
import com.agoda.bapi.server.addon.AddOnDefaults.propertySearchCriteria
import com.agoda.bapi.server.model.pricebreakdown.flights.MockFlightsPricingData
import com.agoda.bapi.server.model.{AncillariesData, BookingPropertiesData, FlightConfirmationData, ProductData}
import com.agoda.bapi.server.service.PapiPropertyStatus
import com.agoda.content.models.db.common.Provider
import com.agoda.content.models.db.information.Address
import com.agoda.content.models.db.reviews.{ReviewResponse, ReviewTotal, ReviewTotals}
import com.agoda.flights.client.v2.model.SearchResponseCurrencyPricing
import enumerations.SpecialRequestIds
import models.pricing.DiscountTypes.PercentDiscount
import models.pricing.RateCategory
import models.starfruit
import models.starfruit.PropertyPricingJsonProtocol.ResponseStateToken
import org.joda.time.DateTime
import org.scalatestplus.mockito.MockitoSugar
import transformers.{DFMetaResult, EnrichedBenefit, EnrichedCampaign, EnrichedCharge, EnrichedMasterRoom, EnrichedPricing, EnrichedPromotion, PriceInfos}

import scala.io.Source
import com.softwaremill.quicklens._
import enumerations.CampaignTypes.PromotionCode
import com.agoda.papi.enums.campaign.CampaignTypes
import com.agoda.papi.enums.campaign.InelegiblePromotionReasons.InvalidPromocode
import com.agoda.winterfell.output.MemberDetails
import models.starfruit.{DisplayBasis, DisplayPrice, DisplaySummary, SummaryElement}

import java.util.UUID
import scala.collection.mutable

trait SetupBookingMock extends MockitoSugar {
  import mocks.BookingMockHelper._
  private def getResouceAsString(path: String) = {
    val source = Source.fromFile(getClass.getResource(path).getPath)
    source.mkString
  }

  def mockBaseResponse: SetupBookingResponse =
    SetupBookingResponse(
      success = true,
      bookingResponse = Some(
        SetupBookingResponseInfo(
          products = mockProducts,
          paymentMethods = mockPaymentMethods,
          chargeCurrencyOptions = Seq(CurrencyOption(100, "USD"), CurrencyOption(3050, "THB")),
          emailMarketingRegulated = false,
          serverStatus = InitializeBookingServerStatus.apply(status = InitializeBookingStatus.Ok),
          bookingToken = bookingTokenData
        )
      )
    )

  def flightsData: Seq[FlightProductItem] =
    Seq(
      FlightProductItem(
        id = "1",
        content = getResouceAsString("/flight/response/flightsapi_mock_1.json"),
        submitToken = FlightToken(
          Some(
            "AAAA.AAAADGI234Rbx1+ayHmxJLPcKKuulFVxiJaegQYFk6FEOzCGI8a7Ul6boIHj9QD4sq2wtzod77L7h+DQvSiI78z9k+nSR4pLLbw0TCbYbV79Lkg7umwlMgNzS49RVtcveQRSGRXZ7sn8ryGyE5e1loiciLvA8zKuV4YT3IbpahJK5kBhjHsTTEC32+SM0Rmlsi/pKYxGavkYjW8pf+h8PLhNPu6aiNjl0oBe1X6dlp7pjS0EoXWBoFlq20l8WL/8kZgEJZrFUXIXKeRZgYH1Y4ic7v9TXAhnq9f4cZi+A7FK/8wqvZEj8vfqUcOcfm3c4JuOxYBKxbFEvMEziG1wOHtL2VPDlnkONlMIdqIPOO35u/3+dHtCGWvUfKbNNQiymwMzrXPr0bn97oIRL+JnQCg8JWymU4wskIBCN4s77kaJ7YBSYc6+Z7ot4zOD7AodfVTu7VQw+VjzVtF7SPdaDRDzNWj0M4xpuef+4cNU9OErfrIKa04BazQoX4NuXlN7b3vuEk5APX4OQZLIWvfG8lcoKH4rIspv/VZ4r2pGKzhPghstYSXQOV/Yy4d98gNuSwIJyUirngvy2JDFqr4oporylxj0A6x3alrQ2gvzjSp1Z3JF3MvR4f7YBL3OmOlnu5B5j2jay6WCV70QvVcUDSbcJ0DTpXcX0nMhMOcFhMsIPA4yXk/zNzAW2+XqN7a7VrEyZoAQAZs8ynsbCwHyo1AVeyclWj8JsE1WcShnK2AWuKjvOtWWrxMbfWQpBcaWxAlPPnsALZFEf7J0yHsWbdVPwtP/quuuUxogOJWVA3bE8aNW8uRDDUaEwrkOKJ3Vw8fNxSL+v9JMb89jLoRefAN2zx9CbrAge+IDomMYOVqfqIJDvF+cOULCnhHrCwOHR63xwGhIP/LO460I7SVJXrth99mkET+OzW6ArkusvOnoLV4DWwnf7ymbaNkhLiOfAr0vaCMPaZtfuNVh3Ig5O/8w1WBSaFWMWXRyltXBv4NlJXDKeaKkaCuF5gn1NhnWfn3HRcPt7JUmaHPEVhKMsf1SS1Qne5PrYllZcOlH7ZAeE3CG4QlKoX/oX4GA2wNXgqXzvMVmHVMn7OgJqFI6ILgl1nlnSmFQDbMKDJhqiq0D1fxoQ851KsvZDiclHzyeEG2Uy1N8NpEEEsIS4W9DjYwI7GKoSWAlSzfbbVTQtW0rHIH68voj0CIIx2HVeE1N/qEC4UHpNmQM1MUwZc6/al3ngpauvkpMMXxv9rv/upYJmJ8A5LEw5gYiV8d9+uOzmw8wqeK3dCH8vu+ZW6ITCec5HkAabUFP6wUbEKdEBSo2XTcODwL4oFeEgYmNiqGyl6C1YQUE7yliTcOG77XmxTDf2PPCYIWkARTpmnTwroJ/rgKtDD4/XIird9JkOvHqcDO1KsRyf6fhcrF6JITdhQqSPdqYRpCgsmJhxB5Rks7pqukiZCD1/kq+Duxj2Cz3LdyzInOQ7Wn5A3E+WNIBdEBSaf8na0OgYArPYqff817zx8G00woY4izBf9eW4R1Zti45LoDaa3yMRBkx2VBruQHQNbkJuCtm"
          )
        )
      )
    )

  def flightsDataWithPaymentFee: Seq[FlightProductItem] =
    Seq(
      FlightProductItem(
        id = "1",
        content = getResouceAsString("/flight/response/flightsapi_mock_5.json"),
        submitToken = FlightToken(
          Some(
            "AAAA.AAAADGI234Rbx1+ayHmxJLPcKKuulFVxiJaegQYFk6FEOzCGI8a7Ul6boIHj9QD4sq2wtzod77L7h+DQvSiI78z9k+nSR4pLLbw0TCbYbV79Lkg7umwlMgNzS49RVtcveQRSGRXZ7sn8ryGyE5e1loiciLvA8zKuV4YT3IbpahJK5kBhjHsTTEC32+SM0Rmlsi/pKYxGavkYjW8pf+h8PLhNPu6aiNjl0oBe1X6dlp7pjS0EoXWBoFlq20l8WL/8kZgEJZrFUXIXKeRZgYH1Y4ic7v9TXAhnq9f4cZi+A7FK/8wqvZEj8vfqUcOcfm3c4JuOxYBKxbFEvMEziG1wOHtL2VPDlnkONlMIdqIPOO35u/3+dHtCGWvUfKbNNQiymwMzrXPr0bn97oIRL+JnQCg8JWymU4wskIBCN4s77kaJ7YBSYc6+Z7ot4zOD7AodfVTu7VQw+VjzVtF7SPdaDRDzNWj0M4xpuef+4cNU9OErfrIKa04BazQoX4NuXlN7b3vuEk5APX4OQZLIWvfG8lcoKH4rIspv/VZ4r2pGKzhPghstYSXQOV/Yy4d98gNuSwIJyUirngvy2JDFqr4oporylxj0A6x3alrQ2gvzjSp1Z3JF3MvR4f7YBL3OmOlnu5B5j2jay6WCV70QvVcUDSbcJ0DTpXcX0nMhMOcFhMsIPA4yXk/zNzAW2+XqN7a7VrEyZoAQAZs8ynsbCwHyo1AVeyclWj8JsE1WcShnK2AWuKjvOtWWrxMbfWQpBcaWxAlPPnsALZFEf7J0yHsWbdVPwtP/quuuUxogOJWVA3bE8aNW8uRDDUaEwrkOKJ3Vw8fNxSL+v9JMb89jLoRefAN2zx9CbrAge+IDomMYOVqfqIJDvF+cOULCnhHrCwOHR63xwGhIP/LO460I7SVJXrth99mkET+OzW6ArkusvOnoLV4DWwnf7ymbaNkhLiOfAr0vaCMPaZtfuNVh3Ig5O/8w1WBSaFWMWXRyltXBv4NlJXDKeaKkaCuF5gn1NhnWfn3HRcPt7JUmaHPEVhKMsf1SS1Qne5PrYllZcOlH7ZAeE3CG4QlKoX/oX4GA2wNXgqXzvMVmHVMn7OgJqFI6ILgl1nlnSmFQDbMKDJhqiq0D1fxoQ851KsvZDiclHzyeEG2Uy1N8NpEEEsIS4W9DjYwI7GKoSWAlSzfbbVTQtW0rHIH68voj0CIIx2HVeE1N/qEC4UHpNmQM1MUwZc6/al3ngpauvkpMMXxv9rv/upYJmJ8A5LEw5gYiV8d9+uOzmw8wqeK3dCH8vu+ZW6ITCec5HkAabUFP6wUbEKdEBSo2XTcODwL4oFeEgYmNiqGyl6C1YQUE7yliTcOG77XmxTDf2PPCYIWkARTpmnTwroJ/rgKtDD4/XIird9JkOvHqcDO1KsRyf6fhcrF6JITdhQqSPdqYRpCgsmJhxB5Rks7pqukiZCD1/kq+Duxj2Cz3LdyzInOQ7Wn5A3E+WNIBdEBSaf8na0OgYArPYqff817zx8G00woY4izBf9eW4R1Zti45LoDaa3yMRBkx2VBruQHQNbkJuCtm"
          )
        )
      )
    )

  def flightsDataWithCart: Seq[FlightProductItem] =
    Seq(
      FlightProductItem(
        id = "1",
        content = getResouceAsString("/flight/response/flightsapi_mock_with_cart_1.json"),
        submitToken = FlightToken(
          Some(
            "AAAA.AAAADGI234Rbx1+ayHmxJLPcKKuulFVxiJaegQYFk6FEOzCGI8a7Ul6boIHj9QD4sq2wtzod77L7h+DQvSiI78z9k+nSR4pLLbw0TCbYbV79Lkg7umwlMgNzS49RVtcveQRSGRXZ7sn8ryGyE5e1loiciLvA8zKuV4YT3IbpahJK5kBhjHsTTEC32+SM0Rmlsi/pKYxGavkYjW8pf+h8PLhNPu6aiNjl0oBe1X6dlp7pjS0EoXWBoFlq20l8WL/8kZgEJZrFUXIXKeRZgYH1Y4ic7v9TXAhnq9f4cZi+A7FK/8wqvZEj8vfqUcOcfm3c4JuOxYBKxbFEvMEziG1wOHtL2VPDlnkONlMIdqIPOO35u/3+dHtCGWvUfKbNNQiymwMzrXPr0bn97oIRL+JnQCg8JWymU4wskIBCN4s77kaJ7YBSYc6+Z7ot4zOD7AodfVTu7VQw+VjzVtF7SPdaDRDzNWj0M4xpuef+4cNU9OErfrIKa04BazQoX4NuXlN7b3vuEk5APX4OQZLIWvfG8lcoKH4rIspv/VZ4r2pGKzhPghstYSXQOV/Yy4d98gNuSwIJyUirngvy2JDFqr4oporylxj0A6x3alrQ2gvzjSp1Z3JF3MvR4f7YBL3OmOlnu5B5j2jay6WCV70QvVcUDSbcJ0DTpXcX0nMhMOcFhMsIPA4yXk/zNzAW2+XqN7a7VrEyZoAQAZs8ynsbCwHyo1AVeyclWj8JsE1WcShnK2AWuKjvOtWWrxMbfWQpBcaWxAlPPnsALZFEf7J0yHsWbdVPwtP/quuuUxogOJWVA3bE8aNW8uRDDUaEwrkOKJ3Vw8fNxSL+v9JMb89jLoRefAN2zx9CbrAge+IDomMYOVqfqIJDvF+cOULCnhHrCwOHR63xwGhIP/LO460I7SVJXrth99mkET+OzW6ArkusvOnoLV4DWwnf7ymbaNkhLiOfAr0vaCMPaZtfuNVh3Ig5O/8w1WBSaFWMWXRyltXBv4NlJXDKeaKkaCuF5gn1NhnWfn3HRcPt7JUmaHPEVhKMsf1SS1Qne5PrYllZcOlH7ZAeE3CG4QlKoX/oX4GA2wNXgqXzvMVmHVMn7OgJqFI6ILgl1nlnSmFQDbMKDJhqiq0D1fxoQ851KsvZDiclHzyeEG2Uy1N8NpEEEsIS4W9DjYwI7GKoSWAlSzfbbVTQtW0rHIH68voj0CIIx2HVeE1N/qEC4UHpNmQM1MUwZc6/al3ngpauvkpMMXxv9rv/upYJmJ8A5LEw5gYiV8d9+uOzmw8wqeK3dCH8vu+ZW6ITCec5HkAabUFP6wUbEKdEBSo2XTcODwL4oFeEgYmNiqGyl6C1YQUE7yliTcOG77XmxTDf2PPCYIWkARTpmnTwroJ/rgKtDD4/XIird9JkOvHqcDO1KsRyf6fhcrF6JITdhQqSPdqYRpCgsmJhxB5Rks7pqukiZCD1/kq+Duxj2Cz3LdyzInOQ7Wn5A3E+WNIBdEBSaf8na0OgYArPYqff817zx8G00woY4izBf9eW4R1Zti45LoDaa3yMRBkx2VBruQHQNbkJuCtm"
          )
        )
      )
    )

  def flightsDataRoundTrip: Seq[FlightProductItem] =
    Seq(
      FlightProductItem(
        id = "1",
        content = getResouceAsString("/flight/response/flightsapi_mock_3.json"),
        submitToken = FlightToken(
          Some(
            "AAAA.AAAADKRX6LgrdB4ba4XrhmD0PJ+eCeCgb2h8tX0xU+8TvRYB0Mci6RVsxC8o3sgqZAATXdKZU77oo1aX1JaCXLOwE77a2szyeAi9REYOtSmwJlMWwMR4DU5WBv59feYJg7xV0fvk8xNvFZMAdX3vgjy9Vbc2FcXzPp6uVLd0vNC7uA3cj15w8x7qkT8HwIFnB/lqUXCNGd6PkDvDmoR19V5cp6gw+L1SgrWkcs85VxuOg/xY5T5HhkV52Pq/b7XCnPkZ4JPq+d18G7SzUOAcFAeKA5P+o3IB/gf/5lZnd8eoOfqBL/p58D3ZaG6Ag9S2vTPZUNAHc1X7/XRe5IclJc3HPIH8Is92prhYnQR+HJpIMqUfrTnhFkXn7mtjPObmfxdQg629tpnPJZHvVFmYxdyGZt+Bf8QIx+7QJ17vuABee9W088NHiqJJghft0HgCN+eOxFgMd2QJ067ountNZpqON92++hVglE5pbtgJUgh37P0qKBxhzVobwwXMneY8Vy7eOtO4ruJrWFUUAJUM5bnph2Sd8kAqqvKfXPGKgxxtm8iElUsI8ti2RDtYu7CCXbk+RgDKRf2s6y1KetV+oOUcgkNyJQ6bAT1kuAqr4R6K3ijWej7pJKY/V3GU0E1CYTyq125yADSVWkQWYItoBv40t2khpT496dkUEDhvAgQV9mR1ebiR7oB3OLs5geKekll9HLSDoogkc1gTEksn0iqSXqNW1i7pMOgN7D5jd1heL2326o3CeIMIJ+Qp2Ux30ak5wFgC6lIh+SXm3ZMEijLlwgXFuUgyw9DZypOutN/FnlLr5h91gjIrngtsTsyfgTV8aC2dHKi96uDSBQs2Olmb6u70AOb/rXkdJlKC/G7l5XMEBu8fr4KzvNSAjbwg/COy2kAW9fyxcacMFNNSDW1NQ5ier4cCbP/jxPCPZlQ3Dut2ejB+MvFN7hIMtnggTM2Ghc6HR9L7hmQKhBISTBLELj3oqUdSpXQYOr8R5tcEmR7hoFrUbuVfPBTS96aN3pPfKzTKqM+Mk7X4HPLslbSoMc9nNJAma814QSssf/ko+22dVIPYWvJRSpFhe6cKCwN8tBdHIbeRH4JPZbgN9Oh4+rpyOH3UcK6jDG/CzCra2JkZT3OkA+difctk5IiLCEztJ1w65cTzbTUidoM8GT1eqdnBAMppPKeFW/X2jcYKMeluNrwWi2ozE31II4CGyuktPIF26a9Dtrz7vEyt9bkP6M/gaAUc/aL/ZmgwbN+1xRVoHz5clwQAEHajqWbGENLwBQrzW9c2NPGc+9PzlrVX9Qk7rUJkVwJbSSUTzo8SiP6SwIkJqkO+6WwM8TSZdpBGVVK2xIlhz+pZlVKypsHGh0frP2FAFVRvxkh3eezwpGs="
          )
        )
      )
    )

  def flightsAgencyData: Seq[FlightProductItem] =
    Seq(
      FlightProductItem(
        id = "1",
        content = getResouceAsString("/flight/response/flightsapi_mock_2.json"),
//          "{\\n    \\\"isCompleted\\\": true,\\n    \\\"itinerary\\\": {\\n        \\\"id\\\": \\\"A:BKK_20191120_A:NRT__1-0-0_ECO___1727318587\\\",\\n        \\\"externalItineraryId\\\": \\\"\\\",\\n        \\\"lapInfantsAllowed\\\": false,\\n        \\\"availableSeats\\\": -1,\\n        \\\"passportRequired\\\": true,\\n        \\\"nationalityRequired\\\": true,\\n        \\\"passengerMinAge\\\": 15,\\n        \\\"totalTripDuration\\\": 23,\\n        \\\"localCurrency\\\": \\\"USD\\\",\\n        \\\"token\\\": \\\"AAAA.AAAADGKX3XI51kCaEa5Tc4a9KSQWxNI7wr6R+S1C02tu7rqdK8v7x8KM2/z3uEQNZfrT7SV7iq691JJzTr1GDIFprEMA9sPCLpc2ABTxrW4ZRQLFpOJapp/B5Xa91ksj4W6c9XhKPcmLHkONy2uc26nK/oXXjZuoCy2uPTa92ISP/tl8KSF33Qt/Oa3fB5+ORnsveyh9QBxOwM8lSy9vDJifbIG5epdD50Vhr12cfz2GD86Ji2zU/NWCOC9AwcIMBfvbRZoJGcPEidMaJDTk+LK66TnOJVtL4gE3P3pItTv0KmmSVR6rVCUVPm49x0WwKLKtDZASmVW9JiWXDEDF0v/E61jnk3eDJU31bO4lfSoc3e5mtTuov/VTEh0I3YGOx4i10kmLpqD7HlQBQu9M1FRaGex2SXdripwlUtlWMNMIWS4Dab0zH1mH7BP6Al8JUjxGOe9I1/wn7iBmrRdN3QdrOmYxZTsXH5mNmJg7Xtyw1kV77R3E6JDW+xud9bRe8k5+bYLBh0xa4H+sm5IhUt0NPf3c4/V6cWNzdQF4fM4JF1PB0H4J8cDTn4DLfiRaj9098YJIygMRjZ3QG1oS4QY5xMSaASxF8sfLcPnIe0Xybwu/uNAXr6QY7WhiLGhEAUrBa9HIfrjssaLKbco2HVlSepD36URuZfMPeU7tZXzzwF3hhLH8XpWVAH/zThysAjeRqgmUldeIGbRigIAuIZxgl8Cp8h7MmDtVvCeo1iBdNb/KjotNegp6/M8zdOniTWAo5wCQsuU5fBeKPJRvEgM29K4uiE5aVgNFOKNbm/6Ar6Vp4RBvGCBWcI1y9j60Q5EfRnKKuhc18FPztBaWsAdt9r/urq0IAvBRr1IGAp41tu4txnZM/mtJabLAfD0pkgqUJle5KNE/Hp83SPnAxfaPstvXA28EsH3CGwtdocIg3YmwG2kBNvoltI3S/qUrjITG/2iLMzlIwZhYdLLcTKMcJzIWIpr58Sm5a1mh/FU9cqicQavcIAnSZoDwls5ggvc9dD/vZNiELA0L1XLHvMetNB0+ENWrq567Pj6JWkxz9JzLVHKruf1jnltVuVocs/4k9rTTcKiiPelPcftke94eDbBMMg+bUZFpvajmRUj4Uc+npkTJRe+maTHHVwM6Qq+7k5CG0BeMD2yucoYllWri+D6QqVBTiQfRsjojKLQIBZCGWj97JKiW3DEGxjSyqkf4DpOQncy/osDvPM7QJMMOyZ/q8m3csGz3RQEc5ytUN1gw+tVTHU0ITtGUo/smUGpSWCzxS0yzy2KsD/wePh9uZuf53ryq2ujVasn9z6DzVhv9NIwGKdGNDcT/OW2fHYelqe+xwf4r3o5u13Lvhb2QeMadY+6NIrSbgOEBDLr8YIzhXA4ToX3SeKyQ5yenKmRc22tbGXf3neQwKMVrkZYWG2qE8DCQhEs=\\\",\\n        \\\"ticketingAirline\\\": \\\"UO\\\",\\n        \\\"voidWindowClose\\\": null,\\n        \\\"price\\\": {\\n            \\\"THB\\\": {\\n                \\\"charges\\\": [\\n                    {\\n                        \\\"type\\\": \\\"Fare\\\",\\n                        \\\"breakDown\\\": [\\n                            {\\n                                \\\"basis\\\": \\\"PAPB\\\",\\n                                \\\"option\\\": \\\"Mandatory\\\",\\n                                \\\"quantity\\\": 1,\\n                                \\\"price\\\": {\\n                                    \\\"exc\\\": 3330.36,\\n                                    \\\"inc\\\": 7108.84\\n                                },\\n                                \\\"total\\\": {\\n                                    \\\"exc\\\": 3330.36,\\n                                    \\\"inc\\\": 7108.84\\n                                }\\n                            }\\n                        ],\\n                        \\\"total\\\": {\\n                            \\\"exc\\\": 3330.36,\\n                            \\\"inc\\\": 7108.84\\n                        }\\n                    },\\n                    {\\n                        \\\"type\\\": \\\"TaxAndFee\\\",\\n                        \\\"breakDown\\\": [\\n                            {\\n                                \\\"basis\\\": \\\"PB\\\",\\n                                \\\"option\\\": \\\"Mandatory\\\",\\n                                \\\"quantity\\\": 1,\\n                                \\\"price\\\": {\\n                                    \\\"exc\\\": 3778.48,\\n                                    \\\"inc\\\": 3778.48\\n                                },\\n                                \\\"total\\\": {\\n                                    \\\"exc\\\": 3778.48,\\n                                    \\\"inc\\\": 3778.48\\n                                }\\n                            },\\n                            {\\n                                \\\"basis\\\": \\\"PAPB\\\",\\n                                \\\"option\\\": \\\"Mandatory\\\",\\n                                \\\"quantity\\\": 1,\\n                                \\\"price\\\": {\\n                                    \\\"exc\\\": 3778.48,\\n                                    \\\"inc\\\": 3778.48\\n                                },\\n                                \\\"total\\\": {\\n                                    \\\"exc\\\": 3778.48,\\n                                    \\\"inc\\\": 3778.48\\n                                }\\n                            }\\n                        ],\\n                        \\\"total\\\": {\\n                            \\\"exc\\\": 3778.48,\\n                            \\\"inc\\\": 3778.48\\n                        }\\n                    }\\n                ],\\n                \\\"display\\\": {\\n                    \\\"perBook\\\": {\\n                        \\\"exclusive\\\": 3330.36,\\n                        \\\"allInclusive\\\": 7108.84,\\n                        \\\"type\\\": null\\n                    },\\n                    \\\"perPax\\\": [\\n                        {\\n                            \\\"exclusive\\\": 3330.36,\\n                            \\\"allInclusive\\\": 7108.84,\\n                            \\\"type\\\": \\\"ADT\\\"\\n                        }\\n                    ]\\n                },\\n                \\\"paymentModel\\\": 2,\\n                \\\"acceptedCreditCards\\\": [{\\\"creditCardTypeId\\\": 1,\\n                \\\"creditCardFeeAmount\\\": 0.0},\\n                {\\\"creditCardTypeId\\\": 2,\\n                \\\"creditCardFeeAmount\\\": 0.0}]\\n                }\\n        },\\n    \\\"prices\\\": {},\\n        \\\"package\\\": null,\\n        \\\"slice\\\": [\\n            {\\n                \\\"id\\\": 1,\\n                \\\"duration\\\": 1425,\\n                \\\"overnightFlight\\\": false,\\n                \\\"segments\\\": [\\n                    {\\n                        \\\"id\\\": 1,\\n                        \\\"arrivalDateTime\\\": \\\"2019-11-20T23:10:00\\\",\\n                        \\\"layoverAfter\\\": {\\n                            \\\"duration\\\": 890\\n                        },\\n                        \\\"departDateTime\\\": \\\"2019-11-20T19:20:00\\\",\\n                        \\\"destinationAirport\\\": \\\"HKG\\\",\\n                        \\\"duration\\\": 230,\\n                        \\\"aircraftCode\\\": \\\"320\\\",\\n                        \\\"flightNumber\\\": \\\"703\\\",\\n                        \\\"carrierCode\\\": \\\"UO\\\",\\n                        \\\"originAirport\\\": \\\"BKK\\\",\\n                        \\\"bkgClass\\\": \\\"W\\\",\\n                        \\\"cabinClassCode\\\": \\\"ECO\\\",\\n                        \\\"genericSeatAssignCost\\\": {\\n                            \\\"amount\\\": 0.0,\\n                            \\\"currency\\\": \\\"USD\\\"\\n                        },\\n                        \\\"genericSeatAssignFlag\\\": false,\\n                        \\\"numSeats\\\": -1,\\n                        \\\"seatMapAvailable\\\": false,\\n                        \\\"seatSelectionAllowed\\\": false,\\n                        \\\"allowStandby\\\": true,\\n                        \\\"allowCabinUpgrades\\\": true,\\n                        \\\"allowPreferredSeating\\\": true,\\n                        \\\"allowPriorityBoarding\\\": true,\\n                        \\\"baggageFee\\\": 0.0,\\n                        \\\"cabinClassContent\\\": {\\n                            \\\"cabinClass\\\": \\\"ECO\\\",\\n                            \\\"cabinName\\\": \\\"Economy Class\\\"\\n                        },\\n                        \\\"carrierContent\\\": {\\n                            \\\"carrierIcon\\\": \\\"https://img.agoda.net/images/mvc/default/airlines/UO.png\\\",\\n                            \\\"carrierCode\\\": \\\"UO\\\",\\n                            \\\"carrierName\\\": \\\"HK Express\\\"\\n                        },\\n                        \\\"airportContent\\\": {\\n                            \\\"departureAirportName\\\": \\\"Suvarnabhumi International Airport\\\",\\n                            \\\"arrivalAirportName\\\": \\\"Hong Kong International Airport\\\"\\n                        },\\n                        \\\"aircraftContent\\\": {\\n                            \\\"aircraftCode\\\": \\\"320\\\",\\n                            \\\"aircraftName\\\": \\\"Airbus A320\\\"\\n                        },\\n                        \\\"operatingCarrierCode\\\": \\\"UO\\\",\\n                        \\\"operatingCarrierContent\\\": {\\n                            \\\"carrierIcon\\\": \\\"https://img.agoda.net/images/mvc/default/airlines/UO.png\\\",\\n                            \\\"carrierCode\\\": \\\"UO\\\",\\n                            \\\"carrierName\\\": \\\"HK Express\\\"\\n                        }\\n                    },\\n                    {\\n                        \\\"id\\\": 2,\\n                        \\\"arrivalDateTime\\\": \\\"2019-11-21T19:05:00\\\",\\n                        \\\"layoverAfter\\\": null,\\n                        \\\"departDateTime\\\": \\\"2019-11-21T14:00:00\\\",\\n                        \\\"destinationAirport\\\": \\\"NRT\\\",\\n                        \\\"duration\\\": 305,\\n                        \\\"aircraftCode\\\": \\\"321\\\",\\n                        \\\"flightNumber\\\": \\\"650\\\",\\n                        \\\"carrierCode\\\": \\\"UO\\\",\\n                        \\\"originAirport\\\": \\\"HKG\\\",\\n                        \\\"bkgClass\\\": \\\"O\\\",\\n                        \\\"cabinClassCode\\\": \\\"ECO\\\",\\n                        \\\"genericSeatAssignCost\\\": {\\n                            \\\"amount\\\": 0.0,\\n                            \\\"currency\\\": \\\"USD\\\"\\n                        },\\n                        \\\"genericSeatAssignFlag\\\": false,\\n                        \\\"numSeats\\\": -1,\\n                        \\\"seatMapAvailable\\\": false,\\n                        \\\"seatSelectionAllowed\\\": false,\\n                        \\\"allowStandby\\\": true,\\n                        \\\"allowCabinUpgrades\\\": true,\\n                        \\\"allowPreferredSeating\\\": true,\\n                        \\\"allowPriorityBoarding\\\": true,\\n                        \\\"baggageFee\\\": 0.0,\\n                        \\\"cabinClassContent\\\": {\\n                            \\\"cabinClass\\\": \\\"ECO\\\",\\n                            \\\"cabinName\\\": \\\"Economy Class\\\"\\n                        },\\n                        \\\"carrierContent\\\": {\\n                            \\\"carrierIcon\\\": \\\"https://img.agoda.net/images/mvc/default/airlines/UO.png\\\",\\n                            \\\"carrierCode\\\": \\\"UO\\\",\\n                            \\\"carrierName\\\": \\\"HK Express\\\"\\n                        },\\n                        \\\"airportContent\\\": {\\n                            \\\"departureAirportName\\\": \\\"Hong Kong International Airport\\\",\\n                            \\\"arrivalAirportName\\\": \\\"Narita International Airport\\\"\\n                        },\\n                        \\\"aircraftContent\\\": {\\n                            \\\"aircraftCode\\\": \\\"321\\\",\\n                            \\\"aircraftName\\\": \\\"Airbus A321\\\"\\n                        },\\n                        \\\"operatingCarrierCode\\\": \\\"UO\\\",\\n                        \\\"operatingCarrierContent\\\": {\\n                            \\\"carrierIcon\\\": \\\"https://img.agoda.net/images/mvc/default/airlines/UO.png\\\",\\n                            \\\"carrierCode\\\": \\\"UO\\\",\\n                            \\\"carrierName\\\": \\\"HK Express\\\"\\n                        }\\n                    }\\n                ],\\n                \\\"freeBags\\\": [],\\n                \\\"cancellationPolicies\\\": [],\\n                \\\"exchangePolicies\\\": []\\n            }\\n        ],\\n        \\\"channel\\\": 10001,\\n        \\\"paymentModel\\\": 2,\\n        \\\"subSupplierId\\\": 30002,\\n        \\\"cancellationPolicies\\\": [],\\n        \\\"exchangePolicies\\\": [],\\n        \\\"hackerFare\\\": false,\\n        \\\"supplierAmount\\\": {\\n          \\\"total\\\": {\\n            \\\"currency\\\": \\\"THB\\\",\\n            \\\"amount\\\": 100.0\\n          },\\n            \\\"perSlices\\\": []\n        }\\n    },\\n    \\\"passengers\\\": [\\n        {\\n            \\\"numberOfPassengers\\\": 1,\\n            \\\"PassengerType\\\": \\\"ADT\\\"\\n        }\\n    ],\\n    \\\"priceChange\\\": null,\\n    \\\"retry\\\": {}\\n}",
        submitToken = FlightToken(
          Some(
            "AAAA.AAAADGI234Rbx1+ayHmxJLPcKKuulFVxiJaegQYFk6FEOzCGI8a7Ul6boIHj9QD4sq2wtzod77L7h+DQvSiI78z9k+nSR4pLLbw0TCbYbV79Lkg7umwlMgNzS49RVtcveQRSGRXZ7sn8ryGyE5e1loiciLvA8zKuV4YT3IbpahJK5kBhjHsTTEC32+SM0Rmlsi/pKYxGavkYjW8pf+h8PLhNPu6aiNjl0oBe1X6dlp7pjS0EoXWBoFlq20l8WL/8kZgEJZrFUXIXKeRZgYH1Y4ic7v9TXAhnq9f4cZi+A7FK/8wqvZEj8vfqUcOcfm3c4JuOxYBKxbFEvMEziG1wOHtL2VPDlnkONlMIdqIPOO35u/3+dHtCGWvUfKbNNQiymwMzrXPr0bn97oIRL+JnQCg8JWymU4wskIBCN4s77kaJ7YBSYc6+Z7ot4zOD7AodfVTu7VQw+VjzVtF7SPdaDRDzNWj0M4xpuef+4cNU9OErfrIKa04BazQoX4NuXlN7b3vuEk5APX4OQZLIWvfG8lcoKH4rIspv/VZ4r2pGKzhPghstYSXQOV/Yy4d98gNuSwIJyUirngvy2JDFqr4oporylxj0A6x3alrQ2gvzjSp1Z3JF3MvR4f7YBL3OmOlnu5B5j2jay6WCV70QvVcUDSbcJ0DTpXcX0nMhMOcFhMsIPA4yXk/zNzAW2+XqN7a7VrEyZoAQAZs8ynsbCwHyo1AVeyclWj8JsE1WcShnK2AWuKjvOtWWrxMbfWQpBcaWxAlPPnsALZFEf7J0yHsWbdVPwtP/quuuUxogOJWVA3bE8aNW8uRDDUaEwrkOKJ3Vw8fNxSL+v9JMb89jLoRefAN2zx9CbrAge+IDomMYOVqfqIJDvF+cOULCnhHrCwOHR63xwGhIP/LO460I7SVJXrth99mkET+OzW6ArkusvOnoLV4DWwnf7ymbaNkhLiOfAr0vaCMPaZtfuNVh3Ig5O/8w1WBSaFWMWXRyltXBv4NlJXDKeaKkaCuF5gn1NhnWfn3HRcPt7JUmaHPEVhKMsf1SS1Qne5PrYllZcOlH7ZAeE3CG4QlKoX/oX4GA2wNXgqXzvMVmHVMn7OgJqFI6ILgl1nlnSmFQDbMKDJhqiq0D1fxoQ851KsvZDiclHzyeEG2Uy1N8NpEEEsIS4W9DjYwI7GKoSWAlSzfbbVTQtW0rHIH68voj0CIIx2HVeE1N/qEC4UHpNmQM1MUwZc6/al3ngpauvkpMMXxv9rv/upYJmJ8A5LEw5gYiV8d9+uOzmw8wqeK3dCH8vu+ZW6ITCec5HkAabUFP6wUbEKdEBSo2XTcODwL4oFeEgYmNiqGyl6C1YQUE7yliTcOG77XmxTDf2PPCYIWkARTpmnTwroJ/rgKtDD4/XIird9JkOvHqcDO1KsRyf6fhcrF6JITdhQqSPdqYRpCgsmJhxB5Rks7pqukiZCD1/kq+Duxj2Cz3LdyzInOQ7Wn5A3E+WNIBdEBSaf8na0OgYArPYqff817zx8G00woY4izBf9eW4R1Zti45LoDaa3yMRBkx2VBruQHQNbkJuCtm"
          )
        )
      )
    )

  def flightsDataWithVirtualInterlining: Seq[FlightProductItem] =
    Seq(
      FlightProductItem(
        id = "1",
        content = getResouceAsString("/flight/response/flightsapi_mock_4.json"),
        submitToken = FlightToken(
          Some(
            "AAAA.AAAADGI234Rbx1+ayHmxJLPcKKuulFVxiJaegQYFk6FEOzCGI8a7Ul6boIHj9QD4sq2wtzod77L7h+DQvSiI78z9k+nSR4pLLbw0TCbYbV79Lkg7umwlMgNzS49RVtcveQRSGRXZ7sn8ryGyE5e1loiciLvA8zKuV4YT3IbpahJK5kBhjHsTTEC32+SM0Rmlsi/pKYxGavkYjW8pf+h8PLhNPu6aiNjl0oBe1X6dlp7pjS0EoXWBoFlq20l8WL/8kZgEJZrFUXIXKeRZgYH1Y4ic7v9TXAhnq9f4cZi+A7FK/8wqvZEj8vfqUcOcfm3c4JuOxYBKxbFEvMEziG1wOHtL2VPDlnkONlMIdqIPOO35u/3+dHtCGWvUfKbNNQiymwMzrXPr0bn97oIRL+JnQCg8JWymU4wskIBCN4s77kaJ7YBSYc6+Z7ot4zOD7AodfVTu7VQw+VjzVtF7SPdaDRDzNWj0M4xpuef+4cNU9OErfrIKa04BazQoX4NuXlN7b3vuEk5APX4OQZLIWvfG8lcoKH4rIspv/VZ4r2pGKzhPghstYSXQOV/Yy4d98gNuSwIJyUirngvy2JDFqr4oporylxj0A6x3alrQ2gvzjSp1Z3JF3MvR4f7YBL3OmOlnu5B5j2jay6WCV70QvVcUDSbcJ0DTpXcX0nMhMOcFhMsIPA4yXk/zNzAW2+XqN7a7VrEyZoAQAZs8ynsbCwHyo1AVeyclWj8JsE1WcShnK2AWuKjvOtWWrxMbfWQpBcaWxAlPPnsALZFEf7J0yHsWbdVPwtP/quuuUxogOJWVA3bE8aNW8uRDDUaEwrkOKJ3Vw8fNxSL+v9JMb89jLoRefAN2zx9CbrAge+IDomMYOVqfqIJDvF+cOULCnhHrCwOHR63xwGhIP/LO460I7SVJXrth99mkET+OzW6ArkusvOnoLV4DWwnf7ymbaNkhLiOfAr0vaCMPaZtfuNVh3Ig5O/8w1WBSaFWMWXRyltXBv4NlJXDKeaKkaCuF5gn1NhnWfn3HRcPt7JUmaHPEVhKMsf1SS1Qne5PrYllZcOlH7ZAeE3CG4QlKoX/oX4GA2wNXgqXzvMVmHVMn7OgJqFI6ILgl1nlnSmFQDbMKDJhqiq0D1fxoQ851KsvZDiclHzyeEG2Uy1N8NpEEEsIS4W9DjYwI7GKoSWAlSzfbbVTQtW0rHIH68voj0CIIx2HVeE1N/qEC4UHpNmQM1MUwZc6/al3ngpauvkpMMXxv9rv/upYJmJ8A5LEw5gYiV8d9+uOzmw8wqeK3dCH8vu+ZW6ITCec5HkAabUFP6wUbEKdEBSo2XTcODwL4oFeEgYmNiqGyl6C1YQUE7yliTcOG77XmxTDf2PPCYIWkARTpmnTwroJ/rgKtDD4/XIird9JkOvHqcDO1KsRyf6fhcrF6JITdhQqSPdqYRpCgsmJhxB5Rks7pqukiZCD1/kq+Duxj2Cz3LdyzInOQ7Wn5A3E+WNIBdEBSaf8na0OgYArPYqff817zx8G00woY4izBf9eW4R1Zti45LoDaa3yMRBkx2VBruQHQNbkJuCtm"
          )
        )
      )
    )

  def flightsDataProduct: Seq[FlightConfirmationData] =
    Seq(
      FlightConfirmationData(
        id = "1",
        token = "",
        submitToken = FlightToken(
          Some(
            "AAAA.AAAADGI234Rbx1+ayHmxJLPcKKuulFVxiJaegQYFk6FEOzCGI8a7Ul6boIHj9QD4sq2wtzod77L7h+DQvSiI78z9k+nSR4pLLbw0TCbYbV79Lkg7umwlMgNzS49RVtcveQRSGRXZ7sn8ryGyE5e1loiciLvA8zKuV4YT3IbpahJK5kBhjHsTTEC32+SM0Rmlsi/pKYxGavkYjW8pf+h8PLhNPu6aiNjl0oBe1X6dlp7pjS0EoXWBoFlq20l8WL/8kZgEJZrFUXIXKeRZgYH1Y4ic7v9TXAhnq9f4cZi+A7FK/8wqvZEj8vfqUcOcfm3c4JuOxYBKxbFEvMEziG1wOHtL2VPDlnkONlMIdqIPOO35u/3+dHtCGWvUfKbNNQiymwMzrXPr0bn97oIRL+JnQCg8JWymU4wskIBCN4s77kaJ7YBSYc6+Z7ot4zOD7AodfVTu7VQw+VjzVtF7SPdaDRDzNWj0M4xpuef+4cNU9OErfrIKa04BazQoX4NuXlN7b3vuEk5APX4OQZLIWvfG8lcoKH4rIspv/VZ4r2pGKzhPghstYSXQOV/Yy4d98gNuSwIJyUirngvy2JDFqr4oporylxj0A6x3alrQ2gvzjSp1Z3JF3MvR4f7YBL3OmOlnu5B5j2jay6WCV70QvVcUDSbcJ0DTpXcX0nMhMOcFhMsIPA4yXk/zNzAW2+XqN7a7VrEyZoAQAZs8ynsbCwHyo1AVeyclWj8JsE1WcShnK2AWuKjvOtWWrxMbfWQpBcaWxAlPPnsALZFEf7J0yHsWbdVPwtP/quuuUxogOJWVA3bE8aNW8uRDDUaEwrkOKJ3Vw8fNxSL+v9JMb89jLoRefAN2zx9CbrAge+IDomMYOVqfqIJDvF+cOULCnhHrCwOHR63xwGhIP/LO460I7SVJXrth99mkET+OzW6ArkusvOnoLV4DWwnf7ymbaNkhLiOfAr0vaCMPaZtfuNVh3Ig5O/8w1WBSaFWMWXRyltXBv4NlJXDKeaKkaCuF5gn1NhnWfn3HRcPt7JUmaHPEVhKMsf1SS1Qne5PrYllZcOlH7ZAeE3CG4QlKoX/oX4GA2wNXgqXzvMVmHVMn7OgJqFI6ILgl1nlnSmFQDbMKDJhqiq0D1fxoQ851KsvZDiclHzyeEG2Uy1N8NpEEEsIS4W9DjYwI7GKoSWAlSzfbbVTQtW0rHIH68voj0CIIx2HVeE1N/qEC4UHpNmQM1MUwZc6/al3ngpauvkpMMXxv9rv/upYJmJ8A5LEw5gYiV8d9+uOzmw8wqeK3dCH8vu+ZW6ITCec5HkAabUFP6wUbEKdEBSo2XTcODwL4oFeEgYmNiqGyl6C1YQUE7yliTcOG77XmxTDf2PPCYIWkARTpmnTwroJ/rgKtDD4/XIird9JkOvHqcDO1KsRyf6fhcrF6JITdhQqSPdqYRpCgsmJhxB5Rks7pqukiZCD1/kq+Duxj2Cz3LdyzInOQ7Wn5A3E+WNIBdEBSaf8na0OgYArPYqff817zx8G00woY4izBf9eW4R1Zti45LoDaa3yMRBkx2VBruQHQNbkJuCtm"
          )
        ),
        isCompleted = true,
        hasFlight = true,
        hasContent = true,
        isHackerFare = false,
        packageRequest = None,
        flightPricing = Some(
          Map(
            "HKD" -> SearchResponseCurrencyPricing(
              charges = MockFlightsPricingData.flightPricing,
              display = MockFlightsPricingData.chargeTotal,
              discount = None,
              crossedOutDisplay = None,
              paymentModel = 1,
              acceptedCreditCards = None
            )
          )
        ),
        priceChange = Some(MockFlightsPricingData.priceChangeResponse),
        flightItinerary = None,
        paxNumberByType = Map.empty,
        campaignInfo = Some(MockFlightsPricingData.promotionInfoResponse)
      )
    )

  def carProductData: Seq[CarConfirmationData] =
    Seq(
      CarConfirmationData(
        id = "Car_2",
        content = "",
        pollingToken = None,
        isCompleted = true,
        hasContent = true,
        cartItemContext = Some(CartItemContext("cartItemId")),
        carInfo = mock[CarOptions],
        carBookingPricing = None,
        customerPolicyInfo = None,
        currentCancellationPolicy = None
      )
    )

  def bookingTokenData: Option[TokenMessage] =
    Some(
      TokenMessage(
        token = "testToken",
        version = 3
      )
    )

  def propertyProductItem(): PropertyProductItem =
    PropertyProductItem(
      id = "1",
      displayPrice = None,
      specialRequestOptions = Seq[Int](
        SpecialRequestIds.SmokingRoom.i,
        SpecialRequestIds.TwinBed.i
      ),
      content =
        Source.fromInputStream(getClass.getResourceAsStream("/mock/papiResponse_mock.json")).getLines().mkString(""),
      packageRequest = None
    )

  def bookingPropertyProduct(): BookingPropertiesData =
    BookingPropertiesData(
      id = "1",
      "",
      papiProperties = Some(
        transformers.Properties(
          property = Seq(baseProperty()),
          debug = None,
          dfMetaResult = DFMetaResult(Some(ResponseStateToken("", true, Some("pricingRequestId-1"))))
        )
      ),
      packageRequest = None,
      papiPropertyStatus = PapiPropertyStatus.Ok,
      selectedChargeOption = None,
      cartItemContext = Some(CartItemContext("cartItemId"))
    )
  def bookingPropertyProductWithNoMasterRoom(): BookingPropertiesData =
    BookingPropertiesData(
      id = "1",
      "",
      papiProperties = Some(
        transformers.Properties(
          property = Seq(baseProperty().copy(masterRooms = Nil)),
          debug = None,
          dfMetaResult = DFMetaResult(Some(ResponseStateToken("", true, Some("pricingRequestId-1"))))
        )
      ),
      packageRequest = None,
      papiPropertyStatus = PapiPropertyStatus.Ok,
      selectedChargeOption = None
    )
  def bookingPropertyProductWithMasterRoomNoChildRoom(): BookingPropertiesData =
    BookingPropertiesData(
      id = "1",
      "",
      papiProperties = Some(
        transformers.Properties(
          property = Seq(baseProperty().copy(masterRooms = Seq(baseMasterRoom().copy(childrenRooms = Nil)))),
          debug = None,
          dfMetaResult = DFMetaResult(Some(ResponseStateToken("", true, Some("pricingRequestId-1"))))
        )
      ),
      packageRequest = None,
      papiPropertyStatus = PapiPropertyStatus.Ok,
      selectedChargeOption = None
    )

  val sixMonthsFromNow = DateTime.now().plusMonths(6)
  val mockPriceInfo    = mock[PriceInfos]
  val mockRateCategory = mock[RateCategory]

  def bookingPropertyProductWithMasterRoomNoChildRoomForPriceFreeze(): BookingPropertiesData = {

    val summaryElement = SummaryElement(
      chargeTotal = DisplayPrice(974.0, 409.0),
      rebateTotal = DisplayPrice(29.0, 29.0),
      rebateExtraBed = DisplayPrice(0.0, 0.0),
      displayTotal = DisplayPrice(945.0, 380.0),
      pseudoCoupon = DisplayPrice(0.0, 0.0),
      originalTotal = DisplayPrice(974.0, 409.0)
    ) // For 1 room and 1 night booking only

    val enrichedPricing = EnrichedPricing(
      display = mock[DisplayBasis],
      crossedOut = mock[DisplayBasis],
      options = mock[Seq[EnrichedCharge]],
      charges = mock[Seq[EnrichedCharge]],
      displaySummary = DisplaySummary(
        perBook = summaryElement,
        perRoomPerBook = summaryElement,
        perRoomPerNight = summaryElement,
        perNight = summaryElement
      )
    )

    BookingPropertiesData(
      id = "1",
      "",
      papiProperties = Some(
        transformers.Properties(
          property = Seq(
            baseProperty()
              .copy(
                propertyId = 233,
                masterRooms = Seq(
                  baseMasterRoom().copy(
                    childrenRooms = List(
                      baseChildrenRoom()
                        .copy(
                          typeId = Some(354),
                          supplierId = Some(23709),
                          masterTypeId = Some(74522),
                          roomName = Some("super deluxe"),
                          promotions = Some(
                            EnrichedPromotion(
                              12,
                              "",
                              "",
                              "",
                              "",
                              starfruit.Discount(12.3, PercentDiscount),
                              applicableUntil = Some(sixMonthsFromNow)
                            )
                          ),
                          benefits = Seq(
                            EnrichedBenefit(1, "hair dryer", symbol = "E", remark = "r", value = 462.32),
                            EnrichedBenefit(2, "breakfast", symbol = "E", remark = "r", value = 462.32)
                          ),
                          priceInfos = Map("THB" -> mockPriceInfo),
                          availableRooms = Some(6),
                          pricing = Map[String, EnrichedPricing](
                            "THB" -> enrichedPricing,
                            "JPY" -> enrichedPricing
                          )
                        )
                        .modify(_.payment.each.cancellation.code)
                        .setTo("365D100_100P")
                    ),
                    typeId = 74522,
                    name = Some("super deluxe")
                  )
                )
              )
              .modify(_.booking.each.rooms.each.rateCategory.id)
              .setTo(4725365)
          ),
          debug = None,
          dfMetaResult = DFMetaResult(Some(ResponseStateToken("", true, Some("pricingRequestId-1"))))
        )
      ),
      packageRequest = None,
      papiPropertyStatus = PapiPropertyStatus.Ok,
      selectedChargeOption = None
    )
  }

  def bookingPropertyProductForCFAR(): BookingPropertiesData = {

    val summaryElement = SummaryElement(
      chargeTotal = DisplayPrice(974.0, 409.0),
      rebateTotal = DisplayPrice(29.0, 29.0),
      rebateExtraBed = DisplayPrice(0.0, 0.0),
      displayTotal = DisplayPrice(945.0, 380.0),
      pseudoCoupon = DisplayPrice(0.0, 0.0),
      originalTotal = DisplayPrice(974.0, 409.0)
    ) // For 1 room and 1 night booking only

    val enrichedPricing = EnrichedPricing(
      display = mock[DisplayBasis],
      crossedOut = mock[DisplayBasis],
      options = mock[Seq[EnrichedCharge]],
      charges = mock[Seq[EnrichedCharge]],
      displaySummary = DisplaySummary(
        perBook = summaryElement,
        perRoomPerBook = summaryElement,
        perRoomPerNight = summaryElement,
        perNight = summaryElement
      )
    )

    val childRoom = baseChildrenRoom().copy(
      pricing = Map(
        "HKD" -> enrichedPricing
      )
    )

    val property = baseProperty().copy(
      reviews = Some(
        ReviewResponse(
          cumulative = Some(
            ReviewTotals(
              propertyId = 1L,
              providers = Vector(
                Provider(
                  id = 1L,
                  default = true,
                  totalIndex = 1
                )
              ),
              totals = Vector(
                ReviewTotal(
                  providerId = Some(1),
                  reviewCount = Some(100),
                  reviewCommentsCount = Some(56),
                  demographic = None,
                  score = Some(9.8),
                  scoreText = None,
                  maxScore = Some(10),
                  countText = None
                )
              )
            )
          ),
          commentary = None,
          demographics = None,
          summaries = None,
          filters = None,
          positiveMentions = None,
          counters = None
        )
      ),
      infoSummary = Some(
        baseProperty().infoSummary.get.copy(
          displayName = "International Space Station",
          address = Some(
            Address(
              country = None,
              city = Some("Earth-1"),
              state = None,
              area = None,
              address1 = None,
              address2 = None,
              postalCode = None,
              GmtOffset = None,
              utcOffset = None,
              countryId = None,
              cityId = None,
              areaId = None,
              countryCode = Some("Orbit-1002MA"),
              stateId = None
            )
          )
        )
      ),
      masterRooms = Seq(
        baseProperty().masterRooms.head.copy(
          childrenRooms = List(childRoom)
        )
      )
    )

    bookingPropertyProduct().copy(
      papiProperties = Some(
        transformers.Properties(
          property = Seq(property),
          debug = None,
          dfMetaResult = DFMetaResult(Some(ResponseStateToken("", true, Some("pricingRequestId-1"))))
        )
      )
    )
  }

  def bookingPropertyProductForCWS(priceAdjustmentId: Option[Long]): BookingPropertiesData = {
    val childrenRoom = baseChildrenRoom().copy(priceAdjustmentId = priceAdjustmentId)
    val property = baseProperty().copy(
      masterRooms = Seq(baseMasterRoom().copy(childrenRooms = List(childrenRoom))),
      rooms = Seq(childrenRoom)
    )
    bookingPropertyProduct()
      .copy(papiProperties = Some(transformers.Properties(property = Seq(property), debug = None)))
  }

  def bookingPropertyWithNoMasterRoom(): BookingPropertiesData = {
    val propertyWithNoMasterRoom = baseProperty().copy(masterRooms = Seq.empty, rooms = Seq.empty)
    BookingPropertiesData(
      id = "1",
      "",
      papiProperties = Some(
        transformers.Properties(
          property = Seq(propertyWithNoMasterRoom),
          debug = None,
          dfMetaResult = DFMetaResult(Some(ResponseStateToken("", true, Some("pricingRequestId-1"))))
        )
      ),
      packageRequest = None,
      papiPropertyStatus = PapiPropertyStatus.Ok,
      selectedChargeOption = None
    )
  }

  def bookingPropertyProductWithCampaignPromotion(): BookingPropertiesData = {
    val childrenRoomWithCampaignPromotion = List(
      baseChildrenRoom().copy(
        campaignPromotions = Some(
          List(
            enrichedPartnerCampaign
          )
        )
      )
    )
    val propertyWithCampaignPromotion = baseProperty().copy(masterRooms =
      Seq[EnrichedMasterRoom](baseMasterRoom().copy(childrenRooms = childrenRoomWithCampaignPromotion))
    )
    BookingPropertiesData(
      id = "2",
      "",
      papiProperties = Some(
        transformers.Properties(
          property = Seq(propertyWithCampaignPromotion),
          debug = None,
          dfMetaResult = DFMetaResult(Some(ResponseStateToken("", true, Some("pricingRequestId-1"))))
        )
      ),
      packageRequest = None,
      papiPropertyStatus = PapiPropertyStatus.Ok,
      selectedChargeOption = None,
      cartItemContext = Some(CartItemContext("cartItemId"))
    )
  }

  def bookingPropertyProductWithPublishPrice(): BookingPropertiesData = {
    bookingPropertyProductForCFAR().copy(
      propertySearchCriteria = Some(
        propertySearchCriteria.copy(publishPrice = Some(PublishPrice(100.0, "USD")))
      )
    )
  }

  def mockProducts: ProductItems =
    ProductItems(
      properties = Seq(propertyProductItem()),
      flights = flightsData,
      cars = Seq.empty,
      tripProtections = Seq.empty,
      totalPriceDisplay = None,
      packageToken = None,
      priceChange = None,
      priceConfirmed = true
    )

  def mockProductsProduct: ProductData =
    ProductData(
      properties = Seq(bookingPropertyProduct),
      flights = flightsDataProduct,
      cars = carProductData,
      protections = Seq.empty,
      activities = Seq.empty,
      totalPriceDisplay = None,
      priceDisplayType = None,
      packageToken = None,
      priceChange = None,
      priceConfirmed = true
    )

  def mockMemberDetails(isUserHasWallet: Boolean, walletCountry: Option[String]): MemberDetails =
    MemberDetails(
      memberId = 1,
      memberCode = "12345",
      email = "<EMAIL>",
      firstName = "John",
      lastName = "Doe",
      title = Some(""),
      birthDate = None,
      nationalityId = 0,
      languageId = None,
      locale = None,
      mergeTo = 1,
      specialRemarks = None,
      recordStatus = 1,
      userId = UUID.randomUUID(),
      homePhone = "000",
      isUserHasWallet = isUserHasWallet,
      isNewsletter = None,
      nationalityName = None,
      recordCreatedBy = None,
      recordCreatedWhen = None,
      recordModifiedBy = None,
      recordModifiedWhen = None,
      preferredPriusProgramId = None,
      whiteLabelId = None,
      origin = Some("agoda"),
      whiteLabelProperties = None,
      userWalletCountry = walletCountry
    )

  def mockPaymentMethods: Seq[PaymentMethodDetailsV2] =
    Seq(
      PaymentMethodDetailsV2(
        id = 2,
        name = "MasterCard",
        paymentFlow = PaymentFlow.CreditCard,
        paymentGroupCategory = PaymentGroupCategory.CreditDebitCard,
        timeout = Some(0),
        gatewayName = None,
        icons = Seq(PaymentMethodIcon(1, "https://cdn{0}.agoda.net/images/mvc/default/ic_master.png")),
        remarks = Seq(),
        chargeDateTypes = ChargeOption.PayNow,
        chargeOptions = Set(ChargeOption.PayNow, ChargeOption.PayLater),
        isRecommended = false,
        ranking = 1,
        defaultCurrency = "USD",
        cardNumRegEx = Some("^[0-9]{16}$"),
        cvcRegEx = Some("^[0-9]{3}$"),
        isLuhnCheckRecommended = Some(false),
        requiredFields = Some(Map()),
        isFapiaoEligible = Some(false)
      )
    )

  def bookingPropertyProductWithPackagePricing(roomIdentifier: Seq[String] = Seq("")): BookingPropertiesData =
    BookingPropertiesData(
      id = "1",
      "",
      papiProperties =
        Some(transformers.Properties(property = Seq(basePropertyWithPackagePricing(roomIdentifier)), debug = None)),
      packageRequest = None,
      papiPropertyStatus = PapiPropertyStatus.Ok,
      selectedChargeOption = None
    )

  def propertyProductItems(
      propertyId: Long,
      searchId: String,
      searchCriteria: Option[PropertySearchCriteria] = None,
      roomIdentifier: Seq[String] = Seq("")
  ) = {

    val property =
      basePropertyWithPackagePricing(roomIdentifier).copy(
        propertyId = propertyId,
        booking = basicBooking(),
        searchId = searchId
      )

    val bookingProperties = BookingPropertiesData(
      id = "0",
      "",
      papiProperties = Some(
        transformers.Properties(
          property = Seq(property),
          debug = None,
          dfMetaResult = DFMetaResult(
            propertyToken = Some(ResponseStateToken(isNew = true, token = ""))
          )
        )
      ),
      packageRequest = None,
      papiPropertyStatus = PapiPropertyStatus.Ok,
      selectedChargeOption = None,
      propertySearchCriteria = searchCriteria
    )
    ProductData(
      properties = Seq(bookingProperties),
      flights = Seq.empty,
      cars = Seq.empty,
      protections = Seq.empty,
      activities = Seq.empty,
      totalPriceDisplay = None,
      priceDisplayType = None,
      packageToken = None,
      priceChange = None,
      priceConfirmed = true
    )

  }

  def merchantPropertyProductItems(propertyId: Long, searchId: String = "") = {
    val property =
      basePropertyWithMerchantPayment.copy(propertyId = propertyId, searchId = searchId)

    val bookingProperties = BookingPropertiesData(
      id = "0",
      "",
      papiProperties = Some(transformers.Properties(property = Seq(property), debug = None)),
      packageRequest = None,
      papiPropertyStatus = PapiPropertyStatus.Ok,
      selectedChargeOption = None
    )
    ProductData(
      properties = Seq(bookingProperties),
      flights = Seq.empty,
      cars = Seq.empty,
      protections = Seq.empty,
      activities = Seq.empty,
      totalPriceDisplay = None,
      priceDisplayType = None,
      packageToken = None,
      priceChange = None,
      priceConfirmed = true
    )
  }

  val enrichedPartnerCampaign = EnrichedCampaign(
    campaignId = 1909971,
    cid = 1909971,
    promotionCode = "PARTNERPROMO",
    description = "PARTNERPROMO",
    isMarkUse = true,
    isSelected = true,
    campaignType = PromotionCode,
    inapplicableReason = Some(InvalidPromocode),
    inapplicableReasonString = Some("String")
  )

}
