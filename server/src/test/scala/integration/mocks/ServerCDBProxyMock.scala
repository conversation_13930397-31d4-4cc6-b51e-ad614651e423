package integration.mocks

import com.agoda.bapi.Types._
import com.agoda.bapi.common.localization.{CmsItem, CmsItems}
import com.agoda.bapi.common.model.db.CountryInfo
import com.agoda.bapi.common.model.{SurchargeId => _, _}
import com.agoda.bapi.common.proxy.ServerCDBProxy
import com.agoda.bapi.common.util.EncryptionHelper
import com.agoda.bapi.execution.context.NonBlockingExecutionContext.default
import models.db.SiteId
import org.joda.time.DateTime

import scala.concurrent.Future

class ServerCDBProxyMock extends ServerCDBProxy {

  override def getCDBHealth: Future[Option[Boolean]] = Future.successful(Some(true))

  override def getCountryList_v1(languageId: Int): Future[Map[Int, Country]] =
    Future.successful(
      Map(
        191 -> Country(191, "China"),
        167 -> Country(167, "Spain")
      )
    )

  override def getCms_v1(cmsIds: Set[Int], languageId: Int): Future[Vector[CmsItem]] =
    Future.successful(CmsItems.all)

  def getCmsAll_v2(cmsIds: Set[Int]): Future[Map[Int, Vector[CmsItem]]] =
    Future.successful(cmsIds.map(id => id -> Vector[CmsItem](CmsItem(id, id.toString))).toMap)

  override def getLocaleList_v1: Future[Seq[Locale]] =
    Future.successful(
      Seq(
        Locale(1, "en-us")
      )
    )

  override def getCurrencyList_v2: Future[Seq[Currency]] =
    Future.successful(
      Seq(Currency("USD", 2), Currency("THB", 2), Currency("HKD", 2))
    )

  override def getExchangeCurrencyRates(languageId: SurchargeId): Future[Seq[CurrencyExchangeRate]] =
    Future.successful(Seq(CurrencyExchangeRate("USD", 2), CurrencyExchangeRate("THB", 2)))

  override def getProductHotelInformation(hotelId: Int): Future[HotelCheck] =
    Future.successful(
      hotelId match {
        case 410011 => HotelCheck(410011, 1, true, 2)
        case 410012 => HotelCheck(410012, 0, true, 2)
        case i: Int => HotelCheck(i, 1, true, 7)
      }
    )

  override def getHotelsChecks(hotelIds: Seq[Int]): Future[Map[Int, HotelCheck]] =
    Future.traverse(hotelIds)(getProductHotelInformation).map { seq =>
      seq.map(h => h.hotelId -> h).toMap
    }

  override def getPaymentMethodForStoreFront(productTypeId: Int, whitelabelId: Int): Future[Seq[PaymentMethodFromDB]] =
    ???

  override def getPaymentMethodIcons(): Future[Seq[Icon]] = ???

  override def getPaymentMethodsForBCom(propertyId: SurchargeId): Future[Set[SurchargeId]] = ???

  override def getConfigurationKeys(keys: Set[String]): Future[Map[String, String]] =
    Future.successful(
      Map(EncryptionHelper.saltKey -> "As@3sd#2342Tscs%^$#@!frs", EncryptionHelper.bookingIdKey -> "agoda2009!@")
    )

  override def getCreditCardCurrency(countryName: String): Future[Option[CurrencyInfo]] =
    Future.successful(Some(CurrencyInfo(1, "THB", 2)))

  override def getCountry(countryId: SiteId): Future[Country] =
    Future.successful(Country(1, "THAILAND", "THA", "TH"))

  override def getCountries(): Future[Seq[CountryInfo]] = {
    Future.successful(
      Seq(
        CountryInfo(
          329,
          "Bouvet Island",
          "Bouvet Island",
          "BVT",
          "BV"
        ),
        CountryInfo(
          106,
          "Thailand",
          "Thailand",
          "THA",
          "TH"
        ),
        CountryInfo(
          181,
          "United States",
          "United States",
          "USA",
          "US"
        ),
        CountryInfo(
          35,
          "INDIA",
          "INDIA",
          "IND",
          "IN"
        )
      )
    )
  }

  override def getPromotionCodeCampaigns(
      siteId: SiteId,
      bookingDate: DateTime,
      checkInDate: DateTime,
      hotelId: SiteId,
      promotionCode: Option[String]
  ): Future[Seq[Campaign]] = {
    Future.successful(Seq(Campaign(1, "test", 1, "TEST", 1, 0.0, None)))
  }

  override def getHotelIdFromMasterForBcom(masterHotelId: Int): Future[Option[Int]] = Future.successful(Some(12345))

  override def getDmcMessaging: Future[Map[DmcId, DmcMessaging]] = ???

  override def getPropertySpecificPaymentMethods(
      propertyId: SiteId,
      inventoryId: SiteId
  ): Future[Seq[PropertySpecificPaymentMethodFromDB]] = ???

  override def getHotelIdFromProductHotels(masterHotelId: SiteId): Future[Option[SiteId]] = ???
}
