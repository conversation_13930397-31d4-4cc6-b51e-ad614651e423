package integration.mocks

import com.agoda.bapi.common.database.{AGDB, DBGroup}
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.model.farerulepolicy.FareRulePolicyDBModel
import com.agoda.bapi.common.model.flight.FlightInfo.{AirportCode, FlightNumber}
import com.agoda.bapi.common.model.flight.Supplier
import com.agoda.bapi.common.model.flight.flightModel._
import com.agoda.bapi.common.model.itinerary.ItineraryInternalModel
import com.agoda.bapi.common.model.{ActionId, BreakdownId, FlightBaggageAllowanceId, FlightBaggageId, FlightBookingId, FlightBrandAttributeId, FlightBrandAttributeParamId, FlightBrandSelectionId, FlightPaxBreakdownId, FlightPaxId, FlightPolicyId, FlightSeatSelectionId, FlightSegmentId, FlightSegmentInfoId, FlightSliceId, ItineraryId, ItineraryPaymentId}
import com.agoda.bapi.common.proxy.DBConnectionGroup
import com.agoda.bapi.creation.model.db.{DuplicateBookingRecord, FlightStateForContinueCreation}
import com.agoda.bapi.creation.proxy.FlightsDbProxy
import com.agoda.mpb.common.models.state.ProductType.ProductType
import org.joda.time.LocalDate
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.{ExecutionContext, Future}

class FlightDbProxyMock extends FlightsDbProxy with MockitoSugar {
  override val db: AGDB                   = mock[AGDB]
  override val dbConnectionGroup: DBGroup = DBConnectionGroup.BFDB_BCRE

  override def insertFlights(flightModel: FlightModelInternal): Future[FlightModelInternal] =
    Future.successful(mock[FlightModelInternal])

  override def insertOnlyItineraryTableWithConnection(
      itineraryDbModel: ItineraryInternalModel
  )(implicit requestContext: RequestContext): Future[ItineraryInternalModel] =
    Future.successful(mock[ItineraryInternalModel])

  override def getNextActionIdSequenceNumber: Future[ActionId] = Future.successful(1L)

  override def getNextBreakdownIdSequenceNumbers(count: Int): Future[Seq[BreakdownId]] = Future.successful(Seq(1L))

  override def getNextItineraryPaymentIdSequenceNumbers(count: Int): Future[Seq[ItineraryPaymentId]] =
    Future.successful(Seq(1L))

  override def getNextFlightSliceIdSequenceNumbers(count: Int): Future[Seq[FlightSliceId]] = Future.successful(Seq(1L))

  override def getNextFlightSegmentIdSequenceNumbers(count: Int): Future[Seq[FlightSegmentId]] =
    Future.successful(Seq(1L))

  override def getNextFlightSegmentInfoIdSequenceNumbers(count: Int): Future[Seq[FlightSegmentInfoId]] =
    Future.successful(Seq(1L))

  override def getNextFlightBaggageIdSequenceNumbers(count: Int): Future[Seq[FlightBaggageId]] =
    Future.successful(Seq(1L))

  override def getNextFlightBaggageAllowanceIdSequenceNumbers(count: Int): Future[Seq[FlightBaggageAllowanceId]] =
    Future.successful(Seq(1L))

  override def getNextFlightBrandSelectionIdSequenceNumbers(count: Int): Future[Seq[FlightBrandSelectionId]] =
    Future.successful(Seq(1L))

  override def getNextFlightBrandAttributeIdSequenceNumbers(count: Int): Future[Seq[FlightBrandAttributeId]] =
    Future.successful(Seq(1L))

  override def getNextFlightBrandAttributeParamIdSequenceNumbers(count: Int): Future[Seq[FlightBrandAttributeParamId]] =
    Future.successful(Seq(1L))

  override def getNextFlightPolicyIdSequenceNumbers(count: Int): Future[Seq[FlightPolicyId]] =
    Future.successful(Seq(1L))

  override def getNextFlightSeatSelectionIdSequenceNumbers(count: Int): Future[Seq[FlightSeatSelectionId]] =
    Future.successful(Seq(1L))

  override def getNextFlightPaxBreakdownIdSequenceNumbers(count: Int): Future[Seq[FlightPaxBreakdownId]] =
    Future.successful(Seq(1L))

  override def getNextFlightPaxIdSequenceNumbers(count: Int): Future[Seq[FlightPaxId]] = Future.successful(Seq(1L))

  override def getFlightBooking(bookingId: Long): Future[Option[FlightBookingWithMember]] =
    Future.successful(mock[Option[FlightBookingWithMember]])

  override def getFlightSlice(bookingId: Long): Future[Seq[FlightSlice]] =
    Future.successful(mock[Seq[FlightSlice]])

  override def getFlightSegment(bookingId: Long): Future[Seq[FlightSegment]] =
    Future.successful(mock[Seq[FlightSegment]])

  override def getFinancialBreakdown(bookingId: Long): Future[Seq[Breakdown]] =
    Future.successful(mock[Seq[Breakdown]])

  override def getFlightFinancialBreakdownPerPax(bookingId: Long): Future[Seq[BreakdownPerPax]] =
    Future.successful(mock[Seq[BreakdownPerPax]])

  override def getFlightPax(bookingId: Long): Future[Seq[FlightPaxNoPii]] =
    Future.successful(mock[Seq[FlightPaxNoPii]])

  override def getFlightPaxTickets(flightBookingId: Long): Future[Seq[FlightPaxTicketState]] =
    Future.successful(mock[Seq[FlightPaxTicketState]])

  override def getFlightItineraryPayment(bookingId: Long): Future[Seq[PaymentState]] =
    Future.successful(mock[Seq[PaymentState]])

  override def getFlightBookingPayment(bookingId: Long): Future[Seq[BookingPaymentState]] =
    Future.successful(mock[Seq[BookingPaymentState]])

  override def getItineraryPaymentByItineraryId(itineraryId: Long): Future[Seq[PaymentState]] =
    Future.successful(mock[Seq[PaymentState]])

  override def getItineraryActionHistoryByItineraryId(itineraryId: Long): Future[Seq[ItineraryHistory]] =
    Future.successful(mock[Seq[ItineraryHistory]])

  override def getFlightBaggageAllowance(bookingId: Long): Future[Seq[FlightBaggageAllowance]] =
    Future.successful(mock[Seq[FlightBaggageAllowance]])

  override def getFlightSegmentInfoByPaxType(bookingId: FlightBookingId): Future[Seq[SegmentInfoByPaxType]] =
    Future.successful(mock[Seq[SegmentInfoByPaxType]])

  override def getFlightBookingByItineraryId(itineraryId: FlightBookingId): Future[List[FlightBookingState]] =
    Future.successful(mock[List[FlightBookingState]])

  override def getMultiProductItinerary(itineraryId: ItineraryId): Future[Option[MultiProductItinerary]] =
    Future.successful(mock[Option[MultiProductItinerary]])

  override def getFlightBookingSummaryStateByItineraryId(itineraryId: ItineraryId): Future[Seq[FlightSummary]] =
    Future.successful(mock[Seq[FlightSummary]])

  override def getBookingAttributionsByBookingId(
      flightBookingId: FlightBookingId
  ): Future[Seq[BookingAttributionState]] =
    Future.successful(mock[Seq[BookingAttributionState]])

  override def getFlightBookingUserAgent(flightBookingId: FlightBookingId): Future[Option[UserAgentState]] =
    Future.successful(mock[Option[UserAgentState]])

  override def getFlightBookingRejectedReason(subErrorCode: Int): Future[Option[FlightBookingRejectReason]] =
    Future.successful(mock[Option[FlightBookingRejectReason]])

  override def getSubSuppliers(): Future[Seq[Supplier]] =
    Future.successful(mock[Seq[Supplier]])

  override def deleteFlightSlice(flightSliceId: Long): Future[Unit] =
    Future.successful()

  override def deleteFlightSegment(flightSegmentId: Long): Future[Unit] =
    Future.successful()

  override def deleteFlightBaggageAllowance(flightBaggageId: Long): Future[Unit] = Future.successful()

  override def deleteFlightSegmentInfoByPaxType(flightSegmentInfoId: FlightBookingId): Future[Unit] =
    Future.successful()

  override def checkDuplicateFlightBooking(
      origin: AirportCode,
      destination: AirportCode,
      flightNumber: FlightNumber,
      departureDate: LocalDate,
      piiHash: String,
      checkInLocalDb: Boolean
  ): Future[Seq[DuplicateBookingRecord]] =
    Future.successful(mock[Seq[DuplicateBookingRecord]])

  override def upsertFlightVersionConflictWithConnection(
      conflict: FlightVersionConflict
  ): Future[FlightVersionConflict] =
    Future.successful(mock[FlightVersionConflict])

  override def getNextItinerarySequenceNumberWithConnection: Future[ItineraryId] =
    Future.successful(1L)

  implicit override val dbDispatcher: ExecutionContext = mock[ExecutionContext]

  override def getNextBookingSequenceNumberWithConnection: Future[FlightBookingId] =
    Future.successful(1L)

  def productType: ProductType = mock[ProductType]

  override def getFareRulePoliciesForFlightBooking(bookingId: FlightBookingId): Future[Seq[FareRulePolicyDBModel]] =
    Future.successful(Seq.empty)

  override def getFlightSeatSelectionDetail(bookingId: Long): Future[Seq[FlightSeatSelectionDetail]] =
    Future.successful(mock[Seq[FlightSeatSelectionDetail]])

  override def deleteFlightSeatSelectionDetail(flightSeatSelectionId: Long): Future[Unit] = Future.successful(Unit)

  override def getFlightBaggage(bookingId: FlightBookingId): Future[Seq[FlightBaggage]] =
    Future.successful(mock[List[FlightBaggage]])

  override def deleteFlightBaggage(flightBaggageId: FlightBookingId): Future[Unit] = Future.successful()

  override def getExistingItineraryPayments(flightModelInternal: FlightModelInternal): Future[Seq[PaymentState]] =
    Future.successful(mock[Seq[PaymentState]])

  override def getFlightBrandSelection(bookingId: FlightBookingId): Future[Seq[FlightBrandSelection]] =
    Future.successful(mock[Seq[FlightBrandSelection]])

  override def getFlightBrandAttribute(bookingId: FlightBookingId): Future[Seq[FlightBrandAttribute]] =
    Future.successful(mock[Seq[FlightBrandAttribute]])

  override def getFlightBrandAttributeParam(bookingId: FlightBookingId): Future[Seq[FlightBrandAttributeParam]] =
    Future.successful(mock[Seq[FlightBrandAttributeParam]])

  override def getFlightPostBookingFee(flightBookingId: FlightBookingId): Future[Seq[PostBookingFee]] =
    Future.successful(mock[Seq[PostBookingFee]])

  override def getFlightBookingIdBySupplierBookingId(supplierBookingId: AirportCode): Future[Option[FlightBookingId]] =
    Future.successful(None)

  override def getFlightStateForContinueCreation(bookingId: ActionId): Future[Option[FlightStateForContinueCreation]] =
    Future.successful(None)
}
