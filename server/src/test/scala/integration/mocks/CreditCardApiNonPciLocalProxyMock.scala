package integration.mocks

import com.agoda.bapi.common.WithProxyMessageTestMock
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.creation.proxy.CreditCardNonPciApiLocalProxyV2
import com.agoda.bapi.server.model.CountryId
import com.agoda.creditcardapi.client.v2.common.model._

import scala.concurrent.Future

class CreditCardApiNonPciLocalProxyMock extends CreditCardNonPciApiLocalProxyV2 with WithProxyMessageTestMock {
  var getCcofListResponse: Option[RetrieveCCOnFileNonPciResponse] = None

  override def getCcofList(memberId: Integer)(implicit
      requestContext: RequestContext
  ): Future[RetrieveCCOnFileNonPciResponse] =
    getCcofListResponse match {
      case Some(response) => Future.successful(response)
      case _ =>
        Future.successful(
          RetrieveCCOnFileNonPciResponse(
            StatusCode = Some("OK"),
            RetrieveCcOnFiles = Some(
              List(
                getCCOnFileNonPci(12345, "1111", 1, false, 2030, 1, CountryId.India),
                getCCOnFileNonPci(67890, "5555", 2, false, 2040, 1, CountryId.India),
                getCCOnFileNonPci(112233, "1234", 3, true, 2017, 3, CountryId.India)
              )
            ),
            RetrieveNcOnFiles = Some(
              List(
                getNCOnFileNonPci(ccId = 111111, paymentMethodId = 1, displayName = "aaa"),
                getNCOnFileNonPci(ccId = 222222, paymentMethodId = 2, displayName = "bbb")
              )
            )
          )
        )

    }

  override def verifyCcof(memberId: Integer, ccId: Long)(implicit
      requestContext: RequestContext
  ): Future[VerifyCCOnFileResponse] = Future.successful(checkCcof(memberId, ccId))

  private def getCCOnFileNonPci(
      ccId: Long,
      last4: String,
      cardType: Int,
      isExpired: Boolean,
      expMonth: Int,
      expYear: Int,
      issuingCountryId: Int
  ) = {
    RetrieveCCOnFileNonPci(
      CCId = Some(ccId),
      CCDetail = Some(
        CreditCardDetailNonPci(
          CardTypeId = Some(cardType),
          Last4 = Some(last4),
          IsExpired = Some(isExpired),
          ExpiryMonth = Some(expMonth),
          ExpiryYear = Some(expYear),
          IssuingBankCountryId = Some(issuingCountryId)
        )
      )
    )
  }

  private def getNCOnFileNonPci(ccId: Long, paymentMethodId: Integer, displayName: String) = {
    RetrieveNCOnFileDetail(
      CCId = Some(ccId),
      NCDetail = Some(DisplayNonCardDetail(Some(paymentMethodId), Some(displayName)))
    )
  }

  private def checkCcof(memberId: Integer, ccId: Long): VerifyCCOnFileResponse = {
    if (memberId == 12345)
      if (ccId == 111)
        VerifyCCOnFileResponse(StatusCode = Some("OK"), IsExpired = Some(true), IsValid = Some(true))
      else
        VerifyCCOnFileResponse(StatusCode = Some("OK"), IsExpired = Some(false), IsValid = Some(true))
    else if (memberId == 9999 && ccId != 0)
      VerifyCCOnFileResponse(StatusCode = Some("OK"), IsExpired = Some(false), IsValid = Some(false))
    else
      VerifyCCOnFileResponse(StatusCode = Some("OK"), IsExpired = Some(false), IsValid = Some(false))
  }

  override def retrieveNonPciCCDetails(
      ccToken: String
  )(implicit requestContext: RequestContext): Future[RetrieveCCNonPciResponse] = {
    Future.successful(
      RetrieveCCNonPciResponse(
        CreditCard = Some(
          CreditCardNonPci(
            CardholderName = Some("Credit Card Holder Name"),
            LastFour = Some("1234"),
            CardTypeId = Some(1),
            ExpiryMonth = Some(12),
            ExpiryYear = Some(2025),
            IssuingBankName = Some("Issuing Bank Name")
          )
        ),
        IsCcOF = Some(false),
        BinInfo = Some(
          BinInfo(
            CardClass = Some("Card Class"),
            CardScheme = Some("Card Scheme"),
            CardLevel = Some("Card Level"),
            CountryIso3 = Some("CountryIso3"),
            CardTypeId = Some(2),
            TopLevelCardTypeId = Some(1),
            CardLength = Some(12)
          )
        )
      )
    )
  }
}
