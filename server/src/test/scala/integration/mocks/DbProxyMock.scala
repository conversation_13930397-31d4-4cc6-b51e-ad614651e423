package integration.mocks

import com.agoda.bapi.common.model.APIKey
import com.agoda.bapi.server.proxy.ServerBFDBProxy

import scala.concurrent.Future
import scala.util.Random

class DbProxyMock extends ServerBFDBProxy {

  override def getBFDBHealth: Future[Option[Boolean]] = Future.successful(Some(true))

  override def getAPIKey(apiId: Int, clientId: Int): Future[APIKey] =
    (apiId, clientId) match {
      case (8, 14) => Future.successful(APIKey("9agXtjQp9Qx2MNIMWTrFAmJPg6yJYWi5hskPZXd9YB9gVf8ofP", 0))
      case (8, 15) => Future.successful(APIKey("yay7zVwG9B7pbxKqmC9SAqMCsONL09eXpC2Uv4Tz9YqwbGZBqDa", 0))
      case (8, 0) =>
        Future.successful(
          APIKey("no API-Key", 0)
        ) // to make unit test that doesn't send client id and api key pass validation
      case _ => throw new NoSuchElementException(s"Key with apiId $apiId and clientId $clientId not found!")
    }

  /**
    * Returns Master hotel ids for the provided hotel ids.
    *
    * @param hotelIds
    * @return
    */
  override def getMasterHotelIds(hotelIds: Seq[Int]): Future[Map[Int, Int]] =
    Future.successful(hotelIds.map(h => h -> h).toMap)

  override def getPreBookingId: Future[Long] = Future.successful(Random.nextInt(**********).toLong)

}
