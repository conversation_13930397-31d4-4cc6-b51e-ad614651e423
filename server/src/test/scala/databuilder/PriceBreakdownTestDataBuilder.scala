package databuilder

import com.agoda.bapi.common.constants.BookingCampaignType
import com.agoda.bapi.common.model.{Currency, PriceBreakdown}
import com.agoda.bapi.server.model.pricebreakdown.mapper.SavingsMapper
import com.agoda.bapi.server.model.pricebreakdown.{PriceBreakdownBuilderParams, PriceBreakdownFeatures}
import models.pricing.enums.ChargeTypes
import models.starfruit
import models.starfruit.{BenefitOfferBreakdown, BookingExternalLoyaltyPayment, DisplayPrice, LoyaltyEarnInfo, PackageDisplayBasis, PointDetails, PseudoCoupon}
import com.agoda.bapi.common.model.payment.PaymentModel
import com.agoda.bapi.common.model.payment.PaymentModel.{PaymentModel => BAPIPaymentModel}
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.mpb.common.models.state.ProductType.ProductType
import com.agoda.winterfell.output.LoyaltyProfile
import enumerations.CampaignTypes
import transformers.{CorBreakdown, EnrichedCampaign, EnrichedCharge}

trait ExampleData {
  /* =================================
   * INTERNAL CONSTRUCTORS
   * ================================= */
  private[databuilder] val examplePrice        = starfruit.Price(120, 140)
  private[databuilder] val emptyPrice          = starfruit.Price(0, 0)
  private[databuilder] val exampleLoyaltyPrice = starfruit.Price(-10, -10)
  private[databuilder] val exampleCurrency     = Currency("THB", 5, Some(33))
  private[databuilder] val exampleEnrichCharge =
    EnrichedCharge("room", "", ChargeTypes.Room, Seq.empty, examplePrice, examplePrice, examplePrice)
  private[databuilder] val enrichCharge =
    EnrichedCharge("room", "", ChargeTypes.Room, Seq.empty, emptyPrice, examplePrice, examplePrice)
  private[databuilder] val externalLoyaltyCharge =
    EnrichedCharge(
      "external loyalty",
      "",
      ChargeTypes.ExternalLoyaltyPointsPayment,
      Seq.empty,
      emptyPrice,
      exampleLoyaltyPrice,
      exampleLoyaltyPrice
    )
  private[databuilder] val examplePriceBreakdownBuilderParam = PriceBreakdownBuilderParams(
    charges = Seq(exampleEnrichCharge),
    chargeTotal = None,
    pseudoCouponAmount = None,
    originalAmount = 50,
    paymentModel = PaymentModel.Merchant,
    pseudoCoupon = None,
    requestedCurrency = exampleCurrency,
    numberOfRooms = 1,
    lengthOfStay = 1,
    savings = None,
    additionalRate = None,
    includeVipDiscount = false,
    corBreakdown = None,
    features = PriceBreakdownFeatures(),
    isNoPrePaymentRequired = None,
    isSingleProperty = false,
    cashbackTotal = None,
    displayAfterCashback = None
  )

  private[databuilder] val examplePriceBreakdownBuilderParamWithLoyalty =
    PriceBreakdownBuilderParams(
      charges = Seq(enrichCharge, externalLoyaltyCharge),
      chargeTotal = None,
      pseudoCouponAmount = None,
      originalAmount = 150,
      paymentModel = PaymentModel.Merchant,
      pseudoCoupon = None,
      requestedCurrency = exampleCurrency,
      numberOfRooms = 1,
      lengthOfStay = 1,
      savings = None,
      additionalRate = None,
      includeVipDiscount = false,
      corBreakdown = None,
      features = PriceBreakdownFeatures(),
      isNoPrePaymentRequired = None,
      isSingleProperty = false,
      externalLoyaltyOpt = Some(
        BookingExternalLoyaltyPayment(
          points = 1000,
          pointsAmountInUSD = 10,
          pointsToEarn = 500,
          loyaltyToken = "",
          partnerClaimToken = None,
          errorCode = None,
          requestCurrency = None,
          requestCurrencyAmount = None,
          earn = Some(
            LoyaltyEarnInfo(
              points = 0.0,
              benefitOfferBreakdown = Seq(
                BenefitOfferBreakdown(
                  benefitDetails = None,
                  originalPointsDetails = Some(PointDetails(points = 20.0)),
                  benefitPointsDetails = Some(PointDetails(points = 10.0))
                )
              )
            )
          )
        )
      )
    )

  private[databuilder] val examplePriceBreakdownBuilderParamWithGttCampaignPromotions =
    examplePriceBreakdownBuilderParam.copy(
      campaignPromotions = Some(
        List(
          EnrichedCampaign(
            campaignId = 0,
            cid = 1,
            promotionCode = "TEST",
            description = "test",
            isMarkUse = false,
            isSelected = false,
            inapplicableReasonString = None,
            inapplicableReason = None,
            campaignType = CampaignTypes.None,
            campaignGroupId = Some(BookingCampaignType.JapaneseGovernmentCampaign.id) // GTT Campaign Group Id
          )
        )
      )
    )
}

sealed trait DataBuilder {
  case class PriceBreakdownBuilderParamsBuilder(build: PriceBreakdownBuilderParams) {
    type B = PriceBreakdownBuilderParamsBuilder
    def withCharges(charges: Seq[EnrichedCharge]): B          = build.copy(charges = charges)
    def withChargeTotal(chargeTotal: Option[DisplayPrice]): B = build.copy(chargeTotal = chargeTotal)
    def withPseudoCouponAmount(pseudoCouponAmount: Option[DisplayPrice]): B =
      build.copy(pseudoCouponAmount = pseudoCouponAmount)
    def withOriginalAmount(originalAmount: Double): B                      = build.copy(originalAmount = originalAmount)
    def withPaymentModel(paymentModel: BAPIPaymentModel): B                = build.copy(paymentModel = paymentModel)
    def withPseudoCoupon(pseudoCoupon: Option[PseudoCoupon]): B            = build.copy(pseudoCoupon = pseudoCoupon)
    def withRequestedCurrency(requestedCurrency: Currency): B              = build.copy(requestedCurrency = requestedCurrency)
    def withNumberOfRooms(numberOfRooms: Int): B                           = build.copy(numberOfRooms = numberOfRooms)
    def withLengthOfStay(lengthOfStay: Int): B                             = build.copy(lengthOfStay = lengthOfStay)
    def withSavings(savings: Option[SavingsMapper]): B                     = build.copy(savings = savings)
    def withAdditionalRate(additionalRate: Option[PackageDisplayBasis]): B = build.copy(additionalRate = additionalRate)
    def withIncludeVipDiscount(includeVipDiscount: Boolean): B             = build.copy(includeVipDiscount = includeVipDiscount)
    def withCorBreakdown(corBreakdown: Option[CorBreakdown]): B            = build.copy(corBreakdown = corBreakdown)
    def withFeatures(features: PriceBreakdownFeatures): B                  = build.copy(features = features)
    def withCampaignPromotions(campaignPromotions: Option[List[EnrichedCampaign]]): B =
      build.copy(campaignPromotions = campaignPromotions)
    def withLoyaltyProfile(loyaltyProfile: Option[LoyaltyProfile]) = build.copy(loyaltyProfile = loyaltyProfile)
    def withIsNoPrePaymentRequired(isNoPrePaymentRequired: Option[Boolean] = None): B =
      build.copy(isNoPrePaymentRequired = isNoPrePaymentRequired)
    def withIsSingleProperty(isSingleProperty: Boolean): B = build.copy(isSingleProperty = isSingleProperty)
    def withItemPriceInPoints(itemPriceInPoints: Option[Double] = None): B =
      build.copy(propertyItemPriceInPoints = itemPriceInPoints)
    def withExternalLoyalty(externalLoyaltyPayment: BookingExternalLoyaltyPayment): B = {
      build.copy(externalLoyaltyOpt = Some(externalLoyaltyPayment))
    }
    def withCashbackTotal(cashbackTotal: Option[DisplayPrice]): B           = build.copy(cashbackTotal = cashbackTotal)
    def withPayToAgoda(payToAgoda: Option[DisplayPrice]): B                 = build.copy(payToAgoda = payToAgoda)
    def withAddOnPrices(addOnPrices: Map[ProductType, Seq[PriceBreakdown]]) = build.copy(addOnPrices = addOnPrices)
    def withDisplayAfterCashback(displayAfterCashback: Option[DisplayPrice]) =
      build.copy(displayAfterCashback = displayAfterCashback)
    def withSetupBookingContext(setupBookingContext: Option[SetupBookingContext]) =
      build.copy(setupBookingContext = setupBookingContext)
  }

  /* =================================
   * IMPLCIT CONVERSIONS
   * ================================= */

  implicit def toPriceBreakdownBuilderParam(builder: PriceBreakdownBuilderParamsBuilder): PriceBreakdownBuilderParams =
    builder.build
  implicit def toPriceBreakdownBuilderParamBuilder(
      build: PriceBreakdownBuilderParams
  ): PriceBreakdownBuilderParamsBuilder =
    PriceBreakdownBuilderParamsBuilder(build)
}

trait PriceBreakdownTestDataBuilder extends DataBuilder with ExampleData {
  /* =================================
   * EXPOSED BUILDERS
   * ================================= */
  lazy val aValidPriceBreakdownBuilderParam = PriceBreakdownBuilderParamsBuilder(examplePriceBreakdownBuilderParam)

  lazy val aValidPriceBreakdownBuilderParamWithLoyalty = PriceBreakdownBuilderParamsBuilder(
    examplePriceBreakdownBuilderParamWithLoyalty
  )

  lazy val aValidPriceBreakdownBuilderParamWithGttCampaignPromotions = PriceBreakdownBuilderParamsBuilder(
    examplePriceBreakdownBuilderParamWithGttCampaignPromotions
  )
}
