package com.agoda.bapi.server.utils

import com.agoda.bapi.common.ToolSet
import com.agoda.bapi.common.message.CustomerRiskStatus.CustomerRiskStatus
import com.agoda.bapi.common.message.creation.{CustomerV2, PaymentAmount}
import com.agoda.bapi.common.message.pricebreakdown.PriceBreakdownNode
import com.agoda.bapi.common.message.setupBooking.{CrossSellDetail, PackageRequest, ProductsRequest}
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.model.addOn.UserTaxCountry
import com.agoda.bapi.common.model.booking.PaymentMethodDetailsV2
import com.agoda.bapi.common.model.cart.CartContext
import com.agoda.bapi.common.model.creation.CreditCardInfoModel
import com.agoda.bapi.common.model.product.{BookingFlow, BookingRequestTypeResolver}
import com.agoda.bapi.common.model.rebookAndCancel.RebookAndCancelData
import com.agoda.bapi.common.token._
import com.agoda.bapi.common.token.common.ProductTokenKey
import com.agoda.bapi.common.token.property.Token
import com.agoda.bapi.common.util.TokenSerializers._
import com.agoda.bapi.common.util.{BessieUtils, TokenSerializer, TokenSerializers}
import com.agoda.bapi.creation.util.WhitelabelUtils
import com.agoda.bapi.server.exception.MpbPriceBreakdownMapperException
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.{FlightConfirmationData, ProductData}
import com.agoda.bapi.server.reporting.logs.SetupBookingLogMessage
import com.agoda.common.itineraryContext.ItineraryContext
import com.agoda.mpb.common.constants.PriceDisplayVersion
import com.agoda.mpb.common.models.state.{CommonPaymentInfo, DisplayPrice, Money => MPBMoney, PriceBreakdownNode => MPBPriceBreakdownNode, PriceBreakdownResponse => MPBPriceBreakdownResponse, PriceBreakdownType => MPBPriceBreakdownType, ProductType => MPBEProductType}
import com.agoda.mpb.common.{BenefitAcceleratorValueUnit, BenefitCategoryType, BenefitType}
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.agoda.winterfell.output
import com.agoda.winterfell.output.LoyaltyProfile
import scalapb.json4s.JsonFormat

import javax.inject.Inject
import scala.util.{Failure, Success, Try}

trait ProductTokenCreatorUtils {

  def createMultiProductsToken(
      setupBooking: Option[MultiProductSetupBookingToken] = None,
      creationBooking: Option[MultiProductCreationBookingToken] = None,
      retryPaymentBooking: Option[MultiProductRetryPaymentBookingToken] = None,
      timestamp: Option[Long] = None
  ): Try[TokenMessage]

  def createMultiProductSetupBookingToken(
      propertySetupBookings: Map[ProductTokenKey, PropertySetupBookingToken],
      packageRequest: Option[PackageRequest],
      timestamp: Option[Long],
      customerRiskStatus: Option[CustomerRiskStatus],
      customerInfo: Option[CustomerV2],
      installmentPlanToken: Option[String],
      installmentPlanCode: Option[String],
      crossSellDetail: Option[CrossSellDetail] = None,
      addOnBookingModelMap: AncillarySetupModel
  )(implicit context: SetupBookingContext): Try[MultiProductSetupBookingToken]

  def createMultiProductCreationBookingToken(
      flightCreationBookings: FlightBookingModel,
      propertyCreationBookings: PropertyBookingModel,
      tripProtections: TripProtectionModel,
      carCreationBookings: CarBookingModel,
      activityCreationBookings: ActivityBookingModel,
      cegFastTrackCreationToken: AddOnBookingModel,
      timestamp: Option[Long],
      priceBreakdownNode: Option[PriceBreakdownNode],
      creditCardInfo: Option[CreditCardInfoModel],
      paymentRequestInfo: Option[PaymentRequestInfo] = None,
      selectedPaymentMethodDetails: Option[PaymentMethodDetailsV2],
      isNewsLetterOptedIn: Boolean,
      priceDisplayVersion: Option[Int] = None,
      installmentPlanToken: Option[String] = None,
      installmentPlanCode: Option[String] = None,
      isBookingFromCart: Boolean,
      loyaltyProfile: Option[LoyaltyProfile] = None,
      isPartialSuccessAllowed: Boolean = false,
      cartContext: Option[CartContext] = None,
      userTaxCountry: Option[UserTaxCountry] = None,
      aabInfo: Option[AgentAssistedBookingInfo] = None,
      addOns: GenericAddOnBookingModel,
      externalLoyaltyRequestInfo: Option[ExternalLoyaltyRequestInfo] = None,
      productsRequest: ProductsRequest,
      rebookAndCancelData: Option[RebookAndCancelData] = None,
      itineraryContext: Option[ItineraryContext] = None
  )(implicit
      context: SetupBookingContext
  ): Try[MultiProductCreationBookingToken]

  def createMultiProductRetryPaymentBookingToken(
      productItems: ProductData
  )(implicit context: SetupBookingContext): Try[MultiProductRetryPaymentBookingToken]

}

class ProductTokenCreatorUtilsImpl @Inject() (
    encryptedHelper: BookingTokenEncryptionHelper
) extends ProductTokenCreatorUtils
    with ToolSet {

  private val tokenExpiresAfterMinutes                = Some(20L)
  private val tokenExpiresAfterMinutesForAmendment    = Some(5L)
  private val tokenExpiresAfterMinutesForRetryPayment = Some(30L)
  override def createMultiProductCreationBookingToken(
      flightCreationBookings: FlightBookingModel,
      propertyCreationBookings: PropertyBookingModel,
      tripProtections: TripProtectionModel,
      carCreationBookings: CarBookingModel,
      activityCreationBookings: ActivityBookingModel,
      cegFastTrackCreationBookings: AddOnBookingModel,
      timestamp: Option[Long],
      priceBreakdownNode: Option[PriceBreakdownNode],
      creditCardInfo: Option[CreditCardInfoModel],
      paymentRequestInfo: Option[PaymentRequestInfo] = None,
      selectedPaymentMethodDetails: Option[PaymentMethodDetailsV2],
      isNewsLetterOptedIn: Boolean,
      priceDisplayVersion: Option[Int] = None,
      installmentPlanToken: Option[String] = None,
      installmentPlanCode: Option[String] = None,
      isBookingFromCart: Boolean,
      loyaltyProfile: Option[LoyaltyProfile] = None,
      isPartialSuccessAllowed: Boolean = false,
      cartContext: Option[CartContext] = None,
      userTaxCountry: Option[UserTaxCountry] = None,
      aabInfo: Option[AgentAssistedBookingInfo] = None,
      addOns: GenericAddOnBookingModel,
      externalLoyaltyRequestInfo: Option[ExternalLoyaltyRequestInfo] = None,
      productsRequest: ProductsRequest,
      rebookAndCancelData: Option[RebookAndCancelData] = None,
      itineraryContext: Option[ItineraryContext] = None
  )(implicit
      context: SetupBookingContext
  ): Try[MultiProductCreationBookingToken] = {
    for {
      flightToken <- TokenSerializers[FlightBookingModel].serialize(
                       flightCreationBookings,
                       timestamp,
                       tokenExpiresAfterMinutes
                     )
      propertyToken <- TokenSerializers[PropertyBookingModel].serialize(
                         propertyCreationBookings,
                         timestamp,
                         tokenExpiresAfterMinutes
                       )
      tripProtectionToken <- TokenSerializers[TripProtectionModel].serialize(
                               tripProtections,
                               timestamp,
                               tokenExpiresAfterMinutes
                             )
      carToken <- TokenSerializers[CarBookingModel].serialize(
                    carCreationBookings,
                    timestamp,
                    tokenExpiresAfterMinutes
                  )

      activityToken <-
        TokenSerializers[ActivityBookingModel].serialize(activityCreationBookings, timestamp, tokenExpiresAfterMinutes)

      cegFastTrackToken <- TokenSerializers[AddOnBookingModel].serialize(
                             cegFastTrackCreationBookings,
                             timestamp,
                             tokenExpiresAfterMinutes
                           )
      addOnsToken <- TokenSerializers[GenericAddOnBookingModel].serialize(
                       addOns,
                       timestamp,
                       tokenExpiresAfterMinutes
                     )

      // Payment amount is deprecate but this block still depends on.
      paymentAmount <- PaymentAmount
                         .sum(
                           carCreationBookings.values.flatMap(_.paymentAmount)
                             ++ flightCreationBookings.values.flatMap(_.flatMap(_.paymentAmount))
                             ++ propertyCreationBookings.values.flatMap(_.paymentAmount)
                             ++ tripProtections.values.flatMap(_.paymentAmount)
                             ++ activityCreationBookings.values.flatMap(_.paymentAmount)
                         )
                         .toTry
      mpbCommonPayment <- Try {
                            CommonPaymentInfo(
                              method = selectedPaymentMethodDetails
                                .map(method => PaymentMethod.fromValue(method.id))
                                .getOrElse(PaymentMethod.Visa),
                              displayPrice =
                                priceBreakdownNode.map(node => toMPBDisplayPrice(node, priceDisplayVersion)),
                              timeoutMinutes = selectedPaymentMethodDetails.flatMap(_.timeout)
                            )
                          }
      requiredFieldMetadata <- Try(selectedPaymentMethodDetails.flatMap(_.requiredFields))
      partnerExternalInfoOpt = mapPartnerExternalInfo(loyaltyProfile)
    } yield MultiProductCreationBookingToken(
      properties = Option(propertyToken),
      flights = Option(flightToken),
      tripProtections = Option(tripProtectionToken),
      cars = Option(carToken),
      activities = Option(activityToken),
      priceFreezes = None,
      cegFastTracks = Option(cegFastTrackToken),
      addOns = Option(addOnsToken),
      payment = paymentAmount,
      bookingFlowType = getBookingFlowType(
        propertyCreationBookings,
        flightCreationBookings,
        tripProtections,
        carCreationBookings,
        activityCreationBookings,
        addOns,
        context,
        productsRequest,
        itineraryContext
      ),
      priceBreakdown = priceBreakdownNode,
      creditCardInfo = creditCardInfo,
      paymentRequestInfo = paymentRequestInfo,
      commonPayment = Some(mpbCommonPayment),
      optIns = Some(OptIns(isNewsLetterOptedIn)),
      priceDisplayVersion = priceDisplayVersion,
      requiredFieldMetadata = requiredFieldMetadata,
      bookingSessionId = Some(context.bookingSessionId),
      installmentPlanToken = installmentPlanToken,
      installmentPlanCode = installmentPlanCode,
      isBookingFromCart = Some(isBookingFromCart),
      partnerExternalInfo = partnerExternalInfoOpt,
      isPartialSuccessAllowed = Some(isPartialSuccessAllowed),
      cartContext = cartContext,
      userTaxCountry = userTaxCountry,
      aabInfo = aabInfo,
      externalLoyaltyRequestInfo = externalLoyaltyRequestInfo,
      rebookAndCancelData = rebookAndCancelData,
      itineraryContextStr = itineraryContext.map(JsonFormat.toJsonString),
      setupBookingCorrelationId = Some(context.correlationId)
    )
  }

  private def toMPBPriceBreakdownNode(
      priceBreakdownNode: PriceBreakdownNode
  ): MPBPriceBreakdownNode = {
    MPBPriceBreakdownNode(
      value = priceBreakdownNode.value.map { v =>
        val mpbPriceBreakdownType = Try {
          MPBPriceBreakdownType(v.`type`.id)
        } match {
          case Success(breakdownType) => breakdownType
          case Failure(e) =>
            throw new MpbPriceBreakdownMapperException(
              s"Mapping to MPB price breakdown node fail, Enum not found in MPB Common: ${e.getMessage}"
            )
        }

        val mpbProductType = v.productType.map { productType =>
          Try(MPBEProductType(productType.id)) match {
            case Success(productType) => productType
            case Failure(e) =>
              throw new MpbPriceBreakdownMapperException(
                s"Mapping to MPB price type node fail, Enum not found in MPB Common: ${e.getMessage}"
              )
          }
        }

        MPBPriceBreakdownResponse(
          `type` = mpbPriceBreakdownType,
          amount = MPBMoney(v.amount.amount, v.amount.currencyCode),
          originalAmount = v.originalAmount.map(oa => MPBMoney(oa.amount, oa.currencyCode)),
          title = v.title,
          date = v.date,
          averageAmountPerUnit = v.averageAmountPerUnit.map(avp => MPBMoney(avp.amount, avp.currencyCode)),
          productId = v.productId,
          productType = mpbProductType,
          points = v.points,
          amountBeforeDiscount = v.amountBeforeDiscount.map(oa => MPBMoney(oa.amount, oa.currencyCode)),
          discountType = v.discountType
        )
      },
      breakdowns = priceBreakdownNode.breakdowns.map(
        _.map(node => toMPBPriceBreakdownNode(node))
      )
    )
  }

  private def toMPBDisplayPrice(
      priceBreakdownNode: PriceBreakdownNode,
      priceDisplayVersion: Option[Int]
  ) =
    DisplayPrice(
      breakdown = toMPBPriceBreakdownNode(priceBreakdownNode),
      version = priceDisplayVersion.getOrElse(PriceDisplayVersion.ZERO)
    )

  private def getBookingFlowType(
      propertyCreationBookings: PropertyBookingModel,
      flightCreationBookings: FlightBookingModel,
      tripProtections: TripProtectionModel,
      carCreationBookings: CarBookingModel,
      activityCreationBookings: ActivityBookingModel,
      addOns: GenericAddOnBookingModel,
      context: SetupBookingContext,
      productsRequest: ProductsRequest,
      itineraryContext: Option[ItineraryContext]
  ) = {
    if (productsRequest.cartPricingContext.isDefined) {
      BookingFlow.Cart
    } else {
      /* ToDo: once we figured out how to handle resolving BookingFlow for Agoda's Hacker Fare correctly, remove this
       * function */
      BookingRequestTypeResolver
        .getFlowType(
          propertyCreationBookings,
          flightCreationBookings,
          tripProtections,
          carCreationBookings,
          activityCreationBookings,
          addOns,
          context.requestContext.whiteLabelInfo,
          context.session.packages.nonEmpty,
          itineraryContext,
          context.requestContext.featureAware
        )
    }

  }

  override def createMultiProductSetupBookingToken(
      propertySetupBookings: PropertySetupModel,
      packagingToken: Option[PackageRequest],
      timestamp: Option[Long],
      customerRiskStatus: Option[CustomerRiskStatus],
      customerInfo: Option[CustomerV2],
      installmentPlanToken: Option[String],
      installmentPlanCode: Option[String],
      crossSellDetail: Option[CrossSellDetail] = None,
      ancillarySetupModelMap: AncillarySetupModel
  )(implicit context: SetupBookingContext): Try[MultiProductSetupBookingToken] =
    for {
      propertyToken <- TokenSerializers[PropertySetupModel].serialize(
                         propertySetupBookings,
                         timestamp,
                         tokenExpiresAfterMinutes
                       )
      addOnTokenData <- TokenSerializers[AncillarySetupModel].serialize(
                          ancillarySetupModelMap,
                          timestamp,
                          tokenExpiresAfterMinutes
                        )
    } yield MultiProductSetupBookingToken(
      properties = Some(propertyToken),
      packageRequest = packagingToken,
      customerRiskStatus = customerRiskStatus,
      customerInfo = customerInfo,
      bookingSessionId = Some(context.bookingSessionId),
      installmentPlanToken = installmentPlanToken,
      installmentPlanCode = installmentPlanCode,
      crossSellDetail = crossSellDetail,
      addOnTokenData = Option(addOnTokenData)
    )

  override def createMultiProductRetryPaymentBookingToken(
      productItems: ProductData
  )(implicit context: SetupBookingContext): Try[MultiProductRetryPaymentBookingToken] = {
    implicit val FlightConfirmationDataSeqSerializer: TokenSerializer[Seq[FlightConfirmationData]] =
      new TokenSerializer[Seq[FlightConfirmationData]]
    for {
      flightsToken <- TokenSerializers[Seq[FlightConfirmationData]].serialize(
                        productItems.flights,
                        context.session.timestamp,
                        tokenExpiresAfterMinutesForRetryPayment
                      )
    } yield MultiProductRetryPaymentBookingToken(
      flights = Some(flightsToken)
    )
  }

  override def createMultiProductsToken(
      setupBooking: Option[MultiProductSetupBookingToken] = None,
      creationBooking: Option[MultiProductCreationBookingToken] = None,
      retryPaymentBooking: Option[MultiProductRetryPaymentBookingToken] = None,
      timestamp: Option[Long] = None
  ): Try[TokenMessage] = {
    def serializeToken[T: TokenSerializer](
        tokenInput: Option[T],
        expiresAfterMinutes: Option[Long]
    ): Try[Option[Token]] = {
      tokenInput.map(input => TokenSerializers[T].serialize(input, timestamp, expiresAfterMinutes)) match {
        case Some(Success(token))     => Success(Some(token))
        case Some(Failure(exception)) => Failure(exception)
        case None                     => Success(None)
      }
    }

    for {
      setupBookingToken        <- serializeToken(setupBooking, tokenExpiresAfterMinutes)
      creationBookingToken     <- serializeToken(creationBooking, tokenExpiresAfterMinutes)
      retryPaymentBookingToken <- serializeToken(retryPaymentBooking, tokenExpiresAfterMinutesForRetryPayment)
      jsonToken <- TokenSerializers[MultiProductBookingToken].serialize(
                     MultiProductBookingToken(setupBookingToken, creationBookingToken, retryPaymentBookingToken),
                     timestamp,
                     Some(20L)
                   )
      wrapperVersion  <- TokenSerializers.toJsonString(jsonToken)
      encryptedString <- encryptedHelper.encryptToken(wrapperVersion)
    } yield encryptedString
  }

  private def mapPartnerExternalInfo(
      loyaltyProfileOpt: Option[LoyaltyProfile]
  )(implicit
      context: SetupBookingContext
  ): Option[PartnerExternalInfo] = {
    loyaltyProfileOpt.map { loyaltyProfile =>
      def toAccelerator(accelerator: output.Accelerator): Accelerator =
        Accelerator(
          startDate = accelerator.startDate,
          endDate = accelerator.endDate,
          benefitAccelerator = accelerator.benefitAccelerator
        )

      def toEarnAccelerators(earnAccelerators: output.EarnAccelerators): EarnAccelerators = {
        val benefitType         = BenefitType.withNameWithDefault(earnAccelerators.benefitType.toString)
        val benefitCategoryType = BenefitCategoryType.withNameWithDefault(earnAccelerators.benefitCategoryType.toString)
        val benefitAcceleratorValueUnit = BenefitAcceleratorValueUnit
          .withNameWithDefault(earnAccelerators.benefitAcceleratorValueUnit.toString)

        if (benefitCategoryType.toString != earnAccelerators.benefitCategoryType.toString) {
          logger.warn(
            s"EarnAccelerators benefitCategoryType type might not be up to date [ booking-creation ${benefitCategoryType.toString} ] [ winterfell ${earnAccelerators.benefitCategoryType.toString} ]"
          )
        }

        EarnAccelerators(
          benefitType = benefitType,
          benefitCategoryType = benefitCategoryType,
          benefitAcceleratorValueUnit = benefitAcceleratorValueUnit,
          benefitAccelerator = earnAccelerators.benefitAccelerator,
          promotionalAccelerator = earnAccelerators.promotionalAccelerator.map(toAccelerator),
          calculatedAccelerator = earnAccelerators.calculatedAccelerator.map(toAccelerator)
        )
      }

      val partnerSpecificParams = loyaltyProfile.externalLoyaltyProfileInfo.flatMap(_.partnerSpecificParams)
      val subLoyaltyProgram     = loyaltyProfile.externalLoyaltyProfileInfo.flatMap(_.subLoyaltyPrograms.headOption)

      val earnAccelerators = subLoyaltyProgram.flatMap(_.earnAccelerators).map(_.map(toEarnAccelerators))

      /* WLBO-1598 : Get rid of HideBookingPIIHadoop feature once HKG dc is decommissioned
       *
       * Thankyou_member_id and external_member_id fields are soft PII, i.e., Agoda legal team have approved store this
       * fields in hadoop. Only for the WestJet partner these are strict PII and can’t be stored in HK dc. So once HK dc
       * is decommissioned, we can remove this feature and BI team can refer to hadoop. */
      val removePIIHadoopFeatureEnabled = WhitelabelUtils.hideBookingPIIHadoop(context.requestContext)
      val removePIIHadoopExpEnabled     = context.requestContext.featureAware.exists(_.removePIIFieldsFromHadoop)
      val removePIIFieldsFromHadoop     = removePIIHadoopFeatureEnabled && removePIIHadoopExpEnabled
      val memberId =
        if (removePIIFieldsFromHadoop) None
        else loyaltyProfile.externalLoyaltyProfileInfo.flatMap(_.userProfileInfo).flatMap(_.memberId)
      val thankYouId =
        if (removePIIFieldsFromHadoop) None
        else partnerSpecificParams.flatMap(_.get("memberId"))

      PartnerExternalInfo(
        externalMemberId = memberId,
        thankYouMemberId = thankYouId,
        programName = subLoyaltyProgram.flatMap(_.programName),
        programDescription = subLoyaltyProgram.flatMap(_.programDescription),
        earnAccelerators = earnAccelerators,
        channelId = BessieUtils.getChannelIdFromLoyaltyProfileOpt(loyaltyProfileOpt),
        externalPartnerProgramName = subLoyaltyProgram.flatMap(_.externalPartnerProgramName),
        externalPartnerProgramCode = subLoyaltyProgram.flatMap(_.externalPartnerProgramCode),
        loyaltyProfileId = subLoyaltyProgram.flatMap(_.loyaltyProfileId)
      )
    }
  }
}
