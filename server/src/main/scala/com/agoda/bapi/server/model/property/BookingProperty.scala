package com.agoda.bapi.server.model.property

import com.agoda.bapi.common.message.initializeBooking.BookingAmount
import com.agoda.bapi.common.token.property.PriceGuaranteeToken
import transformers.Property

case class BookingProperty(
    propertyId: Long,
    bookingAmount: Option[BookingAmount],
    priceGuaranteeToken: Option[PriceGuaranteeToken],
    properties: Option[transformers.Properties],
    propertiesJson: String
) {
  def externalProperty: Option[Property] = properties.flatMap(_.property.headOption)
}
