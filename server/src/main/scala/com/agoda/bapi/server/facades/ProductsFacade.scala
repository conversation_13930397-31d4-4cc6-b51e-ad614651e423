package com.agoda.bapi.server.facades

import com.agoda.bapi.common.{MessageService, ToolSet}
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.CustomerRiskStatus.CustomerRiskStatus
import com.agoda.bapi.common.message._
import com.agoda.bapi.common.message.creation.CustomerV2
import com.agoda.bapi.common.message.pricebreakdown.{PriceBreakdownNode, PriceDisplayType}
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.model.ChargeOption.ChargeOption
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.addOn.UserTaxCountry
import com.agoda.bapi.common.model.booking.PaymentMethodDetailsV2
import com.agoda.bapi.common.model.cart.CartContext
import com.agoda.bapi.common.model.creation.{PriceChangeOnPollingStatus, PriceChangePerRoomStatus}
import com.agoda.bapi.common.model.externalloyalty.ExternalLoyaltyAdditionalInfo
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.rebookAndCancel.RebookAndCancelData
import com.agoda.bapi.creation.model.CreditCardInfo
import com.agoda.bapi.creation.util.WhitelabelUtils
import com.agoda.bapi.execution.context.NonBlockingExecutionContext.default
import com.agoda.bapi.server.addon._
import com.agoda.bapi.server.facades.activities.ActivityComposerMixin
import com.agoda.bapi.server.facades.addons.{AddOnsComposerMixin, TripProtectionComposerMixin}
import com.agoda.bapi.server.facades.aggregator.ProductTokenAggregatorMixin
import com.agoda.bapi.server.facades.cart.CartComposerMixin
import com.agoda.bapi.server.facades.flight.FlightsComposerMixin
import com.agoda.bapi.server.facades.packaging.PackagingComposerMixin
import com.agoda.bapi.server.facades.properties.PropertiesComposerMixin
import com.agoda.bapi.server.facades.vehicle.VehicleComposerMixin
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model._
import com.agoda.bapi.server.reporting.CustomerContactInfo
import com.agoda.bapi.server.repository.FlightsRepository
import com.agoda.bapi.server.repository.dto.papi.{AdditionalPricingParameter, CardPaymentRequestParameter, CashbackRedemptionParameter, DiscountRequest}
import com.agoda.bapi.server.service._
import com.agoda.bapi.server.service.allotment.AllotmentPreCheckStatus
import com.agoda.bapi.server.service.pricebreakdown.PriceBreakdownService
import com.agoda.bapi.server.utils.RateChannelSwapUtils.{enrichRequestForRateChannelSwap, validatePropertiesAndGetPriceMapForDisplay}
import com.agoda.bapi.server.utils._
import com.agoda.bapi.server.utils.partner.PartnerPromoService
import com.agoda.common.itineraryContext.ItineraryContext
import com.agoda.externalloyalty.client.v2.model.{CartItemReq, DistributePointsResponse}
import com.agoda.mpb.common.models.state.ProductType
import transformers.EnrichedChildRoom
//TODO: Replace with local BookingCountryIndicator in BCRE https://jira.agodadev.io/browse/CFB-1117
import com.agoda.finance.tax.models.BookingCountryIndicator
import com.agoda.winterfell.output.LoyaltyProfile

import javax.inject.{Named, Singleton}
import scala.concurrent.Future
import scala.language.postfixOps
import scala.util.Try

trait ProductsFacade {

  // TODO factor out SetupBookingContext down to required parameters
  def composeProductData(
      request: SetupBookingRequest,
      chargeCurrency: CurrencyCode,
      giftCardBalance: Double = 0,
      memberEmail: Option[String] = None,
      cardPaymentParams: Option[CardPaymentRequestParameter] = None,
      forceAlternativeRoom: Boolean = false,
      loyaltyProfile: Option[LoyaltyProfile] = None,
      essCountryCode: Option[String] = None,
      chargeOption: Option[ChargeOption] = None,
      cashbackRedemptionParameter: Option[CashbackRedemptionParameter] = None,
      bookingCountryIndicatorOpt: Option[BookingCountryIndicator] = None
  )(implicit
      context: SetupBookingContext
  ): Future[ProductData]

  def createProductToken(
      input: ProductData,
      customerRiskStatus: Option[CustomerRiskStatus],
      customerInfo: Option[CustomerV2],
      allotmentPreCheckResults: Seq[AllotmentPreCheckStatus],
      creditCardInfo: Option[CreditCardInfo],
      creditCardValidationInfo: Option[CreditCardInfoSetupResponse],
      paymentRequest: Option[PaymentRequest],
      chargeCurrency: Option[CurrencyCode],
      selectedPaymentMethodDetails: Option[PaymentMethodDetailsV2] = None,
      isNewsletterOptedIn: Boolean,
      productsRequest: ProductsRequest,
      isPriceChangedForRequiredProperty: Option[PriceChangeOnPollingStatus] = None,
      selectedChargeOption: Option[ChargeOption] = None,
      priceChangeStatusOnRooms: Seq[PriceChangePerRoomStatus] = Seq.empty,
      installmentPlanToken: Option[String] = None,
      installmentPlanCode: Option[String] = None,
      loyaltyRequest: Option[LoyaltyRequest] = None,
      loyaltyProfile: Option[LoyaltyProfile] = None,
      enabledFeatures: Option[Seq[String]] = None,
      cartContext: Option[CartContext] = None,
      userTaxCountry: Option[UserTaxCountry] = None,
      aabInfo: Option[AabInfo] = None,
      addOnData: Seq[AddOnDataV2] = Seq.empty,
      itineraryContext: Option[ItineraryContext] = None
  )(implicit context: SetupBookingContext): Try[TokenMessage]
}

object ProductsFacade extends ToolSet {
  val defaultSiteId: Int             = -1
  val defaultCampaignId: Int         = -1
  val PRICE_CHANGE_PERCENTAGE: Int   = 1
  val PriceFreezeRequestName: String = "room_deposit_exercise_quotes_request"
}

import com.google.inject.Inject

@Named("ProductsFacadeImpl")
@Singleton
class ProductsFacadeImpl @Inject() (
    val propertyService: PropertyService,
    override val flightsRepository: FlightsRepository,
    override val carService: CarService,
    override val protectionService: ProtectionService,
    override val priceBreakdownService: PriceBreakdownService,
    override val productTokenUtils: ProductTokenCreatorUtils,
    override val totalSavingsService: TotalSavingsService,
    mixAndSaveService: MixAndSaveService,
    override val campaignService: CampaignService,
    messageService: MessageService,
    override val bookingsService: BookingsService,
    override val activityService: ActivityService,
    bookingMessagingService: BookingMessagingService,
    externalLoyaltyService: ExternalLoyaltyService,
    partnerPromoService: PartnerPromoService,
    consumerFintechService: ConsumerFintechService,
    override val cegFastTrackService: AddOnBookingTokenService,
    override val addOnFacade: AddOnFacade,
    override val addOnsService: AddOnService,
    rebookAndCancelService: RebookAndCancelService
) extends ProductsFacade
    with RoomInfoMapper
    with ToolSet
    with AddOnUtils
    with CampaignComposerMixin
    with CartComposerMixin
    with LoyaltyComposerMixin
    with PropertiesComposerMixin
    with FlightsComposerMixin
    with TripProtectionComposerMixin
    with AddOnsComposerMixin
    with ActivityComposerMixin
    with VehicleComposerMixin
    with PackagingComposerMixin
    with ProductTokenAggregatorMixin {

  override def composeProductData(
      request: SetupBookingRequest,
      chargeCurrency: CurrencyCode,
      giftCardBalance: Double = 0,
      memberEmail: Option[String] = None,
      cardPaymentParams: Option[CardPaymentRequestParameter] = None,
      isForceAlternativeRoom: Boolean = false,
      loyaltyProfile: Option[LoyaltyProfile] = None,
      essCountryCode: Option[String] = None,
      chargeOption: Option[ChargeOption] = None,
      cashbackRedemptionParameter: Option[CashbackRedemptionParameter] = None,
      bookingCountryIndicatorOpt: Option[BookingCountryIndicator] = None
  )(implicit context: SetupBookingContext): Future[ProductData] = {
    implicit val requestContext: RequestContext = context.requestContext

    val isReturnVipDiscount   = request.enabledFeatures.exists(_.contains("pbdvipdiscount"))
    val giftCardRedeemRequest = getGiftCardRedeemRequest(request, giftCardBalance)
    val cid                   = request.userContext.flatMap(_.experimentData.flatMap(_.cId))
    val campaignListF =
      if (
        context.requestContext.featureAware.exists(
          _.removeSpBAPIGetPMCCampaignPromotion
        )
      ) {
        Future.successful(Seq.empty[CampaignInfoInternal])
      } else {
        campaignService.getCampaignInfo(cid, None, request.productsRequest.propertyRequests.headOption)
      }
    val ccBinCampaignsF = getCcBinCampaigns(request)(context)
    val discountRequestF =
      (ccBinCampaigns: Seq[CampaignInfoInternal]) =>
        getCampaignRequest(
          request,
          ccBinCampaigns,
          memberEmail
        )

    val isDisableShowBookingFee           = WhitelabelUtils.isRegulationDisableShowBookingFeePriceBreakdownEnabled(requestContext)
    val isPaymentMethodFeeEnabled         = request.enabledFeatures.exists(_.contains("PaymentMethodFee"))
    val isFlightCheckInEnabled            = context.requestContext.featureAware.exists(_.EnableOnboardCheckIn)
    val isSupportPriceDisplayTypesEnabled = context.requestContext.featureAware.exists(_.supportPriceDisplayTypes)
    val rebookAndCancelData               = mapRebookAndCancelData(rebookAndCancelService.getRebookAndCancelRequirement(request))
    val isInclusivePaySupplier = WhitelabelUtils.isRegulationShowExclusivePriceWithFeeEnabled(
      requestContext
    ) && requestContext.featureAware.exists(_.isInclusivePaySupplier)
    val fixDoubleTaxesAndFeesPaySupplier = requestContext.featureAware.exists(_.fixDoubleTaxesAndFeesPaySupplier)

    context.bookingFlowType match {
      case BookingFlow.MixAndSave =>
        for {
          bookingProperty <- propertyService.retrievePropertyBundles(
                               setDFFlagInProductsRequest(
                                 request,
                                 applyVariableTaxIfNeeded(request)
                               ),
                               request.deviceContext.fold(DevicePlatform.Unknown)(_.deviceTypeId),
                               chargeCurrency,
                               None,
                               giftCardRedeemRequest,
                               None,
                               paymentRequest = request.paymentRequest
                             )
          property   = bookingProperty.flatMap(_.properties)
          roomBundle = property.flatMap(_.property.headOption.flatMap(_.roomBundles.headOption))
          properties = mixAndSaveService.generateBookingData(property, roomBundle)
          bookingPropertyDatas =
            mixAndSaveService.toBookingPropertyData(request.productsRequest.propertyRequests.zip(properties))
          bookingPropertyDatasWithPreBookingId <- mapOrGeneratePreBookingIdsTo(bookingPropertyDatas)
          priceBreakdown <- priceBreakdownService.getMixAndSavePriceBreakdown(
                              roomBundle,
                              context.requestContext.displayCurrency(),
                              request.productsRequest.propertyRequests,
                              isReturnVipDiscount = isReturnVipDiscount,
                              isDisableShowBookingFee = isDisableShowBookingFee,
                              isInclusivePaySupplier = isInclusivePaySupplier,
                              fixDoubleTaxesAndFeesPaySupplier = fixDoubleTaxesAndFeesPaySupplier
                            )
          priceDisplayType = if (isSupportPriceDisplayTypesEnabled) Some(PriceDisplayType.Unknown) else None
          totalSavings    <- totalSavingsForMixAndSaveBundle(roomBundle)
        } yield ProductData(
          properties = bookingPropertyDatasWithPreBookingId,
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          totalPriceDisplay = priceBreakdown,
          priceDisplayType = priceDisplayType,
          packageToken = None,
          priceChange = None,
          priceConfirmed = getSinglePropertyPriceConfirmed(bookingPropertyDatas),
          totalSavings = totalSavings,
          bundleSavings = totalSavings
        )
      case BookingFlow.SingleFlight =>
        for {
          ccBinCampaigns <- ccBinCampaignsF
          flight <- retrieveFirstFlightData(
                      enabledFeatures = request.enabledFeatures,
                      items = request.productsRequest.flightRequests,
                      chargeCurrency = chargeCurrency,
                      userContext = request.userContext,
                      selectedCreditCardTypeId = request.paymentRequest.flatMap(_.selectedPaymentMethod),
                      campaignInfo = request.campaignInfo,
                      campaignsIds = Some(ccBinCampaigns.flatMap(_.promotionId.map(_.toString))),
                      loyaltyRequestOpt = request.loyaltyRequest,
                      selectedPaymentMethodId = request.paymentRequest.flatMap(_.selectedPaymentMethod),
                      aabInfo = request.aabInfo
                    )
          isChildInfantFareFeatureEnabled = context.requestContext.featureAware.exists(_.isFlightChildAndInfantSupport)
          priceBreakdown <-
            priceBreakdownForFlight(
              flightConfirmationData = flight,
              currencyCode = chargeCurrency,
              isProtectionOptIn = false,
              isChildInfantFeature = isChildInfantFareFeatureEnabled,
              protection = Seq.empty,
              isPaymentMethodFeeEnabled = isPaymentMethodFeeEnabled,
              addOnData = Map.empty,
              isFlightCheckInEnabled = isFlightCheckInEnabled
            )
          priceDisplayType = if (isSupportPriceDisplayTypesEnabled) Some(PriceDisplayType.SingleFlight) else None
        } yield {
          val priceChange          = getPriceChangeForFlight(flight, chargeCurrency)
          val totalSaving          = totalSavingsForFlight(flight, chargeCurrency)
          val flightsPromotionInfo = PromocodeUtils.mapToPromotionInfoForFlights(flight, chargeCurrency)
          ProductData(
            properties = Seq.empty,
            flights = Seq(flight),
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = priceBreakdown,
            priceDisplayType = priceDisplayType,
            packageToken = None,
            priceChange = priceChange,
            priceConfirmed = flight.isCompleted,
            totalSavings = totalSaving,
            isPromotionCodeEligible = PromocodeUtils.isPromotionCodeEligibleForFlights(flight.campaignInfo),
            campaignPromotionInfo = flightsPromotionInfo
          )
        }
      case BookingFlow.FlightWithProtection =>
        for {
          ccBinCampaigns <- ccBinCampaignsF
          flight <- retrieveFirstFlightData(
                      enabledFeatures = request.enabledFeatures,
                      items = request.productsRequest.flightRequests,
                      chargeCurrency = chargeCurrency,
                      userContext = request.userContext,
                      campaignInfo = request.campaignInfo,
                      campaignsIds = Some(ccBinCampaigns.flatMap(_.promotionId.map(_.toString))),
                      loyaltyRequestOpt = request.loyaltyRequest,
                      selectedPaymentMethodId = request.paymentRequest.flatMap(_.selectedPaymentMethod),
                      aabInfo = request.aabInfo,
                      isRetryPayment = request.isRetryPayment
                    )
          isChildInfantFareFeatureEnabled = context.requestContext.featureAware.exists(_.isFlightChildAndInfantSupport)
          protections                    <- getTripProtectionProductItems(request, flight, chargeCurrency)
          isProtectionOptIn = // todo rename all isProtectionOptIn to be isProtectionV1OptIn
            protections.headOption
              .exists(_.isProtectionSelected)
          addOns    <- getAddOnDataV2(request, Seq.empty, Seq(flight), Seq.empty, chargeCurrency)
          addOnsData = addOns.flatMap(addOn => addOn.confirmationData.map(addOn.productType -> _)).toMap
          priceBreakdown <- priceBreakdownForFlight(
                              flightConfirmationData = flight,
                              currencyCode = chargeCurrency,
                              isProtectionOptIn = isProtectionOptIn,
                              isChildInfantFeature = isChildInfantFareFeatureEnabled,
                              protection = protections,
                              isPaymentMethodFeeEnabled = isPaymentMethodFeeEnabled,
                              addOnData = addOnsData,
                              isFlightCheckInEnabled = isFlightCheckInEnabled
                            )
          priceDisplayType = if (isSupportPriceDisplayTypesEnabled) Some(PriceDisplayType.SingleFlight) else None
        } yield {
          val priceChange          = getPriceChangeForFlight(flight, chargeCurrency)
          val totalSaving          = totalSavingsForFlight(flight, chargeCurrency)
          val flightsPromotionInfo = PromocodeUtils.mapToPromotionInfoForFlights(flight, chargeCurrency)
          ProductData(
            properties = Seq.empty,
            flights = Seq(flight),
            cars = Seq.empty,
            protections = protections,
            activities = Seq.empty,
            totalPriceDisplay = priceBreakdown,
            priceDisplayType = priceDisplayType,
            packageToken = None,
            priceChange = priceChange,
            priceConfirmed = flight.isCompleted,
            totalSavings = totalSaving,
            isPromotionCodeEligible = PromocodeUtils.isPromotionCodeEligibleForFlights(flight.campaignInfo),
            campaignPromotionInfo = flightsPromotionInfo,
            addOnDataV2 = addOns
          )
        }
      case BookingFlow.SingleProperty =>
        for {
          campaignList    <- campaignListF
          ccBinCampaigns  <- ccBinCampaignsF
          discountRequest <- discountRequestF(ccBinCampaigns)
          requestWithDfFlags = setDFFlagInProductsRequest(
                                 request,
                                 DFFeatureFlagsFromSession(
                                   context.session,
                                   context.requestContext.featureAware,
                                   isForceAlternativeRoom
                                 ) ++ applyVariableTaxIfNeeded(request)
                               )
          isRateChannelSwapExp = context.requestContext.featureAware.exists(_.rateChannelSwapSwitch)
          enrichedRequestRateSwap =
            if (isRateChannelSwapExp) enrichRequestForRateChannelSwap(context, requestWithDfFlags)
            else requestWithDfFlags
          consumerFintechRequirement <- consumerFintechService.getConsumerFintechRequirement(request)
          properties <- propertyService.retrieveProperties(
                          enrichedRequestRateSwap,
                          request.deviceContext.fold(DevicePlatform.Unknown)(_.deviceTypeId),
                          chargeCurrency,
                          None,
                          giftCardRedeemRequest,
                          discountRequest,
                          cardPaymentParams,
                          request.loyaltyRequest,
                          request.paymentRequest,
                          shouldClearPriceGuranteeToken = isForceAlternativeRoom,
                          cartRequest = None,
                          stateId = request.customerInfo.flatMap(_.stateId),
                          rateCategoryIds = getRateCategoryIds(request.productsRequest.bookingToken),
                          consumerFintechRequirement = consumerFintechRequirement,
                          userTaxCountryCode = essCountryCode,
                          cashbackRedemptionParameter = cashbackRedemptionParameter
                        )
          (enrichedPropertiesRateSwap, priceMapForDisplay) =
            if (isRateChannelSwapExp) validatePropertiesAndGetPriceMapForDisplay(properties)
            else (properties, None)
          searchCriteria              = enrichedRequestRateSwap.headOption.map(_.propertySearchCriteria)
          property                    = enrichedPropertiesRateSwap.headOption.flatMap(_.papiProperties.flatMap(_.property.headOption))
          singlePropertyData          = enrichedPropertiesRateSwap.headOption.toSeq
          childRoom                   = extractChildRoom(searchCriteria, property)(context.requestContext)
          totalSavings               <- totalSavingsForSingleProperty(enrichedPropertiesRateSwap)
          displayCurrency             = context.requestContext.displayCurrency()
          propertiesWithPreBookingId <- mapOrGeneratePreBookingIdsTo(enrichedPropertiesRateSwap)
          propertiesWithMessageHostFlag <-
            bookingMessagingService.mapIsMessageHostAvailableTo(propertiesWithPreBookingId)
          propertiesWithConsumerFintechDetail =
            enrichConsumerFintechDetail(consumerFintechRequirement, propertiesWithMessageHostFlag)
          priceChangeWithPreviousCall = getPriceChangeForSingleProperty(childRoom, displayCurrency)
          propertyCurrency            = childRoom.flatMap(_.requestedCurrencyCode)
          addOns <- getAddOnDataV2(
                      request = request,
                      properties = propertiesWithConsumerFintechDetail,
                      flights = Seq.empty,
                      cars = Seq.empty,
                      chargeCurrency = propertyCurrency.getOrElse(RequestContext.DEFAULT_CURRENCY),
                      bookingCountryIndicatorOpt = bookingCountryIndicatorOpt
                    )
          addOnData <-
            getAddOnData(
              request,
              propertiesWithConsumerFintechDetail,
              chargeCurrency,
              essCountryCode,
              chargeOption,
              bookingCountryIndicatorOpt
            )
          _ =
            if (
              addOns.exists(_.productType == ProductType.CEGFastTrack) != addOnData.products
                .exists(_.productType == ProductType.CEGFastTrack)
            ) {
              logger.warn(
                s"CEGFastTrack addOnData and addOns are not in sync. CEGFastTrack addOns V1 exists ${addOnData.products.exists(_.productType == ProductType.CEGFastTrack)}, CEGFastTrack addOns v2 exists = ${addOns.exists(_.productType == ProductType.CEGFastTrack)}"
              )
            }
          filteredAddOns = filteredAncillaryAddOns(addOns)(context)
          filteredAddOnData =
            filteredCegFastTrackAddOnData(addOnData, filteredAddOns, context.requestContext.featureAware)
          priceDisplay <- multiHotelPriceBreakdown(
                            singlePropertyData,
                            isReturnVipDiscount,
                            isSingleProperty = true,
                            isConnectedTripRequest(request),
                            context.requestContext,
                            isDisableShowBookingFee,
                            priceMapForDisplay.filter(_ => isRateChannelSwapExp),
                            Some(filteredAddOnData),
                            filteredAddOns,
                            isCalculateTaxAndFeeWithMarginTaxAndFee(request),
                            isInclusivePaySupplier,
                            fixDoubleTaxesAndFeesPaySupplier
                          ).map(_.headOption.flatten)
          priceDisplayType = if (isSupportPriceDisplayTypesEnabled) Some(PriceDisplayType.SingleProperty) else None
          addOnPriceWithCurrency = getSelectedAddOnsPriceWithCurrency(
                                     addOnProducts = filteredAddOnData.products,
                                     addOnProductsV2 = filteredAddOns
                                   )
          paymentChargeOptions = getPaymentChargeOptions(childRoom, addOnPriceWithCurrency)
        } yield {
          val requestPromotionCode = request.campaignInfo.map(_.promotionCode)
          val shouldHidePromoBoxWhenNoCouponAvailable: Boolean =
            WhitelabelUtils.isHidePromoBoxWhenNoCouponAvailableEnabled(requestContext.whiteLabelInfo)
          CustomerContactInfo(searchCriteria, request.customerInfo, property)(context)
            .map(messageService.sendMessage)
          ProductData(
            properties = propertiesWithConsumerFintechDetail,
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq.empty,
            totalPriceDisplay = priceDisplay,
            priceDisplayType = priceDisplayType,
            packageToken = None,
            priceChange = priceChangeWithPreviousCall,
            priceConfirmed = getSinglePropertyPriceConfirmed(enrichedPropertiesRateSwap),
            totalSavings = totalSavings,
            isPromotionCodeEligible = PromocodeUtils
              .isPromotionCodeEligibleForProperty(
                enrichedPropertiesRateSwap,
                campaignList,
                shouldHidePromoBoxWhenNoCouponAvailable
              ),
            campaignPromotionInfo = Some(
              campaignService.getPromotionInfo(
                enrichedPropertiesRateSwap,
                campaignList,
                ccBinCampaigns,
                requestPromotionCode,
                childRoom
              )
            ),
            paymentChargeOptions = paymentChargeOptions.toSeq,
            walletPromotions = mapWalletPromotions(childRoom),
            addOnData = filteredAddOnData,
            addOnDataV2 = filteredAddOns
          )
        }
      case BookingFlow.Package =>
        val flightF = retrieveFirstFlightData(
          enabledFeatures = request.enabledFeatures,
          items = request.productsRequest.flightRequests,
          chargeCurrency = chargeCurrency,
          userContext = request.userContext,
          packageRequest = context.session.packages,
          selectedCreditCardTypeId = request.paymentRequest.flatMap(_.selectedPaymentMethod),
          loyaltyRequestOpt = request.loyaltyRequest,
          aabInfo = request.aabInfo
        )
        val propertyF = (flight: FlightConfirmationData, discountRequest: Option[DiscountRequest]) =>
          if (flight.hasFlight)
            propertyService.retrieveProperties(
              setDFFlagInProductsRequest(
                request,
                applyVariableTaxIfNeeded(request)
              ),
              request.deviceContext.fold(DevicePlatform.Unknown)(_.deviceTypeId),
              chargeCurrency,
              flight.packageRequest,
              giftCardRedeemRequest,
              discountRequest,
              cartRequest = None,
              paymentRequest = request.paymentRequest
            )
          else Future.successful(Seq.empty)

        val getPackageToken = (flight: FlightConfirmationData, properties: Seq[BookingPropertiesData]) =>
          if (flight.hasFlight) properties.lastOption.flatMap(_.packageRequest)
          else context.session.packages

        val getPriceBreakdown = (flight: FlightConfirmationData, properties: Seq[BookingPropertiesData]) =>
          if (flight.isCompleted)
            priceBreakdownForPackages(properties, request, isInclusivePaySupplier, fixDoubleTaxesAndFeesPaySupplier)
          else Future.successful(None)

        val getTotalSavings = (properties: Seq[BookingPropertiesData]) => totalSavingsForPackages(properties, request)

        val flightDetailF =
          retrieveFirstFlightDetail(
            request.productsRequest.flightRequests,
            chargeCurrency,
            request.userContext,
            context.session.packages,
            request.aabInfo
          )
        val propertyDetailF = (packageRequest: Option[PackageRequest], discountRequest: Option[DiscountRequest]) =>
          propertyService.retrieveProperties(
            setDFFlagInProductsRequest(
              request,
              applyVariableTaxIfNeeded(request)
            ),
            request.deviceContext.fold(DevicePlatform.Unknown)(_.deviceTypeId),
            chargeCurrency,
            packageRequest,
            giftCardRedeemRequest,
            discountRequest,
            paymentRequest = request.paymentRequest
          )
        for {
          campaignList               <- campaignListF
          ccBinCampaigns             <- ccBinCampaignsF
          discountRequest            <- discountRequestF(ccBinCampaigns)
          flight                     <- flightF
          properties                 <- propertyF(flight, discountRequest)
          propertiesWithPreBookingId <- mapOrGeneratePreBookingIdsTo(properties)
          packageToken                = getPackageToken(flight, properties)
          priceBreakdown             <- getPriceBreakdown(flight, properties)
          flightDetail               <- flightDetailF
          propertyDetail             <- propertyDetailF(flightDetail.packageRequest, discountRequest)
          unconfirmedPriceBreakdown <-
            getUnconfirmedPriceBreakdownForPackages(
              request,
              flightDetail,
              propertyDetail,
              isInclusivePaySupplier,
              fixDoubleTaxesAndFeesPaySupplier
            )
          totalSavings <-
            if (priceBreakdown.nonEmpty) getTotalSavings(properties)
            else if (unconfirmedPriceBreakdown.nonEmpty) getTotalSavings(propertyDetail)
            else Future.successful(None)
          priceDisplayType = if (isSupportPriceDisplayTypesEnabled) Some(PriceDisplayType.Package) else None
        } yield
          if (priceBreakdown.nonEmpty)
            ProductData(
              properties = propertiesWithPreBookingId,
              flights = Seq(flight),
              cars = Seq.empty,
              protections = Seq.empty,
              activities = Seq.empty,
              totalPriceDisplay = priceBreakdown,
              priceDisplayType = priceDisplayType,
              packageToken = packageToken,
              priceChange = getPriceChangeForPackages(properties, chargeCurrency),
              priceConfirmed = flight.isCompleted,
              totalSavings = totalSavings,
              isPromotionCodeEligible = PromocodeUtils.isPromotionCodeEligibleForProperty(properties, campaignList),
              campaignPromotionInfo = Some(campaignService.getPromotionInfo(properties, campaignList, ccBinCampaigns))
            )
          else
            ProductData(
              properties = propertyDetail,
              flights = Seq(flightDetail),
              cars = Seq.empty,
              protections = Seq.empty,
              activities = Seq.empty,
              totalPriceDisplay = unconfirmedPriceBreakdown,
              priceDisplayType = priceDisplayType,
              packageToken = packageToken,
              priceChange = None,
              priceConfirmed = flight.isCompleted,
              totalSavings = totalSavings,
              isPromotionCodeEligible = PromocodeUtils.isPromotionCodeEligibleForProperty(propertyDetail, campaignList),
              campaignPromotionInfo = Some(campaignService.getPromotionInfo(properties, campaignList, ccBinCampaigns))
            )
      case BookingFlow.SingleVehicle =>
        for {
          carConfirmationData <- carService.getCarConfirmationData(
                                   carRequestItem = request.productsRequest.getCarRequestOpt.flatMap(_.headOption),
                                   requestId = context.requestContext.correlationId.getOrElse(""),
                                   chargeCurrency = chargeCurrency,
                                   loyaltyRequestOpt = request.loyaltyRequest
                                 )
          carPriceBreakdown <- getSingleVehiclePriceBreakdown(request, chargeCurrency, carConfirmationData)
        } yield ProductData(
          properties = Seq.empty,
          flights = Seq.empty,
          cars = Seq(carConfirmationData),
          protections = Seq.empty,
          activities = Seq.empty,
          totalPriceDisplay = carPriceBreakdown,
          priceDisplayType = if (isSupportPriceDisplayTypesEnabled) Some(PriceDisplayType.Unknown) else None,
          packageToken = None,
          priceChange = None,
          priceConfirmed = true,
          totalSavings = None,
          campaignPromotionInfo = None
        )
      case BookingFlow.MultiHotel =>
        val emptyPackageRequest = PackageRequest("", None)
        val collectProperties =
          (propertyRequests: Seq[PropertyRequestItem], discountRequest: Option[DiscountRequest]) =>
            propertyRequests.foldLeft(Future.successful(Seq.empty[BookingPropertiesData])) {
              (previousProperty, nextProperty) =>
                for {
                  previousResult <- previousProperty
                  nextResult <- propertyService.retrieveProperties(
                                  requestItems = Seq(nextProperty),
                                  devicePlatform = request.deviceContext.fold(DevicePlatform.Unknown)(_.deviceTypeId),
                                  chargeCurrency = chargeCurrency,
                                  packageRequest = previousResult.lastOption.flatMap(_.packageRequest) orElse Some(
                                    emptyPackageRequest
                                  ),
                                  giftCardRedeemRequest = None,
                                  discountRequest = discountRequest,
                                  cardPaymentParams = None,
                                  externalLoyaltyRequest = None,
                                  paymentRequest = request.paymentRequest
                                )
                } yield previousResult ++ nextResult
            }

        for {
          ccBinCampaigns  <- ccBinCampaignsF
          discountRequest <- discountRequestF(ccBinCampaigns)
          properties <- collectProperties(
                          setDFFlagInProductsRequest(
                            request,
                            applyVariableTaxIfNeeded(request)
                          ),
                          discountRequest
                        )
          propertiesWithPreBookingId <- mapOrGeneratePreBookingIdsTo(properties)
          priceBreakdown <-
            priceBreakdownForPackages(properties, request, isInclusivePaySupplier, fixDoubleTaxesAndFeesPaySupplier)
          totalSavings    <- totalSavingsForPackages(properties, request)
          packagesToken    = properties.lastOption.flatMap(_.packageRequest)
          priceDisplayType = if (isSupportPriceDisplayTypesEnabled) Some(PriceDisplayType.Unknown) else None
        } yield ProductData(
          properties = propertiesWithPreBookingId,
          flights = Seq.empty,
          cars = Seq.empty,
          protections = Seq.empty,
          activities = Seq.empty,
          totalPriceDisplay = priceBreakdown,
          priceDisplayType = priceDisplayType,
          packageToken = packagesToken,
          priceChange = None,
          priceConfirmed = getAllPropertyPriceConfirmed(properties),
          totalSavings = totalSavings,
          isPromotionCodeEligible = false,
          campaignPromotionInfo = None
        )
      case BookingFlow.SingleActivity =>
        for {
          activityConfirmationData <- retrieveSingleActivityData(
                                        activityRequestItem = request.productsRequest.activityRequests,
                                        loyaltyRequest = request.loyaltyRequest,
                                        experimentData = request.userContext.flatMap(_.experimentData),
                                        campaignInfoRequest = request.campaignInfo,
                                        chargeCurrency = chargeCurrency
                                      )
          activityPriceBreakdown <- priceBreakdownService.getPriceBreakdownForActivity(
                                      chargeCurrency,
                                      activityConfirmationData.activity,
                                      requestContext.featureAware.exists(_.isPmcWidgetExperiment)
                                    )
          priceDisplayType = if (isSupportPriceDisplayTypesEnabled) Some(PriceDisplayType.Unknown) else None
        } yield {
          val isPmcWidgetEnabled = requestContext.featureAware.exists(_.isPmcWidgetExperiment)

          ProductData(
            properties = Seq.empty,
            flights = Seq.empty,
            cars = Seq.empty,
            protections = Seq.empty,
            activities = Seq(activityConfirmationData),
            totalPriceDisplay = activityPriceBreakdown,
            priceDisplayType = priceDisplayType,
            packageToken = None,
            priceChange = None,
            priceConfirmed = activityConfirmationData.isCompleted,
            totalSavings = None,
            campaignPromotionInfo = PromocodeUtils.mapToPromotionInfoForActivities(
              activityConfirmationData,
              chargeCurrency,
              request.campaignInfo.map(_.cid).getOrElse(-1),
              isPmcWidgetEnabled
            )
          )
        }
      case BookingFlow.Cart =>
        composeCartProductData(
          request = request,
          chargeCurrency = chargeCurrency,
          memberEmail = memberEmail,
          ccBinCampaignsF = ccBinCampaignsF,
          discountRequestF = discountRequestF,
          isFlightCheckInEnabled = isFlightCheckInEnabled,
          loyaltyProfile = loyaltyProfile,
          cardPaymentParams = cardPaymentParams,
          bookingCountryIndicatorOpt = bookingCountryIndicatorOpt,
          rebookAndCancelData = rebookAndCancelData,
          isSupportPriceDisplayTypesEnabled = isSupportPriceDisplayTypesEnabled
        )
      case t => Future.failed(new NotImplementedError(s"product type ${t.toString} is not supported!"))
    }
  }

  private def composeCartProductData(
      request: SetupBookingRequest,
      chargeCurrency: CurrencyCode,
      ccBinCampaignsF: Future[Seq[CampaignInfoInternal]],
      discountRequestF: Seq[CampaignInfoInternal] => Future[Option[DiscountRequest]],
      isFlightCheckInEnabled: Boolean,
      memberEmail: Option[String] = None,
      loyaltyProfile: Option[LoyaltyProfile] = None,
      cardPaymentParams: Option[CardPaymentRequestParameter] = None,
      bookingCountryIndicatorOpt: Option[BookingCountryIndicator] = None,
      rebookAndCancelData: Option[RebookAndCancelData] = None,
      isSupportPriceDisplayTypesEnabled: Boolean = false
  )(implicit context: SetupBookingContext): Future[ProductData] = {
    implicit val requestContext: RequestContext = context.requestContext

    val productsRequest = request.productsRequest
    val propertyRequestsWithDFFlagApply = setDFFlagInProductsRequest(
      request,
      applyVariableTaxIfNeeded(request)
    )

    val partnerPromoUtilsObj           = partnerPromoService.getPartnerPromoUtils(context.whiteLabelInfo)
    val isPartnerPromoEligible         = partnerPromoUtilsObj.isPartnerPromoEligible
    val partnerDiscountRequest         = partnerPromoUtilsObj.getPartnerDiscountRequest(request, memberEmail)
    val partnerPromoEligiblePropertyId = partnerPromoUtilsObj.getPartnerPromoEligiblePropertyId(request, context)

    val cid = request.userContext.flatMap(_.experimentData.flatMap(_.cId))
    val campaignListF = if (context.requestContext.featureAware.exists(_.removeSpBAPIGetPMCCampaignPromotion)) {
      Future.successful(Seq.empty)
    } else {
      campaignService.getCampaignInfo(cid, None, request.productsRequest.propertyRequests.headOption)
    }

    val isInclusivePaySupplier = WhitelabelUtils.isRegulationShowExclusivePriceWithFeeEnabled(
      requestContext
    ) && requestContext.featureAware.exists(_.isInclusivePaySupplier)
    val fixDoubleTaxesAndFeesPaySupplier = requestContext.featureAware.exists(_.fixDoubleTaxesAndFeesPaySupplier)

    val propertiesF =
      (
          propertyRequests: Seq[PropertyRequestItem],
          distributePointsResOpt: Option[DistributePointsResponse],
          normalDiscountRequest: Option[DiscountRequest]
      ) =>
        propertyRequests.foldLeft(Future.successful(Seq.empty[BookingPropertiesData])) {
          (previousProperties, nextProperty) =>
            for {
              previousResult <- previousProperties
              updatedLoyaltyRequest = updateLoyaltyRequestPoints(
                                        request.loyaltyRequest,
                                        CartItemReq.ProductType.HOTELS,
                                        nextProperty.id,
                                        distributePointsResOpt
                                      )
              appliedTravelCreditAmount = partnerPromoUtilsObj.getTravelCreditLeftAmount(
                                            loyaltyProfile,
                                            previousResult
                                          )
              isPartnerPromoEligibleProperty = partnerPromoUtilsObj.isPropertyEligibleForPartnerPromo(
                                                 loyaltyProfile,
                                                 partnerPromoEligiblePropertyId.exists(nextProperty.id.contains)
                                               )
              nextResult <- propertyService.retrieveProperties(
                              requestItems = Seq(nextProperty),
                              devicePlatform = request.deviceContext.fold(DevicePlatform.Unknown)(_.deviceTypeId),
                              chargeCurrency = chargeCurrency,
                              packageRequest = None,
                              giftCardRedeemRequest = None,
                              discountRequest = if (isPartnerPromoEligible) {
                                if (isPartnerPromoEligibleProperty) {
                                  partnerDiscountRequest
                                } else None
                              } else normalDiscountRequest,
                              cardPaymentParams = cardPaymentParams,
                              externalLoyaltyRequest = updatedLoyaltyRequest,
                              paymentRequest = request.paymentRequest,
                              cartRequest = getCartRequestInOrchestration(
                                previousResult.lastOption,
                                request = request,
                                srcIdOpt = Some(nextProperty.id)
                              ),
                              partnerClaimToken = request.loyaltyRequest.flatMap(_.partnerClaimToken),
                              capiToken = request.userContext.flatMap(_.capiToken),
                              additionalPricingParameter = Some(AdditionalPricingParameter(appliedTravelCreditAmount))
                            )
            } yield previousResult ++ nextResult
        }

    val flightF =
      (
          distributePointsResOpt: Option[DistributePointsResponse],
          ccBinCampaigns: Seq[CampaignInfoInternal],
          previousProductResult: Option[BCREInternalProductData]
      ) =>
        retrieveFlightsData(
          enabledFeatures = request.enabledFeatures,
          items = productsRequest.flightRequests,
          chargeCurrency = chargeCurrency,
          setupBookingRequest = request,
          userContext = request.userContext,
          campaignInfo = partnerPromoUtilsObj.getFlightFCampaignInfo(request, productsRequest)(context),
          campaignsIds = partnerPromoUtilsObj.getFlightFCampaignIds(ccBinCampaigns, productsRequest)(context),
          selectedCreditCardTypeId =
            if (
              context.requestContext.featureAware.exists(
                _.migrateFlightToCartFlow(context.requestContext.whiteLabelInfo)
              )
            ) None
            else request.paymentRequest.flatMap(_.selectedPaymentMethod),
          selectedPaymentMethodId =
            if (
              context.requestContext.featureAware.exists(
                _.migrateFlightToCartFlow(context.requestContext.whiteLabelInfo)
              )
            ) request.paymentRequest.flatMap(_.selectedPaymentMethod)
            else None,
          loyaltyRequestOpt = request.loyaltyRequest,
          distributePointsResponseOpt = distributePointsResOpt,
          cartRequest = getCartRequestInOrchestration(
            previousResult = previousProductResult,
            request = request,
            srcIdOpt = request.productsRequest.flightRequests.headOption.flatMap(_.id)
          ),
          aabInfo = request.aabInfo,
          isRetryPayment = request.isRetryPayment
        )
    val flightsDetailF = retrieveFlightsDetail(
      productsRequest.flightRequests,
      chargeCurrency,
      request.userContext,
      request.aabInfo
    )

    val activitiesF = (distributePointsResOpt: Option[DistributePointsResponse]) =>
      retrieveActivitiesData(
        activityRequestItem = productsRequest.activityRequests,
        loyaltyRequest = request.loyaltyRequest,
        experimentData = request.userContext.flatMap(_.experimentData),
        distributePointsResponseOpt = distributePointsResOpt,
        campaignInfoRequest = request.campaignInfo,
        chargeCurrency = chargeCurrency
      )

    val vehiclesF = (distributePointsResOpt: Option[DistributePointsResponse]) =>
      retrieveVehiclesData(
        carRequestItem = productsRequest.carRequestsOpt,
        chargeCurrency = chargeCurrency,
        loyaltyRequestOpt = request.loyaltyRequest,
        distributePointsResponseOpt = distributePointsResOpt
      )

    for {
      campaignList           <- campaignListF
      distributePointsResOpt <- getDistributePoints(request)
      ccBinCampaigns         <- ccBinCampaignsF
      discountRequestOpt     <- discountRequestF(ccBinCampaigns)

      propertiesConfirmationData <-
        propertiesF(propertyRequestsWithDFFlagApply, distributePointsResOpt, discountRequestOpt)
      propertiesWithPreBookingId <- mapOrGeneratePreBookingIdsTo(propertiesConfirmationData)

      childRoom = getChildRoomForCart(request, propertiesConfirmationData)

      flightsConfirmPriceDataMap <-
        flightF(distributePointsResOpt, ccBinCampaigns, propertiesConfirmationData.lastOption)
      flightsItineraryDetailsData <- flightsDetailF
      flightsConfirmPriceData      = flightsConfirmPriceDataMap.values.toSeq

      activityConfirmationsData <- activitiesF(distributePointsResOpt)
      activityValue              = activityConfirmationsData.values.toSeq

      vehiclesConfirmationData <- vehiclesF(distributePointsResOpt)
      vehiclesValue             = vehiclesConfirmationData.values.toSeq

      vehicleProtection <-
        getTripProtectionProductItemsForVehicle(request, vehiclesValue.headOption, chargeCurrency)

      _ = reportCartRestrictedBookingItemsData(propertiesConfirmationData, request)
      addOns <- getAddOnsForCart(
                  request = request,
                  properties = propertiesConfirmationData,
                  flights = flightsConfirmPriceData,
                  cars = vehiclesValue,
                  chargeCurrency = chargeCurrency,
                  bookingCountryIndicatorOpt = bookingCountryIndicatorOpt
                )
      priceBreakdown <- cartPriceBreakdown(
                          request = request,
                          propertyProductItems = propertiesConfirmationData,
                          flights = flightsConfirmPriceDataMap,
                          activities = activityConfirmationsData,
                          vehicles = vehiclesConfirmationData,
                          loyaltyProfile = loyaltyProfile,
                          currencyCode = chargeCurrency,
                          addOnDataV2 = addOns,
                          isFlightCheckInEnabled = isFlightCheckInEnabled,
                          isInclusivePaySupplier = isInclusivePaySupplier,
                          fixDoubleTaxesAndFeesPaySupplier = fixDoubleTaxesAndFeesPaySupplier
                        )
      priceDisplayType = getPriceDisplayTypeForCart(request)

      cart         = CartUtils.extractAllProductCartPricing(request, propertiesConfirmationData, flightsConfirmPriceData)
      cartResponse = cart.map(cart => CartResponse(cart.token))

      priceChange = getPriceChange(request, cart, flightsConfirmPriceData, chargeCurrency)

      allProductConfirmed = getAllPropertyPriceConfirmed(propertiesConfirmationData) &&
                              activityValue.forall(_.isCompleted) &&
                              vehiclesValue.forall(_.isCompleted) &&
                              flightsConfirmPriceData.forall(_.isCompleted)

      campaignInfoInternal = partnerDiscountRequest.flatMap(_.campaignInfos).getOrElse(Seq.empty)

      propertyCampaignPromotionInfo = campaignService.getPromotionInfo(
                                        propertyProductItems = propertiesConfirmationData,
                                        promotionCodeCampaigns = campaignInfoInternal,
                                        ccBinCampaigns = Seq.empty,
                                        roomToBook = childRoom
                                      )
      flightsResponse = getFlightsResponse(flightsConfirmPriceData, flightsItineraryDetailsData, priceBreakdown)
      campaignPromotionInfo = partnerPromoUtilsObj.getCampaignPromotionInfo(
                                propertyCampaignPromotionInfo = propertyCampaignPromotionInfo,
                                ccBinCampaigns = Seq.empty,
                                flights = Some(flightsResponse),
                                currency = chargeCurrency,
                                activities = Some(activityValue),
                                cid = request.campaignInfo.map(_.cid).getOrElse(-1),
                                productsRequest = request.productsRequest
                              )(context)
      isPromotionCodeEligible = partnerPromoUtilsObj.isPartnerPromotionCodeEligible(
                                  partnerDiscountRequest = partnerDiscountRequest,
                                  partnerPromoEligiblePropertyId = partnerPromoEligiblePropertyId,
                                  propertyProductItems = propertiesWithPreBookingId,
                                  campaignList = campaignList,
                                  flightConfirmationData = flightsResponse,
                                  productsRequest = request.productsRequest,
                                  campaignPromotionInfo = campaignPromotionInfo,
                                  activities = activityValue
                                )(context)
      externalLoyaltyAdditionalInfoOpt = ExternalLoyaltyAdditionalInfo(distributePointsResOpt)
      totalSavings                     = totalSavingsF(request, flightsResponse, chargeCurrency)
    } yield ProductData(
      properties = propertiesWithPreBookingId,
      flights = flightsResponse,
      cars = vehiclesValue,
      protections = vehicleProtection,
      activities = activityValue,
      totalPriceDisplay = priceBreakdown,
      priceDisplayType = if (isSupportPriceDisplayTypesEnabled) Some(priceDisplayType) else None,
      packageToken = None,
      priceChange = priceChange,
      priceConfirmed = allProductConfirmed,
      isPromotionCodeEligible = isPromotionCodeEligible,
      campaignPromotionInfo = campaignPromotionInfo,
      cart = cartResponse,
      externalLoyaltyAdditionalInfo = externalLoyaltyAdditionalInfoOpt,
      addOnDataV2 = addOns,
      totalSavings = totalSavings,
      rebookAndCancelData = rebookAndCancelData
    )
  }

  def getChildRoomForCart(
      request: SetupBookingRequest,
      propertiesConfirmationData: Seq[BookingPropertiesData]
  )(implicit context: SetupBookingContext): Option[EnrichedChildRoom] = {
    request.productsRequest.propertyRequests.headOption.flatMap { propertyRequest =>
      extractChildRoom(
        Some(propertyRequest.propertySearchCriteria),
        propertiesConfirmationData.headOption.flatMap(_.papiProperties.flatMap(_.property.headOption))
      )(context.requestContext)
    }
  }

  override def createProductToken(
      productItems: ProductData,
      customerRiskStatus: Option[CustomerRiskStatus] = None,
      customerInfo: Option[CustomerV2] = None,
      allotmentPreCheckResults: Seq[AllotmentPreCheckStatus] = Seq.empty,
      creditCardInfo: Option[CreditCardInfo] = None,
      creditCardValidationInfo: Option[CreditCardInfoSetupResponse] = None,
      paymentRequest: Option[PaymentRequest] = None,
      chargeCurrency: Option[CurrencyCode] = None,
      selectedPaymentMethodDetails: Option[PaymentMethodDetailsV2] = None,
      isNewsletterOptedIn: Boolean,
      productsRequest: ProductsRequest,
      isPriceChangedForRequiredProperty: Option[PriceChangeOnPollingStatus] = None,
      selectedChargeOption: Option[ChargeOption] = None,
      priceChangeStatusOnRooms: Seq[PriceChangePerRoomStatus] = Seq.empty,
      installmentPlanToken: Option[String] = None,
      installmentPlanCode: Option[String] = None,
      loyaltyRequest: Option[LoyaltyRequest] = None,
      loyaltyProfile: Option[LoyaltyProfile] = None,
      enabledFeatures: Option[Seq[String]] = None,
      cartContext: Option[CartContext] = None,
      userTaxCountry: Option[UserTaxCountry] = None,
      aabInfo: Option[AabInfo] = None,
      addOnData: Seq[AddOnDataV2] = Seq.empty,
      itineraryContext: Option[ItineraryContext] = None
  )(implicit context: SetupBookingContext): Try[TokenMessage] = {
    super.createProductToken(
      productItems,
      customerRiskStatus,
      customerInfo,
      allotmentPreCheckResults,
      creditCardInfo,
      creditCardValidationInfo,
      paymentRequest,
      chargeCurrency,
      selectedPaymentMethodDetails,
      isNewsletterOptedIn,
      productsRequest,
      isPriceChangedForRequiredProperty,
      selectedChargeOption,
      priceChangeStatusOnRooms,
      installmentPlanToken,
      installmentPlanCode,
      loyaltyRequest,
      loyaltyProfile,
      enabledFeatures,
      cartContext,
      userTaxCountry,
      aabInfo,
      addOnData,
      itineraryContext = itineraryContext
    )
  }

  override protected[facades] def getIsPartialSuccessAllowed(
      productItems: ProductData,
      enabledFeatures: Option[Seq[CurrencyCode]]
  )(implicit
      context: SetupBookingContext
  ): Boolean = {
    super.getIsPartialSuccessAllowed(productItems, enabledFeatures)
  }

  private def getDistributePoints(
      request: SetupBookingRequest
  )(implicit context: SetupBookingContext): Future[Option[DistributePointsResponse]] = {
    externalLoyaltyService.distributePoints(request, context)
  }

  private def getFlightsResponse(
      flightsConfirmPriceData: Seq[FlightConfirmationData],
      flightsItineraryDetailsData: Seq[FlightConfirmationData],
      priceBreakdown: Option[PriceBreakdownNode]
  )(implicit context: SetupBookingContext): Seq[FlightConfirmationData] = {
    val shouldFixIsFlightConfirmPriceDataReady =
      context.requestContext.featureAware.exists(_.shouldReturnFlightItineraryDetailsWhenFlightConfirmPriceNotCompleted)
    val isFlightConfirmPriceDataCompleted = flightsConfirmPriceData.forall(_.isCompleted)

    val isFlightConfirmPriceDataReady =
      if (shouldFixIsFlightConfirmPriceDataReady)
        isFlightConfirmPriceDataCompleted
      else priceBreakdown.nonEmpty

    if (isFlightConfirmPriceDataReady) {
      flightsConfirmPriceData
    } else {
      flightsItineraryDetailsData
    }
  }
}
