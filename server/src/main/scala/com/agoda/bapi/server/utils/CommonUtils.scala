package com.agoda.bapi.server.utils

import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.creation.model.db.BookingActionState
import com.agoda.mpb.common.MultiProductType
import com.agoda.mpb.common.serialization.Serialization
import com.typesafe.scalalogging.LazyLogging

import scala.util.{Failure, Success, Try}

object CommonUtils extends LazyLogging {

  def getBookingActionsForOperation(
      bookingActions: Seq[BookingWorkflowAction],
      operationId: Long
  ): Seq[BookingWorkflowAction] = bookingActions.filter(_.operationId.contains(operationId))

  def getMasterBookingAction(bookingActions: Seq[BookingWorkflowAction]): Option[BookingWorkflowAction] = {
    bookingActions.find(p => p.bookingId.isEmpty)
  }

  def getPropertyBookingActions(bookingActions: Seq[BookingWorkflowAction]): Seq[BookingWorkflowAction] = {
    bookingActions.filter(p => p.productTypeId.contains(MultiProductType.SingleProperty.id) && p.bookingId.nonEmpty)
  }

  def getBookingActionState(bookingAction: BookingWorkflowAction): Option[BookingActionState] = {
    Try {
      Serialization.fromJson[BookingActionState](bookingAction.state)
    } match {
      case Success(stateOpt) => Some(stateOpt)
      case Failure(exception) =>
        logger.warn(
          s"getBookingActionState cannot deserialize state itineraryId: ${bookingAction.itineraryId}",
          exception
        )
        None
    }
  }

  def getBookingSessionId(
      masterBookingActionState: Option[BookingActionState],
      fallbackBookingSessionId: Option[String] = None
  ): Option[String] = {
    val bookingSessionFromBookingAction = Try(masterBookingActionState.flatMap(_.request.bookingSessionId)) match {
      case Success(bookingSessionIdOpt) => bookingSessionIdOpt
      case Failure(exception) =>
        logger.warn(s"getBookingSessionId cannot get bookingSessionId from master action state", exception)
        None
    }
    bookingSessionFromBookingAction.orElse(fallbackBookingSessionId)
  }

  def getUserId(masterBookingActionState: BookingActionState): Option[String] = {
    Try(Option(masterBookingActionState.request.userId)) match {
      case Success(userIdOpt) => userIdOpt.map(_.toString)
      case Failure(exception) =>
        logger.warn(s"getUserId cannot get userId from master action state", exception)
        None
    }
  }
}
