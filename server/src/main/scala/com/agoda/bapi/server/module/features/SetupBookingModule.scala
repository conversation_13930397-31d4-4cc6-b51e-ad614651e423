package com.agoda.bapi.server.module.features

import cats.effect.IO
import com.agoda.activity.client.ActivitySearchClient
import com.agoda.ancillary.client.AncillaryApiClient
import com.agoda.ancillary.clientV2.api.AncillaryApi
import com.agoda.bapi.common.config._
import com.agoda.bapi.common.proxy.provider.{FraudApiClientProvider, FraudHttpClientProvider}
import com.agoda.bapi.common.proxy.{FraudApiClientProxy, FraudApiClientProxyImpl, FraudHttpClientProxy, FraudHttpClientProxyImpl}
import com.agoda.bapi.common.token.activity.{ActivityTokenService, ActivityTokenServiceImpl}
import com.agoda.bapi.common.token.car.{CarTokenService, CarTokenServiceImpl}
import com.agoda.bapi.common.util.{PaymentUtils, PaymentUtilsImpl}
import com.agoda.bapi.creation.util.{CustomerApiTokenUtils, CustomerApiTokenUtilsImpl}
import com.agoda.bapi.creation.validation.{AllowNoCreditCardChecker, AllowNoCreditCardCheckerImpl, CreditCardChecker, CreditCardCheckerImpl}
import com.agoda.bapi.server.addon.{AddOnBookingTokenService, AddOnBookingTokenServiceImpl}
import com.agoda.bapi.server.config._
import com.agoda.bapi.server.facades.{ProductsFacade, ProductsFacadeImpl, SetupBookingFacade, SetupBookingFacadeImpl}
import com.agoda.bapi.server.handler.context.{SetupBookingContextBuilder, SetupBookingContextBuilderImpl}
import com.agoda.bapi.server.handler.{SetupItineraryHandler, SetupItineraryHandlerImpl}
import com.agoda.bapi.server.module.external.ExternalLoyaltyModule
import com.agoda.bapi.server.proxy._
import com.agoda.bapi.server.proxy.aaprevention.{CegMlEvaluatorApiProxy, CegMlEvaluatorApiProxyImpl}
import com.agoda.bapi.server.proxy.ancillary._
import com.agoda.bapi.server.proxy.ancillary.request.{AncillaryCarMapper, AncillaryFlightMapper, AncillaryPropertyMapper}
import com.agoda.bapi.server.proxy.flight._
import com.agoda.bapi.server.proxy.protection._
import com.agoda.bapi.server.proxy.provider._
import com.agoda.bapi.server.repository._
import com.agoda.bapi.server.repository.aaprevention.{AAPreventionRepository, AAPreventionRepositoryImpl}
import com.agoda.bapi.server.route.{ItineraryContinueBookingRoutes, ItineraryRoutes, Routes}
import com.agoda.bapi.server.service._
import com.agoda.bapi.server.service.aaprevention.{AAPreventionService, AAPreventionServiceImpl}
import com.agoda.bapi.server.service.allotment.{AllotmentPreCheckHelper, AllotmentPreCheckHelperImpl}
import com.agoda.bapi.server.service.bookingFormRegularExpression.{BookingFormRegularExpressionService, BookingFormRegularExpressionServiceImpl}
import com.agoda.bapi.server.service.pricebreakdown.{PriceBreakdownParamComposer, PriceBreakdownParamComposerImpl, PriceBreakdownService, PriceBreakdownServiceImpl}
import com.agoda.bapi.server.utils.partner.{PartnerPromoService, PartnerPromoServiceImpl}
import com.agoda.bapi.server.validator.{RebookAndCancelRequestValidator, RebookAndCancelRequestValidatorImpl, SetupBookingRequestValidator, SetupBookingRequestValidatorImpl}
import com.agoda.cegwl.CegwlApi
import com.agoda.flights.client.v2.api.FlightsApi
import com.agoda.fraud.client.FraudClient
import com.agoda.gandalf.client.api.PromotionApi
import com.agoda.terracotta.clients.rest.FraudRestClient
import com.google.inject.{AbstractModule, TypeLiteral}
import net.codingwell.scalaguice.{ScalaModule, ScalaMultibinder}

import javax.inject.Singleton
import scala.concurrent.Future

class SetupBookingModule extends AbstractModule with ScalaModule {
  private val serverRootPackage = "com.agoda.bapi.server"
  override def configure(): Unit = {
    install(new ExternalLoyaltyModule)
    install(new AddOnModule)
    // config
    bind[Configuration].toProvider[ConfigurationProvider].in[Singleton]
    bind[FlightsApiConfig].toProvider[FlightsApiConfigProvider].in[Singleton]
    bind[GandalfApiConfig].toProvider[GandalfApiConfigProvider].in[Singleton]
    bind[AncillaryApiConfig].toProvider[AncillaryApiPCConfigProvider].in[Singleton]
    bind[VehicleSearchApiConfig].toProvider[VehicleSearchApiConfigProvider].in[Singleton]
    bind[ActivitySearchApiConfig].toProvider[ActivitySearchApiConfigProvider].in[Singleton]
    bind[AAProbabilityThresholdByCountryConfig].toProvider[AAProbabilityThresholdByCountryConfigProvider].in[Singleton]
    bind[CegWlApiConfig].toProvider[CegWlApiConfigProvider].in[Singleton]

    // proxies
    bind[FLAPIClientProxyV2].to[FLAPIClientProxyV2Impl].in[Singleton]
    bind[AncillaryFlightMapper].toInstance(AncillaryFlightMapper)
    bind[AncillaryCarMapper].toInstance(AncillaryCarMapper)
    bind[AncillaryPropertyMapper].toInstance(AncillaryPropertyMapper)
    // remove after BCT-700
    bind[AncillaryProxy].annotatedWithName("AncillaryProxy").to[AncillaryProxyImpl]
    bind[AncillaryProxy].annotatedWithName("AncillaryProxyComposite").to[AncillaryProxyComposite]
    bind[AncillaryProxyV2].to[AncillaryProxyV2Impl]
    bind[ProtectionProxy].annotatedWithName("ProtectionProxy").to[ProtectionProxyImpl]
    bind[ProtectionProxy].annotatedWithName("ProtectionProxyComposite").to[ProtectionProxyComposite]
    bind[ProtectionProxyV2].to[ProtectionProxyV2Impl]
    bind[ProtectionMappingProxy].to[ProtectionMappingProxyImpl]
    bind[VehicleSearchApiClientProxy].to[VehicleSearchApiClientProxyImpl].in[Singleton]
    bind[GandalfProxy].to[GandalfProxyImpl].in[Singleton]
    bind[ActivitySearchApiProxy].to[ActivitySearchApiProxyImpl].in[Singleton]
    bind[CegMlEvaluatorApiProxy].to[CegMlEvaluatorApiProxyImpl].in[Singleton]
    bind[CegWlApiClientProxy].to[CegWlApiClientProxyImpl].in[Singleton]

    // providers
    bind[FlightsApi[Future]].toProvider(classOf[FlightsApiClientV2Provider])
    bind[AncillaryApiClient].toProvider[AncillaryApiPCProvider].in[Singleton]
    bind[AncillaryApi[Future]].toProvider[AncillaryApiV2Provider].in[Singleton]

    bind[VehicleSearchApiHttpClientService]
      .toProvider[VehicleSearchClientProxyProvider]
      .in[Singleton]
    bind[PromotionApi].toProvider[GandalfClientProvider]
    bind(new TypeLiteral[ActivitySearchClient[Future]] {}).toProvider(classOf[ActivitySearchHttpClientProvider])
    bind[OverrideOccupancyConfig].toProvider[OverrideOccupancyConfigProvider].in[Singleton]
    bind[CegwlApi[Future]]
      .toProvider[CegWlClientProvider]
      .in[Singleton]
    bind[FraudClient].toProvider[FraudApiClientProvider].in[Singleton]

    // routes
    val routesMulti = ScalaMultibinder.newSetBinder[Routes](binder)
    routesMulti.addBinding.to[ItineraryContinueBookingRoutes]
    routesMulti.addBinding.to[ItineraryRoutes]

    // validators
    bind[SetupBookingRequestValidator].to[SetupBookingRequestValidatorImpl]
    bind[CreditCardChecker].to[CreditCardCheckerImpl]
    bind[RebookAndCancelRequestValidator].to[RebookAndCancelRequestValidatorImpl]
    // handlers
    bind[SetupItineraryHandler].to[SetupItineraryHandlerImpl]

    // builder
    bind[PriceBreakdownParamComposer].to[PriceBreakdownParamComposerImpl]

    // services
    bind[ActivityService].to[ActivityServiceImpl]
    bind[ActivityTokenService].to[ActivityTokenServiceImpl]
    bind[CarTokenService].to[CarTokenServiceImpl]
    bind[ConsumerFintechService].to[ConsumerFintechServiceImpl]
    bind[SetupBookingFacade].to[SetupBookingFacadeImpl]
    bind[ProductsFacade].to[ProductsFacadeImpl]
    bind[PropertyService].to[PropertyServiceImpl]
    bind[BookingMessagingService].to[BookingMessagingServiceImpl]
    bind[DmcRepository].to[DmcRepositoryImpl]
    bind[FlightsRepository].to[FlapiClientFlightsRepository]
    bind[CarService].to[CarServiceImpl]
    bind[ProtectionService].to[ProtectionServiceImpl]
    bind[SetupBookingContextBuilder].to[SetupBookingContextBuilderImpl]
    bind[SetupPaymentService].to[SetupPaymentServiceImpl]
    bind[PriceBreakdownService].to[PriceBreakdownServiceImpl]
    bind[TotalSavingsService].to[TotalSavingsServiceImpl]
    bind[CampaignService].to[CampaignServiceImpl]
    bind[MixAndSaveService].to[MixAndSaveServiceImpl]
    bind[PaymentUtils].to[PaymentUtilsImpl]
    bind[AllowNoCreditCardChecker].to[AllowNoCreditCardCheckerImpl]
    bind[FraudRestClient].toProvider[FraudHttpClientProvider].in[Singleton]
    bind[FraudHttpClientProxy].to[FraudHttpClientProxyImpl]
    bind[FraudApiClientProxy].to[FraudApiClientProxyImpl]
    bind[BookingConsentService].to[BookingConsentServiceImpl]
    bind[SaveCcofService].to[SaveCcofServiceImpl]
    bind[CegWlService].to[CegWlServiceImpl]
    bind[AAPreventionRepository].to[AAPreventionRepositoryImpl].in[Singleton]
    bind[AAPreventionService].to[AAPreventionServiceImpl].in[Singleton]

    bind[PriceChangeSupplierBlacklistConfig]
      .toProvider[PriceChangeSupplierBlacklistConfigProvider]
      .in[Singleton]
    bind[AllotmentPreCheckHelper].to[AllotmentPreCheckHelperImpl]

    bind[PrecheckRepository].to[PrecheckRepositoryImpl]

    bind[BookingFormRegularExpressionService].to[BookingFormRegularExpressionServiceImpl]

    bind[CustomerMarketingDisplaySettingsService].to[CustomerMarketingDisplaySettingsServiceImpl]

    bind[PartnerPromoService].to[PartnerPromoServiceImpl]

    bind[CustomerApiTokenUtils].to[CustomerApiTokenUtilsImpl]

    bind[AddOnBookingTokenService].to[AddOnBookingTokenServiceImpl]

    bind[RebookAndCancelService].to[RebookAndCancelServiceImpl]

  }
}
