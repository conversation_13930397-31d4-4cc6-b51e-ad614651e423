package com.agoda.bapi.server.proxy

import com.agoda.bapi.common.database.{AGDB, SqlSupport}
import com.agoda.bapi.common.model.{APIKey, BFDBExecutionContext}
import com.agoda.bapi.common.proxy.DependencyNames.Dependency
import com.agoda.bapi.common.proxy.{DBConnectionGroup, DbProxy, DependencyNames, HttpProxy}
import com.agoda.bapi.server.exception.ApiKeyNotFoundException
import com.agoda.sql.ResultSetHelper

import java.sql.ResultSet
import javax.inject.{Inject, Singleton}
import scala.annotation.tailrec
import scala.concurrent.{ExecutionContext, Future}

trait ServerBFDBProxy extends DbProxy {
  def getBFDBHealth: Future[Option[Boolean]]

  def getAPIKey(apiId: Int, clientId: Int): Future[APIKey]

  def getMasterHotelIds(hotelIds: Seq[Int]): Future[Map[Int, Int]]

  def getPreBookingId: Future[Long]

  override def dependency: Dependency = DependencyNames.BfdbBcreMetric
}

@Singleton
@SuppressWarnings(Array("org.wartremover.warts.Null")) // allow nulls because of legacy code
class ServerBfdbProxyImpl @Inject() (
    val db: AGDB,
    bfdbExecutionContext: BFDBExecutionContext
) extends ServerBFDBProxy
    with SqlSupport
    with ResultSetHelper {

  import ServerBfdbProxyImpl._

  implicit val dbDispatcher: ExecutionContext = bfdbExecutionContext.executionContext
  private val bcreConnGroup                   = DBConnectionGroup.BFDB_BCRE

  override def getBFDBHealth: Future[Option[Boolean]] = {
    val queryToExecute = query("SELECT 1")
    executeQuery(
      bcreConnGroup,
      "healthcheck",
      queryToExecute,
      resultSet => Option(resultSet.next())
    )
  }

  override def getPreBookingId: Future[Long] = {
    val queryName = "bcre_select_next_prebookingid_v1"
    executeQuery(
      bcreConnGroup,
      queryName,
      query(s"EXEC dbo.$queryName"),
      mapPrebookingIdResultSet
    )
  }

  override def getAPIKey(apiId: Int, clientId: Int): Future[APIKey] = {
    val queryName      = "bcre_pb_get_api_key_v1"
    val queryToExecute = query(s"EXEC dbo.$queryName @api_id=?, @client_id=?", apiId, clientId)

    executeQuery(
      bcreConnGroup,
      queryName,
      queryToExecute,
      resultSet => resultSetToAPIKey(resultSet, apiId, clientId)
    )
  }

  override def getMasterHotelIds(hotelIds: Seq[Int]): Future[Map[Int, Int]] = {
    val hotelIdsTypeDT = prepareSQLServerDataTable("dbo.IntegerListTableType", hotelIds)
    val queryName      = "bcre_get_master_hotel_ids_for_hotel_ids_v1"
    val queryToExecute = query(s"EXEC dbo.$queryName @hotel_ids=?", hotelIdsTypeDT)

    executeQuery(
      bcreConnGroup,
      queryName,
      queryToExecute,
      getMasterHotelIdsFromResultSet(_).toMap
    )
  }

  @tailrec
  private def getMasterHotelIdsFromResultSet(
      rs: ResultSet,
      result: List[(Int, Int)] = List.empty[(Int, Int)]
  ): List[(Int, Int)] = {
    if (rs.next())
      getMasterHotelIdsFromResultSet(rs, (rs.getInt("hotel_Id") -> rs.getInt("master_hotel_id")) :: result)
    else result.reverse
  }
}

object ServerBfdbProxyImpl {
  val defaultPreBookingId: Int = 0

  def mapPrebookingIdResultSet(rs: ResultSet): Long = {
    if (rs.next())
      rs.getLong("prebooking_id")
    else defaultPreBookingId
  }

  def resultSetToAPIKey(resultSet: ResultSet, apiId: Int, clientId: Int): APIKey =
    if (!resultSet.next())
      throw new ApiKeyNotFoundException(s"Key with apiId $apiId and clientId $clientId not found!")
    else {
      val apiKey = resultSet.getString("api_key")
      val siteId = resultSet.getInt("site_id")
      APIKey(apiKey, siteId)
    }
}
