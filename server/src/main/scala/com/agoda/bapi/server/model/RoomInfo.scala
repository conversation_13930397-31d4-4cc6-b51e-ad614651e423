package com.agoda.bapi.server.model

import api.request.FeatureFlag
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.mapper.booking.RoomSelectionHelper
import com.agoda.bapi.common.message.PropertySearchCriteria
import com.agoda.bapi.common.model.{BookingPaymentModel, InventoryType}
import com.agoda.bapi.common.model.BookingPaymentModel.BookingPaymentModel
import com.agoda.bapi.common.model.InventoryType.InventoryType
import com.agoda.winterfell.Try
import models.pricing.enums.{ChargeTypes, SwapRoomTypes}
import transformers.{EnrichedChildRoom, Property}

case class RoomInfo(
    roomTypeId: Long,
    supplierId: Option[Long],
    bookOnRequest: Boolean,
    paymentModel: Option[BookingPaymentModel],
    merchantOfRecord: Option[Int],
    ratePlanId: Option[Long],
    channelId: Option[Long],
    isNoCreditCardEligible: Boolean,
    supplierPolicyText: Option[String],
    mandatoryExtraBed: Option[Int],
    propertyProductKey: Option[String] = None,
    roomIdentifier: Option[String] = None,
    searchId: Option[String] = None,
    inventoryType: Option[InventoryType] = None,
    allowedGender: Option[String] = None
)

trait RoomInfoMapper {
  def extractChildRoom(
      searchCriteria: Option[PropertySearchCriteria],
      property: Option[Property]
  )(implicit requestContext: RequestContext): Option[EnrichedChildRoom] = {
    property.flatMap { prop =>
      val roomToBookUidOpt = getRoomToBookUid(searchCriteria, property)
      val childRoomSeq     = prop.masterRooms.flatMap(_.childrenRooms)

      roomToBookUidOpt match {
        case Some(roomToBookUid) =>
          childRoomSeq.find(room => room.uid == roomToBookUidOpt || room.roomIdentifiers == roomToBookUidOpt)
        case _ =>
          prop.masterRooms.headOption.flatMap(_.childrenRooms.headOption)
      }
    }
  }

  def extractAllChildrenRooms(property: Option[Property]): Seq[EnrichedChildRoom] = {
    property.flatMap(_.masterRooms.headOption).map(_.childrenRooms).getOrElse(Seq.empty)
  }

  def extractRoomInventoryType(searchCriteria: Option[PropertySearchCriteria], property: Option[Property])(implicit
      requestContext: RequestContext
  ): Option[InventoryType] = {
    val selectedRoom = property.flatMap { prop =>
      getRoomToBookUid(searchCriteria, property) match {
        case Some(roomToBookUid) =>
          prop.booking.flatMap(_.rooms.find(room => room.uid == roomToBookUid))
        case None =>
          prop.booking.flatMap(_.rooms.headOption)
      }
    }
    Try(selectedRoom.flatMap(_.rateCategory.inventoryType).map(InventoryType(_))).toOption.flatten
  }

  private def getRoomToBookUid(searchCriteria: Option[PropertySearchCriteria], property: Option[Property])(implicit
      requestContext: RequestContext
  ): Option[String] = {
    val isPriceChangeSwap =
      property.exists(_.roomSwapping.exists(_.alternativeRoomType == SwapRoomTypes.AlternativeRoom))
    val isRoomAlternativesEnabled =
      !isPriceChangeSwap && RoomSelectionHelper.isAlternativeRoomEnabled(searchCriteria, requestContext)
    val couldDoRateChannelSwap =
      searchCriteria.exists(_.pricingRequest.exists(_.dfFeatureFlags.contains(FeatureFlag.RateChannelSwap.i)))
    if (isRoomAlternativesEnabled || couldDoRateChannelSwap) {
      searchCriteria.flatMap { searchCriteria =>
        searchCriteria.simplifiedRoomSelectionRequest.flatMap { simplifiedRoomSelect =>
          property.flatMap { property =>
            val alternativeRoomOptIn = simplifiedRoomSelect.alternativeOptIn
            val selectRoomIdentifier = simplifiedRoomSelect.roomIdentifier
            val alternativeRoomMap   = property.roomSwapping
            RoomSelectionHelper.getRoomToBookUid(
              alternativeRoomOptIn,
              Some(selectRoomIdentifier),
              alternativeRoomMap,
              Some(property)
            )
          }
        }
      }
    } else {
      None
    }
  }

  private def getMandatoryEB(childRoom: EnrichedChildRoom)(implicit requestContext: RequestContext): Option[Int] =
    childRoom.pricing
      .get(requestContext.userContext.map(_.currency).getOrElse(""))
      .flatMap(_.charges.find(c => c.chargeType == ChargeTypes.ExtraBed))
      .flatMap(_.breakdown.headOption)
      .map(_.quantity)

  private def getChannelId(childRoom: EnrichedChildRoom, propertySearchCriteria: Option[PropertySearchCriteria]) =
    (propertySearchCriteria.flatMap(_.pricingRequest).flatMap(_.selectedPointMaxId), childRoom.pointsMax) match {
      case (Some(pointMaxId), Some(pointsMaxData)) if pointMaxId > 0 => Some(pointsMaxData.channelId)
      case _                                                         => childRoom.channel.map(_.id)
    }

  protected def toRoomInfo(
      property: Option[Property],
      propertySearchCriteria: Option[PropertySearchCriteria],
      propertyProductKey: Option[String] = None
  )(implicit
      requestContext: RequestContext
  ): Option[RoomInfo] =
    for {
      // TODO: handle it properly when we have cross sell
      childRoom       <- extractChildRoom(propertySearchCriteria, property)
      childRoomTypeId <- childRoom.typeId
    } yield RoomInfo(
      roomTypeId = childRoomTypeId,
      supplierId = childRoom.supplierId,
      bookOnRequest = childRoom.roomLevelBOR.isDefined,
      paymentModel = childRoom.payment.map(x => BookingPaymentModel.getFromId(x.paymentModel.i)),
      merchantOfRecord = childRoom.morErpAdminId,
      ratePlanId = childRoom.ratePlan.map(_.id),
      channelId = getChannelId(childRoom, propertySearchCriteria),
      isNoCreditCardEligible = childRoom.payment.exists(_.noCreditCard.isEligible),
      supplierPolicyText = childRoom.dmcPolicyText.map(_.externalData),
      mandatoryExtraBed = getMandatoryEB(childRoom),
      propertyProductKey = propertyProductKey,
      roomIdentifier = childRoom.roomIdentifiers,
      searchId = property.map(_.searchId),
      inventoryType = extractRoomInventoryType(propertySearchCriteria, property),
      allowedGender = property.flatMap(_.rateCategory.flatMap(_.rateCategories.headOption.flatMap(_.gender)))
    )

  protected def toAllRoomsInfo(
      property: Option[Property],
      propertySearchCriteria: Option[PropertySearchCriteria],
      propertyProductKey: Option[String] = None
  )(implicit
      requestContext: RequestContext
  ): Seq[RoomInfo] =
    for {
      childRoom       <- extractAllChildrenRooms(property)
      childRoomTypeId <- childRoom.typeId
    } yield RoomInfo(
      roomTypeId = childRoomTypeId,
      supplierId = childRoom.supplierId,
      bookOnRequest = childRoom.roomLevelBOR.isDefined,
      paymentModel = childRoom.payment.map(x => BookingPaymentModel.getFromId(x.paymentModel.i)),
      merchantOfRecord = childRoom.morErpAdminId,
      ratePlanId = childRoom.ratePlan.map(_.id),
      channelId = getChannelId(childRoom, propertySearchCriteria),
      isNoCreditCardEligible = childRoom.payment.exists(_.noCreditCard.isEligible),
      supplierPolicyText = childRoom.dmcPolicyText.map(_.externalData),
      mandatoryExtraBed = getMandatoryEB(childRoom),
      propertyProductKey = propertyProductKey,
      roomIdentifier = childRoom.roomIdentifiers,
      searchId = property.map(_.searchId),
      allowedGender = property.flatMap(_.rateCategory.flatMap(_.rateCategories.headOption.flatMap(_.gender)))
    )

}
