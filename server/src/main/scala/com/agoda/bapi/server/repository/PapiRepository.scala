package com.agoda.bapi.server.repository

import api.request.FeatureFlag.{<PERSON><PERSON><PERSON><PERSON>, CrossSell, UpsellRoomUpgrade}
import com.agoda.bapi.common.ToolSet
import com.agoda.bapi.common.config.KillSwitches
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.{DevicePlatform, OccupancyRequest}
import com.agoda.bapi.common.model.ChargeOption
import com.agoda.bapi.common.model.consumerFintech.ConsumerFintechRequirement
import com.agoda.bapi.common.model.product.BookingFlow.BookingFlow
import com.agoda.bapi.common.token.property.PriceGuaranteeToken
import com.agoda.bapi.creation.service.{HadoopMessageProcessName, HadoopMessagingService}
import com.agoda.bapi.execution.context.NonBlockingExecutionContext.default
import com.agoda.bapi.server.model.property.BookingProperty
import com.agoda.bapi.server.proxy.{PapiProxy, ServerBFDBProxy}
import com.agoda.bapi.server.reporting.logs.{PapiRepositoryLog, PapiRepositoryLogParams}
import com.agoda.bapi.server.repository.dto.papi.PapiRequestDto
import com.agoda.property.client.ResponseWithJson
import com.google.inject.Inject
import com.softwaremill.quicklens._
import models.starfruit.ReBookingRequest
import request.{FeatureFlagRequest, PropertyRequest}
import transformers.{Properties, Property}

import javax.inject.Singleton
import scala.concurrent.Future
import scala.util.control.NonFatal

trait PapiRepository {
  def getPropertyForBooking(
      papiRequestDto: PapiRequestDto,
      logParams: Option[PapiRepositoryLogParams],
      occupancyRequest: OccupancyRequest,
      consumerFintechRequirement: Option[ConsumerFintechRequirement] = None
  )(implicit
      context: RequestContext,
      bookingFlow: Option[BookingFlow] = None
  ): Future[Option[BookingProperty]]
}

object PapiFeatureFlags extends Enumeration {
  type PapiFeatureFlags = Value
  val M150: PapiFeatureFlags = Value("M150")
}

object PapiRepository {
  import api.request.FeatureFlag
  import api.request.FeatureFlag.{AlternativeRoom, ClientDiscount, MultipleExtraBeds}
  import request.PropertyRequest

  // TODO temporary measure to prevent price mismatches, failure to land to Bookingform or find room in BookingFrom
  // make the PAPI request seem like a dictator request by providing extra properties
  @deprecated("this hack will be removed circa March 2020", "0.104.0")
  def dictatorLike(request: PropertyRequest)(implicit context: RequestContext): PropertyRequest = {
    val featureFlags = List(MultipleExtraBeds, ClientDiscount)
    request.copy(
      featureFlag = request.featureFlag
        .map(featureFlag =>
          featureFlag
            .copy(allowFilterMasterRoom = Some(true), flags = getPapiFeatureFlags(request))
        )
        .orElse(
          Some(
            FeatureFlagRequest(
              allowFilterMasterRoom = Some(true),
              flags = getPapiFeatureFlags(request)
            )
          )
        ),
      pricing = request.pricing.map(p =>
        p.copy(featureFlag =
          request.pricing.map(p => (p.featureFlag.getOrElse(List.empty[FeatureFlag]) ++ featureFlags).distinct)
        )
      ),
      context = request.context.copy(whiteLabelKey = request.context.whiteLabelKey.orElse(context.whiteLabelInfo.token))
    )
  }

  def enrichSalePriceForCancelAndRebookV3(
      request: PropertyRequest,
      consumerFintechRequirement: Option[ConsumerFintechRequirement]
  )(implicit context: RequestContext): PropertyRequest = {
    consumerFintechRequirement
      .flatMap(_.cancelAndRebookRequirement)
      .map { requirement =>
        request.pricing
          .map { pricing =>
            val newPricing = pricing.copy(currency = requirement.saleInclusive.currencyCode)
            // Use with ReBookingRequest to force saleInclusive amount to be in USD amount
            request
              .copy(pricing = Some(newPricing))
              .modify(_.booking.each.reBookingRequest)
              .setTo(
                Some(
                  ReBookingRequest(
                    roomTypeId = requirement.roomTypeId,
                    masterRoomTypeId = None,
                    customerPaidPrice = requirement.saleInclusive.amount.doubleValue(),
                    originalNetIn = None,
                    originalCashback = requirement.originalCashback,
                    originalPromoAmount = requirement.originalPromoAmount,
                    originalUsdToRequestExchangeRate = requirement.originalUsdToRequestExchangeRate,
                    originalSellInUsd = requirement.originalSellInUsd
                  )
                )
              )
          }
          .getOrElse(request)
      }
      .getOrElse(request)
  }

  private[repository] def getPapiFeatureFlags(
      request: PropertyRequest
  )(implicit context: RequestContext): Option[Map[String, Boolean]] = {
    val isPayLaterPayment =
      request.pricing.flatMap(_.paymentRequest).flatMap(_.paymentOption).contains(ChargeOption.PayLater.id)
    if (isPayLaterPayment)
      Some(Map(PapiFeatureFlags.M150.toString -> true))
    else None
  }
}

@Singleton
class PapiRepositoryImpl @Inject() (
    papiProxy: PapiProxy,
    dbProxy: ServerBFDBProxy,
    hadoopMessaging: HadoopMessagingService,
    killSwitches: KillSwitches
) extends PapiRepository
    with ToolSet {
  import PapiRepository._
  import com.agoda.bapi.common.mapper.papi.PapiResponseMapper._

  def getPropertyForBooking(
      papiRequestDto: PapiRequestDto,
      logParams: Option[PapiRepositoryLogParams] = None,
      occupancyRequest: OccupancyRequest,
      consumerFintechRequirement: Option[ConsumerFintechRequirement] = None
  )(implicit
      context: RequestContext,
      bookingFlow: Option[BookingFlow] = None
  ): Future[Option[BookingProperty]] =
    (for {
      masterHotelId                  <- masterHotelId(papiRequestDto.propertyId)
      papiRequestDtoWithMasterHotelId = updatePapiRequestDtoWithMasterHotelId(papiRequestDto, masterHotelId)
      propertyRequest                 = papiRequestDtoWithMasterHotelId.to[request.PropertyRequest]
      propertyRequestForPlatform = if (context.featureAware.exists(_.isAffiliateFeatureFlagEnabled)) propertyRequest
                                   else dictatorLike(propertyRequest)
      propertyRequestForCancelAndRebookV3 =
        if (consumerFintechRequirement.flatMap(_.cancelAndRebookRequirement).isDefined)
          enrichSalePriceForCancelAndRebookV3(propertyRequestForPlatform, consumerFintechRequirement)
        else propertyRequestForPlatform
      papiRequest = propertyRequestForCancelAndRebookV3
      properties <- papiProxy.getPropertyForBooking(papiRequest)
      _ <- if (killSwitches.stopLoggingBapiPapiMessage) Future.successful()
           else sendPapiRequestResponseToHadoop(hadoopMessaging, context, papiRequest, properties)
    } yield properties.response.property.headOption match {
      case Some(property) =>
        val priceGuaranteeToken =
          properties.response.dfMetaResult.propertyToken.map(p => PriceGuaranteeToken(p.token))
        logPapiRepository(
          property,
          papiRequestDto,
          context,
          logParams,
          bookingFlow,
          papiRequest,
          properties.response
        )
        Some(
          BookingProperty(
            property.propertyId,
            calcUsdBookingAmount(property),
            priceGuaranteeToken,
            Some(properties.response),
            properties.jsonString
          )
        )
      case None =>
        logger.log(
          PapiRepositoryLog(
            Some(s"No Property for master hotel id of ${papiRequestDto.propertyId}"),
            context.correlationId,
            logParams.map(_.sessionId).getOrElse(""),
            bookingFlow,
            papiRequest,
            Some(properties.response),
            logParams.flatMap(_.deviceTypeId).getOrElse(DevicePlatform.Unknown),
            logParams.exists(_.hasPackageToken),
            logParams.exists(_.hasCartToken),
            false,
            false
          )
        ) // todo change propertyId
        None
    }).recover {
      case NonFatal(_) => // message is already logged in PapiProxy
        None
    }

  private def sendPapiRequestResponseToHadoop(
      hadoopMessaging: HadoopMessagingService,
      context: RequestContext,
      request: PropertyRequest,
      response: ResponseWithJson[Properties]
  ): Future[Unit] = {
    val responseOpt = response.response.property.headOption
    hadoopMessaging.sendPapiRequestResponse(
      correlationId = context.getCorrelationId(),
      sessionId = context.bookingCreationContext.map(_.sessionId),
      bookingSessionId = context.getBookingSessionId(),
      clientId = context.clientId,
      endpoint = context.path,
      siteId = context.getSiteId(),
      affiliateId = context.userContext.flatMap(_.experimentData.flatMap(_.aId)),
      trackingCookieId = context.userContext.flatMap(_.experimentData.map(_.userId)),
      processName = HadoopMessageProcessName.PapiBooking.toString,
      requestSellIn = request.pricing.flatMap(_.priceAdjustmentRequest.map(_.headOption)).flatten.map(_.requestedPrice),
      papiRequest = request,
      papiResponse = responseOpt
    )
  }

  private def masterHotelIds(hotelIds: Seq[Int]): Future[Map[Int, Int]] =
    dbProxy.getMasterHotelIds(hotelIds)

  private def masterHotelId(hotelId: Long): Future[Long] =
    masterHotelIds(List(hotelId.toInt)).map(hotelIdToMasterHotelId => hotelIdToMasterHotelId(hotelId.toInt))

  protected def updatePapiRequestDtoWithMasterHotelId(
      papiRequestDto: PapiRequestDto,
      masterHotelId: Long
  ): PapiRequestDto =
    papiRequestDto.copy(propertyId = masterHotelId)

  private def logPapiRepository(
      property: Property,
      papiRequestDto: PapiRequestDto,
      context: RequestContext,
      logParams: Option[PapiRepositoryLogParams],
      bookingFlow: Option[BookingFlow],
      propertyRequest: PropertyRequest,
      properties: Properties
  ): Unit = {
    import com.agoda.bapi.server.model.PropertyapiImplicits._

    val masterRoomExists   = property.masterRooms.nonEmpty
    val roomSwappingExists = property.roomSwapping.nonEmpty

    if (!property.hasMasterRoom)
      logger.log(
        PapiRepositoryLog
          .noMasterRooms(
            context.correlationId,
            logParams.map(_.sessionId).getOrElse(""),
            bookingFlow,
            propertyRequest,
            Some(properties),
            logParams.flatMap(_.deviceTypeId),
            logParams.exists(_.hasPackageToken),
            logParams.exists(_.hasCartToken),
            masterRoomExists,
            roomSwappingExists
          )
      )
    else if (!property.hasChildRoom(papiRequestDto.roomIdentifier))
      logger.log(
        PapiRepositoryLog.noChildRoom(
          context.correlationId,
          logParams.map(_.sessionId).getOrElse(""),
          bookingFlow,
          propertyRequest,
          Some(properties),
          logParams.flatMap(_.deviceTypeId),
          logParams.exists(_.hasPackageToken),
          logParams.exists(_.hasCartToken),
          masterRoomExists,
          roomSwappingExists
        )
      )
    else
      logger.log(
        PapiRepositoryLog.success(
          context.correlationId,
          logParams.map(_.sessionId).getOrElse(""),
          bookingFlow,
          propertyRequest,
          Some(properties),
          logParams.flatMap(_.deviceTypeId),
          logParams.exists(_.hasPackageToken),
          logParams.exists(_.hasCartToken),
          masterRoomExists,
          roomSwappingExists
        )
      )
  }
}
