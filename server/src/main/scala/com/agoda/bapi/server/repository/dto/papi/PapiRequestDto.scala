package com.agoda.bapi.server.repository.dto.papi

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.DevicePlatform.DevicePlatform
import com.agoda.bapi.common.message.setupBooking.{LoyaltyRequest, PackageRequest, PaymentRequest}
import com.agoda.bapi.common.message.{OccupancyRequest, _}
import com.agoda.bapi.common.message.{PartnerRequest => PartnersRequest}
import com.agoda.bapi.common.model.ChargeOption.ChargeOption
import com.agoda.bapi.common.model.{CurrencyCode, ExternalServiceMode}
import com.agoda.bapi.common.model.creation.StayType
import com.agoda.bapi.common.model.product.{BookingFlow, BookingFlowType}
import com.agoda.bapi.common.model.product.BookingFlow.BookingFlow
import com.agoda.commons.traffic.{TrafficInfo, TrafficType}
import com.agoda.content.models.db.image.ImageSize
import com.agoda.content.models.request.sf.ContentRateCategoriesRequest
import com.agoda.content.models.{CumulativeRequest, DemographicsRequest, FeaturesRequest, ImagesRequest, KeyImageSize, RateCategoryDetailRequest, RateCategoryRequest, SummariesRequest, SummaryRequest}
import com.agoda.core.search.models.request.{Packaging, PackagingToken}
import com.agoda.papi.enums.room.ChildRateType
import com.agoda.upi.models.request.CartBaseRequest
import models.pricing.enums.{ApplyTypes, SwapRoomTypes}
import models.starfruit
import models.starfruit.{ContextRequest => _, PricingRequest => _, RoomAssignment, _}
import request.{CreditCardPaymentRequest, Experiments, ExternalServiceModes, ForcedExperiment, PAPIRateCategoryRequest, PropertyContext, PropertyPricing, Recommendation, RoomRequestPAPI, SearchTypes}
import api.request.FeatureFlag
import com.agoda.bapi.common.model.ExternalServiceMode.ExternalServiceMode
import org.joda.time.{DateTime, DateTimeZone, LocalDate}

import java.text.Normalizer
import scala.language.postfixOps
import scala.util.{Success, Try}

case class RoomBundleHint(roomIdentifier: String, checkIn: LocalDate, lengthOfStay: Int)

case class PapiRequestDto(
    propertyId: Long,
    roomIdentifier: String,
    priceParameter: PriceParameter,
    contextParameter: ContextParameter,
    bookingParameter: BookingParameter,
    propertyParameter: Option[PropertyParameter],
    roomBundleHint: Option[Seq[RoomBundleHint]],
    cardPaymentRequestParameter: Option[CardPaymentRequestParameter],
    externalLoyaltyRequest: Option[LoyaltyRequest],
    perOccupancyBreakdownType: Option[String],
    partnerClaimToken: Option[String],
    rateCategoryIds: Seq[Long],
    capiToken: Option[String],
    priceAdjustmentId: Option[Long]
) {
  def to[PropertyRequest](implicit mapAction: PapiRequestDto => PropertyRequest) = mapAction(this)
}

object PapiRequestDto {
  val MixAndSaveExperiment  = "DJ-1184"
  val DisableDealForWeb     = "DISABLE-EMPLOYEE-DEAL-FOR-WEB"
  val affiliateStoreFrontId = 89
  val affiliatePlatformId   = 1015
  val papiMobileAppPlatform = 1007
  val papiWeChatMiniProgram = 3001
  val papiJdMiniProgram     = 3002

  def apply(
      propertySearchCriteria: PropertySearchCriteria,
      devicePlatform: DevicePlatform,
      displayCurrency: CurrencyCode,
      priceGuaranteeToken: Option[String],
      bookingFlowType: BookingFlow,
      packagingToken: Option[PackageRequest] = None,
      giftCardRedeemRequest: Option[GiftCardRedeemRequest] = None,
      discountRequest: Option[DiscountRequest] = None,
      roomBundleHint: Option[Seq[RoomBundleHint]] = None,
      selectedChargeOption: Option[ChargeOption] = None,
      cardPaymentRequestParameter: Option[CardPaymentRequestParameter] = None,
      externalLoyaltyRequest: Option[LoyaltyRequest] = None,
      paymentRequest: Option[PaymentRequest] = None,
      cartRequest: Option[CartBaseRequest] = None,
      stateId: Option[Int] = None,
      partnerClaimToken: Option[String] = None,
      rateCategoryIds: Seq[Long] = Seq.empty,
      capiToken: Option[String] = None,
      userTaxCountryCode: Option[String] = None,
      cashbackRedemptionParameter: Option[CashbackRedemptionParameter] = None,
      additionalPricingParameter: Option[AdditionalPricingParameter] = None
  )(implicit context: RequestContext): PapiRequestDto =
    new PapiRequestDto(
      propertyId = propertySearchCriteria.propertyId.getOrElse(0),
      roomIdentifier = propertySearchCriteria.roomIdentifier,
      perOccupancyBreakdownType = propertySearchCriteria.perOccupancyBreakdownType,
      priceParameter = createPriceParameter(
        occupancyRequest = propertySearchCriteria.occupancyRequest,
        durationRequest = propertySearchCriteria.durationRequest,
        pricingRequest = propertySearchCriteria.pricingRequest.get,
        roomSelection = propertySearchCriteria.roomSelectionRequest.map(RoomSelection(_)),
        packageToken = packagingToken.map(p => PackagingToken(p.clientToken, p.interSystemToken)),
        giftCardRedeemRequest = giftCardRedeemRequest,
        discountRequest = discountRequest,
        addMixAndSaveFeatureFlag = roomBundleHint.nonEmpty,
        selectedChargeOption = selectedChargeOption,
        simplifiedRoomSelection = propertySearchCriteria.simplifiedRoomSelectionRequest.map(SimplifiedRoomSelection(_)),
        priceAdjustment = propertySearchCriteria.priceAdjustmentRequest.map(price => price.map(PriceAdjustment(_))),
        partnerRequest = propertySearchCriteria.partnerRequest,
        paymentRequest = paymentRequest,
        cartRequest = cartRequest,
        stateId = stateId,
        userTaxCountryCode = userTaxCountryCode,
        cashbackRedemptionParameter = cashbackRedemptionParameter,
        additionalPricingParameter = additionalPricingParameter
      ),
      contextParameter = createContextParameter(
        devicePlatform,
        propertySearchCriteria.papiContextRequest.get,
        bookingFlow = if (roomBundleHint.nonEmpty) BookingFlow.MixAndSave else bookingFlowType
      ),
      bookingParameter = createBookingParameter(
        priceGuaranteeToken,
        propertySearchCriteria.partnerRequest,
        propertySearchCriteria.durationRequest
      ),
      propertyParameter = propertySearchCriteria.propertyRequest.map(createPropertyParameter(_, bookingFlowType)),
      roomBundleHint = roomBundleHint,
      cardPaymentRequestParameter = cardPaymentRequestParameter.map(c =>
        c.copy(
          ccToken = paymentRequest.flatMap(paymentRequest => paymentRequest.ccToken),
          ccOf = paymentRequest.flatMap(paymentRequest => paymentRequest.ccId),
          installmentInfo = paymentRequest.flatMap(paymentRequest =>
            paymentRequest.installmentPlanCode.map(installmentPlanCode =>
              InstallmentInfo(installmentPlanCode = installmentPlanCode)
            )
          )
        )
      ),
      externalLoyaltyRequest = externalLoyaltyRequest,
      partnerClaimToken = partnerClaimToken,
      rateCategoryIds = rateCategoryIds,
      capiToken = capiToken,
      priceAdjustmentId = propertySearchCriteria.priceAdjustmentId
    )

  def createPriceParameter(
      occupancyRequest: OccupancyRequest,
      durationRequest: DurationRequest,
      pricingRequest: PricingRequest,
      roomSelection: Option[RoomSelection],
      packageToken: Option[PackagingToken],
      giftCardRedeemRequest: Option[GiftCardRedeemRequest] = None,
      discountRequest: Option[DiscountRequest] = None,
      addMixAndSaveFeatureFlag: Boolean = false,
      selectedChargeOption: Option[ChargeOption] = None,
      simplifiedRoomSelection: Option[SimplifiedRoomSelection],
      priceAdjustment: Option[List[PriceAdjustment]],
      partnerRequest: Option[PartnersRequest],
      paymentRequest: Option[PaymentRequest] = None,
      cartRequest: Option[CartBaseRequest] = None,
      stateId: Option[Int] = None,
      userTaxCountryCode: Option[String] = None,
      cashbackRedemptionParameter: Option[CashbackRedemptionParameter] = None,
      additionalPricingParameter: Option[AdditionalPricingParameter] = None
  )(implicit context: RequestContext): PriceParameter = {

    var featureFlags = pricingRequest.dfFeatureFlags

    featureFlags = featureFlags ++ Seq(FeatureFlag.EnableRateCampaignMigration.i)

    if (addMixAndSaveFeatureFlag) {
      featureFlags = featureFlags ++ Seq(FeatureFlag.MixAndSave.i)
    }

    PriceParameter(
      currency = context.userContext.get.currency,
      checkIn = durationRequest.checkIn.toDateTimeAtStartOfDay(DateTimeZone.forOffsetHours(7)),
      lengthOfStay = durationRequest.lengthOfStay,
      room = occupancyRequest.roomCount,
      adult = occupancyRequest.adult,
      child = occupancyRequest.child,
      childAges = occupancyRequest.childAges,
      overrideExtraBed = occupancyRequest.overrideExtraBed,
      ratePlanIds = pricingRequest.ratePlanIds,
      mseHotelIds = pricingRequest.mseHotelIds,
      mseClicked = pricingRequest.mseClicked,
      ppLandingHotelIds = pricingRequest.ppLandingHotelIds.getOrElse(Seq.empty),
      searchedHotelIds = pricingRequest.searchedHotelIds.getOrElse(Seq.empty),
      isMse = pricingRequest.isMse,
      requiredPrice = pricingRequest.requiredPrice,
      requiredBasis = pricingRequest.requiredBasis,
      isRPM2Included = pricingRequest.isRPM2Included,
      selectedPointMaxId = pricingRequest.selectedPointMaxId,
      isIncludeUsdAndLocalCurrency = pricingRequest.isIncludeUsdAndLocalCurrency,
      dfFeatureFlags = featureFlags,
      allowOverrideOccupancy = pricingRequest.allowOverrideOccupancy,
      enableOpaqueChannel = pricingRequest.enableOpaqueChannel,
      isAllowRoomTypeNotGuarantee = Option(pricingRequest.isAllowRoomTypeNotGuarantee),
      synchronous = Option(pricingRequest.synchronous),
      partnerLoyaltyProgramId = pricingRequest.partnerLoyaltyProgramId.get,
      roomSelection = roomSelection,
      packageToken = packageToken,
      giftCardRedeemRequest = giftCardRedeemRequest,
      discountRequest = discountRequest,
      chargeOption = selectedChargeOption.map(_.id),
      simplifiedRoomSelection = simplifiedRoomSelection,
      roomsAssignment = pricingRequest.roomsAssignment,
      priceAdjustmentRequest = priceAdjustment,
      partner = partnerRequest.map(item =>
        starfruit.PartnerRequest(
          partnerRoomRateType = item.partnerRoomRateType,
          partnerSurchargeRateType = item.partnerSurchargeRateType,
          ratePartnerSummaries = item.ratePartnerSummaries,
          discountType = item.discountType,
          isExcludedPfFromTax = item.isExcludedPfFromTax,
          returnDailyRates = item.returnDailyRates
        )
      ),
      isEnabledPriceChannelSelection = pricingRequest.enabledPriceChannelSelection,
      supplierPullMetadataRequest = pricingRequest.precheckAccuracyLevel.map(precheckAccuracyLevel =>
        SupplierPullMetadataRequest(
          requiredPrecheckAccuracyLevel = precheckAccuracyLevel
        )
      ),
      selectedPaymentMethod = paymentRequest.flatMap(_.selectedPaymentMethod),
      bookingDurationType = if (durationRequest.stayType.contains(StayType.Hourly)) {
        Some(List("hourly"))
      } else None,
      selectedRewardOption = pricingRequest.selectedRewardOption,
      cartRequest = cartRequest,
      stateId = stateId,
      userTaxCountryCode = userTaxCountryCode,
      cashbackRedemptionParameter = cashbackRedemptionParameter,
      additionalPricingParameter = additionalPricingParameter
    )
  }

  def createContextParameter(
      devicePlatform: DevicePlatform.DevicePlatform,
      contextRequest: PapiContextRequest,
      bookingFlow: BookingFlow
  )(implicit context: RequestContext): ContextParameter = {
    ContextParameter(
      userId =
        if (context.featureAware.exists(_.isSendEmptyUserIdToPapi)) None
        else context.userContext.flatMap(_.experimentData).map(_.userId),
      correlationId = context.correlationId,
      isUserLoggedIn = context.userContext.exists(_.IsUserLoggedIn),
      cId = Try(
        context.userContext
          .flatMap(_.experimentData)
          .flatMap(_.cId)
          .getOrElse("")
          .toInt
      ).getOrElse(-1),
      locale = context.locale,
      origin = context.userContext.map(_.requestOrigin).getOrElse(""),
      platformId = if (context.featureAware.exists(_.isAffiliateFeatureFlagEnabled)) {
        affiliatePlatformId
      } else if (context.featureAware.exists(_.enableWeChatMiniProgramPlatformMapping)) {
        papiWeChatMiniProgram
      } else if (context.featureAware.exists(_.enableJingDongMiniProgramPlatformMapping)) {
        papiJdMiniProgram
      } else {
        DevicePlatformHelper
          .mapToPapiPlatformId(devicePlatform)
      },
      deviceTypeId = if (context.featureAware.exists(_.isAffiliateFeatureFlagEnabled)) {
        None
      } else {
        Option(devicePlatform.id)
      },
      storeFrontId = if (context.featureAware.exists(_.isAffiliateFeatureFlagEnabled)) {
        Option(affiliateStoreFrontId)
      } else {
        Option(DevicePlatformHelper.mapToPapiStoreFrontId(devicePlatform))
      },
      searchId = java.util.UUID.randomUUID.toString,
      forceByExperiment = getForcedExperimentData(bookingFlow),
      trafficTypeId = contextRequest.trafficTypeId,
      rawBotProfile = Option(contextRequest.rawBotProfile),
      showCms = Option(contextRequest.showCms),
      memberId = context.userContext.map(_.getMemberId).getOrElse(0).toLong,
      isAllowBookOnRequest = Option(contextRequest.isAllowBookOnRequest),
      abTest = contextRequest.abTest,
      ipAddress = context.userContext.flatMap(_.clientIp),
      whiteLabelKey = context.whiteLabelInfo.token,
      isAPO = contextRequest.isAPO,
      pushId = contextRequest.pushId,
      externalPartnerId = context.getExternalPartnerId()
    )
  }

  def createBookingParameter(
      priceGuaranteeToken: Option[String],
      partner: Option[PartnersRequest],
      durationRequest: DurationRequest
  ): BookingParameter =
    BookingParameter(
      priceGuaranteeToken,
      partner.map(item =>
        starfruit.PartnerRequest(
          partnerRoomRateType = item.partnerRoomRateType,
          partnerSurchargeRateType = item.partnerSurchargeRateType,
          ratePartnerSummaries = item.ratePartnerSummaries,
          discountType = item.discountType,
          isExcludedPfFromTax = item.isExcludedPfFromTax,
          returnDailyRates = item.returnDailyRates
        )
      ),
      selectedHourlySlot = durationRequest.selectedHourlySlot.map(s =>
        starfruit.SelectedHourlySlot(
          from = s.from,
          duration = s.duration
        )
      )
    )

  def createPropertyParameter(
      propertyRequest: com.agoda.bapi.common.message.PropertyRequest,
      bookingFlowType: BookingFlow
  ): PropertyParameter =
    PropertyParameter(
      isInformationRequired = propertyRequest.information.isDefined,
      localInformation = propertyRequest.localInformation,
      topSellingPoint = propertyRequest.topSellingPoint,
      recommendation = propertyRequest.recommendation,
      image = Option(
        ImageParameter(
          page = Option(
            com.agoda.content.models.request.Page(
              pageSize = propertyRequest.image
                .flatMap(_.page)
                .map(_.pageSize)
                .getOrElse(0),
              pageNumber = propertyRequest.image
                .flatMap(_.page)
                .map(_.pageNumber)
                .getOrElse(0)
            )
          ),
          indexOffset = propertyRequest.image.flatMap(_.page).map(_.indexOffset),
          sizes = Option(
            propertyRequest.image
              .flatMap(_.size)
              .map(_.sizes)
              .getOrElse(Map.empty)
              .map(_ match {
                case (k, v) =>
                  k -> ImageSize(v.width, v.height, v.aspectRatio)
              })
          ),
          maxWidth = propertyRequest.image.flatMap(_.size).map(_.maxWidth),
          maxHeight = propertyRequest.image.flatMap(_.size).map(_.maxHeight)
        )
      ),
      featureFlag = Option(
        FeatureFlagParameter(
          isAllowBookOnRequest = propertyRequest.featureFlag
            .map(_.isAllowBookOnRequest)
            .getOrElse(false),
          showUnAvailable = propertyRequest.featureFlag.flatMap(_.showUnAvailable),
          filterFavorites = propertyRequest.featureFlag.flatMap(_.filterFavorites),
          isReviewSortFilter = propertyRequest.featureFlag.flatMap(_.isReviewSortFilter),
          isFullMatrixItems = propertyRequest.featureFlag.flatMap(_.isFullMatrixItems),
          includeSoldOutRooms = propertyRequest.featureFlag.flatMap(_.includeSoldOutRooms),
          soldOutRoomsSummary = propertyRequest.featureFlag.flatMap(_.soldOutRoomsSummary),
          bookingHistory = propertyRequest.featureFlag.flatMap(_.bookingHistory),
          promotionUrgencyMessage = propertyRequest.featureFlag.flatMap(_.promotionUrgencyMessage),
          showMinLOSProperty = propertyRequest.featureFlag.flatMap(_.showMinLOSProperty),
          showBeachIcon = propertyRequest.featureFlag.flatMap(_.showBeachIcon),
          showRemainingProperties = propertyRequest.featureFlag.flatMap(_.showRemainingProperties),
          soldOutSimilarProperties = propertyRequest.featureFlag.flatMap(_.soldOutSimilarProperties),
          showPriceHistory = propertyRequest.featureFlag.flatMap(_.showPriceHistory),
          allowFilterMasterRoom = propertyRequest.featureFlag.flatMap(_.allowFilterMasterRoom),
          showSoldOutRooms = propertyRequest.featureFlag.flatMap(_.showSoldOutRooms),
          showInstallmentDetails = propertyRequest.featureFlag.flatMap(_.showInstallmentDetails),
          fiveStarDealOfTheDay = propertyRequest.featureFlag.flatMap(_.fiveStarDealOfTheDay),
          cheaperThanDaysWindowPrice = propertyRequest.featureFlag.flatMap(_.cheaperThanDaysWindowPrice),
          flags = propertyRequest.featureFlag.flatMap(_.flags),
          showPaymentTypes = propertyRequest.featureFlag.flatMap(_.showPaymentTypes),
          calculateCancellationPhases = propertyRequest.featureFlag.flatMap(_.calculateCancellationPhases),
          isEnableSupplierFinancialInfo = propertyRequest.featureFlag.flatMap(_.isEnableSupplierFinancialInfo),
          showRecentlyBookingPrice = propertyRequest.featureFlag.flatMap(_.showRecentlyBookingPrice),
          showHotelMetaRanking = propertyRequest.featureFlag.flatMap(_.showHotelMetaRanking),
          isMultiHotelSearch = if (bookingFlowType == BookingFlow.MultiHotel) Some(true) else None,
          enableAgencySupplyForPackages = propertyRequest.featureFlag.flatMap(_.enableAgencySupplyForPackages),
          enableEscapesPackage = propertyRequest.featureFlag.flatMap(_.enableEscapesPackage),
          enableRichContentOffer = propertyRequest.featureFlag.flatMap(_.enableRichContentOffer),
          enableRatePlanCheckInCheckOut = propertyRequest.featureFlag.flatMap(_.enableRatePlanCheckInCheckOut),
          enablePushDayUseRates = propertyRequest.featureFlag.flatMap(_.enablePushDayUseRates),
          enableCOR = propertyRequest.featureFlag.flatMap(_.enableCOR)
        )
      ),
      isReviewRequired = propertyRequest.isReviewRequired,
      isCumulativeReviewRequired = propertyRequest.isCumulativeReviewRequired,
      isSnippetReviewRequired = propertyRequest.isSnippetReviewRequired,
      room = propertyRequest.room.map { room =>
        RoomParameter(
          images = room.images.map(image =>
            RoomImageParameter(
              sizes = Some(
                image.sizes.map(size => size._1 -> ImageSize(size._2.width, size._2.height, size._2.aspectRatio))
              ),
              maxWidth = Some(image.maxWidth),
              maxHeight = Some(image.maxHeight)
            )
          ),
          externalServiceMode = room.externalServiceMode
        )
      },
      features = propertyRequest.features.map(feature => FeatureParameter(includeMissing = feature.includeMissing))
    )

  private def maskEmailForDiscountRequest(
      email: String
  ): String = {
    val normalizedEmail = Normalizer.normalize(email, Normalizer.Form.NFKC)
    normalizedEmail.split("@") match {
      case Array(emailName, domain) =>
        "*" * emailName.length + '@' + domain
      case _ =>
        "*" * email.length
    }
  }

  implicit def papiRequestMapper(
      requestDto: PapiRequestDto
  )(implicit bookingFlow: Option[BookingFlow], requestContext: RequestContext): request.PropertyRequest = {
    // Always send priceState as Some of empty string when no value.
    // This will help enable price guarantee
    // We will only enable this for single hotel only as we found issue with Mix&Save call.
    val priceStateRequest =
      if (bookingFlow.contains(BookingFlow.SingleProperty))
        requestDto.bookingParameter.priceGuaranteeToken
          .map(pt => PriceStateRequest(pt))
          .orElse(Some(PriceStateRequest("")))
      else
        requestDto.bookingParameter.priceGuaranteeToken
          .map(pt => PriceStateRequest(pt))
    val bookingRequest = BookingRequest(
      filters =
        if (requestDto.roomBundleHint.isEmpty)
          List(
            BookingFilter(
              // affiliate api use old filtering way for the rooms: filter room by uid, not roomIdentifier
              // affiliate always provides the priceAdjustmentRequest fields to tweak price for every room uid,
              // therefore for priceAdjustment case papi will filter room by uid, not roomIdentifier
              // TODO: proper fix: MPBE-2116 required exposing the roomUid fields in setup contract.
              uidList =
                requestDto.priceParameter.priceAdjustmentRequest.map(_.map(adjustment => adjustment.roomId).distinct),
              roomIdentifiers = List(
                RoomIdentifier(
                  uid = requestDto.roomIdentifier, // TODO: need to check if price freeze need to overriding it.
                  overrideIdentifier =
                    OverrideRoomIdentifier(numberOfExtrabed = requestDto.priceParameter.overrideExtraBed)
                )
              )
            )
          )
        else List.empty,
      priceState = priceStateRequest,
      partner = requestDto.bookingParameter.partner,
      selectedHourlySlot = requestDto.bookingParameter.selectedHourlySlot,
      priceAdjustmentId = requestDto.priceAdjustmentId
    )

    val context = PropertyContext(
      userId = requestDto.contextParameter.userId,
      cid = requestDto.contextParameter.cId,
      locale = requestDto.contextParameter.locale,
      memberId = requestDto.contextParameter.memberId,
      origin = requestDto.contextParameter.origin,
      platform = requestDto.contextParameter.platformId,
      deviceTypeId = requestDto.contextParameter.deviceTypeId,
      storeFrontId = requestDto.contextParameter.storeFrontId,
      propertyIds = List(requestDto.propertyId),
      searchId = requestDto.contextParameter.searchId,
      showCMS = requestDto.contextParameter.showCms,
      isAllowBookOnRequest = requestDto.contextParameter.isAllowBookOnRequest,
      isMSE = Some(requestDto.priceParameter.isMse),
      trafficInfo = Option(
        TrafficInfo(
          correlationId =
            Try(java.util.UUID.fromString(requestDto.contextParameter.correlationId.getOrElse(""))) match {
              case Success(value) => Some(value)
              case _              => Some(java.util.UUID.randomUUID())
            },
          trafficType = TrafficType.getByTypeIndex(requestDto.contextParameter.trafficTypeId.getOrElse(0)),
          rawBotProfile = requestDto.contextParameter.rawBotProfile
        )
      ),
      experiments = Option(
        Experiments(
          forceByVariant = None,
          forceByExperiment = Some(requestDto.contextParameter.forceByExperiment.map {
            case (experimentId, variant) =>
              ForcedExperiment(experimentId, variant)
          } toVector)
        )
      ),
      abTest = Some(requestDto.contextParameter.abTest.map(x => starfruit.ABTest(x.testId, x.abUser))),
      ipAddress = requestDto.contextParameter.ipAddress,
      cartRequest = requestDto.priceParameter.cartRequest,
      whiteLabelKey = requestDto.contextParameter.whiteLabelKey,
      isAPO = requestDto.contextParameter.isAPO,
      pushId = requestDto.contextParameter.pushId,
      partnerClaimToken = requestDto.partnerClaimToken,
      capiToken = requestDto.capiToken,
      externalPartnerId = requestDto.contextParameter.externalPartnerId
    )
    val cardPaymentRequest = requestDto.cardPaymentRequestParameter.map { paymentRequest =>
      CreditCardPaymentRequest(
        paymentRequest.paymentCurrency,
        paymentRequest.creditCardCurrency,
        paymentRequest.chargedDateOption,
        paymentRequest.countryIdOfIssuingBank,
        paymentRequest.ccOf,
        paymentRequest.ccToken
      )
    }

    val installmentInfo = requestDto.cardPaymentRequestParameter.flatMap(c =>
      c.installmentInfo.map(i => starfruit.InstallmentInfo(i.installmentPlanCode))
    )

    val roomAssignment: Option[List[RoomAssignment]] = requestDto.priceParameter.roomsAssignment.map { roomAssignment =>
      roomAssignment.map { room =>
        starfruit.RoomAssignment(
          adults = room.adults,
          childrenTypes = room.childrenTypes.map { child =>
            starfruit.ChildType(
              childRateType = ChildRateType.getFromValue(child.childRateType.id),
              quantity = child.quantity
            )
          }
        )
      }
    }

    val pricing = PropertyPricing(
      allowOverrideOccupancy = requestDto.priceParameter.allowOverrideOccupancy,
      bookingDate = DateTime.parse("2020-07-16T11:43:47.936+07:00"),
      checkIn = requestDto.priceParameter.checkIn,
      currency = requestDto.priceParameter.currency,
      enableOpaqueChannel = requestDto.priceParameter.enableOpaqueChannel,
      isIncludeUsdAndLocalCurrency = requestDto.priceParameter.isIncludeUsdAndLocalCurrency,
      isRPM2Included = requestDto.priceParameter.isRPM2Included,
      isAllowRoomTypeNotGuarantee = requestDto.priceParameter.isAllowRoomTypeNotGuarantee,
      isUserLoggedIn = requestDto.contextParameter.isUserLoggedIn,
      lengthOfStay = requestDto.priceParameter.lengthOfStay,
      numberOfAdults = requestDto.priceParameter.adult,
      numberOfChildren = requestDto.priceParameter.child,
      numberOfRooms = requestDto.priceParameter.room,
      partnerLoyaltyProgramId = requestDto.priceParameter.partnerLoyaltyProgramId,
      ratePlanId = requestDto.priceParameter.ratePlanIds.toList,
      partner = requestDto.priceParameter.partner,
      requiredBasis = if (requestDto.priceParameter.requiredBasis.isEmpty) {
        None
      } else {
        Option(ApplyTypes.getApplyType(requestDto.priceParameter.requiredBasis))
      },
      requiredPrice = if (requestDto.priceParameter.requiredPrice.isEmpty) {
        None
      } else {
        Option(SuggestedPrice.getSuggestedPrice(requestDto.priceParameter.requiredPrice))
      },
      isEnabledPartnerChannelSelection = requestDto.priceParameter.isEnabledPriceChannelSelection,
      supplierPullMetadataRequest = requestDto.priceParameter.supplierPullMetadataRequest,
      synchronous = requestDto.priceParameter.synchronous,
      childAges = Option(requestDto.priceParameter.childAges.map(Option(_)).toList),
      storeFrontId = requestDto.contextParameter.storeFrontId,
      featureFlag = Option(
        requestDto.priceParameter.dfFeatureFlags
          .map(feature => FeatureFlag.getFeatureFlag(feature)) toList
      ),
      roomSelection = requestDto.priceParameter.roomSelection.map(roomSelection =>
        starfruit.RoomSelectionRequest(
          occupancy = roomSelection.occupancy,
          children = roomSelection.children,
          extraBed = roomSelection.extraBed,
          mandatoryExtraBed = roomSelection.mandatoryExtraBed,
          room = roomSelection.room,
          isRespectMaxOcc = roomSelection.isRespectMaxOcc,
          needOccupancySearch = roomSelection.needOccupancySearch,
          masterRoomOccupancy = roomSelection.masterRoomOccupancy,
          isFit = roomSelection.isFit,
          requestExtraBedForRoomNumbers = roomSelection.requestExtraBedForRoomNumbers
        )
      ),
      mseHotelIds = Option(requestDto.priceParameter.mseHotelIds.toList),
      mseClicked = requestDto.priceParameter.mseClicked,
      ppLandingHotelIds = Option(requestDto.priceParameter.ppLandingHotelIds.toList),
      searchedHotelIds = Option(requestDto.priceParameter.searchedHotelIds.toList),
      packaging = requestDto.priceParameter.packageToken.map { e =>
        if (e.clientToken.isEmpty && e.interSystemToken.isEmpty)
          Packaging(None)
        else
          Packaging(Option(PackagingToken(e.clientToken, e.interSystemToken)))
      },
      discountRequest = requestDto.priceParameter.discountRequest.map { d =>
        val cInfo = d.campaignInfos.map(_.map { c =>
          models.starfruit
            .CampaignInfo(c.campaignInfo.id, c.campaignInfo.cid, c.campaignInfo.promotionCode, c.promotionId)
        })
        // Mask email leaving the domain
        /* TODO: pending FUSION-6034 to be ready so we'll send the domain value with the new field and fully deprecate
         * email field */
        models.starfruit.DiscountRequest(email = d.email.map(maskEmailForDiscountRequest), campaignInfos = cInfo)
      },
      paymentRequest = Some(
        request.PaymentRequest(
          paymentOption = requestDto.priceParameter.chargeOption,
          loyalty = requestDto.priceParameter.giftCardRedeemRequest.map { giftCard =>
            LoyaltyPaymentRequest(
              giftCard.giftCardRedeemAmount.toDouble,
              giftCard.giftCardBalanceInUsd.toDouble,
              LoyaltyTypes.GiftCard,
              None
            )
          },
          creditCardPayment = cardPaymentRequest,
          installmentInfo = installmentInfo,
          customerTaxCountryCode = requestDto.priceParameter.userTaxCountryCode,
          cashback = requestDto.priceParameter.cashbackRedemptionParameter.map { c =>
            LoyaltyPaymentRequest(
              c.cashbackRedeemAmount.toDouble,
              c.cashbackAvailableBalance.toDouble,
              LoyaltyTypes.Cashback,
              None
            )
          },
          isCashbackRedemptionEligible = requestDto.priceParameter.cashbackRedemptionParameter.map { c =>
            c.isCashbackRedemptionEligible
          }
        )
      ),
      roomBundleHints = requestDto.roomBundleHint.map { hints: Seq[RoomBundleHint] =>
        hints.map { hint: RoomBundleHint =>
          RoomBundleRequest(
            checkin = hint.checkIn.toDateTimeAtStartOfDay(),
            los = hint.lengthOfStay,
            roomIdentifier = Some(
              RoomIdentifier(
                uid = hint.roomIdentifier,
                overrideIdentifier = OverrideRoomIdentifier()
              )
            )
          )
        }.toList
      },
      simplifiedRoomSelectionRequest = requestDto.priceParameter.simplifiedRoomSelection.map(sr =>
        starfruit.SimplifiedRoomSelectionRequest(
          roomIdentifier = sr.roomIdentifier,
          room = sr.requestedRoomNumbers,
          requestExtraBedForRoomNumbers = sr.requestExtraBedForRoomNumbers,
          selectedUpSellingRoomType =
            sr.alternativeOptIn.flatMap(_.swapRoomType.map(SwapRoomTypes.getSwapRoomOptions(_)))
        )
      ),
      roomsAssignment = roomAssignment,
      priceAdjustmentRequest = requestDto.priceParameter.priceAdjustmentRequest.map(
        _.map(priceAdjustment =>
          starfruit.PriceAdjustment(
            roomId = priceAdjustment.roomId,
            requestedPrice = priceAdjustment.requestedPrice,
            chargeType = priceAdjustment.chargeType,
            rateType = priceAdjustment.rateType,
            applyType = priceAdjustment.applyType,
            chargeOption = priceAdjustment.pricingChargeOption,
            surchargeId = priceAdjustment.surchargeId,
            requestCurrency = priceAdjustment.requestCurrency
          )
        )
      ),
      externalLoyalty = requestDto.externalLoyaltyRequest.map(externalLoyalty =>
        ExternalLoyaltyRequest(
          selectedOffersIdentifier = externalLoyalty.selectedOfferIdentifier,
          points = externalLoyalty.points,
          partnerClaimToken = externalLoyalty.partnerClaimToken,
          loyaltySearchType = externalLoyalty.loyaltySearchType
        )
      ),
      paymentId = requestDto.priceParameter.selectedPaymentMethod,
      bookingDurationType = requestDto.priceParameter.bookingDurationType,
      selectedRewardOption = requestDto.priceParameter.selectedRewardOption,
      stateId = requestDto.priceParameter.stateId,
      externalLoyaltyRemainingBenefitCredit =
        requestDto.priceParameter.additionalPricingParameter.flatMap(_.travelCreditAmountLeft)
    )

    val images = requestDto.propertyParameter.map { propertyParameter =>
      ImagesRequest(
        page = propertyParameter.image.flatMap(_.page),
        indexOffset = propertyParameter.image.flatMap(_.indexOffset),
        sizes = propertyParameter.image.flatMap(_.sizes),
        maxWidth = propertyParameter.image.flatMap(_.maxWidth),
        maxHeight = propertyParameter.image.flatMap(_.maxHeight)
      )
    }

    val featureFlag = requestDto.propertyParameter.map { propertyParameter =>
      request.FeatureFlagRequest(
        isAllowBookOnRequest = propertyParameter.featureFlag
          .map(_.isAllowBookOnRequest)
          .getOrElse(false),
        showUnAvailable = propertyParameter.featureFlag
          .map(_.showUnAvailable)
          .getOrElse(Some(false)),
        filterFavorites = propertyParameter.featureFlag
          .map(_.filterFavorites)
          .getOrElse(Some(false)),
        isReviewSortFilter = propertyParameter.featureFlag
          .map(_.isReviewSortFilter)
          .getOrElse(Some(false)),
        isFullMatrixItems = propertyParameter.featureFlag
          .map(_.isFullMatrixItems)
          .getOrElse(Some(false)),
        includeSoldOutRooms = propertyParameter.featureFlag
          .map(_.includeSoldOutRooms)
          .getOrElse(Some(false)),
        bookingHistory = propertyParameter.featureFlag
          .map(_.bookingHistory)
          .getOrElse(Some(false)),
        promotionUrgencyMessage = propertyParameter.featureFlag
          .map(_.promotionUrgencyMessage)
          .getOrElse(Some(false)),
        showBeachIcon = propertyParameter.featureFlag
          .map(_.showBeachIcon)
          .getOrElse(Some(false)),
        showRemainingProperties = propertyParameter.featureFlag
          .map(_.showRemainingProperties)
          .getOrElse(Some(false)),
        soldOutSimilarProperties = propertyParameter.featureFlag
          .map(_.soldOutSimilarProperties)
          .getOrElse(Some(false)),
        showPriceHistory = propertyParameter.featureFlag
          .map(_.showPriceHistory)
          .getOrElse(Some(false)),
        allowFilterMasterRoom = propertyParameter.featureFlag
          .flatMap(_.allowFilterMasterRoom),
        showSoldOutRooms = propertyParameter.featureFlag
          .map(_.showSoldOutRooms)
          .getOrElse(Some(false)),
        showInstallmentDetails = propertyParameter.featureFlag
          .map(_.showInstallmentDetails)
          .getOrElse(Some(false)),
        fiveStarDealOfTheDay = propertyParameter.featureFlag
          .map(_.fiveStarDealOfTheDay)
          .getOrElse(Some(false)),
        cheaperThanDaysWindowPrice = propertyParameter.featureFlag
          .map(_.cheaperThanDaysWindowPrice)
          .getOrElse(Some(false)),
        flags = propertyParameter.featureFlag
          .map(_.flags)
          .getOrElse(Some(Map.empty)),
        showPaymentTypes = propertyParameter.featureFlag
          .map(_.showPaymentTypes)
          .getOrElse(Some(false)),
        calculateCancellationPhases = propertyParameter.featureFlag
          .map(_.calculateCancellationPhases)
          .getOrElse(Some(false)),
        isEnableSupplierFinancialInfo = propertyParameter.featureFlag
          .map(_.isEnableSupplierFinancialInfo)
          .getOrElse(Some(false)),
        isMultiHotelSearch = propertyParameter.featureFlag
          .map(_.isMultiHotelSearch)
          .getOrElse(Some(false)),
        enableAgencySupplyForPackages = propertyParameter.featureFlag
          .map(_.enableAgencySupplyForPackages)
          .getOrElse(Some(false)),
        enableEscapesPackage = propertyParameter.featureFlag
          .map(_.enableEscapesPackage)
          .getOrElse(Some(false)),
        enableRichContentOffer = propertyParameter.featureFlag
          .flatMap(_.enableRichContentOffer),
        enableRatePlanCheckInCheckOut = propertyParameter.featureFlag
          .flatMap(_.enableRatePlanCheckInCheckOut),
        enablePushDayUseRates = propertyParameter.featureFlag
          .flatMap(_.enablePushDayUseRates),
        enableCOR = propertyParameter.featureFlag
          .flatMap(_.enableCOR)
      )
    }

    val localInformation = requestDto.propertyParameter
      .flatMap(_.localInformation.map { localInfo =>
        com.agoda.content.models.LocalInformationRequest(
          localInfo.images.map { image =>
            com.agoda.content.models.db.image.ImageSizeRequest(
              imageSizes = Some(image.sizes.flatMap {
                case (key, size) =>
                  List(KeyImageSize(key, ImageSize(size.width, size.height, size.aspectRatio)))
              }.toList),
              maxWidth = Some(image.maxWidth),
              maxHeight = Some(image.maxHeight)
            )
          }
        )
      })

    val features = requestDto.propertyParameter.flatMap(property =>
      property.features.map(features =>
        FeaturesRequest(
          includeMissing = features.includeMissing.getOrElse(false),
          tags = None,
          groupType = None
        )
      )
    )

    request.PropertyRequest(
      searchType = Some(SearchTypes.Booking),
      booking = Some(bookingRequest),
      context = context,
      pricing = Some(pricing),
      images = images,
      features = features,
      featureFlag = featureFlag,
      // needed to get property infoSummary
      summaryRequest = Option(SummaryRequest(Some("0"))),
      information =
        if (requestDto.propertyParameter.exists(_.isInformationRequired))
          Some(com.agoda.content.models.InformationRequest(None, None))
        else None,
      localInformation = localInformation,
      topSellingPointRequest = requestDto.propertyParameter
        .flatMap(
          _.topSellingPoint
            .map(_.topSellingPoints)
            .map(request.TopSellingPointRequest)
        ),
      recommendation = requestDto.propertyParameter
        .flatMap(_.recommendation.map(r => Recommendation(r.numberOfRecommendations, r.overrideList))),
      reviewDemographics =
        if (requestDto.propertyParameter.exists(_.isReviewRequired))
          Some(DemographicsRequest(None))
        else None,
      reviewCumulative =
        if (requestDto.propertyParameter.exists(_.isCumulativeReviewRequired))
          Some(CumulativeRequest(None))
        else None,
      reviewSnippet =
        if (requestDto.propertyParameter.exists(_.isSnippetReviewRequired))
          Some(SummariesRequest(None, apo = false))
        else None,
      rateCategories = Some(ContentRateCategoriesRequest(None, None)),
      rateCategory = Some(
        PAPIRateCategoryRequest(requestDto.rateCategoryIds.to[Vector], Some(RateCategoryDetailRequest(None)), None)
      ),
      room = requestDto.propertyParameter
        .flatMap(_.room)
        .map { room =>
          RoomRequestPAPI(
            images = room.images.map(img =>
              com.agoda.content.models.db.image.ImageSizeRequest(
                sizes = img.sizes,
                maxWidth = img.maxWidth,
                maxHeight = img.maxHeight,
                imageSizes = img.sizes
                  .map(_.map(size => KeyImageSize(size._1, size._2)).toList)
              )
            ),
            isContentOnly = None,
            externalServiceMode = room.externalServiceMode match {
              case Some(ExternalServiceMode.PricingOnly) => Some(ExternalServiceModes.PricingOnly)
              case Some(ExternalServiceMode.ContentOnly) => Some(ExternalServiceModes.ContentOnly)
              case _                                     => Some(ExternalServiceModes.All)
            }
          )
        },
      perOccupancyBreakdownType = requestDto.perOccupancyBreakdownType.map(ApplyTypes.getApplyType)
    )
  }

  private[dto] def getForcedExperimentData(bookingFlow: BookingFlow)(implicit
      context: RequestContext
  ): Map[String, String] = {
    val forcedExperimentData = context.userContext.flatMap(_.experimentData).flatMap(_.force).getOrElse(Map.empty)

    val forcedExperimentDataByBookingFlow: Map[String, String] = bookingFlow match {
      case BookingFlow.SingleProperty => Map(DisableDealForWeb -> "A")
      case BookingFlow.Cart           => Map(DisableDealForWeb -> "A")
      case BookingFlow.MixAndSave     => Map(MixAndSaveExperiment -> "B")
      case _                          => Map.empty
    }

    forcedExperimentData ++ forcedExperimentDataByBookingFlow
  }
}
