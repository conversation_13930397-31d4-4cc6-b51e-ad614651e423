package com.agoda.bapi.server.validator

import com.agoda.bapi.common.config.Configuration
import com.agoda.bapi.common.message._
import com.agoda.bapi.common.message.creation.CreatedBookingStatus
import com.agoda.bapi.common.message.flightBookingReplicateByItineraryID.{FlightBookingReplicateByItineraryIDRequest, FlightBookingReplicateByItineraryIDResponse}
import com.agoda.bapi.common.message.multi.product._
import com.google.inject.Inject

trait BookingsRequestValidator {

  def validateSetFlightBookingState(request: SetStateRequest): Option[SetStateResponse]
  def validateSetItineraryBookingState(request: SetStateRequest): Option[SetStateResponse]
  def validateFlightBookingReplicateByItineraryIDRequest(
      request: FlightBookingReplicateByItineraryIDRequest
  ): Option[FlightBookingReplicateByItineraryIDResponse]
  def validateNonPropertyBookingReplicateByItineraryIdRequest(
      request: ReplicateByItineraryIdRequest
  ): Option[ReplicateByItineraryIdResponse]
}

class BookingsRequestValidatorImpl @Inject() (config: Configuration) extends BookingsRequestValidator {

  override def validateSetFlightBookingState(request: SetStateRequest): Option[SetStateResponse] = {
    request match {
      case _ if request.flights.isEmpty =>
        Some(SetStateResponse.withError(ResponseErrorCode.InvalidRequest, "flights must not be empty"))
      case _ if request.slices.isEmpty =>
        Some(SetStateResponse.withError(ResponseErrorCode.InvalidRequest, "slices must not be empty"))
      case _ if request.segments.isEmpty =>
        Some(SetStateResponse.withError(ResponseErrorCode.InvalidRequest, "segments must not be empty"))
      case _ if request.passengers.isEmpty =>
        Some(SetStateResponse.withError(ResponseErrorCode.InvalidRequest, "passengers must not be empty"))
//      case _ if request.payments.isEmpty => Some(FlightBookingSetStateResponse.withError(ResponseErrorCode.InvalidRequest, "payments must not be empty"))
      case _ if request.breakdown.isEmpty =>
        Some(SetStateResponse.withError(ResponseErrorCode.InvalidRequest, "breakdown must not be empty"))
//      case _ if request.baggageAllowance.isEmpty => Some(FlightBookingSetStateResponse.withError(ResponseErrorCode.InvalidRequest, "baggageAllowance must not be empty"))
      case _ => None
    }
  }

  override def validateSetItineraryBookingState(request: SetStateRequest): Option[SetStateResponse] =
    request match {
      case _ if request.flights.nonEmpty    => validateSetFlightBookingState(request)
      case _ if request.properties.nonEmpty => validatePropertyState(request.properties.get)
      case _
          if request.flights.isEmpty &&
            request.properties.isEmpty &&
            request.vehicle.isEmpty &&
            request.activities.isEmpty &&
            hasNoItineraryState(
              request
            ) =>
        Some(SetStateResponse.withError(ResponseErrorCode.InvalidRequest, "flight, vehicle and property are empty"))
      case _ => None
    }

  private def hasNoItineraryState(request: SetStateRequest) = {
    request match {
      case _ if request.history.nonEmpty || request.payments.nonEmpty => false
      case _                                                          => true
    }
  }

  private val possiblePropertyStates = Seq(
    CreatedBookingStatus.BookingProcessing.id,
    CreatedBookingStatus.BookingConfirmed.id,
    CreatedBookingStatus.BookingRejected.id,
    CreatedBookingStatus.TechnicalError.id,
    CreatedBookingStatus.ManualProcessing.id
  )

  private def validatePropertyState(propertyBookingState: Seq[PropertyBookingState]): Option[SetStateResponse] = {
    propertyBookingState match {
      case _ if propertyBookingState.map(_.bookingId).exists(id => id <= 0) =>
        Some(
          SetStateResponse.withError(
            ResponseErrorCode.InvalidRequest,
            s"property booking id in ${propertyBookingState.map(_.bookingId)} is invalid"
          )
        )
      case _ if propertyBookingState.map(_.stateId).exists(stateId => !possiblePropertyStates.contains(stateId)) =>
        Some(
          SetStateResponse.withError(
            ResponseErrorCode.InvalidRequest,
            s"property booking state in ${propertyBookingState.map(_.stateId)} is invalid"
          )
        )
      case _ => None
    }
  }

  override def validateFlightBookingReplicateByItineraryIDRequest(
      request: FlightBookingReplicateByItineraryIDRequest
  ): Option[FlightBookingReplicateByItineraryIDResponse] = {
    request match {
      case _ if request.itineraryIds.isEmpty =>
        Some(
          FlightBookingReplicateByItineraryIDResponse
            .withError(ResponseErrorCode.InvalidRequest, "Atleast one itinerary id must be defined")
        )
      case _ if request.itineraryIds.exists(id => id <= 0) =>
        Some(
          FlightBookingReplicateByItineraryIDResponse
            .withError(ResponseErrorCode.InvalidRequest, "All itinerary identifiers must have a positive value.")
        )
      case _ if request.itineraryIds.length > config.replicateExistingBookingConfig.itineraryIdLimit =>
        Some(
          FlightBookingReplicateByItineraryIDResponse.withError(
            ResponseErrorCode.InvalidRequest,
            s"Amount of itinerary identifiers must be less than or equal to ${config.replicateExistingBookingConfig.itineraryIdLimit}."
          )
        )
      case _ => None
    }
  }

  override def validateNonPropertyBookingReplicateByItineraryIdRequest(
      request: ReplicateByItineraryIdRequest
  ): Option[ReplicateByItineraryIdResponse] = request match {
    case _ if request.itineraryIds.isEmpty =>
      Some(
        ReplicateByItineraryIdResponse.withError(
          ResponseErrorCode.InvalidRequest,
          "Request must specify at least one itinerary ID"
        )
      )
    case _ if request.itineraryIds.exists(id => id <= 0) =>
      Some(
        ReplicateByItineraryIdResponse.withError(
          ResponseErrorCode.InvalidRequest,
          "Invalid itinerary ID found in request"
        )
      )
    case _ if request.itineraryIds.length > config.replicateExistingBookingConfig.itineraryIdLimit =>
      Some(
        ReplicateByItineraryIdResponse.withError(
          ResponseErrorCode.Unauthorized,
          s"You are not permitted to perform this operation because the max number of itineraries that can be replicated at once is ${config.replicateExistingBookingConfig.itineraryIdLimit}"
        )
      )
    case _ if ReplicateItineraryProductType.byName(request.productType) == ReplicateItineraryProductType.Unknown =>
      Some(
        ReplicateByItineraryIdResponse.withError(
          ResponseErrorCode.InvalidRequest,
          s"Invalid product type ${request.productType} found. Valid product types are ${ReplicateItineraryProductType.validProductTypes
              .mkString(", ")}"
        )
      )
    case _ => None
  }
}
