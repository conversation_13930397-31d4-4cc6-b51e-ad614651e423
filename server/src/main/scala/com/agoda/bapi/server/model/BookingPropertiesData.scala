package com.agoda.bapi.server.model

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.mapper.booking.RoomSelectionHelper
import com.agoda.bapi.common.message.PropertySearchCriteria
import com.agoda.bapi.common.message.loyalty.LoyaltyPaymentBoundaries
import com.agoda.bapi.common.message.setupBooking.{CrossSellDetail, OccupancyMessage, PackageRequest, PropertyProductItem}
import com.agoda.bapi.common.model.ChargeOption.ChargeOption
import com.agoda.bapi.common.model.booking.RequiredFieldMetadata
import com.agoda.bapi.common.model.cart.CartItemContext
import com.agoda.bapi.common.model.consumerFintech.ConsumerFintechDetail
import com.agoda.bapi.common.model.creation.{AvailabilityType, MorDisclosureType}
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.util.EbeConst.MerchantOfRecord
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.PropertyapiImplicits.PropertiesOps
import com.agoda.bapi.server.model.PropertyapiImplicits.PropertyOps
import com.agoda.bapi.server.service.PapiPropertyStatus.PapiPropertyStatus
import com.agoda.bapi.server.service.{PropertyRequiredFieldParams, RequiredFieldService}
import com.agoda.bapi.common.model.creation.AvailabilityType.AvailabilityType
import models.pricing.enums.SwapRoomTypes
import models.starfruit.{BookingItemBreakdown, LoyaltyReasons}
import com.agoda.papi.enums.campaign.{PromotionType, PromotionTypes}
import transformers.{EnrichedBookingRoom, EnrichedChildRoom, EnrichedEBEBooking, Property}

final case class BookingPropertiesData(
    id: String,
    content: String,
    papiProperties: Option[transformers.Properties],
    packageRequest: Option[PackageRequest],
    papiPropertyStatus: PapiPropertyStatus,
    selectedChargeOption: Option[ChargeOption],
    propertySearchCriteria: Option[PropertySearchCriteria] = None,
    preBookingId: Option[Long] = None,
    crossSellDetail: Option[CrossSellDetail] = None,
    isMessageHostAvailable: Option[Boolean] = None,
    cartItemContext: Option[CartItemContext] = None,
    consumerFintechDetail: Option[ConsumerFintechDetail] = None
) extends BCREInternalProductData {
  private val chinaRequestOrigin = "CN"

  def getPropertyToBook: Option[Property] = papiProperties.flatMap(_.property.headOption)

  def getBreakdownList(roomTypeId: Long): Iterable[BookingItemBreakdown] = {
    (for {
      property   <- getPropertyToBook
      booking    <- property.booking
      roomToBook <- booking.rooms.find(_.roomTypeId == roomTypeId)
    } yield roomToBook.pricing.charge).getOrElse(Nil)
  }

  def getLoyaltyPaymentBoundaries()(implicit context: RequestContext): Option[LoyaltyPaymentBoundaries] =
    getPaymentBoundaries(isCashback = false)

  def getCashbackRedemptionPaymentBoundaries()(implicit context: RequestContext): Option[LoyaltyPaymentBoundaries] =
    getPaymentBoundaries(isCashback = true)

  def getPaymentBoundaries(isCashback: Boolean)(implicit context: RequestContext): Option[LoyaltyPaymentBoundaries] = {
    papiProperties match {
      case Some(properties) =>
        if (isAlternativeRoomEnabled(context)) {
          val childRoom = getChildRoomToBook()
          val boundaries = if (isCashback) {
            childRoom.flatMap(_.cashbackRedemptionBoundaries)
          } else {
            childRoom.flatMap(_.loyaltyResponse)
          }
          boundaries.map(b =>
            LoyaltyPaymentBoundaries(
              min = b.min,
              max = b.max,
              maxBeforeGatewayLimit = b.maxBeforeGatewayLimit,
              minTotalAfterRedeem = b.minTotalAfterRedeem,
              canStayForFree = b.canStayForFree,
              reason = b.reason
            )
          )
        } else {
          if (isCashback) { properties.cashbackRedemptionResponse }
          else { properties.loyaltyResponse }
        }
      case None => None
    }
  }

  def toPropertyProductItem(implicit context: SetupBookingContext): PropertyProductItem = {
    val roomToBook = getChildRoomToBook()

    papiProperties match {
      case Some(properties) =>
        PropertyProductItem(
          id = id,
          content = content,
          displayPrice = None, // TODO to enrich later, price display
          specialRequestOptions = properties.specialRequestIds.map(_.i),
          packageRequest = Option(Set(BookingFlow.Package, BookingFlow.MultiHotel))
            .filter(_.contains(context.bookingFlowType))
            .flatMap(_ => properties.extractPackageToken),
          preBookingId = preBookingId,
          morDisclosureType = Some(calculateMorDisclosureType),
          crossSellDetail = getCrossSellDetail,
          stayPackageType = properties.extractStayPackageType,
          bookingRoomIdentifier = roomToBook.flatMap(_.roomIdentifiers),
          requiredFields = getPropertyRequiredFields,
          isMessageHostAvailable = isMessageHostAvailable,
          occupancyMessages = toOccupancyMessages(roomToBook)
        )
      case None => PropertyProductItem(id, content, packageRequest = packageRequest)
    }
  }

  def toOccupancyMessages(room: Option[EnrichedChildRoom]): Option[Seq[OccupancyMessage]] =
    room.flatMap(r => r.occupancyMessages.map(seq => seq.map(o => OccupancyMessage(o.`type`, o.message))))

  def getCrossSellDetail(implicit context: SetupBookingContext): Option[CrossSellDetail] = {

    val isCrossSellRequest = propertySearchCriteria.exists(
      _.simplifiedRoomSelectionRequest.exists(_.alternativeOptIn.exists(_.crossSellOptIn.exists(_.reason.isDefined)))
    )

    if (isCrossSellRequest) {
      context.session.crossSellDetail
    } else {
      val promotionsFailedToApply  = findPromotionsFailedToApply()
      val isAgodaCashFailedToApply = findAgodaCashLoyaltyFailedToApply()

      if (promotionsFailedToApply.nonEmpty || isAgodaCashFailedToApply) {
        Some(
          CrossSellDetail(
            promotionsFailedToApply = promotionsFailedToApply,
            isAgodaCashFailedToApply = Some(isAgodaCashFailedToApply),
            isPointMaxFailedToApply = None
          )
        )
      } else {
        None
      }
    }

  }

  private[model] def findPromotionsFailedToApply(): Seq[PromotionType] = {

    val roomToBook    = getChildRoomToBook()
    val crossSellRoom = getCrossSellRoom()

    val roomToBookCampaigns =
      roomToBook.flatMap(_.campaignPromotions.find(_.exists(_.inapplicableReason.isEmpty))).getOrElse(List.empty)
    val crossSellRoomCampaigns =
      crossSellRoom.flatMap(_.campaignPromotions.find(_.exists(_.inapplicableReason.isDefined))).getOrElse(List.empty)

    crossSellRoomCampaigns.collect {
      case cs if roomToBookCampaigns.exists(rtb => rtb.campaignType == cs.campaignType) =>
        PromotionTypes.getPromotionTypeFromCampaignTypeId(cs.campaignType.i)
    }
  }

  private[model] def findAgodaCashLoyaltyFailedToApply(): Boolean = {
    val roomToBook    = getChildRoomToBook()
    val crossSellRoom = getCrossSellRoom()

    val roomToBookReason    = roomToBook.flatMap(_.loyaltyResponse.map(_.reason))
    val crossSellRoomReason = crossSellRoom.flatMap(_.loyaltyResponse.map(_.reason))

    roomToBookReason.contains(LoyaltyReasons.Success) && !crossSellRoomReason.contains(
      LoyaltyReasons.Success
    )
  }

  private def calculateMorDisclosureType()(implicit context: SetupBookingContext): Int = {
    def isUserOriginFromCN: Boolean = context.requestContext.userContext.exists(_.requestOrigin == chinaRequestOrigin)

    getChildRoomBookingData.flatMap(_.accountingEntity) match {
      case _ if !isUserOriginFromCN =>
        MorDisclosureType.None.id
      case Some(accountingEntity) if accountingEntity.merchantOfRecord == MerchantOfRecord.quantum =>
        MorDisclosureType.None.id
      case Some(accountingEntity) if accountingEntity.rateContract == MerchantOfRecord.quantum =>
        MorDisclosureType.RceQuantum.id
      case _ =>
        // rateContract == MerchantOfRecord.acs
        MorDisclosureType.RceAcs.id
    }
  }

  def childRooms: Seq[EnrichedChildRoom] =
    papiProperties
      .map { papiProperty =>
        papiProperty.property.flatMap(_.getChildrooms)
      }
      .getOrElse(Seq.empty)

  def getChildRoomToBook(): Option[EnrichedChildRoom] = {
    val alternativeRoomOptIn =
      propertySearchCriteria.flatMap(_.simplifiedRoomSelectionRequest.flatMap(_.alternativeOptIn))
    val alternativeRoomMap = getPropertyToBook.map(_.roomSwapping).getOrElse(Seq.empty)
    val selectedRoomUid = propertySearchCriteria
      .flatMap(_.simplifiedRoomSelectionRequest.map(_.roomIdentifier))
      .orElse(propertySearchCriteria.map(_.roomIdentifier))
    val roomToBookUid: Option[String] =
      RoomSelectionHelper.getRoomToBookUid(alternativeRoomOptIn, selectedRoomUid, alternativeRoomMap, getPropertyToBook)
    // note: as alternative rooms can contain uid or room identifier we should try to match on any of them
    getPropertyToBook.flatMap(
      _.getAllRooms.filter(r => r.uid == roomToBookUid || r.roomIdentifiers == roomToBookUid).headOption
    )
  }

  def getChildRoomBookingData(): Option[EnrichedBookingRoom] = {
    val roomToBookUidOpt: Option[String] = getChildRoomToBook().flatMap(_.uid)
    getPropertyToBook.flatMap(_.booking.flatMap(_.rooms.filter(r => roomToBookUidOpt.contains(r.uid)).headOption))
  }

  def getEbeBookingData(): Option[EnrichedEBEBooking] = {
    val roomToBookUidOpt: Option[String] = getChildRoomToBook().flatMap(_.uid)
    getPropertyToBook.flatMap(
      _.booking.flatMap(
        _.booking.filter(b => b.hotel.exists(_.room.exists(r => roomToBookUidOpt.contains(r.uid)))).headOption
      )
    )
  }

  def getCrossSellRoom(): Option[EnrichedChildRoom] = {
    val crossSellRoom =
      getPropertyToBook.flatMap(_.roomSwapping.find(room => SwapRoomTypes.isCrossSell(room.alternativeRoomType)))

    crossSellRoom.flatMap(room => childRooms.find(_.roomIdentifiers.contains(room.alternativeRoomUid)))
  }

  def isAlternativeRoomEnabled(requestContext: RequestContext): Boolean = {
    val isPriceChangeSwap: Boolean = (for {
      papiProperty <- papiProperties.toList
      property     <- papiProperty.property
      roomSwapping <- property.roomSwapping
    } yield roomSwapping.alternativeRoomType == SwapRoomTypes.AlternativeRoom).contains(true)

    !isPriceChangeSwap && RoomSelectionHelper.isAlternativeRoomEnabled(propertySearchCriteria, requestContext)
  }

  def getAvailabilityType: Option[AvailabilityType] =
    for {
      property    <- getPropertyToBook
      bookingItem <- property.booking
      booking     <- bookingItem.booking.headOption
      hotel       <- booking.hotel.headOption
      room        <- hotel.room.headOption
    } yield AvailabilityType(room.availabilityType.i)

  def getPropertyLanguage: Int =
    (for {
      property <- getPropertyToBook
      info     <- property.info
      local    <- info.local
      language <- local.language
    } yield language.id).getOrElse(0)

  def getPropertyRequiredFields(implicit context: SetupBookingContext): Option[Map[String, RequiredFieldMetadata]] = {
    val requiredFields = RequiredFieldService.getPropertyRequiredFields(
      PropertyRequiredFieldParams(
        availabilityType = getAvailabilityType,
        propertyLanguage = getPropertyLanguage
      )
    )(context.requestContext)
    if (requiredFields.isEmpty) None
    else Some(requiredFields)
  }
}
