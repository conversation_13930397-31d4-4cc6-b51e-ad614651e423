package com.agoda.bapi.server.facades.aggregator

import cats.Id
import com.agoda.bapi.common.logging.PriceDisplayLog
import com.agoda.bapi.common.message.CustomerRiskStatus.CustomerRiskStatus
import com.agoda.bapi.common.message.creation.CustomerV2
import com.agoda.bapi.common.message.pricebreakdown.PriceBreakdownNode
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.model.ChargeOption.ChargeOption
import com.agoda.bapi.common.model.activity.{ActivityBookingToken, ActivityConfirmationData}
import com.agoda.bapi.common.model.addOn.cegFastTrack.AddOnBookingToken
import com.agoda.bapi.common.model.addOn.{AddOnBookingToken => GenericAddOnBookingToken, AddOnBundle, AddOnCharacteristics, AddOnFinancials, AddOnMeta, AddOnSupplier, UserTaxCountry}
import com.agoda.bapi.common.model.booking.PaymentMethodDetailsV2
import com.agoda.bapi.common.model.car.{CarBookingToken, CarConfirmationData}
import com.agoda.bapi.common.model.cart.CartContext
import com.agoda.bapi.common.model.creation.{CreditCardInfoModel, PriceChangeOnPollingStatus, PriceChangePerRoomStatus}
import com.agoda.mpb.common.models.state.ProductType.ProductType
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.protection.ProtectionConfimationData
import com.agoda.bapi.common.model.rebookAndCancel.RebookAndCancelData
import com.agoda.bapi.common.model.tripProtection.TripProtectionToken
import com.agoda.bapi.common.model.{CreditCardInfoSetupResponse, CurrencyCode}
import com.agoda.bapi.common.token.{AgentAssistedBookingInfo => TokenAabInfo, FlightAgentAssistedBookingInfo => TokenFlightAabInfo, _}
import com.agoda.bapi.common.util.OptionUtils._
import com.agoda.bapi.common.util.{TokenSerializer, TokenSerializers}
import com.agoda.bapi.contracts.WithToBookingToken
import com.agoda.bapi.creation.model.CreditCardInfo
import com.agoda.bapi.creation.service.observability.setup.PaymentLog
import com.agoda.bapi.creation.util.WhitelabelUtils
import com.agoda.bapi.server.addon.{AddOnBookingTokenService, AddOnDataV2}
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.{BookingPropertiesData, FlightConfirmationData, ProductData}
import com.agoda.bapi.server.service.allotment.AllotmentPreCheckStatus
import com.agoda.bapi.server.utils.ProductTokenCreatorUtils
import com.agoda.common.itineraryContext.ItineraryContext
import com.agoda.mpb.common.models.state.ProductType
import com.agoda.winterfell.output.LoyaltyProfile

import scala.util.{Failure, Success, Try}

trait ProductTokenAggregatorMixin
    extends CommonProductTokenAggregatorMixin
    with PropertyProductTokenAggregatorMixin
    with FlightProductTokenAggregatorMixin
    with TripProtectionProductTokenAggregatorMixin
    with AddOnProductTokenAggregatorMixin {

  val protectionService: WithToBookingToken[Try, ProtectionConfimationData, TripProtectionToken]
  val carService: WithToBookingToken[Id, (Seq[CarConfirmationData], Option[CurrencyCode]), CarBookingToken]
  val activityService: WithToBookingToken[Try, Seq[ActivityConfirmationData], ActivityBookingToken]
  val cegFastTrackService: AddOnBookingTokenService
  val productTokenUtils: ProductTokenCreatorUtils

  private val supportedGenericAddonProductTypes: Set[ProductType] =
    Set(ProductType.TripProtection, ProductType.CancelForAnyReason)

  def toAddOn(
      productItem: ProductData,
      isMigrateCEGUpsellEnabled: Boolean,
      isFlightCheckInEnabled: Boolean
  ): GenericAddOnBookingModel = {
    val cegUpSell = if (isMigrateCEGUpsellEnabled) { Set(ProductType.CEGFastTrack) }
    else { Set.empty[ProductType] }
    val checkIn = if (isFlightCheckInEnabled) { Set(ProductType.FlightCheckIn) }
    else { Set.empty[ProductType] }

    val supportedProductTypes = supportedGenericAddonProductTypes ++ cegUpSell ++ checkIn

    productItem.addOnDataV2
      .collect {
        case addOn if supportedProductTypes.contains(addOn.productType) =>
          addOn.confirmationData.map { confirmationData =>
            val productTokenKey = confirmationData.productTokenKey
            val token = GenericAddOnBookingToken(
              productTokenKey = productTokenKey,
              characteristics = AddOnCharacteristics(
                productTypeId = addOn.productType.id,
                startDate = confirmationData.startDateTime,
                endDate = confirmationData.endDateTime
              ),
              bundle =
                if (addOn.productRefIds.nonEmpty)
                  Some(
                    AddOnBundle(
                      addOn.productRefIds
                    )
                  )
                else None,
              financials = AddOnFinancials(
                productPaymentModel = confirmationData.paymentModel,
                productPayment = confirmationData.productPayment,
                priceBreakdown = confirmationData.priceBreakdown
              ),
              supplier = confirmationData.supplier
                .map(supplier => AddOnSupplier(supplierId = supplier.supplierId, supplierData = supplier.supplierData)),
              meta = confirmationData.metas.map { meta =>
                AddOnMeta(
                  metaName = meta.metaName,
                  metaValue = meta.metaValue,
                  metaType = meta.metaType,
                  version = meta.version,
                  metaTypeAsString = meta.metaTypeAsString
                )
              }
            )
            (productTokenKey, token)
          }
      }
      .flatten
      .toMap
  }

  def createProductToken(
      productItems: ProductData,
      customerRiskStatus: Option[CustomerRiskStatus] = None,
      customerInfo: Option[CustomerV2] = None,
      allotmentPreCheckResults: Seq[AllotmentPreCheckStatus] = Seq.empty,
      creditCardInfo: Option[CreditCardInfo] = None,
      creditCardValidationInfo: Option[CreditCardInfoSetupResponse] = None,
      paymentRequest: Option[PaymentRequest] = None,
      chargeCurrency: Option[CurrencyCode] = None,
      selectedPaymentMethodDetails: Option[PaymentMethodDetailsV2] = None,
      isNewsletterOptedIn: Boolean,
      productsRequest: ProductsRequest,
      isPriceChangedForRequiredProperty: Option[PriceChangeOnPollingStatus] = None,
      selectedChargeOption: Option[ChargeOption] = None,
      priceChangeStatusOnRooms: Seq[PriceChangePerRoomStatus] = Seq.empty,
      installmentPlanToken: Option[String] = None,
      installmentPlanCode: Option[String] = None,
      loyaltyRequest: Option[LoyaltyRequest] = None,
      loyaltyProfile: Option[LoyaltyProfile] = None,
      enabledFeatures: Option[Seq[String]] = None,
      cartContext: Option[CartContext] = None,
      userTaxCountry: Option[UserTaxCountry] = None,
      aabInfo: Option[AabInfo] = None,
      addOnData: Seq[AddOnDataV2] = Seq.empty,
      itineraryContext: Option[ItineraryContext] = None
  )(implicit context: SetupBookingContext): Try[TokenMessage] = {
    val propertyCreationTokens =
      toPropertyBookingModel(
        productItems.properties,
        allotmentPreCheckResults,
        isPriceChangedForRequiredProperty,
        context.whiteLabelInfo,
        context.requestContext,
        productsRequest.propertyRequests.headOption
          .flatMap(_.propertySearchCriteria.simplifiedRoomSelectionRequest.flatMap(_.alternativeOptIn)),
        context.logContext,
        context.bookingFlowType
      )
    val flightCreationTokensT = toFlightBookingModel(productItems.flights, loyaltyRequest.flatMap(_.partnerClaimToken))
    val carCreationTokens     = carService.toBookingToken(productItems.cars -> chargeCurrency)(context.requestContext)
    val isMigrateCEGUpsellEnabled = (context.requestContext.featureAware.exists(
      _.isCEGUpsellAddOnV2Enabled
    ) && productItems.hasFlight && productItems.flights.nonEmpty) ||
      (productItems.hasProperty && productItems.addOnDataV2.nonEmpty)
    val isFlightCheckInEnabled = context.requestContext.featureAware.exists(_.EnableOnboardCheckIn)
    val propertySetupTokens =
      toPropertySetupModel(
        productItems,
        productsRequest,
        allotmentPreCheckResults,
        isPriceChangedForRequiredProperty,
        priceChangeStatusOnRooms
      )

    val creditCardInfoModel     = toCreditCardInfoModel(creditCardInfo, creditCardValidationInfo, paymentRequest)
    val paymentRequestInfo      = toPaymentRequestInfoModel(selectedChargeOption, productItems.properties)
    val tripProtectionsV1Tokens = toTripProtectionModel(productItems.protections)
    val activityCreationTokenT  = activityService.toBookingToken(productItems.activities)(context.requestContext)
    val addOnsProductMap        = productItems.addOnDataV2.groupBy(_.productType)

    val cegFastTrackCreationTokenT = if (isMigrateCEGUpsellEnabled) {
      Success(Map.empty[common.ProductTokenKey, AddOnBookingToken])
    } else {
      cegFastTrackService.toBookingToken(
        cegFastTrackService.getConfirmationData(productItems.addOnData.products) ++
          addOnsProductMap
            .getOrElse(ProductType.CEGFastTrack, Seq.empty)
            .flatMap(ceg => cegFastTrackService.mapConfirmationData(ceg.confirmationData))
      )(context.requestContext)
    }
    val addOnTokens: GenericAddOnBookingModel = toAddOn(productItems, isMigrateCEGUpsellEnabled, isFlightCheckInEnabled)

    val crossSellDetail = toCrossSellDetail(productItems.properties)

    // If you pass in cart context, we assume you came from cart summary page
    val isBookingFromCart = productsRequest.cartContext.isDefined

    val isPartialSuccessAllowed: Boolean = getIsPartialSuccessAllowed(productItems, enabledFeatures)

    val tokenAabInfo             = toTokenAabInfo(aabInfo)
    val ancillaryBookingModelMap = toAncillarySetupModel(addOnData)

    val externalLoyaltyRequestInfo: Option[ExternalLoyaltyRequestInfo] =
      getExternalLoyaltyRequestInfo(loyaltyRequest, loyaltyProfile)

    def createMultiProductCreationBookingToken(
        flightCreationTokensT: Try[FlightBookingModel],
        propertyCreationTokens: PropertyBookingModel,
        tripProtectionsTokens: TripProtectionModel,
        carCreationToken: CarBookingModel,
        activityCreationToken: ActivityBookingModel,
        cegFastTrackCreationToken: AddOnBookingModel,
        sessionTimestamp: Option[Long],
        priceBreakdownNode: Option[PriceBreakdownNode],
        creditCardInfo: Option[CreditCardInfoModel],
        paymentRequestInfo: Option[PaymentRequestInfo],
        selectedPaymentMethodDetails: Option[PaymentMethodDetailsV2],
        isNewsletterOptedIn: Boolean,
        priceDisplayVersion: Option[Int] = None,
        isBookingFromCart: Boolean,
        loyaltyProfile: Option[LoyaltyProfile] = None,
        isPartialSuccessAllowed: Boolean = false,
        cartContext: Option[CartContext] = None,
        userTaxCountry: Option[UserTaxCountry] = None,
        addOnTokens: GenericAddOnBookingModel,
        aabInfo: Option[TokenAabInfo],
        externalLoyaltyRequestInfo: Option[ExternalLoyaltyRequestInfo] = None,
        productsRequest: ProductsRequest,
        rebookAndCancelData: Option[RebookAndCancelData] = None,
        itineraryContext: Option[ItineraryContext] = None
    ): Try[Option[MultiProductCreationBookingToken]] =
      if (productItems.priceConfirmed)
        flightCreationTokensT.flatMap { flightCreationTokens =>
          productTokenUtils
            .createMultiProductCreationBookingToken(
              flightCreationTokens,
              propertyCreationTokens,
              tripProtectionsTokens,
              carCreationToken,
              activityCreationToken,
              cegFastTrackCreationToken,
              context.session.timestamp,
              priceBreakdownNode,
              creditCardInfo,
              paymentRequestInfo,
              selectedPaymentMethodDetails,
              isNewsletterOptedIn,
              priceDisplayVersion,
              installmentPlanToken,
              installmentPlanCode,
              isBookingFromCart,
              loyaltyProfile,
              isPartialSuccessAllowed,
              cartContext,
              userTaxCountry,
              aabInfo,
              addOnTokens,
              externalLoyaltyRequestInfo,
              productsRequest,
              rebookAndCancelData,
              itineraryContext = itineraryContext
            )
            .transform(
              s => scala.util.Success(Some(s)),
              f => scala.util.Failure(f)
            )
        }
      else scala.util.Success(None)

    val retryPaymentBookingTokenT =
      if (context.requestContext.featureAware.exists(_.encryptRetryPaymentBookingToken))
        productTokenUtils
          .createMultiProductRetryPaymentBookingToken(productItems)
          .map(Some(_))
      else scala.util.Success(None)

    for {
      activityCreationToken     <- activityCreationTokenT
      cegFastTrackCreationToken <- cegFastTrackCreationTokenT
      creationBookingToken <- createMultiProductCreationBookingToken(
                                flightCreationTokensT,
                                propertyCreationTokens,
                                tripProtectionsV1Tokens,
                                carCreationTokens,
                                activityCreationToken,
                                cegFastTrackCreationToken,
                                context.session.timestamp,
                                productItems.totalPriceDisplay,
                                creditCardInfoModel,
                                paymentRequestInfo,
                                selectedPaymentMethodDetails,
                                isNewsletterOptedIn,
                                productsRequest.priceDisplayVersion,
                                isBookingFromCart,
                                loyaltyProfile,
                                isPartialSuccessAllowed,
                                cartContext,
                                userTaxCountry,
                                addOnTokens = addOnTokens,
                                aabInfo = tokenAabInfo,
                                externalLoyaltyRequestInfo = externalLoyaltyRequestInfo,
                                productsRequest = productsRequest,
                                productItems.rebookAndCancelData,
                                itineraryContext = itineraryContext
                              )
      packagesToken =
        productItems.properties.lastOption
          .flatMap(_.packageRequest)
          .orElse(context.session.packages)
      setupBookingToken <-
        productTokenUtils
          .createMultiProductSetupBookingToken(
            propertySetupTokens,
            packagesToken,
            context.session.timestamp,
            customerRiskStatus,
            customerInfo,
            installmentPlanToken,
            installmentPlanCode,
            crossSellDetail,
            ancillaryBookingModelMap
          )
      retryPaymentBookingToken <- retryPaymentBookingTokenT
      multiToken <- productTokenUtils.createMultiProductsToken(
                      Some(setupBookingToken),
                      creationBookingToken,
                      retryPaymentBookingToken,
                      context.session.timestamp
                    )
    } yield {
      appendPaymentLogs(context, creationBookingToken)
      multiToken
    }
  }

  protected def toPaymentRequestInfoModel(
      selectedChargeOption: Option[ChargeOption],
      items: Seq[BookingPropertiesData]
  )(implicit context: SetupBookingContext): Option[PaymentRequestInfo] =
    context.bookingFlowType match {
      case BookingFlow.SingleProperty =>
        toPaymentInfoModelForSingleProperty(selectedChargeOption, items)(context.requestContext)
      case BookingFlow.Cart =>
        toPaymentInfoModelForCart(selectedChargeOption)
      case _ => None // this method is to support returning charge option for noCvc feature in single property funnel
    }

  protected def getIsPartialSuccessAllowed(
      productItems: ProductData,
      enabledFeatures: Option[Seq[CurrencyCode]]
  )(implicit
      context: SetupBookingContext
  ): Boolean = {
    val isFlightRelatedProductWithPartialSuccessAllowance =
      productItems.flights.exists(_.isHackerFare)

    val isPartialSuccessFeatureEnabled =
      enabledFeatures.exists(_.exists(feature => feature.equalsIgnoreCase("PartialSuccess")))

    val isPartialSuccessAllowedForWL = WhitelabelUtils.isPartialSuccessAllowedForWL(context.whiteLabelInfo)

    isPartialSuccessAllowedForWL && (isFlightRelatedProductWithPartialSuccessAllowance || isPartialSuccessFeatureEnabled)
  }

  private def appendPaymentLogs(
      context: SetupBookingContext,
      creationBookingToken: Option[MultiProductCreationBookingToken]
  ): Unit =
    Try {
      context.logContext.addPaymentLog(
        context.correlationId,
        PaymentLog(
          totalChargeAmountUsd = creationBookingToken.map(_.payment.paymentAmountUSD.toDouble),
          totalChargeAmount = creationBookingToken.map(_.payment.paymentAmount.toDouble),
          totalChargeAmountCurrencyCode = creationBookingToken.map(_.payment.paymentCurrency),
          totalPriceDisplay = creationBookingToken
            .flatMap(_.commonPayment)
            .flatMap(_.displayPrice)
            .flatMap(_.breakdown.value)
            .map(priceDisplay => PriceDisplayLog(priceDisplay)),
          exchangeRateOption = creationBookingToken.map(_.payment.exchangeRateOption)
        )
      )
    } match {
      case Success(_) =>
      case Failure(_) => logger.error("Failed to append payment and pricing data to logs.")
    }

  private[facades] def toTokenAabInfo(info: Option[AabInfo]): Option[TokenAabInfo] = {
    info.map(aabInfo =>
      TokenAabInfo(
        flight = aabInfo.flight.map(mapFlightAab)
      )
    )
  }

  private[facades] def mapFlightAab(flightAab: FlightAabInfo): TokenFlightAabInfo =
    TokenFlightAabInfo(flightAab.facilitationFeeWaiverReasonId)

  private def getExternalLoyaltyRequestInfo(
      loyaltyRequest: Option[LoyaltyRequest],
      loyaltyProfile: Option[LoyaltyProfile]
  ): Option[ExternalLoyaltyRequestInfo] = {
    val minimumPointsToRedeemOpt = getMinimumPointsToRedeem(loyaltyProfile)
    val pointsOfferType          = loyaltyRequest.flatMap(_.pointsOfferType)
    val externalLoyaltyRequestInfo: Option[ExternalLoyaltyRequestInfo] =
      if (pointsOfferType.isDefined || minimumPointsToRedeemOpt.isDefined)
        ExternalLoyaltyRequestInfo(
          pointsOfferType = pointsOfferType.map(_.toString),
          minimumPointsToRedeem = minimumPointsToRedeemOpt
        )
      else None
    externalLoyaltyRequestInfo
  }

  private def getMinimumPointsToRedeem(loyaltyProfileOpt: Option[LoyaltyProfile]): Option[Double] =
    loyaltyProfileOpt.flatMap(
      _.externalLoyaltyProfileInfo.flatMap(
        _.subLoyaltyPrograms.headOption.flatMap(_.minimumPointsToRedeem)
      )
    )
}
