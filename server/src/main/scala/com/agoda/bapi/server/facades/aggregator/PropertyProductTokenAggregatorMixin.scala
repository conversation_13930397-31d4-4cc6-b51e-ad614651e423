package com.agoda.bapi.server.facades.aggregator

import com.agoda.bapi.common.ToolSet
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.mapper.booking.RoomSelectionHelper.{RateChannelSwapRoomsContainer, getRateChannelSwapAndBookedRooms}
import com.agoda.bapi.common.mapper.papi.PapiResponseMapper
import com.agoda.bapi.common.message.AlternativeRoomOptIn
import com.agoda.bapi.common.message.setupBooking.{CrossSellDetail, ProductsRequest}
import com.agoda.bapi.common.model.ChargeOption.ChargeOption
import com.agoda.bapi.common.model.WhiteLabelInfo
import com.agoda.bapi.common.model.creation.{BAPIBooking, PriceChangeOnPollingStatus, PriceChangePerRoomStatus}
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.product.BookingFlow.BookingFlow
import com.agoda.bapi.common.token._
import com.agoda.bapi.common.token.common.ProductTokenKey
import com.agoda.bapi.creation.model.messages.BapiProductSetupFact
import com.agoda.bapi.creation.service.observability.LogContext
import com.agoda.bapi.creation.service.observability.setup.PropertyContentLog
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.PropertyapiImplicits.{PropertiesOps, PropertyOps}
import com.agoda.bapi.server.model.{BookingPropertiesData, ProductData}
import com.agoda.bapi.server.service.allotment.{AllotmentPreCheckStatus, AllotmentStatus}
import models.pricing.enums.SwapRoomTypes

import scala.util.{Failure, Try}

trait PropertyProductTokenAggregatorMixin extends ToolSet with CommonProductTokenAggregatorMixin {

  protected def toPropertySetupModel(
      productItems: ProductData,
      productsRequest: ProductsRequest,
      allotmentPreCheckResults: Seq[AllotmentPreCheckStatus],
      isPriceChangedForRequiredProperty: Option[PriceChangeOnPollingStatus] = None,
      priceChangeStatusOnRooms: Seq[PriceChangePerRoomStatus] = Seq.empty
  )(implicit context: SetupBookingContext): PropertySetupModel = {
    val isPreCheckOnAlternatives = context.requestContext.featureAware.exists(_.isPreCheckOnAlternatives)

    def getOldPreCheckStatus(productTokenKey: String): Option[AllotmentPreCheckStatus] =
      if (isPreCheckOnAlternatives)
        None
      else
        allotmentPreCheckResults
          .find(_.productKey == productTokenKey)
          .orElse(
            Some(
              AllotmentPreCheckStatus(
                productKey = productTokenKey,
                roomIdentifier = None,
                requestId = None,
                status = AllotmentStatus.Processing
              )
            )
          )

    def shouldClearPGT(
        productTokenKey: String,
        roomToBookIdentifier: Option[String],
        oldAllotmentPreCheckOpt: Option[AllotmentPreCheckStatus]
    ): Boolean =
      if (isPreCheckOnAlternatives) {
        val isRoomGotPriceChangedAbsStatus = allotmentPreCheckResults
          .find(preCheck => preCheck.productKey == productTokenKey && preCheck.roomIdentifier == roomToBookIdentifier)
          .exists(_.status == AllotmentStatus.PriceChanged)
        val isRoomDfPriceUpdated = priceChangeStatusOnRooms.exists(status =>
          status.productTokenKey == productTokenKey && roomToBookIdentifier.contains(
            status.roomIdentifier
          ) && status.isPriceChanged
        )
        isRoomGotPriceChangedAbsStatus && !isRoomDfPriceUpdated
      } else
        oldAllotmentPreCheckOpt.exists(_.status == AllotmentStatus.PriceChanged) ||
        isPriceChangedForRequiredProperty.exists(_.shouldClearPriceGuaranteeToken(productTokenKey))

    productItems.properties.map { item =>
      val oldAllotmentPreCheckStatus = getOldPreCheckStatus(item.id)
      val shouldClearPGTFlag =
        shouldClearPGT(item.id, item.getChildRoomToBook.flatMap(_.roomIdentifiers), oldAllotmentPreCheckStatus)
      val token =
        if (shouldClearPGTFlag)
          None
        else item.papiProperties.flatMap(_.dfMetaResult.propertyToken.map(_.token))

      val newAllotmentStatuses =
        if (isPreCheckOnAlternatives)
          allotmentPreCheckResults.map { result =>
            RoomAllotmentResult(
              result.roomIdentifier.getOrElse(""),
              result.requestId,
              result.status
            )
          }
        else
          Seq.empty

      val (isRoomHasSwapped, originalRoomIdentifier) =
        getIsRoomHasSwappedAndOriginalIdentifier(productsRequest, productItems, item)

      item.id -> PropertySetupBookingToken(
        item.id,
        oldAllotmentPreCheckStatus.flatMap(_.requestId),
        oldAllotmentPreCheckStatus.map(_.status),
        token,
        item.preBookingId,
        isRoomHasSwapped,
        originalRoomIdentifier,
        newAllotmentStatuses
      )
    }.toMap
  }

  /**
    * RoomSwap feature requires original room identifier and isRoomHasSwapped to be set in token only once after the 1st
    * Properties response
    */
  protected def getIsRoomHasSwappedAndOriginalIdentifier(
      productsRequest: ProductsRequest,
      productItems: ProductData,
      item: BookingPropertiesData
  )(implicit context: SetupBookingContext) = {

    val originalRoomIdentifierInRequest = context.getOriginalRoomIdentifier(item.id)
    val requestedRoomIdentifier =
      productsRequest.propertyRequests.find(_.id == item.id).map(_.propertySearchCriteria.roomIdentifier)

    val originalRoomIdentifier =
      if (!productsRequest.bookingToken.isDefined)
        requestedRoomIdentifier
      else originalRoomIdentifierInRequest

    val isRoomHasSwappedInRequest = context.getIsRoomHasSwapped(item.id)
    val isRoomHasSwapped =
      if (!isRoomHasSwappedInRequest.isDefined)
        Some(
          !productItems.hasRooms(requestedRoomIdentifier.toSeq) &&
            item.papiProperties.map(_.property.forall(_.isOnlyAlternativeRoom.contains(true))).getOrElse(false)
        )
      else isRoomHasSwappedInRequest

    (isRoomHasSwapped, originalRoomIdentifier)
  }

  protected def toPropertyBookingModel(
      items: Seq[BookingPropertiesData],
      allotmentPreCheckResults: Seq[AllotmentPreCheckStatus] = Seq.empty,
      isPriceChangedForRequiredProperty: Option[PriceChangeOnPollingStatus] = None,
      whitelabelInfo: WhiteLabelInfo,
      context: RequestContext,
      alternativeRoomOptIn: Option[AlternativeRoomOptIn],
      logContext: LogContext,
      bookingFlowType: BookingFlow
  ): PropertyBookingModel = {
    def getIsPriceGuaranteeTokenCleared(productKey: String): Boolean =
      if (context.featureAware.exists(_.isPreCheckOnAlternatives))
        // For new pre-check on alternative rooms experiment, we no longer use this flag.
        false
      else
        allotmentPreCheckResults
          .find(_.productKey == productKey)
          .exists(_.status == AllotmentStatus.PriceChanged) || isPriceChangedForRequiredProperty
          .exists(_.shouldClearPriceGuaranteeToken(productKey))

    items.flatMap { item =>
      val isAlternativeRoomEnabled = item.isAlternativeRoomEnabled(context)
      val roomIdentifier           = if (isAlternativeRoomEnabled) item.propertySearchCriteria.map(_.roomIdentifier) else None
      val shouldCheckRcs =
        bookingFlowType == BookingFlow.SingleProperty && context.featureAware.exists(_.rateChannelSwapSwitch)
      val rateChannelSwapRoomIdentifier = if (shouldCheckRcs) getRateChannelSwapRoomIdentifier(items) else None
      item.papiProperties
        .flatMap(
          _.property.headOption
            .flatMap { property =>
              PapiResponseMapper.extractBapiBooking(
                property = Some(property),
                productTokenKey = Option(item.id),
                suppliersInformation = item.papiProperties.map(_.suppliersInformation.values.toSeq),
                selectedChargeOption = item.selectedChargeOption,
                preBookingId = item.preBookingId,
                pricingRequestId =
                  item.papiProperties.flatMap(_.dfMetaResult.propertyToken.flatMap(_.pricingRequestId)),
                firstSearchId = None,
                isPriceGuaranteedTokenCleared = getIsPriceGuaranteeTokenCleared(item.id),
                whiteLabelInfo = whitelabelInfo,
                isAlternativeRoomsEnabled = isAlternativeRoomEnabled,
                alternativeRoomOptIn = alternativeRoomOptIn,
                displayCurrency = Some(context.displayCurrency()),
                item.cartItemContext,
                isRateChannelSwapBooking = rateChannelSwapRoomIdentifier.nonEmpty,
                item.consumerFintechDetail
              )(rateChannelSwapRoomIdentifier.orElse(roomIdentifier), context)
            }
        )
        .map { property =>
          appendProductLogs(context, logContext, item, property)
          item.id -> property
        }
    }.toMap
  }

  private def appendProductLogs(
      context: RequestContext,
      logContext: LogContext,
      item: BookingPropertiesData,
      propertyToken: BAPIBooking
  ): Unit =
    Try {
      val roomToBook     = item.getChildRoomToBook()
      val property       = item.getPropertyToBook
      val ebeBookingData = item.getEbeBookingData
      logContext.addProductLog(
        context.getCorrelationId(),
        item.id,
        PropertyContentLog(
          searchId = Option(propertyToken.searchId),
          pricingSearchId = item.papiProperties.flatMap(_.dfMetaResult.propertyToken.flatMap(_.pricingRequestId)),
          masterRoomTypeId = roomToBook.flatMap(_.masterTypeId),
          supplierId = roomToBook.flatMap(_.supplierId),
          paymentModel = roomToBook.flatMap(_.payment.map(_.paymentModel.i)),
          isNoCC = roomToBook.flatMap(_.payment.map(_.noCreditCard.isEligible)),
          breakfast = roomToBook.flatMap(_.isBreakfastIncluded),
          isNha = propertyToken.infoSummary.flatMap(_.isNHA),
          alternativeRooms = BapiProductSetupFact.alternativeRoomsToString(
            item.papiProperties.flatMap(_.property.headOption.map(_.roomSwapping)).getOrElse(List.empty)
          ),
          isCrossSellAvailable = item.papiProperties.flatMap(
            _.property.headOption.map(
              _.roomSwapping.exists(alternativeRoomMapping =>
                SwapRoomTypes.isCrossSell(alternativeRoomMapping.alternativeRoomType)
              )
            )
          ),
          capacityNoOfAdults = roomToBook.flatMap(_.capacity.map(_.adults)),
          capacityNoOfChildren = roomToBook.flatMap(_.capacity.map(_.children)),
          capacityNoOfExtraBed = roomToBook.flatMap(_.capacity.map(_.extraBed)),
          capacityOccupancy = roomToBook.flatMap(_.capacity.map(_.occupancy)),
          capacityMaxExtraBed = roomToBook.flatMap(_.capacity.map(_.maxExtraBed)),
          capacityAllowedFreeChildrenAndInfants =
            roomToBook.flatMap(_.capacity.flatMap(_.allowedFreeChildrenAndInfants)),
          stayNoOfAdults = property.flatMap(_.stayOccupancy.map(_.stayNoOfAdults)),
          stayNoOfChildren = property.flatMap(_.stayOccupancy.map(_.stayNoOfChildren)),
          stayListOfChildAge = property.flatMap(_.stayOccupancy.map(_.stayListOfChildAge)),
          stayNoOfRoom = property.flatMap(_.stayOccupancy.map(_.stayNoOfRoom)),
          availabilityType =
            ebeBookingData.flatMap(_.hotel.headOption).flatMap(_.room.headOption).map(_.availabilityType.i)
        )
      )
    } match {
      case Failure(error) => logger.error("Failed to append Product Log", error)
      case _              =>
    }

  protected def toPaymentInfoModelForSingleProperty(
      selectedChargeOption: Option[ChargeOption],
      items: Seq[BookingPropertiesData]
  )(implicit context: RequestContext): Option[PaymentRequestInfo] =
    items.headOption.map { item =>
      if (item.isAlternativeRoomEnabled(context))
        buildPaymentRequestInfo(
          selectedChargeOpt = selectedChargeOption,
          creditCardOpt = item.getEbeBookingData().map(_.creditCard)
        )
      else
        buildPaymentRequestInfo(
          selectedChargeOpt = selectedChargeOption,
          creditCardOpt = item.papiProperties.flatMap(_.getProperty.flatMap(_.getCreditCard))
        )
    }

  def getRateChannelSwapRoomIdentifier(items: Seq[BookingPropertiesData]): Option[String] = {
    for {
      item     <- items.headOption
      papiProp <- item.papiProperties
    } yield getRateChannelSwapAndBookedRooms(papiProp.property.headOption) match {
      case RateChannelSwapRoomsContainer(Some(rcsRoom), None) => rcsRoom.roomIdentifiers
      case _                                                  => None
    }
  }.flatten

  private[facades] def toCrossSellDetail(
      items: Seq[BookingPropertiesData]
  )(implicit context: SetupBookingContext): Option[CrossSellDetail] =
    context.bookingFlowType match {
      case BookingFlow.SingleProperty =>
        items.headOption.flatMap(_.getCrossSellDetail)
      case _ => None
    }
}
