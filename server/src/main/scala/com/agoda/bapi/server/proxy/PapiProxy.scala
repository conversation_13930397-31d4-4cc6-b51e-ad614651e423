package com.agoda.bapi.server.proxy

import _root_.request.PropertyRequest
import akka.actor.ActorSystem
import akka.http.scaladsl.model.HttpHeader
import akka.http.scaladsl.model.HttpHeader.ParsingResult
import com.agoda.bapi.common.MessageService
import com.agoda.bapi.common.cache.LocalCache
import com.agoda.bapi.common.config.Configuration
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.proxy.DependencyNames.Dependency
import com.agoda.bapi.common.proxy.{DependencyNames, HttpTimedoutProxy, ProxyMessage}
import com.agoda.bapi.execution.context.NonBlockingExecutionContext.default
import com.agoda.bapi.server.proxy.PapiProxyUtil._
import com.agoda.bapi.server.proxy.util.PapiProxyMaskUtil
import com.agoda.bapi.server.reporting.logs.PapiProxyErrorLog
import com.agoda.commons.http.api.v2.exceptions.HttpException
import com.agoda.mpb.common.header.AgHeaderKey
import com.agoda.property.client.{PAPISearchAgHttpClient, ResponseWithJson}
import com.agoda.upi.models.enums.ProductEntries
import com.google.inject.Singleton
import org.joda.time.DateTime
import transformers.Properties

import java.util.concurrent.TimeoutException
import javax.inject.Inject
import scala.concurrent.Future
import scala.concurrent.duration._
import scala.language.postfixOps

trait PapiProxy extends HttpTimedoutProxy[PapiTimeoutException] with ProxyMessage {
  def getHealth: Future[Option[Boolean]]

  def getPropertyForBooking(propertyRequest: PropertyRequest)(implicit
      context: RequestContext
  ): Future[ResponseWithJson[Properties]]

  override protected def dependency: Dependency = DependencyNames.Papi

  override def raiseTimeoutException(): PapiTimeoutException = new PapiTimeoutException(
    s"papi timeout after $requestTimeout seconds"
  )
}

case class CancellationRequest(
    locale: String,
    hotelId: Long,
    cxlCode: String,
    checkIn: Option[DateTime],
    checkOut: Option[DateTime],
    bookingDate: Option[DateTime],
    outSet: Option[DateTime],
    includePhases: Boolean
)

/* Proxy for calls to PAPI. */
@Singleton
class PapiProxyImpl @Inject() (
    client: PAPISearchAgHttpClient[Future],
    config: Configuration,
    val messagingService: MessageService
)(implicit val actorSystem: ActorSystem)
    extends PapiProxy
    with LocalCache {

  private val maxRetries: Int                 = config.apiCallSettings.papiMaxRetries
  override val requestTimeout: FiniteDuration = 10.seconds

  override def cacheMaximumSize: Option[Int] = Some(500)

  def getHealth: Future[Option[Boolean]] = {
    val tags = getTags("getHealth", "healthcheck")
    withMeasureAndLogContextGeneric(tags) {
      retry(client.getHealthCheck.map(r => Some(r == "OK")), maxRetries).recover {
        case _ => Some(false)
      }
    }
  }

  def getPropertyForBooking(
      propertyRequest: PropertyRequest
  )(implicit context: RequestContext): Future[ResponseWithJson[Properties]] = {
    val requestSellIn = propertyRequest.pricing
      .flatMap(_.priceAdjustmentRequest.map(_.headOption))
      .flatten
      .map(_.requestedPrice.toString)
      .getOrElse("")
    val trackingCookieId = context.userContext.flatMap(_.experimentData.map(_.userId)).getOrElse("")

    val tags = getPapiTags(
      method = "getPropertyForBookingResponse",
      Map[String, String](
        "platformId" -> context.platformId.toString
      )
    )

    def extractResponseOnSuccess(response: ResponseWithJson[Properties]): Map[String, String] = {
      val property = response.response.property.headOption

      Map[String, String](
        "masterRoomExists"   -> property.exists(_.masterRooms.nonEmpty).toString,
        "roomSwappingExists" -> property.exists(_.roomSwapping.nonEmpty).toString
      )
    }

    withMeasureLogAndExtractTags(
      extractTagsOnSuccess = extractResponseOnSuccess,
      extractTagsOnError = _ => Map.empty,
      additionalTags = tags
    ) {
      runWithReqResMessageLog(
        method = "getPropertyForBookingResponseWithJson",
        request = Some(propertyRequest),
        maskRequest = (req: Option[PropertyRequest]) => req.map(PapiProxyMaskUtil.maskGetPropertyForBookingRequest),
        maskResponse = (res: Option[ResponseWithJson[Properties]]) => res.map(_.response),
        additionalData = Map(
          "trackingCookieId" -> trackingCookieId,
          "requestSellIn"    -> requestSellIn
        )
      ) {
        callForPropertiesIncludingRawResponse(propertyRequest)
      }
    }
  }

  private def serializeRequest[T](request: T): String = {
    client.mapper.writeValueAsString(request)
  }

  private def callPropertyAPI[Request, Response](f: => Future[Response], convertRequestToJsonString: () => String)(
      implicit context: RequestContext
  ): Future[Response] = {
    retry(requestWithTimeout(f), maxRetries).recover {
      case e: TimeoutException =>
        logger.log(new PapiProxyErrorLog(context, convertRequestToJsonString.apply(), Some(e)))
        throw new PapiTimeoutException("Retry Limit exceeded", e)
      case t: HttpException =>
        throw new PapiTimeoutException(s"Retry Limited exceeded with bad request: error - ${t.getMessage}", t)
    }
  }

  private def callForProperties(papiRequest: PropertyRequest, propertyIds: Seq[Long])(implicit
      context: RequestContext
  ): Future[Properties] = {
    val agEnvHeaderMap             = getAgEnvHeader()
    val agMsePricingTokenHeaderMap = getAgMsePricingTokenHeader()
    val headers                    = toHeaders(agEnvHeaderMap ++ agMsePricingTokenHeaderMap)

    val responseF = callPropertyAPI(
      client.getPropertiesResponse(papiRequest, headers).map { res =>
        val ids = res.property.map(_.propertyId)
        if (!propertyIds.forall(ids.contains)) {
          val ex = new NoSuchElementException("Not all requested ids found in PAPI /properties response")
          logger.error(ex.getMessage, ex)
          throw ex
        }
        res
      },
      () => serializeRequest(papiRequest)
    )
    responseF.map(response => removeContactsFromPapiResponseModel(response))
  }
  private def callForPropertiesIncludingRawResponse(papiRequest: PropertyRequest)(implicit
      context: RequestContext
  ): Future[ResponseWithJson[Properties]] = {
    val agHeaders = List(
      AgHeaderKey.AG_ENVIRONMENT_HEADER,
      AgHeaderKey.AG_MSE_PRICING_TOKEN,
      AgHeaderKey.AG_CLIENT_PROFILE,
      AgHeaderKey.AG_AID,
      AgHeaderKey.AG_DOMAIN
    )
    val headers = toHeaders(getAgHeaders(agHeaders: _*))
    val responseF = callPropertyAPI(
      client.getPropertyForBookingResponseWithJson(papiRequest, headers),
      () => serializeRequest(papiRequest)
    )
    responseF.map(response =>
      ResponseWithJson[Properties](
        removeContactsFromPapiResponseModel(response.response),
        removeContactFromPapiJson(response.jsonString)
      )
    )
  }

  private def getPapiTags(
      method: String,
      externalTags: Map[String, String] = Map.empty
  )(implicit context: RequestContext): Map[String, String] =
    getTags(
      methodName = method,
      endpoint = context.path,
      client = context.clientId.toString,
      dmcId = -1,
      externalTags = externalTags
    )

  private[proxy] def toHeaders(headersMap: Map[String, String]): collection.immutable.Seq[HttpHeader] =
    headersMap
      .flatMap {
        case (name, value) =>
          HttpHeader.parse(name, value) match {
            case ParsingResult.Ok(header, Nil) => Some(header)
            case ParsingResult.Ok(_, errors) =>
              logger.error(
                s"PAPI proxy ag-env parsing successfully with errors ${errors.map(_.formatPretty).mkString("\n")}"
              )
              None
            case ParsingResult.Error(error) =>
              logger.error(s"PAPI proxy header passing failed with ${error.formatPretty}")
              None
          }
      }
      .to[collection.immutable.Seq]
}

@Singleton
class MeasuringPapiProxy @Inject() (realProxy: PapiProxyImpl, val messagingService: MessageService)(implicit
    override val actorSystem: ActorSystem
) extends PapiProxy {

  override def getHealth: Future[Option[Boolean]] = realProxy.getHealth

  override val requestTimeout: FiniteDuration = realProxy.requestTimeout

  override def getPropertyForBooking(
      propertyRequest: PropertyRequest
  )(implicit context: RequestContext): Future[ResponseWithJson[Properties]] =
    realProxy.getPropertyForBooking(propertyRequest)
}
