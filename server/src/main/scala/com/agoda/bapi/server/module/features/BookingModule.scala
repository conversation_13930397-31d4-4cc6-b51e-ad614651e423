package com.agoda.bapi.server.module.features

import akka.actor.ActorSystem
import akka.stream.{ActorMaterializer, Materializer}
import com.agoda.bapi.common.config._
import com.agoda.bapi.common.handler.{RequestContextBuilder, RequestContextBuilderImpl}
import com.agoda.bapi.common.localization.{LocaleContextFactory, LocaleContextFactoryImpl}
import com.agoda.bapi.common.logging.{<PERSON><PERSON><PERSON><PERSON><PERSON>, BapiLoggerImpl}
import com.agoda.bapi.common.pricing.{Calculator, CalculatorImpl}
import com.agoda.bapi.common.proxy._
import com.agoda.bapi.common.proxy.provider._
import com.agoda.bapi.common.repository.{CountriesRepository, CreditCardOnFileRepository, LocaleRepository, LocaleRepositoryImpl}
import com.agoda.bapi.common.service._
import com.agoda.bapi.common.util.TripProtectionTokenHelper
import com.agoda.bapi.creation.config._
import com.agoda.bapi.creation.mapper.activity.{ActivityMapper, ActivityMapperImpl}
import com.agoda.bapi.creation.mapper.ebe._
import com.agoda.bapi.creation.mapper.retry.{RetryBookingActionMessageMapper, RetryBookingActionMessageMapperImpl}
import com.agoda.bapi.creation.proxy._
import com.agoda.bapi.creation.proxy.db.addOns.{AddOnsBapiDbProxy, AddOnsBkgDbProxy}
import com.agoda.bapi.creation.proxy.db.basebooking.{ActivityBapiDbProxy, ActivityBkgDbProxy, ActivityDbReadProxy}
import com.agoda.bapi.creation.proxy.db.basebooking.{ActivityBapiDbProxy, ActivityBkgDbProxy, BaseBookingDbReadProxy}
import com.agoda.bapi.creation.proxy.db.flight.{FlightBapiDbProxy, FlightBkgDbProxy, FlightsDbReadProxy}
import com.agoda.bapi.creation.proxy.db.protection.{ProtectionBapiDbProxy, ProtectionBkgDbProxy, ProtectionDbReadProxy}
import com.agoda.bapi.creation.proxy.db.vehicle.{VehicleBapiDbProxy, VehicleBkgDbProxy, VehicleDbRead}
import com.agoda.bapi.creation.proxy.provider.CustomerApiAsyncClientProvider
import com.agoda.bapi.creation.repository.{PaymentMethodRepository => _, PaymentMethodRepositoryImpl => _, _}
import com.agoda.bapi.creation.service._
import com.agoda.bapi.creation.service.processing.{ActivityInfoService, ActivityInfoServiceImpl, AddOnsInProcessingService, AddOnsInProcessingServiceImpl}
import com.agoda.bapi.creation.util.CancellationUtils
import com.agoda.bapi.execution.context.NonBlockingExecutionContext
import com.agoda.bapi.server.addon.{CegFastTrackBookingService, CegFastTrackBookingServiceImpl, GenericAddOnStateService, GenericAddOnStateServiceImpl}
import com.agoda.bapi.server.config._
import com.agoda.bapi.server.config.model.{BookingConsentConfig, CustomerMarketingDisplaySettingsConfig}
import com.agoda.bapi.server.handler._
import com.agoda.bapi.server.logging.{AbsApiLogger, AbsApiLoggerImpl}
import com.agoda.bapi.server.mapper.{ItineraryContextMapper, ItineraryContextMapperImpl}
import com.agoda.bapi.server.model.mapper.{RoundCurrencyHelper, RoundCurrencyHelperImpl}
import com.agoda.bapi.server.module.dbs._
import com.agoda.bapi.server.module.external._
import com.agoda.bapi.server.module.{AutoBindModule, WhiteLabelModule}
import com.agoda.bapi.server.proxy.payment._
import com.agoda.bapi.server.proxy.{BookingCreationByDcProxy, BookingCreationByDcProxyImpl, ServerBFDBProxy => _}
import com.agoda.bapi.server.repository._
import com.agoda.bapi.server.route._
import com.agoda.bapi.server.service._
import com.agoda.bapi.server.service.payment.feature.{PaymentFeatureProcessor, PaymentFeatureProcessorComposite, PaymentFeatureProcessorCompositeImpl}
import com.agoda.bapi.server.service.payment.funnel.{FunnelProcessor, FunnelProcessorComposite, FunnelProcessorCompositeImpl}
import com.agoda.bapi.server.service.payment.mapper.{PaymentMapper, PaymentMapperImpl}
import com.agoda.bapi.server.service.payment.{PaymentService, PaymentServiceImpl}
import com.agoda.bapi.server.service.replication.state.{SetStateBridgeService, SetStateBridgeServiceImpl}
import com.agoda.bapi.server.service.vehicle.VehicleBookingService
import com.agoda.bapi.server.utils._
import com.agoda.bapi.server.validator._
import com.agoda.paymentapiv2.client.v2.common.api.{GatewayApi, PaymentNonPciApi}
import com.agoda.winterfell.client.CustomerApi
import com.agoda.winterfell.unified.progress.CryptoMetadataService
import com.google.inject.AbstractModule
import com.google.inject.name.Names
import com.typesafe.config.ConfigFactory
import net.codingwell.scalaguice.{ScalaModule, ScalaMultibinder}
import com.agoda.bapi.creation.repository.{GlobalPaymentMethodRepository, PaymentMethodRepository => CreationPaymentRepository, PaymentMethodRepositoryImpl => CreationPaymentRepositoryImpl}

import javax.inject.Singleton
import scala.concurrent.Future

class BookingModule extends AbstractModule with ScalaModule {
  val bapiRootPackage = "com.agoda.bapi"
  override def configure(): Unit = {
    install(new BfdbModule)
    install(new LocalBkgdbModule)
    install(new BkgdbModule)
    install(new CdbModule)
    install(new PapiModule)
    install(new MdbModule)
    install(new BcreModule)
    install(new CustomerApiModule)
    install(new BookingCustomerServiceModule)
    install(new WhiteLabelModule)
    install(new CcofApiModule)
    install(new SetupBookingModule)
    install(new AbsModule)

    val actorSystem =
      ActorSystem("bookingapi-server", Some(ConfigFactory.load), None, Some(NonBlockingExecutionContext.default))
    bind[ActorSystem].annotatedWith(Names.named("bookingapi-server-actor")).toInstance(actorSystem)
    bind[Materializer]
      .annotatedWith(Names.named("bookingapi-server-actormaterializer"))
      .toInstance(ActorMaterializer()(actorSystem))

    bind[CreationConfig].toProvider[CreationConfigProvider]
    bind[CustomerApi].annotatedWithName("CustomerApiV2Async").toProvider[CustomerApiAsyncClientProvider].in[Singleton]
    bind[CustomerApi]
      .annotatedWithName("CustomerApiAsyncV2Authed")
      .toProvider[CustomerApiAsyncClientProviderV2Authed]
      .in[Singleton]
    bind[CustomerApi]
      .annotatedWithName("CustomerApiAsyncV2Base")
      .toProvider[CustomerApiAsyncClientProviderV2Base]
      .in[Singleton]
    bind[CryptoMetadataService].toProvider[CryptoMetadataServiceProvider].in[Singleton]

    bind[GatewayApi[Future]].toProvider[GatewayApiClientProvider].in[Singleton]
    bind[PaymentNonPciApi[Future]].toProvider[PaymentNonPciApiClientProvider].in[Singleton]

    // routes
    val routesMulti = ScalaMultibinder.newSetBinder[Routes](binder)
    routesMulti.addBinding.to[FlightRoutes]
    routesMulti.addBinding.to[ItineraryRoutes]

    // handlers
    bind[BookingsHandler].to[BookingsHandlerImpl]
    bind[ContinuationHandler].to[ContinuationHandlerImpl]

    // GRPC Service Endpoints
    bind[SetStateBridgeService].to[SetStateBridgeServiceImpl]

    // config
    bind[ValidationConfig].toProvider[ValidationConfigProvider]
    bind[ReplicateStateConfig].toProvider[ReplicateStateConfigProvider].in[Singleton]

    // proxies
    bind[BookingCreationByDcProxy].to[BookingCreationByDcProxyImpl]
    bind[DefaultDbProxy].to[EbeLiteDbProxyImpl]
    bind[CreationCDBProxy].to[CreationCDBProxyImpl]

    bind[PaymentApiProxy].to[PaymentApiProxyImpl]

    bind[EnigmaApiProxy].toProvider[EnigmaApiProxyProvider].in[Singleton]
    bind[CustomerBillingInfoProxy].toProvider[CustomerBillingInfoProxyProvider].in[Singleton]
    bind[ItineraryBillingInfoProxy].toProvider[ItineraryBillingInfoProxyProvider].in[Singleton]

    bind[FlightsDbProxy].to[FlightsDbProxyImpl]
    bind[FlightsDbReadProxy]
      .annotatedWithName("FlightsBkgDbProxy")
      .to[FlightBkgDbProxy]
    bind[FlightsDbReadProxy]
      .annotatedWithName("FlightsLocalBkgDbProxy")
      .to[FlightBapiDbProxy]
    bind[CustomerApiProxy].to[CustomerApiProxyImpl]
    bind[WorkflowDbProxy].to[WorkflowDbProxyImpl]
    bind[AbsProxy].to[AbsProxyImpl]
    bind[MultiProductDbProxy].to[MultiProductDbProxyImpl]
    bind[ProtectionDbProxy].to[ProtectionDbProxyImpl]
    bind[ProtectionDbReadProxy]
      .annotatedWithName("ProtectionBkgDbProxy")
      .to[ProtectionBkgDbProxy]
    bind[ProtectionDbReadProxy]
      .annotatedWithName("ProtectionLocalBkgDbProxy")
      .to[ProtectionBapiDbProxy]
    bind[VehicleDBProxy].to[VehicleDbProxyImpl]
    bind[VehicleDbRead]
      .annotatedWithName("VehicleBkgDbProxy")
      .to[VehicleBkgDbProxy]
    bind[VehicleDbRead]
      .annotatedWithName("VehicleLocalBkgDbProxy")
      .to[VehicleBapiDbProxy]
    bind[FinancialBreakdownProxy].to[FinancialBreakdownProxyImpl]
    bind[GenericProductActionDbProxy].to[GenericProductActionDbProxyImpl]
    bind[ActivityDbProxy].to[ActivityDbProxyImpl]
    bind[PropertyActionDbProxy].to[PropertyActionDbProxyImpl]
    bind[BaseBookingDBProxy].to[BaseBookingDbProxyImpl]
    bind[ActivityDbReadProxy]
      .annotatedWithName("ActivityBkgDbProxy")
      .to[ActivityBkgDbProxy]
    bind[ActivityDbReadProxy]
      .annotatedWithName("ActivityLocalBkgDbProxy")
      .to[ActivityBapiDbProxy]
    bind[BaseBookingDbReadProxy]
      .annotatedWithName("AddOnsBkgDbProxy")
      .to[AddOnsBkgDbProxy]
    bind[BaseBookingDbReadProxy]
      .annotatedWithName("AddOnsLocalBkgDbProxy")
      .to[AddOnsBapiDbProxy]
    bind[CegFastTrackDbProxy].to[CegFastTrackDbProxyImpl]
    bind[CegFastTrackActionDbProxy].to[CegFastTrackActionDbProxyImpl]
    bind[PendingBookingChangeDbProxy].to[PendingBookingChangeDbProxyImpl]
    bind[GenericAddOnDbProxy].to[GenericAddOnDbProxyImpl]
    bind[GenericAddOnActionDbProxy].to[GenericAddOnActionDbProxyImpl]

    // validators
    bind[BookingsRequestValidator].to[BookingsRequestValidatorImpl]
    bind[ContinuePaymentRequestValidator].to[ContinuePaymentRequestValidatorImpl]
    bind[CcofValidator].to[CcofValidatorImpl]
    bind[CarRequestValidator].to[CarRequestValidatorImpl]
    bind[ActivityRequestValidator].to[ActivityRequestValidatorImpl]
    bind[ExternalLoyaltyPointsValidator].to[ExternalLoyaltyPointsValidatorImpl]
    bind[RebookAndCancelRequestValidator].to[RebookAndCancelRequestValidatorImpl]
    // services
    bind[BookingsService].to[BookingsServiceImpl]
    bind[CommonBookingsService].to[CommonBookingsServiceImpl]
    bind[Calculator].to[CalculatorImpl]
    bind[ExchangeRateHelper].to[ExchangeRateHelperImpl]
    bind[FlightBookingsService].to[FlightBookingsServiceImpl]
    bind[FlightInfoService]
      .annotatedWithName("FlightInfoServiceV1")
      .to[FlightInfoServiceImpl]
    bind[FlightInfoService]
      .annotatedWithName("FlightInfoServiceV2")
      .to[FlightInfoServiceV2]
    bind[VehicleInfoService].to[VehicleInfoServiceImpl]
    bind[VehicleBookingService]
    bind[UrlService].to[UrlServiceImpl]
    bind[ContinueCreationService].to[ContinueCreationServiceImpl]
    bind[FlightMessagingCommonService].to[FlightMessagingCommonServiceImpl]
    bind[CreditCardOnFileService].to[CreditCardOnFileServiceImpl]
    bind[CustomerService].to[CustomerServiceImpl]
    bind[CustomerRiskService].to[CustomerRiskServiceImp]
    bind[OrchestrationMessageService].to[OrchestrationMessageServiceImpl]
    bind[BookingActionMessageService].to[BookingActionMessageServiceImpl]
    bind[BcreEndpointMessageService].to[BcreEndpointMessageServiceImpl]
    bind[ActivityService].to[ActivityServiceImpl]
    bind[ActivityInfoService].to[ActivityInfoServiceImpl]
    bind[AddOnsInProcessingService].to[AddOnsInProcessingServiceImpl]
    bind[StatusTokenService].to[StatusTokenServiceImpl]
    bind[CegFastTrackBookingService].to[CegFastTrackBookingServiceImpl]
    bind[GenericAddOnStateService].to[GenericAddOnStateServiceImpl]
    bind[PendingBookingChangeService].to[PendingBookingChangeServiceImpl]
    bind[FlightComplianceService].to[FlightComplianceServiceImpl]
    bind[ItineraryContextMapper].to[ItineraryContextMapperImpl]
    bind[RetryPaymentService].to[RetryPaymentServiceImpl]

    // PaymentService
    bind[PaymentService].to[PaymentServiceImpl]
    AutoBindModule.autoBind(bapiRootPackage, classOf[PaymentFeatureProcessor], binder())
    AutoBindModule.autoBind(bapiRootPackage, classOf[FunnelProcessor], binder())
    bind[PaymentFeatureProcessorComposite].to[PaymentFeatureProcessorCompositeImpl]
    bind[FunnelProcessorComposite].to[FunnelProcessorCompositeImpl]
    bind[PaymentMapper].to[PaymentMapperImpl]

    // repositories
    bind[EbeLiteBookingRepository].to[EbeLiteBookingRepositoryImpl]
    bind[CountriesRepository].to[CountriesRepositoryImpl]
    bind[CmsRepository].to[CmsRepositoryImpl]
    bind[GlobalPaymentMethodRepository].to[PaymentMethodRepositoryImpl]
    bind[CreationPaymentRepository].to[CreationPaymentRepositoryImpl]
    bind[PaymentMethodRepository].to[PaymentMethodRepositoryImpl]
    bind[LocaleRepository].to[LocaleRepositoryImpl]
    bind[HotelRepository].to[HotelRepositoryImpl]
    bind[PaymentApiRepository].to[PaymentApiRepositoryImpl]
    bind[FlightBookingRepository].to[FlightBookingRepositoryImpl]
    bind[WorkflowRepository].to[WorkflowRepositoryImpl]
    bind[MultiProductRepository].to[MultiProductRepositoryImpl]
    bind[CreditCardOnFileRepository].to[CreditCardOnFileRepositoryImpl]
    bind[ProtectionBookingRepository].to[ProtectionBookingRepositoryImpl]
    bind[MultiProductRepository].to[MultiProductRepositoryImpl]
    bind[VehicleBookingRepository].to[VehicleBookingRepositoryImpl]
    bind[ActivityBookingRepository].to[ActivityBookingRepositoryImpl]
    bind[PropertyBookingRepository].to[PropertyBookingRepositoryImpl]
    bind[PaymentLimitationRepository].to[PaymentLimitationRepositoryImpl]
    bind[CegFastTrackRepository].to[CegFastTrackRepositoryImpl]
    bind[GenericAddOnRepository].to[GenericAddOnRepositoryImpl]
    bind[PendingBookingChangeRepository].to[PendingBookingChangeRepositoryImpl]

    // mappers
    bind[FlightSeatSelectionDetailMapper].to[FlightSeatSelectionDetailMapperImpl]
    bind[FlightMapper].to[FlightMapperImpl]
    bind[CurrenciesRepository].to[CurrenciesRepositoryImpl]
    bind[RoundCurrencyHelper].to[RoundCurrencyHelperImpl]
    bind[ProtectionMapper].to[ProtectionMapperImpl]
    bind[PropertyModelInternalMapper].to[PropertyModelInternalMapperImpl]
    bind[ActivityMapper].to[ActivityMapperImpl]
    bind[ActivityRiskInfoMapper].to[ActivityRiskInfoMapperImpl]
    bind[RetryBookingActionMessageMapper].to[RetryBookingActionMessageMapperImpl]

    // builders
    bind[RequestContextBuilder].to[RequestContextBuilderImpl]

    // locale context factory
    bind[LocaleContextFactory].to[LocaleContextFactoryImpl]

    // utils
    bind[ProductTokenCreatorUtils].to[ProductTokenCreatorUtilsImpl]
    bind[ProductTokenExtractorUtils].to[ProductTokenExtractorUtilsImpl]
    bind[TripProtectionTokenHelper].toInstance(TripProtectionTokenHelper)
    bind[CancellationUtils].toInstance(CancellationUtils)
    bind[EbeBookingChargeMapperUtils].to[EbeBookingChargeMapperUtilsImpl]

    bind[DuplicateConfig].toProvider[DuplicateConfigProvider]
    bind[PropertyBookingCreationConfig].toProvider[PropertyBookingCreationConfigProvider].in[Singleton]

    bind[BookingAmendmentConfig].toProvider[BookingAmendmentConfigProvider].in[Singleton]
    bind[BookingConsentConfig].toProvider[BookingConsentConfigProvider].in[Singleton]
    bind[CustomerMarketingDisplaySettingsConfig]
      .toProvider[CustomerMarketingDisplaySettingsConfigProvider]
      .in[Singleton]
    bind[FlightPaymentComplianceIndiaWhiteListConfig]
      .toProvider[FlightPaymentComplianceIndiaWhiteListConfigProvider]
      .in[Singleton]

    // logs
    bind[BapiLogger].to[BapiLoggerImpl]
    bind[AbsApiLogger].to[AbsApiLoggerImpl]
  }
}
