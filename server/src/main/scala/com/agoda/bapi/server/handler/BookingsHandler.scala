package com.agoda.bapi.server.handler

import cats.implicits._
import com.agoda.adp.messaging.message.logging.LogLevel
import com.agoda.bapi.common.ToolSet
import com.agoda.bapi.common.config.KillSwitches
import com.agoda.bapi.common.directive.BookingAPIHttpHeader
import com.agoda.bapi.common.exception.{BookingCreationLogMessageBase, VehicleModelInternalIsNotDefinedException}
import com.agoda.bapi.common.handler.{RequestContext, RequestContextBuilder}
import com.agoda.bapi.common.message._
import com.agoda.bapi.common.message.creation.{GetStatusRequest, GetStatusResponse}
import com.agoda.bapi.common.message.flightBookingReplicateByItineraryID.{FlightBookingReplicateByItineraryIDRequest, FlightBookingReplicateByItineraryIDResponse}
import com.agoda.bapi.common.message.multi.product.SetStateResponse.WarnCode
import com.agoda.bapi.common.message.multi.product._
import com.agoda.bapi.common.model.car.VehicleBookingState
import com.agoda.bapi.common.model.flight.flightModel.FlightModelInternal
import com.agoda.bapi.common.model.multiproduct.{MultiProductInfoDBModel, SetStateItineraryModel}
import com.agoda.mpb.common.models.state.ProductType.ProductType
import com.agoda.bapi.common.model.product.BookingRequestTypeResolver
import com.agoda.bapi.common.model.{InvalidRequestDetail, StatusToken}
import com.agoda.bapi.common.tags.Tags
import com.agoda.bapi.common.util.{RequestBaseUtils, ServerUtils}
import com.agoda.bapi.creation.mapper.ebe.SensitiveInfoMapper
import com.agoda.bapi.creation.service.{BcreEndpointMessageService, HadoopMessageProcessName, HadoopMessagingService}
import com.agoda.bapi.execution.context.NonBlockingExecutionContext.default
import com.agoda.bapi.server.addon.{CegFastTrackBookingService, GenericAddOnStateService}
import com.agoda.bapi.server.proxy.BookingCreationByDcProxy
import com.agoda.bapi.server.service._
import com.agoda.bapi.server.service.vehicle.VehicleBookingService
import com.agoda.bapi.server.utils.CommonUtils
import com.agoda.bapi.server.validator.BookingsRequestValidator
import com.agoda.commons.agprotobuf.scalapb.utils.ProtoConverter
import com.agoda.mpb.common.models.state.ProductType
import com.agoda.mpb.common.MultiProductType
import com.agoda.mpbe.state.product.addOn.AddOnProductModel
import com.agoda.mpbe.state.product.cegFastTrack.CegFastTrackProductModel
import com.fasterxml.jackson.core.JsonParseException
import com.google.inject.Inject

import scala.collection.mutable
import scala.concurrent.Future
import scala.language.{postfixOps, reflectiveCalls}
import scala.util.control.NonFatal
import scala.util.{Failure, Success, Try}

trait BookingsHandler {
  def getItineraryStatus(
      bookingAPIHttpHeader: BookingAPIHttpHeader,
      ip: String,
      request: GetStatusRequest
  ): Future[GetStatusResponse]

  def setBookingState(
      bookingHttpHeader: BookingAPIHttpHeader,
      ip: String,
      request: SetStateRequest
  ): Future[SetStateResponse]

  def replicateItineraryBookingState(
      bookingHttpHeader: BookingAPIHttpHeader,
      ip: String,
      request: SetStateRequest
  ): Future[SetStateResponse]

  def replicateFlightBookingStateByItineraryId(
      bookingHttpHeader: BookingAPIHttpHeader,
      ip: String,
      request: FlightBookingReplicateByItineraryIDRequest
  ): Future[FlightBookingReplicateByItineraryIDResponse]

  def replicateNonPropertyBookingStateByItineraryId(
      bookingAPIHttpHeader: BookingAPIHttpHeader,
      ip: String,
      request: ReplicateByItineraryIdRequest
  ): Future[ReplicateByItineraryIdResponse]
}

/* Maps booking related requests to the service layer. */
class BookingsHandlerImpl @Inject() (
    requestContextBuilder: RequestContextBuilder,
    requestValidator: BookingsRequestValidator,
    bookingsService: BookingsService,
    commonBookingsService: CommonBookingsService,
    bcreProxy: BookingCreationByDcProxy,
    hadoopMessaging: HadoopMessagingService,
    flightBookingsService: FlightBookingsService,
    vehicleBookingService: VehicleBookingService,
    activityService: ActivityService,
    propertyService: PropertyService,
    cegFastTrackBookingService: CegFastTrackBookingService,
    addOnStateService: GenericAddOnStateService,
    pendingBookingChangeService: PendingBookingChangeService,
    bcreEndpointMessageService: BcreEndpointMessageService,
    killSwitches: KillSwitches
) extends BookingsHandler
    with ToolSet {
  override def getItineraryStatus(
      bookingHttpHeader: BookingAPIHttpHeader,
      ip: String,
      request: GetStatusRequest
  ): Future[GetStatusResponse] = {
    val statusTokenOpt = StatusToken.deserialize(request.statusToken, log, withMeasure)

    withReqReslog[GetStatusRequest, GetStatusResponse](
      httpHeader = bookingHttpHeader,
      ip = ip,
      request = request,
      errorMessage = "Error during checking itinerary status",
      createJson = None,
      createAdditionalTags = Some(getAdditionalTags),
      loggerTags = Some(
        Map(
          Tags.Common.ITINERARY_ID       -> statusTokenOpt.map(_.itineraryId.toString).toOption.getOrElse(""),
          Tags.Common.BOOKING_ID         -> statusTokenOpt.map(_.bookingId.toString).toOption.getOrElse(""),
          Tags.Common.SESSION_ID         -> request.bookingContext.map(_.sessionId).getOrElse(""),
          Tags.Common.DEVICE_TYPE        -> RequestBaseUtils.devicePlatform(request).toString,
          Tags.Common.BOOKING_SESSION_ID -> request.bookingContext.flatMap(_.bookingSessionId).getOrElse("")
        )
      )
    ) {
      val bookingActionsFut = statusTokenOpt match {
        case Success(token) if ServerUtils.isSameDC(token.dc) && token.itineraryId != 0 =>
          commonBookingsService.getBookingActionsByItineraryId(token.itineraryId)
        case Success(_) =>
          logger.warn("itinerary from token does not meet same-dc criteria, fallback")
          Future.successful(Seq.empty)
        case Failure(ex) =>
          logger.warn("booking action cannot be retrieved from database", ex)
          Future.successful(Seq.empty)
      }

      val bookingActionWithRequestContextFut = for {
        bookingActions                  <- bookingActionsFut
        masterBookingActionOpt           = CommonUtils.getMasterBookingAction(bookingActions)
        masterBookingActionState         = masterBookingActionOpt.flatMap(CommonUtils.getBookingActionState)
        bookingSessionFromBookingContext = request.bookingContext.flatMap(_.bookingSessionId)
        bookingSessionIdOpt =
          CommonUtils.getBookingSessionId(masterBookingActionState, bookingSessionFromBookingContext)
        userIdFromMasterAction = masterBookingActionState.flatMap(CommonUtils.getUserId)
        /* In status endpoint, it's possible that FE sends different userId than when create, affecting experiment
         * allocation */
        // so we should always override userId from master action
        requestContext <-
          requestContextBuilder.build(request, bookingHttpHeader, bookingSessionIdOpt, userIdFromMasterAction)
      } yield (bookingActions, requestContext)

      bookingActionWithRequestContextFut
        .flatMap {
          case (bookingActions, requestContext) =>
            implicit val context: RequestContext = requestContext
            bcreEndpointMessageService.withGetStatusEndpointResReqLog(
              request,
              bookingHttpHeader,
              statusTokenOpt.toOption
            ) {
              for {
                _ <- if (killSwitches.stopLoggingBapiItineraryStatusLogMessage) Future.successful()
                     else
                       hadoopMessaging.sendBookingGetStatusLogMessage(
                         HadoopMessageProcessName.GetItineraryStatusRequest,
                         context.getCorrelationId(),
                         context.clientId,
                         request,
                         None,
                         context.bookingCreationContext.flatMap(_.bookingSessionId),
                         context.pollingId
                       )
                token <- Future.fromTry(statusTokenOpt)
                result <- if (ServerUtils.isSameDC(token.dc))
                            commonBookingsService.getItineraryStatus(request, bookingActions)
                          else
                            bcreProxy.getItineraryStatus(request, token.dc)
                _ <- if (killSwitches.stopLoggingBapiItineraryStatusLogMessage) Future.successful()
                     else
                       hadoopMessaging.sendBookingGetStatusResponseLogMessage(
                         context.getCorrelationId(),
                         context.clientId,
                         result,
                         context.bookingCreationContext.flatMap(_.bookingSessionId),
                         context.pollingId
                       )
              } yield result
            }
        }
    }
      .recover {
        case e @ (_: JsonParseException | _: IllegalArgumentException) =>
          GetStatusResponse.badRequest(exception = e, statusToken = request.statusToken)
        case NonFatal(error) =>
          GetStatusResponse.internal(exception = error, statusToken = request.statusToken)
      }
  }

  override def setBookingState(
      bookingHttpHeader: BookingAPIHttpHeader,
      ip: String,
      request: SetStateRequest
  ): Future[SetStateResponse] = {
    requestContextBuilder.build(request, bookingHttpHeader).flatMap { implicit context =>
      withReqReslog[SetStateRequest, SetStateResponse](
        httpHeader = bookingHttpHeader,
        ip = ip,
        request = request,
        errorMessage = s"${bookingHttpHeader.path} processed failed on bookingId:${request.bookingId}",
        loggerTags = Some(
          Map(
            Tags.Common.BOOKING_ID   -> Try(request.bookingId.toString).toOption.getOrElse("-1"),
            Tags.Common.ITINERARY_ID -> Try(request.itinerary.itineraryId.toString).toOption.getOrElse("-1")
          )
        ),
        isLogResponse = true,
        createJson = Some(inputData => {
          val jsonValue       = SensitiveInfoMapper.createJson(inputData)
          val maskedJsonValue = SensitiveInfoMapper.maskSensitiveInfo(jsonValue)
          maskedJsonValue
        })
      ) {
        bcreEndpointMessageService.withSetStateEndpointResReqLog(request, bookingHttpHeader) {
          request.crossProductIsolatedFeature match {
            case Some(crossProductIsolatedFeature: CrossProductIsolatedFeature) =>
              crossProductIsolatedFeature.pendingBookingChange match {
                case Some(pendingBookingChange) =>
                  upsertPendingBookingChange(pendingBookingChange)
                case None =>
                  Future.failed(
                    new Exception(
                      s"No feature found inside crossProductIsolatedFeature for bookingId: ${request.bookingId}"
                    )
                  )
              }
            case None =>
              requestValidator.validateSetItineraryBookingState(request) match {
                case Some(validation)                                 => Future.successful(validation)
                case None if request.vehicle.exists(_.nonEmpty)       => saveVehicleBookingState(request)
                case None if request.activities.exists(_.nonEmpty)    => saveActivityBookingState(request)
                case None if request.properties.exists(_.nonEmpty)    => savePropertyBookingState(request)
                case None if request.cegFastTracks.exists(_.nonEmpty) => saveCegFastTrackBookingState(request)
                case None if request.addOns.exists(_.nonEmpty)        => saveAddOnBookingState(request)
                case None if request.flights.nonEmpty =>
                  flightBookingsService.setFlightBookingState(bookingHttpHeader, ip, request, false)
                case None => processOnlyItineraryBookingState(request)
              }
          }

        }
      }
    }
  }

  private[handler] def saveVehicleBookingState(
      setStateRequest: SetStateRequest
  ): Future[SetStateResponse] =
    VehicleBookingState
      .fromSetStateRequest(setStateRequest, None)
      .map { vehicleBookingState =>
        vehicleBookingService
          .saveVehicleBookingState(vehicleBookingState)
          .map { updatedModel =>
            SetStateResponse.fromVehicleBookingState(
              setStateRequest.actionType,
              setStateRequest.actionId,
              setStateRequest.bookingType,
              updatedModel
            )
          }
          .recover {
            case error =>
              logger.error(s"Failed to save vehicle booking ${setStateRequest.bookingId}", error)
              SetStateResponse.withError(ResponseErrorCode.InternalError, error.getMessage)
          }
      }
      .getOrElse(
        Future.failed(
          new VehicleModelInternalIsNotDefinedException(
            s"VehicleModelInternal is not defined for bookingId: ${setStateRequest.bookingId}"
          )
        )
      )

  private[handler] def saveActivityBookingState(
      setStateRequest: SetStateRequest
  ): Future[SetStateResponse] = {
    val multiProductInfos = mapToMultiProductInfoDBModels(setStateRequest.multiProductInfos)
    ActivityBookingState
      .fromSetStateRequest(setStateRequest, None)
      .map { activityBookingState =>
        activityService
          .saveActivityBookingState(activityBookingState, multiProductInfos)
          .map { updatedModel =>
            SetStateResponse.fromActivityBookingState(
              setStateRequest.actionType,
              setStateRequest.actionId,
              setStateRequest.bookingType,
              updatedModel
            )
          }
          .recover {
            case error =>
              logger.error(s"Failed to save activity booking ${setStateRequest.bookingId}", error)
              SetStateResponse.withError(ResponseErrorCode.InternalError, error.getMessage)
          }
      }
      .getOrElse(
        Future.failed(
          new Exception(
            s"ActivityBookingState is not defined for bookingId: ${setStateRequest.bookingId}"
          )
        )
      )
  }

  private[handler] def savePropertyBookingState(
      setStateRequest: SetStateRequest
  ): Future[SetStateResponse] =
    PropertyBookingStateWithItinerary
      .fromSetStateRequest(setStateRequest)
      .map { propertyBookingState =>
        propertyService
          .savePropertyBookingState(propertyBookingState)
          .map { updatedModel =>
            SetStateResponse.fromPropertyBookingState(
              setStateRequest.actionType,
              setStateRequest.actionId,
              setStateRequest.bookingType,
              updatedModel
            )
          }
          .recover {
            case error =>
              logger.error(s"Failed to save property booking ${setStateRequest.bookingId}", error)
              SetStateResponse.withError(ResponseErrorCode.InternalError, error.getMessage)
          }
      }
      .getOrElse(
        Future.failed(
          new Exception(
            s"PropertyModelInternal is not defined for bookingId: ${setStateRequest.bookingId}"
          )
        )
      )

  private[handler] def upsertPendingBookingChange(
      pendingBookingChange: PendingBookingChange
  ): Future[SetStateResponse] = {
    pendingBookingChangeService
      .upsertPendingBookingChange(pendingBookingChange)
      .map { updatedModel =>
        {
          withMeasure(
            "Bcre_pending_booking_change_service",
            1,
            Map("success" -> "true", "exceptionType" -> "na")
          )
          logger.log(
            new BookingCreationLogMessageBase(
              s"pendingBookingChange upsert success for booking: : ${pendingBookingChange.bookingId}"
            ) {
              stringTags.put("bookingId", pendingBookingChange.bookingId.toString)
            }
          )
          SetStateResponse.fromPendingBookingChange(
            updatedModel
          )
        }
      }
      .recover {
        case e =>
          val error_msg =
            s"Failed to save pendingBookingChange with booking: ${pendingBookingChange.bookingId} for error: ${e.getMessage} caused by: ${Option(
                e.getCause
              ).map(cause => cause.getMessage).getOrElse("")}"

          logger.log(
            new BookingCreationLogMessageBase(error_msg, Some(e)) {
              override def logLevel: LogLevel = LogLevel.ERROR
              stringTags.put("bookingId", pendingBookingChange.bookingId.toString)
            }
          )
          withMeasure(
            "Bcre_pending_booking_change_service",
            1,
            Map("success" -> "false", "exceptionType" -> "service_error")
          )
          SetStateResponse.withError(ResponseErrorCode.InternalError, error_msg)
      }

  }

  private[handler] def saveCegFastTrackBookingState(setStateRequest: SetStateRequest): Future[SetStateResponse] = {
    val cegFastTrackBookingStateWithItineraryOpt = setStateRequest.cegFastTracks
      .map { productModels =>
        {
          CegFastTrackBookingStateWithItinerary(
            itinerary = setStateRequest.itinerary,
            itineraryHistories = setStateRequest.history,
            payments = setStateRequest.payments,
            bookingPayments = setStateRequest.bookingPayments.getOrElse(Seq.empty),
            relationships = setStateRequest.bookingRelationships.getOrElse(Seq.empty),
            cegFastTracks = productModels.map(productModel =>
              ProtoConverter.stringToProto(productModel.cegFastTrackState, CegFastTrackProductModel)
            ),
            multiProductBookingGroups =
              setStateRequest.multiProductBookingGroups.getOrElse(Seq.empty).map(_.toMultiProductBookingGroupDBModel)
          )
        }
      }

    cegFastTrackBookingStateWithItineraryOpt
      .map { cegFastTrackBookingState =>
        {
          val multiProductInfos = mapToMultiProductInfoDBModels(setStateRequest.multiProductInfos)
          cegFastTrackBookingService
            .saveCegFastTrackBookingState(cegFastTrackBookingState, multiProductInfos)
            .map { updatedModel =>
              SetStateResponse.fromCegFastTrackBookingState(
                setStateRequest.actionType,
                setStateRequest.actionId,
                setStateRequest.bookingType,
                updatedModel
              )
            }
            .recover {
              case error =>
                logger.error(s"Failed to save cegFastTrack booking ${setStateRequest.bookingId}", error)
                SetStateResponse.withError(ResponseErrorCode.InternalError, error.getMessage)
            }
        }
      }
      .getOrElse {
        Future.failed(
          new Exception(
            s"CegFastTrackBookingState is not defined for bookingId: ${setStateRequest.bookingId}"
          )
        )
      }
  }

  private[handler] def saveAddOnBookingState(setStateRequest: SetStateRequest): Future[SetStateResponse] = {
    val addOnBookingStateWithItineraryOpt = setStateRequest.addOns
      .map { productModels =>
        {
          AddOnBookingStateWithItinerary(
            itinerary = setStateRequest.itinerary,
            itineraryHistories = setStateRequest.history,
            payments = setStateRequest.payments,
            bookingPayments = setStateRequest.bookingPayments.getOrElse(Seq.empty),
            relationships = setStateRequest.bookingRelationships.getOrElse(Seq.empty),
            addOns = productModels.map(productModel =>
              ProtoConverter.stringToProto(productModel.addOnState, AddOnProductModel)
            ),
            multiProductBookingGroups =
              setStateRequest.multiProductBookingGroups.getOrElse(Seq.empty).map(_.toMultiProductBookingGroupDBModel)
          )
        }
      }

    addOnBookingStateWithItineraryOpt
      .map { addOnBookingState =>
        {
          val multiProductInfos = mapToMultiProductInfoDBModels(setStateRequest.multiProductInfos)
          addOnStateService
            .saveAddOnBookingState(addOnBookingState, multiProductInfos)
            .map { updatedModel =>
              SetStateResponse.fromAddonBookingState(
                setStateRequest.actionType,
                setStateRequest.actionId,
                setStateRequest.bookingType,
                updatedModel
              )
            }
            .recover {
              case error =>
                logger.error(
                  s"Failed to save for addon bookings ${setStateRequest.addOns.map(_.map(_.bookingId))}",
                  error
                )
                SetStateResponse.withError(ResponseErrorCode.InternalError, error.getMessage)
            }
        }
      }
      .getOrElse {
        Future.failed(
          new Exception(
            s"AddOnBookingState is not defined"
          )
        )
      }
  }

  private def processOnlyItineraryBookingState(
      request: SetStateRequest
  )(implicit requestContext: RequestContext): Future[SetStateResponse] = {
    for {
      itineraryModel <- flightBookingsService.setOnlyItineraryBookingState(request, false)
    } yield SetStateResponse.withSuccess(itineraryModel)
  }

  override def replicateItineraryBookingState(
      bookingHttpHeader: BookingAPIHttpHeader,
      ip: String,
      request: SetStateRequest
  ): Future[SetStateResponse] = requestContextBuilder.build(request, bookingHttpHeader).flatMap { implicit context =>
    withReqReslog[SetStateRequest, SetStateResponse](
      httpHeader = bookingHttpHeader,
      ip = ip,
      request = request,
      errorMessage = s"${bookingHttpHeader.path} processed failed on bookingId:${request.bookingId}",
      loggerTags = Some(
        Map(
          Tags.Common.BOOKING_ID   -> Try(request.bookingId.toString).toOption.getOrElse("-1"),
          Tags.Common.ITINERARY_ID -> Try(request.itinerary.itineraryId.toString).toOption.getOrElse("-1")
        )
      ),
      isLogResponse = true,
      createJson = Some(inputData => {
        val jsonValue       = SensitiveInfoMapper.createJson(inputData)
        val maskedJsonValue = SensitiveInfoMapper.maskSensitiveInfo(jsonValue)
        maskedJsonValue
      })
    ) {
      bcreEndpointMessageService.withReplicateStateEndpointResReqLog(request, bookingHttpHeader) {
        requestValidator.validateSetItineraryBookingState(request) match {
          case Some(validation) => Future.successful(validation)
          case None =>
            for {
              propertySetStateResponse <- processPropertyBookingState(request)                      // Package,
              flightSetStateResponse   <- processFlightBookingState(request, bookingHttpHeader, ip) // Package, Flight
              vehicleSetStateResponse <-
                replicateVehicle(request, flightSetStateResponse.flatMap(_.toItineraryModel)) // vehicles
              activitySetStateResponse <-
                replicateActivity(request, vehicleSetStateResponse.flatMap(_.toItineraryModel)) // activities
              cegFastTrackSetStateResponse <-
                replicateCegFastTrack(
                  request,
                  flightSetStateResponse.flatMap(_.toItineraryModel)
                ) // passing flight information becasue flight information contain itinerary data.
              addOnSetStateResponse <- replicateAddOn(request, flightSetStateResponse.flatMap(_.toItineraryModel))
            } yield handleSetBookingStateResponse(
              propertySetStateResponse,
              flightSetStateResponse,
              vehicleSetStateResponse,
              activitySetStateResponse,
              cegFastTrackSetStateResponse,
              addOnSetStateResponse
            )
        }
      }
    }
  }

  private def replicateVehicle(
      setStateRequest: SetStateRequest,
      savedItineraryModel: Option[SetStateItineraryModel]
  ): Future[Option[SetStateResponse]] = {
    val protectionModels = setStateRequest.protectionModels.getOrElse(Seq.empty)
    VehicleBookingState
      .fromSetStateRequest(setStateRequest, savedItineraryModel)
      .map { vehicleBookingState =>
        val multiProductInfos = mapToMultiProductInfoDBModels(setStateRequest.multiProductInfos)
        vehicleBookingService
          .saveAndReplicateVehicleBookingState(
            actionType = setStateRequest.actionType,
            actionId = setStateRequest.actionId,
            bookingType = setStateRequest.bookingType,
            vehicleBookingState = vehicleBookingState,
            protections = protectionModels,
            multiProductInfos = multiProductInfos
          )
          .map(updatedModel =>
            SetStateResponse.fromVehicleBookingState(
              setStateRequest.actionType,
              setStateRequest.actionId,
              setStateRequest.bookingType,
              updatedModel
            )
          )
      }
      .sequence
  }

  private def replicateActivity(
      setStateRequest: SetStateRequest,
      savedItineraryModel: Option[SetStateItineraryModel]
  ): Future[Option[SetStateResponse]] = {
    ActivityBookingState
      .fromSetStateRequest(setStateRequest, savedItineraryModel)
      .map { activityBookingState =>
        val multiProductInfos = mapToMultiProductInfoDBModels(setStateRequest.multiProductInfos)
        activityService
          .saveAndReplicateActivityBookingState(
            setStateRequest.actionType,
            setStateRequest.actionId,
            setStateRequest.bookingType,
            activityBookingState,
            multiProductInfos
          )
          .map(updatedModel =>
            SetStateResponse.fromActivityBookingState(
              setStateRequest.actionType,
              setStateRequest.actionId,
              setStateRequest.bookingType,
              updatedModel
            )
          )
      }
      .sequence
  }

  private def saveAndReplicateProperty(
      setStateRequest: SetStateRequest
  )(implicit context: RequestContext): Future[Option[SetStateResponse]] = {
    PropertyBookingStateWithItinerary
      .fromSetStateRequest(setStateRequest)
      .map { propertyBookingState =>
        {
          propertyService
            .saveAndReplicatePropertyBookingState(
              setStateRequest.actionType,
              setStateRequest.actionId,
              setStateRequest.bookingType,
              propertyBookingState
            )
            .map(updatedModel =>
              Some(
                SetStateResponse.fromPropertyBookingState(
                  setStateRequest.actionType,
                  setStateRequest.actionId,
                  setStateRequest.bookingType,
                  updatedModel
                )
              )
            )
        }
      }
      .getOrElse(Future.successful(None))
  }

  private def replicateCegFastTrack(
      setStateRequest: SetStateRequest,
      savedItineraryModel: Option[SetStateItineraryModel]
  ): Future[Option[SetStateResponse]] = {
    CegFastTrackBookingState
      .fromSetStateRequest(setStateRequest, savedItineraryModel)
      .map { bookingState =>
        val multiProductInfos = mapToMultiProductInfoDBModels(setStateRequest.multiProductInfos)
        cegFastTrackBookingService
          .saveAndReplicateCegFastTrackBookingState(
            setStateRequest.actionType,
            setStateRequest.actionId,
            setStateRequest.bookingType,
            bookingState,
            multiProductInfos
          )
          .map(updatedModel =>
            SetStateResponse.fromCegFastTrackBookingState(
              setStateRequest.actionType,
              setStateRequest.actionId,
              setStateRequest.bookingType,
              updatedModel
            )
          )
      }
      .sequence
  }

  private def replicateAddOn(
      setStateRequest: SetStateRequest,
      savedItineraryModel: Option[SetStateItineraryModel]
  ): Future[Option[SetStateResponse]] = {
    AddOnBookingState
      .fromSetStateRequest(setStateRequest, savedItineraryModel)
      .map { bookingState =>
        val multiProductInfos = mapToMultiProductInfoDBModels(setStateRequest.multiProductInfos)
        addOnStateService
          .saveAndReplicateAddOnBookingState(
            setStateRequest.actionType,
            setStateRequest.actionId,
            setStateRequest.bookingType,
            bookingState,
            multiProductInfos
          )
          .map(updatedModel =>
            SetStateResponse.fromAddonBookingState(
              setStateRequest.actionType,
              setStateRequest.actionId,
              setStateRequest.bookingType,
              updatedModel
            )
          )
      }
      .sequence
  }

  private def mapToMultiProductInfoDBModels(
      multiProductInfos: Option[Seq[MultiProductInfoSetState]]
  ): Seq[MultiProductInfoDBModel] =
    multiProductInfos
      .map(_.map(mp => MultiProductInfoDBModel(mp.multiProductId, MultiProductType(mp.multiProductType))))
      .getOrElse(Seq.empty)

  private def processPropertyBookingState(
      request: SetStateRequest
  )(implicit context: RequestContext): Future[Option[SetStateResponse]] =
    for {
      propertyItineraryModel <- setPropertyItineraryBookingStateWhenNoFlights(request)
      replicatePropertySetStateResponse <- saveAndReplicateProperty(
                                             request
                                           ) // For "BFDB data" save and replication only
      propertySetStateResponses <- setPropertyBookingStates(request)
    } yield mergePropertySetStateResponses(
      propertySetStateResponses,
      propertyItineraryModel,
      replicatePropertySetStateResponse
    )

  private def setPropertyItineraryBookingStateWhenNoFlights(
      request: SetStateRequest
  )(implicit requestContext: RequestContext): Future[Option[FlightModelInternal]] = {
    (request.properties, request.flights, request.vehicle, request.activities) match {
      case (Some(properties), flights, vehicle, activities)
          if properties.nonEmpty && flights.isEmpty && vehicle.forall(_.isEmpty) && activities.forall(_.isEmpty) =>
        flightBookingsService
          .setOnlyItineraryBookingState(request, isNewState = true)
          .map(Some(_))
      case _ => Future.successful(None)
    }
  }

  private def setPropertyBookingStates(request: SetStateRequest): Future[Seq[SetStateResponse]] =
    request.properties
      .map { propertyBookings =>
        Future.traverse(propertyBookings)(state => bookingsService.setPropertyBookingState(state))
      }
      .getOrElse(Future.successful(Seq.empty))

  private def mergePropertySetStateResponses(
      propertySetStateResponses: Seq[SetStateResponse],
      propertyItineraryModel: Option[FlightModelInternal],
      replicatePropertySetStateResponse: Option[SetStateResponse]
  ): Option[SetStateResponse] = {
    if (propertySetStateResponses.isEmpty)
      None
    else {
      val isSuccess = propertySetStateResponses.forall(_.success)
      val combinedErrorCode =
        if (isSuccess) None
        else
          Some(propertySetStateResponses.flatMap(_.errorCode).mkString(","))
      val combinedErrorMessage =
        if (isSuccess) None
        else
          Some(propertySetStateResponses.flatMap(_.errorMessage).mkString(","))

      Some(
        SetStateResponse(
          isSuccess,
          combinedErrorCode,
          combinedErrorMessage,
          bookingState = propertyItineraryModel,
          propertyBookings = replicatePropertySetStateResponse.flatMap(_.propertyBookings)
        )
      )
    }
  }

  private def processFlightBookingState(
      request: SetStateRequest,
      bookingHttpHeader: BookingAPIHttpHeader,
      ip: String
  )(implicit requestContext: RequestContext): Future[Option[SetStateResponse]] =
    request.flights.headOption
      .map(_ => flightBookingsService.setFlightBookingState(bookingHttpHeader, ip, request, true))
      .sequence

  private def handleSetBookingStateResponse(
      propertySetStateResponse: Option[SetStateResponse],
      flightSetStateResponse: Option[SetStateResponse],
      vehicleSetStateResponse: Option[SetStateResponse],
      activitySetStateResponse: Option[SetStateResponse],
      cegFastTrackSetStateResponse: Option[SetStateResponse],
      addOnSetStateResponse: Option[SetStateResponse]
  ): SetStateResponse = {

    val addOnResponses = addOnSetStateResponse
      .flatMap(_.addOnBookings)
      .getOrElse(Nil)
      .map(addOnBookingState =>
        Try(ProductType(addOnBookingState.productTypeId)).getOrElse(ProductType.Unknown) -> addOnSetStateResponse
      )
    val allSetStateResponses: Map[ProductType, Option[SetStateResponse]] = Map(
      ProductType.Hotel        -> propertySetStateResponse,
      ProductType.Flights      -> flightSetStateResponse,
      ProductType.Activity     -> activitySetStateResponse,
      ProductType.Cars         -> vehicleSetStateResponse,
      ProductType.CEGFastTrack -> cegFastTrackSetStateResponse
    ) ++ addOnResponses

    val setStateResponses: Map[ProductType, SetStateResponse] =
      allSetStateResponses.collect { case (productType, Some(setStateRes)) => (productType, setStateRes) }

    (
      propertySetStateResponse,
      flightSetStateResponse,
      vehicleSetStateResponse,
      activitySetStateResponse,
      cegFastTrackSetStateResponse,
      addOnSetStateResponse
    ) match {
      case (_, _, _, _, _, _) if setStateResponses.size > 1 =>
        handleMultiProductSetStateResponses(setStateResponses)
      case (Some(property), None, _, _, _, _)              => property
      case (None, Some(flight), _, _, _, _)                => flight
      case (None, None, Some(vehicle), _, _, _)            => vehicle
      case (None, None, None, Some(activity), _, _)        => activity
      case (None, None, None, None, Some(cegFastTrack), _) => cegFastTrack
      case (None, None, None, None, None, Some(addOn))     => addOn
      case (None, None, None, None, None, None) =>
        SetStateResponse.withError(
          ResponseErrorCode.InternalError,
          "Neither property, flight, vehicle, activity, priceFreeze nor cegFastTrack states are updated because they are not existed in the request"
        )
    }
  }

  private def handleMultiProductSetStateResponses(
      setStateResponses: Map[ProductType, SetStateResponse]
  ): SetStateResponse = {
    val (successResponses, failedResponses) = setStateResponses.partition(kv => kv._2.success)

    if (failedResponses.isEmpty) {
      aggregateAllSuccessSetStateResponse(successResponses)
    } else if (failedResponses.nonEmpty && successResponses.nonEmpty) {
      aggregatePartialSuccessSetStateResponse(successResponses, setStateResponses)
    } else {
      val (combinedErrorCodeOpt, combinedErrorMessageOpt) = combineSetStateResponseErrorCodeAndMessage(failedResponses)
      SetStateResponse(success = false, errorCode = combinedErrorCodeOpt, errorMessage = combinedErrorMessageOpt)
    }
  }

  private def getProductName(productType: ProductType): String = {
    productType match {
      case ProductType.Hotel    => "Properties"
      case ProductType.Flights  => "Flight"
      case ProductType.Cars     => "Vehicle"
      case ProductType.Activity => "Activity"
    }
  }

  private def aggregateAllSuccessSetStateResponse(
      successResponses: Map[ProductType, SetStateResponse]
  ): SetStateResponse = {
    // FlightInternalModel contains Itinerary data so need to map from other product when FlightSetStateRes is None
    val flightModelInternalOpt                          = extractFlightModelInternalFromSetStateResponse(successResponses)
    val (combinedErrorCodeOpt, combinedErrorMessageOpt) = combineSetStateResponseErrorCodeAndMessage(successResponses)
    val invalidRequestDetailSequence = successResponses.values.foldLeft(Seq.empty: Seq[InvalidRequestDetail]) {
      (invalidRequestDetailList, currentSetStateResponse) =>
        invalidRequestDetailList ++ currentSetStateResponse.invalidRequestData.getOrElse(Seq.empty)
    }
    val combinedInvalidRequestDetailOpt =
      if (invalidRequestDetailSequence.nonEmpty) Some(invalidRequestDetailSequence) else None

    SetStateResponse.combineProductResponses(
      successResponses.values.toSeq,
      SetStateResponse(
        success = true,
        errorCode = combinedErrorCodeOpt,
        errorMessage = combinedErrorMessageOpt,
        invalidRequestData = combinedInvalidRequestDetailOpt,
        bookingState = flightModelInternalOpt
      )
    )
  }

  private def aggregatePartialSuccessSetStateResponse(
      successResponses: Map[ProductType, SetStateResponse],
      allResponses: Map[ProductType, SetStateResponse]
  ): SetStateResponse = {
    // FlightInternalModel contains Itinerary data so need to map from other product when FlightSetStateRes is None
    val flightModelInternalOpt = extractFlightModelInternalFromSetStateResponse(successResponses)

    val (_, combinedErrorAndWarningMessageOpt) = combineSetStateResponseErrorCodeAndMessage(
      allResponses
    ) // did not use failedResponses for case that success response also contain warning msg
    val invalidRequestDetailSequence = successResponses.values.foldLeft(Seq.empty: Seq[InvalidRequestDetail]) {
      (invalidRequestDetailList, currentSetStateResponse) =>
        invalidRequestDetailList ++ currentSetStateResponse.invalidRequestData.getOrElse(Seq.empty)
    }
    val combinedInvalidRequestDetailOpt =
      if (invalidRequestDetailSequence.nonEmpty) Some(invalidRequestDetailSequence) else None

    SetStateResponse.combineProductResponses(
      successResponses.values.toSeq,
      SetStateResponse(
        success = true,
        errorCode = Some(WarnCode.PARTIAL_UPDATE.toString),
        errorMessage = combinedErrorAndWarningMessageOpt,
        invalidRequestData = combinedInvalidRequestDetailOpt,
        bookingState = flightModelInternalOpt
      )
    )
  }

  private def extractFlightModelInternalFromSetStateResponse(
      successResponses: Map[ProductType, SetStateResponse]
  ): Option[FlightModelInternal] = {
    val flightModelInternalFromFlight      = successResponses.get(ProductType.Flights).flatMap(_.bookingState)
    val flightModelInternalFromCar         = successResponses.get(ProductType.Cars).flatMap(_.bookingState)
    val flightModelInternalFromActivity    = successResponses.get(ProductType.Activity).flatMap(_.bookingState)
    val flightModelInternalFromHotel       = successResponses.get(ProductType.Hotel).flatMap(_.bookingState)
    val flightModelInternalFromPrizeFreeze = successResponses.get(ProductType.PriceFreeze).flatMap(_.bookingState)

    flightModelInternalFromFlight
      .orElse(flightModelInternalFromCar)
      .orElse(flightModelInternalFromActivity)
      .orElse(flightModelInternalFromHotel)
      .orElse(flightModelInternalFromPrizeFreeze)
  }

  private def combineSetStateResponseErrorCodeAndMessage(
      setStateResponses: Map[ProductType, SetStateResponse]
  ): (Option[String], Option[String]) = {
    val combinedErrorCodes = setStateResponses.map {
      case (productType, setStateRes) =>
        setStateRes.errorCode.map(errCode => getProductName(productType) + " ErrorCode: " + errCode)
    }
    val combinedErrorMessages = setStateResponses.map {
      case (productType, setStateRes) =>
        setStateRes.errorMessage.map(errMsg => getProductName(productType) + " ErrorMessage: " + errMsg)
    }
    val combinedErrorCode    = combinedErrorCodes.filter(_.nonEmpty).flatten.mkString(" & ")
    val combinedErrorMessage = combinedErrorMessages.filter(_.nonEmpty).flatten.mkString(" & ")

    val combinedErrorCodeOpt    = if (combinedErrorCode.isEmpty) None else Some(combinedErrorCode)
    val combinedErrorMessageOpt = if (combinedErrorMessage.isEmpty) None else Some(combinedErrorMessage)

    (combinedErrorCodeOpt, combinedErrorMessageOpt)
  }

  override def replicateFlightBookingStateByItineraryId(
      bookingHttpHeader: BookingAPIHttpHeader,
      ip: String,
      request: FlightBookingReplicateByItineraryIDRequest
  ): Future[FlightBookingReplicateByItineraryIDResponse] =
    withReqReslog[FlightBookingReplicateByItineraryIDRequest, FlightBookingReplicateByItineraryIDResponse](
      bookingHttpHeader,
      ip,
      request,
      s"${bookingHttpHeader.path} processed failed on ItineraryId's:${request.itineraryIds}"
    ) {
      requestValidator.validateFlightBookingReplicateByItineraryIDRequest(request) match {
        case Some(validation) => Future.successful(validation)
        case None =>
          flightBookingsService.replicateFlightBookingByItineraryId(bookingHttpHeader, ip, request)
      }
    }

  override def replicateNonPropertyBookingStateByItineraryId(
      bookingAPIHttpHeader: BookingAPIHttpHeader,
      ip: String,
      request: ReplicateByItineraryIdRequest
  ): Future[ReplicateByItineraryIdResponse] =
    requestContextBuilder.build(request, bookingAPIHttpHeader).flatMap { implicit context =>
      withReqReslog[ReplicateByItineraryIdRequest, ReplicateByItineraryIdResponse](
        bookingAPIHttpHeader,
        ip,
        request,
        s"POST ${bookingAPIHttpHeader.path} failed on ItineraryId's:${request.itineraryIds}"
      ) {
        (
          requestValidator.validateNonPropertyBookingReplicateByItineraryIdRequest(request),
          ReplicateItineraryProductType.byName(request.productType)
        ) match {
          case (Some(errorResponse: ReplicateByItineraryIdResponse), _) => Future.successful(errorResponse)
          case (None, ReplicateItineraryProductType.Flight) =>
            flightBookingsService
              .replicateFlightBookingByItineraryId(
                bookingAPIHttpHeader,
                ip,
                ReplicateByItineraryIdRequest.mapToFlightBookingReplicateByItineraryIDRequest(request)
              )
              .map(res => FlightBookingReplicateByItineraryIDResponse.mapToReplicateByItineraryIdResponse(res))
          case (None, ReplicateItineraryProductType.Activity) =>
            activityService.replicateBookingsByItineraryId(request)
          case (None, ReplicateItineraryProductType.Vehicle) =>
            vehicleBookingService.replicateBookingsByItineraryId(request)
          case (None, ReplicateItineraryProductType.CegFastTrack) =>
            cegFastTrackBookingService.replicateBookingsByItineraryId(request)
          case (None, ReplicateItineraryProductType.AddOn) => addOnStateService.replicateBookingsByItineraryId(request)
          case (None, _) =>
            Future.failed(new IllegalArgumentException("Unsupported product type"))
        }
      } recoverWith {
        case err: Throwable =>
          Future.successful(ReplicateByItineraryIdResponse.withError(ResponseErrorCode.TechnicalError, err.getMessage))
      }
    }

  private[handler] def getAdditionalTags(
      request: GetStatusRequest,
      response: Option[GetStatusResponse]
  ): Map[String, String] =
    response match {
      case Some(resp) =>
        val tagsMap: mutable.Map[String, Option[String]] = mutable.Map()
        resp.status match {
          case Some(status) => tagsMap += ("booking_status" -> Some(status.bookingStatus.toString))
          case None         => tagsMap += ("booking_status" -> None)
        }
        resp.itinerary match {
          case Some(itinerary) =>
            val productType  = Some(BookingRequestTypeResolver.getFlowType(itinerary).toString)
            val hotelStatus  = itinerary.hotels.headOption.map(hotel => hotel.bookingStatus.toString)
            val flightStatus = itinerary.flights.headOption.map(flight => flight.bookingStatus.toString)
            tagsMap ++= Map(
              "hotel_booking_status"  -> hotelStatus,
              "flight_booking_status" -> flightStatus,
              "product_type"          -> productType
            )
          case None => tagsMap += ("product_type" -> None)
        }
        tagsMap += ("device_type" -> Some(RequestBaseUtils.devicePlatform(request).toString))
        StatusToken.deserialize(request.statusToken, log, withMeasure) match {
          case Success(token) =>
            tagsMap ++= Map(
              "statusToken_dc" -> Some(token.dc),
              "is_same_dc"     -> Some(ServerUtils.isSameDC(token.dc).toString)
            )
          case Failure(_) =>
            tagsMap += ("statusToken_dc" -> None)
        }
        tagsMap.collect { case (tag, Some(value)) => (tag, value) }.toMap
      case _ => Map.empty
    }

}
