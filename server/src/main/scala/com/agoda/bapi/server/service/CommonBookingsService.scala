package com.agoda.bapi.server.service

import com.agoda.bapi.agent.common.schema.BookingActionMessageTopic.{BAM_Topic_Redirect_OTPRequired, BAM_Topic_Unknown}
import com.agoda.bapi.agent.common.schema.ProvisioningResult.{ProvisioningConfirmed, ProvisioningFailed, ProvisioningTimeout, ProvisioningUnconfirmed}
import com.agoda.bapi.agent.common.schema.{Gateway => _, Payment3DSResponse => _, PreAuthResult => PreAuthResultProto, TokenConfiguration => BAPITokenConfiguration, _}
import com.agoda.bapi.common.{MessageService, ToolSet}
import com.agoda.bapi.common.config.{Configuration, KillSwitches}
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.localization.CmsContextFactory
import com.agoda.bapi.common.message.creation.CreatedBookingStatus.CreatedBookingStatus
import com.agoda.bapi.common.message.creation.InstantBookingStatus.InstantBookingStatus
import com.agoda.bapi.common.message.creation.extensions.MFAResult
import com.agoda.bapi.common.message.creation.{CrossSellResult => BAPICrossSellResult, LocalScriptType, PaymentReason, _}
import com.agoda.bapi.common.model.WhiteLabel.{WhiteLabel, isAgodaWhitelabelId}
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.common.model.creation.{mapper => Mapper}
import com.agoda.bapi.common.model.payment.{PaymentContinuation, PaymentToken, PaymentTokenType}
import com.agoda.bapi.common.model.product.ProductTypeEnum
import com.agoda.bapi.common.model.protection.ProtectionBookingState
import com.agoda.bapi.common.model.protection.ProtectionBookingState.ProtectionBookingState
import com.agoda.bapi.common.model.swipe.PaymentRedirect
import com.agoda.bapi.common.util.{CommonWhitelabelUtils, WFAESEncryption}
import com.agoda.bapi.creation.model.db.BookingActionState
import com.agoda.bapi.creation.repository.{EbeLiteBookingRepository, ProtectionBookingRepository, WorkflowRepository}
import com.agoda.bapi.creation.service._
import com.agoda.bapi.creation.service.processing.{ActivityInfoService, AddOnsInProcessingService}
import com.agoda.bapi.execution.context.NonBlockingExecutionContext.default
import com.agoda.bapi.server.reporting.BookingActionMessageConsumeEvent
import com.agoda.bapi.server.utils.CommonUtils
import com.agoda.bapi.common.MessageService
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.creation.util.WhitelabelUtils
import com.agoda.bapi.server.service.payment.const.PaymentReasonGroup
import com.agoda.bapi.server.utils.CommonUtils
import com.agoda.bapi.server.service.payment.const.PaymentReasonGroup
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpb.common.serialization.Serialization
import com.agoda.mpbe.state.common.enums.Gateway.Gateway
import com.google.inject.Inject
import org.apache.commons.lang3.StringUtils
import org.joda.time.DateTime
import scalapb.json4s.JsonFormat

import javax.inject.Named
import scala.concurrent.Future
import scala.util.{Failure, Success, Try}

trait CommonBookingsService {
  def getItineraryStatus(
      getStatusRequest: GetStatusRequest,
      bookingActions: Seq[BookingWorkflowAction]
  )(implicit context: RequestContext): Future[GetStatusResponse]

  def getBookingActionsByItineraryId(itineraryId: Long): Future[Seq[BookingWorkflowAction]]
}

object CommonBookingsService {}

/* Responsible for the booking related business logic. */
class CommonBookingsServiceImpl @Inject() (
    ebeLiteBookingRepository: EbeLiteBookingRepository,
    workflowRepository: WorkflowRepository,
    protectionRepository: ProtectionBookingRepository,
    @Named("FlightInfoServiceV1") flightInfoService: FlightInfoService,
    @Named("FlightInfoServiceV2") flightInfoServiceV2: FlightInfoService,
    activityInfoService: ActivityInfoService,
    vehicleInfoService: VehicleInfoService,
    urlService: UrlService,
    config: Configuration,
    cmsContextFactory: CmsContextFactory,
    bookingActionMessageService: BookingActionMessageService,
    hadoopMessagingService: MessageService,
    addOnInfoService: AddOnsInProcessingService,
    statusTokenService: StatusTokenService,
    killSwitches: KillSwitches
) extends CommonBookingsService
    with ToolSet {

  val wfAESEncryption = new WFAESEncryption(config.autoLoginTokenConfig.salt)

  override def getItineraryStatus(
      getStatusRequest: GetStatusRequest,
      bookingActionsFromParam: Seq[BookingWorkflowAction]
  )(implicit context: RequestContext): Future[GetStatusResponse] = {
    val statusToken = getStatusRequest.statusToken
    val selectedFlightInfoService =
      if (context.featureAware.exists(_.enableGetStatusDataFromBkgDb))
        flightInfoServiceV2
      else
        flightInfoService

    for {
      itineraryToken          <- statusTokenService.materializeToken(statusToken)
      itineraryId              = itineraryToken.itineraryId
      actionId                 = itineraryToken.actionId
      operationType            = itineraryToken.operationType
      isStatusWithRetryPayment = context.featureAware.exists(_.enableRetryPaymentOnStatusEndpoint)
      isMemberIdEnabled =
        context.featureAware.exists(_.enableMemberId) && WhitelabelUtils.enableMemberIdForBookingCreation(
          context.whiteLabelInfo
        )
      productType: Set[ProductTypeEnum.Value] =
        itineraryToken.productType.flatMap(productName => Try(ProductTypeEnum.withName(productName)).toOption)
      createWhiteLabelId =
        itineraryToken.whitelabelId.map(whitelableId => WhiteLabel(whitelableId)).getOrElse(WhiteLabel.Agoda)
      allBookingActions <-
        if (bookingActionsFromParam.nonEmpty)
          Future.successful(bookingActionsFromParam)
        else
          workflowRepository.getBookingActionByItineraryId(itineraryId)
      bookingActions = itineraryToken.operationId match {
                         case Some(itineraryTokenOperationId) if killSwitches.enableRebookAndCancelFlow =>
                           CommonUtils.getBookingActionsForOperation(allBookingActions, itineraryTokenOperationId)
                         case _ => allBookingActions
                       }

      masterBookingActionRecord = CommonUtils.getMasterBookingAction(bookingActions)

      masterBookingActionMessage <- if (masterBookingActionRecord.nonEmpty)
                                      bookingActionMessageService
                                        .getContinueBookingActionMessageWithTopic(
                                          masterBookingActionRecord.get.actionId,
                                          BookingActionMessageTopic.BAM_Topic_AutoLoginToken
                                        )
                                    else Future.successful(None)
      loginToken = getLoginTokenAndRemoveIfExists(masterBookingActionMessage)
      masterBookingActionState =
        if (isStatusWithRetryPayment || isMemberIdEnabled)
          masterBookingActionRecord.flatMap(e => deserializeBookingActionState(e))
        else None
      isPartialSuccessAllowed =
        if (isStatusWithRetryPayment)
          getIsPartialSuccessAllowedFromState(masterBookingActionState)
        else masterBookingActionRecord.flatMap(bookingAction => getIsPartialSuccessAllowed(bookingAction))
      isRetryPaymentAllowed = if (isStatusWithRetryPayment) getIsRetryAllowed(masterBookingActionState) else None
      memberId              = if (isMemberIdEnabled) getMemberId(masterBookingActionState) else 0

      propertyBookingStatus <- if (productType(ProductTypeEnum.Property))
                                 getPropertyBookingStatus(itineraryId.toInt, bookingActions)
                               else Future.successful(Seq.empty)
      flightsBookingStatus <- if (productType(ProductTypeEnum.Flight))
                                selectedFlightInfoService.getFlightsItineraryStatus(itineraryId)
                              else Future.successful(Seq.empty)
      protectionBookingStatus <- if (productType(ProductTypeEnum.Protection))
                                   getProtectionBookingStatus(
                                     itineraryId,
                                     context.featureAware.exists(_.enableGetStatusDataFromBkgDb)
                                   )
                                 else Future.successful(Seq.empty)
      activityBookingStatus <- if (productType(ProductTypeEnum.Activity))
                                 activityInfoService.getActivityItineraryStatus(
                                   itineraryId,
                                   useBkgDb = context.featureAware.exists(_.enableGetStatusDataFromBkgDb)
                                 )
                               else Future.successful(Seq.empty)
      vehicleBookingStatus <- if (productType(ProductTypeEnum.Car))
                                vehicleInfoService.getVehicleItineraryStatus(
                                  itineraryId = itineraryId,
                                  operationType = operationType,
                                  useBkgDb = context.featureAware.exists(_.enableGetStatusDataFromBkgDb)
                                )
                              else Future.successful(Seq.empty)
      bookingActionMessage <- bookingActionMessageService.getContinueBookingActionMessage(actionId)
      allProductsStatuses   = getAllProductsStatuses(propertyBookingStatus, flightsBookingStatus, activityBookingStatus)
      addOnStatuses <- addOnInfoService
                         .getGenericAddonStatuses(
                           itineraryId = itineraryId,
                           useBkgDb = context.featureAware.exists(_.enableGetStatusDataFromBkgDb)
                         )
                         .recover {
                           // still experimental, but tested - purpose not to do it under experiment to avoid dilution
                           case exception =>
                             logger.error(s"Unable to fetch add-on info for ${itineraryId}", exception)
                             Seq.empty
                         }
      itineraryPaymentResult <- getItineraryPaymentResult(
                                  allProductsStatuses,
                                  actionId,
                                  itineraryToken.topic,
                                  bookingActionMessage,
                                  itineraryId
                                )
      itineraryPartnerPaymentResult <- getItineraryPartnerPaymentResult(
                                         allProductsStatuses,
                                         actionId,
                                         itineraryToken.topic,
                                         bookingActionMessage
                                       )
      mfaResult = getMfaResult(bookingActionMessage)
      updatedStatusToken = getUpdatedStatusToken(
                             itineraryToken,
                             itineraryPaymentResult.flatMap(_.topic),
                             itineraryPartnerPaymentResult.flatMap(_.topic),
                             mfaResult
                           )

    } yield
      if (
        propertyBookingStatus.isEmpty && flightsBookingStatus.isEmpty && vehicleBookingStatus.isEmpty && activityBookingStatus.isEmpty
      )
        GetStatusResponse(
          success = false,
          errorMessage = Some(
            s"There's no booking status of products: ${productType.mkString(", ")} for itinerary id: $itineraryId"
          ),
          bookingContext = getBookingContext
        )
      else if (validateJtbWhitelabelId(createWhiteLabelId, context.whiteLabelInfo.whiteLabelId))
        GetStatusResponse(
          success = false,
          errorMessage = Some(
            s"whitelabelId in create is $createWhiteLabelId but in get status is ${context.whiteLabelInfo.whiteLabelId}"
          )
        )
      else
        GetStatusResponse(
          success = true,
          itinerary = Some(
            Itinerary(
              itineraryId = itineraryId,
              bookings = propertyBookingStatus,
              flights = flightsBookingStatus,
              cars = vehicleBookingStatus,
              protections = protectionBookingStatus,
              activities = activityBookingStatus,
              statusToken = updatedStatusToken,
              addOns = addOnStatuses
            )
          ),
          paymentResult = itineraryPaymentResult,
          partnerPaymentResult = itineraryPartnerPaymentResult,
          manualFraudSubmitUrl = getFraudResultURL(bookingActionMessage),
          bookingContext = getBookingContext,
          crossSellResult = getCrossSellResult(bookingActionMessage),
          borBookingResult = getBorBookingResult(bookingActionMessage),
          mfaResult = mfaResult,
          isPartialSuccessAllowed = isPartialSuccessAllowed,
          loginToken = loginToken,
          isRetryPaymentAllowed = isRetryPaymentAllowed,
          memberId = memberId
        )
  }

  override def getBookingActionsByItineraryId(itineraryId: Long): Future[Seq[BookingWorkflowAction]] = {
    workflowRepository.getBookingActionByItineraryId(itineraryId).transformWith {
      case Success(value) => Future.successful(value)
      case Failure(exception) =>
        logger.warn(
          "Booking actions cannot be retrieved from database",
          exception
        )
        Future.successful(Seq.empty)
    }
  }

  private[service] def getPropertyBookingStatus(
      itineraryId: Int,
      bookingActions: Seq[BookingWorkflowAction]
  )(implicit context: RequestContext): Future[Seq[HotelBooking]] = {
    val hotelBookingsFuture =
      for {
        ebeLiteBooking <- ebeLiteBookingRepository.getEbeLiteBooking(itineraryId)
        getSelfServiceUrl <-
          urlService.getSelfServiceURLs(
            context.whiteLabelInfo.whiteLabelId,
            isDomainIncluded = false
          )
      } yield ebeLiteBooking.map { booking =>
        val masterBookingAction    = CommonUtils.getMasterBookingAction(bookingActions)
        val masterRejectReasonCode = masterBookingAction.flatMap(getRejectReason)
        val propertyBookingId      = booking.bookingId

        val propertyBookingActions = CommonUtils.getPropertyBookingActions(bookingActions)
        val propertyBookingAction =
          propertyBookingActions.find(bookingAction => bookingAction.bookingId.contains(propertyBookingId))
        val propertyRejectReasonCode = propertyBookingAction.flatMap(getRejectReason)
        val propertyProductKey       = propertyBookingAction.flatMap(getProductKey)

        val bookingStatus = booking.stateId
          .map(CreatedBookingStatus(_))
          .getOrElse(CreatedBookingStatus.TechnicalError)

        val rejectReason = rejectReasonCodeResolver(
          bookingStatus.id,
          masterRejectReasonCode,
          propertyRejectReasonCode
        )

        HotelBooking(
          itineraryId = itineraryId,
          bookingId = propertyBookingId,
          productKey = propertyProductKey,
          bookingStatus = bookingStatus,
          rejectReason = rejectReason,
          selfServiceUrl = getSelfServiceUrl(propertyBookingId),
          isCurrentOperation = Some(propertyBookingAction.isDefined)
        )
      }

    hotelBookingsFuture.flatMap(hotelBookings =>
      Future.sequence {
        hotelBookings.map(hotelBooking => {
          val hotelBookingActions = bookingActions.find(booking => booking.bookingId.contains(hotelBooking.bookingId))
          for {
            provisioningResultMessage <-
              getProvisioningResultMessages(hotelBookingActions.map(_.actionId))
          } yield hotelBooking.copy(
            externalBookingId = mapExternalBookingId(hotelBooking.bookingId, provisioningResultMessage),
            instantBookingStatus = mapInstantBookingStatus(provisioningResultMessage),
            supplierResponse = mapSupplierResponse(provisioningResultMessage)
          )
        })
      }
    )
  }

  private def getBookingContext(implicit context: RequestContext): Option[BookingContext] =
    context.bookingCreationContext.map(bcc => BookingContext(bookingSessionId = bcc.bookingSessionId))

  private def getProvisioningResultMessages(
      actionIdOpt: Option[ActionId]
  )(implicit context: RequestContext): Future[Option[BookingActionMessage]] = {
    actionIdOpt match {
      case Some(actionId) =>
        val topic = context.whiteLabelInfo match {
          case wlInfo if CommonWhitelabelUtils.isJtbWl(wlInfo) =>
            BookingActionMessageTopic.BAM_Topic_JTBPartnerProvisioningResult
          case _ => BookingActionMessageTopic.BAM_Topic_ProvisioningResult
        }
        bookingActionMessageService.getContinueBookingActionMessageWithTopic(
          actionId,
          topic
        )
      case _ => Future.successful(None)
    }
  }

  private[service] def mapExternalBookingId(
      bookingId: BookingId,
      provisioningResultMessage: Option[BookingActionMessage]
  ): Option[String] = {
    for {
      content           <- provisioningResultMessage.map(_.content)
      actionMessage     <- getActionMessageFromJsonString(content)
      externalBookingId <- actionMessage.provisioningData.flatMap(_.bookingExternalReference.get(bookingId))
    } yield externalBookingId
  }

  private[service] def mapInstantBookingStatus(
      provisioningResultMessage: Option[BookingActionMessage]
  ): Option[InstantBookingStatus] = {
    for {
      content       <- provisioningResultMessage.map(_.content)
      actionMessage <- getActionMessageFromJsonString(content)
      topic          = provisioningResultMessage.map(_.topic)
      provisioningResult = topic match {
                             case Some(BookingActionMessageTopic.BAM_Topic_JTBPartnerProvisioningResult.value) =>
                               actionMessage.jtbPartnerProvisioningResult
                                 .map(_.result)
                                 .getOrElse(ProvisioningUnconfirmed)
                             case _ => actionMessage.provisioningResult
                           }
    } yield provisioningResult match {
      case ProvisioningConfirmed => InstantBookingStatus.Confirmed
      case ProvisioningTimeout   => InstantBookingStatus.Timeout
      case ProvisioningFailed    => InstantBookingStatus.Failed
      case _                     => InstantBookingStatus.UnConfirmed
    }
  }

  private def getProtectionBookingStatus(
      itineraryId: Long,
      useBkgDb: Boolean
  )(implicit context: RequestContext): Future[Seq[ProtectionBooking]] = {
    val data =
      if (useBkgDb) protectionRepository.getTripProtectionBookingInfoForItineraryV2(itineraryId)
      else protectionRepository.getTripProtectionBookingInfoForItinerary(itineraryId)

    data
      .map { protectionBooking =>
        protectionBooking.map { booking =>
          ProtectionBooking(
            bookingId = booking.protectionBookingId.toInt,
            bookingStatus = getBookingStatusFromProtectionState(booking.protectionBookingStateId),
            supplierPolicyId = booking.supplierPolicyId,
            protectionTypeId = booking.protectionTypeId
          )
        }
      }
      .recoverWith {
        case exception: Exception =>
          logger.error(s"Unable to fetch protection info for ${itineraryId}", exception)
          Future.successful(Seq.empty)
      }
  }

  private def getCrossSellResult(bookingActionMessage: Option[BookingActionMessage]): Option[BAPICrossSellResult] = {
    bookingActionMessage.map(_.topic).filter(_ == BookingActionMessageTopic.BAM_Topic_CrossSell.value).flatMap { _ =>
      val actionMessage = bookingActionMessage.flatMap(message => getActionMessageFromJsonString(message.content))
      actionMessage.map(msg => BAPICrossSellResult(msg.crossSellResult.map(_.reasonId)))
    }
  }

  private def getBorBookingResult(bookingActionMessage: Option[BookingActionMessage]): Option[BorBookingResult] = {
    bookingActionMessage.map(_.topic).filter(_ == BookingActionMessageTopic.BAM_Topic_BOR.value).flatMap { _ =>
      val actionMessage = bookingActionMessage.flatMap(message => getActionMessageFromJsonString(message.content))
      actionMessage.map(msg => BorBookingResult(msg.borBooking.map(_.status)))
    }
  }

  private def getMfaResult(bookingActionMessage: Option[BookingActionMessage]): Option[MFAResult] = {
    bookingActionMessage.map(_.topic).filter(_ == BookingActionMessageTopic.BAM_Topic_FraudOTPRequired.value).flatMap {
      _ =>
        val actionMessage = bookingActionMessage.flatMap(message => getActionMessageFromJsonString(message.content))
        actionMessage.map(msg => MFAResult(redirectUrl = msg.requiredMfaOTP.map(_.redirectUrl)))
    }
  }

  private def getAllProductsStatuses(
      propertyBookings: Seq[HotelBooking],
      flightBookings: Seq[FlightBooking],
      activityBookings: Seq[ActivityBooking]
  ): Seq[CreatedBookingStatus] = {
    val propertyBookingsStatus = propertyBookings.map(_.bookingStatus)
    val flightBookingsStatus   = flightBookings.map(_.bookingStatus)
    val activityBookingsStatus = activityBookings.map(_.bookingStatus)
    propertyBookingsStatus ++ flightBookingsStatus ++ activityBookingsStatus
  }

  private def getItineraryPartnerPaymentResult(
      allProductsStatuses: Seq[CreatedBookingStatus],
      actionId: ActionId,
      topic: Option[Int],
      bookingActionMessage: Option[BookingActionMessage]
  )(implicit context: RequestContext): Future[Option[PartnerPaymentResult]] = {
    if (allProductsStatuses.nonEmpty && allProductsStatuses.forall(_ == CreatedBookingStatus.BookingProcessing))
      bookingActionMessageService.getContinueBookingActionMessageWithTopic(
        actionId,
        BookingActionMessageTopic.fromValue(topic.getOrElse(0))
      ) flatMap { prevBookingActionMessage =>
        val continueFeedbackOpt = prevBookingActionMessage
          .flatMap(message => getActionMessageFromJsonString(message.content))
          .flatMap(_.continueFeedback)

        continueFeedbackOpt match {
          case Some(continueFeedback) => getPartnerContinueFeedbackResult(topic, continueFeedback)
          case None                   => getContinuePartnerPaymentResult(bookingActionMessage)
        }
      }
    else
      Future.successful(None)
  }

  private[service] def getItineraryPaymentResult(
      allProductsStatuses: Seq[CreatedBookingStatus],
      actionId: ActionId,
      topic: Option[Int],
      bookingActionMessage: Option[BookingActionMessage],
      itineraryId: ItineraryId
  )(implicit context: RequestContext): Future[Option[PaymentResult]] = {

    val topicId                              = bookingActionMessage.map(_.topic)
    val actionMessage                        = bookingActionMessage.flatMap(message => getActionMessageFromJsonString(message.content))
    val preAuthResult: Option[PaymentResult] = getPreAuthResult(actionMessage.flatMap(_.preAuthResult))
    val publishBamTopicPreAuthResult: Boolean =
      isInsufficientFundGroup(preAuthResult) &&
        isAgodaWhitelabelId(context.whiteLabelInfo.whiteLabelId) &&
        context.featureAware.exists(_.publishBamTopicPreAuthResult)

    if (
      allProductsStatuses.nonEmpty && allProductsStatuses.forall(_ == CreatedBookingStatus.BookingProcessing)
    ) // all bookings are processing
      bookingActionMessageService.getContinueBookingActionMessageWithTopic(
        actionId,
        BookingActionMessageTopic.fromValue(topic.getOrElse(0))
      ) flatMap { prevBookingActionMessage =>
        val continueFeedbackOpt = prevBookingActionMessage
          .flatMap(message => getActionMessageFromJsonString(message.content))
          .flatMap(_.continueFeedback)

        continueFeedbackOpt match {
          case Some(continueFeedback) => getContinueFeedbackResult(topic, continueFeedback)
          case None =>
            getContinuePaymentResult(
              actionMessage,
              topicId,
              itineraryId,
              preAuthResult.filter(_ => publishBamTopicPreAuthResult)
            )
        }
      }
    else if (
      topicId.contains(BookingActionMessageTopic.BAM_Topic_PreAuthResult.value) && publishBamTopicPreAuthResult
    ) {
      logger.info(s"[PX-180-BCRE] [$itineraryId] Not all products are processing ${allProductsStatuses.mkString(", ")}")
      Future.successful(preAuthResult.map(r => r.copy(topic = topicId)))
    } else
      Future.successful(None)
  }

  private def getUpdatedStatusToken(
      itineraryToken: StatusToken,
      paymentResultTopic: Option[Int],
      partnerPaymentResultTopic: Option[Int],
      mfaResult: Option[MFAResult]
  ): String = {
    ((itineraryToken.topic, paymentResultTopic, partnerPaymentResultTopic, mfaResult) match {
      case (_, _, Some(partnerPaymentResultTopic), _) =>
        sendBamPaymentConsumeEventLogging(
          itineraryToken.topic.getOrElse(partnerPaymentResultTopic),
          partnerPaymentResultTopic,
          Some(itineraryToken.actionId)
        )
        itineraryToken.copy(topic = Some(partnerPaymentResultTopic))
      case (_, Some(paymentTopic), _, _) =>
        sendBamPaymentConsumeEventLogging(
          itineraryToken.topic.getOrElse(paymentTopic),
          paymentTopic,
          Some(itineraryToken.actionId)
        )
        itineraryToken.copy(topic = Some(paymentTopic))
      case (_, _, _, Some(mfaResult)) if mfaResult.redirectUrl.isDefined =>
        sendBamPaymentConsumeEventLogging(
          itineraryToken.topic.getOrElse(BookingActionMessageTopic.BAM_Topic_FraudOTPRequired.value),
          BookingActionMessageTopic.BAM_Topic_FraudOTPRequired.value,
          Some(itineraryToken.actionId)
        )
        itineraryToken.copy(topic = Some(BookingActionMessageTopic.BAM_Topic_FraudOTPRequired.value))
      case (Some(topic), None, None, None)
          if !Set(
            BookingActionMessageTopic.BAM_Topic_ManualFraud.value,
            BookingActionMessageTopic.BAM_Topic_CrossSell.value,
            BookingActionMessageTopic.BAM_Topic_BOR.value,
            BookingActionMessageTopic.BAM_Topic_FraudOTPRequired.value
          ).contains(topic) =>
        itineraryToken.copy(topic = Some(BookingActionMessageTopic.BAM_Topic_Unknown.value))
      case _ => itineraryToken
    }).serialize(log, withMeasure)
  }

  private def getContinuePaymentResult(
      actionMessage: Option[AgentBookingActionMessage],
      topicId: Option[Int],
      itineraryId: ItineraryId,
      preAuthResult: Option[PaymentResult] = None
  ): Future[Option[PaymentResult]] = {

    topicId match {
      case Some(topic) =>
        val result = topic match {
          case BookingActionMessageTopic.BAM_Topic_Redirect.value =>
            getRedirectPaymentResult(actionMessage.flatMap(_.redirect))
          case BookingActionMessageTopic.BAM_Topic_PreAuth3dsRequired.value |
              BookingActionMessageTopic.BAM_Topic_AwaitJTB3DS.value =>
            get3dsPaymentResult(actionMessage.flatMap(_.preAuth3DsRequired))
          case BookingActionMessageTopic.BAM_Topic_PreAuth3ds2Required.value =>
            get3ds2PaymentResult(actionMessage.flatMap(_.preAuth3Ds2Required))
          case BookingActionMessageTopic.BAM_Topic_PreAuthCvcRequired.value =>
            getRequireCvvPaymentResult(cvvRequired = true)
          case BookingActionMessageTopic.BAM_Topic_RetryPayment.value =>
            getRetryPaymentReadyResult(actionMessage.flatMap(_.retryFeedback))
          case BookingActionMessageTopic.BAM_Topic_AwaitPaymentToken.value =>
            getClientSideJavaScriptPaymentResult(actionMessage.flatMap(_.clientSideJavaScriptPaymentResult))
          case BookingActionMessageTopic.BAM_Topic_PreAuthResult.value if preAuthResult.isDefined =>
            logger.info(s"[PX-180-BCRE] [$itineraryId] all products are processing")
            preAuthResult
          case _ => None
        }
        Future.successful(result.map(r => r.copy(topic = topicId)))
      case _ => Future.successful(None)
    }
  }

  private def getContinuePartnerPaymentResult(
      bookingActionMessage: Option[BookingActionMessage]
  ): Future[Option[PartnerPaymentResult]] = {

    val topicId = bookingActionMessage.map(_.topic)
    topicId match {
      case Some(topic) =>
        val actionMessage = bookingActionMessage.flatMap(message => getActionMessageFromJsonString(message.content))
        val result = topic match {
          case BookingActionMessageTopic.BAM_Topic_Redirect_OTPRequired.value =>
            getRedirectOtpRequiredPaymentResult(actionMessage.flatMap(_.redirectOtpRequired))
          case _ => None
        }
        Future.successful(result.map(r => r.copy(topic = topicId)))
      case _ => Future.successful(None)
    }
  }

  private def getRedirectOtpRequiredPaymentResult(
      redirectOtpRequiredOpt: Option[RedirectOTPRequired]
  ): Option[PartnerPaymentResult] =
    redirectOtpRequiredOpt.map { redirectOtpRequired =>
      PartnerPaymentResult(
        redirectPayment = Some(
          PaymentRedirect(
            url = redirectOtpRequired.postFields.getOrElse("url", ""),
            postFields = Some(redirectOtpRequired.postFields.-("url")),
            additionalFields = None
          )
        )
      )
    }

  private def getClientSideJavaScriptPaymentResult(
      clientSidePaymentResult: Option[ClientSideJavaScriptPaymentResult]
  ): Option[PaymentResult] = {
    val paymentResult = for {
      clientSide <- clientSidePaymentResult
    } yield {
      val gatewayId      = Gateway.fromValue(clientSide.getContinuation.gatewayId.value)
      val tokenConfig    = clientSide.getTokenConfiguration
      val tokenUrl       = tokenConfig.externalScriptUrl
      val tokenParameter = tokenConfig.tokenParameters
      PaymentResult(
        continuation = PaymentContinuation(
          gatewayId = gatewayId,
          gatewayInfoId = Some(0)
        ),
        redirect3ds = None,
        redirectPayment = None,
        clientSideJavascriptPayment = Some(
          buildPaymentClientSideJavascriptResponse(gatewayId, tokenUrl, Some(tokenParameter))
        )
      )
    }
    paymentResult
  }

  private def buildPaymentClientSideJavascriptResponse(
      gatewayId: Gateway,
      tokenUrl: String,
      tokenParameters: Option[Map[String, String]]
  ): PaymentClientSideJavascriptResponse = {
    PaymentClientSideJavascriptResponse(
      TokenConfiguration(
        externalScriptUrl = tokenUrl,
        localScriptType = gatewayId match {
          case Gateway.GMO      => LocalScriptType.GMO
          case Gateway.SoftBank => LocalScriptType.SoftBank
          case _                => LocalScriptType.GMO // TODO: Figure out what to pass
        },
        // remap fields for GMO in workflow-agent
        tokenParameters = tokenParameters
      )
    )
  }

  private def getContinueFeedbackResult(
      topicId: Option[Int],
      continueFeedback: ContinueFeedback
  ): Future[Option[PaymentResult]] = {
    val paymentResult = PaymentResult(
      continuation = PaymentContinuation(Gateway.None),
      redirect3ds = None,
      topic = topicId,
      thirdPartyStatus = Some(ThirdPartyStatus(continueFeedback.thirdPartyStatus))
    )

    Future.successful(Some(paymentResult))
  }

  private def getPartnerContinueFeedbackResult(
      topicId: Option[Int],
      continueFeedback: ContinueFeedback
  ): Future[Option[PartnerPaymentResult]] =
    topicId.getOrElse(BAM_Topic_Unknown) match {
      case BAM_Topic_Redirect_OTPRequired.value =>
        val partnerPaymentResult = PartnerPaymentResult(
          redirectPayment = None,
          topic = topicId,
          thirdPartyStatus = Some(ThirdPartyStatus(continueFeedback.thirdPartyStatus))
        )
        Future.successful(Some(partnerPaymentResult))
      case _ => Future.successful(None)
    }

  private def getFraudResultURL(bookingActionMessage: Option[BookingActionMessage]): Option[String] = {
    val topicId = bookingActionMessage.map(_.topic)
    topicId match {
      case Some(BookingActionMessageTopic.BAM_Topic_ManualFraud.value) =>
        val actionMessage  = bookingActionMessage.flatMap(message => getActionMessageFromJsonString(message.content))
        val manualFraudURL = actionMessage.map(_.manualFraudUrl)
        manualFraudURL
      case _ => None
    }
  }

  private[service] def getLoginTokenAndRemoveIfExists(
      bookingActionMessageOpt: Option[BookingActionMessage]
  ): Option[String] = {
    val loginToken = bookingActionMessageOpt.flatMap { bookingActionMessage =>
      try {
        val autoLoginToken =
          Serialization.fromJson[AgentBookingActionMessage](bookingActionMessage.content).autoLoginToken
        autoLoginToken
          .map(_.token)
          .flatMap(token =>
            if (StringUtils.isNotBlank(token)) {
              bookingActionMessage.messageId.flatMap(actionMessageId =>
                Some(wfAESEncryption.decrypt(config.autoLoginTokenConfig.secretKey, token)).map(decryptedToken => {
                  workflowRepository.updateBookingActionMessage(
                    actionMessageId,
                    JsonFormat.toJsonString(
                      AgentBookingActionMessage(autoLoginToken =
                        Some(AutoLoginToken(token = "", consumedWhen = Some(DateTime.now())))
                      )
                    )
                  )
                  sendBamConsumeEventLogging(
                    messageId = Some(actionMessageId),
                    actionId = Some(bookingActionMessage.actionId),
                    topicId = BookingActionMessageTopic.BAM_Topic_AutoLoginToken.value
                  )
                  decryptedToken
                })
              )
            } else {
              None
            }
          )
      } catch {
        case ex: Exception =>
          logger.error(s"Unable to map booking action state to JsonNode", ex)
          None
      }
    }
    loginToken
  }

  private[service] def getIsPartialSuccessAllowed(
      masterBookingAction: BookingWorkflowAction
  ): Option[Boolean] = {
    try {
      val bookingActionState: BookingActionState =
        Serialization.fromJson[BookingActionState](masterBookingAction.state)
      bookingActionState.request.isPartialSuccessAllowed
    } catch {
      case exception: Throwable =>
        logger.error(s"Unable to parse booking action state to get isPartialSuccessAllowed", exception)
        None
    }
  }

  private[service] def getIsPartialSuccessAllowedFromState(
      bookingActionState: Option[BookingActionState]
  ): Option[Boolean] = {
    bookingActionState match {
      case Some(state) => state.request.isPartialSuccessAllowed
      case None        => None
    }
  }

  private[service] def getIsRetryAllowed(bookingActionState: Option[BookingActionState]): Option[Boolean] = {
    bookingActionState match {
      case Some(state) => state.request.allowRetryPayment
      case None        => None
    }
  }

  private[service] def getMemberId(bookingWorkflowAction: Option[BookingActionState]): Int =
    bookingWorkflowAction.map(_.customer.memberId).getOrElse(0)

  private[service] def deserializeBookingActionState(
      masterBookingAction: BookingWorkflowAction
  ): Option[BookingActionState] = {
    try {
      val bookingState = Serialization.fromJson[BookingActionState](masterBookingAction.state)
      Some(bookingState)
    } catch {
      case exception: Throwable =>
        logger.error(s"Unable to parse booking action state for action ${masterBookingAction.actionId}", exception)
        None
    }
  }

  private[service] def getRejectReason(bookingAction: BookingWorkflowAction): Option[Int] = {
    Try(Mapper.readTree(bookingAction.state)).toEither match {
      case Right(stateNode) =>
        val rejectRootCauseNode = stateNode.get("rejectRootCause")
        if (rejectRootCauseNode == null || rejectRootCauseNode.isNull || !rejectRootCauseNode.isInt) None
        else Some(rejectRootCauseNode.asInt())
      case exception =>
        logger.error(s"Unable to parse booking action state to JsonNode", exception)
        None
    }
  }

  private[service] def getProductKey(bookingAction: BookingWorkflowAction): Option[String] = {
    try {
      val bookingActionState: BookingActionState =
        Serialization.fromJson[BookingActionState](bookingAction.state)
      bookingActionState.propertyBookingState.flatMap(_.bookingList.map(_.productKey).headOption.flatten)
    } catch {
      case exception: Throwable =>
        logger.error(s"Unable to parse booking action state to get product key", exception)
        None
    }
  }

  private def getBookingStatusFromProtectionState(
      protectionState: ProtectionBookingState
  ): CreatedBookingStatus.CreatedBookingStatus = {
    protectionState match {
      case ProtectionBookingState.Created   => CreatedBookingStatus.BookingProcessing
      case ProtectionBookingState.Purchased => CreatedBookingStatus.BookingConfirmed
      case ProtectionBookingState.Failed    => CreatedBookingStatus.BookingRejected
    }
  }

  private def getRedirectPaymentResult(preAuthRedirectResult: Option[AgentPreAuthRedirectResult]) = {
    preAuthRedirectResult.map { preAuth =>
      val paymentContinuation = PaymentContinuation(
        Gateway.fromValue(preAuth.gatewayId),
        Some(preAuth.gatewayInfoId),
        preAuth.gwTransactionId,
        Some(preAuth.internalToken)
      )

      val paymentRedirectResponse =
        preAuth.redirect.map(redirect =>
          PaymentRedirect(redirect.issuerUrl, Some(redirect.postFields), Some(redirect.additionalFields))
        )
      PaymentResult(paymentContinuation, None, paymentRedirectResponse)
    }
  }

  private def get3dsPaymentResult(preAuth3dsResult: Option[PreAuth3dsRequiredResult]) = {
    preAuth3dsResult.flatMap { preAuth =>
      preAuth.continuation.map { continuation =>
        val paymentContinuation = PaymentContinuation(
          Gateway.fromValue(continuation.gatewayId.value),
          Some(continuation.gatewayInfoId),
          continuation.transactionId,
          Some(continuation.redirectPaymentToken),
          continuation.paymentToken.map(pt =>
            PaymentToken(PaymentTokenType(pt.tokenType.value), pt.tokenValue, Some(pt.additionalInfo))
          ),
          Some(continuation.mpiId)
        )
        val redirect3ds = preAuth.redirect3Ds.map(r3DS =>
          new Payment3DSResponse(
            r3DS.post3DFields,
            r3DS.issuerUrl,
            r3DS.require3DFields,
            r3DS.returnUrlField,
            Some(r3DS.referenceToken),
            Some(r3DS.internalToken),
            Some(r3DS.processed3DSOption),
            Some(r3DS.bankCallback3DS1Url)
          )
        )
        val redirectPayment = preAuth.redirectPayment.map(rp =>
          PaymentRedirect(rp.issuerUrl, Some(rp.postFields), Some(rp.additionalFields))
        )

        PaymentResult(paymentContinuation, redirect3ds, redirectPayment)
      }
    }
  }

  private def get3ds2PaymentResult(preAuth3ds2Result: Option[PreAuth3ds2RequiredResult]): Option[PaymentResult] = {
    preAuth3ds2Result.flatMap { authResult =>
      authResult.continuation.map { continuation =>
        val paymentContinuation = PaymentContinuation(
          Gateway.fromValue(continuation.gatewayId.value),
          Some(continuation.gatewayInfoId),
          continuation.transactionId,
          Some(continuation.redirectPaymentToken),
          continuation.paymentToken.map(pt =>
            PaymentToken(PaymentTokenType(pt.tokenType.value), pt.tokenValue, Some(pt.additionalInfo))
          ),
          Some(continuation.mpiId)
        )
        val redirect3ds: Option[Payment3DSResponse] = authResult.redirect3Ds.flatMap(r3DS =>
          Some(
            Payment3DSResponse(
              r3DS.post3DFields,
              r3DS.issuerUrl,
              r3DS.require3DFields,
              r3DS.returnUrlField,
              Some(r3DS.referenceToken),
              Some(r3DS.internalToken),
              Some(r3DS.processed3DSOption),
              Some(r3DS.bankCallback3DS1Url)
            )
          )
        )
        val redirectPayment: Option[PaymentRedirect] =
          authResult.redirectPayment.map(rp =>
            PaymentRedirect(rp.issuerUrl, Some(rp.postFields), Some(rp.additionalFields))
          )
        val payment3ds2Type: Option[Int] = authResult.payment3Ds2Type match {
          case Payment3DS2Type.Payment3DS2Type_RequiredDeviceFingerprint =>
            Some(ErrorCode.RequiredDeviceFingerprint.id)
          case Payment3DS2Type.Payment3DS2Type_RequiredChallenge => Some(ErrorCode.RequiredChallenge.id)
          case _                                                 => None
        }

        PaymentResult(
          continuation = paymentContinuation,
          redirect3ds = redirect3ds,
          redirectPayment = redirectPayment,
          payment3ds2Type = payment3ds2Type
        )
      }
    }
  }

  private def getPaymentReason(preAuthResultProto: PreAuthResultProto): Option[PaymentReason] = {
    preAuthResultProto.reason.map { reason =>
      PaymentReason(
        groupCode = Some(reason.groupCode),
        code = Some(reason.code),
        message = Some(reason.message)
      )
    }
  }

  private def getPreAuthResult(preAuthResultProto: Option[PreAuthResultProto]): Option[PaymentResult] = {
    preAuthResultProto.map { proto =>
      PaymentResult(
        continuation = PaymentContinuation(Gateway.None),
        redirect3ds = None,
        paymentReason = getPaymentReason(proto)
      )
    }
  }

  private def getActionMessageFromJsonString(content: String) =
    Try(JsonFormat.fromJsonString[AgentBookingActionMessage](content)).toOption

  private def getRequireCvvPaymentResult(cvvRequired: Boolean) =
    Option(
      PaymentResult(
        continuation = PaymentContinuation(Gateway.None),
        redirect3ds = None,
        requireCvv = Some(cvvRequired)
      )
    )

  private def getRetryPaymentReadyResult(
      retryFeedBack: Option[RetryFeedback]
  ): Option[PaymentResult] = {
    Some(
      PaymentResult(
        topic = Some(BookingActionMessageTopic.BAM_Topic_RetryPayment.value),
        continuation = PaymentContinuation(Gateway.None),
        redirect3ds = None,
        paymentReason = retryFeedBack.flatMap(_.preAuthResult).flatMap(getPaymentReason)
      )
    )
  }

  private def mapSupplierResponse(bam: Option[BookingActionMessage]): Option[SupplierResponse] = {
    bam.map(_.topic) match {
      case Some(BookingActionMessageTopic.BAM_Topic_JTBPartnerProvisioningResult.value) =>
        val content       = bam.flatMap(message => getActionMessageFromJsonString(message.content))
        val inventoryType = content.flatMap(_.jtbPartnerProvisioningResult).map(_.inventoryType)
        val inventoryData = content.flatMap(_.jtbPartnerProvisioningResult).map(_.inventoryData)
        Some(
          SupplierResponse(
            hrStockInformation = inventoryData,
            inventoryType = inventoryType
          )
        )
      case _ => None
    }
  }

  private def validateJtbWhitelabelId(createWhiteLabelId: WhiteLabel, getStatusWhiteLabelId: WhiteLabel)(implicit
      context: RequestContext
  ): Boolean = {
    val isGetStatusJtbWhitelabel = CommonWhitelabelUtils.isJtbSupplyWl(context.whiteLabelInfo)
    val isDifferent              = createWhiteLabelId != getStatusWhiteLabelId

    isGetStatusJtbWhitelabel && isDifferent
  }

  private[service] def rejectReasonCodeResolver(
      bookingStatus: Int,
      masterRejectReasonCode: Option[Int],
      propertyRejectReasonCode: Option[Int]
  )(implicit context: RequestContext): Option[RejectedReason] = {

    val rejectReasonCode =
      if (bookingStatus == CreatedBookingStatus.BookingConfirmed.id) None
      else if (masterRejectReasonCode.isDefined) masterRejectReasonCode
      else propertyRejectReasonCode

    val rejectReason = rejectReasonCode.map(rejectCode =>
      RejectedReason(code = rejectCode, message = "", subErrorCode = Some(rejectCode))
    )

    rejectReason
  }

  private def sendBamConsumeEventLogging(
      messageId: Option[Long] = None,
      actionId: Option[Long] = None,
      topicId: Int
  ) = {
    val metricName = "consumeBookingActionMessage"
    withMeasure(metricName, 1L, Map("topic" -> topicId.toString))
    hadoopMessagingService.sendMessage(
      BookingActionMessageConsumeEvent(messageId = messageId, actionId = actionId, topicId = topicId)
    )
  }

  // for payment we only log on the first consume event where incoming topic != outgoing topic
  private def sendBamPaymentConsumeEventLogging(
      requestTopic: Int,
      returnTopic: Int,
      actionId: Option[Long] = None
  ) = {
    if (requestTopic != returnTopic)
      sendBamConsumeEventLogging(actionId = actionId, topicId = returnTopic)
  }

  private def isInsufficientFundGroup(preAuthResult: Option[PaymentResult]): Boolean = {
    preAuthResult.exists(_.paymentReason.exists(_.groupCode.contains(PaymentReasonGroup.InsufficientFunds.id)))
  }
}
