package com.agoda.bapi.server.service

import api.request.FeatureFlag
import com.agoda.bapi.common.MessageService
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.DevicePlatform.DevicePlatform
import com.agoda.bapi.common.message.setupBooking.{LoyaltyRequest, PackageRequest, PaymentRequest, PropertyRequestItem}
import com.agoda.bapi.common.message.{DurationRequest, OccupancyRequest, PropertyBookingStateWithItinerary, PropertySearchCriteria}
import com.agoda.bapi.common.model.ChargeOption.ChargeOption
import com.agoda.bapi.common.model.booking.BookingStateMessage
import com.agoda.bapi.common.model.consumerFintech.products.ConsumerFintechProductDetail
import com.agoda.bapi.common.model.consumerFintech.products.smartFlex.{SmartFlexOfferDetail, SmartFlexProductDetail, SmartFlexReplacementDetail}
import com.agoda.bapi.common.model.consumerFintech.products.smartsaver.{SmartSaverOfferDetail, SmartSaverProductDetail}
import com.agoda.bapi.common.model.consumerFintech.{ConsumerFintechDetail, ConsumerFintechRequirement}
import com.agoda.bapi.common.model.flight.history.ActionType.ActionType
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{ActionId, CurrencyCode}
import com.agoda.bapi.common.util.ChargeOptionHelper
import com.agoda.bapi.creation.repository.PropertyBookingRepository
import com.agoda.bapi.server.handler.context.SetupBookingContext
import com.agoda.bapi.server.model.BookingPropertiesData
import com.agoda.bapi.server.model.property.BookingProperty
import com.agoda.bapi.server.reporting.logs.PapiRepositoryLogParams
import com.agoda.bapi.server.repository.PapiRepository
import com.agoda.bapi.server.repository.dto.papi._
import com.agoda.bapi.server.service.PapiPropertyStatus.PapiPropertyStatus
import com.agoda.upi.models.request.CartBaseRequest
import com.google.inject.Inject
import models.starfruit.FinanceProductInfo
import org.joda.time.LocalDate

import scala.concurrent.Future

trait PropertyService {
  def retrievePropertyBundles(
      requestItems: Seq[PropertyRequestItem],
      devicePlatform: DevicePlatform,
      chargeCurrency: CurrencyCode,
      packageRequest: Option[PackageRequest],
      giftCardRedeemRequest: Option[GiftCardRedeemRequest],
      discountRequest: Option[DiscountRequest],
      paymentRequest: Option[PaymentRequest] = None
  )(implicit context: SetupBookingContext): Future[Option[BookingProperty]]

  def retrieveProperties(
      requestItems: Seq[PropertyRequestItem],
      devicePlatform: DevicePlatform,
      chargeCurrency: CurrencyCode,
      packageRequest: Option[PackageRequest],
      giftCardRedeemRequest: Option[GiftCardRedeemRequest] = None,
      discountRequest: Option[DiscountRequest] = None,
      cardPaymentParams: Option[CardPaymentRequestParameter] = None,
      externalLoyaltyRequest: Option[LoyaltyRequest] = None,
      paymentRequest: Option[PaymentRequest] = None,
      shouldClearPriceGuranteeToken: Boolean = false,
      cartRequest: Option[CartBaseRequest] = None,
      stateId: Option[Int] = None,
      partnerClaimToken: Option[String] = None,
      rateCategoryIds: Seq[Long] = Seq.empty,
      capiToken: Option[String] = None,
      consumerFintechRequirement: Option[ConsumerFintechRequirement] = None,
      userTaxCountryCode: Option[String] = None,
      cashbackRedemptionParameter: Option[CashbackRedemptionParameter] = None,
      additionalPricingParameter: Option[AdditionalPricingParameter] = None
  )(implicit context: SetupBookingContext): Future[Seq[BookingPropertiesData]]

  def savePropertyBookingState(
      propertyBookingStateWithItinerary: PropertyBookingStateWithItinerary
  ): Future[PropertyBookingStateWithItinerary]

  def saveAndReplicatePropertyBookingState(
      actionType: ActionType,
      actionId: ActionId,
      bookingType: Option[Int],
      propertyBookingStateWithItinerary: PropertyBookingStateWithItinerary
  )(implicit context: RequestContext): Future[PropertyBookingStateWithItinerary]
}

class PropertyServiceImpl @Inject() (
    papiRepository: PapiRepository,
    messaging: MessageService,
    propertyBookingRepository: PropertyBookingRepository
) extends PropertyService {
  import PapiPropertyService._
  import com.agoda.bapi.execution.context.NonBlockingExecutionContext.default
  import com.agoda.bapi.server.model.PropertyapiImplicits.PropertiesOps

  override def retrievePropertyBundles(
      requestItems: Seq[PropertyRequestItem],
      devicePlatform: DevicePlatform,
      chargeCurrency: CurrencyCode,
      packageRequest: Option[PackageRequest] = None,
      giftCardRedeemRequest: Option[GiftCardRedeemRequest] = None,
      discountRequest: Option[DiscountRequest] = None,
      paymentRequest: Option[PaymentRequest] = None
  )(implicit context: SetupBookingContext): Future[Option[BookingProperty]] = {
    implicit val rc: RequestContext = context.requestContext
    implicit val bookingFlow        = Some(context.bookingFlowType)
    val roomBundleHints = requestItems.map { item =>
      RoomBundleHint(
        item.propertySearchCriteria.roomIdentifier,
        item.propertySearchCriteria.durationRequest.checkIn,
        item.propertySearchCriteria.durationRequest.lengthOfStay
      )
    }

    val searchCriteria = requestItems
      .map(_.propertySearchCriteria)
      .reduceOption { (newSearch, oldSearch) =>
        newSearch.copy(
          durationRequest = newSearch.durationRequest.copy(
            checkIn =
              if (newSearch.durationRequest.checkIn.isBefore(oldSearch.durationRequest.checkIn))
                newSearch.durationRequest.checkIn
              else oldSearch.durationRequest.checkIn,
            lengthOfStay = newSearch.durationRequest.lengthOfStay + oldSearch.durationRequest.lengthOfStay
          )
        )
      }
      .getOrElse(
        PropertySearchCriteria(
          None,
          "",
          OccupancyRequest(1, 1),
          DurationRequest(LocalDate.now(), 0),
          None,
          None,
          None,
          None,
          None,
          None
        )
      )

    // TODO Multiproduct or PACMAN at least we can find all of them and fix
    val papiRequestDto = PapiRequestDto.apply(
      propertySearchCriteria = searchCriteria,
      devicePlatform = devicePlatform,
      displayCurrency = chargeCurrency,
      priceGuaranteeToken = None,
      bookingFlowType = BookingFlow.SingleProperty,
      packagingToken = packageRequest,
      giftCardRedeemRequest = giftCardRedeemRequest,
      discountRequest = discountRequest,
      roomBundleHint = Some(roomBundleHints),
      paymentRequest = paymentRequest
    )(rc)

    papiRepository.getPropertyForBooking(
      papiRequestDto,
      Some(
        PapiRepositoryLogParams(
          sessionId = context.sessionId,
          deviceTypeId = context.deviceContext.map(_.deviceTypeId),
          hasPackageToken = packageRequest.exists(_.clientToken.nonEmpty),
          hasCartToken = false
        )
      ),
      searchCriteria.occupancyRequest
    )
  }

  override def retrieveProperties(
      requestItems: Seq[PropertyRequestItem],
      devicePlatform: DevicePlatform,
      chargeCurrency: CurrencyCode,
      packageRequest: Option[PackageRequest],
      giftCardRedeemRequest: Option[GiftCardRedeemRequest] = None,
      discountRequest: Option[DiscountRequest] = None,
      cardPaymentParams: Option[CardPaymentRequestParameter],
      externalLoyaltyRequest: Option[LoyaltyRequest] = None,
      paymentRequest: Option[PaymentRequest] = None,
      shouldClearPriceGuranteeToken: Boolean = false,
      cartRequest: Option[CartBaseRequest] = None,
      stateId: Option[Int] = None,
      partnerClaimToken: Option[String] = None,
      rateCategoryIds: Seq[Long] = Seq.empty,
      capiToken: Option[String] = None,
      consumerFintechRequirement: Option[ConsumerFintechRequirement] = None,
      userTaxCountryCode: Option[String] = None,
      cashbackRedemptionParameter: Option[CashbackRedemptionParameter] = None,
      additionalPricingParameter: Option[AdditionalPricingParameter] = None
  )(implicit context: SetupBookingContext): Future[Seq[BookingPropertiesData]] = {
    implicit val rc: RequestContext = context.requestContext
    implicit val bookingFlow        = Some(context.bookingFlowType)

    // TODO Multiproduct or PACMAN at least we can find all of them and fix
    def papiRequestDto(
        sc: PropertySearchCriteria,
        priceGuaranteeToken: Option[String],
        selectedChargeOption: Option[ChargeOption]
    ) =
      PapiRequestDto(
        sc: PropertySearchCriteria,
        devicePlatform,
        chargeCurrency,
        priceGuaranteeToken,
        context.bookingFlowType,
        packageRequest,
        giftCardRedeemRequest,
        discountRequest,
        selectedChargeOption = selectedChargeOption,
        cardPaymentRequestParameter = cardPaymentParams,
        externalLoyaltyRequest = externalLoyaltyRequest,
        paymentRequest = paymentRequest,
        cartRequest = cartRequest,
        stateId = stateId,
        partnerClaimToken = partnerClaimToken,
        rateCategoryIds = rateCategoryIds,
        capiToken = capiToken,
        userTaxCountryCode = userTaxCountryCode,
        cashbackRedemptionParameter = cashbackRedemptionParameter,
        additionalPricingParameter = additionalPricingParameter
      )(rc)

    val productItemFs = requestItems map { ri =>
      val propertySearchCriteria = enrichPropertySearchCriteria(context, ri.propertySearchCriteria)
      val priceGuaranteeToken = if (shouldClearPriceGuranteeToken) {
        None
      } else {
        context.session.properties.get(ri.id).flatMap(_.productToken)
      }
      for {
        bookingPropertyO <- papiRepository.getPropertyForBooking(
                              papiRequestDto(
                                propertySearchCriteria,
                                priceGuaranteeToken,
                                ChargeOptionHelper.getChargeOptionForSingleHotel(
                                  ri.payment.map(_.selectedChargeOption),
                                  ri.propertySearchCriteria.simplifiedRoomSelectionRequest
                                    .flatMap(_.alternativeOptIn.flatMap(_.crossSellOptIn))
                                )
                              ),
                              Some(
                                PapiRepositoryLogParams(
                                  sessionId = context.sessionId,
                                  deviceTypeId = context.deviceContext.map(_.deviceTypeId),
                                  packageRequest.exists(_.clientToken.nonEmpty),
                                  cartRequest.exists(_.token.nonEmpty)
                                )
                              ),
                              propertySearchCriteria.occupancyRequest,
                              consumerFintechRequirement
                            )
      } yield {
        val papiValidationStatus = PapiPropertyService.getPapiPropertyStatus(bookingPropertyO)
        val selectedChargeOption = ri.payment.map(_.selectedChargeOption)

        bookingPropertyO match {
          case Some(bp) =>
            BookingPropertiesData(
              ri.id,
              bp.propertiesJson,
              bp.properties,
              bp.properties.flatMap(_.extractPackageToken),
              papiValidationStatus,
              selectedChargeOption = selectedChargeOption,
              Some(ri.propertySearchCriteria),
              cartItemContext = ri.cartItemContext,
              consumerFintechDetail = extractConsumerFintechDetail(bp)
            )
          case None =>
            BookingPropertiesData(
              ri.id,
              "",
              None,
              packageRequest,
              papiValidationStatus,
              ri.payment.map(_.selectedChargeOption),
              Some(ri.propertySearchCriteria),
              cartItemContext = ri.cartItemContext
            )
        }
      }
    }
    Future.sequence(productItemFs)
  }

  override def savePropertyBookingState(
      propertyBookingStateWithItinerary: PropertyBookingStateWithItinerary
  ): Future[PropertyBookingStateWithItinerary] = {
    propertyBookingRepository.savePropertyBookingState(propertyBookingStateWithItinerary)
  }

  override def saveAndReplicatePropertyBookingState(
      actionType: ActionType,
      actionId: ActionId,
      bookingType: Option[Int],
      propertyBookingStateWithItinerary: PropertyBookingStateWithItinerary
  )(implicit context: RequestContext): Future[PropertyBookingStateWithItinerary] = {
    for {
      savedPropertyBookingState <- savePropertyBookingState(propertyBookingStateWithItinerary)
      bookingMessage = BookingStateMessage.fromPropertyProtoState(
                         actionType,
                         actionId,
                         bookingType,
                         savedPropertyBookingState
                       )
      _ = messaging.sendMessage(bookingMessage)
    } yield savedPropertyBookingState
  }

  private def extractConsumerFintechDetail(
      bookingProperty: BookingProperty
  )(implicit rc: RequestContext): Option[ConsumerFintechDetail] = {
    val smartFlexOpt  = extractSmartFlexOption(bookingProperty)
    val smartSaverOpt = extractSmartSaverOption(bookingProperty)
    val serviceTaxCountryOpt = if (rc.featureAware.exists(_.enableServiceTaxCountry)) {
      extractServiceTaxCountryOption(bookingProperty)
    } else None

    if (smartFlexOpt.isEmpty && smartSaverOpt.isEmpty && serviceTaxCountryOpt.isEmpty)
      None
    else
      Some(
        ConsumerFintechDetail(
          products = ConsumerFintechProductDetail(
            smartFlex = smartFlexOpt,
            cancelAndRebookV3 = None,
            smartSaver = smartSaverOpt
          ),
          serviceTaxCountry = serviceTaxCountryOpt
        )
      )
  }

  private def extractFintechProductOption[T](
      bookingProperty: BookingProperty
  )(extractor: FinanceProductInfo => Option[T]) =
    for {
      properties <- bookingProperty.properties
      property   <- properties.property.headOption
      masterRoom <- property.masterRooms.headOption
      room       <- masterRoom.childrenRooms.headOption
      finProduct <- room.finProductInfo
      product    <- extractor(finProduct)
    } yield product

  private def extractSmartFlexOption(bookingProperty: BookingProperty) =
    extractFintechProductOption(bookingProperty) { finProduct =>
      finProduct.products.smartFlex.map { smartFlex =>
        SmartFlexProductDetail(
          offer =
            if (smartFlex.originalCxlCode.nonEmpty)
              Some(SmartFlexOfferDetail(cxlCodeOriginal = smartFlex.originalCxlCode))
            else None,
          replacement = Some(
            SmartFlexReplacementDetail(smartFlex.processReplacement)
          )
        )
      }
    }

  private def extractSmartSaverOption(bookingProperty: BookingProperty) =
    extractFintechProductOption(bookingProperty) { finProduct =>
      finProduct.products.smartSaver.map { smartSaver =>
        SmartSaverProductDetail(
          offer =
            if (smartSaver.originalCxlCode.nonEmpty)
              Some(SmartSaverOfferDetail(cxlCodeOriginal = smartSaver.originalCxlCode))
            else None
        )
      }
    }

  private def extractServiceTaxCountryOption(bookingProperty: BookingProperty) =
    extractFintechProductOption(bookingProperty) { finProduct =>
      finProduct.serviceTaxCountry
    }

  private def enrichPropertySearchCriteria(
      context: SetupBookingContext,
      propertySearchCriteria: PropertySearchCriteria
  ): PropertySearchCriteria = {
    val requestContext = context.requestContext
    // if the request comes from AffiliateApi we just make the PAPI request consistent with AffiliateApi -> PAPI one
    if (requestContext.featureAware.exists(_.isAffiliateFeatureFlagEnabled))
      propertySearchCriteria
    else {
      val pricingRequest = propertySearchCriteria.pricingRequest.map { pr =>
        val featureFlags =
          pr.dfFeatureFlags ++ Seq(FeatureFlag.DomesticTaxReceipt.i, FeatureFlag.QuantumPaymentsEnabled.i)
        pr.copy(dfFeatureFlags = featureFlags)
      }
      propertySearchCriteria.copy(pricingRequest = pricingRequest)
    }
  }
}

object PapiPropertyService {
  def getPapiPropertyStatus(bookingProperty: Option[BookingProperty]): PapiPropertyStatus = {
    bookingProperty match {
      case Some(p) =>
        p.properties.map(getPapiPropertyStatus).getOrElse(PapiPropertyStatus.PropertyNotFound)
      case None => PapiPropertyStatus.PropertyNotFound
    }
  }

  def getPapiPropertyStatus(properties: transformers.Properties): PapiPropertyStatus = {
    val MAXIMUM_BOOKING_AMOUNT = 100000;
    val bookingAmount = properties.property.headOption.flatMap(
      _.masterRooms.headOption.flatMap(
        _.childrenRooms.headOption.flatMap(_.pricing.get("USD").map(_.displaySummary.perBook.chargeTotal.allInclusive))
      )
    )

    bookingAmount match {
      case Some(amount) =>
        if (amount > MAXIMUM_BOOKING_AMOUNT)
          PapiPropertyStatus.MaximumBookingAmountExceeded
        else
          PapiPropertyStatus.Ok
      case _ => PapiPropertyStatus.PropertyNotFound
    }
  }
}
