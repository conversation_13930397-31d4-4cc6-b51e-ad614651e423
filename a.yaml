Name:               heisenberg-property
Namespace:          stack-1141072
CreationTimestamp:  <PERSON><PERSON>, 15 Jul 2025 17:54:21 +0700
Labels:             app=heisenberg-property
                    component=heisenberg-property
                    devstack=true
                    heritage=controller
                    sidecar.istio.io/inject=true
                    stack_id=1141072
                    stack_name=booking-creation
                    type=component
                    username=aragaey
Annotations:        deployment.kubernetes.io/revision: 1
Selector:           app=heisenberg-property,component=heisenberg-property,devstack=true,heritage=controller,sidecar.istio.io/inject=true,stack_id=1141072,stack_name=booking-creation,type=component,username=aragaey
Replicas:           1 desired | 1 updated | 1 total | 0 available | 1 unavailable
StrategyType:       Recreate
MinReadySeconds:    0
Pod Template:
  Labels:           app=heisenberg-property
                    component=heisenberg-property
                    devstack=true
                    heritage=controller
                    sidecar.istio.io/inject=true
                    stack_id=1141072
                    stack_name=booking-creation
                    type=component
                    username=aragaey
  Annotations:      attribute.monitoring.agoda.is/service.name: devstack-apps
                    logging.monitoring.agoda.is/add-labels: component,stack_name,stack_id,username
                    logging.monitoring.agoda.is/enabled: true
                    logging.monitoring.agoda.is/log-pattern: (?P<logLevel>INFO|info|DEBUG|debug|ERROR|error|WARN|warn)
  Service Account:  heisenberg-property
  Init Containers:
   wait-for:
    Image:      images-ci.agodadev.io/aiab/dockerize:latest
    Port:       <none>
    Host Port:  <none>
    Command:
      /bin/sh
      -c
      /usr/local/bin/dockerize -wait tcp://mdc:1433 -timeout 10m && /usr/local/bin/dockerize -wait tcp://cdb:1433 -timeout 10m && /usr/local/bin/dockerize -wait tcp://devweb:1433 -timeout 10m && /usr/local/bin/dockerize -wait tcp://db-cbcache:8091 -timeout 10m && /usr/local/bin/dockerize -wait tcp://featurestore-serving:6566 -timeout 10m
    Environment:  <none>
    Mounts:       <none>
  Containers:
   main:
    Image:      images-ci.agodadev.io/papi/pricing/heisenberg:latest
    Port:       2601/TCP
    Host Port:  0/TCP
    Limits:
      cpu:     4
      memory:  14Gi
    Requests:
      cpu:      2
      memory:   6Gi
    Liveness:   http-get http://:2601/health/live delay=5s timeout=1s period=10s #success=1 #failure=80
    Readiness:  http-get http://:2601/health/ready delay=5s timeout=1s period=10s #success=1 #failure=100
    Startup:    http-get http://:2601/health/startup delay=60s timeout=1s period=5s #success=1 #failure=60
    Environment:
      AG_SERVICE_NAME:              heisenberg-property
      AG_SERVICE_VERSION:           latest
      ADS_NAMESPACE:                stack-1141072
      ADS_INGRESS_POSTFIX:          devstack.qa.agoda.is
      ADS_CONSUL_TAG:               namespace=stack-1141072
      OTEL_EXPORTER_OTLP_ENDPOINT:  http://otlp.qa.agoda.is
      OTEL_EXPORTER_OTLP_PROTOCOL:  http/protobuf
      OTEL_RESOURCE_ATTRIBUTES:     devstack.stack.id=1141072,devstack.stack.user=aragaey
      NODE_NAME:                     (v1:spec.nodeName)
      AG_FLEET_DIR:                 /var/agoda/fleet
      PYROSCOPE_APPLICATION_NAME:   heisenberg-property
      PYROSCOPE_SERVER_ADDRESS:     http://pyroscope.qa.agoda.is
      ADS_USERNAME:                 aragaey
      CDB_ENDPOINT:                 cdb:1433
      DEPLOYMENT:                   privatecloud
      DEVWEB_ENDPOINT:              devweb:1433
      FEAST_CORE_ENDPOINT:          feast-core
      FEAST_CORE_PORT:              8080
      FEAST_SERVING_ENDPOINT:       featurestore-serving:6566
      MDC_ENDPOINT:                 mdc:1433
      PRC_DEBUG:                    disabled
      PRC_ENV:                      regression-devstack-df
      PRC_HDFS_FALLBACK:            disabled
      PRC_JMX:                      disabled
      PRC_PROFILER:                 disabled
      PRC_PYROSCOPE:                disabled
      TZ:                           Asia/Bangkok
      VAULT_TOKEN:                  
    Mounts:
      /etc/consul.d/template/config.json from consul-template-config (ro,path="consul-template.hcl")
      /var/agoda/fleet from fleet-identity (rw)
      /var/log/adp-messaging from adp-messaging (rw)
   istio-proxy:
    Image:        auto
    Port:         <none>
    Host Port:    <none>
    Environment:  <none>
    Mounts:       <none>
   vault-agent:
    Image:      images-ci.agodadev.io/devops/devstack/vault_sidecar:latest
    Port:       8200/TCP
    Host Port:  0/TCP
    Command:
      vault
      agent
      -config
      /etc/vault-agent/vault-agent.hcl
    Limits:
      memory:  2Gi
    Requests:
      cpu:     250m
      memory:  500Mi
    Environment:
      AG_SERVICE_NAME:              heisenberg-property
      AG_SERVICE_VERSION:           latest
      ADS_NAMESPACE:                stack-1141072
      ADS_INGRESS_POSTFIX:          devstack.qa.agoda.is
      ADS_CONSUL_TAG:               namespace=stack-1141072
      OTEL_EXPORTER_OTLP_ENDPOINT:  http://otlp.qa.agoda.is
      OTEL_EXPORTER_OTLP_PROTOCOL:  http/protobuf
      OTEL_RESOURCE_ATTRIBUTES:     devstack.stack.id=1141072,devstack.stack.user=aragaey
      NODE_NAME:                     (v1:spec.nodeName)
      AG_FLEET_DIR:                 /var/agoda/fleet
      PYROSCOPE_APPLICATION_NAME:   heisenberg-property
      PYROSCOPE_SERVER_ADDRESS:     http://pyroscope.qa.agoda.is
      ADS_USERNAME:                 aragaey
      VAULT_ADDR:                   https://hk.qa-vault.agoda.local:8200
    Mounts:
      /etc/vault-agent from agent-config (rw)
      /var/agoda/fleet from fleet-identity (rw)
  Volumes:
   fleet-identity:
    Type:              CSI (a Container Storage Interface (CSI) volume source)
    Driver:            identity.agoda.com
    FSType:            
    ReadOnly:          true
    VolumeAttributes:  <none>
   agent-config:
    Type:      ConfigMap (a volume populated by a ConfigMap)
    Name:      heisenberg-property-vault-agent
    Optional:  false
   consul-template-config:
    Type:      ConfigMap (a volume populated by a ConfigMap)
    Name:      heisenberg-property-consul-template
    Optional:  false
   adp-messaging:
    Type:          HostPath (bare host directory volume)
    Path:          /var/log/adp-messaging
    HostPathType:  Directory
Conditions:
  Type           Status  Reason
  ----           ------  ------
  Available      False   MinimumReplicasUnavailable
  Progressing    False   ProgressDeadlineExceeded
OldReplicaSets:  <none>
NewReplicaSet:   heisenberg-property-689c7c9984 (1/1 replicas created)
Events:
  Type    Reason             Age   From                   Message
  ----    ------             ----  ----                   -------
  Normal  ScalingReplicaSet  21m   deployment-controller  Scaled up replica set heisenberg-property-689c7c9984 to 1
