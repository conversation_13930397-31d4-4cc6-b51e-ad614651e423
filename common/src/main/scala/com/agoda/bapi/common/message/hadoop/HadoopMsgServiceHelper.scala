package com.agoda.bapi.common.message.hadoop

import com.agoda.adp.messaging.message.Message
import com.agoda.bapi.common.{MessageService, ToolSet}
import com.google.inject.{ImplementedBy, Inject, Singleton}

import scala.concurrent.Future

@ImplementedBy(classOf[HadoopMsgServiceHelperImpl])
trait HadoopMsgServiceHelper {
  def log(message: Message): Future[Unit]
}
object HadoopMsgServiceHelper {
  object ApiTrack extends Enumeration {
    type ApiTrack = Value

    val AbsApiRequest: ApiTrack  = Value("AbsApiRequest")
    val AbsApiResponse: ApiTrack = Value("AbsApiResponse")

    val BapiRequest: ApiTrack  = Value("BapiRequest")
    val BapiResponse: ApiTrack = Value("BapiResponse")
  }
}

@Singleton
class HadoopMsgServiceHelperImpl @Inject() (hadoopMessageService: MessageService)
    extends HadoopMsgServiceHelper
    with ToolSet {

  override def log(message: Message): Future[Unit] = {
    hadoopMessageService.sendMessage(message)
  }
}
