package com.agoda.bapi.common.config

import javax.inject.Inject

trait KillSwitches {
  def enableStopSendingFlightVersionConflictToBFDB: <PERSON><PERSON><PERSON>
  def enableIpParserCaseInsensitive: <PERSON><PERSON><PERSON>
  def stopLoggingBapiPapiMessage: <PERSON><PERSON><PERSON>
  def stopLoggingBapiItineraryStatusLogMessage: <PERSON><PERSON><PERSON>
  def stopLoggingAbsApiMessage: <PERSON><PERSON><PERSON>
  def stopLoggingBapiMessage: <PERSON>olean
  def stopLoggingBapiCreateBookingLogMessage: <PERSON><PERSON><PERSON>
  def enableAncillaryV2AccuracyChecker: <PERSON><PERSON><PERSON>
  def disableBNPLDebitCardCheck: <PERSON><PERSON><PERSON>
  def sendFlightVersionConflictFallbackMessageToHadoop: <PERSON><PERSON><PERSON>
  def enableRebookAndCancelFlow: Boolean = false
  def enableReturnOriginalUserAgentState: Boolean
  def sendBreakdownListForAbsPrecheck: Boolean
}

object KillSwitchKeys extends Enumeration {
  val EnableStopSendingFlightVersionConflictToBFDB: Value = Value("enable-stop-sending-flight-version-conflict-to-bfdb")
  val EnableIpParserCaseInsensitive: Value                = Value("enable-ip-parser-case-insensitive")
  val StopLoggingBapiPapiMessage: Value                   = Value("stop-logging-bapipapimessage")
  val StopLoggingBapiItineraryStatusLogMessage: Value     = Value("stop-logging-bapiitinerarystatuslogmessage")
  val StopLoggingAbsApiMessage: Value                     = Value("stop-logging-absapimessage")
  val StopLoggingBapiMessage: Value                       = Value("stop-logging-bapimessage")
  val StopLoggingBapiCreateBookingLogMessage: Value       = Value("stop-logging-bapicreatebookinglogmessage")
  val EnableAncillaryV2AccuracyChecker: Value             = Value("enable-ancillaryv2-accuracychecker")
  val DisableBNPLDebitCardCheck: Value                    = Value("disable-bnpl-debitcard-check")
  val SendFlightVersionConflictFallbackMessageToHadoop: Value = Value(
    "send-flight-version-conflict-fallback-message-to-hadoop"
  )
  val EnableRebookAndCancelFlow: Value          = Value("enable-rebookandcancelflow")
  val EnableReturnOriginalUserAgentState: Value = Value("enable-return-original-user-agent-state")
  val sendBreakdownListForAbsPrecheck: Value    = Value("send-breakdownlist-abs-precheck")

}

class ConsulKvBasedKillSwitches @Inject() (consulState: InternalConsulState) extends KillSwitches {

  private val killSwitchesStates =
    KillSwitchKeys.values.foldLeft(Map.empty[KillSwitchKeys.Value, DynamicConsulKV]) {
      case (acc, key) => acc + (key -> new DynamicConsulKV(consulState, s"kill-switch/${key.toString}") {})
    }

  private def findKillSwitchValue(key: KillSwitchKeys.Value): Boolean = {
    killSwitchesStates.get(key).flatMap(_.getBooleanFromState).getOrElse(false)
  }

  override def enableStopSendingFlightVersionConflictToBFDB: Boolean = findKillSwitchValue(
    KillSwitchKeys.EnableStopSendingFlightVersionConflictToBFDB
  )

  override def enableIpParserCaseInsensitive: Boolean = findKillSwitchValue(
    KillSwitchKeys.EnableIpParserCaseInsensitive
  )

  override def stopLoggingBapiPapiMessage: Boolean = findKillSwitchValue(
    KillSwitchKeys.StopLoggingBapiPapiMessage
  )

  override def stopLoggingBapiItineraryStatusLogMessage: Boolean = findKillSwitchValue(
    KillSwitchKeys.StopLoggingBapiItineraryStatusLogMessage
  )

  override def stopLoggingAbsApiMessage: Boolean = findKillSwitchValue(
    KillSwitchKeys.StopLoggingAbsApiMessage
  )

  override def stopLoggingBapiMessage: Boolean = findKillSwitchValue(
    KillSwitchKeys.StopLoggingBapiMessage
  )

  override def stopLoggingBapiCreateBookingLogMessage: Boolean = findKillSwitchValue(
    KillSwitchKeys.StopLoggingBapiCreateBookingLogMessage
  )

  override def enableAncillaryV2AccuracyChecker: Boolean = findKillSwitchValue(
    KillSwitchKeys.EnableAncillaryV2AccuracyChecker
  )

  override def disableBNPLDebitCardCheck: Boolean = findKillSwitchValue(
    KillSwitchKeys.DisableBNPLDebitCardCheck
  )

  override def enableRebookAndCancelFlow: Boolean = findKillSwitchValue(
    KillSwitchKeys.EnableRebookAndCancelFlow
  )

  override def sendFlightVersionConflictFallbackMessageToHadoop: Boolean = findKillSwitchValue(
    KillSwitchKeys.SendFlightVersionConflictFallbackMessageToHadoop
  )

  override def enableReturnOriginalUserAgentState: Boolean = findKillSwitchValue(
    KillSwitchKeys.EnableReturnOriginalUserAgentState
  )

  override def sendBreakdownListForAbsPrecheck: Boolean = findKillSwitchValue(
    KillSwitchKeys.sendBreakdownListForAbsPrecheck
  )
}
