package com.agoda.bapi.common.mapper.papi

import com.agoda.bapi.common.ToolSet
import com.agoda.bapi.common.builder.PropertyProductPaymentInfoBuilderParams
import com.agoda.bapi.common.builder.property.PropertyProductPaymentInfoBuilder
import com.agoda.bapi.common.exception.ProductNotFoundException
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.logging.ProductPaymentInfoBuilderLog
import com.agoda.bapi.common.mapper.booking.RoomSelectionHelper
import com.agoda.bapi.common.mapper.papi.PapiResponseConversions._
import com.agoda.bapi.common.mapper.papi.PapiResponseMapper.{filterBookingByRoomUid, filterWithAlternativeOptIn}
import com.agoda.bapi.common.message.AlternativeRoomOptIn
import com.agoda.bapi.common.message.initializeBooking.BookingAmount
import com.agoda.bapi.common.model.ChargeOption.ChargeOption
import com.agoda.bapi.common.model.WhiteLabelInfo
import com.agoda.bapi.common.model.cart.CartItemContext
import com.agoda.bapi.common.model.consumerFintech.ConsumerFintechDetail
import com.agoda.bapi.common.model.creation.{AlternativeRoomInfo, BAPIBooking}
import com.agoda.bapi.common.token.common.ProductTokenKey
import models.pricing.enums.{ChargeTypes, SwapRoomTypes}
import models.starfruit.AlternativeRoom
import transformers.{EnrichedBookingItem, EnrichedBookingRoom, EnrichedEBEBooking, EnrichedEBEHotel, Property, SupplierInformation}

object PapiResponseMapper extends ToolSet {
  def calcUsdBookingAmount(property: Property): Option[BookingAmount] =
    for {
      masterRoom            <- property.masterRooms.headOption
      childRoom             <- masterRoom.childrenRooms.headOption
      usdPricing            <- childRoom.pricing.get("USD")
      totalInclusivePriceUSD = usdPricing.displaySummary.perBook.chargeTotal.allInclusive
      totalExclusivePriceUSD = usdPricing.displaySummary.perBook.chargeTotal.exclusive
    } yield {
      val discount = usdPricing.charges.collect {
        case c if c.chargeType == ChargeTypes.CampaignDiscount =>
          BigDecimal(c.total.inc)
      }.sum
      BookingAmount(
        finalPriceUSD = totalInclusivePriceUSD,
        finalExclusivePriceUSD = totalExclusivePriceUSD,
        taxAndFeeUSD = (BigDecimal(totalInclusivePriceUSD) - BigDecimal(totalExclusivePriceUSD)).toDouble,
        discountUSD = discount.toDouble
      )
    }

  def extractBapiBooking(
      property: Option[Property],
      productTokenKey: Option[ProductTokenKey] = None,
      suppliersInformation: Option[Seq[SupplierInformation]] = None,
      selectedChargeOption: Option[ChargeOption] = None,
      preBookingId: Option[Long] = None,
      pricingRequestId: Option[String] = None,
      firstSearchId: Option[String] = None,
      isPriceGuaranteedTokenCleared: Boolean = false,
      whiteLabelInfo: WhiteLabelInfo,
      isAlternativeRoomsEnabled: Boolean = false,
      alternativeRoomOptIn: Option[AlternativeRoomOptIn] = None,
      displayCurrency: Option[String] = None,
      cartItemContext: Option[CartItemContext] = None,
      isRateChannelSwapBooking: Boolean = false,
      consumerFintechDetail: Option[ConsumerFintechDetail] = None
  )(implicit
      roomUid: Option[String] = None,
      context: RequestContext
  ): Option[BAPIBooking] = {
    val searchId = property.map(_.searchId)
    property.map { p =>
      val (roomToBook, isCrossSellAvailable) =
        determineAlternativeRoomItemAndFlag(p, isAlternativeRoomsEnabled, roomUid, alternativeRoomOptIn, searchId)

      // overriding original room with alternatives, or fallback to pre-existing filter value
      val filterUid: Option[String] =
        if (roomUid.isDefined)
          roomToBook.flatMap(_.booking.flatMap(_.hotel.flatMap(_.room.map(_.uid))).headOption).orElse {
            if (isAlternativeRoomsEnabled && roomUid.isDefined)
              Some("edge case if alternative rooms there requested, but not available anymore")
            else
              roomUid
          }
        else None

      BAPIBooking(
        searchId = firstSearchId.getOrElse(p.searchId),
        prebookingId = preBookingId,
        propertyId = p.propertyId,
        cityId = p.cityId,
        countryId = p.countryId,
        booking = enrichedBookingToBookingItem(
          roomToBook,
          p.stayOccupancy,
          suppliersInformation = suppliersInformation,
          pricingRequestId = pricingRequestId,
          firstSearchId = firstSearchId,
          alternativeRoomOptIn = alternativeRoomOptIn,
          roomSwapping = p.roomSwapping,
          displayCurrency = displayCurrency
        ),
        infoSummary = papiInfoSummaryToBapiInfoSummary(p.infoSummary, p.info),
        masterRooms = papiMasterRoomsToBapiMasterRooms(p.masterRooms)(filterUid),
        paymentAmount = papiBookingToBapiPaymentAmount(roomToBook),
        campaignInfo = mapPropertyCampaignInfo(p.getAllRooms, roomToBook),
        productTokenKey = productTokenKey,
        productPayment =
          if (
            context.featureAware.exists(_.comparePaymentProductInfoBuilder) || context.featureAware
              .exists(_.shouldMigrateToPaymentProductInfoBuilder)
          ) {
            val oldProductPaymentInfo =
              createProductPaymentInfo(p.masterRooms, roomToBook, selectedChargeOption, whiteLabelInfo, displayCurrency)
            val newProductPaymentInfo = PropertyProductPaymentInfoBuilder.calculateProductPaymentInfo(
              PropertyProductPaymentInfoBuilderParams(
                p.masterRooms,
                roomToBook,
                selectedChargeOption,
                whiteLabelInfo,
                displayCurrency,
                context
              )
            )
            logger.log(
              ProductPaymentInfoBuilderLog(
                oldProductPaymentInfo = oldProductPaymentInfo,
                newProductPaymentInfo = newProductPaymentInfo,
                productType = "Property"
              )
            )
            if (context.featureAware.exists(_.shouldMigrateToPaymentProductInfoBuilder)) {
              newProductPaymentInfo
            } else {
              oldProductPaymentInfo
            }
          } else {
            createProductPaymentInfo(p.masterRooms, roomToBook, selectedChargeOption, whiteLabelInfo, displayCurrency)
          },
        isPriceGuaranteedTokenCleared = isPriceGuaranteedTokenCleared,
        isCrossSellAvailable = isCrossSellAvailable,
        alternativeRoomInfo = Some(
          AlternativeRoomInfo(
            upSellTypeInRequest = alternativeRoomOptIn.flatMap(_.swapRoomType),
            availableAlternativeTypes = p.roomSwapping.map(_.alternativeRoomType.i),
            isCrossSellAvailable = isCrossSellAvailable,
            isCrossSellRequest = alternativeRoomOptIn.flatMap(_.crossSellOptIn.flatMap(_.reason)).isDefined,
            isRateChannelSwap = isRateChannelSwapBooking
          )
        ),
        requiredGuestContact = p.infoSummary.exists(i => i.requiredGuestContact),
        cartItemContext = cartItemContext,
        consumerFintechDetails = consumerFintechDetail
      )
    }
  }

  def determineAlternativeRoomItemAndFlag(
      property: Property,
      isAlternativeRoomsEnabled: Boolean,
      roomUid: Option[String],
      alternativeRoomOptIn: Option[AlternativeRoomOptIn] = None,
      searchId: Option[String]
  ): (Option[EnrichedBookingItem], Boolean) = {
    (isAlternativeRoomsEnabled, roomUid) match {
      case (true, Some(roomIdentifier)) =>
        val roomIdentifierToUid = property.getAllRooms.map(r => (r.roomIdentifiers, r.uid))
        filterWithAlternativeOptIn(
          alternativeRoomOptIn,
          property.booking,
          roomIdentifier,
          property.roomSwapping,
          roomIdentifierToUid.toMap,
          searchId
        )
      case _ => (filterBookingByRoomUid(property.booking, roomUid), false)
    }
  }

  private def filterWithAlternativeOptIn(
      alternativeRoomOptIn: Option[AlternativeRoomOptIn],
      booking: Option[EnrichedBookingItem],
      selectedRoomIdentifier: String,
      alternativeRoomMap: Seq[AlternativeRoom],
      roomIdentifierToUidMap: Map[Option[String], Option[String]],
      searchId: Option[String]
  ): Tuple2[Option[EnrichedBookingItem], Boolean] = {
    val rooms = booking.map(_.rooms).getOrElse(List.empty)
    if (rooms.isEmpty) throw ProductNotFoundException("No rooms available!")
    if (
      rooms.size == 1 && alternativeRoomMap.isEmpty && !RoomSelectionHelper.isUpXSellingOptedIn(alternativeRoomOptIn)
    ) {
      // DF in case room identifier was changed (rehashed), but room didn't have alternatives will not populate
      /* alternative mappings with rehashing hint, in that case only single room is returned and it matches requested
       * one */
      rooms.headOption.map(room => (filterByRoomUid(booking, room.uid), false)).getOrElse((None, false))
    } else {
      val roomToBookIdentifier =
        RoomSelectionHelper.getRoomToBookUid(
          alternativeRoomOptIn,
          Some(selectedRoomIdentifier),
          alternativeRoomMap,
          None
        )
      val crossSellIsAvailable =
        RoomSelectionHelper.hasCrossSelling(alternativeRoomOptIn, alternativeRoomMap)

      if (roomToBookIdentifier.isEmpty)
        logger.warn(s"[ALT_ROOM] Requested room not found! (search id: ${searchId.getOrElse("Unknown")})")

      roomToBookIdentifier
        .map(rid =>
          (filterByRoomUid(booking, roomIdentifierToUidMap(Some(rid)).getOrElse("No Match")), crossSellIsAvailable)
        )
        .getOrElse(None, false)
    }
  }

  private def filterByRoomUid(booking: Option[EnrichedBookingItem], ruid: String): Option[EnrichedBookingItem] = {
    val bookingMeta: List[EnrichedEBEBooking] =
      booking.map(_.booking.filter(b => b.hotel.exists(_.room.exists(_.uid == ruid)))).getOrElse(List.empty)
    val hotelToBook: Option[EnrichedEBEHotel] = bookingMeta.flatMap { b =>
      val result: Seq[EnrichedEBEHotel] = b.hotel.filter(_.room.exists(_.uid == ruid))
      result.headOption
    }.headOption
    val roomToBook = hotelToBook.flatMap(_.room.filter(_.uid == ruid).headOption)

    booking.map(
      _.copy(booking = bookingMeta.map(_.copy(hotel = hotelToBook.map(_.copy(room = roomToBook.toList)).toList)).toList)
    )
  }

  @deprecated("after experiments for alternative rooms enabled are integration change to filterByRoomUid")
  private def filterBookingByRoomUid(
      booking: Option[EnrichedBookingItem],
      roomUidOpt: Option[String]
  ): Option[EnrichedBookingItem] =
    if (roomUidOpt.isEmpty)
      booking
    else {
      val b = booking.get.booking.filter(t => roomUidOpt.contains(t.hotel.head.room.head.uid))
      booking.map(_.copy(booking = b))
    }
}
