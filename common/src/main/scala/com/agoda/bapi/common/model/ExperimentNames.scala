package com.agoda.bapi.common.model

object ExperimentNames
    extends MPBEExperimentNames
    with WLBCExperimentNames
    with CFBExperimentNames
    with SKYCExperimentNames
    with VCARDAPIExperimentNames
    with INSTExperimentNames
    with BCTExperimentNames
    with PCExperimentNames
    with PaymentExperimentNames
    with UNIBFExperimentNames
    with WLBOExperimentNames
    with WLBEExperimentNames
    with SKYAExperimentNames
    with RTAPExperimentNames
    with ABEXExperimentNames
    with CEGFAExperimentNames
    with ACTExperimentNames {
  val UseUpcLocalService: String                           = "BTBIB-348"
  val showWeChatDomestic: String                           = "WECHAT-PAY-DOMESTIC"
  val isPriceChangePollingForMSPA: String                  = "SCP-3076-MWEB"
  val enableUSRequiredFields: String                       = "MPBU-269"
  val roomSwapDSPA: String                                 = "SCP-3184-DWEB"
  val isPreCheckOnAlternatives: String                     = "MBPSEA-81"
  val isCrossSellOnUpSell: String                          = "MBPSEA-423"
  val isCrossSellAllBreakfastTypes: String                 = "MBPSEA-424"
  val isIndiaEnabledForPackage: String                     = "PACKAGES-HH-IN"
  val preProcessPaymentSettlement: String                  = "SKYSB-2358"
  val cegAAPreventionHideOTCPaymentOptions: String         = "CV-245"
  val reduceAllotmentAlertRateForOfflinePayment: String    = "CEGHZ-1722-KILLSWITCH"
  val enableWeChatMiniProgramPlatformMapping: String       = "CG-WMP-MAPPING"
  val enableJingDongMiniProgramPlatformMapping: String     = "CG-3855"
  val enableDBSHKOnAgoda: String                           = "WLPLAUNCH-DBSHK-DIRECT"
  val enableDBSSGOnAgoda: String                           = "WLPLAUNCH-DBSSG-DIRECT"
  val rateChannelSwapSwitchExp: String                     = "POPT-10068"
  val useCartBreakdownForSingleVehicle                     = "CARR-2529"
  val fallbackToJapanChildRate                             = "JP-1518"
  val ArrivalTimeValidation: String                        = "WLBF-2136"
  val useHybridPriceBreakdownBuilderForCart: String        = "MPWPI-21"
  val enableActivityCartBFMigration: String                = "AFT-149"
  val enableFlightPayoutAgent                              = "SKYWL-5469"
  val disableApplePay                                      = "CH-2541"
  val dynamicBookingQuestions: String                      = "AFT-350"
  val enableFastTrackPricingAdjustment: String             = "FASTTRACK-PRICING"
  val enableFastTrackPromoCodeBadge: String                = "FASTTRACK-PROMOCODE"
  val adjustFastTrackFee: String                           = "MMB-8745"
  val expandFastTrackInvoiceOrigins: String                = "MMB-8880"
  val useAABUpdateCreditCardInfo: String                   = "CEGWL-4649"
  val enableApostropheInGreetingMessage: String            = "APA-16107"
  val isRemoveFenceSelfServiceURLsForRurubu: String        = "WLBE-533"
  val EnableJava11HttpBackendForNusaFlightSupplier: String = "FLCO-6843"
  val EnableAllDmcBookingToSaveDiscountInformation: String = "RC-2092"
  val RemoveReadingPMCCampaignDataFromBFDB: String         = "PMC-5033"
  val EnableChangeHotelFundedTextFallback: String          = "PMC-5316"
  val EnableLogDateOfBirth: String                         = "WLBE-819"
  val useNewFraudClient: String                            = "MPF-1742"
  val isInclusivePaySupplier: String                       = "LT-1172"
  val isForceMdbCashbackRedemption                         = "CASH-3628"
  val FixPromotionDiscountsInfo: String                    = "JTBB-5922"
  val enablePassingBookingSource: String                   = "PAPI-22423"
  val removeSpBAPIGetPMCCampaignPromotion: String          = "PMC-5507"
  val enableFlightsMultiProductPMCMigration                = "FLMKT-2691"
  val enableMemberId                                       = "JTBFP-1414"
  val EnableNaverPayPayLaterForIOS: String                 = "PAYFLEX-57-NAVERPAY-PAYLATER-IOS"
  val EnableNaverPayPayLaterForAndroid: String             = "PAYFLEX-57-NAVERPAY-PAYLATER-ANDROID"
  val EnableNaverPayPayLaterForMWeb: String                = "PAYFLEX-57-NAVERPAY-PAYLATER-MWEB"
  val EnableNaverPayPayLaterForDWeb: String                = "PAYFLEX-57-NAVERPAY-PAYLATER-DWEB"
  val EnableThaiVietJetMigration: String                   = "FLCO-8163"
  val fixDoubleTaxesAndFeesPaySupplier: String             = "LT-1428"
  val enableDuplicateRequestCheckForCartFlow               = "WLBE-894"
  val EnableFixedDiscountPerAppliedDate: String            = "ASTE-4252"
  val EnableRurubuDirectSupply: String                     = "JTBB-6117"
  val addSearchClusterUserAgentModel: String               = "PRIVATECM-3074"
  val enableLionAirMultiSession: String                    = "FLCO-7920"
  val EnableStatusTokenV6: String                          = "PBA-338"
}
