package com.agoda.bapi.common.model.flight

import com.agoda.bapi.common.model.PNR
import com.agoda.bapi.common.util.JacksonSerializer.EnumAsStringDeserializer
import com.fasterxml.jackson.core.`type`.TypeReference
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.module.scala.JsonScalaEnumeration
import io.swagger.annotations.{ApiModel, ApiModelProperty, Extension, ExtensionProperty}
import org.joda.time.DateTime

import scala.annotation.meta.field

@ApiModel(value = "FlightSliceForBookingDetails", description = "Flight booking (details) slice")
case class FlightSlice(
    flightSliceId: Long,
    flightBookingId: Long,
    origin: String,
    originFullName: Option[String],
    destination: String,
    destinationFullName: Option[String],
    departure: DateTime,
    localDeparture: String,
    arrival: DateTime,
    localArrival: String,
    duration: Int,
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    recStatus: Option[Int],
    recCreatedWhen: DateTime,
    recModifiedWhen: Option[DateTime],
    baggageAllowance: Option[List[FlightBaggageAllowance]],
    segments: List[FlightSegment]
)

@ApiModel(value = "FlightSegmentForBookingDetails", description = "Flight booking (details) segment")
case class FlightSegment(
    flightSegmentId: Long,
    flightSliceId: Long,
    marketingAirline: String,
    marketingAirlineFullName: Option[String],
    operatingAirline: String,
    operatingAirlineFullName: Option[String],
    flightNo: String,
    origin: String,
    originFullName: Option[String],
    departure: DateTime,
    localDeparture: String,
    destination: String,
    destinationFullName: Option[String],
    arrival: DateTime,
    localArrival: String,
    airEquipmentCode: String,
    bookingClass: String,
    cabinClass: String,
    cabinName: String,
    fareBasisCode: String,
    fareRules: String,
    duration: Int,
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    recStatus: Option[Int],
    recCreatedWhen: DateTime,
    recModifiedWhen: Option[DateTime],
    baggageAllowance: Option[FlightBaggageAllowance],
    carrierPnr: Option[PNR],
    airEquipmentName: Option[String]
)

@ApiModel(
  value = "FlightBaggageAllowanceForBookingDetails",
  description = "Baggage allowance in flight booking details"
)
case class FlightBaggageAllowance(
    flightBaggageId: Long,
    flightSliceId: Long,
    @(ApiModelProperty @field)(dataType = "Long", required = false)
    flightSegmentId: Option[Long],
    `type`: Int,
    count: Int,
    paxType: String,
    maxWeightKg: Option[BigDecimal],
    maxWeightLbs: Option[BigDecimal],
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    totalSizeCm: Option[Int],
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    totalSizeIn: Option[Int],
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    lengthCm: Option[Int],
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    lengthIn: Option[Int],
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    widthCm: Option[Int],
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    widthIn: Option[Int],
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    heightCm: Option[Int],
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    heightIn: Option[Int],
    priceAmt: Option[BigDecimal],
    priceCurrency: Option[String]
)

@ApiModel(value = "FlightPaxForBookingDetails", description = "Passenger information in flight booking details")
case class FlightPax(
    firstName: String,
    middleName: Option[String],
    lastName: String,
    gender: String,
    flightPaxId: Long,
    flightBookingId: Long,
    passengerTypeCode: Option[String],
    birthDate: Option[DateTime],
    passportNumber: Option[String],
    passportExpires: Option[DateTime],
    primary: Option[String],
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    nationalityId: Option[Int],
    passportSeries: Option[String],
    passportCountry: Option[String],
    ticketNumbers: Seq[FlightPaxTicket],
    nationalityName: Option[String],
    @(ApiModelProperty @field)(dataType = "Long", required = false)
    supplierFlightPaxId: Option[Long]
)

@ApiModel(value = "BreakdownForBookingDetails", description = "Price breakdown in flight booking details")
case class Breakdown(
    breakdownId: Long,
    itineraryId: Long,
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    bookingType: Option[Int],
    @(ApiModelProperty @field)(dataType = "Long", required = false)
    bookingId: Option[Long],
    @(ApiModelProperty @field)(dataType = "Long", required = false)
    actionId: Option[Long],
    eventDate: DateTime,
    itemId: Int,
    typeId: Int,
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    taxFeeId: Option[Int],
    quantity: Int,
    localCurrency: String,
    localAmount: BigDecimal,
    exchangeRate: BigDecimal,
    usdAmount: BigDecimal,
    requestedAmount: Option[BigDecimal] = None,
    @(ApiModelProperty @field)(dataType = "Long", required = false)
    refBreakdownId: Option[Long],
    vendorExchangeRate: BigDecimal
)

object TicketType extends Enumeration {
  type TicketType = Value
  val E = Value('E')
  val P = Value('P')
  val X = Value('X')
  val N = Value('N')
}
class TicketTypeType         extends TypeReference[TicketType.type]
class TicketTypeDeserializer extends EnumAsStringDeserializer(TicketType)

case class FlightPaxTicket(
    flightPaxId: Long,
    @ApiModel(description = "Indicates the ticket type. Most common E - electronic and P - paper.")
    @JsonDeserialize(using = classOf[TicketTypeDeserializer])
    @JsonScalaEnumeration(classOf[TicketTypeType])
    @(ApiModelProperty @field)(
      dataType = "com.agoda.bapi.common.model.flight.TicketType$",
      required = true,
      extensions = Array(
        new Extension(
          name = "ms-enum",
          properties = Array(
            new ExtensionProperty(name = "name", value = "TicketType"),
            new ExtensionProperty(name = "modelAsString", value = "false")
          )
        )
      )
    )
    ticketType: TicketType.Value, // E or P
    ticketNumber: String
)
