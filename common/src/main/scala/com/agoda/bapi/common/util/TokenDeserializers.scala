package com.agoda.bapi.common.util

import com.agoda.bapi.common.message.creation.ReferenceToken
import com.agoda.bapi.common.model.AbsData
import com.agoda.bapi.common.model.activity.ActivityBookingToken
import com.agoda.bapi.common.model.addOn.cegFastTrack.AddOnBookingToken
import com.agoda.bapi.common.model.car.CarBookingToken
import com.agoda.bapi.common.model.flight.FlightBookingToken
import com.agoda.bapi.common.model.tripProtection.TripProtectionToken

import com.agoda.bapi.common.model.addOn
import com.agoda.bapi.common.token._
import com.agoda.bapi.common.token.common.ProductTokenKey
import com.agoda.bapi.common.token.property.{Token, WhitelabelAffiliateBookingToken}

import scala.util.{Failure, Try}

// Business Exception
class VersionIsNull() extends Exception(s"token not contain version")

class ContentIsNull() extends Exception(s"token not contain content")

class VersionNotSupported(version: Int) extends Exception(s"version: $version is not supported")
class SetupContentInvalid()             extends Exception(s"setup booking content is invalid or missing.")
class RetryPaymentContentInvalid()      extends Exception(s"retry payment content is invalid or missing.")

class TokenDeserializer[T: Manifest] {

  protected[util] def validateToken(token: Token)(f: (TokenContent, TokenVersion) => Try[T]): Try[T] =
    Try {
      val content = token.content.getOrElse(throw new ContentIsNull())
      val version = token.version.getOrElse(throw new VersionIsNull())
      f(content, version)
    }.flatten

  type TokenContent = String
  type TokenVersion = Int

  protected def defaultVersion: PartialFunction[(TokenContent, TokenVersion), Try[T]] = {
    case (tokenContent, 1) => JacksonSerializer.deserialize[T](tokenContent)
  }

  protected def fallback: PartialFunction[(TokenContent, TokenVersion), Failure[T]] = {
    case (_, version) => Failure(new VersionNotSupported(version))
  }

  protected def fromTokenContentAndVersion: PartialFunction[(TokenContent, TokenVersion), Try[T]] =
    defaultVersion.orElse(fallback)

  def deserialize(token: Token): Try[T] =
    validateToken(token) { case (cont, ver) => fromTokenContentAndVersion.apply(cont, ver) }
}

trait TokenDeserializers {
  def apply[T: TokenDeserializer]: TokenDeserializer[T]
  def toToken(token: String): Try[Token]
}

object TokenDeserializers extends TokenDeserializers {

  implicit val CarBookingModelDeserializer: TokenDeserializer[ProductBookingModel[CarBookingToken]] =
    new TokenDeserializer[ProductBookingModel[CarBookingToken]]
  implicit val FlightBookingModelDeserializer: TokenDeserializer[ProductBookingModel[Seq[FlightBookingToken]]] =
    new TokenDeserializer[ProductBookingModel[Seq[FlightBookingToken]]] {
      override protected def defaultVersion
          : PartialFunction[(TokenContent, TokenVersion), Try[ProductBookingModel[Seq[FlightBookingToken]]]] = {
        case (tokenContent, BookingModelTokenVersion.flightBookingModel) =>
          JacksonSerializer.deserialize[ProductBookingModel[Seq[FlightBookingToken]]](tokenContent)
      }

      override protected def fromTokenContentAndVersion
          : PartialFunction[(TokenContent, TokenVersion), Try[ProductBookingModel[Seq[FlightBookingToken]]]] = {
        case (tokenContent, 1) =>
          JacksonSerializer
            .deserialize[Map[ProductTokenKey, FlightBookingToken]](tokenContent)
            .map(token => token.mapValues(t => Seq(t)))
        case (tokenContent, BookingModelTokenVersion.flightBookingModel) =>
          defaultVersion(tokenContent, BookingModelTokenVersion.flightBookingModel)
        case (_, version) => Failure(new VersionNotSupported(version))
      }
    }
  implicit val PropertyBookingModelDeserializer: TokenDeserializer[PropertyBookingModel] =
    new TokenDeserializer[PropertyBookingModel]
  implicit val PropertySetupModelDeserializer: TokenDeserializer[PropertySetupModel] =
    new TokenDeserializer[PropertySetupModel]
  implicit val MultiProductCreationBookingTokenDeserializer: TokenDeserializer[MultiProductCreationBookingToken] =
    new TokenDeserializer[MultiProductCreationBookingToken]
  implicit val MultiProductSetupBookingTokenDeserializer: TokenDeserializer[MultiProductSetupBookingToken] =
    new TokenDeserializer[MultiProductSetupBookingToken]
  implicit val MultiProductRetryPaymentBookingTokenDeserializer
      : TokenDeserializer[MultiProductRetryPaymentBookingToken] =
    new TokenDeserializer[MultiProductRetryPaymentBookingToken]
  implicit val MultiProductBookingTokenDeserializer: TokenDeserializer[MultiProductBookingToken] =
    new TokenDeserializer[MultiProductBookingToken]
  implicit val MultiProductBookingDetailTokenDeserializer: TokenDeserializer[ItineraryAssociatedBookingsToken] =
    new TokenDeserializer[ItineraryAssociatedBookingsToken]
  implicit val ProtectionBookingModelDeserializer: TokenDeserializer[ProductBookingModel[TripProtectionToken]] =
    new TokenDeserializer[ProductBookingModel[TripProtectionToken]]
  implicit val ReferenceTokenDeserializer: TokenDeserializer[ReferenceToken] =
    new TokenDeserializer[ReferenceToken]
  implicit val AbsDataTokenDeserializer: TokenDeserializer[AbsData] =
    new TokenDeserializer[AbsData]
  implicit val WhitelabelAffiliateBookingTokenDeserializer: TokenDeserializer[WhitelabelAffiliateBookingToken] =
    new TokenDeserializer[WhitelabelAffiliateBookingToken]

  implicit val ActivityBookingModelDeSerializer: TokenDeserializer[ProductBookingModel[ActivityBookingToken]] =
    new TokenDeserializer[ProductBookingModel[ActivityBookingToken]]
  implicit val AddOnBookingModelDeserializer: TokenDeserializer[ProductBookingModel[AddOnBookingToken]] =
    new TokenDeserializer[ProductBookingModel[AddOnBookingToken]]
  implicit val GenericAddOnBookingModelDeserializer: TokenDeserializer[ProductBookingModel[addOn.AddOnBookingToken]] =
    new TokenDeserializer[ProductBookingModel[addOn.AddOnBookingToken]]
  implicit val AncillarySetupModelDeserializer: TokenDeserializer[AncillarySetupModel] =
    new TokenDeserializer[AncillarySetupModel]

  def apply[T: TokenDeserializer]: TokenDeserializer[T] = implicitly[TokenDeserializer[T]]

  def toToken(token: String): Try[Token] = JacksonSerializer.deserialize[Token](token)
}
