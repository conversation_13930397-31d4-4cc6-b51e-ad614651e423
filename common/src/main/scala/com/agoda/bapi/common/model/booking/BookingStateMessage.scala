package com.agoda.bapi.common.model.booking

import com.agoda.adp.messaging.scala.avro.annotation.SchemaName
import com.agoda.adp.messaging.scala.message.Message
import com.agoda.bapi.common.mapper.booking.BookingStateMessageMapper
import com.agoda.bapi.common.model.ActionId
import com.agoda.bapi.common.model.base.BaseBookingEssInfoInternal
import com.agoda.bapi.common.model.flight.flightModel.InclusionType.InclusionType
import com.agoda.bapi.common.model.flight.flightModel.Scope.Scope

import java.util.Date

/**
  * This is the case class used for replication (send message to Kafka)
  */
case class BookingStateMessage(
    actionType: Int,
    actionId: ActionId,
    bookingType: Option[Int],
    bookingId: Long,
    schemaVersion: String,
    flights: Seq[FlightBookingForMessage],
    slices: Seq[FlightSliceForMessage],
    segments: Seq[FlightSegmentForMessage],
    passengers: Seq[FlightPaxForMessage],
    payments: Seq[PaymentForMessage],
    bookingPayments: Seq[BookingPaymentForMessage],
    bookingRelationships: Seq[BaseBookingRelationshipForMessage],
    breakdown: Seq[BreakdownForMessage],
    breakdownPerPax: Seq[BreakdownPerPaxForMessage],
    baggageAllowance: Seq[FlightBaggageAllowanceForMessage],
    history: Seq[ItineraryHistoryForMessage],
    paxTickets: Seq[FlightPaxTicketStateForMessage],
    itinerary: FlightItineraryForMessage,
    summary: Seq[FlightSummaryForMessage],
    userAgent: Option[UserAgentStateForMessage],
    bookingAttribution: Seq[BookingAttributionStateForMessage],
    itineraryDate: Date,
    protectionModels: Option[Seq[ProtectionModelForMessage]],
    multiProductInfos: Option[Seq[MultiProductInfoForMessage]],
    @deprecated("please use below segmentInfoByPaxType instead. This field name mismatch with SetStateRequest.")
    flightSegmentInfoByPaxType: Seq[FlightSegmentInfoByPaxTypeForMessage],
    segmentInfoByPaxType: Seq[FlightSegmentInfoByPaxTypeForMessage],
    fareRulePolicies: Option[Seq[FareRulePolicyForMessage]],
    flightSeatSelection: Seq[FlightSeatSelectionDetailForMessage],
    vehicle: Option[Seq[VehiclesForMessage]],
    baggage: Seq[FlightBaggageForMessage],
    activities: Option[Seq[ActivityForMessage]], // this should not be seq
    properties: Option[Seq[PropertyForMessage]],
    cegFastTracks: Option[Seq[CegFastTrackForMessage]],
    addOns: Option[Seq[AddOnForMessage]],
    multiProductBookingGroups: Option[Seq[MultiProductBookingGroupModelMessage]],
    @deprecated("please use flightBrandSelections instead")
    flightBrandSelection: Option[Seq[FlightBrandSelectionMessage]] = None,
    @deprecated("please use flightBrandAttributes instead")
    flightBrandAttribute: Option[Seq[FlightBrandAttributeMessage]] = None,
    @deprecated("please use flightBrandAttributeParams instead")
    flightBrandAttributeParam: Option[Seq[FlightBrandAttributeParamMessage]] = None,
    flightBrandSelections: Option[Seq[FlightBrandSelectionForMessage]],
    flightBrandAttributes: Option[Seq[FlightBrandAttributeForMessage]],
    flightBrandAttributeParams: Option[Seq[FlightBrandAttributeParamMessage]],
    flightBaseBooking: Option[BaseBookingMessage],
    flightBaseCancellationInfo: Option[BaseCancellationInfoMessage],
    postBookingFee: Option[Seq[PostBookingFeeMessage]] = None,
    crossProductIsolatedFeature: Option[CrossProductIsolatedFeatureForMessage],
    essInfos: Option[Seq[BaseBookingEssInfoMessage]] = None
) extends Message[BookingStateMessage] {
  // Every booking will always land to a same partition
  override def getPartitionKey: String = s"${itinerary.itineraryId}-$bookingId"
}

object BookingStateMessage extends BookingStateMessageMapper

@SchemaName("CrossProductIsolatedFeature")
final case class CrossProductIsolatedFeatureForMessage(
    pendingBookingChange: Option[PendingBookingChangeMessage]
)

@SchemaName("PendingBookingChange")
final case class PendingBookingChangeMessage(
    changeId: Long,
    bookingId: Long,
    actionTypeId: Int,
    changeProtobuf: Option[String],
    version: Int,
    statusId: Int,
    recCreatedWhen: Date
)

case class PostBookingFeeMessage(
    feeType: Int,
    currencyCode: String,
    amount: Double,
    exchangeRate: Double,
    usdAmount: Double
)

case class FlightBookingExperimentForMessage(
    trackingCookieId: Option[String],
    recCreatedWhen: Option[Date] = None
)
@SchemaName("FlightBooking")
case class FlightBookingForMessage(
    flightBookingId: Long,
    itineraryId: Long,
    multiProductId: Option[Long],
    ticketingAirline: String,
    pnr: Option[String],
    gdsPnr: Option[String],
    version: Int,
    flightStateId: Int,
    isCancelled: Boolean,
    cancellationDate: Option[Date] = None,
    voidWindowUntil: Option[Date],
    paymentModel: Int,
    supplierId: Int,
    subSupplierId: Int,
    pointOfSale: Option[String] = None,
    experimentVariant: Option[String] = None,
    supplierSpecificData: String,
    accountingEntity: Option[AccountingEntityForMessage],
    supplierStatusCode: Option[String] = None,
    supplierReasonCode: Option[String] = None,
    supplierBookingId: String,
    supplierCommissionAmount: Option[Double],
    supplierCommissionPercentage: Option[Double],
    fraudScore: Option[Int],
    fraudAction: Option[Int],
    fraudCheckIp: String,
    storefrontId: Int,
    platformId: Option[Int],
    languageId: Option[Int],
    displayCurrency: Option[String],
    serverName: Option[String],
    cid: Option[Int],
    sessionId: String,
    clientIpAddress: String,
    trackingCookieId: Option[String],
    trackingCookieDate: Option[Date],
    trackingTag: Option[String],
    @deprecated("Will be replaced with searchRequestId")
    searchId: Option[Long],
    searchRequestId: Option[String] = None,
    flapiItineraryId: Option[String] = None,
    recCreatedWhen: Option[Date] = None,
    whitelabelId: Int,
    recStatus: Option[Int] = None,
    recModifiedWhen: Option[Date] = None,
    rejectReasonCode: Option[Int] = None,
    tripStartDate: Option[Date],
    tripEndDate: Option[Date],
    commonBookingInfo: Option[CommonBookingInfoForMessage],
    commonBookingEventsInfo: Option[CommonBookingEventsInfoForMessage],
    virtualInterlining: Option[Boolean] = None,
    freeBagScope: Option[Int],
    fareRuleScope: Option[Int],
    promotionCampaignId: Option[Int],
    supplierPaymentMethod: Option[Int],
    supplierReservationExpireDateTime: Option[Date] = None,
    supplierReservationExpireInMinutes: Option[Int] = None,
    originalAttemptBookingId: Option[Long] = None,
    flightPostbookingStateId: Option[Int] = None,
    facilitationFeeWaiverReasonId: Option[Int] = None
)

@SchemaName("BookingAction")
case class BookingActionForMessage(
    actionId: Long,
    itineraryId: Long,
    bookingType: Option[Int],
    bookingId: Long,
    memberId: Int,
    actionTypeId: Int,
    correlationId: String,
    requestId: String,
    workflowId: Int,
    workflowStateId: Int,
    stateSchemaVersion: Int,
    state: String,
    storefrontId: Option[Int],
    languageId: Option[Int]
)

@SchemaName("AccountingEntity")
case class AccountingEntityForMessage(
    merchantOfRecord: Int,
    rateContract: Int,
    revenue: Int,
    argument: Option[String] = None
)

@SchemaName("CommonBookingInfo")
case class CommonBookingInfoForMessage(
    origin: Option[String],
    isTestBooking: Boolean
)

@SchemaName("CommonBookingEventsInfo")
case class CommonBookingEventsInfoForMessage(
    confirmationDate: Option[Date] = None,
    cancellationDate: Option[Date] = None
)

@SchemaName("FlightSlice")
case class FlightSliceForMessage(
    referenceId: Int, // in natural order across all slices
    flightSliceId: Long,
    flightBookingId: Long,
    origin: String,
    destination: String,
    departure: Date,
    arrival: Date,
    duration: Int,
    recStatus: Option[Int] = None,
    recCreatedWhen: Option[Date] = None,
    recModifiedWhen: Option[Date] = None
)

@SchemaName("FlightSegment")
case class FlightSegmentForMessage(
    referenceId: Int, // in natural order across all segments
    sliceReferenceId: Int,
    flightSegmentId: Long,
    flightSliceId: Long,
    marketingAirline: String,
    operatingAirline: String,
    flightNo: String,
    origin: String,
    departure: Date,
    destination: String,
    arrival: Date,
    airEquipmentCode: String,
    bookingClass: String,
    cabinClass: String,
    cabinName: String,
    fareBasisCode: String,
    fareRules: String,
    duration: Int,
    carrierPnr: Option[String] = None,
    recStatus: Option[Int] = None,
    recCreatedWhen: Option[Date] = None,
    recModifiedWhen: Option[Date] = None,
    bagsRecheckRequired: Option[Boolean] = None,
    departureTerminal: Option[String] = None,
    arrivalTerminal: Option[String] = None
)

case class FlightSegmentInfoByPaxTypeForMessage(
    referenceId: Int,
    segmentReferenceId: Int,
    segmentInfoByPaxTypeId: Long,
    flightSegmentId: Long,
    passengerType: String,
    bookingClass: String,
    fareBasisCode: String,
    recStatus: Option[Int],
    recCreatedWhen: Option[Date],
    recModifiedWhen: Option[Date],
    fareRuleRevisionId: String
)

@SchemaName("FlightBaggageAllowance")
case class FlightBaggageAllowanceForMessage(
    referenceId: Int, // in natural order across all baggage allowance
    sliceReferenceId: Int,
    segmentReferenceId: Int,
    flightBaggageId: Long,
    flightSliceId: Long,
    flightSegmentId: Option[Long],
    paxType: Option[String],
    `type`: Int,
    count: Int,
    maxWeightKg: Option[Double],
    maxWeightLbs: Option[Double],
    totalSizeCm: Option[Int],
    totalSizeIn: Option[Int],
    lengthCm: Option[Int],
    lengthIn: Option[Int],
    widthCm: Option[Int],
    widthIn: Option[Int],
    heightCm: Option[Int],
    heightIn: Option[Int],
    priceAmt: Option[Double],
    priceCurrency: Option[String],
    recStatus: Option[Int] = None,
    recCreatedWhen: Option[Date] = None,
    recModifiedWhen: Option[Date] = None,
    source: Option[String] = None,
    cmsMappingValue: Option[String] = None
)

@SchemaName("FlightBaggage")
case class FlightBaggageForMessage(
    referenceId: Int,
    sliceReferenceId: Int,
    flightBaggageId: Long,
    flightPaxId: Long,
    flightSliceId: Long,
    baggageTypeId: Int,
    quantity: Option[Int],
    maxWeight: Option[Double],
    maxWeightUnit: Option[String],
    weightLimitPerBag: Option[Double],
    weightLimitPerBagUnit: Option[String],
    sizeLength: Option[Double],
    sizeWidth: Option[Double],
    sizeHeight: Option[Double],
    sizeUnit: Option[String],
    priceAmount: Double,
    priceCurrency: String,
    supplierData: String,
    baggageStatus: Int,
    isCarryOn: Boolean,
    recStatus: Option[Int] = None,
    recCreatedWhen: Option[Date] = None,
    recModifiedWhen: Option[Date] = None,
    scope: Int,
    isPartialSettlementRequired: Option[Boolean]
)

@SchemaName("FlightPax")
case class FlightPaxForMessage(
    referenceId: Int,
    flightPaxId: Long,
    flightBookingId: Long,
    passengerTypeCode: String,
    piiHash: String,
    supplierFlightPaxId: Option[Long] = None,
    recStatus: Option[Int] = None,
    recCreatedWhen: Option[Date] = None,
    recModifiedWhen: Option[Date] = None
)

@SchemaName("Breakdown")
case class BreakdownForMessage(
    referenceId: Int,
    breakdownId: Long,
    itineraryId: Long,
    bookingType: Option[Int],
    bookingId: Option[Long],
    actionId: Option[Long],
    eventDate: Date,
    itemId: Int,
    typeId: Int,
    taxFeeId: Option[Int],
    quantity: Int,
    localCurrency: String,
    localAmount: Double,
    exchangeRate: Double,
    usdAmount: Double,
    requestedAmount: Option[Double] = None,
    refBreakdownId: Option[Long],
    recStatus: Option[Int] = None,
    recCreatedWhen: Option[Date] = None,
    vendorExchangeRate: Double,
    upcId: Option[Long]
)

@SchemaName("BreakdownPerPax")
case class BreakdownPerPaxForMessage(
    referenceId: Int,
    breakdownId: Long,
    itineraryId: Long,
    bookingType: Option[Int],
    bookingId: Option[Long],
    actionId: Option[Long],
    eventDate: Date,
    itemId: Int,
    typeId: Int,
    taxFeeId: Option[Int],
    quantity: Int,
    localCurrency: String,
    localAmount: Double,
    exchangeRate: Double,
    usdAmount: Double,
    requestedAmount: Option[Double] = None,
    refBreakdownId: Option[Long],
    recStatus: Option[Int] = None,
    recCreatedWhen: Option[Date] = None,
    vendorExchangeRate: Double,
    requestedCurrency: String,
    applyType: Int,
    paxId: Option[Long] = None,
    typeRefId: Option[Long] = None
)

@SchemaName("Payment")
case class PaymentForMessage(
    referenceId: Long,
    paymentId: Long,
    itineraryId: Long,
    actionId: Option[Long],
    creditCardId: Option[Int],
    transactionDate: Date,
    transactionType: Int,
    paymentState: Int,
    referenceNo: String,
    referenceType: Int,
    last4Digits: String,
    paymentMethodId: Int,
    gatewayId: Int,
    transactionId: String,
    paymentCurrency: String,
    paymentAmount: Double,
    amountUsd: Double,
    supplierCurrency: String,
    supplierAmount: Double,
    exchangeRateSupplierToPayment: Double,
    creditCardCurrency: String,
    upliftAmount: Double,
    siteExchangeRate: Double,
    upliftExchangeRate: Double,
    remark: Option[String] = None,
    paymentTypeId: Option[Int],
    token: Option[String],
    recStatus: Option[Int] = None,
    recCreatedWhen: Option[Date] = None,
    referencePaymentId: Option[Long] = None,
    points: Option[Double] = None
)

@SchemaName("BookingPayment")
case class BookingPaymentForMessage(
    paymentId: Long,
    bookingId: Long,
    paymentCurrency: String,
    paymentAmount: Double,
    amountUsd: Double,
    recStatus: Option[Int] = None,
    recCreatedWhen: Option[Date] = None,
    fxiUplift: Option[Double] = None,
    loyaltyPoints: Option[Double] = None,
    supplierCurrency: Option[String] = None,
    supplierExchangeRate: Option[Double] = None,
    bookingPaymentId: Option[Long]
)

@SchemaName("BaseBookingRelationship")
case class BaseBookingRelationshipForMessage(
    sourceBookingId: Long,
    targetBookingId: Long,
    relationshipStatusId: Int,
    relationshipTypeId: Int,
    recStatus: Int,
    recCreatedWhen: Date,
    recCreatedBy: String,
    recModifiedWhen: Option[Date],
    recModifiedBy: Option[String],
    relationshipId: Long
)

@SchemaName("ItineraryHistory")
case class ItineraryHistoryForMessage(
    actionId: ActionId,
    itineraryId: Long,
    bookingType: Option[Int],
    bookingId: Option[Long],
    actionType: Int,
    version: Int,
    actionDate: Date,
    parameters: String,
    description: String,
    recStatus: Option[Int] = None,
    recCreatedWhen: Option[Date] = None,
    replicatedFromDC: Option[String] = None
)

@SchemaName("FlightItinerary")
case class FlightItineraryForMessage(
    itineraryId: Long,
    memberId: Int,
    recStatus: Option[Int] = None,
    recCreatedWhen: Option[Date] = None,
    recModifiedWhen: Option[Date] = None
)

@SchemaName("FlightPaxTicketState")
case class FlightPaxTicketStateForMessage(
    ticketNumberId: Long,
    flightPaxId: Long,
    ticketNumber: String,
    ticketType: String, // E or P
    recStatus: Option[Int],
    recCreatedWhen: Option[Date] = None,
    recModifiedWhen: Option[Date] = None,
    flightSegmentId: Option[Long]
)

@SchemaName("FlightSummary")
case class FlightSummaryForMessage(
    bookingId: Long,
    currency: String,
    baseFare: Double,
    taxAndFee: Double,
    baseDiscount: Double,
    campaignDiscount: Double,
    totalFare: Double,
    recStatus: Option[Int] = None,
    recCreatedWhen: Option[Date] = None,
    recModifiedWhen: Option[Date] = None
)

@SchemaName("UserAgentState")
case class UserAgentStateForMessage(
    flightBookingId: Long,
    origin: String,
    osName: String,
    osVersion: String,
    browserName: String,
    browserLanguage: String,
    browserVersion: String,
    browserSubVersion: String,
    browserBuildNumber: String,
    deviceBrand: String,
    deviceModel: String,
    deviceTypeId: Int = 0,
    isMobile: Option[Boolean] = None,
    isTouch: Option[Boolean] = None,
    additionalInfo: Option[String] = None,
    recStatus: Option[Int] = None,
    recCreatedWhen: Option[Date] = None,
    recModifiedWhen: Option[Date] = None
)

@SchemaName("BookingAttributionState")
case class BookingAttributionStateForMessage(
    modelId: Int,
    tag: String,
    siteId: Int,
    clickDateTime: Option[Date],
    additionalData: String,
    bookingId: Option[Long],
    createdWhen: Option[Date]
)

@SchemaName("ProtectionModel")
case class ProtectionModelForMessage(
    protectionBookingId: Long,
    itineraryId: Long,
    protectionBookingStateId: Int,
    protectionTypeId: Int,
    priceAmount: Double,
    priceAmountUSD: Double,
    marginAmount: Double,
    marginAmountUSD: Double,
    marginPercentage: Double,
    currency: String,
    supplierId: Int,
    subSupplierId: Int,
    supplierSearchId: String,
    supplierPolicyId: Option[String],
    supplierResultCode: Option[Int],
    supplierSpecificData: Option[String],
    accountingEntity: Option[AccountingEntityForMessage],
    multiProductId: Option[Long],
    platformId: Option[Int],
    languageId: Option[Int],
    serverName: Option[String],
    cid: Option[Int],
    sessionId: String,
    clientIpAddress: String,
    trackingCookingId: Option[String],
    trackingCookieDate: Option[Date],
    trackingTag: Option[String],
    recStatus: Option[Int],
    recCreatedWhen: Option[Date],
    recModifiedWhen: Option[Date],
    whitelabelId: Int,
    requestId: String,
    correlationId: String,
    version: Option[Int],
    protectionProductBooking: Seq[ProtectionProductBookingForMessage],
    tripCostAmount: Option[Double],
    tripCostCurrency: Option[String],
    financialBreakdowns: Seq[BreakdownForMessage],
    bookingPayments: Option[Seq[BookingPaymentForMessage]],
    paymentModel: Int,
    tripStartDate: Option[Date],
    tripEndDate: Option[Date],
    commonBookingInfo: Option[CommonBookingInfoForMessage],
    commonBookingEventsInfo: Option[CommonBookingEventsInfoForMessage],
    protectionCfar: Option[ProtectionCfarForMessage]
)

@SchemaName("ProtectionCfar")
case class ProtectionCfarForMessage(
    protectionBookingId: Long,
    coveragePercentage: Double,
    coverageAmount: Double,
    currency: String,
    claimUrl: Option[String],
    recStatus: Int,
    recCreatedWhen: Option[Date],
    recModifiedWhen: Option[Date]
)
@SchemaName("ProtectionProductBooking")
case class ProtectionProductBookingForMessage(
    productBookingId: Long,
    protectionBookingId: Long,
    productType: Int,
    recStatus: Option[Int],
    recCreatedWhen: Option[Date],
    recModifiedWhen: Option[Date]
)

@SchemaName("MultiProductInfo")
case class MultiProductInfoForMessage(
    multiProductId: Long,
    multiProductType: Int
)

@SchemaName("FareRulePolicy")
case class FareRulePolicyForMessage(
    policyId: Long,
    paxType: String,
    flightBookingId: Long,
    fareRulePolicyType: Int,
    allowed: Boolean,
    penalty: Double,
    currencyCode: String,
    validUntilHoursBeforeBoarding: Int,
    validFromHoursBeforeBoarding: Int,
    recCreatedWhen: Option[Date],
    recModifyWhen: Option[Date],
    sliceId: Option[Long],
    segmentId: Option[Long],
    source: Option[String],
    cmsMappingValue: Option[String]
)

@SchemaName("FlightSeatSelectionDetail")
case class FlightSeatSelectionDetailForMessage(
    referenceId: Int,
    segmentReferenceId: Int,
    flightSeatSelectionId: Long,
    flightSegmentId: Long,
    flightSegmentIndex: Int,
    flightPaxId: Long,
    seatRow: String,
    seatColumn: String,
    flightSeatState: Int,
    recStatus: Int,
    recCreatedWhen: Option[Date],
    recModifiedWhen: Option[Date],
    priceAmount: Option[Double],
    priceCurrency: Option[String],
    supplierData: Option[String],
    isPartialSettlementRequired: Option[Boolean]
)

@SchemaName("Vehicles")
final case class VehiclesForMessage(
    vehicleBooking: VehicleBookingForMessage,
    vehicleBookingLocation: VehicleBookingLocationsForMessage,
    vehicleBookingSummary: VehicleBookingSummaryForMessage,
    vehicleBookingTrip: VehicleBookingTripForMessage,
    vehicleFinancialBreakdowns: Seq[BreakdownForMessage],
    @deprecated
    vehicleBookingExtraOffers: Seq[VehicleBookingExtraOfferMessage] = Seq.empty,
    vehicleBookingCancellation: Option[VehicleBookingCancellationMessage],
    vehicleInfo: Option[VehicleInfoMessage]
)

@SchemaName("VehicleBookingLocations")
final case class VehicleBookingLocationsForMessage(
    pickUp: VehicleBookingLocationForMessage,
    dropOff: VehicleBookingLocationForMessage
)

@SchemaName("VehicleBookingLocation")
final case class VehicleBookingLocationForMessage(
    vehicleBookingLocationId: Long,
    vehicleBookingId: Long,
    countryId: Long,
    cityId: Long,
    addressLine: String,
    postalCode: String,
    airportCode: Option[String],
    locationName: String,
    isAirport: Boolean,
    airportProviderLocation: Option[String],
    extraInfo: String,
    supplierLocationCode: Option[String],
    phoneNumber: Option[String],
    locationType: Option[String],
    recModifiedWhen: Date
)

@SchemaName("VehicleBookingTrip")
final case class VehicleBookingTripForMessage(
    vehicleBookingId: Long,
    vehicleCode: String,
    vehicleName: String,
    classification: String,
    pickupDatetime: Date,
    dropOffDatetime: Date,
    pickupLocationId: Long,
    dropOffLocationId: Long,
    driverAge: Int,
    flightNo: Option[String],
    recModifiedWhen: Date,
    supplierConfirmationCode: Option[String],
    customerAgeGroup: Option[String],
    securityDepositType: Option[String],
    localRenter: Option[String]
)

@SchemaName("VehicleBookingSummary")
final case class VehicleBookingSummaryForMessage(
    vehicleBookingId: Long,
    displayCurrency: String,
    totalSurcharge: Double,
    surchargeDetails: String,
    baseDiscount: Double,
    campaignDiscount: Double,
    totalFare: Double,
    agodaFee: Double,
    recModifiedWhen: Date,
    recStatus: Int,
    policyChargeDetails: Option[String],
    paymentModel: Option[Int],
    baseFare: Option[Double],
    taxAndFee: Option[Double],
    extraChargeDetails: Option[String],
    postBookingMetadata: Option[String]
)

@SchemaName("VehicleBooking")
final case class VehicleBookingForMessage(
    vehicleBookingId: Long,
    itineraryId: Long,
    multiProductId: Option[Long],
    bookingDate: Date,
    paymentModel: Int,
    displayCurrency: String,
    supplierId: Int,
    providerCode: String,
    supplierBookingId: String,
    supplierSpecificData: String,
    supplierStatusCode: Option[String],
    supplierCommissionAmount: Double,
    supplierCommissionPercentage: Double,
    whitelabelId: Int,
    isCancelled: Boolean,
    cancellationPolicy: String,
    cancellationDate: Option[Date],
    fraudScore: Option[Int],
    fraudAction: Option[Int],
    fraudCheckIp: String,
    storefrontId: Int,
    platformId: Option[Int],
    languageId: Option[Int],
    serverName: Option[String],
    cid: Option[Int],
    searchId: Option[Long],
    searchRequestId: String,
    sessionId: String,
    clientIpAddress: String,
    trackingCookieId: Option[String],
    trackingCookieDate: Option[Date],
    trackingTag: Option[String],
    recModifiedWhen: Date,
    recStatus: Int,
    vehicleStateId: Int,
    rejectReasonMessage: Option[String],
    rejectReasonCode: Option[Int],
    accountingEntityStr: Option[String],
    postBookingStateId: Option[Int],
    commonBookingInfo: Option[CommonBookingInfoForMessage] = None,
    commonBookingEventsInfo: Option[CommonBookingEventsInfoForMessage] = None
)

@SchemaName("VehicleBookingExtraOffer")
@deprecated
final case class VehicleBookingExtraOfferMessage(
    vehicleExtraOfferId: Long,
    vehicleBookingId: Long,
    extraOfferType: String,
    providerCode: String,
    supplierProductCode: String,
    supplierReferenceCode: String,
    preBookingExtraData: Option[String],
    postBookingExtraData: Option[String],
    totalPrice: Option[Double],
    currency: Option[String],
    quantityBooked: Int,
    isInclude: Boolean,
    recStatus: Int,
    recCreatedWhen: Option[Date],
    recModifiedWhen: Date
)

@SchemaName("VehicleBookingCancellation")
final case class VehicleBookingCancellationMessage(
    vehicleCancellationId: Long,
    vehicleBookingId: Long,
    actionId: Long,
    cancellationReason: String,
    isForcedRefunded: Boolean,
    forcedRefundReason: Option[String],
    isPastDeparted: Boolean,
    cancelledOnSupplier: Boolean,
    isSupplierAcceptedRefund: Boolean,
    supplierErrorMessage: Option[String],
    recStatus: Int,
    recCreatedWhen: Option[Date],
    recModifiedWhen: Date,
    recCreatedBy: Option[String],
    recModifiedBy: String
)
@SchemaName("VehicleInfo")
final case class VehicleInfoMessage(
    vehicleInfoId: Long,
    vehicleBookingId: Long,
    vehicleCode: String,
    vehicleName: String,
    vehicleClassification: String,
    vehicleDoors: Option[Int],
    vehicleSeats: Option[Int],
    vehicleSuitcases: Option[Int],
    vehicleTransmission: Option[String],
    vehicleIsAircon: Option[Boolean],
    vehicleIsAirbag: Option[Boolean],
    vehicleFuelType: Option[String],
    recCreatedWhen: Option[Date],
    recModifiedWhen: Date,
    vehicleMileagePolicy: Option[VehicleMileagePolicyMessage],
    vehicleFuelPolicy: Option[VehicleFuelPolicyMessage],
    imageUrl: Option[String],
    pickUpSupplierOperationHours: Option[Seq[OperationScheduleMessage]],
    dropOffSupplierOperationHours: Option[Seq[OperationScheduleMessage]],
    providerIconUrl: Option[String],
    acrissCode: Option[String]
)

final case class OperationScheduleMessage(
    day: String,
    operationHours: Seq[VehicleSupplierOperationHourMessage]
)

final case class VehicleSupplierOperationHourMessage(from: String, to: String)

final case class VehicleMileagePolicyMessage(
    freeDistance: Double,
    code: String,
    description: String,
    charge: Option[VehiclePolicyChargeMessage],
    isFreeCoverage: Boolean
)

object VehicleMileagePolicyMessage {
  def empty: VehicleMileagePolicyMessage = VehicleMileagePolicyMessage(0, "", "", None, isFreeCoverage = false)
}
final case class VehicleFuelPolicyMessage(
    coverageType: String,
    code: String,
    description: String,
    charge: Option[VehiclePolicyChargeMessage],
    isFreeCoverage: Boolean
)

object VehicleFuelPolicyMessage {
  def empty: VehicleFuelPolicyMessage = VehicleFuelPolicyMessage("", "", "", None, isFreeCoverage = false)
}

final case class VehiclePolicyChargeMessage(unit: String, perUnit: Long, amount: Double, currency: String)

@SchemaName("MultiProductBookingGroupModelMessage")
final case class MultiProductBookingGroupModelMessage(
    bookingId: Long,
    itineraryId: Long,
    cartId: Long,
    packageId: Option[Long] = None
)

@SchemaName("Activity")
final case class ActivityForMessage(activityState: String, bookingId: Long)

@SchemaName("Property")
final case class PropertyForMessage(propertyState: String, bookingId: Long, stateId: Int)

@SchemaName("CegFastTrack")
final case class CegFastTrackForMessage(cegFastTrackState: String, bookingId: Long)

@SchemaName("AddOn")
final case class AddOnForMessage(addOnState: String, bookingId: Long, productTypeId: Int)

@SchemaName("FlightBrandSelectionForMessage")
final case class FlightBrandSelectionForMessage(
    flightBrandSelectionId: Long,
    flightSliceId: Long,
    brandName: String,
    isUpsell: Boolean,
    upsellMargin: Double,
    upsellPrice: Double,
    upsellCurrency: String,
    recStatus: Option[Int],
    recCreatedWhen: Option[Date],
    recModifiedWhen: Option[Date],
    scope: Int
)

@SchemaName("FlightBrandAttributeForMessage")
final case class FlightBrandAttributeForMessage(
    flightBrandAttributeId: Long,
    flightBrandSelectionId: Long,
    brandAttributeType: String,
    brandAttributeInclusion: Int,
    brandAttributeDetailType: Option[String],
    brandAttributeDetailInclusion: Option[Int],
    recStatus: Option[Int],
    recCreatedWhen: Option[Date],
    recModifiedWhen: Option[Date]
)

@SchemaName("FlightBrandSelection")
@Deprecated
final case class FlightBrandSelectionMessage(
    flightBrandSelectionId: Long,
    flightSliceId: Long,
    brandName: String,
    isUpsell: Boolean,
    upsellMargin: Double,
    upsellPrice: Double,
    upsellCurrency: String,
    recStatus: Option[Int],
    recCreatedWhen: Option[Date],
    recModifiedWhen: Option[Date],
    scope: Scope
)

@SchemaName("FlightBrandAttribute")
@Deprecated
final case class FlightBrandAttributeMessage(
    flightBrandAttributeId: Long,
    flightBrandSelectionId: Long,
    brandAttributeType: String,
    brandAttributeInclusion: InclusionType,
    brandAttributeDetailType: Option[String],
    brandAttributeDetailInclusion: Option[InclusionType],
    recStatus: Option[Int],
    recCreatedWhen: Option[Date],
    recModifiedWhen: Option[Date]
)

@SchemaName("FlightBrandAttributeParam")
final case class FlightBrandAttributeParamMessage(
    flightBrandAttributeParamId: Long,
    flightBrandAttributeId: Long,
    name: String,
    code: String,
    recStatus: Option[Int],
    recCreatedWhen: Option[Date],
    recModifiedWhen: Option[Date]
)

@SchemaName("BaseBooking")
final case class BaseBookingMessage(
    bookingId: Long,
    itineraryId: Long,
    multiProductId: Long,
    productId: Long,
    bookingDate: Date,
    bookingStartDate: Date,
    bookingEndDate: Option[Date],
    bookingConfirmationDate: Option[Date],
    isTestBooking: Boolean,
    paymentModel: Int,
    bookingStateId: Int,
    postBookingStateId: Option[Int],
    rejectReasonCode: Option[Int],
    rejectReasonMsg: Option[String],
    recStatus: Int,
    recCreatedWhen: Date,
    recCreatedBy: String,
    recModifiedWhen: Option[Date],
    recModifiedBy: Option[String],
    productTypeId: Int
)

@SchemaName("BaseCancellationInfo")
final case class BaseCancellationInfoMessage(
    bookingId: Long,
    isCancelled: Int,
    cancellationPolicyCode: Option[String],
    cancellationDate: Option[Date],
    voidWindowUntil: Option[Date],
    recCreatedWhen: Option[Date],
    recCreatedBy: Option[String],
    recModifiedWhen: Option[Date],
    recModifiedBy: Option[String],
    rejectReason: Option[String],
    cancellationReason: Option[String],
    supplierCancellationInfo: Option[String],
    cancelledOnSupplier: Option[Boolean],
    isForcedRefund: Option[Boolean],
    forcedRefundReason: Option[String]
)

@SchemaName("BaseBookingEssInfo")
final case class BaseBookingEssInfoMessage(
    bookingEssInfoId: Long,
    bookingId: Long,
    userTaxCountryId: Int,
    recStatus: Int,
    recCreatedWhen: Date,
    recCreatedBy: String,
    recModifiedWhen: Option[Date],
    recModifiedBy: Option[String],
    bookerResidenceCountryId: Option[Int],
    paymentInstrumentCountryId: Option[Int],
    ipAddressCountryId: Option[Int]
)
