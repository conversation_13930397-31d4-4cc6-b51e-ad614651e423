package com.agoda.bapi.common.message.setupBooking

import com.agoda.activity.client.model.{CampaignInfo, ExperimentInfo, ForcedExperiment, InternalContext, PrepareBookingRequest => ActivityPrepareBookingRequest, PrepareBookingRequestParameters, RedeemRequest => ActivityRedeemRequest}
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.{BookingCreationContext, CustomerV2, HotelGuest}
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.message.{BookingCreationFlowContext, DeviceContext, ExperimentData, PropertySearchCriteria}
import com.agoda.bapi.common.model.ChargeOption.ChargeOption
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.cart.{CartContext, CartItemContext}
import com.agoda.bapi.common.model.creation.{FlightsLocalDateTimeDeserializer, FlightsLocalDateTimeSerializer, JavaOptionLocalDateDeserializer, JavaOptionLocalDateSerializer}
import com.agoda.bapi.common.model.enums.serialization.{PointsOfferTypeOptionDeserializer, PointsOfferTypeOptionSerializer, PointsOfferTypeReference}
import com.agoda.bapi.common.model.product.BookingFlow.BookingFlow
import com.agoda.bapi.common.model.product.ProductTypeEnum.ProductTypeEnum
import com.agoda.bapi.common.model.product.{BookingFlow, ProductTypeEnumDeserializer, ProductTypeEnumType}
import com.agoda.bapi.common.token.Money
import com.agoda.bapi.common.util.ChargeOptionHelper
import com.agoda.car.client.models.request.LoyaltyOfferCategory.LoyaltyOfferCategory
import com.agoda.car.client.models.request._
import com.agoda.externalloyalty.client.v2.model.DistributePointsRequest.PointsOfferType
import com.agoda.externalloyalty.client.v2.model.OfferV3Request.SearchType
import com.agoda.papi.ypl.models.SupplierId
import com.fasterxml.jackson.annotation.{JsonIgnore, JsonProperty}
import com.fasterxml.jackson.databind.annotation.{JsonDeserialize, JsonSerialize}
import com.fasterxml.jackson.module.scala.JsonScalaEnumeration
import io.swagger.annotations.{ApiModel, ApiModelProperty, Extension, ExtensionProperty}
import com.agoda.papi.ypl.models.GUIDGeneratorHelper
import com.agoda.papi.ypl.models.OverrideRoomIdentifier

import java.time.{LocalDate, LocalDateTime}
import scala.annotation.meta.field

@ApiModel(parent = classOf[BookingCreationFlowContext])
final case class SetupBookingRequest(
    @ApiModel(description = "Identifies a booking session")
    productsRequest: ProductsRequest,
    @ApiModel(description = "Identifies this particular call chain.")
    @ApiModelProperty(dataType = "String")
    correlationId: Option[String] = None,
    @ApiModel(description = "Payment details")
    paymentRequest: Option[PaymentRequest] = None,
    userContext: Option[UserContext] = None,
    @ApiModel(description = "Customer details for setup booking, person who is making the booking")
    customerInfo: Option[CustomerV2] = None,
    @ApiModel(description = "Agoda cash redeem request from the customer")
    redeemRequest: Option[RedeemRequest] = None,
    @ApiModel(description = "Indicates if there are any features to enable in api.")
    enabledFeatures: Option[Seq[String]] = None,
    @ApiModel(description = "Campaign Information from the customer")
    campaignInfo: Option[CampaignInfoRequest] = None,
    @ApiModel(description = "Auto Apply CreditCard Promotion Info")
    autoApplyCreditCardPromotionInfo: Option[AutoApplyCreditCardPromotionInfo] = None,
    @ApiModelProperty(
      value =
        "A unique identifier for each polling session. It is also shared with other services to aid investigation."
    )
    override val pollingId: Option[String] = None,
    override val deviceContext: Option[DeviceContext] = None,
    override val bookingContext: Option[BookingCreationContext] = None,
    @ApiModel(description = "Campaign Information from the customer")
    loyaltyRequest: Option[LoyaltyRequest] = None,
    addOnRequests: Option[Seq[AddOnRequestItem]] = None,
    addOnRequestsV2: Option[Seq[AddOnRequestItemV2]] = None,
    @ApiModel(description = "Information related to agent assisted booking flow")
    aabInfo: Option[AabInfo] = None,
    @ApiModel(description = "Rebook and Cancel request")
    rebookAndCancelRequest: Option[RebookAndCancelRequest] = None,
    @ApiModel(description = "Indicates if request is for payment retry attempt")
    @ApiModelProperty(dataType = "Boolean")
    isRetryPayment: Option[Boolean] = None
) extends BookingCreationFlowContext {
  def withMaskedBookingToken(): SetupBookingRequest =
    this.copy(
      productsRequest = productsRequest.copy(
        bookingToken = productsRequest.bookingToken.map(
          _.copy(
            token = "****"
          )
        )
      )
    )

  def hasAddOnRequest: Boolean =
    addOnRequests.exists(_.nonEmpty) || addOnRequestsV2.exists(_.nonEmpty)
}

final case class PaymentRequest(
    @ApiModel(description = "Credit card BIN (First 6 digits)")
    @ApiModelProperty(dataType = "String")
    ccBin: Option[String] = None,
    @ApiModel(description = "Credit card on file ID")
    @(ApiModelProperty @field)(dataType = "Long")
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    ccId: Option[Long] = None,
    @ApiModel(description = "Selected payment for booking, can be empty")
    @(ApiModelProperty @field)(dataType = "Int")
    selectedPaymentMethod: Option[Int] = None,
    @ApiModel(description = "Selected charge currency for booking, can be empty")
    @(ApiModelProperty @field)(dataType = "String")
    selectedChargeCurrency: Option[CurrencyCode] = None,
    @ApiModel(description = "Credit card token")
    @ApiModelProperty(dataType = "String")
    ccToken: Option[String] = None,
    @ApiModel(description = "Selected installment plan descriptor")
    @ApiModelProperty(dataType = "String")
    installmentPlanCode: Option[String] = None,
    @ApiModel(description = "Allows to request data on third party installment providers.")
    @ApiModelProperty(dataType = "Boolean")
    includeThirdPartyInstallmentProviders: Option[Boolean] = None,
    @ApiModel(description = "Seq of charge options to get installment data")
    @(ApiModelProperty @field)(dataType = "List[Int]")
    chargeOptionMask: Option[Seq[Int]] = None
)

final case class ProductsRequest(
    bookingToken: Option[TokenMessage] = None,
    propertyRequests: Seq[PropertyRequestItem] = Seq.empty,
    flightRequests: Seq[FlightRequestItem] = Seq.empty,
    @JsonProperty(value = "carRequests")
    @ApiModelProperty(name = "carRequests", accessMode = ApiModelProperty.AccessMode.READ_WRITE)
    carRequestsOpt: Option[Seq[CarRequestItem]] = None,
    tripProtectionRequests: Option[Seq[TripProtectionRequestItem]] = None,
    tripProtectionRequestsV2: Option[Seq[TripProtectionRequestV2Item]] = None,
    packageRequest: Option[PackageRequest] = None,
    activityRequests: Option[Seq[ActivityRequestItem]] = None,
    @(ApiModelProperty @field)(dataType = "Int", value = "priceDisplay Version understood by the Client")
    priceDisplayVersion: Option[Int] = None,
    cartContext: Option[CartContext] = None,
    cartPricingContext: Option[CartPricingContext] = None
) {
  def hasProperty: Boolean         = propertyRequests.nonEmpty
  def hasFlight: Boolean           = flightRequests.nonEmpty
  def roomIdentifiers: Seq[String] = propertyRequests map (_.propertySearchCriteria.roomIdentifier)

  @(ApiModelProperty @field)(hidden = true)
  @JsonIgnore()
  def getCartToken: Option[String] = cartPricingContext.flatMap(_.token)

  def shouldSendCartRequest: Boolean = cartPricingContext.nonEmpty

  /**
    * Parse roomidentifier into readable format using DF helper method. will default to None if roomidentifier is
    * invalid
    */
  def supplierIdFromRoomUid(): Option[SupplierId] = {
    def extractSupplierId(roomIdentifier: String): Option[SupplierId] = {
      GUIDGeneratorHelper
        .getRoomIdentifiersFromUid(
          roomIdentifier,
          OverrideRoomIdentifier()
        )
        .map(_.coreFields.supplierId)
    }
    propertyRequests.headOption.flatMap { property =>
      extractSupplierId(
        property.propertySearchCriteria.roomIdentifier
      ) orElse property.propertySearchCriteria.simplifiedRoomSelectionRequest.flatMap(req =>
        extractSupplierId(req.roomIdentifier)
      )
    }
  }

  def roomCount(): Int =
    propertyRequests.lastOption.map(_.propertySearchCriteria.occupancyRequest.roomCount).getOrElse(0)
  def lengthOfStay(): Int =
    propertyRequests.lastOption.map(_.propertySearchCriteria.durationRequest.lengthOfStay).getOrElse(0)

  def getChargeOption(
      bookingFlow: BookingFlow
  ): Option[ChargeOption] =
    bookingFlow match {
      case BookingFlow.SingleProperty =>
        ChargeOptionHelper.getChargeOptionForSingleHotel(
          propertyRequests.headOption.flatMap(_.payment.map(_.selectedChargeOption)),
          propertyRequests.headOption.flatMap(
            _.propertySearchCriteria.simplifiedRoomSelectionRequest.flatMap(
              _.alternativeOptIn.flatMap(_.crossSellOptIn)
            )
          )
        )
      case BookingFlow.Cart =>
        ChargeOptionHelper.getChargeOptionForCart(
          propertyRequests.flatMap(_.payment.map(_.selectedChargeOption))
        )
      case _ => None // for other funnel we still need to design on how we should aggregate charge option
    }

  @(ApiModelProperty @field)(hidden = true)
  @JsonIgnore()
  def getCarRequestOpt: Option[Seq[CarRequestItem]] = this.carRequestsOpt

  def hasVehicleWithProtection(): Boolean =
    this.carRequestsOpt.exists(carRequest =>
      carRequest.size == 1 && carRequest.headOption.exists(_.tripProtectionRequest.isDefined)
    )

  def productCountWithoutAddons: Int = {
    val propertyCount = propertyRequests.size
    val flightCount   = flightRequests.size
    val carCount      = carRequestsOpt.map(_.size).getOrElse(0)
    val activityCount = activityRequests.map(_.size).getOrElse(0)
    propertyCount + flightCount + carCount + activityCount
  }

  def hasVehicle: Boolean = carRequestsOpt.exists(_.nonEmpty)

  def hasActivity: Boolean = activityRequests.exists(_.nonEmpty)

  @(ApiModelProperty @field)(hidden = true)
  @JsonIgnore()
  def isOnlyFlightRequest: Boolean =
    hasFlight && !hasProperty && !hasVehicle && !hasActivity

  @(ApiModelProperty @field)(hidden = true)
  @JsonIgnore()
  def isOnlySinglePropertyRequest: Boolean =
    hasProperty && !hasFlight && !hasVehicle && !hasActivity && propertyRequests.size == 1

  def hasPackage: Boolean = packageRequest.isDefined

  def hasTripProtection: Boolean =
    tripProtectionRequests.exists(_.nonEmpty) || tripProtectionRequestsV2.exists(_.nonEmpty)

}

final case class CartPricingContext(token: Option[String], previousTotalPrice: Option[Money])

final case class TravellerInfo(passportNumber: String, countryId: Int)

final case class PropertyRequestItem(
    id: String,
    propertySearchCriteria: PropertySearchCriteria,
    payment: Option[ProductPaymentRequest] = None,
    confirmPriceRequest: Option[PropertyConfirmPriceRequest] = None,
    cartItemContext: Option[CartItemContext] = None,
    guests: Option[Seq[HotelGuest]] = None,
    consumerFintechRequest: Option[ConsumerFintechRequest] = None
) {
  def hasNumberOfExtraBedsChanged: Boolean =
    propertySearchCriteria.roomSelectionRequest.exists(rs =>
      rs.requestExtraBedForRoomNumbers.size != rs.mandatoryExtraBed
    )
  def hasNumberOfExtraBedsChangedInSimplifiedRoomSelectionReq: Boolean =
    propertySearchCriteria.simplifiedRoomSelectionRequest.exists(rs => rs.requestExtraBedForRoomNumbers.nonEmpty)
}

final case class PropertyConfirmPriceRequest(
    enabledFeatures: Option[Seq[String]] = None
)

final case class ConsumerFintechRequest(
    product: ConsumerFintechProductRequest
)

final case class ConsumerFintechProductRequest(
    cancelAndRebook: Option[ConsumerFintechCancelAndRebookProductRequest]
)

final case class ConsumerFintechCancelAndRebookProductRequest(tokenMessage: TokenMessage)

final case class FlightRequestItem(
    id: Option[String],
    confirmPriceRequest: Option[ConfirmPriceRequest],
    instantPriceConfirmRequest: Option[InstantPriceConfirmRequest],
    payment: Option[ProductPaymentRequest] = None,
    paymentInfo: Option[FlightsPaymentInfo] = None,
    cartItemContext: Option[CartItemContext] = None
)

final case class FlightsPaymentInfo(
    @ApiModel(description = "Add convenience fee separately")
    addConvenienceFee: Boolean = true
)

final case class TripProtectionRequestItem(
    id: String,
    @JsonDeserialize(using = classOf[ProductTypeEnumDeserializer])
    @JsonScalaEnumeration(classOf[ProductTypeEnumType])
    @(ApiModelProperty @field)(
      dataType = "com.agoda.bapi.common.model.product.ProductTypeEnum$",
      required = true,
      extensions = Array(
        new Extension(
          name = "ms-enum",
          properties = Array(
            new ExtensionProperty(name = "name", value = "ProductTypeEnum"),
            new ExtensionProperty(name = "modelAsString", value = "false")
          )
        )
      )
    )
    tripProtectionType: ProductTypeEnum,
    @ApiModel(description = "Type of the option no selection(0), decline(1), accept(2)")
    @ApiModelProperty(dataType = "Integer")
    optInValue: Int,
    latestSupportedVersion: Option[String] = None
)

final case class TripProtectionRequestV2Item(
    protectionId: String,
    selectedOptionIds: Seq[String]
)

final case class CarRequestItem(
    id: String,
    confirmPriceRequest: CarConfirmPriceRequest,
    cartItemContext: Option[CartItemContext] = None,
    tripProtectionRequest: Option[TripProtectionRequestItem] = None
)

final case class ConfirmPriceRequest(
    itineraryId: String,
    searchToken: String,
    enabledFeatures: Option[Seq[String]] = None,
    passengers: Option[Seq[AncillaryPassengerDetails]] = None,
    addOns: Option[Seq[FlightAddOn]] = None,
    brands: Option[Seq[FlightBrand]] = None,
    requestSource: Option[String] = None
)

final case class InstantPriceConfirmRequest(
    passengers: Vector[SearchRequestPassengers] = Vector.empty,
    trips: Vector[InstantSearchRequestTrip] = Vector.empty,
    externalTrackingId: Option[String] = None
)

final case class FlightBrand(
    scope: String,
    scopeRefId: Int,
    brandId: String
)

final case class FlightAddOn(
    passengerId: Int,
    @deprecated("Please use this value in FlightSeatSelection")
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    segmentId: Option[Int] = None,
    @deprecated("Please use seats instead")
    seat: Option[FlightSeatSelection] = None,
    seats: Option[Seq[FlightSeatSelection]] = None,
    baggage: Option[Seq[BaggageSelection]] = None
)

final case class TokenFlightAddOn(
    passenger: AncillaryPassengerDetails,
    @deprecated("Please use this value in FlightSeatSelection")
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    segmentId: Option[Int] = None,
    @deprecated("Please use seats instead")
    seat: Option[FlightSeatSelection] = None,
    seats: Option[Seq[FlightSeatSelection]] = None,
    baggage: Option[Seq[BaggageSelection]] = None
)

final case class FlightSeatSelection(
    column: String,
    row: String,
    segmentId: Int = 0,
    seatToken: Option[String]
)

final case class BaggageSelection(
    @deprecated("Please use scope and scopeRefId instead, wil be removed in SKYA-3621")
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    sliceId: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "String", required = false)
    scope: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    scopeRefId: Option[Int] = None,
    baggageTokens: Vector[String] = Vector.empty
)

final case class AncillaryPassengerDetails(
    passengerId: Int,
    passengerType: String,
    @JsonSerialize(using = classOf[JavaOptionLocalDateSerializer])
    @JsonDeserialize(using = classOf[JavaOptionLocalDateDeserializer])
    birthDate: Option[LocalDate] = None
)

final case class SearchRequestPassengers(passengerNum: Int, passengerType: String)

final case class InstantSearchRequestTrip(
    id: Int,
    displayedPrice: Double,
    referralId: String,
    tripType: String,
    slices: Vector[InstantSearchRequestSlice],
    displayedCurrency: Option[String] = None
)

final case class InstantSearchRequestSlice(id: Int, segments: Vector[InstantSearchRequestSegment])

final case class InstantSearchRequestSegment(
    id: Int,
    bookingCode: String,
    flightNumber: Int,
    cabinClass: String,
    airlineCode: String,
    @JsonDeserialize(using = classOf[FlightsLocalDateTimeDeserializer])
    @JsonSerialize(using = classOf[FlightsLocalDateTimeSerializer])
    @(ApiModelProperty @field)(
      extensions = Array(
        new Extension(
          name = "x-date-time",
          properties = Array(new ExtensionProperty(name = "type", value = "local"))
        )
      ),
      example = "2025-07-15T12:00:00"
    )
    departDateTime: LocalDateTime,
    originAirport: String,
    destinationAirport: String
)

final case class ProtectionRequest(itineraryId: String, protectionData: String)

final case class PackageRequest(clientToken: String, interSystemToken: Option[String])

final case class CarConfirmPriceRequest(
    @deprecated("please use searchToken instead") identifier: Option[String] = None,
    searchToken: String,
    miles: Option[CarMileRequest] = None,
    customerPolicyInfo: Option[CarCustomerPolicyInfo] = None
) {
  def toVehiclePrepareBookingRequest(
      context: Option[VehicleRequestContext],
      loyaltyRequestOpt: Option[LoyaltyRequest]
  )(implicit
      requestContext: RequestContext
  ): PrepareBookingRequest = {
    PrepareBookingRequest(
      token = searchToken,
      context = Context(
        requestedCurrency = context.map(context => context.requestedCurrency).getOrElse("USD"),
        loyaltyOffersRequest = loyaltyRequestOpt.map { loyaltyRequest =>
          LoyaltyOffersRequest(
            loyaltyOfferCategory = getLoyaltySearchType(loyaltyRequest),
            loyaltyPartnerClaim = loyaltyRequest.partnerClaimToken,
            points = loyaltyRequest.points,
            loyaltySplitTenderId = None
          )
        },
        memberId = context.flatMap(context => context.memberIdOpt)
      ),
      miles = miles.map(_.toPrepareBookingMiles),
      experimentInfo = None,
      extraOffers = None,
      customerPolicyInfo = customerPolicyInfo.map(_.toPrepareBookingCustomerPolicyInfo)
    )
  }

  private def getLoyaltySearchType(
      loyaltyRequest: LoyaltyRequest
  )(implicit
      requestContext: RequestContext
  ): LoyaltyOfferCategory = {
    val mapCorrectLoyaltySearchTypeExp = requestContext.featureAware.exists(_.mapCorrectLoyaltySearchType)
    loyaltyRequest.loyaltySearchType.map(_.toUpperCase) match {
      case _ if !mapCorrectLoyaltySearchTypeExp => LoyaltyOfferCategory.BURN
      case Some("EARN")                         => LoyaltyOfferCategory.EARN
      case Some("BURNEARN") | Some("BURN_EARN") => LoyaltyOfferCategory.BURNEARN
      case _                                    => LoyaltyOfferCategory.BURN
    }
  }
}

final case class RedeemRequest(redeemAmount: BigDecimal, cashbackRedeemAmount: Option[BigDecimal] = None)

final case class CampaignInfoRequest(
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    id: Option[Int],
    cid: Int,
    promotionCode: String
)

final case class AutoApplyCreditCardPromotionInfo(
    campaigns: List[CreditCardPromotionCampaign] = List.empty
)

final case class CreditCardPromotionCampaign(
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    cid: Option[Int],
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    campaignId: Option[Int]
)

final case class CarMileRequest(amountInUsd: Double, meta: String) {
  def toPrepareBookingMiles: Miles =
    Miles(
      amountInUsd = amountInUsd,
      meta = meta
    )
}

final case class CarCustomerPolicyInfo(
    @(ApiModelProperty @field)(dataType = "String", required = false)
    driverAgeGroup: Option[String],
    @(ApiModelProperty @field)(dataType = "String", required = false)
    securityDepositOption: Option[String],
    @(ApiModelProperty @field)(dataType = "String", required = false)
    localRenter: Option[String]
) {
  def toPrepareBookingCustomerPolicyInfo: CustomerPolicyInfo =
    CustomerPolicyInfo(
      driverAgeGroup = driverAgeGroup,
      securityDepositOption = securityDepositOption,
      localRenter = localRenter
    )
}

final case class ProductPaymentRequest(
    @ApiModel(description = "Indicates charge option (paynow/paylater/payAtCheckIn).")
    @JsonSerialize(using = classOf[ChargeOptionSerializer])
    @JsonDeserialize(using = classOf[ChargeOptionAsIntDeserializer])
    @JsonScalaEnumeration(classOf[ChargeOptionType])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    selectedChargeOption: ChargeOption
)

final case class ActivityRequestItem(
    id: String,
    confirmPriceRequest: ActivityConfirmPriceRequest,
    cartItemContext: Option[CartItemContext] = None
)

final case class ActivityConfirmPriceRequest(
    activityToken: String, // include date, time slot, pax mix, offer id, activity id
    redeemLoyaltySplitTenderId: String
) {
  def toActivityPrepareBookingRequest(
      activityRequestContext: ActivityRequestContext,
      loyaltyRequest: Option[LoyaltyRequest],
      experimentData: Option[ExperimentData],
      campaignInfoRequest: Option[CampaignInfoRequest],
      chargeCurrency: CurrencyCode,
      isPmcWidgetEnabled: Boolean = false
  ): ActivityPrepareBookingRequest = {
    val forcedExperimentVariant = experimentData.flatMap(_.forceByVariant)
    val forcedExperiments = experimentData
      .flatMap(_.force)
      .map(_.map(expMapKeyValue => ForcedExperiment(expMapKeyValue._1, expMapKeyValue._2)).toSeq)

    val experimentInfo = ExperimentInfo(forcedExperimentVariant, forcedExperiments)
    ActivityPrepareBookingRequest(
      context = InternalContext(
        currency = chargeCurrency,
        memberId = activityRequestContext.memberId,
        experimentInfo = experimentInfo,
        loyaltyPartnerClaim = loyaltyRequest.flatMap(_.partnerClaimToken),
        skipDownlift = activityRequestContext.skipDownlift
      ),
      prepareBookingRequest = PrepareBookingRequestParameters(
        token = activityToken,
        redeem = Some(
          ActivityRedeemRequest(
            loyaltySplitTenderId = Some(redeemLoyaltySplitTenderId),
            points = loyaltyRequest.flatMap(_.points)
          )
        ),
        campaignInfo =
          if (isPmcWidgetEnabled)
            campaignInfoRequest.map { campaignInfo =>
              CampaignInfo(
                id = campaignInfo.id,
                cid = campaignInfo.cid,
                promotionCode = campaignInfo.promotionCode
              )
            }
          else
            Some(
              CampaignInfo(
                promotionCode = campaignInfoRequest.fold("")(req => req.promotionCode),
                cid = campaignInfoRequest.fold(-1)(req => req.cid),
                id = campaignInfoRequest.fold(None: Option[Int])(req => req.id)
              )
            )
      )
    )
  }
}

final case class LoyaltyRequest(
    partnerClaimToken: Option[String] = None,
    selectedOfferIdentifier: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "Double", required = false)
    points: Option[Double] = None,
    loyaltySearchType: Option[String] = None,
    @JsonSerialize(using = classOf[PointsOfferTypeOptionSerializer])
    @JsonDeserialize(using = classOf[PointsOfferTypeOptionDeserializer])
    @JsonScalaEnumeration(classOf[PointsOfferTypeReference])
    @(ApiModelProperty @field)(dataType = "String", required = false)
    pointsOfferType: Option[PointsOfferType] = None
)

final case class AddOnRequestItem(id: String)
final case class AddOnRequestItemV2(
    id: String,
    choiceIds: Seq[String],
    uniqueOptionId: Option[String]
)

final case class AabInfo(
    @ApiModel(description = "Flight agent assisted booking info")
    flight: Option[FlightAabInfo] = None
)

final case class RebookAndCancelRequest(tokenMessage: TokenMessage)

final case class FlightAabInfo(
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    facilitationFeeWaiverReasonId: Option[Int] = None
)

final case class FeaturesRequest(
    includeMissing: Option[Boolean] = None
)

final case class CegFeeWaiverReasonRequest(
    flights: Seq[Int] = Seq.empty,
    activities: Seq[Int] = Seq.empty,
    properties: Seq[Int] = Seq.empty,
    cars: Seq[Int] = Seq.empty
)
