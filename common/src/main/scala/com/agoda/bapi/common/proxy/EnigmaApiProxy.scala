package com.agoda.bapi.common.proxy

import com.agoda.bapi.common.MessageService
import com.agoda.bapi.common.config.AgodaConfig
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.LanguageScriptType.{<PERSON><PERSON>, <PERSON><PERSON>}
import com.agoda.bapi.common.message.creation.{Customer, FlightPax, HotelGuest}
import com.agoda.bapi.common.model.WhiteLabel._
import com.agoda.bapi.common.model.booking.CommonCustomerContact
import com.agoda.bapi.common.model.car.EnigmaCarDriverInfo
import com.agoda.bapi.common.model.payment.CreditCardBillingInfo
import com.agoda.bapi.common.proxy.DependencyNames.Dependency
import com.agoda.bapi.common.proxy.util.EnigmaApiProxyMaskUtil
import com.agoda.bapi.common.util.{CarServiceModelMapper, CommonWhitelabelU<PERSON>s, JodaToJavaDateTimeConversions, LocalizeNameMapper}
import com.agoda.bapi.execution.context.NonBlockingExecutionContext.default
import com.agoda.capi.enigma.client.booking._
import com.agoda.capi.enigma.client.booking.flight.FlightService
import com.agoda.capi.enigma.client.car.{CarBillingInfoService, CarDriverService}
import com.agoda.capi.enigma.shared_model.booking.contact.{AgodaCustomerContact, CustomerContact, JTBCustomerContact}
import com.agoda.capi.enigma.shared_model.booking.flight.{CreditCardBillingInfo => CapiCreditCardBillingInfo, FlightCustomer => CapiFlightCustomer, FlightCustomerDetails => CapiFlightCustomerDetails, FlightCustomerInformation => CapiFlightCustomerInformation, FlightPassenger => CapiFlightPassenger, FlightPassengers => CapiFlightPassengers, FlightPassengersDetails => CapiFlightPassengersDetails, FrequentFlyer => EnigmaFrequentFlyer}
import com.agoda.capi.enigma.shared_model.booking.pax.{BaseBookingPax, BookingGuest}
import com.agoda.capi.enigma.shared_model.booking.{BookingCustomer, BookingPax, CustomerBillingInformation}
import com.agoda.capi.enigma.shared_model.car.billinginfo.CarBillingInfo
import com.agoda.capi.enigma.shared_model.car.driver.{CarDriver => CapiCarDrivers}
import com.agoda.capi.enigma.shared_model.common.{Metadata, RecStatus}
import com.agoda.capi.enigma.shared_model.itinerary.billinginfo.ItineraryBillingInfo
import com.agoda.winterfell.Try
import com.google.inject.Singleton
import com.typesafe.config.Config
import org.joda.time.format.DateTimeFormat

import java.time.temporal.ChronoUnit
import java.time.{Instant => JavaInstant}
import java.util.UUID
import javax.inject.Inject
import scala.concurrent.Future

trait EnigmaApiProxy extends HttpProxy with ProxyMessage {

  def saveBaseBookingPaxes(paxes: Seq[BaseBookingPax])(implicit
      requestContext: RequestContext
  ): Future[Seq[BaseBookingPax]]

  def getBaseBookingPaxByBookingId(bookingId: Long)(implicit
      requestContext: RequestContext
  ): Future[Seq[BaseBookingPax]]

  def saveFlightPassengers(bookingId: Int, passengers: Seq[FlightPax], ids: Seq[Long])(implicit
      requestContext: RequestContext
  ): Future[CapiFlightPassengers]

  def savePropertyCustomerContact(
      bookingId: Int,
      customer: Customer,
      primaryGuest: HotelGuest,
      whiteLabel: WhiteLabel,
      bookerAnswer: Option[String]
  )(implicit requestContext: RequestContext): Future[BookingCustomer]

  def savePropertyGuests(bookingId: Int, passengers: Seq[HotelGuest], whiteLabel: WhiteLabel)(implicit
      requestContext: RequestContext
  ): Future[Vector[BookingPax]]

  def saveFlightCustomer(
      bookingId: Int,
      creditCardBillingInfo: CreditCardBillingInfo,
      customerInfo: Customer
  )(implicit requestContext: RequestContext): Future[CapiFlightCustomer]

  def saveVehicleDriver(
      bookingId: Long,
      drivers: Seq[EnigmaCarDriverInfo],
      ids: Seq[Long]
  )(implicit requestContext: RequestContext): Future[Vector[CapiCarDrivers]]

  def saveVehicleBillingInfo(
      bookingId: Long,
      creditCardBillingInfo: CreditCardBillingInfo,
      customerInfo: Customer
  )(implicit requestContext: RequestContext): Future[CarBillingInfo]

  val defaultBaseMetadata: Metadata

  def saveCustomerBillingInfo(customer: CustomerBillingInformation)(implicit
      requestContext: RequestContext
  ): Future[CustomerBillingInformation]

  def saveItineraryBillingInfo(itineraryId: Int, entity: String)(implicit
      requestContext: RequestContext
  ): Future[ItineraryBillingInfo]

  override protected def dependency: Dependency = DependencyNames.Enigma

  protected val flightsSuffix: String = "flights"

  protected val vehicleSuffix: String = "vehicle"
}

object EnigmaApiProxy {
  def toCommonCustomerContact(customerContact: CustomerContact): Try[CommonCustomerContact] = {
    Try {
      customerContact match {
        case agoda: AgodaCustomerContact =>
          CommonCustomerContact(
            emailAddress = agoda.emailAddress,
            phoneNumber = agoda.phoneNumber
          )
        case jtb: JTBCustomerContact =>
          CommonCustomerContact(
            emailAddress = Some(jtb.email),
            phoneNumber = jtb.phone
          )
      }
    }
  }
}

@Singleton
class EnigmaApiProxyImpl @Inject() (
    flightClient: FlightService,
    carDriverClient: CarDriverService,
    carBillingClient: CarBillingInfoService,
    propertyGuestClient: BookingService,
    propertyCustomerContractClient: BookingCustomerService,
    baseBookingPaxClient: BaseBookingPaxService,
    config: Config,
    agodaConfig: AgodaConfig,
    customerBillingInfoProxy: CustomerBillingInfoProxy,
    itineraryBillingInfoProxy: ItineraryBillingInfoProxy,
    val messagingService: MessageService
) extends EnigmaApiProxy {

  val startIndex: Int     = 1
  val bapiUuid: UUID      = UUID.fromString(agodaConfig.bapiIdentifierUuid)
  val datePattern: String = "yyyy-MM-dd"

  private[proxy] def localDateToDateTime(localDate: java.time.LocalDate) = {
    val formatter = DateTimeFormat.forPattern(datePattern)
    formatter.parseDateTime(localDate.toString)
  }

  override val defaultBaseMetadata
      : Metadata = // Default enigma metadata ; Ideally should not be sent by client, instead default in Enigma Service
    Metadata(
      status = RecStatus.Active,
      createdBy = bapiUuid,
      createdWhen = JavaInstant.now.truncatedTo(
        ChronoUnit.MILLIS
      ), //  MDC understands only MIlLIS. JDK 11+ defaults to MICROS and it fails
      modifiedBy = None,
      modifiedWhen = None
    )

  override def saveBaseBookingPaxes(
      paxes: Seq[BaseBookingPax]
  )(implicit requestContext: RequestContext): Future[Seq[BaseBookingPax]] =
    withMeasureAndLog(metricName(), getMethodTag("saveBaseBookingPaxes")) {
      runWithReqResMessageLog(
        method = "baseBookingPaxClient.saveBaseBookingPaxes",
        request = Some(paxes),
        maskRequest = EnigmaApiProxyMaskUtil.maskBaseBookingPaxes,
        maskResponse = EnigmaApiProxyMaskUtil.maskBaseBookingPaxes,
        additionalData = Map("modifiedBy" -> bapiUuid.toString)
      )(
        baseBookingPaxClient.saveBaseBookingPaxes(
          paxes,
          bapiUuid,
          whiteLabelId = requestContext.whiteLabelInfo.whiteLabelId.id
        )
      )
    }

  override def getBaseBookingPaxByBookingId(
      bookingId: Long
  )(implicit requestContext: RequestContext): Future[Seq[BaseBookingPax]] =
    withMeasureAndLog(metricName(), getMethodTag("getBaseBookingPaxByBookingId")) {
      runWithReqResMessageLog(
        method = "baseBookingPaxClient.getBaseBookingPaxesByBookingId",
        request = None,
        maskRequest = EnigmaApiProxyMaskUtil.maskBaseBookingPaxes,
        maskResponse = EnigmaApiProxyMaskUtil.maskBaseBookingPaxes,
        additionalData = Map("bookingId" -> bookingId.toString)
      )(baseBookingPaxClient.getBaseBookingPaxesByBookingId(bookingId, requestContext.whiteLabelInfo.whiteLabelId.id))
    }

  override def saveFlightPassengers(
      bookingId: Int,
      passengers: Seq[FlightPax],
      ids: Seq[Long]
  )(implicit requestContext: RequestContext): Future[CapiFlightPassengers] = {
    val capiPassengers = passengers.zip(ids).map {
      case (pax, id) => mapCapiFlightPassenger(pax, id)
    }
    val details = CapiFlightPassengersDetails(capiPassengers)

    withMeasureAndLog(metricName(Some(flightsSuffix)), getMethodTag("saveFlightPassengers")) {
      runWithReqResMessageLog(
        method = "flightClient.saveFlightPassengers",
        request = Some(details),
        maskRequest = EnigmaApiProxyMaskUtil.maskCapiFlightPassengersDetails,
        maskResponse = EnigmaApiProxyMaskUtil.maskCapiFlightPassengers,
        additionalData = Map("modifiedBy" -> bapiUuid.toString, "bookingId" -> bookingId.toString)
      )(
        flightClient.saveFlightPassengers(
          bookingId,
          details,
          modifiedBy = bapiUuid,
          whiteLabelId = requestContext.whiteLabelInfo.whiteLabelId.id
        )
      )
    }
  }

  override def saveFlightCustomer(
      bookingId: Int,
      creditCardBillingInfo: CreditCardBillingInfo,
      customerInfo: Customer
  )(implicit requestContext: RequestContext): Future[CapiFlightCustomer] = {
    val capiCreditCardBillingInfo = mapCapiCreditCardBillingInfo(creditCardBillingInfo)
    val capiFlightCustomerInfo    = mapFlightCapiCustomerInfo(customerInfo)
    val details                   = CapiFlightCustomerDetails(capiCreditCardBillingInfo, Some(capiFlightCustomerInfo))

    withMeasureAndLog(metricName(Some(flightsSuffix)), getMethodTag("saveFlightCustomer")) {
      runWithReqResMessageLog(
        method = "flightClient.saveFlightCustomer",
        request = Some(details),
        maskRequest = EnigmaApiProxyMaskUtil.maskCapiFlightCustomerDetail,
        maskResponse = EnigmaApiProxyMaskUtil.maskCapiFlightCustomer,
        additionalData = Map("modifiedBy" -> bapiUuid.toString, "bookingId" -> bookingId.toString)
      ) {
        flightClient.saveFlightCustomer(
          bookingId,
          details,
          modifiedBy = bapiUuid,
          whiteLabelId = requestContext.whiteLabelInfo.whiteLabelId.id
        )
      }
    }
  }

  private[proxy] def mapCapiFlightPassenger(pax: FlightPax, passengerID: Long): CapiFlightPassenger =
    CapiFlightPassenger(
      passengerID,
      title = Option(pax.title),
      firstName = pax.firstname,
      middleName = Option(pax.middlename),
      lastName = pax.lastname,
      primary = Option(pax.primary),
      birthDate = JodaToJavaDateTimeConversions.toJavaLocalDate(pax.birthDate),
      nationalityId = Option(pax.nationalityId),
      gender = pax.gender,
      passportNumber = pax.passportNumber,
      passportExpires = pax.passportExpires.map(JodaToJavaDateTimeConversions.toJavaLocalDate),
      passportCountry = pax.passportCountryId,
      passengerType = pax.passengerType,
      knownTravelerNumber = pax.knownTravelerNumber,
      frequentFlyer = pax.frequentFlyer
        .map(_.map(fff => EnigmaFrequentFlyer(fff.airlineId, fff.frequentFlyerId)))
        .getOrElse(Seq.empty),
      countryOfResidence = pax.countryOfResidenceId
    )

  private[proxy] def mapPropertyGuest(pax: HotelGuest, bookingId: Int): BookingGuest = {
    // put roomId as 1 to be align with ebe_booking_pax in PropertyModelInternalMapper
    val roomId = pax.roomIndex orElse Some(startIndex)

    BookingGuest(
      bookingId = bookingId,
      guestNo = pax.guestNo,
      nationalityId = Some(pax.nationalityId),
      firstName = pax.firstname,
      middleName = Some(pax.middlename),
      lastName = pax.lastname,
      roomId = roomId,
      kanjiFirstName = LocalizeNameMapper.mapLocalizedNames(pax.localizedNames, Kanji).map(_.firstname),
      kanjiLastName = LocalizeNameMapper.mapLocalizedNames(pax.localizedNames, Kanji).map(_.lastname),
      kanaFirstName = LocalizeNameMapper.mapLocalizedNames(pax.localizedNames, Kana).map(_.firstname),
      kanaLastName = LocalizeNameMapper.mapLocalizedNames(pax.localizedNames, Kana).map(_.lastname),
      age = Some(pax.age),
      gender = pax.gender,
      email = pax.email,
      phoneNumber = pax.phoneFormat,
      emergencyPhoneNumber = pax.emergencyPhone,
      postcode = pax.postcode,
      city = pax.city,
      area = pax.area,
      address = pax.address,
      stateId = pax.stateId
    )
  }

  private[proxy] def mapCustomerContactBasedOnWhiteLabel(
      customer: Customer,
      primaryGuest: HotelGuest,
      whiteLabel: WhiteLabel,
      bookerAnswer: Option[String]
  )(implicit requestContext: RequestContext): CustomerContact = {
    val isJtbSupplyWl = CommonWhitelabelUtils.isJtbSupplyWl(requestContext.whiteLabelInfo)
    if (isJtbSupplyWl) {
      JTBCustomerContact(
        phone = Some(customer.phoneFormat),
        emergencyPhone = customer.emergencyPhoneNumber,
        email = customer.email,
        title = Some(customer.title),
        firstName = customer.firstname,
        middleName = Some(customer.middlename),
        lastName = customer.lastname,
        kanjiFirstName = LocalizeNameMapper.mapLocalizedNames(customer.localizedNames, Kanji).map(_.firstname),
        kanjiLastName = LocalizeNameMapper.mapLocalizedNames(customer.localizedNames, Kanji).map(_.lastname),
        kanaFirstName = LocalizeNameMapper.mapLocalizedNames(customer.localizedNames, Kana).map(_.firstname),
        kanaLastName = LocalizeNameMapper.mapLocalizedNames(customer.localizedNames, Kana).map(_.lastname),
        nationalityId = Some(customer.countryId),
        citizenshipId = primaryGuest.citizenshipId,
        address1 = Some(customer.address1),
        address2 = Some(customer.address2),
        postalCode = Some(customer.postcode),
        region = Some(customer.region),
        countryId = Some(customer.countryId),
        stateId = customer.stateId,
        city = Some(customer.city),
        area = Some(customer.area),
        age = customer.age,
        gender = customer.gender,
        whiteLabelId = Some(whiteLabel.id),
        bookerAnswer = bookerAnswer
      )
    } else {
      AgodaCustomerContact(
        phoneNumber = Some(customer.phoneFormat),
        emailAddress = Some(customer.email),
        guestEmail = primaryGuest.email.flatMap(email => if (email.isEmpty) None else Some(email)),
        b2bCustomerEmail = customer.b2bCustomerEmail,
        title = Some(customer.title),
        firstName = Some(customer.firstname),
        middleName = Some(customer.middlename),
        lastName = Some(customer.lastname),
        citizenshipId = primaryGuest.citizenshipId,
        countryId = Some(customer.countryId)
      )
    }
  }

  private[proxy] def mapFlightCapiCustomerInfo(customerInfo: Customer): CapiFlightCustomerInformation =
    CapiFlightCustomerInformation(
      Some(customerInfo.title),
      Some(customerInfo.firstname),
      Some(customerInfo.middlename),
      Some(customerInfo.lastname),
      Some(customerInfo.email),
      Some(customerInfo.countryId),
      Some(customerInfo.phoneFormat)
    )

  private[proxy] def mapCapiCreditCardBillingInfo(
      creditCardBillingInfo: CreditCardBillingInfo
  ): CapiCreditCardBillingInfo =
    CapiCreditCardBillingInfo(
      creditCardBillingInfo.billingAddress1,
      creditCardBillingInfo.billingAddress2,
      creditCardBillingInfo.billingPostalcode,
      creditCardBillingInfo.billingCountryId,
      creditCardBillingInfo.billingCity,
      creditCardBillingInfo.billingState
    )

  override def savePropertyGuests(bookingId: Int, passengers: Seq[HotelGuest], whiteLabel: WhiteLabel)(implicit
      requestContext: RequestContext
  ): Future[Vector[BookingPax]] = {
    val affiliateSiteId = requestContext.userContext
      .flatMap(_.experimentData.flatMap(_.cId.flatMap(s => Try(s.toInt).toOption)))
      .getOrElse(-1)
    val capiGuests = passengers.map {
      mapPropertyGuest(_, bookingId)
    }
    if (requestContext.featureAware.exists(_.isUseNewEnigmaInsertEndpoint))
      withMeasureAndLog(metricName(), getMethodTag("insertPropertyGuests")) {
        runWithReqResMessageLog(
          method = "propertyGuestClient.insertBookingPaxes",
          request = Some(capiGuests.toVector),
          maskRequest = EnigmaApiProxyMaskUtil.maskVectorBookingGuest,
          maskResponse = EnigmaApiProxyMaskUtil.maskVectorBookingPax,
          additionalData = Map(
            "modifiedBy"      -> bapiUuid.toString,
            "bookingId"       -> bookingId.toString,
            "affiliateSiteId" -> affiliateSiteId.toString,
            "whiteLabelId"    -> whiteLabel.id.toString
          )
        ) {
          propertyGuestClient.insertBookingPaxes(
            bookingId,
            capiGuests.toVector,
            bapiUuid,
            affiliateSiteId,
            whiteLabel.id
          )
        }
      }
    else
      withMeasureAndLog(metricName(), getMethodTag("savePropertyGuests")) {
        runWithReqResMessageLog(
          method = "propertyGuestClient.saveBookingPaxes",
          request = Some(capiGuests.toVector),
          maskRequest = EnigmaApiProxyMaskUtil.maskVectorBookingGuest,
          maskResponse = EnigmaApiProxyMaskUtil.maskVectorBookingPax,
          additionalData = Map(
            "modifiedBy"      -> bapiUuid.toString,
            "bookingId"       -> bookingId.toString,
            "affiliateSiteId" -> affiliateSiteId.toString,
            "whiteLabelId"    -> whiteLabel.id.toString
          )
        ) {
          propertyGuestClient.saveBookingPaxes(bookingId, capiGuests.toVector, bapiUuid, affiliateSiteId, whiteLabel.id)
        }
      }
  }

  override def savePropertyCustomerContact(
      bookingId: Int,
      customer: Customer,
      primaryGuest: HotelGuest,
      whiteLabel: WhiteLabel,
      bookerAnswer: Option[String]
  )(implicit requestContext: RequestContext): Future[BookingCustomer] = {
    val capiCustomerContact = mapCustomerContactBasedOnWhiteLabel(customer, primaryGuest, whiteLabel, bookerAnswer)
    if (requestContext.featureAware.exists(_.isUseNewEnigmaInsertEndpoint))
      withMeasureAndLog(metricName(), getMethodTag("insertPropertyCustomerContact")) {
        runWithReqResMessageLog(
          method = "propertyCustomerContractClient.insertBookingCustomer",
          request = Some(capiCustomerContact),
          maskRequest = EnigmaApiProxyMaskUtil.maskCustomerContract,
          maskResponse = EnigmaApiProxyMaskUtil.maskBookingContract,
          additionalData = Map("bookingId" -> bookingId.toString, "modifiedBy" -> bapiUuid.toString)
        ) {
          propertyCustomerContractClient.insertBookingCustomer(
            bookingId,
            capiCustomerContact,
            bapiUuid,
            whiteLabelId = requestContext.whiteLabelInfo.whiteLabelId.id
          )
        }
      }
    else
      withMeasureAndLog(metricName(), getMethodTag("savePropertyCustomerContact")) {
        runWithReqResMessageLog(
          method = "propertyCustomerContractClient.saveBookingCustomer",
          request = Some(capiCustomerContact),
          maskRequest = EnigmaApiProxyMaskUtil.maskCustomerContract,
          maskResponse = EnigmaApiProxyMaskUtil.maskBookingContract,
          additionalData = Map("bookingId" -> bookingId.toString, "modifiedBy" -> bapiUuid.toString)
        ) {
          propertyCustomerContractClient.saveBookingCustomer(
            bookingId,
            capiCustomerContact,
            bapiUuid,
            whiteLabelId = requestContext.whiteLabelInfo.whiteLabelId.id
          )
        }
      }
  }

  override def saveVehicleDriver(
      bookingId: Long,
      drivers: Seq[EnigmaCarDriverInfo],
      ids: Seq[Long]
  )(implicit requestContext: RequestContext): Future[Vector[CapiCarDrivers]] = {
    val carDrivers = drivers.zip(ids).map {
      case (driver, id) => driver.toCapiDriverInfo(bookingId, id)
    }

    withMeasureAndLog(metricName(Some(vehicleSuffix)), getMethodTag("saveVehicleDriver")) {
      runWithReqResMessageLog(
        method = "carDriverClient.saveCarDriverService",
        request = Some(carDrivers.toVector),
        maskRequest = EnigmaApiProxyMaskUtil.maskMultiDriverInfo,
        maskResponse = EnigmaApiProxyMaskUtil.maskMultiCarDriver,
        additionalData = Map("bookingId" -> bookingId.toString, "modifiedBy" -> bapiUuid.toString)
      ) {
        carDriverClient.saveCarDriverService(
          bookingId.intValue, // TODO: Update after enigma client
          modifiedBy = bapiUuid,
          whiteLabelId = requestContext.whiteLabelInfo.whiteLabelId.id
        )(carDrivers.toVector)
      }
    }
  }

  override def saveVehicleBillingInfo(
      bookingId: Long,
      creditCardBillingInfo: CreditCardBillingInfo,
      customerInfo: Customer
  )(implicit requestContext: RequestContext): Future[CarBillingInfo] = {
    val request = CarServiceModelMapper.mapCarBillingInfoDetails(creditCardBillingInfo, customerInfo)
    withMeasureAndLog(metricName(Some(vehicleSuffix)), getMethodTag("saveVehicleBillingInfo")) {
      runWithReqResMessageLog(
        method = "carBillingClient.saveCarBillingInfo",
        request = Some(request),
        maskRequest = EnigmaApiProxyMaskUtil.maskCarBillingInfoDetail,
        maskResponse = EnigmaApiProxyMaskUtil.maskCarBillingInfo,
        additionalData = Map("bookingId" -> bookingId.toString, "modifiedBy" -> bapiUuid.toString)
      ) {
        carBillingClient.saveCarBillingInfo(
          bookingId.intValue, // TODO: Update after enigma client
          request,
          modifiedBy = bapiUuid,
          whiteLabelId = requestContext.whiteLabelInfo.whiteLabelId.id
        )
      }
    }
  }

  override def saveCustomerBillingInfo(customer: CustomerBillingInformation)(implicit
      requestContext: RequestContext
  ): Future[CustomerBillingInformation] =
    customerBillingInfoProxy.saveCustomerBillingInfo(customer)

  override def saveItineraryBillingInfo(itineraryId: Int, entity: String)(implicit
      requestContext: RequestContext
  ): Future[ItineraryBillingInfo] =
    itineraryBillingInfoProxy.saveItineraryBillingInfo(itineraryId, entity)
}
