package com.agoda.bapi.common.model.creation

import java.io.IOException
import com.agoda.bapi.common.ProductInfo
import com.agoda.bapi.common.json.{DateOptionWithTZSerializerAndDefaultNull, DateWithTZSerializer}
import com.agoda.bapi.common.message.ChildrenType
import com.agoda.bapi.common.message.SelectedHourlySlot
import com.agoda.bapi.common.message.creation.{BenefitID, PartnerLoyaltyPoint, PaymentAmount, PrebookingId}
import com.agoda.bapi.common.model.CancellationClass.CancellationClass
import com.agoda.bapi.common.model.cart.CartItemContext
import com.agoda.bapi.common.model.consumerFintech.{ConsumerFintechDetail, PropertyBookingConsumerFintech}
import com.agoda.bapi.common.model.creation.AvailabilityType.AvailabilityType
import com.agoda.bapi.common.model.creation.CancellationChargeType.CancellationChargeType
import com.agoda.bapi.common.model.creation.StayType.StayType
import com.agoda.bapi.common.model.creation.common.{AccountingEntity, JsonAccountingEntity}
import com.agoda.bapi.common.model.creation.PaymentModel.PaymentModel
import com.agoda.bapi.common.model.payment.{PaymentModel => PaymentModelPayment}
import com.agoda.bapi.common.model.{CampaignType, CampaignTypeDeserializer, CampaignTypeSerializer, OptionCancellationClassDeserializer, OptionCancellationClassSerialization}
import com.agoda.bapi.common.token.common.ProductTokenKey
import com.agoda.bapi.common.util.JacksonSerializer.{EnumAsIntDeserializer, EnumAsIntSerializer, EnumAsOptStringDeserializer, EnumAsOptStringSerializer}
import com.agoda.content.models.db.rateCategories.RateCategoryCheckTime
import com.agoda.finance.tax.common.Types.SupplierId
import com.agoda.finance.tax.enums.ProductType
import com.agoda.finance.tax.services.tax.AccountingEntityProvider
import com.agoda.mpb.common.models.state.ProductPaymentInfo
import com.fasterxml.jackson.core.{JsonParseException, JsonParser}
import com.fasterxml.jackson.databind._
import com.fasterxml.jackson.databind.annotation.{JsonDeserialize, JsonSerialize}
import io.swagger.annotations.{ApiModel, ApiModelProperty, Extension, ExtensionProperty}
import models.starfruit.{CampaignDiscount, LocalVoucher}
import org.joda.time.{DateTime, LocalDate}
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.write

import java.util.UUID
import com.agoda.zaidel.warts.misc.SwaggerAnnotationsControl
import com.fasterxml.jackson.annotation.JsonIgnore

import scala.annotation.meta.field
import scala.math.BigDecimal

@ApiModel(description = "papiResponse-like BAPI object")
final case class BAPIBooking(
    searchId: String,
    @(ApiModelProperty @field)(dataType = "Long")
    prebookingId: Option[PrebookingId],
    propertyId: Long,
    cityId: Long,
    countryId: Long,
    booking: Option[BookingItem] = None,
    infoSummary: Option[InfoSummary],
    masterRooms: Seq[MasterRoom],
    @deprecated("Use productPayment instead")
    paymentAmount: Option[PaymentAmount] = None,
    campaignInfo: Option[PropertyCampaignInfo] = None,
    // require
    productTokenKey: Option[ProductTokenKey] = None,
    creditCardInfo: Option[CreditCardInfoModel] = None,
    productPayment: Option[ProductPaymentInfo],
    isPriceGuaranteedTokenCleared: Boolean = false, // TODO: Remove field after take MBPSEA-81
    @deprecated("Use alternativeRoomInfo.isCrossSellAvailable")
    isCrossSellAvailable: Boolean = false,
    alternativeRoomInfo: Option[AlternativeRoomInfo] = None,
    requiredGuestContact: Boolean = false,
    roomAllocationItemInfo: Option[RoomAllocationItemInfo] = None,
    cartItemContext: Option[CartItemContext] = None,
    consumerFintechDetails: Option[ConsumerFintechDetail] = None
) extends ProductInfo
    with SwaggerAnnotationsControl {

  def withBooking(newBooking: Option[BookingItem]): BAPIBooking = {
    this.copy(booking = newBooking)
  }

  @(ApiModelProperty @field)(hidden = true)
  @JsonIgnore
  def getRooms(): Seq[BookingRoom] = {
    this.booking.map(_.booking.flatMap(_.hotel.flatMap(_.room))).getOrElse(Seq.empty)
  }

  override def productPaymentModel: PaymentModelPayment.PaymentModel = {
    val bapiBookingPaymentModel: PaymentModel =
      getRooms().headOption.map(_.paymentModels).getOrElse(PaymentModel.Unknown)

    PaymentModelPayment.findByName(bapiBookingPaymentModel.toString).getOrElse(PaymentModelPayment.default)
  }
}

final case class PropertyCampaignInfo(
    campaignId: Int,
    promotionCode: String,
    @JsonDeserialize(using = classOf[CampaignTypeDeserializer])
    @JsonSerialize(using = classOf[CampaignTypeSerializer])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    cType: CampaignType.CampaignType,
    @(ApiModelProperty @field)(dataType = "Int")
    discountType: Option[Int],
    discountAmount: Option[BigDecimal],
    promotionText: Option[String],
    hotelFundingText: Option[String],
    localVoucher: Option[LocalVoucher],
    @(ApiModelProperty @field)(dataType = "Boolean")
    isStateIdRequired: Option[Boolean]
) extends SwaggerAnnotationsControl

final case class CreditCardInfoModel(
    issueBankCountryId: Int,
    @(ApiModelProperty @field)(dataType = "Long")
    ccId: Option[Long],
    ccBin: Option[String],
    @(ApiModelProperty @field)(dataType = "Boolean")
    isNoCvc: Option[Boolean] = None,
    @(ApiModelProperty @field)(dataType = "Boolean")
    isBankNameRequired: Option[Boolean] = None,
    @(ApiModelProperty @field)(dataType = "Boolean")
    isSaveCcofEligible: Option[Boolean] = None
) extends SwaggerAnnotationsControl

final case class BookingItem(
    booking: List[EBEBooking] = List.empty
) {
  def withEbeBooking(newEbeBooking: List[EBEBooking]): BookingItem = {
    this.copy(booking = newEbeBooking)
  }
}

final case class SupplierInfo(
    @ApiModel(description = "email of supplier")
    email: Option[String] = None,
    @ApiModel(description = "phone of supplier")
    phone: Option[String] = None,
    @ApiModel(description = "displayName of supplier")
    supplierDisplayName: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    @ApiModel(description = "dmcType for supplier")
    dmcType: Option[Int] = None
) extends SwaggerAnnotationsControl

final case class InfoSummary(
    /**
      * @deprecated
      *   Bapi does not use this field. We will remove it from the contract in future.
      */
    @Deprecated
    hasHostExperience: Boolean,
    @(ApiModelProperty @field)(dataType = "Boolean", required = true)
    @ApiModel(description = "Use singleRoomNonHotelAccommodation from papi response to map the field")
    isNHA: Option[Boolean], // non hotel accommodation
    @(ApiModelProperty @field)(dataType = "Boolean", required = true)
    @ApiModel(description = "send sms to hotel along with booking confirmation voucher")
    isSendSmsForBooking: Option[Boolean] = None,
    @(ApiModelProperty @field)(dataType = "Boolean", required = true)
    @ApiModel(description = "Use partner branding")
    isGlobalPartnerServiceBranding: Option[Boolean] = None,
    @ApiModel(description = "hotel primary email")
    hotelPrimaryEmail: Option[String] = None,
    @ApiModel(description = "hotel Phone")
    hotelPhone: Option[String] = None,
    @ApiModel(description = "hotel sms")
    hotelSms: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "Boolean")
    @ApiModel(description = "messaging allowed with Booking")
    isMessagingAllowedWithBooking: Option[Boolean] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    @ApiModel(description = "email Display Setting")
    emailDisplaySetting: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "Boolean")
    @ApiModel(description = "show customer info voucher")
    isShowCustomerInfoInVoucher: Option[Boolean] = None,
    @ApiModel(description = "ycs phone")
    ycsPhone: Option[String] = None,
    @ApiModel(description = "ycs email")
    ycsEmail: Option[String] = None,
    @ApiModel(description = "hotel name")
    hotelDisplayName: Option[String] = None,
    @ApiModel(description = "hotel local name")
    hotelLocalName: Option[String] = None,
    @ApiModel(description = "hotel Locale")
    hotelLocale: Option[String] = None,
    @ApiModel(description = "hotel default name")
    hotelDefaultName: Option[String] = None
) extends SwaggerAnnotationsControl

final case class MasterRoom(
    typeId: Long,
    childrenRooms: List[ChildRoom],
    @(ApiModelProperty @field)(dataType = "Boolean")
    isRoomDayUsed: Option[Boolean] = None
) extends SwaggerAnnotationsControl

final case class ChildRoom(
    uid: Option[String] = None,
    priceOfferId: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "Boolean")
    @JsonDeserialize(contentAs = classOf[java.lang.Boolean])
    @ApiModel(description = "IsROH room")
    isRoomTypeNotGuarantee: Option[Boolean] = None,
    payment: Option[ChildRoomPayment] = None,
    localizedRatePlanName: Option[String] = None,
    localizedBenefits: Option[Seq[String]] = None,
    childRateSettings: Option[ChildRateSettings] = None,
    roomIdentifiers: Option[String] = None,
    pricingInUSD: Option[ChildRoomPricing] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    dynamicRoomMappingType: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    externalLoyaltyDisplayTierId: Option[Int] = None,
    roomAllocationItemInfo: Option[Seq[RoomAllocationItemInfo]] = None,
    roomCheckTime: Option[ChildRoomCheckTime] = None
) extends SwaggerAnnotationsControl

final case class ChildRoomCheckTime(
    checkIn: Option[RateCategoryCheckTime] = None,
    checkOut: Option[RateCategoryCheckTime] = None
) extends SwaggerAnnotationsControl

final case class ChildRoomPricing(
    display: DisplayBasis
) extends SwaggerAnnotationsControl

final case class DisplayBasis(
    perBook: DisplayPrice
) extends SwaggerAnnotationsControl

final case class DisplayPrice(exclusive: Double, allInclusive: Double) extends SwaggerAnnotationsControl

final case class ChildRoomPayment(
    cancellation: Cancellation
) extends SwaggerAnnotationsControl

final case class Cancellation(
    @JsonSerialize(using = classOf[OptionCancellationClassSerialization])
    @JsonDeserialize(using = classOf[OptionCancellationClassDeserializer])
    @ApiModel(description = "FreeCancellation/PartiallyRefundable/NonRefundable/SpecialConditions/NotApplicable")
    cancellationType: Option[CancellationClass] = None
) extends SwaggerAnnotationsControl

final case class ChildPromotion(
    campaignId: Int,
    promotionCode: String,
    discountAmount: Double,
    localDiscountAmount: Double
) extends SwaggerAnnotationsControl

final case class EBEBooking(
    hotel: List[EBEHotel],
    discount: Option[CampaignDiscount] = None,
    childPromotions: Option[List[ChildPromotion]] = None
) extends SwaggerAnnotationsControl {
  def withEbeHotel(newHotel: List[EBEHotel]): EBEBooking = {
    this.copy(hotel = newHotel)
  }
}

final case class EBEHotel(
    @JsonSerialize(using = classOf[DateWithTZSerializer])
    @JsonDeserialize(using = classOf[CreationDateTimeDeserializer])
    checkIn: DateTime,
    @JsonSerialize(using = classOf[DateWithTZSerializer])
    @JsonDeserialize(using = classOf[CreationDateTimeDeserializer])
    checkOut: DateTime,
    numberOfAdults: Int,
    numberOfChildren: Int,
    numberOfRooms: Int,
    occFreeSearch: Boolean = false,
    room: List[BookingRoom],
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    @JsonSerialize(using = classOf[StayTypeSerializer])
    @JsonDeserialize(using = classOf[StayTypeDeserializer])
    stayType: Option[StayType] = None,
    selectedHourlySlot: Option[SelectedHourlySlot] = None
) extends SwaggerAnnotationsControl {
  def withRoom(newRoom: List[BookingRoom]): EBEHotel = {
    this.copy(room = newRoom)
  }
}

final case class Capacity(
    occupancy: Int
) extends SwaggerAnnotationsControl

final case class BookingPrice(
    charge: scala.List[BookingItemBreakdown]
) extends SwaggerAnnotationsControl

final case class BookingItemBreakdown(
    // at dbo.ebe_booking_charges saving as YYYY-MM-DD 00:00:00 (no timezone)
    @JsonSerialize(using = classOf[DateOptionWithTZSerializerAndDefaultNull])
    @JsonDeserialize(using = classOf[CreationDateTimeDeserializerOption])
    chargeDate: Option[DateTime] = None,
    exchangeRate: BigDecimal,
    itemId: Long,
    localAmount: BigDecimal,
    localCurrency: String,
    quantity: Int,
    roomNo: Long,
    usdAmount: BigDecimal,
    @(ApiModelProperty @field)(dataType = "Int")
    surchargeId: Option[Int],
    @(ApiModelProperty @field)(dataType = "Int")
    taxFeeId: Option[Int],
    typeId: Long,
    @(ApiModelProperty @field)(dataType = "Int")
    taxProtoTypeId: Option[Int],
    /* subtypeId defines the mapped agoda financial_breakdown_subtype(table) Id which describes the bookingcategory in
     * terms of facilities/PaxGroup/spclPerks etc. e.g Adult,ChildBedAdultMeal,ChildBedChildMeal,Infant...etc */
    @(ApiModelProperty @field)(dataType = "Int")
    subTypeId: Option[Int]
) extends SwaggerAnnotationsControl

final case class ChildRateSettings(
    childRateSettings: scala.List[ChildRateSetting]
) extends SwaggerAnnotationsControl

final case class ChildRateSetting(
    childRateTypeId: String,
    childRatePricingTypeId: String,
    value: BigDecimal,
    isCountAsRoomOcc: Boolean
) extends SwaggerAnnotationsControl

final case class BookingRoomPayment(
    @(ApiModelProperty @field)(dataType = "Int")
    localDecimalPlace: Option[Int],
    @(ApiModelProperty @field)(dataType = "Int")
    usdDecimalPlace: Option[Int]
) extends SwaggerAnnotationsControl

//  ToDO: ideally should be as part of creation-common
object AccountingEntity {

  private val defaultEntity =
    com.agoda.bapi.common.model.creation.common
      .AccountingEntity(merchantOfRecord = 5632, rateContract = 30001, revenue = 5632, None)
  private val packagingEntity =
    com.agoda.bapi.common.model.creation.common
      .AccountingEntity(merchantOfRecord = 5632, rateContract = 5632, revenue = 5632, None)
  private val carEntity =
    com.agoda.bapi.common.model.creation.common
      .AccountingEntity(merchantOfRecord = 5632, rateContract = 31001, revenue = 5674, None)
  private val protectionEntity =
    com.agoda.bapi.common.model.creation.common
      .AccountingEntity(merchantOfRecord = 5632, rateContract = 5632, revenue = 5632, None)

  def carAccountingEntity(supplierId: SupplierId): AccountingEntity =
    com.agoda.bapi.common.model.creation.common
      .AccountingEntity(merchantOfRecord = 5632, rateContract = supplierId, revenue = 5674, None)

  def mapFlightAccountingEntity(whiteLabelId: Option[Int], origin: Option[String]): AccountingEntity = {
    val financeModelAccountingEntity = AccountingEntityProvider.getEntity(
      productType = ProductType.FLIGHT,
      whiteLabelId = whiteLabelId,
      origin = origin
    ) // Using finance tax library to get AccountingEntity
    val bookingModelAccountingEntity = com.agoda.bapi.common.model.creation.common.AccountingEntity(
      merchantOfRecord = financeModelAccountingEntity.merchantOfRecord,
      rateContract = financeModelAccountingEntity.rateContract,
      revenue = financeModelAccountingEntity.revenue
    ) // Map finance tax library's AccountingEntity to booking's AccountingEntity
    bookingModelAccountingEntity
  }

  /* NOTE : we must have to throw exception if not able to serialize/deserialize accounting entity */

  @throws(classOf[IOException])
  @throws(classOf[JsonParseException])
  @throws(classOf[JsonMappingException])
  def jsonToAccountingEntity(value: Option[String]): AccountingEntity =
    JsonAccountingEntity.jsonToAccountingEntity(value)

  @throws(classOf[IOException])
  @throws(classOf[JsonParseException])
  @throws(classOf[JsonMappingException])
  def accountingEntityToJson(accountingEntity: Option[AccountingEntity]): String =
    JsonAccountingEntity.accountingEntityToJson(accountingEntity)

  @throws(classOf[IOException])
  @throws(classOf[JsonParseException])
  @throws(classOf[JsonMappingException])
  def accountingEntityToOptionJson(valueOpt: Option[AccountingEntity]): Option[String] =
    JsonAccountingEntity.accountingEntityToOptionJson(valueOpt)

  def getPackagingEntity: AccountingEntity = packagingEntity

  def getCarTBUEntity: AccountingEntity = carEntity

  def getProtectionEntity: AccountingEntity = protectionEntity
}

final case class BookingCancellation(
    code: String,
    noShowPolicy: Option[String],
    defaultNoShowPolicy: String,
    policies: Option[scala.Seq[String]],
    defaultPolicies: scala.Seq[String],
    @JsonSerialize(using = classOf[CancellationChargeTypeSerializer])
    @JsonDeserialize(using = classOf[CancellationChargeTypeDeserializer])
    @ApiModel(description = "PerBook/PerNight/FirstNight")
    @(ApiModelProperty @field)(
      dataType = "com.agoda.bapi.common.model.creation.CancellationChargeType$",
      required = false,
      extensions = Array(
        new Extension(
          name = "ms-enum",
          properties = Array(
            new ExtensionProperty(name = "name", value = "CancellationChargeType"),
            new ExtensionProperty(name = "modelAsString", value = "false")
          )
        )
      )
    )
    cancellationChargeType: Option[CancellationChargeType]
) extends SwaggerAnnotationsControl

final case class BookingSellInfo(
    downliftUsd: Option[BigDecimal],
    @(ApiModelProperty @field)(dataType = "Int")
    priceTemplatedId: Option[Int] = None,
    @deprecated("use pricingRequest id")
    searchId: String = "",
    isAdvanceGuarantee: Boolean,
    @(ApiModelProperty @field)(dataType = "Int")
    sellTagId: Option[Int] = None,
    pricingRequestId: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    firedrillContractId: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    firedrillContractTypeId: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "Boolean")
    advancePay: Option[Boolean] = None
) extends SwaggerAnnotationsControl

final case class BookingYCSPromotion(id: Long, text: String, foreignText: String) extends SwaggerAnnotationsControl

final case class BookingYCSPromotionBreakdown(
    id: Long,
    name: String,
    discountValue: Double,
    discountType: Int,
    appliedDate: List[LocalDate],
    discountAppliedDate: List[LocalDate] = List.empty
) extends SwaggerAnnotationsControl

final case class BookingChannelDiscountBreakdown(
    channelId: Int,
    name: String,
    discountPercent: Double,
    appliedDate: List[LocalDate],
    discountAppliedDate: List[LocalDate] = List.empty
) extends SwaggerAnnotationsControl

final case class BookingRateCategory(
    id: Int,
    Code: Option[String] = None,
    applyTo: Option[String] = None,
    Amount: BigDecimal,
    @(ApiModelProperty @field)(dataType = "Int")
    InventoryTypeId: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    stayPackageType: Option[Int] = None,
    rateCategoryContentToken: Option[String] = None
) extends SwaggerAnnotationsControl

final case class PriceChangeOnPollingStatus(priceChangeStatus: Map[ProductTokenKey, Boolean] = Map.empty)
    extends SwaggerAnnotationsControl {
  // if there exist a property where price is not changed, then we should poll
  val isPollingRequired: Boolean = priceChangeStatus.values.exists(_ == false)
  // PGT is only cleared if the price change status is false, mean the price didnt not change
  def shouldClearPriceGuaranteeToken(key: ProductTokenKey): Boolean = priceChangeStatus.get(key).contains(false)
}

final case class PriceChangePerRoomStatus(
    productTokenKey: ProductTokenKey,
    roomIdentifier: String,
    isPriceChanged: Boolean
) extends SwaggerAnnotationsControl

final case class PropertyKeyAndRoomId(
    productTokenKey: ProductTokenKey,
    roomIdentifier: Option[String]
) extends SwaggerAnnotationsControl

final case class BookingRoomBenefit(
    id: Long,
    remark: Option[String],
    value: Double,
    benefitParameters: Option[List[StructuredBenefitInfo]] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    targetType: Option[Int] = None,
    description: Option[String] = None
) extends SwaggerAnnotationsControl

final case class StructuredBenefitInfo(
    @(ApiModelProperty @field)(dataType = "Int")
    unitId: Option[Int],
    paramValue: String,
    paramPosition: Int
) extends SwaggerAnnotationsControl

object StructuredBenefitInfo {
  def convertBenefitParamsJson(benefitParametersOpt: Option[List[StructuredBenefitInfo]]): Option[String] = {
    implicit val formats = DefaultFormats
    benefitParametersOpt.map { benefitParameters =>
      write(benefitParameters)
    }
  }
}

final case class BookingGiftCardEarning(
    amount: BigDecimal,
    @(ApiModelProperty @field)(dataType = "Int")
    offsetDays: Option[Int],
    isAutoMigrate: Boolean,
    giftCardGuid: String,
    expiryDays: Int
) extends SwaggerAnnotationsControl

final case class BookingCashBackEarning(
    usdAmount: BigDecimal,
    @(ApiModelProperty @field)(dataType = "Int")
    offsetDays: Int,
    cashBackGuid: String,
    expiryDays: Int,
    cashBackPercentage: BigDecimal,
    cashbackVersion: Option[String] = None,
    cashbackType: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    rewardTypeId: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "Double")
    agodaCashbackValue: Option[Double] = None,
    @(ApiModelProperty @field)(dataType = "Double")
    promocodeCashbackValue: Option[Double] = None,
    @(ApiModelProperty @field)(dataType = "Double")
    cofundedCashbackValue: Option[Double] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    promotionId: Option[Int] = None
) extends SwaggerAnnotationsControl

final case class RateModel(id: Int) extends SwaggerAnnotationsControl

final case class BORSupplier(confirmByMins: Int) extends SwaggerAnnotationsControl

final case class BookingRoom(
    uid: String,
    accountingEntity: Option[AccountingEntity],
    @ApiModel(description = "Unknown/Guarantee/Realtime/OnRequest/OnRequestSupplier")
    @JsonDeserialize(using = classOf[AvailabilityTypeDeserializer])
    @JsonSerialize(using = classOf[AvailabilityTypeSerializer])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    availabilityType: AvailabilityType,
    hotelId: Long,
    capacity: Capacity,
    @(ApiModelProperty @field)(dataType = "Boolean")
    @JsonDeserialize(contentAs = classOf[java.lang.Boolean])
    @ApiModel(description = "Does Booking.com allow to pay before checkin")
    isPrepaymentRequired: Option[Boolean],
    @(ApiModelProperty @field)(dataType = "Int")
    lineItemId: Option[Int] = None,
    noOfAdults: Int,
    noOfChildren: Int,
    noOfExtrabeds: Int,
    numberOfRoom: Int,
    breakfastIncluded: Boolean,
    breakfastInfo: String,
    cancellation: BookingCancellation,
    ycsRatePlanId: Long,
    pricing: BookingPrice,
    exchange: BookingRoomPayment,
    rateCategory: Option[
      BookingRateCategory
    ], // before agoda use ratePlanId to describe benefits. maybe some rooms still does not has this info, therefore it optional
    borSupplier: Option[BORSupplier] = None,
    isNotCcRequired: Boolean,
    @(ApiModelProperty @field)(dataType = "Int")
    dmcId: Option[Int],
    dmcSpecificData: Option[String],
    excluded: Option[String],
    included: Option[String],
    roomTypeId: Long,
    roomTypeName: String,
    @(ApiModelProperty @field)(dataType = "Long")
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    searchId: Option[Long],
    @(ApiModelProperty @field)(dataType = "Long")
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    searchResultId: Option[Long],
    sellInfo: Option[BookingSellInfo],
    promotion: Option[BookingYCSPromotion],
    displayCurrency: Option[String],
    benefit: Seq[BookingRoomBenefit] = Seq(),
    rateModel: RateModel,
    taxSurchargeInfo: Option[String],
    @ApiModel(description = "Unknown(0)/Merchant(1)/Agency(2)/MerchantCommission(4)")
    @JsonDeserialize(using = classOf[PaymentModelDeserializer])
    @JsonSerialize(using = classOf[PaymentModelSerializer])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    paymentModels: PaymentModel,
    @(ApiModelProperty @field)(dataType = "Int")
    npclnChannel: Option[Int] = None,
    @deprecated("in PAPI contracts marked as deprecated + type mismatch")
    @(ApiModelProperty @field)(dataType = "Int")
    pointMultiply: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "Boolean")
    isAgodaAgency: Option[Boolean] = None,
    giftCardEarning: Option[BookingGiftCardEarning] = None,
    partnerLoyaltyPoint: Option[PartnerLoyaltyPoint] = None,
    supplierInfo: Option[SupplierInfo] = None,
    childRate: Option[ChildRateSettings] = None,
    cashBackEarning: Option[BookingCashBackEarning] = None,
    promotionsBreakdown: Option[List[BookingYCSPromotionBreakdown]] = None,
    channelDiscountBreakdown: Option[List[BookingChannelDiscountBreakdown]] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    externalLoyaltyDisplayTierId: Option[Int] = None,
    bookingConsumerFintech: Option[ConsumerFintechDetail] = None
) extends SwaggerAnnotationsControl {
  def isBreakfastIncluded: Boolean =
    this.benefit
      .exists(item => item.id.toInt == BenefitID.Breakfast.id) || this.breakfastIncluded

  def withPartnerLoyaltyPoint(newPartnerLoyaltyPoint: Option[PartnerLoyaltyPoint]): BookingRoom = {
    this.copy(partnerLoyaltyPoint = newPartnerLoyaltyPoint)
  }

  def withExternalLoyaltyDisplayTierId(newExternalLoyaltyDisplayTierId: Option[Int]): BookingRoom = {
    this.copy(externalLoyaltyDisplayTierId = newExternalLoyaltyDisplayTierId)
  }

  @(ApiModelProperty @field)(hidden = true)
  @JsonIgnore
  def isBor: Boolean = this.borSupplier.nonEmpty
}

final case class AlternativeRoomInfo(
    @(ApiModelProperty @field)(dataType = "Int")
    upSellTypeInRequest: Option[Int],
    availableAlternativeTypes: Seq[Int],
    isCrossSellAvailable: Boolean,
    isCrossSellRequest: Boolean,
    isRateChannelSwap: Boolean
) extends SwaggerAnnotationsControl

// follow PAPI naming
object PaymentModel extends Enumeration {
  type PaymentModel = Value
  val Unknown: PaymentModel  = Value(0)
  val Merchant: PaymentModel = Value(1) // Agoda
  val Agency: PaymentModel   = Value(2) // PayHotel
  //// 3 hotelBooking, is not using now
  val MerchantCommission: PaymentModel = Value(4) // MerchantCommission
}

class PaymentModelSerializer   extends EnumAsIntSerializer[PaymentModel.PaymentModel]
class PaymentModelDeserializer extends EnumAsIntDeserializer(PaymentModel)

object AvailabilityType extends Enumeration {
  type AvailabilityType = Value
  val Unknown: AvailabilityType           = Value(0)
  val Guarantee: AvailabilityType         = Value(1)
  val Realtime: AvailabilityType          = Value(2)
  val OnRequest: AvailabilityType         = Value(3)
  val OnRequestSupplier: AvailabilityType = Value(4)
}

class AvailabilityTypeSerializer   extends EnumAsIntSerializer[AvailabilityType]
class AvailabilityTypeDeserializer extends EnumAsIntDeserializer(AvailabilityType)

object CancellationChargeType extends Enumeration {
  type CancellationChargeType = Value
  val PerBook: CancellationChargeType    = Value("PerBook")
  val PerNight: CancellationChargeType   = Value("PerNight")
  val FirstNight: CancellationChargeType = Value("FirstNight")
  def getCancelChargeType(papiCancellationChargeType: Option[String]): Option[CancellationChargeType] =
    papiCancellationChargeType.map(CancellationChargeType.withName)
}

class CancellationChargeTypeSerializer extends EnumAsOptStringSerializer[CancellationChargeType] {
  //  Special None as empty serialization
  override def isEmpty(provider: SerializerProvider, value: Option[CancellationChargeType.Value]): Boolean =
    value.isEmpty
}
class CancellationChargeTypeDeserializer extends EnumAsOptStringDeserializer(CancellationChargeType) {
  //  Special "throwable" deserialization logic
  override def deserialize(p: JsonParser, ctx: DeserializationContext) = {
    val stringValue = p.getValueAsString
    Option(CancellationChargeType.withName(stringValue))
  }
}

final case class RoomAllocationItemInfo(
    adults: Int,
    childrenCountAsRoomOcc: Int,
    childrenTypes: List[ChildrenType] = List.empty
) extends SwaggerAnnotationsControl
