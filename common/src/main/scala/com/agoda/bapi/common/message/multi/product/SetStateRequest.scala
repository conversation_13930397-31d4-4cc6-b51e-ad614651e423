package com.agoda.bapi.common.message.multi.product

import com.agoda.bapi.common.message._
import com.agoda.bapi.common.model.base.{BaseBookingEssInfoInternal, BaseBookingModelInternal, BaseBookingRelationshipInternal, BaseCancellationInfoModelInternal}
import com.agoda.bapi.common.model.car.VehicleBookingStateModel.VehicleModelInternal
import com.agoda.bapi.common.model.flight.flightModel._
import com.agoda.bapi.common.model.flight.history.ActionType.ActionType
import com.agoda.bapi.common.model.flight.history.{ActionTypeDeserializer, ActionTypeType}
import com.agoda.bapi.common.model.itinerary.ItineraryModelTrait
import com.agoda.bapi.common.model.multiproduct.MultiProductBookingGroupDBModel
import com.agoda.bapi.common.model.protection.ProtectionBookingStateModel.ProtectionModelInternal
import com.agoda.bapi.common.model.{ActionId, UserContext}
import com.fasterxml.jackson.databind.annotation.{JsonDeserialize, JsonSerialize}
import com.fasterxml.jackson.module.scala.JsonScalaEnumeration
import io.swagger.annotations.{ApiModel, ApiModelProperty}
import org.joda.time.DateTime

import scala.annotation.meta.field

@ApiModel(parent = classOf[Request])
final case class SetStateRequest(
    @ApiModel(description = "Indicates the current phase the booking is in.")
    @JsonSerialize(using = classOf[ActionTypeSerializer])
    @JsonDeserialize(using = classOf[ActionTypeDeserializer])
    @JsonScalaEnumeration(classOf[ActionTypeType])
    @(ApiModelProperty @field)(dataType = "Int")
    actionType: ActionType,
    actionId: ActionId, // flight action id
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    bookingType: Option[Int], // flight booking type
    bookingId: Long,          // flight booking id
    schemaVersion: String,
    flights: Seq[FlightBookingState],
    slices: Seq[FlightSlice],
    segments: Seq[FlightSegment],
    passengers: Seq[FlightPaxNoPii],
    payments: Seq[PaymentState],
    breakdown: Seq[Breakdown],
    breakdownPerPax: Option[Seq[BreakdownPerPax]],
    baggageAllowance: Seq[FlightBaggageAllowance],
    segmentInfoByPaxType: Option[Seq[SegmentInfoByPaxType]],
    history: Seq[ItineraryHistory],
    paxTickets: Seq[FlightPaxTicketState],
    itinerary: MultiProductItinerary,
    summary: Seq[FlightSummary],
    userAgent: Option[UserAgentState],
    bookingAttribution: Seq[BookingAttributionState],
    itineraryDate: DateTime,
    correlationId: Option[String] = None,
    userContext: Option[UserContext] = None,
    properties: Option[Seq[PropertyBookingState]] = None,
    protectionModels: Option[Seq[ProtectionModelInternal]] = None,
    multiProductInfos: Option[Seq[MultiProductInfoSetState]] = None,
    fareRulePolicies: Option[Seq[FareRulePolicyModelInternal]] = None,
    vehicle: Option[Seq[VehicleModelInternal]] = None,
    @deprecated("move to use flightSeatSelection instead will be removed from SKYA-5848")
    seatSelections: Option[Seq[FlightSeatSelectionDetail]] = None,
    baggage: Option[Seq[FlightBaggage]] = None,
    bookingPayments: Option[Seq[BookingPaymentState]] = None,
    bookingRelationships: Option[Seq[BaseBookingRelationshipInternal]] = None,
    multiProductBookingGroups: Option[Seq[MultiProductBookingGroup]] = None,
    activities: Option[Seq[ActivityBookingState]] = None,
    cegFastTracks: Option[Seq[CegFastTrackBookingState]] = None,
    addOns: Option[Seq[AddOnBookingState]] = None,
    flightBaseBooking: Option[BaseBookingModelInternal] = None,
    flightBaseCancellationInfo: Option[BaseCancellationInfoModelInternal] = None,
    flightBrandSelections: Option[Seq[FlightBrandSelection]] = None,
    flightBrandAttributes: Option[Seq[FlightBrandAttribute]] = None,
    flightBrandAttributeParams: Option[Seq[FlightBrandAttributeParam]] = None,
    flightSeatSelection: Option[Seq[FlightSeatSelectionDetail]] = None,
    postBookingFee: Option[Seq[PostBookingFee]] = None,
    crossProductIsolatedFeature: Option[CrossProductIsolatedFeature] = None,
    essInfos: Option[Seq[BaseBookingEssInfoInternal]] = None
) extends Request
    with FlightModelTrait
    with ItineraryModelTrait
    with ActionModelTrait

final case class PropertyBookingState(
    bookingId: Long,
    stateId: Int,
    propertyState: Option[String] = None
)

final case class MultiProductInfoSetState(multiProductId: Long, multiProductType: Int)

final case class MultiProductBookingGroup(
    bookingId: Long,
    itineraryId: Long,
    cartId: Long,
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    @(ApiModelProperty @field)(dataType = "Long", required = false)
    packageId: Option[Long] = None
) {
  def toMultiProductBookingGroupDBModel: MultiProductBookingGroupDBModel =
    MultiProductBookingGroupDBModel(
      bookingId = bookingId,
      itineraryId = itineraryId,
      cartId = cartId,
      packageId = packageId
    )
}
