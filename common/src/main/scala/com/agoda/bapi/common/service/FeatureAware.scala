package com.agoda.bapi.common.service

import com.agoda.bapi.common.message.{DevicePlatform, ExperimentData}
import com.agoda.bapi.common.model.{BCTExperiment, CFBExperiment, ExperimentNames, INSTExperiment, MPBEExperiment, SKYCExperiment, VCARDAPIExperiment, WLBCExperiment}
import com.agoda.bapi.common.model._
import com.agoda.experiments.{ExperimentManager, RequestContext}
import org.apache.commons.lang3.StringUtils

import scala.collection.concurrent.TrieMap
import scala.util.Try

trait FeatureAware
    extends ExperimentSides
    with MPBEExperiment
    with WLBCExperiment
    with CFBExperiment
    with SKYCExperiment
    with VCARDAPIExperiment
    with INSTExperiment
    with BCTExperiment
    with PCExperiment
    with PaymentExperiment
    with UNIBFExperiment
    with WLBOExperiment
    with WLBEExperiment
    with SKYAExperiment
    with RTAPExperiment
    with ABEXExperiment
    with ACTExperiment
    with PaymentFlexibilityExperiment {

  def enableFlightPayoutAgent: Boolean
  def isUseUpcLocalService: <PERSON><PERSON><PERSON>
  def showWeChatDomestic: <PERSON><PERSON><PERSON>
  def isPriceChangePollingEnabled: Boolean
  def isPriceChangePollingEnabledForMSPA: Boolean
  def enableUSRequiredFields: Boolean
  def isRoomSwapEnabled: Boolean
  def isPreCheckOnAlternatives: Boolean
  def isCrossSellOnUpSell: Boolean
  def isCrossSellAllBreakfastTypes: Boolean
  def isIndiaEnabledForPackage: Boolean
  def isPreProcessPaymentSettlement: Boolean
  def isHideOTCPaymentOptions: Boolean
  def isReduceAllotmentAlertRateForOfflinePaymentEnabled: Boolean
  def enableWeChatMiniProgramPlatformMapping: Boolean
  def enableJingDongMiniProgramPlatformMapping: Boolean
  def enableDBSHKOnAgoda: Boolean
  def enableDBSSGOnAgoda: Boolean
  def rateChannelSwapSwitch: Boolean
  def fallbackToJapanChildRate: Boolean
  def useCartBreakdownForSingleVehicle: Boolean
  def isArrivalTimeValidationEnabled: Boolean
  def enableWebviewBFPaymentMethodsApps: Boolean
  def useHybridPriceBreakdownBuilderForCart: Boolean
  def disableApplePay: Boolean
  def dynamicBookingQuestions: Boolean
  def useNewFastTrackFeePercentage: Boolean
  def enableFastTrackPromoCodeBadge: Boolean
  def enableFastTrackPricingAdjustment: Boolean
  def expandFastTrackInvoiceOrigins: Boolean
  def useAABUpdateCreditCardInfo: Boolean
  def enableApostropheInGreetingMessage: Boolean
  def isRemoveFenceSelfServiceURLsForRurubu: Boolean
  def EnableJava11HttpBackendForNusaFlightSupplier: Boolean
  def CegFastTrackMigration: Boolean
  def enableServiceTaxCountry: Boolean
  def enableAllDmcBookingToSaveDiscountInformation: Boolean
  def RemoveReadingPMCCampaignDataFromBFDB: Boolean
  def enableChangeHotelFundedTextFallback: Boolean
  def enableLogDateOfBirth: Boolean
  def cegFastTrackMigrationCreateFlow: Boolean
  def useNewFraudClient: Boolean
  def isInclusivePaySupplier: Boolean
  def fixDoubleTaxesAndFeesPaySupplier: Boolean
  def isForceMdbForCashbackRedemption: Boolean
  def fixPromotionDiscountsInfo: Boolean
  def enablePassingBookingSource: Boolean
  def removeSpBAPIGetPMCCampaignPromotion: Boolean
  def buildSeenExperimentTags(experimentNames: Seq[String]): Map[String, String]
  def enableFlightsMultiProductPMCMigration: Boolean
  def enableMemberId: Boolean
  def enableFlightsHWFlow: Boolean
  def enableThaiVietJetMigration: Boolean
  def enableSwapFinNodeWithEmailNodeForBkgRejection: Boolean
  def enableFlightReverseBreakdown: Boolean
  def enableDuplicateRequestCheckForCartFlow: Boolean
  def enableAddSearchClusterUserAgentModel: Boolean
  def enableRurubuDirectSupply: Boolean
  def enableFixedDiscountPerAppliedDate: Boolean
  def enableLionAirMultiSession: Boolean
  def enableStatusTokenV6: Boolean
}

class FeatureAwareImpl(
    experimentManager: ExperimentManager,
    experimentData: ExperimentData,
    languageId: Int,
    origin: String
) extends FeatureAware {
  private val experimentAllocationRegistry = TrieMap[String, Boolean]()

  private val bVariant: Char = 'B'
  private val overriddenAllocationVariantMap = experimentData.force.map { value =>
    value.collect {
      case (expId, variant) if !StringUtils.isEmpty(variant) => expId -> variant.charAt(0)
    }
  }

  private val experimentRequestContext = RequestContext(
    languageId = languageId.toString,
    deviceTypeId = experimentData.deviceTypeId,
    trafficGroup = experimentData.trafficGroup.getOrElse(""),
    cId = experimentData.cId.getOrElse(""),
    aId = experimentData.aId.getOrElse(""),
    serverName = experimentData.serverName.getOrElse(""),
    origin = origin,
    userId = experimentData.userId,
    memberId = experimentData.memberId,
    forceUserVariant = experimentData.forceByVariant.flatMap(_.headOption),
    forceOnIntegrationRun = experimentData.forceOnIntegrationRun.getOrElse(false),
    forceOnZeroTraffic =
      experimentData.forceByVariant.nonEmpty || experimentData.force.nonEmpty || experimentData.forceOnZeroTraffic
        .getOrElse(false),
    overridenAllocationVariantMap = overriddenAllocationVariantMap
  )

  override def isBVariant(experimentName: String): Boolean = {
    val isBVariant = isForceB(experimentName) || isB(experimentName)
    if (isB(ExperimentNames.supportExperimentAllocationRegistry)) {
      experimentAllocationRegistry.putIfAbsent(experimentName, isBVariant)
    }
    isBVariant
  }

  override def buildSeenExperimentTags(experimentNames: Seq[String]): Map[String, String] = {
    // build tags for the experiments required.
    experimentNames
      .filter(experimentAllocationRegistry.contains)
      .map(exp => exp -> experimentAllocationRegistry(exp).toString)
      .toMap
  }

  override def getExperimentData: ExperimentData = experimentData

  override def variant(experimentName: String): Char = {
    // for logging purpose, should use isBVariant to decide experiment
    if (isForceB(experimentName))
      'B'
    else experimentManager.determineVariant(experimentName, experimentRequestContext)
  }

  // Kill switch for change to use upc-local-service in each dc
  override def isUseUpcLocalService: Boolean = isBDeprecated(ExperimentNames.UseUpcLocalService)

  override def isForceB(exp: String): Boolean =
    overriddenAllocationVariantMap.flatMap(_.get(exp)).contains(bVariant)

  @deprecated("use isB version, instead")
  def isBDeprecated(exp: String): Boolean =
    experimentManager.determineVariant(exp, experimentRequestContext).equals(bVariant)

  /**
    * Determines if experiment allocation should go to variant B or not.
    *
    * @note:
    *   sends additional information to calculon
    * @param exp
    * @param isMemberIdAllocation
    * @return
    */
  def isB(exp: String): Boolean = {
    val isB = experimentManager.determineVariant(exp, experimentRequestContext).equals(bVariant)

    experimentManager.sendExperimentsSeenMessage(experimentRequestContext)

    isB
  }

  override def showWeChatDomestic: Boolean =
    isForceB(ExperimentNames.showWeChatDomestic)

  override def isPriceChangePollingEnabled: Boolean =
    isPriceChangePollingEnabledForMSPA

  override def isPriceChangePollingEnabledForMSPA: Boolean =
    isB(ExperimentNames.isPriceChangePollingForMSPA) || isForceB(ExperimentNames.isPriceChangePollingForMSPA)

  override def enableUSRequiredFields: Boolean = isBVariant(ExperimentNames.enableUSRequiredFields)

  override def isRoomSwapEnabled: Boolean = isRoomSwapEnabledDSPA

  private def isRoomSwapEnabledDSPA =
    (experimentData.deviceTypeId == DevicePlatform.WebDesktop.id.toString || experimentData.deviceTypeId == DevicePlatform.WebTablet.id.toString) && isBVariant(
      ExperimentNames.roomSwapDSPA
    )

  override def isPreCheckOnAlternatives: Boolean = isBVariant(ExperimentNames.isPreCheckOnAlternatives)

  override def isCrossSellOnUpSell: Boolean = isBVariant(ExperimentNames.isCrossSellOnUpSell)

  override def isCrossSellAllBreakfastTypes: Boolean = isBVariant(ExperimentNames.isCrossSellAllBreakfastTypes)

  override def isIndiaEnabledForPackage: Boolean = isBVariant(ExperimentNames.isIndiaEnabledForPackage)

  override def isPreProcessPaymentSettlement: Boolean = isBVariant(ExperimentNames.preProcessPaymentSettlement)

  override def isHideOTCPaymentOptions: Boolean = isBVariant(ExperimentNames.cegAAPreventionHideOTCPaymentOptions)

  override def isReduceAllotmentAlertRateForOfflinePaymentEnabled: Boolean = isBVariant(
    ExperimentNames.reduceAllotmentAlertRateForOfflinePayment
  )

  override def enableWeChatMiniProgramPlatformMapping: Boolean = isForceB(
    ExperimentNames.enableWeChatMiniProgramPlatformMapping
  )

  override def enableJingDongMiniProgramPlatformMapping: Boolean = isForceB(
    ExperimentNames.enableJingDongMiniProgramPlatformMapping
  )

  override def enableDBSHKOnAgoda: Boolean = isBVariant(ExperimentNames.enableDBSHKOnAgoda)

  override def enableDBSSGOnAgoda: Boolean = isBVariant(ExperimentNames.enableDBSSGOnAgoda)

  override def rateChannelSwapSwitch: Boolean = isBVariant(
    ExperimentNames.rateChannelSwapSwitchExp
  )

  override def fallbackToJapanChildRate: Boolean =
    isBVariant(ExperimentNames.fallbackToJapanChildRate)

  override def useCartBreakdownForSingleVehicle: Boolean = isForceB(ExperimentNames.useCartBreakdownForSingleVehicle)

  override def isArrivalTimeValidationEnabled: Boolean = isBVariant(
    ExperimentNames.ArrivalTimeValidation
  )

  override def enableWebviewBFPaymentMethodsApps: Boolean = {
    val devicePlatformId = DevicePlatform.getFromId(Try(experimentData.deviceTypeId.toInt).getOrElse(0))
    DevicePlatform.isIOSPlatform(
      devicePlatformId
    ) || DevicePlatform.isAndroidPlatform(
      devicePlatformId
    )
  }

  override def useHybridPriceBreakdownBuilderForCart: Boolean = isBVariant(
    ExperimentNames.useHybridPriceBreakdownBuilderForCart
  )

  override def enableFlightPayoutAgent: Boolean =
    isBVariant(ExperimentNames.enableFlightPayoutAgent)

  override def disableApplePay: Boolean = isBVariant(ExperimentNames.disableApplePay)

  override def dynamicBookingQuestions: Boolean = isBVariant(ExperimentNames.dynamicBookingQuestions)

  override def useNewFastTrackFeePercentage: Boolean = isBVariant(
    ExperimentNames.adjustFastTrackFee
  )

  override def enableFastTrackPromoCodeBadge: Boolean = isBVariant(
    ExperimentNames.enableFastTrackPromoCodeBadge
  )

  override def enableFastTrackPricingAdjustment: Boolean = isBVariant(
    ExperimentNames.enableFastTrackPricingAdjustment
  )

  override def expandFastTrackInvoiceOrigins: Boolean = isBVariant(
    ExperimentNames.expandFastTrackInvoiceOrigins
  )

  override def useAABUpdateCreditCardInfo: Boolean = isBVariant(
    ExperimentNames.useAABUpdateCreditCardInfo
  )

  override def enableApostropheInGreetingMessage: Boolean = isBVariant(
    ExperimentNames.enableApostropheInGreetingMessage
  )

  override def isRemoveFenceSelfServiceURLsForRurubu: Boolean = isBVariant(
    ExperimentNames.isRemoveFenceSelfServiceURLsForRurubu
  )

  override def EnableJava11HttpBackendForNusaFlightSupplier: Boolean = isBVariant(
    ExperimentNames.EnableJava11HttpBackendForNusaFlightSupplier
  )

  override def CegFastTrackMigration: Boolean = isBVariant(
    ExperimentNames.CegFastTrackMigration
  )

  override def enableServiceTaxCountry: Boolean = isBVariant(
    ExperimentNames.enableServiceTaxCountry
  )

  override def enableAllDmcBookingToSaveDiscountInformation: Boolean = isBVariant(
    ExperimentNames.EnableAllDmcBookingToSaveDiscountInformation
  )

  override def RemoveReadingPMCCampaignDataFromBFDB: Boolean = isBVariant(
    ExperimentNames.RemoveReadingPMCCampaignDataFromBFDB
  )

  override def enableChangeHotelFundedTextFallback: Boolean = isBVariant(
    ExperimentNames.EnableChangeHotelFundedTextFallback
  )

  override def enableLogDateOfBirth: Boolean = isBVariant(
    ExperimentNames.EnableLogDateOfBirth
  )

  override def cegFastTrackMigrationCreateFlow: Boolean = isBVariant(
    ExperimentNames.cegFastTrackMigrationCreateFlow
  )

  override def useNewFraudClient: Boolean = isBVariant(
    ExperimentNames.useNewFraudClient
  )

  override def isInclusivePaySupplier: Boolean = isBVariant(
    ExperimentNames.isInclusivePaySupplier
  )

  override def fixDoubleTaxesAndFeesPaySupplier: Boolean = isBVariant(
    ExperimentNames.fixDoubleTaxesAndFeesPaySupplier
  )

  override def isForceMdbForCashbackRedemption: Boolean = isBVariant(ExperimentNames.isForceMdbCashbackRedemption)

  override def fixPromotionDiscountsInfo: Boolean = isBVariant(
    ExperimentNames.FixPromotionDiscountsInfo
  )

  override def enablePassingBookingSource: Boolean = isBVariant(
    ExperimentNames.enablePassingBookingSource
  )

  override def removeSpBAPIGetPMCCampaignPromotion: Boolean = isBVariant(
    ExperimentNames.removeSpBAPIGetPMCCampaignPromotion
  )

  override def enableFlightsMultiProductPMCMigration: Boolean = isBVariant(
    ExperimentNames.enableFlightsMultiProductPMCMigration
  )

  override def enableMemberId: Boolean = isBVariant(
    ExperimentNames.enableMemberId
  )

  override def enableFlightsHWFlow: Boolean = isBVariant(
    ExperimentNames.isHyperWalletExperiment
  )

  override def enableThaiVietJetMigration: Boolean = isBVariant(
    ExperimentNames.EnableThaiVietJetMigration
  )

  override def enableSwapFinNodeWithEmailNodeForBkgRejection: Boolean = isBVariant(
    ExperimentNames.enableSwapFinNodeWithEmailNodeForBkgRejection
  )

  override def enableFlightReverseBreakdown: Boolean = isBVariant(
    ExperimentNames.enableFlightReverseBreakdown
  )

  override def enableDuplicateRequestCheckForCartFlow: Boolean = isBVariant(
    ExperimentNames.enableDuplicateRequestCheckForCartFlow
  )

  override def enableAddSearchClusterUserAgentModel: Boolean = isBVariant(
    ExperimentNames.addSearchClusterUserAgentModel
  )

  override def enableFixedDiscountPerAppliedDate: Boolean = isBVariant(
    ExperimentNames.EnableFixedDiscountPerAppliedDate
  )

  override def enableRurubuDirectSupply: Boolean = isBVariant(
    ExperimentNames.EnableRurubuDirectSupply
  )

  override def enableLionAirMultiSession: Boolean = isBVariant(
    ExperimentNames.enableLionAirMultiSession
  )

  override def enableStatusTokenV6: Boolean = isBVariant(ExperimentNames.EnableStatusTokenV6)
}
