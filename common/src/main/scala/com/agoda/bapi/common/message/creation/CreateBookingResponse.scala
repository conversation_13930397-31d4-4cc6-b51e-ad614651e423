package com.agoda.bapi.common.message.creation

import com.agoda.abspnx.client.models.abs.whitelabel.ErrorType.ErrorType
import com.agoda.abspnx.client.models.abs.whitelabel.{ErrorType => AbsErrorType}
import com.agoda.bapi.common.message.ResponseBase
import com.agoda.bapi.common.message.creation.BookingElement.BookingElement
import com.agoda.bapi.common.message.creation.LocalScriptType.LocalScriptType
import com.agoda.bapi.common.message.creation.ThirdPartyStatus.ThirdPartyStatus
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.payment.PaymentContinuation
import com.agoda.bapi.common.model.protection.ProtectionBookingState.ProtectionBookingState
import com.agoda.bapi.common.model.protection.{ProtectionBookingStateDeserializer, ProtectionBookingStateSerializer}
import com.agoda.bapi.common.model.swipe.{PaymentRedirect, Redirect3dsParams}
import com.agoda.bapi.common.util.JacksonSerializer.{EnumAsIntDeserializer, EnumAsIntSerializer}
import com.agoda.bapi.common.util.TokenIsExpired
import com.agoda.mpbe.state.common.enums.Gateway.Gateway
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.core.`type`.TypeReference
import com.fasterxml.jackson.databind.annotation.{JsonDeserialize, JsonSerialize}
import com.fasterxml.jackson.module.scala.JsonScalaEnumeration
import io.swagger.annotations.{ApiModel, ApiModelProperty}
import org.joda.time.DateTime
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpb.common.errors.ErrorCode.ErrorCode

import scala.annotation.meta.field

@ApiModel(parent = classOf[ResponseBase])
case class CreateBookingResponse(
    /**
      * Response base
      */
    @ApiModel(description = "Indicates whether this request completed successfully.")
    success: Boolean,
    @ApiModel(description = "Non-localized description of the error to help troubleshooting.")
    errorMessage: Option[String] = None,
    @ApiModel(description = "Identifies the error in case success is false to allow clients to handle specific cases.")
    errorCode: Option[String] = None,
    /**
      * For clear error type
      */
    @(ApiModelProperty @field)(dataType = "Int")
    subErrorCode: Option[Int] = None,
    @ApiModel(description = "Details about the invalid request data.")
    invalidRequestData: Option[Seq[InvalidRequestDetail]] = None,
    /**
      */

    @(ApiModelProperty @field)(hidden = true)
    hotelRooms: Option[Seq[Itinerary]] = None,
    itinerary: Option[Itinerary] = None,
    paymentResult: Option[PaymentResult] = None,
    duplicateBookings: Seq[DuplicateBooking] = Seq(),
    // TODO should change to mandatory once BAPI rollout is successful.
    status: Option[BookingServerStatus],
    @ApiModel(
      description = "SupplierResponse contains data that partner api required to complete booking process on their site"
    )
    supplierResponse: Option[SupplierResponse] = None
) extends CreateResponseBase

trait CreateResponseBase extends ResponseBase {
  val subErrorCode: Option[Int]
  val status: Option[BookingServerStatus]
}

object CreateBookingResponse {
  def badRequest(subErrorCode: ErrorCode): CreateBookingResponse = badRequest(subErrorCode.id, subErrorCode.toString)

  def invalidBookingToken(ex: Throwable): CreateBookingResponse =
    ex.getCause match {
      case c: TokenIsExpired =>
        CreateBookingResponse(
          success = false,
          errorMessage = Some(c.getLocalizedMessage),
          errorCode = Some(ErrorType.BadRequest.toString),
          status = BookingServerStatus.BookingSessionExpired
        )
      case c: Throwable =>
        CreateBookingResponse(
          success = false,
          errorMessage = Some(c.getLocalizedMessage),
          errorCode = Some(ErrorType.BadRequest.toString),
          status = BookingServerStatus.InvalidBookingToken
        )
    }

  def badRequest(subErrorCode: Int, message: String): CreateBookingResponse = {
    if (subErrorCode == ErrorCode.DuplicateBooking.id) {
      // TODO: Remove this if when introduce ErrorCode.DuplicateRequest
      /* DuplicateBooking rejection here was raised only from DuplicateRequestChecker, which is currently only
       * SingleProperty */
      val dummyBookingId = -1
      val dummyDuplicateBooking =
        DuplicateBooking(
          BookingElement.Hotel,
          bookingId = dummyBookingId,
          bookingDate = DateTime.now(),
          selfServiceUrl = ""
        )
      duplicate(duplicates = Seq(dummyDuplicateBooking))
    } else {
      CreateBookingResponse(
        success = false,
        errorMessage = Some(message),
        errorCode = Some(ErrorType.BadRequest.toString),
        subErrorCode = Some(subErrorCode),
        status =
          if (subErrorCode == ErrorCode.GatewayRequireCVC.id)
            BookingServerStatus.ChallengeCVV
          else if (subErrorCode == ErrorCode.ELAPIParnerClaimTokenExpired.id)
            BookingServerStatus.LoyaltyPartnerClaimTokenExpired
          else if (ErrorCode.PaymentFailedErrorCodes(ErrorCode(subErrorCode)))
            BookingServerStatus.PaymentFailed
          else
            BookingServerStatus.InvalidRequest
      )
    }
  }

  def badRequest(subErrorCode: ErrorCode, message: Option[String]): CreateBookingResponse =
    badRequest(subErrorCode.id, message.getOrElse(subErrorCode.toString))

  def error(
      subErrorCode: ErrorCode,
      message: Option[String] = None,
      supplierResponse: Option[SupplierResponse] = None
  ): CreateBookingResponse =
    CreateBookingResponse(
      success = false,
      errorMessage = message.orElse(Some(subErrorCode.toString)),
      errorCode = Some(ErrorType.ProcessingError.toString),
      subErrorCode = Some(subErrorCode.id),
      status =
        if (subErrorCode == ErrorCode.TerroristIdentifiedInBooking) BookingServerStatus.TerroristIdentified
        else BookingServerStatus.Error,
      supplierResponse = supplierResponse
    )

  def technicalErrorFor3DS2(subErrorCode: ErrorCode, message: Option[String] = None): CreateBookingResponse =
    CreateBookingResponse(
      success = false,
      errorMessage = message.orElse(Some(subErrorCode.toString)),
      errorCode = Some(ErrorType.TechnicalError.toString),
      subErrorCode = Some(subErrorCode.id),
      status = BookingServerStatus.Error
    )

  def technical(subErrorCode: ErrorCode, exception: Option[Throwable]): CreateBookingResponse =
    CreateBookingResponse(
      success = false,
      errorMessage = exception.map(_.getLocalizedMessage),
      errorCode = Some(ErrorType.TechnicalError.toString),
      subErrorCode = Some(subErrorCode.id),
      status = BookingServerStatus.Error
    )

  def internal(exception: Throwable): CreateBookingResponse =
    CreateBookingResponse(
      success = false,
      errorMessage = Some(exception.getLocalizedMessage),
      errorCode = Some(ErrorType.InternalError.toString),
      subErrorCode = Some(ErrorCode.UnexpectedInternalError.id),
      status = BookingServerStatus.Error
    )

  def duplicate(duplicates: Seq[DuplicateBooking]): CreateBookingResponse =
    CreateBookingResponse(
      success = false,
      errorMessage = Some(ErrorCode.DuplicateBooking.toString),
      errorCode = Some(ErrorType.BadRequest.toString),
      subErrorCode = Some(ErrorCode.DuplicateBooking.id),
      duplicateBookings = duplicates,
      status = BookingServerStatus.DuplicateBooking
    )

  /** For current hotel bookings */
  def success(
      hotels: Seq[Itinerary],
      duplicates: Seq[DuplicateBooking] = Seq(),
      paymentResult: PaymentResult
  ): CreateBookingResponse =
    CreateBookingResponse(
      success = true,
      hotelRooms = Some(hotels),
      itinerary = Some(Itinerary(hotels.head.itineraryId, hotels.flatMap(_.bookings))), // TODO: real status token
      duplicateBookings = duplicates,
      paymentResult = Some(paymentResult),
      status = BookingServerStatus.Created
    )

  def successWithBookingToken(
      hotels: Seq[Itinerary],
      bookingToken: Option[TokenMessage],
      hrStockInformation: Option[String],
      inventoryType: Option[String]
  ): CreateBookingResponse =
    CreateBookingResponse(
      success = true,
      hotelRooms = Some(hotels),
      itinerary = Some(Itinerary(hotels.head.itineraryId, hotels.flatMap(_.bookings))),
      status = BookingServerStatus.Pending,
      supplierResponse = Some(
        SupplierResponse(
          bookingToken = bookingToken,
          hrStockInformation = hrStockInformation,
          inventoryType = inventoryType
        )
      )
    )

  def carSuccessMock(): CreateBookingResponse = {
    val itineraryId = 12181670
    val bookingId   = 51181670
    CreateBookingResponse(
      success = true,
      itinerary = Some(
        Itinerary(
          itineraryId = itineraryId,
          cars = Seq(
            VehicleBooking(
              bookingId = bookingId,
              bookingStatus = CreatedBookingStatus.BookingConfirmed
            )
          )
        )
      ),
      paymentResult = Some(
        PaymentResult(
          continuation = PaymentContinuation(
            gatewayId = Gateway.MyCheck
          ),
          redirect3ds = None
        )
      ),
      status = BookingServerStatus.Created
    )
  }

  def itinerarySuccess(itinerary: Itinerary): CreateBookingResponse =
    CreateBookingResponse(
      success = true,
      hotelRooms = Some(Seq(itinerary)),
      itinerary = Some(itinerary),
      status = BookingServerStatus.Processing
    )

  def redirect3ds(paymentResult: PaymentResult): CreateBookingResponse =
    CreateBookingResponse(
      success = true,
      paymentResult = Some(paymentResult),
      status = BookingServerStatus.Challenge3ds
    )

  def threeDsV2ThirdStepOTPError(paymentResult: PaymentResult): CreateBookingResponse =
    CreateBookingResponse(
      success = true,
      paymentResult = Some(paymentResult),
      status = BookingServerStatus.Challenge_OTP
    )

  /* If subErrorCode is UnknownErrorCode, we want to send a response as TechnicalError so Booking form will handle it as
   * system error */
  def errorFromSupplier(
      errorType: ErrorType,
      subErrorCode: ErrorCode,
      message: Option[String] = None,
      supplierResponse: Option[SupplierResponse] = None
  ): CreateBookingResponse = {
    val errorCode =
      if (
        errorType == AbsErrorType.UnknownErrorType ||
        subErrorCode == ErrorCode.UnknownErrorCode || subErrorCode == ErrorCode.TechnicalError
      )
        ErrorType.TechnicalError.toString
      else errorType.toString

    CreateBookingResponse(
      success = false,
      errorMessage = message.orElse(Some(subErrorCode.toString)),
      errorCode = Some(errorCode),
      subErrorCode = Some(subErrorCode.id),
      status = BookingServerStatus.Error,
      supplierResponse = supplierResponse
    )
  }
}

case class Itinerary(
    itineraryId: ItineraryId = 0,
    @(ApiModelProperty @field)(hidden = true)
    bookings: Seq[HotelBooking] = Seq(), // Should deprecate that
    flights: Seq[FlightBooking] = Seq(),
    cars: Seq[VehicleBooking] = Seq(),
    protections: Seq[ProtectionBooking] = Seq(),
    activities: Seq[ActivityBooking] = Seq.empty,
    @deprecated // pricefreeze code are removed
    priceFreezes: Seq[PriceFreezeBaseBookingCreationResult] = Seq.empty,
    cegFastTracks: Seq[CegFastTrackCreateResult] = Seq.empty,
    addOns: Seq[GenericProductCreateResult] = Seq.empty,
    statusToken: String = "",
    itineraryAssociatedBookingsToken: Option[TokenMessage] = None,
    itineraryDate: Option[DateTime] = None
) {
  @ApiModel(
    description =
      "BookingReceived/BookingConfirmed/BookingCharged/Departed/BookingRejected/BookingCancelledByCustomer/BookingCancelled/BookingTest/TechnicalError"
  )
  @JsonScalaEnumeration(classOf[CreatedBookingStatusType])
  @JsonSerialize(using = classOf[CreatedBookingStatusSerializer])
  @JsonDeserialize(using = classOf[CreatedBookingStatusDeserializer])
  @(ApiModelProperty @field)(dataType = "Int", required = true)
  val status: CreatedBookingStatus.CreatedBookingStatus = {
    val status = bookings.map(_.bookingStatus) ++
      flights.map(_.bookingStatus) ++
      cars.map(_.bookingStatus) ++
      protections.map(_.bookingStatus) ++
      activities.map(_.bookingStatus) ++
      addOns.map(_.bookingStatus)

    if (status.nonEmpty) {
      status.min
    } else {
      CreatedBookingStatus.TechnicalError
    }
  }

  val hotels: Seq[HotelBooking] = bookings
}

case class HotelBooking(
    lineItemId: Int = 0,
    itineraryId: Int = 0,
    bookingId: BookingId = 0,
    productKey: Option[String] = None,
    @ApiModel(
      description =
        "BookingReceived/BookingConfirmed/BookingCharged/Departed/BookingRejected/BookingCancelledByCustomer/BookingCancelled/BookingTest/TechnicalError"
    )
    @JsonScalaEnumeration(classOf[CreatedBookingStatusType])
    @JsonSerialize(using = classOf[CreatedBookingStatusSerializer])
    @JsonDeserialize(using = classOf[CreatedBookingStatusDeserializer])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    bookingStatus: CreatedBookingStatus.CreatedBookingStatus,
    selfServiceUrl: String = "",
    rejectReason: Option[RejectedReason] = None,
    externalBookingId: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    stayPackageType: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    stayType: Option[Int] = None,
    @ApiModel(
      description = "UnConfirmed/Confirmed/Failed/Timeout"
    )
    @JsonScalaEnumeration(classOf[InstantBookingStatusType])
    @JsonSerialize(contentUsing = classOf[InstantBookingStatusSerializer])
    @JsonDeserialize(contentUsing = classOf[InstantBookingStatusDeserializer])
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    instantBookingStatus: Option[InstantBookingStatus.InstantBookingStatus] = None,
    supplierResponse: Option[SupplierResponse] = None,
    @(ApiModelProperty @field)(dataType = "Boolean", required = false)
    isCurrentOperation: Option[Boolean] = None
)

case class FlightBooking(
    bookingId: BookingId = 0,
    @ApiModel(
      description =
        "BookingReceived/BookingConfirmed/BookingCharged/Departed/BookingRejected/BookingCancelledByCustomer/BookingCancelled/BookingTest/TechnicalError"
    )
    @JsonScalaEnumeration(classOf[CreatedBookingStatusType])
    @JsonSerialize(using = classOf[CreatedBookingStatusSerializer])
    @JsonDeserialize(using = classOf[CreatedBookingStatusDeserializer])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    bookingStatus: CreatedBookingStatus.CreatedBookingStatus,
    pnr: Option[PNR] = None,
    rejectReason: Option[RejectedReason] = None,
    flightToken: Option[String] = None
)

case class VehicleBooking(
    bookingId: BookingId = 0,
    @ApiModel(
      description =
        "BookingReceived/BookingConfirmed/BookingCharged/Departed/BookingRejected/BookingCancelledByCustomer/BookingCancelled/BookingTest/TechnicalError"
    )
    @JsonScalaEnumeration(classOf[CreatedBookingStatusType])
    @JsonSerialize(using = classOf[CreatedBookingStatusSerializer])
    @JsonDeserialize(using = classOf[CreatedBookingStatusDeserializer])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    bookingStatus: CreatedBookingStatus.CreatedBookingStatus,
    rejectReason: Option[RejectedReason] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    postBookingStatus: Option[Int] = None
)

case class PaymentResult(
    continuation: PaymentContinuation,
    redirect3ds: Option[Payment3DSResponse],
    redirectPayment: Option[PaymentRedirect] = None,
    clientSideJavascriptPayment: Option[PaymentClientSideJavascriptResponse] = None,
    @(ApiModelProperty @field)(hidden = true)
    @JsonIgnore
    topic: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "Boolean")
    requireCvv: Option[Boolean] = None,
    @(ApiModelProperty @field)(
      dataType = "Int",
      value =
        "indicate payment 3ds type of payment result, possible values could be found: https://github.agodadev.io/ebe/mpb-commons/blob/24d279af95bdd138d0b495d8397951b4e39dbfdb/schema/src/main/protobuf/Enum.proto#L63"
    )
    payment3ds2Type: Option[Int] = None,
    otpRequired: Option[OTPResult] = None,
    wrongOTP: Option[OTPResult] = None,
    @JsonScalaEnumeration(classOf[ThirdPartyStatusType])
    @JsonSerialize(contentUsing = classOf[ThirdPartyStatusSerializer])
    @JsonDeserialize(contentUsing = classOf[ThirdPartyStatusDeserializer])
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    thirdPartyStatus: Option[ThirdPartyStatus] = None,
    @deprecated("No longer a part of payment result, moved to status response")
    @(ApiModelProperty @field)(dataType = "Boolean")
    isReadyForPaymentRetry: Option[Boolean] = None,
    @ApiModel(description = "gateway reason for unsuccessful payment")
    paymentReason: Option[PaymentReason] = None
)

case class PartnerPaymentResult(
    redirectPayment: Option[PaymentRedirect] = None,
    @(ApiModelProperty @field)(hidden = true)
    @JsonIgnore
    topic: Option[Int] = None,
    @JsonScalaEnumeration(classOf[ThirdPartyStatusType])
    @JsonSerialize(contentUsing = classOf[ThirdPartyStatusSerializer])
    @JsonDeserialize(contentUsing = classOf[ThirdPartyStatusDeserializer])
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    thirdPartyStatus: Option[ThirdPartyStatus] = None
)

case class OTPResult(
    isResendOTPAllowed: Boolean
)

case class CrossSellResult(
    @(ApiModelProperty @field)(dataType = "Int")
    reasonId: Option[Int] = None
)

case class BorBookingResult(
    @(ApiModelProperty @field)(dataType = "Int")
    status: Option[Int] = None
)

case class ProtectionBooking(
    bookingId: BookingId,
    @deprecated("no usage on client suspected, will remove it once confirmed for legacy clients")
    protectionTypeId: Int,
    @ApiModel(
      description =
        "BookingReceived/BookingConfirmed/BookingCharged/Departed/BookingRejected/BookingCancelledByCustomer/BookingCancelled/BookingTest/TechnicalError"
    )
    @JsonScalaEnumeration(classOf[CreatedBookingStatusType])
    @JsonSerialize(using = classOf[CreatedBookingStatusSerializer])
    @JsonDeserialize(using = classOf[CreatedBookingStatusDeserializer])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    bookingStatus: CreatedBookingStatus.CreatedBookingStatus,
    @(ApiModelProperty @field)(dataType = "Long")
    productBookingId: Option[Long] = Some(0L), // TODO clean up to remove
    @deprecated("no usage on client suspected, will remove it once confirmed for legacy clients")
    supplierPolicyId: Option[String] = None
)

case class ActivityBooking(
    bookingId: BookingId = 0,
    @ApiModel(
      description =
        "BookingReceived/BookingConfirmed/BookingCharged/Departed/BookingRejected/BookingCancelledByCustomer/BookingCancelled/BookingTest/TechnicalError"
    )
    @JsonScalaEnumeration(classOf[CreatedBookingStatusType])
    @JsonSerialize(using = classOf[CreatedBookingStatusSerializer])
    @JsonDeserialize(using = classOf[CreatedBookingStatusDeserializer])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    bookingStatus: CreatedBookingStatus.CreatedBookingStatus,
    rejectReason: Option[RejectedReason] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    postBookingStatus: Option[Int] = None // Activity don't have post-booking flow for now
)

case class PriceFreezeBaseBookingCreationResult(
    bookingId: BookingId = 0,
    @ApiModel(
      description =
        "BookingReceived/BookingConfirmed/BookingCharged/Departed/BookingRejected/BookingCancelledByCustomer/BookingCancelled/BookingTest/TechnicalError"
    )
    @JsonScalaEnumeration(classOf[CreatedBookingStatusType])
    @JsonSerialize(using = classOf[CreatedBookingStatusSerializer])
    @JsonDeserialize(using = classOf[CreatedBookingStatusDeserializer])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    bookingStatus: CreatedBookingStatus.CreatedBookingStatus
)

case class CegFastTrackCreateResult(
    bookingId: BookingId = 0,
    @ApiModel(
      description =
        "BookingReceived/BookingConfirmed/BookingCharged/Departed/BookingRejected/BookingCancelledByCustomer/BookingCancelled/BookingTest/TechnicalError"
    )
    @JsonScalaEnumeration(classOf[CreatedBookingStatusType])
    @JsonSerialize(using = classOf[CreatedBookingStatusSerializer])
    @JsonDeserialize(using = classOf[CreatedBookingStatusDeserializer])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    bookingStatus: CreatedBookingStatus.CreatedBookingStatus
)

case class GenericProductCreateResult(
    bookingId: BookingId = 0,
    @ApiModel(
      description =
        "BookingReceived/BookingConfirmed/BookingCharged/Departed/BookingRejected/BookingCancelledByCustomer/BookingCancelled/BookingTest/TechnicalError"
    )
    @JsonScalaEnumeration(classOf[CreatedBookingStatusType])
    @JsonSerialize(using = classOf[CreatedBookingStatusSerializer])
    @JsonDeserialize(using = classOf[CreatedBookingStatusDeserializer])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    bookingStatus: CreatedBookingStatus.CreatedBookingStatus,
    productTypeId: Int
)

case class ProtectionStatusResult(
    protectionBookingId: Long,
    price: CurrencyOption,
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    @JsonDeserialize(using = classOf[ProtectionBookingStateDeserializer])
    @JsonSerialize(using = classOf[ProtectionBookingStateSerializer])
    protectionBookingStateId: ProtectionBookingState,
    productBookingId: Long,
    productType: Int,
    protectionTypeId: Int,
    @(ApiModelProperty @field)(dataType = "Double")
    marginAmount: Option[Double],
    @(ApiModelProperty @field)(dataType = "Double")
    marginPercentage: Option[Double],
    usdAmount: Double,
    @(ApiModelProperty @field)(dataType = "Double")
    usdMarginAmount: Option[Double],
    @(ApiModelProperty @field)(dataType = "Int")
    supplierId: Option[Int],
    @(ApiModelProperty @field)(dataType = "Int")
    subSupplierId: Option[Int],
    supplierSearchId: String,
    supplierPolicyId: Option[String],
    @(ApiModelProperty @field)(dataType = "Int")
    supplierResultCode: Option[Int],
    supplierSpecificData: Option[String],
    @(ApiModelProperty @field)(dataType = "Long")
    multiProductId: Option[Long],
    @(ApiModelProperty @field)(dataType = "Int")
    platformId: Option[Int],
    @(ApiModelProperty @field)(dataType = "Int")
    languageId: Option[Int],
    @(ApiModelProperty @field)(dataType = "Int")
    cid: Option[Int],
    recCreatedWhen: DateTime,
    recModifiedWhen: Option[DateTime]
)

case class Payment3DSResponse(
    @ApiModel(description = "The fields need to post to bank issuer's website")
    post3DFields: Map[String, String],
    @ApiModel(description = "The issuer URL for post to bank issuer's website for 3D Secure")
    issuerUrl: String,
    @ApiModel(description = "the fields which are required from bank issuer's post back")
    require3DFields: Seq[String],
    @ApiModel(
      description =
        "The field name which contain the value of postback URL (Agoda url) to be able to collect the Require3DFields from bank issuer's website"
    )
    returnUrlField: String,
    @ApiModel(
      description =
        "this token will be used in 3ds - Abs flow. It contains an encryption of bookingId, sessionId and itinerary in JSON format"
    )
    referenceToken: Option[String] = None,
    @ApiModel(description = "this token will be used in 3ds2 where device finger print is needed")
    internalToken: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    processed3DSOption: Option[Int] = None,
    @ApiModel(
      description =
        "in processing 3DS1 Payment, after customer finish challenge in bank site, bank will make Http Post this url to send notification"
    )
    bankCallback3DS1Url: Option[String] = None
)
object Payment3DSResponse {
  def apply(redirect3dsParams: Redirect3dsParams, returnUrl: String): Payment3DSResponse =
    Payment3DSResponse(
      post3DFields = redirect3dsParams.postFields,
      issuerUrl = redirect3dsParams.url,
      require3DFields = redirect3dsParams.requiredFields.toSeq,
      returnUrlField = returnUrl
    )
}

case class DuplicateBooking(
    @ApiModel(description = "Hotel/Flight/Vehicle")
    @JsonDeserialize(using = classOf[BookingElementDeserializer])
    @JsonSerialize(using = classOf[BookingElementSerializer])
    @JsonScalaEnumeration(classOf[BookingElementType])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    bookingElement: BookingElement,
    bookingId: BookingId = 0,
    bookingDate: DateTime,
    selfServiceUrl: String = "",
    statusToken: Option[String] = None
)

case class SupplierResponse(
    @ApiModel(
      description =
        "booking token store all data that required to complete the book and to cut or cancel allotment through ABS"
    )
    bookingToken: Option[TokenMessage] = None,
    @ApiModel(
      description = "this is a JSON object of room information converted to a string that is returned by supplier API"
    )
    hrStockInformation: Option[String] = None,
    @ApiModel(description = "inventoryType that ABS cut the allot")
    inventoryType: Option[String] = None,
    @ApiModel(description = "error message from supplier")
    supplierRemark: Option[String] = None,
    @ApiModel(description = "Product request id")
    id: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    allotmentStatus: Option[Int] = None
)

case class PaymentReason(
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    groupCode: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "String", required = false)
    code: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "String", required = false)
    message: Option[String] = None
)

// $COVERAGE-OFF$
case class PaymentClientSideJavascriptResponse(
    tokenConfiguration: TokenConfiguration
)
case class TokenConfiguration(
    externalScriptUrl: String,
    @JsonDeserialize(using = classOf[LocalScriptTypeDeserializer])
    @JsonSerialize(using = classOf[LocalScriptTypeSerializer])
    @JsonScalaEnumeration(classOf[LocalScriptTypeTypeRef])
    @ApiModel(description = "indicates which client-side script to use. (GMO/SoftBank)")
    @(ApiModelProperty @field)(dataType = "Int")
    localScriptType: LocalScriptType,
    tokenParameters: Option[Map[String, String]] = None
)
// $COVERAGE-ON$

object ErrorType extends Enumeration {
  type ErrorType = Value

  val UnknownError    = Value(0, "Unknown Error")      // We don't really know the reason
  val ValidationError = Value(1, "Validation Error")   // Input data is not correct
  val BadRequest      = Value(2, "Bad request")        // No permissions
  val InternalError   = Value(3, "Internal Error")     // Error inside BAPI processing
  val TechnicalError  = Value(4, "Technical Error")    // Error working with external services / DB
  val ProcessingError = Value(5, "Processing Error")   // Can't be processed by non-technical reason
  val Unauthorized    = Value(6, "Unauthorized Error") // No permissions
}

object LocalScriptType extends Enumeration {
  type LocalScriptType = Value
  val GMO      = Value(1)
  val SoftBank = Value(2)
}

class LocalScriptTypeTypeRef      extends TypeReference[LocalScriptType.type]
class LocalScriptTypeSerializer   extends EnumAsIntSerializer[LocalScriptType]
class LocalScriptTypeDeserializer extends EnumAsIntDeserializer(LocalScriptType)
