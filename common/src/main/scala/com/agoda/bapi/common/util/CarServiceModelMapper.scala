package com.agoda.bapi.common.util

import com.agoda.bapi.common.message.creation.Customer
import com.agoda.bapi.common.model.car.{CarBillingInformation, CarBillingInformationDetails, CarDriverInformation, CarDriversInformation, CreditCardBillingInformation, CustomerInformation, InformationDetails}
import com.agoda.bapi.common.model.payment.CreditCardBillingInfo
import com.agoda.capi.enigma.shared_model.car.billinginfo.{CarBillingInfo, CarBillingInfoDetails, CreditCardBillingInfo => CapiCreditCardBillingInfo, CustomerInfo, Information}
import com.agoda.capi.enigma.shared_model.car.driver.{CarDriverInfo, DriverInfo}

object CarServiceModelMapper {
  implicit class DriverInfoExtension(driverInfo: CarDriverInfo) {
    def toCarDriversInformation: CarDriversInformation =
      CarDriversInformation(
        bookingId = driverInfo.bookingId.value,
        drivers = driverInfo.drivers.map((driver: DriverInfo) =>
          CarDriverInformation(
            bookingId = driver.bookingId.value,
            id = driver.id,
            title = driver.title,
            firstName = driver.firstName,
            middleName = driver.middleName,
            lastName = driver.lastName,
            gender = driver.gender,
            age = driver.age,
            licenseNo = driver.licenseNo,
            countryOfResidenceId = driver.countryOfResidence
          )
        )
      )
  }

  implicit class BillingInfoExtension(carBillingInfo: CarBillingInfo) {
    def toCarBillingInformation: CarBillingInformation =
      CarBillingInformation(
        bookingId = carBillingInfo.bookingId,
        details = CarBillingInformationDetails(
          customerInfo = CustomerInformation(
            creditCardBillingInfo = CreditCardBillingInformation(
              billingAddress1 = carBillingInfo.details.customerInfo.creditCardBillingInfo.billingAddress1,
              billingAddress2 = carBillingInfo.details.customerInfo.creditCardBillingInfo.billingAddress2,
              billingPostalCode = carBillingInfo.details.customerInfo.creditCardBillingInfo.billingPostalCode,
              billingCity = carBillingInfo.details.customerInfo.creditCardBillingInfo.billingCity,
              billingState = carBillingInfo.details.customerInfo.creditCardBillingInfo.billingState,
              billingCountry = carBillingInfo.details.customerInfo.creditCardBillingInfo.billingCountry
            ),
            information = InformationDetails(
              title = carBillingInfo.details.customerInfo.information.title,
              firstName = carBillingInfo.details.customerInfo.information.firstName,
              middleName = carBillingInfo.details.customerInfo.information.middleName,
              lastName = carBillingInfo.details.customerInfo.information.lastName,
              email = carBillingInfo.details.customerInfo.information.email,
              phone = carBillingInfo.details.customerInfo.information.phone
            )
          )
        )
      )
  }

  def mapCarBillingInfoDetails(
      creditCardBillingInfo: CreditCardBillingInfo,
      customerInfo: Customer
  ): CarBillingInfoDetails = {
    CarBillingInfoDetails(
      CustomerInfo(
        CapiCreditCardBillingInfo(
          billingAddress1 = creditCardBillingInfo.billingAddress1,
          billingAddress2 = creditCardBillingInfo.billingAddress2,
          billingPostalCode = creditCardBillingInfo.billingPostalcode,
          billingCity = creditCardBillingInfo.billingCity,
          billingState = creditCardBillingInfo.billingState,
          billingCountry = creditCardBillingInfo.billingCountryId match {
            case Some(id) => id.toString
            case None     => ""
          }
        ),
        Information(
          title = Some(customerInfo.title),
          firstName = Some(customerInfo.firstname),
          middleName = Some(customerInfo.middlename),
          lastName = Some(customerInfo.lastname),
          email = Some(customerInfo.email),
          phone = Some(customerInfo.phoneFormat)
        )
      )
    )
  }
}
