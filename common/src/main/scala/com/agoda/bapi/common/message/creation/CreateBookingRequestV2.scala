package com.agoda.bapi.common.message.creation

import com.agoda.bapi.common.message.creation.LanguageScriptType.LanguageScriptType
import com.agoda.bapi.common.message.setupBooking.LoyaltyRequest
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.message.{BookingCreationFlowContext, DeviceContext}
import com.agoda.bapi.common.model.{Storefront, UserContext}
import com.agoda.bapi.common.model.creation._
import com.agoda.bapi.common.model.payment.PaymentToken
import com.agoda.bapi.common.model.product.BookingFlow.BookingFlow
import com.agoda.bapi.common.model.product.{BookingFlowDeserializer, BookingFlowOptionDeserializer, BookingFlowType}
import com.agoda.bapi.common.util.{ProductEntryDeserializer, ProductEntrySerializer}
import com.agoda.bapi.common.{OptionLocalDateDeserializer, OptionLocalDateSerializer}
import com.agoda.mpb.common.MultiProductType.MultiProductType
import com.agoda.mpb.common.serialization.Serialization.{MultiProductOptionTypeDeserializer, MultiProductTypeReference}
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.agoda.upi.models.enums.ProductEntry
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.annotation.{JsonDeserialize, JsonSerialize}
import com.fasterxml.jackson.module.scala.JsonScalaEnumeration
import io.swagger.annotations.{ApiModel, ApiModelProperty}
import org.joda.time.LocalDate

import scala.annotation.meta.field

@ApiModel(parent = classOf[BookingCreationFlowContext])
final case class CreateBookingRequestV2(
    // TODO-Multiproduct - Context parameters to set
    agentAssist: Option[AgentAssist],
    customer: CustomerV2,
    payment: BookingPaymentV2,
    products: ProductsV2,
    affiliate: Option[Affiliate],
    instantBooking: Option[InstantBook],
    fraudInfo: Option[FraudInfoV2],
    // attribution: Seq[AttributionV2], TODO-Multiproduct - need to finalize.
    @ApiModel(description = "Multiple product token")
    bookingToken: Option[TokenMessage] = None,
    userContext: Option[UserContext] = None,
    attributions: Option[Attribution] = None,
    @(ApiModelProperty @field)
    referralUrl: Option[String] = None,
    @(ApiModelProperty @field)
    trackingInfo: Option[TrackingInfo] = None,
    supplierData: Option[SupplierData] = None,
    @(ApiModelProperty @field)(dataType = "Boolean")
    allowDuplicateBooking: Option[Boolean] = Some(false),
    override val correlationId: Option[String] = None,
    override val deviceContext: Option[DeviceContext] = None,
    override val bookingContext: Option[BookingCreationContext] = None,
    externalLoyaltyRequest: Option[LoyaltyRequest] = None,
    bookingHoldingPartnerNames: Option[Seq[BookingHoldingPartner]] = None,
    isTestBooking: Boolean = false // Used by Finance to ignore bookings
) extends BookingCreationFlowContext {
  def getStoreFrontId(clientId: Int): Option[Int] = {
    deviceContext.map(context => Storefront.mapToStoreFrontId(context.deviceTypeId, clientId))
  }

  def withMaskedBookingToken(): CreateBookingRequestV2 =
    this.copy(bookingToken = bookingToken.map(_.copy(token = "****")))
}

final case class BirthInfo(
    @JsonSerialize(using = classOf[OptionLocalDateSerializer])
    @JsonDeserialize(using = classOf[OptionLocalDateDeserializer])
    birthDate: Option[LocalDate],
    @(ApiModelProperty @field)(dataType = "Int")
    birthPlace: Option[Int]
)

final case class PhoneContact(
    countryCallingCode: String,
    phoneNumber: String
)

final case class TrackingInfo(tag: Option[String])

final case class CustomerV2(
    firstName: String,
    lastName: String,
    email: String,
    countryId: Int,
    title: Option[String] = None,
    middleName: Option[String] = None,
    @deprecated("use phoneContact")
    phoneFormat: Option[String] = None,
    faxFormat: Option[String] = None,
    address1: Option[String] = None,
    address2: Option[String] = None,
    postcode: Option[String] = None,
    region: Option[String] = None,
    state: Option[String] = None,
    city: Option[String] = None,
    area: Option[String] = None,
    birthInfo: Option[BirthInfo] = None,
    phoneContact: Option[PhoneContact] = None,
    @ApiModel(description = "Marketing email news letter opt-In preference")
    @(ApiModelProperty @field)(dataType = "Boolean")
    isNewsletterOptIn: Option[Boolean] = None,
    @ApiModel(
      description =
        "state ID from our geo2_state table - in Japan these records store Japanese prefectures and could be used to build a dropdown list on BF"
    )
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    stateId: Option[Int] = None,
    emergencyPhoneNumber: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    age: Option[Int] = None,
    @ApiModel(description = "gender is PII data. Gender will be M = Male, F = Female or None if data doesn't exist")
    gender: Option[String] = None,
    localizedNames: Option[Seq[LocalizedNameV2]] = None,
    externalMemberId: Option[String] = None,
    b2bCustomerEmail: Option[String] = None
)

final case class BookingPaymentV2(
    @ApiModel(description = "Indicates the credit card type.")
    @JsonSerialize(using = classOf[PaymentMethodSerializer])
    @JsonDeserialize(using = classOf[PaymentMethodDeserializer])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    method: PaymentMethod,
    creditCard: Option[CreditCardV2],
    @ApiModel(description = "Token to reference to the credit card information from Agoda Pay via CC API")
    ccToken: Option[String] = None,
    @ApiModel(description = "front end redirect payment url information")
    paymentRedirect: Option[PaymentRedirectRequest] = None,
    @(ApiModelProperty @field)(dataType = "Int", value = "Payment GatewayId for payment processing", example = "8")
    paymentGatewayId: Option[Int] = None,
    @(ApiModelProperty @field)(
      dataType = "Int",
      value = "Payment GatewayInfoId for payment processing",
      example = "823"
    )
    paymentGatewayInfoId: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "Int", value = "MerchantPlug-in for payment processing", example = "1")
    mpiId: Option[Int] = None,
    @ApiModel(description = "User input fields required for payment method")
    requiredFields: Option[Map[String, String]] = None,
    paymentToken: Option[PaymentToken] = None,
    nonCard: Option[NonCardV2] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    paymentChannel: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "Boolean")
    disableCurrencyValidation: Option[Boolean] = Some(false)
)

final case class PaymentRedirectRequest(
    successUrl: String,
    cancelUrl: String,
    @ApiModel(description = "Fields required for specific payment method")
    requiredPostFields: Option[Map[String, String]] = None
)

final case class NonCardV2(
    @(ApiModelProperty @field)(dataType = "Long")
    ccofId: Option[Long] = None,
    @(ApiModelProperty @field)(dataType = "Boolean")
    shouldSave: Option[Boolean] = None
)

final case class CreditCardV2(
    detail: Option[CreditCardDetail],
    shouldSave: Boolean,
    payment3DS: Option[Payment3DSRequestV2],
    @(ApiModelProperty @field)(dataType = "Int")
    installmentPlanId: Option[Int] = None,
    installmentPlanCode: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "Long")
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    aabCCReferenceId: Option[Long] = None
) {
  @(ApiModelProperty @field)(hidden = true)
  @JsonIgnore
  def isCCOF: Boolean =
    detail.exists(det => det.ccofId.exists(id => id != 0) || det.creditCardNumber.exists(_.nonEmpty) && shouldSave)
}

final case class CreditCardDetail(
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    @(ApiModelProperty @field)(dataType = "Long")
    ccofId: Option[Long],
    cardHolderName: Option[String],
    creditCardNumber: Option[String],
    expiryDate: Option[CreditCardExpiration],
    securityCode: Option[String],
    billingAddress1: Option[String],
    billingAddress2: Option[String],
    billingCity: Option[String],
    billingState: Option[String],
    @(ApiModelProperty @field)(dataType = "Int")
    billingCountryId: Option[Int],
    billingPostalCode: Option[String],
    phoneNumber: Option[String],
    issuingBank: Option[IssuingBank]
)

final case class IssuingBank(
    name: String
)

final case class CreditCardExpiration(
    month: Int,
    year: Int
) {
  override def toString: String = s"%02d/%d".format(month, year)
}

final case class Payment3DSRequestV2(
    @ApiModel(description = "If 3ds allowed/required")
    @JsonSerialize(using = classOf[Payment3DSOptionSerializer])
    @JsonDeserialize(using = classOf[Payment3DSOptionDeserializer])
    @JsonScalaEnumeration(classOf[Payment3DSOptionType])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    payment3DSOption: Payment3DSOption.Payment3DSOption,
    acceptHeader: String = "",
    userAgent: String = "",
    @(ApiModelProperty @field)(dataType = "String", value = "client browser language from js", example = "TH")
    language: Option[Option[String]] = None,
    @(ApiModelProperty @field)(dataType = "Int", value = "client browser language from js", example = "24")
    colorDepth: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "Int", value = "client browser screen width from js", example = "723")
    screenHeight: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "Int", value = "client browser screen height from js", example = "1536")
    screenWidth: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "Int", value = "client browser screen height from js", example = "0")
    timeZoneOffset: Option[Int] = None,
    @(ApiModelProperty @field)(
      dataType = "String",
      value = "URL that customer will be landed after finish payment 3DS2 challenge.",
      example = "http://sampleUrl/gateway/ThreeDS2Notification"
    )
    returnUrl: Option[String] = None,
    @(ApiModelProperty @field)(
      dataType = "Boolean",
      value = "client browser information from js (window.navigator.javaEnabled())"
    )
    javaEnabled: Option[Boolean] = None,
    @(ApiModelProperty @field)(
      dataType = "Boolean",
      value = "client browser information from js (window.navigator.javaEnabled())"
    )
    javaScriptEnabled: Option[Boolean] = None,
    @(ApiModelProperty @field)(
      dataType = "List[Int]",
      value = "List of MerchantPlugIn for payment processing that client could support",
      example = "[1]"
    )
    supportedMPIs: Option[Seq[Int]] = None,
    @(ApiModelProperty @field)(
      dataType = "String",
      value = "URL that customer will be landed after finish payment 3DS1 challenge from Bank OTP page.",
      example = "http://sampleUrl/threeDS1Notificaiton"
    )
    bankCallback3DS1Url: Option[String] = None,
    @(ApiModelProperty @field)(
      dataType = "String",
      value = "URL that will receive notification for device collection in 3ds2",
      example = "http://sampleUrl/threeDS2DeviceCollectionNotification"
    )
    deviceCollectionUrl: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    affiliatePaymentModel: Option[Int] = None
)

final case class Affiliate(
    @ApiModel(description = "B2C/B2B/NET")
    @JsonDeserialize(using = classOf[AffiliateModelDeserializer])
    @JsonSerialize(using = classOf[AffiliateModelSerializer])
    @JsonScalaEnumeration(classOf[AffiliateModelType])
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    model: AffiliateModel.AffiliateModel,
    @ApiModel(description = "None/Invoice/CorporateCard/CustomerCard")
    @JsonDeserialize(using = classOf[AffiliatePaymentMethodDeserializer])
    @JsonSerialize(using = classOf[AffiliatePaymentMethodSerializer])
    @JsonScalaEnumeration(classOf[AffiliatePaymentMethodType])
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    paymentMethod: AffiliatePaymentMethod.AffiliatePaymentMethod,
    isTravelAgency: Boolean,
    addDelay: Boolean,
    @ApiModel(description = "Additional fields to send to ABS during provisioning")
    additionalSupplierInfo: Map[String, String] = Map.empty
)

final case class ProductsV2(
    propertyItems: Seq[PropertyItemV2] = Seq.empty,
    flightItems: Seq[FlightItemV2] = Seq.empty,
    carItems: Option[Seq[CarItemV2]] = None,
    activitiesItems: Option[Seq[ActivitiesItemV2]] = None
)

final case class FlightItemV2(
    id: String,
    pax: Option[Seq[FlightPax]]
)

final case class PropertyItemV2(
    id: String,
    specialRequest: Option[SpecialRequests],
    guests: Seq[HotelGuest],
    partnerLoyalty: Option[PartnerLoyaltyPointV2] = None,
    @ApiModel(description = "For NHA bookings, the user will send hello message to the host.")
    greetingMessage: Option[String] = None,
    occupancy: Option[Seq[Occupancy]] = None
)

final case class CarItemV2(
    id: String,
    driver: CarDriver
)

final case class PartnerLoyaltyPointV2(
    programId: Int,
    membershipId: String
)

final case class FraudInfoV2(
    @ApiModel(description = "Serialized Device Fingerprint Data")
    deviceFingerprintData: Option[String],
    @ApiModel(description = "Session Id for Fraud MFA check")
    tmxSessionId: Option[String] = None,
    @ApiModel(description = "Forter Token")
    forterToken: Option[String] = None
)

final case class LocalizedNameV2(
    languageId: Int,
    @ApiModel(description = "Indicates the script type. (Latin/Kanji/Kana)")
    @JsonDeserialize(using = classOf[LanguageScriptTypeDeserializer])
    @JsonSerialize(using = classOf[LanguageScriptTypeSerializer])
    @JsonScalaEnumeration(classOf[LanguageScriptTypeRef])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    scriptId: LanguageScriptType,
    firstname: String,
    lastname: String
)

final case class BookingCreationContext(
    sessionId: String,
    userAgent: UserAgent,
    acceptHeader: Option[String] = None,
    @ApiModel(description = "Indicates booking flow")
    @JsonDeserialize(using = classOf[BookingFlowOptionDeserializer])
    @JsonScalaEnumeration(classOf[BookingFlowType])
    @(ApiModelProperty @field)(dataType = "String", required = false)
    bookingFlow: Option[BookingFlow] = None,
    @ApiModel(description = "Indicates multi product type")
    @JsonDeserialize(using = classOf[MultiProductOptionTypeDeserializer])
    @JsonScalaEnumeration(classOf[MultiProductTypeReference])
    @(ApiModelProperty @field)(dataType = "String", required = false)
    multiProductType: Option[MultiProductType] = None,
    @(ApiModelProperty @field)(dataType = "Long", hidden = true, required = false)
    itineraryId: Option[Long] = None,
    @(ApiModelProperty @field)(dataType = "String", required = false)
    bookingSessionId: Option[String] = None,
    @ApiModel(description = "In case of webview, this will represent browser user agent")
    browserUserAgent: Option[UserAgent] = None,
    @ApiModel(description = "in case of webview, this will represent native app user agent")
    appUserAgent: Option[UserAgent] = None,
    @(ApiModelProperty @field)(dataType = "String", required = false)
    analyticsSessionId: Option[String] = None,
    @ApiModel(description = "Indicates product entry type")
    @JsonSerialize(using = classOf[ProductEntrySerializer])
    @JsonDeserialize(using = classOf[ProductEntryDeserializer])
    @(ApiModelProperty @field)(dataType = "String", required = false)
    productEntry: Option[ProductEntry] = None
)

final case class Attribution(data: Seq[AttributionV2] = Seq.empty)

final case class ActivitiesItemV2(
    id: String,
    pax: Option[Seq[ActivityPax]],
    bookingAnswers: Option[Seq[ActivityBookingAnswer]],
    languageGuide: Option[ActivityLanguageGuideAnswer]
)
