package com.agoda.bapi.common.model.product

import com.agoda.bapi.common.exception.BookingTypeNotFoundException
import com.agoda.bapi.common.message.creation.{CreateBookingRequest, Itinerary, Products}
import com.agoda.bapi.common.message.setupBooking.{ProductsRequest, SetupBookingRequest}
import com.agoda.bapi.common.model.WhiteLabelInfo
import com.agoda.bapi.common.model.activity.ActivityBookingToken
import com.agoda.bapi.common.model.addOn.{AddOnBookingToken => GenericAddOnBookingToken}
import com.agoda.bapi.common.model.car.CarBookingToken
import com.agoda.bapi.common.model.creation.BAPIBooking
import com.agoda.bapi.common.model.flight.FlightBookingToken
import com.agoda.bapi.common.model.product.BookingFlow.BookingFlow
import com.agoda.bapi.common.model.product.ProductTypeEnum.ProductTypeEnum
import com.agoda.bapi.common.model.protection.ProtectionRequestItemOptInValue
import com.agoda.bapi.common.model.tripProtection.TripProtectionToken
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.token.common.ProductTokenKey
import com.agoda.common.itineraryContext.ItineraryContext
import com.agoda.mpb.common.models.state.{ProductType => MpbProductType}

object BookingRequestTypeResolver {

  final case class MultiProductFlowDefinition(
      numberOfProperty: Int = 0,
      numberOfFlight: Int = 0,
      numberOfTripProtection: Int = 0,
      numberOfCar: Int = 0,
      numberOfActivity: Int = 0,
      numberOfCegFastTrack: Int = 0,
      numberOfAddOns: Int = 0,
      isPackagingRequest: Boolean = false
  )

  object MultiProductFlowDefinition {
    val emptyProducts: MultiProductFlowDefinition = MultiProductFlowDefinition(
      numberOfProperty = 0,
      numberOfFlight = 0,
      numberOfTripProtection = 0,
      numberOfCar = 0,
      numberOfActivity = 0,
      numberOfCegFastTrack = 0,
      isPackagingRequest = false
    )
    val singlePropertyFlowDefinition: MultiProductFlowDefinition       = emptyProducts.copy(numberOfProperty = 1)
    val singleFlightFlowDefinition: MultiProductFlowDefinition         = emptyProducts.copy(numberOfFlight = 1)
    val singleTripProtectionFlowDefinition: MultiProductFlowDefinition = emptyProducts.copy(numberOfTripProtection = 1)
    val singleVehicleFlowDefinition: MultiProductFlowDefinition        = emptyProducts.copy(numberOfCar = 1)
    val singleActivityFlowDefinition: MultiProductFlowDefinition       = emptyProducts.copy(numberOfActivity = 1)
    val hackerFareDefinition: MultiProductFlowDefinition               = emptyProducts.copy(numberOfFlight = 2)
    val multiFlightsWithProtection: MultiProductFlowDefinition =
      emptyProducts.copy(numberOfFlight = 2, numberOfTripProtection = 1, numberOfAddOns = 1)
  }

  // Remove this when UNIBF-946 is taken
  // this resolver used for creation token
  /* Another Map so that we can have different bookingFlow between Setup & Create. Should remove this & rely on context
   * instead. */
  // Useful for Hacker Fare so that we can have SingleFlight for Setup, but HackerFare for Create.
  private val multiProductFlowDefinitionMap = Map(
    MultiProductFlowDefinition(numberOfProperty = 1) -> BookingFlow.SingleProperty,
    MultiProductFlowDefinition(numberOfFlight = 1)   -> BookingFlow.SingleFlight,
    MultiProductFlowDefinition(
      numberOfProperty = 1,
      numberOfFlight = 1,
      isPackagingRequest = true
    )                                                                          -> BookingFlow.Package,
    MultiProductFlowDefinition(numberOfProperty = 2)                           -> BookingFlow.MixAndSave,
    MultiProductFlowDefinition(numberOfFlight = 2)                             -> BookingFlow.Hackerfare,
    MultiProductFlowDefinition(numberOfFlight = 1, numberOfTripProtection = 1) -> BookingFlow.FlightWithProtection,
    MultiProductFlowDefinition(
      numberOfFlight = 2,
      numberOfTripProtection = 1
    )                                                                           -> BookingFlow.MultiFlightsWithProtection,
    MultiProductFlowDefinition(numberOfCar = 1)                                 -> BookingFlow.SingleVehicle,
    MultiProductFlowDefinition(numberOfProperty = 2, isPackagingRequest = true) -> BookingFlow.MultiHotel,
    MultiProductFlowDefinition(numberOfActivity = 1)                            -> BookingFlow.SingleActivity
  )

  private def getMultiProductFlowDefinitionMapV2(
      isFP2CartMigration: Boolean,
      isHF2CartMigration: Boolean
  ): Map[MultiProductFlowDefinition, BookingFlow] = {
    val flightWithProtectionDef = MultiProductFlowDefinition(numberOfFlight = 1, numberOfTripProtection = 1) -> {
      if (isFP2CartMigration) BookingFlow.Cart else BookingFlow.FlightWithProtection
    }
    val hackFareDef = MultiProductFlowDefinition(numberOfFlight = 2) -> {
      if (isHF2CartMigration) BookingFlow.Cart else BookingFlow.Hackerfare
    }
    val multiFlightWithProtectionDef =
      MultiProductFlowDefinition(numberOfFlight = 2, numberOfTripProtection = 1) -> {
        if (isHF2CartMigration) BookingFlow.Cart else BookingFlow.MultiFlightsWithProtection
      }
    multiProductFlowDefinitionMapV2 + flightWithProtectionDef + hackFareDef + multiFlightWithProtectionDef
  }

  private val multiProductFlowDefinitionMapV2 = Map(
    MultiProductFlowDefinition(numberOfProperty = 1) -> BookingFlow.SingleProperty,
    MultiProductFlowDefinition(numberOfFlight = 1)   -> BookingFlow.SingleFlight,
    MultiProductFlowDefinition(
      numberOfProperty = 1,
      numberOfFlight = 1,
      isPackagingRequest = true
    )                                                                           -> BookingFlow.Package,
    MultiProductFlowDefinition(numberOfProperty = 2)                            -> BookingFlow.MixAndSave,
    MultiProductFlowDefinition(numberOfCar = 1)                                 -> BookingFlow.SingleVehicle,
    MultiProductFlowDefinition(numberOfProperty = 2, isPackagingRequest = true) -> BookingFlow.MultiHotel,
    MultiProductFlowDefinition(numberOfActivity = 1)                            -> BookingFlow.SingleActivity
  )

  def getFlowType(
      request: SetupBookingRequest,
      featureAware: Option[FeatureAware],
      whiteLabelInfo: WhiteLabelInfo
  ): BookingFlow =
    if (request.productsRequest.packageRequest.isDefined && request.productsRequest.propertyRequests.size > 1)
      BookingFlow.MultiHotel
    else if (request.productsRequest.packageRequest.isDefined) BookingFlow.Package
    else if (
      request.productsRequest.cartPricingContext.isDefined || request.productsRequest
        .hasVehicleWithProtection()
    )
      BookingFlow.Cart
    else if (request.productsRequest.carRequestsOpt.exists(_.size == 1)) BookingFlow.SingleVehicle
    else if (
      request.productsRequest.propertyRequests.nonEmpty && request.productsRequest.propertyRequests.size > 1 && request.productsRequest.flightRequests.isEmpty
    ) BookingFlow.MixAndSave
    else if (request.productsRequest.propertyRequests.nonEmpty && request.productsRequest.propertyRequests.size == 1)
      BookingFlow.SingleProperty
    else if (request.productsRequest.flightRequests.size == 1) {
      if (request.productsRequest.flightRequests.headOption.flatMap(_.instantPriceConfirmRequest).isDefined) {
        BookingFlow.FlightWithProtection
      } else if (featureAware.exists(_.migrateFlightToCartFlow(whiteLabelInfo))) {
        BookingFlow.Cart
      } else {
        BookingFlow.FlightWithProtection
      }
    } else if (request.productsRequest.activityRequests.exists(_.size == 1)) BookingFlow.SingleActivity
    else BookingFlow.Unknown

  // U1: creation token stamping
  def getFlowType(
      propertyCreationBookings: Map[ProductTokenKey, BAPIBooking],
      flightCreationBookings: Map[ProductTokenKey, Seq[FlightBookingToken]],
      tripProtections: Map[ProductTokenKey, TripProtectionToken],
      carCreationBookings: Map[ProductTokenKey, CarBookingToken],
      activityCreationBookings: Map[ProductTokenKey, ActivityBookingToken],
      addOns: Map[ProductTokenKey, GenericAddOnBookingToken],
      whitelabelInfo: WhiteLabelInfo,
      isPackagingRequest: Boolean = false,
      itineraryContext: Option[ItineraryContext] = None,
      featureAware: Option[FeatureAware] = None
  ): BookingFlow = {
    // TODO: remove while integrating "UNIBF-2248"
    val flightItemBookingCount =
      flightCreationBookings.map(_._2.size).sum // Since hackerfare will have 2 items in sequence

    val propertyItemBookingCount   = propertyCreationBookings.size
    val tripProtectionAddOnCount   = addOns.count(_._2.getProductType == MpbProductType.TripProtection)
    val tripProtectionBookingCount = tripProtections.size + tripProtectionAddOnCount
    val carBookingCount            = carCreationBookings.size
    val activityCount              = activityCreationBookings.size

    getFlowTypeBySize(
      propertyItemBookingCount,
      flightItemBookingCount,
      tripProtectionBookingCount,
      carBookingCount,
      activityCount,
      isPackagingRequest,
      featureAware,
      whitelabelInfo
    )
  }

  // create booking v1 supports on single product types
  // used already for measurements
  def getProductType(request: CreateBookingRequest): ProductTypeEnum = {
    val flightItems   = request.products.flightItems.getOrElse(Seq.empty)
    val propertyItems = request.products.propertyItems.getOrElse(Seq.empty)
    val carItems      = request.products.carItems.getOrElse(Seq.empty)
    val activityItems = request.products.activitiesItems.getOrElse(Seq.empty)

    def countNonEmptyProducts(items: Seq[Boolean]): Int = items.count(_ == true)

    val allProducts = Seq(
      flightItems.nonEmpty,
      propertyItems.nonEmpty,
      carItems.nonEmpty,
      activityItems.nonEmpty
    )

    countNonEmptyProducts(allProducts) match {
      case 0 => throw new BookingTypeNotFoundException(Option(request.prebookingId))
      case 1 =>
        if (carItems.nonEmpty) ProductTypeEnum.Car
        else if (activityItems.nonEmpty) ProductTypeEnum.Activity
        else if (flightItems.nonEmpty) ProductTypeEnum.Flight
        else if (propertyItems.nonEmpty) ProductTypeEnum.Property
        else
          throw new BookingTypeNotFoundException(
            Option(request.prebookingId)
          ) // in case of new product OR priceFreezeItems.size > 1
      case _ => ProductTypeEnum.Multi
    }
  }

  // U1: as fallback if token booking flow is empty
  // U2: for TPRM in create to exclude SH
  def getFlowType(request: CreateBookingRequest): BookingFlow = {
    val flow = getFlowType(request.products)
    if (flow == BookingFlow.Unknown)
      throw new BookingTypeNotFoundException(Option(request.prebookingId))
    else
      flow
  }

  private def getFlowType(products: Products): BookingFlow =
    if (products.carItems.exists(_.size == 1)) BookingFlow.SingleVehicle
    else if (
      products.flightItems
        .getOrElse(Seq.empty)
        .nonEmpty && products.propertyItems.getOrElse(Seq.empty).isEmpty
    ) BookingFlow.SingleFlight
    else if (
      products.propertyItems.exists(_.size >= 2)
      && products.flightItems.getOrElse(Seq.empty).isEmpty
    ) BookingFlow.MixAndSave
    else if (
      products.propertyItems
        .getOrElse(Seq.empty)
        .nonEmpty && products.flightItems.getOrElse(Seq.empty).isEmpty
    ) BookingFlow.SingleProperty
    else if (
      products.propertyItems
        .getOrElse(Seq.empty)
        .nonEmpty && products.flightItems.getOrElse(Seq.empty).nonEmpty
    ) BookingFlow.Package
    else
      BookingFlow.Unknown

  // use itinerary data since this is created specifically to resolve booking type in GetStatusResponse
  // and we only have the information from itinerary level in the response
  def getFlowType(itinerary: Itinerary): BookingFlow = {
    if (
      itinerary.hotels.length == 1 && itinerary.flights.length == 1 && itinerary.cars.isEmpty && itinerary.activities.isEmpty && itinerary.protections.isEmpty
    )
      BookingFlow.Package
    else if (
      itinerary.hotels.isEmpty && itinerary.flights.length == 1 && itinerary.cars.isEmpty && itinerary.activities.isEmpty
    )
      BookingFlow.SingleFlight
    else if (
      itinerary.hotels.isEmpty && itinerary.flights.length == 2 && itinerary.cars.isEmpty && itinerary.activities.isEmpty
    )
      BookingFlow.Hackerfare
    else if (
      itinerary.flights.isEmpty && itinerary.hotels.length == 1 && itinerary.cars.isEmpty && itinerary.activities.isEmpty && itinerary.protections.isEmpty
    )
      BookingFlow.SingleProperty
    else if (
      itinerary.flights.isEmpty && itinerary.hotels.length == 2 && itinerary.cars.isEmpty && itinerary.activities.isEmpty && itinerary.protections.isEmpty
    )
      BookingFlow.MixAndSave
    else if (
      itinerary.activities.length == 1 && itinerary.hotels.isEmpty && itinerary.flights.isEmpty && itinerary.cars.isEmpty && itinerary.protections.isEmpty
    )
      BookingFlow.SingleActivity
    else BookingFlow.Unknown
  }

  private def getFlowTypeBySize(
      numberOfProperty: Int,
      numberOfFlight: Int,
      numberOfTripProtection: Int,
      numberOfCar: Int,
      numberOfActivity: Int,
      isPackagingRequest: Boolean,
      featureAware: Option[FeatureAware],
      whiteLabelInfo: WhiteLabelInfo
  ): BookingFlow = {
    val inputDefinition = MultiProductFlowDefinition(
      numberOfProperty = numberOfProperty,
      numberOfFlight = numberOfFlight,
      numberOfTripProtection = numberOfTripProtection,
      numberOfCar = numberOfCar,
      numberOfActivity = numberOfActivity,
      isPackagingRequest = isPackagingRequest
    )
    if (
      featureAware.exists(_.migrateFlightToCartFlow(whiteLabelInfo)) ||
      featureAware.exists(_.migrateHackerFareToCartFlow(whiteLabelInfo))
    )
      getMultiProductFlowDefinitionMapV2(
        featureAware.exists(_.migrateFlightToCartFlow(whiteLabelInfo)),
        featureAware.exists(_.migrateHackerFareToCartFlow(whiteLabelInfo))
      )
        .getOrElse(inputDefinition, BookingFlow.Unknown)
    else
      multiProductFlowDefinitionMap.getOrElse(inputDefinition, BookingFlow.Unknown)
  }

  def buildMultiProductFlowDefinition(
      request: SetupBookingRequest,
      isActivityCartBF: Boolean
  ): MultiProductFlowDefinition = {
    val products               = request.productsRequest
    val numberOfTripProtection = calculateTripProtectionCount(products, isActivityCartBF)

    MultiProductFlowDefinition(
      numberOfProperty = products.propertyRequests.size,
      numberOfFlight = products.flightRequests.size,
      numberOfCar = products.carRequestsOpt.getOrElse(Seq.empty).size,
      numberOfActivity = products.activityRequests.getOrElse(Seq.empty).size,
      numberOfTripProtection = numberOfTripProtection
    )
  }

  // Temporary helper function to calculate actual Trip Protection Count
  // https://agoda.slack.com/archives/CLJDXKZD1/p1701239712853299
  private[product] def calculateTripProtectionCount(products: ProductsRequest, isActivityCartBF: Boolean): Int = {
    val tripProtectionRequests = products.tripProtectionRequests.getOrElse(Seq.empty)
    if (
      isActivityCartBF &&
      tripProtectionRequests.size == 1 && tripProtectionRequests
        .map(_.optInValue)
        .headOption
        .getOrElse(ProtectionRequestItemOptInValue.None) != ProtectionRequestItemOptInValue.Purchase
    ) 0
    else tripProtectionRequests.size
  }

  def buildMultiProductFlowDefinition(request: CreateBookingRequest): MultiProductFlowDefinition = {
    val products = request.products
    MultiProductFlowDefinition(
      numberOfProperty = products.propertyItems.getOrElse(Seq.empty).size,
      numberOfFlight = products.flightItems.getOrElse(Seq.empty).size,
      numberOfCar = products.carItems.getOrElse(Seq.empty).size,
      numberOfActivity = products.activitiesItems.getOrElse(Seq.empty).size,
      numberOfTripProtection = products.protectionItems.getOrElse(Seq.empty).size
    )
  }
}
