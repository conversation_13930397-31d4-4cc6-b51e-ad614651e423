package com.agoda.bapi.common

import com.agoda.bapi.common.model.activity.ActivityBookingToken
import com.agoda.bapi.common.model.addOn.cegFastTrack.AddOnBookingToken
import com.agoda.bapi.common.model.addOn.{AddOnBookingToken => GenericAddOnBookingToken}
import com.agoda.bapi.common.model.car.CarBookingToken
import com.agoda.bapi.common.model.creation.BAPIBooking
import com.agoda.bapi.common.model.flight.FlightBookingToken
import com.agoda.bapi.common.model.tripProtection.TripProtectionToken
import com.agoda.bapi.common.token.common.ProductTokenKey

package object token {
  type ProductBookingModel[T]   = Map[ProductTokenKey, T]
  type FlightBookingModel       = ProductBookingModel[Seq[FlightBookingToken]]
  type PropertyBookingModel     = ProductBookingModel[BAPIBooking]
  type CarBookingModel          = ProductBookingModel[CarBookingToken]
  type TripProtectionModel      = ProductBookingModel[TripProtectionToken]
  type PropertySetupModel       = ProductBookingModel[PropertySetupBookingToken]
  type ActivityBookingModel     = ProductBookingModel[ActivityBookingToken]
  type AddOnBookingModel        = ProductBookingModel[AddOnBookingToken]
  type GenericAddOnBookingModel = ProductBookingModel[GenericAddOnBookingToken]
  type AncillarySetupModel      = ProductBookingModel[String]
}
