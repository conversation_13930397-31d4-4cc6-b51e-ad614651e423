package com.agoda.bapi.common.model.whitelabel

object WhiteLabelFeatureName extends Enumeration {
  type WhiteLabelFeatureName = Value

  val KeylessEarn: Value                                     = Value("KeylessEarnEnabled")
  val TPRMCheck: Value                                       = Value("TPRMCheck")
  val UseHybridPriceBreakDown: Value                         = Value("UseHybridPriceBreakDown")
  val ActivityPaymentPointValidation: Value                  = Value("ActivityPaymentPointValidation")
  val ExternalLoyaltyMemberBalance: Value                    = Value("ExternalLoyaltyMemberBalance")
  val InternationalAgencyBooking: Value                      = Value("InternationalAgencyBooking")
  val JapanGovernmentCampaign: Value                         = Value("JapanGovernmentCampaign")
  val DinerPaymentMethod: Value                              = Value("DinerPaymentMethod")
  val InstantBooking: Value                                  = Value("instantBooking")
  val MemberValidationCheckOnCreateBookingEndpoint: Value    = Value("MemberValidationCheckOnCreateBookingEndpoint")
  val PropertyPaymentMethodsConfig: Value                    = Value("propertyPaymentMethodsConfig")
  val RegulationDisableShowBookingFeePriceBreakdown: Value   = Value("RegulationDisableShowBookingFeePriceBreakdown")
  val HidePromoBoxWhenNoCouponAvailable: Value               = Value("HidePromoBoxWhenNoCouponAvailable")
  val PartnerLoyaltyBurn: Value                              = Value("PartnerLoyaltyBurn")
  val PartnerLoyaltyEarn: Value                              = Value("PartnerLoyaltyEarn")
  val PartnerLoyaltyEarnWithBurn: Value                      = Value("PartnerLoyaltyEarnWithBurn")
  val CashbackRedemption: Value                              = Value("CashbackRedemption")
  val PartnerClaimExchange: Value                            = Value("PartnerClaimExchangeForInstallments")
  val DisableAmexForRurubu: Value                            = Value("DisableAmex")
  val CampaignInfoCheck: Value                               = Value("CampaignInfoCheck")
  val MatchedRoomIdentifiersCheck: Value                     = Value("MatchedRoomIdentifiersCheck")
  val UserBalanceValidationWithTotalItemPriceInPoints: Value = Value("UserBalanceValidationWithTotalItemPriceInPoints")
  val EnablePayPalForCart: Value                             = Value("EnablePayPalForCart")
  val FlightsFacilitationFee: Value                          = Value("FlightsFacilitationFee")
  val MapELAPIErrorWithBookingSubStatus: Value               = Value("MapELAPIErrorWithBookingSubStatus")
  val IncludeLoyaltyRequestInProductRequest: Value           = Value("IncludeLoyaltyRequestInProductRequest")
  val MinimumPointsRedemptionValidation: Value               = Value("MinimumPointsRedemptionValidation")
  val DisableIssuingBankDisplay: Value                       = Value("DisableIssuingBankDisplay")
  val OverridePointsOfferType: Value                         = Value("OverridePointsOfferType")
  val DisableSavedCard: Value                                = Value("DisableSavedCard")
  val IsJtbWl: Value                                         = Value("IsJtbWl")
  val IsJapanicanWl: Value                                   = Value("IsJapanicanWl")
  val IsRurubuWl: Value                                      = Value("IsRurubuWl")
  val FullBurnBooking: Value                                 = Value("FullBurnBooking")                   // US Bank Create request validator
  val ValidateNoCashPortionForAllPoints: Value               = Value("ValidateNoCashPortionForAllPoints") // create request validator
  val ValidateMinimumPointsRedemption: Value                 = Value("ValidateMinimumPointsRedemption")   // create request validator
  val AllowNoCreditCardBooking: Value                        = Value("AllowNoCreditCardBooking")          // create request validator
  val EnableInstallmentFeature: Value                        = Value("EnableInstallmentFeature")
  val CustomPartnerPromoUtils: Value                         = Value("CustomPartnerPromoUtils")
  val SingleFlightPriceBDInCartBooking: Value                = Value("SingleFlightPriceBreakdownInCartBookingForm")
  val UseItineraryContextForHackerFare: Value                = Value("UseItineraryContextForHackerFare")
  val RegulationShowExclusivePriceWithFee: Value             = Value("RegulationShowExclusivePriceWithFee")
  val DirectPartners: Value                                  = Value("DirectPartners")
  val FlightToCartMigration: Value                           = Value("FlightToCartMigration")
  val HideBookingPIIHadoop: Value                            = Value("HideBookingPIIHadoop")
  val ExternalLoyaltyPointsErrorV2: Value                    = Value("ExternalLoyaltyPointsErrorV2")
  val EnableMemberIdForBookingCreation: Value                = Value("EnableMemberIdForBookingCreation")
  val DoNotOverrideCustomerEmailWhiteLabel: Value            = Value("DoNotOverrideCustomerEmailWhiteLabel")
}
