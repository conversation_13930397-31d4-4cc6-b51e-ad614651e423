package com.agoda.bapi.common.util

import com.agoda.bapi.common.message.creation.ReferenceToken
import com.agoda.bapi.common.model.AbsData
import com.agoda.bapi.common.model.rebookAndCancel.RebookAndCancelData
import com.agoda.bapi.common.token._
import com.agoda.bapi.common.token.property.{Token, WhitelabelAffiliateBookingToken}

import scala.util.Try

/**
  * Cautions: for any new version from now please map it back to V1 (assume V1 is business model of this app)
  */
class TokenSerializer[T: Manifest] {

  protected val version: Int = 1

  def serialize(input: T, timestamp: Option[Long], expiresAfterMinutes: Option[Long]): Try[Token] =
    JacksonSerializer
      .serialize(input)
      .map(serializeString =>
        Token(
          content = Some(serializeString),
          version = Some(version),
          timestamp = timestamp,
          expiresAfterMinutes = expiresAfterMinutes
        )
      )
}

object TokenSerializers {

  implicit val CarBookingModelSerializer: TokenSerializer[CarBookingModel] =
    new TokenSerializer[CarBookingModel]
  implicit val FlightBookingModelSerializer: TokenSerializer[FlightBookingModel] =
    new TokenSerializer[FlightBookingModel] {
      override protected val version: Int = BookingModelTokenVersion.flightBookingModel
    }
  implicit val PropertyBookingModelSerializer: TokenSerializer[PropertyBookingModel] =
    new TokenSerializer[PropertyBookingModel]
  implicit val PropertySetupModelSerializer: TokenSerializer[PropertySetupModel] =
    new TokenSerializer[PropertySetupModel]
  implicit val TripProtectionModelSerializer: TokenSerializer[TripProtectionModel] =
    new TokenSerializer[TripProtectionModel]
  implicit val MultiProductCreationBookingTokenSerializer: TokenSerializer[MultiProductCreationBookingToken] =
    new TokenSerializer[MultiProductCreationBookingToken]
  implicit val MultiProductSetupBookingTokenSerializer: TokenSerializer[MultiProductSetupBookingToken] =
    new TokenSerializer[MultiProductSetupBookingToken]
  implicit val MultiProductRetryPaymentBookingTokenSerializer: TokenSerializer[MultiProductRetryPaymentBookingToken] =
    new TokenSerializer[MultiProductRetryPaymentBookingToken]
  implicit val MultiProductBookingTokenSerializer: TokenSerializer[MultiProductBookingToken] =
    new TokenSerializer[MultiProductBookingToken]
  implicit val MultiProductBookingDetailTokenSerializer: TokenSerializer[ItineraryAssociatedBookingsToken] =
    new TokenSerializer[ItineraryAssociatedBookingsToken]
  implicit val ReferenceTokenSerializer: TokenSerializer[ReferenceToken] =
    new TokenSerializer[ReferenceToken]
  implicit val AbsDataTokenSerializer: TokenSerializer[AbsData] =
    new TokenSerializer[AbsData]
  implicit val WhitelabelAffiliateBookingTokenSerializer: TokenSerializer[WhitelabelAffiliateBookingToken] =
    new TokenSerializer[WhitelabelAffiliateBookingToken]
  implicit val ActivityBookingModelSerializer: TokenSerializer[ActivityBookingModel] =
    new TokenSerializer[ActivityBookingModel]
  implicit val AddOnBookingModelSerializer: TokenSerializer[AddOnBookingModel] =
    new TokenSerializer[AddOnBookingModel]
  implicit val GenericAddOnBookingModelSerializer: TokenSerializer[GenericAddOnBookingModel] =
    new TokenSerializer[GenericAddOnBookingModel]
  implicit val AncillarySetupModelSerializer: TokenSerializer[AncillarySetupModel] =
    new TokenSerializer[AncillarySetupModel]
  implicit val RebookAndCancelDataSerializer: TokenSerializer[RebookAndCancelData] =
    new TokenSerializer[RebookAndCancelData]

  def apply[T: TokenSerializer]: TokenSerializer[T] = implicitly[TokenSerializer[T]]
  def toJsonString(token: Token): Try[String]       = JacksonSerializer.serialize(token)
}

object BookingModelTokenVersion {
  val flightBookingModel: Int = 2
}
