package com.agoda.bapi.common.message.multi.product

import cats.implicits._
import com.agoda.bapi.common.message.{ActivityBookingState, ActivityBookingStateWithItinerary, AddOnBookingState, AddOnBookingStateWithItinerary, CegFastTrackBookingState, CegFastTrackBookingStateWithItinerary, CrossProductIsolatedFeature, PendingBookingChange, PropertyBookingStateWithItinerary, ResponseBase}
import com.agoda.bapi.common.message.ResponseErrorCode.ResponseErrorCode
import com.agoda.bapi.common.message.multi.product.SetStateResponse.WarnCode.WarnCode
import com.agoda.bapi.common.model.car.VehicleBookingState
import com.agoda.bapi.common.model.car.VehicleBookingStateModel.VehicleModelInternal
import com.agoda.bapi.common.model.flight.flightModel.FlightModelInternal
import com.agoda.bapi.common.model.flight.history.ActionType.ActionType
import com.agoda.bapi.common.model.multiproduct.SetStateItineraryModel
import com.agoda.bapi.common.model.{ActionId, InvalidRequestDetail}
import com.agoda.commons.agprotobuf.scalapb.utils.ProtoConverter
import io.swagger.annotations.ApiModel

import scala.util.Try

final case class SetStateResponse(
    @ApiModel(description = "Indicates whether this request completed successfully.")
    success: Boolean,
    @ApiModel(description = "Identifies the error in case success is false to allow clients to handle specific cases.")
    errorCode: Option[String] = None,
    @ApiModel(description = "Non-localized description of the error to help troubleshooting.")
    errorMessage: Option[String] = None,
    @ApiModel(description = "Details about the invalid request data.")
    invalidRequestData: Option[Seq[InvalidRequestDetail]] = None,
    bookingState: Option[FlightModelInternal] = None,
    vehicleBookings: Option[Seq[VehicleModelInternal]] = None,
    activityBookings: Option[Seq[ActivityBookingState]] = None,
    propertyBookings: Option[Seq[PropertyBookingState]] = None,
    cegFastTrackBookings: Option[Seq[CegFastTrackBookingState]] = None,
    addOnBookings: Option[Seq[AddOnBookingState]] = None,
    crossProductIsolatedFeature: Option[CrossProductIsolatedFeature] = None
) extends ResponseBase {
  def toItineraryModel: Option[SetStateItineraryModel] =
    bookingState.map { state =>
      SetStateItineraryModel(
        payments = state.payments,
        history = state.history,
        bookingPayments = state.bookingPayments
      )
    }
}

object SetStateResponse {

  import scala.language.implicitConversions

  implicit def seqToOptionSeq[T <: scala.Product with Serializable](seq: Seq[T]): Option[Seq[T]] =
    if (seq.isEmpty) None else Some(seq)

  private def addBookingStates[T](stateSeq1: Seq[T], stateSeq2: Seq[T]): Seq[T] = stateSeq1 ++ stateSeq2

  object WarnCode extends Enumeration {
    type WarnCode = Value

    val MISSING_PREVIOUS_VERSION: SetStateResponse.WarnCode.Value = Value("MISSING_PREVIOUS_VERSION")
    val BOOKING_NOT_FOUND: SetStateResponse.WarnCode.Value        = Value("BOOKING_NOT_FOUND")
    val PARTIAL_UPDATE: SetStateResponse.WarnCode.Value           = Value("PARTIAL_UPDATE")
  }

  def withError(errorCode: ResponseErrorCode, errorMessage: String): SetStateResponse = {
    SetStateResponse(success = false, Some(errorCode.toString), Some(errorMessage))
  }

  def withWarning(warningCode: WarnCode, warningMessage: String): SetStateResponse = {
    SetStateResponse(success = true, errorCode = Some(warningCode.toString), errorMessage = Some(warningMessage))
  }

  def withSuccess(bookingState: FlightModelInternal): SetStateResponse = {
    SetStateResponse(success = true, bookingState = Some(bookingState))
  }

  /**
    * Create SetStateResponse from vehicleBookingState
    *
    * @param actionType
    * @param actionId
    * @param bookingType
    * @param vehicleBookingState
    * @return
    */
  def fromVehicleBookingState(
      actionType: ActionType,
      actionId: ActionId,
      bookingType: Option[Int],
      vehicleBookingState: VehicleBookingState
  ): SetStateResponse = {
    SetStateResponse(
      success = true,
      bookingState = Some(
        FlightModelInternal(
          actionType = actionType,
          actionId = actionId,
          bookingType = bookingType,
          bookingId = 0,
          schemaVersion = FlightModelInternal.SCHEMA_VERSION,
          itinerary = vehicleBookingState.itinerary,
          payments = vehicleBookingState.payments,
          history = vehicleBookingState.itineraryHistories
        )
      ),
      vehicleBookings = Some(vehicleBookingState.vehicleModelsInternal)
    )
  }

  def fromActivityBookingState(
      actionType: ActionType,
      actionId: ActionId,
      bookingType: Option[Int],
      activityBookingState: ActivityBookingStateWithItinerary
  ): SetStateResponse = {
    SetStateResponse(
      success = true,
      bookingState = Some(
        FlightModelInternal(
          actionType = actionType,
          actionId = actionId,
          bookingType = bookingType,
          bookingId = 0,
          schemaVersion = FlightModelInternal.SCHEMA_VERSION,
          itinerary = activityBookingState.itinerary,
          payments = activityBookingState.payments,
          bookingPayments = activityBookingState.bookingPayments,
          history = activityBookingState.itineraryHistories
        )
      ),
      activityBookings = Some(activityBookingState.activities.map { activity =>
        ActivityBookingState(ProtoConverter.protoToString(activity), activity.product.booking.bookingId)
      })
    )
  }

  def fromPropertyBookingState(
      actionType: ActionType,
      actionId: ActionId,
      bookingType: Option[Int],
      propertyBookingStateWithItinerary: PropertyBookingStateWithItinerary
  ): SetStateResponse = {
    SetStateResponse(
      success = true,
      bookingState = Some(
        FlightModelInternal(
          actionType = actionType,
          actionId = actionId,
          bookingType = bookingType,
          bookingId = 0,
          schemaVersion = FlightModelInternal.SCHEMA_VERSION,
          itinerary = propertyBookingStateWithItinerary.itinerary,
          payments = Seq.empty,
          bookingPayments = Seq.empty,
          history = Seq.empty
        )
      ),
      propertyBookings = Some(propertyBookingStateWithItinerary.properties.map { property =>
        PropertyBookingState(
          bookingId = property.bookingId,
          stateId = property.stateId,
          propertyState = Try(property.propertyProductModel.map(ProtoConverter.protoToString(_))).toOption.flatten
        )
      })
    )
  }

  def fromPendingBookingChange(
      pendingBookingChange: PendingBookingChange
  ): SetStateResponse = {
    SetStateResponse(
      success = true,
      crossProductIsolatedFeature = Some(CrossProductIsolatedFeature(pendingBookingChange = Some(pendingBookingChange)))
    )
  }

  def fromCegFastTrackBookingState(
      actionType: ActionType,
      actionId: ActionId,
      bookingType: Option[Int],
      bookingState: CegFastTrackBookingStateWithItinerary
  ): SetStateResponse = {
    SetStateResponse(
      success = true,
      bookingState = Some(
        FlightModelInternal(
          actionType = actionType,
          actionId = actionId,
          bookingType = bookingType,
          bookingId = 0,
          schemaVersion = FlightModelInternal.SCHEMA_VERSION,
          itinerary = bookingState.itinerary,
          payments = bookingState.payments,
          bookingPayments = bookingState.bookingPayments,
          history = bookingState.itineraryHistories,
          bookingRelationships = Some(bookingState.relationships)
        )
      ),
      cegFastTrackBookings = Some(bookingState.cegFastTracks.map { cegFastTrack =>
        CegFastTrackBookingState(ProtoConverter.protoToString(cegFastTrack), cegFastTrack.product.booking.bookingId)
      })
    )
  }

  def fromAddonBookingState(
      actionType: ActionType,
      actionId: ActionId,
      bookingType: Option[Int],
      bookingState: AddOnBookingStateWithItinerary
  ): SetStateResponse = {
    SetStateResponse(
      success = true,
      bookingState = Some(
        FlightModelInternal(
          actionType = actionType,
          actionId = actionId,
          bookingType = bookingType,
          bookingId = 0,
          schemaVersion = FlightModelInternal.SCHEMA_VERSION,
          itinerary = bookingState.itinerary,
          payments = bookingState.payments,
          bookingPayments = bookingState.bookingPayments,
          history = bookingState.itineraryHistories,
          bookingRelationships = Some(bookingState.relationships)
        )
      ),
      addOnBookings = Some(bookingState.addOns.map { addOn =>
        AddOnBookingState(
          ProtoConverter.protoToString(addOn),
          addOn.product.booking.bookingId,
          addOn.product.booking.productTypeId
        )
      })
    )
  }

  def combineProductResponses(stateResponses: Seq[SetStateResponse], to: SetStateResponse): SetStateResponse = {

    to.copy(
      vehicleBookings = stateResponses
        .map(_.vehicleBookings.getOrElse(Nil))
        .foldLeft(Seq.empty[VehicleModelInternal])(addBookingStates),
      activityBookings = stateResponses
        .map(_.activityBookings.getOrElse(Nil))
        .foldLeft(Seq.empty[ActivityBookingState])(addBookingStates),
      propertyBookings = stateResponses
        .map(_.propertyBookings.getOrElse(Nil))
        .foldLeft(Seq.empty[PropertyBookingState])(addBookingStates),
      cegFastTrackBookings = stateResponses
        .map(_.cegFastTrackBookings.getOrElse(Nil))
        .foldLeft(Seq.empty[CegFastTrackBookingState])(addBookingStates),
      addOnBookings = stateResponses
        .map(_.addOnBookings.getOrElse(Nil))
        .foldLeft(Seq.empty[AddOnBookingState])(addBookingStates)
    )

  }

}
