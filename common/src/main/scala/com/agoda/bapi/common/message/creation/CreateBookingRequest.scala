package com.agoda.bapi.common.message.creation

import com.agoda.bapi.common.exception.ProductNotFoundException
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.mapper.booking.PlatformIdMapper
import com.agoda.bapi.common.message.Request
import com.agoda.bapi.common.message.creation.ChildType.ChildType
import com.agoda.bapi.common.message.creation.LanguageScriptType.LanguageScriptType
import com.agoda.bapi.common.message.creation.common.Payment3DSRequest
import com.agoda.bapi.common.message.pricebreakdown.PriceBreakdownNode
import com.agoda.bapi.common.message.setupBooking.LoyaltyRequest
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.model.BookingTag.BookingTag
import com.agoda.bapi.common.model.ChargeOption.ChargeOption
import com.agoda.bapi.common.model.booking.RequiredFieldMetadata
import com.agoda.bapi.common.model.creation._
import com.agoda.bapi.common.model.flight.Flight
import com.agoda.bapi.common.model.payment.{GatewayReferenceRedirect, PaymentContinuation}
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.car.EnigmaCarDriverInfo
import com.agoda.bapi.common.token.MultiProductCreationBookingToken
import com.agoda.bapi.common.token.common.ProductTokenKey
import com.agoda.bapi.common.util.{JodaDateTimeUtils, ServerUtils}
import com.agoda.bapi.common.validation.Required.requiredSingle
import com.agoda.bapi.common.{LocalDateDeserializer, LocalDateSerializer, OptionLocalDateDeserializer, OptionLocalDateSerializer}
import com.agoda.mpbe.state.common.enums.Gateway.Gateway
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.agoda.zaidel.warts.misc.SwaggerAnnotationsControl
import com.fasterxml.jackson.annotation.{JsonIgnore, JsonProperty}
import com.fasterxml.jackson.databind.annotation.{JsonDeserialize, JsonSerialize}
import com.fasterxml.jackson.module.scala.JsonScalaEnumeration
import enumerations.SpecialRequestIds._
import enumerations.{SpecialRequestId, SpecialRequestIds}
import io.swagger.annotations.{ApiModel, ApiModelProperty}
import org.joda.time.{DateTime, LocalDate}

import java.util.UUID
import scala.annotation.meta.field
import scala.util.Try

final class ProductItemNotFound(productTokenKey: Option[ProductTokenKey])
    extends Exception(s"Can't found productItem from key: ${productTokenKey}")

final class PrimaryGuestNotFound(productTokenKey: Option[ProductTokenKey])
    extends Exception(s"Can't find primary guest from key: ${productTokenKey}")

final class PrimaryPaxNotFound(productTokenKey: Option[ProductTokenKey])
    extends Exception(s"Can't find primary pax from key: ${productTokenKey}")

final class PaxNotFound() extends Exception("Can't find pax info")

@ApiModel(parent = classOf[Request])
final case class CreateBookingRequest(
    prebookingId: PrebookingId,
    /**
      * General fields
      */
    // required
    clientIp: String = "",
    @deprecated("Deprecated as this field is not available in V2, use ServerUtils instead")
    serverName: String = "",
    @(ApiModelProperty @field)(dataType = "Int")
    siteId: Option[Int] = None,
    // required
    storefrontId: StorefrontId,
    @(ApiModelProperty @field)(dataType = "Int")
    agodaCancellationFeeId: Option[Int],
    sessionId: String = "",
    bookingSessionId: Option[String] = None,
    analyticsSessionId: Option[String] = None,
    userId: UUID, // is it ClientID
    trackingCookieId: Option[String] = None,
    // required
    @JsonDeserialize(using = classOf[CreationDateTimeDeserializer])
    @JsonSerialize(using = classOf[CreationDateTimeSerializer])
    trackingCookieDate: DateTime = DateTime.now(),
    trackingTag: Option[String] = None,
    referralUrl: String = "",
    // required
    allowDuplicateBooking: Boolean,
    tmSessionId: Option[String] = None,
    /**
      * Agent assist
      */
    // required
    agentAssist: Option[AgentAssist],
    /**
      * Datacenter
      */
    @deprecated("Deprecated as this field is not available in V2, use ServerUtils instead")
    dataCenter: String = "",
    /**
      * Platform
      */
    @(ApiModelProperty @field)(dataType = "Int")
    platformId: Option[Int],
    /**
      * Attribution obsolete, skipped
      */

    /**
      * AttributionV2
      */
    attributionsV2: Seq[AttributionV2],
    /**
      * Booking Agent
      */
    userAgent: UserAgent,
    /**
      * Payment
      */
    payment: BookingPayment,
    /**
      * Customer
      */
    // required
    customer: Customer,
    /**
      * Discount
      */
    // required
    @(ApiModelProperty @field)(required = true)
    discount: Option[Discount],
    /**
      * Guest
      */
    @(ApiModelProperty @field)(hidden = true)
    guests: Option[Seq[HotelGuest]],
    /**
      * Hotels and rooms
      */
    products: Products,
    @deprecated("should be passed via BAPIBooking - Room - GiftCardEarnings")
    giftCardEarning: Option[GiftCardEarning] = None,
    partnerLoyaltyPoint: Option[PartnerLoyaltyPoint] = None,
    /**
      * Fraud
      */
    fraudInfo: FraudInfo,
    /**
      * All of next is not used by Web Booking form
      */
    @ApiModel(description = "B2C/B2B/NET")
    @JsonDeserialize(contentUsing = classOf[AffiliateModelDeserializer])
    @JsonScalaEnumeration(classOf[AffiliateModelType])
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    /* @(ApiModelProperty @field)( dataType = "com.agoda.bapi.common.message.AffiliateModel$", required = false,
     * extensions = Array( new Extension( name = "ms-enum", properties = Array( new ExtensionProperty(name = "name",
     * value = "AffiliateModel"), new ExtensionProperty(name = "modelAsString", value = "false") ) ) ) ) */
    affiliateModel: Option[AffiliateModel.AffiliateModel],
    @ApiModel(description = "None/Invoice/CorporateCard/CustomerCard")
    @JsonDeserialize(contentUsing = classOf[AffiliatePaymentMethodDeserializer])
    @JsonScalaEnumeration(classOf[AffiliatePaymentMethodType])
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    /* @(ApiModelProperty @field)( dataType = "com.agoda.bapi.common.message.AffiliatePaymentMethod$", required = false,
     * extensions = Array( new Extension( name = "ms-enum", properties = Array( new ExtensionProperty(name = "name",
     * value = "AffiliatePaymentMethod"), new ExtensionProperty(name = "modelAsString", value = "false") ) ) ) ) */
    affiliatePaymentMethod: Option[AffiliatePaymentMethod.AffiliatePaymentMethod],
    addDelay: Boolean,
    isTravelAgency: Boolean,
    // RequestBase
    correlationId: Option[String],
    userContext: Option[UserContext],
    // will save to mdb.ebe_booking as "ss_id" field
    @(ApiModelProperty @field)(dataType = "Long")
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    searchId: Option[Long],
    @ApiModel(
      description =
        """when xml partner have the CFD code defined, affiliate will pass it to BAPI BAPI must passit to EBE. EBE will
         save it at booking creation time, and use it for mapping credentials linked to the cfd code and return when
         get booking details"""
    )
    cfdCode: Option[String] = None,
    /**
      * Affiliate Instant Book
      */
    @ApiModel(description = "This field contains all flag field of instantBooking Feature")
    instantBook: Option[InstantBook] = None,
    @ApiModel(description = "This is a supplierData that ABS will send to supplier API to search or cut allotment")
    supplierData: Option[SupplierData] = None,
    @ApiModel(description = "Government campaign information for government subsidised bookings")
    governmentCampaignInfo: Option[GovernmentCampaignInfo] = None,
    @ApiModel(description = "Proxy's acceptHeader")
    acceptHeader: Option[String] = None,
    externalLoyaltyRequest: Option[LoyaltyRequest] = None,
    @ApiModel(description = "BookingHolding partner names")
    bookingHoldingPartnerNames: Option[Seq[BookingHoldingPartner]] = None,
    @ApiModel(description = "Used by Finance to ignore bookings & MPBE for bypass settlement, upc, preauth & allotment")
    @(ApiModelProperty @field)(hidden = true)
    isTestBooking: Boolean = false,
    @ApiModel(description = "Additional fields to send to ABS during provisioning")
    affiliateAdditionalSupplierInfo: Map[String, String] = Map.empty
) extends Request
    with SwaggerAnnotationsControl {

  @(ApiModelProperty @field)(hidden = true)
  def isGovernmentCampaign: Boolean =
    governmentCampaignInfo.isDefined

  @(ApiModelProperty @field)(hidden = true)
  def getBookingTags: Seq[BookingTag] =
    if (governmentCampaignInfo.exists(_.campaignId == 3))
      Seq(BookingTag.GovernmentCampaignPhase3ver1)
    else
      Seq()

  private def getPropertyItem(key: Option[ProductTokenKey]): PropertyItem = {
    products.propertyItems.toSeq.flatten
      .find(_.productTokenKey == key)
      .getOrElse(throw new ProductItemNotFound(key))
  }

  private def getFlightItem(key: Option[ProductTokenKey]): FlightItem = {
    products.flightItems.toSeq.flatten
      .find(_.productTokenKey == key)
      .getOrElse(throw new ProductItemNotFound(key))
  }

  private def getVehicleItem(key: Option[ProductTokenKey]): CarItem = {
    products.carItems.toSeq.flatten
      .find(_.productTokenKey == key)
      .getOrElse(throw new ProductItemNotFound(key))
  }

  private def getActivitiesItem(key: Option[ProductTokenKey]): ActivitiesItem = {
    products.activitiesItems.toSeq.flatten
      .find(_.productTokenKey == key)
      .getOrElse(throw new ProductItemNotFound(key))
  }

  @(ApiModelProperty @field)(hidden = true)
  def is3DSFinal: Boolean = {
    val payment3DS = payment.creditCard.flatMap(_.payment3DS)
    payment3DS.flatMap(_.postBackFields).nonEmpty &&
    payment3DS.flatMap(_.referenceToken).nonEmpty
  }

  @(ApiModelProperty @field)(hidden = true)
  def getGuestList(key: Option[ProductTokenKey]): Seq[HotelGuest] = {
    getPropertyItem(key).guests.toSeq.flatten
  }

  @(ApiModelProperty @field)(hidden = true)
  def getFlightPaxList(key: Option[ProductTokenKey]): Seq[FlightPax] = {
    getFlightItem(key).pax.toSeq.flatten
  }

  @SuppressWarnings(Array("org.wartremover.warts.IterableOps"))
  @(ApiModelProperty @field)(hidden = true)
  def getProtectionPaxList(): Seq[FlightPax] = {
    products.flightItems.toSeq.flatten.head.pax.getOrElse(throw new PaxNotFound())
  }

  @(ApiModelProperty @field)(hidden = true)
  def getVehicleDriverList(key: Option[ProductTokenKey]): Seq[CarDriver] = {
    getVehicleItem(key).driver.toSeq
  }

  @(ApiModelProperty @field)(hidden = true)
  def getActivityPaxList(key: Option[ProductTokenKey]): Seq[ActivityPax] = {
    getActivitiesItem(key).pax.toSeq.flatten
  }

  @(ApiModelProperty @field)(hidden = true)
  def getActivityLanguageGuide(key: Option[ProductTokenKey]): Option[ActivityLanguageGuideAnswer] = {
    getActivitiesItem(key).languageGuide
  }

  @(ApiModelProperty @field)(hidden = true)
  def getActivityBookingAnswers(key: Option[ProductTokenKey]): Seq[ActivityBookingAnswer] = {
    getActivitiesItem(key).bookingAnswers.toSeq.flatten
  }

  @(ApiModelProperty @field)(hidden = true)
  def getPrimaryGuest(key: Option[ProductTokenKey]): HotelGuest =
    getGuestList(key)
      .find(_.primary)
      .getOrElse(throw new PrimaryGuestNotFound(key))

  @(ApiModelProperty @field)(hidden = true)
  def getPrimaryFlightPax(key: Option[ProductTokenKey]): FlightPax =
    getFlightPaxList(key)
      .find(_.primary)
      .getOrElse(throw new PrimaryPaxNotFound(key))

  @(ApiModelProperty @field)(hidden = true)
  def getOccupancy(key: Option[ProductTokenKey]): Option[Seq[Occupancy]] = {
    getPropertyItem(key).occupancy
  }

  private val propertyGuestList: Option[Seq[HotelGuest]] =
    products.propertyItems.flatMap(_.headOption).flatMap(_.guests)
  private val flightPaxList: Option[Seq[FlightPax]]     = products.flightItems.flatMap(_.headOption).flatMap(_.pax)
  private val vehicleDriverList: Option[CarDriver]      = products.carItems.flatMap(_.headOption).flatMap(_.driver)
  private val activityPaxList: Option[Seq[ActivityPax]] = products.activitiesItems.flatMap(_.headOption.flatMap(_.pax))

  @(ApiModelProperty @field)(hidden = true)
  val guestList: Seq[HotelGuest] = guests.orElse(propertyGuestList).getOrElse(Seq.empty)

  @(ApiModelProperty @field)(hidden = true)
  lazy val paxList: Seq[FlightPax] = flightPaxList.getOrElse(Seq.empty)

  @(ApiModelProperty @field)(hidden = true)
  lazy val driverOption: Option[CarDriver] = vehicleDriverList

  @(ApiModelProperty @field)(hidden = true)
  lazy val activityPaxes: Option[Seq[ActivityPax]] = activityPaxList

  @SuppressWarnings(Array("org.wartremover.warts.OptionPartial"))
  @(ApiModelProperty @field)(hidden = true)
  lazy val primaryGuest: HotelGuest = guestList.collectFirst {
    case g if g.primary => g
  }.get

  @SuppressWarnings(Array("org.wartremover.warts.OptionPartial"))
  @(ApiModelProperty @field)(hidden = true)
  lazy val primaryFlightPax: FlightPax = paxList.collectFirst {
    case p if p.primary => p
  }.get

  @(ApiModelProperty @field)(hidden = true)
  def languageId: LanguageId = userContext.map(_.languageId).getOrElse(1)

  @(ApiModelProperty @field)(hidden = true)
  def isAssistOrInvoiceBooking: Boolean =
    this.affiliatePaymentMethod
      .contains(AffiliatePaymentMethod.Invoice) || this.agentAssist.filter(_.agentName.nonEmpty).isDefined

  @(ApiModelProperty @field)(hidden = true)
  def getFullyAuthDate(bookingDate: DateTime): DateTime =
    this.payment.creditCard
      .filter(_ => !this.isAssistOrInvoiceBooking)
      .map(_.fullyAuthDate)
      .map(JodaDateTimeUtils.toDateTimeWithoutMillis)
      .getOrElse(bookingDate)

  @(ApiModelProperty @field)(hidden = true)
  def getFullyChargeDate(bookingDate: DateTime): DateTime =
    this.payment.creditCard
      .filter(_ => !this.isAssistOrInvoiceBooking)
      .map(_.fullyChargeDate)
      .map(JodaDateTimeUtils.toDateTimeWithoutMillis)
      .getOrElse(bookingDate)

  @(ApiModelProperty @field)(hidden = true)
  def getChargeOption: ChargeOption.Value =
    if (this.isAssistOrInvoiceBooking) ChargeOption.PayNow
    else
      this.payment.creditCard
        .map(_.chargeOption match {
          case ChargeOption.None => ChargeOption.PayNow
          case chargeOption      => chargeOption
        })
        .getOrElse(ChargeOption.PayNow)

  @(ApiModelProperty @field)(hidden = true)
  def discountV1: Discount = discount.getOrElse(Discount(discountType = DiscountType.None, membershipContentText = ""))

  @SuppressWarnings(Array("org.wartremover.warts.Null"))
  @(ApiModelProperty @field)(hidden = true)
  def isMPBE(): Boolean = this.products.bookingToken.isDefined && this.products.bookingToken != Some(null)
}

// format: off
// error due to deeply nested codes https://scalameta.org/scalafmt/docs/known-issues.html#deeply-nested-code
object CreateBookingRequest {
  def apply(createBookingRequestV2: CreateBookingRequestV2,
            clientId: Int,
            clientIp: String,
            decryptedCreationTokenOpt: Option[MultiProductCreationBookingToken])(implicit context: RequestContext)
  :CreateBookingRequest = {
    val products = Products(createBookingRequestV2.products, createBookingRequestV2.bookingToken)
    new CreateBookingRequest(
      //TODO context data to map once context is finalized jira: https://jira.agoda.local/browse/PACMAN-195
      prebookingId = 0, // Deprecated for multi-product
      guests = None, // Deprecated for multi-product
      products = products,
      giftCardEarning = None, // Note: Enriched after processing booking token
      partnerLoyaltyPoint = None, // Deprecated for multi-product
      agodaCancellationFeeId = None, // Deprecated for multi-product
      trackingCookieDate = DateTime.now(), // (should be) Deprecated, just put default value
      trackingTag = createBookingRequestV2.trackingInfo.flatMap(_.tag),
      tmSessionId = None, // (should be) Deprecated, just put default value
      storefrontId = createBookingRequestV2.getStoreFrontId(clientId).getOrElse(0),
      platformId = createBookingRequestV2.deviceContext.map(d => PlatformIdMapper.toPlatformId(
        devicePlatform = d.deviceTypeId,
        bookingFlow = decryptedCreationTokenOpt.map(_.bookingFlowType).getOrElse(BookingFlow.Unknown),
        isAffiliate = createBookingRequestV2.affiliate.isDefined)
      ),
      correlationId = createBookingRequestV2.correlationId,
      userContext = createBookingRequestV2.userContext,
      userId = createBookingRequestV2.userContext
        .flatMap(_.experimentData.map(exp => UUID.fromString(exp.userId)))
        .getOrElse(java.util.UUID.randomUUID),
      trackingCookieId = createBookingRequestV2.userContext.flatMap(_.experimentData.map(_.userId)), // Map from global context user_id
      sessionId = createBookingRequestV2.bookingContext.map(_.sessionId).getOrElse(""), // Map from Booking Context
      analyticsSessionId = createBookingRequestV2.bookingContext.flatMap(_.analyticsSessionId), // Map from Booking Context
      clientIp = clientIp, // Map from Booking Context (if needed)
      serverName = ServerUtils.serverHostName(),
      dataCenter = ServerUtils.serverDc(),
      userAgent = createBookingRequestV2.bookingContext.map(_.userAgent).getOrElse(UserAgent()), // Map from Booking Context (if needed)
      searchId = None, // Deprecated
      fraudInfo = FraudInfo(
        defensiveCheckboxClicked = false, // Deprecated
        deviceFingerprintData = createBookingRequestV2.fraudInfo.flatMap(_.deviceFingerprintData),
        tmxSessionId = createBookingRequestV2.fraudInfo.flatMap(_.tmxSessionId),
        forterToken = createBookingRequestV2.fraudInfo.flatMap(_.forterToken)
      ),
      affiliateModel = createBookingRequestV2.affiliate.map(_.model) orElse Some(AffiliateModel.B2C),
      affiliatePaymentMethod = createBookingRequestV2.affiliate.map(_.paymentMethod) orElse Some(AffiliatePaymentMethod.None), // default value needed to pass validation for agency properties
      addDelay = createBookingRequestV2.affiliate.map(_.addDelay).getOrElse(false),
      isTravelAgency = createBookingRequestV2.affiliate.map(_.isTravelAgency).getOrElse(false),
      agentAssist = createBookingRequestV2.agentAssist,
      payment = BookingPayment(createBookingRequestV2.payment),
      customer = Customer(createBookingRequestV2.customer, createBookingRequestV2.userContext, decryptedCreationTokenOpt),
      allowDuplicateBooking = createBookingRequestV2.allowDuplicateBooking.getOrElse(false),
      attributionsV2 = createBookingRequestV2.attributions.map(_.data).getOrElse(Seq.empty),
      siteId = createBookingRequestV2.userContext.flatMap(_.experimentData.flatMap(_.cId.map(_.toInt))), // TODO-Multiproduct - need to finalize on attribution feature.
      referralUrl = createBookingRequestV2.referralUrl.getOrElse(""),
      instantBook = createBookingRequestV2.instantBooking,
      supplierData = createBookingRequestV2.supplierData,
      discount = None,
      externalLoyaltyRequest = createBookingRequestV2.externalLoyaltyRequest,
      bookingHoldingPartnerNames = createBookingRequestV2.bookingHoldingPartnerNames,
      isTestBooking = createBookingRequestV2.isTestBooking,
      affiliateAdditionalSupplierInfo = createBookingRequestV2.affiliate.map(_.additionalSupplierInfo).getOrElse(Map.empty)
    )
  }
}

// format: on
final case class AgentAssist(
    agentName: String = "",
    // required
    agentBookingChannel: Int = 0,
    // required
    agentDeductAmount: BigDecimal = 0,
    agentDeductReference: String = "",
    agentClientIp: String = ""
) extends SwaggerAnnotationsControl

final case class AttributionV2(
    // required
    modelId: Int,
    tag: String,
    // required
    siteId: Int,
    @JsonDeserialize(using = classOf[CreationDateTimeDeserializerOption])
    clickDateTime: Option[DateTime],
    additionalData: String
) extends SwaggerAnnotationsControl

final case class InstantBook(
    isActive: Boolean = false,
    waitTimeMs: Long = 0,
    @deprecated("No longer use from v1.17.0")
    isConfirmBookingOnly: Boolean = false,
    @ApiModel(description = "For MPBE Only")
    @(ApiModelProperty @field)(dataType = "Boolean")
    rejectBookingIfOverWaitTime: Option[Boolean] = None
) extends SwaggerAnnotationsControl

final case class BookingPayment(
    amount: PaymentAmount,
    @ApiModel(description = "Indicates the credit card type.")
    @JsonSerialize(using = classOf[PaymentMethodSerializer])
    @JsonDeserialize(using = classOf[PaymentMethodDeserializer])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    method: PaymentMethod,
    creditCard: Option[CreditCard],
    redirect: Option[GatewayReferenceRedirect], // TODO: Not use it in current booking flow
    continuation: Option[PaymentContinuation],  // redirect payment
    ccToken: Option[String] = None,             // Token to reference to the credit card information from Agoda Pay via CC API
    @ApiModel(description = "User input fields required for payment method")
    requiredFields: Option[Map[String, String]] = None,
    @ApiModelProperty(hidden = true)
    requiredFieldMetadata: Option[Map[String, RequiredFieldMetadata]] =
      None, // The value will be set from creationToken
    postBackFields: Option[Map[String, String]] = None,
    @(ApiModelProperty @field)(hidden = true)
    nonCard: Option[NonCard] = None,
    @(ApiModelProperty @field)(dataType = "Boolean")
    isApplyPaymentLimitationCheck: Option[Boolean] = Some(false),
    @(ApiModelProperty @field)(dataType = "Int")
    paymentChannel: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "Boolean")
    disableCurrencyValidation: Option[Boolean] = Some(false)
) extends SwaggerAnnotationsControl {
  @(ApiModelProperty @field)(hidden = true)
  @JsonIgnore
  def getPaymentTokenValue: Option[String] =
    for {
      paymentContinuation <- continuation
      paymentToken        <- paymentContinuation.paymentToken
    } yield paymentToken.tokenValue
}

object BookingPayment {
  def apply(bookingPaymentV2: BookingPaymentV2): BookingPayment =
    new BookingPayment(
      creditCard = bookingPaymentV2.creditCard.map(CreditCard(_, bookingPaymentV2)),
      nonCard =
        bookingPaymentV2.nonCard.map(nonCardV2 => NonCard(ccId = nonCardV2.ccofId, isSaveCCOF = nonCardV2.shouldSave)),
      amount = PaymentAmount(siteExchangeRate = 0, upliftExchangeRate = 0, destinationExchangeRate = 0),
      method = bookingPaymentV2.method,
      redirect = bookingPaymentV2.paymentRedirect.map(redirect =>
        GatewayReferenceRedirect(
          cancelUrl = redirect.cancelUrl,
          returnUrl = redirect.successUrl,
          productName = "",
          requiredPostFields = redirect.requiredPostFields
        )
      ),
      continuation = Some(
        PaymentContinuation(
          gatewayId = bookingPaymentV2.paymentGatewayId.map(Gateway.fromValue).getOrElse(Gateway.None),
          gatewayInfoId = bookingPaymentV2.paymentGatewayInfoId,
          mpiId = bookingPaymentV2.mpiId,
          paymentToken = bookingPaymentV2.paymentToken
        )
      ),
      ccToken = bookingPaymentV2.ccToken,
      requiredFields = bookingPaymentV2.requiredFields,
      paymentChannel = bookingPaymentV2.paymentChannel,
      disableCurrencyValidation = bookingPaymentV2.disableCurrencyValidation
    )
}

final case class NonCard(
    ccId: Option[Long] = None,
    isSaveCCOF: Option[Boolean] = None
)

final case class CreditCard(
    // required
    creditcardId: Long = 0,
    // required
    @ApiModel(description = "Indicates the credit card type.")
    @JsonSerialize(using = classOf[PaymentMethodSerializer])
    @JsonDeserialize(using = classOf[PaymentMethodDeserializer])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    @Deprecated
    creditcardType: PaymentMethod,
    cardholderName: String = "",
    @JsonDeserialize(using = classOf[NullValueToEmptyStringJsonDeserializer])
    creditcardNumber: String = "",
    expiryDate: Option[String],
    securityCode: Option[String] = None,
    // required
    issueBankCountryId: Int = 0,
    issueBank: String = "",
    billingAddress1: String = "",
    billingAddress2: String = "",
    billingCity: String = "",
    billingState: String = "",
    // required
    @(ApiModelProperty @field)(dataType = "Int")
    billingCountryId: Option[Int] = None,
    billingPostalcode: String = "",
    // required
    @ApiModel(description = "Indicates charge option (paynow/paylater/payAtCheckIn).")
    @JsonDeserialize(using = classOf[ChargeOptionAsIntDeserializer])
    @JsonScalaEnumeration(classOf[ChargeOptionType])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    chargeOption: ChargeOption,
    // at dbo.booking_summary saving as YYYY-MM-DD (no time or timezone)
    // required
    @JsonDeserialize(using = classOf[CreationDateTimeDeserializer])
    fullyAuthDate: DateTime = new DateTime(0),
    // at dbo.booking_summary saving as YYYY-MM-DD (no time or timezone)
    // required
    @JsonDeserialize(using = classOf[CreationDateTimeDeserializer])
    fullyChargeDate: DateTime = new DateTime(0),
    payment3DS: Option[Payment3DSRequest],
    internalToken: Option[String], // For redirect
    isSaveCCOF: Boolean,
    /** region NTT */

    /// <summary>
    /// The customer ID card.
    /// </summary>
    customerIdCard: Option[String],
    @(ApiModelProperty @field)(dataType = "Int")
    customerIdCardType: Option[Int],
    /// <summary>
    /// The OTP number.
    /// </summary>
    otp: Option[String],
    /// <summary>
    /// The customer phone number for
    /// </summary>
    phoneNumber: Option[String],
    /// <summary>
    /// The customer's payment option.
    /// </summary>
    // required
    paymentOption: Int = 0,
    /// <summary>
    /// Deprecated. Please use string type installmentPlanCode with the same semantic instead.
    /// Kept for backward compatability.
    /// </summary>
    @(ApiModelProperty @field)(dataType = "Int")
    installmentPlanId: Option[Int] = None,
    /// <summary>
    /* If the Customer's bank supports installment(for example, full price is 300$ and customer will pay 100$ every
     * month) */
    /// the field will defined the kind of installment (3 month, 6 month, etc), depend on the bank abilities
    /// </summary>
    @(ApiModelProperty @field)(dataType = "String")
    installmentPlanCode: Option[String] = None,
    /// <summary>
    /// Current installment token.
    /// The value filled by createBooking based on bookingToken data and installmentPlanCode
    /// </summary>
    @ApiModelProperty(hidden = true)
    installmentPlanToken: Option[String] = None,
    @(ApiModelProperty @field)(hidden = true, dataType = "Boolean")
    isNoCvc: Option[Boolean] = None,
    @(ApiModelProperty @field)(hidden = true, dataType = "Boolean")
    isBankNameRequired: Option[Boolean] = None,
    @(ApiModelProperty @field)(dataType = "Long")
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    aabCCReferenceId: Option[Long] = None
) extends SwaggerAnnotationsControl {

  @(ApiModelProperty @field)(hidden = true)
  @JsonIgnore
  def isCCOF: Option[Boolean] = Option(creditcardId != 0 || creditcardNumber.nonEmpty && isSaveCCOF)

  @(ApiModelProperty @field)(hidden = true)
  @JsonIgnore
  def isNewCC: Option[Boolean] = Option(creditcardId == 0)

  def expiryMonth: Option[Int] =
    expiryDate.flatMap { date =>
      if (date.nonEmpty)
        if (date.length == 5 || date.length == 7)
          Try(date.substring(0, 2).toInt).toOption
        else None
      else None
    }

  def expiryYear: Option[Int] =
    expiryDate.flatMap { date =>
      if (date.nonEmpty)
        if (date.length == 7)
          Try(date.substring(3, 7).toInt).toOption
        else if (date.length == 5)
          Try(2000 + date.substring(3, 5).toInt).toOption
        else None
      else None
    }

  def last4Digits: Option[String] =
    getCreditCardNumberLastN(creditcardNumber, 4)

  def getCreditCardNumberLastN(creditCardNumber: String, n: Int): Option[String] = {
    if (creditCardNumber.length > n) {
      Some(creditCardNumber.takeRight(n))
    } else {
      None
    }
  }
}

object CreditCard {
  def apply(creditCardV2: CreditCardV2, payment: BookingPaymentV2): CreditCard =
    new CreditCard(
      creditcardId = creditCardV2.detail.flatMap(_.ccofId).getOrElse(0),
      creditcardType = payment.method,
      cardholderName = creditCardV2.detail.flatMap(_.cardHolderName).getOrElse(""),
      creditcardNumber = creditCardV2.detail.flatMap(_.creditCardNumber).getOrElse(""),
      expiryDate = creditCardV2.detail.flatMap(_.expiryDate.map(_.toString)),
      securityCode = creditCardV2.detail.flatMap(_.securityCode),
      issueBankCountryId = 0, // This fields gets populated from token, refer ProductUtils.enrichCreditCardInfo
      issueBank = creditCardV2.detail.flatMap(_.issuingBank.map(_.name)).getOrElse(""),
      billingAddress1 = creditCardV2.detail.flatMap(_.billingAddress1).getOrElse(""),
      billingAddress2 = creditCardV2.detail.flatMap(_.billingAddress2).getOrElse(""),
      billingCity = creditCardV2.detail.flatMap(_.billingCity).getOrElse(""),
      billingState = creditCardV2.detail.flatMap(_.billingState).getOrElse(""),
      billingCountryId = creditCardV2.detail.flatMap(_.billingCountryId),
      billingPostalcode = creditCardV2.detail.flatMap(_.billingPostalCode).getOrElse(""),
      chargeOption = ChargeOption.None, // TODO map this from token.
      payment3DS = creditCardV2.payment3DS.map(Payment3DSRequest(_)),
      isSaveCCOF = creditCardV2.shouldSave,
      phoneNumber = creditCardV2.detail.flatMap(_.phoneNumber),
      paymentOption = 0,
      internalToken = None,
      customerIdCard = None,
      customerIdCardType = None,
      otp = None,
      installmentPlanId = creditCardV2.installmentPlanId,
      installmentPlanCode = creditCardV2.installmentPlanCode,
      aabCCReferenceId = creditCardV2.aabCCReferenceId
    )

}

object Payment3DSRequest {
  def apply(payment3DSRequestV2: Payment3DSRequestV2): com.agoda.bapi.common.message.creation.common.Payment3DSRequest =
    new com.agoda.bapi.common.message.creation.common.Payment3DSRequest(
      payment3DSOption = payment3DSRequestV2.payment3DSOption,
      acceptHeader = payment3DSRequestV2.acceptHeader,
      userAgent = payment3DSRequestV2.userAgent,
      language = payment3DSRequestV2.language,
      colorDepth = payment3DSRequestV2.colorDepth,
      screenHeight = payment3DSRequestV2.screenHeight,
      screenWidth = payment3DSRequestV2.screenWidth,
      timeZoneOffset = payment3DSRequestV2.timeZoneOffset,
      returnUrl = payment3DSRequestV2.returnUrl,
      javaEnabled = payment3DSRequestV2.javaEnabled,
      javaScriptEnabled = payment3DSRequestV2.javaScriptEnabled,
      supportedMPIs = payment3DSRequestV2.supportedMPIs,
      bankCallback3DS1Url = payment3DSRequestV2.bankCallback3DS1Url,
      deviceCollectionUrl = payment3DSRequestV2.deviceCollectionUrl,
      affiliatePaymentModel = payment3DSRequestV2.affiliatePaymentModel
    )
}

@ApiModel(description = "PaymentAmount is ignored from bookingToken version 3 onwards")
final case class PaymentAmount(
    // required
    paymentCurrency: String = "",
    // required
    paymentAmount: BigDecimal = 0,
    // required
    paymentAmountUSD: BigDecimal = 0,
    // required
    exchangeRate: BigDecimal = 0,
    // required
    upliftAmount: BigDecimal = 0,
    // required
    siteExchangeRate: BigDecimal,
    // required
    upliftExchangeRate: BigDecimal,
    @(ApiModelProperty @field)(dataType = "String")
    destinationCurrency: Option[String] = None,
    // required
    destinationExchangeRate: BigDecimal,
    rateQuoteId: Long = 0,
    rewardsRedeemedPoint: Int = 0,
    // required
    rewardsSaving: BigDecimal = 0,
    // required
    giftcardAmount: BigDecimal = 0,
    // required
    giftcardAmountUSD: BigDecimal = 0,
    exchangeRateOption: Int = 0,
    /* Todo: PaymentToken is ApplePay Required field but not for other payment method and should not be inside
     * paymentAmount */
    @ApiModel(
      description =
        "paymentToken is ignored from bookingToken version 3 onwards. So if we want to support paymentToken with v3, we need to move it out of PaymentAmount model"
    )
    @(ApiModelProperty @field)(dataType = "String", required = false, hidden = true)
    paymentToken: Option[String] = None,
    externalLoyalty: Option[ExternalLoyaltyPayment] = None,
    cashbackPayment: Option[CashbackPayment] = None
) extends SwaggerAnnotationsControl

object PaymentAmount {

  val ZERO: BigDecimal = BigDecimal(0)

  @SuppressWarnings(Array("org.wartremover.warts.IterableOps"))
  def sum(amounts: Traversable[PaymentAmount]): Either[IllegalArgumentException, PaymentAmount] = {
    val curexrs = amounts.map(a => (a.paymentCurrency, a.exchangeRate, a.siteExchangeRate)).toSeq.distinct
    if (amounts.isEmpty) throw ProductNotFoundException(s"Sum of Payment Amount is None. Product not found")
    requiredSingle(curexrs, "Product exchange rates mismatched!") { curexr =>
      amounts.foldLeft(
        PaymentAmount(
          paymentCurrency = curexr._1,
          exchangeRate = curexr._2,
          siteExchangeRate = curexr._3,
          upliftExchangeRate = amounts.map(_.upliftExchangeRate).max, // taking max uplift exchange rate
          destinationExchangeRate = ZERO,
          externalLoyalty = getExternalLoyaltyPayment(amounts)
        )
      ) { (p, c) =>
        p.copy(
          paymentAmount = p.paymentAmount + c.paymentAmount,
          paymentAmountUSD = p.paymentAmountUSD + c.paymentAmountUSD,
          giftcardAmount = p.giftcardAmount + c.giftcardAmount,
          giftcardAmountUSD = p.giftcardAmountUSD + c.giftcardAmountUSD,
          cashbackPayment = (p.cashbackPayment, c.cashbackPayment) match {
            case (None, None) => None
            case (pc, cc) =>
              Some(
                CashbackPayment(
                  cashbackAmount = pc.map(_.cashbackAmount).getOrElse(BigDecimal(0))
                    + cc.map(_.cashbackAmount).getOrElse(BigDecimal(0)),
                  cashbackAmountUSD = pc.map(_.cashbackAmountUSD).getOrElse(BigDecimal(0))
                    + cc.map(_.cashbackAmountUSD).getOrElse(BigDecimal(0))
                )
              )
            case _ => None
          }
        )
      }
    }
  }

  private def getExternalLoyaltyPayment(amounts: Traversable[PaymentAmount]): Option[ExternalLoyaltyPayment] = {
    amounts.flatMap(_.externalLoyalty).headOption
  }

}

final case class Customer(
    // required
    memberId: Int = 0,
    title: String = "",
    // required
    firstname: String = "",
    middlename: String = "",
    // required
    lastname: String = "",
    // required
    email: String = "",
    phoneFormat: String = "",
    faxFormat: String = "",
    address1: String = "",
    address2: String = "",
    postcode: String = "",
    region: String = "",
    // required
    countryId: Int = 0,
    state: String = "",
    city: String = "",
    area: String = "",
    // required
    newsletter: Boolean = false,
    // required
    isUserLoggedIn: Boolean = true,
    // some countries eg.brazil require tax id for customers
    taxId: Option[String] = None,
    b2bCustomerEmail: Option[String] = None,
    expUserId: Option[String] = None,
    dateOfBirth: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    placeOfBirth: Option[Int] = None,
    externalMemberId: Option[String] = None,
    @ApiModel(
      description =
        "state ID from our geo2_state table - in Japan these records store Japanese prefectures and could be used to build a dropdown list on BF"
    )
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    stateId: Option[Int] = None,
    emergencyPhoneNumber: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    age: Option[Int] = None,
    @ApiModel(description = "gender is PII data. Gender will be M = Male, F = Female or None if data doesn't exist")
    gender: Option[String] = None,
    localizedNames: Option[Seq[LocalizedName]] = None
) extends SwaggerAnnotationsControl

object Customer {

  private def getFormattedPhoneNumber(customerV2: CustomerV2)(implicit context: RequestContext): String = {
    val wlFeatureToNotPrependCountryCode =
      context.whiteLabelInfo.feature.bookingFormRequirements.customerPhoneNumberWithoutCountryCode.getOrElse(false)

    (customerV2.phoneContact, wlFeatureToNotPrependCountryCode) match {
      case (Some(phoneContact), false)
          if phoneContact.phoneNumber.nonEmpty && phoneContact.countryCallingCode.nonEmpty =>
        s"${phoneContact.countryCallingCode} ${phoneContact.phoneNumber}"
      case (Some(phoneContact), _) if phoneContact.phoneNumber.nonEmpty => s"${phoneContact.phoneNumber}"
      case (Some(_), _)                                                 => ""
      case (None, _)                                                    => customerV2.phoneFormat.getOrElse("")
    }
  }

  private def getCurrentValueForNewsletterOptIn(
      createBookingToken: Option[MultiProductCreationBookingToken]
  ): Boolean = {
    createBookingToken.exists(_.optIns.exists(_.isNewsletterOptedIn))
  }

  def apply(
      customerV2: CustomerV2,
      userContext: Option[UserContext],
      bookingToken: Option[MultiProductCreationBookingToken]
  )(implicit context: RequestContext): Customer =
    new Customer(
      memberId = userContext.flatMap(_.memberId).getOrElse(0),
      title = customerV2.title.getOrElse(""),
      firstname = customerV2.firstName,
      middlename = customerV2.middleName.getOrElse(""),
      lastname = customerV2.lastName,
      email = customerV2.email,
      phoneFormat = getFormattedPhoneNumber(customerV2),
      faxFormat = customerV2.faxFormat.getOrElse(""),
      address1 = customerV2.address1.getOrElse(""),
      address2 = customerV2.address2.getOrElse(""),
      postcode = customerV2.postcode.getOrElse(""),
      region = customerV2.region.getOrElse(""),
      countryId = customerV2.countryId,
      state = customerV2.state.getOrElse(""),
      city = customerV2.city.getOrElse(""),
      area = customerV2.area.getOrElse(""),
      newsletter = customerV2.isNewsletterOptIn.getOrElse(getCurrentValueForNewsletterOptIn(bookingToken)),
      isUserLoggedIn = userContext.map(_.isLoggedInUser).getOrElse(false),
      stateId = customerV2.stateId,
      emergencyPhoneNumber = customerV2.emergencyPhoneNumber,
      age = customerV2.age,
      gender = customerV2.gender,
      localizedNames = customerV2.localizedNames match {
        case Some(items) => Option(items.map(LocalizedName(_)))
        case None        => None
      },
      externalMemberId = customerV2.externalMemberId,
      b2bCustomerEmail = customerV2.b2bCustomerEmail
    )
}

final case class Discount(
    // required
    @ApiModel(description = "Discount type")
    @JsonDeserialize(using = classOf[DiscountTypeDeserializer])
    @JsonScalaEnumeration(classOf[DiscountTypeType])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    discountType: DiscountType.DiscountType,
    // required
    discountAmount: BigDecimal = 0,
    promotionCode: Option[String] = None,
    promotionText: Option[String] = None,
    // required
    promotionCampaignId: Int = 0,
    // required
    @SuppressWarnings(Array("org.wartremover.warts.Null"))
    membershipContentText: String = null,
    // required
    membershipContentLabel: String = ""
) extends SwaggerAnnotationsControl

trait Guest {
  def guestNo: Int

  def title: String

  def firstname: String

  def lastname: String

  def primary: Boolean

  def middlename: String

  def nationalityId: Int

  def isAdult: Boolean
}

final case class HotelGuest(
    // required
    guestNo: Int = 0,
    title: String = "",
    // required
    firstname: String = "",
    // required
    lastname: String = "",
    // required
    primary: Boolean = false,
    middlename: String = "",
    suffix: String = "",
    // required
    isExtrabed: Boolean = false,
    // required
    age: Int = 0,
    // required
    nationalityId: Int = 0,
    // required
    isAdult: Boolean = true,
    email: Option[String] = None,
    @ApiModel(description = "gender is PII data. Gender will be M = Male, F = Female or None if data doesn't exist")
    gender: Option[String] = None,
    phoneFormat: Option[String] = None,
    emergencyPhone: Option[String] = None,
    postcode: Option[String] = None,
    city: Option[String] = None,
    area: Option[String] = None,
    address: Option[String] = None,
    @ApiModel(
      description =
        "state ID from our geo2_state table - in Japan these records store Japanese prefectures and could be used to build a dropdown list on BF"
    )
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    stateId: Option[Int] = None,
    localizedNames: Option[Seq[LocalizedName]] = None,
    @ApiModel(description = "room index used to assign guest to a specific room")
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    roomIndex: Option[Int] = None,
    citizenshipId: Option[String] = None,
    @(ApiModelProperty @field)(dataType = "Boolean", required = false)
    allGuestsSameNationality: Option[Boolean] = None
) extends Guest
    with SwaggerAnnotationsControl

final case class FrequentFlyer(airlineId: String, frequentFlyerId: String)

final case class FlightPax(
    id: Int = 0,
    guestNo: Int = 0,
    title: String = "",
    firstname: String = "",
    lastname: String = "",
    primary: Boolean = false,
    middlename: String = "",
    @JsonSerialize(using = classOf[LocalDateSerializer])
    @JsonDeserialize(using = classOf[LocalDateDeserializer])
    birthDate: LocalDate,
    nationalityId: Int = 0,
    isAdult: Boolean = true,
    gender: String = "",
    passportNumber: Option[String] = None,
    @JsonSerialize(using = classOf[OptionLocalDateSerializer])
    @JsonDeserialize(using = classOf[OptionLocalDateDeserializer])
    passportExpires: Option[LocalDate] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    passportCountryId: Option[Int] = None,
    @(ApiModelProperty @field)(dataType = "Boolean")
    shouldSaveFavorite: Option[Boolean] = Some(false),
    passengerType: Option[String] = None,
    knownTravelerNumber: Option[String] = None,
    frequentFlyer: Option[Seq[FrequentFlyer]] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    countryOfResidenceId: Option[Int] = None
) extends Guest
    with SwaggerAnnotationsControl {
  def piiHash: String = Flight.passengerPIIHash(firstname, lastname, birthDate, gender)
}

final case class CarDriver(
    title: String,
    @JsonProperty("firstname")
    @ApiModelProperty(name = "firstname", accessMode = ApiModelProperty.AccessMode.READ_WRITE)
    firstName: String,
    @JsonProperty("lastname")
    @ApiModelProperty(name = "lastname", accessMode = ApiModelProperty.AccessMode.READ_WRITE)
    lastName: String,
    email: Option[String],
    phoneNumber: Option[String],
    @JsonProperty("middlename")
    @ApiModelProperty(name = "middlename", accessMode = ApiModelProperty.AccessMode.READ_WRITE)
    middleName: Option[String],
    gender: Option[String],
    flightNo: Option[String],
    license: Option[String],
    address: Option[String],
    @(ApiModelProperty @field)(dataType = "Int")
    countryOfResidenceId: Option[Int] = None
) extends SwaggerAnnotationsControl {
  def toEnigmaDriverInfo: EnigmaCarDriverInfo =
    EnigmaCarDriverInfo(
      title = title,
      firstName = firstName,
      lastName = lastName,
      middleName = middleName,
      email = email,
      phoneNumber = phoneNumber,
      gender = gender,
      flightNo = flightNo,
      license = license,
      address = address,
      countryOfResidenceId = countryOfResidenceId
    )
}

object CarDriver {
  def apply(carDriver: CarDriver): CarDriver =
    carDriver.copy(
      title = carDriver.title.trim,
      firstName = carDriver.firstName.trim,
      lastName = carDriver.lastName.trim,
      middleName = carDriver.middleName.map(name => name.trim)
    )
}

final case class ActivityPax(
    id: Long = 0,
    title: Option[String] = None,
    firstname: Option[String] = None,
    lastname: Option[String] = None,
    primary: Boolean = false,
    middlename: Option[String] = None,
    @ApiModel(description = "type for this pax")
    @JsonSerialize(using = classOf[PaxTypeSerializer])
    @JsonDeserialize(using = classOf[PaxTypeDeserializer])
    @JsonScalaEnumeration(classOf[PaxType])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    paxType: PaxType.PaxType,
    @JsonSerialize(using = classOf[OptionLocalDateSerializer])
    @JsonDeserialize(using = classOf[OptionLocalDateDeserializer])
    birthDate: Option[LocalDate] = None,
    gender: Option[String] = None,
    @ApiModel(description = "nationality for this pax")
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    nationalityId: Option[Int] = None,
    passportNumber: Option[String] = None,
    @JsonSerialize(using = classOf[OptionLocalDateSerializer])
    @JsonDeserialize(using = classOf[OptionLocalDateDeserializer])
    passportExpires: Option[LocalDate] = None,
    email: Option[String] = None,
    phoneNumber: Option[String] = None,
    phoneCountryCode: Option[String] = None,
    @ApiModel(description = "A dynamic structure to support arbitrary pax information e.g. weight")
    meta: Seq[ActivityPaxMetaInfo] = Seq.empty
) extends SwaggerAnnotationsControl {
  // def piiHash: String = "DO SOME HASH" //should we share PII hash logic? do we need PII hash logic?
}

final case class ActivityPaxMetaInfo( // e.g. height, weight
    key: String,
    value: String,
    unit: String
) extends SwaggerAnnotationsControl

final case class ActivityBookingAnswer(
    bookingQuestionCode: String, // for other per booking question
    questionAns: String,
    ansUnit: String
) extends SwaggerAnnotationsControl

final case class ActivityLanguageGuideAnswer(
    `type`: String,
    language: String
) extends SwaggerAnnotationsControl

final case class Products(
    propertyItems: Option[Seq[PropertyItem]] = None,
    flightItems: Option[Seq[FlightItem]] = None,
    carItems: Option[Seq[CarItem]] = None,
    protectionItems: Option[Seq[ProtectionItem]] = None,
    @ApiModel(description = "Multiple product token")
    bookingToken: Option[TokenMessage] = None,
    totalPriceDisplay: Option[PriceBreakdownNode] = None,
    @(ApiModelProperty @field)(dataType = "Int")
    priceDisplayVersion: Option[Int] = None,
    activitiesItems: Option[Seq[ActivitiesItem]] = None
) extends SwaggerAnnotationsControl

object Products {
  def apply(productsV2: ProductsV2, bookingToken: Option[TokenMessage]): Products =
    new Products(
      propertyItems = Option(productsV2.propertyItems.map(PropertyItem(_))),
      flightItems = Option(productsV2.flightItems.map(f => FlightItem(f, "TODO"))),
      carItems = productsV2.carItems match {
        case Some(items) => Option(items.map(CarItem(_)))
        case None        => None
      },
      bookingToken = bookingToken,
      activitiesItems = productsV2.activitiesItems.map(_.map(ActivitiesItem(_)))
    )
}

final case class ProtectionItem(
    protectionBookingToken: String,
    @ApiModel(description = "key for mapping multi token with product array")
    productTokenKey: Option[ProductTokenKey] = None
) extends SwaggerAnnotationsControl

final case class PropertyItem(
    // roomUid: String,
    // papiRequest: String, //PropertyRequest serialized to JSON
    papiJsonToken: Option[String] = None, // Serialized BAPIBooking object
    // requestUniqueId: String, //ABS requestUniqueId
    @deprecated("use specialRequestV2")
    specialRequest: Option[String] = None,
    specialRequestV2: Option[SpecialRequests] = None,
    priceAdjust: Option[PriceAdjust] = None,
    // partnerInfo: Option[PartnerInfo] // affiliate-team does not need this
    @ApiModel(description = "Set name of room type, if papi does not has this information")
    @(ApiModelProperty @field)(hidden = true) // NOT TODO: not sure does affiliate still need this crutch
    @JsonIgnore
    roomTypeName: Option[String] = None,
    guests: Option[Seq[HotelGuest]] = None,
    // TODO: deprecate after multiple product token implementation
    bookingToken: Option[TokenMessage] = None,
    @ApiModel(description = "key for mapping multi token with product array")
    productTokenKey: Option[ProductTokenKey] = None,
    occupancy: Option[Seq[Occupancy]] = None,
    @ApiModel(description = "For NHA bookings, the user will send hello message to the host.")
    greetingMessage: Option[String] = None
) extends SwaggerAnnotationsControl

object PropertyItem {
  def apply(
      propertyItemV2: PropertyItemV2
  ): PropertyItem =
    new PropertyItem(
      specialRequestV2 = propertyItemV2.specialRequest,
      priceAdjust = None,
      specialRequest = None,
      guests = Option(propertyItemV2.guests),
      productTokenKey = Option(propertyItemV2.id),
      greetingMessage = propertyItemV2.greetingMessage,
      occupancy = propertyItemV2.occupancy
    )
}

final case class SpecialRequests(
    selectedRequests: Seq[SpecialRequestId],
    additionalNotes: Option[String] = None,
    flightArrivalNo: Option[String] = None,
    flightArrivalTime: Option[String] = None,
    arrivalTime: Option[String] = None,
    /* If the hotel has questions like food allergic, pick up place, etc. the hotel will set question in Booking-form
     * Then when customer books the room and answer those question through bookerAnswer field This field will be an
     * additional field in booking-form and will be passed all the way to ABS to the hotel */
    bookerAnswer: Option[String] = None,
    airportTransferNotes: Option[String] = None
) extends SwaggerAnnotationsControl {
  def toSpecialRequestString(
      hotelFundingText: Option[String],
      localVoucherText: Option[String]
  ): String = {
    val needAirportTransfer = selectedRequests.exists(s => s == SpecialRequestIds.AirportTransfer)
    val specialRequestsList: Seq[String] = selectedRequests.collect {
      case sr if sr != SpecialRequestIds.AdditionalNotes => sr.getEbeString()
    }.distinct ++ (
      flightArrivalNo.withFilter(a => needAirportTransfer && a.nonEmpty).map { flightNo =>
        s"ArrivalFlightNo:${cleanSpecialRequestText(flightNo)}"
      } ++
        flightArrivalTime.withFilter(a => needAirportTransfer && a.nonEmpty).map { arrivalTime =>
          s"ArrivalFlightTime:${cleanSpecialRequestText(arrivalTime)}"
        } ++
        airportTransferNotes.withFilter(a => needAirportTransfer && a.nonEmpty).map { transferNotes =>
          s"AirportTransferNotes:${cleanSpecialRequestText(transferNotes)}"
        } ++
        arrivalTime.withFilter(a => a.nonEmpty).map { arrivalTime =>
          s"ArrivalTime:${cleanSpecialRequestText(arrivalTime)}"
        } ++
        additionalNotes.withFilter(a => a.nonEmpty).map { additionalNotes =>
          s"AdditionalNotes:${cleanSpecialRequestText(additionalNotes)}"
        } ++
        hotelFundingText.withFilter(a => a.nonEmpty).map { hotelFundingText =>
          s"CouponInfo:${cleanSpecialRequestText(hotelFundingText)}"
        } ++
        localVoucherText.withFilter(a => a.nonEmpty).map(localVoucherText => localVoucherText)
    )
    specialRequestsList.mkString(", ")
  }

  private def cleanSpecialRequestText(text: String) = text.replace(',', ' ').replace(':', ' ')
}

final case class FlightItem(
    @ApiModel(description = "For now it is serialized price confirmation response")
    flapiJsonToken: String,
    pax: Option[Seq[FlightPax]],
    @ApiModel(description = "key for mapping multi token with product array")
    productTokenKey: Option[ProductTokenKey] = None
) extends SwaggerAnnotationsControl

object FlightItem {
  def apply(flightItemV2: FlightItemV2, flapiJsonToken: String): FlightItem =
    new FlightItem(pax = flightItemV2.pax, productTokenKey = Some(flightItemV2.id), flapiJsonToken = flapiJsonToken)
}

final case class CarItem(
    driver: Option[CarDriver],
    productTokenKey: Option[ProductTokenKey] = None
) extends SwaggerAnnotationsControl

object CarItem {
  def apply(carItemV2: CarItemV2): CarItem = CarItem(Some(CarDriver(carItemV2.driver)), Some(carItemV2.id))
}

final case class ActivitiesItem(
    pax: Option[Seq[ActivityPax]],
    bookingAnswers: Option[Seq[ActivityBookingAnswer]],
    languageGuide: Option[ActivityLanguageGuideAnswer],
    productTokenKey: Option[ProductTokenKey] = None
) extends SwaggerAnnotationsControl

object ActivitiesItem {
  def apply(activitiesItemV2: ActivitiesItemV2): ActivitiesItem =
    ActivitiesItem(
      pax = activitiesItemV2.pax,
      bookingAnswers = activitiesItemV2.bookingAnswers,
      languageGuide = activitiesItemV2.languageGuide,
      productTokenKey = Some(activitiesItemV2.id)
    )
}

final case class FraudInfo(
    defensiveCheckboxClicked: Boolean,
    // TODO: maybe we will meed add fraudIP
    @ApiModel(description = "Serialized Device Fingerprint Data")
    @(ApiModelProperty @field)(dataType = "String", required = false)
    deviceFingerprintData: Option[String],
    @ApiModel(description = "Session Id for Fraud MFA check")
    @(ApiModelProperty @field)(dataType = "String", required = false)
    tmxSessionId: Option[String] = None,
    @ApiModel(description = "Forter Token")
    @(ApiModelProperty @field)(dataType = "String", required = false)
    forterToken: Option[String] = None
) extends SwaggerAnnotationsControl

/** Price adjustment */
final case class PriceAdjust(
    roomPriceDiffResult: Option[RoomPriceDiffResult],
    surchargePriceDiffResult: Set[SurchargePriceDiffResult]
) extends SwaggerAnnotationsControl

final case class RoomPriceDiffResult(
    sellInAdjusted: Money,
    sellInNetInAdjusted: Money
) extends SwaggerAnnotationsControl

final case class SurchargePriceDiffResult(surchargeId: Long, adjustedPrice: Money) extends SwaggerAnnotationsControl

final case class GiftCardEarning(
    amount: BigDecimal,
    @(ApiModelProperty @field)(dataType = "Int")
    offsetDays: Option[Int],
    isAutoMigrate: Boolean,
    giftCardGuid: String,
    expiryDays: Int
) extends SwaggerAnnotationsControl

final case class PartnerLoyaltyPoint(
    programId: Int,
    membershipId: Option[String],
    totalPoint: BigDecimal,
    totalCostAmountUsd: BigDecimal,
    totalCostAmount: BigDecimal,
    gstCostAmountUsd: BigDecimal,
    gstCostAmount: BigDecimal,
    currency: String
) extends SwaggerAnnotationsControl

/* ReferenceToken will be used with GMO-3DS payment which the booking flow calls ABS 2 times.
 * All values will be assigned from the 1st call to ABS.
 * It use to store important value that BAPI/ABS will reuse in the 2nd call For more information, please refer here
 * https://confluence.agoda.local/x/Wp6xBg */
final case class ReferenceToken(bookingId: Long, sessionId: String, itineraryId: Long, externalBookingId: String)

final case class LocalizedName(
    languageId: Int,
    @ApiModel(description = "Indicates the script type. (Latin/Kanji/Kana)")
    @JsonDeserialize(using = classOf[LanguageScriptTypeDeserializer])
    @JsonSerialize(using = classOf[LanguageScriptTypeSerializer])
    @JsonScalaEnumeration(classOf[LanguageScriptTypeRef])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    scriptId: LanguageScriptType,
    firstname: String,
    lastname: String
) extends SwaggerAnnotationsControl

object LocalizedName {
  val japaneseLanguageId: Int = 6

  def apply(nameV2: LocalizedNameV2): LocalizedName =
    new LocalizedName(nameV2.languageId, nameV2.scriptId, nameV2.firstname, nameV2.lastname)
}

final case class Occupancy(
    roomNo: Int,
    noOfAdultMales: Int,
    noOfAdultFemales: Int,
    childOccupancy: Option[Seq[ChildOccupancy]] = None
) extends SwaggerAnnotationsControl

final case class ChildOccupancy(
    @ApiModel(
      description = "Indicates the child category type. (A:Bed+AdultMeal/B:Bed+ChildMeal/C:Infant+Bed/D:Infant)"
    )
    @JsonDeserialize(using = classOf[ChildTypeDeserializer])
    @JsonSerialize(using = classOf[ChildTypeSerializer])
    @JsonScalaEnumeration(classOf[ChildTypeRef])
    @(ApiModelProperty @field)(dataType = "Int", required = true)
    typeId: ChildType,
    count: Int
) extends SwaggerAnnotationsControl

final case class SupplierData(
    @ApiModel(description = "This is a site id from partner api. This value is not the same as our internal SiteId")
    supplierSiteId: Option[String],
    @ApiModel(
      description =
        "This is a subsite id from partner api. Use to check device types and login status of partner booking"
    )
    supplierSubSiteId: Option[String],
    @ApiModel(
      description =
        "This is a transaction id from partner api. It is a required field to cut allotment with JTB supplier api"
    )
    supplierTransactionId: Option[String],
    @ApiModel(description = "External booking id from partner api")
    externalBookingId: Option[String],
    @ApiModel(
      description =
        "Additional information from supplier api which is related to the supplier discount, coupon, points, and campaign (these are not Agoda data)"
    )
    supplierAdditionalInfo: Option[String],
    @ApiModel(description = "JSON data from supplier api. All data in this field will be used to show on YCS UI only")
    supplierFreeItem: Option[String],
    @ApiModel(description = "Data related to the reservation sending from supplier api (these are not Agoda data)")
    supplierReservationInfo: Option[String],
    @ApiModel(description = "Specific hotel chains will have special behaviors")
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    supplierChainId: Option[Int],
    @ApiModel(description = "AuthenticationKey per request, will use for cancellation")
    @(ApiModelProperty @field)(dataType = "String", required = false)
    authenticationKey: Option[String],
    @ApiModel(description = "PaymentChannel from supplier api use in CMAPI get booking details")
    @JsonSerialize(contentUsing = classOf[SupplierPaymentChannelSerializer])
    @JsonDeserialize(contentUsing = classOf[SupplierPaymentChannelDeserializer])
    @JsonScalaEnumeration(classOf[SupplierPaymentChannelType])
    @(ApiModelProperty @field)(dataType = "Int", required = false)
    paymentChannel: Option[SupplierPaymentChannel.SupplierPaymentChannel]
) extends SwaggerAnnotationsControl

final case class GovernmentCampaignInfo(
    @ApiModel(description = "This is campaign id for booking process")
    campaignId: Int,
    @ApiModel(description = "This is campaign data for booking process")
    campaignToken: String,
    @ApiModel(description = "This is last 4 characters of the citizen Id used for confirming campaign booking")
    last4CitizenId: Option[String],
    @ApiModel(description = "This is reference Id communicated to government partner")
    referenceId: Option[String]
) extends SwaggerAnnotationsControl
