package com.agoda.bapi.common.mapper.booking

import com.agoda.bapi.common.mapper.booking.vehicle.VehicleModelInternalToVehiclesForMessageConverter
import com.agoda.bapi.common.message.{ActivityBookingStateWithItinerary, AddOnBookingStateWithItinerary, CegFastTrackBookingStateWithItinerary, PropertyBookingStateWithItinerary}
import com.agoda.bapi.common.model.base.{BaseBookingEssInfoInternal, BaseBookingModelInternal, BaseBookingRelationshipInternal, BaseCancellationInfoModelInternal}
import com.agoda.bapi.common.model.booking._
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.common.model.car.VehicleBookingState
import com.agoda.bapi.common.model.creation.common.AccountingEntity
import com.agoda.bapi.common.model.flight.flightModel._
import com.agoda.bapi.common.model.flight.history.ActionType.ActionType
import com.agoda.bapi.common.model.itinerary.ItineraryInternalModel
import com.agoda.bapi.common.model.multiproduct.{MultiProductBookingGroupDBModel, MultiProductInfoDBModel}
import com.agoda.bapi.common.model.protection.ProtectionBookingStateModel.{ProtectionCfar, ProtectionModelInternal}
import com.agoda.bapi.common.model.{ActionId, CommonBookingEventsInfo, CommonBookingInfo}
import com.agoda.bapi.common.util.JodaToJavaDateTimeConversions
import com.agoda.bapi.common.util.LocalDateTimeUtil.toJodaLocalDateTime
import com.agoda.commons.agprotobuf.scalapb.utils.ProtoConverter
import io.scalaland.chimney.dsl.TransformerOps

import java.util.Date

trait BookingStateMessageMapper {
  private val schemaVersion  = "1"
  private val emptyBookingId = 0L

  def fromVehicleBookingState(
      actionType: ActionType,
      actionId: ActionId,
      bookingType: Option[Int],
      vehicleBookingState: VehicleBookingState,
      protectionModel: Seq[ProtectionModelInternal] = Seq.empty,
      multiProductInfos: Seq[MultiProductInfoDBModel]
  ): BookingStateMessage =
    BookingStateMessage(
      actionType = actionType.id,
      actionId = actionId,
      bookingType = bookingType,
      bookingId = emptyBookingId, // legacy field, we replicate itinerary which can contain multiple bookingIds
      schemaVersion = schemaVersion,
      flights = Seq.empty,
      slices = Seq.empty,
      segments = Seq.empty,
      passengers = Seq.empty,
      payments = mapPaymentToPaymentForMessage(vehicleBookingState.payments),
      bookingPayments = mapBookingPaymentToBookingPaymentForMessage(vehicleBookingState.bookingPayments),
      bookingRelationships = Seq.empty,
      breakdown = Seq.empty, // vehicle's breakdowns are in `vehicles.vehicleFinancialBreakdowns`
      breakdownPerPax = Seq.empty,
      baggageAllowance = Seq.empty,
      baggage = Seq.empty,
      history = mapItineraryHistoryToItineraryHistoryForMessage(vehicleBookingState.itineraryHistories),
      summary = Seq.empty,
      paxTickets = Seq.empty,
      itinerary = mapMultiProductItineraryToFlightItineraryForMessage(vehicleBookingState.itinerary),
      userAgent = None,
      bookingAttribution = Seq.empty,
      itineraryDate = new Date(), // this field is not used by vehicles, defaults to current time
      protectionModels = Option(mapProtectionBookingToProtectionBookingForMessage(protectionModel)).filter(_.nonEmpty),
      multiProductInfos = mapMultiProductInfosForMessage(multiProductInfos),
      flightSegmentInfoByPaxType = Seq.empty,
      segmentInfoByPaxType = Seq.empty,
      fareRulePolicies = None,
      flightSeatSelection = Seq.empty,
      vehicle = Some(vehicleBookingState.vehicleModelsInternal.map(VehicleModelInternalToVehiclesForMessageConverter)),
      activities = None,
      properties = None,
      cegFastTracks = None,
      addOns = None,
      multiProductBookingGroups = mapMultiProductBookingGroupsToMessage(vehicleBookingState.multiProductBookingGroups),
      flightBrandSelections = None,
      flightBrandAttributes = None,
      flightBrandAttributeParams = None,
      flightBaseBooking = None,
      flightBaseCancellationInfo = None,
      crossProductIsolatedFeature = None
    )

  def fromActivityProtoState(
      actionType: ActionType,
      actionId: ActionId,
      bookingType: Option[Int],
      activityBookingState: ActivityBookingStateWithItinerary,
      multiProductInfos: Seq[MultiProductInfoDBModel]
  ): BookingStateMessage = {
    BookingStateMessage(
      actionType = actionType.id,
      actionId = actionId,
      bookingType = bookingType,
      bookingId = emptyBookingId,
      schemaVersion = schemaVersion,
      flights = Seq.empty,
      slices = Seq.empty,
      segments = Seq.empty,
      passengers = Seq.empty,
      payments = mapPaymentToPaymentForMessage(activityBookingState.payments),
      bookingPayments = mapBookingPaymentToBookingPaymentForMessage(activityBookingState.bookingPayments),
      bookingRelationships = Seq.empty,
      breakdown = Seq.empty,
      breakdownPerPax = Seq.empty,
      baggageAllowance = Seq.empty,
      baggage = Seq.empty,
      history = mapItineraryHistoryToItineraryHistoryForMessage(activityBookingState.itineraryHistories),
      summary = Seq.empty,
      paxTickets = Seq.empty,
      itinerary = mapMultiProductItineraryToFlightItineraryForMessage(activityBookingState.itinerary),
      userAgent = None,
      bookingAttribution = Seq.empty,
      itineraryDate = new Date(), // what should be the value for activity?
      protectionModels = None,
      multiProductInfos = mapMultiProductInfosForMessage(multiProductInfos),
      flightSegmentInfoByPaxType = Seq.empty,
      segmentInfoByPaxType = Seq.empty,
      fareRulePolicies = None,
      flightSeatSelection = Seq.empty,
      vehicle = None,
      activities = Some(
        activityBookingState.activities.map { activity =>
          ActivityForMessage(
            ProtoConverter.protoToString(activity),
            activity.product.booking.bookingId
          )
        }
      ),
      properties = None,
      cegFastTracks = None,
      addOns = None,
      multiProductBookingGroups = mapMultiProductBookingGroupsToMessage(activityBookingState.multiProductBookingGroups),
      flightBrandSelections = None,
      flightBrandAttributes = None,
      flightBrandAttributeParams = None,
      flightBaseBooking = None,
      flightBaseCancellationInfo = None,
      crossProductIsolatedFeature = None
    )
  }

  def fromPropertyProtoState(
      actionType: ActionType,
      actionId: ActionId,
      bookingType: Option[Int],
      propertyBookingStateWithItinerary: PropertyBookingStateWithItinerary
  ): BookingStateMessage = {
    BookingStateMessage(
      actionType = actionType.id,
      actionId = actionId,
      bookingType = bookingType,
      bookingId = emptyBookingId,
      schemaVersion = schemaVersion,
      flights = Seq.empty,
      slices = Seq.empty,
      segments = Seq.empty,
      passengers = Seq.empty,
      payments = Seq.empty,
      bookingPayments = Seq.empty,
      bookingRelationships = Seq.empty,
      breakdown = Seq.empty,
      breakdownPerPax = Seq.empty,
      baggageAllowance = Seq.empty,
      baggage = Seq.empty,
      history = Seq.empty,
      summary = Seq.empty,
      paxTickets = Seq.empty,
      itinerary = mapMultiProductItineraryToFlightItineraryForMessage(propertyBookingStateWithItinerary.itinerary),
      userAgent = None,
      bookingAttribution = Seq.empty,
      itineraryDate = propertyBookingStateWithItinerary.itinerary.recCreatedWhen.map(_.toDate).getOrElse(new Date()),
      protectionModels = None,
      multiProductInfos = None,
      flightSegmentInfoByPaxType = Seq.empty,
      segmentInfoByPaxType = Seq.empty,
      fareRulePolicies = None,
      flightSeatSelection = Seq.empty,
      vehicle = None,
      activities = None,
      properties = Some(
        propertyBookingStateWithItinerary.properties.flatMap { property =>
          property.propertyProductModel.map(propertyProductModel =>
            PropertyForMessage(
              propertyState = ProtoConverter.protoToString(propertyProductModel),
              bookingId = property.bookingId,
              stateId = property.stateId
            )
          )
        }
      ),
      cegFastTracks = None,
      addOns = None,
      multiProductBookingGroups = None,
      flightBrandSelections = None,
      flightBrandAttributes = None,
      flightBrandAttributeParams = None,
      flightBaseBooking = None,
      flightBaseCancellationInfo = None,
      crossProductIsolatedFeature = None
    )
  }

  def fromCegFastTrackProtoState(
      actionType: ActionType,
      actionId: ActionId,
      bookingType: Option[Int],
      bookingState: CegFastTrackBookingStateWithItinerary,
      multiProductInfos: Seq[MultiProductInfoDBModel]
  ): BookingStateMessage = {
    BookingStateMessage(
      actionType = actionType.id,
      actionId = actionId,
      bookingType = bookingType,
      bookingId = emptyBookingId,
      schemaVersion = schemaVersion,
      flights = Seq.empty,
      slices = Seq.empty,
      segments = Seq.empty,
      passengers = Seq.empty,
      payments = mapPaymentToPaymentForMessage(bookingState.payments),
      bookingPayments = mapBookingPaymentToBookingPaymentForMessage(bookingState.bookingPayments),
      bookingRelationships = mapBaseBookingRelationshipToBaseBookingRelationshipForMessage(bookingState.relationships),
      breakdown = Seq.empty,
      breakdownPerPax = Seq.empty,
      baggageAllowance = Seq.empty,
      baggage = Seq.empty,
      history = mapItineraryHistoryToItineraryHistoryForMessage(bookingState.itineraryHistories),
      summary = Seq.empty,
      paxTickets = Seq.empty,
      itinerary = mapMultiProductItineraryToFlightItineraryForMessage(bookingState.itinerary),
      userAgent = None,
      bookingAttribution = Seq.empty,
      itineraryDate = new Date(), // what should be the value for activity?
      protectionModels = None,
      multiProductInfos = mapMultiProductInfosForMessage(multiProductInfos),
      flightSegmentInfoByPaxType = Seq.empty,
      segmentInfoByPaxType = Seq.empty,
      fareRulePolicies = None,
      flightSeatSelection = Seq.empty,
      vehicle = None,
      activities = None,
      properties = None,
      cegFastTracks = Some(
        bookingState.cegFastTracks.map { cegFastTrack =>
          CegFastTrackForMessage(
            ProtoConverter.protoToString(cegFastTrack),
            cegFastTrack.product.booking.bookingId
          )
        }
      ),
      addOns = None,
      multiProductBookingGroups = mapMultiProductBookingGroupsToMessage(bookingState.multiProductBookingGroups),
      flightBrandSelections = None,
      flightBrandAttributes = None,
      flightBrandAttributeParams = None,
      flightBaseBooking = None,
      flightBaseCancellationInfo = None,
      crossProductIsolatedFeature = None
    )
  }

  def fromAddOnProtoState(
      actionType: ActionType,
      actionId: ActionId,
      bookingType: Option[Int],
      bookingState: AddOnBookingStateWithItinerary,
      multiProductInfos: Seq[MultiProductInfoDBModel]
  ): BookingStateMessage = {
    val addOnMessages = bookingState.addOns.map { addOn =>
      AddOnForMessage(
        ProtoConverter.protoToString(addOn),
        addOn.product.booking.bookingId,
        addOn.product.booking.productTypeId
      )
    }
    BookingStateMessage(
      actionType = actionType.id,
      actionId = actionId,
      bookingType = bookingType,
      bookingId = emptyBookingId,
      schemaVersion = schemaVersion,
      flights = Seq.empty,
      slices = Seq.empty,
      segments = Seq.empty,
      passengers = Seq.empty,
      payments = mapPaymentToPaymentForMessage(bookingState.payments),
      bookingPayments = mapBookingPaymentToBookingPaymentForMessage(bookingState.bookingPayments),
      bookingRelationships = mapBaseBookingRelationshipToBaseBookingRelationshipForMessage(bookingState.relationships),
      breakdown = Seq.empty,
      breakdownPerPax = Seq.empty,
      baggageAllowance = Seq.empty,
      baggage = Seq.empty,
      history = mapItineraryHistoryToItineraryHistoryForMessage(bookingState.itineraryHistories),
      summary = Seq.empty,
      paxTickets = Seq.empty,
      itinerary = mapMultiProductItineraryToFlightItineraryForMessage(bookingState.itinerary),
      userAgent = None,
      bookingAttribution = Seq.empty,
      itineraryDate = new Date(),
      protectionModels = None,
      multiProductInfos = mapMultiProductInfosForMessage(multiProductInfos),
      flightSegmentInfoByPaxType = Seq.empty,
      segmentInfoByPaxType = Seq.empty,
      fareRulePolicies = None,
      flightSeatSelection = Seq.empty,
      vehicle = None,
      activities = None,
      properties = None,
      cegFastTracks = None,
      addOns = Option(addOnMessages).filter(_.nonEmpty), // return None if addOnMessages is empty
      multiProductBookingGroups = mapMultiProductBookingGroupsToMessage(bookingState.multiProductBookingGroups),
      flightBrandSelections = None,
      flightBrandAttributes = None,
      flightBrandAttributeParams = None,
      flightBaseBooking = None,
      flightBaseCancellationInfo = None,
      crossProductIsolatedFeature = None
    )
  }

  def mapItineraryModelToBookingStateMessage(
      itineraryDbModel: ItineraryInternalModel,
      actionType: ActionType,
      actionId: ActionId,
      bookingType: Option[Int],
      multiProductInfos: Seq[MultiProductInfoDBModel],
      multiProductBookingGroups: Seq[MultiProductBookingGroupDBModel]
  ): BookingStateMessage =
    BookingStateMessage(
      actionType = actionType.id,
      actionId = actionId,
      bookingType = bookingType,
      bookingId = emptyBookingId,
      schemaVersion = schemaVersion, // Hardcoded, move to config?
      flights = Seq.empty,
      slices = Seq.empty,
      segments = Seq.empty,
      passengers = Seq.empty,
      payments = mapPaymentToPaymentForMessage(itineraryDbModel.payments),
      bookingPayments = mapBookingPaymentToBookingPaymentForMessage(itineraryDbModel.bookingPayments),
      bookingRelationships =
        mapBaseBookingRelationshipToBaseBookingRelationshipForMessage(itineraryDbModel.relationships),
      breakdown = Seq.empty,
      baggage = Seq.empty,
      breakdownPerPax = Seq.empty,
      baggageAllowance = Seq.empty,
      history = mapItineraryHistoryToItineraryHistoryForMessage(itineraryDbModel.history),
      summary = Seq.empty,
      paxTickets = Seq.empty,
      itinerary = mapMultiProductItineraryToFlightItineraryForMessage(itineraryDbModel.itinerary),
      userAgent = None,
      bookingAttribution = Seq.empty,
      itineraryDate = itineraryDbModel.itinerary.recCreatedWhen.map(_.toDate).getOrElse(new Date()),
      protectionModels = None,
      multiProductInfos = mapMultiProductInfosForMessage(multiProductInfos),
      flightSegmentInfoByPaxType = Seq.empty,
      segmentInfoByPaxType = Seq.empty,
      fareRulePolicies = None,
      flightSeatSelection = Seq.empty,
      vehicle = None,
      activities = None,
      properties = None,
      cegFastTracks = None,
      addOns = None,
      multiProductBookingGroups = mapMultiProductBookingGroupsToMessage(multiProductBookingGroups),
      flightBrandSelections = None,
      flightBrandAttributes = None,
      flightBrandAttributeParams = None,
      flightBaseBooking = None,
      flightBaseCancellationInfo = None,
      crossProductIsolatedFeature = None
    )

  def mapFlightModelToBookingStateMessage(
      flightModel: FlightModelInternal,
      multiProductInfos: Seq[MultiProductInfoDBModel],
      protectionModel: Seq[ProtectionModelInternal] = Seq.empty,
      multiProductBookingGroups: Seq[MultiProductBookingGroupDBModel] = Seq.empty
  ): BookingStateMessage =
    BookingStateMessage(
      actionType = flightModel.actionType.id,
      actionId = flightModel.actionId,
      bookingType = flightModel.bookingType,
      bookingId = flightModel.bookingId,
      schemaVersion = schemaVersion, // Hardcoded, move to config?
      flights = mapFlightBookingToFlightBookingForMessage(flightModel.flights),
      slices = mapFlightSliceToFlightSliceForMessage(flightModel.slices),
      segments = mapFlightSegmentToFlightSegmentForMessage(flightModel.segments),
      passengers = mapFlightPaxToFlightPaxForMessage(flightModel.passengers),
      payments = mapPaymentToPaymentForMessage(flightModel.payments),
      bookingPayments = mapBookingPaymentToBookingPaymentForMessage(flightModel.bookingPayments),
      bookingRelationships = Seq.empty,
      breakdown = mapBreakdownToBreakdownForMessage(flightModel.breakdown),
      baggage = mapFlightBaggageForMessage(flightModel.baggage),
      breakdownPerPax = mapBreakdownToBreakdownPerPaxForMessage(flightModel.breakdownPerPax),
      baggageAllowance = mapFlightBaggageAllowanceToFlightBaggageAllowanceForMessage(flightModel.baggageAllowance),
      history = mapItineraryHistoryToItineraryHistoryForMessage(flightModel.history),
      summary = mapFlightSummaryToFlightSummaryForMessage(flightModel.summary),
      paxTickets = mapFlightPaxTicketStateToFlightPaxTicketStateForMessage(flightModel.paxTickets),
      itinerary = mapMultiProductItineraryToFlightItineraryForMessage(flightModel.itinerary),
      userAgent = flightModel.userAgent.map(mapUserAgentStateToUserAgentStateForMessage),
      bookingAttribution = flightModel.bookingAttribution.map(mapBookingAttributionToBookingAttributionForMessage),
      itineraryDate = flightModel.itineraryDate.toDate,
      protectionModels = Option(mapProtectionBookingToProtectionBookingForMessage(protectionModel)).filter(_.nonEmpty),
      multiProductInfos = mapMultiProductInfosForMessage(multiProductInfos),
      flightSegmentInfoByPaxType =
        mapFlightSegmentInfoByPaxTypeToFlightSegmentInfoByPaxTypeForMessage(flightModel.segmentInfoByPaxType),
      segmentInfoByPaxType =
        mapFlightSegmentInfoByPaxTypeToFlightSegmentInfoByPaxTypeForMessage(flightModel.segmentInfoByPaxType),
      fareRulePolicies = flightModel.fareRulePolicies.map(mapFareRulePolicies),
      flightSeatSelection = mapFlightSeatSelectionToFlightSeatSelectionTypeForMessage(flightModel.seatSelections),
      vehicle = None,
      activities = None,
      properties = None,
      cegFastTracks = None,
      addOns = None,
      multiProductBookingGroups = mapMultiProductBookingGroupsToMessage(multiProductBookingGroups),
      flightBrandSelections = Option(mapFlightBrandSelectionForMessage(flightModel.brandSelections)).filter(_.nonEmpty),
      flightBrandAttributes = Option(mapFlightBrandAttributeForMessage(flightModel.brandAttributes)).filter(_.nonEmpty),
      flightBrandAttributeParams =
        Option(mapFlightBrandAttributeParamForMessage(flightModel.brandAttributeParams)).filter(_.nonEmpty),
      flightBaseBooking = flightModel.baseBooking.map(mapBaseBookingModelInternalMessage),
      flightBaseCancellationInfo = flightModel.baseCancellationInfo.map(mapBaseCancellationInfoModelInternalMessage),
      postBookingFee = Some(flightModel.postBookingFee.map(mapPostBookingFee)),
      crossProductIsolatedFeature = None,
      essInfos = Option(flightModel.essInfos.map(mapBaseBookingEssInfo)).filter(_.nonEmpty)
    )

  def mapPostBookingFee(fee: PostBookingFee): PostBookingFeeMessage = {
    PostBookingFeeMessage(
      feeType = fee.feeType.id,
      currencyCode = fee.currencyCode,
      amount = fee.amount,
      exchangeRate = fee.exchangeRate,
      usdAmount = fee.usdAmount
    )
  }

  def mapBaseBookingEssInfo(info: BaseBookingEssInfoInternal): BaseBookingEssInfoMessage = {
    info
      .into[BaseBookingEssInfoMessage]
      .withFieldComputed(_.recCreatedWhen, from => toJodaLocalDateTime(from.recCreatedWhen.toString).toDate)
      .withFieldComputed(
        _.recModifiedWhen,
        from => from.recModifiedWhen.map(date => toJodaLocalDateTime(date.toString).toDate)
      )
      .transform
  }

  def mapBaseBookingModelInternalMessage(
      baseBookingModelInternal: BaseBookingModelInternal
  ): BaseBookingMessage =
    BaseBookingMessage(
      bookingId = baseBookingModelInternal.bookingId,
      itineraryId = baseBookingModelInternal.itineraryId,
      multiProductId = baseBookingModelInternal.multiProductId,
      productId = baseBookingModelInternal.productId,
      bookingDate = toJodaLocalDateTime(baseBookingModelInternal.bookingDate.toString).toDate,
      bookingStartDate = toJodaLocalDateTime(baseBookingModelInternal.bookingStartDate.toString).toDate,
      bookingEndDate =
        baseBookingModelInternal.bookingEndDate.map(endDate => toJodaLocalDateTime(endDate.toString).toDate),
      bookingConfirmationDate = baseBookingModelInternal.bookingConfirmationDate.map(confirmationDate =>
        toJodaLocalDateTime(confirmationDate.toString).toDate
      ),
      isTestBooking = baseBookingModelInternal.isTestBooking,
      paymentModel = baseBookingModelInternal.paymentModel,
      bookingStateId = baseBookingModelInternal.bookingStateId,
      postBookingStateId = baseBookingModelInternal.postBookingStateId,
      rejectReasonCode = baseBookingModelInternal.rejectReasonCode,
      rejectReasonMsg = baseBookingModelInternal.rejectReasonMsg,
      recStatus = baseBookingModelInternal.recStatus,
      recCreatedWhen = toJodaLocalDateTime(baseBookingModelInternal.recCreatedWhen.toString).toDate,
      recCreatedBy = baseBookingModelInternal.recCreatedBy,
      recModifiedWhen =
        baseBookingModelInternal.recModifiedWhen.map(modifiedWhen => toJodaLocalDateTime(modifiedWhen.toString).toDate),
      recModifiedBy = baseBookingModelInternal.recModifiedBy,
      productTypeId = baseBookingModelInternal.productTypeId
    )

  def mapBaseCancellationInfoModelInternalMessage(
      baseCancellationInfoModelInternal: BaseCancellationInfoModelInternal
  ): BaseCancellationInfoMessage =
    BaseCancellationInfoMessage(
      bookingId = baseCancellationInfoModelInternal.bookingId,
      isCancelled = baseCancellationInfoModelInternal.isCancelled,
      cancellationPolicyCode = baseCancellationInfoModelInternal.cancellationPolicyCode,
      cancellationDate =
        baseCancellationInfoModelInternal.cancellationDate.map(cancelDate => toJodaLocalDateTime(cancelDate).toDate),
      voidWindowUntil =
        baseCancellationInfoModelInternal.voidWindowUntil.map(voidUntil => toJodaLocalDateTime(voidUntil).toDate),
      recCreatedWhen =
        baseCancellationInfoModelInternal.recCreatedWhen.map(createdWhen => toJodaLocalDateTime(createdWhen).toDate),
      recCreatedBy = baseCancellationInfoModelInternal.recCreatedBy,
      recModifiedWhen =
        baseCancellationInfoModelInternal.recModifiedWhen.map(modifiedWhen => toJodaLocalDateTime(modifiedWhen).toDate),
      recModifiedBy = baseCancellationInfoModelInternal.recModifiedBy,
      rejectReason = baseCancellationInfoModelInternal.rejectReason,
      cancellationReason = baseCancellationInfoModelInternal.cancellationReason,
      supplierCancellationInfo = baseCancellationInfoModelInternal.supplierCancellationInfo,
      cancelledOnSupplier = baseCancellationInfoModelInternal.cancelledOnSupplier,
      isForcedRefund = baseCancellationInfoModelInternal.isForcedRefund,
      forcedRefundReason = baseCancellationInfoModelInternal.forcedRefundReason
    )

  def mapFlightBrandSelectionForMessage(
      brandSelections: Seq[FlightBrandSelection]
  ): Seq[FlightBrandSelectionForMessage] = {
    brandSelections.map { brandSelection =>
      FlightBrandSelectionForMessage(
        flightBrandSelectionId = brandSelection.flightBrandSelectionId,
        flightSliceId = brandSelection.flightSliceId,
        brandName = brandSelection.brandName,
        isUpsell = brandSelection.isUpsell,
        upsellMargin = brandSelection.upsellMargin,
        upsellPrice = brandSelection.upsellPrice,
        upsellCurrency = brandSelection.upsellCurrency,
        recStatus = brandSelection.recStatus,
        recCreatedWhen = brandSelection.recCreatedWhen.map(_.toDate),
        recModifiedWhen = brandSelection.recModifiedWhen.map(_.toDate),
        scope = brandSelection.scope.id
      )
    }

  }

  def mapFlightBrandAttributeForMessage(
      brandAttributes: Seq[FlightBrandAttribute]
  ): Seq[FlightBrandAttributeForMessage] = brandAttributes.map { brandAttribute =>
    FlightBrandAttributeForMessage(
      flightBrandAttributeId = brandAttribute.flightBrandAttributeId,
      flightBrandSelectionId = brandAttribute.flightBrandSelectionId,
      brandAttributeType = brandAttribute.brandAttributeType,
      brandAttributeInclusion = brandAttribute.brandAttributeInclusion.id,
      brandAttributeDetailType = brandAttribute.brandAttributeDetailType,
      brandAttributeDetailInclusion = brandAttribute.brandAttributeDetailInclusion.map(_.id),
      recStatus = brandAttribute.recStatus,
      recCreatedWhen = brandAttribute.recCreatedWhen.map(_.toDate),
      recModifiedWhen = brandAttribute.recModifiedWhen.map(_.toDate)
    )
  }

  def mapFlightBrandAttributeParamForMessage(
      brandAttributeParams: Seq[FlightBrandAttributeParam]
  ): Seq[FlightBrandAttributeParamMessage] = brandAttributeParams.map { brandAttributeParam =>
    FlightBrandAttributeParamMessage(
      flightBrandAttributeParamId = brandAttributeParam.flightBrandAttributeParamId,
      flightBrandAttributeId = brandAttributeParam.flightBrandAttributeId,
      name = brandAttributeParam.name,
      code = brandAttributeParam.code,
      recStatus = brandAttributeParam.recStatus,
      recCreatedWhen = brandAttributeParam.recCreatedWhen.map(_.toDate),
      recModifiedWhen = brandAttributeParam.recModifiedWhen.map(_.toDate)
    )
  }

  def mapFareRulePolicies(
      fareRulePolicies: Seq[FareRulePolicyModelInternal]
  ): Seq[FareRulePolicyForMessage] =
    fareRulePolicies
      .map(p =>
        FareRulePolicyForMessage(
          policyId = p.policyId,
          paxType = p.paxType,
          flightBookingId = p.flightBookingId,
          fareRulePolicyType = p.fareRulePolicyType,
          allowed = p.allowed,
          penalty = p.penalty,
          currencyCode = p.currencyCode,
          validUntilHoursBeforeBoarding = p.validUntilHoursBeforeBoarding,
          validFromHoursBeforeBoarding = p.validFromHoursBeforeBoarding,
          recCreatedWhen = p.recCreatedWhen.map(_.toDate),
          recModifyWhen = p.recModifyWhen.map(_.toDate),
          sliceId = p.sliceId,
          segmentId = p.segmentId,
          source = p.source,
          cmsMappingValue = p.cmsMappingValue
        )
      )

  def mapFlightBookingToExpTeamFlightBookingForMessage(
      bookings: Seq[FlightBookingState]
  ): Seq[FlightBookingExperimentForMessage] =
    bookings.map { booking =>
      FlightBookingExperimentForMessage(
        trackingCookieId = booking.trackingCookieId,
        recCreatedWhen = booking.recCreatedWhen.map(_.toDate)
      )
    }

  def mapFlightBookingToFlightBookingForMessage(bookings: Seq[FlightBookingState]): Seq[FlightBookingForMessage] =
    bookings.map { booking =>
      FlightBookingForMessage(
        flightBookingId = booking.flightBookingId,
        itineraryId = booking.itineraryId,
        multiProductId = booking.multiProductId,
        ticketingAirline = booking.ticketingAirline,
        pnr = booking.pnr,
        gdsPnr = booking.gdsPnr,
        version = booking.version,
        flightStateId = booking.flightStateId,
        isCancelled = booking.isCancelled,
        cancellationDate = booking.cancellationDate.map(_.toDate),
        voidWindowUntil = booking.voidWindowUntil.map(_.toDate),
        paymentModel = booking.paymentModel,
        supplierId = booking.supplierId,
        subSupplierId = booking.subSupplierId,
        pointOfSale = booking.pointOfSale,
        experimentVariant = booking.experimentVariant,
        supplierSpecificData = booking.supplierSpecificData,
        accountingEntity = booking.accountingEntity.map(mapAccountingEntityToAccountingEntityForMessage),
        supplierBookingId = booking.supplierBookingId,
        supplierStatusCode = booking.supplierStatusCode,
        supplierReasonCode = booking.supplierReasonCode,
        supplierCommissionAmount = booking.supplierCommissionAmount,
        supplierCommissionPercentage = booking.supplierCommissionPercentage,
        fraudScore = booking.fraudScore,
        fraudAction = booking.fraudAction,
        fraudCheckIp = booking.fraudCheckIp,
        storefrontId = booking.storefrontId,
        platformId = booking.platformId,
        languageId = booking.languageId,
        displayCurrency = booking.displayCurrency,
        serverName = booking.serverName,
        cid = booking.cid,
        sessionId = booking.sessionId,
        clientIpAddress = booking.clientIpAddress,
        trackingCookieId = booking.trackingCookieId,
        trackingCookieDate = booking.trackingCookieDate.map(_.toDate),
        trackingTag = booking.trackingTag,
        searchId = booking.searchId,
        searchRequestId = booking.searchRequestId,
        flapiItineraryId = booking.flapiItineraryId,
        recCreatedWhen = booking.recCreatedWhen.map(_.toDate),
        whitelabelId = booking.whitelabelId,
        recStatus = booking.recStatus,
        recModifiedWhen = booking.recModifiedWhen.map(_.toDate),
        rejectReasonCode = booking.rejectReasonCode,
        tripStartDate = booking.tripStartDate.map(_.toDate),
        tripEndDate = booking.tripEndDate.map(_.toDate),
        commonBookingInfo = booking.commonBookingInfo.map(mapCommonBookingInfoToCommonBookingInfoForMessage),
        commonBookingEventsInfo =
          booking.commonBookingEventsInfo.map(mapCommonBookingEventsInfoToCommonBookingEventsInfoForMessage),
        virtualInterlining = booking.virtualInterlining,
        freeBagScope = booking.freeBagScope.map(_.id),
        fareRuleScope = booking.fareRuleScope.map(_.id),
        promotionCampaignId = booking.promotionCampaignId,
        supplierPaymentMethod = booking.supplierPaymentMethod.map(_.id),
        supplierReservationExpireDateTime = booking.supplierReservationExpireDateTime.map(_.toDate),
        supplierReservationExpireInMinutes = booking.supplierReservationExpireInMinutes,
        originalAttemptBookingId = booking.originalAttemptBookingId,
        flightPostbookingStateId = booking.flightPostbookingStateId.map(_.toInt),
        facilitationFeeWaiverReasonId = booking.facilitationFeeWaiverReasonId
      )
    }

  def mapBookingActionToBookingActionForMessage(action: BookingWorkflowAction): BookingActionForMessage =
    BookingActionForMessage(
      actionId = action.actionId,
      itineraryId = action.itineraryId,
      bookingType = action.bookingType,
      bookingId = action.bookingId.getOrElse(0),
      memberId = action.memberId,
      actionTypeId = action.actionTypeId,
      correlationId = action.correlationId,
      requestId = action.requestId,
      workflowId = action.workflowId,
      workflowStateId = action.workflowStateId,
      stateSchemaVersion = action.stateSchemaVersion,
      state = action.state,
      storefrontId = action.storefrontId,
      languageId = action.languageId
    )

  def mapAccountingEntityToAccountingEntityForMessage(accountingEntity: AccountingEntity): AccountingEntityForMessage =
    AccountingEntityForMessage(
      merchantOfRecord = accountingEntity.merchantOfRecord,
      rateContract = accountingEntity.rateContract,
      revenue = accountingEntity.revenue,
      argument = accountingEntity.argument
    )

  def mapCommonBookingInfoToCommonBookingInfoForMessage(
      commonBookingInfo: CommonBookingInfo
  ): CommonBookingInfoForMessage =
    CommonBookingInfoForMessage(
      origin = commonBookingInfo.origin,
      isTestBooking = commonBookingInfo.isTestBooking
    )

  def mapCommonBookingEventsInfoToCommonBookingEventsInfoForMessage(
      commonBookingEventsInfo: CommonBookingEventsInfo
  ): CommonBookingEventsInfoForMessage =
    CommonBookingEventsInfoForMessage(
      confirmationDate = commonBookingEventsInfo.confirmationDate.map(_.toDate),
      cancellationDate = commonBookingEventsInfo.cancellationDate.map(_.toDate)
    )

  def mapFlightSliceToFlightSliceForMessage(slices: Seq[FlightSlice]): Seq[FlightSliceForMessage] =
    slices.map { slice =>
      FlightSliceForMessage(
        referenceId = slice.referenceId,
        flightSliceId = slice.flightSliceId,
        flightBookingId = slice.flightBookingId,
        origin = slice.origin,
        destination = slice.destination,
        departure = slice.departure.toDate,
        arrival = slice.arrival.toDate,
        duration = slice.duration,
        recStatus = slice.recStatus,
        recCreatedWhen = slice.recCreatedWhen.map(_.toDate),
        recModifiedWhen = slice.recModifiedWhen.map(_.toDate)
      )
    }

  def mapFlightSegmentToFlightSegmentForMessage(segments: Seq[FlightSegment]): Seq[FlightSegmentForMessage] =
    segments.map { segment =>
      FlightSegmentForMessage(
        referenceId = segment.referenceId,
        sliceReferenceId = segment.sliceReferenceId,
        flightSegmentId = segment.flightSegmentId,
        flightSliceId = segment.flightSliceId,
        marketingAirline = segment.marketingAirline,
        operatingAirline = segment.operatingAirline,
        flightNo = segment.flightNo,
        origin = segment.origin,
        departure = segment.departure.toDate,
        destination = segment.destination,
        arrival = segment.arrival.toDate,
        airEquipmentCode = segment.airEquipmentCode,
        bookingClass = segment.bookingClass,
        cabinClass = segment.cabinClass,
        cabinName = segment.cabinName,
        fareBasisCode = segment.fareBasisCode,
        fareRules = segment.fareRules,
        duration = segment.duration,
        carrierPnr = segment.carrierPnr,
        recStatus = segment.recStatus,
        recCreatedWhen = segment.recCreatedWhen.map(_.toDate),
        recModifiedWhen = segment.recModifiedWhen.map(_.toDate),
        bagsRecheckRequired = segment.bagsRecheckRequired,
        departureTerminal = segment.departureTerminal,
        arrivalTerminal = segment.arrivalTerminal
      )
    }

  def mapFlightSegmentInfoByPaxTypeToFlightSegmentInfoByPaxTypeForMessage(
      segmentInfoByPaxType: Seq[SegmentInfoByPaxType]
  ): Seq[FlightSegmentInfoByPaxTypeForMessage] = {
    segmentInfoByPaxType.map { segmentInfo =>
      FlightSegmentInfoByPaxTypeForMessage(
        referenceId = segmentInfo.referenceId,
        segmentReferenceId = segmentInfo.segmentReferenceId,
        segmentInfoByPaxTypeId = segmentInfo.segmentInfoByPaxTypeId,
        flightSegmentId = segmentInfo.flightSegmentId,
        passengerType = segmentInfo.passengerType,
        bookingClass = segmentInfo.bookingClass,
        fareBasisCode = segmentInfo.fareBasisCode,
        recStatus = segmentInfo.recStatus,
        recCreatedWhen = segmentInfo.recCreatedWhen.map(_.toDate),
        recModifiedWhen = segmentInfo.recModifiedWhen.map(_.toDate),
        fareRuleRevisionId = segmentInfo.fareRuleRevisionId.getOrElse("")
      )
    }
  }

  def mapFlightBaggageAllowanceToFlightBaggageAllowanceForMessage(
      baggages: Seq[FlightBaggageAllowance]
  ): Seq[FlightBaggageAllowanceForMessage] =
    baggages.map { baggage =>
      FlightBaggageAllowanceForMessage(
        referenceId = baggage.referenceId,
        sliceReferenceId = baggage.sliceReferenceId,
        segmentReferenceId = baggage.segmentReferenceId,
        flightBaggageId = baggage.flightBaggageId,
        flightSliceId = baggage.flightSliceId,
        flightSegmentId = baggage.flightSegmentId,
        `type` = baggage.`type`,
        count = baggage.count,
        maxWeightKg = baggage.maxWeightKg,
        maxWeightLbs = baggage.maxWeightLbs,
        totalSizeCm = baggage.totalSizeCm,
        totalSizeIn = baggage.totalSizeIn,
        lengthCm = baggage.lengthCm,
        lengthIn = baggage.lengthIn,
        widthCm = baggage.widthCm,
        widthIn = baggage.widthIn,
        heightCm = baggage.heightCm,
        heightIn = baggage.heightIn,
        priceAmt = baggage.priceAmt,
        priceCurrency = baggage.priceCurrency,
        recStatus = baggage.recStatus,
        recCreatedWhen = baggage.recCreatedWhen.map(_.toDate),
        recModifiedWhen = baggage.recModifiedWhen.map(_.toDate),
        paxType = baggage.paxType,
        source = baggage.source,
        cmsMappingValue = baggage.cmsMappingValue
      )
    }

  def mapFlightBaggageForMessage(
      baggage: Seq[FlightBaggage]
  ): Seq[FlightBaggageForMessage] =
    baggage.map { baggage =>
      FlightBaggageForMessage(
        referenceId = baggage.referenceId,
        sliceReferenceId = baggage.sliceReferenceId,
        flightBaggageId = baggage.flightBaggageId,
        flightPaxId = baggage.flightPaxId,
        flightSliceId = baggage.flightSliceId,
        baggageTypeId = baggage.baggageTypeId,
        quantity = baggage.quantity,
        maxWeight = baggage.maxWeight,
        maxWeightUnit = baggage.maxWeightUnit,
        weightLimitPerBag = baggage.weightLimitPerBag,
        weightLimitPerBagUnit = baggage.weightLimitPerBagUnit,
        sizeLength = baggage.sizeLength,
        sizeWidth = baggage.sizeWidth,
        sizeHeight = baggage.sizeHeight,
        sizeUnit = baggage.sizeUnit,
        priceAmount = baggage.priceAmount,
        priceCurrency = baggage.priceCurrency,
        supplierData = baggage.supplierData,
        baggageStatus = baggage.baggageStatus,
        isCarryOn = baggage.isCarryOn,
        recStatus = baggage.recStatus,
        recCreatedWhen = baggage.recCreatedWhen.map(_.toDate),
        recModifiedWhen = baggage.recModifiedWhen.map(_.toDate),
        scope = baggage.scope.id,
        isPartialSettlementRequired = baggage.isPartialSettlementRequired
      )
    }

  def mapFlightPaxToFlightPaxForMessage(pax: Seq[FlightPaxNoPii]): Seq[FlightPaxForMessage] =
    pax.map { pax =>
      FlightPaxForMessage(
        referenceId = pax.referenceId,
        flightPaxId = pax.flightPaxId,
        flightBookingId = pax.flightBookingId,
        passengerTypeCode = pax.passengerTypeCode,
        piiHash = pax.piiHash,
        supplierFlightPaxId = pax.supplierFlightPaxId,
        recStatus = pax.recStatus,
        recCreatedWhen = pax.recCreatedWhen.map(_.toDate),
        recModifiedWhen = pax.recModifiedWhen.map(_.toDate)
      )
    }

  def mapBreakdownToBreakdownForMessage(breakdowns: Seq[Breakdown]): Seq[BreakdownForMessage] =
    breakdowns.map { breakdown =>
      BreakdownForMessage(
        referenceId = breakdown.referenceId,
        breakdownId = breakdown.breakdownId,
        itineraryId = breakdown.itineraryId,
        bookingType = breakdown.bookingType,
        bookingId = breakdown.bookingId,
        actionId = breakdown.actionId,
        eventDate = breakdown.eventDate.toDate,
        itemId = breakdown.itemId,
        typeId = breakdown.typeId,
        taxFeeId = breakdown.taxFeeId,
        quantity = breakdown.quantity,
        localCurrency = breakdown.localCurrency,
        localAmount = breakdown.localAmount,
        exchangeRate = breakdown.exchangeRate,
        usdAmount = breakdown.usdAmount,
        requestedAmount = breakdown.requestedAmount,
        refBreakdownId = breakdown.refBreakdownId,
        recStatus = breakdown.recStatus,
        recCreatedWhen = breakdown.recCreatedWhen.map(_.toDate),
        vendorExchangeRate = breakdown.vendorExchangeRate,
        upcId = breakdown.upcId
      )
    }

  def mapBreakdownToBreakdownPerPaxForMessage(breakdowns: Seq[BreakdownPerPax]): Seq[BreakdownPerPaxForMessage] =
    breakdowns.map { breakdown =>
      BreakdownPerPaxForMessage(
        referenceId = breakdown.referenceId,
        breakdownId = breakdown.breakdownId,
        itineraryId = breakdown.itineraryId,
        bookingType = breakdown.bookingType,
        bookingId = breakdown.bookingId,
        actionId = breakdown.actionId,
        eventDate = breakdown.eventDate.toDate,
        itemId = breakdown.itemId,
        typeId = breakdown.typeId,
        taxFeeId = breakdown.taxFeeId,
        quantity = breakdown.quantity,
        localCurrency = breakdown.localCurrency,
        localAmount = breakdown.localAmount,
        exchangeRate = breakdown.exchangeRate,
        usdAmount = breakdown.usdAmount,
        requestedAmount = breakdown.requestedAmount,
        refBreakdownId = breakdown.refBreakdownId,
        recStatus = breakdown.recStatus,
        recCreatedWhen = breakdown.recCreatedWhen.map(_.toDate),
        vendorExchangeRate = breakdown.vendorExchangeRate,
        requestedCurrency = breakdown.requestedCurrency,
        applyType = breakdown.applyType,
        paxId = breakdown.paxId,
        typeRefId = breakdown.typeRefId
      )
    }

  def mapPaymentToPaymentForMessage(payments: Seq[PaymentState]): Seq[PaymentForMessage] =
    payments.map { payment =>
      PaymentForMessage(
        referenceId = payment.referenceId,
        paymentId = payment.paymentId,
        itineraryId = payment.itineraryId,
        actionId = payment.actionId,
        creditCardId = payment.creditCardId,
        transactionDate = payment.transactionDate.toDate,
        transactionType = payment.transactionType,
        paymentState = payment.paymentState,
        referenceNo = payment.referenceNo,
        referenceType = payment.referenceType,
        last4Digits = payment.last4Digits,
        paymentMethodId = payment.paymentMethodId,
        gatewayId = payment.gatewayId,
        transactionId = payment.transactionId,
        paymentCurrency = payment.paymentCurrency,
        paymentAmount = payment.paymentAmount,
        amountUsd = payment.amountUsd,
        supplierCurrency = payment.supplierCurrency,
        supplierAmount = payment.supplierAmount,
        exchangeRateSupplierToPayment = payment.exchangeRateSupplierToPayment,
        creditCardCurrency = payment.creditCardCurrency,
        upliftAmount = payment.upliftAmount,
        siteExchangeRate = payment.siteExchangeRate,
        upliftExchangeRate = payment.upliftExchangeRate,
        remark = payment.remark,
        paymentTypeId = payment.paymentTypeId,
        token = payment.token,
        recStatus = payment.recStatus,
        recCreatedWhen = payment.recCreatedWhen.map(_.toDate),
        referencePaymentId = payment.referencePaymentId,
        points = payment.points
      )
    }

  def mapBookingPaymentToBookingPaymentForMessage(
      bookingPayments: Seq[BookingPaymentState]
  ): Seq[BookingPaymentForMessage] =
    bookingPayments.map { bookingPayment =>
      BookingPaymentForMessage(
        paymentId = bookingPayment.paymentId,
        bookingId = bookingPayment.bookingId,
        paymentCurrency = bookingPayment.paymentCurrency,
        paymentAmount = bookingPayment.paymentAmount.toDouble,
        amountUsd = bookingPayment.amountUsd.toDouble,
        recStatus = bookingPayment.recStatus,
        recCreatedWhen = bookingPayment.recCreatedWhen.map(_.toDate),
        fxiUplift = bookingPayment.fxiUplift.map(_.toDouble),
        loyaltyPoints = bookingPayment.loyaltyPoints,
        supplierCurrency = bookingPayment.supplierCurrency,
        supplierExchangeRate = bookingPayment.supplierExchangeRate.map(_.toDouble),
        bookingPaymentId = bookingPayment.bookingPaymentId
      )
    }

  def mapBaseBookingRelationshipToBaseBookingRelationshipForMessage(
      bookingRelationships: Seq[BaseBookingRelationshipInternal]
  ): Seq[BaseBookingRelationshipForMessage] =
    bookingRelationships.map { bookingRelationship =>
      BaseBookingRelationshipForMessage(
        sourceBookingId = bookingRelationship.sourceBookingId,
        targetBookingId = bookingRelationship.targetBookingId,
        relationshipStatusId = bookingRelationship.relationshipStatusId,
        relationshipTypeId = bookingRelationship.relationshipTypeId,
        recStatus = bookingRelationship.recStatus,
        recCreatedWhen = bookingRelationship.recCreatedWhen.toDate,
        recModifiedWhen = bookingRelationship.recModifiedWhen.map(_.toDate),
        recCreatedBy = bookingRelationship.recCreatedBy,
        recModifiedBy = bookingRelationship.recModifiedBy,
        relationshipId = bookingRelationship.relationshipId
      )
    }

  def mapItineraryHistoryToItineraryHistoryForMessage(history: Seq[ItineraryHistory]): Seq[ItineraryHistoryForMessage] =
    history.map { h =>
      ItineraryHistoryForMessage(
        actionId = h.actionId,
        itineraryId = h.itineraryId.toInt,
        bookingType = h.bookingType,
        bookingId = h.bookingId,
        actionType = h.actionType,
        version = h.version,
        actionDate = h.actionDate.toDate,
        parameters = h.parameters,
        description = h.description,
        recStatus = h.recStatus,
        recCreatedWhen = h.recCreatedWhen.map(_.toDate)
      )
    }

  def mapFlightPaxTicketStateToFlightPaxTicketStateForMessage(
      paxTickets: Seq[FlightPaxTicketState]
  ): Seq[FlightPaxTicketStateForMessage] =
    paxTickets.map { ticket =>
      FlightPaxTicketStateForMessage(
        ticketNumberId = ticket.ticketNumberId,
        flightPaxId = ticket.flightPaxId,
        ticketNumber = ticket.ticketNumber,
        ticketType = ticket.ticketType,
        recStatus = ticket.recStatus,
        recCreatedWhen = ticket.recCreatedWhen.map(_.toDate),
        recModifiedWhen = ticket.recModifiedWhen.map(_.toDate),
        flightSegmentId = ticket.flightSegmentId
      )
    }

  def mapMultiProductItineraryToFlightItineraryForMessage(itinerary: MultiProductItinerary): FlightItineraryForMessage =
    FlightItineraryForMessage(
      itineraryId = itinerary.itineraryId,
      memberId = itinerary.memberId,
      recStatus = itinerary.recStatus,
      recCreatedWhen = itinerary.recCreatedWhen.map(_.toDate),
      recModifiedWhen = itinerary.recModifiedWhen.map(_.toDate)
    )

  def mapFlightSummaryToFlightSummaryForMessage(flightSummary: Seq[FlightSummary]): Seq[FlightSummaryForMessage] =
    flightSummary.map { summary =>
      FlightSummaryForMessage(
        bookingId = summary.bookingId,
        currency = summary.currency,
        baseFare = summary.baseFare,
        taxAndFee = summary.taxAndFee,
        baseDiscount = summary.baseDiscount,
        campaignDiscount = summary.campaignDiscount,
        totalFare = summary.totalFare,
        recStatus = summary.recStatus,
        recCreatedWhen = summary.recCreatedWhen.map(_.toDate),
        recModifiedWhen = summary.recModifiedWhen.map(_.toDate)
      )
    }

  def mapUserAgentStateToUserAgentStateForMessage(userAgent: UserAgentState): UserAgentStateForMessage =
    UserAgentStateForMessage(
      flightBookingId = userAgent.flightBookingId,
      origin = userAgent.origin,
      osName = userAgent.osName,
      osVersion = userAgent.osVersion,
      browserName = userAgent.browserName,
      browserLanguage = userAgent.browserLanguage,
      browserVersion = userAgent.browserVersion,
      browserSubVersion = userAgent.browserSubVersion,
      browserBuildNumber = userAgent.browserBuildNumber,
      deviceBrand = userAgent.deviceBrand,
      deviceModel = userAgent.deviceModel,
      deviceTypeId = userAgent.deviceTypeId,
      isMobile = userAgent.isMobile,
      isTouch = userAgent.isTouch,
      additionalInfo = userAgent.additionalInfo,
      recStatus = userAgent.recStatus,
      recCreatedWhen = userAgent.recCreatedWhen.map(_.toDate),
      recModifiedWhen = userAgent.recModifiedWhen.map(_.toDate)
    )

  def mapBookingAttributionToBookingAttributionForMessage(
      bookingAttribution: BookingAttributionState
  ): BookingAttributionStateForMessage =
    BookingAttributionStateForMessage(
      modelId = bookingAttribution.modelId,
      tag = bookingAttribution.tag,
      siteId = bookingAttribution.siteId,
      clickDateTime = bookingAttribution.clickDateTime.map(_.toDate),
      additionalData = bookingAttribution.additionalData,
      bookingId = bookingAttribution.bookingId,
      createdWhen = bookingAttribution.createdWhen.map(_.toDate)
    )

  def mapProtectionCfarForMessage(protectionCfar: ProtectionCfar): ProtectionCfarForMessage = {
    ProtectionCfarForMessage(
      protectionBookingId = protectionCfar.protectionBookingId,
      coveragePercentage = protectionCfar.coveragePercentage,
      coverageAmount = protectionCfar.coverageAmount,
      currency = protectionCfar.currency,
      claimUrl = protectionCfar.claimUrl,
      recStatus = protectionCfar.recStatus,
      recCreatedWhen = protectionCfar.recCreatedWhen.map(_.toDate),
      recModifiedWhen = protectionCfar.recModifiedWhen.map(_.toDate)
    )
  }
  def mapProtectionBookingToProtectionBookingForMessage(
      bookings: Seq[ProtectionModelInternal]
  ): Seq[ProtectionModelForMessage] =
    bookings.map(bookings =>
      ProtectionModelForMessage(
        protectionBookingId = bookings.protectionBookingId,
        itineraryId = bookings.itineraryId,
        protectionBookingStateId = bookings.protectionBookingStateId.id,
        protectionTypeId = bookings.protectionTypeId,
        priceAmount = bookings.priceAmount,
        priceAmountUSD = bookings.priceAmountUSD,
        marginAmount = bookings.marginAmount,
        marginAmountUSD = bookings.marginAmountUSD,
        marginPercentage = bookings.marginPercentage,
        currency = bookings.currency,
        supplierId = bookings.supplierId,
        subSupplierId = bookings.subSupplierId,
        supplierSearchId = bookings.supplierSearchId,
        supplierPolicyId = bookings.supplierPolicyId,
        supplierResultCode = bookings.supplierResultCode,
        supplierSpecificData = bookings.supplierSpecificData,
        accountingEntity = bookings.accountingEntity.map(mapAccountingEntityToAccountingEntityForMessage),
        multiProductId = bookings.multiProductId,
        platformId = bookings.platformId,
        languageId = bookings.languageId,
        serverName = bookings.serverName,
        cid = bookings.cid,
        sessionId = bookings.sessionId,
        clientIpAddress = bookings.clientIpAddress,
        trackingCookingId = bookings.trackingCookingId,
        trackingCookieDate = bookings.trackingCookieDate.map(_.toDate),
        trackingTag = bookings.trackingTag,
        recStatus = bookings.recStatus,
        recCreatedWhen = bookings.recCreatedWhen.map(_.toDate),
        recModifiedWhen = bookings.recModifiedWhen.map(_.toDate),
        whitelabelId = bookings.whitelabelId,
        requestId = bookings.requestId,
        correlationId = bookings.correlationId,
        version = bookings.version,
        protectionProductBooking = bookings.protectionProductBooking.map { ppm =>
          ProtectionProductBookingForMessage(
            productBookingId = ppm.productBookingId,
            protectionBookingId = ppm.protectionBookingId,
            productType = ppm.productType,
            recStatus = ppm.recStatus,
            recCreatedWhen = ppm.recCreatedWhen.map(_.toDate),
            recModifiedWhen = ppm.recModifiedWhen.map(_.toDate)
          )
        },
        tripCostAmount = bookings.tripCostAmount,
        tripCostCurrency = bookings.tripCostCurrency,
        financialBreakdowns = mapBreakdownToBreakdownForMessage(bookings.financialBreakdowns),
        bookingPayments = bookings.bookingPayments.map(mapBookingPaymentToBookingPaymentForMessage),
        paymentModel = bookings.paymentModel,
        tripStartDate = bookings.tripStartDate.map(_.toDate),
        tripEndDate = bookings.tripEndDate.map(_.toDate),
        commonBookingInfo = bookings.commonBookingInfo.map(mapCommonBookingInfoToCommonBookingInfoForMessage),
        commonBookingEventsInfo =
          bookings.commonBookingEventsInfo.map(mapCommonBookingEventsInfoToCommonBookingEventsInfoForMessage),
        protectionCfar = bookings.protectionCfar.map(mapProtectionCfarForMessage)
      )
    )

  def mapFlightSeatSelectionToFlightSeatSelectionTypeForMessage(
      seatSelections: Seq[FlightSeatSelectionDetail]
  ): Seq[FlightSeatSelectionDetailForMessage] =
    seatSelections.map(seatSelection =>
      FlightSeatSelectionDetailForMessage(
        referenceId = seatSelection.referenceId,
        segmentReferenceId = seatSelection.segmentReferenceId,
        flightSeatSelectionId = seatSelection.flightSeatSelectionId,
        flightSegmentId = seatSelection.flightSegmentId,
        flightSegmentIndex = seatSelection.flightSegmentIndex,
        flightPaxId = seatSelection.flightPaxId,
        seatRow = seatSelection.seatRow,
        seatColumn = seatSelection.seatColumn,
        flightSeatState = seatSelection.flightSeatState,
        recStatus = seatSelection.recStatus,
        recCreatedWhen = seatSelection.recCreatedWhen.map(_.toDate),
        recModifiedWhen = seatSelection.recModifiedWhen.map(_.toDate),
        priceAmount = seatSelection.priceAmount,
        priceCurrency = seatSelection.priceCurrency,
        supplierData = seatSelection.supplierData,
        isPartialSettlementRequired = seatSelection.isPartialSettlementRequired
      )
    )

  def mapMultiProductInfosForMessage(
      multiProductInfos: Seq[MultiProductInfoDBModel]
  ): Option[Seq[MultiProductInfoForMessage]] = {
    multiProductInfos.map(db => MultiProductInfoForMessage(db.multiProductId, db.multiProductType.id)) match {
      case Nil  => None
      case data => Some(data)
    }
  }

  def mapMultiProductBookingGroupsToMessage(
      multiProductBookingGroups: Seq[MultiProductBookingGroupDBModel]
  ): Option[Seq[MultiProductBookingGroupModelMessage]] = {
    multiProductBookingGroups.map(db =>
      MultiProductBookingGroupModelMessage(
        bookingId = db.bookingId,
        itineraryId = db.itineraryId,
        cartId = db.cartId,
        packageId = db.packageId
      )
    ) match {
      case Nil  => None
      case data => Some(data)
    }
  }

}
