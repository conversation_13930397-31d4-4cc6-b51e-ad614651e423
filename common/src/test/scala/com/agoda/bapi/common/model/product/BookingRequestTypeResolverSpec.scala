package com.agoda.bapi.common.model.product

import com.agoda.bapi.common.ProductTokenMockHelper
import com.agoda.bapi.common.message._
import com.agoda.bapi.common.message.creation.{ActivitiesItem, CarItem, CreateBookingRequest, CreatedBookingStatus, FlightBooking, FlightItem, HotelBooking, Itinerary, Products, PropertyItem, ProtectionItem}
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.model.WhiteLabelInfo
import com.agoda.bapi.common.model.cart.CartContext
import com.agoda.bapi.common.model.creation.mocks.CreateBookingRequestMock
import com.agoda.bapi.common.model.product.BookingRequestTypeResolver.MultiProductFlowDefinition
import com.agoda.bapi.common.model.protection.ProtectionRequestItemOptInValue
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.service.FeatureAware
import org.joda.time.LocalDate
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfterEach
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.must.Matchers
import org.scalatestplus.mockito.MockitoSugar

class BookingRequestTypeResolverSpec
    extends AnyFunSpec
    with Matchers
    with MockitoSugar
    with ProductTokenMockHelper
    with BeforeAndAfterEach {
  val mockFeatureAware: FeatureAware     = mock[FeatureAware]
  val mockWhitelabelInfo: WhiteLabelInfo = mock[WhiteLabelInfo]

  override def beforeEach(): Unit = {
    reset(mockFeatureAware)
    reset(mockWhitelabelInfo)
  }

  describe("getProductType for setup booking request") {
    it("should work properly with package request") {
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          packageRequest = Some(PackageRequest("client", Some("package")))
        )
      )

      val flowType = BookingRequestTypeResolver.getFlowType(request, Some(mockFeatureAware), mockWhitelabelInfo)
      flowType mustBe BookingFlow.Package
    }

    it("should work properly with single property request") {
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          propertyRequests = Seq(makePropertyRequestRequestItem(""))
        )
      )

      val flowType = BookingRequestTypeResolver.getFlowType(request, Some(mockFeatureAware), mockWhitelabelInfo)
      flowType mustBe BookingFlow.SingleProperty
    }

    it("should work properly with multi product request") {
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          flightRequests = Seq.empty,
          propertyRequests = Seq(makePropertyRequestRequestItem(""), makePropertyRequestRequestItem(""))
        )
      )

      val flowType = BookingRequestTypeResolver.getFlowType(request, Some(mockFeatureAware), mockWhitelabelInfo)
      flowType mustBe BookingFlow.MixAndSave
    }

    it("should work properly with single car request") {
      // Arrange
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          flightRequests = Seq.empty,
          propertyRequests = Seq(makePropertyRequestRequestItem(""), makePropertyRequestRequestItem("")),
          carRequestsOpt = Option(
            Seq(
              CarRequestItem(
                id = "1",
                confirmPriceRequest = CarConfirmPriceRequest(
                  identifier = None,
                  searchToken =
                    "AAAA7/AdeyJ2IjoxLCJ0T2JqIjp7ImlkIjoiQ0FSLTkwYWwiLCJkYSI6MzUsInBpY2sgACBjbyIAoDEzNDg3OSwiY3QOAMA5OTk5LCJsb2MiOiIMABAiDQD2GHQiOjIsImR0IjoiMjAyMC0wMy0yM1QxODoyNDowMFoifSwiZHJvcFQARDc3NzdSAFQxOTI4MlMAAQ0AD1QABho1VAARYVIA8ApwIjozODkwLjg4LCJjdXIiOiJUSEIifX19"
                )
              )
            )
          )
        )
      )

      // Act
      val flowType = BookingRequestTypeResolver.getFlowType(request, Some(mockFeatureAware), mockWhitelabelInfo)

      // Assert
      flowType mustBe BookingFlow.SingleVehicle
    }

    it("should work properly with trip protection request") {
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None)),
          tripProtectionRequests = Some(Seq(TripProtectionRequestItem("", ProductTypeEnum.Flight, 1)))
        )
      )

      val flowType = BookingRequestTypeResolver.getFlowType(request, Some(mockFeatureAware), mockWhitelabelInfo)
      flowType mustBe BookingFlow.FlightWithProtection
    }

    it("should mark as cart for flight with trip protection request when UNIBF-946 is enabled") {
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None)),
          tripProtectionRequests = Some(Seq(TripProtectionRequestItem("", ProductTypeEnum.Flight, 1)))
        )
      )
      val featureAware = mock[FeatureAware]
      when(featureAware.migrateFlightToCartFlow(mockWhitelabelInfo)).thenReturn(true)

      val flowType = BookingRequestTypeResolver.getFlowType(request, Some(featureAware), mockWhitelabelInfo)
      flowType mustBe BookingFlow.Cart
    }

    it("should mark as flight with trip protection for MSE Requests and when UNIBF-946 is enabled") {
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          flightRequests = Seq(FlightRequestItem(Some(""), None, Some(InstantPriceConfirmRequest()))),
          tripProtectionRequests = Some(Seq(TripProtectionRequestItem("", ProductTypeEnum.Flight, 1)))
        )
      )
      val featureAware = mock[FeatureAware]
      when(featureAware.migrateFlightToCartFlow(mockWhitelabelInfo)).thenReturn(true)

      val flowType = BookingRequestTypeResolver.getFlowType(request, Some(featureAware), mockWhitelabelInfo)
      flowType mustBe BookingFlow.FlightWithProtection
    }

    it("should mark as cart for singleFlight request when UNIBF-946 is enabled") {
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None))
        )
      )
      val featureAware = mock[FeatureAware]
      when(featureAware.migrateFlightToCartFlow(mockWhitelabelInfo)).thenReturn(true)

      val flowType = BookingRequestTypeResolver.getFlowType(request, Some(featureAware), mockWhitelabelInfo)
      flowType mustBe BookingFlow.Cart
    }

    it("should mask as cart when trip protection request is empty seq and UNIBF-946 is enabled") {
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None)),
          tripProtectionRequests = Some(Seq.empty)
        )
      )
      val featureAware = mock[FeatureAware]
      when(featureAware.migrateFlightToCartFlow(mockWhitelabelInfo)).thenReturn(true)

      val flowType = BookingRequestTypeResolver.getFlowType(request, Some(featureAware), mockWhitelabelInfo)
      flowType mustBe BookingFlow.Cart
    }

    it("should mask as FlightWithProtection when trip protection request is empty seq") {
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None)),
          tripProtectionRequests = Some(Seq.empty)
        )
      )

      val flowType = BookingRequestTypeResolver.getFlowType(request, Some(mockFeatureAware), mockWhitelabelInfo)
      flowType mustBe BookingFlow.FlightWithProtection
    }

    it("should not return trip protection for packages fare") {
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          packageRequest = Some(PackageRequest("client", Some("package"))),
          tripProtectionRequests = Some(Seq(TripProtectionRequestItem("", ProductTypeEnum.Flight, 1)))
        )
      )

      val flowType = BookingRequestTypeResolver.getFlowType(request, Some(mockFeatureAware), mockWhitelabelInfo)
      flowType mustBe BookingFlow.Package
    }

    it("should work properly with Multi Hotel request") {
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          packageRequest = Some(PackageRequest("client", Some("package"))),
          propertyRequests = Seq(makePropertyRequestRequestItem(""), makePropertyRequestRequestItem(""))
        )
      )

      val flowType = BookingRequestTypeResolver.getFlowType(request, Some(mockFeatureAware), mockWhitelabelInfo)
      flowType mustBe BookingFlow.MultiHotel
    }

    it("should work properly with Cart Request when cartPricingContext is exist") {
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          cartPricingContext = Some(CartPricingContext(token = None, previousTotalPrice = None)),
          propertyRequests = Seq(
            makePropertyRequestRequestItem("")
          )
        )
      )

      val flowType = BookingRequestTypeResolver.getFlowType(request, Some(mockFeatureAware), mockWhitelabelInfo)
      flowType mustBe BookingFlow.Cart
    }
    it("should mark as FlightWithProtection for SingleFlight request") {
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          flightRequests = Seq(FlightRequestItem(Some(""), Some(ConfirmPriceRequest("", "")), None))
        )
      )
      val featureAware = mock[FeatureAware]

      val flowType = BookingRequestTypeResolver.getFlowType(request, Some(featureAware), mockWhitelabelInfo)
      flowType mustBe BookingFlow.FlightWithProtection
    }
  }

  it("should work properly with car request with protection") {
    // Arrange
    val request = SetupBookingRequest(
      productsRequest = ProductsRequest(
        flightRequests = Seq.empty,
        propertyRequests = Seq.empty,
        carRequestsOpt = Option(
          Seq(
            CarRequestItem(
              id = "1",
              confirmPriceRequest = CarConfirmPriceRequest(
                identifier = None,
                searchToken =
                  "AAAA7/AdeyJ2IjoxLCJ0T2JqIjp7ImlkIjoiQ0FSLTkwYWwiLCJkYSI6MzUsInBpY2sgACBjbyIAoDEzNDg3OSwiY3QOAMA5OTk5LCJsb2MiOiIMABAiDQD2GHQiOjIsImR0IjoiMjAyMC0wMy0yM1QxODoyNDowMFoifSwiZHJvcFQARDc3NzdSAFQxOTI4MlMAAQ0AD1QABho1VAARYVIA8ApwIjozODkwLjg4LCJjdXIiOiJUSEIifX19"
              ),
              tripProtectionRequest = Some(TripProtectionRequestItem("", ProductTypeEnum.Car, 1))
            )
          )
        )
      )
    )

    // Act
    val flowType = BookingRequestTypeResolver.getFlowType(request, Some(mockFeatureAware), mockWhitelabelInfo)

    // Assert
    flowType mustBe BookingFlow.Cart
  }

  describe("getFlowType for create booking v1 request") {
    // Note: Multi-product and packages are not supported
    it("should work properly with single flight request") {
      val request = CreateBookingRequestMock
        .getMock()
        .copy(
          products = Products(
            flightItems = Some(Seq(FlightItem("", None)))
          )
        )

      val flowType = BookingRequestTypeResolver.getFlowType(request)
      flowType mustBe BookingFlow.SingleFlight
    }

    it("should work properly with single property request") {
      val request = CreateBookingRequestMock
        .getMock()
        .copy(
          products = Products(
            propertyItems = Some(Seq(PropertyItem()))
          )
        )

      val flowType = BookingRequestTypeResolver.getFlowType(request)
      flowType mustBe BookingFlow.SingleProperty
    }

    it("should work properly with multiple property requests") {
      val request = CreateBookingRequestMock
        .getMock()
        .copy(
          products = Products(
            propertyItems = Some(Seq(PropertyItem(), PropertyItem()))
          )
        )

      val flowType = BookingRequestTypeResolver.getFlowType(request)
      flowType mustBe BookingFlow.MixAndSave
    }
  }

  describe("get bookingFlow from itinerary") {
    it("should return single flight when hotel is empty and the length of flight items is one") {
      val itinerary = createItinerary(Seq.empty, Seq(FlightBooking(1, CreatedBookingStatus.BookingProcessing)))
      BookingRequestTypeResolver.getFlowType(itinerary) mustBe BookingFlow.SingleFlight
    }

    it("should return single flight when hotel is empty and the length of flight items is more then one") {
      val itinerary = createItinerary(
        Seq.empty,
        Seq(
          FlightBooking(1, CreatedBookingStatus.BookingProcessing),
          FlightBooking(1, CreatedBookingStatus.BookingProcessing)
        )
      )
      BookingRequestTypeResolver.getFlowType(itinerary) mustBe BookingFlow.Hackerfare
    }

    it("should return single property when flight is empty and the length of hotel items is one") {
      val itinerary = createItinerary(
        Seq(
          HotelBooking(
            lineItemId = 1,
            itineraryId = 1,
            bookingId = 1,
            bookingStatus = CreatedBookingStatus.BookingProcessing
          )
        ),
        Seq.empty
      )
      BookingRequestTypeResolver.getFlowType(itinerary) mustBe BookingFlow.SingleProperty
    }

    it("should return mix and save when flight is empty and the length of hotel items is greater than one") {
      val itinerary = createItinerary(
        Seq(
          HotelBooking(
            lineItemId = 1,
            itineraryId = 1,
            bookingId = 1,
            bookingStatus = CreatedBookingStatus.BookingProcessing
          ),
          HotelBooking(1, 1, 1, bookingStatus = CreatedBookingStatus.BookingProcessing)
        ),
        Seq.empty
      )
      BookingRequestTypeResolver.getFlowType(itinerary) mustBe BookingFlow.MixAndSave
    }
    it("should return package when flight is nonEmpty and hotel is nonEmpty") {
      val itinerary = createItinerary(
        Seq(
          HotelBooking(
            lineItemId = 1,
            itineraryId = 1,
            bookingId = 1,
            bookingStatus = CreatedBookingStatus.BookingProcessing
          )
        ),
        Seq(FlightBooking(1, CreatedBookingStatus.BookingProcessing))
      )
      BookingRequestTypeResolver.getFlowType(itinerary) mustBe BookingFlow.Package
    }
  }

  describe("get bookingFlow from all product tokens") {

    val propertyBookingToken    = Map("1" -> defaultPropertyBookingToken)
    val flightBookingToken      = Map("2" -> Seq(defaultFlightBookingToken))
    val tripProtectionToken     = Map("3" -> defaultTripProtectionToken)
    val hackerfareBookingTokens = Map("4" -> Seq(defaultFlightBookingToken, defaultFlightBookingToken))
    val mixAndSaveToken         = Map("1" -> defaultPropertyBookingToken, "2" -> defaultPropertyBookingToken)
    val vehicleToken            = Map("5" -> defaultCarToken)
    val activityToken           = Map("12" -> defaultActivityToken)
    val productsRequest         = ProductsRequest()

    it("should return single property when we have only 1 property") {
      BookingRequestTypeResolver.getFlowType(
        propertyBookingToken,
        Map.empty,
        Map.empty,
        Map.empty,
        Map.empty,
        Map.empty,
        mockWhitelabelInfo,
        isPackagingRequest = false
      ) mustBe BookingFlow.SingleProperty
    }

    it("should return single flight when we have only 1 flight") {
      BookingRequestTypeResolver.getFlowType(
        Map.empty,
        flightBookingToken,
        Map.empty,
        Map.empty,
        Map.empty,
        Map.empty,
        mockWhitelabelInfo,
        isPackagingRequest = false
      ) mustBe BookingFlow.SingleFlight
    }

    it("should return single MixAndSave when we have only 2 properties") {
      BookingRequestTypeResolver.getFlowType(
        mixAndSaveToken,
        Map.empty,
        Map.empty,
        Map.empty,
        Map.empty,
        Map.empty,
        mockWhitelabelInfo,
        isPackagingRequest = false
      ) mustBe BookingFlow.MixAndSave
    }

    it("should return Package when we have only 1 property and 1 flight") {
      BookingRequestTypeResolver.getFlowType(
        propertyBookingToken,
        flightBookingToken,
        Map.empty,
        Map.empty,
        Map.empty,
        Map.empty,
        mockWhitelabelInfo,
        isPackagingRequest = true
      ) mustBe BookingFlow.Package
    }

    it("should return single vehicle when we have only 1 vehicle") {
      BookingRequestTypeResolver.getFlowType(
        Map.empty,
        Map.empty,
        Map.empty,
        vehicleToken,
        Map.empty,
        Map.empty,
        mockWhitelabelInfo,
        isPackagingRequest = false
      ) mustBe BookingFlow.SingleVehicle
    }

    it("should return Flight With Protection when we have only 1 flight and 1 tripProtection") {
      BookingRequestTypeResolver.getFlowType(
        Map.empty,
        flightBookingToken,
        tripProtectionToken,
        Map.empty,
        Map.empty,
        Map.empty,
        mockWhitelabelInfo,
        false
      ) mustBe BookingFlow.FlightWithProtection
    }

    it("should return Hackefare when we have only 2 flights") {
      BookingRequestTypeResolver.getFlowType(
        Map.empty,
        hackerfareBookingTokens,
        Map.empty,
        Map.empty,
        Map.empty,
        Map.empty,
        mockWhitelabelInfo,
        isPackagingRequest = false
      ) mustBe BookingFlow.Hackerfare
    }

    it("should return MultiFlightsWithProtection when we have only 2 flights with protection") {
      BookingRequestTypeResolver.getFlowType(
        Map.empty,
        hackerfareBookingTokens,
        tripProtectionToken,
        Map.empty,
        Map.empty,
        Map.empty,
        mockWhitelabelInfo,
        isPackagingRequest = false
      ) mustBe BookingFlow.MultiFlightsWithProtection
    }

    it(
      "should return MultiFlightsWithProtection when we have 2 flights with protection even when UNIBF-946 enabled"
    ) {
      val migrationEnabledFeatureAware = mock[FeatureAware]
      when(migrationEnabledFeatureAware.migrateFlightToCartFlow(mockWhitelabelInfo)).thenReturn(true)
      BookingRequestTypeResolver.getFlowType(
        Map.empty,
        hackerfareBookingTokens,
        tripProtectionToken,
        Map.empty,
        Map.empty,
        Map.empty,
        mockWhitelabelInfo,
        isPackagingRequest = false,
        featureAware = Some(migrationEnabledFeatureAware)
      ) mustBe BookingFlow.MultiFlightsWithProtection
    }

    it(
      "should return Cart when we have 2 flights with protection when UNIBF-2875 enabled"
    ) {
      val migrationEnabledFeatureAware = mock[FeatureAware]
      when(migrationEnabledFeatureAware.migrateHackerFareToCartFlow(mockWhitelabelInfo)).thenReturn(true)
      BookingRequestTypeResolver.getFlowType(
        Map.empty,
        hackerfareBookingTokens,
        tripProtectionToken,
        Map.empty,
        Map.empty,
        Map.empty,
        mockWhitelabelInfo,
        isPackagingRequest = false,
        featureAware = Some(migrationEnabledFeatureAware)
      ) mustBe BookingFlow.Cart
    }

    it(
      "should return Cart when we have 2 flights (hackerfare) when UNIBF-2875 enabled"
    ) {
      val migrationEnabledFeatureAware = mock[FeatureAware]
      when(migrationEnabledFeatureAware.migrateHackerFareToCartFlow(mockWhitelabelInfo)).thenReturn(true)
      BookingRequestTypeResolver.getFlowType(
        Map.empty,
        hackerfareBookingTokens,
        Map.empty,
        Map.empty,
        Map.empty,
        Map.empty,
        mockWhitelabelInfo,
        isPackagingRequest = false,
        featureAware = Some(migrationEnabledFeatureAware)
      ) mustBe BookingFlow.Cart
    }

    it(
      "should return Cart when we have 2 flights (hackerfare) when UNIBF-2875 & UNIBF-946 are enabled"
    ) {
      val migrationEnabledFeatureAware = mock[FeatureAware]
      when(migrationEnabledFeatureAware.migrateHackerFareToCartFlow(mockWhitelabelInfo)).thenReturn(true)
      when(migrationEnabledFeatureAware.migrateFlightToCartFlow(mockWhitelabelInfo)).thenReturn(true)
      BookingRequestTypeResolver.getFlowType(
        Map.empty,
        hackerfareBookingTokens,
        Map.empty,
        Map.empty,
        Map.empty,
        Map.empty,
        mockWhitelabelInfo,
        isPackagingRequest = false,
        featureAware = Some(migrationEnabledFeatureAware)
      ) mustBe BookingFlow.Cart
    }

    it(
      "should return Cart when we have single flights with protection when UNIBF-946 enabled"
    ) {
      val migrationEnabledFeatureAware = mock[FeatureAware]
      when(mockWhitelabelInfo.isFeatureEnabled(WhiteLabelFeatureName.FlightToCartMigration)).thenReturn(true)
      when(migrationEnabledFeatureAware.migrateFlightToCartFlow(mockWhitelabelInfo)).thenReturn(true)

      BookingRequestTypeResolver.getFlowType(
        Map.empty,
        flightBookingToken,
        tripProtectionToken,
        Map.empty,
        Map.empty,
        Map.empty,
        mockWhitelabelInfo,
        isPackagingRequest = false,
        featureAware = Some(migrationEnabledFeatureAware)
      ) mustBe BookingFlow.Cart
    }

    it(
      "should return Cart when we have single flights with protection when UNIBF-2519 enabled"
    ) {
      val migrationEnabledFeatureAware = mock[FeatureAware]
      when(mockWhitelabelInfo.isFeatureEnabled(WhiteLabelFeatureName.FlightToCartMigration)).thenReturn(true)
      when(migrationEnabledFeatureAware.migrateHackerFareToCartFlow(mockWhitelabelInfo)).thenReturn(true)

      BookingRequestTypeResolver.getFlowType(
        Map.empty,
        flightBookingToken,
        tripProtectionToken,
        Map.empty,
        Map.empty,
        Map.empty,
        mockWhitelabelInfo,
        isPackagingRequest = false,
        featureAware = Some(migrationEnabledFeatureAware)
      ) mustBe BookingFlow.FlightWithProtection
    }

    it("should return Unknown when we have undefined combination") {
      BookingRequestTypeResolver.getFlowType(
        propertyBookingToken,
        hackerfareBookingTokens,
        tripProtectionToken,
        Map.empty,
        Map.empty,
        Map.empty,
        mockWhitelabelInfo,
        false
      ) mustBe BookingFlow.Unknown
    }

    it("should return MultiHotel when we have only 2 properties and PackageRequest") {
      BookingRequestTypeResolver.getFlowType(
        mixAndSaveToken,
        Map.empty,
        Map.empty,
        Map.empty,
        Map.empty,
        Map.empty,
        mockWhitelabelInfo,
        isPackagingRequest = true
      ) mustBe BookingFlow.MultiHotel
    }

    it("should return SingleActivity when we have only 1 activity") {
      BookingRequestTypeResolver.getFlowType(
        Map.empty,
        Map.empty,
        Map.empty,
        Map.empty,
        activityToken,
        Map.empty,
        mockWhitelabelInfo,
        isPackagingRequest = false
      ) mustBe BookingFlow.SingleActivity
    }
  }

  describe("buildMultiProductFlowDefinition from SetupBookingRequest") {
    it("build number of product correctly and Cart Pricing context is defined") {
      val productsRequest = ProductsRequest(
        propertyRequests = Seq(mock[PropertyRequestItem]),
        flightRequests = Seq(mock[FlightRequestItem], mock[FlightRequestItem]),
        carRequestsOpt = Some(Seq(mock[CarRequestItem], mock[CarRequestItem], mock[CarRequestItem])),
        activityRequests = Some(
          Seq(
            mock[ActivityRequestItem],
            mock[ActivityRequestItem],
            mock[ActivityRequestItem],
            mock[ActivityRequestItem]
          )
        ),
        tripProtectionRequests = Some(
          Seq(
            mock[TripProtectionRequestItem],
            mock[TripProtectionRequestItem],
            mock[TripProtectionRequestItem],
            mock[TripProtectionRequestItem],
            mock[TripProtectionRequestItem]
          )
        ),
        cartPricingContext = Some(mock[CartPricingContext])
      )

      val request = SetupBookingRequest(productsRequest)
      val expectedMultiProductDefinition = MultiProductFlowDefinition(
        numberOfProperty = 1,
        numberOfFlight = 2,
        numberOfCar = 3,
        numberOfActivity = 4,
        numberOfTripProtection = 5
      )
      BookingRequestTypeResolver.buildMultiProductFlowDefinition(
        request,
        isActivityCartBF = false
      ) mustBe expectedMultiProductDefinition
    }

    it("build number of product correctly and Cart Pricing context is defined and tripProtection optInVal is None") {
      val protectionItem = mock[TripProtectionRequestItem]
      when(protectionItem.optInValue).thenReturn(ProtectionRequestItemOptInValue.None)
      val productsRequest = ProductsRequest(
        activityRequests = Some(
          Seq(
            mock[ActivityRequestItem]
          )
        ),
        tripProtectionRequests = Some(
          Seq(
            protectionItem
          )
        ),
        cartPricingContext = Some(mock[CartPricingContext])
      )

      val request = SetupBookingRequest(productsRequest)
      val expectedMultiProductDefinition = MultiProductFlowDefinition(
        numberOfProperty = 0,
        numberOfFlight = 0,
        numberOfCar = 0,
        numberOfActivity = 1,
        numberOfTripProtection = 0
      )
      BookingRequestTypeResolver.buildMultiProductFlowDefinition(
        request,
        isActivityCartBF = true
      ) mustBe expectedMultiProductDefinition
    }

    it("build number of product correctly and Cart Pricing context is defined and tripProtection optInVal is Decline") {
      val protectionItem = mock[TripProtectionRequestItem]
      when(protectionItem.optInValue).thenReturn(ProtectionRequestItemOptInValue.Decline)
      val productsRequest = ProductsRequest(
        activityRequests = Some(
          Seq(
            mock[ActivityRequestItem]
          )
        ),
        tripProtectionRequests = Some(
          Seq(
            protectionItem
          )
        ),
        cartPricingContext = Some(mock[CartPricingContext])
      )

      val request = SetupBookingRequest(productsRequest)
      val expectedMultiProductDefinition = MultiProductFlowDefinition(
        numberOfProperty = 0,
        numberOfFlight = 0,
        numberOfCar = 0,
        numberOfActivity = 1,
        numberOfTripProtection = 0
      )
      BookingRequestTypeResolver.buildMultiProductFlowDefinition(
        request,
        isActivityCartBF = true
      ) mustBe expectedMultiProductDefinition
    }

    it("build number of product correctly and Cart Pricing context is defined for SingleActivity") {
      val productsRequest = ProductsRequest(
        activityRequests = Some(
          Seq(
            mock[ActivityRequestItem]
          )
        ),
        cartPricingContext = Some(mock[CartPricingContext])
      )

      val request = SetupBookingRequest(productsRequest)
      val expectedMultiProductDefinition = MultiProductFlowDefinition(
        numberOfActivity = 1
      )
      BookingRequestTypeResolver.buildMultiProductFlowDefinition(
        request,
        isActivityCartBF = true
      ) mustBe expectedMultiProductDefinition
    }

    it("build number of product correctly and Cart Pricing context is not defined") {
      val productsRequest = ProductsRequest(
        propertyRequests = Seq(mock[PropertyRequestItem]),
        cartPricingContext = None
      )

      val request = SetupBookingRequest(productsRequest)
      val expectedMultiProductDefinition = MultiProductFlowDefinition(
        numberOfProperty = 1,
        numberOfFlight = 0,
        numberOfCar = 0,
        numberOfActivity = 0,
        numberOfTripProtection = 0
      )
      BookingRequestTypeResolver.buildMultiProductFlowDefinition(
        request,
        isActivityCartBF = false
      ) mustBe expectedMultiProductDefinition
    }
  }

  describe("calculateTripProtection from SetupBookingRequest") {
    it("calculate trip protection count correctly if enableActivityCartBF is false") {
      val productsRequest = ProductsRequest(
        propertyRequests = Seq(mock[PropertyRequestItem]),
        flightRequests = Seq(mock[FlightRequestItem], mock[FlightRequestItem]),
        carRequestsOpt = Some(Seq(mock[CarRequestItem], mock[CarRequestItem], mock[CarRequestItem])),
        activityRequests = Some(
          Seq(
            mock[ActivityRequestItem],
            mock[ActivityRequestItem],
            mock[ActivityRequestItem],
            mock[ActivityRequestItem]
          )
        ),
        tripProtectionRequests = Some(
          Seq(
            mock[TripProtectionRequestItem],
            mock[TripProtectionRequestItem],
            mock[TripProtectionRequestItem],
            mock[TripProtectionRequestItem],
            mock[TripProtectionRequestItem]
          )
        ),
        cartPricingContext = Some(mock[CartPricingContext])
      )

      BookingRequestTypeResolver.calculateTripProtectionCount(productsRequest, isActivityCartBF = false) mustBe 5
    }

    it("calculate trip protection count correctly if enableActivityCartBF is true and OptInValue is None") {
      val protectionItem = mock[TripProtectionRequestItem]
      when(protectionItem.optInValue).thenReturn(ProtectionRequestItemOptInValue.None)
      val productsRequest = ProductsRequest(
        activityRequests = Some(
          Seq(
            mock[ActivityRequestItem]
          )
        ),
        tripProtectionRequests = Some(
          Seq(
            protectionItem
          )
        ),
        cartPricingContext = Some(mock[CartPricingContext])
      )

      BookingRequestTypeResolver.calculateTripProtectionCount(productsRequest, isActivityCartBF = true) mustBe 0
    }

    it("calculate trip protection count correctly if enableActivityCartBF is true and OptInValue is Decline") {
      val protectionItem = mock[TripProtectionRequestItem]
      when(protectionItem.optInValue).thenReturn(ProtectionRequestItemOptInValue.Decline)
      val productsRequest = ProductsRequest(
        flightRequests = Seq(mock[FlightRequestItem]),
        tripProtectionRequests = Some(
          Seq(
            protectionItem
          )
        ),
        cartPricingContext = Some(mock[CartPricingContext])
      )

      BookingRequestTypeResolver.calculateTripProtectionCount(productsRequest, isActivityCartBF = true) mustBe 0
    }

    it(
      "calculate trip protection count correctly if enableActivityCartBF is true and tripProtection optInVal is Purchase"
    ) {
      val protectionItem = mock[TripProtectionRequestItem]
      when(protectionItem.optInValue).thenReturn(ProtectionRequestItemOptInValue.Purchase)
      val productsRequest = ProductsRequest(
        flightRequests = Seq(mock[FlightRequestItem]),
        tripProtectionRequests = Some(
          Seq(
            protectionItem
          )
        ),
        cartPricingContext = Some(mock[CartPricingContext])
      )

      BookingRequestTypeResolver.calculateTripProtectionCount(productsRequest, isActivityCartBF = true) mustBe 1
    }
  }

  describe("buildMultiProductFlowDefinition from CreateBookingRequest") {
    it("build number of product correctly and Cart request is defined") {
      val products = Products(
        propertyItems = Some(Seq(mock[PropertyItem])),
        flightItems = Some(Seq(mock[FlightItem], mock[FlightItem])),
        carItems = Some(Seq(mock[CarItem], mock[CarItem], mock[CarItem])),
        activitiesItems =
          Some(Seq(mock[ActivitiesItem], mock[ActivitiesItem], mock[ActivitiesItem], mock[ActivitiesItem])),
        protectionItems = Some(
          Seq(
            mock[ProtectionItem],
            mock[ProtectionItem],
            mock[ProtectionItem],
            mock[ProtectionItem],
            mock[ProtectionItem]
          )
        )
      )
      val request = mock[CreateBookingRequest]
      when(request.products).thenReturn(products)
      val expectedMultiProductDefinition = MultiProductFlowDefinition(
        numberOfProperty = 1,
        numberOfFlight = 2,
        numberOfCar = 3,
        numberOfActivity = 4,
        numberOfTripProtection = 5
      )
      BookingRequestTypeResolver.buildMultiProductFlowDefinition(request) mustBe expectedMultiProductDefinition
    }

    it("build number of product correctly and Cart request is not defined") {
      val products = Products(propertyItems = Some(Seq(mock[PropertyItem])))
      val request  = mock[CreateBookingRequest]
      when(request.products).thenReturn(products)
      val expectedMultiProductDefinition = MultiProductFlowDefinition(
        numberOfProperty = 1,
        numberOfFlight = 0,
        numberOfCar = 0,
        numberOfActivity = 0,
        numberOfTripProtection = 0
      )
      BookingRequestTypeResolver.buildMultiProductFlowDefinition(request) mustBe expectedMultiProductDefinition
    }
  }

  describe("getProductType for measurementsContext") {
    // Note: Multi-product and packages are not supported
    it("should work properly with single flight request") {
      val request = CreateBookingRequestMock
        .getMock()
        .copy(
          products = Products(
            flightItems = Some(Seq(FlightItem("", None)))
          )
        )

      val productType = BookingRequestTypeResolver.getProductType(request)
      productType mustBe ProductTypeEnum.Flight
    }

    it("should work properly with single property request") {
      val request = CreateBookingRequestMock
        .getMock()
        .copy(
          products = Products(
            propertyItems = Some(Seq(PropertyItem()))
          )
        )

      val productType = BookingRequestTypeResolver.getProductType(request)
      productType mustBe ProductTypeEnum.Property
    }

    it("should work properly with single vehicle request") {
      val request = CreateBookingRequestMock
        .getMock()
        .copy(
          products = Products(
            carItems = Some(Seq(CarItem(None, None)))
          )
        )

      val productType = BookingRequestTypeResolver.getProductType(request)
      productType mustBe ProductTypeEnum.Car
    }

    it("should work properly with single activity request") {
      val request = CreateBookingRequestMock
        .getMock()
        .copy(
          products = Products(
            activitiesItems = Some(Seq(ActivitiesItem(None, None, None, None)))
          )
        )

      val productType = BookingRequestTypeResolver.getProductType(request)
      productType mustBe ProductTypeEnum.Activity
    }

    it("should work properly with multiple property requests") {
      val request = CreateBookingRequestMock
        .getMock()
        .copy(
          products = Products(
            propertyItems = Some(Seq(PropertyItem(), PropertyItem()))
          )
        )

      val productType = BookingRequestTypeResolver.getProductType(request)
      productType mustBe ProductTypeEnum.Property
    }

    it("should work properly with multiple flights requests") {
      val request = CreateBookingRequestMock
        .getMock()
        .copy(
          products = Products(
            flightItems = Some(Seq(FlightItem("", None), FlightItem("", None)))
          )
        )

      val productType = BookingRequestTypeResolver.getProductType(request)
      productType mustBe ProductTypeEnum.Flight
    }

    it("should work properly with multiple vehicle request") {
      val request = CreateBookingRequestMock
        .getMock()
        .copy(
          products = Products(
            carItems = Some(Seq(CarItem(None, None), CarItem(None, None)))
          )
        )

      val productType = BookingRequestTypeResolver.getProductType(request)
      productType mustBe ProductTypeEnum.Car
    }

    it("should work properly with multiple activity request") {
      val request = CreateBookingRequestMock
        .getMock()
        .copy(
          products = Products(
            activitiesItems = Some(Seq(ActivitiesItem(None, None, None, None), ActivitiesItem(None, None, None, None)))
          )
        )

      val productType = BookingRequestTypeResolver.getProductType(request)
      productType mustBe ProductTypeEnum.Activity
    }

    it("should work properly with hotel+flights requests") {
      val request = CreateBookingRequestMock
        .getMock()
        .copy(
          products = Products(
            propertyItems = Some(Seq(PropertyItem())),
            flightItems = Some(Seq(FlightItem("", None)))
          )
        )

      val productType = BookingRequestTypeResolver.getProductType(request)
      productType mustBe ProductTypeEnum.Multi
    }

    it("should work properly with vehicle+activity requests") {
      val request = CreateBookingRequestMock
        .getMock()
        .copy(
          products = Products(
            carItems = Some(Seq(CarItem(None, None), CarItem(None, None))),
            activitiesItems = Some(Seq(ActivitiesItem(None, None, None, None), ActivitiesItem(None, None, None, None)))
          )
        )

      val productType = BookingRequestTypeResolver.getProductType(request)
      productType mustBe ProductTypeEnum.Multi
    }

    it("should work properly with hotel+flight+vehicle+activity requests") {
      val request = CreateBookingRequestMock
        .getMock()
        .copy(
          products = Products(
            propertyItems = Some(Seq(PropertyItem())),
            flightItems = Some(Seq(FlightItem("", None))),
            carItems = Some(Seq(CarItem(None, None), CarItem(None, None))),
            activitiesItems = Some(Seq(ActivitiesItem(None, None, None, None), ActivitiesItem(None, None, None, None)))
          )
        )

      val productType = BookingRequestTypeResolver.getProductType(request)
      productType mustBe ProductTypeEnum.Multi
    }

    it("should work properly with rebook and cancel single property") {
      // Arrange
      val request = SetupBookingRequest(
        productsRequest = ProductsRequest(
          flightRequests = Seq.empty,
          propertyRequests = Seq(makePropertyRequestRequestItem("")),
          carRequestsOpt = Option(
            Seq.empty
          ),
          cartContext = Some(CartContext("")),
          cartPricingContext = Some(CartPricingContext(Some(""), None))
        ),
        rebookAndCancelRequest = Some(RebookAndCancelRequest(tokenMessage = TokenMessage("", 1)))
      )

      // Act
      val flowType = BookingRequestTypeResolver.getFlowType(request, Some(mockFeatureAware), mockWhitelabelInfo)

      // Assert
      flowType mustBe BookingFlow.Cart
    }
  }

  private def createItinerary(hotels: Seq[HotelBooking], flights: Seq[FlightBooking]): Itinerary = {
    Itinerary(itineraryId = 123456, bookings = hotels, flights = flights)
  }

  private def makePropertyRequestRequestItem(testId: String) =
    PropertyRequestItem(
      testId,
      PropertySearchCriteria(
        propertyId = Some(114558),
        roomIdentifier = "08064e41-955b-7e44-ff89-a7f5dc349eb4",
        occupancyRequest = OccupancyRequest(
          roomCount = 1,
          adult = 2,
          child = 0,
          childAges = List(),
          overrideExtraBed = Some(0)
        ),
        durationRequest = DurationRequest(
          checkIn = new LocalDate("2019-12-12"),
          lengthOfStay = 2
        ),
        pricingRequest = Some(
          PricingRequest(
            isMse = false,
            requiredPrice = "",
            requiredBasis = "",
            isRPM2Included = false,
            selectedPointMaxId = None,
            isIncludeUsdAndLocalCurrency = false,
            allowOverrideOccupancy = false,
            enableOpaqueChannel = false,
            isAllowRoomTypeNotGuarantee = false,
            synchronous = true,
            partnerLoyaltyProgramId = Some(0)
          )
        ),
        papiContextRequest = Some(
          PapiContextRequest(isAllowBookOnRequest = true, trafficTypeId = Some(1), rawBotProfile = "", showCms = false)
        ),
        roomSelectionRequest = None,
        propertyRequest = None,
        simplifiedRoomSelectionRequest = None,
        partnerRequest = Some(
          PartnerRequest(
            partnerRoomRateType = Some(3),
            partnerSurchargeRateType = Some(1),
            ratePartnerSummaries = Some(true),
            discountType = Some(1),
            isExcludedPfFromTax = Some(false),
            returnDailyRates = Some(true)
          )
        )
      )
    )
}
