package com.agoda.bapi.common.mapper.booking

import com.agoda.adp.messaging.schema.CompatibilityResult
import com.agoda.bapi.common.mapper.booking.vehicle._
import com.agoda.bapi.common.message.creation.BreakDownItemID
import com.agoda.bapi.common.message.{ActivityBookingStateWithItinerary, AddOnBookingStateWithItinerary, CegFastTrackBookingStateWithItinerary}
import com.agoda.bapi.common.model.base.{BaseBookingEssInfoInternal, BaseBookingModelInternal, BaseBookingRelationshipInternal, BaseCancellationInfoModelInternal}
import com.agoda.bapi.common.model.booking.BookingStateMessage.mapBaseBookingRelationshipToBaseBookingRelationshipForMessage
import com.agoda.bapi.common.model.booking._
import com.agoda.bapi.common.model.car.VehicleBookingState
import com.agoda.bapi.common.model.car.VehicleBookingStateModel._
import com.agoda.bapi.common.model.creation.AccountingEntity
import com.agoda.bapi.common.model.flight.flightModel._
import com.agoda.bapi.common.model.flight.history.ActionType
import com.agoda.bapi.common.model.itinerary.ItineraryInternalModel
import com.agoda.bapi.common.model.multiproduct.{MultiProductBookingGroupDBModel, MultiProductInfoDBModel}
import com.agoda.bapi.common.model.payment.{PaymentModel, SupplierPaymentMethod}
import com.agoda.bapi.common.model.product.ProductTypeEnum
import com.agoda.bapi.common.model.protection.ProtectionBookingState
import com.agoda.bapi.common.model.protection.ProtectionBookingStateModel.{ProtectionCfar, ProtectionModelInternal, ProtectionProductBooking}
import com.agoda.bapi.common.model.{CommonBookingEventsInfo, CommonBookingInfo}
import com.agoda.bapi.common.util.LocalDateTimeUtil
import com.agoda.commons.agprotobuf.scalapb.utils.ProtoConverter
import com.agoda.commons.agprotobuf.types.TimeOfDay
import com.agoda.mpb.common.MultiProductType
import com.agoda.mpbe.state.booking._
import com.agoda.mpbe.state.product.activity.ActivityProductModel
import com.agoda.mpbe.state.product.addOn.AddOnProductModel
import com.agoda.mpbe.state.product.cegFastTrack.CegFastTrackProductModel
import com.danielasfregola.randomdatagenerator.RandomDataGenerator._
import org.joda.time.DateTime
import org.scalacheck.Arbitrary
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

import java.sql.Timestamp
import java.time.{Instant, LocalDateTime}
import java.util.Date

class BookingStateMessageMapperTest extends AnyWordSpec with Matchers {

  private val multiProductBookingGroupDBModel = Seq(
    MultiProductBookingGroupDBModel(
      bookingId = -1,
      itineraryId = -2,
      cartId = -3,
      packageId = Some(-4)
    )
  )

  private val expectedMultiProductBookingGroupModelMessage = Seq(
    MultiProductBookingGroupModelMessage(
      bookingId = -1,
      itineraryId = -2,
      cartId = -3,
      packageId = Some(-4)
    )
  )

  "BookingStateMessageMapper" should {
    "map correctly mapMultiProductInfosForMessage" in {
      val expected = Some(Seq(MultiProductInfoForMessage(1, 6)))
      val input    = Seq(MultiProductInfoDBModel(1, MultiProductType.FlightWithProtection))
      val result   = BookingStateMessage.mapMultiProductInfosForMessage(input)
      result shouldBe expected
    }

    "map multi product info to None in case of Nil" in {
      val result = BookingStateMessage.mapMultiProductInfosForMessage(Nil)
      result shouldBe None
    }

    "map fare rule policies correctly" in {
      val inputCreatedWhen     = DateTime.parse("2019-08-02T16:01")
      val inputModifiedWhen    = DateTime.parse("2021-12-01T16:01")
      val expectedCreatedWhen  = new Date(1564736460000L)
      val expectedModifiedWhen = new Date(1638349260000L)
      val expected = Seq(
        FareRulePolicyForMessage(
          1,
          "2",
          3,
          4,
          true,
          1.2,
          "rub",
          1,
          2,
          Some(expectedCreatedWhen),
          Some(expectedModifiedWhen),
          Some(23455),
          Some(5678991),
          Some("SUPPLIER"),
          Some("INFO_UNAVAILABLE")
        )
      )

      val result = BookingStateMessage.mapFareRulePolicies(
        Seq(
          FareRulePolicyModelInternal(
            1,
            "2",
            3,
            4,
            true,
            1.2,
            "rub",
            1,
            2,
            Some(inputCreatedWhen),
            Some(inputModifiedWhen),
            Some(23455L),
            Some(5678991L),
            Some(0),
            Some(0),
            Some("SUPPLIER"),
            Some("INFO_UNAVAILABLE")
          )
        )
      )
      result shouldBe expected
    }

    "map baggage allowances correctly correctly" in {
      val expected = Seq(
        FlightBaggageAllowanceForMessage(
          referenceId = 1, // in natural order across all baggage allowance
          sliceReferenceId = 1,
          segmentReferenceId = 1,
          flightBaggageId = 0,
          flightSliceId = 0,
          flightSegmentId = None,
          `type` = 0,
          count = 0,
          maxWeightKg = Some(20),
          maxWeightLbs = Some(110),
          totalSizeCm = Some(10),
          totalSizeIn = Some(2),
          lengthCm = Some(50),
          lengthIn = Some(1),
          widthCm = Some(30),
          widthIn = Some(30),
          heightCm = Some(10),
          heightIn = Some(25),
          priceAmt = Some(12),
          priceCurrency = None,
          paxType = Some("ADT"),
          source = None,
          cmsMappingValue = Some("NO_FREE_BAGS")
        )
      )

      val input = Seq(
        FlightBaggageAllowance(
          referenceId = 1, // in natural order across all baggage allowance
          sliceReferenceId = 1,
          segmentReferenceId = 1,
          flightBaggageId = 0,
          flightSliceId = 0,
          flightSegmentId = None,
          `type` = 0,
          count = 0,
          maxWeightKg = Some(20),
          maxWeightLbs = Some(110),
          totalSizeCm = Some(10),
          totalSizeIn = Some(2),
          lengthCm = Some(50),
          lengthIn = Some(1),
          widthCm = Some(30),
          widthIn = Some(30),
          heightCm = Some(10),
          heightIn = Some(25),
          priceAmt = Some(12),
          priceCurrency = None,
          paxType = Some("ADT"),
          source = None,
          cmsMappingValue = Some("NO_FREE_BAGS")
        )
      )

      val result = BookingStateMessage.mapFlightBaggageAllowanceToFlightBaggageAllowanceForMessage(
        input
      )
      result shouldBe expected
    }

    "map VehicleBookingState correctly" in {
      val actionTypeId = 1
      val actionId     = 1
      val bookingType  = Some(1)
      val vehicleModelInternal = VehicleModelInternal(
        vehicleBooking = VehicleModelBooking(
          vehicleBookingId = 1,
          itineraryId = 1,
          multiProductId = Some(1),
          bookingDate = DateTime.parse("2019-08-02T16:01"),
          paymentModel = 1,
          displayCurrency = "USD",
          supplierId = 1,
          providerCode = "123",
          supplierBookingId = "123",
          supplierSpecificData = "BKK",
          supplierStatusCode = Some("1"),
          supplierCommissionAmount = 0.0,
          supplierCommissionPercentage = 0.0,
          whitelabelId = 1,
          isCancelled = false,
          cancellationPolicy = "",
          cancellationDate = None,
          fraudScore = None,
          fraudAction = None,
          fraudCheckIp = "",
          storefrontId = 1,
          platformId = Some(1),
          languageId = Some(1),
          serverName = Some("dev"),
          cid = None,
          searchId = None,
          searchRequestId = "123",
          sessionId = "123",
          clientIpAddress = "123",
          trackingCookieId = Some("123"),
          trackingCookieDate = None,
          trackingTag = Some("123"),
          recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
          recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
          recStatus = 1,
          vehicleStateId = 1,
          rejectReasonMessage = None,
          rejectReasonCode = None,
          accountingEntityStr = None,
          postBookingStateId = Some(1)
        ),
        vehicleBookingLocation = VehicleModelBookingPickUpDropOffLocation(
          pickUp = VehicleModelBookingLocation(
            vehicleBookingLocationId = 1,
            vehicleBookingId = 1,
            countryId = 1,
            cityId = 1,
            addressLine = "123",
            postalCode = "123",
            airportCode = Some("123"),
            locationName = "abc",
            isAirport = true,
            airportProviderLocation = None,
            extraInfo = "",
            supplierLocationCode = None,
            phoneNumber = None,
            locationType = None,
            recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
            recModifiedWhen = DateTime.parse("2019-08-02T16:01")
          ),
          dropOff = VehicleModelBookingLocation(
            vehicleBookingLocationId = 1,
            vehicleBookingId = 1,
            countryId = 1,
            cityId = 1,
            addressLine = "123",
            postalCode = "123",
            airportCode = Some("123"),
            locationName = "abc",
            isAirport = true,
            airportProviderLocation = None,
            extraInfo = "",
            supplierLocationCode = None,
            phoneNumber = None,
            locationType = None,
            recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
            recModifiedWhen = DateTime.parse("2019-08-02T16:01")
          )
        ),
        vehicleBookingSummary = VehicleModelBookingSummary(
          vehicleBookingId = 1,
          displayCurrency = "USD",
          totalSurcharge = 0.0,
          surchargeDetails = "THIS IS JSON STRING",
          baseDiscount = 0.0,
          campaignDiscount = 0.0,
          totalFare = 0.0,
          agodaFee = 0.0,
          recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
          recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
          recStatus = 1,
          policyChargeDetails = Some("THIS IS JSON STRING"),
          paymentModel = Some(1),
          baseFare = Some(0.0),
          taxAndFee = Some(0.0),
          extraChargeDetails = Some("THIS IS JSON STRING"),
          postBookingMetadata = Some("THIS IS JSON STRING")
        ),
        vehicleBookingTrip = VehicleModelBookingTrip(
          vehicleBookingId = 1,
          vehicleCode = "abc",
          vehicleName = "abc",
          classification = "abc",
          pickupDatetime = DateTime.parse("2019-08-02T16:01"),
          dropOffDatetime = DateTime.parse("2019-08-03T16:01"),
          pickupLocationId = 1,
          dropOffLocationId = 1,
          driverAge = 30,
          flightNo = None,
          recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
          recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
          supplierConfirmationCode = Some("abc"),
          customerAgeGroup = Some("age-group"),
          securityDepositType = Some("security-deposit"),
          localRenter = Some("local-renter")
        ),
        vehicleFinancialBreakdowns = Seq.empty,
        vehicleBookingExtraOffers = None,
        vehicleBookingCancellation = None,
        vehicleInfo = Some(
          VehicleInfo(
            vehicleInfoId = 123L,
            vehicleBookingId = 321L,
            vehicleCode = "CAR-001",
            vehicleName = "Honda",
            vehicleClassification = "Mini",
            vehicleDoors = Some(4),
            vehicleSeats = Some(4),
            vehicleSuitcases = Some(1),
            vehicleTransmission = Some("vehicleTransmission"),
            vehicleIsAircon = Some(true),
            vehicleIsAirbag = Some(true),
            vehicleFuelType = Some("vehicleFuelType"),
            recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
            recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
            vehicleMileagePolicy = Some(
              VehicleMileagePolicy(
                freeDistance = 0,
                code = "",
                description = "Mock Car Fuel Info",
                charge = None,
                isFreeCoverage = true
              )
            ),
            vehicleFuelPolicy = Some(
              VehicleFuelPolicy(
                coverageType = "Full_To_Full",
                code = "",
                description = "fuel policy",
                charge = None,
                isFreeCoverage = true
              )
            ),
            imageUrl = Some("imageUrl"),
            pickUpSupplierOperationHours =
              Some(Seq(VehicleOperationSchedule("Monday", Seq(VehicleSupplierOperationHour("06:00", "00:00"))))),
            dropOffSupplierOperationHours =
              Some(Seq(VehicleOperationSchedule("Monday", Seq(VehicleSupplierOperationHour("06:00", "00:00"))))),
            providerIconUrl = Some("iconUrl"),
            acrissCode = Some("acrissCode")
          )
        ),
        baseBooking = None
      )
      implicit val arbitraryJodaDateTime = Arbitrary(DateTime.now)
      val bookingPayment                 = random[BookingPaymentState]
      val vehicleBookingState = VehicleBookingState(
        vehicleModelsInternal = Seq(vehicleModelInternal),
        payments = Seq.empty,
        bookingPayments = Seq(bookingPayment),
        itinerary = MultiProductItinerary(itineraryId = 1, memberId = 1),
        itineraryHistories = Seq.empty,
        multiProductBookingGroups = multiProductBookingGroupDBModel
      )
      val expected = BookingStateMessage(
        actionType = actionTypeId,
        actionId = actionId,
        bookingType = bookingType,
        bookingId = 0, // legacy field, we replicate itinerary which can contain multiple bookingIds
        schemaVersion = "1",
        flights = Seq.empty,
        slices = Seq.empty,
        segments = Seq.empty,
        passengers = Seq.empty,
        payments = Seq.empty,
        breakdown = Seq.empty, // vehicle's breakdowns are in `vehicles.vehicleFinancialBreakdowns`
        bookingPayments = Seq(
          BookingPaymentForMessage(
            paymentId = bookingPayment.paymentId,
            bookingId = bookingPayment.bookingId,
            paymentCurrency = bookingPayment.paymentCurrency,
            paymentAmount = bookingPayment.paymentAmount.toDouble,
            amountUsd = bookingPayment.amountUsd.toDouble,
            recStatus = bookingPayment.recStatus,
            recCreatedWhen = bookingPayment.recCreatedWhen.map(_.toDate),
            fxiUplift = bookingPayment.fxiUplift.map(_.toDouble),
            loyaltyPoints = bookingPayment.loyaltyPoints,
            supplierCurrency = bookingPayment.supplierCurrency,
            supplierExchangeRate = bookingPayment.supplierExchangeRate.map(_.toDouble),
            bookingPaymentId = bookingPayment.bookingPaymentId
          )
        ),
        bookingRelationships = Seq.empty,
        breakdownPerPax = Seq.empty,
        baggageAllowance = Seq.empty,
        baggage = Seq.empty,
        history = Seq.empty,
        summary = Seq.empty,
        paxTickets = Seq.empty,
        itinerary = FlightItineraryForMessage(
          itineraryId = 1,
          memberId = 1,
          recStatus = None,
          recCreatedWhen = None,
          recModifiedWhen = None
        ),
        userAgent = None,
        bookingAttribution = Seq.empty,
        itineraryDate = new Date(), // this field is not used by vehicles, defaults to current time
        protectionModels = None,
        multiProductInfos = Some(Seq(MultiProductInfoForMessage(1, MultiProductType.SingleVehicle.id))),
        flightSegmentInfoByPaxType = Seq.empty,
        segmentInfoByPaxType = Seq.empty,
        fareRulePolicies = None,
        flightSeatSelection = Seq.empty,
        vehicle = Some(
          Seq(
            VehiclesForMessage(
              vehicleBooking =
                VehicleModelBookingToVehicleBookingForMessageConverter(vehicleModelInternal.vehicleBooking),
              vehicleBookingLocation = VehicleModelBookingLocationsToVehicleBookingLocationsForMessageConverter(
                vehicleModelInternal.vehicleBookingLocation
              ),
              vehicleBookingSummary = VehicleModelBookingSummaryToVehicleBookingSummaryForMessageConverter(
                vehicleModelInternal.vehicleBookingSummary
              ),
              vehicleBookingTrip =
                VehicleModelBookingTripToVehicleBookingTripForMessageConverter(vehicleModelInternal.vehicleBookingTrip),
              vehicleFinancialBreakdowns =
                BookingStateMessage.mapBreakdownToBreakdownForMessage(vehicleModelInternal.vehicleFinancialBreakdowns),
              vehicleBookingExtraOffers = Seq.empty,
              vehicleBookingCancellation = vehicleModelInternal.vehicleBookingCancellation.map(cancellation =>
                VehicleModelBookingCancellationToVehicleBookingCancellationForMessageConverter(cancellation)
              ),
              vehicleInfo =
                vehicleModelInternal.vehicleInfo.map(info => VehicleModelInfoToVehicleInfoMessageConverter(info))
            )
          )
        ),
        activities = None,
        properties = None,
        cegFastTracks = None,
        addOns = None,
        multiProductBookingGroups = Some(expectedMultiProductBookingGroupModelMessage),
        flightBrandSelections = None,
        flightBrandAttributes = None,
        flightBrandAttributeParams = None,
        flightBaseBooking = None,
        flightBaseCancellationInfo = None,
        crossProductIsolatedFeature = None
      )

      val mockMultiProductInfos = Seq(MultiProductInfoDBModel(1, MultiProductType.SingleVehicle))

      val result = BookingStateMessage.fromVehicleBookingState(
        actionType = ActionType(actionTypeId),
        actionId = actionTypeId,
        bookingType = bookingType,
        vehicleBookingState = vehicleBookingState,
        protectionModel = Seq.empty,
        multiProductInfos = mockMultiProductInfos
      )
      result shouldBe expected.copy(itineraryDate = result.itineraryDate)
    }

    "return getPartitionKey properly" in {
      val bookingStateMessage = BookingStateMessage(
        actionType = 1,
        actionId = 2,
        bookingType = None,
        bookingId = 12345,
        schemaVersion = "1",
        flights = Seq.empty,
        slices = Seq.empty,
        segments = Seq.empty,
        passengers = Seq.empty,
        payments = Seq.empty,
        bookingPayments = Seq.empty,
        bookingRelationships = Seq.empty,
        breakdown = Seq.empty,
        breakdownPerPax = Seq.empty,
        baggageAllowance = Seq.empty,
        history = Seq.empty,
        summary = Seq.empty,
        paxTickets = Seq.empty,
        itinerary = FlightItineraryForMessage(
          itineraryId = 54321,
          memberId = 1,
          recStatus = None,
          recCreatedWhen = None,
          recModifiedWhen = None
        ),
        userAgent = None,
        bookingAttribution = Seq.empty,
        itineraryDate = new Date(), // this field is not used by vehicles, defaults to current time
        protectionModels = None,
        multiProductInfos = None,
        flightSegmentInfoByPaxType = Seq.empty,
        segmentInfoByPaxType = Seq.empty,
        fareRulePolicies = None,
        flightSeatSelection = Seq.empty,
        vehicle = None,
        baggage = Nil,
        activities = None,
        properties = None,
        cegFastTracks = None,
        addOns = None,
        multiProductBookingGroups = None,
        flightBrandSelections = None,
        flightBrandAttributes = None,
        flightBrandAttributeParams = None,
        flightBaseBooking = None,
        flightBaseCancellationInfo = None,
        crossProductIsolatedFeature = None
      )

      val partitionKey = bookingStateMessage.getPartitionKey
      partitionKey shouldBe "54321-12345"
    }

    "map FlightBaggageForMessage" in {
      val inputDateTime = DateTime.parse("2019-08-02T16:01")
      val expectedDate  = new Date(1564736460000L)
      val baggage = Seq(
        FlightBaggage(
          referenceId = 1,
          sliceReferenceId = 100,
          flightBaggageId = 200,
          flightPaxId = 300,
          flightSliceId = 400,
          baggageTypeId = 1,
          quantity = Some(2),
          maxWeight = Some(2),
          maxWeightUnit = Some("KG"),
          weightLimitPerBag = Some(40),
          weightLimitPerBagUnit = Some("KG"),
          sizeLength = Some(10),
          sizeWidth = Some(20),
          sizeHeight = Some(30),
          sizeUnit = Some("CM"),
          priceAmount = 300,
          priceCurrency = "THB",
          supplierData = "~",
          baggageStatus = 1,
          isCarryOn = true,
          recStatus = Some(1),
          recCreatedWhen = Some(inputDateTime),
          recModifiedWhen = Some(inputDateTime),
          scope = Scope.SLICE,
          isPartialSettlementRequired = Some(false)
        )
      )
      val expected = Seq(
        FlightBaggageForMessage(
          referenceId = 1,
          sliceReferenceId = 100,
          flightBaggageId = 200,
          flightPaxId = 300,
          flightSliceId = 400,
          baggageTypeId = 1,
          quantity = Some(2),
          maxWeight = Some(2),
          maxWeightUnit = Some("KG"),
          weightLimitPerBag = Some(40),
          weightLimitPerBagUnit = Some("KG"),
          sizeLength = Some(10),
          sizeWidth = Some(20),
          sizeHeight = Some(30),
          sizeUnit = Some("CM"),
          priceAmount = 300,
          priceCurrency = "THB",
          supplierData = "~",
          baggageStatus = 1,
          isCarryOn = true,
          recStatus = Some(1),
          recCreatedWhen = Some(expectedDate),
          recModifiedWhen = Some(expectedDate),
          scope = Scope.SLICE.id,
          isPartialSettlementRequired = Some(false)
        )
      )
      val actual = BookingStateMessage.mapFlightBaggageForMessage(baggage)
      actual shouldBe expected
    }

    "map mapFlightBrandSelectionForMessage" in {
      val now = DateTime.now
      val input = Seq(
        FlightBrandSelection(
          referenceId = 1,
          sliceReferenceId = 2,
          flightBrandSelectionId = 1,
          flightSliceId = 2,
          brandName = "brandName",
          isUpsell = true,
          upsellMargin = 1.0,
          upsellPrice = 2.0,
          upsellCurrency = "USD",
          recStatus = Option(1),
          recCreatedWhen = Option(now),
          recModifiedWhen = Option(now),
          scope = Scope.ITINERARY
        )
      )
      val expected = Seq(
        FlightBrandSelectionForMessage(
          flightBrandSelectionId = 1,
          flightSliceId = 2,
          brandName = "brandName",
          isUpsell = true,
          upsellMargin = 1.0,
          upsellPrice = 2.0,
          upsellCurrency = "USD",
          recStatus = Option(1),
          recCreatedWhen = Option(now.toDate),
          recModifiedWhen = Option(now.toDate),
          scope = Scope.ITINERARY.id
        )
      )
      val actual = BookingStateMessage.mapFlightBrandSelectionForMessage(input)
      actual shouldBe expected
    }

    "map mapFlightSegmentInfoByPaxTypeForMessage" in {
      val now = DateTime.now
      val input = Seq(
        SegmentInfoByPaxType(
          referenceId = 1,
          segmentReferenceId = 1,
          segmentInfoByPaxTypeId = 1,
          flightSegmentId = 1,
          passengerType = "ADT",
          bookingClass = "K",
          fareBasisCode = "KLMNOP",
          recStatus = Some(1),
          recCreatedWhen = Some(now),
          recModifiedWhen = Some(now),
          fareRuleRevisionId = Some("KLMNOP-111")
        )
      )
      val expected = Seq(
        FlightSegmentInfoByPaxTypeForMessage(
          referenceId = 1,
          segmentReferenceId = 1,
          segmentInfoByPaxTypeId = 1,
          flightSegmentId = 1,
          passengerType = "ADT",
          bookingClass = "K",
          fareBasisCode = "KLMNOP",
          recStatus = Some(1),
          recCreatedWhen = Some(now.toDate),
          recModifiedWhen = Some(now.toDate),
          fareRuleRevisionId = "KLMNOP-111"
        )
      )
      val actual = BookingStateMessage.mapFlightSegmentInfoByPaxTypeToFlightSegmentInfoByPaxTypeForMessage(input)
      actual shouldBe expected
    }

    "map mapFlightSeatSelectionToFlightSeatSelectionTypeForMessage" in {
      val now = DateTime.now
      val input =
        Seq(
          FlightSeatSelectionDetail(
            referenceId = 1,
            segmentReferenceId = 1001,
            flightSeatSelectionId = 0,
            flightSegmentId = 0,
            flightSegmentIndex = 1,
            flightPaxId = 1,
            seatRow = "1",
            seatColumn = "A",
            flightSeatState = 1,
            recStatus = 1,
            recCreatedWhen = Some(now.toDateTime),
            recModifiedWhen = Some(now.toDateTime),
            priceAmount = Some(10.0),
            priceCurrency = Some("USD"),
            supplierData = Some("SUPPLIER_DATA"),
            isPartialSettlementRequired = Some(true)
          )
        )

      val expected = Seq(
        FlightSeatSelectionDetailForMessage(
          referenceId = 1,
          segmentReferenceId = 1001,
          flightSeatSelectionId = 0,
          flightSegmentId = 0,
          flightSegmentIndex = 1,
          flightPaxId = 1,
          seatRow = "1",
          seatColumn = "A",
          flightSeatState = 1,
          recStatus = 1,
          recCreatedWhen = Some(now.toDate),
          recModifiedWhen = Some(now.toDate),
          priceAmount = Some(10.0),
          priceCurrency = Some("USD"),
          supplierData = Some("SUPPLIER_DATA"),
          isPartialSettlementRequired = Some(true)
        )
      )
      val actual = BookingStateMessage.mapFlightSeatSelectionToFlightSeatSelectionTypeForMessage(input)
      actual shouldBe expected
    }

    "map mapFlightBaggageForMessage" in {
      val now = DateTime.now
      val input =
        Seq(
          FlightBaggage(
            referenceId = 0,
            sliceReferenceId = 0,
            flightBaggageId = 12,
            flightPaxId = 101010101,
            flightSliceId = 444555666,
            baggageTypeId = 2,
            quantity = Some(1),
            maxWeight = Some(30.0),
            maxWeightUnit = Some("KG"),
            weightLimitPerBag = Some(31.0),
            weightLimitPerBagUnit = Some("KG"),
            sizeLength = Some(100.0),
            sizeWidth = Some(25.0),
            sizeHeight = Some(50.0),
            sizeUnit = Some("IN"),
            priceAmount = 500.0,
            priceCurrency = "USD",
            supplierData = "baggageSupplierData",
            baggageStatus = 2,
            isCarryOn = false,
            recStatus = Some(1),
            recCreatedWhen = Some(now.toDateTime),
            recModifiedWhen = Some(now.toDateTime),
            scope = Scope.SLICE,
            isPartialSettlementRequired = Some(true)
          )
        )

      val expected = Seq(
        FlightBaggageForMessage(
          referenceId = 0,
          sliceReferenceId = 0,
          flightBaggageId = 12,
          flightPaxId = 101010101,
          flightSliceId = 444555666,
          baggageTypeId = 2,
          quantity = Some(1),
          maxWeight = Some(30.0),
          maxWeightUnit = Some("KG"),
          weightLimitPerBag = Some(31.0),
          weightLimitPerBagUnit = Some("KG"),
          sizeLength = Some(100.0),
          sizeWidth = Some(25.0),
          sizeHeight = Some(50.0),
          sizeUnit = Some("IN"),
          priceAmount = 500.0,
          priceCurrency = "USD",
          supplierData = "baggageSupplierData",
          baggageStatus = 2,
          isCarryOn = false,
          recStatus = Some(1),
          recCreatedWhen = Some(now.toDate),
          recModifiedWhen = Some(now.toDate),
          scope = Scope.SLICE.id,
          isPartialSettlementRequired = Some(true)
        )
      )
      val actual = BookingStateMessage.mapFlightBaggageForMessage(input)
      actual shouldBe expected
    }

    "map mapFlightBrandAttributeForMessage" in {
      val now = DateTime.now
      val input = Seq(
        FlightBrandAttribute(
          referenceId = 2,
          brandSelectionReferenceId = 3,
          flightBrandAttributeId = 2,
          flightBrandSelectionId = 3,
          brandAttributeType = "ATTRIBUTE_TYPE",
          brandAttributeInclusion = InclusionType.INCLUDED,
          brandAttributeDetailType = Option("ATTRIBUTE_DETAIL_TYPE"),
          brandAttributeDetailInclusion = Some(InclusionType.NOT_INCLUDED),
          recStatus = Option(1),
          recCreatedWhen = Option(now),
          recModifiedWhen = Option(now)
        )
      )
      val expected = Seq(
        FlightBrandAttributeForMessage(
          flightBrandAttributeId = 2,
          flightBrandSelectionId = 3,
          brandAttributeType = "ATTRIBUTE_TYPE",
          brandAttributeInclusion = InclusionType.INCLUDED.id,
          brandAttributeDetailType = Option("ATTRIBUTE_DETAIL_TYPE"),
          brandAttributeDetailInclusion = Some(InclusionType.NOT_INCLUDED.id),
          recStatus = Option(1),
          recCreatedWhen = Option(now.toDate),
          recModifiedWhen = Option(now.toDate)
        )
      )
      val actual = BookingStateMessage.mapFlightBrandAttributeForMessage(input)
      actual shouldBe expected
    }

    "map mapFlightBrandAttributeParamForMessage" in {
      val now = DateTime.now
      val input = Seq(
        FlightBrandAttributeParam(
          referenceId = 2,
          brandAttributeReferenceId = 3,
          flightBrandAttributeParamId = 2,
          flightBrandAttributeId = 3,
          name = "name",
          code = "code",
          recStatus = Option(1),
          recCreatedWhen = Option(now),
          recModifiedWhen = Option(now)
        )
      )
      val expected = Seq(
        FlightBrandAttributeParamMessage(
          flightBrandAttributeParamId = 2,
          flightBrandAttributeId = 3,
          name = "name",
          code = "code",
          recStatus = Option(1),
          recCreatedWhen = Option(now.toDate),
          recModifiedWhen = Option(now.toDate)
        )
      )
      val actual = BookingStateMessage.mapFlightBrandAttributeParamForMessage(input)
      actual shouldBe expected
    }

    "map mapFlightPaxTicketStateToFlightPaxTicketStateForMessage " in {
      val inputDateTime = DateTime.parse("2019-08-02T16:01")
      val expectedDate  = new Date(1564736460000L)

      val paxTickets = Seq(
        FlightPaxTicketState(
          ticketNumberId = 123L,
          flightPaxId = 321L,
          ticketNumber = "Test123",
          ticketType = "E",
          recStatus = Some(1),
          recCreatedWhen = Some(inputDateTime),
          recModifiedWhen = Some(inputDateTime),
          flightSegmentId = Some(333L)
        )
      )
      val expected = Seq(
        FlightPaxTicketStateForMessage(
          ticketNumberId = 123L,
          flightPaxId = 321L,
          ticketNumber = "Test123",
          ticketType = "E",
          recStatus = Some(1),
          recCreatedWhen = Some(expectedDate),
          recModifiedWhen = Some(expectedDate),
          flightSegmentId = Some(333L)
        )
      )
      val actual = BookingStateMessage.mapFlightPaxTicketStateToFlightPaxTicketStateForMessage(paxTickets)
      actual shouldBe expected
    }

    "map mapFlightBookingToFlightBookingForMessage " in {
      val flightBookingState = FlightBookingState(
        flightBookingId = 789,
        itineraryId = 123,
        multiProductId = Some(1),
        multiProductType = Some(MultiProductType.Package),
        ticketingAirline = "",
        pnr = Some("a"),
        gdsPnr = Some("aa"),
        version = 1,
        flightStateId = 0,
        isCancelled = true,
        voidWindowUntil = Some(
          new DateTime(
            Timestamp.from(Instant.parse("2014-12-03T10:15:00.00Z")).getTime
          )
        ),
        paymentModel = 1,
        supplierId = 1,
        subSupplierId = 1,
        supplierSpecificData = "",
        supplierStatusCode = Some("status"),
        supplierReasonCode = Some("reason"),
        accountingEntity = Some(com.agoda.bapi.common.model.creation.AccountingEntity.jsonToAccountingEntity(None)),
        supplierBookingId = "",
        supplierCommissionAmount = Some(20.00),
        supplierCommissionPercentage = Some(0.02),
        fraudScore = Some(1),
        fraudAction = Some(1),
        fraudCheckIp = "",
        storefrontId = 1,
        platformId = Some(1),
        languageId = Some(1),
        displayCurrency = Some("aa"),
        serverName = Some("aa"),
        cid = Some(1),
        sessionId = "",
        clientIpAddress = "",
        trackingCookieId = Some("aa"),
        trackingCookieDate = Some(
          new DateTime(
            Timestamp.from(Instant.parse("2014-12-03T10:15:00.00Z")).getTime
          )
        ),
        trackingTag = Some("aa"),
        searchId = Some(1),
        searchRequestId = Some("8463d781-6c32-4416-a253-1c9cc0c4cec2"),
        flapiItineraryId = Some("**********"),
        recCreatedWhen = Some(
          new DateTime(
            Timestamp.from(Instant.parse("2014-12-03T10:15:00.00Z")).getTime
          )
        ),
        whitelabelId = 1,
        recStatus = Some(1),
        recModifiedWhen = Some(new DateTime(Timestamp.from(Instant.parse("2014-12-03T10:15:00.00Z")).getTime)),
        rejectReasonCode = Some(419),
        flightPostbookingStateId = Some(2),
        virtualInterlining = Some(false),
        pointOfSale = Some("US"),
        experimentVariant = Some("a"),
        ancillaryData = Some("ancillaryToken"),
        tripStartDate = Some(DateTime.parse("2022-04-26")),
        tripEndDate = Some(DateTime.parse("2022-04-30")),
        freeBagScope = Some(Scope.ITINERARY),
        fareRuleScope = Some(Scope.ITINERARY),
        promotionCampaignId = Some(101),
        supplierPaymentMethod = Some(SupplierPaymentMethod.None),
        facilitationFeeWaiverReasonId = Some(1)
      )
      val expected =
        FlightBookingForMessage(
          flightBookingId = 789,
          itineraryId = 123,
          multiProductId = Some(1),
          ticketingAirline = "",
          pnr = Some("a"),
          gdsPnr = Some("aa"),
          version = 1,
          flightStateId = 0,
          isCancelled = true,
          voidWindowUntil = Some(
            new DateTime(
              Timestamp.from(Instant.parse("2014-12-03T10:15:00.00Z")).getTime
            ).toDate
          ),
          paymentModel = 1,
          supplierId = 1,
          subSupplierId = 1,
          supplierSpecificData = "",
          supplierStatusCode = Some("status"),
          supplierReasonCode = Some("reason"),
          accountingEntity = Some(
            com.agoda.bapi.common.model.booking
              .AccountingEntityForMessage(merchantOfRecord = 5632, rateContract = 30001, revenue = 5632, None)
          ),
          supplierBookingId = "",
          supplierCommissionAmount = Some(20.00),
          supplierCommissionPercentage = Some(0.02),
          fraudScore = Some(1),
          fraudAction = Some(1),
          fraudCheckIp = "",
          storefrontId = 1,
          platformId = Some(1),
          languageId = Some(1),
          displayCurrency = Some("aa"),
          serverName = Some("aa"),
          cid = Some(1),
          sessionId = "",
          clientIpAddress = "",
          trackingCookieId = Some("aa"),
          trackingCookieDate = Some(
            new DateTime(
              Timestamp.from(Instant.parse("2014-12-03T10:15:00.00Z")).getTime
            ).toDate
          ),
          trackingTag = Some("aa"),
          searchId = Some(1),
          searchRequestId = Some("8463d781-6c32-4416-a253-1c9cc0c4cec2"),
          flapiItineraryId = Some("**********"),
          recCreatedWhen = Some(
            new DateTime(
              Timestamp.from(Instant.parse("2014-12-03T10:15:00.00Z")).getTime
            ).toDate
          ),
          whitelabelId = 1,
          recStatus = Some(1),
          recModifiedWhen = Some(new DateTime(Timestamp.from(Instant.parse("2014-12-03T10:15:00.00Z")).getTime).toDate),
          rejectReasonCode = Some(419),
          virtualInterlining = Some(false),
          pointOfSale = Some("US"),
          experimentVariant = Some("a"),
          tripStartDate = Some(DateTime.parse("2022-04-26").toDate),
          tripEndDate = Some(DateTime.parse("2022-04-30").toDate),
          commonBookingInfo = None,
          commonBookingEventsInfo = None,
          freeBagScope = Some(0),
          fareRuleScope = Some(0),
          promotionCampaignId = Some(101),
          supplierPaymentMethod = Some(0),
          flightPostbookingStateId = Some(2),
          facilitationFeeWaiverReasonId = Some(1)
        )

      val actual = BookingStateMessage.mapFlightBookingToFlightBookingForMessage(bookings = Seq(flightBookingState))
      actual shouldBe Seq(expected)
    }

    "map mapFlightBookingToExpTeamFlightBookingForMessage " in {
      val flightBookingState = FlightBookingState(
        flightBookingId = 789,
        itineraryId = 123,
        multiProductId = Some(1),
        multiProductType = Some(MultiProductType.Package),
        ticketingAirline = "",
        pnr = Some("a"),
        gdsPnr = Some("aa"),
        version = 1,
        flightStateId = 0,
        isCancelled = true,
        voidWindowUntil = Some(
          new DateTime(
            Timestamp.from(Instant.parse("2014-12-03T10:15:00.00Z")).getTime
          )
        ),
        paymentModel = 1,
        supplierId = 1,
        subSupplierId = 1,
        supplierSpecificData = "",
        supplierStatusCode = Some("status"),
        supplierReasonCode = Some("reason"),
        accountingEntity = Some(com.agoda.bapi.common.model.creation.AccountingEntity.jsonToAccountingEntity(None)),
        supplierBookingId = "",
        supplierCommissionAmount = Some(20.00),
        supplierCommissionPercentage = Some(0.02),
        fraudScore = Some(1),
        fraudAction = Some(1),
        fraudCheckIp = "",
        storefrontId = 1,
        platformId = Some(1),
        languageId = Some(1),
        displayCurrency = Some("aa"),
        serverName = Some("aa"),
        cid = Some(1),
        sessionId = "",
        clientIpAddress = "",
        trackingCookieId = Some("aa"),
        trackingCookieDate = Some(
          new DateTime(
            Timestamp.from(Instant.parse("2014-12-03T10:15:00.00Z")).getTime
          )
        ),
        trackingTag = Some("aa"),
        searchId = Some(1),
        searchRequestId = Some("8463d781-6c32-4416-a253-1c9cc0c4cec2"),
        flapiItineraryId = Some("**********"),
        recCreatedWhen = Some(
          new DateTime(
            Timestamp.from(Instant.parse("2014-12-03T10:15:00.00Z")).getTime
          )
        ),
        whitelabelId = 1,
        recStatus = Some(1),
        recModifiedWhen = Some(new DateTime(Timestamp.from(Instant.parse("2014-12-03T10:15:00.00Z")).getTime)),
        rejectReasonCode = Some(419),
        flightPostbookingStateId = Some(0),
        virtualInterlining = Some(false),
        pointOfSale = Some("US"),
        experimentVariant = Some("a"),
        ancillaryData = Some("ancillaryToken"),
        tripStartDate = Some(DateTime.parse("2022-04-26")),
        tripEndDate = Some(DateTime.parse("2022-04-30")),
        freeBagScope = Some(Scope.ITINERARY),
        fareRuleScope = Some(Scope.ITINERARY),
        promotionCampaignId = Some(101),
        facilitationFeeWaiverReasonId = Some(1)
      )
      val expected =
        FlightBookingExperimentForMessage(
          trackingCookieId = Some("aa"),
          recCreatedWhen = Some(
            new DateTime(
              Timestamp.from(Instant.parse("2014-12-03T10:15:00.00Z")).getTime
            ).toDate
          )
        )

      val actual =
        BookingStateMessage.mapFlightBookingToExpTeamFlightBookingForMessage(bookings = Seq(flightBookingState))
      actual shouldBe Seq(expected)
    }

    "map mapMultiProductBookingGroupsToMessage" in {
      val multiProductGroups =
        Seq(MultiProductBookingGroupDBModel(bookingId = 11L, itineraryId = 12L, cartId = 1L, packageId = Some(5L)))

      val expected =
        Some(
          Seq(
            MultiProductBookingGroupModelMessage(bookingId = 11L, itineraryId = 12L, cartId = 1L, packageId = Some(5L))
          )
        )

      val actual = BookingStateMessage.mapMultiProductBookingGroupsToMessage(multiProductGroups)

      actual shouldBe expected
    }

    "map ActivityProtoState correctly" in {
      val actionTypeId                    = 1
      val actionId                        = 1
      val bookingType                     = Some(1)
      implicit val arbitraryLocalDateTime = Arbitrary(LocalDateTime.now())
      implicit val arbitraryJodaDateTime  = Arbitrary(DateTime.now)
      implicit val arbitraryTimeOfDay     = Arbitrary(TimeOfDay(9, 0, 0))

      val mockActivityProductModel = ActivityProductModel(
        product = BaseProductModel(
          booking = random[BaseBooking],
          bookingGeos = Seq(random[BaseBookingGeo]),
          bookingMetas = Seq(random[BaseBookingMeta]),
          bookingOffers = Seq(random[BaseBookingOffer]),
          bookingPaxes = Seq(random[BaseBookingPax]),
          bookingPriceSummaries = Seq(random[BaseBookingPriceSummary]),
          cancellationInfo = Some(random[BaseCancellationInfo]),
          clientInfo = Some(random[BaseClientInfo]),
          financeInfo = Some(random[BaseFinanceInfo]),
          fraudInfo = Some(random[BaseFraudInfo]),
          supplierInfo = Some(random[BaseSupplierInfo])
        )
      )

      val mockActivityState: ActivityBookingStateWithItinerary = ActivityBookingStateWithItinerary(
        itinerary = random[MultiProductItinerary],
        itineraryHistories = Seq(random[ItineraryHistory]),
        payments = Seq(random[PaymentState]),
        bookingPayments = Seq(random[BookingPaymentState]),
        activities = Seq(mockActivityProductModel),
        multiProductBookingGroups = multiProductBookingGroupDBModel,
        relationships = Nil
      )

      val mockMultiProductInfos  = Seq(MultiProductInfoDBModel(1, MultiProductType.SingleActivity))
      val expectedPayment        = mockActivityState.payments.head
      val expectedBookingPayment = mockActivityState.bookingPayments.head
      val expectedHistory        = mockActivityState.itineraryHistories.head

      val expected = BookingStateMessage(
        actionType = actionTypeId,
        actionId = actionId,
        bookingType = bookingType,
        bookingId = 0,
        schemaVersion = "1",
        flights = Seq.empty,
        slices = Seq.empty,
        segments = Seq.empty,
        passengers = Seq.empty,
        payments = Seq(
          PaymentForMessage(
            referenceId = expectedPayment.referenceId,
            paymentId = expectedPayment.paymentId,
            itineraryId = expectedPayment.itineraryId,
            actionId = expectedPayment.actionId,
            creditCardId = expectedPayment.creditCardId,
            transactionDate = expectedPayment.transactionDate.toDate,
            transactionType = expectedPayment.transactionType,
            paymentState = expectedPayment.paymentState,
            referenceNo = expectedPayment.referenceNo,
            referenceType = expectedPayment.referenceType,
            last4Digits = expectedPayment.last4Digits,
            paymentMethodId = expectedPayment.paymentMethodId,
            gatewayId = expectedPayment.gatewayId,
            transactionId = expectedPayment.transactionId,
            paymentCurrency = expectedPayment.paymentCurrency,
            paymentAmount = expectedPayment.paymentAmount,
            amountUsd = expectedPayment.amountUsd,
            supplierCurrency = expectedPayment.supplierCurrency,
            supplierAmount = expectedPayment.supplierAmount,
            exchangeRateSupplierToPayment = expectedPayment.exchangeRateSupplierToPayment,
            creditCardCurrency = expectedPayment.creditCardCurrency,
            upliftAmount = expectedPayment.upliftAmount,
            siteExchangeRate = expectedPayment.siteExchangeRate,
            upliftExchangeRate = expectedPayment.upliftExchangeRate,
            remark = expectedPayment.remark,
            paymentTypeId = expectedPayment.paymentTypeId,
            token = expectedPayment.token,
            recStatus = expectedPayment.recStatus,
            recCreatedWhen = expectedPayment.recCreatedWhen.map(_.toDate),
            referencePaymentId = expectedPayment.referencePaymentId,
            points = expectedPayment.points
          )
        ),
        bookingPayments = Seq(
          BookingPaymentForMessage(
            paymentId = expectedBookingPayment.paymentId,
            bookingId = expectedBookingPayment.bookingId,
            paymentCurrency = expectedBookingPayment.paymentCurrency,
            paymentAmount = expectedBookingPayment.paymentAmount.toDouble,
            amountUsd = expectedBookingPayment.amountUsd.toDouble,
            recStatus = expectedBookingPayment.recStatus,
            recCreatedWhen = expectedBookingPayment.recCreatedWhen.map(_.toDate),
            fxiUplift = expectedBookingPayment.fxiUplift.map(_.toDouble),
            loyaltyPoints = expectedBookingPayment.loyaltyPoints,
            supplierCurrency = expectedBookingPayment.supplierCurrency,
            supplierExchangeRate = expectedBookingPayment.supplierExchangeRate.map(_.toDouble),
            bookingPaymentId = expectedBookingPayment.bookingPaymentId
          )
        ),
        bookingRelationships = Seq.empty,
        breakdown = Seq.empty,
        breakdownPerPax = Seq.empty,
        baggageAllowance = Seq.empty,
        baggage = Seq.empty,
        history = Seq(
          ItineraryHistoryForMessage(
            actionId = expectedHistory.actionId,
            itineraryId = expectedHistory.itineraryId.toInt,
            bookingType = expectedHistory.bookingType,
            bookingId = expectedHistory.bookingId,
            actionType = expectedHistory.actionType,
            version = expectedHistory.version,
            actionDate = expectedHistory.actionDate.toDate,
            parameters = expectedHistory.parameters,
            description = expectedHistory.description,
            recStatus = expectedHistory.recStatus,
            recCreatedWhen = expectedHistory.recCreatedWhen.map(_.toDate)
          )
        ),
        summary = Seq.empty,
        paxTickets = Seq.empty,
        itinerary = FlightItineraryForMessage(
          itineraryId = mockActivityState.itinerary.itineraryId,
          memberId = mockActivityState.itinerary.memberId,
          recStatus = mockActivityState.itinerary.recStatus,
          recCreatedWhen = mockActivityState.itinerary.recCreatedWhen.map(_.toDate),
          recModifiedWhen = mockActivityState.itinerary.recModifiedWhen.map(_.toDate)
        ),
        userAgent = None,
        bookingAttribution = Seq.empty,
        itineraryDate = new Date(),
        protectionModels = None,
        multiProductInfos =
          Some(mockMultiProductInfos.map(pf => MultiProductInfoForMessage(pf.multiProductId, pf.multiProductType.id))),
        flightSegmentInfoByPaxType = Seq.empty,
        segmentInfoByPaxType = Seq.empty,
        fareRulePolicies = None,
        flightSeatSelection = Seq.empty,
        vehicle = None,
        activities = Some(
          Seq(
            ActivityForMessage(
              ProtoConverter.protoToString(mockActivityState.activities.head),
              mockActivityProductModel.product.booking.bookingId
            )
          )
        ),
        properties = None,
        cegFastTracks = None,
        addOns = None,
        multiProductBookingGroups = Some(expectedMultiProductBookingGroupModelMessage),
        flightBrandSelections = None,
        flightBrandAttributes = None,
        flightBrandAttributeParams = None,
        flightBaseBooking = None,
        flightBaseCancellationInfo = None,
        crossProductIsolatedFeature = None
      )

      val result = BookingStateMessage.fromActivityProtoState(
        ActionType(actionTypeId),
        actionTypeId,
        bookingType,
        mockActivityState,
        mockMultiProductInfos
      )
      result shouldBe expected.copy(itineraryDate = result.itineraryDate)
    }

    "map CegFastTrackProto correctly" in {
      val actionTypeId                    = 1
      val actionId                        = 1
      val bookingType                     = Some(1)
      implicit val arbitraryLocalDateTime = Arbitrary(LocalDateTime.now())
      implicit val arbitraryJodaDateTime  = Arbitrary(DateTime.now)
      implicit val arbitraryTimeOfDay     = Arbitrary(TimeOfDay(9, 0, 0))

      val mockCegFastTrackProductModel = CegFastTrackProductModel(
        product = BaseProductModel(
          booking = random[BaseBooking],
          bookingGeos = Seq(random[BaseBookingGeo]),
          bookingMetas = Seq(random[BaseBookingMeta]),
          bookingOffers = Seq(random[BaseBookingOffer]),
          bookingPaxes = Seq(random[BaseBookingPax]),
          bookingPriceSummaries = Seq(random[BaseBookingPriceSummary]),
          cancellationInfo = Some(random[BaseCancellationInfo]),
          clientInfo = Some(random[BaseClientInfo]),
          financeInfo = Some(random[BaseFinanceInfo]),
          fraudInfo = Some(random[BaseFraudInfo]),
          supplierInfo = Some(random[BaseSupplierInfo]),
          essInfo = Some(random[BaseBookingEssInfo])
        )
      )

      val mockCegFastTrackState = CegFastTrackBookingStateWithItinerary(
        itinerary = random[MultiProductItinerary],
        itineraryHistories = Seq(random[ItineraryHistory]),
        payments = Seq(random[PaymentState]),
        relationships = Seq(random[BaseBookingRelationshipInternal]),
        bookingPayments = Seq(random[BookingPaymentState]),
        cegFastTracks = Seq(mockCegFastTrackProductModel),
        multiProductBookingGroups = multiProductBookingGroupDBModel
      )

      val mockMultiProductInfos        = Seq(MultiProductInfoDBModel(1, MultiProductType.CEGFastTrack))
      val expectedPayment              = mockCegFastTrackState.payments.head
      val expectedBookingPayment       = mockCegFastTrackState.bookingPayments.head
      val expectedBookingRelationships = mockCegFastTrackState.relationships.head
      val expectedHistory              = mockCegFastTrackState.itineraryHistories.head

      val expected = BookingStateMessage(
        actionType = actionTypeId,
        actionId = actionId,
        bookingType = bookingType,
        bookingId = 0,
        schemaVersion = "1",
        flights = Seq.empty,
        slices = Seq.empty,
        segments = Seq.empty,
        passengers = Seq.empty,
        payments = Seq(
          PaymentForMessage(
            referenceId = expectedPayment.referenceId,
            paymentId = expectedPayment.paymentId,
            itineraryId = expectedPayment.itineraryId,
            actionId = expectedPayment.actionId,
            creditCardId = expectedPayment.creditCardId,
            transactionDate = expectedPayment.transactionDate.toDate,
            transactionType = expectedPayment.transactionType,
            paymentState = expectedPayment.paymentState,
            referenceNo = expectedPayment.referenceNo,
            referenceType = expectedPayment.referenceType,
            last4Digits = expectedPayment.last4Digits,
            paymentMethodId = expectedPayment.paymentMethodId,
            gatewayId = expectedPayment.gatewayId,
            transactionId = expectedPayment.transactionId,
            paymentCurrency = expectedPayment.paymentCurrency,
            paymentAmount = expectedPayment.paymentAmount,
            amountUsd = expectedPayment.amountUsd,
            supplierCurrency = expectedPayment.supplierCurrency,
            supplierAmount = expectedPayment.supplierAmount,
            exchangeRateSupplierToPayment = expectedPayment.exchangeRateSupplierToPayment,
            creditCardCurrency = expectedPayment.creditCardCurrency,
            upliftAmount = expectedPayment.upliftAmount,
            siteExchangeRate = expectedPayment.siteExchangeRate,
            upliftExchangeRate = expectedPayment.upliftExchangeRate,
            remark = expectedPayment.remark,
            paymentTypeId = expectedPayment.paymentTypeId,
            token = expectedPayment.token,
            recStatus = expectedPayment.recStatus,
            recCreatedWhen = expectedPayment.recCreatedWhen.map(_.toDate),
            referencePaymentId = expectedPayment.referencePaymentId,
            points = expectedPayment.points
          )
        ),
        bookingPayments = Seq(
          BookingPaymentForMessage(
            paymentId = expectedBookingPayment.paymentId,
            bookingId = expectedBookingPayment.bookingId,
            paymentCurrency = expectedBookingPayment.paymentCurrency,
            paymentAmount = expectedBookingPayment.paymentAmount.toDouble,
            amountUsd = expectedBookingPayment.amountUsd.toDouble,
            recStatus = expectedBookingPayment.recStatus,
            recCreatedWhen = expectedBookingPayment.recCreatedWhen.map(_.toDate),
            fxiUplift = expectedBookingPayment.fxiUplift.map(_.toDouble),
            loyaltyPoints = expectedBookingPayment.loyaltyPoints,
            supplierCurrency = expectedBookingPayment.supplierCurrency,
            supplierExchangeRate = expectedBookingPayment.supplierExchangeRate.map(_.toDouble),
            bookingPaymentId = expectedBookingPayment.bookingPaymentId
          )
        ),
        bookingRelationships =
          mapBaseBookingRelationshipToBaseBookingRelationshipForMessage(Seq(expectedBookingRelationships)),
        breakdown = Seq.empty,
        breakdownPerPax = Seq.empty,
        baggageAllowance = Seq.empty,
        baggage = Seq.empty,
        history = Seq(
          ItineraryHistoryForMessage(
            actionId = expectedHistory.actionId,
            itineraryId = expectedHistory.itineraryId.toInt,
            bookingType = expectedHistory.bookingType,
            bookingId = expectedHistory.bookingId,
            actionType = expectedHistory.actionType,
            version = expectedHistory.version,
            actionDate = expectedHistory.actionDate.toDate,
            parameters = expectedHistory.parameters,
            description = expectedHistory.description,
            recStatus = expectedHistory.recStatus,
            recCreatedWhen = expectedHistory.recCreatedWhen.map(_.toDate)
          )
        ),
        summary = Seq.empty,
        paxTickets = Seq.empty,
        itinerary = FlightItineraryForMessage(
          itineraryId = mockCegFastTrackState.itinerary.itineraryId,
          memberId = mockCegFastTrackState.itinerary.memberId,
          recStatus = mockCegFastTrackState.itinerary.recStatus,
          recCreatedWhen = mockCegFastTrackState.itinerary.recCreatedWhen.map(_.toDate),
          recModifiedWhen = mockCegFastTrackState.itinerary.recModifiedWhen.map(_.toDate)
        ),
        userAgent = None,
        bookingAttribution = Seq.empty,
        itineraryDate = new Date(),
        protectionModels = None,
        multiProductInfos =
          Some(mockMultiProductInfos.map(pf => MultiProductInfoForMessage(pf.multiProductId, pf.multiProductType.id))),
        flightSegmentInfoByPaxType = Seq.empty,
        segmentInfoByPaxType = Seq.empty,
        fareRulePolicies = None,
        flightSeatSelection = Seq.empty,
        vehicle = None,
        activities = None,
        properties = None,
        cegFastTracks = Some(
          Seq(
            CegFastTrackForMessage(
              ProtoConverter.protoToString(mockCegFastTrackState.cegFastTracks.head),
              mockCegFastTrackProductModel.product.booking.bookingId
            )
          )
        ),
        addOns = None,
        multiProductBookingGroups = Some(expectedMultiProductBookingGroupModelMessage),
        flightBrandSelections = None,
        flightBrandAttributes = None,
        flightBrandAttributeParams = None,
        flightBaseBooking = None,
        flightBaseCancellationInfo = None,
        crossProductIsolatedFeature = None
      )

      val result = BookingStateMessage.fromCegFastTrackProtoState(
        ActionType(actionTypeId),
        actionTypeId,
        bookingType,
        mockCegFastTrackState,
        mockMultiProductInfos
      )

      result shouldBe expected.copy(itineraryDate = result.itineraryDate)
    }

    "map AddOnProto correctly" in {
      val actionTypeId                    = 1
      val actionId                        = 1
      val bookingType                     = Some(1)
      implicit val arbitraryLocalDateTime = Arbitrary(LocalDateTime.now())
      implicit val arbitraryJodaDateTime  = Arbitrary(DateTime.now)
      implicit val arbitraryTimeOfDay     = Arbitrary(TimeOfDay(9, 0, 0))

      val mockAddOnProductModel = AddOnProductModel(
        product = BaseProductModel(
          booking = random[BaseBooking],
          bookingGeos = Seq(random[BaseBookingGeo]),
          bookingMetas = Seq(random[BaseBookingMeta]),
          bookingOffers = Seq(random[BaseBookingOffer]),
          bookingPaxes = Seq(random[BaseBookingPax]),
          bookingPriceSummaries = Seq(random[BaseBookingPriceSummary]),
          cancellationInfo = Some(random[BaseCancellationInfo]),
          clientInfo = Some(random[BaseClientInfo]),
          financeInfo = Some(random[BaseFinanceInfo]),
          fraudInfo = Some(random[BaseFraudInfo]),
          supplierInfo = Some(random[BaseSupplierInfo]),
          essInfo = Some(random[BaseBookingEssInfo])
        )
      )

      val mockAddOnState = AddOnBookingStateWithItinerary(
        itinerary = random[MultiProductItinerary],
        itineraryHistories = Seq(random[ItineraryHistory]),
        payments = Seq(random[PaymentState]),
        relationships = Seq(random[BaseBookingRelationshipInternal]),
        bookingPayments = Seq(random[BookingPaymentState]),
        addOns = Seq(mockAddOnProductModel),
        multiProductBookingGroups = multiProductBookingGroupDBModel
      )

      val mockMultiProductInfos        = Seq(MultiProductInfoDBModel(1, MultiProductType.SingleProtection))
      val expectedPayment              = mockAddOnState.payments.head
      val expectedBookingPayment       = mockAddOnState.bookingPayments.head
      val expectedBookingRelationships = mockAddOnState.relationships.head
      val expectedHistory              = mockAddOnState.itineraryHistories.head
      val expectedAddOnProto = Some(
        Seq(
          AddOnForMessage(
            ProtoConverter.protoToString(mockAddOnState.addOns.head),
            mockAddOnProductModel.product.booking.bookingId,
            mockAddOnProductModel.product.booking.productTypeId
          )
        )
      )

      val expected = BookingStateMessage(
        actionType = actionTypeId,
        actionId = actionId,
        bookingType = bookingType,
        bookingId = 0,
        schemaVersion = "1",
        flights = Seq.empty,
        slices = Seq.empty,
        segments = Seq.empty,
        passengers = Seq.empty,
        payments = Seq(
          PaymentForMessage(
            referenceId = expectedPayment.referenceId,
            paymentId = expectedPayment.paymentId,
            itineraryId = expectedPayment.itineraryId,
            actionId = expectedPayment.actionId,
            creditCardId = expectedPayment.creditCardId,
            transactionDate = expectedPayment.transactionDate.toDate,
            transactionType = expectedPayment.transactionType,
            paymentState = expectedPayment.paymentState,
            referenceNo = expectedPayment.referenceNo,
            referenceType = expectedPayment.referenceType,
            last4Digits = expectedPayment.last4Digits,
            paymentMethodId = expectedPayment.paymentMethodId,
            gatewayId = expectedPayment.gatewayId,
            transactionId = expectedPayment.transactionId,
            paymentCurrency = expectedPayment.paymentCurrency,
            paymentAmount = expectedPayment.paymentAmount,
            amountUsd = expectedPayment.amountUsd,
            supplierCurrency = expectedPayment.supplierCurrency,
            supplierAmount = expectedPayment.supplierAmount,
            exchangeRateSupplierToPayment = expectedPayment.exchangeRateSupplierToPayment,
            creditCardCurrency = expectedPayment.creditCardCurrency,
            upliftAmount = expectedPayment.upliftAmount,
            siteExchangeRate = expectedPayment.siteExchangeRate,
            upliftExchangeRate = expectedPayment.upliftExchangeRate,
            remark = expectedPayment.remark,
            paymentTypeId = expectedPayment.paymentTypeId,
            token = expectedPayment.token,
            recStatus = expectedPayment.recStatus,
            recCreatedWhen = expectedPayment.recCreatedWhen.map(_.toDate),
            referencePaymentId = expectedPayment.referencePaymentId,
            points = expectedPayment.points
          )
        ),
        bookingPayments = Seq(
          BookingPaymentForMessage(
            paymentId = expectedBookingPayment.paymentId,
            bookingId = expectedBookingPayment.bookingId,
            paymentCurrency = expectedBookingPayment.paymentCurrency,
            paymentAmount = expectedBookingPayment.paymentAmount.toDouble,
            amountUsd = expectedBookingPayment.amountUsd.toDouble,
            recStatus = expectedBookingPayment.recStatus,
            recCreatedWhen = expectedBookingPayment.recCreatedWhen.map(_.toDate),
            fxiUplift = expectedBookingPayment.fxiUplift.map(_.toDouble),
            loyaltyPoints = expectedBookingPayment.loyaltyPoints,
            supplierCurrency = expectedBookingPayment.supplierCurrency,
            supplierExchangeRate = expectedBookingPayment.supplierExchangeRate.map(_.toDouble),
            bookingPaymentId = expectedBookingPayment.bookingPaymentId
          )
        ),
        bookingRelationships =
          mapBaseBookingRelationshipToBaseBookingRelationshipForMessage(Seq(expectedBookingRelationships)),
        breakdown = Seq.empty,
        breakdownPerPax = Seq.empty,
        baggageAllowance = Seq.empty,
        baggage = Seq.empty,
        history = Seq(
          ItineraryHistoryForMessage(
            actionId = expectedHistory.actionId,
            itineraryId = expectedHistory.itineraryId.toInt,
            bookingType = expectedHistory.bookingType,
            bookingId = expectedHistory.bookingId,
            actionType = expectedHistory.actionType,
            version = expectedHistory.version,
            actionDate = expectedHistory.actionDate.toDate,
            parameters = expectedHistory.parameters,
            description = expectedHistory.description,
            recStatus = expectedHistory.recStatus,
            recCreatedWhen = expectedHistory.recCreatedWhen.map(_.toDate)
          )
        ),
        summary = Seq.empty,
        paxTickets = Seq.empty,
        itinerary = FlightItineraryForMessage(
          itineraryId = mockAddOnState.itinerary.itineraryId,
          memberId = mockAddOnState.itinerary.memberId,
          recStatus = mockAddOnState.itinerary.recStatus,
          recCreatedWhen = mockAddOnState.itinerary.recCreatedWhen.map(_.toDate),
          recModifiedWhen = mockAddOnState.itinerary.recModifiedWhen.map(_.toDate)
        ),
        userAgent = None,
        bookingAttribution = Seq.empty,
        itineraryDate = new Date(),
        protectionModels = None,
        multiProductInfos =
          Some(mockMultiProductInfos.map(pf => MultiProductInfoForMessage(pf.multiProductId, pf.multiProductType.id))),
        flightSegmentInfoByPaxType = Seq.empty,
        segmentInfoByPaxType = Seq.empty,
        fareRulePolicies = None,
        flightSeatSelection = Seq.empty,
        vehicle = None,
        activities = None,
        properties = None,
        cegFastTracks = None,
        addOns = expectedAddOnProto,
        multiProductBookingGroups = Some(expectedMultiProductBookingGroupModelMessage),
        flightBrandSelections = None,
        flightBrandAttributes = None,
        flightBrandAttributeParams = None,
        flightBaseBooking = None,
        flightBaseCancellationInfo = None,
        crossProductIsolatedFeature = None
      )

      val result = BookingStateMessage.fromAddOnProtoState(
        ActionType(actionTypeId),
        actionTypeId,
        bookingType,
        mockAddOnState,
        mockMultiProductInfos
      )

      result shouldBe expected.copy(itineraryDate = result.itineraryDate)
    }

    "map BaseBookingRelationshipInternal correctly" in {
      val dateTime = DateTime.now()
      val input = Seq(
        BaseBookingRelationshipInternal(
          sourceBookingId = 1,
          targetBookingId = 2,
          relationshipStatusId = 3,
          relationshipTypeId = 4,
          recStatus = 5,
          recCreatedWhen = dateTime,
          recCreatedBy = "ABC",
          recModifiedWhen = Some(dateTime.plusDays(1)),
          recModifiedBy = Some("XYZ"),
          relationshipId = 6
        )
      )

      val expected = Seq(
        BaseBookingRelationshipForMessage(
          sourceBookingId = 1,
          targetBookingId = 2,
          relationshipStatusId = 3,
          relationshipTypeId = 4,
          recStatus = 5,
          recCreatedWhen = dateTime.toDate,
          recCreatedBy = "ABC",
          recModifiedWhen = Some(dateTime.plusDays(1).toDate),
          recModifiedBy = Some("XYZ"),
          relationshipId = 6
        )
      )

      val actual = BookingStateMessage.mapBaseBookingRelationshipToBaseBookingRelationshipForMessage(input)
      actual shouldBe expected
    }

    "map PostBookingFee correctly" in {
      val input = PostBookingFee(
        feeType = FeeTypeId.POST_BOOKING_FEE,
        currencyCode = "THB",
        amount = 3571.43,
        exchangeRate = 0.028,
        usdAmount = 100
      )
      val expected = PostBookingFeeMessage(
        feeType = FeeTypeId.POST_BOOKING_FEE.id,
        currencyCode = "THB",
        amount = 3571.43,
        exchangeRate = 0.028,
        usdAmount = 100
      )
      val actual = BookingStateMessage.mapPostBookingFee(input)
      actual shouldBe expected
    }

    "map BaseBookingModelInternal correctly" in {
      val dateTime = DateTime.now()
      val input = BaseBookingModelInternal(
        bookingId = 1,
        itineraryId = 1,
        multiProductId = 1,
        productId = 2,
        bookingDate = dateTime,
        bookingStartDate = dateTime.plusDays(5),
        bookingEndDate = Some(dateTime.plusDays(6)),
        bookingConfirmationDate = Some(dateTime.plusDays(1)),
        isTestBooking = false,
        paymentModel = 0,
        bookingStateId = 1,
        postBookingStateId = Some(1),
        rejectReasonCode = None,
        rejectReasonMsg = None,
        recStatus = 1,
        recCreatedWhen = dateTime,
        recCreatedBy = "ABC",
        recModifiedWhen = Some(dateTime.plusDays(2)),
        recModifiedBy = Some("XYZ"),
        productTypeId = ProductTypeEnum.Activity.id
      )

      val expected = BaseBookingMessage(
        bookingId = 1,
        itineraryId = 1,
        multiProductId = 1,
        productId = 2,
        bookingDate = LocalDateTimeUtil.toJodaLocalDateTime(dateTime.toString).toDate,
        bookingStartDate = LocalDateTimeUtil.toJodaLocalDateTime(dateTime.plusDays(5).toString).toDate,
        bookingEndDate = Some(LocalDateTimeUtil.toJodaLocalDateTime(dateTime.plusDays(6).toString).toDate),
        bookingConfirmationDate = Some(LocalDateTimeUtil.toJodaLocalDateTime(dateTime.plusDays(1).toString).toDate),
        isTestBooking = false,
        paymentModel = 0,
        bookingStateId = 1,
        postBookingStateId = Some(1),
        rejectReasonCode = None,
        rejectReasonMsg = None,
        recStatus = 1,
        recCreatedWhen = LocalDateTimeUtil.toJodaLocalDateTime(dateTime.toString).toDate,
        recCreatedBy = "ABC",
        recModifiedWhen = Some(LocalDateTimeUtil.toJodaLocalDateTime(dateTime.plusDays(2).toString).toDate),
        recModifiedBy = Some("XYZ"),
        productTypeId = ProductTypeEnum.Activity.id
      )
      val actual = BookingStateMessage.mapBaseBookingModelInternalMessage(input)
      actual shouldBe expected
    }

    "map BaseCancellationInfoModelInternal correctly" in {
      val dateTime = DateTime.now()
      val input = BaseCancellationInfoModelInternal(
        bookingId = 1,
        isCancelled = 1,
        cancellationPolicyCode = Some("code"),
        cancellationDate = Some(dateTime.plusDays(3).toString),
        voidWindowUntil = None,
        recCreatedWhen = Some(dateTime.toString),
        recCreatedBy = Some("ABC"),
        recModifiedWhen = None,
        recModifiedBy = None,
        rejectReason = None,
        cancellationReason = Some("reason"),
        supplierCancellationInfo = Some("supplierInfo"),
        cancelledOnSupplier = Some(true),
        isForcedRefund = None,
        forcedRefundReason = None
      )

      val expected = BaseCancellationInfoMessage(
        bookingId = 1,
        isCancelled = 1,
        cancellationPolicyCode = Some("code"),
        cancellationDate = Some(LocalDateTimeUtil.toJodaLocalDateTime(dateTime.plusDays(3).toString).toDate),
        voidWindowUntil = None,
        recCreatedWhen = Some(LocalDateTimeUtil.toJodaLocalDateTime(dateTime.toString).toDate),
        recCreatedBy = Some("ABC"),
        recModifiedWhen = None,
        recModifiedBy = None,
        rejectReason = None,
        cancellationReason = Some("reason"),
        supplierCancellationInfo = Some("supplierInfo"),
        cancelledOnSupplier = Some(true),
        isForcedRefund = None,
        forcedRefundReason = None
      )
      val actual = BookingStateMessage.mapBaseCancellationInfoModelInternalMessage(input)
      actual shouldBe expected
    }

    "map ItineraryModel correctly" in {
      val actionTypeId     = 1
      val actionId         = 1
      val bookingType      = Some(1)
      val multiProductInfo = Seq(MultiProductInfoDBModel(1, MultiProductType.SingleProperty))

      val inputItinerary = MultiProductItinerary(itineraryId = 1, memberId = 1, recCreatedWhen = Some(DateTime.now()))
      val inputHistory =
        ItineraryHistory(
          actionId = 1,
          itineraryId = 2,
          bookingType = Some(3),
          bookingId = Some(123),
          actionType = 1,
          version = 0,
          actionDate = DateTime.now(),
          parameters = "mockParam",
          description = "mockDescription",
          recStatus = Some(3),
          recCreatedWhen = Some(DateTime.now()),
          replicatedFromDC = Some("HK")
        )
      val inputPayment =
        PaymentState(
          referenceId = 1,
          paymentId = 1,
          itineraryId = 1,
          actionId = Some(1),
          creditCardId = Some(1),
          transactionDate = DateTime.now(),
          transactionType = 1,
          paymentState = 1,
          referenceNo = "1",
          referenceType = 1,
          last4Digits = "1234",
          paymentMethodId = 1,
          gatewayId = 1,
          transactionId = "1251",
          paymentCurrency = "USD",
          paymentAmount = 20.2,
          amountUsd = 20.2,
          supplierCurrency = "USD",
          supplierAmount = 0.0,
          exchangeRateSupplierToPayment = 0.0,
          creditCardCurrency = "USD",
          upliftAmount = 0.0,
          siteExchangeRate = 0.0,
          upliftExchangeRate = 0.0,
          paymentTypeId = Some(1),
          token = Some("mockToken"),
          remark = Some("mockRemark"),
          installmentPlanId = None,
          recStatus = None,
          recCreatedWhen = None,
          referencePaymentId = None,
          points = None
        )

      val inputBookingPayment = BookingPaymentState(
        paymentId = 1234,
        bookingId = 456,
        paymentCurrency = "USD",
        paymentAmount = 100,
        amountUsd = 100
      )

      val inputItineraryModel = ItineraryInternalModel(
        itinerary = inputItinerary,
        history = Seq(inputHistory),
        payments = Seq(inputPayment),
        bookingPayments = Seq(inputBookingPayment),
        transientCCId = None,
        relationships = Seq.empty,
        multiProductBookingGroups = multiProductBookingGroupDBModel,
        ccToken = None
      )

      val expected = BookingStateMessage(
        actionType = actionTypeId,
        actionId = actionId,
        bookingType = bookingType,
        bookingId = 0, // legacy field, we replicate itinerary which can contain multiple bookingIds
        schemaVersion = "1",
        flights = Seq.empty,
        slices = Seq.empty,
        segments = Seq.empty,
        passengers = Seq.empty,
        payments = Seq(
          PaymentForMessage(
            referenceId = inputPayment.referenceId,
            paymentId = inputPayment.paymentId,
            itineraryId = inputPayment.itineraryId,
            actionId = inputPayment.actionId,
            creditCardId = inputPayment.creditCardId,
            transactionDate = inputPayment.transactionDate.toDate,
            transactionType = inputPayment.transactionType,
            paymentState = inputPayment.paymentState,
            referenceNo = inputPayment.referenceNo,
            referenceType = inputPayment.referenceType,
            last4Digits = inputPayment.last4Digits,
            paymentMethodId = inputPayment.paymentMethodId,
            gatewayId = inputPayment.gatewayId,
            transactionId = inputPayment.transactionId,
            paymentCurrency = inputPayment.paymentCurrency,
            paymentAmount = inputPayment.paymentAmount,
            amountUsd = inputPayment.amountUsd,
            supplierCurrency = inputPayment.supplierCurrency,
            supplierAmount = inputPayment.supplierAmount,
            exchangeRateSupplierToPayment = inputPayment.exchangeRateSupplierToPayment,
            creditCardCurrency = inputPayment.creditCardCurrency,
            upliftAmount = inputPayment.upliftAmount,
            siteExchangeRate = inputPayment.siteExchangeRate,
            upliftExchangeRate = inputPayment.upliftExchangeRate,
            remark = inputPayment.remark,
            paymentTypeId = inputPayment.paymentTypeId,
            token = inputPayment.token,
            recStatus = inputPayment.recStatus,
            recCreatedWhen = inputPayment.recCreatedWhen.map(_.toDate),
            referencePaymentId = inputPayment.referencePaymentId,
            points = inputPayment.points
          )
        ),
        breakdown = Seq.empty,
        bookingPayments = Seq(
          BookingPaymentForMessage(
            paymentId = inputBookingPayment.paymentId,
            bookingId = inputBookingPayment.bookingId,
            paymentCurrency = inputBookingPayment.paymentCurrency,
            paymentAmount = inputBookingPayment.paymentAmount.toDouble,
            amountUsd = inputBookingPayment.amountUsd.toDouble,
            recStatus = inputBookingPayment.recStatus,
            recCreatedWhen = inputBookingPayment.recCreatedWhen.map(_.toDate),
            fxiUplift = inputBookingPayment.fxiUplift.map(_.toDouble),
            loyaltyPoints = inputBookingPayment.loyaltyPoints,
            supplierCurrency = inputBookingPayment.supplierCurrency,
            supplierExchangeRate = inputBookingPayment.supplierExchangeRate.map(_.toDouble),
            bookingPaymentId = inputBookingPayment.bookingPaymentId
          )
        ),
        bookingRelationships = Seq.empty,
        breakdownPerPax = Seq.empty,
        baggageAllowance = Seq.empty,
        baggage = Seq.empty,
        history = Seq(
          ItineraryHistoryForMessage(
            actionId = inputHistory.actionId,
            itineraryId = inputHistory.itineraryId.toInt,
            bookingType = inputHistory.bookingType,
            bookingId = inputHistory.bookingId,
            actionType = inputHistory.actionType,
            version = inputHistory.version,
            actionDate = inputHistory.actionDate.toDate,
            parameters = inputHistory.parameters,
            description = inputHistory.description,
            recStatus = inputHistory.recStatus,
            recCreatedWhen = inputHistory.recCreatedWhen.map(_.toDate)
          )
        ),
        summary = Seq.empty,
        paxTickets = Seq.empty,
        itinerary = FlightItineraryForMessage(
          itineraryId = inputItinerary.itineraryId,
          memberId = inputItinerary.memberId,
          recStatus = inputItinerary.recStatus,
          recCreatedWhen = inputItinerary.recCreatedWhen.map(_.toDate),
          recModifiedWhen = inputItinerary.recModifiedWhen.map(_.toDate)
        ),
        userAgent = None,
        bookingAttribution = Seq.empty,
        itineraryDate = inputItinerary.recCreatedWhen.get.toDate,
        protectionModels = None,
        multiProductInfos =
          Some(multiProductInfo.map(pf => MultiProductInfoForMessage(pf.multiProductId, pf.multiProductType.id))),
        flightSegmentInfoByPaxType = Seq.empty,
        segmentInfoByPaxType = Seq.empty,
        fareRulePolicies = None,
        flightSeatSelection = Seq.empty,
        vehicle = None,
        activities = None,
        properties = None,
        cegFastTracks = None,
        addOns = None,
        multiProductBookingGroups = Some(expectedMultiProductBookingGroupModelMessage),
        flightBrandSelections = None,
        flightBrandAttributes = None,
        flightBrandAttributeParams = None,
        flightBaseBooking = None,
        flightBaseCancellationInfo = None,
        crossProductIsolatedFeature = None
      )

      val result = BookingStateMessage.mapItineraryModelToBookingStateMessage(
        itineraryDbModel = inputItineraryModel,
        actionType = ActionType(actionTypeId),
        actionId = actionTypeId,
        bookingType = bookingType,
        multiProductInfos = multiProductInfo,
        multiProductBookingGroups = multiProductBookingGroupDBModel
      )
      result shouldBe expected
    }

    "map BaseBookingEssInfo correctly" in {
      val dateTime = DateTime.now()
      val input = BaseBookingEssInfoInternal(
        bookingEssInfoId = 111,
        bookingId = 222,
        userTaxCountryId = 333,
        recStatus = 1,
        recCreatedWhen = dateTime,
        recCreatedBy = "ABC",
        recModifiedWhen = Some(dateTime.plusDays(2)),
        recModifiedBy = Some("XYZ"),
        bookerResidenceCountryId = Some(123),
        paymentInstrumentCountryId = Some(124),
        ipAddressCountryId = Some(125)
      )
      val expected = BaseBookingEssInfoMessage(
        bookingEssInfoId = 111,
        bookingId = 222,
        userTaxCountryId = 333,
        recStatus = 1,
        recCreatedWhen = LocalDateTimeUtil.toJodaLocalDateTime(dateTime.toString).toDate,
        recCreatedBy = "ABC",
        recModifiedWhen = Some(LocalDateTimeUtil.toJodaLocalDateTime(dateTime.plusDays(2).toString).toDate),
        recModifiedBy = Some("XYZ"),
        bookerResidenceCountryId = Some(123),
        paymentInstrumentCountryId = Some(124),
        ipAddressCountryId = Some(125)
      )
      val actual = BookingStateMessage.mapBaseBookingEssInfo(input)
      actual shouldBe expected
    }
  }

  "BookingStateMessage model" should {
    val datetime = DateTime.now()
    val mockProtectionProductBooking = ProtectionProductBooking(
      productBookingId = 333,
      protectionBookingId = 456,
      productType = 2,
      recStatus = Some(1),
      recCreatedWhen = None,
      recModifiedWhen = None
    )
    val mockFinancialBreakdown = Breakdown(
      referenceId = 0,
      breakdownId = 1,
      itineraryId = 1002L,
      bookingType = Some(2),
      bookingId = Some(456),
      actionId = Some(ActionType.Created.id),
      eventDate = datetime,
      itemId = BreakDownItemID.SalesInclusive.id,
      typeId = 301,
      taxFeeId = Some(0),
      quantity = 1,
      localCurrency = "USD",
      localAmount = 100.1,
      exchangeRate = 1,
      usdAmount = 100.1,
      requestedAmount = Some(100.1),
      refBreakdownId = None,
      recStatus = Some(1),
      recCreatedWhen = Some(datetime),
      requestedCurrency = Some("JPY")
    )
    val mockBookingPayment = BookingPaymentState(
      paymentId = 1234,
      bookingId = 456,
      paymentCurrency = "USD",
      paymentAmount = 100,
      amountUsd = 100,
      bookingPaymentId = Some(111)
    )

    val mockProtectionCfar = ProtectionCfar(
      protectionBookingId = 1001,
      coveragePercentage = 100.0,
      coverageAmount = 100.2,
      currency = "USD",
      claimUrl = None,
      recStatus = 1,
      recCreatedWhen = Some(DateTime.now()),
      recModifiedWhen = Some(DateTime.now())
    )

    val mockProtectionModel = ProtectionModelInternal(
      protectionBookingId = 1001,
      itineraryId = 1002,
      protectionBookingStateId = ProtectionBookingState.Created,
      protectionTypeId = 1,
      priceAmount = 100.2,
      priceAmountUSD = 10,
      marginAmount = 10,
      marginAmountUSD = 2,
      marginPercentage = 10,
      currency = "USD",
      supplierId = 27916,
      subSupplierId = 27916,
      supplierSearchId = "supplier_search_id",
      supplierPolicyId = Some("supplier_policy_id"),
      supplierResultCode = Some(1),
      supplierSpecificData = Some("supplier_specific_data"),
      accountingEntity = Some(AccountingEntity.getProtectionEntity),
      multiProductId = Some(1),
      platformId = Some(1),
      languageId = Some(1),
      serverName = Some("FYKKPF"),
      cid = Some(-1),
      sessionId = "test",
      clientIpAddress = "**********",
      trackingCookingId = Some("tracking_cookie_id"),
      trackingCookieDate = Some(DateTime.parse("2019-04-26")),
      trackingTag = Some("tracking_tag"),
      recStatus = Some(1),
      recCreatedWhen = None,
      recModifiedWhen = None,
      whitelabelId = 1,
      requestId = "66cae3ca-8c8a-4099-85b0-df08afef11ba",
      correlationId = "78c9f8c5-5e53-48d0-b958-26cda202669b",
      version = Some(1),
      protectionProductBooking = Seq(mockProtectionProductBooking),
      financialBreakdowns = Seq(mockFinancialBreakdown),
      bookingPayments = Some(Seq(mockBookingPayment)),
      paymentModel = PaymentModel.Agency.id,
      tripStartDate = Some(DateTime.parse("2022-04-26")),
      tripEndDate = Some(DateTime.parse("2022-04-30")),
      commonBookingInfo = Some(CommonBookingInfo(origin = Some("origin"), isTestBooking = true)),
      commonBookingEventsInfo = Some(
        CommonBookingEventsInfo(
          confirmationDate = Some(DateTime.now()),
          cancellationDate = Some(DateTime.now())
        )
      ),
      protectionCfar = Some(mockProtectionCfar)
    )
    val input = Seq(
      mockProtectionModel
    )

    "should have schema compatible" in {
      val actionTypeId = 1
      val actionId     = 1
      val bookingType  = Some(1)

      val vehicleModelInternal = VehicleModelInternal(
        vehicleBooking = VehicleModelBooking(
          vehicleBookingId = 1,
          itineraryId = 1,
          multiProductId = Some(1),
          bookingDate = DateTime.parse("2019-08-02T16:01"),
          paymentModel = 1,
          displayCurrency = "USD",
          supplierId = 1,
          providerCode = "123",
          supplierBookingId = "123",
          supplierSpecificData = "BKK",
          supplierStatusCode = Some("1"),
          supplierCommissionAmount = 0.0,
          supplierCommissionPercentage = 0.0,
          whitelabelId = 1,
          isCancelled = false,
          cancellationPolicy = "",
          cancellationDate = None,
          fraudScore = None,
          fraudAction = None,
          fraudCheckIp = "",
          storefrontId = 1,
          platformId = Some(1),
          languageId = Some(1),
          serverName = Some("dev"),
          cid = None,
          searchId = None,
          searchRequestId = "123",
          sessionId = "123",
          clientIpAddress = "123",
          trackingCookieId = Some("123"),
          trackingCookieDate = None,
          trackingTag = Some("123"),
          recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
          recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
          recStatus = 1,
          vehicleStateId = 1,
          rejectReasonMessage = None,
          rejectReasonCode = None,
          accountingEntityStr = None,
          postBookingStateId = Some(1)
        ),
        vehicleBookingLocation = VehicleModelBookingPickUpDropOffLocation(
          pickUp = VehicleModelBookingLocation(
            vehicleBookingLocationId = 1,
            vehicleBookingId = 1,
            countryId = 1,
            cityId = 1,
            addressLine = "123",
            postalCode = "123",
            airportCode = Some("123"),
            locationName = "abc",
            isAirport = true,
            airportProviderLocation = None,
            extraInfo = "",
            supplierLocationCode = None,
            phoneNumber = None,
            locationType = None,
            recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
            recModifiedWhen = DateTime.parse("2019-08-02T16:01")
          ),
          dropOff = VehicleModelBookingLocation(
            vehicleBookingLocationId = 1,
            vehicleBookingId = 1,
            countryId = 1,
            cityId = 1,
            addressLine = "123",
            postalCode = "123",
            airportCode = Some("123"),
            locationName = "abc",
            isAirport = true,
            airportProviderLocation = None,
            extraInfo = "",
            supplierLocationCode = None,
            phoneNumber = None,
            locationType = None,
            recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
            recModifiedWhen = DateTime.parse("2019-08-02T16:01")
          )
        ),
        vehicleBookingSummary = VehicleModelBookingSummary(
          vehicleBookingId = 1,
          displayCurrency = "USD",
          totalSurcharge = 0.0,
          surchargeDetails = "THIS IS JSON STRING",
          baseDiscount = 0.0,
          campaignDiscount = 0.0,
          totalFare = 0.0,
          agodaFee = 0.0,
          recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
          recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
          recStatus = 1,
          policyChargeDetails = Some("THIS IS JSON STRING"),
          paymentModel = Some(1),
          baseFare = Some(0.0),
          taxAndFee = Some(0.0),
          extraChargeDetails = Some("THIS IS JSON STRING"),
          postBookingMetadata = Some("THIS IS JSON STRING")
        ),
        vehicleBookingTrip = VehicleModelBookingTrip(
          vehicleBookingId = 1,
          vehicleCode = "abc",
          vehicleName = "abc",
          classification = "abc",
          pickupDatetime = DateTime.parse("2019-08-02T16:01"),
          dropOffDatetime = DateTime.parse("2019-08-03T16:01"),
          pickupLocationId = 1,
          dropOffLocationId = 1,
          driverAge = 30,
          flightNo = None,
          recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
          recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
          supplierConfirmationCode = Some("abc"),
          customerAgeGroup = Some("age-group"),
          securityDepositType = Some("security-deposit"),
          localRenter = Some("local-renter")
        ),
        vehicleFinancialBreakdowns = Seq.empty,
        vehicleBookingCancellation = None,
        vehicleInfo = Some(
          VehicleInfo(
            vehicleInfoId = 123L,
            vehicleBookingId = 321L,
            vehicleCode = "CAR-001",
            vehicleName = "Honda",
            vehicleClassification = "Mini",
            vehicleDoors = Some(4),
            vehicleSeats = Some(4),
            vehicleSuitcases = Some(1),
            vehicleTransmission = Some("vehicleTransmission"),
            vehicleIsAircon = Some(true),
            vehicleIsAirbag = Some(true),
            vehicleFuelType = Some("vehicleFuelType"),
            recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
            recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
            vehicleMileagePolicy = Some(
              VehicleMileagePolicy(
                freeDistance = 0,
                code = "",
                description = "Mock Car Fuel Info",
                charge = None,
                isFreeCoverage = true
              )
            ),
            vehicleFuelPolicy = Some(
              VehicleFuelPolicy(
                coverageType = "Full_To_Full",
                code = "",
                description = "fuel policy",
                charge = None,
                isFreeCoverage = true
              )
            ),
            imageUrl = Some("imageUrl"),
            pickUpSupplierOperationHours =
              Some(Seq(VehicleOperationSchedule("Monday", Seq(VehicleSupplierOperationHour("06:00", "00:00"))))),
            dropOffSupplierOperationHours =
              Some(Seq(VehicleOperationSchedule("Monday", Seq(VehicleSupplierOperationHour("06:00", "00:00"))))),
            providerIconUrl = Some("iconUrl"),
            acrissCode = Some("acrissCode")
          )
        ),
        baseBooking = None
      )

      val vehicleBookingState = VehicleBookingState(
        vehicleModelsInternal = Seq(vehicleModelInternal),
        payments = Seq.empty,
        itinerary = MultiProductItinerary(itineraryId = 1, memberId = 1),
        itineraryHistories = Seq.empty,
        bookingPayments = Seq.empty,
        multiProductBookingGroups = multiProductBookingGroupDBModel
      )
      val mockMultiProductInfos = Seq(MultiProductInfoDBModel(1, MultiProductType.SingleVehicle))

      val messageModelChecked = BookingStateMessage
        .fromVehicleBookingState(
          actionType = ActionType(actionTypeId),
          actionId = actionTypeId,
          bookingType = bookingType,
          vehicleBookingState = vehicleBookingState,
          protectionModel = Seq.empty,
          multiProductInfos = mockMultiProductInfos
        )
        .checkCompatibility()
      messageModelChecked.getCode shouldNot be(CompatibilityResult.COMPATIBILITY_NO)
      messageModelChecked.getCode shouldNot be(CompatibilityResult.COMPATIBILITY_UNKNOWN_ERROR)
    }

    "should mapProtectionBookingToProtectionBookingForMessage correctly" in {

      val expected = Seq(
        ProtectionModelForMessage(
          protectionBookingId = mockProtectionModel.protectionBookingId,
          itineraryId = mockProtectionModel.itineraryId,
          protectionBookingStateId = mockProtectionModel.protectionBookingStateId.id,
          protectionTypeId = mockProtectionModel.protectionTypeId,
          priceAmount = mockProtectionModel.priceAmount,
          priceAmountUSD = mockProtectionModel.priceAmountUSD,
          marginAmount = mockProtectionModel.marginAmount,
          marginAmountUSD = mockProtectionModel.marginAmountUSD,
          marginPercentage = mockProtectionModel.marginPercentage,
          currency = mockProtectionModel.currency,
          supplierId = mockProtectionModel.supplierId,
          subSupplierId = mockProtectionModel.subSupplierId,
          supplierSearchId = mockProtectionModel.supplierSearchId,
          supplierPolicyId = mockProtectionModel.supplierPolicyId,
          supplierResultCode = mockProtectionModel.supplierResultCode,
          supplierSpecificData = mockProtectionModel.supplierSpecificData,
          accountingEntity = Some(
            AccountingEntityForMessage(
              merchantOfRecord = AccountingEntity.getProtectionEntity.merchantOfRecord,
              rateContract = AccountingEntity.getProtectionEntity.rateContract,
              revenue = AccountingEntity.getProtectionEntity.revenue,
              argument = AccountingEntity.getProtectionEntity.argument
            )
          ),
          multiProductId = mockProtectionModel.multiProductId,
          platformId = mockProtectionModel.platformId,
          languageId = mockProtectionModel.languageId,
          serverName = mockProtectionModel.serverName,
          cid = mockProtectionModel.cid,
          sessionId = mockProtectionModel.sessionId,
          clientIpAddress = mockProtectionModel.clientIpAddress,
          trackingCookingId = mockProtectionModel.trackingCookingId,
          trackingCookieDate = mockProtectionModel.trackingCookieDate.map(_.toDate),
          trackingTag = mockProtectionModel.trackingTag,
          recStatus = mockProtectionModel.recStatus,
          recCreatedWhen = mockProtectionModel.recCreatedWhen.map(_.toDate),
          recModifiedWhen = mockProtectionModel.recModifiedWhen.map(_.toDate),
          whitelabelId = mockProtectionModel.whitelabelId,
          requestId = mockProtectionModel.requestId,
          correlationId = mockProtectionModel.correlationId,
          version = mockProtectionModel.version,
          protectionProductBooking = mockProtectionModel.protectionProductBooking.map { ppm =>
            ProtectionProductBookingForMessage(
              productBookingId = ppm.productBookingId,
              protectionBookingId = ppm.protectionBookingId,
              productType = ppm.productType,
              recStatus = ppm.recStatus,
              recCreatedWhen = ppm.recCreatedWhen.map(_.toDate),
              recModifiedWhen = ppm.recModifiedWhen.map(_.toDate)
            )
          },
          tripCostAmount = mockProtectionModel.tripCostAmount,
          tripCostCurrency = mockProtectionModel.tripCostCurrency,
          financialBreakdowns = Seq(
            BreakdownForMessage(
              referenceId = mockFinancialBreakdown.referenceId,
              breakdownId = mockFinancialBreakdown.breakdownId,
              itineraryId = mockFinancialBreakdown.itineraryId,
              bookingType = mockFinancialBreakdown.bookingType,
              bookingId = mockFinancialBreakdown.bookingId,
              actionId = mockFinancialBreakdown.actionId,
              eventDate = mockFinancialBreakdown.eventDate.toDate,
              itemId = mockFinancialBreakdown.itemId,
              typeId = mockFinancialBreakdown.typeId,
              taxFeeId = mockFinancialBreakdown.taxFeeId,
              quantity = mockFinancialBreakdown.quantity,
              localCurrency = mockFinancialBreakdown.localCurrency,
              localAmount = mockFinancialBreakdown.localAmount,
              exchangeRate = mockFinancialBreakdown.exchangeRate,
              usdAmount = mockFinancialBreakdown.usdAmount,
              requestedAmount = mockFinancialBreakdown.requestedAmount,
              refBreakdownId = mockFinancialBreakdown.refBreakdownId,
              recStatus = mockFinancialBreakdown.recStatus,
              recCreatedWhen = mockFinancialBreakdown.recCreatedWhen.map(_.toDate),
              vendorExchangeRate = mockFinancialBreakdown.vendorExchangeRate,
              upcId = mockFinancialBreakdown.upcId
            )
          ),
          bookingPayments = Some(
            Seq(
              BookingPaymentForMessage(
                paymentId = mockBookingPayment.paymentId,
                bookingId = mockBookingPayment.bookingId,
                paymentCurrency = mockBookingPayment.paymentCurrency,
                paymentAmount = mockBookingPayment.paymentAmount.toDouble,
                amountUsd = mockBookingPayment.amountUsd.toDouble,
                recStatus = mockBookingPayment.recStatus,
                recCreatedWhen = mockBookingPayment.recCreatedWhen.map(_.toDate),
                fxiUplift = mockBookingPayment.fxiUplift.map(_.toDouble),
                loyaltyPoints = mockBookingPayment.loyaltyPoints,
                supplierCurrency = mockBookingPayment.supplierCurrency,
                supplierExchangeRate = mockBookingPayment.supplierExchangeRate.map(_.toDouble),
                bookingPaymentId = mockBookingPayment.bookingPaymentId
              )
            )
          ),
          paymentModel = mockProtectionModel.paymentModel,
          tripStartDate = mockProtectionModel.tripStartDate.map(_.toDate),
          tripEndDate = mockProtectionModel.tripEndDate.map(_.toDate),
          commonBookingInfo = Some(
            CommonBookingInfoForMessage(
              origin = mockProtectionModel.commonBookingInfo.get.origin,
              isTestBooking = mockProtectionModel.commonBookingInfo.get.isTestBooking
            )
          ),
          commonBookingEventsInfo = Some(
            CommonBookingEventsInfoForMessage(
              confirmationDate = mockProtectionModel.commonBookingEventsInfo.get.confirmationDate.map(_.toDate),
              cancellationDate = mockProtectionModel.commonBookingEventsInfo.get.cancellationDate.map(_.toDate)
            )
          ),
          protectionCfar = Some(
            ProtectionCfarForMessage(
              protectionBookingId = mockProtectionCfar.protectionBookingId,
              coveragePercentage = mockProtectionCfar.coveragePercentage,
              coverageAmount = mockProtectionCfar.coverageAmount,
              currency = mockProtectionCfar.currency,
              claimUrl = mockProtectionCfar.claimUrl,
              recStatus = mockProtectionCfar.recStatus,
              recCreatedWhen = mockProtectionCfar.recCreatedWhen.map(_.toDate),
              recModifiedWhen = mockProtectionCfar.recModifiedWhen.map(_.toDate)
            )
          )
        )
      )
      val actual = BookingStateMessage.mapProtectionBookingToProtectionBookingForMessage(input)

      actual shouldBe expected
    }

    "should map fromVehicleBookingState correctly with protection" in {
      val actionTypeId = 1
      val bookingType  = Some(1)

      val vehicleModelInternal = VehicleModelInternal(
        vehicleBooking = VehicleModelBooking(
          vehicleBookingId = 1,
          itineraryId = 1,
          multiProductId = Some(1),
          bookingDate = DateTime.parse("2019-08-02T16:01"),
          paymentModel = 1,
          displayCurrency = "USD",
          supplierId = 1,
          providerCode = "123",
          supplierBookingId = "123",
          supplierSpecificData = "BKK",
          supplierStatusCode = Some("1"),
          supplierCommissionAmount = 0.0,
          supplierCommissionPercentage = 0.0,
          whitelabelId = 1,
          isCancelled = false,
          cancellationPolicy = "",
          cancellationDate = None,
          fraudScore = None,
          fraudAction = None,
          fraudCheckIp = "",
          storefrontId = 1,
          platformId = Some(1),
          languageId = Some(1),
          serverName = Some("dev"),
          cid = None,
          searchId = None,
          searchRequestId = "123",
          sessionId = "123",
          clientIpAddress = "123",
          trackingCookieId = Some("123"),
          trackingCookieDate = None,
          trackingTag = Some("123"),
          recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
          recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
          recStatus = 1,
          vehicleStateId = 1,
          rejectReasonMessage = None,
          rejectReasonCode = None,
          accountingEntityStr = None,
          postBookingStateId = Some(1)
        ),
        vehicleBookingLocation = VehicleModelBookingPickUpDropOffLocation(
          pickUp = VehicleModelBookingLocation(
            vehicleBookingLocationId = 1,
            vehicleBookingId = 1,
            countryId = 1,
            cityId = 1,
            addressLine = "123",
            postalCode = "123",
            airportCode = Some("123"),
            locationName = "abc",
            isAirport = true,
            airportProviderLocation = None,
            extraInfo = "",
            supplierLocationCode = None,
            phoneNumber = None,
            locationType = None,
            recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
            recModifiedWhen = DateTime.parse("2019-08-02T16:01")
          ),
          dropOff = VehicleModelBookingLocation(
            vehicleBookingLocationId = 1,
            vehicleBookingId = 1,
            countryId = 1,
            cityId = 1,
            addressLine = "123",
            postalCode = "123",
            airportCode = Some("123"),
            locationName = "abc",
            isAirport = true,
            airportProviderLocation = None,
            extraInfo = "",
            supplierLocationCode = None,
            phoneNumber = None,
            locationType = None,
            recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
            recModifiedWhen = DateTime.parse("2019-08-02T16:01")
          )
        ),
        vehicleBookingSummary = VehicleModelBookingSummary(
          vehicleBookingId = 1,
          displayCurrency = "USD",
          totalSurcharge = 0.0,
          surchargeDetails = "THIS IS JSON STRING",
          baseDiscount = 0.0,
          campaignDiscount = 0.0,
          totalFare = 0.0,
          agodaFee = 0.0,
          recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
          recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
          recStatus = 1,
          policyChargeDetails = Some("THIS IS JSON STRING"),
          paymentModel = Some(1),
          baseFare = Some(0.0),
          taxAndFee = Some(0.0),
          extraChargeDetails = Some("THIS IS JSON STRING"),
          postBookingMetadata = Some("THIS IS JSON STRING")
        ),
        vehicleBookingTrip = VehicleModelBookingTrip(
          vehicleBookingId = 1,
          vehicleCode = "abc",
          vehicleName = "abc",
          classification = "abc",
          pickupDatetime = DateTime.parse("2019-08-02T16:01"),
          dropOffDatetime = DateTime.parse("2019-08-03T16:01"),
          pickupLocationId = 1,
          dropOffLocationId = 1,
          driverAge = 30,
          flightNo = None,
          recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
          recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
          supplierConfirmationCode = Some("abc"),
          customerAgeGroup = Some("age-group"),
          securityDepositType = Some("security-deposit"),
          localRenter = Some("local-renter")
        ),
        vehicleFinancialBreakdowns = Seq.empty,
        vehicleBookingCancellation = None,
        vehicleInfo = Some(
          VehicleInfo(
            vehicleInfoId = 123L,
            vehicleBookingId = 321L,
            vehicleCode = "CAR-001",
            vehicleName = "Honda",
            vehicleClassification = "Mini",
            vehicleDoors = Some(4),
            vehicleSeats = Some(4),
            vehicleSuitcases = Some(1),
            vehicleTransmission = Some("vehicleTransmission"),
            vehicleIsAircon = Some(true),
            vehicleIsAirbag = Some(true),
            vehicleFuelType = Some("vehicleFuelType"),
            recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
            recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
            vehicleMileagePolicy = Some(
              VehicleMileagePolicy(
                freeDistance = 0,
                code = "",
                description = "Mock Car Fuel Info",
                charge = None,
                isFreeCoverage = true
              )
            ),
            vehicleFuelPolicy = Some(
              VehicleFuelPolicy(
                coverageType = "Full_To_Full",
                code = "",
                description = "fuel policy",
                charge = None,
                isFreeCoverage = true
              )
            ),
            imageUrl = Some("imageUrl"),
            pickUpSupplierOperationHours =
              Some(Seq(VehicleOperationSchedule("Monday", Seq(VehicleSupplierOperationHour("06:00", "00:00"))))),
            dropOffSupplierOperationHours =
              Some(Seq(VehicleOperationSchedule("Monday", Seq(VehicleSupplierOperationHour("06:00", "00:00"))))),
            providerIconUrl = Some("iconUrl"),
            acrissCode = Some("acrissCode")
          )
        ),
        baseBooking = None
      )

      val vehicleBookingState = VehicleBookingState(
        vehicleModelsInternal = Seq(vehicleModelInternal),
        payments = Seq.empty,
        itinerary = MultiProductItinerary(itineraryId = 1, memberId = 1),
        itineraryHistories = Seq.empty,
        bookingPayments = Seq.empty,
        multiProductBookingGroups = multiProductBookingGroupDBModel
      )
      val mockMultiProductInfos = Seq(MultiProductInfoDBModel(1, MultiProductType.SingleVehicle))

      val actual = BookingStateMessage
        .fromVehicleBookingState(
          actionType = ActionType(actionTypeId),
          actionId = actionTypeId,
          bookingType = bookingType,
          vehicleBookingState = vehicleBookingState,
          protectionModel = input,
          multiProductInfos = mockMultiProductInfos
        )

      val messageModelChecked = actual
        .checkCompatibility()
      messageModelChecked.getCode shouldNot be(CompatibilityResult.COMPATIBILITY_NO)
      messageModelChecked.getCode shouldNot be(CompatibilityResult.COMPATIBILITY_UNKNOWN_ERROR)
      actual.protectionModels.size shouldBe 1
      actual.vehicle.size shouldBe 1
      actual.multiProductInfos.map(_.size) shouldBe Some(1)
    }

  }
}
