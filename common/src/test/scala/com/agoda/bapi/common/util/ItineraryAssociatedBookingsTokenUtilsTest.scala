package com.agoda.bapi.common.util

import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.token.{BookingTokenEncryptionHelper, ItineraryAssociatedBookingsToken}
import org.scalatest.TryValues

import scala.util.{Failure, Success}
import org.scalatestplus.mockito.MockitoSugar
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

class ItineraryAssociatedBookingsTokenUtilsTest extends AnyWordSpec with Matchers with MockitoSugar with TryValues {

  val util             = new ItineraryAssociatedBookingsTokenUtilsImpl(BookingTokenEncryptionHelper)
  val itineraryId      = 1
  val fightBookingsIds = Seq(2L, 3L)
  val hotelBookingIds  = Seq(4L, 5L)

  "BookingDetailTokenUtils create and extract correct token" in {
    val expected     = ItineraryAssociatedBookingsToken(itineraryId, fightBookingsIds, hotelBookingIds)
    val createdToken = util.create(itineraryId, fightBookingsIds, hotelBookingIds)
    createdToken.get.version shouldBe 1
    val extractedToken = util.extract(createdToken.get)
    extractedToken shouldBe Success(expected)
  }

  "BookingDetailTokenUtils extract should fail if expired" in {
    val expriedTokenMessage = TokenMessage(
      "NHwm8IDDAt7NV5PbWUSjrN1UE9iQWgYSbZUcOI9kI10NOETac7Pmx1UeiNywD7hJLqaKu7LAkqf7GhMM1O+PCgCa4kTIvFcQHEWqZ0hMfOxlfNjNTlgSzMqkkBnmQSZxeBfiNZlYv7xv0eqoKkQAlgy0PkRTIvUfWZ8RV9gDuywSO3leE9bUZeRbCJ3XP1dmFFdndFAqF4c/WBI3k9/1eA==",
      1
    )
    val result            = util.extract(expriedTokenMessage)
    val expectedException = new TokenExpiredException("ItineraryAssociatedBookingsToken expired")
    val resultException   = result.failure.exception
    resultException.getClass shouldBe expectedException.getClass
    resultException.getMessage shouldBe "ItineraryAssociatedBookingsToken expired"
  }
}
