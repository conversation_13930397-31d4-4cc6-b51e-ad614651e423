package com.agoda.bapi.common.message

import com.agoda.bapi.common.message.multi.product.{PropertyBookingState, SetStateResponse}
import com.agoda.bapi.common.message.multi.product.SetStateResponse.WarnCode
import com.agoda.bapi.common.model.car.VehicleBookingStateModel.VehicleModelInternal
import com.agoda.bapi.common.model.flight.flightModel.FlightModelInternal
import org.scalatestplus.mockito.MockitoSugar
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

class SetStateResponseTest extends AnyWordSpec with Matchers with MockitoSugar {

  "withError" should {
    "return model with error message" in {
      val result = SetStateResponse.withError(ResponseErrorCode.TechnicalError, "Test")
      result.errorCode shouldBe Some("TechnicalError")
      result.errorMessage shouldBe Some("Test")
      result.success shouldBe false
    }
  }

  "withWarning" should {
    "return model with warning message" in {
      val result = SetStateResponse.withWarning(WarnCode.BOOKING_NOT_FOUND, "Test")
      result.errorCode shouldBe Some("BOOKING_NOT_FOUND")
      result.errorMessage shouldBe Some("Test")
      result.success shouldBe true
    }
  }

  "withSuccess" should {
    "return model with success result" in {
      val bookingState = mock[FlightModelInternal]
      val result       = SetStateResponse.withSuccess(bookingState)
      result.errorCode shouldBe empty
      result.errorMessage shouldBe empty
      result.success shouldBe true
      result.bookingState shouldBe Some(bookingState)
    }
  }

  "combineProductResponses" should {
    val bookingState        = mock[FlightModelInternal]
    val vehicleBooking      = mock[VehicleModelInternal]
    val activityBooking     = mock[ActivityBookingState]
    val propertyBooking     = mock[PropertyBookingState]
    val cegFastTrackBooking = mock[CegFastTrackBookingState]
    val addOnBooking        = mock[AddOnBookingState]
    val baseResponse        = SetStateResponse.withSuccess(bookingState)

    val vehicleBookingSetStateResponse  = baseResponse.copy(vehicleBookings = Some(Seq(vehicleBooking)))
    val activityBookingSetStateResponse = baseResponse.copy(activityBookings = Some(Seq(activityBooking)))
    val propertyBookingSetStateResponse = baseResponse.copy(propertyBookings = Some(Seq(propertyBooking)))
    val cegFastTrackBookingSetStateResponse =
      baseResponse.copy(cegFastTrackBookings = Some(Seq(cegFastTrackBooking)))
    val addOnBookingSetStateResponse = baseResponse.copy(addOnBookings = Some(Seq(addOnBooking)))
    "combine vehicleBookings, activityBookings, priceFreezeBookings, " +
      "propertyBookings, cegFastTrackBookings, addOnBookings from different SetStateResponse" in {
        SetStateResponse.combineProductResponses(
          Seq(
            vehicleBookingSetStateResponse,
            activityBookingSetStateResponse,
            propertyBookingSetStateResponse,
            cegFastTrackBookingSetStateResponse,
            addOnBookingSetStateResponse
          ),
          baseResponse
        ) shouldBe
          baseResponse.copy(
            activityBookings = Some(Seq(activityBooking)),
            vehicleBookings = Some(Seq(vehicleBooking)),
            addOnBookings = Some(Seq(addOnBooking)),
            propertyBookings = Some(Seq(propertyBooking)),
            cegFastTrackBookings = Some(Seq(cegFastTrackBooking))
          )
      }

    "map None if some of sequences are Nil" in
      SetStateResponse.combineProductResponses(
        Seq(
          baseResponse,
          baseResponse,
          baseResponse,
          propertyBookingSetStateResponse,
          cegFastTrackBookingSetStateResponse,
          baseResponse
        ),
        baseResponse.copy(
          propertyBookings = Some(Seq(propertyBooking)),
          cegFastTrackBookings = Some(Seq(cegFastTrackBooking))
        )
      )
  }
}
