import sbt.Keys.scalaBinaryVersion
import sbt.{ExclusionRule, _}

object Dependencies {

  object Versions {
    val asyncHttpClient               = "2.10.2"
    val adp_messaging_version         = "2.8.0.2"
    val adp_messaging_logback_version = adp_messaging_version
    val ag_vault_version              = "1.1.0"
    val akka_version                  = "2.5.32"
    val pekko_testkit_version         = "1.0.0"
    val akka_http_version             = "10.2.3"
    val booking_query_version         = "1.0.12"
    val commons_io_version            = "2.6"
    val commons_compress_version      = "1.19"
    val scala_parser_version          = "1.1.2"
    val scala_xml_version             = "1.2.0"
    val scalatest_version             = "3.2.10"
    val scalatestplus_mockito_version = "3.2.10.0"
    val shapeless_version             = "2.3.3"
    val scalactic_version             = scalatest_version
    val typesafe_config_version       = "1.4.1"
    val he<PERSON><PERSON>berger_version        = "1.39.2"
    val circe_version                 = "0.14.2"
    val SCALAPB_JSON_VERSION          = "0.9.3"
    val protobuf_version              = "3.0.0"
    val bcre_version                  = "2.311.0"
    // in progress with propertyApiV2 upgrade https://agoda.atlassian.net/browse/BCT-807,
    // please discuss with ticket owner before updating this version
    val propertyapi_client_version          = "3.44.742"
    val ypl_version                         = "4.3.4"
    val enumcommons                         = "0.9.7"
    val paymentapiv2_version                = "0.2.18"
    val credit_card_api_v2_version          = "0.2.37"
    val abs_version                         = "3.3.19"
    val guice_version                       = "4.1.0"
    val mockito_version                     = "3.4.6"
    val iheart_ficus_version                = "1.5.2"
    val scalacache_version                  = "0.9.3"
    val bapicommon_version                  = "0.1.19"
    val experiments_platform_client_version = "4.3.2"
    val deliveryapi_version                 = "1.3.0"
    val json_schema_validator_version       = "2.2.10"
    val wiremock_version                    = "2.27.2"
    val enigma_client_version               = "0.21.347"
    val agoda_commons                       = "0.17.1"
    val agoda_commons_sql                   = "0.17.1"
    val capi_client_version                 = "4.6.14"   // https://gitlab.agodadev.io/data-protection/customer-api/-/releases
    val softwaremill_sttp_version           = "1.5.2"
    val terracotta_client_version           = "3.5.3"
    val wl_client_version                   = "10.1371.0"
    val mssql_jdbc_version                  = "8.2.1.jre8"
    val json4s_version                      = "3.6.7"
    val io_netty_version                    = "4.1.100.Final"
    val flight_awo_version                  = "0.2.12"
    val opRabbitVersion                     = "3.0.0"
    val common_finance_tax_version          = "1.4.128"
    val jwt_circe_version                   = "3.1.0"
    val flights_api_client                  = "0.0.210"
    val simple_warts_version                = "0.2"
    val zaidel_warts_version                = "0.14.0"
    val ancillary_client                    = "0.0.180"
    val vehicle_search_api_version          = "1.351.0"
    val giftcard_client_version             = "1.4.5"
    val jackson_version                     = "2.18.1"
    val gandalf_version                     = "1.2.254"
    val swagger_scala_module_version        = "1.0.6"    // newer versions may contain incompatible guava version
    val swagger_version                     = "1.6.2"    // newer versions may contain incompatible guava version
    val mpb_commons                         = "1.217.0"  // https://gitlab.agodadev.io/ebe/mpb-commons
    val ag_protobuf                         = "3.1106.0" // https://gitlab.agodadev.io/common-libs/ag-protobufs
    val thesamet_scala_pb_version           = "0.9.8"
    val thesamet_scalapb_json4s_version     = "0.9.3"
    val quicklens_version                   = "1.4.12"
    val logback_version                     = "1.3.5"
    val swagger_akka_http_version           = "1.1.2"
    val scala_logging_version               = "3.9.2"
    val diffx_version                       = "0.7.1"
    val upi_version                         = "0.5.68"   //  https://gitlab.agodadev.io/packages/upi
    val ceg_wl_client_version               = "0.0.65"   // https://gitlab.agodadev.io/IT-Ceg/cegwl
    val scalapb_df_version                  = "3.857.0"
    val ag_logging_version                  = "0.11.1"
    val old_ag_http_client_mesh_version     = "0.6.5"
    val ag_consul_version                   = "3.0.4"
    val json_smart_version                  = "2.3"
    val opentelemetry_version               = "1.35.0"

    /**
      * known issue io.grpc:grpc-netty:1.27.2 lingers after updating ag-grpc to 1.3.0 and explicit override was added to
      * get the required version but for future update to ag-grpc version, pls perform the following
      *   1. comment out global override for io.grpc first
      *   2. upversion ag-grpc; reload project and see if io.grpc is refreshed to higher versions 3a. if all
      *      io.grpc:grpc-* are consistent versions, remove override clause 3b. if not, maintain the override and update
      *      version on io_grpc_netty_version along with any inconsistent versions
      */
    val ag_grpc_version                 = "2.8.6" // https://gitlab.agodadev.io/common-libs/ag-grpc
    val io_grpc_netty_version           = "1.51.1"
    val activity_search_api_version     = "1.574.0"
    val scala_collection_compat_version = "2.11.0"
    val cats_core_compat_version        = "2.6.1"
    val cats_effects_compat_version     = "2.5.1"
    val fs2_compat_version              = "2.5.6"
    val external_loyalty_version        = "1.2.425"
    val agObservability                 = "0.5.5"
    val ag_http_client_version          = "2.10.4"
    val jax_rs_api_version              = "2.1.1"
    val jersey_version                  = "2.35"
  }

  import Versions._

  val propertyApiExclusionRule = Seq(
    ExclusionRule("org.mockito"),
    ExclusionRule("com.typesafe.akka"),
    ExclusionRule("org.scalatest"),
    ExclusionRule("com.thesamet.scalapb"),
    ExclusionRule("com.github.cb372"),
    ExclusionRule("com.agoda.paymentapiv2.client"),
    ExclusionRule("io.netty"),
    ExclusionRule("com.agoda.experiments"),
    ExclusionRule("org.apache.kafka"),
    ExclusionRule("com.agoda", "dfprotobuf_2.12"),
    ExclusionRule("com.agoda.commons", "ag-protobuf-scalapb-0-9-7_2.12"),
    ExclusionRule("com.agoda.commons", "ag-protobuf-scalapb-0-11-x-df_2.12"),
    ExclusionRule("com.agoda.commons", "sql_2.12"),
    ExclusionRule("com.agoda.papi", "enum-commons"),
    ExclusionRule("com.softwaremill.sttp.tapir", "tapir-openapi-docs_2.12"),
    ExclusionRule("com.agoda.commons", "ag-http-client-v2_2.12"),
    ExclusionRule("com.agoda.commons", "ag-http-client-mesh-v2_2.12")
  )

  lazy val globalOverrides = Seq(
    "com.agoda.adp.messaging"            % "adp-messaging-client"             % adp_messaging_version,
    "com.agoda.adp.messaging"           %% "adp-messaging-client-scala"       % adp_messaging_version,
    "com.agoda.adp.messaging"            % "adp-messaging-logback"            % adp_messaging_logback_version,
    "org.json4s"                        %% "json4s-core"                      % json4s_version,
    "org.json4s"                        %% "json4s-jackson"                   % json4s_version,
    "org.json4s"                        %% "json4s-ext"                       % json4s_version,
    "org.json4s"                        %% "json4s-native"                    % json4s_version,
    "org.scalactic"                     %% "scalactic"                        % scalactic_version,
    "io.grpc"                            % "grpc-netty"                       % io_grpc_netty_version,
    "com.agoda.commons"                 %% "ag-grpc-client"                   % ag_grpc_version,
    "io.netty"                           % "netty-all"                        % io_netty_version,
    "io.netty"                           % "netty-codec-socks"                % io_netty_version,
    "io.netty"                           % "netty-codec-http"                 % io_netty_version,
    "io.netty"                           % "netty-codec-http2"                % io_netty_version,
    "ch.qos.logback"                     % "logback-classic"                  % logback_version,
    "com.agoda.booking.creation.client" %% "booking-creation-client-scala"    % bcre_version,
    "com.fasterxml.jackson.core"         % "jackson-annotations"              % jackson_version,
    "com.fasterxml.jackson.core"         % "jackson-core"                     % jackson_version,
    "com.fasterxml.jackson.core"         % "jackson-databind"                 % jackson_version,
    "com.fasterxml.jackson.dataformat"   % "jackson-dataformat-yaml"          % jackson_version,
    "com.fasterxml.jackson.datatype"     % "jackson-datatype-jsr310"          % jackson_version,
    "com.fasterxml.jackson.datatype"     % "jackson-datatype-jdk8"            % jackson_version,
    "com.fasterxml.jackson.datatype"     % "jackson-datatype-joda"            % jackson_version,
    "com.fasterxml.jackson.module"      %% "jackson-module-scala"             % jackson_version,
    "com.fasterxml.jackson.module"       % "jackson-module-jaxb-annotations"  % jackson_version,
    "com.fasterxml.jackson.jaxrs"        % "jackson-jaxrs-base"               % jackson_version,
    "com.fasterxml.jackson.jaxrs"        % "jackson-jaxrs-json-provider"      % jackson_version,
    "com.agoda.experiments"             %% "experiments-platform-client"      % experiments_platform_client_version,
    "com.typesafe.akka"                 %% "akka-actor"                       % akka_version,
    "com.typesafe.akka"                 %% "akka-slf4j"                       % akka_version,
    "com.typesafe.akka"                 %% "akka-protobuf"                    % akka_version,
    "com.typesafe.akka"                 %% "akka-stream"                      % akka_version,
    "com.typesafe.akka"                 %% "akka-http"                        % akka_http_version,
    "com.typesafe.akka"                 %% "akka-http-spray-json"             % akka_http_version,
    "com.typesafe.akka"                 %% "akka-http-jackson"                % akka_http_version,
    "com.typesafe.akka"                 %% "akka-http-xml"                    % akka_http_version,
    "de.heikoseeberger"                 %% "akka-http-circe"                  % heikoseeberger_version,
    "com.thesamet.scalapb"              %% "scalapb-runtime"                  % thesamet_scala_pb_version,
    "com.thesamet.scalapb"              %% "scalapb-runtime-grpc"             % thesamet_scala_pb_version,
    "org.scala-lang.modules"            %% "scala-collection-compat"          % scala_collection_compat_version,
    "org.typelevel"                     %% "cats-core"                        % cats_core_compat_version,
    "org.typelevel"                     %% "cats-effect"                      % cats_effects_compat_version,
    "co.fs2"                            %% "fs2-core"                         % fs2_compat_version,
    "com.agoda.commons"                 %% "ag-http-client-mesh-v2"           % ag_http_client_version,
    "com.agoda.commons"                 %% "ag-http-client-v2"                % ag_http_client_version,
    "com.agoda.commons"                  % "ag-dynamic-state-consul"          % ag_consul_version,
    "com.agoda.commons"                 %% "ag-consul"                        % ag_consul_version,
    "io.opentelemetry"                   % "opentelemetry-api"                % opentelemetry_version,
    "io.opentelemetry"                   % "opentelemetry-context"            % opentelemetry_version,
    "com.microsoft.sqlserver"            % "mssql-jdbc"                       % mssql_jdbc_version,
    "net.minidev"                        % "json-smart"                       % json_smart_version,
    "javax.ws.rs"                        % "javax.ws.rs-api"                  % jax_rs_api_version,
    "org.glassfish.jersey.core"          % "jersey-client"                    % jersey_version,
    "org.glassfish.jersey.core"          % "jersey-common"                    % jersey_version,
    "org.glassfish.jersey.inject"        % "jersey-hk2"                       % jersey_version,
    "org.glassfish.jersey.media"         % "jersey-media-json-jackson"        % jersey_version,
    "org.glassfish.jersey.media"         % "jersey-media-multipart"           % jersey_version,
    "org.glassfish.jersey.media"         % "jersey-media-sse"                 % jersey_version,
    "org.glassfish.jersey.media"         % "jersey-media-json-jettison"       % jersey_version,
    "org.glassfish.jersey.media"         % "jersey-media-json-processing"     % jersey_version,
    "org.glassfish.jersey.media"         % "jersey-media-moxy"                % jersey_version,
    "org.glassfish.jersey.media"         % "jersey-media-xml"                 % jersey_version,
    "com.softwaremill.sttp.model"       %% "core"                             % "1.3.4",
    "com.softwaremill.sttp.client3"     %% "core"                             % "3.1.9",
    "com.softwaremill.sttp.client3"     %% "async-http-client-backend-future" % "3.1.9",
    "com.softwaremill.sttp.client3"     %% "async-http-client-backend"        % "3.1.9"
  )

  lazy val globalExcludes = Seq(
    ExclusionRule("org.slf4j", "slf4j-log4j12"),
    ExclusionRule("org.mockito", "mockito-all")
  )

  val winterfellClient = "com.agoda.winterfell" %% "client" % capi_client_version excludeAll
    (
      ExclusionRule("com.typesafe.akka"),
      ExclusionRule("com.thesamet.scalapb"),
      ExclusionRule("com.agoda.commons"),
      ExclusionRule("com.github.tomakehurst")
    )

  val creation = Seq(
    "net.codingwell"        %% "scala-guice" % guice_version,
    "com.agoda.propertyapi" %% "client"      % propertyapi_client_version excludeAll ((propertyApiExclusionRule ++ Seq(
      ExclusionRule("com.agoda.commons", "ag-http-client-v2"),
      ExclusionRule("com.agoda.commons", "ag-http-client-mesh-v2"),
      ExclusionRule("com.agoda.adp.messaging")
    )) *),
    "org.scalatest"                 %% "scalatest"                     % scalatest_version             % Test,
    "org.scalatestplus"             %% "mockito-3-4"                   % scalatestplus_mockito_version % Test,
    "org.mockito"                    % "mockito-core"                  % mockito_version               % Test,
    "com.agoda.terracotta" %% "service-clients" % terracotta_client_version excludeAll ExclusionRule(
      "org.scalatest"
    ),
    "com.agoda.capi.enigma"          %% "client"                        % enigma_client_version,
    "com.agoda.creditcardapi.client" %% "creditcardapi-client-scala-v2" % credit_card_api_v2_version,
    "com.agoda.ebe.mpb.commons"      %% "model"                         % mpb_commons,
    "com.softwaremill.quicklens"     %% "quicklens"                     % quicklens_version,
    "com.agoda.experiments"          %% "experiments-platform-client"   % experiments_platform_client_version,
    winterfellClient,
    "org.asynchttpclient" % "async-http-client" % asyncHttpClient excludeAll (
      ExclusionRule(organization = "io.netty")
    ) force (),
    "com.agoda.ancillaryapi" %% "ancillaryapi-client" % ancillary_client excludeAll (
      ExclusionRule("com.github.swagger-akka-http"),
      ExclusionRule("com.typesafe.akka"),
      ExclusionRule(organization = "io.swagger"),
      ExclusionRule(organization = "com.agoda.commons")
    ) force (),
    "com.agoda.ancillaryapi" %% "ancillaryapi-client-v2"       % ancillary_client,
    "com.agoda.commons"      %% "ag-protobuf-scalapb-0-9-7-df" % scalapb_df_version
  )

  val server = Seq(
    "org.scala-lang.modules"            %% "scala-xml"                     % scala_xml_version,
    "org.scala-lang.modules"            %% "scala-parser-combinators"      % scala_parser_version,
    "com.iheart"                        %% "ficus"                         % iheart_ficus_version,
    "com.typesafe.akka"                 %% "akka-actor"                    % akka_version,
    "com.typesafe.akka"                 %% "akka-stream"                   % akka_version,
    "com.typesafe.akka"                 %% "akka-testkit"                  % akka_version % Test,
    "com.typesafe.akka"                 %% "akka-http"                     % akka_http_version,
    "com.typesafe.akka"                 %% "akka-http-spray-json"          % akka_http_version,
    "com.typesafe.akka"                 %% "akka-http-jackson"             % akka_http_version,
    "com.typesafe.akka"                 %% "akka-http-xml"                 % akka_http_version,
    "de.heikoseeberger"                 %% "akka-http-circe"               % heikoseeberger_version,
    "io.circe"                          %% "circe-core"                    % circe_version,
    "io.circe"                          %% "circe-generic"                 % circe_version,
    "io.circe"                          %% "circe-parser"                  % circe_version,
    "commons-io"                         % "commons-io"                    % commons_io_version,
    "org.apache.commons"                 % "commons-compress"              % commons_compress_version,
    "com.agoda.adp.messaging"            % "adp-messaging-client"          % adp_messaging_version,
    "com.typesafe.scala-logging"        %% "scala-logging"                 % scala_logging_version,
    "com.thesamet.scalapb"              %% "scalapb-json4s"                % SCALAPB_JSON_VERSION,
    "com.google.protobuf"                % "protobuf-java"                 % protobuf_version,
    "com.agoda.propertyapi"             %% "client"                        % propertyapi_client_version excludeAll (propertyApiExclusionRule *),
    "com.agoda.booking.creation.client" %% "booking-creation-client-scala" % bcre_version excludeAll (
      ExclusionRule("org.slf4j"),
      ExclusionRule("slf4j-simple"),
      ExclusionRule("org.sonatype.sisu"),
      ExclusionRule("com.typesafe.akka")
    ),
    "net.codingwell"               %% "scala-guice"          % guice_version,
    "com.fasterxml.jackson.module" %% "jackson-module-scala" % jackson_version,
    "com.github.swagger-akka-http" %% "swagger-akka-http"    % swagger_akka_http_version excludeAll (
      ExclusionRule(
        "org.reflections",
        "reflections"
      ),
      ExclusionRule("com.google.guava", "guava")
    ),
    "org.reflections" % "reflections" % "0.9.11" artifacts Artifact(
      "reflections",
      "jar",
      "jar"
    ),
    "io.swagger"         % "swagger-core"         % swagger_version exclude ("com.google.guava", "guava"),
    "io.swagger"         % "swagger-annotations"  % swagger_version exclude ("com.google.guava", "guava"),
    "io.swagger"        %% "swagger-scala-module" % swagger_scala_module_version,
    "com.agoda.gandalf" %% "client"               % gandalf_version excludeAll (
      ExclusionRule("io.circe"),
      ExclusionRule("io.swagger"),
      ExclusionRule("com.fasterxml.jackson.core"),
      ExclusionRule("org.yaml"),
      ExclusionRule("org.slf4j"),
      ExclusionRule("javax.validation"),
      ExclusionRule("org.apache.commons"),
      ExclusionRule("org.yaml"),
      ExclusionRule("javax.ws.rs"),
      ExclusionRule("org.json4s")
    ),
    "com.agoda.experiments"      %% "experiments-platform-client" % experiments_platform_client_version,
    "org.reflections"             % "reflections"                 % "0.9.11",
    "org.scalatest"              %% "scalatest"                   % scalatest_version             % Test,
    "org.scalatestplus"          %% "mockito-3-4"                 % scalatestplus_mockito_version % Test,
    "org.mockito"                 % "mockito-core"                % mockito_version               % Test,
    "com.typesafe.akka"          %% "akka-http-testkit"           % akka_http_version             % Test,
    "com.softwaremill.quicklens" %% "quicklens"                   % quicklens_version             % Test,
    "com.agoda.flights.client"   %% "flights-scala-client-v2"     % flights_api_client,
    "com.agoda.upi"              %% "upi-models"                  % upi_version,
    "org.asynchttpclient"         % "async-http-client"           % asyncHttpClient excludeAll (
      ExclusionRule(organization = "io.netty")
    ),
    "com.agoda.ancillaryapi" %% "ancillaryapi-client" % ancillary_client excludeAll (
      ExclusionRule("com.github.swagger-akka-http"),
      ExclusionRule("com.typesafe.akka"),
      ExclusionRule(organization = "io.swagger"),
      ExclusionRule(organization = "com.agoda.commons")
    ) force (),
    "com.agoda.commons" %% "ag-protobuf-scalapb-0-9-7-df" % scalapb_df_version,
    "com.agoda.cegwl"   %% "cegwl-scala-client"           % ceg_wl_client_version
  ).map(_.exclude("com.agoda.adp.messaging", "adp-messaging-client-shaded"))

  val common = Seq(
    "com.agoda.commons"             %% "ag-observability-sdk-akka-http" % agObservability,
    "com.agoda.commons"             %% "ag-observability-sdk-sttp"      % agObservability,
    "com.agoda.commons"              % "ag-observability-sdk"           % agObservability,
    "com.agoda.commons"              % "ag-vault"                       % ag_vault_version,
    "io.opentelemetry"               % "opentelemetry-exporter-logging" % opentelemetry_version,
    "com.agoda.commons"             %% "ag-http-client-mesh-v2"         % "2.10.4",
    "com.agoda.commons"             %% "ag-http-client-v2"              % "2.10.4", // Force client version as autogen version is old
    "com.agoda.commons"             %% "ag-observability-tracing-scala" % agObservability,
    "com.agoda.commons"              % "ag-observability-sdk"           % agObservability,
    "com.agoda.commons"             %% "ag-observability-tracing-scala" % agObservability,
    "org.asynchttpclient"            % "async-http-client"              % asyncHttpClient,
    "net.codingwell"                %% "scala-guice"                    % guice_version,
    "com.microsoft.sqlserver"        % "mssql-jdbc"                     % mssql_jdbc_version,
    "joda-time"                      % "joda-time"                      % "2.10.2",
    "org.joda"                       % "joda-convert"                   % "2.1.1",
    "com.agoda.capi.enigma"         %% "client"                         % enigma_client_version,
    "com.fasterxml.jackson.module"  %% "jackson-module-scala"           % jackson_version,
    "com.fasterxml.jackson.datatype" % "jackson-datatype-joda"          % jackson_version,
    "io.swagger"                     % "swagger-core"                   % swagger_version exclude ("com.google.guava", "guava"),
    "io.swagger"                     % "swagger-annotations"            % swagger_version exclude ("com.google.guava", "guava"),
    "com.github.cb372"              %% "scalacache-guava"               % scalacache_version,
    "com.agoda.commons"             %% "ag-http-client-mesh"            % old_ag_http_client_mesh_version, // Required by FLAPICustomHttpClientService ServiceMesh
    "com.agoda.finance"             %% "commonfinancetax"               % common_finance_tax_version,
    "com.agoda.experiments"         %% "experiments-platform-client"    % experiments_platform_client_version,
    "com.github.pjfanning"          %% "op-rabbit-core"                 % opRabbitVersion,
    "org.scalatest"                 %% "scalatest"                      % scalatest_version             % Test,
    "org.scalatestplus"             %% "mockito-3-4"                    % scalatestplus_mockito_version % Test,
    "com.typesafe.akka"             %% "akka-http"                      % akka_http_version,
    "com.typesafe.akka"             %% "akka-actor"                     % akka_version,
    "com.typesafe.akka"             %% "akka-stream"                    % akka_version,
    "com.typesafe.akka"             %% "akka-testkit"                   % akka_version                  % Test,
    "org.apache.pekko"              %% "pekko-testkit"                  % pekko_testkit_version         % Test,
    "org.mockito"                    % "mockito-core"                   % mockito_version               % Test,
    "com.danielasfregola"           %% "random-data-generator"          % "2.8"                         % Test,
    "com.softwaremill.diffx"        %% "diffx-scalatest"                % diffx_version                 % Test,
    "com.softwaremill.diffx"        %% "diffx-scalatest-should"         % diffx_version                 % Test,
    "com.agoda.abspnx"              %% "abs-phoenix-client"             % abs_version excludeAll (
      ExclusionRule("org.mockito"),
      ExclusionRule("com.typesafe.akka"),
      ExclusionRule("com.agoda.commons")
    ),
    "com.agoda.commons"           %% "ag-protobuf-scalapb-0-9-7-abs"     % ag_protobuf,
    "com.agoda.commons"           %% "ag-protobuf-scalapb-0-9-7-flights" % ag_protobuf,
    "com.agoda.commons"           %% "ag-grpc-server"                    % ag_grpc_version,
    "com.agoda.experiments"       %% "experiments-platform-client"       % experiments_platform_client_version,
    "org.mockito"                  % "mockito-core"                      % mockito_version % Test,
    "com.agoda.whitelabel.client" %% "wl-client-scala"                   % wl_client_version excludeAll (
      ExclusionRule("org.mockito"),
      ExclusionRule("com.typesafe.akka"),
      ExclusionRule("com.github.blemale")
    ),
    winterfellClient,
    "com.agoda" %% "flights-awo" % flight_awo_version excludeAll (
      ExclusionRule("com.agoda.commons", "ag-protobuf-scalapb-0-9-7-flights_2.12")
    ),
    "com.agoda.car" %% "car-search-client" % vehicle_search_api_version excludeAll (
      ExclusionRule("com.agoda.commons", "ag-http-client-v2"),
      ExclusionRule("com.agoda.commons", "ag-http-client-mesh-v2"),
      ExclusionRule("com.softwaremill.sttp.client3")
    ),
    //  Shouldn't be common dependency
    "com.thesamet.scalapb"  %% "scalapb-json4s" % SCALAPB_JSON_VERSION,
    "com.agoda.propertyapi" %% "client"         % propertyapi_client_version excludeAll (
      (propertyApiExclusionRule ++
        Seq(
          ExclusionRule("com.agoda.commons", "ag-http-client-v2"),
          ExclusionRule("com.agoda.commons", "ag-http-client-mesh-v2")
        )) *
    ),
    "com.agoda.papi"  %% "enum-commons" % enumcommons,
    "com.agoda.dfapi" %% "ypl-models"   % ypl_version excludeAll (
      ExclusionRule("io.github.scalapb-json"),
      ExclusionRule("com.thesamet.scalapb"),
      ExclusionRule("com.agoda.commons", "ag-protobuf-scalapb-0-11-x-df_2.12"),
      ExclusionRule("com.agoda.commons", "ag-protobuf-scalapb-0-11-x-utils_2.12"),
      ExclusionRule("com.agoda.commons", "ag-protobuf-scalapb-0-11-x-hmc_2.12"),
      ExclusionRule("com.agoda.commons", "ag-protobuf-scalapb-0-11-x-ota_2.12")
    ),
    "com.pauldijou"      %% "jwt-circe"         % jwt_circe_version,
    "com.agoda.upi"      %% "upi-models"        % upi_version,
    "org.asynchttpclient" % "async-http-client" % asyncHttpClient excludeAll (
      ExclusionRule(organization = "io.netty")
    ) force (),
    "com.agoda.ebe.mpb.commons" %% "model"           % mpb_commons,
    "com.agoda.terracotta"      %% "service-clients" % terracotta_client_version excludeAll ExclusionRule(
      "org.scalatest"
    ),
    "com.agoda.fraud" %% "fraud-api-scala-client" % "3.2.27" excludeAll (
      ExclusionRule(organization = "org.scalactic"),
      ExclusionRule(organization = "org.scalatest"),
      ExclusionRule(organization = "scalapb"),
      ExclusionRule("com.thesamet.scalapb"),
      ExclusionRule("com.agoda.commons", "ag-protobuf-scalapb-0-9-7_2.12"),
      ExclusionRule("com.agoda.commons", "ag-protobuf-scalapb-0-9-7-flights_2.12")
    ),
    "com.agoda.activity" %% "activity-search-client" % activity_search_api_version excludeAll (
      ExclusionRule("org.scalatest"),
      ExclusionRule("org.mockito"),
      ExclusionRule("com.agoda.commons", "ag-http-client-v2"),
      ExclusionRule("com.agoda.commons", "ag-http-client-mesh-v2"),
      ExclusionRule("io.circe"),
      ExclusionRule("com.softwaremill.sttp.client3")
    ),
    "com.agoda"                           %% "warts-misc"                    % zaidel_warts_version,
    "com.agoda.externalloyalty.client.v2" %% "api-client-v2"                 % external_loyalty_version,
    "com.agoda.commons"                   %% "ag-protobuf-scalapb-0-9-7-df"  % scalapb_df_version,
    "com.agoda.commons"                   %% "ag-logging"                    % ag_logging_version,
    "com.agoda.paymentapiv2.client"       %% "paymentapiv2-client-scala-v2"  % paymentapiv2_version
  ).map(_.exclude("com.agoda.adp.messaging", "adp-messaging-client-shaded"))

  val integration = Seq(
    "org.scalaj"                 %% "scalaj-http"           % "2.3.0",
    "org.scalatest"              %% "scalatest"             % scalatest_version             % Test,
    "org.scalatestplus"          %% "mockito-3-4"           % scalatestplus_mockito_version % Test,
    "com.spotify"                 % "docker-client"         % "8.9.2",
    "com.typesafe.akka"          %% "akka-actor"            % akka_version,
    "com.microsoft.sqlserver"     % "mssql-jdbc"            % mssql_jdbc_version,
    "org.apache.kafka"           %% "kafka"                 % "0.11.0.2",
    "ch.qos.logback"              % "logback-classic"       % logback_version,
    "com.github.java-json-tools"  % "json-schema-validator" % json_schema_validator_version,
    "com.github.tomakehurst"      % "wiremock"              % wiremock_version              % Test,
    "io.circe"                   %% "circe-optics"          % "0.11.0",
    "com.softwaremill.quicklens" %% "quicklens"             % quicklens_version,
    "com.cloudera.impala.jdbc"    % "ImpalaJDBC42"          % "2.6.23.1028"
  ).map(_.exclude("com.agoda.aadp.messaging", "adp-messaging-client-shaded"))

  val regressionTest = Seq(
    "org.scalaj"                   %% "scalaj-http"          % "2.3.0",
    "org.scalatest"                %% "scalatest"            % scalatest_version             % "test",
    "org.scalatestplus"            %% "mockito-3-4"          % scalatestplus_mockito_version % Test,
    "com.agoda.commons"            %% "ag-sql"               % "1.13.0",
    "com.fasterxml.jackson.module" %% "jackson-module-scala" % jackson_version,
    "com.github.tomakehurst"        % "wiremock"             % wiremock_version              % Test,
    "com.microsoft.sqlserver"       % "mssql-jdbc"           % mssql_jdbc_version,
    "com.softwaremill.quicklens"   %% "quicklens"            % quicklens_version,
    "com.github.pureconfig"        %% "pureconfig"           % "0.11.1",
    "com.typesafe.scala-logging"   %% "scala-logging"        % scala_logging_version,
    "com.agoda.commons"            %% "ag-grpc-client"       % ag_grpc_version
  )
  val creationCommon = Seq(
    "com.agoda.upi"                 %% "upi-models"              % upi_version,
    "com.thesamet.scalapb"          %% "scalapb-json4s"          % thesamet_scalapb_json4s_version,
    "com.fasterxml.jackson.module"  %% "jackson-module-scala"    % jackson_version,
    "com.fasterxml.jackson.datatype" % "jackson-datatype-joda"   % jackson_version,
    "com.fasterxml.jackson.datatype" % "jackson-datatype-jsr310" % jackson_version,
    "io.swagger"                     % "swagger-annotations"     % swagger_version exclude ("com.google.guava", "guava"),
    "io.scalaland"                  %% "chimney"                 % "0.6.1",
    "com.agoda.ebe.mpb.commons"     %% "model"                   % mpb_commons
      excludeAll (
        ExclusionRule("org.mockito"),
        ExclusionRule("ch.qos.logback"),
        ExclusionRule("com.agoda.commons"),
        ExclusionRule("com.iheart"),
        ExclusionRule("com.typesafe"),
        ExclusionRule("net.codingwell"),
        ExclusionRule("org.apache.curator"),
        ExclusionRule("org.json4s"),
        ExclusionRule("org.scalaj"),
        ExclusionRule("com.lightbend.akka"),
        ExclusionRule("com.agoda.ebe.mpb.commons"),
        ExclusionRule("com.agoda.adp.messaging"),
        ExclusionRule("com.fasterxml.jackson.core"),
        ExclusionRule("com.fasterxml.jackson.datatype"),
        ExclusionRule("com.fasterxml.jackson.module")
      ),
    "com.typesafe.scala-logging" %% "scala-logging" % scala_logging_version,
    //  mpbe product models
    "com.agoda.commons" %% "ag-protobuf-scalapb-0-9-7-mpbe" % ag_protobuf
      excludeAll (
        ExclusionRule("io.grpc"),
        ExclusionRule("com.thesamet.scalapb", s"scalapb-runtime-grpc_${scalaBinaryVersion}")
      ),
    "org.json4s"         %% "json4s-core"    % json4s_version,
    "org.json4s"         %% "json4s-jackson" % json4s_version,
    "org.json4s"         %% "json4s-ext"     % json4s_version,
    "org.json4s"         %% "json4s-native"  % json4s_version,
    "com.google.protobuf" % "protobuf-java"  % protobuf_version,
    //  Test
    "org.scalatest"          %% "scalatest"                         % scalatest_version               % Test,
    "com.chuusai"            %% "shapeless"                         % shapeless_version               % Test,
    "com.agoda.adp.messaging" % "adp-messaging-client"              % adp_messaging_version,
    "com.typesafe"            % "config"                            % typesafe_config_version,
    "com.thesamet.scalapb"   %% "scalapb-json4s"                    % thesamet_scalapb_json4s_version % Test,
    "com.agoda.adp.messaging" % "adp-messaging-client"              % adp_messaging_version,
    "com.agoda.commons"      %% "ag-protobuf-scalapb-0-9-7-flights" % ag_protobuf,
    "com.pauldijou"          %% "jwt-circe"                         % jwt_circe_version,
    "com.agoda"              %% "flights-awo"                       % flight_awo_version excludeAll (
      ExclusionRule("com.agoda.commons", "ag-protobuf-scalapb-0-9-7-flights_2.12")
    )
  )
}
