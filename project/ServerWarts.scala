import sbt.Keys._
import sbt._
import wartremover.WartRemover.autoImport.wartremoverExcluded

/**
  This is a list of files in `server` module which are excluded from wartremover checks.
  <p>
  DO NOT MODIFY IT.
  */
object ServerWarts {
  lazy val filesToExcludeFromChecks = Seq(
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "cache" / "bookings" / "BookingInvalidation.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "cache" / "InvalidationStrategy.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "config" / "package.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "config" / "ConfigurationProvider.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "config" / "BookingAmendmentConfigProvider.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "directive" / "HeaderDirective.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "exception" / "BookingExceptionForResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "exception" / "HealthcheckException.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "exception" / "PAPITechnicalException.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "exception" / "PrecheckException.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "filter" / "Filter.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "filter" / "FilterReaders.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "filter" / "Interpreter.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "handler" / "ApiFailureMapper.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "handler" / "BookingsHandler.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "handler" / "HealthCheckHandler.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "healthcheck" / "AbsHealthCheck.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "healthcheck" / "BfdbHealthCheck.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "healthcheck" / "CdbHealthCheck.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "healthcheck" / "HealthCheckable.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "healthcheck" / "MdbHealthCheck.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "healthcheck" / "PapiHealthCheck.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "healthcheck" / "UpcHealthCheck.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "localization" / "CmsContext.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "localization" / "CmsContextFactory.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "logging" / "ServiceType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "logging" / "VersionHealthLogMessage.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "logging" / "WhiteFalconLogger.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "logging" / "WhiteFalconLoggingProvider.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "model" / "pricebreakdown" / "BookingFee.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "model" / "pricebreakdown" / "PayAgoda.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "model" / "pricebreakdown" / "PriceBreakdownBuilderParams.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "model" / "pricebreakdown" / "Total.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "model" / "pricebreakdown" / "TotalSurcharge.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "model" / "pricebreakdown" / "TotalSurchargeDetails.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "model" / "pricebreakdown" / "RoomChargePerNight.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "model" / "property" / "BookingProperty.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "model" / "AccountingEntity.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "model" / "BookingCXLDueDateMetadata.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "model" / "Currency.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "model" / "ExchangeRateFromBooking.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "model" / "MasterRoomDetail.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "model" / "PropertyPaymentFilter.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "model" / "RoomInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "proxy" / "PapiHttpClientProvider.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "proxy" / "PapiProxy.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "proxy" / "WhitelabelClientProvider.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "reporting" / "logs" / "BookingRepositoryLog.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "reporting" / "logs" / "PapiRepositoryLog.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "reporting" / "CustomerContactInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "repository" / "dto" / "papi" / "BookingParameter.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "repository" / "dto" / "papi" / "ContextParameter.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "repository" / "dto" / "papi" / "PapiRequestDto.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "repository" / "dto" / "papi" / "PriceParameter.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "repository" / "APIKeyRepository.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "repository" / "CmsRepository.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "repository" / "ConfigurationRepository.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "repository" / "CountriesRepositoryImpl.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "repository" / "CurrenciesRepository.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "repository" / "DmcRepository.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "repository" / "HotelRepository.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "repository" / "PapiRepository.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "repository" / "PaymentApiV2Repository.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "repository" / "PaymentMethodRepository.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "repository" / "PrecheckRepository.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "route" / "BookingRoutes.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "route" / "FlightRoutes.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "route" / "HealthRoutes.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "route" / "HttpHeaderSupport.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "route" / "IpaddressSupport.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "route" / "JacksonSupport.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "route" / "ObjectMapperProvider.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "route" / "Router.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "route" / "Site.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "route" / "SwaggerDocService.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "serialization" / "Deserializer.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "pricebreakdown" / "PriceBreakdownService.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "AllotmentInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "BookingRetrievalOptions.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "BookingsService.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "BookingStatus.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "CommonBookingsService.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "FlightBookingsService.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "FullyAuthChargeDates.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "HealthService.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "Inventory.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "InventoryItem.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "InventoryQuery.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "PayLaterInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "PaymentApiV2Service.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "PropertyResultMapping.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "SpecialRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "Result.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "service" / "Types.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "swagger" / "BapiModelResolver.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "swagger" / "InvalidSwaggerAnnotationValidation.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "utils" / "EbeError.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "utils" / "MoneyFormat.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "utils" / "PartialSuccessFuture.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "server" / "validator" / "BookingsRequestValidator.scala",
  )
}
