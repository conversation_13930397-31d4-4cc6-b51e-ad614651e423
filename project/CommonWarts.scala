import sbt.Keys._
import sbt._
import wartremover.WartRemover.autoImport.wartremoverExcluded

/**
  This is a list of files in `common` module which are excluded from wartremover checks.
  <p>
  DO NOT MODIFY IT.
  */
object CommonWarts {
  lazy val filesToExcludeFromChecks = Seq(
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "token" / "property" / "BookingToken.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "token" / "property" / "SubmitBookingToken.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "token" / "property" / "SetupBookingToken.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "token" / "property" / "PriceGuaranteeToken.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "handler" / "RequestContext.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "handler" / "MeasurementsContext.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "database" / "AGDB.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "database" / "DBWrapper.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "reporting" / "logs" / "RequestResponseLog.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "reporting" / "MessageHelper.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "repository" / "CountriesRepository.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "repository" / "TPRMRepository.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "repository" / "TPRMRepositoryImpl.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "repository" / "LocaleRepository.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "proxy" / "UpcApiProxy.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "proxy" / "TPRMClientProxy.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "proxy" / "AbsProxy.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "proxy" / "FinanceProxy.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "proxy" / "BookingCustomerServiceProvider.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "proxy" / "provider" / "FinanceClientProvider.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "proxy" / "provider" / "UpcApiManagerProvider.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "proxy" / "AbsClientProvider.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "proxy" / "BookingCustomerServiceProxy.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "cache" / "LocalCache.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "util" / "EncryptionHelper.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "util" / "RabbitMqUtility.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "util" / "ServerUtils.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "util" / "XmlDateUtils.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "util" / "RabbitMqPublisher.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "config" / "FinanceApiConfigProvider.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "config" / "AgodaConfigProvider.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "config" / "AgodaConfig.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "config" / "UpcApiConfig.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "config" / "FinanceApiConfig.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "config" / "UpcApiConfigProvider.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "mapper" / "papi" / "PapiResponseConversions.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "mapper" / "papi" / "PapiResponseMapper.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "token" / "TokenMessage.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "pricebreakdown" / "PriceBreakdownNode.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "pricebreakdown" / "PriceBreakdownType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "pricebreakdown" / "PriceBreakdown.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "CancelledInfoResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "RequestBase.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "ChangeBookingGuestsResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "CancelledInfoRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "CancellationInfoResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "GetPaymentFailureInfoRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingCentralDC" / "BookingListByCXLDueDateResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingCentralDC" / "BookingListByCXLDueDateRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingCentralDC" / "SetCxlRemindStatusRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingCentralDC" / "AgodaPaySupportedBookingIdsRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingCentralDC" / "VoucherRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingCentralDC" / "AgodaPaySupportedBookingIdsResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingCentralDC" / "SetCxlRemindStatusResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingCentralDC" / "RawBookingsRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingCentralDC" / "RawBookingsResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "AddCancellationFeeWaiverResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "PrecheckRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "BookingsAffiliateRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "SendVoucherRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "ConfirmAmendmentResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "CancelCancellationRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "flightBookingList" / "response" / "FlightBookingListMMBResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "flightBookingList" / "request" / "FlightBookingListMMBRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "booking" / "PaymentMethodsResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "booking" / "PaymentMethodsRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "CreateCancellationRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "CancelBookingResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "CancelBookingRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "ConfirmAmendmentRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "UpdateBookingResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "UpdateCancellationFeeWaiverStatusRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "SendVoucherResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "ResponseBase.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "DevicePlatform.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeRedirectRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "Swipe3dsRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeLedgerReportResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeOptionsResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeLedgerReportRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "Swipe3dsResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeTransactionListResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeContactUsResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "Swipe3dsCancelRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeStatusRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeRedirectResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeShiftStartRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "Swipe3dsCancelResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeRefundResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeSettlementResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeReceiptResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeShiftEndRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeOptionsRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeChargeRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeContactUsRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeStatusResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeReceiptRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeGetTransactionsRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeSettlementRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeCommentResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeTransactionListRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "package.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeShiftStartResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeShiftStatusResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeShiftStatusRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeShiftEndResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeGetTransactionsResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeCommentRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeChargeResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "swipe" / "SwipeRefundRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "CustomerRiskStatusType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "BookingsV2ResponseContent.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "ConfirmCancellationResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingApproveDecline" / "HostApproveBookingResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingApproveDecline" / "HostDeclineBookingResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingApproveDecline" / "HostApproveBookingRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingApproveDecline" / "HostDeclineBookingRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "BookingsCMResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "ConfirmCancellationRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "BookingsAffiliateResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "CreateDateAmendmentRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "upc" / "CardType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "upc" / "CardStatus.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "upc" / "CcUpcRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "upc" / "GetUpcDataRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "upc" / "GetUpcDataResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "upc" / "UpcApiData.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "upc" / "UpcData.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "UpdateCancellationFeeWaiverStatusResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "BookingsResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "creation" / "ValidationUtils.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "creation" / "GetStatusRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "creation" / "CreateBookingResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "creation" / "ContinuePaymentRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "creation" / "GetStatusResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "creation" / "package.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "CreateDateAmendmentResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "HealthCheckResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "GetPaymentFailureInfoResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "flightPostBooking" / "response" / "FlightPostBookingCCPreAuthResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "flightPostBooking" / "response" / "FlightPostBookingRefundResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "flightPostBooking" / "response" / "FlightPostBookingCCAuthorizedPaymentsResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "flightPostBooking" / "response" / "FlightPostBookingCCChargeResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "flightPostBooking" / "package.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "flightPostBooking" / "request" / "FlightPostBookingRefundRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "flightPostBooking" / "request" / "FlightPostBookingCCAuthorizedPaymentsRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "flightPostBooking" / "request" / "FlightPostBookingCCPreAuthRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "flightPostBooking" / "request" / "FlightPostBookingCCChargeRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "InitializeBookingRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "workflow" / "InsertBookingHistoryResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "workflow" / "InsertBookingHistoryRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "CancelCancellationResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "CancelAmendmentRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "BinRangeRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "PrecheckResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "finance" / "PropertyPaymentMethodResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "finance" / "PropertyPaymentMethodRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "AddCancellationFeeWaiverRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "SendReceiptRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "BinRangeInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "initializeBooking" / "InitializeBookingResponseInfoModel.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "initializeBooking" / "InitializeBookingResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "CancellationInfoRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "BookingsHostResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "BookingsRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "CreateCancellationResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "InitializeBookingStatus.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "HostBookingStatus.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "response" / "BookingAcknowledgeListResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "response" / "BookingListMMBResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "response" / "BookingListCaseManagementResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "response" / "BookingListYCSResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "response" / "BookingsUpdatedListResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "response" / "BookingListRankingResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "response" / "BookingListHostResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "response" / "BookingIdListCaseManagementResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "response" / "BookingListAffiliateResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "request" / "BookingListHostRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "request" / "BookingsUpdatedListRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "request" / "BookingListCaseManagementRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "request" / "BookingAcknowledgeListRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "request" / "BookingListAffiliateRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "request" / "BookingListRankingRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "request" / "BookingListYCSRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "bookingList" / "request" / "BookingListMMBRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "ExperimentData.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "SendReceiptResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "naturalDisaster" / "MarkNaturalDisasterResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "naturalDisaster" / "MarkNaturalDisasterRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "ChangeBookingGuestsRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "CancelAmendmentResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "APIKeyValidationResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "UpdateBookingRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "ToolSet.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "directive" / "HttpHeaderBase.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "json" / "DateWithTZSerializer.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "DailyRateType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "WaiveFeeAsGiftCardOption.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "HotelBookingInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "BookingDates.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "EbeConstants.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "Rewards.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "GiftCardRefund.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "Benefit.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "Language.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingListForCaseManagement" / "CaseManagementFilterStatus.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingListForCaseManagement" / "CaseManagementOrderBy.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingListForCaseManagement" / "CaseManagementOrderBySortOrder.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingListForCaseManagement" / "CaseManagementOrderByKey.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingListForCaseManagement" / "CaseManagementFilter.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingListForCaseManagement" / "CaseManagementSearchType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "PaymentFailureInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "ProductType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "PriceItem.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "AmendmentDetails.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "PaymentInformation.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "CommonSellInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "RateDetail.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "RateCategory.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "RoomInformation.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "BookingCharge.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "BookingAcknowledgedDetails.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "BookingBenefit.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "BookingWithCXLDueDateResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "BookingPaidPayment.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "DomesticTaxReceiptInformation.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "CreditCardInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "BookingWithCxlRemindStatus.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "DiscountInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "BookingAttribution.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "BookingContactInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "GuestInformation.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "CXLReminderStatus.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "HotelInformation.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "CancellationInformation.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "FinancialBreakDown.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "AgentInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "SecureLinkDetails.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "RawBooking.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingCentralDC" / "PriusInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "CancellationPolicy.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "BookingSpecialRequests.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "PaymentType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "CancellationReason.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "LanguageID.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "PaymentInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "Feature.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "BookingChannel.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "Address.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "ChargeDetail.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "OccupancyInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "Error.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "RoomStatusType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "BookingGcIndo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "flightBookingList" / "response" / "FlightMMBResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "BookingBenefits.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "booking" / "PaymentFlow.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "booking" / "RequiredFieldMetadata.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "booking" / "BookingAcknowledgeResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "Booking.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "MoneyUSD.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "SpecialRequestInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "BookingActionStatus.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "AffiliateInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "payment" / "package.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "BookingStatus.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "CheckInOutTime.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "flight" / "response" / "FlightBookingResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "flight" / "BookingDetails.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "flight" / "Flight.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "flight" / "history" / "FlightAction.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "flight" / "history" / "FlightChange.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "Place.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "CancellationCause.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "BookingSetting.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "PaymentPriceItem.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "BookingConfirmation.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "CancellationPolicyPhase.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "BookingAction.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "PaymentMethodFromDB.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "PaymentDetails.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "AdjustmentType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "swipe" / "Redirect3dsParams.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "swipe" / "CreditCard.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "swipe" / "ShiftInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "swipe" / "CreditCardDetails.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "swipe" / "TransactionInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "swipe" / "TransactionDto.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "swipe" / "GatewayReferenceIn.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "swipe" / "PaymentRedirect.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "RoomInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "RewardsPoints.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "WhiteLabelInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "Storefront.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "CancellationPricing.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "PaymentCategory.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "WhiteLabel.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "PaymentModel.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "CommonPaymentItem.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "Paging.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "PayLaterInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "DeviceTypeId.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "HostInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "Money.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "GuestInfoResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "BookingContact.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "APIKey.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "ChannelManagerInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "ChargeOption.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "creation" / "Payment3DSOption.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "creation" / "BapiToken.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "creation" / "DiscountType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "creation" / "CreationDateTime.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "creation" / "BookingDcProcessType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "PaymentMethod.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "BookingActionType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "CancellationInfoResponseContent.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "Country.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "IndexPaging.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "Pagination.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "CancelledInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "AmendmentPricing.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "HealthDetails.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "GeoInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "PropertyRating.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "NonCancelledPaymentDetails.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "Payment.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "OffsetPaging.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "GuestInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "BookingPaymentModel.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "package.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "CancellationClass.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "GiftCard.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "BookingGuests.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "BookingAmendmentDetails.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "CancelledInfoResponseContent.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "precheck" / "response" / "RoomResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "precheck" / "response" / "RateInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "precheck" / "response" / "HotelResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "precheck" / "response" / "ErrorList.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "precheck" / "response" / "SurchargeResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "precheck" / "response" / "PriceDiffResult.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "precheck" / "response" / "PriceToleranceList.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "precheck" / "response" / "Recommendation.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "precheck" / "PrecheckStatusType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "precheck" / "Rate.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "precheck" / "Rule.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "precheck" / "request" / "Hotel.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "precheck" / "request" / "Room.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "precheck" / "request" / "PrecheckDetails.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "precheck" / "RoomRateType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "CreditCardPayment.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "Refund.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "CancellationFeeWaiverStatus.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "GuestInfoRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "PointsMax.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "InvalidRequestDetail.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "SecureLinkMode.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "DuplicateBookingDetails.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "CommonBooking.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingList" / "OrderByType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingList" / "VisibleBookingStatus.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingList" / "response" / "HostResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingList" / "response" / "MMBResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingList" / "response" / "RankingResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingList" / "response" / "AffiliateResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingList" / "BookingStatus.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingList" / "DomesticTaxReceiptStatus.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "bookingList" / "PageType.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "metadata" / "Metadata.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "PropertyInfo.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "BookingSort.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "CancelledPaymentDetails.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "service" / "FeatureAware.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "service" / "CancellationClass.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "service" / "MessagesBag.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "exception" / "PrecheckException.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "exception" / "InternalException.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "exception" / "BookingApiExceptionBase.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "exception" / "ABSTechnicalException.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "localization" / "CmsContextFactory.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "localization" / "CmsItems.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "localization" / "LocaleContext.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "localization" / "CmsContext.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "localization" / "LocaleContextFactory.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "booking" / "BookingStateMessage.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "flight" / "flightModel" / "FlightModelInternal.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "flightBookingSetState" / "SetStateResponse.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "message" / "flightBookingSetState" / "SetStateRequest.scala",
    wartremoverExcluded += baseDirectory.value / "src" / "main" / "scala" / "com" / "agoda" / "bapi" / "common" / "model" / "car" / "VehicleBookingStateModel.scala"
  )
}
