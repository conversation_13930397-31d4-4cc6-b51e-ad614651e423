package com.agoda.integration.flow.mock.config

import com.agoda.bapi.common.config.KillSwitches

class MockKillSwitches extends KillSwitches {

  override def enableStopSendingFlightVersionConflictToBFDB: Boolean = true

  override def enableIpParserCaseInsensitive: Boolean = true

  override def stopLoggingBapiPapiMessage: Boolean = true

  override def stopLoggingBapiItineraryStatusLogMessage: Boolean = true

  override def stopLoggingAbsApiMessage: Boolean = true

  override def stopLoggingBapiMessage: <PERSON><PERSON>an = true

  override def stopLoggingBapiCreateBookingLogMessage: <PERSON><PERSON>an = true

  override def enableAncillaryV2AccuracyChecker: <PERSON><PERSON><PERSON> = true

  override def disableBNPLDebitCardCheck: Boolean = true

  override def enableRebookAndCancelFlow: Boolean = true

  override def sendFlightVersionConflictFallbackMessageToHadoop: <PERSON><PERSON>an = true

  override def enableReturnOriginalUserAgentState: Boolean = true

  override def sendBreakdownListForAbsPrecheck: Boolean = true

}
