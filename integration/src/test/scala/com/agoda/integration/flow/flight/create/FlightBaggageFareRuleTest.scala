package com.agoda.integration.flow.flight.create

import com.agoda.bapi.common.message.creation.{CreateBookingResponse, FlightBooking}
import com.agoda.bapi.common.model.flight.flightModel.{FareRulePolicyModelInternal, FlightBaggageAllowance, Scope}
import com.agoda.bapi.creation.model.db.MultiProductBookingInsertionModel
import com.agoda.integration.flow.fixture.BaseFlowSpec

class FlightBaggageFareRuleTest extends BaseFlowSpec {

  "Create itinerary" should {
    "create response ok" in {
      // Setup booking
      val setupResponse = defaultSetupBooking(getResourcePath)
      defaultSetupBookingAssertions(setupResponse)

      // Extra assertion for setup
      //

      // Create booking
      val createResponse =
        defaultCreateBooking(getResourcePath, setupResponse.bookingResponse.head.bookingToken.head.token)
      defaultCreateBookingAssertions(createResponse)

      // Extra assertion for create
      //
      verifyCreateResponse(createResponse)

      val transactionModel = getTransactionModel
      val bookingIds       = createResponse.itinerary.map(_.flights).getOrElse(Seq.empty).map(_.bookingId)
      val bookingId        = bookingIds.head

      verifyBaggageAllowances(transactionModel, bookingId, isCmsMappingExp = true)
      verifyFareRulePolicies(transactionModel, bookingId, isCmsMappingExp = true)
    }
  }

  private def verifyCreateResponse(createResponse: CreateBookingResponse) = {
    assert(createResponse.success, "Status should be success")
    assert(createResponse.itinerary.map(_.itineraryId).nonEmpty, "itinerary id should not be empty")
    assert(
      createResponse.itinerary.flatMap(_.flights.headOption.map(_.bookingId)).nonEmpty,
      "flight booking id should not be empty"
    )
    assert(createResponse.itinerary.map(_.statusToken).nonEmpty, "polling token should not be empty")
    assert(createResponse.duplicateBookings.isEmpty, "duplicate booking should be empty")

    val transactionModel = getTransactionModel

    assertResult(com.agoda.mpb.common.MultiProductType.SingleFlight.id, "product_type_id")(
      transactionModel.head.workflowActions.filter(_.bookingId.isDefined).head.productTypeId.getOrElse(0)
    )

    createResponse.itinerary.head.flights.sortBy(_.bookingId).zipWithIndex.foreach {
      case (booking, _) => verifyFreeBagAndFareRuleScope(booking)
    }
  }

  private def verifyFreeBagAndFareRuleScope(booking: FlightBooking) = {
    val bookingId          = booking.bookingId
    val flightBookingState = getBookingActionStateByBookingId(bookingId)
    val flight             = flightBookingState.bookingState.map(_.flights).getOrElse(Seq.empty).head
    assert(
      flight.fareRuleScope === Some(Scope.SLICE),
      s"state.fareRuleScope should be ${Scope.SLICE} for bookingId $bookingId"
    )
    assert(
      flight.freeBagScope === Some(Scope.SLICE),
      s"state.freeBagScope should be ${Scope.SLICE} for bookingId $bookingId"
    )
  }

  private def verifyBaggageAllowances(
      transactionModel: Option[MultiProductBookingInsertionModel],
      bookingId: Long,
      isCmsMappingExp: Boolean
  ) = {
    val baggageAllowances = transactionModel.get.flightBookingActionStates
      .filter(_.bookingState.get.bookingId == bookingId)
      .head
      .bookingState
      .get
      .baggageAllowance

    val sliceId = transactionModel.get.flightBookingActionStates
      .filter(_.bookingState.get.bookingId == bookingId)
      .head
      .bookingState
      .get
      .slices
      .head
      .flightSliceId

    assertFreeBag(
      bookingId = bookingId,
      baggageAllowanceInDb = baggageAllowances,
      sliceId = sliceId,
      segmentId = None,
      bagType = 0,
      count = 1,
      maxWeight = Some(25.0),
      totalSizeCm = None,
      price = Some(0.0),
      currency = Some("THB"),
      passengerType = Some("ADT"),
      widthCm = None,
      lengthCm = None,
      heightCm = None,
      source = Some("SUPPLIER"),
      cmsMappingValue = if (isCmsMappingExp) Some("QUANTITY_WEIGHT") else None
    )

    assertFreeBag(
      bookingId = bookingId,
      baggageAllowanceInDb = baggageAllowances,
      sliceId = sliceId,
      segmentId = None,
      bagType = 1,
      count = 1,
      maxWeight = Some(25.0),
      totalSizeCm = None,
      price = Some(0.0),
      currency = Some("THB"),
      passengerType = Some("ADT"),
      widthCm = None,
      lengthCm = None,
      heightCm = None,
      source = Some("SUPPLIER"),
      cmsMappingValue = if (isCmsMappingExp) Some("QUANTITY_WEIGHT") else None
    )
  }

  private def assertFreeBag(
      bookingId: Long,
      baggageAllowanceInDb: Seq[FlightBaggageAllowance],
      sliceId: Long,
      segmentId: Option[Long],
      bagType: Int,
      count: Int,
      maxWeight: Option[Double],
      totalSizeCm: Option[Int],
      price: Option[Double],
      currency: Option[String],
      passengerType: Option[String],
      lengthCm: Option[Int] = None,
      widthCm: Option[Int] = None,
      heightCm: Option[Int] = None,
      source: Option[String] = None,
      cmsMappingValue: Option[String] = None
  ) = {
    assert(
      baggageAllowanceInDb.exists(bag =>
        bag.flightSliceId === sliceId
          && bag.flightSegmentId === segmentId
          && bag.`type` === bagType
          && bag.count === count
          && bag.maxWeightKg === maxWeight
          && bag.totalSizeCm === totalSizeCm
          && bag.priceAmt === price
          && bag.priceCurrency.map(_.toLowerCase) === currency.map(_.toLowerCase)
          && bag.paxType.map(_.toLowerCase) === passengerType.map(_.toLowerCase)
          && bag.lengthCm === lengthCm
          && bag.widthCm === widthCm
          && bag.heightCm === heightCm
          && bag.source === source
          && bag.cmsMappingValue === cmsMappingValue
      ),
      s"Free bag is not exists in database for bookingId $bookingId and sliceId $sliceId"
    )
  }

  private def verifyFareRulePolicies(
      transactionModel: Option[MultiProductBookingInsertionModel],
      bookingId: Long,
      isCmsMappingExp: Boolean
  ) = {
    val fareRulePolicies = transactionModel.get.flightBookingActionStates
      .filter(_.bookingState.get.bookingId == bookingId)
      .head
      .bookingState
      .get
      .fareRulePolicies
      .get

    assertFareRule(
      bookingId = bookingId,
      fareRulePoliciesInDb = fareRulePolicies,
      segmentId = None,
      paxType = "ADT",
      policyType = 1,
      allowed = false,
      penalty = 0.0,
      currency = "USD",
      validUntil = 0,
      validFrom = 0,
      source = Some("SUPPLIER"),
      cmsMappingValue = if (isCmsMappingExp) Some("CANCELLATION_NOT_ALLOWED") else None
    )

    assertFareRule(
      bookingId = bookingId,
      fareRulePoliciesInDb = fareRulePolicies,
      segmentId = None,
      paxType = "ADT",
      policyType = 2,
      allowed = false,
      penalty = 0.0,
      currency = "USD",
      validUntil = 0,
      validFrom = 0,
      source = Some("SUPPLIER"),
      cmsMappingValue = if (isCmsMappingExp) Some("EXCHANGE_NOT_ALLOWED") else None
    )
  }

  private def assertFareRule(
      bookingId: Long,
      fareRulePoliciesInDb: Seq[FareRulePolicyModelInternal],
      segmentId: Option[Long],
      paxType: String,
      policyType: Int,
      allowed: Boolean,
      penalty: Double,
      currency: String,
      validUntil: Int,
      validFrom: Int,
      source: Option[String] = None,
      cmsMappingValue: Option[String] = None
  ) = {
    assert(
      fareRulePoliciesInDb.exists(fareRule =>
        fareRule.paxType.toLowerCase === paxType.toLowerCase
          && fareRule.fareRulePolicyType === policyType
          && fareRule.allowed === allowed
          && fareRule.penalty === penalty
          && fareRule.currencyCode.toLowerCase === currency.toLowerCase
          && fareRule.validUntilHoursBeforeBoarding === validUntil
          && fareRule.validFromHoursBeforeBoarding === validFrom
          && fareRule.segmentId === segmentId
          && fareRule.source === source
          && fareRule.cmsMappingValue === cmsMappingValue
      ),
      s"Fare rule is not exists in database for bookingId $bookingId"
    )
  }
}
