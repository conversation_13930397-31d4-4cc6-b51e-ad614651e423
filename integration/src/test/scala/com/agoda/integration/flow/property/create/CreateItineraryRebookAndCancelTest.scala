package com.agoda.integration.flow.property.create

import com.agoda.bapi.agent.common.schema.MultiProductType
import com.agoda.bapi.common.message.creation.CreateBookingResponse
import com.agoda.bapi.common.model.StatusToken
import com.agoda.bapi.common.model.relationship.RelationshipTypes
import com.agoda.integration.flow.fixture.{BaseFlowSpec, CreateRequestBuilder}
import com.agoda.integration.flow.fixture.TestRoutes.mockKillSwitches
import mocks.MeasurementStubs.{logBookingCreationLogMessageBaseStub, measureStub}

class CreateItineraryRebookAndCancelTest extends BaseFlowSpec {

  def createBookingWithForcedExperiment(
      path: String,
      token: String,
      whiteLabelToken: Option[String] = None,
      shouldEnableTokenV6: Boolean = false
  ): CreateBookingResponse = {
    // Prepare Create Request
    val createRequest = CreateRequestBuilder
      .apply(s"$path/create.json")
      .bookingToken(token)
      .forceExperiment("PBA-338", if (shouldEnableTokenV6) "B" else "A")
      .build()

    // Call /v1/itinerary/create
    create(createRequest, whiteLabelToken)
  }

  "Create itinerary" should
    Seq(true, false).foreach { shouldEnableTokenV6 =>
      s"setup and create booking correctly for rebook and cancel when shouldEnableTokenV6 is $shouldEnableTokenV6" in {

        // Setup booking
        val setupResponse = defaultSetupBooking(getResourcePath)
        defaultSetupBookingAssertions(setupResponse)
        // Create booking
        val createResponse =
          createBookingWithForcedExperiment(
            getResourcePath,
            setupResponse.bookingResponse.head.bookingToken.head.token,
            None,
            shouldEnableTokenV6
          )
        defaultCreateBookingAssertions(createResponse)

        val statusToken = createResponse.itinerary
          .map(_.statusToken)
          .map { input =>
            StatusToken.deserialize(input, logBookingCreationLogMessageBaseStub, measureStub).get
          }
          .get

        if (shouldEnableTokenV6) {
          assert(statusToken.version.get == StatusToken.Version6)
        } else {
          assert(statusToken.version.get == StatusToken.Version4)
        }

        // Extra assertion for create
        val masterActionState  = getMasterActionState
        val bookingActionState = getBookingActionState
        val transactionState   = getTransactionModel

        assert(masterActionState.rebookAndCancelData.nonEmpty)

        masterActionState.bookingState.foreach { bookingState =>
          assert(bookingState.bookingRelationships.nonEmpty)

          val baseBookingRelationship = masterActionState.bookingState
            .flatMap(_.bookingRelationships)
            .map(_.count(_.relationshipTypeId == RelationshipTypes.RebookAndCancel.id))
          assert(
            baseBookingRelationship.getOrElse(0) == 1
          )

          // RxC processed by Cart booking flow but should resolve to SingleProperty in MPBE flow
          assert(transactionState.get.multiProductsInfo.length == 1)
          assert(
            transactionState.get.multiProductsInfo.head.multiProductType.id == MultiProductType.MultiProductType_SingleProperty.value
          )

        }

      }
    }
}
