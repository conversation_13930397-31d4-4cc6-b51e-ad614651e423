package com.agoda.integration.flow.fixture

import com.agoda.bapi.common.model.base.BaseBookingRelationshipInternal
import com.agoda.mpbe.state.booking._
import org.scalatest.OptionValues

import java.time.format.DateTimeFormatter

trait BaseBookingAssertionHelper extends OptionValues {

  protected def assertBaseBooking(
      baseBooking: BaseBooking,
      bookingId: Long,
      itineraryId: Long,
      productId: Int,
      bookingStartDate: String,
      bookingEndDate: Option[String] = None,
      isTestBooking: Boolean = true,
      paymentModel: Int = 1,
      bookingStateId: Int = 0
  ): Unit = {
    assert(baseBooking.bookingId == bookingId)
    assert(baseBooking.itineraryId == itineraryId)
    assert(baseBooking.productId == productId)
    assert(baseBooking.bookingStartDate.format(DateTimeFormatter.ISO_DATE_TIME) == bookingStartDate)
    assert(baseBooking.isTestBooking == isTestBooking)
    assert(baseBooking.paymentModel == paymentModel)
    assert(baseBooking.bookingStateId == bookingStateId)

    if (bookingEndDate.nonEmpty)
      assert(baseBooking.bookingEndDate.map(_.format(DateTimeFormatter.ISO_DATE_TIME)) == bookingEndDate)
  }

  protected def assertBaseBookingOffer(
      baseBookingOffer: BaseBookingOffer,
      bookingId: Long,
      offerTypeId: Int,
      productOfferId: Int,
      quantity: Int,
      startDate: String,
      startTime: Option[String]
  ): Unit = {
    assert(baseBookingOffer.bookingId == bookingId)
    assert(baseBookingOffer.offerTypeId == offerTypeId)
    assert(baseBookingOffer.productOfferId == productOfferId)
    assert(baseBookingOffer.quantity == quantity)
    assert(baseBookingOffer.startDate.format(DateTimeFormatter.ISO_DATE_TIME) == startDate)
    if (startTime.nonEmpty) {
      assert(baseBookingOffer.startTime.map(time => s"${time.hours}:${time.minutes}:${time.seconds}") == startTime)
    }
  }

  protected def assertBaseBookingPax(
      baseBookingPax: BaseBookingPax,
      bookingId: Long,
      paxTypeCode: String,
      isPrimary: Boolean
  ): Unit = {
    assert(baseBookingPax.bookingId == bookingId)
    assert(baseBookingPax.paxTypeCode.toString == paxTypeCode)
    assert(baseBookingPax.isPrimary == isPrimary)
  }

  protected def assertBaseBookingPriceSummary(
      baseBookingPriceSummaries: Seq[BaseBookingPriceSummary],
      bookingId: Long,
      priceType: Int,
      quantity: Int,
      displayCurrency: String,
      baseFare: Double,
      taxAmount: Double = 0,
      feeAmount: Double = 0,
      surchargeAmount: Double = 0,
      baseDiscount: Double = 0,
      totalFare: Double
  ): Unit = {
    val baseBookingPriceSummary = baseBookingPriceSummaries.filter(_.priceType == priceType).head
    assert(baseBookingPriceSummary.bookingId == bookingId)
    assert(baseBookingPriceSummary.priceType == priceType)
    assert(baseBookingPriceSummary.quantity == quantity)
    assert(baseBookingPriceSummary.displayCurrency == displayCurrency)
    assert(baseBookingPriceSummary.baseFare == baseFare)
    assert(baseBookingPriceSummary.taxAmount == taxAmount)
    assert(baseBookingPriceSummary.feeAmount == feeAmount)
    assert(baseBookingPriceSummary.surchargeAmount == surchargeAmount)
    assert(baseBookingPriceSummary.baseDiscount == baseDiscount)
    assert(baseBookingPriceSummary.totalFare == totalFare)
  }

  protected def assertBaseCancellationInfo(
      baseCancellationInfo: BaseCancellationInfo,
      bookingId: Long,
      cancellationPolicyCode: String
  ): Unit = {
    assert(baseCancellationInfo.bookingId == bookingId)
    assert(baseCancellationInfo.isCancelled == 0)
    assert(baseCancellationInfo.cancellationPolicyCode.value == cancellationPolicyCode)
  }

  protected def assertBaseBookingGeo(
      baseBookingGeo: BaseBookingGeo,
      bookingId: Long,
      geoType: String,
      locationType: String,
      geoRef: String,
      addressLine1: String,
      supplierGeoRef: String
  ): Unit = {
    assert(baseBookingGeo.bookingId == bookingId)
    assert(baseBookingGeo.geoType.toString == geoType)
    assert(baseBookingGeo.locationType.toString == locationType)
    assert(baseBookingGeo.geoRef.value == geoRef)
    assert(baseBookingGeo.addressLine1.value == addressLine1)
    assert(baseBookingGeo.supplierGeoRef.value == supplierGeoRef)
  }

  protected def assertBaseBookingMeta(
      baseBookingMetas: Seq[BaseBookingMeta],
      bookingId: Long,
      metaName: String,
      metaType: String,
      metaValue: String
  ): Unit = {
    val baseBookingMeta =
      baseBookingMetas.filter(meta => meta.bookingId == bookingId && meta.metaName.value == metaName).head
    assert(baseBookingMeta.bookingId == bookingId)
    assert(baseBookingMeta.metaName.value == metaName)
    assert(baseBookingMeta.metaType.toString == metaType)
    assert(
      baseBookingMeta.metaValue.value == metaValue,
      s"expected ${metaValue} but get ${baseBookingMeta.metaValue.value}"
    )
  }

  protected def assertBaseSupplierInfo(
      baseSupplierInfo: BaseSupplierInfo,
      bookingId: Long,
      supplierId: Int,
      providerCode: String,
      supplierSpecificData: String
  ): Unit = {
    assert(baseSupplierInfo.bookingId == bookingId)
    assert(baseSupplierInfo.supplierId == supplierId)
    assert(baseSupplierInfo.providerCode.value == providerCode)
    assert(baseSupplierInfo.supplierSpecificData.value == supplierSpecificData)
  }

  protected def assertBaseClientInfo(
      baseClientInfo: BaseClientInfo,
      bookingId: Long,
      whitelabelId: Int,
      languageId: Int,
      customerOrigin: String,
      cid: Option[Int] = None,
      storefrontId: Option[Int] = Some(3),
      platformId: Option[Int] = Some(58),
      trackingCookieId: Option[String] = Some("f5672699-a7f0-4031-9928-35cba7da0fe1"),
      clientIpAddress: Option[String] = Some("127.0.0.1"),
      affiliateModel: Option[Int] = Some(1),
      browserLanguage: Option[String] = Some("en-us"),
      isMobile: Option[Boolean] = Some(false),
      isTouch: Option[Boolean] = Some(false),
      deviceTypeId: Option[Int] = Some(1)
  ): Unit = {
    assert(baseClientInfo.bookingId == bookingId)
    assert(baseClientInfo.whitelabelId == whitelabelId)
    assert(baseClientInfo.languageId == languageId)
    assert(baseClientInfo.customerOrigin.value == customerOrigin)
    assert(baseClientInfo.cid == cid)
    assert(baseClientInfo.storefrontId == storefrontId)
    assert(baseClientInfo.platformId == platformId)
    assert(baseClientInfo.trackingCookieId == trackingCookieId)
    assert(baseClientInfo.trackingCookieDate.nonEmpty)
    assert(baseClientInfo.bookingSessionId.nonEmpty)
    assert(baseClientInfo.clientIpAddress == clientIpAddress)
    assert(baseClientInfo.affiliateModel == affiliateModel)
    assert(baseClientInfo.browserLanguage == browserLanguage)
    assert(baseClientInfo.isMobile == isMobile)
    assert(baseClientInfo.isTouch == isTouch)
    assert(baseClientInfo.deviceTypeId == deviceTypeId)
  }

  protected def assertBaseFinanceInfo(
      baseFinanceInfo: BaseFinanceInfo,
      bookingId: Long,
      merchantOfRecord: Int = 5632,
      revenue: Int = 5659
  ): Unit = {
    assert(baseFinanceInfo.bookingId == bookingId)
    assert(baseFinanceInfo.merchantOfRecord == merchantOfRecord)
    assert(baseFinanceInfo.rateContract == 50001)
    assert(baseFinanceInfo.revenue == revenue)
  }

  protected def assertBaseBookingRelationship(
      baseBookingRelationships: BaseBookingRelationshipInternal,
      sourceBookingId: Long,
      targetBookingId: Long,
      relationshipStatusId: Int = 1,
      relationshipTypeId: Int = 1
  ): Unit = {
    assert(baseBookingRelationships.sourceBookingId == sourceBookingId)
    assert(baseBookingRelationships.targetBookingId == targetBookingId)
    assert(baseBookingRelationships.relationshipStatusId == relationshipStatusId)
    assert(baseBookingRelationships.relationshipTypeId == relationshipTypeId)
  }

  protected def assertBaseEssInfo(
      essInfo: BaseBookingEssInfo,
      bookingId: Long,
      userTaxCountryId: Int = 106,
      paymentInstrumentCountryId: Int = 106,
      ipAddressCountryId: Int = 106
  ): Unit = {
    assert(essInfo.bookingId == bookingId)
    assert(essInfo.userTaxCountryId == userTaxCountryId)
    assert(essInfo.paymentInstrumentCountryId.value == paymentInstrumentCountryId)
    assert(essInfo.ipAddressCountryId.value == ipAddressCountryId)
  }
}
