package com.agoda.integration.flow.fixture

import com.agoda.bapi.common.message.creation.CreatedBookingStatus
import com.agoda.bapi.common.model.{ActionId, BookingId, ItineraryId, StatusToken}
import com.agoda.bapi.common.model.StatusToken.{Version4, Version6}
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.creation.model.db.EbeLiteBookingObject
import com.agoda.integration.flow.fixture.TestRoutes.{mockDefaultDbProxy, mockKillSwitches}
import mocks.MeasurementStubs

trait RebookAndCancelStatusFixture {
  val itineraryId = 99000
  protected def propertyBookingIdForAttempt(itineraryId: ItineraryId, attemptId: Int): BookingId =
    (itineraryId + attemptId + "1").toInt

  protected def getMockBookingAction(attemptId: Int, isMasterAction: Boolean) = {

    def masterActionId(itineraryId: ItineraryId): ActionId     = (itineraryId + attemptId + "0").toLong
    def propertyBookingId(itineraryId: ItineraryId): BookingId = propertyBookingIdForAttempt(itineraryId, attemptId)

    val masterActionIdState: ActionId = masterActionId(itineraryId)
    val masterActionState: String =
      s"""{\"request\":{\"affiliateModel\":1,\"userId\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\",\"storefrontId\":3,\"siteId\":1556943,\"referralUrl\":\"\",\"platformId\":1,\"trackingCookieId\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\",\"sessionId\":\"Test\",\"userAgent\":{\"origin\":\"A1\",\"osName\":\"\",\"osVersion\":\"\",\"browserName\":\"\",\"browserLanguage\":\"en-us\",\"browserVersion\":\"\",\"browserSubVersion\":\"\",\"browserBuildNumber\":\"\",\"deviceBrand\":\"\",\"deviceModel\":\"\",\"deviceTypeId\":1,\"isMobile\":false,\"isTouch\":false,\"additionalInfo\":\"\"},\"clientIp\":\"***********\",\"userContext\":{\"languageId\":1,\"requestOrigin\":\"A1\",\"currency\":\"usd\",\"nationalityId\":0,\"experimentData\":{\"userId\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\",\"deviceTypeId\":\"1\",\"trafficGroup\":\"4\",\"cId\":\"1556943\",\"aId\":\"129878\",\"force\":{\"PACKAGES\":\"B\"}},\"isLoggedInUser\":false},\"isPassportRequired\":false,\"isNationalityRequired\":false},\"customer\":{\"memberId\":0,\"isUserLoggedIn\":false},\"paymentInfo\":{\"method\":1,\"paymentCurrency\":\"USD\",\"paymentAmount\":235.65,\"paymentAmountUSD\":235.65,\"accountingEntity\":{\"merchantOfRecord\":5632,\"rateContract\":5632,\"revenue\":5632},\"siteExchangeRate\":1.0,\"destinationExchangeRate\":0.0,\"rateQuoteId\":0,\"paymentOption\":0,\"displayPricebreakdown\":{\"value\":{\"type\":1,\"amount\":{\"amount\":257.79,\"currencyCode\":\"USD\"},\"originalAmount\":{\"amount\":0.0,\"currencyCode\":\"USD\"}},\"breakdowns\":[{\"value\":{\"type\":2,\"amount\":{\"amount\":235.65,\"currencyCode\":\"USD\"}},\"breakdowns\":[{\"value\":{\"type\":4,\"amount\":{\"amount\":235.65,\"currencyCode\":\"USD\"}},\"breakdowns\":[{\"value\":{\"type\":5,\"amount\":{\"amount\":96.08,\"currencyCode\":\"USD\"},\"averageAmountPerUnit\":{\"amount\":96.08,\"currencyCode\":\"USD\"}}},{\"value\":{\"type\":11,\"amount\":{\"amount\":0.0,\"currencyCode\":\"USD\"}}},{\"value\":{\"type\":10,\"amount\":{\"amount\":139.57,\"currencyCode\":\"USD\"}},\"breakdowns\":[{\"value\":{\"type\":9,\"amount\":{\"amount\":0.0,\"currencyCode\":\"USD\"},\"title\":\"\"}},{\"value\":{\"type\":9,\"amount\":{\"amount\":0.0,\"currencyCode\":\"USD\"},\"title\":\"Tax\"}}]}]}]},{\"value\":{\"type\":3,\"amount\":{\"amount\":22.14,\"currencyCode\":\"USD\"}},\"breakdowns\":[{\"value\":{\"type\":12,\"amount\":{\"amount\":22.14,\"currencyCode\":\"USD\"}},\"breakdowns\":[{\"value\":{\"type\":9,\"amount\":{\"amount\":0.0,\"currencyCode\":\"USD\"},\"title\":\"Resort Fee (Pay at the property)\"}}]}]},{\"value\":{\"type\":15,\"amount\":{\"amount\":0.0,\"currencyCode\":\"USD\"}}}]},\"points\":[{\"pointType\":1,\"pointAttributes\":{\"currency\":\"USD\",\"amount\":0.0}}]},\"creditCardInfo\":{\"creditCardType\":1,\"chargeOption\":0,\"paymentOption\":0,\"creditCardId\":0},\"payment3DS\":{\"payment3DSOption\":2,\"acceptHeader\":\"*/*\",\"userAgent\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_4) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1 Safari/605.1.15\"},\"bookingState\":{\"actionType\":1,\"actionId\":$masterActionIdState,\"bookingId\":0,\"schemaVersion\":\"2\",\"flights\":[],\"slices\":[],\"segments\":[],\"passengers\":[],\"payments\":[],\"breakdown\":[],\"breakdownPerPax\":[],\"baggageAllowance\":[],\"segmentInfoByPaxType\":[],\"history\":[{\"actionId\":$masterActionIdState,\"itineraryId\":$itineraryId,\"actionType\":1,\"version\":0,\"actionDate\":\"2020-04-26 21:13:23.879\",\"parameters\":\"\",\"description\":\"Created\"}],\"paxTickets\":[],\"itinerary\":{\"itineraryId\":$itineraryId,\"memberId\":0},\"summary\":[],\"bookingAttribution\":[],\"itineraryDate\":\"2020-04-26 21:13:23.880\"}}"""

    val propertyBookingIdState: BookingId = propertyBookingId(itineraryId)
    val propertyBookingState =
      s"""{\"request\":{\"affiliateModel\":1,\"userId\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\",\"storefrontId\":3,\"siteId\":1556943,\"referralUrl\":\"\",\"platformId\":1,\"trackingCookieId\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\",\"sessionId\":\"Test\",\"userAgent\":{\"origin\":\"A1\",\"osName\":\"\",\"osVersion\":\"\",\"browserName\":\"\",\"browserLanguage\":\"en-us\",\"browserVersion\":\"\",\"browserSubVersion\":\"\",\"browserBuildNumber\":\"\",\"deviceBrand\":\"\",\"deviceModel\":\"\",\"deviceTypeId\":1,\"isMobile\":false,\"isTouch\":false,\"additionalInfo\":\"\"},\"clientIp\":\"***********\",\"userContext\":{\"languageId\":1,\"requestOrigin\":\"A1\",\"currency\":\"usd\",\"nationalityId\":0,\"experimentData\":{\"userId\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\",\"deviceTypeId\":\"1\",\"trafficGroup\":\"4\",\"cId\":\"1556943\",\"aId\":\"129878\",\"force\":{\"PACKAGES\":\"B\"}},\"isLoggedInUser\":false},\"isPassportRequired\":false,\"isNationalityRequired\":false},\"customer\":{\"memberId\":0,\"isUserLoggedIn\":false},\"paymentInfo\":{\"method\":1,\"paymentCurrency\":\"USD\",\"paymentAmount\":8.27,\"paymentAmountUSD\":8.27,\"accountingEntity\":{\"merchantOfRecord\":5632,\"rateContract\":5632,\"revenue\":5632},\"siteExchangeRate\":1.0,\"destinationExchangeRate\":1.0,\"rateQuoteId\":0,\"paymentOption\":0,\"points\":[{\"pointType\":1,\"pointAttributes\":{\"currency\":\"USD\",\"amount\":0.0}}]},\"creditCardInfo\":{\"creditCardType\":1,\"chargeOption\":0,\"paymentOption\":0,\"creditCardId\":0},\"payment3DS\":{\"payment3DSOption\":2,\"acceptHeader\":\"*/*\",\"userAgent\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_4) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1 Safari/605.1.15\"},\"propertyBookingState\":{\"itinerary\":{\"itineraryId\":$itineraryId,\"itineraryStatusId\":1,\"itineraryTypeId\":1,\"itineraryName\":\"Booking XML API\",\"itineraryDate\":\"2020-04-26 21:13:23.868\",\"isAutoGenerated\":true,\"tripStartDate\":\"2020-05-01 00:00:00.000\",\"tripEndDate\":\"2020-05-02 00:00:00.000\",\"isMultipleBookings\":true,\"isProcessed\":false,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},\"bookingList\":[{\"additionalData\":{\"destinationCityId\":9641,\"destinationCountryId\":35},\"booking\":{\"bookingId\":$propertyBookingIdState,\"itineraryId\":$itineraryId,\"dataCenter\":\"\",\"bookingDate\":\"2020-04-26 21:13:23.868\",\"bookingDateFrom\":\"2020-05-01 00:00:00.000\",\"bookingDateUntil\":\"2020-05-02 00:00:00.000\",\"arrivalTimeWindow\":1,\"availabilityType\":1,\"paymentModel\":1,\"ccReceived\":3,\"rewardsSpecialOfferId\":0,\"cancellationPolicyCode\":\"1D1N_100P\",\"cancellationPolicy\":\"Any cancellation received within 1 day prior to the arrival date will incur the first night''s charge.If you fail to arrive or cancel the booking, no refund will be given.\",\"dmcId\":332,\"dmcCode\":\"YCS\",\"dmcSpecificData\":\"\",\"isAutoProcessed\":false,\"workflowId\":1,\"workflowStateId\":103,\"workflowActionId\":1,\"workflowStateSince\":\"2020-04-26 21:13:23.868\",\"cidList\":\"1556943\",\"sessionId\":\"Test\",\"serverName\":\"\",\"clientIpAddress\":\"***********\",\"referralUrl\":\"\",\"ssResultId\":0,\"ssId\":0,\"ssResultType\":0,\"ssProviderId\":0,\"languageId\":1,\"storefrontId\":3,\"isFraudReview\":false,\"reviewBy\":\"00000000-0000-0000-0000-000000000000\",\"discountType\":0,\"discountAmount\":0.0,\"discountSavings\":0.0,\"ycsPromotionId\":0,\"included\":\"\",\"excluded\":\"\",\"trackingCookieId\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\",\"trackingCookieDate\":\"2020-04-26 21:13:23.000\",\"affiliateModel\":1,\"affiliatePaymentMethod\":0,\"pointMultiply\":0,\"rateChannel\":1,\"promotionCampaignId\":0,\"membershipContentText\":\"\",\"membershipContentLabel\":\"\",\"rateModelType\":2,\"preBookingId\":0,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},\"bookingAgent\":{\"bookingId\":$propertyBookingIdState,\"origin\":\"A1\",\"osName\":\"\",\"osVersion\":\"\",\"browserName\":\"\",\"browserLanguage\":\"en-us\",\"browserVersion\":\"\",\"browserSubversion\":\"\",\"browserBuildNumber\":\"\",\"deviceBrand\":\"\",\"deviceModel\":\"\",\"isMobile\":false,\"isTouch\":false,\"deviceInfo\":\"{\\\"Origin\\\":\\\"A1\\\",\\\"OsName\\\":\\\"\\\",\\\"OsVersion\\\":\\\"\\\",\\\"BrowserName\\\":\\\"\\\",\\\"BrowserLanguage\\\":\\\"en-us\\\",\\\"BrowserVersion\\\":\\\"\\\",\\\"BrowserSubVersion\\\":\\\"\\\",\\\"BrowserBuildNumber\\\":\\\"\\\",\\\"DeviceBrand\\\":\\\"\\\",\\\"DeviceModel\\\":\\\"\\\",\\\"IsMobile\\\":false,\\\"IsTouch\\\":false,\\\"DeviceTypeId\\\":1}\",\"additionalInfo\":\"\",\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},\"bookingSummary\":{\"bookingId\":$propertyBookingIdState,\"multiProductId\":7,\"displayCurrency\":\"USD\",\"originalSellingAmount\":8.27,\"originalSupplierAmount\":6.86,\"originalLocalCurrency\":\"INR\",\"originalLocalSupplierAmount\":464.8,\"originalPaymentCurrency\":\"USD\",\"originalLocalPaymentAmount\":8.27,\"originalLocalCcPaymentAmount\":8.27,\"platformId\":1,\"chargeOptionId\":1,\"originalFullyChargeDate\":\"1970-01-01 07:00:00.000\",\"isUserLoggedIn\":false,\"isTravelAgency\":false,\"travelAgencyLoginChannel\":\"\",\"originalFullyAuthDate\":\"1970-01-01 07:00:00.000\",\"taxSurchargeInfo\":\"Included : Taxes and fees USD 0.89<br>Not Included : Resort Fee (Pay at the property) USD 22.14\",\"isNotCcRequired\":false,\"accountingEntity\":\"{\\\"ContractTypeId\\\":0,\\\"FapiaoInfo\\\":{\\\"IsDigitalGeneralEnabled\\\":false,\\\"IsPhysicalSpecialEnabled\\\":false},\\\"IsDomesticTaxReceiptEnabled\\\":false,\\\"MerchantOfRecord\\\":5632,\\\"MerchantOfRecordType\\\":1,\\\"RateContract\\\":5632,\\\"RateContractType\\\":1,\\\"Revenue\\\":5632,\\\"RevenueType\\\":1}\",\"bookingCreatedDate\":\"2020-04-26 21:13:23.868\",\"whitelabelId\":1,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},\"bookingAttributionV2\":[],\"bookingHotel\":{\"referenceId\":1,\"bookingId\":$propertyBookingIdState,\"hotelId\":297389,\"checkInDate\":\"2020-05-01 00:00:00.000\",\"checkOutDate\":\"2020-05-02 00:00:00.000\",\"noOfRooms\":1,\"noOfAdults\":2,\"noOfChildren\":0,\"flightNumber\":\"\",\"airportTransfer\":false,\"otherSpecialNeeds\":\"AdditionalNotes:201|201 201|201\",\"noExtraBedRateChoice\":0,\"occupancyFreeSearch\":false,\"isNha\":false,\"isAgodaReception\":false,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},\"bookingHotelRoom\":[{\"referenceId\":1,\"bookingHotelReferenceId\":1,\"bookingId\":$propertyBookingIdState,\"roomTypeId\":10365360,\"roomTypeName\":\"Penthouse\",\"roomNo\":1,\"occupancy\":2,\"noOfExtrabeds\":0,\"breakfastIncluded\":false,\"breakfastInfo\":\"\",\"roomRemark\":\"\",\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"}],\"fraudInfo\":{\"bookingId\":$propertyBookingIdState,\"isAcknowledgedPolicy\":false,\"acknowledgedDate\":\"2020-04-26 21:13:23.868\",\"isActive\":true,\"createdWhen\":\"2020-04-26 21:13:23.868\"},\"financialBreakdown\":[{\"referenceId\":1001,\"bookingRoomReferenceId\":1,\"bookingId\":$propertyBookingIdState,\"dateOfStay\":\"2020-05-01 00:00:00.000\",\"itemId\":10,\"typeId\":1,\"surchargeId\":0,\"taxFeeId\":0,\"quantity\":1,\"localCurrency\":\"INR\",\"localAmount\":464.8,\"exchangeRate\":67.7507,\"usdAmount\":6.86,\"isTransfer\":false,\"hotelPaymentMethodId\":0,\"taxPrototypeId\":0,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},{\"referenceId\":1002,\"bookingRoomReferenceId\":1,\"bookingId\":$propertyBookingIdState,\"dateOfStay\":\"2020-05-01 00:00:00.000\",\"itemId\":2,\"typeId\":1,\"surchargeId\":0,\"taxFeeId\":52,\"quantity\":1,\"localCurrency\":\"INR\",\"localAmount\":49.8,\"exchangeRate\":67.7507,\"usdAmount\":0.73,\"isTransfer\":false,\"hotelPaymentMethodId\":0,\"taxPrototypeId\":0,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},{\"referenceId\":1003,\"bookingRoomReferenceId\":1,\"bookingId\":$propertyBookingIdState,\"dateOfStay\":\"2020-05-01 00:00:00.000\",\"itemId\":12,\"typeId\":1,\"surchargeId\":0,\"taxFeeId\":0,\"quantity\":1,\"localCurrency\":\"INR\",\"localAmount\":560.0,\"exchangeRate\":67.7507,\"usdAmount\":8.27,\"isTransfer\":false,\"hotelPaymentMethodId\":0,\"taxPrototypeId\":0,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},{\"referenceId\":1004,\"bookingRoomReferenceId\":1,\"bookingId\":$propertyBookingIdState,\"dateOfStay\":\"2020-05-01 00:00:00.000\",\"itemId\":1,\"typeId\":1,\"surchargeId\":0,\"taxFeeId\":0,\"quantity\":1,\"localCurrency\":\"INR\",\"localAmount\":415.0,\"exchangeRate\":67.7507,\"usdAmount\":6.13,\"isTransfer\":false,\"hotelPaymentMethodId\":0,\"taxPrototypeId\":0,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},{\"referenceId\":1005,\"bookingRoomReferenceId\":1,\"bookingId\":$propertyBookingIdState,\"dateOfStay\":\"2020-05-01 00:00:00.000\",\"itemId\":11,\"typeId\":1,\"surchargeId\":0,\"taxFeeId\":0,\"quantity\":1,\"localCurrency\":\"INR\",\"localAmount\":500.0,\"exchangeRate\":67.7507,\"usdAmount\":7.38,\"isTransfer\":false,\"hotelPaymentMethodId\":0,\"taxPrototypeId\":0,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},{\"referenceId\":1006,\"bookingRoomReferenceId\":1,\"bookingId\":$propertyBookingIdState,\"dateOfStay\":\"2020-05-01 00:00:00.000\",\"itemId\":5,\"typeId\":1,\"surchargeId\":0,\"taxFeeId\":0,\"quantity\":1,\"localCurrency\":\"INR\",\"localAmount\":10.2,\"exchangeRate\":67.7507,\"usdAmount\":0.16,\"isTransfer\":false,\"hotelPaymentMethodId\":0,\"taxPrototypeId\":0,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},{\"referenceId\":1007,\"bookingRoomReferenceId\":1,\"bookingId\":$propertyBookingIdState,\"dateOfStay\":\"2020-05-01 00:00:00.000\",\"itemId\":0,\"typeId\":1,\"surchargeId\":0,\"taxFeeId\":0,\"quantity\":1,\"localCurrency\":\"INR\",\"localAmount\":95.2,\"exchangeRate\":67.7507,\"usdAmount\":1.41,\"isTransfer\":false,\"hotelPaymentMethodId\":0,\"taxPrototypeId\":0,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},{\"referenceId\":1008,\"bookingRoomReferenceId\":1,\"bookingId\":$propertyBookingIdState,\"dateOfStay\":\"2020-05-01 00:00:00.000\",\"itemId\":49,\"typeId\":1,\"surchargeId\":0,\"taxFeeId\":0,\"quantity\":1,\"localCurrency\":\"INR\",\"localAmount\":0.0,\"exchangeRate\":67.7507,\"usdAmount\":0.0,\"isTransfer\":false,\"hotelPaymentMethodId\":0,\"taxPrototypeId\":0,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},{\"referenceId\":1009,\"bookingRoomReferenceId\":1,\"bookingId\":$propertyBookingIdState,\"dateOfStay\":\"2020-05-01 00:00:00.000\",\"itemId\":3,\"typeId\":1,\"surchargeId\":0,\"taxFeeId\":0,\"quantity\":1,\"localCurrency\":\"INR\",\"localAmount\":85.0,\"exchangeRate\":67.7507,\"usdAmount\":1.25,\"isTransfer\":false,\"hotelPaymentMethodId\":0,\"taxPrototypeId\":0,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},{\"referenceId\":1010,\"bookingRoomReferenceId\":1,\"bookingId\":$propertyBookingIdState,\"dateOfStay\":\"2020-05-01 00:00:00.000\",\"itemId\":48,\"typeId\":1,\"surchargeId\":0,\"taxFeeId\":0,\"quantity\":1,\"localCurrency\":\"INR\",\"localAmount\":49.8,\"exchangeRate\":67.7507,\"usdAmount\":0.73,\"isTransfer\":false,\"hotelPaymentMethodId\":0,\"taxPrototypeId\":0,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},{\"referenceId\":1011,\"bookingRoomReferenceId\":1,\"bookingId\":$propertyBookingIdState,\"dateOfStay\":\"2020-05-01 00:00:00.000\",\"itemId\":41,\"typeId\":1,\"surchargeId\":0,\"taxFeeId\":0,\"quantity\":1,\"localCurrency\":\"INR\",\"localAmount\":560.0,\"exchangeRate\":67.7507,\"usdAmount\":8.27,\"isTransfer\":false,\"hotelPaymentMethodId\":0,\"taxPrototypeId\":0,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},{\"referenceId\":1012,\"bookingRoomReferenceId\":1,\"bookingId\":$propertyBookingIdState,\"dateOfStay\":\"2020-05-01 00:00:00.000\",\"itemId\":44,\"typeId\":1,\"surchargeId\":0,\"taxFeeId\":0,\"quantity\":1,\"localCurrency\":\"INR\",\"localAmount\":0.0,\"exchangeRate\":67.7507,\"usdAmount\":0.01,\"isTransfer\":false,\"hotelPaymentMethodId\":0,\"taxPrototypeId\":0,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},{\"referenceId\":1013,\"bookingRoomReferenceId\":1,\"bookingId\":$propertyBookingIdState,\"dateOfStay\":\"2020-05-01 00:00:00.000\",\"itemId\":43,\"typeId\":1,\"surchargeId\":0,\"taxFeeId\":0,\"quantity\":1,\"localCurrency\":\"INR\",\"localAmount\":10.2,\"exchangeRate\":67.7507,\"usdAmount\":0.15,\"isTransfer\":false,\"hotelPaymentMethodId\":0,\"taxPrototypeId\":0,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"}],\"bookingSucc\":[],\"charges\":[{\"referenceId\":1001,\"bookingRoomReferenceId\":1,\"bookingId\":$propertyBookingIdState,\"chargeDate\":\"2020-05-01 00:00:00.000\",\"chargeTypeId\":40,\"chargeName\":\"Room Charge\",\"description\":\"Room Charge\",\"quantity\":1,\"unitOfMeasure\":\"ROM\",\"unitSellingPrice\":8.27,\"unitSupplierPrice\":6.86,\"sellingAmount\":8.27,\"sellingTax\":0.73,\"supplierAmount\":6.86,\"supplierTax\":0.73,\"localSupplierAmount\":464.8,\"localSupplierTax\":49.8,\"localCurrency\":\"INR\",\"exchangeRateLocalToSupplier\":67.7507,\"exchangeRateLastUpdate\":\"2020-04-26 21:13:23.868\",\"refChargeId\":0,\"isRecorded\":0,\"localSellingAmount\":560.0,\"hasBreakdown\":true,\"isBreakdownProcessed\":true,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"}],\"sellInfo\":{\"bookingId\":$propertyBookingIdState,\"downLiftAmountUsd\":0.0,\"sellTagId\":0,\"searchId\":\"54b4d8a0-50c9-44a1-b6b8-c0eba60161fa\",\"isAdvanceGuarantee\":false,\"offerId\":\"54b4d8a0-50c9-44a1-b6b8-c0eba60161fa76d5a2ac-8cd8-f5e9-f33c-534f0c48b4b0\",\"lastUpdatedWhen\":\"2020-04-26 21:13:23.868\",\"lastUpdatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},\"sellInfoHistory\":[{\"historyActionId\":1,\"historyActionDate\":\"2020-04-26 21:13:23.868\",\"historyActionBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\",\"bookingId\":$propertyBookingIdState,\"downliftAmountUsd\":0.0,\"sellTagId\":0,\"lastUpdatedWhen\":\"2020-04-26 21:13:23.868\",\"searchId\":\"54b4d8a0-50c9-44a1-b6b8-c0eba60161fa\",\"isAdvanceGuarantee\":false,\"lastUpdatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"}],\"rateCategory\":{\"referenceId\":1,\"bookingId\":$propertyBookingIdState,\"rateCategoryId\":660678,\"rateCategoryCode\":\"\",\"chargeTypeCode\":\"PRPN\",\"rateCategoryValue\":0.0,\"isActive\":true,\"isRoh\":false,\"lastUpdatedWhen\":\"2020-04-26 21:13:23.868\",\"lastUpdatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"},\"rateCategoryBenefit\":[],\"paymentsAdvanceInfo\":[],\"payment\":[],\"provisioning\":[{\"referenceId\":1,\"bookingId\":$propertyBookingIdState,\"dmcId\":332,\"methodId\":1,\"dmcDueDate\":\"2020-04-29 23:00:00.000\",\"localDmcCurrency\":\"INR\",\"originalNetRate\":464.8,\"netRate\":464.8,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"}],\"propertyBookingPax\":[{\"referenceId\":1,\"bookingRoomReferenceId\":1,\"guestNo\":1,\"title\":\"\",\"firstName\":\"xxx\",\"middleName\":\"\",\"lastName\":\"x\",\"suffix\":\"\",\"isExtraBed\":false,\"age\":0,\"nationalityId\":0,\"recStatus\":1,\"recCreatedWhen\":\"2020-04-26 21:13:23.868\",\"recCreatedBy\":\"f5672699-a7f0-4031-9928-35cba7da0fe1\"}]}]},\"riskInfo\":{\"cancellationType\":\"FreeCancellation\"}, \"campaignAction\":{\"campaignType\":5,\"campaignId\":\"1594323\",\"promotionCode\":\"1234567\",\"attributes\":[]}}"""

    BookingWorkflowAction(
      actionId = if (isMasterAction) masterActionId(itineraryId) else propertyBookingId(itineraryId),
      itineraryId = itineraryId,
      bookingType = None,
      bookingId = if (isMasterAction) None else Some(propertyBookingId(itineraryId)),
      memberId = 0,
      actionTypeId = 1,
      correlationId = "3e4c75f8-1414-417c-a55c-9b49a8768ae2",
      requestId = "3e4c75f8-1414-417c-a55c-9b49a8768ae2",
      workflowId = 0,
      workflowStateId = 0,
      productTypeId = Some(5),
      stateSchemaVersion = 1,
      state = if (isMasterAction) masterActionState else propertyBookingState,
      storefrontId = Some(3),
      languageId = Some(1),
      operationId = Some(originalOperationId)
    )
  }

  val preAuthRejectedState = "{\"rejectRootCause\": 419}"

  val bookingData =
    """{"BookingId":null,"DuplicatedBooking":{"DuplicateBookingItems":[]},"FraudData":null,"PaymentData":null,"RequestHotelIndex":0,"RequestRoomIndex":0,"UpcData":null,"AbsData":null,"ResponseData":null}"""

  val originalOperationId: Long = 10000000L
  val failedOperationId: Long   = originalOperationId + 1
  val successOperationId: Long  = originalOperationId + 2

  val originalBooking: Seq[BookingWorkflowAction] = Seq(
    getMockBookingAction(0, isMasterAction = true).copy(
      operationId = Some(originalOperationId)
    ),
    getMockBookingAction(0, isMasterAction = false).copy(
      operationId = Some(originalOperationId)
    )
  )

  val failedBooking: Seq[BookingWorkflowAction] = Seq(
    getMockBookingAction(1, isMasterAction = true).copy(
      operationId = Some(failedOperationId),
      state = preAuthRejectedState
    ),
    getMockBookingAction(1, isMasterAction = false).copy(
      operationId = Some(failedOperationId),
      state = preAuthRejectedState
    )
  )

  val successBooking: Seq[BookingWorkflowAction] = Seq(
    getMockBookingAction(2, isMasterAction = true).copy(
      operationId = Some(successOperationId)
    ),
    getMockBookingAction(2, isMasterAction = false).copy(
      operationId = Some(successOperationId)
    )
  )

  val originalBookingObj: EbeLiteBookingObject = EbeLiteBookingObject(
    itineraryId = itineraryId,
    bookingId = propertyBookingIdForAttempt(itineraryId, 0),
    bookingData = bookingData,
    stateId = Some(CreatedBookingStatus.BookingConfirmed.id)
  )

  val failedBookingObj: EbeLiteBookingObject = EbeLiteBookingObject(
    itineraryId = itineraryId,
    bookingId = propertyBookingIdForAttempt(itineraryId, 1),
    bookingData = bookingData,
    stateId = Some(CreatedBookingStatus.BookingRejected.id)
  )

  val successBookingObj: EbeLiteBookingObject = EbeLiteBookingObject(
    itineraryId = itineraryId,
    bookingId = propertyBookingIdForAttempt(itineraryId, 2),
    bookingData = bookingData,
    stateId = Some(CreatedBookingStatus.BookingProcessing.id)
  )
  
  val forcedExperiments: Option[Map[String, String]] = Some(Map("PBA-338" -> "B"))
  protected def setupCommonMocks(): Unit = {
    mockDefaultDbProxy.getEbeLiteBookingObjectsResponse = Some(
      Seq(
        originalBookingObj,
        failedBookingObj,
        successBookingObj
      )
    )
  }

  protected def createStatusToken(operationId: Option[Long]): String = {
    StatusToken(
      itineraryId = itineraryId,
      actionId = 99013,
      productType = Set[String]("Property"),
      dc = "dev",
      version = if (operationId.isDefined) Some(Version6) else Some(Version4), // version4 does not support operationId
      operationId = operationId
    ).serialize(MeasurementStubs.logBookingCreationLogMessageBaseStub, MeasurementStubs.measureStub)
  }

}
