package com.agoda.integration.flow.fixture

import akka.actor.ActorSystem
import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.model.{ContentTypes, HttpEntity, HttpMethods, HttpRequest}
import akka.http.scaladsl.server.Route
import akka.http.scaladsl.testkit.{RouteTestTimeout, ScalatestRouteTest}
import com.agoda.bapi.common.message.DevicePlatform.DevicePlatform
import com.agoda.bapi.common.message.creation.{BookingCreationContext, ContinuePaymentRequest, CreateBookingRequestV2, CreateBookingResponse, GetStatusRequest, GetStatusResponse, UserAgent}
import com.agoda.bapi.common.message.multi.product.{SetStateRequest, SetStateResponse}
import com.agoda.bapi.common.message.setupBooking.{SetupBookingRequest, SetupBookingResponse}
import com.agoda.bapi.common.message.{DevicePlatform, ExperimentData}
import com.agoda.bapi.common.model.UserContext
import com.agoda.bapi.server.route.{JacksonSupport, ObjectMapperWithBKKTimeZoneProvider}
import com.fasterxml.jackson.databind.{DeserializationFeature, ObjectMapper}
import com.agoda.integration.flow.fixture.TestRoutes._
import com.softwaremill.quicklens._
import org.scalatest.BeforeAndAfterEach
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.duration.DurationInt
import scala.reflect.ClassTag

abstract class BaseFlowSpec
    extends AnyWordSpec
    with MockitoSugar
    with ScalatestRouteTest
    with Matchers
    with JacksonSupport
    with ReportLogging
    with BaseAssertionHelper
    with BeforeAndAfterEach {

  private val CLIENT_ID = "23"
  private val API_KEY   = "OGjOiYtdNEof2LgpsHzXRKbtuRoXwauwQb2hwYX5uz6H97neqn"

  implicit def default(implicit system: ActorSystem): RouteTestTimeout = RouteTestTimeout(20.second)

  override def afterEach(): Unit = {
    TestRoutes.reset()
  }

  val jmapper: ObjectMapper = TestRoutes.injector.getInstance(classOf[ObjectMapperWithBKKTimeZoneProvider]).get()
  jmapper.configure(DeserializationFeature.FAIL_ON_NULL_CREATOR_PROPERTIES, false)

  def getResourcePath: String = getClass.getName.replace("com.agoda.integration.", "").replaceAll("\\.", "/")

  def setup(
      setupBookingRequest: SetupBookingRequest,
      whiteLabelToken: Option[String] = None
  ): SetupBookingResponse = {
    val wlTokenHeader: Map[String, String] =
      whiteLabelToken.map(token => Map("x-whitelabel-token" -> token)).getOrElse(Map.empty)

    callEndpoint[SetupBookingRequest, SetupBookingResponse](
      "/v1/itinerary/setup",
      setupBookingRequest,
      routes,
      wlTokenHeader,
      _.modify(_.correlationId).setTo(Some(java.util.UUID.randomUUID().toString)),
      _.modify(
        _.bookingResponse.each.products.properties.each.content
      )
        .setTo("***")
    )
  }

  def defaultSetupBooking(
      path: String,
      checkInDate: Int = 15,
      deviceTypeId: DevicePlatform = DevicePlatform.WebDesktop,
      requestOrigin: String = "TH",
      whiteLabelToken: Option[String] = None
  ): SetupBookingResponse = {
    val setupRequest = SetupRequestBuilder
      .apply(s"$path/setup.json")
      .checkInDate(checkInDate)
      .deviceTypeId(deviceTypeId)
      .requestOrigin(requestOrigin)
      .build()

    // Prepare PAPI Mock
    if (getClass.getResourceAsStream(s"/$path/setup-papi.json") != null)
      mockPapiProxy.getPropertyForBookingResponse = Some(
        List(
          mockPapiProxy.load(
            s"$path/setup-papi.json",
            Some(setupRequest)
          )
        )
      )

    // Prepare FLAPI Mock
    if (getClass.getResourceAsStream(s"/$path/setup-flapi.json") != null)
      mockFLAPIClientProxyV2.postFlightsConfirmPriceResponse = mockFLAPIClientProxyV2.load(
        s"$path/setup-flapi.json"
      )

    // Prepare Protection Mock
    if (getClass.getResourceAsStream(s"/$path/setup-protection.json") != null)
      mockProtectionProxy.callProtectionQuoteResponse = mockProtectionProxy.load(
        s"$path/setup-protection.json"
      )

    // Prepare Ancillary Mock
    if (getClass.getResourceAsStream(s"/$path/setup-ancillary.json") != null)
      mockAncillaryProxy.quoteMultiProductResponse = mockAncillaryProxy.load(
        s"$path/setup-ancillary.json"
      )

    // Prepare Activity Mock
    if (getClass.getResourceAsStream(s"/$path/setup-ActivitySearchClient-getActivityConfirmationData.json") != null)
      mockActivitySearchApiProxy.getActivityConfirmationDataResponse = mockActivitySearchApiProxy.load(
        s"$path/setup-ActivitySearchClient-getActivityConfirmationData.json"
      )

    // Call /v1/itinerary/setup
    setup(setupRequest, whiteLabelToken)
  }

  def create(
      createBookingRequest: CreateBookingRequestV2,
      whiteLabelToken: Option[String] = None,
      extraHeaders: Map[String, String] = Map.empty
  ): CreateBookingResponse = {
    val wlTokenHeader: Map[String, String] =
      whiteLabelToken.map(token => Map("x-whitelabel-token" -> token)).getOrElse(Map.empty)

    val allHeaders = wlTokenHeader ++ extraHeaders

    callEndpoint[CreateBookingRequestV2, CreateBookingResponse](
      "/v1/itinerary/create",
      createBookingRequest,
      pciRoutes,
      allHeaders
    )
  }

  def defaultCreateBooking(
      path: String,
      token: String,
      whiteLabelToken: Option[String] = None
  ): CreateBookingResponse = {
    // Prepare Create Request
    val createRequest = CreateRequestBuilder
      .apply(s"$path/create.json")
      .bookingToken(token)
      .build()

    // Call /v1/itinerary/create
    create(createRequest, whiteLabelToken)
  }

  def getStatus(
      getStatusRequest: GetStatusRequest
  ): GetStatusResponse =
    callEndpoint[GetStatusRequest, GetStatusResponse](
      "/v1/itinerary/status",
      getStatusRequest,
      routes,
      requestTransformer = _.modify(_.correlationId).setTo(Some(java.util.UUID.randomUUID().toString))
    )

  def defaultGetStatus(
      serializedStatusToken: String,
      forcedExperiment: Option[Map[String, String]] = None
  ): GetStatusResponse = {
    // Prepare Status Request
    val getStatusRequest = createGetStatusRequest(serializedStatusToken, forcedExperiment)
    // Call /v1/itinerary/status
    getStatus(getStatusRequest)
  }

  def continue(
      continueRequest: ContinuePaymentRequest,
      whiteLabelToken: Option[String] = None
  ): GetStatusResponse = {
    val wlTokenHeader: Map[String, String] =
      whiteLabelToken.map(token => Map("x-whitelabel-token" -> token)).getOrElse(Map.empty)

    callEndpoint[ContinuePaymentRequest, GetStatusResponse](
      "/v1/itinerary/continue",
      continueRequest,
      continueRoutes,
      wlTokenHeader,
      _.modify(_.correlationId).setTo(Some(java.util.UUID.randomUUID().toString))
    )
  }

  def defaultContinue(
      serializedStatusToken: String,
      forcedExperiment: Option[Map[String, String]] = None,
      whiteLabelToken: Option[String] = None
  ): GetStatusResponse = {
    // Prepare Continue Request
    val continueRequest = createContinueRequest(serializedStatusToken, forcedExperiment)
    // Call /v1/itinerary/continue
    continue(continueRequest, whiteLabelToken)
  }

  def setState(setStateRequest: SetStateRequest, whiteLabelToken: Option[String] = None) = {
    val wlTokenHeader: Map[String, String] =
      whiteLabelToken.map(token => Map("x-whitelabel-token" -> token)).getOrElse(Map.empty)

    callEndpoint[SetStateRequest, SetStateResponse](
      "/v1/flight/bookings/setState",
      setStateRequest,
      flightRoutes,
      wlTokenHeader,
      _.modify(_.correlationId).setTo(Some(java.util.UUID.randomUUID().toString))
    )
  }

  def defaultSetState(
      path: String,
      whiteLabelToken: Option[String] = None
  ): SetStateResponse = {
    // Prepare SetState Request
    val setStateRequest = SetStateRequestBuilder
      .apply(s"$path/setState.json")
      .build()

    // Call /v1/flight/bookings/setState
    setState(setStateRequest, whiteLabelToken)
  }

  def replicateState(setStateRequest: SetStateRequest, whiteLabelToken: Option[String] = None) = {
    val wlTokenHeader: Map[String, String] =
      whiteLabelToken.map(token => Map("x-whitelabel-token" -> token)).getOrElse(Map.empty)

    callEndpoint[SetStateRequest, SetStateResponse](
      "/v1/itinerary/bookings/replicateState",
      setStateRequest,
      routes,
      wlTokenHeader,
      _.modify(_.correlationId).setTo(Some(java.util.UUID.randomUUID().toString))
    )
  }

  def defaultReplicateState(
      path: String,
      whiteLabelToken: Option[String] = None
  ): SetStateResponse = {
    // Prepare SetState Request
    val setStateRequest = SetStateRequestBuilder
      .apply(s"$path/replicateState.json")
      .build()

    // Call /v1/flight/bookings/setState
    setState(setStateRequest, whiteLabelToken)
  }

  private def createGetStatusRequest(
      statusToken: String,
      forcedExperiment: Option[Map[String, String]] = None
  ): GetStatusRequest = {
    GetStatusRequest(
      statusToken = statusToken,
      correlationId = None,
      userContext = Some(
        UserContext(
          languageId = 1,
          requestOrigin = "",
          currency = "USD",
          nationalityId = 0,
          experimentData = Some(
            ExperimentData(
              userId = " ",
              deviceTypeId = "1",
              memberId = None,
              serverName = None,
              force = forcedExperiment,
              forceByVariant = None,
              trafficGroup = None,
              cId = None,
              aId = None
            )
          )
        )
      ),
      bookingContext = Option(
        BookingCreationContext(
          sessionId = "",
          userAgent = UserAgent()
        )
      )
    )
  }

  private def createContinueRequest(
      statusToken: String,
      forcedExperiment: Option[Map[String, String]] = None
  ): ContinuePaymentRequest = {
    ContinuePaymentRequest(
      statusToken = statusToken,
      postBackFields = None,
      userContext = Some(
        UserContext(
          languageId = 1,
          requestOrigin = "",
          currency = "USD",
          nationalityId = 0,
          experimentData = Some(
            ExperimentData(
              userId = " ",
              deviceTypeId = "1",
              memberId = None,
              serverName = None,
              force = forcedExperiment,
              forceByVariant = None,
              trafficGroup = None,
              cId = None,
              aId = None
            )
          )
        )
      ),
      correlationId = Some(""),
      internalToken = Some("dummyInternalToken"),
      securityCode = None,
      bookingContext = Option(
        BookingCreationContext(
          sessionId = "",
          userAgent = UserAgent()
        )
      )
    )
  }

  private def createHttpRequest(
      uri: String,
      body: String,
      extraHeaders: Map[String, String] = Map()
  ): HttpRequest = {
    HttpRequest(
      method = HttpMethods.POST,
      headers = scala.collection.immutable.Seq(
        RawHeader("Client-ID", CLIENT_ID),
        RawHeader("API-Key", API_KEY),
        RawHeader("X-Forwarded-For", "127.0.0.1"),
        RawHeader("Request-ID", "123")
      ) ++ extraHeaders.map(h => RawHeader(h._1, h._2)).toSeq,
      uri = uri,
      entity = HttpEntity(ContentTypes.`application/json`, body.getBytes())
    )
  }

  private def callEndpoint[Request, Response: ClassTag](
      url: String,
      body: Request,
      route: Route,
      extraHeaders: Map[String, String] = Map.empty,
      requestTransformer: Request => Request = identity[Request](_),
      responseTransformer: Response => Response = identity[Response](_)
  ): Response = {
    val request           = requestTransformer(body)
    val requestBodyString = BaseRequestBuilder.mapper().writerWithDefaultPrettyPrinter().writeValueAsString(request)

    logJson(s"$url Request", requestBodyString)
    createHttpRequest(
      url,
      requestBodyString,
      extraHeaders = extraHeaders
    ) ~> route ~> check {
      val rawResponse = jmapper.readTree(responseAs[String])
      val response =
        jmapper.readValue(rawResponse.toString, implicitly[ClassTag[Response]].runtimeClass).asInstanceOf[Response]
      val responseJson = jmapper.writerWithDefaultPrettyPrinter.writeValueAsString(responseTransformer(response))
      logJson(s"$url Response", responseJson)

      status.isSuccess shouldEqual true
      response
    }
  }
}
