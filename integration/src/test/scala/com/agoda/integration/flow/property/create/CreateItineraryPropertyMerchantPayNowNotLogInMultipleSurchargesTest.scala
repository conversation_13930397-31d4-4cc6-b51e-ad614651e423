package com.agoda.integration.flow.property.create

import com.agoda.finance.tax.models.amendment.BreakdownItem.{TaxPayToGovernment, TaxPayToProperty}
import com.agoda.integration.flow.fixture.BaseFlowSpec

class CreateItineraryPropertyMerchantPayNowNotLogInMultipleSurchargesTest extends BaseFlowSpec {

  "Create itinerary" should {
    "setup and create booking correctly" in {
      // Setup booking
      val setupResponse = defaultSetupBooking(getResourcePath)
      defaultSetupBookingAssertions(setupResponse)

      // Extra assertion for setup

      // Create booking
      val createResponse =
        defaultCreateBooking(getResourcePath, setupResponse.bookingResponse.head.bookingToken.head.token)
      defaultCreateBookingAssertions(createResponse)

      // Extra assertion for create
      val bookingActionState = getBookingActionState

      assert(bookingActionState.propertyBookingState.nonEmpty, "propertyBookingState should not be empty")
      assert(bookingActionState.propertyBookingState.get.bookingList.nonEmpty, "bookingList should not be empty")

      assert(
        bookingActionState.propertyBookingState.get.bookingList.head.financialBreakdown.nonEmpty,
        "financialBreakdown should not be empty"
      )

      val financialBreakdownResult = bookingActionState.propertyBookingState.get.bookingList.head.financialBreakdown

      val financialBreakdownItemsByBookingId = financialBreakdownResult.groupBy(_.bookingId.longValue)
      val items                              = financialBreakdownItemsByBookingId.get(createResponse.itinerary.head.bookings.head.bookingId)
      assert(items.nonEmpty, "financialBreakdownItemsByBookingId should exists")
      assert(
        items.get
          .map(_.itemId)
          .exists(
            List(TaxPayToProperty, TaxPayToGovernment)
              .contains(_)
          ),
        "the booking should have non-mandatory surcharges: TaxPayToProperty and TaxPayToGovernment"
      )
    }
  }
}
