package com.agoda.integration.flow.fixture

import com.agoda.bapi.common.message.pricebreakdown.{PriceBreakdownNode, PriceBreakdownType}
import com.agoda.bapi.common.message.pricebreakdown.PriceBreakdownType.PriceBreakdownType
import com.agoda.bapi.common.message.setupBooking.SetupBookingResponse
import com.agoda.bapi.common.model.UserContext
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.common.token.Money
import com.agoda.bapi.creation.model.db.PaymentInfo
import com.agoda.mpbe.state.booking.{BaseBooking, BaseBookingGeo, BaseBookingMeta, BaseBookingOffer, BaseBookingPax, BaseBookingPriceSummary, BaseCancellationInfo, BaseClientInfo, BaseFinanceInfo, BaseProductModel, BaseSupplierInfo}
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.agoda.mpbe.state.product.common.FinancialBreakdown
import com.google.`type`.timeofday.TimeOfDay
import org.scalatest.Assertion
import org.scalatest.OptionValues.convertOptionToValuable
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper

import java.sql.ResultSet
import java.time.format.DateTimeFormatter

trait BaseActivityAssertionHelper {

  protected def assertPriceBreakdown(
      setupBookingResponse: SetupBookingResponse,
      productSummaryAmounts: List[Double],
      bundleSummaryAmount: Double,
      productIds: Seq[String]
  ): Unit = {
    val priceSummaryNode = setupBookingResponse.bookingResponse.flatMap(_.products.totalPriceDisplay)
    val isPriceSummaryType: Boolean =
      priceSummaryNode.exists(_.value.exists(_.`type` == PriceBreakdownType.PriceSummary))
    val productSummaryNodes =
      priceSummaryNode.flatMap(
        _.breakdowns.map(_.filter(_.value.exists(_.`type` == PriceBreakdownType.ProductSummary)))
      )
    val bundleSummaryNode = priceSummaryNode.flatMap(
      _.breakdowns.map(_.filter(_.value.exists(_.`type` == PriceBreakdownType.BundledSummary)))
    )
    assert(isPriceSummaryType, "price summary type")
    assert(
      productSummaryNodes.map(_.size).getOrElse(0) == productSummaryAmounts.size,
      "number of product summary nodes"
    )
    assert(
      bundleSummaryNode
        .flatMap(_.headOption.flatMap(_.value.map(_.amount.amount)))
        .getOrElse(0) == bundleSummaryAmount,
      "bundle summary amount"
    )
    productSummaryNodes
      .getOrElse(Seq.empty)
      .foreach(node => {
        assert(productSummaryAmounts.contains(node.value.map(_.amount.amount).getOrElse(0)), "product summary amount")
        assert(productIds.contains(node.value.flatMap(_.productId).getOrElse("")), "product Id")
      })
  }

  protected def assertCartPriceBreakdownForAgodaWL(
      setupResponse: SetupBookingResponse,
      expectedCurrency: String,
      expectedTotalAmount: BigDecimal,
      expectedPerPersonAmount: Option[BigDecimal] = None,
      expectedPerAdultAmount: Option[BigDecimal] = None,
      expectedPerChildAmount: Option[BigDecimal] = None,
      expectedPerInfantAmount: Option[BigDecimal] = None,
      expectedPerYouthAmount: Option[BigDecimal] = None,
      expectedPerSeniorAmount: Option[BigDecimal] = None
  ): Assertion = {
    val priceSummaryNode = setupResponse.bookingResponse.flatMap(_.products.totalPriceDisplay)
    val productSummaryNodes =
      priceSummaryNode.flatMap(
        _.breakdowns.map(_.filter(_.value.exists(_.`type` == PriceBreakdownType.ProductSummary)))
      )
    val payAgodaNode = productSummaryNodes.get.head.breakdowns.get.head

    val bookingPriceNode = payAgodaNode.breakdowns.get.head

    // Assert perPax breakdowns
    val perPaxBreakdowns = bookingPriceNode.breakdowns.get

    expectedPerPersonAmount.map(amount =>
      assertPerPaxPriceBreakdowns(perPaxBreakdowns, PriceBreakdownType.TotalFarePerPerson, amount, expectedCurrency)
    )

    expectedPerAdultAmount.map(amount =>
      assertPerPaxPriceBreakdowns(perPaxBreakdowns, PriceBreakdownType.TotalFarePerAdult, amount, expectedCurrency)
    )
    expectedPerChildAmount.map(amount =>
      assertPerPaxPriceBreakdowns(perPaxBreakdowns, PriceBreakdownType.TotalFarePerChild, amount, expectedCurrency)
    )
    expectedPerInfantAmount.map(amount =>
      assertPerPaxPriceBreakdowns(perPaxBreakdowns, PriceBreakdownType.TotalFarePerLapInfant, amount, expectedCurrency)
    )
    expectedPerYouthAmount.map(amount =>
      assertPerPaxPriceBreakdowns(perPaxBreakdowns, PriceBreakdownType.TotalFarePerYouth, amount, expectedCurrency)
    )
    expectedPerSeniorAmount.map(amount =>
      assertPerPaxPriceBreakdowns(perPaxBreakdowns, PriceBreakdownType.TotalFarePerSenior, amount, expectedCurrency)
    )

    // Assert bookingPrice breakdowns
    val bookingPrice = getBreakdownNodeMoney(bookingPriceNode)
    bookingPrice.amount shouldEqual expectedTotalAmount
    bookingPrice.currencyCode shouldEqual expectedCurrency
  }

  private def assertPerPaxPriceBreakdowns(
      perPaxBreakdowns: Seq[PriceBreakdownNode],
      priceBreakdownType: PriceBreakdownType,
      expectedAmount: BigDecimal,
      expectedCurrency: String
  ): Assertion = {
    val perPaxNode  = perPaxBreakdowns.find(_.value.get.`type` == priceBreakdownType).get
    val perPaxTotal = getBreakdownNodeMoney(perPaxNode)
    perPaxTotal.amount shouldEqual expectedAmount
    perPaxTotal.currencyCode shouldEqual expectedCurrency

    val perPaxBaseNode = perPaxNode.breakdowns.get.head
    perPaxBaseNode.value.get.`type` shouldEqual PriceBreakdownType.BaseProductCharges
    val perPaxBase = getBreakdownNodeMoney(perPaxBaseNode)
    perPaxBase.amount shouldEqual expectedAmount
    perPaxBase.currencyCode shouldEqual expectedCurrency
  }

  protected def getBreakdownNodeMoney(node: PriceBreakdownNode): Money = {
    node.value.map(_.amount).value
  }

  protected def assertBookingAction(
      bookingAction: BookingWorkflowAction,
      itineraryId: Long,
      bookingId: Long,
      languageId: Int
  ): Unit = {
    assert(bookingAction.itineraryId == itineraryId)
    assert(bookingAction.bookingType.getOrElse(0) == 102)
    assert(bookingAction.bookingId.getOrElse(0L) == bookingId)
    assert(bookingAction.actionTypeId == 1)
    assert(bookingAction.correlationId == "3e4c75f8-1414-417c-a55c-9b49a8768ae2")
    assert(bookingAction.requestId == "3e4c75f8-1414-417c-a55c-9b49a8768ae2")
    assert(bookingAction.workflowId == 6)
    assert(bookingAction.workflowStateId == 4001)
    assert(bookingAction.productTypeId.getOrElse(0) == com.agoda.mpb.common.MultiProductType.SingleActivity.id)
    assert(bookingAction.storefrontId.getOrElse(0) == 3)
    assert(bookingAction.languageId.getOrElse(0) == languageId)
  }

  protected def assertUserContext(
      userContext: UserContext,
      currency: String,
      languageId: Int,
      requestOrigin: String
  ): Unit = {
    assert(userContext.currency == currency)
    assert(userContext.languageId == languageId)
    assert(userContext.requestOrigin == requestOrigin)
  }

  protected def assertPaymentInfo(
      paymentInfo: PaymentInfo,
      paymentCurrency: String = "USD",
      paymentMethod: PaymentMethod = PaymentMethod.Visa,
      paymentAmount: Double,
      paymentAmountUSD: Double,
      siteExchangeRate: Double = 1.0,
      pointsExist: Boolean
  ): Unit = {
    assert(paymentInfo.paymentCurrency == paymentCurrency)
    assert(paymentInfo.method == paymentMethod)
    assert(paymentInfo.paymentAmount == paymentAmount)
    assert(paymentInfo.paymentAmountUSD == paymentAmountUSD)
    assert(paymentInfo.siteExchangeRate.value == siteExchangeRate)
    assert(paymentInfo.destinationExchangeRate.value == 1.0)
    assert(paymentInfo.points.nonEmpty == pointsExist)
  }

  protected def assertCrossedOutPrice(
      setupBookingResponse: SetupBookingResponse,
      expectedCrossedOutPrice: Double = 0.0
  ): Unit = {
    val priceSummaryNode = setupBookingResponse.bookingResponse.flatMap(_.products.totalPriceDisplay)
    assert(
      priceSummaryNode
        .flatMap(_.value.flatMap(_.originalAmount.map(_.amount)))
        .getOrElse(0.0) == expectedCrossedOutPrice,
      "crossedOutPrice checker"
    )
  }
}
