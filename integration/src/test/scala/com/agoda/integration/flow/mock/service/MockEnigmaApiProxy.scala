package com.agoda.integration.flow.mock.service

import com.agoda.bapi.common.MessageService
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.LanguageScriptType.{<PERSON><PERSON>, Ka<PERSON>}
import com.agoda.bapi.common.message.creation.{Customer, FlightPax, HotelGuest}
import com.agoda.bapi.common.model.WhiteLabel.WhiteLabel
import com.agoda.bapi.common.model.car.{CarBillingInformation, CarDriversInformation, EnigmaCarDriverInfo}
import com.agoda.bapi.common.model.flight.flightModel.FlightPassengers
import com.agoda.bapi.common.model.payment.CreditCardBillingInfo
import com.agoda.bapi.common.proxy.EnigmaApiProxy
import com.agoda.bapi.common.util.LocalizeNameMapper
import com.agoda.capi.enigma.shared_model.booking.contact.AgodaCustomerContact
import com.agoda.capi.enigma.shared_model.booking.flight.{FlightCustomer, CreditCardBillingInfo => CapiCreditCardBillingInfo, FlightCustomerInformation => CapiFlightCustomerInformation, FlightPassenger => CapiFlightPassenger}
import com.agoda.capi.enigma.shared_model.booking.pax.{BaseBookingPax, BookingGuest}
import com.agoda.capi.enigma.shared_model.booking.{BookingCustomer, BookingPax, CustomerBillingInformation, flight}
import com.agoda.capi.enigma.shared_model.car.billinginfo.{CarBillingInfo, CarBillingInfoDetails}
import com.agoda.capi.enigma.shared_model.car.driver.CarDriver
import com.agoda.capi.enigma.shared_model.common.{IntValue, Metadata, RecStatus}
import java.time.{Instant, LocalDate => JavaLocalDate}
import com.agoda.capi.enigma.shared_model.itinerary.billinginfo.{ItineraryBillingInfo, ItineraryBillingInfoDetails}
import com.agoda.integration.flow.mock.{MockException, MockTrait}
import com.github.nscala_time.time.Imports
import org.scalatestplus.mockito.MockitoSugar
import com.agoda.capi.enigma.shared_model.car.billinginfo.{CarBillingInfo, CarBillingInfoDetails, CreditCardBillingInfo => CarCreditCardBillingInfo, CustomerInfo => CarCustomerInfo, Information => CarInformation}
import com.agoda.capi.enigma.shared_model.car.driver.{CarDriverInfo, DriverInfo, CarDriver => CapiCarDriver}

import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.UUID
import scala.concurrent.Future

class MockEnigmaApiProxy extends EnigmaApiProxy with MockitoSugar with MockTrait {

  override def resetAll(): Unit = {}

  override def saveBaseBookingPaxes(paxes: Seq[BaseBookingPax])(implicit
      requestContext: RequestContext
  ): Future[Seq[BaseBookingPax]] = Future.successful(paxes)

  override def getBaseBookingPaxByBookingId(bookingId: Long)(implicit
      requestContext: RequestContext
  ): Future[Seq[BaseBookingPax]] = Future.failed(MockException.unimplementedException)

  override def saveFlightPassengers(bookingId: Int, passengers: Seq[FlightPax], ids: Seq[Long])(implicit
      requestContext: RequestContext
  ): Future[flight.FlightPassengers] = Future.successful(
    flight.FlightPassengers(
      bookingId = bookingId,
      passengers = passengers.map(passenger =>
        CapiFlightPassenger(
          passengerID = passenger.id,
          title = Some(passenger.title),
          firstName = passenger.firstname,
          middleName = Some(passenger.middlename),
          lastName = passenger.lastname,
          primary = Some(true),
          birthDate = JavaLocalDate.now,
          nationalityId = Some(passenger.nationalityId),
          gender = passenger.gender,
          passportNumber = passenger.passportNumber,
          passportExpires = None,
          passportCountry = None,
          passengerType = passenger.passengerType,
          knownTravelerNumber = passenger.knownTravelerNumber,
          frequentFlyer = Seq.empty
        )
      ),
      metadata = Metadata.empty
    )
  )

  override def savePropertyCustomerContact(
      bookingId: Int,
      customer: Customer,
      primaryGuest: HotelGuest,
      whiteLabel: WhiteLabel,
      bookerAnswer: Option[String]
  )(implicit requestContext: RequestContext): Future[BookingCustomer] =
    Future.successful(
      BookingCustomer(
        bookingId = bookingId,
        contact = AgodaCustomerContact(
          phoneNumber = Some(customer.phoneFormat),
          emailAddress = Some(customer.email),
          guestEmail = primaryGuest.email.flatMap(email => if (email.isEmpty) None else Some(email)),
          b2bCustomerEmail = customer.b2bCustomerEmail,
          title = Some(customer.title),
          firstName = Some(customer.firstname),
          middleName = Some(customer.middlename),
          lastName = Some(customer.lastname),
          citizenshipId = primaryGuest.citizenshipId,
          countryId = Some(customer.countryId)
        ),
        metadata = defaultBaseMetadata
      )
    )

  override def savePropertyGuests(bookingId: Int, passengers: Seq[HotelGuest], whiteLabel: WhiteLabel)(implicit
      requestContext: RequestContext
  ): Future[Vector[BookingPax]] = Future.successful(
    passengers
      .map(pax =>
        BookingPax(
          UUID.randomUUID(),
          BookingGuest(
            bookingId = bookingId,
            guestNo = pax.guestNo,
            nationalityId = Some(pax.nationalityId),
            firstName = pax.firstname,
            middleName = Some(pax.middlename),
            lastName = pax.lastname,
            roomId = Some(1),
            kanjiFirstName = LocalizeNameMapper.mapLocalizedNames(pax.localizedNames, Kanji).map(_.firstname),
            kanjiLastName = LocalizeNameMapper.mapLocalizedNames(pax.localizedNames, Kanji).map(_.lastname),
            kanaFirstName = LocalizeNameMapper.mapLocalizedNames(pax.localizedNames, Kana).map(_.firstname),
            kanaLastName = LocalizeNameMapper.mapLocalizedNames(pax.localizedNames, Kana).map(_.lastname),
            age = Some(pax.age),
            gender = pax.gender,
            email = pax.email,
            phoneNumber = pax.phoneFormat,
            emergencyPhoneNumber = pax.emergencyPhone,
            postcode = pax.postcode,
            city = pax.city,
            area = pax.area,
            address = pax.address,
            stateId = pax.stateId
          ),
          defaultBaseMetadata
        )
      )
      .toVector
  )

  override def saveFlightCustomer(bookingId: Int, creditCardBillingInfo: CreditCardBillingInfo, customerInfo: Customer)(
      implicit requestContext: RequestContext
  ): Future[FlightCustomer] = Future.successful(
    FlightCustomer(
      bookingId = bookingId,
      creditCardBillingInfo = CapiCreditCardBillingInfo(
        creditCardBillingInfo.billingAddress1,
        creditCardBillingInfo.billingAddress2,
        creditCardBillingInfo.billingPostalcode,
        creditCardBillingInfo.billingCountryId,
        creditCardBillingInfo.billingCity,
        creditCardBillingInfo.billingState
      ),
      information = Some(
        CapiFlightCustomerInformation(
          Some(customerInfo.title),
          Some(customerInfo.firstname),
          Some(customerInfo.middlename),
          Some(customerInfo.lastname),
          Some(customerInfo.email),
          Some(customerInfo.countryId),
          Some(customerInfo.phoneFormat)
        )
      ),
      metadata = Metadata.empty
    )
  )

  override def saveVehicleDriver(bookingId: Long, drivers: Seq[EnigmaCarDriverInfo], ids: Seq[Long])(implicit
      requestContext: RequestContext
  ): Future[Vector[CarDriver]] = Future.successful(
    Vector(
      CapiCarDriver(
        UUID.randomUUID(),
        DriverInfo(
          IntValue(bookingId.toInt),
          1,
          "Mr",
          "John",
          "",
          "Doe",
          Some("male"),
          Some(40),
          Some("1a2b3c"),
          Some(106)
        ),
        Metadata.empty
      )
    )
  )

  override def saveVehicleBillingInfo(
      bookingId: Long,
      creditCardBillingInfo: CreditCardBillingInfo,
      customerInfo: Customer
  )(implicit requestContext: RequestContext): Future[CarBillingInfo] =
    Future.successful(
      CarBillingInfo(
        bookingId = 1,
        CarBillingInfoDetails(
          customerInfo = CarCustomerInfo(
            creditCardBillingInfo = CarCreditCardBillingInfo(
              billingAddress1 = "billingAddress1",
              billingAddress2 = "billingAddress2",
              billingPostalCode = "billingPostalCode",
              billingCity = "billingCity",
              billingState = "billingState",
              billingCountry = "billingCountry"
            ),
            information = CarInformation(
              title = Some("title"),
              firstName = Some("firstName"),
              middleName = Some("middleName"),
              lastName = Some("lastName"),
              email = Some("email"),
              phone = Some("phone")
            )
          )
        ),
        metadata = Metadata(
          status = RecStatus.Active,
          createdBy = UUID.randomUUID(),
          createdWhen = Instant.now(),
          modifiedBy = Some(UUID.randomUUID()),
          modifiedWhen = Some(Instant.now())
        )
      )
    )

  override def saveCustomerBillingInfo(customer: CustomerBillingInformation)(implicit
      requestContext: RequestContext
  ): Future[CustomerBillingInformation] = Future.successful(customer)

  override def saveItineraryBillingInfo(itineraryId: Int, entity: String)(implicit
      requestContext: RequestContext
  ): Future[ItineraryBillingInfo] = Future.successful(
    ItineraryBillingInfo(
      itineraryId = itineraryId,
      details = ItineraryBillingInfoDetails(
        postalCode = Some("10200"),
        taxId = Some("1234"),
        paymentPhone = Some("+66123456789"),
        appUserId = None,
        appOption = None
      ),
      metadata = defaultBaseMetadata
    )
  )

  override val defaultBaseMetadata: Metadata = Metadata(
    status = RecStatus.Active,
    createdBy = UUID.fromString("BD811DA2-B3D0-470E-B7CD-F15B8A460EB0"),
    createdWhen = Instant.now.truncatedTo(
      ChronoUnit.MILLIS
    ),
    modifiedBy = None,
    modifiedWhen = None
  )

  override protected val messagingService: MessageService = mock[MessageService]
}
