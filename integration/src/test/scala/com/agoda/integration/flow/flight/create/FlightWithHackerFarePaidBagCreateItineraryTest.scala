package com.agoda.integration.flow.flight.create

import com.agoda.bapi.common.message.creation.BreakDownTypeID
import com.agoda.bapi.common.model.flight.FlightBookingToken
import com.agoda.bapi.common.model.flight.flightModel.{Breakdown, BreakdownPerPax}
import com.agoda.bapi.common.util.BookingTokenHelper
import com.agoda.bapi.common.util.TokenDeserializers._
import com.agoda.bapi.creation.model.db.BookingActionState
import com.agoda.integration.flow.fixture.BaseFlowSpec
import com.agoda.mpb.common.serialization.Serialization

import java.sql.ResultSet

class FlightWithHackerFarePaidBagCreateItineraryTest extends BaseFlowSpec {

  "Create itinerary" should {
    "create response ok" in {
      // Setup booking
      val setupResponse = defaultSetupBooking(getResourcePath)
      defaultSetupBookingAssertions(setupResponse)

      // Extra assertion for setup
      //

      // Create booking
      val createResponse =
        defaultCreateBooking(getResourcePath, setupResponse.bookingResponse.head.bookingToken.head.token)
      defaultCreateBookingAssertions(createResponse)

      // Extra assertion for create
      //

      assert(createResponse.success, "Status should be success")
      assert(createResponse.itinerary.map(_.itineraryId).nonEmpty, "itinerary id should not be empty")
      assert(
        createResponse.itinerary.flatMap(_.flights.headOption.map(_.bookingId)).nonEmpty,
        "flight booking id should not be empty"
      )
      assert(createResponse.itinerary.map(_.statusToken).nonEmpty, "polling token should not be empty")
      assert(createResponse.duplicateBookings.isEmpty, "duplicate booking should be empty")

      val transactionModel = getTransactionModel

      val bookingIds        = createResponse.itinerary.map(_.flights).getOrElse(Seq.empty).map(_.bookingId)
      val outboundBookingId = bookingIds.head
      val inboundBookingId  = bookingIds.last

      val outboundBaggage = transactionModel.get.flightBookingActionStates
        .filter(_.bookingState.get.bookingId == outboundBookingId)
        .head
        .bookingState
        .get
        .baggage
        .head

      assertResult(1, "baggage_type_id")(outboundBaggage.baggageTypeId)
      assertResult(1, "quantity")(outboundBaggage.quantity.getOrElse(0))
      assertResult(0.0, "max_weight")(outboundBaggage.maxWeight.getOrElse(0.0))
      assertResult(None, "max_weight_unit")(outboundBaggage.maxWeightUnit)
      assertResult(7.0, "weight_limit_per_bag")(outboundBaggage.weightLimitPerBag.getOrElse(0.0))
      assertResult("KG", "weight_limit_per_bag_unit")(outboundBaggage.weightLimitPerBagUnit.getOrElse(""))
      assertResult(56.0, "size_length")(outboundBaggage.sizeLength.getOrElse(0.0))
      assertResult(23.0, "size_length")(outboundBaggage.sizeWidth.getOrElse(0.0))
      assertResult(36.0, "size_length")(outboundBaggage.sizeHeight.getOrElse(0.0))
      assertResult("CM", "size_unit")(outboundBaggage.sizeUnit.getOrElse(""))
      assertResult(true, "price_amount")(outboundBaggage.priceAmount > 0)
      assertResult("USD", "price_currency")(outboundBaggage.priceCurrency)
      assertResult(false, "supplier_data")(outboundBaggage.supplierData.isEmpty)
      assertResult(0, "baggage_status")(outboundBaggage.baggageStatus)
      assertResult(false, "is_carry_on")(outboundBaggage.isCarryOn)
      assertResult(Some(false), "is_partial_settlement_required")(
        outboundBaggage.isPartialSettlementRequired
      )

      val inboundBaggage = transactionModel.get.flightBookingActionStates
        .filter(_.bookingState.get.bookingId == inboundBookingId)
        .head
        .bookingState
        .get
        .baggage
        .head

      assertResult(1, "baggage_type_id")(inboundBaggage.baggageTypeId)
      assertResult(2, "quantity")(inboundBaggage.quantity.getOrElse(0))
      assertResult(0.0, "max_weight")(inboundBaggage.maxWeight.getOrElse(0.0))
      assertResult(None, "max_weight_unit")(inboundBaggage.maxWeightUnit)
      assertResult(7.0, "weight_limit_per_bag")(inboundBaggage.weightLimitPerBag.getOrElse(0.0))
      assertResult("KG", "weight_limit_per_bag_unit")(inboundBaggage.weightLimitPerBagUnit.getOrElse(""))
      assertResult(56.0, "size_length")(inboundBaggage.sizeLength.getOrElse(0.0))
      assertResult(23.0, "size_length")(inboundBaggage.sizeWidth.getOrElse(0.0))
      assertResult(36.0, "size_length")(inboundBaggage.sizeHeight.getOrElse(0.0))
      assertResult("CM", "size_unit")(inboundBaggage.sizeUnit.getOrElse(""))
      assertResult(true, "price_amount")(inboundBaggage.priceAmount > 0)
      assertResult("USD", "price_currency")(inboundBaggage.priceCurrency)
      assertResult(false, "supplier_data")(inboundBaggage.supplierData.isEmpty)
      assertResult(0, "baggage_status")(inboundBaggage.baggageStatus)
      assertResult(false, "is_carry_on")(inboundBaggage.isCarryOn)
      assertResult(Some(false), "is_partial_settlement_required")(
        inboundBaggage.isPartialSettlementRequired
      )

      val bookingCreationToken =
        BookingTokenHelper.extractCreationObject(setupResponse.bookingResponse.head.bookingToken.head).get
      val flightBookingTokens =
        BookingTokenHelper.extractCreationObjects[Seq[FlightBookingToken]](bookingCreationToken.flights).get.head._2

      val outboundToken      = flightBookingTokens.head
      val outboundBreakdowns = transactionModel.head.flightBookingActionStates.head.bookingState.head.breakdown
      val outboundBreakdownsPerPax =
        transactionModel.head.flightBookingActionStates.head.bookingState.head.breakdownPerPax

      val inboundToken      = flightBookingTokens.last
      val inboundBreakdowns = transactionModel.head.flightBookingActionStates.last.bookingState.head.breakdown
      val inboundBreakdownsPerPax =
        transactionModel.head.flightBookingActionStates.last.bookingState.head.breakdownPerPax

      assertFinancialBreakdown(outboundBreakdowns, outboundBookingId, BreakDownTypeID.FlightSurchargeBaggage.id)
      assertFinancialBreakdown(inboundBreakdowns, inboundBookingId, BreakDownTypeID.FlightSurchargeBaggage.id)

      assertFinancialBreakdown(outboundBreakdowns, outboundBookingId, BreakDownTypeID.FlightFare.id)
      assertFinancialBreakdown(inboundBreakdowns, inboundBookingId, BreakDownTypeID.FlightFare.id)

      assertFinancialBreakdownPerPax(
        outboundBreakdownsPerPax,
        outboundBookingId,
        BreakDownTypeID.FlightSurchargeBaggage.id
      )
      assertFinancialBreakdownPerPax(
        inboundBreakdownsPerPax,
        inboundBookingId,
        BreakDownTypeID.FlightSurchargeBaggage.id
      )

      assertFinancialBreakdownPerPax(
        outboundBreakdownsPerPax,
        outboundBookingId,
        BreakDownTypeID.FlightFare.id
      )
      assertFinancialBreakdownPerPax(
        inboundBreakdownsPerPax,
        inboundBookingId,
        BreakDownTypeID.FlightFare.id
      )

      assertResult(com.agoda.mpb.common.MultiProductType.SingleFlight.id, "outbound product_type_id")(
        transactionModel.head.workflowActions.filter(_.bookingId.isDefined).head.productTypeId.getOrElse(0)
      )

      assertResult(com.agoda.mpb.common.MultiProductType.SingleFlight.id, "inbound product_type_id")(
        transactionModel.head.workflowActions.filter(_.bookingId.isDefined).last.productTypeId.getOrElse(0)
      )
    }
  }

  private def assertFinancialBreakdown(breakdowns: Seq[Breakdown], bookingId: Long, breakdownTypeId: Int): Unit = {
    val breakdownItemIds = Vector(
      1,  // NetExclusive
      2,  // Tax
      3,  // Margin
      9,  // Down-lift
      10, // NetInclusive
      11, // SellExclusive
      12, // SellInclusive
      41  // ReferenceSellInclusive
    )
    breakdownItemIds.foreach { itemId =>
      def checkAmount: Double => Boolean =
        itemId match {
          case 2 if breakdownTypeId == BreakDownTypeID.FlightSurchargeBaggage.id => amount => amount == 0d
          case 9                                                                 => amount => amount < 0
          case _                                                                 => amount => amount > 0d
        }
      assertFinancialBreakdownForItemId(
        breakdowns.filter(breakdown => breakdown.itemId == itemId && breakdown.typeId == breakdownTypeId).head,
        itemId = itemId,
        bookingId = bookingId,
        typeId = breakdownTypeId,
        checkAmount = checkAmount
      )
    }
  }

  private def assertFinancialBreakdownPerPax(
      breakdowns: Seq[BreakdownPerPax],
      bookingId: Long,
      breakdownTypeId: Int
  ): Unit = {
    val breakdownItemIds = Vector(
      1,  // NetExclusive
      2,  // Tax
      3,  // Margin
      9,  // Down-lift
      10, // NetInclusive
      11, // SellExclusive
      12, // SellInclusive
      41  // ReferenceSellInclusive
    )
    breakdownItemIds.foreach { itemId =>
      def checkAmount: Double => Boolean =
        itemId match {
          case 2 if breakdownTypeId == BreakDownTypeID.FlightSurchargeBaggage.id => amount => amount == 0d
          case 9                                                                 => amount => amount < 0
          case _                                                                 => amount => amount > 0d
        }
      assertPerPaxFinancialBreakdownForItemId(
        breakdowns.filter(breakdown => breakdown.itemId == itemId && breakdown.typeId == breakdownTypeId).head,
        itemId = itemId,
        bookingId = bookingId,
        typeId = breakdownTypeId,
        checkAmount = checkAmount
      )
    }
  }

  private def assertFinancialBreakdownForItemId(
      financialBreakdown: Breakdown,
      itemId: Long,
      bookingId: Long,
      typeId: Long,
      checkAmount: Double => Boolean
  ): Unit = {
    assert(financialBreakdown.bookingId.getOrElse(0L) == bookingId, s"booking_id for item_id: ${itemId}")
    assert(financialBreakdown.bookingType.getOrElse(0) == 99, s"booking_type for item_id: ${itemId}")
    assert(financialBreakdown.localCurrency == "THB", s"local_currency for item_id: ${itemId}")
    assert(financialBreakdown.itemId == itemId, s"item_id: ${itemId}")
    assert(financialBreakdown.typeId == typeId, s"type_id for item_id: ${itemId}")
    assert(financialBreakdown.exchangeRate > 0.0, s"exchange_rate for item_id: ${itemId}")
    assert(
      financialBreakdown.vendorExchangeRate > 0.0,
      s"vendor_exchange_rate for item_id: ${itemId}"
    )
    assert(checkAmount(financialBreakdown.localAmount), s"local_amount for item_id: ${itemId}")
    assert(
      checkAmount(financialBreakdown.requestedAmount.getOrElse(0.0)),
      s"requested_amount for item_id: ${itemId}"
    )
    assert(checkAmount(financialBreakdown.usdAmount), s"usd_amount for item_id: ${itemId}")
  }

  private def assertPerPaxFinancialBreakdownForItemId(
      financialBreakdown: BreakdownPerPax,
      itemId: Long,
      bookingId: Long,
      typeId: Long,
      checkAmount: Double => Boolean
  ): Unit = {
    assert(financialBreakdown.bookingId.getOrElse(0L) == bookingId, s"booking_id for item_id: ${itemId}")
    assert(financialBreakdown.bookingType.getOrElse(0) == 99, s"booking_type for item_id: ${itemId}")
    assert(financialBreakdown.localCurrency == "THB", s"local_currency for item_id: ${itemId}")
    assert(financialBreakdown.itemId == itemId, s"item_id: ${itemId}")
    assert(financialBreakdown.typeId == typeId, s"type_id for item_id: ${itemId}")
    assert(financialBreakdown.exchangeRate > 0.0, s"exchange_rate for item_id: ${itemId}")
    assert(
      financialBreakdown.vendorExchangeRate > 0.0,
      s"vendor_exchange_rate for item_id: ${itemId}"
    )
    assert(
      checkAmount(financialBreakdown.localAmount),
      s"local_amount for item_id: ${itemId}"
    )
    assert(
      checkAmount(financialBreakdown.requestedAmount.getOrElse(0.0d)),
      s"requested_amount for item_id: ${itemId}"
    )
    assert(checkAmount(financialBreakdown.usdAmount), s"usd_amount for item_id: ${itemId}")
  }
}
