package com.agoda.integration.flow.flight.create

import com.agoda.bapi.common.message.creation.BreakDownTypeID
import com.agoda.bapi.common.model.flight.FlightBookingToken
import com.agoda.bapi.common.model.flight.flightModel.{Breakdown, BreakdownPerPax}
import com.agoda.bapi.common.util.BookingTokenHelper
import com.agoda.bapi.common.util.TokenDeserializers._
import com.agoda.integration.flow.fixture.BaseFlowSpec
import com.agoda.mpb.common.BookingType

class FlightWithHackerFarePaidSeatCreateItineraryTest extends BaseFlowSpec {

  "Create itinerary" should {
    "create response ok" in {
      // Setup booking
      val setupResponse = defaultSetupBooking(getResourcePath)
      defaultSetupBookingAssertions(setupResponse)

      // Extra assertion for setup
      //

      // Create booking
      val createResponse =
        defaultCreateBooking(getResourcePath, setupResponse.bookingResponse.head.bookingToken.head.token)
      defaultCreateBookingAssertions(createResponse)

      // Extra assertion for create
      //

      assert(createResponse.success, "Status should be success")
      assert(createResponse.itinerary.map(_.itineraryId).nonEmpty, "itinerary id should not be empty")
      assert(
        createResponse.itinerary.flatMap(_.flights.headOption.map(_.bookingId)).nonEmpty,
        "flight booking id should not be empty"
      )
      assert(createResponse.itinerary.map(_.statusToken).nonEmpty, "polling token should not be empty")
      assert(createResponse.duplicateBookings.isEmpty, "duplicate booking should be empty")

      val transactionModel = getTransactionModel

      val bookingIds        = createResponse.itinerary.map(_.flights).getOrElse(Seq.empty).map(_.bookingId)
      val outboundBookingId = bookingIds.head
      val inboundBookingId  = bookingIds.last

      val outboundSeatSelection = transactionModel.get.flightBookingActionStates
        .filter(_.bookingState.get.bookingId == outboundBookingId)
        .head
        .bookingState
        .get
        .seatSelections
        .head

      assertResult(1, "flight_segment_index")(outboundSeatSelection.flightSegmentIndex)
      assertResult("1", "seat_row")(outboundSeatSelection.seatRow)
      assertResult("A", "seat_column")(outboundSeatSelection.seatColumn)
      assertResult(1, "flight_seat_state")(outboundSeatSelection.flightSeatState)
      assertResult(true, "price_amount")(outboundSeatSelection.priceAmount.getOrElse(0.0) > 0)
      assertResult(Some("USD"), "price_currency")(outboundSeatSelection.priceCurrency)
      assertResult(1, "rec_status")(outboundSeatSelection.recStatus)
      assertResult(false, "supplier_data")(outboundSeatSelection.supplierData.isEmpty)
      assertResult(Some(false), "is_partial_settlement_required")(outboundSeatSelection.isPartialSettlementRequired)

      val inboundSeatSelection = transactionModel.get.flightBookingActionStates
        .filter(_.bookingState.get.bookingId == inboundBookingId)
        .head
        .bookingState
        .get
        .seatSelections
        .head

      assertResult(1, "flight_segment_index")(inboundSeatSelection.flightSegmentIndex)
      assertResult("2", "seat_row")(inboundSeatSelection.seatRow)
      assertResult("B", "seat_column")(inboundSeatSelection.seatColumn)
      assertResult(1, "flight_seat_state")(inboundSeatSelection.flightSeatState)
      assertResult(true, "price_amount")(inboundSeatSelection.priceAmount.getOrElse(0.0) > 0)
      assertResult(Some("USD"), "price_currency")(inboundSeatSelection.priceCurrency)
      assertResult(1, "rec_status")(inboundSeatSelection.recStatus)
      assertResult(false, "supplier_data")(inboundSeatSelection.supplierData.isEmpty)
      assertResult(Some(false), "is_partial_settlement_required")(inboundSeatSelection.isPartialSettlementRequired)

      val bookingCreationToken =
        BookingTokenHelper.extractCreationObject(setupResponse.bookingResponse.head.bookingToken.head).get
      val flightBookingTokens =
        BookingTokenHelper.extractCreationObjects[Seq[FlightBookingToken]](bookingCreationToken.flights).get.head._2

      val outboundToken      = flightBookingTokens.head
      val outboundBreakdowns = transactionModel.head.flightBookingActionStates.head.bookingState.head.breakdown
      val outboundBreakdownsPerPax =
        transactionModel.head.flightBookingActionStates.head.bookingState.head.breakdownPerPax

      val inboundToken      = flightBookingTokens.last
      val inboundBreakdowns = transactionModel.head.flightBookingActionStates.last.bookingState.head.breakdown
      val inboundBreakdownsPerPax =
        transactionModel.head.flightBookingActionStates.last.bookingState.head.breakdownPerPax

      assertFinancialBreakdown(
        outboundBookingId,
        outboundToken,
        outboundBreakdowns,
        BreakDownTypeID.FlightSurchargeSeat.id
      )
      assertFinancialBreakdown(
        inboundBookingId,
        inboundToken,
        inboundBreakdowns,
        BreakDownTypeID.FlightSurchargeSeat.id
      )

      assertFinancialBreakdown(
        outboundBookingId,
        outboundToken,
        outboundBreakdowns,
        BreakDownTypeID.FlightFare.id
      )
      assertFinancialBreakdown(
        inboundBookingId,
        inboundToken,
        inboundBreakdowns,
        BreakDownTypeID.FlightFare.id
      )

      assertPerPaxFinancialBreakdownForItemId(
        outboundBookingId,
        outboundToken,
        outboundBreakdownsPerPax,
        BreakDownTypeID.FlightSurchargeSeat.id
      )

      assertPerPaxFinancialBreakdownForItemId(
        inboundBookingId,
        inboundToken,
        inboundBreakdownsPerPax,
        BreakDownTypeID.FlightSurchargeSeat.id
      )

      assertPerPaxFinancialBreakdownForItemId(
        outboundBookingId,
        outboundToken,
        outboundBreakdownsPerPax,
        BreakDownTypeID.FlightFare.id
      )

      assertPerPaxFinancialBreakdownForItemId(
        inboundBookingId,
        inboundToken,
        inboundBreakdownsPerPax,
        BreakDownTypeID.FlightFare.id
      )
    }
  }

  private def assertFinancialBreakdown(
      bookingId: Long,
      token: FlightBookingToken,
      breakdowns: Seq[Breakdown],
      breakdownTypeId: Int
  ): Unit = {
    val tokenBreakdowns =
      token.info.get.priceBreakdowns.filter(_.typeId == breakdownTypeId)
    val transactionModelBreakdowns = breakdowns.filter(breakdown => breakdown.typeId == breakdownTypeId)

    assert(tokenBreakdowns.size == transactionModelBreakdowns.size, "number of item")
    tokenBreakdowns.sortBy(_.itemId).zip(transactionModelBreakdowns.sortBy(_.itemId)).map {
      case (expected, actual) =>
        assert(actual.bookingId.getOrElse(0) == bookingId, s"booking_id")
        assert(actual.bookingType.getOrElse(0) == BookingType.Flight.id, s"booking_type")
        assert(actual.localCurrency == expected.localCurrency, s"local_currency")
        assert(actual.itemId == expected.itemId, s"item_id")
        assert(actual.typeId === expected.typeId, s"type_id")
        assert(actual.exchangeRate.compareTo(expected.exchangeRate.toDouble) == 0, "exchange_rate")
        assert(actual.vendorExchangeRate.compareTo(expected.vendorExchangeRate.toDouble) == 0, "vendor_exchange_rate")
        assert(actual.localAmount.compareTo(expected.localAmount.toDouble) == 0, "local_amount")
        assert(actual.requestedAmount.getOrElse(0.0).compareTo(expected.reqAmount.toDouble) == 0, "requested_amount")
        assert(actual.usdAmount.compareTo(expected.usdAmount.toDouble) == 0, "usd_amount")
    }
  }

  private def assertPerPaxFinancialBreakdownForItemId(
      bookingId: Long,
      token: FlightBookingToken,
      breakdownsPerPax: Seq[BreakdownPerPax],
      breakdownTypeId: Int
  ): Unit = {

    val expectedList = token.info.get.priceBreakdownsPerPax
      .getOrElse(Seq.empty)
      .groupBy(_.paxRefId)
      .mapValues(_.filter(item => item.typeId == breakdownTypeId))
      .toList
      .sortBy(_._1)
      .flatMap(_._2)

    val actualList = breakdownsPerPax
      .filter(breakdown => breakdown.typeId == breakdownTypeId)
      .groupBy(_.paxId)
      .toList
      .sortBy(_._1)
      .flatMap(_._2.sortBy(_.itemId))

    assert(actualList.size == expectedList.size, "number of item")
    expectedList.sortBy(_.itemId).zip(actualList.sortBy(_.itemId)).map {
      case (expected, actual) =>
        assert(actual.bookingId.getOrElse(0) == bookingId, "booking_id")
        assert(actual.bookingType.getOrElse(0) == BookingType.Flight.id, "booking_type")
        assert(actual.localCurrency == expected.localCurrency, "local_currency")
        assert(actual.itemId == expected.itemId, "item_id")
        assert(actual.exchangeRate.compareTo(expected.exchangeRate.toDouble) == 0, "exchange_rate")
        assert(actual.vendorExchangeRate.compareTo(expected.vendorExchangeRate.toDouble) == 0, "vendor_exchange_rate")
        assert(actual.localAmount.compareTo(expected.localAmount.toDouble) == 0, "local_amount")
        assert(actual.requestedAmount.getOrElse(0.0).compareTo(expected.reqAmount.toDouble) == 0, "requested_amount")
        assert(actual.usdAmount.compareTo(expected.usdAmount.toDouble) == 0, "usd_amount")
    }
  }
}
