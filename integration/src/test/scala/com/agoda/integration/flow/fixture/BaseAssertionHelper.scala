package com.agoda.integration.flow.fixture

import com.agoda.bapi.common.message.creation.{BookingStatus, CreateBookingResponse}
import com.agoda.bapi.common.message.setupBooking.SetupBookingResponse
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.message.{InitializeBookingStatus, InitializeBookingStatusCategory}
import com.agoda.bapi.common.model.creation.BAPIBooking
import com.agoda.bapi.common.token.{BookingTokenEncryptionHelper, MultiProductBookingToken, MultiProductCreationBookingToken}
import com.agoda.bapi.common.util.OptionUtils._
import com.agoda.bapi.common.util.TokenDeserializers._
import com.agoda.bapi.common.util.{BookingTokenHelper, SetupContentInvalid, TokenDeserializers}
import com.agoda.bapi.creation.model.db.{BookingActionState, MultiProductBookingInsertionModel}
import com.agoda.integration.flow.mock.MockCollector
import com.agoda.mpb.common.serialization.Serialization
import com.agoda.mpbe.state.product.common.FinancialBreakdown
import org.scalatest.{OptionValues, TryValues}

trait BaseAssertionHelper extends OptionValues with TryValues {

  private val precisionTolerance = 0.1

  def defaultSetupBookingAssertions(setupResponse: SetupBookingResponse): Unit = {
    assert(setupResponse.success, "Response should be success")
    assert(
      setupResponse.bookingResponse.get.serverStatus.status == InitializeBookingStatus.Ok,
      s"serverStatus.status should be OK"
    )
    assert(
      setupResponse.bookingResponse.get.serverStatus.category == InitializeBookingStatusCategory.Success,
      s"serverStatus.category should be SUCCESS"
    )
    assert(setupResponse.bookingResponse.head.bookingToken.head.token.nonEmpty, "Booking token should not be empty")
  }

  def getSetupBookingBapiBooking(setupResponse: SetupBookingResponse): BAPIBooking = {
    val bookingToken = setupResponse.bookingResponse.get.bookingToken.get
    val multiProductBookingToken =
      BookingTokenHelper.extractCreationObject(bookingToken)
    val tokenKeyBapiBookingMap =
      BookingTokenHelper.extractPropertyCreationObjects(multiProductBookingToken.get)
    val productTokenKey =
      setupResponse.bookingResponse.get.products.properties.head.id
    val bapiBooking = tokenKeyBapiBookingMap.get(productTokenKey)
    bapiBooking
  }

  def defaultCreateBookingAssertions(createResponse: CreateBookingResponse): Unit = {
    // Default assertion for create
    assert(createResponse.success, "Response should be success")
    assert(
      createResponse.status.get.bookingStatus == BookingStatus.Processing,
      "Booking Status should be BookingStatus.Processing"
    )
    assert(createResponse.itinerary.nonEmpty, "Itinerary should not be empty")
    assert(
      createResponse.itinerary.get.bookings.nonEmpty || createResponse.itinerary.get.flights.nonEmpty || createResponse.itinerary.get.activities.nonEmpty || createResponse.itinerary.get.cars.nonEmpty,
      "Bookings should not be empty"
    )
  }

  protected def assertFinancialBreakdown(
      financialBreakdowns: Seq[FinancialBreakdown],
      itineraryId: Long,
      bookingId: Long,
      bookingType: Int = 102,
      itemId: Int,
      typeId: Int = 301,
      taxFeeId: Int = 0,
      quantity: Int = 1,
      localCurrency: String = "USD",
      localAmount: Double,
      exchangeRate: Double = 1,
      usdAmount: Double,
      requestedAmount: Double,
      vendorExchangeRate: Double = 1
  ): Unit = {
    val financialBreakdown = financialBreakdowns
      .filter(breakdown =>
        breakdown.itineraryId == itineraryId
          && breakdown.bookingId.value == bookingId
          && breakdown.itemId == itemId
      )
      .head
    assert(financialBreakdown.itineraryId == itineraryId)
    assert(financialBreakdown.bookingType.value == bookingType)
    assert(financialBreakdown.bookingId.value == bookingId)
    assert(financialBreakdown.itemId == itemId)
    assert(financialBreakdown.typeId == typeId)
    assert(financialBreakdown.taxFeeId.value == taxFeeId)
    assert(financialBreakdown.quantity == quantity)
    assert(financialBreakdown.localCurrency == localCurrency)
    assert(financialBreakdown.localAmount == localAmount)
    assert(financialBreakdown.exchangeRate == exchangeRate)
    assert(financialBreakdown.usdAmount == usdAmount)
    assert(financialBreakdown.requestedAmount.value == requestedAmount)
    assert(financialBreakdown.vendorExchangeRate == vendorExchangeRate)
  }

  def getBookingActionState: BookingActionState = getBookingActionStateByBookingId(0)

  def getBookingActionStateByBookingId(bookingId: Long = 0): BookingActionState = {
    val transactionModel = getTransactionModel

    assert(transactionModel.isDefined, "transactionModel should be collected")
    assert(
      transactionModel.head.workflowActions.exists(_.bookingId.isDefined),
      "propertyBookingState should not be empty"
    )
    val rawBookingActionState = bookingId match {
      case bid if bid > 0 => transactionModel.head.workflowActions.filter(_.bookingId.getOrElse(0L) == bid).head.state
      case _              => transactionModel.head.workflowActions.filter(_.bookingId.isDefined).head.state
    }

    val bookingActionState = Serialization.fromJson[BookingActionState](rawBookingActionState)
    bookingActionState
  }

  def getMasterActionState: BookingActionState = {
    val transactionModel = getTransactionModel

    assert(transactionModel.isDefined, "transactionModel should be collected")
    val rawMasterActionState = transactionModel.head.workflowActions.filter(_.bookingId.isEmpty).head.state
    val masterActionState    = Serialization.fromJson[BookingActionState](rawMasterActionState)
    masterActionState
  }

  def getTransactionModel: Option[MultiProductBookingInsertionModel] =
    MockCollector.storageGet[MultiProductBookingInsertionModel]("transactionModel")

  def getBookingActionMessage(messageId: Option[Long]): Option[String] = {
    MockCollector.storageGet[String](s"bookingActionMessage_${messageId.get}")
  }

  def getBookingToken(tokenMessage: TokenMessage): MultiProductCreationBookingToken = {
    val creationBookingToken = (
      for {
        decryptedTokenString     <- BookingTokenEncryptionHelper.decryptToken(tokenMessage)
        multiTokenJsonString     <- TokenDeserializers.toToken(decryptedTokenString)
        multiProductBookingToken <- TokenDeserializers[MultiProductBookingToken].deserialize(multiTokenJsonString)
        creationBookingTokenStr  <- multiProductBookingToken.creationBookingToken.toTry(new SetupContentInvalid())
        creationBookingToken <-
          TokenDeserializers[MultiProductCreationBookingToken].deserialize(creationBookingTokenStr)
      } yield creationBookingToken
    ).success.value
    creationBookingToken
  }

  def assertAreOptionsEqual(
      a: Option[BigDecimal],
      b: Option[BigDecimal],
      precisionTolerance: BigDecimal = precisionTolerance
  ): Unit = {
    def isEqualWithTolerance(value1: BigDecimal, value2: BigDecimal): Boolean = {
      (value1 - value2).abs <= precisionTolerance
    }

    val areOptionsEqual = (a, b) match {
      case (Some(value1), Some(value2)) => isEqualWithTolerance(value1, value2)
      case (None, None)                 => true
      case _                            => false
    }

    assert(areOptionsEqual)
  }
}
