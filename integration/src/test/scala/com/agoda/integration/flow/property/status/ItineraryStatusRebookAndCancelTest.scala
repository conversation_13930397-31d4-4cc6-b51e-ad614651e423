package com.agoda.integration.flow.property.status

import com.agoda.bapi.common.message.creation.CreatedBookingStatus
import com.agoda.integration.flow.fixture.{BaseFlowSpec, RebookAndCancelStatusFixture}
import com.agoda.integration.flow.fixture.TestRoutes.mockWorkflowDBProxy

class ItineraryStatusRebookAndCancelTest extends BaseFlowSpec with RebookAndCancelStatusFixture {

  "Itinerary Status" should {
    "Does not have reject reason with status token v6 for successful booking" in {

      setupCommonMocks()
      mockWorkflowDBProxy.getBookingActionByItineraryIdResponse = Some(
        originalBooking
      )

      val statusToken    = createStatusToken(Some(originalOperationId))
      val statusResponse = defaultGetStatus(statusToken, forcedExperiments)
      val booking        = statusResponse.itinerary.flatMap(_.bookings.find(_.bookingId == originalBookingObj.bookingId)).get

      assert(booking.bookingStatus == CreatedBookingStatus.BookingConfirmed)
      assert(booking.rejectReason.isEmpty)

    }

    "Has reject reason with status token v6 for failed rebooking" in {

      setupCommonMocks()
      mockWorkflowDBProxy.getBookingActionByItineraryIdResponse = Some(
        originalBooking ++ failedBooking
      )

      val statusToken    = createStatusToken(Some(failedOperationId))
      val statusResponse = defaultGetStatus(statusToken, forcedExperiments)
      val booking        = statusResponse.itinerary.flatMap(_.bookings.find(_.bookingId == failedBookingObj.bookingId)).get

      assert(booking.bookingStatus == CreatedBookingStatus.BookingRejected)
      assert(booking.rejectReason.isDefined)
    }

    "Does not have reject reason with status token v6 for failed rebooking with success retry within retention period" in {

      setupCommonMocks()
      mockWorkflowDBProxy.getBookingActionByItineraryIdResponse = Some(
        originalBooking ++ failedBooking ++ successBooking
      )

      val statusToken    = createStatusToken(Some(successOperationId))
      val statusResponse = defaultGetStatus(statusToken, forcedExperiments)
      val booking        = statusResponse.itinerary.flatMap(_.bookings.find(_.bookingId == successBookingObj.bookingId)).get

      assert(booking.bookingStatus == CreatedBookingStatus.BookingProcessing)
      assert(booking.rejectReason.isEmpty)

    }

    "Does not have reject reason with status token v6 for failed rebooking with success retry after retention period" in {

      setupCommonMocks()
      mockWorkflowDBProxy.getBookingActionByItineraryIdResponse = Some(
        failedBooking ++ successBooking
      )

      val statusToken    = createStatusToken(Some(successOperationId))
      val statusResponse = defaultGetStatus(statusToken, forcedExperiments)
      val booking        = statusResponse.itinerary.flatMap(_.bookings.find(_.bookingId == successBookingObj.bookingId)).get

      assert(booking.bookingStatus == CreatedBookingStatus.BookingProcessing)
      assert(booking.rejectReason.isEmpty)

    }
    "Does not have reject reason when operation id is not present for failed rebooking with success retry within retention period" in {

      setupCommonMocks()
      mockWorkflowDBProxy.getBookingActionByItineraryIdResponse = Some(
        originalBooking ++ failedBooking ++ successBooking
      )

      val statusToken    = createStatusToken(None)
      val statusResponse = defaultGetStatus(statusToken, forcedExperiments)
      val booking        = statusResponse.itinerary.flatMap(_.bookings.find(_.bookingId == successBookingObj.bookingId)).get

      assert(booking.bookingStatus == CreatedBookingStatus.BookingProcessing)
      assert(booking.rejectReason.isEmpty)

    }

    "Has rejectReason incorrectly when operation id is not present for failed rebooking with success retry after retention period" in {

      setupCommonMocks()
      mockWorkflowDBProxy.getBookingActionByItineraryIdResponse = Some(
        failedBooking ++ successBooking
      )

      val statusToken    = createStatusToken(None)
      val statusResponse = defaultGetStatus(statusToken, forcedExperiments)
      val booking        = statusResponse.itinerary.flatMap(_.bookings.find(_.bookingId == successBookingObj.bookingId)).get

      assert(booking.bookingStatus == CreatedBookingStatus.BookingProcessing)
      assert(booking.rejectReason.isDefined)

    }
  }
}
