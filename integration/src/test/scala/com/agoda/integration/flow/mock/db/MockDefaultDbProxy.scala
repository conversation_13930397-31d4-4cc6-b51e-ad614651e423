package com.agoda.integration.flow.mock.db

import com.agoda.bapi.common.message.creation.CreatedBookingStatus.CreatedBookingStatus
import com.agoda.bapi.common.model.db.CountryInfo
import com.agoda.bapi.common.model.{BookingId, ItineraryId, SiteId}
import com.agoda.bapi.creation.model.PaymentLimitationInfo
import com.agoda.bapi.creation.model.db.{CardBinRangeInfo, EbeLiteBookingObject, PropertyDuplicatedBookingCandidate}
import com.agoda.bapi.creation.model.properties.PropertyBookingDuplicationCriteria
import com.agoda.bapi.creation.proxy.DefaultDbProxy
import com.agoda.bapi.creation.validation.LockingInsertionResult
import com.agoda.integration.flow.mock.{MockException, MockTrait}
import org.joda.time.DateTime

import scala.concurrent.Future

class MockDefaultDbProxy extends DefaultDbProxy with MockTrait {

  var isRedirectCardResponse: Option[Boolean]                                                         = None
  var isPaymentTokenEnabledResponse: Option[Boolean]                                                  = None
  var findAllDuplicatedBookingCandidatesBFDBResponse: Option[Seq[PropertyDuplicatedBookingCandidate]] = None
  var getEbeLiteBookingObjectsResponse: Option[Seq[EbeLiteBookingObject]]                             = None

  override def resetAll(): Unit = {
    isRedirectCardResponse = None
    isPaymentTokenEnabledResponse = None
    findAllDuplicatedBookingCandidatesBFDBResponse = None
    getEbeLiteBookingObjectsResponse = None
  }

  override def getEbeLiteBookingObjects(itineraryId: ItineraryId): Future[Seq[EbeLiteBookingObject]] =
    getEbeLiteBookingObjectsResponse match {
      case Some(response) => Future.successful(response)
      case _              => Future.successful(Seq())
    }

  override def getEbeLiteBookingObjectsByBookingId(bookingId: BookingId): Future[Seq[EbeLiteBookingObject]] =
    Future.failed(MockException.unimplementedException)

  override def getBinRangeData(bin: String): Future[Option[CardBinRangeInfo]] =
    Future.failed(MockException.unimplementedException)

  override def getBinLength(): Future[Int] = Future.successful(11)

  override def getCountryInfos(): Future[List[CountryInfo]] = Future.failed(MockException.unimplementedException)

  override def findAllDuplicatedBookingCandidatesBFDB(
      propertyBookingDuplicationCriteria: PropertyBookingDuplicationCriteria,
      dayOffset: Int,
      isTagIdCheckEnabled: Option[Boolean]
  ): Future[Seq[PropertyDuplicatedBookingCandidate]] = findAllDuplicatedBookingCandidatesBFDBResponse match {
    case Some(response) => Future.successful(response)
    case _              => Future.successful(Seq())
  }

  override def getGuestRestrictedNationality(countryId: Int): Future[Option[Int]] = countryId match {
    case 0 => Future.successful(Some(-2))
    case _ => Future.successful(Some(1))
  }

  override def checkDuplicateRequestAndInsertNewCandidate(
      hashedRequest: String,
      requestTypeName: String,
      expiryDatetime: DateTime
  ): Future[Int] = Future.successful(LockingInsertionResult.InsertedLocking)

  override def isHotelNationalityRestriction(hotelId: Int, countryId: Int): Future[Option[Boolean]] =
    Future.successful(Some(false))

  override def isAirportNationalityRestriction(airportCode: String, countryId: Int): Future[Option[Boolean]] =
    Future.successful(Some(false))

  override def getHotelCountryIdWithRecStatus(hotelId: Int): Future[Option[Int]] = Future.successful(Some(1))

  override def getAirportCountryId(airportCode: String): Future[Option[Int]] =
    Future.successful(Some(1)) // this is only used for check `RestrictedAirportNationality`

  override def isCurrencyOffered(currencyCode: String): Future[Boolean] = Future.successful(true)

  override def getConfigurations(groupId: Int, key: String): Future[Option[String]] = (groupId, key) match {
    case (6, "RNDCCY") => Future.successful(Some("BIF,BYR,DJF,GNF,JPY,KMF,KRW,PYG,RWF,VND,VUV,XPF,IDR"))
    case (1, "SSBKEY") => Future.successful(Some("Key"))
    case (1, "AESSAL") => Future.successful(Some("Salt"))
    case _             => Future.failed(MockException.unimplementedException)
  }

  override def isRedirectCard(paymentMethodId: Int): Future[Boolean] =
    isRedirectCardResponse match {
      case Some(response) => Future.successful(response)
      case _              => Future.successful(false)
    }

  override def updatePublishStatus(itineraryId: Int): Future[Boolean] =
    Future.failed(MockException.unimplementedException)

  override def isPaymentTokenEnabled(whitelabelId: Int, paymentMethodId: Int, productTypeId: Int): Future[Boolean] =
    isPaymentTokenEnabledResponse match {
      case Some(response) => Future.successful(response)
      case _              => Future.successful(false)
    }

  override def setPropertyBookingState(bookingId: Long, state: CreatedBookingStatus): Future[Int] =
    Future.successful(1)

  override def getNextItineraryNumber: Future[Long] = Future.failed(MockException.unimplementedException)

  override def getNextBookingId: Future[BookingId] = Future.failed(MockException.unimplementedException)

  override def getPaymentLimitation(siteId: SiteId): Future[Option[PaymentLimitationInfo]] = Future.successful(
    Some(
      PaymentLimitationInfo(
        siteId = siteId,
        paymentMethodIds = None,
        paymentMethodNames = None,
        bookingStartDate = "",
        bookingEndDate = "",
        errorCMSId = 0,
        alternatePaymentCMSId = None,
        platformIds = None,
        binList = None
      )
    )
  )
}
