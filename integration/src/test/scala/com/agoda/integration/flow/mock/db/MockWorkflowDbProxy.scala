package com.agoda.integration.flow.mock.db

import com.agoda.bapi.common.model.booking.local.{BookingActionWorkflowPhase, BookingActionWorkflowState, BookingWorkflowAction}
import com.agoda.bapi.common.model.{ActionId, FlightBookingId, ItineraryId, TopicId, WorkflowId}
import com.agoda.bapi.creation.model.db.BookingActionIdentifier
import com.agoda.bapi.creation.proxy.WorkflowDbProxy
import com.agoda.bapi.creation.service.BookingActionMessage
import com.agoda.integration.flow.mock.{MockCollector, MockException, MockTrait}
import com.github.nscala_time.time.Imports.DateTime
import org.joda.time.LocalDate

import scala.concurrent.Future

class MockWorkflowDbProxy extends WorkflowDbProxy with MockTrait {

  val defaultRecDateTime: DateTime                                              = LocalDate.now().toDateTimeAtStartOfDay()
  var getBookingActionByItineraryIdResponse: Option[Seq[BookingWorkflowAction]] = None
  var getBookingActionMessageResponse: Option[Seq[BookingActionMessage]]        = None

  override def resetAll(): Unit = {
    getBookingActionByItineraryIdResponse = None
    getBookingActionMessageResponse = None
  }

  override def insertBookingAction(bookingAction: BookingWorkflowAction): Future[BookingWorkflowAction] =
    Future.failed(MockException.unimplementedException)

  override def insertBookingActionMessage(message: BookingActionMessage): Future[ActionId] =
    Future.successful(1L)

  override def updateBookingActionMessage(messageId: ActionId, content: String): Future[Int] = {
    MockCollector.storagePut(s"bookingActionMessage_$messageId", content)
    Future.successful(1)
  }

  override def getBookingAction(actionId: ActionId): Future[Option[BookingWorkflowAction]] =
    Future.failed(MockException.unimplementedException)

  override def getBookingActionByBookingId(bookingId: FlightBookingId): Future[Option[BookingWorkflowAction]] =
    Future.failed(MockException.unimplementedException)

  override def getBookingActionByWorkflowId(
      itineraryId: ItineraryId,
      workflowId: WorkflowId
  ): Future[Option[BookingWorkflowAction]] = Future.failed(MockException.unimplementedException)

  override def getBookingActionMessage(actionId: ActionId): Future[Seq[BookingActionMessage]] =
    getBookingActionMessageResponse match {
      case Some(response) => Future.successful(response)
      case _              => Future.successful(Seq())
    }

  override def getBookingActionBySupplierBookingId(
      supplierBookingId: String,
      topicId: TopicId,
      uuid: String
  ): Future[Option[BookingWorkflowAction]] = Future.failed(MockException.unimplementedException)

  override def getMasterBookingActionByItineraryId(ItineraryId: ActionId): Future[Option[BookingWorkflowAction]] =
    Future.failed(MockException.unimplementedException)

  override def getBookingActionByItineraryId(itineraryId: ItineraryId): Future[Seq[BookingWorkflowAction]] =
    getBookingActionByItineraryIdResponse match {
      case Some(response) => Future.successful(response)
      case _              => Future.successful(Seq())
    }

  override def getBaseBookingActionBySupplierBookingId(
      supplierBookingId: String,
      topicId: TopicId,
      uuid: String
  ): Future[Option[BookingWorkflowAction]] = Future.failed(MockException.unimplementedException)

  override def getBookingActionIdentifierByBookingIdAndTopicId(
      bookingId: FlightBookingId,
      topicId: TopicId,
      recCreatedBy: String
  ): Future[Option[BookingActionIdentifier]] =
    Future.failed(MockException.unimplementedException)
}
