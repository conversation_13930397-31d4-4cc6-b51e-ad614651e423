schema: v1
service: booking-creation
environments:
  regression:
    description: dev environment
    spec:
      ports:
        - number: 8080
          protocol: TCP
        - number: 5006
          protocol: TCP
        - number: 50051
          protocol: GRPC
      artifact:
        image:
          repository: bapi/booking-creation
          tag: PR-7077
      healthCheck:
        readiness:
          httpGet:
            path: /online
            port: 8080
          initialDelaySeconds: 5
          timeoutSeconds: 1
          periodSeconds: 1
          failureThreshold: 5
        startup:
          httpGet:
            path: /health?type=all
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          failureThreshold: 60
      envs:
        BFDB_SERVICE_HOST: bfdb
        DOCKER_CONFIG_FILE: docker.conf
        JAVA_OPTS: >
          -Dlog.dir=log -Dag-consul.servers.0=http://localhost:8500

          -Dconfig.file=/app/conf/docker.conf 

          -XX:ActiveProcessorCount=8 

          -Denigma.client.consul-discovery.enabled=false 

          -Dag-http-client.mesh.services.activity-search-client.discovery.consul.tags.0=xxx 

          -Dag-consul.servers.0=http://localhost:8500
        TZ: Asia/Bangkok
        ancillaryV2_url: ancillary-api:8080
        conf.abs.port: "8080"
        conf.abs.root.host.url: mock-api
        conf.abs.root.isHttps: "false"
        conf.abs.root.port: "8080"
        conf.activity-search-api-config.server: activity-search:8084
        conf.agoda.creation.creditCardApiV2.localEndpointDevstack: creditcard-api-local:80
        conf.ancillary-api-pc-config.endPoints.url: http://ancillary-api:8080
        conf.bapidb.password: agoda123*
        conf.bapidb.url: **********************************************************************************************************************************;
        conf.bapidb.username: sa
        conf.bfdb-bcre.password: agoda123*
        conf.bfdb-bcre.url: ******************************************************************************************************************************************;
        conf.bfdb-bcre.username: sa
        conf.bkgdb.password: agoda123*
        conf.bkgdb.url: *********************************************************************************************************************************;
        conf.bkgdb.username: sa
        conf.cdb.password: agoda123*
        conf.cdb.url: ***************************************************************************************************************************;
        conf.cdb.username: sa
        conf.consul.server: http://localhost:8500
        conf.customerApi.url: http://customer-api:8087/V1/RewardsApiService
        conf.customerApi.urlv2: http://customer-api:8087/v2/transit
        conf.customerApi.urlv2Authed: http://customer-api:8087/v2/authed
        conf.customerApi.urlv2Base: http://customer-api:8087/v2
        conf.deliveryClient.urls.url: http://localhost:80/
        conf.enigma.client.api-key: test
        conf.enigma.client.base-url: http://enigma:8080
        conf.experimentsPlatform.experiments-service.host: bk-devweb-1001.agoda.local
        conf.flights-api-config.endPoints.url: flapi:8080
        conf.fraud-api-client.endpoint: http://fraud-api
        conf.fraud-api.endpoint: fraud-api-grpc:50000
        conf.fraudApi.url: http://fraud-api:80/frauddetection/fraudservice.asmx
        conf.gandalf-api-config.url: http://promo-api:8080
        conf.mdb.password: agoda123*
        conf.mdb.url: *****************************************************************************************************************************************;
        conf.mdb.username: sa
        conf.op-rabbit.connection.hosts.url: rabbitmq
        conf.op-rabbit.connection.password: "123456"
        conf.op-rabbit.connection.port: "5672"
        conf.op-rabbit.connection.username: devrabbit
        conf.papi.endpoints.url: propertyapisearchdocker
        conf.papi.port: "8080"
        conf.payment-api-v2.endpoint.url: payment-api-local:5000
        conf.securityApi.url: http://customer-api:8087
        conf.tprm.url: http://customer-api:8087
        conf.vehicle-search-api-config.server: car-search:8084
        enigma_client: enigma:8080
      resources:
        limits:
          memory: 4Gi
        requests:
          cpu: 500m
          memory: 2Gi
      consul:
        enabled: true
      vault:
        enabled: true
      ingress:
        enabled: true
        protocol: http
        port: 8080
      ingresses:
        - enabled: true
          protocol: grpc
          port: 50051
        - enabled: true
          protocol: http
          port: 8080
      db:
        migration: true
    dependencies:
      - registry:
          service: bfdb
          environment: pinto_qa_data_hotel-list-flapi-tests
          version: latest
      - registry:
          service: mdb
          environment: pinto_qa_data_hotel-list-bpf-velocity
          version: latest
      - registry:
          service: cdb
          environment: pinto_qa_data_hotel-list-bpf-velocity
          version: latest
      - registry:
          service: bkgdb
          environment: pinto_qa_schema_nodata
          version: latest
      - registry:
          service: bapidb
          environment: pinto_qa_schema_nodata
          version: latest
      - internal:
          spec:
            name: rabbitmq
            ports:
              - number: 5672
                protocol: TCP
              - number: 15672
                protocol: TCP
            artifact:
              image:
                repository: bpf/rabbitmq
                tag: test-devstack
            healthCheck:
              readiness:
                tcpSocket:
                  port: 15672
                periodSeconds: 5
                failureThreshold: 3
              startup:
                tcpSocket:
                  port: 15672
                periodSeconds: 1
                successThreshold: 1
                failureThreshold: 300
            envs:
              RABBITMQ_DEFAULT_PASS: "123456"
              RABBITMQ_DEFAULT_USER: devrabbit
              RABBITMQ_DEFAULT_VHOST: /
              RABBITMQ_ERLANG_COOKIE: 60d6368a-b4eb-41e1-a021-8e5939b02505
              RABBITMQ_WEBUI_PORT: "15672"
            resources:
              limits:
                memory: 1Gi
              requests:
                cpu: 200m
                memory: 512Mi
            ingress:
              enabled: true
              port: 15672
      - internal:
          spec:
            name: fraud-api
            ports:
              - number: 8080
                protocol: TCP
            artifact:
              image:
                repository: bpf/bpf-wiremock
                tag: latest
            healthCheck:
              readiness:
                tcpSocket:
                  port: 8080
              startup:
                tcpSocket:
                  port: 8080
                periodSeconds: 1
                successThreshold: 1
                failureThreshold: 60
            resources:
              limits:
                memory: 1Gi
              requests:
                cpu: 100m
                memory: 512Mi
            ingress:
              enabled: true
              port: 8080
      - internal:
          spec:
            name: fraud-api-grpc
            ports:
              - number: 50000
                protocol: GRPC
            artifact:
              image:
                repository: bpf/grpc-wiremock
                tag: latest
            healthCheck:
              readiness:
                tcpSocket:
                  port: 50000
              startup:
                tcpSocket:
                  port: 50000
                periodSeconds: 1
                successThreshold: 1
                failureThreshold: 60
            resources:
              limits:
                memory: 1Gi
              requests:
                cpu: 100m
                memory: 512Mi
            ingress:
              enabled: true
              port: 50000
      - internal:
          spec:
            name: customer-api
            ports:
              - number: 8087
                protocol: TCP
            artifact:
              image:
                repository: bpf/bpf-mock
                tag: latest
            healthCheck:
              readiness:
                tcpSocket:
                  port: 8087
              startup:
                tcpSocket:
                  port: 8087
                periodSeconds: 1
                successThreshold: 1
                failureThreshold: 60
            resources:
              limits:
                memory: 6Gi
              requests:
                cpu: 100m
                memory: 4Gi
            ingress:
              enabled: true
              port: 8087
      - internal:
          spec:
            name: propertyapisearchdocker
            ports:
              - number: 8080
                protocol: http
              - number: 5005
                protocol: tcp
              - number: 3333
                protocol: tcp
            artifact:
              image:
                repository: papi/search/propertyapi/merge_request
                tag: "10646.18374064"
                # repository: papi/search/propertyapi/develop
                # tag: latest
                # build:
                #   dockerfile: DockerfileGitlab
                #   context: .
            healthCheck:
              liveness:
                httpGet:
                  path: /ping
                  port: 8080
                initialDelaySeconds: 5
                periodSeconds: 5
                failureThreshold: 3
              readiness:
                httpGet:
                  path: /ping
                  port: 8080
                initialDelaySeconds: 5
                periodSeconds: 5
                failureThreshold: 3
              startup:
                httpGet:
                  path: /ping
                  port: 8080
                initialDelaySeconds: 30
                periodSeconds: 5
                failureThreshold: 40
            envs:
              CONFIG_FILE: dev-ci-devstack.conf
              DEPLOYMENT: dev-ci
              TZ: Asia/Bangkok
            resources:
              limits:
                cpu: "5"
                memory: 8Gi
              requests:
                cpu: "5"
                memory: 8Gi
            consul:
              enabled: true
            vault:
              enabled: true
            ingress:
              enabled: true
              port: 8080
            waitFor:
              services:
                - name: cdb
                  port: 1433
                - name: dfcitydb
                  port: 1433
            db:
              migration: true
          dependencies:
            - registry:
                service: db-cbcache
                environment: dev
                version: latest
            - registry:
                service: dfcitydb
                environment: papi_pinto_qa_data_papi-search-tests
                version: stable
            - registry:
                service: everest-docker
                environment: dev
                version: latest
            - registry:
                service: propertyapicontentdocker
                environment: papi
                version: stable
            - registry:
                service: propertyapipricingdocker
                environment: dev
                version: pull-18151376
            - registry:
                service: featurestore-serving
                environment: dev
                version: latest
            - registry:
                service: pricestreamapidocker
                environment: dev
                version: latest
            - registry:
                service: feast-core
                environment: dev
                version: latest
            - registry:
                service: customerapi
                environment: dev
                version: latest
            - registry:
                service: gp-commission-api
                environment: dev
                version: 921e9b51-5339
            - registry:
                service: cdb
                environment: papi_pinto_qa_data_papi-search-tests
                version: stable
            # - internal:
            #     spec:
            #       name: propertyapipricingdocker
            #       ports:
            #         - number: 8080
            #           protocol: http
            #         - number: 5005
            #           protocol: tcp
            #         - number: 3333
            #           protocol: tcp
            #       artifact:
            #         image:
            #           repository: papi/pricing/dfapi/pull
            #           tag: pull-18374218
            #           # repository: papi/search/dfapi/release
            #           # tag: latest
            #       healthCheck:
            #         liveness:
            #           httpGet:
            #             path: /ping
            #             port: 8080
            #           initialDelaySeconds: 5
            #           periodSeconds: 5
            #           failureThreshold: 3
            #         readiness:
            #           httpGet:
            #             path: /ping
            #             port: 8080
            #           initialDelaySeconds: 5
            #           periodSeconds: 5
            #           failureThreshold: 3
            #         startup:
            #           httpGet:
            #             path: /ping
            #             port: 8080
            #           initialDelaySeconds: 30
            #           periodSeconds: 5
            #           failureThreshold: 40
            #       envs:
            #         CONFIG_FILE: devstack.conf
            #         DEBUG: "true"
            #         MAXHEAP: 4G
            #         OTEL_PROPAGATOR_CUSTOM_HEADERS: ag-user-id,ag-language-id,ag-origin,ag-device-type-id,ag-traffic-group,ag-cid,ag-aid,ag-member-id,ag-bot-info,ag-gk-rq-priority,ag-gk-priority,ag-client-profile,ag-mse-pricing-token
            #         TZ: Asia/Bangkok
            #         YOUNGGEN: 3G
            #       resources:
            #         limits:
            #           memory: 6Gi
            #         requests:
            #           cpu: "2"
            #           memory: 6Gi
            #       consul:
            #         enabled: true
            #       vault:
            #         enabled: true
            #       ingress:
            #         enabled: true
            #         port: 8080
            #       waitFor:
            #         services:
            #           - name: whitelabel-service-api
            #             port: 80
            #           - name: dfhoteldb
            #             port: 1433
            #           - name: propertyapipricingpcstreamdocker
            #             port: 80
            #           - name: tax-management-api
            #             port: 8080
            #     dependencies:
            #       - registry:
            #           service: merlin
            #           environment: dev
            #           version: df-stable
            #       - registry:
            #           service: heisenberg-property
            #           environment: dev
            #           version: df-stable
            #       - registry:
            #           service: soybean-app
            #           environment: dev
            #           version: df-stable
            #       - registry:
            #           service: pricestreamapidocker
            #           environment: dev
            #           version: df-stable
            #       - registry:
            #           service: everest-docker
            #           environment: dev
            #           version: df-stable
            #       - registry:
            #           service: whitelabel-service-api
            #           environment: dev
            #           version: df-stable
            #       - registry:
            #           service: experiments-service
            #           environment: main
            #           version: latest
            #       - registry:
            #           service: db-cbcache
            #           environment: dev
            #           version: latest
            #       - registry:
            #           service: tax-management-api
            #           environment: api-upstream-dfapi
            #           version: latest
            #       - registry:
            #           service: cdb
            #           environment: dfapi_pinto_qa_data_papi-tests
            #           version: stable
            #       - registry:
            #           service: dfhoteldb
            #           environment: papi_pinto_qa_dfhotel_data_papi_search_test
            #           version: stable

      # - registry:
      #     service: propertyapisearchdocker
      #     version: latest
      #     environment: fake-api
      - registry:
          service: enigma
          environment: dev
          version: latest
      - registry:
          service: ebe-payment-api-netcore
          environment: intg_test
          version: master
      - registry:
          service: ebe-creditcard-service-netcore
          environment: central
          version: master
      - registry:
          service: creditcard-api-local
          environment: local
          version: master
      - registry:
          service: mock-api
          environment: mock
          version: latest
      - registry:
          service: soybean-app
          environment: dev
          version: latest
      - internal:
          spec:
            name: flapi
            ports:
              - number: 8080
                protocol: TCP
            artifact:
              image:
                repository: flights/flightsapi
                tag: "latest"
            envs:
              filtration_cache: "/user/it-flights-supply-booking/sgdev/filtration/RequestRules_test.csv"
              filtration_blacklist_cache: "/user/it-flights-supply-booking/sgdev/filtration/migration/RequestRules_test.csv"
              filtration_l2migration_cache: "/user/it-flights-supply-booking/sgdev/filtration/migration/l2/RequestRules_test.csv"
              filtration_l3migration_cache: "/user/it-flights-supply-booking/sgdev/filtration/migration/l2/RequestRules_test.csv" #check for L3
              inclusion_cache: "/user/it-flights-supply-booking/sgdev/inclusion/InclusionRules_test.csv"
              inclusion_blacklist_cache: "/user/it-flights-supply-booking/sgdev/inclusion/migration/InclusionRules_test.csv"
              inclusion_l2migration_cache: "/user/it-flights-supply-booking/sgdev/inclusion/migration/l2/InclusionRules_test.csv"
              inclusion_l3migration_cache: "/user/it-flights-supply-booking/sgdev/inclusion/migration/l2/InclusionRules_test.csv"
              soybean_connection: "http://soybean-app:2501"
              JAVA_OPTS: -Dappname=flights-api
                -Dconfig.file=/app/conf/application-flapi-devstack.conf
                -Djava.security.auth.login.config=/app/conf/flights-dev-jaas.conf
                -Dlog.dir=/var/log/flights-api
                -Dhk.cluster-20.kafka.authentication.enabled=false
                -Dag-consul.hk.hikari.databases
            ingress:
              enabled: true
              port: 8080
            healthCheck:
              startup:
                tcpSocket:
                  port: 8080
                initialDelaySeconds: 5
                periodSeconds: 5
                failureThreshold: 12
                timeoutSeconds: 5
              readiness:
                httpGet:
                  path: /healthcheck
                  port: 8080
                periodSeconds: 5
                failureThreshold: 3
                timeoutSeconds: 5
            resources:
              requests:
                cpu: "2"
                memory: 3Gi
              limits:
                memory: 6Gi
          dependencies:
            - internal:
                spec:
                  name: adp-kafka
                  ports:
                    - number: 9092
                      protocol: TCP
                    - number: 9093
                      protocol: TCP
                    - number: 9094
                      protocol: TCP
                  artifact:
                    image:
                      repository: adp-messaging/kafka-server
                      tag: "2.4.1"
                  envs:
                    HOST_IP: adp-kafka
                    POD_IP: adp-kafka
                  resources:
                    requests:
                      cpu: 100m
                      memory: 1Gi
                    limits:
                      memory: 4Gi
                  healthCheck:
                    startup:
                      tcpSocket:
                        port: 9092
                      initialDelaySeconds: 5
                      periodSeconds: 1
                      failureThreshold: 300
                    readiness:
                      tcpSocket:
                        port: 9092
                      periodSeconds: 5
                      failureThreshold: 3
            - internal:
                spec:
                  name: scylla
                  artifact:
                    image:
                      repository: flights/qa_scylla_data_flapi-tests
                      tag: "latest"
                  ports:
                    - number: 9042
                      protocol: TCP
                    - number: 9160
                      protocol: TCP
                  resources:
                    requests:
                      cpu: "2"
                      memory: 4Gi
                    limits:
                      cpu: "2"
                      memory: 4Gi
                  envs:
                    MAX_MEMORY: "1000"
                  ingresses:
                    - enabled: true
                      protocol: tcp
                      port: 9042
                  healthCheck:
                    startup:
                      exec:
                        command: [ "/bin/bash", "-c", "/usr/bin/cqlsh -u cassandra -p cassandra -e 'describe keyspaces' localhost" ]
                      initialDelaySeconds: 30
                      timeoutSeconds: 10
                      periodSeconds: 10
                    readiness:
                      exec:
                        command: [ "/bin/bash", "-c", "/usr/bin/cqlsh -u cassandra -p cassandra -e 'describe keyspaces' localhost" ]
                      initialDelaySeconds: 30
                      timeoutSeconds: 10
                      periodSeconds: 10
                    liveness:
                      exec:
                        command: [ "/bin/bash", "-c", "/usr/bin/cqlsh -u cassandra -p cassandra -e 'describe keyspaces' localhost" ]
                      initialDelaySeconds: 30
                      timeoutSeconds: 10
                      periodSeconds: 10

      - internal:
          spec:
            name: ancillary-api
            ports:
              - number: 8080
                protocol: TCP
            envs:
              JAVA_OPTS: "-Dconfig.file=/app/conf/application-mock.conf -Dhttp.server-port=8080"
              TZ: "Asia/Bangkok"
              creditcard_host.0: "ebe-creditcard-service-netcore:80"
            artifact:
              image:
                repository: flights/ancillary_api
                tag: "latest"
            consul:
              enabled: true
            vault:
              enabled: true
            ingress:
              enabled: true
              port: 8080
            resources:
              requests:
                cpu: "2"
                memory: 4Gi
              limits:
                cpu: "4"
                memory: 8Gi
            healthCheck:
              startup:
                httpGet:
                  path: /health
                  port: 8080
                initialDelaySeconds: 20
                periodSeconds: 5
                failureThreshold: 60
              readiness:
                httpGet:
                  path: /health
                  port: 8080
                initialDelaySeconds: 20
                periodSeconds: 10
                failureThreshold: 50
                timeoutSeconds: 15
      - internal:
          spec:
            name: promo-api
            ports:
              - number: 8080
                protocol: TCP
            artifact:
              image:
                repository: devops/devstack/service_proxifier
                tag: latest
            healthCheck:
              readiness:
                httpGet:
                  path: /healthcheck
                  port: 8080
                initialDelaySeconds: 1
                timeoutSeconds: 1
                periodSeconds: 5
                failureThreshold: 5
              startup:
                httpGet:
                  path: /healthcheck
                  port: 8080
                initialDelaySeconds: 1
                timeoutSeconds: 1
                periodSeconds: 1
                failureThreshold: 60
            envs:
              CONFIG: |
                server {
                    listen 8080 default_server;
                    location / {
                        proxy_pass http://promo-api-qa.qa.agoda.is:80;
                        proxy_http_version 1.1;
                        proxy_set_header Connection "Upgrade";
                    }
                }
            resources:
              limits:
                memory: 512Mi
              requests:
                cpu: 200m
                memory: 256Mi
            ingress:
              enabled: true
              port: 8080
      - internal:
          spec:
            name: car-search
            artifact:
              image:
                repository: it-cars/car-search
                tag: latest
            envs:
              JAVA_OPTS: "-Dconfig.file=conf/docker.conf
                    -DDOCKER_VSUPAPI_HOST=vehicle-supply-api
                    -DDOCKER_VSUPAPI_PORT=8090
                    -DDOCKER_CDB_HOST=cdb
                    -DDOCKER_CDB_PORT=1433
                    -Dhttp.nonProxyHosts=localhost|127.0.0.1
                    -Dappname=car-search -Dlog.dir=/opt/car-search/car-log
                    -Dlogback.configurationFile=/opt/car-search/conf/logback.xml -Xms4g -Xmx4g -server
                    -Dcom.sun.management.jmxremote.port=3333 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false"
            ports:
              - number: 8084
                protocol: http
            ingress:
              enabled: true
              port: 8084
            resources:
              limits:
                cpu: "2"
                memory: 4Gi
              requests:
                cpu: "1"
                memory: 2Gi
            healthCheck:
              startup:
                httpGet:
                  port: 8084
                  path: /ag-http-server/status
                initialDelaySeconds: 30
                timeoutSeconds: 1
                periodSeconds: 5
                failureThreshold: 40
                successThreshold: 1
              readiness:
                httpGet:
                  port: 8084
                  path: /ag-http-server/status
                initialDelaySeconds: 5
                timeoutSeconds: 1
                periodSeconds: 5
                failureThreshold: 3
                successThreshold: 1
              liveness:
                httpGet:
                  port: 8084
                  path: /ag-http-server/version
                initialDelaySeconds: 5
                timeoutSeconds: 1
                periodSeconds: 5
                failureThreshold: 10
                successThreshold: 1
          dependencies:
            - internal:
                spec:
                  name: vehicle-supply-api
                  artifact:
                    image:
                      repository: it-cars/vehicle-supply-api
                      tag: latest
                      build:
                        dockerfile: ./search-server/Dockerfile
                  command:
                    - /bin/sh
                    - -c
                    - |
                      syncdb toserver -f / -d all -s cdb -t cdb -p 1433 -u sa -w agoda123* -c apply -e aiab
                      ./entrypoint.sh
                  resources:
                    requests:
                      cpu: "2"
                      memory: 4Gi
                    limits:
                      cpu: "2"
                      memory: 4Gi
                  healthCheck:
                    startup:
                      tcpSocket:
                        port: 8090
                      initialDelaySeconds: 30
                      timeoutSeconds: 1
                      periodSeconds: 5
                      failureThreshold: 40
                      successThreshold: 1
                    readiness:
                      tcpSocket:
                        port: 8090
                      initialDelaySeconds: 5
                      timeoutSeconds: 1
                      periodSeconds: 5
                      failureThreshold: 3
                      successThreshold: 1
                    liveness:
                      tcpSocket:
                        port: 8090
                      initialDelaySeconds: 5
                      timeoutSeconds: 1
                      periodSeconds: 5
                      failureThreshold: 10
                      successThreshold: 1
                  envs:
                    DOCKER_CDB_HOST: cdb
                    DOCKER_CDB_PORT: "1433"
                    DOCKER_CDB_USERNAME: sa
                    DOCKER_CDB_PASSWORD: agoda123* # pragma: allowlist secret
                    MAX_MEMORY: "1000"
                    JAVA_OPTS: -Dconfig.file=conf/docker.conf
                  ports:
                    - number: 8090
                      protocol: GRPC
                  ingress:
                    enabled: true
                    port: 8090
                    protocol: tcp
      - internal:
          dependencies:
            - internal:
                spec:
                  name: activity-supply
                  ports:
                    - number: 8090
                      protocol: grpc
                  artifact:
                    image:
                      repository: it-activities/activity-supply
                      tag: latest
                  healthCheck:
                    readiness:
                      tcpSocket:
                        port: 8090
                      initialDelaySeconds: 5
                      failureThreshold: 40
                    startup:
                      tcpSocket:
                        port: 8090
                      initialDelaySeconds: 5
                      failureThreshold: 40
                  envs:
                    JAVA_OPTS: >
                      -Dconfig.file=conf/docker.conf -Dappname=activity-supply
                      -Dlog.dir=/opt/activity-supply/logs
                              -Dlogback.configurationFile=conf/logback.xml -Xms3g -Xmx3g -server -Dcom.sun.management.jmxremote.port=14000 
                              -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false
                              -DDOCKER_CDB_HOST=cdb
                              -DDOCKER_CDB_PORT=1433
                    LISTEN_PORT: "8090"
                    OTEL_EXPORTER_OTLP_ENDPOINT: https://otlp.qa.agoda.is
                    OTEL_EXPORTER_OTLP_PROTOCOL: http/protobuf
                    OTEL_INSTRUMENTATION_GRPC_ENABLED: "false"
                    OTEL_TRACES_SAMPLER: always_on
                    PROXY_URL: activity-supply.privatecloud.qa.agoda.is
                  resources:
                    limits:
                      cpu: "1"
                      memory: 4Gi
                    requests:
                      cpu: 500m
                      memory: 4Gi
                  consul:
                    enabled: true
                  vault:
                    enabled: true
                  ingress:
                    enabled: true
                    port: 8090
          spec:
            name: activity-search
            ports:
              - number: 8084
                protocol: TCP
            artifact:
              image:
                repository: it-activities/activity-search
                tag: latest
            healthCheck:
              readiness:
                httpGet:
                  path: /ag-http-server/status
                  port: 8084
                initialDelaySeconds: 5
                failureThreshold: 40
              startup:
                httpGet:
                  path: /ag-http-server/status
                  port: 8084
                initialDelaySeconds: 5
                failureThreshold: 40
            envs:
              JARVIS_HOST: http://jarvis
              JAVA_OPTS: >
                -Dconfig.file=conf/docker.conf
                      -DDOCKER_ASUPAPI_HOST=activity-supply
                      -DDOCKER_ASUPAPI_PORT=8090
                      -DDOCKER_CDB_HOST=cdb
                      -DDOCKER_CDB_PORT=1433
                      -DELAPI_HOST=wl-loyalty-elapi.aiab.qa.agoda.is
                      -DELAPI_PORT=80
                      -Dappname=activity-search -Dlog.dir=/opt/activity-search/activity-log
                      -Dlogback.configurationFile=/opt/activity-search/conf/logback.xml -Xms3g -Xmx3g -server
                      -Dcom.sun.management.jmxremote.port=3333 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false"
              OTEL_EXPORTER_OTLP_ENDPOINT: https://otlp.qa.agoda.is
              OTEL_EXPORTER_OTLP_PROTOCOL: http/protobuf
              OTEL_INSTRUMENTATION_GRPC_ENABLED: "false"
              OTEL_TRACES_SAMPLER: always_on
            resources:
              limits:
                cpu: "1"
                memory: 4Gi
              requests:
                cpu: 500m
                memory: 4Gi
            consul:
              enabled: true
            vault:
              enabled: true
            ingress:
              enabled: true
              port: 8084
            waitFor:
              services:
                - name: activity-supply
                  port: 8090
                - name: cdb
                  port: 1433
