include "akka.conf"
include "bapi-prod-common.conf"
include "whitelabel-prod.conf"
include "cusco.conf"
include "cryptography.conf"
include "consul.conf"
include "certificate.conf"
include "encryption.conf"

//----------------- non pci common config -----------------
ag-observability {
  service-name = "booking-creation"
  metrics.adp.token = "booking-creation"
  metrics.prefix = "booking-creation"
  tracing.enabled = true
  tracing.sampling.rate = 1 # sample 5% of traffic.
}

sitesettings {
  port = 80
  interface = "0.0.0.0"
  apiShutdownTimeout = 1
  https {
    force = true
    port = 443
  }
  isAuthen = true
  bookingDetailsLimit = 40
  bookingDetailsHostLimit = 40
  bookingListForRankingLimit = 1000
  bookingListByCXLDueDateLimit = 10
  bookingListByCXLDueDateExpireTimeSeconds = 2
  listForAffiliateV2 = 1000
  bookingsForCMLimit = 50
  getMetaDataByExternalBookingIdTimeoutSeconds = 120
}

replicateExistingBookingConfig {
  itineraryIdLimit = 15
}

flightSendMessageConfig {
  itineraryIdLimit = 15
}

multiProductReplication {
  logMismatch = false
}

cache {
  cms {
    maxCapacity = 500
    initialCapacity = 16
    timeToLive = 1 hour
  }
}

hikari {
  retry-count = 2
}

bookingcentraldc {
  getRawBookingsLimit = 1000
  getBookingsCxlDueDateLimit = 1000
  getBookingsCxlDueDateQueryTimeout = 30
  getBookingsCxlDueDateTimeoutSeconds = 180
  getBookingsCxlDueDateLowerBoundOffset = 365
  getBookingIdsTimeout = 30
  getAcknowledgeBookingIdsTimeout = 30
  getAcknowledgeBookingIdsPageSize = 1000
  bookingAcknowledgeListLimit = 1000
  refreshOnCMBookingIdLimit = 5000
  getBAPIUserId = "BD811DA2-B3D0-470E-B7CD-F15B8A460EB0"
}

akka.ssl-config {
  default = false
  enabledProtocols = ["TLSv1.2"]
  enabledCipherSuites = [
    "TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256",
    "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384",
    "TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA",
    "TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA",
    "TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256",
    "TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384",
    "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256",
    "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
    "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA",
    "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA",
    "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256",
    "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384",
    "TLS_DHE_RSA_WITH_AES_128_GCM_SHA256",
    "TLS_DHE_RSA_WITH_AES_256_GCM_SHA384",
    "TLS_DHE_RSA_WITH_AES_128_CBC_SHA",
    "TLS_DHE_RSA_WITH_AES_256_CBC_SHA",
    "TLS_DHE_RSA_WITH_AES_128_CBC_SHA256",
    "TLS_DHE_RSA_WITH_AES_256_CBC_SHA256",
    "ECDHE-ECDSA-AES128-GCM-SHA256",
    "ECDHE-ECDSA-AES256-GCM-SHA384",
    "ECDHE-ECDSA-AES128-SHA",
    "ECDHE-ECDSA-AES256-SHA",
    "ECDHE-ECDSA-AES128-SHA256",
    "ECDHE-ECDSA-AES256-SHA384",
    "ECDHE-RSA-AES128-GCM-SHA256",
    "ECDHE-RSA-AES256-GCM-SHA384",
    "ECDHE-RSA-AES128-SHA",
    "ECDHE-RSA-AES256-SHA",
    "ECDHE-RSA-AES128-SHA256",
    "ECDHE-RSA-AES256-SHA384",
    "DHE-RSA-AES128-GCM-SHA256",
    "DHE-RSA-AES256-GCM-SHA384",
    "DHE-RSA-AES128-SHA",
    "DHE-RSA-AES256-SHA",
    "DHE-RSA-AES128-SHA256",
    "DHE-RSA-AES256-SHA256"
  ]
}

deliveryApi {
  client-id = 5041
  sms-template-id = 534
}

domains.website = "https://www.agoda.com"

fraud-api-client {
  endpoint = ${conf.fraud-api-client.endpoint}
  authToken = ${conf.fraud-api-client.authToken}
  timeout = 2500
}

agoda {
  domainUrl = ${conf.agoda.domainUrl}
  selfServiceUrls {
    Hotel: "/account/editbooking.html",
    PriceFreeze: "/account/pfz/editbooking.html"
  }
  bapiIdentifierUuid = ${conf.agoda.bapiIdentifierUuid}
  creation {
    autoAmendmentUserId = ${conf.agoda.creation.autoAmendmentUserId}
    hasBookingSummaryItineraryIndex = ${conf.agoda.creation.hasBookingSummaryItineraryIndex}
    propertyBooking {
      earnGiftCard {
        defaultOffsetDay = 5
      }
      fraud {
        secondFraudCheckOffsetDay = 2
      }
    }

    duplicate {
      checkDaysAgo = 7
      salt = ${conf.agoda.creation.duplicate.salt}
    }
    paymentApi {
      clientId = 17
      apiKey = ${conf.agoda.creation.paymentApi.apiKey}
    }
    flightPaymentApi {
      1 = {
        clientId = 66
        apiKey = ${conf.agoda.creation.flightPaymentApi.1.apiKey}
      }
      5 = {
        clientId = 91
        apiKey = ${conf.agoda.creation.flightPaymentApi.5.apiKey}
      }
    }
    vehiclePaymentApi {
      whitelabelIds = [6, 7, 22]
    }
    validation {
      enableDebitCardOnBNPL = true
      dayALlowBNPL = 1
      SettleDays = 4
      BNPCSettleDays = 5
      minBookingAmount {
        usd = 2
        jpy = 1
      }
      priceToleranceInUSD = 0.1
    }
    creditCardApi {
      apiKey = ${conf.agoda.creation.creditCardApi.apiKey}
      clientId = "17"
      subSystemId = 1
      secureKeyId = 1006
      storageExpirationTimeInHours = 23
      publicKeyId = 17
      publicKey = ${conf.agoda.creation.creditCardApi.publicKey}
    }
    mdb {
      insertDuplicatedCandidateTimeoutSec = 1
      insertDuplicatedCandidateForcedTimeoutMSec = 1500
      checkDuplicatedCandidateForcedTimeoutMSec = 1000
    }
  }
}

japanese-campaign {
  campaignIds = [
    482880,
    482881,
    482885,
    482886,
    482887,
    482888,
    482889,
    482890,
    482891,
    482892,
    482893,
    482894,
    482895,
    482896,
    482897
  ]
}

ebeclients {
  akka.http {
    host-connection-pool {
      max-retries = 1
    }
  }
}

ebehealthchecks {
  akka.http {
    host-connection-pool {
      max-retries = 1
    }
  }
}

emailService {
  host = ${conf.emailService.host}
  port = ${conf.emailService.port} #80
  sendVOC = "/email/emailservice.asmx"
}

sendHotelVoucher {
  rabbitMq {
    topic = "EBE.Email.SendEmail.Federated"
    messageType = "Agoda.EBE.Agents.Email.SendEmail.Object.SendEmailMessage:Agoda.EBE.Agents.Email"
  }
  email {
    workflowActionId = 8
    moduleId = 2
    templateId = 88
    priority = 1
    emailFormat = 2
  }
}

ag-http-client.client {
  services {
    // abs-service
    abs-client {
      //abs server side timeout is 120 seconds, setting 121 to give 1 extra second to recieve timeout on http
      individual-timeout = 121 second
    }
    car-search-client {
      individual-timeout = 30 seconds
    }
    ancillary-api {
      individual-timeout = 5 seconds
    }
    flight-api {
      individual-timeout = 10 seconds
    }
    gateway-api {
      individual-timeout = 10 seconds
    }
    payment-non-pci-api {
      individual-timeout = 10 seconds
    }
  }
}

booking-creation-client {
  clientId = 17
  apiKey = ${conf.booking-creation-client.apiKey}
  localDcs = ${conf.booking-creation-client.localDcs}
  request-timeout = 10 s
  connect-timeout = 3 s
  max-retries = 0
  max-connections-per-host = 4
  useHttps = true
  useHttps = ${?conf.booking-creation-client.useHttps}
}

priceTolerance {
  maxUpperPercentage = 20
  maxLowerPercentage = -20
}

//----------------- non-pci dc specific config -----------------
papi {
  defaultConnectionTimeout = 5000
  clientsettings {
    client.max-connections = 8
    client.min-connections = 1
    client.max-retries = 3
    client.max-open-requests = 40
    client.pipelining-limit = 8
    client.idle-timeout = 30s
    httpclient.request-timeout = 5100
    httpclient.bufferSize = 200
  }
}

ag-http-client {
  mesh {
    default {}
    services {
      papi-search {
        discovery {
          method = "consul"
          consul {
            service = "papi-search"
            tags = ["fe"]
            initial-discovery-timeout = 1 minute
            initial-hosts = []
          }
        }

        round-robin {
          method = "simple"
        }

        request {
          max-attempts = 1
        }
      }

      car-search-client {
        discovery {
          method = "static"
          static {
            hosts = ${conf.vehicle-search-api-config.servers}
          }
        }
      }

      enigma {
        round-robin {
          method = "weighted"
          weighted {
            total-weight = 100
            decrement = 10
            increment = 10
          }
        }
        discovery {
          method = "static"
          static {
            hosts = ["qa-master.enigma.svc.cluster.local"]
          }
        }
      }
    }
  }
}

abs {
  root {
    host = ${conf.abs.root.host}
    port = ${conf.abs.root.port} #9002
    isHttps = ${conf.abs.root.isHttps}
    consul-setting = {
      use-service-discovery = false
      discovery-settings {
        service-name = "abs-phoenix"
        tags = ["abs-phoenix", "live"]
      }
    }
  }
  default-timeout {
    millis = 7000
  }
  booking-timeout {
    millis = 120000
  }
  cancel-timeout {
    millis = 120000
  }
  amendment-timeout {
    millis = 80000
  }
  port = ${conf.abs.port} #9002
  clientsettings {
    max-retries = 0
  }
  bufferTime = 5s
  supplierNotSupportInstantBook = [28047, 28055]
}

vehicle-search-api-config {
  retries = 5,
  timeout = 30 seconds,
  backOffInterval = 5 seconds,
}

ancillary-api-pc-config {
  maxRetries = 0,
  backOffMillis = 300,
  readTimeout = 5s,
  endPoints = ${conf.ancillary-api-pc-config.endPoints}
}

deliveryClient {
  name = "DeliveryClient"
  conn-timeout = 1000
  read-timeout = 4000
  retries = 2
  enabled = true
  failure-count = 3
  disable-duration = 15 seconds
  urls = ${conf.deliveryClient.urls}
}

sql = ${conf.sql}

message {
  enable = true
  dataCenter = ${conf.message.dataCenter}
  rabbitmq {
    exchange = "EBE.Booking.Federated"
    routingKey = ""
    queue = "EBE.Booking.Federated"
  }
}

op-rabbit {
  topic-exchange-name = "amq.topic"
  channel-dispatcher = "op-rabbit.default-channel-dispatcher"
  default-channel-dispatcher {
    type = Dispatcher
    executor = "fork-join-executor"
    fork-join-executor {
      parallelism-min = 2
      parallelism-factor = 2.0
      parallelism-max = 4
    }
    throughput = 100
  }
  connection {
    virtual-host = "/"
    hosts = ${conf.op-rabbit.connection.hosts}
    username = ${conf.op-rabbit.connection.username}
    password = ${conf.op-rabbit.connection.password}
    port = ${conf.op-rabbit.connection.port} #5672
    ssl = false
    connection-timeout = 2s
  }
}

experimentsPlatform {
  experiments-service {
    timeout = 5000
    host = ${conf.experimentsPlatform.experiments-service.host}
  }
  applyZForDefaultUser = ""
  applicationName = "booking-creation"
  messagingAPIToken = "bapi"
  clientId = 19
}

customerApi {
  url = ${conf.customerApi.url},
  urlv2 = ${conf.customerApi.urlv2},
  urlv2Authed = ${conf.customerApi.urlv2Authed},
  urlv2Base = ${conf.customerApi.urlv2Base}
  clientApp = "bapi",
  timeout = 500
}

enigma {
  client {
    api-key = ${conf.enigma.client.api-key}
    timeout = "9 sec"
    retry-limit = 1
    log-level = "error"
    searchLimit = 1250
  }
}

tprm {
  url = ${conf.tprm.url},
  timeout = 600,
  clientApp = "bapi"
}

wl-client {
  hostsSettings = ${conf.wl-client.hostsSettings}
}


flights-api-config {
  timeout = 10000
}


gandalf-api-config {
  url = ${conf.gandalf-api-config.url},
  timeoutInMs = 900,
  retryCount = 2
}


bookingConsentConfig {
  bookingConsentItems = [
    {
      whitelabelIds = [1]
      origins = []
      cids = [1891504, 1891677, 1891678, 1891679, 1891680, 1891681, 1891682, 1891683, 1896369, 1896370, 1896371, 1896372, 1913762, 1913763, 1913764, 1913765, 1915237, 1915238]
      isRequired = true
      consentCmsId = 123826
      validationCmsId = 123705
      consentIdentifier = 1
    },
    {
      whitelabelIds = [1]
      origins = ["CN"]
      cids = []
      isRequired = true
      consentCmsId = 137158
      validationCmsId = 137162
      consentIdentifier = 2
    },
    {
      whitelabelIds = [1]
      origins = ["CN"]
      cids = []
      isRequired = true
      consentCmsId = 137159
      validationCmsId = 137162
      consentIdentifier = 3
    },
    {
      whitelabelIds = [1]
      origins = ["CN"]
      cids = []
      isRequired = true
      consentCmsId = 137160
      validationCmsId = 137162
      consentIdentifier = 4
    },
    {
      whitelabelIds = [1]
      origins = ["CN"]
      cids = []
      isRequired = true
      consentCmsId = 137161
      validationCmsId = 137162
      consentIdentifier = 5
    },
    {
      whitelabelIds = [1]
      origins = ["KR"]
      cids = []
      isRequired = true
      consentCmsId = 131557
      validationCmsId = 130070
      consentIdentifier = 6
    },
    {
      whitelabelIds = [1]
      origins = ["KR"]
      cids = []
      isRequired = true
      consentCmsId = 131558
      validationCmsId = 130070
      consentIdentifier = 7
    },
    {
      whitelabelIds = [1]
      origins = ["KR"]
      cids = []
      isRequired = true
      consentCmsId = 131559
      validationCmsId = 130070
      consentIdentifier = 8
    }
  ]
}

ag-logging.metrics {
  enabled = true
  application = "booking-creation"
  messaging-api-token = "bapi"
  send-async = true
  prefix = "bcre"
}

hadoop {
  enabled = true
  messagingAPIToken = "bapi"
  sendAsync = true
}

ag-grpc.server {
  service-name = "booking-creation-grpc"
  discovery {
    //  Consul discovery settings
    enabled = true
  }
  endpoint {
    //    max-inbound-message-size is not set by default meaning there is no limit
    //    Example of setting to 10M:
    maxInboundMessageSize = 10485760
    port = 50051
  }
}

activity-search-api-config {
  retries = 5,
  timeout = 30 seconds
  backOffInterval = 5 seconds
}

ag-http-client.mesh {
  default {}
  services {
    activity-search-client {
      discovery {
        method = "consul"
        consul {
          service = "activity-search"
          tags = ${conf.activity-search-client.discovery.consul.tags}
          initial-discovery-timeout = 1 minute
          initial-hosts = ${conf.activity-search-client.discovery.consul.initial-hosts}
        }
      }
    }

    whitelabel_loyalty_api {
      discovery {
        method = "consul"
        consul {
          service = "whitelabel_loyalty_api"
          tags = ${conf.whitelabel_loyalty_api.discovery.consul.tags}
          initial-discovery-timeout = 1 minute
          initial-hosts = ${conf.whitelabel_loyalty_api.discovery.consul.initial-hosts}
        }
      }
      round-robin {
        method = "simple"
      }
      request {
        max-attempts = 3
      }
    }
    ceg-wl-api-client {
      discovery {
        method = "static"
        static.hosts = ["prod.cegwl.svc.cluster.local:8080"]
      }
      request {
        maxAttempts = 3
      }
    }

    // creditcard api client
    creditcard-api-local-client {
      discovery {
        method = "static"
        static {
          hosts = [${conf.agoda.creation.creditCardApiV2.localEndpoint}]
        }
      }
      request {
        max-attempts = 1
      }
    }
    creditcard-api-aab-client {
      discovery {
        method = "static"
        static {
          hosts = [${conf.agoda.creation.creditCardApiV2.localEndpoint}]
        }
      }
      request {
        max-attempts = 1
      }
    }
    creditcard-nonpci-api-local-client {
      discovery {
        method = "static"
        static {
          hosts = [${conf.agoda.creation.creditCardApiV2.localEndpoint}]
        }
      }
      request {
        max-attempts = 1
      }
    }

    // ancillary-api
    ancillary-api {
      discovery {
        method = "static"
        static {
          hosts = [${?conf.ancillary-api.endpoint}]
        }
      }
      request {
        max-attempts = 1 // no retry
      }
    }

    flight-api {
      discovery {
        method = "static"
        static {
          hosts = ${?conf.flights-api-config.endPoints}
        }
      }
      request {
        max-attempts = 1
      }
    }

    // payment-api
    gateway-api {
      discovery {
        method = "static"
        static {
          hosts = [${?conf.payment-api-v2.endpoint}]
        }
      }
      request {
        max-attempts = 1 // no retry
      }
    }

    payment-non-pci-api {
      discovery {
        method = "static"
        static {
          hosts = [${?conf.payment-api-v2.endpoint}]
        }
      }
      request {
        max-attempts = 1 // no retry
      }
    }

  }
}

ag-consul {
  cache-dir = "/opt/consul_backup/" //see init.pp
}

external-loyalty-client {
  serviceName: "whitelabel_loyalty_api",
  requestTimeout: 8000,
  maxRetries: 3,
  apiKey: ${conf.elapiClient.apiKey}
}


customerMarketingDisplaySettingsConfig {
  excludeOrigins = ["AT", "BE", "BG", "CY", "CZ", "DE", "DK", "EE", "FI", "FR", "GR", "HR", "HU", "IE", "IT", "LT", "LU", "LV", "MT", "NL", "PL", "PT", "RO", "SE", "SI", "SK", "NZ", "AU", "US", "KR", "CN", "SG", "GB", "BR"]
  disclaimerCmsId = 153944
}

capi {
  loyalty-profile {
    max-retries: 3
  }
  exchange-claim {
    max-retries: 3
  }
}

ag-grpc.client {
  default {
    retry.method = "none"
    channel.max-inbound-message-size = 10485760
  }
  services {
    fraud-api {
      apiKey = ${conf.fraud-api.apiKey}
      default-deadline = 2.5 seconds
      discovery {
        method = "static"
        static {
          hosts = [${?conf.fraud-api.endpoint}]
          authority = ${?conf.fraud-api.endpoint}
        }
      }
      retry {
        method = "none"
      }
    }
  }
}

sql-execution {
  maxRetries: 3
}

ceg-wl-api-config {
  apiKey = ${conf.ceg-wl-api-config.apiKey},
  retries = 3,
  timeout = 5 seconds
}

