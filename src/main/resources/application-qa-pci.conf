include "common-app-pci.conf"
include "vault-api-keys.conf"
include "rabbitmq.conf"

security.enabledModules = [
  "create",
  "flightpostbooking",
  "amendment",
  "creditcard",
  "piipci"
]

// common

conf.fraud-api-client.endpoint = "http://general-fraud-service-qa-local.privatecloud.qa.agoda.is"
conf.fraud-api.endpoint = "fraud-api-qa.privatecloud.qa.agoda.is:50051" // New fraud-api

conf.agoda.domainUrl = "https://master.qa.www.agoda.com"
conf.agoda.bapiIdentifierUuid = "BD811DA2-B3D0-470E-B7CD-F15B8A460EB0"
conf.agoda.creation.autoAmendmentUserId = "7580C3D7-B1BC-4FC8-9DA7-1274E8739F46"


conf.emailService.host = "qa.central.webservices.agoda.local"
conf.emailService.port = 80

conf.booking-creation-client.localDcs = [
  "bk"
]

// non-pci
ag-http-client.mesh.services {
  papi-search.discovery {
    method = "static"
    static {
      hosts = ["propertyapisearchdocker-qa.privatecloud.qa.agoda.is"]
    }
  }
}

conf.abs.root.host = [
  "mock-api.qa.agoda.is"
]
conf.abs.root.port = 80
conf.abs.root.isHttps = false
conf.abs.port = ""

conf.agoda.creation.creditCardApiV2.localEndpoint = "local-qa.ebe-creditcard-service-netcore.svc:5000"

conf.agoda.creation.hasBookingSummaryItineraryIndex = true

conf.vehicle-search-api-config.servers = ["car-search-qa.privatecloud.qa.agoda.is:80"]

conf.ancillary-api-pc-config.endPoints = [
  "https://ancillary-api-qa.qa.agoda.is"
]
conf.ancillary-api.endpoint = "ancillary-api-qa.qa.agoda.is"

conf.payment-api-v2.endpoint = "qa-pay-el2.agoda.local:80"

conf.deliveryClient.urls = [
  "http://delivery-bkk:51841/"
]

conf.message.dataCenter = "BK"

conf.customerApi.url = "http://customerapi-qa.privatecloud.qa.agoda.is/V1/RewardsApiService"
conf.customerApi.urlv2 = "http://customerapi-qa.privatecloud.qa.agoda.is/v2/transit"
conf.customerApi.urlv2Authed = "http://customerapi-qa.privatecloud.qa.agoda.is/v2/authed"
conf.customerApi.urlv2Base = "http://customerapi-qa.privatecloud.qa.agoda.is/v2"

conf.tprm.url = "http://customerapi-qa.privatecloud.qa.agoda.is"

conf.wl-client.hostsSettings = [
  "http://whitelabel-service-api-qa.privatecloud.qa.agoda.is"
]

conf.flights-api-config.endPoints = [
  "flights-api-qa.privatecloud.qa.agoda.is:80"
]

conf.gandalf-api-config.url = "https://promo-api-qa.qa.agoda.is"

sql = [
  {
    group = "bapi"
    hikari {
      databases = [
        {
          id = "bk-qacdb-1000"
          url = "**********************************************************************************************************************************;"
          include "database-common.conf"
          include "database-vault-cdb_bapi_ro-credentials.conf"
        },
        {
          id = "bk-qacdb-1001"
          url = "**********************************************************************************************************************************;"
          include "database-common.conf"
          include "database-vault-cdb_bapi_ro-credentials.conf"
        }
      ],
      retry-count = 2
    }
  },
  {
    group = "bcre_bfdb"
    hikari {
      databases = [
        {
          id = "qabfdblistener"
          url = "**********************************************************************************************************************************************************************;"
          connectionTimeout = 60000 milliseconds
          connectionTestQuery = "SELECT 1"
          maximumPoolSize = 20
          include "database-vault-bfdb_bcre_rw-credentials.conf"
        }
      ]
    }
  },
  {
    group = "bcre_mdb"
    hikari {
      databases = [
        {
          id = "HK-QAMDB-2006"
          url = "**************************************************************************************************************************************************;"
          connectionTimeout = 60000 milliseconds
          connectionTestQuery = "SELECT 1"
          maximumPoolSize = 10
          include "database-vault-mdb_bcre_rw-credentials.conf"
        }
      ]
    }
  },
  {
    group = "bcre_bkgdb"
    hikari {
      databases = [
        {
          id = "bcre_bkgdb"
          url = "*************************************************************************************************************************************************;"
          include "database-common-bkgdb-pci.conf"
          include "database-vault-bkgdb-credentials.conf"
        }
      ]
    }
  },
  {
    group = "bcre_bapidb"
    hikari {
      databases = [
        {
          id = "bcre_bapidb"
          url = "*******************************************************************************************************************************************************;"
          include "database-common-bapidb.conf"
          include "database-vault-bapidb-bcre-credentials.conf"
        }
      ]
    }
  }
]
// ad hoc config for dev/sbx only
akka.loglevel = "DEBUG"
agoda.creation.creditCardApi.secureKeyId = 17
agoda.creation.authRecurRequired = [
  {
    destinationCountries = [
      153
    ]
    excludeOrigins = [
      130,
      118,
      13,
      158,
      99,
      28,
      153,
      101,
      197,
      37,
      54,
      205,
      145,
      187,
      157,
      7,
      57,
      149,
      129,
      182,
      213,
      125,
      27,
      167,
      49,
      51
    ]
  },
  {
    destinationCountries = [
      3 // For QA testing only the actual value is 153
    ]
    excludeOrigins = [
      130,
      118,
      13,
      158,
      99,
      28,
      153,
      101,
      197,
      37,
      54,
      205,
      145,
      187,
      157,
      7,
      57,
      149,
      129,
      182,
      213,
      125,
      27,
      167,
      49,
      51
    ]
  },
  {
    includeOrigins = [19, 35, 117] // Blacklisted countries
  }
]
agoda.domainUrl = "https://master.qa.www.agoda.com"

bk.bcre.local.servers = ["hk-qabcre-2c01:8443", "hk-qabcre-2c01.agoda.local:8443"]
hk.bcre.local.servers = ["hk-qabcre-2c01:8443", "hk-qabcre-2c01.agoda.local:8443"]

consul-client.servers.default = ["http://bkconsul.agoda.local:8500"]

customerApi.timeout = 3000

deliveryApi.contact-us-email-template-id = 4212
deliveryApi.ledger-report-email-template-id = 4254
deliveryApi.receipt-email-template-id = 4257
deliveryApi.sms-template-id = 3997

domains.website = "https://master.qa.www.agoda.com"

japanese-campaign.campaignIds = [
  1698845,
  1698840,
  1698860
]

papi.clientsettings.httpclient.request-timeout = 3000

sitesettings.bookingAcknowledgeListLimit = 1000
sitesettings.bookingDetailsHostLimit = 20
sitesettings.bookingDetailsLimit = 15
sitesettings.https.force = false
sitesettings.https.port = 8443
sitesettings.isAuthen = false
sitesettings.listForAffiliateV2 = 500
sitesettings.listForHostDbTimeout = 25
sitesettings.port = 8080

whitelabel.canonicalUrls = {
  Japanican: "https://master.qa.notyja.com",
  Rurubu: "https://master.qa.notyru.com"
}

ag-http-client.client.services.car-search-client.individual-timeout = 30 seconds

ag-consul.token = "8be20753-633b-794d-4ef9-e5d9098f0fd1"

# activity search private cloud
conf.activity-search-client.discovery.consul.initial-hosts = []
conf.activity-search-client.discovery.consul.tags = ["qa-/activity-search"]

conf.whitelabel_loyalty_api.discovery.consul.initial-hosts = ["whitelabel-loyalty-api-qa.privatecloud.qa.agoda.is"]
conf.whitelabel_loyalty_api.discovery.consul.tags = ["qa-/whitelabel_loyalty_api"]

abs.root.consul-setting.discovery-settings.tags = ["abs-phoenix", "qa"]

#Vault Transit
ag-vault.http.url = "https://hk.qa-vault.agoda.local:8200"

conf.experimentsPlatform.experiments-service.host = "https://experiments-service.qa.agoda.is"