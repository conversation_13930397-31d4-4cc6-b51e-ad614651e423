include "common-app.conf"

agoda {
  creation {
    authRecurRequired = [
      {
        destinationCountries = [
          153
        ]
        excludeOrigins = [
          130,
          118,
          13,
          158,
          99,
          28,
          153,
          101,
          197,
          37,
          54,
          205,
          145,
          187,
          157,
          7,
          57,
          149,
          129,
          182,
          213,
          125,
          27,
          167,
          49,
          51
        ]
      },
      {
        includeOrigins = [19, 35, 117] // Blacklisted countries
      },
      {
        includeHotels = [
          624306,
          251643,
          6412758,
          294897,
          263325,
          1986334,
          1315492,
          303390,
          1030811,
          929139,
          930782,
          487255,
          567154,
          2062002,
          1110316,
          647605,
          490036,
          174434,
          4878677,
          2282287,
          255893,
          2817624,
          71953,
          240077,
          557928,
          570303,
          3164,
          2320629,
          2320628,
          503166,
          462004,
          376399,
          1845958,
          237749,
          10325,
          788342,
          1061630,
          818809,
          1030641,
          544190,
          10351,
          337808,
          161700,
          45559,
          240606,
          1166228,
          665674,
          8493906
        ]
      }
    ]
  }
}

abs.root.consul-setting.discovery-settings.tags = ["abs-phoenix", "live-pci"]
