include "common-app.conf"

security.enabledModules = [
  "precheck",
  "booking",
  "bookingcentraldc",
  "initializebooking",
  "updatebooking",
  "create",
  "flightpostbooking",
  "setupbooking",
  "resellbooking",
  "debug",
  "amendment",
  "piipci",
  "creditcard"
]

# Docker parameters
# conf.op-rabbit.connection.username = "eberabbit"
# conf.op-rabbit.connection.password = "ebe!123"
# conf.enigma.client.api-key = "ts9MXSvXTR83CykBlKqhJPAt7Yf7x9YF"
conf.abs.root.host = [${conf.abs.root.host.url}]
# conf.abs.root.port = 9002
# conf.abs.root.isHttps = false
conf.abs.port = ""
conf.agoda.creation.creditCardApiV2.localEndpoint = "ebe-creditcard-service-netcore-local-qa.privatecloud.qa.agoda.is:80"
conf.agoda.creation.creditCardApiV2.localEndpoint = ${?conf.agoda.creation.creditCardApiV2.localEndpointDevstack}

conf.vehicle-search-api-config.servers = [${?conf.vehicle-search-api-config.server}]
conf.ancillary-api-pc-config.endPoints = [${?conf.ancillary-api-pc-config.endPoints.url}]
conf.ancillary-api.endpoint = "main.ancillary-api.svc.cluster.local:8080"
conf.ancillary-api.endpoint = ${?ancillaryV2_url}
conf.payment-api-v2.endpoint = "qa-pay-el2.agoda.local:80"
conf.payment-api-v2.endpoint = ${?conf.payment-api-v2.endpoint.url}
conf.flight-api.endpoint = ["flights-api-qa.privatecloud.qa.agoda.is:80"]
conf.flight-api.endpoint = [${?conf.flights-api-config.endPoints.url}]
conf.deliveryClient.urls = [${?conf.deliveryClient.urls.url}]
conf.op-rabbit.connection.hosts = [${?conf.op-rabbit.connection.hosts.url}]
# conf.op-rabbit.connection.port = 5672
# conf.paymentapi-v2-client.endpoint = "http://local-qa.ebe-payment-api-netcore.svc.cluster.local:5000/paymentapi/v2"
# conf.customerApi.url = "http://qa.customerapi.svc.cluster.local/V1/RewardsApiService"
# conf.customerApi.urlv2 = "http://qa.customerapi.svc.cluster.local/v2/transit"
# conf.customerApi.urlv2Authed = "http://qa.customerapi.svc.cluster.local/v2/authed"
# conf.customerApi.urlv2Base = "http://qa.customerapi.svc.cluster.local/v2"
# conf.tprm.url = "http://qa.customerapi.svc.cluster.local"
conf.flights-api-config.endPoints = [${?conf.flights-api-config.endPoints.url}]
conf.flights-api-config.tags = ["qa-/flights-api"] // not existed tag to prevent discovery
# conf.fraud-api-client.endpoint = "http://qa-local-fraud.agoda.local"
conf.fraud-api.endpoint = "fraud-api-qa.privatecloud.qa.agoda.is:50051"
conf.fraud-api.endpoint = ${?conf.fraud-api.endpoint}
consul-client.servers.default = [${?conf.consul.server}]
ag-consul.servers = [${?conf.consul.server}]

# conf.cdb.url
# conf.cdb.username
# conf.cdb.password
# conf.bfdb.url
# conf.bfdb.username
# conf.bfdb.password
# conf.bfdb-bf.url
# conf.bfdb-bf.username
# conf.bfdb-bf.password
# conf.bfdb-ebe.url
# conf.bfdb-ebe.username
# conf.bfdb-ebe.password
# conf.bapidb.url
# conf.bapidb.username
# conf.bapidb.password
# conf.mdb.url
# conf.mdb.username
# conf.mdb.password
# conf.bkgdb.url
# conf.bkgdb.username
# conf.bkgdb.password
# conf.bapidb.url
# conf.bapidb.username
# conf.bapidb.password

// common
conf.ebe.header.apiKey = "k9vO1JAXDxH3GA0lV3m0DZehA7PJto4CzSf29D2DL1NptjtDuP"

conf.fraud-api-client.authToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZWFtX25hbWUiOiJNUEJFLWJvb2tpbmctY3JlYXRpb24iLCJ1c2VyX2lkIjoiYmQ4MTFkYTItYjNkMC00NzBlLWI3Y2QtZjE1YjhhNDYwZWIwIiwiYXBwX25hbWUiOiJCb29raW5nLWNyZWF0aW9uIiwiaWF0IjoxNTE2MjM5MDIyfQ.ncSygqye1YQW3NeP00NKWkdYB0Bcggvj_nXM15jpHp0"

conf.fraud-api.apiKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZWFtX25hbWUiOiJNUEJFLWJvb2tpbmctY3JlYXRpb24iLCJ1c2VyX2lkIjoiYmQ4MTFkYTItYjNkMC00NzBlLWI3Y2QtZjE1YjhhNDYwZWIwIiwiYXBwX25hbWUiOiJCb29raW5nLWNyZWF0aW9uIiwiaWF0IjoxNjQ4NjE0NTY2fQ.SMV0dcGExFUpoya9l0GR65XyfSbGJQ64OjH8rNO_0Z8"  // pragma: allowlist secret

conf.agoda.domainUrl = "https://master.qa.www.agoda.com"
conf.agoda.bapiIdentifierUuid = "BD811DA2-B3D0-470E-B7CD-F15B8A460EB0"
conf.agoda.creation.autoAmendmentUserId = "7580C3D7-B1BC-4FC8-9DA7-1274E8739F46"
conf.agoda.creation.paymentApi.apiKey = "8IzVMvwWpkTcKnBKCkMBo7DdEDzfb0mrIHdTr9BXQKhCImwLs3"
conf.agoda.creation.flightPaymentApi.1.apiKey = "k3VvFEzJvZoggju7kKjFF8PG6OYyCnvVO0YUT5R8oz78L8lZPP"
conf.agoda.creation.flightPaymentApi.5.apiKey = "S2pkwkprXwbJPZpnTDyiOej7NXso0Q50puvFu6TUD8uUFefJih"
conf.agoda.creation.creditCardApi.apiKey = "Qk7X1drjc2LKecKbBcomempS2Bq2AFwtTY2yDPKzgddxoJnbWI"
conf.agoda.creation.creditCardApi.publicKey = "<RSAKeyValue><Modulus>07qeNT1+CYG92jGkQOKBsp8/uquPSbWt8xbMn9IGZLl6Sz6lhrlVP1uozMW07jmdEpINsf23mGe4KtpQ5OnwsS1fATAssv/2M2XJCm0J9iZse8CAOCbHZDt+nFdwsGD0dHsMBttQcPOb/knbXdQ69Sq1GYdit9YyE/Gv8LRe3nDpRVJksD5cEFE+96nEyfZAOw1DgV6mg9NEJeZRezEHJf1lz9N3CuJCHsuhMNiwTgcZD4eXYa0+KH6kn2aRug9EW1dRYWxJ3yOxwlclbkIP8GsG0fxhR/m5iZ5pPuKBlCyP6BcXKCDERB6/E5Udp9ANNGlyWrbNd4cFo8EQB0SKXQ==</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>"

conf.emailService.host = "qa.central.webservices.agoda.local"
conf.emailService.port = 80

conf.booking-creation-client.apiKey = "9xEdYdAbva2Hm31A5vMYysaEYY3F7ulkQdsO3M6pr9ClTl9ogL"
conf.booking-creation-client.localDcs = [
  "bk"
]

// non-pci
conf.agoda.creation.hasBookingSummaryItineraryIndex = true

conf.message.dataCenter = "BK"

conf.wl-client.hostsSettings = [
  "http://whitelabel-service-api-qa.privatecloud.qa.agoda.is"
]

conf.gandalf-api-config.url = "https://promo-api-qa.qa.agoda.is"

hikari.default {
  queryTimeout = 30
  queryRetry = 1
  awaitConnectionThreadPoolSize = 32
}

conf.sql = [
  {
    group = "bapi"
    hikari {
      databases = [
        {
          id = "docker-cdb"
          url = ${?conf.cdb.url}
          include "database-common.conf"
          username = ${?conf.cdb.username}
          password = ${?conf.cdb.password}
        }
      ],
      retry-count = 2
    }
  },
  {
    group = "bcre_bfdb"
     hikari {
        databases = [
          {
            id = "hk-qabfdb-2001"
            connectionTimeout = 60000 milliseconds
            connectionTestQuery = "SELECT 1"
            maximumPoolSize = 20
            url = ${?conf.bfdb-bcre.url}
            include "database-vault-bfdb_bcre_rw-credentials.conf"
            username = ${?conf.bfdb-bcre.username}
            password = ${?conf.bfdb-bcre.password}
          }
        ]
      }
    },
  {
    group = "bcre_mdb"
     hikari {
        databases = [
          {
            id = "hk-qamdb-2006"
            connectionTimeout = 60000 milliseconds
            connectionTestQuery = "SELECT 1"
            maximumPoolSize = 10
            url = ${?conf.mdb.url}
            username = ${?conf.mdb.username}
            password = ${?conf.mdb.password}
          }
        ]
      }
    },
  {
    group = "bcre_bkgdb"
    hikari {
      databases = [
        {
          id = "bcre_bkgdb"
          connectionTimeout = 60000 milliseconds
          connectionTestQuery = "SELECT 1"
          maximumPoolSize = 10
          url = ${?conf.bkgdb.url}
          username = ${?conf.bkgdb.username}
          password = ${?conf.bkgdb.password}
        }
      ]
    }
  },
  {
    group = "bcre_bapidb"
    hikari {
      databases = [
        {
          id = "bcre_bapidb"
          connectionTimeout = 60000 milliseconds
          connectionTestQuery = "SELECT 1"
          maximumPoolSize = 10
          url = ${?conf.bapidb.url}
          username = ${?conf.bapidb.username}
          password = ${?conf.bapidb.password}
        }
      ]
    }
  }
]

// ad hoc config for dev/sbx only
akka.loglevel = "DEBUG"
agoda.creation.creditCardApi.secureKeyId = 17
agoda.creation.authRecurRequired = [
  {
    destinationCountries = [
      153
    ]
    excludeOrigins = [
      130,
      118,
      13,
      158,
      99,
      28,
      153,
      101,
      197,
      37,
      54,
      205,
      145,
      187,
      157,
      7,
      57,
      149,
      129,
      182,
      213,
      125,
      27,
      167,
      49,
      51
    ]
  },
  {
    includeOrigins = [19, 35, 117] // Blacklisted countries
  },
  {
    includeHotels = [
      624306,
      251643,
      6412758,
      294897,
      263325,
      1986334,
      1315492,
      303390,
      1030811,
      929139,
      930782,
      487255,
      567154,
      2062002,
      1110316,
      647605,
      490036,
      174434,
      4878677,
      2282287,
      255893,
      2817624,
      71953,
      240077,
      557928,
      570303,
      3164,
      2320629,
      2320628,
      503166,
      462004,
      376399,
      1845958,
      237749,
      10325,
      788342,
      1061630,
      818809,
      1030641,
      544190,
      10351,
      337808,
      161700,
      45559,
      240606,
      1166228,
      665674,
      8493906
    ]
  }
]
agoda.domainUrl = "https://master.qa.www.agoda.com"

bk.bcre.local.servers = ["localhost:8080"]

customerApi.timeout = 3000

deliveryApi.contact-us-email-template-id = 4212
deliveryApi.ledger-report-email-template-id = 4254
deliveryApi.receipt-email-template-id = 4257
deliveryApi.sms-template-id = 3997

domains.website = "https://master.qa.www.agoda.com"

japanese-campaign.campaignIds = [
  1698845,
  1698840,
  1698860
]

papi.clientsettings.httpclient.request-timeout = 15000
papi.clientsettings.client.idle-timeout=90s

sitesettings.bookingAcknowledgeListLimit = 1000
sitesettings.bookingDetailsHostLimit = 20
sitesettings.bookingDetailsLimit = 15
sitesettings.https.force = false
sitesettings.https.port = 8443
sitesettings.isAuthen = false
sitesettings.listForAffiliateV2 = 500
sitesettings.listForHostDbTimeout = 25
sitesettings.port = 8080

whitelabel.canonicalUrls = {
  Japanican: "https://master.qa.notyja.com",
  Rurubu: "https://master.qa.notyru.com"
}

flights-api-config.timeout = 40000

ag-http-client {
  mesh {
    default {}
    services {
      papi-search {
        discovery {
          method = "static"
          static {
            hosts = [
              ${conf.papi.endpoints.url}":"${conf.papi.port}
            ]
          }
        }
        round-robin {
          method = "simple"
        }
        request {
          max-attempts = 1
        }
      }

      enigma {
        discovery {
          method = "static"
          static {
            hosts = [${?enigma_client}]
          }
        }
      }
      car-search-client {
        discovery {
          method = "static"
          static {
            hosts = [${?conf.vehicle-search-api-config.server}]
          }
        }
      }
      ceg-wl-api-client {
        discovery {
          method = "static"
          static.hosts = ["cegwl-qa.privatecloud.qa.agoda.is"]
        }
        request {
          maxAttempts = 3
        }
      }
    }
  }
}

ag-grpc.server.discovery.enabled = false

conf.activity-search-client.discovery.consul.initial-hosts = []
conf.activity-search-client.discovery.consul.tags = ["qa-/activity-search"]
conf.activity-search-client.discovery.consul.initial-hosts = [${?conf.activity-search-api-config.server}]

# ELAPI
conf.whitelabel_loyalty_api.discovery.consul.initial-hosts = ["whitelabel-loyalty-api-qa.privatecloud.qa.agoda.is"]
conf.whitelabel_loyalty_api.discovery.consul.tags = ["qa-/whitelabel_loyalty_api"]
conf.elapiClient.apiKey = "5e282e41bb5bb10d3297c103f0de361f" # pragma: allowlist secret
 ceg-wl-api-config {
   retries = 3,
   timeout = 5 seconds,
   apiKey = "ONH7cXPQ500q/xumXvgwtJdRhu3tzEezy6UkF6mdfYw=" # pragma: allowlist secret
 }

conf.experimentsPlatform.experiments-service.host = "https://experiments-service.qa.agoda.is"

bookingToken {
    key = "bWUuR9/63YpaTgATziAuHFYwnJThNKVj"
    algorithm = "SHA-1"
    cipher = "AES/ECB/PKCS5PADDING"
    salt = "01TCoDsLitnh+F0tXUCahi/MQ18fSJio"
}
