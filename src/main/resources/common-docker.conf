include "whitelabel-qa.conf"
include "cusco.conf"
include "cryptography.conf"
include "encryption.conf"

akka {
  loglevel = "DEBUG"

  http.client.parsing.max-chunk-size = 10m
  http.host-connection-pool.client.parsing.max-chunk-size = 10m
}

security {
  enabledModules = ["create", "booking", "initializebooking", "setupbooking"]
}

papi {
  defaultConnectionTimeout = 5000
  clientsettings {
    client.max-connections = 8
    client.min-connections = 1
    client.max-retries = 3
    client.max-open-requests = 40
    client.pipelining-limit = 8
    client.idle-timeout = 30s
    httpclient.request-timeout = 5000
    httpclient.bufferSize = 200
  }
}

ag-http-client {
  mesh {
    default {}
    services {
      papi-search {
        discovery {
          method = "static"
          static {
            hosts = [
              ${?papi_endpoints}":8080"
            ]
          }
        }

        round-robin {
          method = "simple"
        }

        request {
          max-attempts = 1
        }
      }
    }
  }
}

sitesettings {
  listForHostDbTimeout = 25
  getMetaDataByExternalBookingIdTimeoutSeconds = 120
  https {
    certPath = "cacert.jks"
    trustPath = "truststore.jks"
    certStore = "JKS"
    password = "agoda123"
  }
}

replicateExistingBookingConfig {
  itineraryIdLimit = 15
}

flightSendMessageConfig {
  itineraryIdLimit = 15
}

multiProductReplication {
  logMismatch = false
}

bookingConsentConfig {
  bookingConsentItems = [
    {
      whitelabelIds = [1]
      origins = [],
      cids = [1891504, 1891677, 1891678, 1891679, 1891680, 1891681, 1891682, 1891683, 1896369, 1896370, 1896371, 1896372, 1913762, 1913763, 1913764, 1913765, 1915237, 1915238]
      isRequired = true
      consentCmsId = 123826
      validationCmsId = 123705
      consentIdentifier = 1
    },
    {
      whitelabelIds = [1]
      origins = ["CN"]
      cids = []
      isRequired = true
      consentCmsId = 137158
      validationCmsId = 137162
      consentIdentifier = 2
    },
    {
      whitelabelIds = [1]
      origins = ["CN"]
      cids = []
      isRequired = true
      consentCmsId = 137159
      validationCmsId = 137162
      consentIdentifier = 3
    },
    {
      whitelabelIds = [1]
      origins = ["CN"]
      cids = []
      isRequired = true
      consentCmsId = 137160
      validationCmsId = 137162
      consentIdentifier = 4
    },
    {
      whitelabelIds = [1]
      origins = ["CN"]
      cids = []
      isRequired = true
      consentCmsId = 137161
      validationCmsId = 137162
      consentIdentifier = 5
    },
    {
      whitelabelIds = [1]
      origins = ["KR"]
      cids = []
      isRequired = true
      consentCmsId = 131557
      validationCmsId = 130070
      consentIdentifier = 6
    },
    {
      whitelabelIds = [1]
      origins = ["KR"]
      cids = []
      isRequired = true
      consentCmsId = 131558
      validationCmsId = 130070
      consentIdentifier = 7
    },
    {
      whitelabelIds = [1]
      origins = ["KR"]
      cids = []
      isRequired = true
      consentCmsId = 131559
      validationCmsId = 130070
      consentIdentifier = 8
    }
  ]
}

priceTolerance {
  maxUpperPercentage = 20
  maxLowerPercentage = -20
}

abs {
  port = 9002
  endpoints = ${?abs_endpoints}
  root { // new config for latest abs version (Remove the above two lines after merge)
    host = []
    host.0 = ${?abs_endpoint_1}
    port = 80
    isHttps = false
  }
  clientsettings {
    max-retries = 0
  }
  default-timeout {
    millis = 7000
  }
  booking-timeout {
    millis = 120000
  }
  cancel-timeout {
    millis = 120000
  }
  amendment-timeout {
    millis = 80000
  }
  bufferTime = 5s
  supplierNotSupportInstantBook = [28047, 28055]
}

tprm {
  url = ${?tprm_client},
  timeout = 600,
  apiKey = "test",
  clientApp = "bapi"
}

sql = [
  {
    group = "bapi"
    hikari {
      datasource-properties = {
        disableStatementPooling = false
        statementPoolingCacheSize = 200
      }
      databases = [
        {
          include "database-common.conf"
          include "database-qa-credentials.conf"
          id = "ci-cdb-1"
          url = ${?bapi_connection_strings}
          retry-count = 2
          username = ${?cdb_username}
          password = ${?cdb_password}
        }
      ]
    }
  },
  {
    group = "bcre_bfdb"
    hikari {
      datasource-properties = {
        disableStatementPooling = false
        statementPoolingCacheSize = 200
      }
      databases = [
        {
          id = "qabfdblistener"
          url = "**********************************************************************************************************************************************************************;"
          connectionTimeout = 60000 milliseconds
          connectionTestQuery = "SELECT 1"
          maximumPoolSize = 20
          include "database-qa-bfdb_bcre-credentials.conf"
        }
      ]
    }
  },
  {
    group = "bcre_mdb"
    hikari {
      datasource-properties = {
        disableStatementPooling = false
        statementPoolingCacheSize = 200
      }
      databases = [
        {
          id = "HK-QAMDB-2006"
          url = "**************************************************************************************************************************************************;"
          url = ${?bcre_mdb_connection_string}
          connectionTimeout = 60000 milliseconds
          connectionTestQuery = "SELECT 1"
          maximumPoolSize = 10
          include "database-qa-mdb-bcre_rw_credentials.conf"
          username = ${?bapi_db_username}
          password = ${?bapi_db_password}

        }
      ]
    }
  },
  {
    group = "bcre_bkgdb"
    hikari {
      databases = [
        {
          id = "bcre_bkgdb"
          url = "*************************************************************************************************************************************************;"
          include "database-common-bkgdb.conf"
          include "database-qa-bkgdb-credentials.conf"
        }
      ]
    }
  },
  {
    group = "bcre_bapidb"
    hikari {
      databases = [
        {
          id = "bcre_bapidb"
          url = "********************************************************************************************************************************************************;"
          include "database-common-bapidb.conf"
          include "database-qa-bapidb-bcre-credentials.conf"
        }
      ]
    }
  }
]

# kafka connection settings
kafka {
  topic = "gm-BookingCacheDetail-test"
  group = "booking-cache-consumer-group"
  auto_offset_reset = "largest"
  brokers = ["kafka:9092"]
  zookeeperNodes = "kafka:2181"
}

cache {
  cms {
    maxCapacity = 500
    initialCapacity = 16
    timeToLive = 1 hour
  }
}

experimentsPlatform {
  experiments-service {
    timeout = 5000
    host = "https://experiments-service.qa.agoda.is"
  }
  applyZForDefaultUser = ""
  applicationName = "booking-creation"
  messagingAPIToken = "bapi"
  clientId = 19
}

deliveryClient {
  name = "DeliveryClient"
  conn-timeout = 1000
  read-timeout = 4000
  retries = 2
  enabled = true
  failure-count = 3
  disable-duration = 15 seconds
  urls = ${?delivery_client_urls}
}

deliveryApi {
  client-id = 5041
  receipt-email-template-id = 4257
  sms-template-id = 3997
  ledger-report-email-template-id = 4254
  contact-us-email-template-id = 4212
}

bookingcentraldc {
  getRawBookingsLimit = 15
  getBookingsCxlDueDateLimit = 1000
  getBookingsCxlDueDateQueryTimeout = 30
  getBookingsCxlDueDateTimeoutSeconds = 180
  getBookingsCxlDueDateLowerBoundOffset = 365
  getBookingIdsTimeout = 30
  getAcknowledgeBookingIdsTimeout = 30
  getAcknowledgeBookingIdsPageSize = 1000
  refreshOnCMBookingIdLimit = 10
  getBAPIUserId = "BD811DA2-B3D0-470E-B7CD-F15B8A460EB0"
}


enigma {
  client {
    api-key = "G4zajYMmcyFTewQx"
    timeout = "5 sec"
    retry-limit = 1
    log-level = "error"
    searchLimit = 1250
  }
}

customerApi {
  url = ${?customerapi_client},
  urlv2Authed = ${?customerapi_client_v2_authed}
  urlv2Base = ${?customerapi_client_v2_base}
  apiKey = "test",
  clientApp = "bapi",
  timeout = 3000
}

agoda {
  creation {
    propertyBooking {
      earnGiftCard {
        defaultOffsetDay = 5
      }
      fraud {
        secondFraudCheckOffsetDay = 2
      }
    }
    creditCardApi {
      apiKey = "Qk7X1drjc2LKecKbBcomempS2Bq2AFwtTY2yDPKzgddxoJnbWI"
      clientId = "17"
      subSystemId = 1
      secureKeyId = 17
      storageExpirationTimeInHours = 23
      publicKeyId = 17
      publicKey = "<RSAKeyValue><Modulus>07qeNT1+CYG92jGkQOKBsp8/uquPSbWt8xbMn9IGZLl6Sz6lhrlVP1uozMW07jmdEpINsf23mGe4KtpQ5OnwsS1fATAssv/2M2XJCm0J9iZse8CAOCbHZDt+nFdwsGD0dHsMBttQcPOb/knbXdQ69Sq1GYdit9YyE/Gv8LRe3nDpRVJksD5cEFE+96nEyfZAOw1DgV6mg9NEJeZRezEHJf1lz9N3CuJCHsuhMNiwTgcZD4eXYa0+KH6kn2aRug9EW1dRYWxJ3yOxwlclbkIP8GsG0fxhR/m5iZ5pPuKBlCyP6BcXKCDERB6/E5Udp9ANNGlyWrbNd4cFo8EQB0SKXQ==</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>"
    }
    flightPaymentApi {
      1 = {
        clientId = 66
        apiKey = "k3VvFEzJvZoggju7kKjFF8PG6OYyCnvVO0YUT5R8oz78L8lZPP"
      }
      5 = {
        clientId = 91
        apiKey = "S2pkwkprXwbJPZpnTDyiOej7NXso0Q50puvFu6TUD8uUFefJih"
      }
    }
    vehiclePaymentApi {
      whitelabelIds = [6, 7, 22]
      clientId = "105"
      apiKey = "R5UNs1JtUA9B6RXrnwomlEPlObqrATwOJs9p1gLlWtSDhhqhwJ"   // pragma: allowlist secret
    }
    validation {
      enableDebitCardOnBNPL = true
      dayALlowBNPL = 1
      SettleDays = 4
      BNPCSettleDays = 5
      minBookingAmount {
        usd = 2
        jpy = 1
      }
      priceToleranceInUSD = 0.1
    }
    authRecurRequired = [
      {
        destinationCountries = [
          153
        ]
        excludeOrigins = [
          130,
          118,
          13,
          158,
          99,
          28,
          153,
          101,
          197,
          37,
          54,
          205,
          145,
          187,
          157,
          7,
          57,
          149,
          129,
          182,
          213,
          125,
          27,
          167,
          49,
          51
        ]
      },
      {
        includeOrigins = [19, 35, 117] // Blacklisted countries
      },
      {
        includeHotels = [
          624306,
          251643,
          6412758,
          294897,
          263325,
          1986334,
          1315492,
          303390,
          1030811,
          929139,
          930782,
          487255,
          567154,
          2062002,
          1110316,
          647605,
          490036,
          174434,
          4878677,
          2282287,
          255893,
          2817624,
          71953,
          240077,
          557928,
          570303,
          3164,
          2320629,
          2320628,
          503166,
          462004,
          376399,
          1845958,
          237749,
          10325,
          788342,
          1061630,
          818809,
          1030641,
          544190,
          10351,
          337808,
          161700,
          45559,
          240606,
          1166228,
          665674,
          8493906
        ]
      }
    ]
    mdb {
      insertDuplicatedCandidateTimeoutSec = 1
      insertDuplicatedCandidateForcedTimeoutMSec = 1500
      checkDuplicatedCandidateForcedTimeoutMSec = 1000
    }
  }
  bapiIdentifierUuid = "BD811DA2-B3D0-470E-B7CD-F15B8A460EB0"
}

japanese-campaign {
  campaignIds = [
    1698845,
    1698840,
    1698860
  ]
}

wl-client {
  hostsSettings = [
    "http://whitelabel-service-api-qa.privatecloud.qa.agoda.is"
  ]
}

flights-api-config {
  timeout = 10000
}


vehicle-search-api-config {
  retries = 5,
  timeout = 30 seconds
  backOffInterval = 5 seconds
}

ag-http-client.mesh.services {
  car-search-client {
    discovery {
      method = "static"
      static {
        hosts = ["hk-qaeiab-2a07.agoda.local:9006"]
      }
    }
  }
}

ancillary-api-pc-config {
  endPoints = [
    ${?ancillary_pc_url}
  ],
  maxRetries = 0,
  backOffMillis = 300,
  readTimeout = 5s
}

emailService {
  host = "central.webservices.agoda.local"
  port = 80
  sendVOC = "/email/emailservice.asmx"
}

sendHotelVoucher {
  rabbitMq {
    topic = "EBE.Email.SendEmail.Federated"
    messageType = "Agoda.EBE.Agents.Email.SendEmail.Object.SendEmailMessage:Agoda.EBE.Agents.Email"
  }
  email {
    workflowActionId = 8
    moduleId = 2
    templateId = 88
    priority = 1
    emailFormat = 2
  }
}

gandalf-api-config {
  url = ${?gandalf_url},
  timeoutInMs = 900,
  retryCount = 2
}

booking-creation-client {
  clientId = 17
  apiKey = "9xEdYdAbva2Hm31A5vMYysaEYY3F7ulkQdsO3M6pr9ClTl9ogL"
  localDcs = ["ci"]
  request-timeout = 10 s
  connect-timeout = 20 s
  max-retries = 0
  max-connections-per-host = 4
  useHttps = false
}
ci.bcre.local.servers = [${?bapi-url}]

fraud-api-client {
  endpoint = ${?fraud-api-client-endpoint}
  authToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE1NDc0NjEyODksInRlYW1fbmFtZSI6IiBJVC1CRS1CQVBJIiwidXNlcl9pZCI6ImJkODExZGEyLWIzZDAtNDcwZS1iN2NkLWYxNWI4YTQ2MGViMCIsImFwcF9uYW1lIjoiQm9va2luZyBBUEkifQ.vMjNrgBDFhwRRvjhuxK6dUQRpH56hp8rosQfXMyPFrE"  // pragma: allowlist secret
  timeout = 2500
}

ag-grpc.client {
  default {
    retry.method = "none"
    channel.max-inbound-message-size = 10485760
  }
  services {
    fraud-api {
      apiKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZWFtX25hbWUiOiJNUEJFLWJvb2tpbmctY3JlYXRpb24iLCJ1c2VyX2lkIjoiYmQ4MTFkYTItYjNkMC00NzBlLWI3Y2QtZjE1YjhhNDYwZWIwIiwiYXBwX25hbWUiOiJCb29raW5nLWNyZWF0aW9uIiwiaWF0IjoxNjQ4NjE0NTY2fQ.SMV0dcGExFUpoya9l0GR65XyfSbGJQ64OjH8rNO_0Z8"  // pragma: allowlist secret
      default-deadline = 2.5 seconds
      discovery {
        method = "static"
        static {
          hosts = [${?conf.fraud-api.endpoint}]
          authority = ${?conf.fraud-api.endpoint}
        }
      }
      retry {
        method = "none"
      }
    }
  }
}

consul-client {
  servers {
    default: ["http://qa1consul.agoda.local:8500"]
  }
}
ag-consul {
  servers = ["http://qa1consul.agoda.local:8500"]
}

ag-logging.metrics {
  enabled = true
  application = "booking-creation"
  messaging-api-token = "bapi"
  send-async = true
  prefix = "bcre"
}

ag-grpc.server {
  service-name = "booking-creation-grpc"
  discovery {
    //  Consul discovery settings
    enabled = false
  }
  endpoint {
    //    max-inbound-message-size is not set by default meaning there is no limit
    //    Example of setting to 10M:
    maxInboundMessageSize = 10485760
    port = 50051
  }
}

activity-search-api-config {
  retries = 5,
  timeout = 30 seconds
  backOffInterval = 5 seconds
}

ag-http-client.mesh {
  default {}
  services {
    activity-search-client {
      discovery {
        method = "consul"
        consul {
          service = "activity-search"
          tags = []
          initial-discovery-timeout = 1 minute
          initial-hosts = ${conf.activity-search-client.discovery.consul.initial-hosts}
        }
      }
    }

    whitelabel_loyalty_api {
      discovery {
        method = "static"
        static {
          hosts = ["wl-loyalty-elapi.aiab.qa.agoda.is"]
        }
      }
      round-robin {
        method = "simple"
      }
      request {
        max-attempts = 3
      }
    }
    enigma {
      round-robin {
        method = "weighted"
        weighted {
          total-weight = 100
          decrement = 10
          increment = 10
        }
      }
      discovery {
        method = "static"
        static {
          hosts = [${?enigma_client}]
        }
      }
    }
    ceg-wl-api-client {
      discovery {
        method = "static"
        static.hosts = ["cegwl-qa.privatecloud.qa.agoda.is"]
      }
      request {
        maxAttempts = 3
      }
    }
    // creditcard api client
    creditcard-api-local-client {
      discovery {
        method = "static"
        static {
          hosts = ["ebe-creditcard-service-netcore-local-qa.privatecloud.qa.agoda.is:80"]
        }
      }
      request {
        max-attempts = 1
      }
    }
    creditcard-api-aab-client {
      discovery {
        method = "static"
        static {
          hosts = ["ebe-creditcard-service-netcore-local-qa.privatecloud.qa.agoda.is:80"]
        }
      }
      request {
        max-attempts = 1
      }
    }
    creditcard-nonpci-api-local-client {
      discovery {
        method = "static"
        static {
          hosts = ["ebe-creditcard-service-netcore-local-qa.privatecloud.qa.agoda.is:80"]
        }
      }
      request {
        max-attempts = 1
      }
    }
    ancillary-api {
      discovery {
        method = "static"
        static {
          hosts = [${?ancillaryV2_url}]
        }
      }

      round-robin {
        method = "simple"
      }

      request {
        max-attempts = 1
      }
    }
  }
}

external-loyalty-client {
  serviceName: "whitelabel_loyalty_api",
  requestTimeout: 8000,
  maxRetries: 3,
  apiKey: "5e282e41bb5bb10d3297c103f0de361f" # pragma: allowlist secret
}

conf.activity-search-client.discovery.consul.initial-hosts = ["activity-search.aiab.qa.agoda.is"]

customerMarketingDisplaySettingsConfig {
  excludeOrigins = ["AT", "BE", "BG", "CY", "CZ", "DE", "DK", "EE", "FI", "FR", "GR", "HR", "HU", "IE", "IT", "LT", "LU", "LV", "MT", "NL", "PL", "PT", "RO", "SE", "SI", "SK", "NZ", "AU", "US", "KR", "CN", "SG", "GB", "BR"]
  disclaimerCmsId = 153944
}

capi {
  loyalty-profile {
    max-retries: 3
  }
  exchange-claim {
    max-retries: 3
  }
}

sql-execution {
  maxRetries: 3
}
ceg-wl-api-config {
  retries = 3,
  timeout = 5 seconds,
  apiKey = "ONH7cXPQ500q/xumXvgwtJdRhu3tzEezy6UkF6mdfYw=" # pragma: allowlist secret
}
