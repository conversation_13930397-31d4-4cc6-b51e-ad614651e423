include "common-app.conf"

security.enabledModules = [
  "create", "booking", "initializebooking", "setupbooking", "debug"
]

// common
conf.fraud-api.endpoint = "fraud-api-qa.privatecloud.qa.agoda.is:50051" // New fraud-api
conf.fraud-api.apiKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZWFtX25hbWUiOiJNUEJFLWJvb2tpbmctY3JlYXRpb24iLCJ1c2VyX2lkIjoiYmQ4MTFkYTItYjNkMC00NzBlLWI3Y2QtZjE1YjhhNDYwZWIwIiwiYXBwX25hbWUiOiJCb29raW5nLWNyZWF0aW9uIiwiaWF0IjoxNjQ4NjE0NTY2fQ.SMV0dcGExFUpoya9l0GR65XyfSbGJQ64OjH8rNO_0Z8"  // pragma: allowlist secret

conf.fraud-api-client.endpoint = "http://general-fraud-service-qa-local.privatecloud.qa.agoda.is"
conf.fraud-api-client.authToken = ""

conf.agoda.domainUrl = "https://master.qa.www.agoda.com"
conf.agoda.bapiIdentifierUuid = "BD811DA2-B3D0-470E-B7CD-F15B8A460EB0"
conf.agoda.creation.autoAmendmentUserId = "7580C3D7-B1BC-4FC8-9DA7-1274E8739F46"
conf.agoda.creation.paymentApi.apiKey = "8IzVMvwWpkTcKnBKCkMBo7DdEDzfb0mrIHdTr9BXQKhCImwLs3"
conf.agoda.creation.flightPaymentApi.1.apiKey = "k3VvFEzJvZoggju7kKjFF8PG6OYyCnvVO0YUT5R8oz78L8lZPP"
conf.agoda.creation.flightPaymentApi.5.apiKey = "S2pkwkprXwbJPZpnTDyiOej7NXso0Q50puvFu6TUD8uUFefJih"
conf.agoda.creation.creditCardApi.apiKey = "Qk7X1drjc2LKecKbBcomempS2Bq2AFwtTY2yDPKzgddxoJnbWI"
conf.agoda.creation.creditCardApi.publicKey = "<RSAKeyValue><Modulus>07qeNT1+CYG92jGkQOKBsp8/uquPSbWt8xbMn9IGZLl6Sz6lhrlVP1uozMW07jmdEpINsf23mGe4KtpQ5OnwsS1fATAssv/2M2XJCm0J9iZse8CAOCbHZDt+nFdwsGD0dHsMBttQcPOb/knbXdQ69Sq1GYdit9YyE/Gv8LRe3nDpRVJksD5cEFE+96nEyfZAOw1DgV6mg9NEJeZRezEHJf1lz9N3CuJCHsuhMNiwTgcZD4eXYa0+KH6kn2aRug9EW1dRYWxJ3yOxwlclbkIP8GsG0fxhR/m5iZ5pPuKBlCyP6BcXKCDERB6/E5Udp9ANNGlyWrbNd4cFo8EQB0SKXQ==</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>"

conf.emailService.host = "qa.central.webservices.agoda.local"
conf.emailService.port = 80

conf.booking-creation-client.apiKey = "9xEdYdAbva2Hm31A5vMYysaEYY3F7ulkQdsO3M6pr9ClTl9ogL"
conf.booking-creation-client.localDcs = [
  "bk"
]

conf.op-rabbit.connection.username = "eberabbit"
conf.op-rabbit.connection.password = "ebe!123"

conf.enigma.client.api-key = "ts9MXSvXTR83CykBlKqhJPAt7Yf7x9YF"
// non-pci
conf.abs.root.host = [
  "mock-api.qa.agoda.is"
]
conf.abs.root.port = 80
conf.abs.root.isHttps = false
conf.abs.port = ""

conf.agoda.creation.creditCardApiV2.localEndpoint = "ebe-creditcard-service-netcore-local-qa.privatecloud.qa.agoda.is:80"

conf.agoda.creation.hasBookingSummaryItineraryIndex = true

conf.ancillary-api-pc-config.endPoints = [
  "https://ancillary-api-qa.qa.agoda.is"
]

conf.deliveryClient.urls = [
  "http://delivery-bkk:51841/"
]

conf.message.dataCenter = "BK"

conf.op-rabbit.connection.hosts = [
  "qaeberabbit"
]
conf.op-rabbit.connection.port = 5672

conf.customerApi.url = "http://customerapi-qa.privatecloud.qa.agoda.is/V1/RewardsApiService"
conf.customerApi.urlv2 = "http://customerapi-qa.privatecloud.qa.agoda.is/v2/transit"
conf.customerApi.urlv2Authed = "http://customerapi-qa.privatecloud.qa.agoda.is/v2/authed"
conf.customerApi.urlv2Base = "http://customerapi-qa.privatecloud.qa.agoda.is/v2"

conf.tprm.url = "http://customerapi-qa.privatecloud.qa.agoda.is"

conf.wl-client.hostsSettings = [
  "http://whitelabel-service-api-qa.privatecloud.qa.agoda.is"
]

conf.flights-api-config.endPoints = [
  "flights-api-qa.privatecloud.qa.agoda.is:80"
]

conf.gandalf-api-config.url = "https://promo-api-qa.qa.agoda.is"

sql = [
  {
    group = "bapi"
    hikari {
      databases = [
        {
          id = "bk-qacdb-1000"
          url = "**********************************************************************************************************************************;"
          include "database-common.conf"
          include "database-qa-credentials.conf"
        },
        {
          id = "bk-qacdb-1001"
          url = "**********************************************************************************************************************************;"
          include "database-common.conf"
          include "database-qa-credentials.conf"
        }
      ],
      retry-count = 2
    }
  },
  {
    group = "bcre_bfdb"
    hikari {
      databases = [
        {
          id = "qabfdblistener"
          url = "**********************************************************************************************************************************************************************;"
          connectionTimeout = 60000 milliseconds
          connectionTestQuery = "SELECT 1"
          maximumPoolSize = 20
          include "database-qa-bfdb_bcre-credentials.conf"
        }
      ]
    }
  },
  {
    group = "bcre_mdb"
    hikari {
      databases = [
        {
          id = "HK-QAMDB-2006"
          url = "**************************************************************************************************************************************************;"
          connectionTimeout = 60000 milliseconds
          connectionTestQuery = "SELECT 1"
          maximumPoolSize = 10
          include "database-qa-mdb-bcre_rw_credentials.conf"
        }
      ]
    }
  },
  {
    group = "bcre_bkgdb"
    hikari {
      databases = [
        {
          id = "bcre_bkgdb"
          url = "*************************************************************************************************************************************************;"
          include "database-common-bkgdb.conf"
          include "database-qa-bkgdb-credentials.conf"
        }
      ]
    }
  },
  {
    group = "bcre_bapidb"
    hikari {
      databases = [
        {
          id = "bcre_bapidb"
          url = "*******************************************************************************************************************************************************;"
          include "database-common-bapidb.conf"
          include "database-qa-bapidb-bcre-credentials.conf"
        }
      ]
    }
  }
]
// ad hoc config for dev/sbx only
akka.loglevel = "DEBUG"
agoda.creation.creditCardApi.secureKeyId = 17
agoda.creation.authRecurRequired = [
  {
    destinationCountries = [
      153
    ]
    excludeOrigins = [
      130,
      118,
      13,
      158,
      99,
      28,
      153,
      101,
      197,
      37,
      54,
      205,
      145,
      187,
      157,
      7,
      57,
      149,
      129,
      182,
      213,
      125,
      27,
      167,
      49,
      51
    ]
  },
  {
    includeOrigins = [19, 35, 117] // Blacklisted countries
  },
  {
    includeHotels = [
      624306,
      251643,
      6412758,
      294897,
      263325,
      1986334,
      1315492,
      303390,
      1030811,
      929139,
      930782,
      487255,
      567154,
      2062002,
      1110316,
      647605,
      490036,
      174434,
      4878677,
      2282287,
      255893,
      2817624,
      71953,
      240077,
      557928,
      570303,
      3164,
      2320629,
      2320628,
      503166,
      462004,
      376399,
      1845958,
      237749,
      10325,
      788342,
      1061630,
      818809,
      1030641,
      544190,
      10351,
      337808,
      161700,
      45559,
      240606,
      1166228,
      665674,
      8493906
    ]
  }
]
agoda.domainUrl = "https://master.qa.www.agoda.com"

bk.bcre.local.servers = ["hk-qabcre-2c01.agoda.local:8443"]

consul-client.servers.default = ["http://bkconsul.agoda.local:8500"]

customerApi.timeout = 3000

deliveryApi.contact-us-email-template-id = 4212
deliveryApi.ledger-report-email-template-id = 4254
deliveryApi.receipt-email-template-id = 4257
deliveryApi.sms-template-id = 3997

domains.website = "https://master.qa.www.agoda.com"

japanese-campaign.campaignIds = [
  1698845,
  1698840,
  1698860
]

papi.clientsettings.httpclient.request-timeout = 3000

sitesettings.bookingAcknowledgeListLimit = 1000
sitesettings.bookingDetailsHostLimit = 20
sitesettings.bookingDetailsLimit = 15
sitesettings.https.force = false
sitesettings.https.port = 8443
sitesettings.isAuthen = false
sitesettings.listForAffiliateV2 = 500
sitesettings.listForHostDbTimeout = 25
sitesettings.port = 8080

whitelabel.canonicalUrls = {
  Japanican: "https://master.qa.notyja.com",
  Rurubu: "https://master.qa.notyru.com"
}

conf.vehicle-search-api-config.servers = ["car-search-qa.privatecloud.qa.agoda.is:80"]

ag-http-client {
  mesh {
    default {}
    services {
      papi-search {
        discovery {
          method = "static"
          static {
            hosts = [
              "propertyapisearchdocker-qa.privatecloud.qa.agoda.is"
            ]
          }
        }

        round-robin {
          method = "simple"
        }

        request {
          max-attempts = 1
        }
      }
    }
  }
}

ag-grpc.server.discovery.enabled = false


conf.activity-search-client.discovery.consul.initial-hosts = ["activity-search.privatecloud.qa.agoda.is"]
conf.activity-search-client.discovery.consul.tags = ["qa-/activity-search"]

conf.whitelabel_loyalty_api.discovery.consul.initial-hosts = ["whitelabel-loyalty-api-qa.privatecloud.qa.agoda.is"]
conf.whitelabel_loyalty_api.discovery.consul.tags = ["qa-/whitelabel_loyalty_api"]

ag-http-client.mesh {
  default {}
  services {
    activity-search-client {
      discovery {
        method = "consul"
        consul {
          service = "activity-search"
          tags = ${conf.activity-search-client.discovery.consul.tags}
          initial-discovery-timeout = 1 minute
          initial-hosts = ${conf.activity-search-client.discovery.consul.initial-hosts}
        }
      }
    }

    whitelabel_loyalty_api {
      discovery {
        method = "consul"
        consul {
          service = "whitelabel_loyalty_api"
          tags = ${conf.whitelabel_loyalty_api.discovery.consul.tags}
          initial-discovery-timeout = 1 minute
          initial-hosts = ${conf.whitelabel_loyalty_api.discovery.consul.initial-hosts}
        }
      }
      round-robin {
        method = "simple"
      }
      request {
        max-attempts = 3
      }
    }

    ceg-wl-api-client {
      discovery {
        method = "static"
        static.hosts = ["cegwl-qa.privatecloud.qa.agoda.is"]
      }
      request {
        maxAttempts = 3
      }
    }
  }
}

external-loyalty-client {
  serviceName: "whitelabel_loyalty_api",
  requestTimeout: 8000,
  maxRetries: 3,
  apiKey: ${conf.elapiClient.apiKey}
}
conf.elapiClient.apiKey = "5e282e41bb5bb10d3297c103f0de361f" # pragma: allowlist secret

abs.root.consul-setting.use-service-discovery = false
abs.root.consul-setting.discovery-settings.tags = ["abs-phoenix", "qa"]

# Vault
# ag-vault.http.auth.token = "vault-dev-token"

sql-execution {
  maxRetries: 3
}

ceg-wl-api-config {
  retries = 3,
  timeout = 5 seconds,
  apiKey = "ONH7cXPQ500q/xumXvgwtJdRhu3tzEezy6UkF6mdfYw=" # pragma: allowlist secret
}

conf.experimentsPlatform.experiments-service.host = "https://experiments-service.qa.agoda.is"