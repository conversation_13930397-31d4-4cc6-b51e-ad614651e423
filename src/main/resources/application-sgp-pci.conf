include "common-app-pci.conf"
include "vault-api-keys.conf"
include "rabbitmq.conf"

security.enabledModules = ["create"]

// common
conf.agoda.domainUrl = "https://www.agoda.com"
conf.agoda.bapiIdentifierUuid = "BD811DA2-B3D0-470E-B7CD-F15B8A460EB0"
conf.agoda.creation.autoAmendmentUserId = "7580C3D7-B1BC-4FC8-9DA7-1274E8739F46"


conf.emailService.host = "central.webservices.agoda.local"
conf.emailService.port = 80

conf.booking-creation-client.localDcs = [
  "am",
  "as",
  "hk",
  "sg",
  "sh"
]

conf.agoda.creation.creditCardApiV2.localEndpoint = "local.ebe-creditcard-service-netcore.svc:5000"

conf.agoda.creation.hasBookingSummaryItineraryIndex = false

conf.vehicle-search-api-config.servers = ["car-search-main.sg.agoda.is:80"]

conf.ancillary-api-pc-config.endPoints = [
  "http://main.ancillary-api.svc.cluster.local:8080"
]
conf.ancillary-api.endpoint = "main.ancillary-api.svc.cluster.local:8080"

conf.payment-api-v2.endpoint = "local.ebe-payment-api-netcore.svc.cluster.local:5000"

conf.deliveryClient.urls = [
  "http://delivery-sgp.agprod1.agoda.local:56424/"
]

conf.message.dataCenter = "SG"

conf.experimentsPlatform.experiments-service.host = "https://experiments-service.agoda.is"

conf.customerApi.url = "http://booking-main.customerapi.svc.cluster.local/V1/RewardsApiService"
conf.customerApi.urlv2 = "http://booking-main.customerapi.svc.cluster.local/v2/transit"
conf.customerApi.urlv2Authed = "http://booking-main.customerapi.svc.cluster.local/v2/authed"
conf.customerApi.urlv2Base = "http://booking-main.customerapi.svc.cluster.local/v2"

# svc mesh enigma configuration: https://gitlab.agodadev.io/PrivateCloud/service-routing/-/blob/main/components/mesh-config/booking-creation/5_pci/prod-booking-engima.yaml?ref_type=heads
ag-http-client.mesh.services {
  enigma.discovery {
    method = "static"
    static {
      hosts = ["prod-bcre.enigma.svc.cluster.local"]
    }
  }
}


conf.tprm.url = "http://booking-main.customerapi.svc.cluster.local"

conf.wl-client.hostsSettings = [
  "http://whitelabel-service-api-prod.sg.agoda.is"
]

conf.flights-api-config.endPoints = [
  "flights-api-direct.privatecloud.sg.agoda.is:80"
]

conf.gandalf-api-config.url = "http://main-pci.promo-api.svc.cluster.local:8085"

// pci
conf.abs.root.host = ["abs-phoenix-app-pci.privatecloud.sg.agoda.is"]
conf.abs.root.port = 443
conf.abs.root.isHttps = true
conf.abs.port = 443
abs.root.consul-setting.use-service-discovery = false

sql = [
  {
    group = "bapi"
    hikari {
      datasource-properties = {
        disableStatementPooling = false
        statementPoolingCacheSize = 200
      }
      databases = [
        {
          id = "SG-ERRCDB-6001"
          group = "bapi"
          url = "***********************************************************************************************************************************;"
          include "database-common.conf"
          include "database-vault-cdb_bapi_ro-credentials.conf"
        }
      ]
    }
  },
  {
    group = "bcre_bfdb"
    hikari {
      datasource-properties = {
        disableStatementPooling = false
        statementPoolingCacheSize = 200
      }
      databases = [
        {
          id = "sgbfdblistener"
          group = "bcre_bfdb"
          url = "****************************************************************************************************************************************************************************;"
          include "database-common-bfdb.conf"
          include "database-vault-bfdb_bcre_rw-credentials.conf"
        }
      ]
    }
  },
  {
    group = "bcre_mdb"
    hikari {
      datasource-properties = {
        disableStatementPooling = false
        statementPoolingCacheSize = 200
      }
      databases = [
        {
          id = "hkagmdb2000"
          group = "bcre_mdb"
          url = "************************************************************************************************************************************************;"
          include "database-common-mdb-pci.conf"
          include "database-vault-mdb_bcre_rw-credentials.conf"
        }
      ]
    }
  },
  {
    group = "bcre_bkgdb"
    hikari {
      datasource-properties = {
        disableStatementPooling = false
        statementPoolingCacheSize = 200
      }
      databases = [
        {
          id = "bcre_bkgdb"
          url = "********************************************************************************************************************;"
          include "database-common-bkgdb-pci.conf"
          include "database-vault-bkgdb-credentials.conf"
        }
      ]
    }
  },
  {
    group = "bcre_bapidb"
    hikari {
      datasource-properties = {
        disableStatementPooling = false
        statementPoolingCacheSize = 200
      }
      databases = [
        {
          id = "bcre_bapidb"
          url = "*********************************************************************************************************************************;"
          include "database-common-bapidb.conf"
          include "database-vault-bapidb-bcre-credentials.conf"
        }
      ]
    }
  }
]

ag-consul.token = "70271904-ebed-0fcd-5e53-a62cf18f61db"

conf.activity-search-client.discovery.consul.initial-hosts = []
conf.activity-search-client.discovery.consul.tags = ["production-/activity-search"]

multiProductReplication.logMismatch = true

conf.whitelabel_loyalty_api.discovery.consul.initial-hosts = ["whitelabel-loyalty-api-production.privatecloud.sg.agoda.is"]
conf.whitelabel_loyalty_api.discovery.consul.tags = ["production-/whitelabel_loyalty_api"]

conf.fraud-api-client.endpoint = "http://prod-local.general-fraud-service.svc.cluster.local:8000"
conf.fraud-api.endpoint = "prod.fraud-api.svc.cluster.local:50051" // New fraud-api
