package com.agoda.bookingapi

import akka.actor.ActorSystem
import akka.http.scaladsl.Http.ServerBinding
import akka.http.scaladsl.server._
import akka.http.scaladsl.{ConnectionContext, Http, HttpsConnectionContext}
import akka.stream.ActorMaterializer
import com.agoda.bapi.common.ToolSet
import com.agoda.bapi.common.config.HttpsSettings
import com.agoda.bapi.common.db.execution.context._
import com.agoda.bapi.common.token.AesSettings
import com.agoda.bapi.execution.context.NonBlockingExecutionContext.default
import com.agoda.bapi.execution.context.{ForkJoinMetricsReporter, NonBlockingExecutionContext, ThreadPoolExecutorMetricsReporter}
import com.agoda.bapi.server.config.model.ServerConfiguration
import com.agoda.bapi.server.config.{ConfigurationProvider, ServerConfigurationProvider}
import com.agoda.bapi.server.grpc.BapiCreationGrpcServer
import com.agoda.bapi.server.route.Router
import com.agoda.bapi.server.service.HealthService
import com.agoda.commons.observability.sdk.AgOpenTelemetry
import com.agoda.sql.DB
import com.google.inject.{CreationException, Guice}
import com.typesafe.config.{Config, ConfigFactory}
import com.typesafe.sslconfig.akka.AkkaSSLConfig
import io.opentelemetry.api.OpenTelemetry

import java.io.{FileInputStream, InputStream}
import java.security.{KeyStore, SecureRandom}
import javax.net.ssl.{KeyManagerFactory, SSLContext, TrustManagerFactory}
import scala.concurrent.duration._
import scala.concurrent.{Await, Future}
import scala.util.{Failure, Success, Try}

trait ShutdownAwareApp extends App {
  sys.addShutdownHook(shutdown())
  def shutdown(): Unit
}

object Boot extends ShutdownAwareApp with ToolSet with Directives {
  logger.info("Starting server...")

  val config              = ConfigFactory.load()
  val confProvider        = new ConfigurationProvider(config)
  val configuration       = confProvider.get()
  val serverConfProvider  = new ServerConfigurationProvider(config)
  val serverConfiguration = serverConfProvider.get()

  validateEncryptionConfig() // Refactoring needed for AesSettings, follow-up Jira BCT-937
  var injector = Guice.createInjector() // assign vanilla injector to 'jumpstart' initialization prior to try-catch
  try initializeInjector(config, serverConfiguration)
  catch {
    case ex: CreationException =>
      logger.error(s"dependency injection failed. aborting startup", ex)
      System.exit(130)
    case ex: Throwable =>
      logger.error("unexpected error, aborting startup", ex)
      System.exit(130)
  }

  var serverBindings: Seq[Future[ServerBinding]] = Seq.empty

  implicit val actorSystem: ActorSystem        = injector.getInstance(classOf[ActorSystem])
  implicit val materializer: ActorMaterializer = injector.getInstance(classOf[ActorMaterializer])
  injector.getInstance(classOf[OpenTelemetry]).asInstanceOf[AgOpenTelemetry].registerGlobal()
  startEnabledModules()

  // GRPC server boot
  BapiCreationGrpcServer.boot(config, injector)

  private[bookingapi] def initializeInjector(config: Config, securitySettings: ServerConfiguration): Unit = {
    import scala.collection.JavaConverters._
    val enabledModules = securitySettings.enabledModules(config)
    logger.info(s"initialising DI for modules ${enabledModules.asScala.map(_.getClass.getSimpleName)}")
    injector = Guice.createInjector(enabledModules)
  }

  private def startEnabledModules(): Unit = {
    startServer() onComplete {
      case Success(_) =>
        logger.info("Server start-up routine done")
        actorSystem.scheduler.schedule(1.minute, 5.minutes) {
          injector.getInstance(classOf[HealthService]).reportVersionHealth()
        }
        actorSystem.scheduler.schedule(1.minute, 1.minute) {
          injector.getInstance(classOf[HealthService]).reportHealthMeasurement()
        }
        reportThreadPoolsMetrics(actorSystem)
      case Failure(ex) =>
        logger.error(s"Server failed to start. ${ex.getMessage}", ex)
        actorSystem.terminate()
    }
  }

  private def reportThreadPoolsMetrics(actorSystem: ActorSystem): Unit = {
    val forkJoinMetricsReporter =
      new ForkJoinMetricsReporter(NonBlockingExecutionContext.BapiAsyncForkJoinPool, "bapi_default_forkjoin")

    val cdbThreadPoolExecutorMetricsReporter =
      new ThreadPoolExecutorMetricsReporter(CDBExecutionContext.CDBExecutorService, "cdb_thread_pool")
    val bfdbThreadPoolExecutorMetricsReporter =
      new ThreadPoolExecutorMetricsReporter(BFDBExecutionContext.BFDBExecutorService, "bfdb_thread_pool")
    val mdbThreadPoolExecutorMetricsReporter =
      new ThreadPoolExecutorMetricsReporter(MDBExecutionContext.MDBExecutorService, "mdb_thread_pool")
    val bkgdbThreadPoolExecutorMetricsReporter =
      new ThreadPoolExecutorMetricsReporter(BkgDbExecutionContext.dbExecutorService, "bkgdb_thread_pool")
    val bapidbThreadPoolExecutorMetricsReporter =
      new ThreadPoolExecutorMetricsReporter(BapiDbExecutionContext.dbExecutorService, "bapidb_thread_pool")

    actorSystem.scheduler.schedule(1.minute, 1.minute) {
      forkJoinMetricsReporter.sendMetrics()
      cdbThreadPoolExecutorMetricsReporter.sendMetrics()
      bfdbThreadPoolExecutorMetricsReporter.sendMetrics()
      mdbThreadPoolExecutorMetricsReporter.sendMetrics()
      bkgdbThreadPoolExecutorMetricsReporter.sendMetrics()
      bapidbThreadPoolExecutorMetricsReporter.sendMetrics()
    }
  }

  private def startServer(): Future[Unit] =
    Future {
      val siteSettings                              = configuration.siteSettings
      val httpsContext: Try[HttpsConnectionContext] = buildHttpsContext(siteSettings.https)
      val routes                                    = initHttpService()
      if (siteSettings.https.force)
        httpsContext match {
          case Failure(throwable) => throw throwable
          case Success(ctx)       => Http().setDefaultServerHttpContext(ctx)
        }
      else {
        serverBindings = serverBindings :+ Http().bindAndHandle(routes, siteSettings.interface, siteSettings.port)
        httpsContext match {
          case Success(ctx)       => logger.info("HTTPS context successfully created")
          case Failure(throwable) => logger.warn("HTTPS listener is not started due configuration failure", throwable)
        }
      }
      if (httpsContext.isSuccess)
        serverBindings = serverBindings :+ Http().bindAndHandle(
          routes,
          siteSettings.interface,
          siteSettings.https.port,
          connectionContext = httpsContext.get
        )
    }

  private def initHttpService(): Route = {
    injector.getInstance(classOf[Router]).routes
  }

  override def shutdown(): Unit = {
    serverBindings.foreach(shutdownServer)
    logger.info("Shutting down server...")
  }

  private def shutdownServer(serverBindingFuture: Future[ServerBinding]) = {
    Await
      .ready(serverBindingFuture.flatMap(_.unbind()), configuration.siteSettings.apiShutdownTimeout.second)
      .onComplete { result =>
        logger.info(s"HTTP port unbind completed? ${result.isSuccess}")
      }

    Await.ready(Future(shutdownServices()), configuration.siteSettings.apiShutdownTimeout.second)
  }

  private def shutdownServices(): Unit = {
    val db = injector.getInstance(classOf[DB])
    db.shutdown()
  }

  private def buildHttpsContext(httpsSettings: HttpsSettings): Try[HttpsConnectionContext] =
    Try {
      val sslConfig = AkkaSSLConfig().config

      val ks: KeyStore          = KeyStore.getInstance(httpsSettings.certStore)
      val keystore: InputStream = new FileInputStream(httpsSettings.certPath)

      require(keystore != null, "Keystore required!")
      ks.load(keystore, httpsSettings.password.toCharArray)

      val keyManagerFactory: KeyManagerFactory = KeyManagerFactory.getInstance(sslConfig.keyManagerConfig.algorithm)
      keyManagerFactory.init(ks, httpsSettings.password.toCharArray)

      val tmf: TrustManagerFactory = TrustManagerFactory.getInstance(sslConfig.trustManagerConfig.algorithm)
      tmf.init(ks)

      val sslContext: SSLContext = SSLContext.getInstance(sslConfig.protocol)
      sslContext.init(keyManagerFactory.getKeyManagers, tmf.getTrustManagers, new SecureRandom)
      ConnectionContext.https(sslContext)
    }

  private def validateEncryptionConfig(): Unit = {
    try {
      AesSettings()
      logger.info("AES Settings loaded successfully")
    } catch {
      case ex: Throwable =>
        logger.error("Failed to load AES Settings. Aborting startup.", ex)
        System.exit(130)
    }
  }

}
