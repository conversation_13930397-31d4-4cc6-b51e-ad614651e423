package com.agoda.bapi.creation

import com.agoda.bapi.common.handler.{MeasurementsContext, RequestContext}
import com.agoda.bapi.common.message.creation.common.Payment3DSRequest
import com.agoda.bapi.common.message.creation.{CreateBookingRequest, CreateBookingRequestV2, Credit<PERSON><PERSON>, Customer, DuplicateBooking, InstantBook}
import com.agoda.bapi.common.model.activity.ActivityBookingToken
import com.agoda.bapi.common.model.addOn.UserTaxCountry
import com.agoda.bapi.common.model.addOn.cegFastTrack.AddOnBookingToken
import com.agoda.bapi.common.model.car.CarBookingToken
import com.agoda.bapi.common.model.cart.CartItineraryInfo
import com.agoda.bapi.common.model.creation.BookingDcProcessType
import com.agoda.bapi.common.model.db.CountryInfo
import com.agoda.bapi.common.model.flight.FlightBookingToken
import com.agoda.mpb.common.MultiProductType.MultiProductType
import com.agoda.bapi.common.model.product.BookingFlow.BookingFlow
import com.agoda.bapi.common.model.product.BookingRequestTypeResolver.MultiProductFlowDefinition
import com.agoda.bapi.common.model.product.{BookingFlow, BookingRequestTypeResolver}
import com.agoda.bapi.common.model.rebookAndCancel.RebookAndCancelData
import com.agoda.bapi.common.model.{BookingId, CurrencyInfo, ExperimentNames, addOn}
import com.agoda.bapi.common.token.{AgentAssistedBookingInfo, PartnerExternalInfo}
import com.agoda.bapi.creation.mapper.addon.AddOnEnumMap
import com.agoda.bapi.creation.model.db.{CardBinRangeInfo, CreationRequest, CreditCardInfo => DbCreditCardInfo, FraudInfo}
import com.agoda.bapi.creation.model.flights.{DecodedFlight, RequestWithFlightsNDuplicates}
import com.agoda.bapi.creation.model.multi.{MultiProductsRequest, Product, ValidateProductRequest}
import com.agoda.bapi.creation.model.payment.SupplierSpecificData
import com.agoda.bapi.creation.model.rooms.{DecodedRooms, RequestWithRoomsNDuplicates, RoomInfo}
import com.agoda.bapi.creation.service.stage.presave.ProtectionAncillaryModel
import com.agoda.bapi.creation.util.{InstantBookUtils, MultiProductUtils, WhitelabelUtils}
import com.agoda.mpb.common.{BookingType, MultiProductType}
import com.agoda.common.itineraryContext.ItineraryContext
import com.agoda.mpb.common.BookingType.BookingType
import com.agoda.mpb.common.models.state.CommonPaymentInfo

package object model {

  type RoomUid = String

  trait CreateRequest {
    def request: CreateBookingRequest

    def requestContext: RequestContext

    def userTaxCountryId: Option[Int] = None

    def userTaxCountry: Option[UserTaxCountry] = None

    def measurementsContext: MeasurementsContext = {
      val instantBookingEnabled = WhitelabelUtils.instantBookingEnabled(requestContext.whiteLabelInfo)
      val bookingDcProcessType = BookingDcProcessType
        .getBookingDcProcessType(
          request,
          Option(requestContext.whiteLabelInfo.feature),
          instantBookingEnabled
        )
      MeasurementsContext(
        requestContext.getCorrelationId(),
        requestContext.clientId,
        requestContext.path,
        request.storefrontId,
        request.siteId.getOrElse(0),
        requestContext.userContext.map(_.languageId).getOrElse(1),
        request.payment.method,
        request.payment.amount.paymentCurrency,
        bookingDcProcessType,
        BookingRequestTypeResolver.getProductType(request),
        isRecheckAllotmentOnCentralDC,
        requestContext.whiteLabelInfo.whiteLabelId,
        request.payment.creditCard.flatMap(_.isCCOF).getOrElse(false)
      )
    }

    def isRecheckAllotmentOnCentralDC: Boolean =
      request.instantBook match {
        case Some(InstantBook(_, _, true, _)) => false
        case _                                => requestContext.whiteLabelInfo.feature.instantBooking.recheckAllotmentOnCentralDC.getOrElse(true)
      }

    def getInstallmentPlanId(bookingType: BookingType): Option[Int] =
      if (bookingType == BookingType.CreditCard || bookingType == BookingType.BoRSupplier)
        request.payment.creditCard match {
          case Some(v) => v.installmentPlanId
          case None    => Some(0)
        }
      else Some(0)

    def getGateWayInfoId(bookingType: BookingType): Option[Int] =
      if (bookingType == BookingType.RedirectCard)
        request.payment.continuation.flatMap(_.gatewayInfoId)
      else None

    def getMemberId: Int =
      requestContext.userContext.flatMap(_.memberId).getOrElse(request.customer.memberId)

    def getCustomer: Customer =
      request.customer.copy(memberId = getMemberId)

    def getPayment3DS: Option[Payment3DSRequest] =
      request.payment.creditCard.flatMap(_.payment3DS)

    def getCreditCardInfo: Option[DbCreditCardInfo] =
      request.payment.creditCard.map { card: CreditCard =>
        DbCreditCardInfo(
          card.creditcardType.value,
          card.chargeOption.id,
          card.paymentOption,
          card.creditcardId,
          card.customerIdCard,
          card.installmentPlanId,
          saveAsPermanent = card.isSaveCCOF,
          issueBankCountryId = card.issueBankCountryId,
          last4Digits = card.last4Digits,
          isNewCC = card.isNewCC
        )
      }

    def getWhitelabelToken: Option[String] = requestContext.whiteLabelInfo.token

    def toCreationRequest: CreationRequest = {
      val seenExperimentTags = requestContext.featureAware
        .map(_.buildSeenExperimentTags(Seq(ExperimentNames.migrateFlightToCartFlow)))
        .filter(_.nonEmpty)

      val userContextWithSeenExperimentTags = requestContext.userContext.map { userCtx =>
        userCtx.copy(
          experimentData = userCtx.experimentData.map(
            _.copy(seenExperiments = seenExperimentTags)
          )
        )
      }

      CreationRequest(
        affiliateModel = request.affiliateModel,
        userId = request.userId,
        storefrontId = request.storefrontId,
        siteId = request.siteId,
        referralUrl = request.referralUrl,
        platformId = request.platformId,
        trackingCookieId = request.trackingCookieId,
        sessionId = request.sessionId,
        userAgent = request.userAgent,
        clientIp = request.clientIp,
        userContext = userContextWithSeenExperimentTags,
        isNewsletterOptIn = request.customer.newsletter,
        tmSessionId = request.tmSessionId,
        acceptHeader = request.acceptHeader,
        fraudInfo = Some(FraudInfo(deviceFingerprintData = request.fraudInfo.deviceFingerprintData)),
        requestedTime = Some(requestContext.requestedTime),
        instantBookSetting = InstantBookUtils.toInstantBookSetting(request, requestContext)
      )
    }

    def getPartnerClaimToken: Option[String] =
      request.externalLoyaltyRequest.flatMap(_.partnerClaimToken)
  }

  trait BookingTypeInfo {
    def bookingType: BookingType.BookingType
  }

  trait Duplicates {
    def duplicates: Seq[DuplicateBooking]
  }

  trait SupplierInfo {
    def supplierData: SupplierSpecificData
  }

  case class MultiProduct(
      properties: Seq[RoomInfo] = Seq.empty,
      flights: Seq[FlightBookingToken] = Seq.empty,
      vehicles: Seq[CarBookingToken] = Seq.empty,
      protections: Seq[ProtectionAncillaryModel] = Seq.empty,
      commonPayment: Option[CommonPaymentInfo] = None,
      activities: Seq[ActivityBookingToken] = Seq.empty,
      cegFastTracks: Seq[AddOnBookingToken] = Seq.empty,
      addOns: Seq[addOn.AddOnBookingToken] = Seq.empty,
      isBookingFromCart: Option[Boolean] = None,
      partnerExternalInfo: Option[PartnerExternalInfo] = None,
      isPartialSuccessAllowed: Option[Boolean] = None,
      userTaxCountryId: Option[Int] = None,
      userTaxCountry: Option[UserTaxCountry] = None,
      aabInfo: Option[AgentAssistedBookingInfo] = None,
      rebookAndCancelData: Option[RebookAndCancelData] = None
  )

  case class RequestWithProducts(
      request: CreateBookingRequest,
      requestContext: RequestContext,
      @deprecated("Use the products.properties field instead")
      rooms: Map[RoomUid, RoomInfo],
      flights: Seq[FlightBookingToken],
      car: Option[CarBookingToken],
      protections: Seq[ProtectionAncillaryModel],
      bookingFlow: BookingFlow, // grab from creation booking token from setup booking
      products: MultiProduct = MultiProduct(),
      cartItineraryInfo: Option[CartItineraryInfo] = None,
      requestV2: Option[CreateBookingRequestV2], // to be stored in dbo.duplicate_request_candidates.hashed_request
      itineraryContext: Option[ItineraryContext] = None
  ) extends CreateRequest
      with DecodedRooms
      with DecodedFlight {

    override def measurementsContext =
      bookingFlow match {
        case BookingFlow.SingleFlight =>
          super.measurementsContext
            .copy(paymentModel = flights.headOption.flatMap(_.info.map(_.paymentModel.id)))
        case _ => super.measurementsContext
      }

    def withType(bookingType: BookingType.BookingType): RequestWithProductsNType =
      RequestWithProductsNType(
        request,
        requestContext,
        rooms,
        flights,
        protections,
        bookingType,
        bookingFlow,
        products,
        cartItineraryInfo,
        itineraryContext
      )
  }

  // todo: [multi] alter this class to handle multi booking
  case class RequestWithProductsNType(
      request: CreateBookingRequest,
      requestContext: RequestContext,
      @deprecated("Use the products.properties field instead")
      rooms: Map[RoomUid, RoomInfo],
      flights: Seq[FlightBookingToken],
      protections: Seq[ProtectionAncillaryModel],
      bookingType: BookingType,
      bookingFlow: BookingFlow,
      products: MultiProduct = MultiProduct(),
      cartItineraryInfo: Option[CartItineraryInfo] = None,
      itineraryContext: Option[ItineraryContext] = None
  ) extends CreateRequest
      with DecodedRooms
      with DecodedFlight
      with BookingTypeInfo {

    def toFlightsWithType: Seq[ValidateProductRequest[FlightBookingToken]] =
      products.flights.map(productInfo =>
        ValidateProductRequest(request, requestContext, Product(BookingType.Flight, productInfo, None))
      )

    def toRoomsWithType: Seq[ValidateProductRequest[RoomInfo]] =
      products.properties.map(productInfo =>
        ValidateProductRequest(request, requestContext, Product(bookingType, productInfo, None))
      )

    def toVehiclesWithType: Seq[ValidateProductRequest[CarBookingToken]] =
      products.vehicles.map(productInfo =>
        ValidateProductRequest(request, requestContext, Product(BookingType.Vehicle, productInfo, None))
      )

    def toActivityWithType: Seq[ValidateProductRequest[ActivityBookingToken]] =
      products.activities.map(productInfo =>
        ValidateProductRequest(
          request = request,
          requestContext = requestContext,
          product = Product(BookingType.Activity, productInfo, Some(MultiProductType.SingleActivity))
        )
      )

    def withRoomDuplicates(duplicates: Seq[DuplicateBooking]): RequestWithRoomsNDuplicates =
      RequestWithRoomsNDuplicates(request, requestContext, rooms, bookingType, duplicates, bookingFlow)

    def withFlightDuplicates(duplicates: Seq[DuplicateBooking]): RequestWithFlightsNDuplicates =
      RequestWithFlightsNDuplicates(request, requestContext, flights, bookingType, duplicates)

    def toMultiProductsRequest()(implicit mpDefinition: MultiProductFlowDefinition): MultiProductsRequest = {
      // todo; resolve multiProductType with proper method
      val multiProductType: Option[MultiProductType] = bookingFlow match {
        case BookingFlow.Package        => Some(MultiProductType.Package)
        case BookingFlow.MixAndSave     => Some(MultiProductType.MixAndSave)
        case BookingFlow.SingleProperty => Some(MultiProductType.SingleProperty)
        case BookingFlow.SingleFlight if flights.size > 1 =>
          Some(
            MultiProductType.HackerFare
          ) // TODO: remove after deployment, this for independecy deployment of Pci and NonPci
        case BookingFlow.Hackerfare                 => Some(MultiProductType.HackerFare)
        case BookingFlow.MultiFlightsWithProtection => Some(MultiProductType.MultiFlightsWithProtection)
        case BookingFlow.FlightWithProtection       => Some(MultiProductType.FlightWithProtection)
        case BookingFlow.MultiHotel                 => Some(MultiProductType.MultiProperties)
        case BookingFlow.SingleActivity             => Some(MultiProductType.SingleActivity)
        case BookingFlow.Cart =>
          Some(
            MultiProductType.apply(
              MultiProductUtils
                .getMultiProductType(
                  bookingFlow,
                  requestContext.featureAware.exists(_.migrateFlightToCartFlow(requestContext.whiteLabelInfo)),
                  requestContext.featureAware.exists(_.migrateHackerFareToCartFlow(requestContext.whiteLabelInfo))
                )
                .id
            )
          )
        case _ => None
      }

      val flightsInfo  = products.flights.map(Product[FlightBookingToken](BookingType.Flight, _, multiProductType))
      val vehiclesInfo = products.vehicles.map(Product[CarBookingToken](BookingType.Vehicle, _, multiProductType))
      val roomsInfo    = products.properties.map(r => Product[RoomInfo](bookingType, r, multiProductType))
      val protectionInfo =
        products.protections.map(Product[ProtectionAncillaryModel](BookingType.Protection, _, multiProductType))
      val activitiesInfo =
        products.activities.map(Product[ActivityBookingToken](BookingType.Activity, _, multiProductType))

      val cegFastTrackInfo: Seq[Product[AddOnBookingToken]] =
        products.cegFastTracks.map(Product[AddOnBookingToken](BookingType.CEGFastTrack, _, multiProductType))

      val addOnInfo: Seq[Product[addOn.AddOnBookingToken]] =
        products.addOns.map(addOnProduct =>
          Product[addOn.AddOnBookingToken](
            bookingType = AddOnEnumMap.getBookingTypeFromProductType(
              addOnProduct.getProductType
            ),
            info = addOnProduct,
            multiProductType = multiProductType
          )
        )

      MultiProductsRequest(
        request = request,
        requestContext = requestContext,
        properties = roomsInfo,
        flights = flightsInfo,
        vehicles = vehiclesInfo,
        protections = protectionInfo,
        activities = activitiesInfo,
        cegFastTracks = cegFastTrackInfo,
        addOns = addOnInfo,
        bookingFlow = bookingFlow,
        commonPayment = products.commonPayment,
        isBookingFromCart = products.isBookingFromCart,
        partnerExternalInfo = products.partnerExternalInfo,
        isPartialSuccessAllowed = products.isPartialSuccessAllowed,
        cartItineraryInfo = cartItineraryInfo,
        userTaxCountryId = products.userTaxCountryId,
        userTaxCountry = products.userTaxCountry,
        aabInfo = products.aabInfo,
        rebookAndCancelData = products.rebookAndCancelData,
        itineraryContext = itineraryContext
      )
    }

    def justProducts(): RequestWithProducts =
      RequestWithProducts(request, requestContext, rooms, flights, None, protections, bookingFlow, requestV2 = None)
  }

  case class CreatedBooking(
      itineraryId: Int,
      bookingId: BookingId
  )

  case class CreditCardInfo(
      currencyInfo: Option[CurrencyInfo],
      countryInfo: Option[CountryInfo],
      binRangeInfo: Option[CardBinRangeInfo]
  )

  case class Airport(
      airportCode: String,
      airportName: String,
      landmarkId: Int,
      cityCode: String
  )

  case class Aircraft(
      aircraftId: Int,
      aircraftCode: String,
      aircraftName: String
  )

  case class CabinClass(
      carrierId: Int,
      cabinClassCode: String,
      cabinClassName: String
  )

  case class Carrier(
      carrierId: Int,
      supplierId: Int,
      carrierCode: String,
      carrierName: String,
      icon: Option[String],
      carrierType: Option[String]
  )
}
