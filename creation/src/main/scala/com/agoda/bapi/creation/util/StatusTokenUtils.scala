package com.agoda.bapi.creation.util

import com.agoda.bapi.agent.common.schema.BookingActionMessageTopic
import com.agoda.bapi.common.StatusTokenVersionChooser
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.model.{StatusToken, WhiteLabel}
import com.agoda.bapi.common.model.WhiteLabel.WhiteLabel
import com.agoda.bapi.common.service.FeatureAware

object StatusTokenUtils {
  def getBookingActionMessageTopic(whiteLabelId: WhiteLabel)(implicit requestContext: RequestContext): Option[Int] = {
    val isJtbWl = requestContext.whiteLabelInfo match {
      case wlInfo if WhitelabelUtils.isJtbWl(wlInfo) => true
      case _                                         => false
    }
    if (isJtbWl)
      Some(BookingActionMessageTopic.BAM_Topic_JTBPartnerProvisioningResult.value)
    else None
  }

  def getStatusTokenVersionAndOperationId(
      operationId: Option[Long]
  )(implicit requestContext: RequestContext): (Int, Option[Long]) =
    if (requestContext.featureAware.exists(_.enableStatusTokenV6)) (StatusToken.Version6, operationId)
    else (StatusToken.Version4, None)
}
