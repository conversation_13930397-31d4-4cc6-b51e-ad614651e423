package com.agoda.bapi.creation.mapper.ebe

import com.agoda.bapi.common.config.PropertyBookingCreationConfig
import com.agoda.bapi.common.handler.{MeasurementsContext, RequestContext}
import com.agoda.bapi.common.message.creation._
import com.agoda.bapi.common.model.WhiteLabel.WhiteLabel
import com.agoda.bapi.common.model.creation.{AvailabilityType, _}
import com.agoda.mpb.common.models.state.ProductType
import com.agoda.bapi.common.model.property.PropertyBookingStateModel._
import com.agoda.bapi.common.model.{ActionId, ChargeOption, CurrencyCode, ItineraryId}
import com.agoda.bapi.common.repository.CountriesRepository
import com.agoda.bapi.common.util.{JodaDateTimeUtils, JodaToJavaDateTimeConversions, TimeZoneValidationSupport}
import com.agoda.bapi.creation.mapper.ebe.builder._
import com.agoda.bapi.creation.model.CreateRequest
import com.agoda.bapi.creation.model.db.{HotelInfo, SurchargeInfo => SurchargeInfoDB}
import com.agoda.bapi.creation.model.multi.{EmptyProductReservedIds, ReservedIds}
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.bapi.creation.proxy.CreationCDBProxy
import com.agoda.bapi.creation.repository.EbeLiteBookingRepository
import com.agoda.bapi.creation.service.UrlService
import com.agoda.bapi.creation.util.{CancellationUtils, CustomerApiTokenUtils, FinancialBreakdownUtils, ToolSet}
import com.agoda.mpb.common.DmcId
import org.joda.time.{DateTime, DateTimeZone, Days}

import java.util.UUID
import javax.inject.{Inject, Singleton}
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

trait PropertyModelInternalMapper extends ToolSet {
  def mapPropertyModelInternal(
      createRequest: CreateRequest,
      itineraryId: ItineraryId,
      reservedInfo: ReservedIds[RoomInfo, EmptyProductReservedIds],
      tripStart: Option[DateTime],
      tripEnd: Option[DateTime],
      prebookingId: PrebookingId,
      productIndex: Option[Int],
      cancellationChargeItemId: Option[Int] = None,
      masterActionId: ActionId
  )(implicit measurementCxt: MeasurementsContext, context: RequestContext): Future[PropertyModelInternal]
}

final case class BookingContext(
    request: CreateBookingRequest,
    requestContext: RequestContext,
    bookingId: Int,
    itineraryId: Int,
    hotelId: Long,
    noOfAdults: Int,
    noOfChildren: Int,
    totalStayPeriod: Int,
    bookingDate: DateTime,
    checkInDate: DateTime,
    checkOutDate: DateTime,
    isYCSMigration: Boolean,
    hotelGMTOffset: Int,
    dmcCode: String,
    chargeOption: ChargeOption.Value,
    isAgentAssist: Boolean,
    fullyAuthDate: DateTime,
    fullyChargeDate: DateTime,
    agodaManagedYcs: Boolean,
    isAgencyPrePay: Boolean
)

@Singleton
class PropertyModelInternalMapperImpl @Inject() (
    ebeLiteBookingRepository: EbeLiteBookingRepository,
    ebeConfig: PropertyBookingCreationConfig,
    dateTimeUtils: JodaDateTimeUtils,
    cancellationUtils: CancellationUtils,
    chargeUtils: EbeBookingChargeMapperUtils,
    propertyBookingSummaryBuilder: PropertyBookingSummaryBuilder,
    urlService: UrlService,
    cdbProxy: CreationCDBProxy,
    customerApiTokenUtils: CustomerApiTokenUtils,
    countriesRepository: CountriesRepository
)(implicit executionContext: ExecutionContext)
    extends PropertyModelInternalMapper
    with TimeZoneValidationSupport {
  val startIndex: Int                 = 1
  val modReferenceId: Int             = 1000
  val modProductIndexReferenceId: Int = 10000
  val isHideSensitiveData: Boolean    = true
  val defaultDMCCode: String          = ""
  val noMemberId: Int                 = 0

  val bapiUserId: UUID = UUID.fromString("BD811DA2-B3D0-470E-B7CD-F15B8A460EB0")

  override def mapPropertyModelInternal(
      createRequest: CreateRequest,
      itineraryId: ItineraryId,
      reservedInfo: ReservedIds[RoomInfo, EmptyProductReservedIds],
      tripStart: Option[DateTime],
      tripEnd: Option[DateTime],
      prebookingId: PrebookingId,
      productIndex: Option[Int],
      cancellationChargeItemId: Option[Int] = None,
      masterActionId: ActionId
  )(implicit measurementCxt: MeasurementsContext, context: RequestContext): Future[PropertyModelInternal] = {

    withMeasureAndLogWithContext(measurementCxt)(
      creationMeasurementName("property.PropertyModelInternalMapper.MapPropertyModelInternal")
    ) {

      val productTokenKey = reservedInfo.product.info.bapiBooking.productTokenKey
      val bookingId       = reservedInfo.bookingId.toInt
      val bapiBooking     = reservedInfo.product.info.bapiBooking
      val request         = createRequest.request
      val requestContext  = createRequest.requestContext
      val userId          = request.userId
      val chargeOption    = getChargeOption(request, bapiBooking)
      val bookingDateTime = dateTimeUtils.getCurrentDateTime
      val memberId        = Some(request.customer.memberId).filterNot(_ == noMemberId)
      val isMultiBooking  = reservedInfo.multiProductId.isDefined
      // ebe_itinerary
      val itinerary =
        PropertyItineraryBuilder(itineraryId, isMultiBooking, tripStart, tripEnd, memberId, userId, bookingDateTime)

      val specialRequest           = reservedInfo.product.info.specialRequest
      val isNHA                    = reservedInfo.product.info.bapiBooking.infoSummary.flatMap(_.isNHA).getOrElse(false)
      val childRoomInfo            = getChildRoomInfo(reservedInfo.product.info.bapiBooking)
      val masterRoomTypeId         = reservedInfo.product.info.bapiBooking.masterRooms.headOption.map(_.typeId.toInt)
      val greetingMessage          = reservedInfo.product.info.greetingMessage
      val childPromotions          = getChildPromotions(reservedInfo.product.info.bapiBooking)
      val fallbackToJapanChildRate = requestContext.featureAware.exists(_.fallbackToJapanChildRate) // JP-1518
      val capiToken = createRequest.requestContext.userContext
        .flatMap(_.capiToken)
        .orElse(createRequest.request.userContext.flatMap(_.capiToken))
      val isAgentAssistantBooking = capiToken
        .map(customerApiTokenUtils.isAgentAssistantBooking)
        .getOrElse(false)
      val countriesModelFuture = countriesRepository.getCountries()

      val internalBooking = for {
        countriesInfo   <- countriesModelFuture
        usdCurrencyInfo <- ebeLiteBookingRepository.getCurrencyInfo(CurrencyCode.USD)
        ebeHotelInfo    <- Future.fromTry(getEBEHotelInfo(reservedInfo.product.info.bapiBooking))
        ebeRoomInfo     <- Future.fromTry(getEBERoomInfo(ebeHotelInfo))
        dmcCode         <- getDmcCodeByDmcId(ebeRoomInfo.dmcId.getOrElse(0))
        hotelInfo       <- getHotelInfoByHotelId(ebeRoomInfo.hotelId)
        paymentModel <-
          Future.fromTry(getPaymentModel(ebeRoomInfo, request.payment, requestContext.whiteLabelInfo.whiteLabelId))
        availabilityType <- Future.fromTry(getAvailabilityType(ebeRoomInfo.availabilityType))
        gttCampaignPromo <- reservedInfo.product.info.bapiBooking.campaignInfo match {
                              case Some(campaignInfo) => cdbProxy.getPlecsCampaignPromocode(campaignInfo.campaignId)
                              case None               => Future.successful(None)
                            }

        bookingContext: BookingContext = getBookingContext(
                                           request,
                                           requestContext,
                                           itineraryId.toInt,
                                           bookingId,
                                           ebeHotelInfo,
                                           ebeRoomInfo,
                                           hotelInfo,
                                           dmcCode,
                                           bookingDateTime,
                                           bapiBooking,
                                           isAgentAssistantBooking
                                         )
        usdDigit = usdCurrencyInfo.map(_.noDecimal).getOrElse(throw new USDCurrencyInfoNotFound())

        paymentAmount = reservedInfo.product.info.bapiBooking.paymentAmount.getOrElse(throw new PaymentAmountNotFound())
        breakdowns = BookingItemMapper.buildChargeBreakDownItemWithRateChannel(
                       ebeRoomInfo.ycsRatePlanId,
                       ebeRoomInfo.pricing.charge,
                       ebeRoomInfo.exchange
                     )
        enrichBreakdowns =
          breakdowns.flatMap(
            enrichBreakdownMissingChargeDate(_, usdDigit, bookingContext.totalStayPeriod, bookingContext.checkInDate)
          )

        // mapping part
        bookingOccupancy           = request.getOccupancy(productTokenKey).map(PropertyBookingOccupancyBuilder(_))
        bookingHoldingPartnerNames = mapToBookingHoldingPartnerNames(request.bookingHoldingPartnerNames, bookingId)
        urlMapping <- urlService.getSelfServiceURLs(
                        whitelabelId = requestContext.whiteLabelInfo.whiteLabelId,
                        dmcId = ebeRoomInfo.dmcId
                      )
        additionalData = AdditionalBookingDataBuilder(
                           request,
                           reservedInfo.product.info,
                           childRoomInfo,
                           bookingOccupancy,
                           urlMapping(bookingId),
                           bookingHoldingPartnerNames
                         )

        // ebe_booking
        booking = PropertyBookingBuilder(
                    enrichBreakdowns,
                    ebeRoomInfo,
                    paymentModel,
                    availabilityType,
                    dmcCode,
                    bookingContext,
                    reservedInfo.product.info.bapiBooking.campaignInfo,
                    prebookingId,
                    childPromotions
                  )
        // ebe_booking_summary
        bookingSummary <- propertyBookingSummaryBuilder.createPropertyBookingSummary(
                            ebeRoomInfo,
                            paymentAmount,
                            reservedInfo.multiProductId,
                            enrichBreakdowns,
                            bookingContext,
                            reservedInfo.product.info.productPayment,
                            request.payment,
                            ebeHotelInfo,
                            request.isTestBooking,
                            paymentModel,
                            productTokenKey,
                            cancellationChargeItemId = cancellationChargeItemId
                          )
        // ebe_booking_agent
        bookingAgent = PropertyBookingAgentBuilder(bookingContext)
        // ebe_booking_attribution_v2
        bookingAttributions = PropertyBookingAttributionV2Builder(bookingContext)
        // ebe_booking_schedule
        bookingSchedule = mapToPropertyBookingSchedule(bookingContext)
        // ebe_booking_hotel
        bookingHotel = PropertyBookingHotelBuilder(
                         startIndex,
                         ebeHotelInfo,
                         ebeRoomInfo,
                         specialRequest,
                         isNHA,
                         greetingMessage,
                         request.getOccupancy(productTokenKey).getOrElse(Seq.empty),
                         bookingContext,
                         fallbackToJapanChildRate
                       )
        // ebe_booking_hotel_room
        bookingRoomList = mapToPropertyBookingHotelRoom(
                            startIndex,
                            enrichBreakdowns,
                            ebeRoomInfo,
                            request.getOccupancy(productTokenKey).getOrElse(Seq.empty),
                            childRoomInfo.flatMap(_.dynamicRoomMappingType),
                            masterRoomTypeId,
                            bookingContext: BookingContext
                          )
        // ebe_booking_hotel_room_children
        bookingRoomChildrenList = mapToPropertyBookingHotelRoomChildren(
                                    request.getOccupancy(productTokenKey).getOrElse(Seq.empty),
                                    bookingContext: BookingContext,
                                    childRoomInfo.flatMap(_.roomAllocationItemInfo),
                                    fallbackToJapanChildRate
                                  )
        // ebe_booking_pax
        bookingPax = request
                       .getGuestList(productTokenKey)
                       .map { guest =>
                         val roomIndex = guest.roomIndex match {
                           case Some(id) if id > 0 => id
                           case _                  => startIndex
                         }
                         PropertyBookingPaxBuilder(
                           roomIndex,
                           startIndex,
                           guest,
                           isHideSensitiveData,
                           bookingDateTime,
                           userId
                         )
                       }
        // ebe_financial_breakdown
        financialBreakdown = mapToPropertyFinancialBreakdown(
                               breakdowns = enrichBreakdowns,
                               bookingContext = bookingContext: BookingContext,
                               productIndex = productIndex,
                               masterActionId = masterActionId
                             )
        // ebe_booking_charges
        chargeBreakdown <- mapToPropertyBookingCharge(enrichBreakdowns, bookingContext, productIndex)
        dmcDueDate       = checkDmcDueDate(ebeRoomInfo.cancellation.code, bookingContext)
        // ebe_booking_provisioning
        bookingProvisioning = PropertyBookingProvisioningBuilder(
                                startIndex,
                                ebeRoomInfo,
                                enrichBreakdowns,
                                chargeBreakdown,
                                dmcDueDate,
                                bookingContext
                              )
        // ebe_booking_setting
        bookingSetting = PropertyBookingSettingBuilder(ebeRoomInfo, bookingContext)
        // ebe_booking_sell_info
        bookingSellInfo = PropertyBookingSellInfoBuilder(
                            ebeRoomInfo,
                            childRoomInfo.flatMap(_.priceOfferId),
                            bookingContext
                          )
        bookingSellInfoHistory = mapSellInfoHistory(bookingSellInfo)
        // ebe_booking_partner_loyalty_point
        bookingPartnerLoyalPoint =
          PropertyBookingPartnerLoyaltyPointBuilder(bookingContext, ebeRoomInfo)
        // ebe_booking_ratecategory
        rateCategory = PropertyBookingRateCategoryBuilder(
                         startIndex,
                         ebeRoomInfo,
                         childRoomInfo.flatMap(_.isRoomTypeNotGuarantee),
                         bookingContext
                       )
        // ebe_booking_ratecategory_benefit
        rateCategoryBenefit =
          rateCategory.toSeq.flatMap(rateCate =>
            mapToPropertyBookingRateCategoryBenefit(rateCate.referenceId, ebeRoomInfo, bookingContext)
          )
        // ebe_booking_earning_giftcard
        earnGiftCard = PropertyBookingEarningGiftCardBuilder(
                         ebeConfig.earnGiftCard.defaultOffsetDay,
                         bookingContext,
                         ebeRoomInfo
                       )
        // ebe_booking_earning_giftcard_info
        earnGiftCardInfo = PropertyBookingEarningGiftCardInfoBuilder(bookingContext, ebeRoomInfo)
        // ebe_booking_fraud_info
        fraudInfo = PropertyBookingFraudInfoBuilder(bookingContext)
        // ebe_booking_token
        bookingToken = PropertyBookingTokenBuilder.buildToken(bookingId).toOption
        // ebe_booking_supplier_data
        bookingSupplierData = PropertyBookingSupplierDataBuilder(
                                bookingContext,
                                request.supplierData,
                                childRoomInfo
                              )
        // ebe_booking_child_promotions
        childPromotionsList = mapToPropertyBookingChildPromotions(
                                childPromotions,
                                bookingContext: BookingContext
                              )
        earnReward = PropertyBookingEarningCashBackBuilder(bookingContext, ebeRoomInfo)

        /* TODO currently this campaign is only being used by gtt for mpbe booking, if no campaignId present
         * PropertyBookingCampaign object will not be populated */
        gttCampaignId = gttCampaignPromo.flatMap(campaignPromo => if (campaignPromo.isGTTCampaign) Some(7) else None)
        bookingCampaign = PropertyBookingCampaignBuilder(
                            bookingContext,
                            gttCampaignId,
                            if (gttCampaignId.isDefined) """{"PartnerToken":"","Last4digitsCitizenId":""}""" else ""
                          )
        bookingDiscountInfo = PropertyBookingDiscountInformationBuilder(
                                bookingContext,
                                ebeRoomInfo
                              )
        bookingConsumerFintech = PropertyBookingConsumerFintechBuilder(bookingContext, bapiBooking)
        serviceTaxCountryId = bapiBooking.consumerFintechDetails.flatMap { consumerFintechDetail =>
                                consumerFintechDetail.serviceTaxCountry.flatMap { taxCountry =>
                                  countriesInfo.find(_.countryISO2 == taxCountry).map(_.countryId)
                                }
                              }
        baseBookingEssInfos = reservedInfo.essInfoId.flatMap { essInfoId =>
                                BaseBookingEssInfoBuilder.getEssInfoByPropertyFinancialBreakdown(
                                  bookingEssInfoId = essInfoId,
                                  bookingId = bookingId,
                                  userTaxCountryOpt = createRequest.userTaxCountry,
                                  recCreatedBy = bookingContext.request.userId,
                                  propertyFinancialBreakdowns = financialBreakdown,
                                  userTaxCountryId = serviceTaxCountryId
                                )
                              }.toSeq
      } yield PropertyBookingModel(
        additionalData = additionalData,
        booking = booking,
        bookingAgent = bookingAgent,
        bookingSummary = bookingSummary,
        bookingAttributionV2 = bookingAttributions,
        bookingHotel = bookingHotel,
        bookingHotelRoom = bookingRoomList,
        bookingHotelRoomChildren = bookingRoomChildrenList,
        bookingSetting = bookingSetting,
        bookingToken = bookingToken,
        fraudInfo = Some(fraudInfo),
        financialBreakdown = financialBreakdown,
        bookingSucc = Seq.empty,
        charges = chargeBreakdown,
        sellInfo = Some(bookingSellInfo),
        sellInfoHistory = Seq(bookingSellInfoHistory),
        partnerLoyaltyPoint = bookingPartnerLoyalPoint,
        rateCategory = rateCategory,
        rateCategoryBenefit = rateCategoryBenefit,
        paymentsAdvanceInfo = Seq.empty,
        payment = Seq.empty,
        provisioning = Seq(bookingProvisioning),
        earningGiftCard = earnGiftCard,
        earningGiftCardInfo = earnGiftCardInfo,
        schedule = bookingSchedule,
        propertyBookingPax = bookingPax,
        fraudCcBlacklist = None,
        fraudCookieBlacklist = None,
        productKey = productTokenKey,
        bookingSupplierData = Some(bookingSupplierData),
        bookingChildPromotions = childPromotionsList,
        rewardEarning = earnReward,
        bookingCampaign = bookingCampaign,
        bookingDiscountInformations = bookingDiscountInfo,
        bookingConsumerFintech = bookingConsumerFintech,
        essInfos = baseBookingEssInfos,
        baseBooking = None
      )

      internalBooking.map { booking =>
        PropertyModelInternal(
          itinerary = itinerary,
          bookingList = Seq(booking)
        )
      }
    }
  }

  def getBookingContext(
      request: CreateBookingRequest,
      requestContext: RequestContext,
      itinerary: Int,
      bookingId: Int,
      ebeHotelInfo: EBEHotel,
      roomInfo: BookingRoom,
      hotelInfo: HotelInfo,
      dmcCode: String,
      bookingDateTime: DateTime,
      bapiBooking: BAPIBooking,
      isAgentAssistantBooking: Boolean
  ): BookingContext = {
    val agodaManagedYcs = roomInfo.dmcId.contains(DmcId.YCS.id) && !hotelInfo.ycsAllotmentNotManagedByAgoda
    val isAgencyPrePay =
      roomInfo.isPrepaymentRequired.exists(isPrePay => isPrePay && roomInfo.paymentModels == PaymentModel.Agency)
    BookingContext(
      request = request,
      requestContext = requestContext,
      bookingId = bookingId,
      itineraryId = itinerary,
      hotelId = roomInfo.hotelId,
      noOfAdults = roomInfo.noOfAdults,
      noOfChildren = roomInfo.noOfChildren,
      totalStayPeriod = Days.daysBetween(ebeHotelInfo.checkIn.toLocalDate, ebeHotelInfo.checkOut.toLocalDate).getDays,
      bookingDate = bookingDateTime,
      checkInDate = dateTimeUtils.toDateTimeWithoutMillis(ebeHotelInfo.checkIn),
      checkOutDate = dateTimeUtils.toDateTimeWithoutMillis(ebeHotelInfo.checkOut),
      isYCSMigration = hotelInfo.isYCSMigration,
      hotelGMTOffset = hotelInfo.GMTOffSet,
      dmcCode = dmcCode,
      chargeOption = getChargeOption(request, bapiBooking),
      isAgentAssist = request.agentAssist.exists(_.agentName.nonEmpty) || isAgentAssistantBooking,
      fullyAuthDate = bapiBooking.productPayment
        .flatMap(_.payLater.map(payLater => JodaToJavaDateTimeConversions.toDateTime(payLater.fullyAuthDate)))
        .getOrElse(request.getFullyAuthDate(bookingDateTime)),
      fullyChargeDate = bapiBooking.productPayment
        .flatMap(_.payLater.map(payLater => JodaToJavaDateTimeConversions.toDateTime(payLater.fullyChargeDate)))
        .getOrElse(request.getFullyChargeDate(bookingDateTime)),
      agodaManagedYcs = agodaManagedYcs,
      isAgencyPrePay = isAgencyPrePay
    )
  }

  def getPaymentModel(
      roomInfo: BookingRoom,
      payment: BookingPayment,
      whiteLabelId: WhiteLabel
  )(implicit context: RequestContext): Try[PaymentModel.Value] = {
    roomInfo.paymentModels match {
      case PaymentModel.Unknown => Failure(new PaymentModelNotFound())
      case PaymentModel.Agency =>
        Success(convertPaymentModelAgency(roomInfo = roomInfo, payment = payment, whiteLabelId = whiteLabelId))
      case paymentModel => Success(paymentModel)
    }
  }

  private def convertPaymentModelAgency(
      roomInfo: BookingRoom,
      payment: BookingPayment,
      whiteLabelId: WhiteLabel
  )(implicit context: RequestContext): PaymentModel.Value = {
    val paymentModel = roomInfo.paymentModels
    val isOTARurubuPaynow = propertyBookingSummaryBuilder.isOTARurubuPayNow(
      roomInfo = roomInfo,
      payment = payment,
      whitelabelId = whiteLabelId.id
    )
    if (paymentModel == PaymentModel.Agency && isOTARurubuPaynow)
      PaymentModel.Merchant
    else
      PaymentModel.Agency
  }

  def getAvailabilityType(input: AvailabilityType.Value): Try[AvailabilityType.Value] =
    if (input == AvailabilityType.Unknown) Failure(new AvailabilityTypeNotFound()) else Success(input)

  def getEBEHotelInfo(input: BAPIBooking): Try[EBEHotel] =
    Try {
      input.booking
        .flatMap(_.booking.headOption.flatMap(_.hotel.headOption))
        .getOrElse(throw new EBEHotelNotFound())
    }

  def getEBERoomInfo(input: EBEHotel): Try[BookingRoom] =
    Try {
      input.room.headOption.getOrElse(throw new EBEBookingRoomNotFound())
    }

  def getChildRoomInfo(input: BAPIBooking): Option[ChildRoom] =
    input.masterRooms.headOption.flatMap(_.childrenRooms.headOption)

  def getChildPromotions(input: BAPIBooking): List[ChildPromotion] =
    input.booking.flatMap(_.booking.headOption).flatMap(_.childPromotions).getOrElse(List.empty)

  private[ebe] def calculateDailyNet(amount: BigDecimal, totalBookingDay: Int, digit: Int): Seq[BigDecimal] =
    (for (_ <- 0 until totalBookingDay)
      yield (amount / totalBookingDay).setScale(digit, BigDecimal.RoundingMode.HALF_UP)) match {
      case init :+ _ => init :+ (amount - init.sum)
      case _         => Seq.empty
    }

  private[ebe] def enrichBreakdownMissingChargeDate(
      breakdown: BookingItemBreakdown,
      usdDigit: Int,
      totalStayPeriod: Int,
      checkInDate: DateTime
  ): Seq[BookingItemBreakdown] =
    if (
      breakdown.chargeDate.isEmpty && (
        BreakDownTypeID.getOption(breakdown.typeId.toInt).contains(BreakDownTypeID.RoomCharge) ||
          BreakDownTypeID.getOption(breakdown.typeId.toInt).contains(BreakDownTypeID.ExtraBed)
      )
    ) {
      val noOfNight  = totalStayPeriod
      val localDairy = calculateDailyNet(breakdown.localAmount, noOfNight, usdDigit)
      val usdDairy   = calculateDailyNet(breakdown.usdAmount, noOfNight, usdDigit)

      (localDairy zip usdDairy).zipWithIndex.map {
        case ((localAmount, usdAmount), index) =>
          breakdown.copy(
            chargeDate = Some(checkInDate.plusDays(index)),
            localAmount = localAmount,
            usdAmount = usdAmount
          )
      }
    } else Seq(breakdown)

  private[ebe] def getDmcCodeByDmcId(dmcId: Int): Future[String] =
    ebeLiteBookingRepository
      .getSupplierInfoByDmcId(dmcId)
      .map(_.map(_.code).getOrElse {
        logger.warn(s"No dmc code for dmc id: $dmcId")
        defaultDMCCode
      })
      .recover {
        case e =>
          logger.warn(s"Failed to retrieve dmc code for dmc id: $dmcId", e)
          defaultDMCCode
      }

  private[ebe] def getHotelInfoByHotelId(hotelId: Long): Future[HotelInfo] =
    ebeLiteBookingRepository.getHotelInfoByHotelId(hotelId).map(_.getOrElse(throw new HotelInfoNotFound()))

  private[ebe] def checkDmcDueDate(cxlCode: String, bookingContext: BookingContext): DateTime =
    if (cancellationUtils.isNonRefundable(cxlCode))
      bookingContext.bookingDate
    else {
      val cxlDueDate = cancellationUtils.getCancellationDueDate(cxlCode, bookingContext.checkInDate)
      val dmcDueDate =
        cancellationUtils.getDmcDueDate(cxlCode, bookingContext.checkInDate, cxlDueDate, bookingContext.bookingDate)
      toServerTimeFromHotelDateTime(
        localTime = dmcDueDate,
        hotelGMTOffset = bookingContext.hotelGMTOffset
      )
    }

  private def toServerTimeFromHotelDateTime(localTime: DateTime, hotelGMTOffset: Int): DateTime = {
    val timeZone =
      if (validGmtOffset(hotelGMTOffset, 0)) DateTimeZone.forOffsetHours(hotelGMTOffset) else DateTimeZone.getDefault
    localTime.withZoneRetainFields(timeZone).withZone(DateTimeZone.getDefault)
  }

  private[ebe] def mapSellInfoHistory(sellInfo: PropertyBookingSellInfo): PropertyBookingSellInfoHistory = {
    val defaultActionId = 1
    PropertyBookingSellInfoHistory(
      historyActionId = defaultActionId,
      historyActionDate = sellInfo.lastUpdatedWhen,
      historyActionBy = sellInfo.lastUpdatedBy,
      bookingId = sellInfo.bookingId,
      pricingTemplateId = sellInfo.pricingTemplateId,
      downliftAmountUsd = sellInfo.downLiftAmountUsd,
      sellTagId = sellInfo.sellTagId,
      lastUpdatedWhen = sellInfo.lastUpdatedWhen,
      searchId = sellInfo.searchId,
      isAdvanceGuarantee = sellInfo.isAdvanceGuarantee,
      lastUpdatedBy = sellInfo.lastUpdatedBy,
      bookingSellInfoHistoryId = None,
      firedrillContractId = sellInfo.firedrillContractId,
      firedrillContractTypeId = sellInfo.firedrillContractTypeId
    )
  }

  private[ebe] def mapToPropertyFinancialBreakdown(
      breakdowns: Seq[BookingItemBreakdown],
      bookingContext: BookingContext,
      productIndex: Option[Int],
      masterActionId: ActionId
  ): Seq[PropertyFinancialBreakdown] =
    breakdowns.zipWithIndex.map {
      case (breakdown, breakdownIndex) =>
        // Noted: ReferenceId should be unique if it's not the financial breakdown might not be inserted into database.
        PropertyFinancialBreakdownBuilder(
          referenceId = FinancialBreakdownUtils.referenceIdGenerator(
            productIndex,
            breakdownIndex,
            breakdown.roomNo
          ),
          bookingRoomReferenceId = breakdown.roomNo,
          bookingContext = bookingContext,
          bookingItemBreakdown = breakdown,
          masterActionId = masterActionId
        )
    }

  private[ebe] def mapToPropertyBookingHotelRoom(
      bookingHotelReferenceId: Long,
      breakdowns: Seq[BookingItemBreakdown],
      roomInfo: BookingRoom,
      occupancy: Seq[Occupancy],
      dynamicMappingTypeId: Option[Int],
      masterRoomTypeId: Option[Int],
      bookingContext: BookingContext
  ): Seq[PropertyBookingHotelRoom] =
    for (roomNo <- startIndex to roomInfo.numberOfRoom) yield {
      val isBreakfastInclude = roomInfo.isBreakfastIncluded
      val noOfExtraBed       = breakdowns.getNoOfExtraBed(roomNo)
      PropertyBookingHotelRoomBuilder(
        roomNo,
        bookingHotelReferenceId,
        breakdowns,
        roomInfo,
        noOfExtraBed,
        isBreakfastInclude,
        occupancy.find(_.roomNo == roomNo),
        dynamicMappingTypeId,
        masterRoomTypeId,
        bookingContext
      )
    }

  private[ebe] def mapToPropertyBookingHotelRoomChildren(
      occupancyList: Seq[Occupancy],
      bookingContext: BookingContext,
      roomAllocationInfoList: Option[Seq[RoomAllocationItemInfo]],
      fallbackToJapanChildRate: Boolean // JP-1518
  ): Seq[PropertyBookingHotelRoomChildren] = {
    roomAllocationInfoList match {
      case Some(info) if fallbackToJapanChildRate =>
        info.zipWithIndex.flatMap {
          case (roomAllocation, roomNo) =>
            roomAllocation.childrenTypes.map { childOcc =>
              PropertyBookingHotelRoomChildrenBuilder(
                roomNo + 1,
                ChildOccupancy(childOcc.childRateType, childOcc.quantity),
                bookingContext
              )
            }
        }
      case _ =>
        val mapRoomChildren = occupancyList.flatMap { occ =>
          occ.childOccupancy.map { childOccs =>
            childOccs.map { childOcc =>
              PropertyBookingHotelRoomChildrenBuilder(
                occ.roomNo,
                childOcc,
                bookingContext
              )
            }
          }
        }
        mapRoomChildren.flatten.filter(_.number > 0)
    }
  }

  private[ebe] def mapToPropertyBookingRateCategoryBenefit(
      rateCategoryReferenceId: Long,
      roomInfo: BookingRoom,
      bookingContext: BookingContext
  ): Seq[PropertyBookingRateCategoryBenefit] =
    Option(roomInfo.benefit).toSeq.flatMap { benefit =>
      val rateCategoryMode = rateCategoryReferenceId * modReferenceId
      benefit.zipWithIndex.map {
        case (benefit, index) =>
          PropertyBookingRateCategoryBenefitBuilder(
            startIndex + index + rateCategoryMode,
            rateCategoryReferenceId,
            benefit,
            bookingContext
          )
      }
    }

  private[ebe] def mapToPropertyBookingCharge(
      breakdownList: Seq[BookingItemBreakdown],
      bookingContext: BookingContext,
      productIndex: Option[Int]
  ): Future[Seq[PropertyBookingCharges]] =
    Future
      .sequence(
        chargeUtils.groupBreakdownItemsByChargeAttribute(breakdownList).map { breakdown =>
          ebeLiteBookingRepository
            .getSurchargeInfoByChargeId(breakdown.surchargeId.getOrElse(0))
            .recover { case _ => None }
            .map(surchargeInfo => (surchargeInfo, breakdown))
        }
      )
      .map {
        _.foldLeft(Seq.empty[PropertyBookingCharges]) {
          case (accChargeBreakdowns, (surchargeInfo, breakdown)) =>
            val index = accChargeBreakdowns.length + startIndex
            val result = BreakDownTypeID.get(breakdown.typeId.toInt) match {
              case BreakDownTypeID.Surcharge =>
                val chargeTypeId = surchargeInfo.map(_.chargeTypeId).getOrElse(0)
                val chargeName = getSurchargeDescription(
                  surchargeInfo,
                  bookingContext.isYCSMigration,
                  bookingContext.totalStayPeriod,
                  bookingContext.noOfAdults,
                  bookingContext.noOfChildren
                )
                chargeUtils.toChargeItem(
                  index,
                  bookingContext.bookingId,
                  bookingContext.request.userId,
                  breakdown,
                  breakdownList,
                  chargeTypeId,
                  chargeName,
                  bookingContext.bookingDate,
                  productIndex
                ) match {
                  case Success(value)     => Seq(value)
                  case Failure(exception) => throw exception
                }

              case BreakDownTypeID.Discount =>
                chargeUtils.getDiscountChargeItemList(
                  index,
                  bookingContext.bookingId,
                  bookingContext.request.userId,
                  breakdown,
                  breakdownList,
                  bookingContext.request.discountV1.promotionCode,
                  bookingContext.bookingDate,
                  productIndex
                ) match {
                  case Success(value)     => value
                  case Failure(exception) => throw exception
                }

              case _ =>
                chargeUtils.toChargeItem(
                  index,
                  bookingContext.bookingId,
                  bookingContext.request.userId,
                  breakdown,
                  breakdownList,
                  0,
                  "",
                  bookingContext.bookingDate,
                  productIndex
                ) match {
                  case Success(value)     => Seq(value)
                  case Failure(exception) => throw exception
                }
            }
            accChargeBreakdowns ++ result
        }
      }

  private[ebe] def getSurchargeDescription(
      surchargeInfo: Option[SurchargeInfoDB],
      isYCSMigrationHotel: Boolean,
      noOfNight: Int,
      noOfAdult: Int,
      noOfChildren: Int
  ): String =
    surchargeInfo
      .flatMap { surcharge =>
        val description = if (isYCSMigrationHotel) surcharge.descriptionYCS else surcharge.description
        description.map { value =>
          value
            .replace("[NIGHTS]", noOfNight.toString)
            .replace("[ADULTS]", noOfAdult.toString)
            .replace("[CHILDREN]", noOfChildren.toString)
        }
      }
      .getOrElse(BreakDownTypeID.Surcharge.toString)

  private[ebe] def mapToPropertyBookingSchedule(
      bookingContext: BookingContext
  ): Option[PropertyBookingSchedule] =
    if (ChargeOption.isBNPLOrBNPC(bookingContext.chargeOption)) {
      val secondFraudCheckDate = getSecondFraudCheckDate(
        bookingContext.fullyAuthDate,
        dateTimeUtils.getCurrentDateTime,
        ebeConfig.fraud.secondFraudCheckOffsetDay
      )
      Some(
        PropertyBookingScheduleBuilder(
          bookingContext,
          secondFraudCheckDate
        )
      )
    } else
      None

  private[ebe] def mapToPropertyBookingChildPromotions(
      childPromotions: List[ChildPromotion],
      bookingContext: BookingContext
  ): Seq[PropertyBookingChildPromotions] =
    childPromotions.map { promo =>
      PropertyBookingChildPromotionsBuilder(
        promo,
        bookingContext
      )
    }

  private[ebe] def getSecondFraudCheckDate(
      fullyAuthDateTime: DateTime,
      currentDateTime: DateTime,
      secondFraudCheckConfig: Int
  ): DateTime = {
    val secondFraudCheckDate = fullyAuthDateTime.toLocalDate.plusDays(-secondFraudCheckConfig)
    val currentDate          = currentDateTime.toLocalDate
    if (currentDate.isBefore(secondFraudCheckDate))
      secondFraudCheckDate.toDateTimeAtCurrentTime.withTimeAtStartOfDay().withHourOfDay(0)
    else currentDate.toDateTimeAtCurrentTime.withTimeAtStartOfDay().withHourOfDay(0)
  }

  private def getChargeOption(request: CreateBookingRequest, bapiBooking: BAPIBooking): ChargeOption.Value =
    bapiBooking.productPayment match {
      case Some(payment) if payment.agency.isDefined   => ChargeOption.PayNow
      case Some(payment) if payment.payNow.isDefined   => ChargeOption.PayNow
      case Some(payment) if payment.payLater.isDefined => ChargeOption.PayLater
      case _                                           => request.getChargeOption
    }

  private[ebe] def mapToBookingHoldingPartnerNames(
      partnerNames: Option[Seq[BookingHoldingPartner]],
      bookingId: Int
  ): Seq[PropertyBookingBookingHoldingPartnerName] = {
    partnerNames.getOrElse(Seq.empty).map { partnerName =>
      PropertyBookingBookingHoldingPartnerName(bookingId, partnerName.level, partnerName.id, partnerName.name)
    }
  }
}
