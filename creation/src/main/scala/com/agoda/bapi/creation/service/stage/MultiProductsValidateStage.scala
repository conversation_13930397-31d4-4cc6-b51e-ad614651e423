package com.agoda.bapi.creation.service.stage

import com.agoda.bapi.common.handler.MeasurementsContext
import com.agoda.bapi.common.message.CustomerRiskStatus
import com.agoda.bapi.common.message.CustomerRiskStatus.CustomerRiskStatus
import com.agoda.bapi.common.message.creation.CreateBookingResponse
import com.agoda.bapi.common.model.activity.ActivityBookingToken
import com.agoda.bapi.common.model.car.CarBookingToken
import com.agoda.bapi.common.model.flight.FlightBookingToken
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.mpb.common.models.state.ProductType
import com.agoda.bapi.common.model.product.BookingFlow.BookingFlow
import com.agoda.bapi.common.model.product.BookingRequestTypeResolver.MultiProductFlowDefinition
import com.agoda.bapi.common.util.CollectionUtils._
import com.agoda.bapi.creation.model._
import com.agoda.bapi.creation.model.multi.{MultiProductsRequest, ValidateProductRequest}
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.bapi.creation.service.flow.ProcessStage
import com.agoda.bapi.creation.service.{HadoopMessagingService, TPRMService}
import com.agoda.bapi.creation.util.{ToolSet, WhitelabelUtils}
import com.agoda.bapi.execution.context.NonBlockingExecutionContext.default
import com.agoda.mpb.common.errors.ErrorCode

import javax.inject.Inject
import scala.concurrent.Future

class MultiProductsValidateStage @Inject() (
    tprmService: TPRMService,
    hotelValidateStage: ProcessStage[ValidateProductRequest[RoomInfo], ValidateProductRequest[
      RoomInfo
    ], CreateBookingResponse],
    flightValidateStage: ProcessStage[ValidateProductRequest[FlightBookingToken], ValidateProductRequest[
      FlightBookingToken
    ], CreateBookingResponse],
    vehicleValidateStage: ProcessStage[ValidateProductRequest[CarBookingToken], ValidateProductRequest[
      CarBookingToken
    ], CreateBookingResponse],
    activityValidateStage: ProcessStage[ValidateProductRequest[ActivityBookingToken], ValidateProductRequest[
      ActivityBookingToken
    ], CreateBookingResponse],
    hadoopMessaging: HadoopMessagingService
) extends ProcessStage[RequestWithProductsNType, MultiProductsRequest, CreateBookingResponse]
    with ToolSet {

  val allowByPassingTPRMWhenAskForMoreInfo: List[BookingFlow] =
    List(
      BookingFlow.SingleProperty
    ) // this to keep behavior consistent with current flow

  override def process(
      request: RequestWithProductsNType
  ): Future[Either[CreateBookingResponse, MultiProductsRequest]] = {
    implicit val measurementCxt: MeasurementsContext      = request.measurementsContext
    implicit val mpDefinition: MultiProductFlowDefinition = buildMultiProductFlowDefinition(request)
    withMeasureAndLogWithContext(measurementCxt)(creationMeasurementName("ValidateStage")) {
      (for {
        customerTPRMCheckResult <- customerTerroristCheck(request)
      } yield customerTPRMCheckResult match {
        case Left(response) => Future.successful(Left(response))
        case Right(_) =>
          val hotelDuplicateF    = Future.traverse(request.toRoomsWithType)(hotelValidateStage.process)
          val flightDuplicateF   = Future.traverse(request.toFlightsWithType)(flightValidateStage.process)
          val vehicleDuplicateF  = Future.traverse(request.toVehiclesWithType)(vehicleValidateStage.process)
          val activityValidatedF = Future.traverse(request.toActivityWithType)(activityValidateStage.process)
          for {
            hotelDuplicate    <- hotelDuplicateF
            flightDuplicate   <- flightDuplicateF
            vehicleDuplicate  <- vehicleDuplicateF
            activityValidated <- activityValidatedF
          } yield for {
            _ <- hotelDuplicate.flattenRight.right
            _ <- flightDuplicate.flattenRight.right
            _ <- vehicleDuplicate.flattenRight.right
            _ <- activityValidated.flattenRight.right
          } yield request.toMultiProductsRequest()
      }).flatten.recover {
        case exception: Exception =>
          logError(exception)
          val recoveredResponse = CreateBookingResponse.technical(ErrorCode.UnexpectedInternalError, Some(exception))
          hadoopMessaging.sendBapiCreateFactLogMessage(request, recoveredResponse, "MultiProductValidateStage")
          Left(recoveredResponse)
      }
    }
  }

  private[stage] def customerTerroristCheck(
      request: RequestWithProductsNType
  )(implicit measurementCxt: MeasurementsContext): Future[Either[CreateBookingResponse, CustomerRiskStatus]] = {
    withMeasureAndLogWithContext(measurementCxt)(creationMeasurementName("ValidateStage")) {
      val requestContext = request.requestContext
      for {
        isTPRMCheckForWhitelabelFeatureEnabled <-
          Future.successful(WhitelabelUtils.isTPRMCheckEnabled(requestContext.whiteLabelInfo))

        tprmCheckResult <- if (isTPRMCheckForWhitelabelFeatureEnabled) {
                             tprmService.findCustomerTerrorists(request.request.customer)(requestContext).map {
                               case CustomerRiskStatus.Safe => Right(CustomerRiskStatus.Safe)
                               case CustomerRiskStatus.Unknown =>
                                 Right(
                                   CustomerRiskStatus.Safe
                                 ) // discussed with compliance and to align all produce when TPRM is unavailable
                               case CustomerRiskStatus.AskMoreInfo
                                   if allowByPassingTPRMWhenAskForMoreInfo.contains(request.bookingFlow) =>
                                 Right(CustomerRiskStatus.Safe)
                               case _ => Left(CreateBookingResponse.error(ErrorCode.TerroristIdentifiedInBooking))
                             }
                           } else {
                             Future.successful(Right(CustomerRiskStatus.Safe))
                           }
      } yield tprmCheckResult
    }
  }

  def buildMultiProductFlowDefinition(request: RequestWithProductsNType): MultiProductFlowDefinition = {
    val products = request.products
    MultiProductFlowDefinition(
      numberOfProperty = products.properties.size,
      numberOfFlight = products.flights.size,
      numberOfCar = products.vehicles.size,
      numberOfActivity = products.activities.size,
      numberOfTripProtection =
        products.protections.size + products.addOns.count( // TODO: Refactor with TPMigration Cleanup
          _.getProductType == ProductType.TripProtection
        ),
      numberOfAddOns = products.addOns.size
    )
  }
}
