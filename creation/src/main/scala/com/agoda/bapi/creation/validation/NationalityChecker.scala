package com.agoda.bapi.creation.validation

import com.agoda.bapi.common.message.creation.{FlightPax, HotelGuest}
import com.agoda.bapi.creation.model.RequestWithProducts
import com.agoda.bapi.creation.repository.EbeLiteBookingRepository
import javax.inject.{Inject, Singleton}

import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpb.common.errors.ErrorCode.ErrorCode

import scala.collection.mutable
import scala.concurrent.{ExecutionContext, Future}

trait NationalityChecker {
  def validate(request: RequestWithProducts)(implicit executionContext: ExecutionContext): Future[List[ErrorCode]]
}

@Singleton
case class NationalityCheckerImpl @Inject() (ebeLiteBookingRepository: EbeLiteBookingRepository)
    extends NationalityChecker {

  val RESTRICTED_COUNTRY_ID: Int = -1

  override def validate(
      request: RequestWithProducts
  )(implicit executionContext: ExecutionContext): Future[List[ErrorCode]] = {

    val roomsValidation =
      if (request.rooms.nonEmpty)
        validateRooms(request)
      else
        Future.successful(List.empty)

    val flightsValidation =
      if (request.flights.nonEmpty)
        validateFlights(request)
      else
        Future.successful(List.empty)

    Future.sequence(List(roomsValidation, flightsValidation)).map(_.flatten)
  }

  private[validation] def validateRooms(
      request: RequestWithProducts
  )(implicit executionContext: ExecutionContext): Future[List[ErrorCode]] = {
    val guestList: List[HotelGuest]      = request.request.guestList.toList.distinct
    val primaryGuest: Option[HotelGuest] = request.request.guestList.find(_.primary)
    val hotelIds: List[Long]             = request.rooms.map { case (_, value) => value.bapiBooking.propertyId }.toList.distinct

    if (primaryGuest.exists(_.nationalityId != 0)) {
      val guestNationalityRestrictionFut: Future[Option[ErrorCode]] =
        Future
          .sequence(
            guestList.map(validateGuestNationality(_))
          )
          .map(_.collectFirst { case Some(v) => v })

      val guestNationalityRestrictedByHotelFut: Future[Option[ErrorCode]] = {
        val combinations = for {
          g <- guestList
          h <- hotelIds
        } yield (g, h)

        Future
          .sequence(
            combinations.map(pair => validateGuestNationalityByHotel(pair._1, pair._2.toInt))
          )
          .map(_.collectFirst { case Some(v) => v })
      }

      val hotelCountryRestrictionFut: Future[Option[ErrorCode]] =
        Future
          .sequence(
            hotelIds.map(id => validateHotelCountry(id.toInt))
          )
          .map(_.collectFirst { case Some(v) => v })

      for {
        r1 <- guestNationalityRestrictionFut
        r2 <- guestNationalityRestrictedByHotelFut
        r3 <- hotelCountryRestrictionFut
        r   = List(r1, r2, r3).flatten
      } yield r

    } else Future.successful(Nil)
  }

  private[validation] def validateGuestNationality(
      guest: HotelGuest
  )(implicit executionContext: ExecutionContext): Future[Option[ErrorCode]] = {

    if (guest.nationalityId == 0)
      Future.successful(Some(ErrorCode.InvalidNationalityId))
    else
      ebeLiteBookingRepository.getRestrictedNationality(guest.nationalityId).map[Option[ErrorCode]] {
        case Some(value) =>
          if (value == -2) // Nationality is restricted, 725
            Some(ErrorCode.NationalityIsRestricted)
          else
            None
        case None => // Guest nationality restriction, 447
          Some(ErrorCode.RestrictedGuestNationality)
      }
  }

  private[validation] def validateGuestNationalityByHotel(guest: HotelGuest, hotelId: Int)(implicit
      executionContext: ExecutionContext
  ): Future[Option[ErrorCode]] = {
    if (guest.nationalityId == 0)
      Future.successful(Some(ErrorCode.InvalidNationalityId))
    else
      ebeLiteBookingRepository
        .getHotelRestrictedNationality(hotelId, guest.nationalityId)
        .map(
          _.flatMap(result =>
            if (result)
              Some(ErrorCode.RestrictedHotelNationality)
            else None
          )
        )
  }

  private[validation] def validateHotelCountry(
      hotelId: Int
  )(implicit executionContext: ExecutionContext): Future[Option[ErrorCode]] = {
    // Original validation logic commented out to bypass "Hotel is in restricted country" error
    /* ebeLiteBookingRepository .getHotelCountryIdWithRecStatus(hotelId) .map[Option[ErrorCode]] { countryOp => if
     * (countryOp.contains(RESTRICTED_COUNTRY_ID)) { Some(ErrorCode.HotelIsInRestrictedCountry) } else { None } } */
    Future.successful(None)
  }

  private[validation] def validateFlights(
      request: RequestWithProducts
  )(implicit executionContext: ExecutionContext): Future[List[ErrorCode]] = {

    val paxList: List[FlightPax] =
      request.request.paxList.toList // .distinct //@TODO: Not sure that "distinct" worth it
    val primaryPax: Option[FlightPax] = paxList.find(_.primary)

    val airports = request.flights.flatMap { tokens =>
      val airportSet: mutable.Set[String] = mutable.Set()
      tokens.info.map { flight =>
        flight.slices.flatMap { slice =>
          slice.segments.flatMap { segment =>
            airportSet += (segment.origin, segment.destination)
          }
        }
      }
      airportSet.toSet
    }

    if (primaryPax.exists(_.nationalityId != 0)) {
      val paxNationalityRestrictionFut: Future[Option[ErrorCode]] =
        Future
          .sequence(
            paxList.map(validatePaxNationality(_))
          )
          .map(_.collectFirst { case Some(v) => v })

      val paxNationalityRestrictedByAirportFut: Future[Option[ErrorCode]] = {
        val combinations = for {
          p <- paxList
          a <- airports
        } yield (p, a)

        Future
          .sequence(
            combinations.map(pair => validatePaxNationalityByAirport(pair._1, pair._2))
          )
          .map(_.collectFirst { case Some(v) => v })
      }

      val airportCountryRestrictionFut: Future[Option[ErrorCode]] =
        Future
          .sequence(airports.map(airportCode => validateAirportCountry(airportCode)))
          .map(_.collectFirst { case Some(v) => v })

      for {
        r1 <- paxNationalityRestrictionFut
        r2 <- paxNationalityRestrictedByAirportFut
        r3 <- airportCountryRestrictionFut
        r   = List(r1, r2, r3).flatten
      } yield r

    } else Future.successful(Nil)
  }

  /**
    * Validate pax nationality (for example, Agoda do not allow to book flights for reptiloids)
    */
  private[validation] def validatePaxNationality(
      pax: FlightPax
  )(implicit executionContext: ExecutionContext): Future[Option[ErrorCode]] = {

    if (pax.nationalityId == 0)
      Future.successful(Some(ErrorCode.InvalidNationalityId))
    else
      ebeLiteBookingRepository.getRestrictedNationality(pax.nationalityId).map {
        case Some(value) =>
          if (value == -2) // Nationality is restricted, 725
            Some(ErrorCode.NationalityIsRestricted)
          else
            None
        case None => // Pax nationality restriction, 474
          Some(ErrorCode.RestrictedPaxNationality)
      }
  }

  /**
    * Validate pax nationality by origin/destination
    */
  private[validation] def validatePaxNationalityByAirport(pax: FlightPax, airportCode: String)(implicit
      executionContext: ExecutionContext
  ): Future[Option[ErrorCode]] = {

    if (pax.nationalityId == 0)
      Future.successful(Some(ErrorCode.InvalidNationalityId))
    else
      ebeLiteBookingRepository.getAirportRestrictedNationality(airportCode, pax.nationalityId).map {
        case Some(restriction) if restriction => Some(ErrorCode.RestrictedAirportNationality)
        case _                                => None
      }
  }

  private[validation] def validateAirportCountry(
      airportCode: String
  )(implicit executionContext: ExecutionContext): Future[Option[ErrorCode]] = {

    ebeLiteBookingRepository.getAirportCountryId(airportCode).map {
      case Some(value) if value == 179 || value == 224 || value == 331 =>
        Some(ErrorCode.AirportIsInRestrictedCountry)
      case _ => None
    }
  }
}
