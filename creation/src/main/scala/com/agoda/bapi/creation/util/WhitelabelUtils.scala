package com.agoda.bapi.creation.util

import com.agoda.bapi.Types.DmcId
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.model.WhiteLabelInfo
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.util.CommonWhitelabelUtils
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import generated.model.DmcControlSettingModel

import scala.util.Try

object WhitelabelUtils {

  def isRetrieveCcInfoAfterFraudWithValidatePaymentMethod(paymentMethod: PaymentMethod)(implicit
      whiteLabelInfo: Option[WhiteLabelInfo]
  ): Boolean = {
    isRetrieveCcInfoAfterFraud && paymentMethod == PaymentMethod.None
  }

  def isRetrieveCcInfoAfterFraud()(implicit
      whiteLabelInfo: Option[WhiteLabelInfo]
  ): Boolean = {
    whiteLabelInfo.flatMap(_.feature.mpbe.isRetrieveCcInfoAfterFraud).getOrElse(false)
  }

  def isSkipValidate3DS()(implicit
      whiteLabelInfo: WhiteLabelInfo
  ): Boolean = {
    whiteLabelInfo.feature.mpbe.isSkipValidate3DS.getOrElse(false)
  }

  def isSkipFilterCCOFByPaymentMethod()(implicit
      whiteLabelInfo: WhiteLabelInfo
  ): Boolean = {
    whiteLabelInfo.feature.mpbe.isSkipFilterCCOFByPaymentMethod.getOrElse(false)
  }

  def isUseUpi()(implicit
      whiteLabelInfo: WhiteLabelInfo
  ): Boolean = whiteLabelInfo.feature.mpbe.isUseUPI.getOrElse(true)

  def hideAvailablePaymentMethods()(
      whiteLabelInfo: WhiteLabelInfo
  ): Boolean = {
    whiteLabelInfo.feature.mpbe.hideAvailablePaymentMethods.getOrElse(false)
  }

  def isForceTestBooking()(implicit
      whiteLabelInfo: WhiteLabelInfo
  ): Boolean = {
    whiteLabelInfo.feature.mpbe.isForceTestBooking.getOrElse(false)
  }

  def isGetMemberLoyaltyProfile()(
      whiteLabelInfo: WhiteLabelInfo
  ): Boolean = {
    whiteLabelInfo.feature.mpbe.isGetMemberLoyaltyProfile.getOrElse(false)
  }

  def isInstallmentEligibleForInstallmentGrid()(implicit
      whiteLabelInfo: WhiteLabelInfo
  ): Boolean = {
    whiteLabelInfo.feature.mpbe.isInstallmentEligibleForInstallmentGrid.getOrElse(false)
  }

  def hasMinimumPointsBalanceRequirement(whiteLabelInfo: WhiteLabelInfo): Boolean =
    whiteLabelInfo.feature.partnerLoyaltyConfiguration.pointsRequirements
      .flatMap(_.hasMinimumBalance)
      .getOrElse(false)

  def hasUserBalanceValidationWithTotalItemPriceInPoints(whiteLabelInfo: WhiteLabelInfo): Boolean =
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.UserBalanceValidationWithTotalItemPriceInPoints)

  def isPartialSuccessAllowedForWL(whiteLabelInfo: WhiteLabelInfo): Boolean =
    whiteLabelInfo.feature.mpbe.isPartialSuccessAllowed.getOrElse(false)

  def isInternationalAgencyBookingEnabled(whiteLabelInfo: WhiteLabelInfo): Boolean = {
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.InternationalAgencyBooking)
  }
  def isMinimumPointsRedemptionValidation(whiteLabelInfo: WhiteLabelInfo): Boolean = {
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.MinimumPointsRedemptionValidation)
  }

  def isTPRMCheckEnabled(whiteLabelInfo: WhiteLabelInfo): Boolean = {
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.TPRMCheck)
  }

  def instantBookingEnabled(whiteLabelInfo: WhiteLabelInfo): Boolean = {
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.InstantBooking)
  }

  def isKeylessEarnEnabled(whiteLabelInfo: WhiteLabelInfo): Boolean = {
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.KeylessEarn)
  }

  def useHybridPriceBreakDown(requestContext: RequestContext): Boolean = {
    val whiteLabelInfo = requestContext.whiteLabelInfo
    whiteLabelInfo.isFeatureEnabled(
      featureName = WhiteLabelFeatureName.UseHybridPriceBreakDown,
      isBVariant = expName => requestContext.featureAware.exists(_.isBVariant(expName))
    )
  }

  def isMemberValidationOnCreateEndpointEnabled(requestContext: RequestContext): Boolean = {
    val whiteLabelInfo = requestContext.whiteLabelInfo
    whiteLabelInfo.isFeatureEnabled(
      featureName = WhiteLabelFeatureName.MemberValidationCheckOnCreateBookingEndpoint,
      isBVariant = expName => requestContext.featureAware.exists(_.isBVariant(expName))
    )
  }

  def isActivityPaymentPointValidationEnabled(whiteLabelInfo: WhiteLabelInfo): Boolean =
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.ActivityPaymentPointValidation)

  def isJapanGovernmentCampaignEnabled(whiteLabelInfo: WhiteLabelInfo): Boolean =
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.JapanGovernmentCampaign)

  def isHidePromoBoxWhenNoCouponAvailableEnabled(whiteLabelInfo: WhiteLabelInfo): Boolean =
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.HidePromoBoxWhenNoCouponAvailable)

  def isRegulationDisableShowBookingFeePriceBreakdownEnabled(requestContext: RequestContext): Boolean = {
    val whiteLabelInfo = requestContext.whiteLabelInfo
    val origin         = requestContext.userContext.map(_.requestOrigin)
    val deviceTypeId   = requestContext.experimentData.flatMap(expData => Try(expData.deviceTypeId.toInt).toOption)

    whiteLabelInfo.isFeatureEnabled(
      WhiteLabelFeatureName.RegulationDisableShowBookingFeePriceBreakdown,
      origin,
      deviceTypeId
    )
  }

  def isRegulationShowExclusivePriceWithFeeEnabled(requestContext: RequestContext): Boolean = {
    val whiteLabelInfo = requestContext.whiteLabelInfo
    val origin         = requestContext.userContext.map(_.requestOrigin)
    val deviceTypeId   = requestContext.experimentData.flatMap(expData => Try(expData.deviceTypeId.toInt).toOption)

    whiteLabelInfo.isFeatureEnabled(
      WhiteLabelFeatureName.RegulationShowExclusivePriceWithFee,
      origin,
      deviceTypeId
    )
  }

  private[util] def isPartnerLoyaltyBurnEnabled(whiteLabelInfo: WhiteLabelInfo) =
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.PartnerLoyaltyBurn)

  private[util] def isPartnerLoyaltyEarnEnabled(whiteLabelInfo: WhiteLabelInfo) =
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.PartnerLoyaltyEarn)

  private[util] def isPartnerLoyaltyEarnWithBurnEnabled(whiteLabelInfo: WhiteLabelInfo) =
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.PartnerLoyaltyEarnWithBurn)

  def isExternalLoyaltyWhiteLabel(whiteLabelInfo: WhiteLabelInfo): Boolean =
    isPartnerLoyaltyBurnEnabled(whiteLabelInfo) ||
      isPartnerLoyaltyEarnEnabled(whiteLabelInfo) ||
      isPartnerLoyaltyEarnWithBurnEnabled(whiteLabelInfo)

  def isExternalLoyaltyBalanceEnabled(whiteLabelInfo: WhiteLabelInfo): Boolean =
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.ExternalLoyaltyMemberBalance)

  def isCashbackRedemptionEnabled(
      whiteLabelInfo: WhiteLabelInfo,
      memberId: Option[Int] = None,
      capiToken: Option[String] = None
  ): Boolean =
    memberId.getOrElse(0) > 0 && capiToken.exists(_.trim.nonEmpty) && whiteLabelInfo.isFeatureEnabled(
      WhiteLabelFeatureName.CashbackRedemption
    )

  def isPartnerClaimExchangeEnabled(whiteLabelInfo: WhiteLabelInfo): Boolean = {
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.PartnerClaimExchange)
  }

  def isCampaignInfoCheckEnabled(whiteLabelInfo: WhiteLabelInfo): Boolean = {
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.CampaignInfoCheck)
  }

  def matchedRoomIdentifiersCheck(whiteLabelInfo: WhiteLabelInfo): Boolean = {
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.MatchedRoomIdentifiersCheck)
  }

  def overridePointsOfferType(whiteLabelInfo: WhiteLabelInfo): Boolean =
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.OverridePointsOfferType)

  def isFlightFacilitationFeeEnabled(whiteLabelInfo: WhiteLabelInfo): Boolean =
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.FlightsFacilitationFee)

  def isCCOFFeatureDisabledForWhiteLabel(whiteLabelInfo: WhiteLabelInfo): Boolean =
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.DisableSavedCard)

  def isJtbWl(whiteLabelInfo: WhiteLabelInfo): Boolean =
    CommonWhitelabelUtils.isJtbWl(whiteLabelInfo)

  def isJapanicanWl(whiteLabelInfo: WhiteLabelInfo): Boolean =
    CommonWhitelabelUtils.isJapanicanWl(whiteLabelInfo)

  def isRurubuWl(whiteLabelInfo: WhiteLabelInfo): Boolean =
    CommonWhitelabelUtils.isRurubuWl(whiteLabelInfo)

  def isJtbSupplyWl(whiteLabelInfo: WhiteLabelInfo): Boolean =
    CommonWhitelabelUtils.isJtbSupplyWl(whiteLabelInfo)

  def getDmcControlSetting(
      whiteLabelInfo: WhiteLabelInfo,
      dmcId: Option[DmcId] = None
  ): Option[DmcControlSettingModel] =
    CommonWhitelabelUtils.getDmcControlSetting(whiteLabelInfo, dmcId)

  def isJtbDmcBooking(whiteLabelInfo: WhiteLabelInfo, dmcId: Option[DmcId] = None): Boolean =
    CommonWhitelabelUtils.isJtbDmcBooking(whiteLabelInfo, dmcId)

  def fullBurnBooking(whiteLabelInfo: WhiteLabelInfo): Boolean =
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.FullBurnBooking)

  def allowNoCreditCardBooking(whiteLabelInfo: WhiteLabelInfo): Boolean =
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.AllowNoCreditCardBooking)

  def useSingleFlightPriceInCart(whiteLabelInfo: WhiteLabelInfo): Boolean =
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.SingleFlightPriceBDInCartBooking)

  def isDirectPartnersEnabled(requestContext: RequestContext): Boolean = {
    val whiteLabelInfo = requestContext.whiteLabelInfo
    val deviceTypeId   = requestContext.experimentData.flatMap(expData => Try(expData.deviceTypeId.toInt).toOption)
    whiteLabelInfo.isFeatureEnabled(
      featureName = WhiteLabelFeatureName.DirectPartners,
      deviceTypeId = deviceTypeId
    )
  }

  def hideBookingPIIHadoop(requestContext: RequestContext): Boolean = {
    val whiteLabelInfo = requestContext.whiteLabelInfo
    whiteLabelInfo.isFeatureEnabled(featureName = WhiteLabelFeatureName.HideBookingPIIHadoop)
  }

  def isExternalLoyaltyPointsErrorV2(requestContext: RequestContext): Boolean = {
    val whiteLabelInfo = requestContext.whiteLabelInfo
    whiteLabelInfo.isFeatureEnabled(
      featureName = WhiteLabelFeatureName.ExternalLoyaltyPointsErrorV2
    )
  }

  def enableMemberIdForBookingCreation(whiteLabelInfo: WhiteLabelInfo): Boolean = {
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.EnableMemberIdForBookingCreation)
  }

  def doNotOverrideEmailForWhiteLabels(requestContext: RequestContext): Boolean = {
    val whiteLabelInfo = requestContext.whiteLabelInfo
    whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.DoNotOverrideCustomerEmailWhiteLabel)
  }
}
