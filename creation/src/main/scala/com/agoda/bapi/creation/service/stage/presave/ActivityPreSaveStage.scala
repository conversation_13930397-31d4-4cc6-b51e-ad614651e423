package com.agoda.bapi.creation.service.stage.presave

import com.agoda.bapi.common.handler.{MeasurementsContext, RequestContext}
import com.agoda.bapi.common.message.creation.{ActivityBookingAnswer, ActivityPax, CreateBookingResponse, CreditCard, Customer}
import com.agoda.bapi.common.model.BasePaxId
import com.agoda.bapi.common.model.activity.ActivityBookingToken
import com.agoda.bapi.common.model.product.ProductTypeEnum
import com.agoda.bapi.common.model.product.ProductTypeEnum.ProductTypeEnum
import com.agoda.bapi.creation.mapper.activity.{ActivityConstants, ActivityMapper}
import com.agoda.bapi.creation.model.CreateRequest
import com.agoda.bapi.creation.model.multi._
import com.agoda.bapi.creation.repository.{ActivityBookingRepository, BaseBookingRepository, FlightBookingRepository}
import org.joda.time.DateTime

import javax.inject.Inject
import scala.concurrent.{ExecutionContext, Future}

class ActivityPreSaveStage @Inject() (
    activityBookingRepository: ActivityBookingRepository,
    baseBookingRepository: BaseBookingRepository,
    val workflowRepository: FlightBookingRepository,
    activityMapper: ActivityMapper
)(implicit val executionContext: ExecutionContext)
    extends ProductPreSaveStage[
      MultiProductPreSaveRequest,
      ActivityBookingToken,
      NonPropertySaveStageResponse,
      EmptyProductReservedIds
    ]
    with NonHotelPreSaveStage[ActivityBookingToken] {
  override def productType(
      preSaveProductStageRequest: PreSaveProductStageRequest[ActivityBookingToken]
  ): ProductTypeEnum = ProductTypeEnum.Activity

  override protected def toPreSaveProductStageRequests(
      preSaveRequest: MultiProductPreSaveRequest
  ): Seq[PreSaveProductStageRequest[ActivityBookingToken]] =
    preSaveRequest.multiProductsRequest.activities.map(createPreSaveProductStageRequest(preSaveRequest, _))

  override protected def preSave(
      request: PreSaveProductStageRequest[ActivityBookingToken]
  )(implicit
      measurementCxt: MeasurementsContext,
      context: RequestContext
  ): Future[Either[CreateBookingResponse, NonPropertySaveStageResponse]] = {
    val answersForBillingInfo = getAnswersForBillingInfo(request)
    for {
      reservedIds <- measuredReservedProductId(request)
      paxList      = request.request.getActivityPaxList(request.product.info.productTokenKey)
      paxIdList <- Future.sequence(paxList.map { _ =>
                     baseBookingRepository.getNextBaseBookingPaxSequenceNumber
                   })
      activityPaxWithIds = getPaxListWithId(paxList, paxIdList)
      _ <- saveDataToEnigma(
             bookingId = reservedIds.bookingId,
             paxes = activityPaxWithIds,
             customer = request.getCustomer,
             contactInfoAnswer = answersForBillingInfo,
             creditcard = request.request.payment.creditCard,
             measurementsContext = request.measurementsContext
           )
    } yield {
      val activityProductModel = activityMapper.toProductModel(
        request = request,
        activityPaxes = activityPaxWithIds,
        itineraryId = request.itineraryInfo.itineraryId,
        masterActionId = request.itineraryInfo.actionId,
        reservedIds = reservedIds,
        userTaxCountryId = request.userTaxCountryId,
        userTaxCountry = request.userTaxCountry,
        promotionInfo = request.product.info.promotionInfo,
        productTokenKey = request.product.info.productTokenKey
      )
      val bookingActionState = activityMapper.toBookingActionState(
        request = request,
        itineraryId = request.itineraryInfo.itineraryId,
        reservedIds = reservedIds,
        productModel = activityProductModel,
        bookingDateTime = DateTime.now()
      )
      val bookingAction =
        activityMapper.toBookingAction(
          request = request,
          itineraryId = request.itineraryInfo.itineraryId,
          reservedIds = reservedIds,
          bookingActionState = bookingActionState
        )
      Right(
        NonPropertySaveStageResponse(
          productType(request),
          bookingAction,
          bookingActionState,
          productTokenKey = request.product.info.productTokenKey
        )
      )
    }
  }

  override protected def reservedProductId(
      request: PreSaveProductStageRequest[ActivityBookingToken]
  )(implicit measurementCxt: MeasurementsContext): Future[ReservedIds[ActivityBookingToken, EmptyProductReservedIds]] =
    for {
      bookingId <- baseBookingRepository.getNextBookingSequenceNumber
      actionId  <- workflowRepository.getNextActionIdSequenceNumber
      offerId   <- baseBookingRepository.getNextBaseBookingOfferSequenceNumber
      metaId    <- baseBookingRepository.getNextBaseBookingMetaSequenceNumber
      geoId     <- baseBookingRepository.getNextBaseBookingGeoSequenceNumber
      essInfoId <- baseBookingRepository.getNextBaseBookingEssInfoSequenceNumber
      breakdownIds <- workflowRepository.getNextBreakdownIdSequenceNumbers(
                        request.product.info.activityInfo.priceBreakdown.size
                      )
    } yield ReservedIds(
      bookingId,
      actionId,
      multiProductId = request.multiProductId.map(_.multiProductId),
      product = request.product,
      offerId = Option(offerId),
      metaId = Option(metaId),
      geoId = Option(geoId),
      essInfoId = Option(essInfoId),
      breakdownIds = breakdownIds
    )

  private def enrichPax(paxes: ActivityPax, customer: Customer): ActivityPax = paxes match {
    case pax if pax.primary =>
      pax.copy(
        email = Some(pax.email.getOrElse(customer.email)),
        phoneNumber = Some(pax.phoneNumber.getOrElse(customer.phoneFormat)),
        phoneCountryCode = pax.phoneCountryCode
      )
    case pax => pax
  }

  private[presave] def saveDataToEnigma(
      bookingId: Long,
      paxes: Seq[ActivityPax],
      customer: Customer,
      contactInfoAnswer: Seq[ActivityBookingAnswer],
      creditcard: Option[CreditCard],
      measurementsContext: MeasurementsContext
  )(implicit requestContext: RequestContext): Future[_] = {
    implicit val measurementCxt: MeasurementsContext = measurementsContext
    withMeasureAndLogWithContext(measurementCxt)(creationMeasurementName("activity.SaveDataToEnigma")) {
      // TODO Currently mapping email/phone from Customer (until ABE-703/ABE-715)
      activityBookingRepository.savePaxToEnigma(bookingId, paxes.map(p => enrichPax(p, customer)))
      activityBookingRepository.saveCustomerBillingInfoToEnigma(bookingId, customer, contactInfoAnswer, creditcard)
    }
  }

  private[presave] def getPaxListWithId(
      paxList: Seq[ActivityPax],
      paxIdList: Seq[BasePaxId]
  ): Seq[ActivityPax] = paxList.zip(paxIdList).map(p => p._1.copy(id = p._2))

  private def getAnswersForBillingInfo(request: CreateRequest): Seq[ActivityBookingAnswer] = {
    val nonPaxBookingAnswers = request.request.products.activitiesItems
      .map(_.flatMap(_.bookingAnswers.getOrElse(Seq.empty)))
      .getOrElse(Seq.empty)
    nonPaxBookingAnswers.filter(q => ActivityConstants.QuestionsRequiredForBillingInfo.contains(q.bookingQuestionCode))
  }
}
