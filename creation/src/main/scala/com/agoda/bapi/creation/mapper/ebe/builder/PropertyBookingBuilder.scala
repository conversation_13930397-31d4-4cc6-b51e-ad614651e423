package com.agoda.bapi.creation.mapper.ebe.builder

import java.util.UUID
import com.agoda.bapi.common.message.creation.{AgentAssist, PrebookingId}
import com.agoda.bapi.common.model.creation.{AvailabilityType, BookingItemBreakdown, BookingItemBreakdownCalculator, BookingRoom, ChildPromotion, PaymentModel, PropertyCampaignInfo}
import com.agoda.bapi.common.model.property.PropertyBookingStateModel
import com.agoda.bapi.common.model.{ChargeOption, EbeTimeWindow, EbeWorkflowId, EbeWorkflowState}
import com.agoda.bapi.common.util.{JodaDateTimeUtils, ServerUtils}
import com.agoda.bapi.creation.mapper.ebe._
import com.agoda.mpb.common.CCReceived
import org.joda.time.{DateTime, Days}

object PropertyBookingBuilder {
  def apply(
      breakdowns: Seq[BookingItemBreakdown],
      roomInfo: BookingRoom,
      paymentModel: PaymentModel.Value,
      availabilityType: AvailabilityType.Value,
      dmcCode: String,
      bookingContext: BookingContext,
      campaignInfo: Option[PropertyCampaignInfo],
      prebookingId: PrebookingId,
      childPromotions: Seq[ChildPromotion]
  ): PropertyBookingStateModel.PropertyBooking = {

    val discountSaving = breakdowns.calculateDiscountSaving
    val feCid          = bookingContext.request.attributionsV2.find(_.modelId == AttributionModelId.FESession).map(_.siteId)

    PropertyBookingStateModel.PropertyBooking(
      bookingId = bookingContext.bookingId,
      itineraryId = Some(bookingContext.itineraryId),
      dataCenter = Some(ServerUtils.serverDc()),
      preBookingIdOld = None,
      bookingDate = Some(bookingContext.bookingDate),
      bookingDateFrom = Some(bookingContext.checkInDate),
      bookingDateUntil = Some(bookingContext.checkOutDate),
      paymentModel = Some(paymentModel.id),
      rewardsSpecialOfferId = Some(0), // default value in insert SP
      cancellationPolicyCode = Some(roomInfo.cancellation.code),
      cancellationPolicy = Some(
        roomInfo.cancellation.policies.map(_.mkString).getOrElse("") +
          roomInfo.cancellation.noShowPolicy.getOrElse("")
      ),
      dmcId = roomInfo.dmcId,
      dmcCode = Some(dmcCode),
      dmcSpecificData = roomInfo.dmcSpecificData,
      isAutoProcessed = Some(false), // default value in insert SP
      bookingTypeId = None,          // default value in insert SP
      cidList = bookingContext.request.siteId.map(_.toString),
      sessionId = Some(bookingContext.request.sessionId),
      serverName = Some(ServerUtils.serverHostName()),
      clientIpAddress = Some(bookingContext.request.clientIp),
      referralUrl = Some(bookingContext.request.referralUrl),
      ssResultId = Some(roomInfo.searchResultId.getOrElse(0)), // default value in BookingItemMapper
      ssId = Some(roomInfo.searchId.getOrElse(0)),             // default value in BookingItemMapper
      ssIdFb = None,                                           // default value in schema
      ssResultType = Some(0),                                  // default value in insert SP
      ssProviderId = Some(0),                                  // default value in insert SP
      languageId = Some(bookingContext.request.languageId),
      storefrontId = Some(bookingContext.request.storefrontId),
      agentName = bookingContext.request.agentAssist.map(_.agentName),
      agentBookingChannel = bookingContext.request.agentAssist.map(_.agentBookingChannel),
      agentClientIp = bookingContext.request.agentAssist.map(_.agentClientIp),
      isFraudReview = Some(false),                                              // default value in SP
      reviewBy = Some(UUID.fromString("00000000-0000-0000-0000-000000000000")), // default value in SP
      isLocked = None,
      promotionCode = campaignInfo.map(_.promotionCode),
      promotionText = campaignInfo.flatMap(_.promotionText),
      discountType = campaignInfo.flatMap(_.discountType),
      discountAmount = campaignInfo.flatMap(_.discountAmount),
      promotionCampaignId = campaignInfo.map(_.campaignId),
      discountSavings = Some(discountSaving),
      ycsPromotionId = Some(roomInfo.promotion.map(_.id.toInt).getOrElse(0)), // default value in BookingItemMapper
      ycsPromotionText = roomInfo.promotion.flatMap(ele => Option(ele.text)),
      ycsForeignPromotionText = roomInfo.promotion.flatMap(ele => Option(ele.foreignText)),
      included = roomInfo.included,
      excluded = roomInfo.excluded,
      isAgentAssist = Some(getIsAgentAssist(bookingContext.request.agentAssist, bookingContext.isAgentAssist)),
      recStatus = RecStatus.Active,
      recCreatedWhen = bookingContext.bookingDate,
      recCreatedBy = bookingContext.request.userId,
      recModifyWhen = None,
      recModifyBy = None,
      trackingCookieId = bookingContext.request.trackingCookieId,
      trackingCookieDate = Some(JodaDateTimeUtils.toDateTimeWithoutMillis(bookingContext.request.trackingCookieDate)),
      affiliateModel = bookingContext.request.affiliateModel.map(_.id),
      affiliatePaymentMethod = bookingContext.request.affiliatePaymentMethod.map(_.id),
      pointMultiply = Some(roomInfo.pointMultiply.getOrElse(0)), // default value in BookingItemMapper
      trackingTag = bookingContext.request.trackingTag,
      rateChannel = Some(roomInfo.ycsRatePlanId match {
        case 0L => roomInfo.npclnChannel.getOrElse(roomInfo.ycsRatePlanId.toInt)
        case _  => roomInfo.ycsRatePlanId.toInt
      }),
      membershipContentText = Some(bookingContext.request.discountV1.membershipContentText),
      membershipContentLabel = Some(bookingContext.request.discountV1.membershipContentLabel),
      agodaCancellationFeeId = bookingContext.request.agodaCancellationFeeId,
      rateModelType = Some(roomInfo.rateModel.id),
      feCid = feCid,
      preBookingId = Some(prebookingId),
      ///////////////////////// Provisioning //////////////////////////
      bookingExternalReference = None,
      ///////////////////////// WorkflowInfo //////////////////////////
      // fixed value from @art
      arrivalTimeWindow = Some(
        getArrivalTimeWindow(
          chargeOption = bookingContext.chargeOption,
          bookingDate = bookingContext.bookingDate,
          checkInDate = bookingContext.checkInDate
        )
      ),
      availabilityType = Some(availabilityType.id),
      ccReceived = getCcReceived(bookingContext.chargeOption, roomInfo.isNotCcRequired),
      workflowId = Some(EbeWorkflowId.Property),
      workflowStateId = Some(EbeWorkflowState.CreateBooking),
      workflowActionId = Some(WorkflowActionId.Default),
      workflowParameterId = None,
      workflowParameterValue = None,
      workflowStateSince = Some(bookingContext.bookingDate),
      //////////////////////////// Fraud //////////////////////////////
      fraudScore = None,
      fraudAction = None,
      isEmailWhitelist = None,
      fraudCheckIp = None,
      /////////////////////////// Default /////////////////////////////
      userTracking = None,                 // default value in schema
      faxFormRequestStatus = None,         // default value in schema
      cancellationFeeAmount = None,        // default value in schema
      fallbackCxlPolicyCode = None,        // default value in schema
      cancellationFeeLocalAmount = None,   // default value in schema
      cancellationFeeLocalCurrency = None, // default value in schema
      isReminderSent = None,               // default value in schema
      isChargeBackRequested = None,        // default value in schema
      isSpecialOffer = None,               // default value in schema
      isAffiliateProcessed = None,         // default value in schema
      isRewardsProcessed = None,           // default value in schema
      agodaManagedYcs = Some(bookingContext.agodaManagedYcs),
      childPromotions = Option(PropertyBookingChildPromotionBuilder(childPromotions)),
      externalLoyaltyDisplayTierId = roomInfo.externalLoyaltyDisplayTierId
    )
  }

  def getIsAgentAssist(agentAssist: Option[AgentAssist], isAgentAssist: Boolean): Boolean = {
    agentAssist.exists(_.agentName.nonEmpty) || isAgentAssist
  }

  def getCcReceived(chargeOption: ChargeOption.Value, isNotCcRequired: Boolean): Option[Int] = {
    (chargeOption, isNotCcRequired) match {
      case (ChargeOption.PayLater, _) => Some(CCReceived.USD_1_PreAuth.id)
      case (_, false)                 => Some(CCReceived.Yes.id)
      case _                          => Some(CCReceived.No.id)
    }
  }

  def getArrivalTimeWindow(chargeOption: ChargeOption.Value, bookingDate: DateTime, checkInDate: DateTime): Int = {
    val arrivalWindowDay = Days.daysBetween(bookingDate, checkInDate).getDays
    if (ChargeOption.isBNPLOrBNPC(chargeOption)) EbeTimeWindow.NORMAL
    else if (arrivalWindowDay <= 3)
      EbeTimeWindow.ULMB
    else if (arrivalWindowDay <= 10)
      EbeTimeWindow.LMB
    else
      EbeTimeWindow.NORMAL
  }
}
