package com.agoda.bapi.creation.service

import akka.actor.Scheduler
import com.agoda.bapi.common.config.{AgodaConfig, CancellationChargeConfig, KillSwitches, NhaHotelIdMappingConfig}
import com.agoda.bapi.common.handler.{MeasurementsContext, RequestContext}
import com.agoda.bapi.common.message.creation.CreateBookingResponse
import com.agoda.bapi.common.proxy.EnigmaApiProxy
import com.agoda.bapi.common.util.ItineraryAssociatedBookingsTokenUtils
import com.agoda.bapi.creation.config.{DuplicateConfig, TestBookingConfig}
import com.agoda.bapi.creation.mapper.activity.ActivityMapper
import com.agoda.bapi.creation.mapper.addon.AddOnMapper
import com.agoda.bapi.creation.mapper.addon.cegFastTrack.CegFastTrackMapper
import com.agoda.bapi.creation.mapper.ebe._
import com.agoda.bapi.creation.mapper.{BookingRelationshipHelperImpl, BookingTypeMapper}
import com.agoda.bapi.creation.model.{RequestWithProducts, RequestWithProductsNType}
import com.agoda.bapi.creation.proxy.CreditCardApiLocalProxyV2
import com.agoda.bapi.creation.repository._
import com.agoda.bapi.creation.service.flow.ProcessStage
import com.agoda.bapi.creation.service.helper.MPLogHelper
import com.agoda.bapi.creation.service.stage._
import com.agoda.bapi.creation.service.stage.presave._
import com.agoda.bapi.creation.util.{ToolSet, WhitelabelUtils}
import com.agoda.bapi.creation.validation.CreateBookingRequestValidator
import com.agoda.bapi.execution.context.NonBlockingExecutionContext.default

import javax.inject.{Inject, Named, Singleton}
import scala.concurrent.{ExecutionContext, Future}

trait ItineraryService {
  def create(createBookingRequest: RequestWithProducts)(implicit
      requestContext: RequestContext
  ): Future[CreateBookingResponse]
}

@Singleton
class ItineraryServiceImpl @Inject() (
    flightBookingRepository: FlightBookingRepository,
    multiProductRepository: MultiProductRepository,
    paymentMethodRepository: PaymentMethodRepository,
    creditCardInfoRepository: CreditCardInfoRepository,
    vehicleBookingRepository: VehicleBookingRepository,
    tprmService: TPRMService,
    hadoopMessaging: HadoopMessagingService,
    @Named("CreateBookingRequestV2Validator") createBookingRequestValidator: CreateBookingRequestValidator,
    enigmaApiProxy: EnigmaApiProxy,
    @Named("DuplicateCheckServiceV1") duplicateCheckService: DuplicateCheckService,
    @Named("DuplicateCheckServiceV2") duplicateCheckServiceV2: DuplicateCheckService,
    orchestrationMessageService: OrchestrationMessageService,
    multiProductMasterMapper: MPMasterMapper,
    saveBookingMapper: SaveBookingMapper,
    flightMapper: FlightMapper,
    vehicleMapper: VehicleMapper,
    protectionMapper: ProtectionMapper,
    urlService: UrlService,
    mpLogHelper: MPLogHelper,
    cancellationChargeConfig: CancellationChargeConfig,
    itineraryAssociatedBookingsTokenUtils: ItineraryAssociatedBookingsTokenUtils,
    creditCardLocalProxy: CreditCardApiLocalProxyV2,
    bookingActionMessageService: BookingActionMessageService,
    activityBookingRepository: ActivityBookingRepository,
    activityMapper: ActivityMapper,
    itineraryBookingRepository: ItineraryBookingRepository,
    creationMdbRepository: CreationMdbRepository,
    testBookingConfig: TestBookingConfig,
    baseBookingRepository: BaseBookingRepository,
    agodaConfig: AgodaConfig,
    cegFastTrackMapper: CegFastTrackMapper,
    addOnMapper: AddOnMapper,
    cegFastTrackRepository: CegFastTrackRepository,
    genericAddOnRepository: GenericAddOnRepository,
    scheduler: Scheduler,
    killSwitches: KillSwitches,
    duplicateConfig: DuplicateConfig,
    creationCdbRepository: CreationCdbRepository,
    nhaHotelIdMappingConfig: NhaHotelIdMappingConfig
) extends ItineraryService
    with BookingTypeMapper
    with ToolSet {

  override def create(
      requestWithProducts: RequestWithProducts
  )(implicit requestContext: RequestContext): Future[CreateBookingResponse] = {

    val measurementsContext: MeasurementsContext =
      if (requestContext.featureAware.exists(_.isBcreAaTest07))
        requestWithProducts.measurementsContext
      else requestWithProducts.measurementsContext
    val bookingHandler = new ProcessStage[RequestWithProducts, CreateBookingResponse, CreateBookingResponse] {
      override def process(
          request: RequestWithProducts
      ): Future[Either[CreateBookingResponse, CreateBookingResponse]] =
        for {
          isInternationalAgencyBookingFeatureEnabled <-
            Future.successful(
              WhitelabelUtils.isInternationalAgencyBookingEnabled(requestContext.whiteLabelInfo)
            )
          hotelBookingType <- getBookingType(
                                paymentMethodRepository,
                                request,
                                isInternationalAgencyBookingFeatureEnabled
                              )

          requestWithType = request.withType(hotelBookingType)
          processor       = buildMultiProductProcessingFlow()
          result         <- processor.process(requestWithType)
        } yield result
    }

    withMeasureAndLogWithContext(measurementsContext)("bapi_create_itinerary") {
      // $COVERAGE-OFF$
      // This not unit testable code. To just cover Left, Right. The test need to understand process implementation
      new TestBookingModificationStage(testBookingConfig)
        .flatMap(new CreateBookingMetricStage(hadoopMessaging, mpLogHelper))
        .flatMap(new CreditCardRetrieveStage(creditCardInfoRepository))
        .flatMap(new ValidationStage(createBookingRequestValidator, hadoopMessaging))
        .flatMap {
          val aaTest08 = if (requestContext.featureAware.exists(_.isBcreAaTest08)) 1 else 1
          bookingHandler
        }
        .process(requestWithProducts)
        .map {
          case Left(response) =>
            if (requestContext.featureAware.exists(_.isBcreAaTest09)) response else response
          case Right(response) =>
            if (requestContext.featureAware.exists(_.isBcreAaTest10)) response else response
        }
      // $COVERAGE-ON$
    }
  }

  private[service] def buildMultiProductProcessingFlow()(implicit
      requestContext: RequestContext
  ): ProcessStage[RequestWithProductsNType, CreateBookingResponse, CreateBookingResponse] = {
    val flightPreSaveStage =
      new FlightPreSaveStage(
        flightBookingRepository,
        baseBookingRepository,
        enigmaApiProxy,
        flightMapper
      )
    val propertyPreSaveStage =
      new PropertyPreSaveStage(
        cancellationChargeConfig,
        saveBookingMapper,
        flightBookingRepository,
        enigmaApiProxy,
        paymentMethodRepository,
        hadoopMessaging,
        baseBookingRepository,
        creationCdbRepository
      )(implicitly[ExecutionContext], killSwitches, nhaHotelIdMappingConfig)
    val activityPreSaveStage =
      new ActivityPreSaveStage(
        activityBookingRepository,
        baseBookingRepository,
        flightBookingRepository,
        activityMapper
      )
    val vehiclePreSaveStage =
      new VehiclePreSaveStage(enigmaApiProxy, vehicleBookingRepository, vehicleMapper, flightBookingRepository)
    val protectionPreSaveStage =
      new ProtectionPreSaveStage(flightBookingRepository, protectionMapper)
    val cegFastTrackPreSaveStage = new CEGFastTrackPreSaveStage(
      baseBookingRepository,
      flightBookingRepository,
      cegFastTrackMapper,
      agodaConfig
    )
    val addOnPreSaveStage: AddOnPreSaveStage = new AddOnPreSaveStage(
      baseBookingRepository,
      flightBookingRepository,
      addOnMapper,
      agodaConfig
    )
    val relationshipMapper = new BookingRelationshipHelperImpl(agodaConfig)
    val nonHotelPreSaveFacade = new NonHotelPreSaveFacade(
      flightPreSaveProcess = flightPreSaveStage,
      vehiclePreSaveProcess = vehiclePreSaveStage,
      activityPreSaveProcess = activityPreSaveStage,
      protectionPreSaveProcess = protectionPreSaveStage,
      cegFastTrackPreSaveProcess = cegFastTrackPreSaveStage,
      addOnPreSaveProcess = addOnPreSaveStage
    )
    val preSaveStage = new MultiProductPreSaveStage(
      paymentMethodRepository,
      flightBookingRepository,
      multiProductRepository,
      multiProductMasterMapper,
      propertyPreSaveStage,
      nonHotelPreSaveFacade,
      creditCardLocalProxy,
      hadoopMessaging,
      relationshipMapper
    )(killSwitches, requestContext)
    val saveStage = new MultiProductSaveStage(
      urlService,
      flightBookingRepository,
      multiProductRepository,
      orchestrationMessageService,
      itineraryAssociatedBookingsTokenUtils,
      vehicleBookingRepository,
      bookingActionMessageService,
      hadoopMessaging,
      activityBookingRepository,
      itineraryBookingRepository,
      creationMdbRepository,
      cegFastTrackRepository,
      genericAddOnRepository,
      scheduler,
      duplicateConfig,
      creationCdbRepository
    )(killSwitches)

    val flightValidateStage =
      new FlightValidateStage(duplicateCheckService, duplicateCheckServiceV2, tprmService, hadoopMessaging)
    val propertyValidateStage = new PropertyValidateStage(
      duplicateCheckService,
      tprmService,
      hadoopMessaging,
      duplicateConfig,
      creationCdbRepository
    )(killSwitches)
    val vehicleValidateStage = new VehicleValidateStage(duplicateCheckService, duplicateCheckServiceV2, hadoopMessaging)
    val activityValidateStage =
      new ActivityValidateStage(duplicateCheckService, duplicateCheckServiceV2, hadoopMessaging)

    val validateStage =
      new MultiProductsValidateStage(
        tprmService,
        propertyValidateStage,
        flightValidateStage,
        vehicleValidateStage,
        activityValidateStage,
        hadoopMessaging
      )

    validateStage
      .flatMap(preSaveStage)
      .flatMap(saveStage)
  }

}
