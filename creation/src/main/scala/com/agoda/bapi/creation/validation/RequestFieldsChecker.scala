package com.agoda.bapi.creation.validation

import com.agoda.bapi.common.ToolSet
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.ValidationUtils._
import com.agoda.bapi.common.message.creation.{ValidationUtils, _}
import com.agoda.bapi.common.model.booking.RequiredFieldMetadata
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{<PERSON>quired<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, WhiteLabelInfo}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.model.RequestWithProducts
import com.agoda.bapi.creation.util.WhitelabelUtils
import com.agoda.bapi.creation.validation.activity.ActivityLeadTravellerValidator
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpb.common.errors.ErrorCode.ErrorCode
import com.agoda.mpbe.state.common.enums.Gateway.Gateway
import org.joda.time.LocalDate

import java.nio.charset.StandardCharsets
import java.util.Base64
import scala.util.Try
import scala.util.matching.Regex

trait RequestFieldsChecker extends CommonValidation {
  // pass enableCountryOfResidenceValidation as param because we want to allocate the exp only one time
  // Todo remove this enableCountryOfResidenceValidation param when integrate WLBE-960
  def validate(request: RequestWithProducts, enableCountryOfResidenceValidation: Boolean)(implicit
      requestContext: RequestContext
  ): List[ErrorCode]
}

case class RequestFieldsCheckerImpl() extends RequestFieldsChecker with ToolSet {
  override def validate(request: RequestWithProducts, enableCountryOfResidenceValidation: Boolean)(implicit
      requestContext: RequestContext
  ): List[ErrorCode] = {
    val createBookingRequest = request.request
    val whiteLabelInfo       = request.requestContext.whiteLabelInfo
    val roomDmcId =
      request.products.properties.flatMap(_.bapiBooking.getRooms().flatMap(_.dmcId)).headOption.getOrElse(0)

    val acceptEmailWithHyphenSuffix = WhitelabelUtils.isJtbSupplyWl(whiteLabelInfo)

    (if (request.rooms.nonEmpty)
       validateGuestList(createBookingRequest, acceptEmailWithHyphenSuffix)
     else Nil) :::
      (if (request.flights.nonEmpty) validatePaxList(createBookingRequest)
       else Nil) :::
      (if (request.car.nonEmpty) validateDriver(createBookingRequest) else Nil) :::
      (if (request.products.activities.nonEmpty)
         ActivityLeadTravellerValidator.validateActivityLeadTraveller(createBookingRequest)
       else Nil) :::
      validateProduct(request) :::
      validateCreditCard(createBookingRequest) :::
      validateGateWayId(createBookingRequest).toList :::
      validateCustomer(
        createBookingRequest,
        acceptEmailWithHyphenSuffix,
        whiteLabelInfo,
        roomDmcId,
        enableCountryOfResidenceValidation
      ) :::
      validateProductKeys(request) :::
      validateRequiredFields(createBookingRequest, request.requestContext.featureAware).toList
  }

  private[validation] def validateGuestList(
      request: CreateBookingRequest,
      acceptEmailWithHyphenSuffix: Boolean = false
  ): List[ErrorCode] = {
    type ValidateGuest = HotelGuest => Option[ErrorCode] // Function Type

    val isFirstNameEmpty: ValidateGuest = (guest: HotelGuest) =>
      if (!ValidationUtils.isNotEmpty(guest.firstname)) Some(ErrorCode.GuestFirstNameIsEmpty) else None
    val isLastNameEmpty: ValidateGuest = (guest: HotelGuest) =>
      if (!ValidationUtils.isNotEmpty(guest.lastname)) Some(ErrorCode.GuestLastNameIsEmpty) else None
    val isInvalidEmail: ValidateGuest = (guest: HotelGuest) =>
      guest.email.flatMap(mail => {
        var standardPattern = ValidationUtils.EMAIL_PATTERN_WITH_SPECIAL_CHARACTER

        val emailPattern =
          if (acceptEmailWithHyphenSuffix)
            ValidationUtils.EMAIL_PATTERN_HYPHEN_SUFFIX
          else standardPattern

        if (mail != "" && emailPattern.findFirstIn(mail).isEmpty)
          Some(ErrorCode.InvalidGuestEmailFormat)
        else None
      })
    val isInvalidNationality: ValidateGuest = (guest: HotelGuest) =>
      if (guest.nationalityId == 0) Some(ErrorCode.InvalidNationalityId) else None

    (List(
      errorHandler(request.guestList.nonEmpty, ErrorCode.GuestListIsEmpty),
      errorHandler(request.guestList.isEmpty || request.guestList.exists(_.primary), ErrorCode.InvalidPrimaryGuest),
      errorHandler(request.guestList.count(_.primary) < 2, ErrorCode.DuplicatedPrimaryGuest),
      errorHandler(
        request.guestList.isEmpty || request.guestList.find(g => g.primary).forall(g => g.isAdult),
        ErrorCode.PrimaryGuestMustBeAdult
      )
    ) ::: List( // Verify each guest info in the guest list
      toVerifyMultiObj(isFirstNameEmpty),
      toVerifyMultiObj(isLastNameEmpty),
      toVerifyMultiObj(isInvalidEmail),
      toVerifyMultiObj(isInvalidNationality)
    ).foldLeft[List[Option[ErrorCode]]](Nil)((list, validateFunc) => list :+ validateFunc(request.guestList.toList)))
      .collect { case Some(v) => v }
  }

  private[validation] def isPaxFirstNameValid(pax: FlightPax) = {
    isNotEmpty(pax.firstname) ?! // && isMatching(pax.firstname, PAX_NAME)) ?! // Keep for reference to return after MVP
      ErrorCode.InvalidFlightPaxFirstName
  }

  private[validation] def isPaxLastNameValid(pax: FlightPax) = {
    isNotEmpty(pax.lastname) ?! // && isMatching(pax.lastname, PAX_NAME)) ?! // Keep for reference to return after MVP
      ErrorCode.InvalidFlightPaxLastName
  }

  private[validation] def isPaxGenderValid(pax: FlightPax) = {
    (isNotEmpty(pax.gender) &&
      isMatching(pax.gender, PAX_GENDER)) ?!
      ErrorCode.InvalidFlightPaxGender
  }

  private[validation] def isPaxBirthDateValid(pax: FlightPax) = {
    val adultDate = LocalDate.now().minusYears(18)
    val yearMatch =
      if (pax.isAdult)
        pax.birthDate.isBefore(adultDate)
      else
        pax.birthDate.isAfter(adultDate)
    yearMatch ?! ErrorCode.InvalidFlightPaxBirthDate
  }

  private[validation] def isPaxNationalityValid(pax: FlightPax) = {
    (pax.nationalityId != 0) ?!
      ErrorCode.InvalidFlightPaxNationality
  }

  private[validation] def isPaxPassportNumberValid(pax: FlightPax)(implicit requestContext: RequestContext) = {
    pax.passportNumber match {
      case Some(number) =>
        val passportRegex =
          if (requestContext.featureAware.exists(_.isUpdatePassportNoLengthTo10))
            PAX_PASSPORT_NUMBER_MAX10
          else
            PAX_PASSPORT_NUMBER
        (isNotEmpty(number) && isMatching(number, passportRegex)) ?! ErrorCode.InvalidFlightPaxPassportNumber
      case _ => None
    }
  }

  private[validation] def isPaxPassportExpiresDate(pax: FlightPax) = {
    pax.passportExpires match {
      case Some(expires) => // Maybe need to check with bookingDate or with last segment arrival date?
        isAfterNow(expires) ?! ErrorCode.InvalidFlightPaxPassportExpirationDate
      case _ => None
    }
  }

  private[validation] def isPaxKnownTravelerNumberValid(pax: FlightPax) = {
    pax.knownTravelerNumber match {
      case Some(ktn) =>
        isMatching(ktn, PAX_KNOWN_TRAVELER_NUMBER) ?! ErrorCode.InvalidFlightPaxKnownTravelerNumber
      case _ => None
    }
  }

  private[validation] def validatePax(pax: FlightPax)(implicit requestContext: RequestContext) = {
    List(
      isPaxFirstNameValid(pax),
      isPaxLastNameValid(pax),
      isPaxGenderValid(pax),
      // isPaxBirthDateValid(pax), // Keep for reference to return after MVP
      isPaxNationalityValid(pax),
      isPaxPassportNumberValid(pax),
      isPaxPassportExpiresDate(pax),
      isPaxKnownTravelerNumberValid(pax)
    ).flatten
  }

  private[validation] def isPaxListValid(pax: Seq[FlightPax]) =
    pax.nonEmpty ?! ErrorCode.InvalidFlightPaxAmount

  private[validation] def isPaxPrimaryValid(paxList: Seq[FlightPax]) = {
    val primaries = paxList.filter(_.primary)

    if (primaries.isEmpty) Some(ErrorCode.InvalidPrimaryPax)
    else if (primaries.size > 1) Some(ErrorCode.DuplicatedPrimaryPax)
    else if (!primaries.forall(_.isAdult)) Some(ErrorCode.PrimaryPaxMustBeAdult)
    else None
  }

  private[validation] def validatePaxList(
      request: CreateBookingRequest
  )(implicit requestContext: RequestContext): List[ErrorCode] = {
    val paxValidation = request.paxList.flatMap { pax =>
      validatePax(pax)
    }.toList

    val primaryGuestValidation = isPaxPrimaryValid(request.paxList)

    val paxListValidation = isPaxListValid(request.paxList)

    paxValidation ::: primaryGuestValidation.toList ::: paxListValidation.toList
  }

  private[validation] def validateProduct(request: RequestWithProducts): List[ErrorCode] = {
    val products = request.request.products
    // todo: is it correct ???
    val propertyItemsNonEmpty = products.propertyItems.isEmpty ||
      (products.propertyItems.isDefined && products.propertyItems.getOrElse(Seq.empty).nonEmpty)
    val flightItemsNonEmpty = products.flightItems.isEmpty ||
      (products.flightItems.isDefined && products.flightItems.getOrElse(Seq.empty).nonEmpty)
    val productsProvided =
      products.propertyItems.nonEmpty || products.flightItems.nonEmpty || products.carItems.nonEmpty

    List(
      if (
        !Set(
          BookingFlow.SingleFlight,
          BookingFlow.Hackerfare,
          BookingFlow.FlightWithProtection,
          BookingFlow.MultiFlightsWithProtection,
          BookingFlow.SingleVehicle,
          BookingFlow.SingleActivity,
          BookingFlow.Cart
        ).contains(request.bookingFlow)
      ) errorHandler(propertyItemsNonEmpty, ErrorCode.PropertyListIsEmpty)
      else None,
      if (
        !Set(
          BookingFlow.SingleProperty,
          BookingFlow.MixAndSave,
          BookingFlow.SingleVehicle,
          BookingFlow.MultiHotel,
          BookingFlow.SingleActivity,
          BookingFlow.Cart
        ).contains(request.bookingFlow)
      ) errorHandler(flightItemsNonEmpty, ErrorCode.FlightListIsEmpty)
      else None,
      errorHandler(productsProvided, ErrorCode.NoProductsProvided)
    ).flatten
  }

  private[validation] def validateCreditCard(request: CreateBookingRequest): List[ErrorCode] = {
    val creditCard = request.payment.creditCard
    val isRestrictedCountry: Option[ErrorCode] =
      creditCard
        .map(_.issueBankCountryId)
        .find(ValidationUtils.isRestrictCountry)
        .map(_ => ErrorCode.IssuedBankCountryIsRestricted)
    val isRestrictedBillingCountry: Option[ErrorCode] =
      creditCard
        .flatMap(_.billingCountryId)
        .find(ValidationUtils.isRestrictCountry)
        .map(_ => ErrorCode.BillingCountryIsRestricted)

    List(isRestrictedCountry, isRestrictedBillingCountry).collect { case Some(v) => v }
  }

  /**
    * return Some(InvalidGatewayID), if GateWay Id is equal to None and Transaction Id is exist return None, otherwise
    */
  private[validation] def validateGateWayId(request: CreateBookingRequest): Option[ErrorCode] = {
    val paymentContinuation  = request.payment.continuation
    val isTransactionIdExist = paymentContinuation.exists(_.transactionId.nonEmpty)

    if (isTransactionIdExist && paymentContinuation.exists(_.gatewayId == Gateway.None))
      Some(ErrorCode.InvalidGatewayID)
    else None
  }

  private[validation] def validateCustomer(
      request: CreateBookingRequest,
      acceptEmailWithHyphenSuffix: Boolean = false,
      whiteLabelInfo: WhiteLabelInfo,
      roomDmcId: Int,
      enableCountryOfResidenceValidation: Boolean
  ): List[ErrorCode] = {
    val customer = request.customer

    var standardPattern = ValidationUtils.EMAIL_PATTERN_WITH_SPECIAL_CHARACTER

    val emailPattern =
      if (acceptEmailWithHyphenSuffix)
        ValidationUtils.EMAIL_PATTERN_HYPHEN_SUFFIX
      else standardPattern

    List(
      errorHandler(ValidationUtils.isNotEmpty(customer.firstname), ErrorCode.InvalidCustomerFirstName),
      errorHandler(ValidationUtils.isNotEmpty(customer.lastname), ErrorCode.InvalidCustomerLastName),
      errorHandler(
        (customer.memberId == 0 && ValidationUtils.isNotEmpty(customer.email)) ||
          (customer.memberId != 0 &&
            (ValidationUtils.isNotEmpty(customer.email) || ValidationUtils.isNotEmpty(customer.phoneFormat))),
        ErrorCode.RequiredCustomerEmail
      ),
      errorHandler(
        !ValidationUtils
          .isNotEmpty(customer.email) || emailPattern.findFirstIn(customer.email).isDefined,
        ErrorCode.InvalidCustomerEmailFormat
      ),
      errorHandler(
        isNameValid(customer.firstname, whiteLabelInfo, roomDmcId),
        ErrorCode.CustomerFirstNameIsNotEnglish
      ),
      errorHandler(
        isNameValid(customer.lastname, whiteLabelInfo, roomDmcId),
        ErrorCode.CustomerLastNameIsNotEnglish
      ),
      withCountryOfResidenceExpWrapper(
        errorHandler(!ValidationUtils.isRestrictCountry(customer.countryId), ErrorCode.CounryIsRestricted),
        enableCountryOfResidenceValidation
      ),
      withCountryOfResidenceExpWrapper(
        errorHandler(customer.countryId != 0, ErrorCode.InvalidCustomerCountryID),
        enableCountryOfResidenceValidation
      )
    ).collect { case Some(v) => v }
  }

  private[validation] def withCountryOfResidenceExpWrapper(
      errorCodeOpt: Option[ErrorCode],
      enableCountryOfResidenceValidation: Boolean
  ): Option[ErrorCode] = {
    // always return None here as countryId related logic will be validate in CountryOfResidenceChecker.scala
    if (enableCountryOfResidenceValidation) None
    else errorCodeOpt
  }

  private[validation] def validateDriver(request: CreateBookingRequest): List[ErrorCode] = {
    val driver = request.driverOption match {
      case Some(drv) => isDriverValid(drv)
      case None      => List(ErrorCode.NoDriverInformationProvided)
    }
    driver
  }

  private[validation] def isDriverValid(driver: CarDriver): List[ErrorCode] = {

    val firstNameValidation = isNotEmpty(driver.firstName) ?! ErrorCode.InvalidDriverFirstName
    val lastNameValidation  = isNotEmpty(driver.lastName) ?! ErrorCode.InvalidDriverLastName

    List(
      firstNameValidation,
      lastNameValidation
    ).flatten
  }

  private[validation] def validateProductKeys(request: RequestWithProducts): List[ErrorCode] = {
    List(
      validatePropertyRequest(request),
      validateFlightRequestItem(request),
      validateVehicleRequestItem(request),
      validateActivityRequestItem(request)
    ).flatten
  }

  private[validation] def validatePropertyRequest(request: RequestWithProducts): Option[ErrorCode] = {

    val requestPropertyItems = request.request.products.propertyItems.getOrElse(Seq.empty)
    val requestPropertyKeys =
      requestPropertyItems.flatMap(_.productTokenKey).toSet
    val propertyTokenKeys = request.products.properties.flatMap(_.bapiBooking.productTokenKey.toSeq).toSet
    if (requestPropertyKeys == propertyTokenKeys)
      None
    else
      Some(ErrorCode.MissingPropertyItems)
  }

  private[validation] def validateFlightRequestItem(request: RequestWithProducts): Option[ErrorCode] = {
    val requestFlightItems = request.request.products.flightItems.getOrElse(Seq.empty)
    val requestFlightKeys  = requestFlightItems.flatMap(_.productTokenKey).toSet
    val flightTokenKeys    = request.products.flights.flatMap(_.productTokenKey.toSeq).toSet
    if (requestFlightKeys == flightTokenKeys)
      None
    else
      Some(ErrorCode.MissingFlightItems)
  }

  private[validation] def validateVehicleRequestItem(request: RequestWithProducts): Option[ErrorCode] = {
    val requestVehicleItems = request.request.products.carItems.getOrElse(Seq.empty)
    val requestVehicleKeys  = requestVehicleItems.flatMap(_.productTokenKey).toSet
    val vehicleTokenKeys    = request.products.vehicles.flatMap(_.productTokenKey.toSeq).toSet
    if (requestVehicleKeys == vehicleTokenKeys)
      None
    else
      Some(ErrorCode.MissingVehicleItems)
  }

  private[validation] def validateActivityRequestItem(request: RequestWithProducts): Option[ErrorCode] = {
    val requestActivityItems = request.request.products.activitiesItems.getOrElse(Seq.empty)
    val requestActivityKeys  = requestActivityItems.flatMap(_.productTokenKey).toSet
    val activityTokenKeys    = request.products.activities.flatMap(_.productTokenKey).toSet

    if (requestActivityKeys == activityTokenKeys) None
    else Some(ErrorCode.MissingActivityItems)
  }

  private[validation] def validateRequiredFields(
      request: CreateBookingRequest,
      featureAware: Option[FeatureAware] = None
  ): Option[ErrorCode] = {

    val requiredFields        = request.payment.requiredFields.getOrElse(Map.empty)
    val requiredFieldMetadata = request.payment.requiredFieldMetadata.getOrElse(Map.empty)

    validateRequiredFieldsV2(requiredFields, requiredFieldMetadata)
  }

  private[validation] def validateRequiredFieldsV2(
      requiredFields: Map[String, String],
      requiredFieldMetadata: Map[String, RequiredFieldMetadata]
  ): Option[ErrorCode] = {

    val requiredFieldMetadataNames = requiredFieldMetadata.keys.toSeq
    val requiredFieldNames         = requiredFields.keys.toSeq
    val requiredFieldsToValidate   = requiredFields.filterKeys(key => requiredFieldMetadataNames.contains(key))

    val requiredFieldsNotPresented = requiredFieldMetadataNames.filter(key => !requiredFieldNames.contains(key))
    if (requiredFieldsNotPresented.nonEmpty)
      Some(ErrorCode.InvalidRequiredFieldError)
    else {
      if (requiredFields.size != requiredFieldsToValidate.size)
        logger.warn(
          s"RequiredFields not equal to Metadata RequiredFields: " +
            s"RequiredFields size: ${requiredFields.size} include $requiredFieldNames, " +
            s"Metadata requiredFields size: ${requiredFieldMetadata.size} include $requiredFieldMetadataNames"
        )

      requiredFieldsToValidate.flatMap(isValidRequiredFieldPattern(_, requiredFieldMetadata)).headOption
    }
  }

  private[validation] def isValidRequiredFieldPattern(
      requiredField: (String, String),
      requiredFieldMetadata: Map[String, RequiredFieldMetadata]
  ): Option[ErrorCode] = {
    val requiredFieldKey   = requiredField._1
    val requiredFieldValue = requiredField._2
    val outcomeOpt = requiredFieldMetadata.get(requiredFieldKey).flatMap { foundValue =>
      decodeBase64(foundValue.regex).map(requiredFieldValue.matches)
    }
    if (outcomeOpt.isEmpty || outcomeOpt.contains(false))
      Some(RequiredField.toSubErrorCode(requiredFieldKey))
    else None
  }

  private def decodeBase64(input: String): Option[String] = {
    Try(new String(Base64.getDecoder.decode(input), StandardCharsets.UTF_8)).toOption
  }

  private[validation] def isNameValid(
      name: String,
      whiteLabelInfo: WhiteLabelInfo,
      roomDmcId: Int
  ): Boolean = {
    val isEmpty = !ValidationUtils.isNotEmpty(name)
    val regexNamePattern =
      whiteLabelInfo.feature.bookingCreationRegex.dmcSetting
        .flatMap(_.find(_.dmcId.contains(roomDmcId)).flatMap(_.customerName))
    val namePattern = new Regex(regexNamePattern.getOrElse(ValidationUtils.NAME_PATTERN.regex)).pattern

    isEmpty || namePattern.matcher(name).matches()
  }
}
