package com.agoda.bapi.creation.util.serializer

import com.agoda.abspnx.client.models.abs.{CreditCardInfo, GuestInfo, ReservationInfo}
import com.agoda.bapi.common.message.creation._
import com.agoda.bapi.common.message.payout.PayoutApiData
import com.agoda.bapi.common.model.UserContext
import com.agoda.bapi.common.util.MaskUtils
import com.agoda.fraud.proto.HotelFraudCheckRequest
import com.agoda.paymentapiv2.client.v2.common.model.{CustomerInfo, Guest}
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, LocalDate}
import org.json4s.JsonAST.JString
import org.json4s.reflect.TypeInfo
import org.json4s.{Extraction, Formats, JValue, Serializer}

import java.time.OffsetDateTime

/**
  * The object helps to sanitize pii and pci data <p>It was deprecated, please, update
  * {@link com.agoda.bapi.common.reporting.SensitiveDataSerializer} instead
  */
@deprecated
object PIISerializer extends Serializer[AnyRef] {

  private val dateFormatter = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZZ")

  private val dateTimeFormat = new Serializer[DateTime] {
    override def deserialize(implicit format: Formats): PartialFunction[(TypeInfo, JValue), DateTime] =
      PartialFunction.empty

    override def serialize(implicit formats: Formats): PartialFunction[Any, JValue] = {
      case x: DateTime =>
        JString(dateFormatter.print(x))
    }
  }

  override def deserialize(implicit format: Formats): PartialFunction[(TypeInfo, JValue), AnyRef] =
    PartialFunction.empty

  private def maskGuestLocalizedNames(localizedNames: Seq[LocalizedName]): Seq[LocalizedName] = {
    localizedNames.map(localizedName =>
      localizedName.copy(
        firstname = MaskUtils.maskAnyCharacter(localizedName.firstname),
        lastname = MaskUtils.maskAnyCharacter(localizedName.lastname)
      )
    )
  }

  private def maskGuestInfo(guestInfo: GuestInfo) = {
    GuestInfo(
      firstName = guestInfo.firstName.map(MaskUtils.maskAnyCharacter),
      lastName = guestInfo.lastName.map(MaskUtils.maskAnyCharacter),
      phoneNumber = guestInfo.phoneNumber.map(MaskUtils.maskAnyCharacter),
      email = guestInfo.email.map(MaskUtils.maskAnyCharacter),
      nationality = guestInfo.nationality,
      age = guestInfo.age.map(MaskUtils.maskNumber),
      sex = null,
      guestType = guestInfo.guestType,
      guestContactType = guestInfo.guestContactType,
      stateId = guestInfo.stateId.map(MaskUtils.maskNumber),
      localizedNames = guestInfo.localizedNames.map(localizedName =>
        localizedName.copy(
          firstName = MaskUtils.maskAnyCharacter(localizedName.firstName),
          lastName = MaskUtils.maskAnyCharacter(localizedName.lastName)
        )
      ),
      prefecture = guestInfo.prefecture.map(MaskUtils.maskAnyCharacter)
    )
  }

  private def maskCCDetail(x: CreditCardDetail) = {
    x.copy(
      ccofId = x.ccofId.map(MaskUtils.maskNumber(_)),
      cardHolderName = Some(MaskUtils.maskAnyCharacter(x.cardHolderName.getOrElse(""))),
      creditCardNumber = Some(MaskUtils.maskAnyCharacter(x.creditCardNumber.getOrElse(""))),
      securityCode = Some(MaskUtils.maskAnyCharacter(x.securityCode.getOrElse(""))),
      billingAddress1 = Some(MaskUtils.maskAnyCharacter(x.billingAddress1.getOrElse(""))),
      billingAddress2 = Some(MaskUtils.maskAnyCharacter(x.billingAddress2.getOrElse(""))),
      billingCity = Some(MaskUtils.maskAnyCharacter(x.billingCity.getOrElse(""))),
      billingState = Some(MaskUtils.maskAnyCharacter(x.billingState.getOrElse(""))),
      billingCountryId = None,
      billingPostalCode = None,
      phoneNumber = Some(MaskUtils.maskAnyCharacter(x.phoneNumber.getOrElse(""))),
      expiryDate =
        x.expiryDate.map(d => d.copy(month = MaskUtils.maskNumber(d.month), year = MaskUtils.maskNumber(d.year))),
      issuingBank = x.issuingBank.map(b =>
        b.copy(
          name = MaskUtils.maskAnyCharacter(b.name)
        )
      )
    )
  }

  private def maskCreditCardV2(x: CreditCardV2) = {
    x.copy(
      detail = x.detail.map(maskCCDetail(_)),
      payment3DS = x.payment3DS
    )
  }

  override def serialize(implicit format: Formats): PartialFunction[Any, JValue] = {
    case x: DateTime =>
      val df = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZZ")
      JString(df.print(x))
    case x: CarDriver =>
      val newX = x.copy(
        title = MaskUtils.maskAnyCharacter(x.title),
        firstName = MaskUtils.maskAnyCharacter(x.firstName),
        lastName = MaskUtils.maskAnyCharacter(x.lastName),
        middleName = x.middleName.map(MaskUtils.maskAnyCharacter),
        gender = maskGenderOpt(x.gender),
        email = x.email.map(MaskUtils.maskAnyCharacter),
        phoneNumber = x.phoneNumber.map(MaskUtils.maskAnyCharacter)
      )
      Extraction.decompose(newX)(format - this)
    case x: Customer =>
      val newX = x.copy(
        title = MaskUtils.maskAnyCharacter(x.title),
        firstname = MaskUtils.maskAnyCharacter(x.firstname),
        middlename = MaskUtils.maskAnyCharacter(x.middlename),
        lastname = MaskUtils.maskAnyCharacter(x.lastname),
        email = MaskUtils.maskAnyCharacter(x.email),
        phoneFormat = MaskUtils.maskAnyCharacter(x.phoneFormat),
        faxFormat = MaskUtils.maskAnyCharacter(x.faxFormat),
        address1 = MaskUtils.maskAnyCharacter(x.address1),
        address2 = MaskUtils.maskAnyCharacter(x.address2),
        postcode = MaskUtils.maskAnyCharacter(x.postcode),
        region = MaskUtils.maskAnyCharacter(x.region),
        countryId = -1,
        state = MaskUtils.maskAnyCharacter(x.state),
        city = MaskUtils.maskAnyCharacter(x.city),
        area = MaskUtils.maskAnyCharacter(x.area),
        b2bCustomerEmail = MaskUtils.maskAnyCharacter(x.b2bCustomerEmail),
        stateId = x.stateId.map(MaskUtils.maskNumber),
        age = x.age.map(MaskUtils.maskNumber),
        gender = maskGenderOpt(x.gender),
        localizedNames = x.localizedNames.map(maskGuestLocalizedNames),
        emergencyPhoneNumber = x.emergencyPhoneNumber.map(MaskUtils.maskAnyCharacter)
      )
      Extraction.decompose(newX)(format - this + dateTimeFormat)
    case x: HotelGuest =>
      val newX = x.copy(
        firstname = MaskUtils.maskAnyCharacter(x.firstname),
        middlename = MaskUtils.maskAnyCharacter(x.middlename),
        lastname = MaskUtils.maskAnyCharacter(x.lastname),
        suffix = MaskUtils.maskAnyCharacter(x.suffix),
        age = MaskUtils.maskNumber(x.age),
        nationalityId = x.nationalityId,
        email = MaskUtils.maskAnyCharacter(x.email),
        gender = maskGenderOpt(x.gender),
        phoneFormat = x.phoneFormat.map(MaskUtils.maskAnyCharacter),
        emergencyPhone = x.emergencyPhone.map(MaskUtils.maskAnyCharacter),
        postcode = x.postcode.map(MaskUtils.maskAnyCharacter),
        city = x.city.map(MaskUtils.maskAnyCharacter),
        area = x.area.map(MaskUtils.maskAnyCharacter),
        address = x.address.map(MaskUtils.maskAnyCharacter),
        stateId = x.stateId.map(MaskUtils.maskNumber),
        localizedNames = x.localizedNames.map(maskGuestLocalizedNames)
      )
      Extraction.decompose(newX)(format - this)
    case x: BookingPaymentV2 =>
      val newX = x.copy(
        ccToken = MaskUtils.maskAnyCharacter(x.ccToken),
        creditCard = x.creditCard.map(maskCreditCardV2(_))
      )
      Extraction.decompose(newX)(format - this + dateTimeFormat)
    case x: CreditCard =>
      val newX = x.copy(
        cardholderName = MaskUtils.maskAnyCharacter(x.cardholderName),
        creditcardNumber = MaskUtils.maskAnyCharacter(x.creditcardNumber),
        expiryDate = Some(MaskUtils.maskAnyCharacter(x.expiryDate.getOrElse(""))),
        securityCode = Some(MaskUtils.maskAnyCharacter(x.securityCode.getOrElse(""))),
        billingAddress1 = MaskUtils.maskAnyCharacter(x.billingAddress1),
        billingAddress2 = MaskUtils.maskAnyCharacter(x.billingAddress2),
        billingCity = MaskUtils.maskAnyCharacter(x.billingCity),
        billingState = MaskUtils.maskAnyCharacter(x.billingState),
        billingCountryId = None,
        billingPostalcode = MaskUtils.maskAnyCharacter(x.billingPostalcode),
        phoneNumber = Some(MaskUtils.maskAnyCharacter(x.phoneNumber.getOrElse("")))
      )
      Extraction.decompose(newX)(format - this + dateTimeFormat)
    case x: ContinuePaymentRequest =>
      val newX = x.copy(
        securityCode = x.securityCode.map(MaskUtils.maskAnyCharacter)
      )
      Extraction.decompose(newX)(format - this + dateTimeFormat)
    case x: CreditCardV2 =>
      val newX = maskCreditCardV2(x)
      Extraction.decompose(newX)(format - this + dateTimeFormat)
    case x: CreditCardDetail =>
      val newX = maskCCDetail(x)
      Extraction.decompose(newX)(format - this + dateTimeFormat)
    case x: CustomerInfo =>
      val newX = x.copy(
        Name = x.Name.map(c => MaskUtils.maskAnyCharacter(c)),
        LastName = x.LastName.map(c => MaskUtils.maskAnyCharacter(c)),
        PhoneNo = x.PhoneNo.map(c => MaskUtils.maskAnyCharacter(c)),
        PhoneCountryCode = x.PhoneCountryCode.map(c => MaskUtils.maskAnyCharacter(c)),
        Address1 = x.Address1.map(c => MaskUtils.maskAnyCharacter(c)),
        Address2 = x.Address2.map(c => MaskUtils.maskAnyCharacter(c)),
        Email = x.Email.map(c => MaskUtils.maskAnyCharacter(c)),
        CountryCode = x.CountryCode.map(c => MaskUtils.maskAnyCharacter(c)),
        PostalCode = x.PostalCode.map(c => MaskUtils.maskAnyCharacter(c)),
        City = x.City.map(c => MaskUtils.maskAnyCharacter(c)),
        State = x.State.map(c => MaskUtils.maskAnyCharacter(c)),
        TaxId = x.TaxId.map(c => MaskUtils.maskAnyCharacter(c)),
        IdCardNumber = x.IdCardNumber.map(c => MaskUtils.maskAnyCharacter(c)),
        Guests = x.Guests.map(guest =>
          guest.map(g =>
            Guest(
              Option(MaskUtils.maskAnyCharacter(g.FirstName.getOrElse(""))),
              Option(MaskUtils.maskAnyCharacter(g.LastName.getOrElse("")))
            )
          )
        )
      )
      Extraction.decompose(newX)(format - this + dateTimeFormat)
    case x: com.agoda.paymentapiv2.client.v2.common.model.CreditCardInfo =>
      val newX = x.copy(
        CardNumber = x.CardNumber.map(c => MaskUtils.maskAnyCharacter(c)),
        CardHolderName = x.CardHolderName.map(c => MaskUtils.maskAnyCharacter(c)),
        CardExpMonth = x.CardExpMonth.map(c => MaskUtils.maskAnyCharacter(c)),
        CardExpYear = x.CardExpYear.map(c => MaskUtils.maskAnyCharacter(c)),
        Cvv = x.Cvv.map(c => MaskUtils.maskAnyCharacter(c))
      )
      Extraction.decompose(newX)(format - this + dateTimeFormat)
    case x: HotelFraudCheckRequest =>
      val newX = x.copy(
        creditCardNo = MaskUtils.maskAnyCharacter(x.creditCardNo),
        phoneNo = MaskUtils.maskAnyCharacter(x.phoneNo),
        cardHolderName = MaskUtils.maskAnyCharacter(x.cardHolderName),
        billingCity = MaskUtils.maskAnyCharacter(x.billingCity),
        billingRegion = MaskUtils.maskAnyCharacter(x.billingRegion),
        billingPostal = MaskUtils.maskAnyCharacter(x.billingPostal),
        billingCountry = MaskUtils.maskAnyCharacter(x.billingCountry),
        issuingBankName = MaskUtils.maskAnyCharacter(x.issuingBankName),
        issuingBankPhone = MaskUtils.maskAnyCharacter(x.issuingBankPhone),
        customerFirstName = MaskUtils.maskAnyCharacter(x.customerFirstName),
        customerLastName = MaskUtils.maskAnyCharacter(x.customerLastName),
        email = MaskUtils.maskAnyCharacter(x.email),
        objGuestName = Seq.empty
      )
      Extraction.decompose(newX)(format - this + dateTimeFormat)
    case x: PayoutApiData =>
      val newX = PayoutApiData(
        payoutUuid = x.payoutUuid,
        paymentMethod = MaskUtils.maskAnyCharacter(x.paymentMethod),
        paymentCondition = MaskUtils.maskAnyCharacter(x.paymentCondition),
        paymentDate = Some(java.time.LocalDate.parse("1970-01-01")),
        paymentMethodReference = None,
        creditCardId = x.creditCardId,
        currencyCode = MaskUtils.maskAnyCharacter(x.currencyCode),
        requestAmount = -1,
        cardBalance = Some(-1),
        last4digits = MaskUtils.maskAnyCharacter(x.last4digits),
        ccActivationDate = OffsetDateTime.parse("1970-01-01T00:00:00+00:00"),
        ccExpirationDate = OffsetDateTime.parse("1970-01-01T00:00:00+00:00"),
        cardNumber = MaskUtils.maskAnyCharacter(x.cardNumber),
        cardCVV = MaskUtils.maskAnyCharacter(x.cardCVV),
        expirationDate = MaskUtils.maskAnyCharacter(x.expirationDate),
        expirationMonth = -1,
        expirationYear = -1
      )
      Extraction.decompose(newX)(format - this + dateTimeFormat)
    case x: CreditCardInfo =>
      val newX = CreditCardInfo(
        cardNumber = MaskUtils.maskAnyCharacter(x.cardNumber),
        ccv = x.ccv.map(c => MaskUtils.maskAnyCharacter(c)),
        expireMonth = MaskUtils.maskAnyCharacter(x.expireMonth),
        expireYear = MaskUtils.maskAnyCharacter(x.expireYear),
        holderName = MaskUtils.maskAnyCharacter(x.holderName),
        cardType = null
      )
      Extraction.decompose(newX)(format - this + dateTimeFormat)
    case x: com.agoda.abspnx.client.models.abs.GuestInfo =>
      val newX = maskGuestInfo(x)
      Extraction.decompose(newX)(format - this + dateTimeFormat)
    case x: com.agoda.paymentapiv2.client.v2.common.model.Guest =>
      val newX = x.copy(
        FirstName = Some(MaskUtils.maskAnyCharacter(x.FirstName.getOrElse(""))),
        LastName = Some(MaskUtils.maskAnyCharacter(x.FirstName.getOrElse("")))
      )
      Extraction.decompose(newX)(format - this + dateTimeFormat)
    case x: UserContext =>
      val newX = x.copy(
        languageId = MaskUtils.maskNumber(x.languageId),
        requestOrigin = MaskUtils.maskAnyCharacter(x.requestOrigin),
        currency = x.currency,
        nationalityId = MaskUtils.maskNumber(x.nationalityId),
        experimentData = x.experimentData,
        isLoggedInUser = x.isLoggedInUser,
        clientIp = MaskUtils.maskAnyCharacter(x.clientIp),
        capiToken = MaskUtils.maskAnyCharacter(x.capiToken)
      )
      Extraction.decompose(newX)(format - this + dateTimeFormat)
    case x: FlightPax =>
      val newX = FlightPax(
        guestNo = x.guestNo,
        title = MaskUtils.maskAnyCharacter(x.title),
        firstname = MaskUtils.maskAnyCharacter(x.firstname),
        lastname = MaskUtils.maskAnyCharacter(x.lastname),
        primary = x.primary,
        middlename = MaskUtils.maskAnyCharacter(x.middlename),
        birthDate = LocalDate.parse("1970-01-01"),
        nationalityId = MaskUtils.maskNumber(x.nationalityId),
        isAdult = x.isAdult,
        gender = maskGender(x.gender),
        passportNumber = Some(MaskUtils.maskAnyCharacter(x.passportNumber.getOrElse(""))),
        passportExpires = Some(LocalDate.parse("1970-01-01")),
        passportCountryId = Some(MaskUtils.maskNumber(x.passportCountryId.getOrElse(0))),
        knownTravelerNumber = Some(MaskUtils.maskAnyCharacter(x.knownTravelerNumber.getOrElse("")))
      )
      Extraction.decompose(newX)(format - this + dateTimeFormat)
    case x: ReservationInfo =>
      val newX = x.copy(
        b2bEmailAddress = x.b2bEmailAddress.map(MaskUtils.maskAnyCharacter),
        guests = x.guests.map(_.map(maskGuestInfo))
      )
      Extraction.decompose(newX)(format - this + dateTimeFormat)
    case x: ActivityPax =>
      val newX = x.copy(
        title = x.title.map(MaskUtils.maskAnyCharacter),
        firstname = x.firstname.map(MaskUtils.maskAnyCharacter),
        lastname = x.lastname.map(MaskUtils.maskAnyCharacter),
        primary = x.primary,
        middlename = x.middlename.map(MaskUtils.maskAnyCharacter),
        birthDate = x.birthDate.map(_ => LocalDate.parse("1970-01-01")),
        nationalityId = x.nationalityId.map(MaskUtils.maskNumber),
        gender = x.gender.map(maskGender),
        passportNumber = Some(MaskUtils.maskAnyCharacter(x.passportNumber.getOrElse(""))),
        passportExpires = Some(LocalDate.parse("1970-01-01")),
        email = x.email.map(MaskUtils.maskAnyCharacter),
        phoneNumber = x.phoneNumber.map(MaskUtils.maskAnyCharacter),
        phoneCountryCode = x.phoneCountryCode.map(MaskUtils.maskAnyCharacter)
      )
      Extraction.decompose(newX)(format - this + dateTimeFormat)
  }

  private def maskGender(gender: String) =
    MaskUtils.maskAnyCharacter(gender.take(1))

  private def maskGenderOpt(gender: Option[String]) =
    MaskUtils.maskAnyCharacter(gender.map(x => x.take(1)))
}
