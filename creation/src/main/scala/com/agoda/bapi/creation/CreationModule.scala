package com.agoda.bapi.creation

import com.agoda.bapi.common.config._
import com.agoda.bapi.common.handler.{RequestContextBuilder, RequestContextBuilderImpl}
import com.agoda.bapi.common.localization.{LocaleContextFactory, LocaleContextFactoryImpl}
import com.agoda.bapi.common.proxy._
import com.agoda.bapi.common.proxy.provider.{CryptoMetadataServiceProvider, CustomerApiAsyncClientProviderV2Authed, CustomerApiAsyncClientProviderV2Base, FraudApiClientProvider, FraudHttpClientProvider}
import com.agoda.bapi.common.repository.{LocaleRepository, LocaleRepositoryImpl}
import com.agoda.bapi.common.util.{PaymentUtils, PaymentUtilsImpl, TripProtectionTokenHelper}
import com.agoda.bapi.creation.config._
import com.agoda.bapi.creation.handler.{<PERSON><PERSON><PERSON><PERSON>, CreationHandlerImpl}
import com.agoda.bapi.creation.mapper.activity.{ActivityMapper, ActivityMapperImpl}
import com.agoda.bapi.creation.mapper.addon.cegFastTrack.{CegFastTrackMapper, CegFastTrackMapperImpl}
import com.agoda.bapi.creation.mapper.addon.{AddOnMapper, AddOnMapperImpl}
import com.agoda.bapi.creation.mapper.ebe._
import com.agoda.bapi.creation.proxy._
import com.agoda.bapi.creation.proxy.db.basebooking.{ActivityBapiDbProxy, ActivityBkgDbProxy, ActivityDbReadProxy, BaseBookingDbReadProxy}
import com.agoda.bapi.creation.proxy.db.flight.{FlightBapiDbProxy, FlightBkgDbProxy, FlightsDbReadProxy}
import com.agoda.bapi.creation.proxy.db.protection.{ProtectionBapiDbProxy, ProtectionBkgDbProxy, ProtectionDbReadProxy}
import com.agoda.bapi.creation.proxy.db.vehicle.{VehicleBapiDbProxy, VehicleBkgDbProxy, VehicleDbRead}
import com.agoda.bapi.creation.proxy.db.addOns.{AddOnsBapiDbProxy, AddOnsBkgDbProxy}
import com.agoda.bapi.creation.proxy.provider._
import com.agoda.bapi.creation.repository._
import com.agoda.bapi.creation.service._
import com.agoda.bapi.creation.service.helper.{MPLogHelper, MPLogHelperImpl}
import com.agoda.bapi.creation.util._
import com.agoda.bapi.creation.validation._
import com.agoda.creditcardapi.client.v2.common.api.{CreditCardApi => CreditCardApiV2, CreditCardNonPciApi => CreditCardNonPciApiV2}
import com.agoda.fraud.client.FraudClient
import com.agoda.terracotta.clients.rest.FraudRestClient
import com.agoda.winterfell.client.CustomerApi
import com.agoda.winterfell.unified.progress.CryptoMetadataService
import com.google.inject.AbstractModule
import com.google.inject.name.Names
import net.codingwell.scalaguice.ScalaModule

import javax.inject.Singleton
import scala.concurrent.Future

class CreationModule extends AbstractModule with ScalaModule {
  override def configure(): Unit = {
    // config

    bind[CreationConfig].toProvider[CreationConfigProvider]
    bind[CreationConfig].toProvider[CreationConfigProvider]
    bind[DuplicateConfig].toProvider[DuplicateConfigProvider]
    bind[ValidationConfig].toProvider[ValidationConfigProvider]
    bind[CancellationChargeConfig].toProvider[DMCCancellationConfigProvider]
    bind[PropertyBookingCreationConfig].toProvider[PropertyBookingCreationConfigProvider].in[Singleton]
    // providers
    bind[CustomerApi].annotatedWithName("CustomerApiV2Async").toProvider[CustomerApiAsyncClientProvider].in[Singleton]
    bind[CustomerApi]
      .annotatedWithName("CustomerApiAsyncV2Authed")
      .toProvider[CustomerApiAsyncClientProviderV2Authed]
      .in[Singleton]
    bind[CustomerApi]
      .annotatedWithName("CustomerApiAsyncV2Base")
      .toProvider[CustomerApiAsyncClientProviderV2Base]
      .in[Singleton]
    bind[InstantBookBlockedPaymentMethodsConfig]
      .toProvider[InstantBookBlockedPaymentMethodsConfigProvider]
      .in[Singleton]
    bind[TestBookingConfig].toProvider[TestBookingConfigProvider].in[Singleton]
    bind[ReplicateStateConfig].toProvider[ReplicateStateConfigProvider].in[Singleton]
    bind[CryptoMetadataService].toProvider[CryptoMetadataServiceProvider].in[Singleton]
    bind[KillSwitches].to[ConsulKvBasedKillSwitches].in[Singleton]
    bind[AdditionalExperimentMeasureConfig].toProvider[AdditionalExperimentMeasureConfigProvider].in[Singleton]
    bind[NhaHotelIdMappingConfig].toProvider[NhaHotelIdMappingConfigProvider].in[Singleton]

    // handlers
    bind[CreationHandler].to[CreationHandlerImpl]

    // builders
    bind[RequestContextBuilder].to[RequestContextBuilderImpl]

    // factory
    bind[LocaleContextFactory].to[LocaleContextFactoryImpl]

    // validators
    bind[CreateBookingRequestValidator]
      .annotatedWith(Names.named("CreateBookingRequestV1Validator"))
      .to[CreateBookingRequestV1ValidatorImpl]
    bind[CreateBookingRequestValidator]
      .annotatedWith(Names.named("CreateBookingRequestV2Validator"))
      .to[CreateBookingRequestV2ValidatorImpl]
    bind[RequestFieldsChecker].to[RequestFieldsCheckerImpl]
    bind[CreditCardChecker].to[CreditCardCheckerImpl]
    bind[ExtendedCreditCardChecker].to[ExtendedCreditCardCheckerImpl]
    bind[AllowNoCreditCardChecker].to[AllowNoCreditCardCheckerImpl]
    bind[DuplicateRequestChecker].to[DuplicateRequestCheckerImpl]
    bind[NationalityChecker].to[NationalityCheckerImpl]
    bind[CountryOfResidenceChecker].to[CountryOfResidenceCheckerImpl]
    bind[PaymentChecker].annotatedWith(Names.named("PaymentCheckerV1")).to[PaymentCheckerV1Impl]
    bind[PaymentChecker].annotatedWith(Names.named("PaymentCheckerV2")).to[PaymentCheckerV2Impl]
    bind[PropertyChecker].to[PropertyCheckerImpl]
    bind[RoomChecker].to[RoomCheckerImpl]
    bind[FlightChecker].to[FlightCheckerImpl]
    bind[TripProtectionChecker].to[TripProtectionCheckerImpl]
    bind[FlightPaxListChecker].to[FlightPaxListCheckerImpl]
    bind[BNPLChecker].to[BNPLCheckerImpl]
    bind[BORChecker].to[BORCheckerImpl]
    bind[BookingAmountChecker].to[BookingAmountCheckerImpl]
    bind[GeneralChecker].to[GeneralCheckerImpl]
    bind[PartnerLoyaltyPointChecker].to[PartnerLoyaltyPointCheckerImpl]
    bind[ExternalLoyaltyAllPointsNoCashChecker].to[ExternalLoyaltyAllPointsNoCashCheckerImpl]
    bind[ExternalLoyaltyMinimumPointsRedemptionChecker]
      .to[ExternalLoyaltyMinimumPointsRedemptionCheckerImpl]
    bind[VoidBookingRequestValidator].to[VoidBookingRequestValidatorImpl]
    bind[JapanGovernmentCampaignChecker].to[JapanGovernmentCampaignCheckerImpl]
    bind[SingleHotelChecker].to[SingleHotelCheckerImpl]
    bind[PartnerLoginChecker].to[PartnerLoginCheckerImpl]
    bind[StateIdRequiredCampaignChecker].to[StateIdRequiredCampaignCheckerImpl]
    bind[ResellDMCHotelChecker].annotatedWith(Names.named("ResellDMCHotelCheckerV1")).to[ResellDMCHotelCheckerV1Impl]
    bind[ResellDMCHotelChecker].annotatedWith(Names.named("ResellDMCHotelCheckerV2")).to[ResellDMCHotelCheckerV2Impl]
    bind[PaymentPhoneChecker].annotatedWith(Names.named("PaymentPhoneCheckerV1")).to[PaymentPhoneCheckerV1Impl]
    bind[PaymentPhoneChecker].annotatedWith(Names.named("PaymentPhoneCheckerV2")).to[PaymentPhoneCheckerV2Impl]
    bind[PhoneNumberChecker].to[PhoneNumberCheckerImpl]
    bind[AABChecker].to[AABCheckerImpl]
    bind[MemberChecker].to[MemberCheckerImpl]
    bind[HotelComplianceChecker].to[HotelComplianceCheckerImpl]

    // services
    bind[ItineraryService].to[ItineraryServiceImpl]
    bind[UrlService].to[UrlServiceImpl]
    bind[FlightInfoService]
      .annotatedWithName("FlightInfoServiceV1")
      .to[FlightInfoServiceImpl]
    bind[FlightInfoService]
      .annotatedWithName("FlightInfoServiceV2")
      .to[FlightInfoServiceV2]
    bind[TPRMService].to[TPRMServiceImpl]
    bind[OrchestrationMessageService].to[OrchestrationMessageServiceImpl]
    bind[DuplicateCheckService]
      .annotatedWithName("DuplicateCheckServiceV1")
      .to[DuplicateCheckServiceImpl]
    bind[DuplicateCheckService]
      .annotatedWithName("DuplicateCheckServiceV2")
      .to[DuplicateCheckServiceV2]
    bind[UserAgentService].to[UserAgentServiceImpl]
    bind[BcreEndpointMessageService].to[BcreEndpointMessageServiceImpl]
    // stages

    // repositories
    bind[EbeLiteBookingRepository].to[EbeLiteBookingRepositoryImpl]
    bind[DuplicateFlightBookingRepository].to[FlightBookingRepositoryImpl]
    bind[FlightBookingRepository].to[FlightBookingRepositoryImpl]
    bind[PaymentMethodRepository].to[PaymentMethodRepositoryImpl]
    bind[CreditCardInfoRepository].to[CreditCardInfoRepositoryImpl]
    bind[WorkflowRepository].to[WorkflowRepositoryImpl]
    bind[MultiProductRepository].to[MultiProductRepositoryImpl]
    bind[ProtectionBookingRepository].to[ProtectionBookingRepositoryImpl]
    bind[VehicleBookingRepository].to[VehicleBookingRepositoryImpl]
    bind[LocaleRepository].to[LocaleRepositoryImpl]
    bind[BaseBookingRepository].to[BaseBookingRepositoryImpl]
    bind[ActivityBookingRepository].to[ActivityBookingRepositoryImpl]
    bind[PaymentLimitationRepository].to[PaymentLimitationRepositoryImpl]
    bind[ItineraryBookingRepository].to[ItineraryBookingRepositoryImpl]
    bind[CreationMdbRepository].to[CreationMdbRepositoryImpl]
    bind[CegFastTrackRepository].to[CegFastTrackRepositoryImpl]
    bind[GenericAddOnRepository].to[GenericAddOnRepositoryImpl]
    bind[CreationCdbRepository].to[CreationCdbRepositoryImpl]
    // proxies
    bind[CreationCDBProxy].to[CreationCDBProxyImpl]
    bind[DefaultDbProxy].to[EbeLiteDbProxyImpl]
    bind[FlightsDbProxy].to[FlightsDbProxyImpl]
    bind[FlightsDbReadProxy]
      .annotatedWithName("FlightsBkgDbProxy")
      .to[FlightBkgDbProxy]
    bind[FlightsDbReadProxy]
      .annotatedWithName("FlightsLocalBkgDbProxy")
      .to[FlightBapiDbProxy]
    bind[WorkflowDbProxy].to[WorkflowDbProxyImpl]
    bind[MultiProductDbProxy].to[MultiProductDbProxyImpl]
    bind[ProtectionDbProxy].to[ProtectionDbProxyImpl]
    bind[ProtectionDbReadProxy]
      .annotatedWithName("ProtectionBkgDbProxy")
      .to[ProtectionBkgDbProxy]
    bind[ProtectionDbReadProxy]
      .annotatedWithName("ProtectionLocalBkgDbProxy")
      .to[ProtectionBapiDbProxy]
    bind[VehicleDBProxy].to[VehicleDbProxyImpl]
    bind[VehicleDbRead]
      .annotatedWithName("VehicleBkgDbProxy")
      .to[VehicleBkgDbProxy]
    bind[VehicleDbRead]
      .annotatedWithName("VehicleLocalBkgDbProxy")
      .to[VehicleBapiDbProxy]
    bind[BaseBookingDBProxy].to[BaseBookingDbProxyImpl]
    bind[ActivityDbReadProxy]
      .annotatedWithName("ActivityBkgDbProxy")
      .to[ActivityBkgDbProxy]
    bind[ActivityDbReadProxy]
      .annotatedWithName("ActivityLocalBkgDbProxy")
      .to[ActivityBapiDbProxy]
    bind[BaseBookingDbReadProxy]
      .annotatedWithName("AddOnsBkgDbProxy")
      .to[AddOnsBkgDbProxy]
    bind[BaseBookingDbReadProxy]
      .annotatedWithName("AddOnsLocalBkgDbProxy")
      .to[AddOnsBapiDbProxy]
    bind[FinancialBreakdownProxy].to[FinancialBreakdownProxyImpl]
    bind[GenericProductActionDbProxy].to[GenericProductActionDbProxyImpl]
    bind[ActivityDbProxy].to[ActivityDbProxyImpl]
    bind[CegFastTrackDbProxy].to[CegFastTrackDbProxyImpl]
    bind[CegFastTrackActionDbProxy].to[CegFastTrackActionDbProxyImpl]
    bind[GenericAddOnDbProxy].to[GenericAddOnDbProxyImpl]
    bind[GenericAddOnActionDbProxy].to[GenericAddOnActionDbProxyImpl]

    bind[CreditCardApiLocalProxyV2].to[CreditCardApiLocalProxyV2Impl] // ccapi proxy v2
    bind[CreditCardApiAABProxyV2].to[CreditCardApiAABProxyV2Impl]     // ccapi aab proxy v2
    bind[CreditCardNonPciApiLocalProxyV2].to[CreditCardNonPciApiLocalProxyV2Impl]

    bind[AbsProxy].to[AbsProxyImpl]
    bind[CustomerApiProxy].to[CustomerApiProxyImpl]

    // utils
    bind[EBEUtils].toInstance(EBEUtils)
    bind[CancellationUtils].toInstance(CancellationUtils)
    bind[ProductUtils].to[ProductUtilsImpl]
    bind[TripProtectionTokenHelper].toInstance(TripProtectionTokenHelper)
    bind[PaymentUtils].to[PaymentUtilsImpl]
    bind[MPLogHelper].to[MPLogHelperImpl]
    bind[CustomerApiTokenUtils].to[CustomerApiTokenUtilsImpl]

    // client
    bind[CreditCardApiV2[Future]]
      .annotatedWithName("CreditcardApiLocalClientV2")
      .toProvider[CreditcardApiLocalClientV2Provider] // ccapi local v2
    bind[CreditCardApiV2[Future]]
      .annotatedWithName("CreditcardAABApiClientV2")
      .toProvider[CreditcardAABApiClientV2Provider]                                          // ccapi AAB v2
    bind[CreditCardNonPciApiV2[Future]].toProvider[CreditcardNonPciApiLocalClientV2Provider] // ccapi non pci v2

    bind[FraudRestClient].toProvider[FraudHttpClientProvider].in[Singleton]
    bind[FraudHttpClientProxy].to[FraudHttpClientProxyImpl]
    bind[FraudClient].toProvider[FraudApiClientProvider].in[Singleton]
    bind[FraudApiClientProxy].to[FraudApiClientProxyImpl]

    // mapper
    bind[FlightSeatSelectionDetailMapper].to[FlightSeatSelectionDetailMapperImpl]
    bind[FlightMapper].to[FlightMapperImpl]
    bind[SaveBookingMapper].to[SaveBookingMapperImpl]
    bind[BookingSummaryMapper].to[BookingSummaryMapperImpl]
    bind[MPMasterMapper].to[MPMasterMapperImpl]
    bind[PropertyModelInternalMapper].to[PropertyModelInternalMapperImpl]
    bind[EbeBookingChargeMapperUtils].to[EbeBookingChargeMapperUtilsImpl]
    bind[ProtectionMapper].to[ProtectionMapperImpl]
    bind[RiskInfoMapper].to[RiskInfoMapperImpl]
    bind[ActivityRiskInfoMapper].to[ActivityRiskInfoMapperImpl]
    bind[VehicleMapper].to[VehicleMapperImpl]
    bind[ActivityMapper].to[ActivityMapperImpl]
    bind[CegFastTrackMapper].to[CegFastTrackMapperImpl]
    bind[AddOnMapper].to[AddOnMapperImpl]
  }
}
