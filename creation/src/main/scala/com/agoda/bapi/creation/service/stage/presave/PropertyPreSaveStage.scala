package com.agoda.bapi.creation.service.stage.presave

import com.agoda.bapi.common.config.{CancellationChargeConfig, KillSwitches, NhaHotelIdMappingConfig, NhaHotelIdMappingConfigProvider}
import com.agoda.bapi.common.handler.{MeasurementsContext, RequestContext}
import com.agoda.bapi.common.message.creation.{CreateBookingRequest, CreateBookingResponse, Customer, HotelGuest, Products}
import com.agoda.bapi.common.model.WhiteLabel.WhiteLabel
import com.agoda.bapi.common.model.product.ProductTypeEnum.ProductTypeEnum
import com.agoda.bapi.common.model.product.{BookingRequestTypeResolver, ProductTypeEnum}
import com.agoda.bapi.common.model.{ItineraryId, WhiteLabel}
import com.agoda.bapi.common.proxy.EnigmaApiProxy
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.mapper.ebe.SaveBookingMapper
import com.agoda.bapi.creation.model.multi._
import com.agoda.bapi.creation.model.payment.SupplierSpecificData
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.bapi.creation.repository.{BaseBookingRepository, CreationCdbRepository, FlightBookingRepository, PaymentMethodRepository}
import com.agoda.bapi.creation.service.HadoopMessagingService
import com.agoda.bapi.creation.service.stage.PersonName
import com.agoda.bapi.creation.service.stage.PersonName.GuestNameMaxLength
import com.agoda.winterfell.Try

import javax.inject.Inject
import scala.concurrent.{ExecutionContext, Future}
import scala.language.implicitConversions

class PropertyPreSaveStage @Inject() (
    cancellationChargeConfig: CancellationChargeConfig,
    propertyMapper: SaveBookingMapper,
    val workflowRepository: FlightBookingRepository,
    val enigmaApiProxy: EnigmaApiProxy,
    paymentMethodRepository: PaymentMethodRepository,
    hadoopMessaging: HadoopMessagingService,
    baseBookingRepository: BaseBookingRepository,
    creationCdbRepository: CreationCdbRepository
)(implicit
    val executionContext: ExecutionContext,
    killSwitches: KillSwitches,
    nhaHotelIdMappingConfig: NhaHotelIdMappingConfig
) extends ProductPreSaveStage[
      MultiProductPreSaveRequest,
      RoomInfo,
      PropertySaveStageResponse,
      EmptyProductReservedIds
    ]
    with WithSaveItineraryBillingInfo {
  override def productType(preSaveProductStageRequest: PreSaveProductStageRequest[RoomInfo]): ProductTypeEnum =
    ProductTypeEnum.Property

  override protected def toPreSaveProductStageRequests(
      preSaveRequest: MultiProductPreSaveRequest
  ): Seq[PreSaveProductStageRequest[RoomInfo]] =
    preSaveRequest.multiProductsRequest.properties.zipWithIndex.map {
      case (product, index) =>
        PreSaveProductStageRequest(
          preSaveRequest.multiProductsRequest.request,
          preSaveRequest.multiProductsRequest.requestContext,
          preSaveRequest.itineraryReservedIds,
          preSaveRequest.multiProductIds.get(product.getMultiProductIdentifier),
          product,
          preSaveRequest.multiProductsRequest.bookingFlow,
          Some(index + 1),
          userTaxCountryId = preSaveRequest.multiProductsRequest.userTaxCountryId,
          userTaxCountry = preSaveRequest.multiProductsRequest.userTaxCountry
        )
    }

  private[presave] def swapHotelIdInReservedIds(
      reservedIds: ReservedIds[RoomInfo, EmptyProductReservedIds]
  ): ReservedIds[RoomInfo, EmptyProductReservedIds] = {
    val hotelMappings = nhaHotelIdMappingConfig.getHotelMappings
    val propertyId    = reservedIds.product.info.bapiBooking.propertyId

    hotelMappings.get(propertyId) match {
      case Some(childHotelId) =>
        val updatedBapiBooking = reservedIds.product.info.bapiBooking.copy(
          propertyId = childHotelId,
          booking = reservedIds.product.info.bapiBooking.booking.map(bookingItem =>
            bookingItem.copy(
              booking = bookingItem.booking.map(ebeBooking =>
                ebeBooking.copy(
                  hotel = ebeBooking.hotel.map(ebeHotel =>
                    ebeHotel.copy(
                      room = ebeHotel.room.map(room => room.copy(hotelId = childHotelId))
                    )
                  )
                )
              )
            )
          )
        )

        reservedIds.copy(
          product = reservedIds.product.copy(
            info = reservedIds.product.info.copy(
              bapiBooking = updatedBapiBooking
            )
          )
        )
      case None => reservedIds
    }
  }

  override protected def preSave(
      request: PreSaveProductStageRequest[RoomInfo]
  )(implicit
      measurementCxt: MeasurementsContext,
      context: RequestContext
  ): Future[Either[CreateBookingResponse, PropertySaveStageResponse]] = {
    val productTokenKey                             = request.product.info.bapiBooking.productTokenKey
    val guestList                                   = request.request.getGuestList(productTokenKey)
    val primaryGuest                                = request.request.getPrimaryGuest(productTokenKey)
    val itineraryId                                 = request.itineraryInfo.itineraryId
    val whitelabelId                                = request.requestContext.whiteLabelInfo.whiteLabelId.id
    implicit val featureAware: Option[FeatureAware] = request.requestContext.featureAware
    for {
      reservedIds <- measuredReservedProductId(request)
      // this change is only for NHA POC, need to be removed once POC is concluded
      updatedReservedIds = swapHotelIdInReservedIds(reservedIds)
      _ <- saveDataToEnigma(
             request.request,
             trimExcessNames(request.request.customer),
             updatedReservedIds.bookingId,
             guestList.map(trimExcessNames(_)),
             trimExcessNames(primaryGuest),
             Try(WhiteLabel(whitelabelId)).getOrElse(WhiteLabel.Agoda),
             itineraryId,
             getTrimmedBookerAnswer(request.request.products)
           )
      paymentMethodInfo <- paymentMethodRepository.getPaymentMethodInfo(
                             request.request.payment.method.value,
                             BookingRequestTypeResolver.getProductType(request.request).id,
                             whitelabelId
                           )
      supplierData = getSupplierData(request.product.info)
      propertyId   = updatedReservedIds.product.info.bapiBooking.propertyId
      masterHotelId <- creationCdbRepository
                         .getMasterHotelIdFromJTBMapping(propertyId)
                         .map(_.getOrElse(propertyId))
      propertyDbModel <- propertyMapper.mapPropertyBookingCreationLocal(
                           itineraryId,
                           request,
                           request.product.bookingType,
                           updatedReservedIds,
                           Some(supplierData),
                           masterHotelId
                         )
      bookingAction <- propertyMapper.mapBookingAction(
                         request,
                         request.itineraryInfo.itineraryId,
                         updatedReservedIds,
                         request.itineraryInfo.tripStart,
                         request.itineraryInfo.tripEnd,
                         paymentMethodInfo,
                         request.productIndex,
                         getCancellationChargeItemIdFromConfig(request.product.info),
                         request.itineraryInfo.actionId
                       )
    } yield Right(PropertySaveStageResponse(bookingAction, propertyDbModel, productTokenKey = productTokenKey))
  }

  override protected def reservedProductId(
      request: PreSaveProductStageRequest[RoomInfo]
  )(implicit measurementCxt: MeasurementsContext): Future[ReservedIds[RoomInfo, EmptyProductReservedIds]] = {
    for {
      bookingId <- workflowRepository.getNextBookingSequenceNumber
      actionId  <- workflowRepository.getNextActionIdSequenceNumber
      essInfoId <- baseBookingRepository.getNextBaseBookingEssInfoSequenceNumber
    } yield ReservedIds(
      bookingId = bookingId,
      actionId = actionId,
      multiProductId = request.multiProductId.map(_.multiProductId),
      product = request.product,
      essInfoId = Option(essInfoId)
    )
  }

  private def trimExcessNames[T](personName: PersonName[T])(implicit featureAware: Option[FeatureAware]): T = {
    val isFirstNameLengthExceeded  = personName.firstname.length > GuestNameMaxLength
    val isMiddleNameLengthExceeded = personName.middlename.length > GuestNameMaxLength
    val isLastNameLengthExceeded   = personName.lastname.length > GuestNameMaxLength
    val person                     = personName.person
    if (isFirstNameLengthExceeded || isMiddleNameLengthExceeded || isLastNameLengthExceeded)
      personName.trimNames(GuestNameMaxLength)
    else
      person
  }

  private[presave] def saveDataToEnigma(
      request: CreateBookingRequest,
      customer: Customer,
      bookingId: Long,
      guestList: Seq[HotelGuest],
      primaryGuest: HotelGuest,
      whiteLabel: WhiteLabel,
      itineraryId: ItineraryId,
      bookerAnswer: Option[String]
  )(implicit measurementCxt: MeasurementsContext, requestContext: RequestContext): Future[_] = {
    withMeasureAndLogWithContext(measurementCxt)(creationMeasurementName("property.SaveDataToEnigma")) {
      for {
        _ <- enigmaApiProxy.savePropertyCustomerContact(
               bookingId.toInt,
               customer,
               primaryGuest,
               whiteLabel,
               bookerAnswer
             )
        _ <- enigmaApiProxy.savePropertyGuests(bookingId.toInt, guestList, whiteLabel)
        _ <- saveItineraryBillingInfo(
               request.payment.requiredFields,
               itineraryId,
               request.payment.creditCard.flatMap(_.phoneNumber)
             )
        _ <- hadoopMessaging.sendBapiCreateBookingMissingGuests(bookingId.toInt, guestList)
      } yield {}
    }
  }

  private[presave] def getCancellationChargeItemIdFromConfig(
      product: RoomInfo
  ): Option[Int] = {
    product.bapiBooking.booking.headOption
      .flatMap(_.booking.headOption.flatMap(_.hotel.headOption.map(_.room.headOption)))
      .flatMap(roomOpt => roomOpt.map(rm => evaluateCxlBreakDownId(rm.dmcId)))
  }
  private[presave] def getSupplierData(
      product: RoomInfo
  )(implicit measurementCxt: MeasurementsContext): SupplierSpecificData = {
    withMeasureAndLogProcess(measurementCxt)(creationMeasurementName("property.GetSupplierData")) { () =>
      {
        val breakdownItemIdsForCxlCharge = product.bapiBooking.booking.headOption
          .flatMap(_.booking.headOption.flatMap(_.hotel.headOption.map(_.room.headOption)))
          .flatMap(roomOpt => roomOpt.map(rm => rm.uid -> evaluateCxlBreakDownId(rm.dmcId)))
          .toMap
        SupplierSpecificData(breakdownItemIdsForCxlCharge)
      }
    }
  }

  private def evaluateCxlBreakDownId(dmcIdOpt: Option[Int]): Int = {
    cancellationChargeConfig.dmcConfig
      .find(conf => dmcIdOpt.contains(conf.dmcId))
      .map(_.breakdownItemIdForCxlCharge)
      .getOrElse(cancellationChargeConfig.defaultBreakdownItemIdForCxlCharge)
  }

  private val EnigmaBookerAnswerLengthLimit = 512
  private val BookerAnswerExampleLimit      = 1024
  private[presave] def getTrimmedBookerAnswer(
      products: Products
  ): Option[String] = for {
    propertyItem     <- products.propertyItems.flatMap(_.headOption)
    specialRequestV2 <- propertyItem.specialRequestV2
    bookerAnswer     <- specialRequestV2.bookerAnswer
  } yield
    if (bookerAnswer.length > EnigmaBookerAnswerLengthLimit) {
      val trimmedBookerAnswer = bookerAnswer.take(EnigmaBookerAnswerLengthLimit)
      val logBookerAnswer     = bookerAnswer.take(BookerAnswerExampleLimit)
      logger.warn(s"BookerAnswer is exceeding 512 characters: $logBookerAnswer")
      trimmedBookerAnswer
    } else bookerAnswer

}
