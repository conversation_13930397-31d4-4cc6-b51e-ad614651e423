package com.agoda.bapi.creation.proxy

import com.agoda.bapi.common.database.AGDB
import com.agoda.bapi.common.message.PropertyBookingStateWithItinerary
import com.agoda.bapi.common.model.BFDBExecutionContext
import com.agoda.mpb.common.models.state.ProductType
import com.agoda.mpb.common.models.state.ProductType.ProductType
import com.google.inject.Inject
import com.softwaremill.quicklens._

import scala.concurrent.{ExecutionContext, Future}

trait PropertyActionDbProxy extends CommonBFDBProxy {
  def productType: ProductType = ProductType.Hotel
  def insertPropertyBookingDetailInBfdb(
      propertyBookingState: PropertyBookingStateWithItinerary
  ): Future[PropertyBookingStateWithItinerary]
}

class PropertyActionDbProxyImpl @Inject() (
    val db: AGDB,
    val bfdbExecutionContext: BFDBExecutionContext,
    genericProductActionDbProxy: GenericProductActionDbProxy,
    baseBookingDBProxy: BaseBookingDBProxy
) extends PropertyActionDbProxy {
  implicit override val dbDispatcher: ExecutionContext = bfdbExecutionContext.executionContext
  def insertPropertyBookingDetailInBfdb(
      propertyBookingStateWithItinerary: PropertyBookingStateWithItinerary
  ): Future[PropertyBookingStateWithItinerary] = {
    withExecutionTimeMeasure(metricName(), getMethodTag("insertProperty")) {
      Future {
        db.withConnectionGroup(bcreConnGroup.value) { implicit conn =>
          val autoCommitInitialState = conn.getAutoCommit
          conn.setAutoCommit(false)

          val updatedProperties = propertyBookingStateWithItinerary.properties.map { property =>
            {
              property
                .modify(_.propertyProductModel.each.essInfos.each)
                .using(baseBookingDBProxy.upsertBaseBookingEssInfo(_))
                .modify(_.propertyProductModel.each.baseBooking.each)
                .using(baseBookingDBProxy.upsertBaseBooking)
            }
          }

          conn.commit()
          conn.setAutoCommit(autoCommitInitialState)

          propertyBookingStateWithItinerary.copy(
            properties = updatedProperties
          )
        }
      }
    }
  }
}
