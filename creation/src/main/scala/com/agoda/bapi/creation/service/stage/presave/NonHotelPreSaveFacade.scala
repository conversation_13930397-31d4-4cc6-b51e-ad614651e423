package com.agoda.bapi.creation.service.stage.presave

import cats.data.EitherT
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.CreateBookingResponse
import com.agoda.bapi.common.model.activity.ActivityBookingToken
import com.agoda.bapi.common.model.addOn.cegFastTrack.AddOnBookingToken
import com.agoda.bapi.common.model.car.CarBookingToken
import com.agoda.bapi.common.model.flight.FlightBookingToken
import com.agoda.bapi.common.model.addOn
import com.agoda.bapi.creation.model.multi._

import scala.concurrent.{ExecutionContext, Future}

class NonHotelPreSaveFacade(
    flightPreSaveProcess: ProductPreSaveStage[
      MultiProductPreSaveRequest,
      FlightBookingToken,
      NonPropertySaveStageResponse,
      FlightReservedIds
    ],
    vehiclePreSaveProcess: ProductPreSaveStage[
      MultiProductPreSaveRequest,
      CarBookingToken,
      NonPropertySaveStageResponse,
      VehicleReservedIds
    ],
    activityPreSaveProcess: ProductPreSaveStage[
      MultiProductPreSaveRequest,
      ActivityBookingToken,
      NonPropertySaveStageResponse,
      EmptyProductReservedIds
    ],
    protectionPreSaveProcess: ProductPreSaveStage[
      MultiProductPreSaveRequestWithDependencies,
      ProtectionAncillaryModel,
      NonPropertySaveStageResponse,
      EmptyProductReservedIds
    ],
    cegFastTrackPreSaveProcess: ProductPreSaveStage[
      MultiProductPreSaveRequest,
      AddOnBookingToken,
      NonPropertySaveStageResponse,
      EmptyProductReservedIds
    ],
    addOnPreSaveProcess: ProductPreSaveStage[
      MultiProductPreSaveRequest,
      addOn.AddOnBookingToken,
      NonPropertySaveStageResponse,
      EmptyProductReservedIds
    ]
)(implicit ec: ExecutionContext, context: RequestContext) {
  def preSave(
      preSaveRequest: MultiProductPreSaveRequest
  ): Future[Either[CreateBookingResponse, Seq[NonPropertySaveStageResponse]]] = {

    val flightProcessF       = flightPreSaveProcess.process(preSaveRequest)
    val vehicleProcessF      = vehiclePreSaveProcess.process(preSaveRequest)
    val activityProcessF     = activityPreSaveProcess.process(preSaveRequest)
    val cegFastTrackProcessF = cegFastTrackPreSaveProcess.process(preSaveRequest)
    val addOnProcessF        = addOnPreSaveProcess.process(preSaveRequest)

    (for {
      flightPreSaveResults   <- EitherT(flightProcessF)
      vehiclePreSaveResults  <- EitherT(vehicleProcessF)
      activityPreSaveResults <- EitherT(activityProcessF)
      protectionProcessF =
        protectionPreSaveProcess.process(preSaveRequest.withDependencies(flightPreSaveResults, vehiclePreSaveResults))

      protectionPreSaveResults   <- EitherT(protectionProcessF)
      cegFastTrackPreSaveResults <- EitherT(cegFastTrackProcessF)
      addOnPreSaveResults        <- EitherT(addOnProcessF)
    } yield flightPreSaveResults ++
      vehiclePreSaveResults ++
      activityPreSaveResults ++
      protectionPreSaveResults ++
      cegFastTrackPreSaveResults ++
      addOnPreSaveResults).value
  }
}
