package com.agoda.bapi.creation.model.db

import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.common.model.itinerary.ItineraryInternalModel
import com.agoda.bapi.common.model.multiproduct.{MultiProductBookingGroupDBModel, MultiProductInfoDBModel}

final case class MultiProductBookingInsertionModel(
    multiProductsInfo: Seq[MultiProductInfoDBModel],
    workflowActions: Seq[BookingWorkflowAction],
    itineraryModel: ItineraryInternalModel,
    flightBookingActionStates: Seq[BookingActionState],
    propertyModels: Seq[PropertyBookingCreationLocal],
    vehicleBookingActionStates: Seq[BookingActionState],
    protectionBookingActionStates: Seq[BookingActionState],
    activityBookingActionState: Seq[BookingActionState],
    cegFastTrackBookingActionState: Seq[BookingActionState],
    addOnsBookingActionState: Seq[BookingActionState],
    multiProductBookingGroups: Seq[MultiProductBookingGroupDBModel],
    operationId: Option[Long] = None
)
