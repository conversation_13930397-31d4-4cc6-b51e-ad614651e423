package com.agoda.bapi.creation.proxy

import com.agoda.bapi.common.database._
import com.agoda.bapi.common.exception.DbException
import com.agoda.bapi.common.model.booking.local.{BookingActionWorkflowPhase, BookingActionWorkflowState, BookingWorkflowAction}
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.proxy.DependencyNames.Dependency
import com.agoda.bapi.common.proxy.{DBConnectionGroup, DbProxy, DependencyNames}
import com.agoda.bapi.creation.model.db.BookingActionIdentifier
import com.agoda.bapi.creation.proxy.EbeLiteDbProxyImpl.mapSetPropertyBookingStateResult
import com.agoda.bapi.creation.service.BookingActionMessage
import com.agoda.bapi.creation.util.ToolSet
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.sql.ResultSetHelper
import com.typesafe.config.Config

import java.sql.{CallableStatement, SQLException, Timestamp}
import javax.inject.Inject
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal

/**
  * marker trait only
  */
trait WorkflowDbProxy extends DbProxy with WorkflowDbUpserts with WorkflowQueries {
  override protected def dependency: Dependency = DependencyNames.BfdbBcreMetric
}

trait WorkflowQueries {
  def getBookingAction(actionId: ActionId): Future[Option[BookingWorkflowAction]]

  def getBookingActionByBookingId(bookingId: FlightBookingId): Future[Option[BookingWorkflowAction]]

  def getBookingActionByWorkflowId(
      itineraryId: ItineraryId,
      workflowId: WorkflowId
  ): Future[Option[BookingWorkflowAction]]

  def getBookingActionMessage(actionId: ActionId): Future[Seq[BookingActionMessage]]

  def getBookingActionBySupplierBookingId(
      supplierBookingId: String,
      topicId: TopicId,
      uuid: String
  ): Future[Option[BookingWorkflowAction]]

  def getMasterBookingActionByItineraryId(ItineraryId: Long): Future[Option[BookingWorkflowAction]]

  def getBookingActionByItineraryId(itineraryId: ItineraryId): Future[Seq[BookingWorkflowAction]]

  def getBaseBookingActionBySupplierBookingId(
      supplierBookingId: String,
      topicId: TopicId,
      uuid: String
  ): Future[Option[BookingWorkflowAction]]

  def getBookingActionIdentifierByBookingIdAndTopicId(
      bookingId: FlightBookingId,
      topicId: TopicId,
      recCreatedBy: String
  ): Future[Option[BookingActionIdentifier]]
}

trait WorkflowDbUpserts {
  def insertBookingAction(bookingAction: BookingWorkflowAction): Future[BookingWorkflowAction]
  def insertBookingActionMessage(message: BookingActionMessage): Future[Long]
  def updateBookingActionMessage(messageId: Long, content: String): Future[Int]
}

class WorkflowDbProxyImpl @Inject() (
    val db: AGDB,
    dbExecutionContext: BFDBExecutionContext,
    config: Config
) extends WorkflowDbProxy
    with WorkflowDbProxyAction
    with SqlSupport
    with ToolSet
    with ResultSetHelper {
  implicit val dbDispatcher: ExecutionContext = dbExecutionContext.executionContext

  private val maxRetries: Int  = config.getInt("sql-execution.maxRetries")
  val bcreConnGroupId: DBGroup = DBConnectionGroup.BFDB_BCRE

  override def getBookingAction(actionId: ActionId): Future[Option[BookingWorkflowAction]] = {
    val queryName      = "bcre_get_booking_action_v2"
    val queryToExecute = query(s"EXEC dbo.$queryName @actionId = ?", actionId)
    executeQuery(
      bcreConnGroupId,
      queryName,
      queryToExecute,
      WorkflowQueriedDataMapper.toBookingAction
    )
  }

  override def insertBookingAction(
      bookingAction: BookingWorkflowAction
  ): Future[BookingWorkflowAction] = {
    executeCallableStatement(
      bcreConnGroupId,
      insertBookingActionScope.queryName,
      CallableQuery(insertBookingActionScope.queryToExecute),
      insertBookingActionScope.insertBookingActionCallableStmtExecBlock(bookingAction, _)
    ).recoverWith {
      case NonFatal(e) => Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def getBookingActionByWorkflowId(
      itineraryId: ItineraryId,
      workflowId: WorkflowId
  ): Future[Option[BookingWorkflowAction]] = {
    val queryName      = "bcre_get_booking_action_by_workflow_id_v2"
    val queryToExecute = query(s"EXEC dbo.$queryName @itineraryId = ?, @workflowId = ?", itineraryId, workflowId)
    executeQuery(
      bcreConnGroupId,
      queryName,
      queryToExecute,
      WorkflowQueriedDataMapper.toBookingAction
    )
  }

  override def insertBookingActionMessage(message: BookingActionMessage): Future[Long] = {

    val callableStmtExecBlock = { callable: CallableStatement =>
      callable.setLong(1, message.actionId)
      callable.setInt(2, message.topic)
      callable.setString(3, message.content)
      callable.setString(4, message.recCreatedBy)
      callable.setTimestamp(5, new Timestamp(message.recCreatedWhen.getMillis))
      callable.registerOutParameter(6, java.sql.Types.INTEGER)
      callable.execute()
      val rowsAffected = callable.getLong(6)
      if (rowsAffected < 1)
        throw new SQLException(s"Cannot insert booking action message with actionId: ${message.actionId}")
      else
        rowsAffected
    }
    val queryName     = "bcre_creation_booking_action_message_insert_v1"
    val migratedQuery = s"""{ call dbo.$queryName (?,?,?,?,?,?) }"""

    executeCallableStatement(
      bcreConnGroupId,
      queryName,
      CallableQuery(migratedQuery),
      callableStmtExecBlock
    ).recoverWith {
      case NonFatal(e) => Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def getBookingActionMessage(actionId: ActionId): Future[Seq[BookingActionMessage]] = {
    val queryName      = "bcre_get_booking_action_message_v2"
    val queryToExecute = query(s"EXEC dbo.$queryName @actionId = ?", actionId)
    executeQuery(
      bcreConnGroupId,
      queryName,
      queryToExecute,
      WorkflowQueriedDataMapper.toBookingActionMessage
    )
  }

  override def getBookingActionBySupplierBookingId(
      supplierBookingId: String,
      topicId: TopicId,
      uuid: String
  ): Future[Option[BookingWorkflowAction]] = {
    val queryName = "bcre_get_booking_action_by_supplier_booking_id_v2"
    val queryToExecute = query(
      s"EXEC dbo.$queryName @supplierBookingId = ?, @topicId = ?, @recCreatedBy = ?",
      supplierBookingId,
      topicId,
      uuid
    )
    executeQuery(
      bcreConnGroupId,
      queryName,
      queryToExecute,
      WorkflowQueriedDataMapper.toBookingAction
    )
  }

  override def getBookingActionByBookingId(bookingId: FlightBookingId): Future[Option[BookingWorkflowAction]] = {
    val queryName      = "bcre_get_booking_action_by_booking_id_v2"
    val queryToExecute = query(s"EXEC dbo.$queryName @bookingId = ?", bookingId)
    executeQuery(
      bcreConnGroupId,
      queryName,
      queryToExecute,
      WorkflowQueriedDataMapper.toBookingAction
    )
  }

  def getMasterBookingActionByItineraryId(itineraryId: Long): Future[Option[BookingWorkflowAction]] = {
    val queryName      = "bcre_get_master_booking_action_by_itinerary_id_v3"
    val queryToExecute = query(s"EXEC dbo.${queryName} @itinerary_id = ?", itineraryId)
    executeQuery(
      bcreConnGroupId,
      queryName,
      queryToExecute,
      WorkflowQueriedDataMapper.toBookingAction
    )
  }

  override def getBookingActionByItineraryId(itineraryId: ItineraryId): Future[Seq[BookingWorkflowAction]] = {
    val queryName      = "bcre_get_booking_action_by_itinerary_id_v2"
    val queryToExecute = query(s"EXEC dbo.${queryName} @itinerary_id = ?", itineraryId)
    executeQueryWithRetry(
      bcreConnGroupId,
      queryName,
      queryToExecute,
      itineraryId.toInt,
      WorkflowQueriedDataMapper.toBookingActionList,
      maxRetries
    )
  }

  override def getBaseBookingActionBySupplierBookingId(
      supplierBookingId: String,
      topicId: TopicId,
      uuid: PNR
  ): Future[Option[BookingWorkflowAction]] = {
    val queryName = "bcre_get_base_booking_action_by_supplier_booking_id_v2"
    val queryToExecute = query(
      s"EXEC dbo.$queryName @supplierBookingId = ?, @topicId = ?, @recCreatedBy = ?",
      supplierBookingId,
      topicId,
      uuid
    )
    executeQuery(
      bcreConnGroupId,
      queryName,
      queryToExecute,
      WorkflowQueriedDataMapper.toBookingAction
    )
  }

  override def getBookingActionIdentifierByBookingIdAndTopicId(
      bookingId: FlightBookingId,
      topicId: TopicId,
      recCreatedBy: String
  ): Future[Option[BookingActionIdentifier]] = {
    val queryName = "bcre_get_booking_action_identifier_by_booking_id_and_topic_id_v1"
    val sqlQuery = query(
      s"EXEC dbo.$queryName @bookingId = ?, @topicId = ?, @recCreatedBy = ?",
      bookingId,
      topicId,
      recCreatedBy
    )

    executeQuery(
      group = bcreConnGroupId,
      queryName = queryName,
      query = sqlQuery,
      extractData = WorkflowQueriedDataMapper.toBookingActionIdentifier
    )
  }

  override def updateBookingActionMessage(messageId: Long, content: String): Future[Int] = {
    val queryName = "bcre_update_booking_action_message_v1"
    val sqlQuery  = query(s"EXEC dbo.$queryName @messageId = ?, @content = ?", messageId, content)

    executeQuery(
      bcreConnGroupId,
      queryName,
      sqlQuery,
      mapSetPropertyBookingStateResult
    ) recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }
}
