package com.agoda.bapi.creation.service.stage

import akka.actor.Scheduler
import cats.implicits._
import com.agoda.bapi.agent.common.schema.BookingActionMessageTopic.{BAM_Topic_JTBPartnerProvisioningResult, BAM_Topic_ProvisioningResult}
import com.agoda.bapi.agent.common.schema.{AgentBookingActionMessage, JtbPartnerProvisioningResult, ProvisioningResult}
import com.agoda.bapi.common.config.KillSwitches
import com.agoda.bapi.common.handler.{MeasurementsContext, RequestContext}
import com.agoda.bapi.common.message.creation._
import com.agoda.bapi.common.message.{ActivityBookingStateWithItinerary, AddOnBookingStateWithItinerary, CegFastTrackBookingStateWithItinerary}
import com.agoda.bapi.common.model.car.VehicleBookingState
import com.agoda.bapi.common.model.car.VehicleBookingStateModel.VehicleModelInternal
import com.agoda.bapi.common.model.flight.flightModel.FlightModelInternal
import com.agoda.bapi.common.model.itinerary.ItineraryInternalModel
import com.agoda.bapi.common.model.relationship.RelationshipTypes
import com.agoda.bapi.common.model.{BookingId, Storefront, WhiteLabel}
import com.agoda.bapi.common.util.ItineraryAssociatedBookingsTokenUtils
import com.agoda.bapi.creation.config.DuplicateConfig
import com.agoda.bapi.creation.model.db.ebelite.EbeLiteBookingSummary
import com.agoda.bapi.creation.model.db.{DuplicatedBookingCandidate, MultiProductBookingInsertionModel}
import com.agoda.bapi.creation.model.multi.MultiProductSaveStageRequest
import com.agoda.bapi.creation.repository._
import com.agoda.bapi.creation.service.flow.ProcessStage
import com.agoda.bapi.creation.service.{BookingActionMessageService, HadoopMessagingService, OrchestrationMessageService, UrlService}
import com.agoda.bapi.creation.util.BookingActionStateHelper.getAllowRetryPayment
import com.agoda.bapi.creation.util.DuplicateCheckUtil.hashPiiData
import com.agoda.bapi.creation.util.PropertyDuplicationCheckUtil.mapLocalizedNames
import com.agoda.bapi.creation.util.{ToolSet, WhitelabelUtils}
import com.agoda.bapi.execution.context.NonBlockingExecutionContext.default
import com.agoda.mpb.common.MultiProductType
import com.agoda.mpb.common.errors.ErrorCode
import scalapb.json4s.JsonFormat

import scala.concurrent.Future

class MultiProductSaveStage(
    urlService: UrlService,
    flightBookingRepository: FlightBookingRepository,
    multiProductRepository: MultiProductRepository,
    orchestrationMessageService: OrchestrationMessageService,
    itineraryAssociatedBookingsTokenUtils: ItineraryAssociatedBookingsTokenUtils,
    vehicleBookingRepository: VehicleBookingRepository,
    bookingActionMessageService: BookingActionMessageService,
    hadoopMessaging: HadoopMessagingService,
    activityBookingRepository: ActivityBookingRepository,
    itineraryBookingRepository: ItineraryBookingRepository,
    creationMdbRepository: CreationMdbRepository,
    cegFastTrackRepository: CegFastTrackRepository,
    genericAddOnRepository: GenericAddOnRepository,
    scheduler: Scheduler,
    duplicateConfig: DuplicateConfig,
    creationCdbRepository: CreationCdbRepository
)(implicit killSwitches: KillSwitches)
    extends ProcessStage[MultiProductSaveStageRequest, CreateBookingResponse, CreateBookingResponse]
    with ToolSet {

  import MultiProductSaveStage._

  override def process(
      request: MultiProductSaveStageRequest
  ): Future[Either[CreateBookingResponse, CreateBookingResponse]] = {
    implicit val measurementCxt: MeasurementsContext = request.measurementsContext
    withMeasureAndLogWithContext(measurementCxt)(creationMeasurementName("SaveStage")) {
      implicit val requestContext: RequestContext = request.requestContext
      val urlMappingF =
        urlService.getSelfServiceURLs(
          request.requestContext.whiteLabelInfo.whiteLabelId,
          isDomainIncluded = false
        )
      val savedBookingModel   = request.saveBookingModel
      val whiteLabelId        = requestContext.whiteLabelInfo.whiteLabelId
      val attemptId           = getAttemptId(request)
      val stopBfdbReplication = requestContext.featureAware.exists(_.stopBfdbReplication)
      (for {
        _                       <- sendBookingActionSizeMessage(savedBookingModel)
        updatedSaveBookingModel <- insertMultiBooking(request)
        _                       <- sendFlightHadoopMessage(updatedSaveBookingModel, stopBfdbReplication)
        _ = if (isInstantBooking(request))
              processProvisioningMessage(updatedSaveBookingModel, whiteLabelId)
        _ <- sendVehicleHadoopMessage(updatedSaveBookingModel, stopBfdbReplication)
        _ <- sendActivityHadoopMessage(updatedSaveBookingModel, stopBfdbReplication)
        _ <- sendCegFastTrackHadoopMessage(updatedSaveBookingModel, stopBfdbReplication)
        _ <- if (stopBfdbReplication) Future.unit
             else sendAddOnReplicateMessage(updatedSaveBookingModel)
        _ <-
          if (isHotelBookingFlowOnly(savedBookingModel) && !stopBfdbReplication)
            sendItineraryReplicateMessage(savedBookingModel) // when integrate MPBE-4506, remove the whole if-else block
          else Future.successful()

        _ <- orchestrationMessageService
               .sendCreateBookingMessage(
                 request,
                 savedBookingModel.itineraryModel.itinerary.itineraryId,
                 getOperationIdExperimentAware(
                   savedBookingModel,
                   updatedSaveBookingModel,
                   stopBfdbReplication
                 ),
                 attemptId
               )

        urlMapping <- urlMappingF

        itineraryResponseInfo = mapSuccessCreateBookingResponse(request, urlMapping)
      } yield {
        val response = CreateBookingResponse.itinerarySuccess(itineraryResponseInfo)
        hadoopMessaging.sendBapiCreateFactLogMessage(request, response, "MultiProductSaveStage")
        Right(response)
      }).recover {
        case exception: Exception =>
          logError(exception)
          // todo; have to clarify with error code we gonna used for each stage
          val recoveredResponse = CreateBookingResponse.technical(ErrorCode.UnexpectedInternalError, Some(exception))
          hadoopMessaging.sendBapiCreateFactLogMessage(request, recoveredResponse, "MultiProductSaveStage")
          Left(recoveredResponse)
      }
    }
  }

  private def getOperationIdExperimentAware(
      savedBookingModel: MultiProductBookingInsertionModel,
      updatedSaveBookingModel: MultiProductBookingInsertionModel,
      stopBfdbReplication: Boolean
  ): Option[Long] = {
    if (stopBfdbReplication) savedBookingModel.operationId
    else updatedSaveBookingModel.operationId
  }

  private[stage] def getAttemptId(request: MultiProductSaveStageRequest): Option[String] = {
    val isRetryEligible =
      getAllowRetryPayment(request.hasFlightsMainProductOnly, request.requestContext).getOrElse(false)
    val publishAttemptIdEnabled = request.requestContext.featureAware.exists(_.publishAttemptId)

    if (isRetryEligible || publishAttemptIdEnabled) {
      Some(java.util.UUID.randomUUID.toString)
    } else {
      None
    }

  }

  /* TODO: After integrate MPBE-4506, Change method name to insert BFDB EBE Lite, booking actions, workaround tables,
   * and duplicateBookingCandidates. */
  private[stage] def insertMultiBooking(
      request: MultiProductSaveStageRequest
  ): Future[MultiProductBookingInsertionModel] = {
    implicit val salt: String                   = duplicateConfig.salt
    implicit val requestContext: RequestContext = request.requestContext

    val resultF      = multiProductRepository.insertMultiBooking(request.saveBookingModel)
    val featureAware = requestContext.featureAware

    resultF.foreach(
      _.propertyModels
        .flatMap(_.summary)
        .map { summary =>
          val masterHotelIdFuture: Future[Long] = creationCdbRepository
            .getMasterHotelIdFromJTBMapping(summary.masterHotelId)
            .map(_.getOrElse(summary.masterHotelId))
          for {
            masterHotelId <- masterHotelIdFuture
            candidates     = List(buildDuplicateCandidate(request, summary, masterHotelId))
            _ <- Future.sequence(
                   candidates.map(candidate =>
                     creationMdbRepository.insertDuplicatedBookingCandidate(
                       candidate,
                       featureAware,
                       scheduler
                     )
                   )
                 )
          } yield {}
        }
    )

    resultF.map { result =>
      if (featureAware.exists(_.stopBfdbReplication)) {
        request.saveBookingModel
      } else {
        request.saveBookingModel.copy(
          activityBookingActionState = result.activityBookingActionState,
          cegFastTrackBookingActionState = result.cegFastTrackBookingActionState,
          addOnsBookingActionState = result.addOnsBookingActionState
        )
      }
    }

  }

  private def buildDuplicateCandidate(
      request: MultiProductSaveStageRequest,
      summary: EbeLiteBookingSummary,
      masterHotelId: Long
  ): DuplicatedBookingCandidate = {
    implicit val salt: String = duplicateConfig.salt
    val requestContext        = request.requestContext
    val whiteLabelId          = requestContext.whiteLabelInfo.whiteLabelId
    val memberId              = request.request.customer.memberId
    val (guestKanaFirstname, guestKanaLastname) =
      mapLocalizedNames(request.request)(requestContext)

    val hashedPii = hashPiiData(
      guestFirstName = summary.guestFirstName,
      guestLastName = summary.guestLastName,
      guestKanaFirstname = guestKanaFirstname,
      guestKanaLastname = guestKanaLastname
    )

    DuplicatedBookingCandidate(
      bookingId = summary.bookingId,
      guestFirstName = summary.guestFirstName,
      guestLastName = summary.guestLastName,
      whiteLabel = whiteLabelId,
      bookingDateUntil = summary.bookingDateUtil,
      bookingDateFrom = summary.checkIn,
      bookingDate = requestContext.requestedDateTime.toString(BookingDateFormat),
      storefrontId = summary.storefrontId,
      languageId = summary.languageId,
      roomTypeId = summary.roomTypeId,
      userId = request.request.userId,
      kanaFirstName = guestKanaFirstname,
      kanaLastName = guestKanaLastname,
      itineraryId = Some(request.saveBookingModel.itineraryModel.itinerary.itineraryId),
      affiliateTagId =
        if (summary.storefrontId == Storefront.XmlPartner) request.request.trackingTag
        else None,
      hashedPii = hashedPii,
      memberId = memberId,
      masterHotelId = masterHotelId
    )
  }
  private def sendBookingActionSizeMessage(savedBookingModel: MultiProductBookingInsertionModel)(implicit
      measurementCxt: MeasurementsContext
  ): Future[Unit] = {
    Future
      .traverse(savedBookingModel.workflowActions) { bookingAction =>
        val numChars = bookingAction.state.size
        hadoopMessaging.sendBookingActionSizeMessage(
          correlationId = measurementCxt.correlationId,
          itineraryId = bookingAction.itineraryId,
          bookingId = bookingAction.bookingId,
          productTypeId = bookingAction.productTypeId,
          bookingTypeId = bookingAction.bookingType,
          numCharacters = numChars,
          sizeInBytes = numChars * 2
        )
      }
      .map(_ => ())
  }

  private[stage] def mapSuccessCreateBookingResponse(
      request: MultiProductSaveStageRequest,
      urlMapping: BookingId => String
  ): Itinerary = {
    val itineraryId   = request.saveBookingModel.itineraryModel.itinerary.itineraryId
    val itineraryDate = request.saveBookingModel.itineraryModel.itinerary.recCreatedWhen

    val propertyResults = request.saveBookingModel.propertyModels.map(result => {
      val stayPackageType = for {
        booking         <- result.reservedIds.product.info.bapiBooking.booking
        bookingItem     <- booking.booking.headOption
        hotel           <- bookingItem.hotel.headOption
        room            <- hotel.room.headOption
        rateCategory    <- room.rateCategory
        stayPackageType <- rateCategory.stayPackageType
      } yield stayPackageType

      val stayType = for {
        booking     <- result.reservedIds.product.info.bapiBooking.booking
        bookingItem <- booking.booking.headOption
        hotel       <- bookingItem.hotel.headOption
        stayType    <- hotel.stayType.map(_.id)
      } yield stayType

      HotelBooking(
        bookingId = result.booking.bookingId.toInt,
        itineraryId = result.itinerary.itineraryId.toInt,
        bookingStatus = CreatedBookingStatus.BookingProcessing,
        selfServiceUrl = urlMapping(result.booking.bookingId.toInt),
        stayPackageType = stayPackageType,
        stayType = stayType
      )
    })
    val flightResults = request.saveBookingModel.flightBookingActionStates.flatMap(_.bookingState).map { result =>
      FlightBooking(result.bookingId.toInt, CreatedBookingStatus.BookingProcessing)
    }

    val carResults = request.saveBookingModel.vehicleBookingActionStates.flatMap(_.vehicleBookingState).map { result =>
      VehicleBooking(result.vehicleBooking.vehicleBookingId.toInt, CreatedBookingStatus.BookingProcessing)
    }

    val protectionResults = request.saveBookingModel.protectionBookingActionStates
      .flatMap(_.tripProtectionBookingState)
      .map(result =>
        ProtectionBooking(
          result.protectionBookingId.toInt,
          result.protectionTypeId,
          CreatedBookingStatus.BookingProcessing
        )
      )

    val activityResults = request.saveBookingModel.activityBookingActionState
      .flatMap(_.itineraryState.product.activities)
      .map(result => ActivityBooking(result.product.booking.bookingId.toInt, CreatedBookingStatus.BookingProcessing))

    val cegFastTrackResults = request.saveBookingModel.cegFastTrackBookingActionState
      .flatMap(_.itineraryState.product.cegFastTracks)
      .map(result =>
        CegFastTrackCreateResult(
          bookingId = result.product.booking.bookingId.toInt,
          bookingStatus = CreatedBookingStatus.BookingProcessing
        )
      )

    val addOnResults = request.saveBookingModel.addOnsBookingActionState
      .flatMap(_.itineraryState.product.addOns)
      .map(result =>
        GenericProductCreateResult(
          bookingId = result.product.booking.bookingId.toInt,
          bookingStatus = CreatedBookingStatus.BookingProcessing,
          productTypeId = result.product.booking.productTypeId
        )
      )

    val bookingDetailToken = itineraryAssociatedBookingsTokenUtils
      .create(itineraryId, flightResults.map(_.bookingId), propertyResults.map(_.bookingId))
      .toOption

    Itinerary(
      itineraryId = itineraryId,
      bookings = propertyResults,
      flights = flightResults,
      cars = carResults,
      protections = protectionResults,
      activities = activityResults,
      cegFastTracks = cegFastTrackResults,
      addOns = addOnResults,
      statusToken = request.statusToken.serialize(log, withMeasure),
      itineraryAssociatedBookingsToken = bookingDetailToken,
      itineraryDate = itineraryDate
    )
  }

  private[stage] def sendFlightHadoopMessage(
      saveBookingModel: MultiProductBookingInsertionModel,
      stopBfdbReplication: Boolean
  )(implicit measurementCxt: MeasurementsContext): Future[Unit] = {
    withMeasureAndLogWithContext(measurementCxt)(creationMeasurementName("SendReplicateMessage")) {
      Future.traverse(saveBookingModel.flightBookingActionStates.flatMap(_.bookingState.toSeq)) { savedFlight =>
        val bookingActionF = saveBookingModel.workflowActions
          .find(_.bookingId.contains(savedFlight.bookingId))
          .map(Future.successful)
          .getOrElse(Future.failed(new BookingActionNotFound(savedFlight.bookingId)))
        val savedFlightModel = composeFlightModelInternalWithItinerary(savedFlight, saveBookingModel.itineraryModel)
        for {
          bookingAction <- bookingActionF
          _ <- if (stopBfdbReplication)
                 Future.unit
               else
                 flightBookingRepository.sendFlightModelForReplication(
                   savedFlightModel,
                   saveBookingModel.multiProductsInfo,
                   saveBookingModel.protectionBookingActionStates.flatMap(_.tripProtectionBookingState),
                   saveBookingModel.multiProductBookingGroups
                 )
          _ <- flightBookingRepository.sendBapiCreateFlightBookingMessage(savedFlightModel, bookingAction)
          _ <- flightBookingRepository.sendBapiCreateFlightBookingExperimentMessage(savedFlightModel)
        } yield {}
      }
    }.map(_ => ())
  }

  private[stage] def sendVehicleHadoopMessage(
      saveBookingModel: MultiProductBookingInsertionModel,
      stopBfdbReplication: Boolean
  )(implicit measurementCxt: MeasurementsContext): Future[Unit] = {
    withMeasureAndLogWithContext(measurementCxt)(creationMeasurementName("SendVehicleReplicateMessage")) {
      Future.traverse(saveBookingModel.vehicleBookingActionStates.flatMap(_.vehicleBookingState.toSeq)) {
        savedVehicleBooking =>
          val bookingActionF = saveBookingModel.workflowActions
            .find(_.bookingId.contains(savedVehicleBooking.vehicleBooking.vehicleBookingId))
            .map(Future.successful)
            .getOrElse(Future.failed(new BookingActionNotFound(savedVehicleBooking.vehicleBooking.vehicleBookingId)))
          val savedVehicleModel =
            composeVehicleBookingStateWithItinerary(savedVehicleBooking, saveBookingModel)
          for {
            bookingAction <- bookingActionF
            _ <- if (stopBfdbReplication) Future.unit
                 else
                   vehicleBookingRepository.sendVehicleModelForReplication(
                     savedVehicleModel,
                     bookingAction,
                     saveBookingModel.multiProductsInfo,
                     saveBookingModel.protectionBookingActionStates.flatMap(_.tripProtectionBookingState)
                   )
            _ <- Future.sequence(
                   savedVehicleModel.vehicleModelsInternal.map(vehicle =>
                     vehicleBookingRepository.sendBapiCreateVehicleBookingMessage(vehicle.vehicleBooking, bookingAction)
                   )
                 )
          } yield {}
      }
    }.map(_ => ())
  }

  private[stage] def sendActivityHadoopMessage(
      saveBookingModel: MultiProductBookingInsertionModel,
      stopBfdbReplication: Boolean
  )(implicit measurementCxt: MeasurementsContext): Future[Unit] = {
    val defaultBookingId = 0
    withMeasureAndLogWithContext(measurementCxt)(creationMeasurementName("SendActivityReplicateMessage")) {
      Future.traverse(saveBookingModel.activityBookingActionState.map(_.itineraryState)) { itineraryState =>
        val bookingIdOpt: Option[Long] = itineraryState.product.activities.headOption.map(_.product.booking.bookingId)
        val bookingActionF = bookingIdOpt
          .flatMap { bookingId =>
            saveBookingModel.workflowActions.find(_.bookingId.contains(bookingId))
          }
          .map(Future.successful)
          .getOrElse(Future.failed(new BookingActionNotFound(bookingIdOpt.getOrElse(defaultBookingId))))

        for {
          bookingAction <- bookingActionF
          activityBookingState = ActivityBookingStateWithItinerary.fromItineraryState(
                                   itineraryState,
                                   saveBookingModel.multiProductBookingGroups
                                 )
          _ <- if (stopBfdbReplication) Future.unit
               else
                 activityBookingRepository.sendModelForReplication(
                   activityBookingState,
                   bookingAction,
                   saveBookingModel.multiProductsInfo
                 )
          _ <- Future.sequence(
                 activityBookingState.activities.map(activity =>
                   activityBookingRepository.sendBapiCreateBookingMessage(activity, bookingAction)
                 )
               )
        } yield {}

      }
    }.map(_ => ())
  }

  private[stage] def sendCegFastTrackHadoopMessage(
      saveBookingModel: MultiProductBookingInsertionModel,
      stopBfdbReplication: Boolean
  )(implicit measurementCxt: MeasurementsContext): Future[Unit] = {
    val defaultBookingId = 0
    withMeasureAndLogWithContext(measurementCxt)(creationMeasurementName("SendCegFastTrackReplicateMessage")) {
      Future.traverse(saveBookingModel.cegFastTrackBookingActionState.map(_.itineraryState)) { itineraryState =>
        val bookingIdOpt: Option[Long] = itineraryState.product.cegFastTracks.headOption
          .map(_.product.booking.bookingId)
        val bookingActionF = bookingIdOpt
          .flatMap { bookingId =>
            saveBookingModel.workflowActions.find(_.bookingId.contains(bookingId))
          }
          .map(Future.successful)
          .getOrElse(Future.failed(new BookingActionNotFound(bookingIdOpt.getOrElse(defaultBookingId))))

        for {
          bookingAction <- bookingActionF
          cegFastTrackBookingState = CegFastTrackBookingStateWithItinerary.fromItineraryState(
                                       itineraryState,
                                       saveBookingModel.multiProductBookingGroups
                                     )
          _ <- if (stopBfdbReplication) Future.unit
               else
                 cegFastTrackRepository.sendModelForReplication(
                   cegFastTrackBookingState,
                   bookingAction,
                   saveBookingModel.multiProductsInfo
                 )
          _ <- Future.sequence(
                 cegFastTrackBookingState.cegFastTracks.map(cegFastTrack =>
                   cegFastTrackRepository.sendBapiCreateBookingMessage(cegFastTrack, bookingAction)
                 )
               )
        } yield {}
      }
    }.void
  }

  @deprecated("Remove when integrate MPBE-4506")
  private[stage] def sendAddOnReplicateMessage(
      saveBookingModel: MultiProductBookingInsertionModel
  )(implicit measurementCxt: MeasurementsContext): Future[Unit] = {
    val defaultBookingId = 0
    withMeasureAndLogWithContext(measurementCxt)(creationMeasurementName("SendAddOnReplicateMessage")) {
      Future.traverse(saveBookingModel.addOnsBookingActionState.map(_.itineraryState)) { itineraryState =>
        val bookingIdOpt: Option[Long] = itineraryState.product.addOns.headOption
          .map(_.product.booking.bookingId)
        val bookingActionF = bookingIdOpt
          .flatMap { bookingId =>
            saveBookingModel.workflowActions.find(_.bookingId.contains(bookingId))
          }
          .map(Future.successful)
          .getOrElse(Future.failed(new BookingActionNotFound(bookingIdOpt.getOrElse(defaultBookingId))))

        for {
          bookingAction <- bookingActionF
          addOnBookingState = AddOnBookingStateWithItinerary.fromItineraryState(
                                itineraryState,
                                saveBookingModel.multiProductBookingGroups
                              )
          _ <- genericAddOnRepository.sendModelForReplication(
                 addOnBookingState,
                 bookingAction,
                 saveBookingModel.multiProductsInfo
               )
        } yield {}
      }
    }.void
  }

  @deprecated("Remove when integrate MPBE-4506")
  private[stage] def sendItineraryReplicateMessage(
      saveBookingModel: MultiProductBookingInsertionModel
  )(implicit measurementCxt: MeasurementsContext): Future[Unit] = {
    val defaultBookingId = 0

    withMeasureAndLogWithContext(measurementCxt)(creationMeasurementName("SendItineraryReplicateMessage")) {
      val itineraryModel           = saveBookingModel.itineraryModel
      val multiProductInfo         = saveBookingModel.multiProductsInfo
      val multiProductBookingGroup = saveBookingModel.multiProductBookingGroups
      val propertyBidOpt           = saveBookingModel.propertyModels.headOption.map(_.booking.bookingId)
      val workflowActionF = propertyBidOpt
        .flatMap { bookingId =>
          saveBookingModel.workflowActions.find(_.bookingId.contains(bookingId))
        }
        .map(Future.successful)
        .getOrElse(Future.failed(new BookingActionNotFound(propertyBidOpt.getOrElse(defaultBookingId))))
      for {
        workflowAction <- workflowActionF
        _ <- itineraryBookingRepository.sendItineraryModelForReplication(
               itineraryModel,
               workflowAction,
               multiProductInfo,
               multiProductBookingGroup
             )
      } yield {}
    }
  }

  private[stage] def composeFlightModelInternalWithItinerary(
      flightModelInternal: FlightModelInternal,
      itineraryDbModel: ItineraryInternalModel
  ): FlightModelInternal =
    flightModelInternal.copy(
      payments = itineraryDbModel.payments,
      bookingPayments = itineraryDbModel.bookingPayments,
      history = itineraryDbModel.history,
      itinerary = itineraryDbModel.itinerary
    )

  private[stage] def composeVehicleBookingStateWithItinerary(
      vehicleModelInternal: VehicleModelInternal,
      saveBookingModel: MultiProductBookingInsertionModel
  ): VehicleBookingState =
    VehicleBookingState(
      vehicleModelsInternal = Seq(vehicleModelInternal),
      payments = saveBookingModel.itineraryModel.payments,
      bookingPayments = saveBookingModel.itineraryModel.bookingPayments,
      itineraryHistories = saveBookingModel.itineraryModel.history,
      itinerary = saveBookingModel.itineraryModel.itinerary,
      multiProductBookingGroups = saveBookingModel.multiProductBookingGroups
    )

  // Insert booking action message topic provisioning result, for tracking instant book provisioning process
  private[stage] def processProvisioningMessage(
      insertionModel: MultiProductBookingInsertionModel,
      whiteLabel: WhiteLabel.WhiteLabel
  )(implicit requestContext: RequestContext): Future[Unit] = {
    val productActions = insertionModel.workflowActions.filterNot(action => action.bookingId.isEmpty)
    val isJtbWl        = WhitelabelUtils.isJtbWl(requestContext.whiteLabelInfo)

    val (topic, replyMessages) = if (isJtbWl) {
      val message = AgentBookingActionMessage(
        jtbPartnerProvisioningResult =
          Some(JtbPartnerProvisioningResult(result = ProvisioningResult.ProvisioningUnconfirmed))
      )
      (BAM_Topic_JTBPartnerProvisioningResult.value, message)
    } else {
      val message = AgentBookingActionMessage(provisioningResult = ProvisioningResult.ProvisioningUnconfirmed)
      (BAM_Topic_ProvisioningResult.value, message)
    }

    Future.traverse(productActions) { action =>
      bookingActionMessageService.replyMessage(
        action.actionId,
        topic,
        JsonFormat.toJsonString(replyMessages)
      )
    } map (_ => ())
  }
  private def isInstantBooking(request: MultiProductSaveStageRequest): Boolean = {
    request.request.instantBook.exists(_.isActive) && request.saveBookingModel.workflowActions.headOption
      .flatMap(_.productTypeId)
      .contains(MultiProductType.SingleProperty.id)
  }

  private def isHotelBookingFlowOnly(savedBookingModel: MultiProductBookingInsertionModel): Boolean = {
    val isEmptyFlightBookings     = savedBookingModel.flightBookingActionStates.isEmpty
    val isEmptyVehicleBookings    = savedBookingModel.vehicleBookingActionStates.isEmpty
    val isEmptyActivityBookings   = savedBookingModel.activityBookingActionState.isEmpty
    val isEmptyProtectionBookings = savedBookingModel.protectionBookingActionStates.isEmpty
    val isEmptyAddOnBookings      = savedBookingModel.addOnsBookingActionState.isEmpty
    val isContainPropertyBookings = savedBookingModel.propertyModels.nonEmpty

    isEmptyFlightBookings && isEmptyVehicleBookings && isEmptyActivityBookings && isEmptyProtectionBookings && isEmptyAddOnBookings && isContainPropertyBookings
  }
}

object MultiProductSaveStage {
  private[stage] val BookingDateFormat = "yyyy-MM-dd' 'HH:mm:00"
}

final class MainBookingActionNotFound() extends Exception(s"Itinerary BookingAction can't found")
final class BookingActionNotFound(bookingId: Long)
    extends Exception(s"BookingAction can't found from bookingId: ${bookingId}")
