package com.agoda.bapi.creation.validation

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.{FlightPax, HotelGuest}
import com.agoda.bapi.creation.model.RequestWithProducts
import com.agoda.bapi.creation.repository.EbeLiteBookingRepository
import com.agoda.bapi.creation.util.ToolSet

import javax.inject.{Inject, Singleton}
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpb.common.errors.ErrorCode.ErrorCode

import scala.concurrent.{ExecutionContext, Future}

trait CountryOfResidenceChecker {
  def validate(
      request: RequestWithProducts
  )(implicit executionContext: ExecutionContext, requestContext: RequestContext): Future[List[ErrorCode]]
}

@Singleton
final case class CountryOfResidenceCheckerImpl @Inject() (ebeLiteBookingRepository: EbeLiteBookingRepository)
    extends CountryOfResidenceChecker
    with ToolSet {
  private val metricName                 = "CountryOfResidenceChecker"
  private val RESTRICTED_COUNTRY_ID: Int = -1

  override def validate(
      request: RequestWithProducts
  )(implicit executionContext: ExecutionContext, requestContext: RequestContext): Future[List[ErrorCode]] = {

    val customerValidation = validateCustomer(request)

    val roomsValidation =
      if (request.rooms.nonEmpty)
        validateRooms(request)
      else
        Future.successful(List.empty)

    val flightsValidation =
      if (request.flights.nonEmpty)
        validateFlights(request)
      else
        Future.successful(List.empty)

    Future.sequence(List(customerValidation, roomsValidation, flightsValidation)).map(_.flatten)
  }

  ////// Customer validation //////
  private[validation] def validateCustomer(
      request: RequestWithProducts
  )(implicit executionContext: ExecutionContext, requestContext: RequestContext): Future[List[ErrorCode]] = {
    val customerCountryOfResidence = request.request.customer.countryId
    val errorCodeF =
      for {
        errorCode <- if (customerCountryOfResidence == 0)
                       Future.successful(Some(ErrorCode.InvalidCustomerCountryID))
                     else
                       ebeLiteBookingRepository
                         .getRestrictedNationality(customerCountryOfResidence)
                         .map[Option[ErrorCode]] {
                           case Some(-2) => Some(ErrorCode.CounryIsRestricted)
                           case _        => None
                         }
      } yield {
        sendMetric(
          methodName = "validateCustomer",
          errorCode = errorCode,
          customerCOR = Some(customerCountryOfResidence)
        )
        errorCode
      }

    errorCodeF.map {
      case Some(errorCode) => List(errorCode)
      case None            => List.empty[ErrorCode]
    }
  }

  ////// Property related validation //////
  private[validation] def validateRooms(
      request: RequestWithProducts
  )(implicit executionContext: ExecutionContext, requestContext: RequestContext): Future[List[ErrorCode]] = {
    val guestList: List[HotelGuest] = request.request.guestList.toList.distinct
    val hotelIds: List[Long]        = request.rooms.map { case (_, value) => value.bapiBooking.propertyId }.toList.distinct

    // Please noted that nationalityId that we see in BE is being mapped by CountryOfResidence in FE
    /* Todo we will have a investigation ticket to refactor BE param field name in
     * https://jira.agodadev.io/browse/WLBE-1016 */
    val guestCountryOfResidenceRestrictionFut: Future[Option[ErrorCode]] =
      Future
        .sequence(
          guestList.map(validateGuestCountryOfResidence(_))
        )
        .map(_.collectFirst { case Some(v) => v })

    val guestCountryOfResidenceRestrictedByHotelFut: Future[Option[ErrorCode]] = {
      val combinations = for {
        g <- guestList
        h <- hotelIds
      } yield (g, h)

      Future
        .sequence(
          combinations.map(pair => validateGuestCountryOfResidenceByHotel(pair._1, pair._2.toInt))
        )
        .map(_.collectFirst { case Some(v) => v })
    }

    val hotelCountryRestrictionFut: Future[Option[ErrorCode]] =
      Future
        .sequence(
          hotelIds.map(id => validateHotelCountry(id.toInt))
        )
        .map(_.collectFirst { case Some(v) => v })

    for {
      r1 <- guestCountryOfResidenceRestrictionFut
      r2 <- guestCountryOfResidenceRestrictedByHotelFut
      r3 <- hotelCountryRestrictionFut
      r   = List(r1, r2, r3).flatten
    } yield r
  }

  private[validation] def validateGuestCountryOfResidence(
      guest: HotelGuest
  )(implicit executionContext: ExecutionContext, requestContext: RequestContext): Future[Option[ErrorCode]] = {
    for {
      errorCode <- // Todo refactor ErrorCode name to be around CountryResidence not Nationality https://jira.agodadev.io/browse/WLBE-1017
        if (guest.nationalityId == 0)
          Future.successful(Some(ErrorCode.InvalidNationalityId))
        else
          ebeLiteBookingRepository.getRestrictedNationality(guest.nationalityId).map[Option[ErrorCode]] {
            case Some(value) =>
              if (value == -2) // Nationality is restricted, 725
                Some(ErrorCode.NationalityIsRestricted)
              else
                None
            case None => // Guest nationality restriction, 447
              Some(ErrorCode.RestrictedGuestNationality)
          }
    } yield {
      sendMetric(
        methodName = "validateGuestCountryOfResidence",
        errorCode = errorCode,
        guestCOR = Some(guest.nationalityId)
      )
      errorCode
    }
  }

  private[validation] def validateGuestCountryOfResidenceByHotel(guest: HotelGuest, hotelId: Int)(implicit
      executionContext: ExecutionContext,
      requestContext: RequestContext
  ): Future[Option[ErrorCode]] = {
    for {
      errorCode <- if (guest.nationalityId == 0)
                     Future.successful(Some(ErrorCode.InvalidNationalityId))
                   else
                     ebeLiteBookingRepository
                       .getHotelRestrictedNationality(hotelId, guest.nationalityId)
                       .map(
                         _.flatMap(result =>
                           if (result)
                             Some(ErrorCode.RestrictedHotelNationality)
                           else None
                         )
                       )
    } yield {
      sendMetric(
        methodName = "validateGuestCountryOfResidenceByHotel",
        errorCode = errorCode,
        hotelId = Some(hotelId),
        guestCOR = Some(guest.nationalityId)
      )
      errorCode
    }

  }

  private[validation] def validateHotelCountry(
      hotelId: Int
  )(implicit executionContext: ExecutionContext, requestContext: RequestContext): Future[Option[ErrorCode]] = {
    // Original validation logic commented out to bypass "Hotel is in restricted country" error
    /* for { errorCode <- ebeLiteBookingRepository .getHotelCountryIdWithRecStatus(hotelId) .map[Option[ErrorCode]] {
     * countryOp => if (countryOp.contains(RESTRICTED_COUNTRY_ID)) { Some(ErrorCode.HotelIsInRestrictedCountry) } else {
     * None } } } yield { sendMetric( methodName = "validateHotelCountry", errorCode = errorCode, hotelId =
     * Some(hotelId) ) errorCode } */
    Future.successful(None)
  }

  ////// Flight related validation //////
  private[validation] def validateFlights(
      request: RequestWithProducts
  )(implicit executionContext: ExecutionContext, requestContext: RequestContext): Future[List[ErrorCode]] = {
    val paxList: List[FlightPax] = request.request.paxList.toList

    val airports: Seq[String] = request.flights.flatMap { tokens =>
      tokens.info
        .map { flight =>
          flight.slices.flatMap { slice =>
            slice.segments.foldLeft(Set[String]()) { (airportSet, segment) =>
              airportSet + segment.origin + segment.destination
            }
          }
        }
        .getOrElse(Set())
    }

    val paxCountryOfResidenceRestrictionFut: Future[Option[ErrorCode]] =
      Future
        .sequence(
          paxList.map(validatePaxCountryOfResidence(_))
        )
        .map(_.collectFirst { case Some(v) => v })

    val paxCountryOfResidenceRestrictedByAirportFut: Future[Option[ErrorCode]] = {
      val combinations = for {
        p <- paxList
        a <- airports
      } yield (p, a)

      Future
        .sequence(
          combinations.map(pair => validatePaxCountryOfResidenceByAirport(pair._1, pair._2))
        )
        .map(_.collectFirst { case Some(v) => v })
    }

    val airportCountryRestrictionFut: Future[Option[ErrorCode]] =
      Future
        .sequence(airports.map(airportCode => validateAirportCountry(airportCode)))
        .map(_.collectFirst { case Some(v) => v })

    for {
      r1 <- paxCountryOfResidenceRestrictionFut
      r2 <- paxCountryOfResidenceRestrictedByAirportFut
      r3 <- airportCountryRestrictionFut
      r   = List(r1, r2, r3).flatten
    } yield r

  }

  private[validation] def validatePaxCountryOfResidence(
      pax: FlightPax
  )(implicit executionContext: ExecutionContext, requestContext: RequestContext): Future[Option[ErrorCode]] = {
    pax.countryOfResidenceId
      .map { paxCountryOfResidence =>
        for {
          errorCode <- if (paxCountryOfResidence == 0)
                         Future.successful(Some(ErrorCode.InvalidNationalityId))
                       else
                         ebeLiteBookingRepository.getRestrictedNationality(paxCountryOfResidence).map {
                           case Some(value) =>
                             if (value == -2)
                               Some(ErrorCode.NationalityIsRestricted)
                             else
                               None
                           case None => // Pax nationality restriction, 474
                             Some(ErrorCode.RestrictedPaxNationality)
                         }
        } yield {
          sendMetric(
            methodName = "validatePaxCountryOfResidence",
            errorCode = errorCode,
            flightPaxCOR = Some(paxCountryOfResidence)
          )
          errorCode
        }
      }
      .getOrElse(Future.successful(None))
  }

  /**
    * Validate pax nationality by origin/destination
    */
  private[validation] def validatePaxCountryOfResidenceByAirport(pax: FlightPax, airportCode: String)(implicit
      executionContext: ExecutionContext,
      requestContext: RequestContext
  ): Future[Option[ErrorCode]] = {
    pax.countryOfResidenceId
      .map { paxCountryOfResidence =>
        for {
          errorCode <- if (paxCountryOfResidence == 0)
                         Future.successful(Some(ErrorCode.InvalidNationalityId))
                       else
                         ebeLiteBookingRepository
                           .getAirportRestrictedNationality(airportCode, paxCountryOfResidence)
                           .map {
                             case Some(restriction) if restriction => Some(ErrorCode.RestrictedAirportNationality)
                             case _                                => None
                           }
        } yield {
          sendMetric(
            methodName = "validatePaxCountryOfResidenceByAirport",
            errorCode = errorCode,
            flightPaxCOR = Some(paxCountryOfResidence),
            flightAirportCode = Some(airportCode)
          )
          errorCode
        }
      }
      .getOrElse(Future.successful(None))
  }

  private[validation] def validateAirportCountry(
      airportCode: String
  )(implicit executionContext: ExecutionContext, requestContext: RequestContext): Future[Option[ErrorCode]] = {
    for {
      countryIdOpt <- ebeLiteBookingRepository.getAirportCountryId(airportCode)
      restrictedStatus <- countryIdOpt
                            .map(ebeLiteBookingRepository.getRestrictedNationality)
                            .getOrElse(Future.successful(None))
    } yield {
      val errorCode = restrictedStatus match {
        case Some(-2) => Some(ErrorCode.AirportIsInRestrictedCountry)
        case _        => None
      }
      sendMetric(methodName = "validateAirportCountry", errorCode = errorCode, flightAirportCountry = countryIdOpt)
      errorCode
    }
  }

  private def sendMetric(
      methodName: String,
      errorCode: Option[ErrorCode],
      customerCOR: Option[Int] = None,
      hotelId: Option[Int] = None,
      guestCOR: Option[Int] = None,
      flightPaxCOR: Option[Int] = None,
      flightAirportCountry: Option[Int] = None,
      flightAirportCode: Option[String] = None
  )(implicit
      requestContext: RequestContext
  ) = {
    val whitelabelId = requestContext.whiteLabelInfo.whiteLabelId.id
    val tags = Map(
      "methodName"           -> methodName,
      "whitelabelId"         -> whitelabelId.toString,
      "errorCode"            -> errorCode.map(_.id.toString).getOrElse("None"),
      "customerCOR"          -> customerCOR.map(_.toString).getOrElse("None"),
      "hotelId"              -> hotelId.map(_.toString).getOrElse("None"),
      "guestCOR"             -> guestCOR.map(_.toString).getOrElse("None"),
      "flightPaxCOR"         -> flightPaxCOR.map(_.toString).getOrElse("None"),
      "flightAirportCountry" -> flightAirportCountry.map(_.toString).getOrElse("None"),
      "flightAirportCode"    -> flightAirportCode.getOrElse("None")
    )
    withMeasure(metricName = metricName, value = 1, tags = tags)
  }
}
