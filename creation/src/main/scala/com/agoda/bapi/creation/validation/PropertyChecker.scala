package com.agoda.bapi.creation.validation

import com.agoda.ancillary.models.request.RequestContext.empty.correlationId
import com.agoda.bapi.common.ToolSet
import com.agoda.bapi.common.handler.MeasurementsContext
import com.agoda.bapi.common.logging.ArrivalTimeValidationLogMessage
import com.agoda.bapi.common.model.creation.{BAPIBooking, BookingRoom, ChildRoomCheckTime}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.model.RequestWithProducts
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.bapi.creation.repository.EbeLiteBookingRepository
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpb.common.errors.ErrorCode.ErrorCode
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat

import javax.inject.{Inject, Singleton}
import scala.concurrent.ExecutionContext
import scala.util.Try

trait PropertyChecker {
  def validate(request: RequestWithProducts)(implicit executionContext: ExecutionContext): List[ErrorCode]

  def validateCheckInDate(bapiBooking: BAPIBooking): Option[ErrorCode]

  def validateCheckOutDate(bapiBooking: BAPIBooking): Option[ErrorCode]

  def validateBookingPeriod(bapiBooking: BAPIBooking): Option[ErrorCode]

  def validateRoomInfo(bapiBooking: BAPIBooking): Option[ErrorCode]

  def validateNumberOfAdults(bapiBooking: BAPIBooking): Option[ErrorCode]

  def validateNumberOfChildren(bapiBooking: BAPIBooking): Option[ErrorCode]

  def validateHotelId(bapiBooking: BAPIBooking): Option[ErrorCode]

  def validateDmcId(bapiBooking: BAPIBooking): Option[ErrorCode]

  def validateRoomTypeName(bapiBooking: BAPIBooking): Option[ErrorCode]

  def validateChargeBreakDown(bapiBooking: BAPIBooking): Option[ErrorCode]

  def validateArrivalTime(
      requestProperties: Seq[RoomInfo],
      featureAware: Option[FeatureAware]
  )(implicit measurementsContext: MeasurementsContext): Option[ErrorCode]
}

@Singleton
case class PropertyCheckerImpl @Inject() (ebeLiteBookingRepository: EbeLiteBookingRepository)
    extends PropertyChecker
    with ToolSet {
  private val formatMeridiem           = DateTimeFormat.forPattern("hh:mm a")
  private val format24                 = DateTimeFormat.forPattern("HH:mm")
  private val arrivalTimeRegex         = """(\d{2}):(\d{2})""".r
  private val arrivalTimeMeridiemRegex = """(\d{1,2}):(\d{2})\s*(am|pm)""".r
  private val arrivalTimePattern       = """^(.*?)\s*-\s*(.*?)\s*(\(.*\))?$""".r

  override def validate(
      request: RequestWithProducts
  )(implicit executionContext: ExecutionContext): List[ErrorCode] = {
    implicit val measurementsContext: MeasurementsContext = request.measurementsContext;
    def validateAll(bapiBooking: BAPIBooking): List[ErrorCode] = {
      List(
        validateCheckInDate(bapiBooking),
        validateCheckOutDate(bapiBooking),
        validateBookingPeriod(bapiBooking),
        validateRoomInfo(bapiBooking),
        validateNumberOfAdults(bapiBooking),
        validateNumberOfChildren(bapiBooking),
        validateHotelId(bapiBooking),
        validateDmcId(bapiBooking),
        validateRoomTypeName(bapiBooking),
        validateChargeBreakDown(bapiBooking),
        validateArrivalTime(
          request.products.properties,
          request.requestContext.featureAware
        )
      ).collect { case Some(v) => v }
    }

    val bookings = request.rooms.values.map(_.bapiBooking).toList

    bookings.flatMap(validateAll)
  }

  override def validateCheckInDate(bapiBooking: BAPIBooking): Option[ErrorCode] = { // Return 452
    // TODO: Temporarily disabled check-in date validation to allow past dates
    // Original validation logic commented out to bypass "Invalid check in information" error
    /*
    bapiBooking.booking
      .map(_.booking.flatMap(_.hotel.map(_.checkIn)))
      .getOrElse(Nil)
      .find(isInvalidateDate)
      .map(_ => ErrorCode.InvalidCheckInInfo)
    */

    // Return None to indicate no validation error (bypass the date restriction)
    None
  }

  override def validateCheckOutDate(bapiBooking: BAPIBooking): Option[ErrorCode] = { // Return 453
    bapiBooking.booking
      .map(_.booking.flatMap(_.hotel.map(_.checkOut)))
      .getOrElse(Nil)
      .find(isInvalidateDate)
      .map(_ => ErrorCode.InvalidCheckOutInfo)
  }

  override def validateBookingPeriod(bapiBooking: BAPIBooking): Option[ErrorCode] = { // Return 456
    bapiBooking.booking
      .map(_.booking.flatMap(_.hotel.map(hotel => (hotel.checkIn, hotel.checkOut))))
      .getOrElse(Nil)
      .find(pair => pair._1.getMillis >= pair._2.getMillis)
      .map(_ => ErrorCode.InvalidBookingPeriod)
  }

  override def validateRoomInfo(bapiBooking: BAPIBooking): Option[ErrorCode] = { // Return 461
    val hotels = bapiBooking.booking.map(_.booking.flatMap(_.hotel)).getOrElse(Nil)

    hotels.find(_.room.isEmpty).map(_ => ErrorCode.InvalidRoomInfo)
  }

  override def validateNumberOfAdults(bapiBooking: BAPIBooking): Option[ErrorCode] = { // Return 718
    val hotels = bapiBooking.booking.map(_.booking.flatMap(_.hotel)).getOrElse(Nil)

    hotels
      .find(hotel => hotel.numberOfAdults != hotel.room.foldLeft(0)((a, b) => a + b.noOfAdults))
      .map(_ => ErrorCode.InvalidNumberOfAdults)
  }

  def validateNumberOfChildren(bapiBooking: BAPIBooking): Option[ErrorCode] = { // Return 719
    val hotels = bapiBooking.booking.map(_.booking.flatMap(_.hotel)).getOrElse(Nil)

    hotels
      .find(hotel => hotel.numberOfChildren != hotel.room.foldLeft(0)((a, b) => a + b.noOfChildren))
      .map(_ => ErrorCode.InvalidNumberOfChildren)
  }

  override def validateHotelId(bapiBooking: BAPIBooking): Option[ErrorCode] = { // return 454
    if (bapiBooking.propertyId == 0)
      Some(ErrorCode.InvalidPropertyId)
    else None
  }

  override def validateDmcId(bapiBooking: BAPIBooking): Option[ErrorCode] = { // return 455
    val rooms     = bapiBooking.booking.map(_.booking.flatMap(_.hotel.flatMap(_.room))).getOrElse(Nil)
    val dmcIdList = rooms.map(_.dmcId).collect { case Some(v) => v }

    if (dmcIdList.contains(0))
      Some(ErrorCode.InvalidDmcId)
    else None
  }

  override def validateRoomTypeName(bapiBooking: BAPIBooking): Option[ErrorCode] = { // return 463
    val rooms = bapiBooking.booking.map(_.booking.flatMap(_.hotel.flatMap(_.room))).getOrElse(Nil)
    val g     = rooms.map(room => Option(room.roomTypeName).getOrElse("")).find(_.isEmpty)

    g.map(_ => ErrorCode.InvalidRoomTypeName)
  }

  override def validateChargeBreakDown(bapiBooking: BAPIBooking): Option[ErrorCode] = { // Return 471
    if (getBookingRooms(bapiBooking).isEmpty)
      Some(ErrorCode.InvalidChargeBreakDownInfo)
    else None
  }

  def isInvalidateDate(date: DateTime): Boolean = {
    isInvalidateDate(date, DateTime.now)
  }

  def isInvalidateDate(date: DateTime, serverDate: DateTime): Boolean = {
    val serverDateWithDefaultTime = serverDate.withTimeAtStartOfDay
    date.getMillis < serverDateWithDefaultTime.minusDays(1).getMillis
  }

  private def getBookingRooms(property: BAPIBooking): List[BookingRoom] =
    property.booking.map(_.booking.flatMap(_.hotel.flatMap(_.room))).getOrElse(Nil)

  override def validateArrivalTime(
      requestProperties: Seq[RoomInfo],
      featureAware: Option[FeatureAware]
  )(implicit measurementsContext: MeasurementsContext): Option[ErrorCode] = {
    // Will be used for logging only. Will add error after we catch all the edge cases
    if (featureAware.exists(_.isArrivalTimeValidationEnabled)) {
      requestProperties.exists(property => isInvalidPropertyArrivalTime(property))
    }
    None
  }

  private[validation] def isInvalidPropertyArrivalTime(
      property: RoomInfo
  )(implicit measurementsContext: MeasurementsContext): Boolean = {
    def sendArrivalValidationErrorLog(
        message: String,
        cause: Option[Throwable],
        roomCheckTimes: Seq[ChildRoomCheckTime],
        arrivalTime: Option[String]
    ): Unit = {
      logger.log(
        ArrivalTimeValidationLogMessage(
          message = message,
          clientId = measurementsContext.clientId,
          correlationId = measurementsContext.correlationId,
          propertyId = property.bapiBooking.propertyId,
          roomUid = property.roomUid,
          roomCheckTimes = roomCheckTimes.flatMap(roomCheckTime =>
            roomCheckTime.checkIn.map(
              _.startTime.getOrElse("") + "-" + roomCheckTime.checkIn.flatMap(_.endTime).getOrElse("")
            )
          ),
          arrivalTime = arrivalTime.getOrElse(""),
          cause = cause
        )
      )
    }

    try {
      val roomCheckTimes = property.bapiBooking.masterRooms.flatMap(_.childrenRooms).flatMap(_.roomCheckTime)
      val arrivalTime    = property.unmodifiedSpecialRequest.flatMap(_.arrivalTime)

      val isInvalid = roomCheckTimes.exists { roomCheckTime =>
        val checkInStartTime = roomCheckTime.checkIn.flatMap(_.startTime)
        val checkInEndTime   = roomCheckTime.checkIn.flatMap(_.endTime)
        val arrivalTimeRange = parseArrivalTimeRange(arrivalTime)

        checkInValidArrivalTime(checkInStartTime, checkInEndTime, arrivalTimeRange)
      }

      if (isInvalid) {
        sendArrivalValidationErrorLog(
          "Failed at validation process",
          None,
          roomCheckTimes,
          arrivalTime
        )
      }

      isInvalid
    } catch {
      case e: Exception =>
        sendArrivalValidationErrorLog(
          e.getMessage,
          Some(e),
          Seq.empty,
          Some("")
        )
        true
    }
  }

  private[validation] def checkInValidArrivalTime(
      checkInStartTime: Option[String],
      checkInEndTime: Option[String],
      arrivalTimeRange: Option[(String, String, String)]
  ): Boolean = {
    (checkInStartTime, checkInEndTime, arrivalTimeRange) match {
      case (Some(checkInStart), Some(checkInEnd), Some((arrivalStart, arrivalEnd, nextDay))) =>
        val parsedCheckInStartTime = parseStringToDateTime(checkInStart)
        val parsedCheckInEndTime = parseStringToDateTime(checkInEnd, isEndTime = true).map(checkInEndTime =>
          parsedCheckInStartTime match {
            case Some(parsedCheckInStartTime) if checkInEndTime.isBefore(parsedCheckInStartTime) =>
              checkInEndTime.plusDays(1)
            case _ => checkInEndTime
          }
        )
        val parsedArrivalStartTime = parseStringToDateTime(arrivalStart, isNextDay = nextDay.nonEmpty)
        val parsedArrivalEndTime   = parseStringToDateTime(arrivalEnd, isEndTime = true, isNextDay = nextDay.nonEmpty)

        val isArrivalTimeValid = compareArrivalTimeWithCheckInTime(
          parsedCheckInStartTime,
          parsedCheckInEndTime,
          parsedArrivalStartTime,
          parsedArrivalEndTime
        )
        !isArrivalTimeValid
      case _ => false
    }
  }

  private[validation] def compareArrivalTimeWithCheckInTime(
      parsedCheckInStartTime: Option[DateTime],
      parsedCheckInEndTime: Option[DateTime],
      parsedArrivalStartTime: Option[DateTime],
      parsedArrivalEndTime: Option[DateTime]
  ): Boolean = {
    (parsedCheckInStartTime, parsedCheckInEndTime, parsedArrivalStartTime, parsedArrivalEndTime) match {
      case (
            Some(parsedCheckInStartTime),
            Some(parsedCheckInEndTime),
            Some(parsedArrivalStartTime),
            Some(parsedArrivalEndTime)
          ) =>
        val isArrivalStartTimeValid =
          parsedArrivalStartTime.isEqual(parsedCheckInStartTime) || parsedArrivalStartTime.isAfter(
            parsedCheckInStartTime
          )
        val isArrivalEndTimeValid =
          parsedArrivalEndTime.isEqual(parsedCheckInEndTime) || parsedArrivalEndTime.isBefore(
            parsedCheckInEndTime
          )

        isArrivalStartTimeValid && isArrivalEndTimeValid
      case _ => false
    }
  }

  private[validation] def parseStringToDateTime(
      arrivalTime: String,
      isEndTime: Boolean = false,
      isNextDay: Boolean = false
  ): Option[DateTime] = {
    Try {
      val addedDayAfterMidnight = if (isEndTime) 1 else 0
      val addedDayIfNextDay     = if (isNextDay) 1 else 0
      arrivalTime.toLowerCase() match {
        case "24:00" => Some(format24.parseDateTime("00:00").plusDays(1 + addedDayIfNextDay))
        case "00:00 am" =>
          Some(format24.parseDateTime("00:00").plusDays(addedDayAfterMidnight + addedDayIfNextDay))
        case arrivalTimeRegex(_, _) =>
          Some(format24.parseDateTime(arrivalTime).plusDays(addedDayIfNextDay))
        case arrivalTimeMeridiemRegex(_, _, _) =>
          Some(formatMeridiem.parseDateTime(arrivalTime).plusDays(addedDayIfNextDay))
        case _ => None
      }
    }.toOption.flatten
  }

  private[validation] def parseArrivalTimeRange(arrivalTime: Option[String]): Option[(String, String, String)] = {
    arrivalTime.flatMap {
      case arrivalTimePattern(start, end, nextDay) => Some((start.trim, end.trim, Option(nextDay).getOrElse("").trim))
      case _                                       => None
    }
  }
}
