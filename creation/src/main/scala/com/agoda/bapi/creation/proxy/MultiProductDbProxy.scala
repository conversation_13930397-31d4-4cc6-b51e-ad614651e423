package com.agoda.bapi.creation.proxy

import com.agoda.bapi.common.config.{AgodaConfig, KillSwitches}
import com.agoda.bapi.common.database.{AGDB, SqlSupport}
import com.agoda.bapi.common.exception.{BookingActionStateLimitException, DbException}
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.booking.local.{BookingWorkflowAction, WorkflowItineraryOperation}
import com.agoda.bapi.common.model.multiproduct.{MultiProductBookingGroupDBModel, MultiProductInfoDBModel}
import com.agoda.bapi.common.proxy.DependencyNames.Dependency
import com.agoda.bapi.common.proxy.{DBConnectionGroup, DbProxy, DependencyNames}
import com.agoda.bapi.common.util.converters.{ItineraryConverters, ItineraryStateConverters}
import com.agoda.bapi.creation.model.db.{BookingActionState, MultiProductBookingInsertionModel}
import com.agoda.bapi.creation.util.{BookingActionStateHelper, ToolSet}
import com.agoda.mpb.common.WorkflowId
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpb.common.models.state.ProductType
import com.agoda.sql.ResultSetHelper

import java.sql.SQLException
import javax.inject.Inject
import scala.concurrent.Future
import scala.util.control.NonFatal

trait MultiProductDbProxy extends DbProxy {

  def insertMultiProductInfo(multiProductInfo: MultiProductInfoDBModel): Future[MultiProductInfoDBModel]

  def insertMultiBooking(
      transactionModel: MultiProductBookingInsertionModel
  )(implicit requestRequest: RequestContext): Future[MultiProductBookingInsertionModel]

  def getNextMultiProductSequenceNumber: Future[MultiProductId]

  def getMultiProductInfo(multiProductId: Long): Future[Option[MultiProductInfoDBModel]]

  def getNextPackageSequenceNumber(): Future[PackageId]

  def getNextCartSequenceNumber(): Future[CartId]

  def getNextOperationIdSequenceNumber(): Future[OperationId]

  def insertMultiProductBookingGroup(
      multiProductBookingGroup: MultiProductBookingGroupDBModel
  ): Future[MultiProductBookingGroupDBModel]

  def getMultiProductBookingGroup(bookingId: Long): Future[Option[MultiProductBookingGroupDBModel]]

  def getMultiProductBookingGroupByItineraryId(itineraryId: Long): Future[Seq[MultiProductBookingGroupDBModel]]

  def getNextBaseBookingRelationshipId(): Future[BookingRelationshipId]

  override protected def dependency: Dependency = DependencyNames.BfdbBcreMetric

}

class MultiProductDbProxyImpl @Inject() (
    val db: AGDB,
    dbExecutionContext: BFDBExecutionContext,
    val baseBookingDBProxy: BaseBookingDBProxy,
    genericProductActionDbProxy: GenericProductActionDbProxy,
    agodaConfig: AgodaConfig,
    val killSwitches: KillSwitches
) extends MultiProductDbProxy
    with SqlSupport
    with ToolSet
    with ResultSetHelper
    with FlightsDbProxyAction
    with VehicleDbProxyAction
    with PropertyDbProxyAction
    with ProtectionDbProxyAction
    with WorkflowDbProxyAction
    with MultiProductDbProxyAction
    with ItineraryDbProxyAction {

  import MultiProductQueriedDataMapper._

  implicit override val dbDispatcher = dbExecutionContext.executionContext
  private val bcreDbConnGroup        = DBConnectionGroup.BFDB_BCRE

  override def getNextMultiProductSequenceNumber: Future[MultiProductId] = {
    val queryName      = "bcre_select_next_multi_product_id_v1"
    val queryToExecute = query(s"EXEC dbo.$queryName")
    val errMsg         = "Unable to get next multi booking id sequence number"
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapNextSequenceNumberQueryResult(errMsg)
    )
  }

  override def insertMultiProductInfo(multiProductInfo: MultiProductInfoDBModel): Future[MultiProductInfoDBModel] = {
    val spName         = "multi_product_info_insert_v1"
    val queryToExecute = s"""{ call dbo.$spName (?,?) }"""
    withExecutionTimeMeasure(metricName(), getMethodTag(spName)) {
      Future {
        db.withConnectionGroup(bcreDbConnGroup.value) { implicit conn =>
          insertMultiProductInfoAction(multiProductInfo)
        }
      } andThen {
        handleFailure(spName, query(queryToExecute))
      } recoverWith {
        case NonFatal(e) => Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
      }
    }
  }

  override def getMultiProductInfo(multiProductId: Long): Future[Option[MultiProductInfoDBModel]] = {
    val queryName      = "bcre_get_multi_product_info_by_multi_product_id_v1"
    val queryToExecute = query(s"EXEC dbo.$queryName @multiProductId = ?", multiProductId)
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      toMultiProductInfo
    )
  }

  override def insertMultiBooking(
      transactionModel: MultiProductBookingInsertionModel
  )(implicit requestContext: RequestContext): Future[MultiProductBookingInsertionModel] = {
    val spName = "insertMultiProduct"
    withExecutionTimeMeasure(metricName(), getMethodTag(spName)) {
      Future {
        db.withConnectionGroup(bcreDbConnGroup.value) { implicit conn =>
          val autoCommitInitialState = conn.getAutoCommit
          conn.setAutoCommit(false) // open transaction

          import ItineraryStateConverters._

          val stopBfdbReplication = requestContext.featureAware.exists(_.stopBfdbReplication)

          /* Workaround - we still need to insert multi_product_info to BFDB because transfer agent reads from it.
           * Ticket to fix: MPBE-6030 */
          val multiProductsInfo = transactionModel.multiProductsInfo.map(insertMultiProductInfoAction)

          val itineraryModel =
            if (stopBfdbReplication) transactionModel.itineraryModel
            else insertItinerary(transactionModel.itineraryModel)(conn, killSwitches)

          val operationId = insertItineraryOperation(
            WorkflowItineraryOperation(
              operationId = transactionModel.operationId.getOrElse(0),
              itineraryId = transactionModel.itineraryModel.itinerary.itineraryId,
              actionTypeId = OperationActionTypes.CreateBooking.id
            )
          ).operationId

          val multiProductBookingGroups =
            if (stopBfdbReplication) transactionModel.multiProductBookingGroups
            else
              transactionModel.multiProductBookingGroups.map(
                insertMultiProductBookingGroupAction
              )

          val updatedFlightBookingStates =
            if (stopBfdbReplication) transactionModel.flightBookingActionStates
            else
              transactionModel.flightBookingActionStates.map { state =>
                val updateState = state.bookingState.map(state => insertFlightsWithoutItinerary(state, agodaConfig))
                state.copy(
                  bookingState = updateState,
                  itineraryProtoState = updateState.map(ItineraryStateConverters.toItineraryState(_).toBase64String)
                )
              }

          val updatedVehicleBookingStates =
            if (stopBfdbReplication) transactionModel.vehicleBookingActionStates
            else
              transactionModel.vehicleBookingActionStates.map { state =>
                val updateState = state.vehicleBookingState.map(insertVehicleWithoutItinerary)
                state.copy(
                  vehicleBookingState = updateState,
                  itineraryProtoState = updateState.map(ItineraryStateConverters.toItineraryState(_).toBase64String)
                )
              }

          val propertyBookingList =
            transactionModel.propertyModels.map(t => insertPropertyCreationBookingTable(t)(conn, killSwitches))

          val updatedProtectionBookingState =
            if (stopBfdbReplication) transactionModel.protectionBookingActionStates
            else
              transactionModel.protectionBookingActionStates.map { state =>
                val updateState = state.tripProtectionBookingState.map(upsertProtection)
                state.copy(
                  tripProtectionBookingState = updateState,
                  itineraryProtoState = updateState.map(ItineraryStateConverters.toItineraryState(_).toBase64String)
                )
              }

          val updatedActivityBookingActionState =
            if (stopBfdbReplication) transactionModel.activityBookingActionState
            else
              transactionModel.activityBookingActionState.map { state =>
                val updateActivityState = state.itineraryState.product.activities
                  .map(activity =>
                    activity.copy(
                      product = genericProductActionDbProxy.insertProductModelWithoutItinerary(activity.product)
                    )
                  )

                val updateState = state.itineraryState.copy(
                  itinerary = ItineraryConverters.toItineraryModel(itineraryModel),
                  product = state.itineraryState.product.copy(
                    activities = updateActivityState
                  )
                )
                state.copy(
                  itineraryProtoState = Some(updateState.toBase64String)
                )
              }

          val updatedCegFastTrackBookingActionState =
            if (stopBfdbReplication) transactionModel.cegFastTrackBookingActionState
            else
              transactionModel.cegFastTrackBookingActionState.map { state =>
                val updatedCegFastTrackState = state.itineraryState.product.cegFastTracks
                  .map(cegFastTrack =>
                    cegFastTrack.copy(
                      product = genericProductActionDbProxy.insertProductModelWithoutItinerary(cegFastTrack.product)
                    )
                  )

                val updateState = state.itineraryState.copy(
                  itinerary = ItineraryConverters.toItineraryModel(itineraryModel),
                  product = state.itineraryState.product.copy(
                    cegFastTracks = updatedCegFastTrackState
                  )
                )
                state.copy(itineraryProtoState = Some(updateState.toBase64String))
              }

          val updatedAddOnBookingActionState =
            if (stopBfdbReplication) transactionModel.addOnsBookingActionState
            else
              transactionModel.addOnsBookingActionState.map { state =>
                val updatedAddOnState = state.itineraryState.product.addOns
                  .map(addOn =>
                    addOn.copy(
                      product = genericProductActionDbProxy.insertProductModelWithoutItinerary(addOn.product)
                    )
                  )

                val updateState = state.itineraryState.copy(
                  itinerary = ItineraryConverters.toItineraryModel(itineraryModel),
                  product = state.itineraryState.product.copy(
                    addOns = updatedAddOnState
                  )
                )
                state.copy(itineraryProtoState = Some(updateState.toBase64String))
              }

          // operationId copy was done at MultiProductPreSaveStage already
          val workflowActionsWithOperationId =
            if (stopBfdbReplication) transactionModel.workflowActions
            else transactionModel.workflowActions.map(_.copy(operationId = Some(operationId)))

          val workflowActionsWithLatestState =
            if (stopBfdbReplication) workflowActionsWithOperationId
            else
              workflowActionsWithOperationId
                .map(
                  updateWorkflowAction(
                    _,
                    updatedFlightBookingStates = updatedFlightBookingStates,
                    updatedVehicleBookingStates = updatedVehicleBookingStates,
                    updatedProtectionBookingStates = updatedProtectionBookingState,
                    updatedActivityBookingStates = updatedActivityBookingActionState,
                    updatedCegFastTrackBookingStates = updatedCegFastTrackBookingActionState,
                    updatedAddOnsBookingStates = updatedAddOnBookingActionState
                  )
                )

          val workflowActionsAfterInsertF = Future.sequence(workflowActionsWithLatestState.map { action =>
            val (sizeBucket, sizeBucketStr) = BookingActionStateHelper.calculateSizeBucket(action.state)
            val tags = getMethodTag(spName) ++ Map(
              "producttype"     -> action.productTypeId.toString,
              "workflowstateid" -> action.workflowStateId.toString,
              "actiontypeid"    -> action.actionTypeId.toString,
              "size"            -> sizeBucketStr
            )
            withExecutionTimeMeasure(metricName(Some("action")), tags) {
              if (BookingActionStateHelper.isPersistAllowed(action.state)) {
                Future.successful(insertBookingAction(action))
              } else {
                Future.failed(new BookingActionStateLimitException(sizeBucket))
              }
            }
          })

          conn.commit()
          conn.setAutoCommit(autoCommitInitialState)
          for {
            workflowActions <- workflowActionsAfterInsertF
          } yield MultiProductBookingInsertionModel(
            multiProductsInfo = multiProductsInfo,
            workflowActions = workflowActions,
            itineraryModel = itineraryModel,
            flightBookingActionStates = updatedFlightBookingStates,
            propertyModels = propertyBookingList, // Note - this object is the same as input
            vehicleBookingActionStates = updatedVehicleBookingStates,
            protectionBookingActionStates = updatedProtectionBookingState,
            activityBookingActionState = updatedActivityBookingActionState,
            cegFastTrackBookingActionState = updatedCegFastTrackBookingActionState,
            addOnsBookingActionState = updatedAddOnBookingActionState,
            multiProductBookingGroups = multiProductBookingGroups,
            operationId = Some(operationId) // Note - operationId has been populated since MultiProductPreSaveStage
          )
        }
      }.flatten
    }.andThen {
      handleFailure(spName, query(spName))
    }.recoverWith {
      recoverDbExecution
    }
  }

  override def getNextPackageSequenceNumber(): Future[PackageId] = {
    val queryName      = "bcre_sequence_next_package_id_v1"
    val queryToExecute = s"EXEC dbo.$queryName"
    executeQuery(
      bcreDbConnGroup,
      queryName,
      query(queryToExecute),
      result =>
        if (result.next())
          result.getLong(1)
        else
          throw new SQLException(s"Unable to get next package id sequence number")
    )
  }

  override def getNextCartSequenceNumber(): Future[CartId] = {
    val queryName      = "bcre_sequence_next_cart_id_v1"
    val queryToExecute = s"EXEC dbo.$queryName"
    executeQuery(
      bcreDbConnGroup,
      queryName,
      query(queryToExecute),
      result =>
        if (result.next())
          result.getLong(1)
        else
          throw new SQLException(s"Unable to get next cart id sequence number")
    )
  }

  override def getNextOperationIdSequenceNumber(): Future[OperationId] = {
    val queryName      = "bcre_sequence_next_operation_id_v2"
    val queryToExecute = s"EXEC dbo.$queryName"
    executeQuery(
      bcreDbConnGroup,
      queryName,
      query(queryToExecute),
      result =>
        if (result.next())
          result.getLong(1)
        else
          throw new SQLException(s"Unable to get next operation id sequence number")
    )
  }

  @deprecated("Remove when integrate MPBE-4506")
  private[proxy] def updateWorkflowAction(
      workflowAction: BookingWorkflowAction,
      updatedFlightBookingStates: Seq[BookingActionState],
      updatedVehicleBookingStates: Seq[BookingActionState],
      updatedProtectionBookingStates: Seq[BookingActionState],
      updatedActivityBookingStates: Seq[BookingActionState],
      updatedCegFastTrackBookingStates: Seq[BookingActionState],
      updatedAddOnsBookingStates: Seq[BookingActionState]
  )(implicit requestContext: RequestContext): BookingWorkflowAction = {
    val bookingWorkflowActionOpt =
      if (requestContext.featureAware.exists(_.enableDeprecateWorkflowId))
        updateWorkflowActionByProductTypeId(
          workflowAction,
          updatedFlightBookingStates,
          updatedVehicleBookingStates,
          updatedProtectionBookingStates,
          updatedActivityBookingStates,
          updatedCegFastTrackBookingStates,
          updatedAddOnsBookingStates
        )
      else
        updateWorkflowActionByWorkflowId(
          workflowAction,
          updatedFlightBookingStates,
          updatedVehicleBookingStates,
          updatedProtectionBookingStates,
          updatedActivityBookingStates,
          updatedCegFastTrackBookingStates,
          updatedAddOnsBookingStates
        )

    bookingWorkflowActionOpt
      .getOrElse(throw new WorkflowActionNotFound(workflowAction.bookingId))
  }

  private[proxy] def updateWorkflowActionByWorkflowId(
      workflowAction: BookingWorkflowAction,
      updatedFlightBookingStates: Seq[BookingActionState],
      updatedVehicleBookingStates: Seq[BookingActionState],
      updatedProtectionBookingStates: Seq[BookingActionState],
      updatedActivityBookingStates: Seq[BookingActionState],
      updatedCegFastTrackBookingStates: Seq[BookingActionState],
      updatedAddOnsBookingStates: Seq[BookingActionState]
  ): Option[BookingWorkflowAction] = {
    val workflowId = WorkflowId.getWithDefault(workflowAction.workflowId, WorkflowId.Unknown)

    workflowId match {
      case _
          if updatedAddOnsBookingStates.exists(
            _.itineraryState.product.addOns
              .exists(_.product.booking.bookingId == workflowAction.bookingId.getOrElse(-1L))
          ) => // generic check where we don't check workflow id
        for {
          bookingId <- workflowAction.bookingId
          stateObj <-
            updatedAddOnsBookingStates.find(
              _.itineraryState.product.addOns.exists(_.product.booking.bookingId == bookingId)
            )
        } yield workflowAction.copy(state = stateObj.toJson)
      case WorkflowId.Flight =>
        for {
          bookingId <- workflowAction.bookingId
          stateObj  <- updatedFlightBookingStates.find(_.bookingState.exists(_.bookingId == bookingId))
        } yield workflowAction.copy(state = stateObj.toJson)
      case WorkflowId.Vehicle =>
        for {
          bookingId <- workflowAction.bookingId
          stateObj <- updatedVehicleBookingStates.find(
                        _.vehicleBookingState.exists(_.vehicleBooking.vehicleBookingId == bookingId)
                      )
        } yield workflowAction.copy(state = stateObj.toJson)
      case WorkflowId.Protection =>
        for {
          bookingId <- workflowAction.bookingId
          stateObj <-
            updatedProtectionBookingStates.find(_.tripProtectionBookingState.exists(_.protectionBookingId == bookingId))
        } yield workflowAction.copy(state = stateObj.toJson)
      case WorkflowId.Activity =>
        for {
          bookingId <- workflowAction.bookingId
          stateObj <-
            updatedActivityBookingStates.find(
              _.itineraryState.product.activities.exists(_.product.booking.bookingId == bookingId)
            )
        } yield workflowAction.copy(state = stateObj.toJson)
      case WorkflowId.CegFastTrack =>
        for {
          bookingId <- workflowAction.bookingId
          stateObj <-
            updatedCegFastTrackBookingStates.find(
              _.itineraryState.product.cegFastTracks.exists(_.product.booking.bookingId == bookingId)
            )
        } yield workflowAction.copy(state = stateObj.toJson)
      case _ =>
        Some(workflowAction)
    }
  }

  private[proxy] def updateWorkflowActionByProductTypeId(
      workflowAction: BookingWorkflowAction,
      updatedFlightBookingStates: Seq[BookingActionState],
      updatedVehicleBookingStates: Seq[BookingActionState],
      updatedProtectionBookingStates: Seq[BookingActionState],
      updatedActivityBookingStates: Seq[BookingActionState],
      updatedCegFastTrackBookingStates: Seq[BookingActionState],
      updatedAddOnsBookingStates: Seq[BookingActionState]
  ) = {
    val productType = workflowAction.productTypeId
      .map(ProductType.getWithDefault(_, ProductType.Unknown))
      .getOrElse(ProductType.Unknown)

    productType match {
      case _
          if updatedAddOnsBookingStates.exists(
            _.itineraryState.product.addOns
              .exists(_.product.booking.bookingId == workflowAction.bookingId.getOrElse(-1L))
          ) =>
        for {
          bookingId <- workflowAction.bookingId
          stateObj <-
            updatedAddOnsBookingStates.find(
              _.itineraryState.product.addOns.exists(_.product.booking.bookingId == bookingId)
            )
        } yield workflowAction.copy(state = stateObj.toJson)
      case ProductType.Flights =>
        for {
          bookingId <- workflowAction.bookingId
          stateObj  <- updatedFlightBookingStates.find(_.bookingState.exists(_.bookingId == bookingId))
        } yield workflowAction.copy(state = stateObj.toJson)
      case ProductType.Cars =>
        for {
          bookingId <- workflowAction.bookingId
          stateObj <- updatedVehicleBookingStates.find(
                        _.vehicleBookingState.exists(_.vehicleBooking.vehicleBookingId == bookingId)
                      )
        } yield workflowAction.copy(state = stateObj.toJson)
      case ProductType.TripProtection =>
        for {
          bookingId <- workflowAction.bookingId
          stateObj <-
            updatedProtectionBookingStates.find(_.tripProtectionBookingState.exists(_.protectionBookingId == bookingId))
        } yield workflowAction.copy(state = stateObj.toJson)
      case ProductType.Activity =>
        for {
          bookingId <- workflowAction.bookingId
          stateObj <-
            updatedActivityBookingStates.find(
              _.itineraryState.product.activities.exists(_.product.booking.bookingId == bookingId)
            )
        } yield workflowAction.copy(state = stateObj.toJson)
      case ProductType.CEGFastTrack =>
        for {
          bookingId <- workflowAction.bookingId
          stateObj <-
            updatedCegFastTrackBookingStates.find(
              _.itineraryState.product.cegFastTracks.exists(_.product.booking.bookingId == bookingId)
            )
        } yield workflowAction.copy(state = stateObj.toJson)
      case _ =>
        Some(workflowAction)
    }
  }

  private def recoverDbExecution[T]: PartialFunction[Throwable, Future[T]] = {
    case error: WorkflowActionNotFound => Future.failed(error)
    case NonFatal(e)                   => Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
  }

  override def insertMultiProductBookingGroup(
      multiProductBookingGroup: MultiProductBookingGroupDBModel
  ): Future[MultiProductBookingGroupDBModel] = {
    val spName         = "multi_product_booking_grouping_insert_v1"
    val queryToExecute = s"""{ call dbo.$spName (?,?,?,?) }"""
    withExecutionTimeMeasure(metricName(), getMethodTag(spName)) {
      Future {
        db.withConnectionGroup(bcreDbConnGroup.value) { implicit conn =>
          insertMultiProductBookingGroupAction(multiProductBookingGroup)
        }
      } andThen {
        handleFailure(spName, query(queryToExecute))
      } recoverWith {
        case NonFatal(e) => Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
      }
    }
  }

  override def getMultiProductBookingGroup(bookingId: Long): Future[Option[MultiProductBookingGroupDBModel]] = {
    val queryName      = "bcre_get_multi_product_booking_group_v1"
    val queryToExecute = query(s"EXEC dbo.$queryName @bookingId = ?", bookingId)
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      toMultiProductBookingGroup
    )
  }

  override def getMultiProductBookingGroupByItineraryId(
      itineraryId: Long
  ): Future[Seq[MultiProductBookingGroupDBModel]] = {
    val queryName      = "bcre_get_multi_product_booking_group_by_itinerary_id_v1"
    val queryToExecute = query(s"EXEC dbo.$queryName @itineraryId = ?", itineraryId)
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      toMultiProductBookingGroups
    )
  }

  override def getNextBaseBookingRelationshipId(): Future[BookingRelationshipId] = {
    val queryName      = "bcre_sequence_next_base_booking_relationship_id_v1"
    val queryToExecute = s"EXEC dbo.$queryName"
    executeQuery(
      bcreDbConnGroup,
      queryName,
      query(queryToExecute),
      result =>
        if (result.next())
          result.getLong(1)
        else
          throw new SQLException(s"Unable to get next base booking relationship id sequence number")
    )
  }
}

class WorkflowActionNotFound(bookingId: Option[Long])
    extends Exception(s"BookingAction bookingId: $bookingId not found")
