package com.agoda.bapi.creation.mapper.ebe.builder

import com.agoda.bapi.common.message.creation.{BookingPayment, PaymentAmount}
import com.agoda.bapi.common.model.{ChannelManagerUserId, ChargeOption}
import com.agoda.bapi.common.model.consumerFintech.ConsumerFintechDetail
import com.agoda.bapi.common.model.creation.{BookingItemBreakdown, BookingRoom, EBEHotel}
import com.agoda.bapi.common.model.property.PropertyBookingStateModel
import com.agoda.bapi.common.util.{JacksonSerializer, JodaDateTimeUtils, JodaToJavaDateTimeConversions}
import com.agoda.bapi.creation.mapper.ebe.{BookingContext, RecStatus}
import com.agoda.bapi.creation.proxy.CreationCDBProxy
import com.agoda.mpb.common.models.state.ProductPaymentInfo
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.agoda.supply.calc.proto.InventoryType
import com.agoda.winterfell.ExecutionContext

import javax.inject.{Inject, Singleton}
import scala.concurrent.Future
import com.agoda.bapi.common.model.creation.PaymentModel
import com.agoda.bapi.common.model.creation.PaymentModel.PaymentModel
import com.agoda.bapi.common.token.common.ProductTokenKey
import com.agoda.bapi.creation.model.PriceDisplaySettingType
import com.agoda.bapi.creation.model.db.AgencyHotelPriceDisplaySetting
import com.agoda.bapi.creation.model.messages.PriceDisplaySettingMessage
import com.agoda.bapi.common.MessageService
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.creation.util.WhitelabelUtils
import com.agoda.mpb.common.serialization.Serialization
import models.consts.DMC

final case class AccountingEntity(
    MerchantOfRecord: Int,
    Revenue: Int,
    RateContract: Int
)

@Singleton
class PropertyBookingSummaryBuilder @Inject() (creationCDBProxy: CreationCDBProxy, messageService: MessageService)(
    implicit ec: ExecutionContext
) {

  private[builder] def getIsNotCcRequired(
      roomInfo: BookingRoom,
      payment: BookingPayment,
      whitelabelId: Int
  )(implicit context: RequestContext): Option[Boolean] = {
    val isJtbWl = WhitelabelUtils.isJtbWl(context.whiteLabelInfo)

    if (isOTARurubuPayNow(roomInfo, payment, whitelabelId))
      Some(false)
    else if (isJtbWl)
      Some(true)
    else
      Some(roomInfo.isNotCcRequired)
  }

  def isOTARurubuPayNow(
      roomInfo: BookingRoom,
      payment: BookingPayment,
      whitelabelId: Int
  )(implicit context: RequestContext): Boolean = {
    val inventoryTypeId = roomInfo.rateCategory.flatMap(_.InventoryTypeId).getOrElse(InventoryType.Agoda.value)
    val paymentMethod   = payment.method
    val isRurubuWl      = WhitelabelUtils.isRurubuWl(context.whiteLabelInfo)

    isRurubuWl && inventoryTypeId == InventoryType.OTARurubu.value && paymentMethod != PaymentMethod.None
  }

  private[builder] def getChargeOption(
      productPayment: Option[ProductPaymentInfo],
      bookingContextChargeOptionId: Int
  ): Option[Int] =
    if (productPayment.exists(_.payLater.isDefined))
      Some(ChargeOption.PayLater.id)
    else
      Some(bookingContextChargeOptionId)

  def createPropertyBookingSummary(
      roomInfo: BookingRoom,
      paymentAmount: PaymentAmount,
      multiBookingId: Option[Long],
      breakdowns: Seq[BookingItemBreakdown],
      bookingContext: BookingContext,
      productPayment: Option[ProductPaymentInfo],
      payment: BookingPayment,
      ebeHotelInfo: EBEHotel,
      isTestBooking: Boolean,
      paymentModel: PaymentModel,
      productTokenKey: Option[ProductTokenKey] = None,
      consumerFintechDetailOpt: Option[ConsumerFintechDetail] = None,
      cancellationChargeItemId: Option[Int] = None
  )(implicit context: RequestContext): Future[PropertyBookingStateModel.PropertyBookingSummary] = {

    val originalSellingAmount       = breakdowns.calculateOriginalSellingAmount
    val originalSupplierAmount      = breakdowns.calculateOriginalSupplierAmount
    val originalLocalSupplierAmount = breakdowns.calculateOriginalLocalSupplierAmount
    val originalFullyChargeDate = productPayment
      .flatMap(_.payLater.map(_.fullyChargeDate))
      .map(JodaToJavaDateTimeConversions.toDateTime) orElse bookingContext.request.payment.creditCard
      .map(_.fullyChargeDate)
      .map(JodaDateTimeUtils.toDateTimeWithoutMillis)
    val originalFullyAuthDate = productPayment
      .flatMap(_.payLater.map(_.fullyAuthDate))
      .map(JodaToJavaDateTimeConversions.toDateTime) orElse bookingContext.request.payment.creditCard
      .map(_.fullyAuthDate)
      .map(JodaDateTimeUtils.toDateTimeWithoutMillis)

    val accountingEntity = roomInfo.accountingEntity.flatMap { account =>
      account.argument
        .filter(_.nonEmpty)
        .orElse(
          JacksonSerializer
            .serialize(
              AccountingEntity(
                MerchantOfRecord = account.merchantOfRecord,
                Revenue = account.revenue,
                RateContract = account.rateContract
              )
            )
            .toOption
        )
    }

    val whitelabelId = bookingContext.requestContext.whiteLabelInfo.whiteLabelId.id

    for {
      priceDisplaySettingJsonString <- priceDisplaySetting(roomInfo, paymentModel)
      _                             <- messageService.sendMessage(getPriceDisplaySettingsMessage(bookingContext, priceDisplaySettingJsonString))
    } yield PropertyBookingStateModel.PropertyBookingSummary(
      bookingId = bookingContext.bookingId,
      multiProductId = multiBookingId,
      displayCurrency = roomInfo.displayCurrency.getOrElse(""), // default on booking agent is empty
      recStatus = RecStatus.Active,
      recCreatedWhen = bookingContext.bookingDate,
      recCreatedBy = bookingContext.request.userId,
      recModifyWhen = None,
      recModifyBy = None,
      originalSellingAmount = Some(originalSellingAmount),
      originalSupplierAmount = Some(originalSupplierAmount),
      originalLocalCurrency = breakdowns.headOption.map(_.localCurrency),
      originalLocalSupplierAmount = Some(originalLocalSupplierAmount),
      originalPaymentCurrency = Some(paymentAmount.paymentCurrency),
      originalLocalPaymentAmount = Some(paymentAmount.paymentAmount),   // todo: need to calculate from payment or not
      originalLocalCcPaymentAmount = Some(paymentAmount.paymentAmount), // todo: need to calculate from payment or not
      originalRedeemAmount = None,                                      // not support redeem
      platformId = bookingContext.request.platformId,
      chargeOptionId = getChargeOption(productPayment, bookingContext.chargeOption.id),
      originalFullyChargeDate = originalFullyChargeDate,
      isUserLoggedIn = Some(bookingContext.request.customer.isUserLoggedIn),
      isTravelAgency = Some(bookingContext.request.isTravelAgency),
      travelAgencyLoginChannel = Some(""), // default by BookingItemMapper `_ta_login_channel`
      originalFullyAuthDate = originalFullyAuthDate,
      taxSurchargeInfo = roomInfo.taxSurchargeInfo,
      isNotCcRequired = getIsNotCcRequired(roomInfo, payment, whitelabelId),
      exchangeRateOption = Some(paymentAmount.exchangeRateOption),
      priceDisplaySetting = Some(priceDisplaySettingJsonString),
      displayAmount = None, // send as 0 on BookingItemMapper
      accountingEntity = accountingEntity,
      bookingCreatedDate = Some(bookingContext.bookingDate),
      whitelabelId = Some(whitelabelId),
      /////////////////////// Provisioning //////////////////////////
      isConfirmedBooking = None,
      stayType = ebeHotelInfo.stayType.map(_.id),
      isTestBooking = Some(isTestBooking),
      isSmartFlex = Some(setIsSmartFlex(roomInfo)),
      originalCancellationPolicyCode = Some(setOriginalCancellationPolicyCode(roomInfo)),
      allGuestSameNationality = setAllGuestSameNationality(bookingContext, productTokenKey),
      cancellationChargeType = getCancellationChargeType(roomInfo),
      cancellationChargeItemId = cancellationChargeItemId,
      isSmartSaver = Some(setIsSmartSaver(roomInfo))
    )
  }

  private[builder] def getCancellationChargeType(
      roomInfo: BookingRoom
  ): Option[Int] = roomInfo.cancellation.cancellationChargeType.map(_.id)
  private[builder] def setAllGuestSameNationality(
      bookingContext: BookingContext,
      productTokenKey: Option[ProductTokenKey]
  ): Option[Boolean] = {
    bookingContext.request.getGuestList(productTokenKey).find(_.primary).flatMap(_.allGuestsSameNationality)
  }

  private[builder] def setOriginalCancellationPolicyCode(
      roomInfo: BookingRoom
  ): String = {
    val smartFlexOriginalCxlCode = for {
      consumerFintechDetail <- roomInfo.bookingConsumerFintech
      smartFlex             <- consumerFintechDetail.products.smartFlex
      offer                 <- smartFlex.offer
    } yield offer.cxlCodeOriginal
    val smartSaverOriginalCxlCode = for {
      consumerFintechDetail <- roomInfo.bookingConsumerFintech
      smartSaver            <- consumerFintechDetail.products.smartSaver
      offer                 <- smartSaver.offer
    } yield offer.cxlCodeOriginal
    (smartFlexOriginalCxlCode, smartSaverOriginalCxlCode) match {
      case (Some(smartFlexOriginalCxlCode), _)  => smartFlexOriginalCxlCode
      case (_, Some(smartSaverOriginalCxlCode)) => smartSaverOriginalCxlCode
      case _                                    => roomInfo.cancellation.code
    }
  }

  private[builder] def setIsSmartFlex(
      roomInfo: BookingRoom
  ): Boolean = {
    for {
      consumerFintechDetail <- roomInfo.bookingConsumerFintech
      smartFlex             <- consumerFintechDetail.products.smartFlex
      offer                 <- smartFlex.offer
    } yield offer.cxlCodeOriginal.nonEmpty
  }.getOrElse(false)

  private[builder] def setIsSmartSaver(
      roomInfo: BookingRoom
  ): Boolean = {
    for {
      consumerFintechDetail <- roomInfo.bookingConsumerFintech
      smartSaver            <- consumerFintechDetail.products.smartSaver
      offer                 <- smartSaver.offer
    } yield offer.cxlCodeOriginal.nonEmpty
  }.getOrElse(false)

  private[builder] def getPriceDisplaySettingsMessage(
      bookingContext: BookingContext,
      priceDisplaySettingJsonString: String
  ): PriceDisplaySettingMessage =
    PriceDisplaySettingMessage(
      bookingContext.bookingId,
      bookingContext.dmcCode,
      priceDisplaySettingJsonString
    )

  private[builder] def priceDisplaySetting(roomInfo: BookingRoom, paymentModel: PaymentModel)(implicit
      requestContext: RequestContext
  ): Future[String] = {
    val isJtbDmcBooking = WhitelabelUtils.isJtbDmcBooking(
      requestContext.whiteLabelInfo,
      roomInfo.dmcId
    )
    (paymentModel, roomInfo.dmcId) match {
      /* We don't have hotel price display setting in DB table for agency payment model because YCS haven't support the
       * setting yet. So, we default it here. */
      case (PaymentModel.Agency, _) if isJtbDmcBooking => getPriceDisplaySettingJtbAgency(roomInfo.hotelId)
      case (PaymentModel.Agency, Some(dmcID)) if dmcID != DMC.Bcom =>
        getPriceDisplaySettingAgodaAgency(roomInfo.hotelId)
      case _ => getPriceDisplaySetting(roomInfo, paymentModel)
    }
  }

  private def getPriceDisplaySettingAgodaAgency(hotelId: Long): Future[String] = {
    creationCDBProxy
      .getHotelChannelManagerUserId(hotelId)
      .map {
        case Some(userId) if userId == ChannelManagerUserId.RedDoorZ =>
          List(
            AgencyHotelPriceDisplaySetting(
              HotelId = hotelId,
              Type = PriceDisplaySettingType.Voucher.id,
              IsShowNetInclusive = true,
              IsShowSellInclusive = true
            ),
            AgencyHotelPriceDisplaySetting(
              HotelId = hotelId,
              Type = PriceDisplaySettingType.YCS_API.id,
              IsShowNetInclusive = true,
              IsShowSellInclusive = true
            ),
            AgencyHotelPriceDisplaySetting(
              HotelId = hotelId,
              Type = PriceDisplaySettingType.YCS_UI.id,
              IsShowNetInclusive = true,
              IsShowSellInclusive = true
            )
          )
        case _ =>
          List(
            AgencyHotelPriceDisplaySetting(
              HotelId = hotelId,
              Type = PriceDisplaySettingType.Voucher.id,
              IsShowSellInclusive = true
            ),
            AgencyHotelPriceDisplaySetting(
              HotelId = hotelId,
              Type = PriceDisplaySettingType.YCS_API.id,
              IsShowSellInclusive = true
            ),
            AgencyHotelPriceDisplaySetting(
              HotelId = hotelId,
              Type = PriceDisplaySettingType.YCS_UI.id,
              IsShowSellInclusive = true
            )
          )
      }
      .map(Serialization.toJson)
  }

  private def getPriceDisplaySettingJtbAgency(hotelId: Long): Future[String] = {
    /* For JTB agency payment model, we require IsShowRefSellInclusive = true and IsShowNetInclusive = true for all
     * channel managers. */
    val settings = List(
      AgencyHotelPriceDisplaySetting(
        HotelId = hotelId,
        Type = PriceDisplaySettingType.Voucher.id,
        IsShowNetInclusive = true,
        IsShowRefSellInclusive = true
      ),
      AgencyHotelPriceDisplaySetting(
        HotelId = hotelId,
        Type = PriceDisplaySettingType.YCS_API.id,
        IsShowNetInclusive = true,
        IsShowRefSellInclusive = true
      ),
      AgencyHotelPriceDisplaySetting(
        HotelId = hotelId,
        Type = PriceDisplaySettingType.YCS_UI.id,
        IsShowNetInclusive = true,
        IsShowRefSellInclusive = true
      )
    )
    Future.successful(Serialization.toJson(settings))
  }

  private def getPriceDisplaySetting(
      roomInfo: BookingRoom,
      paymentModel: PaymentModel
  ): Future[String] =
    creationCDBProxy
      .getAllPriceDisplaySettings(roomInfo.hotelId)
      .map(
        _.filter(_.PaymentModel == paymentModel.id)
          .groupBy(_.Type)
          .values
          .flatMap(_.headOption) // Take only first one of each price display setting type if more than one exist
          .toList
      )
      .map(Serialization.toJson)
}
