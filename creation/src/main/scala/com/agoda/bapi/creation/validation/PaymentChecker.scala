package com.agoda.bapi.creation.validation

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation._
import com.agoda.bapi.common.model.{Storefront, WhiteLabel}
import com.agoda.bapi.common.model.payment.PaymentTokenType
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.product.BookingFlow.BookingFlow
import com.agoda.bapi.creation.model.RequestWithProducts
import com.agoda.bapi.creation.repository.{EbeLiteBookingRepository, PaymentLimitationRepository, PaymentMethodRepository}
import com.agoda.bapi.creation.util.WhitelabelUtils
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpb.common.errors.ErrorCode.ErrorCode
import com.agoda.mpb.common.models.state.ProductPaymentInfo
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod

import scala.concurrent.{ExecutionContext, Future}

trait PaymentChecker {
  def validate(
      request: RequestWithProducts
  )(implicit executionContext: ExecutionContext, requestContext: RequestContext): Future[List[ErrorCode]]
}

trait CommonPaymentChecker extends PaymentChecker {
  val ebeLiteBookingRepository: EbeLiteBookingRepository
  val paymentMethodRepository: PaymentMethodRepository
  val paymentLimitationRepository: PaymentLimitationRepository
  val PaymentMethodsWhichDoesNotRequireContinuationCheck =
    Seq(PaymentMethod.PaoTang, PaymentMethod.UnionPayNTT, PaymentMethod.ApplePay)

  def validate(
      request: RequestWithProducts
  )(implicit executionContext: ExecutionContext, requestContext: RequestContext): Future[List[ErrorCode]] = {
    val payment = request.request.payment

    val currencyValidateFut           = validateCurrencyCode(payment, request.request.storefrontId)
    val roundValidateFut              = validCurrencyRounding(payment)
    val paymentAmountValidFut         = Future.successful(validatePaymentAmount(payment, request.bookingFlow))
    val installmentPlanValidateFut    = Future.successful(validInstallmentPlan(payment))
    val validPaymentTokenValidateFut  = Future.successful(validatePaymentToken(payment))
    val productLevelPaymentFut        = Future.successful(validateProductLevelPayments(request))
    val paymentLimitationValidateFut  = validatePaymentLimitation(request.request)
    val paymentSingleCurrencyValidFut = validateSingleCurrencyCode(request)

    for {
      f1 <- currencyValidateFut
      f2 <- roundValidateFut
      f3 <- paymentAmountValidFut
      f5 <- installmentPlanValidateFut
      f6 <- validPaymentTokenValidateFut
      f7 <- productLevelPaymentFut
      f8 <- paymentLimitationValidateFut
      f9 <- paymentSingleCurrencyValidFut
    } yield f1.toList ::: f2.toList ::: f3 ::: f5.toList ::: f6.toList ::: f7 ::: f8.toList ::: f9.toList
  }

  private[validation] def validateTransactionId(
      payment: BookingPayment
  )(implicit
      executionContext: ExecutionContext
  ): Future[Option[ErrorCode.Value]] = {
    val isRedirect = ebeLiteBookingRepository.isRedirectCard(payment.method.value)
    isRedirect.map { isRedirect =>
      if (
        payment.amount.paymentAmount <= 0 && payment.continuation.isEmpty || payment.method == PaymentMethod.MOHEwallet
      )
        None
      else if (isRedirect && isRequireContinuationCheck(payment.method))
        payment.continuation.flatMap(c => Option(c.transactionId)) match {
          case Some(v) if v.trim.nonEmpty => None
          case _                          => Some(ErrorCode.InvalidTransactionID)
        }
      else None
    }
  }

  private[validation] def validateCurrencyCode(
      payment: BookingPayment,
      storefrontId: Int
  )(implicit executionContext: ExecutionContext): Future[Option[ErrorCode]] = {
    if (payment.amount.paymentCurrency.isEmpty)
      Future.successful(Some(ErrorCode.PaymentCurrencyEmpty))
    else if (storefrontId == Storefront.XmlPartner && payment.disableCurrencyValidation.getOrElse(false))
      Future.successful(None)
    else
      ebeLiteBookingRepository.isCurrencyOffered(payment.amount.paymentCurrency).map {
        case true =>
          None
        case false =>
          Some(ErrorCode.CurrenyIsNotOffered)
      }
  }

  private def getPaymentCurrency(productPayment: Option[ProductPaymentInfo]): Option[String] = {
    productPayment.flatMap(_.payNow.flatMap(payNow => Option(payNow.payment.paymentCurrency).filter(_.nonEmpty)))
  }

  private[validation] def validateSingleCurrencyCode(
      request: RequestWithProducts
  )(implicit executionContext: ExecutionContext, requestContext: RequestContext): Future[Option[ErrorCode]] = {
    val paymentCurrencySet =
      if (requestContext.featureAware.exists(_.validatePaymentCurrencyForFlightVehicleActivity)) {
        request.products.properties.flatMap(p => getPaymentCurrency(p.productPayment)) ++
          request.products.cegFastTracks.flatMap(c => getPaymentCurrency(c.productPayment)) ++
          request.products.addOns.flatMap(a => getPaymentCurrency(a.productPayment)) ++
          request.products.flights.flatMap(f => getPaymentCurrency(f.productPayment)) ++
          request.products.vehicles.flatMap(v => getPaymentCurrency(v.productPayment)) ++
          request.products.activities.flatMap(a => getPaymentCurrency(a.productPayment))
      }.toSet
      else
        {
          request.products.properties.flatMap(p => getPaymentCurrency(p.productPayment)) ++
            request.products.cegFastTracks.flatMap(c => getPaymentCurrency(c.productPayment)) ++
            request.products.addOns.flatMap(a => getPaymentCurrency(a.productPayment))
        }.toSet

    if (paymentCurrencySet.size > 1) {
      Future.successful(Some(ErrorCode.UnexpectedPaymentError))
    } else {
      Future.successful(None)
    }
  }

  private[validation] def validCurrencyRounding(
      payment: BookingPayment
  )(implicit executionContext: ExecutionContext): Future[Option[ErrorCode]] = {
    isCurrencyRoundingValid(payment.amount).map(result => if (result) None else Some(ErrorCode.IncorrectRounding))
  }

  // TODO: Installment plan field is not provided in request yet
  private[validation] def validInstallmentPlan(payment: BookingPayment): Option[ErrorCode] = {
    None // 746
  }

  private[validation] def validatePaymentToken(payment: BookingPayment): Option[ErrorCode] = { // 2600
    // Payment token is not null or white space when this payment is Apply pay
    val tokenType = for {
      continuation <- payment.continuation
      token        <- continuation.paymentToken
    } yield token.tokenType

    if (
      isApplePay(payment) && (!tokenType
        .contains(PaymentTokenType.ApplePay) || payment.getPaymentTokenValue.getOrElse("").trim.isEmpty)
    )
      Some(ErrorCode.RequiredPaymentToken)
    else None
  }

  private[validation] def validatePaymentAmount(
      payment: BookingPayment,
      bookingFlow: BookingFlow
  ): List[ErrorCode] = {
    val paymentAmount = payment.amount
    val onlyExchangeRateAvailable =
      bookingFlow == BookingFlow.Package |
        bookingFlow == BookingFlow.SingleFlight |
        bookingFlow == BookingFlow.SingleVehicle |
        bookingFlow == BookingFlow.SingleActivity

    List(
      if (onlyExchangeRateAvailable)
        errorHandler(paymentAmount.exchangeRate != 0, ErrorCode.ExchangeRateZero)
      else
        errorHandler(
          paymentAmount.siteExchangeRate != 0 && paymentAmount.upliftExchangeRate != 0 && paymentAmount.exchangeRate != 0,
          ErrorCode.ExchangeRateZero
        ),
      errorHandler(paymentAmount.exchangeRateOption >= 0, ErrorCode.InvalidExchangeRateOption),
      if (onlyExchangeRateAvailable)
        errorHandler(paymentAmount.exchangeRate > 0, ErrorCode.ExchangeRateZero)
      else
        errorHandler(
          paymentAmount.exchangeRate > 0 && paymentAmount.upliftExchangeRate > 0 && paymentAmount.siteExchangeRate > 0,
          ErrorCode.InvaidExchangeRate
        ),
      errorHandler(
        paymentAmount.rewardsSaving <= 0 || paymentAmount.giftcardAmountUSD <= 0,
        ErrorCode.InvalidRedeemInfo
      )
    ).flatten
  }

  private def isCurrencyRoundingValid(
      amount: PaymentAmount
  )(implicit executionContext: ExecutionContext): Future[Boolean] = {
    def hasPointNumber(bigDecimal: BigDecimal) = bigDecimal % 1.0 > 0

    ebeLiteBookingRepository
      .getConfigurations(6, "RNDCCY")
      .map(currencyConfig =>
        currencyConfig.map(conf => {
          val currencies = conf.split(",")

          if (currencies.contains(amount.paymentCurrency))
            amount.paymentAmount == 0 || !hasPointNumber(amount.paymentAmount)
          else true
        })
      )
      .map(_.getOrElse(true))
  }

  private[validation] def isApplePay(payment: BookingPayment): Boolean =
    payment.method == PaymentMethod.ApplePay

  private[validation] def isRequireContinuationCheck(paymentMethod: PaymentMethod): Boolean =
    !PaymentMethodsWhichDoesNotRequireContinuationCheck.contains(paymentMethod)

  private def errorHandler(isValid: Boolean, errorCode: ErrorCode): Option[ErrorCode] = {
    if (isValid) None else Some(errorCode)
  }

  private[validation] def validateProductLevelPayments(
      request: RequestWithProducts
  )(implicit requestContext: RequestContext): List[ErrorCode] = {
    if (request.request.isMPBE() && shouldValidateByWhitelabel())
      List(
        errorHandler(
          request.products.properties.forall(_.productPayment.isDefined),
          ErrorCode.InsufficientPaymentDataError
        ),
        errorHandler(
          request.products.flights.forall(_.productPayment.isDefined),
          ErrorCode.InsufficientPaymentDataError
        ),
        errorHandler(
          request.products.protections.forall(_.productPayment.isDefined),
          ErrorCode.InsufficientPaymentDataError
        ),
        errorHandler(
          request.products.vehicles.forall(_.productPayment.isDefined),
          ErrorCode.InsufficientPaymentDataError
        ),
        errorHandler(
          request.products.addOns.forall(_.financials.flatMap(_.productPayment).isDefined),
          ErrorCode.InsufficientPaymentDataError
        ),
        errorHandler(
          request.products.commonPayment.isDefined,
          ErrorCode.InsufficientPaymentDataError
        )
      ).flatten
    else List.empty[ErrorCode]
  }

  private[validation] def validatePaymentLimitation(
      request: CreateBookingRequest
  )(implicit executionContext: ExecutionContext, requestContext: RequestContext): Future[List[ErrorCode]] = {
    if (request.payment.isApplyPaymentLimitationCheck.getOrElse(false)) {
      val binLength = 6

      // Check if the experiment is on the B side
      val paymentLimitationFuture = if (requestContext.featureAware.exists(_.RemoveReadingPMCCampaignDataFromBFDB)) {
        Future.successful(None)
      } else {
        paymentLimitationRepository.getPaymentLimitation(request.siteId.getOrElse(0))
      }

      paymentLimitationFuture.map {
        case Some(paymentInfo) =>
          val paymentMethodError = if (paymentInfo.paymentMethodIds.isDefined) {
            List(
              errorHandler(
                paymentInfo.paymentMethodIds.get.contains(request.payment.method.value),
                ErrorCode.InvalidPaymentMethodError
              )
            ).flatten
          } else List.empty[ErrorCode]

          val creditCardNumber = request.payment.creditCard.map(_.creditcardNumber).getOrElse("")
          val cardNumberError = if (paymentInfo.binList.isDefined && creditCardNumber.nonEmpty) {
            val bin = creditCardNumber.take(binLength).toInt
            List(
              errorHandler(
                paymentInfo.binList.get.contains(bin),
                ErrorCode.InvalidCardNumber
              )
            ).flatten
          } else List.empty[ErrorCode]

          paymentMethodError ++ cardNumberError

        case None => List.empty[ErrorCode] // Return empty list if no payment info is found
      }
    } else {
      Future.successful(List.empty[ErrorCode])
    }
  }

  private def shouldValidateByWhitelabel()(implicit requestContext: RequestContext): Boolean = {
    !WhitelabelUtils.isJtbSupplyWl(requestContext.whiteLabelInfo)
  }
}
