package com.agoda.bapi.creation.service.stage.presave

import com.agoda.bapi.common.handler.{MeasurementsContext, RequestContext}
import com.agoda.bapi.common.message.creation.CreateBookingResponse
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.car.{CarBookingToken, EnigmaCarDriverInfo}
import com.agoda.bapi.common.model.payment.CreditCardBillingInfo
import com.agoda.bapi.common.model.product.ProductTypeEnum
import com.agoda.bapi.common.model.product.ProductTypeEnum.ProductTypeEnum
import com.agoda.bapi.common.proxy.EnigmaApiProxy
import com.agoda.bapi.creation.mapper.ebe.VehicleMapper
import com.agoda.bapi.creation.model.CreateRequest
import com.agoda.bapi.creation.model.multi.{NonPropertySaveStageResponse, PreSaveProductStageRequest, ReservedIds, VehicleReservedIds}
import com.agoda.bapi.creation.repository.{FlightBookingRepository, VehicleBookingRepository}

import javax.inject.Inject
import scala.concurrent.{ExecutionContext, Future}

class VehiclePreSaveStage @Inject() (
    enigmaApiProxy: EnigmaApiProxy,
    vehicleBookingRepository: VehicleBookingRepository,
    vehicleMapper: VehicleMapper,
    val workflowRepository: FlightBookingRepository
)(implicit val executionContext: ExecutionContext)
    extends ProductPreSaveStage[
      MultiProductPreSaveRequest,
      CarBookingToken,
      NonPropertySaveStageResponse,
      VehicleReservedIds
    ]
    with NonHotelPreSaveStage[CarBookingToken] {
  override def productType(preSaveProductStageRequest: PreSaveProductStageRequest[CarBookingToken]): ProductTypeEnum =
    ProductTypeEnum.Car

  override protected def toPreSaveProductStageRequests(
      preSaveRequest: MultiProductPreSaveRequest
  ): Seq[PreSaveProductStageRequest[CarBookingToken]] =
    preSaveRequest.multiProductsRequest.vehicles.map(createPreSaveProductStageRequest(preSaveRequest, _))

  override protected def preSave(
      request: PreSaveProductStageRequest[CarBookingToken]
  )(implicit
      measurementCxt: MeasurementsContext,
      context: RequestContext
  ): Future[Either[CreateBookingResponse, NonPropertySaveStageResponse]] = {
    val drivers   = request.request.getVehicleDriverList(request.product.info.productTokenKey)
    val driverAge = request.product.info.driverInfo.map(_.driverAge)

    // Instead of getting driver age from carItemRequest, we will get it from create token
    val updatedDrivers = drivers.map(_.toEnigmaDriverInfo.copy(age = driverAge.getOrElse(0)))

    for {
      reservedIds <- measuredReservedProductId(request)
      driverIds   <- Future.traverse(updatedDrivers)(_ => vehicleBookingRepository.getNextVehicleSequenceNumber)
      _           <- saveDataToEnigma(request, reservedIds.bookingId.toInt, updatedDrivers, driverIds)
      vehicleInternalModel = vehicleMapper.toInternalModel(
                               request = request,
                               itineraryId = request.itineraryInfo.itineraryId,
                               masterActionId = request.itineraryInfo.actionId,
                               reservedIds = reservedIds,
                               enigmaPassengerIds = driverIds
                             )
    } yield {
      val bookingActionState = vehicleMapper.toBookingActionState(
        request,
        request.itineraryInfo.itineraryId,
        reservedIds,
        vehicleInternalModel
      )
      val bookingAction =
        vehicleMapper.toBookingAction(
          request,
          request.itineraryInfo.itineraryId,
          reservedIds,
          bookingActionState
        )
      Right(
        NonPropertySaveStageResponse(
          productType(request),
          bookingAction,
          bookingActionState,
          productTokenKey = request.product.info.productTokenKey
        )
      )
    }
  }

  private[presave] def saveDataToEnigma(
      request: CreateRequest,
      bookingId: Long,
      drivers: Seq[EnigmaCarDriverInfo],
      ids: Seq[Long]
  ): Future[Unit] = {
    implicit val measurementCxt: MeasurementsContext = request.measurementsContext
    implicit val requestContext: RequestContext      = request.requestContext
    withMeasureAndLogWithContext(measurementCxt)(creationMeasurementName("vehicle.SaveDataToEnigma")) {
      val saveVehicleBillingF = enigmaApiProxy.saveVehicleBillingInfo(
        bookingId,
        CreditCardBillingInfo(request.request.payment.creditCard),
        request.getCustomer
      )
      val saveVehicleDriver = enigmaApiProxy.saveVehicleDriver(bookingId, drivers, ids)
      for {
        _ <- saveVehicleBillingF
        _ <- saveVehicleDriver
      } yield {}
    }
  }

  override protected def reservedProductId(
      request: PreSaveProductStageRequest[CarBookingToken]
  )(implicit measurementCxt: MeasurementsContext): Future[ReservedIds[CarBookingToken, VehicleReservedIds]] = {
    val numberOfBreakdowns = request.product.info.info.priceBreakdowns.size
    for {
      genericReservedIds <- super.reservedProductId(request)
      breakdownIds       <- workflowRepository.getNextBreakdownIdSequenceNumbers(numberOfBreakdowns)
      pickUpId           <- vehicleBookingRepository.getNextVehicleBookingLocationSequenceNumber
      dropOffId          <- vehicleBookingRepository.getNextVehicleBookingLocationSequenceNumber
      infoId             <- vehicleBookingRepository.getNextVehicleInfoSequenceNumber
    } yield genericReservedIds.copy(
      breakdownIds = breakdownIds,
      productReservedIds = Some(
        VehicleReservedIds(
          vehicleBookingLocationPickUpId = Some(pickUpId),
          vehicleBookingLocationDropOffId = Some(dropOffId),
          vehicleInfoId = Some(infoId)
        )
      )
    )
  }
}
