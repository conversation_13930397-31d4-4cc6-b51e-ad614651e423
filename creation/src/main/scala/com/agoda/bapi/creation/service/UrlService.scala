package com.agoda.bapi.creation.service
import com.agoda.bapi.Types.DmcId
import com.agoda.bapi.common.ToolSet
import com.agoda.bapi.common.config.{AgodaConfig, WhitelabelConfig}
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.model.WhiteLabel.WhiteLabel
import com.agoda.bapi.common.model.{BookingId, WhiteLabel}
import com.agoda.bapi.common.util.EncryptionHelper
import com.agoda.bapi.creation.repository.EbeLiteBookingRepository
import com.agoda.bapi.execution.context.NonBlockingExecutionContext.default
import com.agoda.bapi.creation.util.WhitelabelUtils
import com.agoda.mpb.common.models.state.ProductType
import com.agoda.mpb.common.models.state.ProductType.ProductType

import java.net.URLEncoder
import java.util.Base64
import javax.inject.{Inject, Singleton}
import scala.concurrent.Future

trait UrlService {
  def getSelfServiceURL(bookingId: Long, productType: ProductType): Future[String]
  def getSelfServiceURLs(baseUrl: String): Future[BookingId => String]
  def getSelfServiceURL(
      whitelabelId: WhiteLabel,
      bookingId: Long
  )(implicit requestContext: RequestContext): Future[String]
  def getSelfServiceURLs(
      whitelabelId: WhiteLabel,
      isDomainIncluded: Boolean = true,
      dmcId: Option[DmcId] = None
  )(implicit requestContext: RequestContext): Future[BookingId => String]
}

@Singleton
class UrlServiceImpl @Inject() (
    agodaConfig: AgodaConfig,
    ebeLiteBookingRepository: EbeLiteBookingRepository,
    whitelabelConfig: WhitelabelConfig
) extends UrlService
    with ToolSet {

  def getDomainUrl(optURL: Option[String], defaultDomain: String, isDomainIncluded: Boolean): String = {
    if (isDomainIncluded) {
      val domain = optURL
        .filter(_.nonEmpty)
        .getOrElse(defaultDomain)
      if (domain.last == '/') domain.dropRight(1) else domain
    } else {
      ""
    }
  }

  def encode(param: String): String = param

  override def getSelfServiceURL(bookingId: Long, productType: ProductType): Future[String] =
    getSelfServiceURLs(s"${agodaConfig.domainUrl}${agodaConfig.getSelfServiceURL(productType)}").map(_(bookingId))

  override def getSelfServiceURLs(baseUrl: String): Future[BookingId => String] = {
    ebeLiteBookingRepository
      .getEncryptConfiguration(EncryptionHelper.ebeAfmConfigGroupId)
      .map(configuration =>
        (bookingId: BookingId) => {
          val encryptedBookingId: Option[String] = encrypt(configuration, bookingId.toString)
          encryptedBookingId
            .map(parameters => {
              val urlEncodedParam = URLEncoder.encode(parameters, UrlServiceImpl.Charset)
              s"${baseUrl}?bookingId=$urlEncodedParam"
            })
            .getOrElse("")
        }
      )
  }

  override def getSelfServiceURL(
      whitelabelId: WhiteLabel,
      bookingId: Long
  )(implicit requestContext: RequestContext): Future[String] =
    getSelfServiceURLs(whitelabelId = whitelabelId).map(_(bookingId))

  override def getSelfServiceURLs(
      whitelabelId: WhiteLabel,
      isDomainIncluded: Boolean = true,
      dmcId: Option[DmcId] = None
  )(implicit requestContext: RequestContext): Future[BookingId => String] = {
    val shouldBlockSelfServiceURL =
      if (requestContext.featureAware.exists(_.isRemoveFenceSelfServiceURLsForRurubu)) {
        WhitelabelUtils
          .getDmcControlSetting(requestContext.whiteLabelInfo, dmcId)
          .exists(_.isBlockSelfServiceUrl.getOrElse(false))
      } else {
        whitelabelId == WhiteLabel.Rurubu || whitelabelId == WhiteLabel.Jtb
      }

    val result = if (shouldBlockSelfServiceURL) {
      Future.successful((_: BookingId) => "")
    } else {
      ebeLiteBookingRepository.getEncryptConfiguration(EncryptionHelper.ebeAfmConfigGroupId).map {
        configuration => (bookingId: BookingId) =>
          {
            val encryptedBookingId: Option[String] = encrypt(configuration, bookingId.toString)
            val domainURL                          = whitelabelConfig.getCanonicalUrl(whitelabelId)
            encryptedBookingId
              .map { parameters =>
                val domain          = getDomainUrl(domainURL, agodaConfig.domainUrl, isDomainIncluded)
                val urlEncodedParam = URLEncoder.encode(parameters, UrlServiceImpl.Charset)
                s"$domain${agodaConfig.getSelfServiceURL(ProductType.Hotel)}?bookingId=$urlEncodedParam"
              }
              .getOrElse("")
          }
      }
    }

    result
  }

  def encrypt(config: Map[String, String], str: String): Option[String] = {
    EncryptionHelper.encrypt(config, str)
  }
}

object UrlServiceImpl {
  private val Charset = "utf-8"
  private val MD5Key  = "agoda2009!@"
}
