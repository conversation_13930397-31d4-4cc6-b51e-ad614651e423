package com.agoda.bapi.creation.service

import akka.actor.Scheduler
import com.agoda.bapi.common.config.KillSwitches
import com.agoda.bapi.common.handler.{MeasurementsContext, RequestContext}
import com.agoda.bapi.common.message.creation.{ActivityPax, BookingElement, CreateBookingRequest, DuplicateBooking, DuplicateCandidateStatus, FlightPax, MpbWorkflowPhase}
import com.agoda.bapi.common.model.flight.FlightInfo
import com.agoda.bapi.common.model.product.ProductTypeEnum
import com.agoda.bapi.common.model.{ItineraryId, StatusToken}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.util.ServerUtils
import com.agoda.bapi.creation.mapper.ebe.{EBEHotelNotFound, FlightInfoNotFound}
import com.agoda.bapi.creation.model.db.DatabaseEnum.DatabaseEnum
import com.agoda.bapi.creation.model.db.{DatabaseEnum, PropertyDuplicatedBookingCandidate}
import com.agoda.bapi.creation.model.properties.PropertyBookingDuplicationCriteria
import com.agoda.bapi.creation.repository._
import com.agoda.bapi.creation.service.stage.{AffiliateTagIdDuplicateValidationLog, DuplicateBookingReporter}
import com.agoda.bapi.creation.service.stage.CombineMdbBfdbDuplicateAccuracyCheckerLog
import com.agoda.bapi.creation.util.DuplicateCheckUtil.isValidateAffiliateTagId
import com.agoda.bapi.creation.util.{StatusTokenUtils, WhitelabelUtils}
import com.agoda.bapi.execution.context.NonBlockingExecutionContext.default
import com.agoda.capi.enigma.shared_model.booking.pax.BaseBookingPax
import org.joda.time.DateTime

import javax.inject.{Inject, Named}
import scala.concurrent.{ExecutionContext, Future}

trait DuplicateCheckService {
  def findPropertyDuplicates(
      propertyBookingDuplicationCriteria: Option[PropertyBookingDuplicationCriteria],
      featureAware: Option[FeatureAware],
      request: CreateBookingRequest
  )(implicit
      measurementCxt: MeasurementsContext,
      requestContext: RequestContext
  ): Future[(Seq[DuplicateBooking], DatabaseEnum)]

  def findFlightDuplicates(flights: FlightInfo, primaryGuest: FlightPax, storefrontId: Int, checkInLocalDb: Boolean)(
      implicit executionContext: ExecutionContext
  ): Future[Seq[DuplicateBooking]]

  def findVehicleDuplicates(
      identifier: String,
      pickupDatetime: DateTime,
      dropOffDatetime: DateTime,
      pickupLocationCode: String,
      dropOffLocationCode: String
  )(implicit executionContext: ExecutionContext): Future[Seq[DuplicateBooking]]

  def findActivityDuplicates(
      productId: Int,
      bookingStartDate: DateTime,
      offerId: Int,
      offerStartTime: Option[String],
      offerEndTime: Option[String]
  )(implicit executionContext: ExecutionContext): Future[Seq[DuplicateBooking]]

  def findAllPaxMisMatchBookings(
      activityDuplicatedBooking: Seq[DuplicateBooking],
      paxRequest: Seq[ActivityPax]
  )(implicit requestContext: RequestContext): Future[Seq[Long]]
}

class DuplicateCheckServiceImpl @Inject() (
    duplicateFlightBookingRepository: DuplicateFlightBookingRepository,
    ebeLiteBookingRepository: EbeLiteBookingRepository,
    vehicleBookingRepository: VehicleBookingRepository,
    workflowRepository: WorkflowRepository,
    activityBookingRepository: ActivityBookingRepository,
    creationMdbRepository: CreationMdbRepository,
    urlService: UrlService,
    override val hadoopMessagingService: HadoopMessagingService,
    scheduler: Scheduler
)(implicit killSwitches: KillSwitches)
    extends DuplicateCheckService
    with DuplicateBookingReporter {

  def findFlightDuplicates(flight: FlightInfo, primaryGuest: FlightPax, storefrontId: Int, checkInLocalDb: Boolean)(
      implicit executionContext: ExecutionContext
  ): Future[Seq[DuplicateBooking]] = {
    flight.slices.headOption
      .flatMap(_.segments.headOption)
      .map { segment =>
        val origin        = segment.origin
        val destination   = segment.destination
        val flightNumber  = segment.flightNumber
        val departureDate = segment.departure.toLocalDate
        duplicateFlightBookingRepository.checkFlightDuplicate(
          origin,
          destination,
          flightNumber,
          departureDate,
          primaryGuest.piiHash,
          checkInLocalDb
        )
      }
      .getOrElse(Future.failed(new FlightInfoNotFound()))
  }

  private def findAllPropertyDuplicatesInMDB(
      propertyBookingDuplicationCriteria: PropertyBookingDuplicationCriteria,
      featureAware: Option[FeatureAware],
      isTagIdCheckEnabled: Option[Boolean]
  )(implicit measurementsContext: MeasurementsContext): Future[Seq[PropertyDuplicatedBookingCandidate]] = {
    withMeasureAndLogWithContext(measurementsContext)(DuplicationCheckInMdbWithCombineTimeMetric) {
      creationMdbRepository
        .findAllDuplicatedBookingCandidatesMDB(
          propertyBookingDuplicationCriteria,
          featureAware,
          scheduler,
          isTagIdCheckEnabled
        )
        .recover {
          case ex: Throwable =>
            logWarn("DuplicateCheckService.findAllPropertyDuplicatesInMDB failed due to reason: ", Some(ex))
            Seq.empty
        }
    }
  }

  private def findAllPropertyDuplicatesInBFDB(
      propertyBookingDuplicationCriteria: PropertyBookingDuplicationCriteria,
      featureAware: Option[FeatureAware],
      isTagIdCheckEnabled: Option[Boolean]
  )(implicit measurementsContext: MeasurementsContext): Future[Seq[PropertyDuplicatedBookingCandidate]] =
    withMeasureAndLogWithContext(measurementsContext)(DuplicationCheckInBfdbWithCombineTimeMetric) {
      ebeLiteBookingRepository
        .findAllDuplicatedBookingCandidatesBFDB(
          propertyBookingDuplicationCriteria,
          featureAware,
          isTagIdCheckEnabled
        )
        .recover {
          case ex: Throwable =>
            logWarn("DuplicateCheckService.findAllPropertyDuplicatesInBFDB failed due to reason: ", Some(ex))
            Seq.empty
        }
    }

  def findPropertyDuplicates(
      propertyBookingDuplicationCriteria: Option[PropertyBookingDuplicationCriteria],
      featureAware: Option[FeatureAware],
      request: CreateBookingRequest
  )(implicit
      measurementCxt: MeasurementsContext,
      requestContext: RequestContext
  ): Future[(Seq[DuplicateBooking], DatabaseEnum)] =
    propertyBookingDuplicationCriteria
      .map { propertyBookingDuplicationCriteria =>
        def toDuplicateBooking(duplicate: PropertyDuplicatedBookingCandidate): Future[DuplicateBooking] = {
          val whiteLabel = propertyBookingDuplicationCriteria.whiteLabel
          if (isValidateAffiliateTagId(request.storefrontId, featureAware)) {
            for {
              selfServiceUrl <-
                urlService.getSelfServiceURL(whiteLabel, duplicate.bookingId)
              statusToken <- getStatusToken(duplicate.itineraryId, requestContext)(featureAware)
              _ =
                if (
                  statusToken.isEmpty
                ) // if statusToken is empty, please check itineraryId is exist or master booking action is exist on same DC
                  logger.log(
                    AffiliateTagIdDuplicateValidationLog(
                      s"Can't build statusToken for this bookingId: ${duplicate.bookingId}, itineraryId: ${duplicate.itineraryId}",
                      requestContext
                    )
                  )
            } yield DuplicateBooking(
              BookingElement.Hotel,
              duplicate.bookingId,
              duplicate.bookingDate,
              selfServiceUrl,
              statusToken
            )
          } else {
            urlService.getSelfServiceURL(whiteLabel, duplicate.bookingId).map { selfServiceUrl =>
              DuplicateBooking(BookingElement.Hotel, duplicate.bookingId, duplicate.bookingDate, selfServiceUrl)
            }
          }
        }

        val isTagIdCheckEnabled = isValidateAffiliateTagId(request.storefrontId, featureAware)

        val findDuplicatesInBFDB = findAllPropertyDuplicatesInBFDB(
          propertyBookingDuplicationCriteria,
          featureAware,
          Some(isTagIdCheckEnabled)
        )

        val findDuplicatesInMDB = findAllPropertyDuplicatesInMDB(
          propertyBookingDuplicationCriteria,
          featureAware,
          Some(isTagIdCheckEnabled)
        )

        def combineResult(
            mdbDuplicatedCandidates: Seq[PropertyDuplicatedBookingCandidate],
            bfdbDuplicatedCandidates: Seq[PropertyDuplicatedBookingCandidate]
        ): Seq[PropertyDuplicatedBookingCandidate] = {
          val uniqueBookingIds = (mdbDuplicatedCandidates ++ bfdbDuplicatedCandidates).map(_.bookingId).distinct

          val combinedAndFilteredDuplicatedBooking = uniqueBookingIds
            .flatMap { bookingId =>
              val mdbCandidate  = mdbDuplicatedCandidates.find(_.bookingId == bookingId)
              val bfdbCandidate = bfdbDuplicatedCandidates.find(_.bookingId == bookingId)
              selectCandidate(mdbCandidate, bfdbCandidate)
            }
            .filter {
              case (duplicatedBooking, source) =>
                duplicatedBooking.candidateStatus.contains(DuplicateCandidateStatus.Active)
            }
            .sortBy {
              case (duplicatedBooking, source) =>
                duplicatedBooking.bookingDate.getMillis
            }(Ordering.Long.reverse)
            .map {
              case (duplicatedBooking, source) =>
                sendDuplicateBookingHadoopLog(duplicatedBooking, source)
                duplicatedBooking
            }
          combinedAndFilteredDuplicatedBooking
        }

        def selectCandidate(
            maybeMdbCandidate: Option[PropertyDuplicatedBookingCandidate],
            maybeBfdbCandidate: Option[PropertyDuplicatedBookingCandidate]
        ): Option[(PropertyDuplicatedBookingCandidate, DatabaseEnum)] = {
          val mdbCandidateAndSource  = maybeMdbCandidate.map((_, DatabaseEnum.MDB))
          val bfdbCandidateAndSource = maybeBfdbCandidate.map((_, DatabaseEnum.BFDB))

          val candidatePriority = Map(
            DuplicateCandidateStatus.Inactive  -> 3, // top priority
            DuplicateCandidateStatus.Active    -> 2,
            DuplicateCandidateStatus.Awaiting  -> 1,
            DuplicateCandidateStatus.Undefined -> -999
          )

          val mdbStatus  = maybeMdbCandidate.flatMap(_.candidateStatus).getOrElse(DuplicateCandidateStatus.Undefined)
          val bfdbStatus = maybeBfdbCandidate.flatMap(_.candidateStatus).getOrElse(DuplicateCandidateStatus.Undefined)

          if (candidatePriority(mdbStatus) >= candidatePriority(bfdbStatus)) mdbCandidateAndSource
          else bfdbCandidateAndSource
        }

        def logAccuracyChecker(
            mdbResult: Seq[PropertyDuplicatedBookingCandidate],
            bfdbResult: Seq[PropertyDuplicatedBookingCandidate],
            combinedResult: Seq[PropertyDuplicatedBookingCandidate]
        ): Unit = {
          // only consider bookingId and candidateStatus for comparison
          val toBeComparedMdbResult  = mdbResult.map(result => (result.bookingId, result.candidateStatus))
          val toBeComparedBfdbResult = bfdbResult.map(result => (result.bookingId, result.candidateStatus))
          if (toBeComparedMdbResult.toSet != toBeComparedBfdbResult.toSet) {
            val message = if (combinedResult.nonEmpty) {
              s"blocked (${combinedResult.map(_.bookingId).mkString(", ")})."
            } else {
              s"no block."
            }
            log(
              CombineMdbBfdbDuplicateAccuracyCheckerLog(
                message,
                propertyBookingDuplicationCriteria,
                mdbResult,
                bfdbResult,
                combinedResult,
                requestContext
              )
            )
          }
        }

        for {
          mdbDuplicatedBookings     <- findDuplicatesInMDB
          bfdbDuplicatedBookings    <- findDuplicatesInBFDB
          combinedDuplicatedBookings = combineResult(mdbDuplicatedBookings, bfdbDuplicatedBookings)
          _                          = logAccuracyChecker(mdbDuplicatedBookings, bfdbDuplicatedBookings, combinedDuplicatedBookings)
          duplicateBookings         <- Future.traverse(combinedDuplicatedBookings)(toDuplicateBooking)
        } yield (duplicateBookings, DatabaseEnum.COMBINED)
      }
      .getOrElse(Future.failed(new EBEHotelNotFound()))

  private[service] def getStatusToken(itineraryId: Option[ItineraryId], requestContext: RequestContext)(implicit
      featureAware: Option[FeatureAware]
  ): Future[Option[String]] =
    itineraryId
      .map { itineraryId =>
        workflowRepository.getMasterBookingActionByItineraryId(itineraryId).map { bookingActionOpt =>
          bookingActionOpt
            .map { bookingAction =>
              val whiteLabelId = requestContext.whiteLabelInfo.whiteLabelId
              val (version, operationId) =
                StatusTokenUtils.getStatusTokenVersionAndOperationId(None)(requestContext)
              val topic = StatusTokenUtils.getBookingActionMessageTopic(whiteLabelId)(requestContext)
              val statusToken = StatusToken(
                itineraryId = bookingAction.itineraryId,
                actionId = bookingAction.actionId,
                productType = Set(ProductTypeEnum.Property.toString), // Expect only single property to this flow
                dc = ServerUtils.serverDc(),
                whitelabelId = Some(whiteLabelId.id),
                topic = topic,
                version = Some(version),
                operationId = operationId
              )
              statusToken.serialize(log, withMeasure)
            }
        }
      }
      .getOrElse(Future.successful(None))

  def findVehicleDuplicates(
      identifier: String,
      pickUpDatetime: DateTime,
      dropOffDatetime: DateTime,
      pickupLocationCode: String,
      dropOffLocationCode: String
  )(implicit executionContext: ExecutionContext): Future[Seq[DuplicateBooking]] = {
    vehicleBookingRepository.checkVehicleDuplicate(
      identifier,
      pickUpDatetime,
      dropOffDatetime,
      pickupLocationCode,
      dropOffLocationCode
    )
  }

  override def findActivityDuplicates(
      productId: Int,
      bookingStartDate: DateTime,
      offerId: Int,
      offerStartTime: Option[String],
      offerEndTime: Option[String]
  )(implicit executionContext: ExecutionContext): Future[Seq[DuplicateBooking]] =
    activityBookingRepository.checkDuplicateActivityBooking(
      productId,
      bookingStartDate,
      offerId,
      offerStartTime,
      offerEndTime
    )

  private def isEqualPax(ePax: BaseBookingPax, rPax: ActivityPax): Boolean = {
    ePax.isPrimary == rPax.primary &&
    ePax.paxType == rPax.paxType.id &&
    ePax.firstName == rPax.firstname &&
    ePax.middleName == rPax.middlename &&
    ePax.lastName == rPax.lastname &&
    ePax.gender == rPax.gender
  }
  private def isDuplicate(enigmaPax: Seq[BaseBookingPax], paxRequest: Seq[ActivityPax]): Boolean = {
    if (enigmaPax.size == paxRequest.size) {
      val sortedEnigmaPax = enigmaPax.sortBy(ePax =>
        (
          ePax.isPrimary,
          ePax.paxType,
          ePax.firstName,
          ePax.middleName,
          ePax.lastName,
          ePax.gender
        )
      )
      val sortedRequestPax = paxRequest.sortBy(rPax =>
        (
          rPax.primary,
          rPax.paxType.id,
          rPax.firstname,
          rPax.middlename,
          rPax.lastname,
          rPax.gender
        )
      )
      sortedEnigmaPax.zip(sortedRequestPax).forall { case (enigmaPax, requestPax) => isEqualPax(enigmaPax, requestPax) }
    } else {
      false
    }
  }

  def findAllPaxMisMatchBookings(
      activityDuplicatedBooking: Seq[DuplicateBooking],
      paxRequest: Seq[ActivityPax]
  )(implicit requestContext: RequestContext): Future[Seq[Long]] = {
    val paxMissMatchBooking = activityDuplicatedBooking.map { dBooking =>
      val enigmaPaxF = activityBookingRepository.getPaxFromEnigma(dBooking.bookingId)
      enigmaPaxF.map { enigmaPax =>
        if (!isDuplicate(enigmaPax, paxRequest)) {
          Some(dBooking.bookingId)
        } else {
          None
        }
      }
    }
    Future.sequence(paxMissMatchBooking).map(_.flatten)
  }

  private def sendDuplicateBookingHadoopLog(duplicateBooking: PropertyDuplicatedBookingCandidate, source: DatabaseEnum)(
      implicit requestContext: RequestContext
  ): Future[Unit] = {
    hadoopMessagingService.sendDuplicateBookingLogMessage(
      correlationId = requestContext.correlationId,
      bookingSessionId = Some(requestContext.getBookingSessionId()),
      sessionId = requestContext.bookingCreationContext.map(_.sessionId).getOrElse(""),
      duplicatedBookingId = duplicateBooking.bookingId,
      duplicatedStorefrontId = duplicateBooking.storeFrontId,
      duplicatedWorkflowPhaseId = duplicateBooking.workflowPhaseId,
      source = source.toString
    )
  }
}

class DuplicateCheckServiceV2 @Inject() (
    vehicleBookingRepository: VehicleBookingRepository,
    duplicateFlightBookingRepository: DuplicateFlightBookingRepository,
    activityBookingRepository: ActivityBookingRepository,
    override val hadoopMessagingService: HadoopMessagingService,
    @Named("DuplicateCheckServiceV1") delegate: DuplicateCheckService
) extends DuplicateCheckService
    with DuplicateBookingReporter {
  override def findPropertyDuplicates(
      propertyBookingDuplicationCriteria: Option[PropertyBookingDuplicationCriteria],
      featureAware: Option[FeatureAware],
      request: CreateBookingRequest
  )(implicit
      measurementCxt: MeasurementsContext,
      requestContext: RequestContext
  ): Future[(Seq[DuplicateBooking], DatabaseEnum)] = Future.failed(
    new UnsupportedOperationException(
      s"property booking duplicate checks not supported in ${this.getClass.getSimpleName} for feature $featureAware"
    )
  )

  override def findFlightDuplicates(
      flight: FlightInfo,
      primaryGuest: FlightPax,
      storefrontId: Int,
      checkInLocalDb: Boolean
  )(implicit
      executionContext: ExecutionContext
  ): Future[Seq[DuplicateBooking]] =
    flight.slices.headOption
      .flatMap(_.segments.headOption)
      .map { segment =>
        val origin        = segment.origin
        val destination   = segment.destination
        val flightNumber  = segment.flightNumber
        val departureDate = segment.departure.toLocalDate
        duplicateFlightBookingRepository.checkFlightDuplicateV2(
          origin,
          destination,
          flightNumber,
          departureDate,
          primaryGuest.piiHash,
          checkInLocalDb
        )
      }
      .getOrElse(Future.failed(new FlightInfoNotFound()))

  override def findVehicleDuplicates(
      identifier: String,
      pickupDatetime: DateTime,
      dropOffDatetime: DateTime,
      pickupLocationCode: String,
      dropOffLocationCode: String
  )(implicit executionContext: ExecutionContext): Future[Seq[DuplicateBooking]] =
    vehicleBookingRepository
      .checkVehicleDuplicateV2(identifier, pickupDatetime, dropOffDatetime, pickupLocationCode, dropOffLocationCode)

  override def findActivityDuplicates(
      productId: Int,
      bookingStartDate: DateTime,
      offerId: Int,
      offerStartTime: Option[String],
      offerEndTime: Option[String]
  )(implicit executionContext: ExecutionContext): Future[Seq[DuplicateBooking]] =
    activityBookingRepository.checkDuplicateActivityBookingV2(
      productId,
      bookingStartDate,
      offerId,
      offerStartTime,
      offerEndTime
    )

  override def findAllPaxMisMatchBookings(
      activityDuplicatedBooking: Seq[DuplicateBooking],
      paxRequest: Seq[ActivityPax]
  )(implicit requestContext: RequestContext): Future[Seq[Long]] =
    delegate.findAllPaxMisMatchBookings(activityDuplicatedBooking, paxRequest)
}
