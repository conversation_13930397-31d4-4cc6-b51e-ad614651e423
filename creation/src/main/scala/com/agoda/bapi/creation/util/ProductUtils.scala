package com.agoda.bapi.creation.util

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.{ActivitiesItem, CarItem, CreateBookingRequest, CreateBookingRequestV2, CreditCard, FlightItem, PartnerLoyaltyPoint, PropertyItem}
import com.agoda.bapi.common.model.ChargeOption.ChargeOption
import com.agoda.bapi.common.model.activity.ActivityBookingToken
import com.agoda.bapi.common.model.{ChargeOption, addOn}
import com.agoda.bapi.common.model.addOn.cegFastTrack.AddOnBookingToken
import com.agoda.bapi.common.model.car.CarBookingToken
import com.agoda.bapi.common.model.creation.BAPIBooking
import com.agoda.bapi.common.model.flight.FlightBookingToken
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.tripProtection.TripProtectionToken
import com.agoda.bapi.common.token._
import com.agoda.bapi.common.token.common.ProductTokenKey
import com.agoda.bapi.common.util.TokenDeserializers.{ActivityBookingModelDeSerializer, AddOnBookingModelDeserializer, CarBookingModelDeserializer, FlightBookingModelDeserializer, GenericAddOnBookingModelDeserializer, ProtectionBookingModelDeserializer}
import com.agoda.bapi.common.util.{BookingTokenHelper, CartUtils, DecodeBookingTokenFailed, ItineraryContextUtils}
import com.agoda.bapi.creation.model._
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.bapi.creation.service.HadoopMessagingService
import com.agoda.bapi.creation.service.stage.presave.ProtectionAncillaryModel

import javax.inject.{Inject, Singleton}
import scala.util.control.Exception.allCatch
import scala.util.{Failure, Success, Try}

trait ProductUtils {

  def createRequestWithProducts(
      createBookingRequest: CreateBookingRequest,
      requestContext: RequestContext,
      requestV2: CreateBookingRequestV2,
      attemptedDecryptedCreationToken: Try[MultiProductCreationBookingToken]
  ): Try[RequestWithProducts]

}

@Singleton
class ProductUtilsImpl @Inject() (
    encryptedHelper: BookingTokenEncryptionHelper,
    val hadoopMessagingService: HadoopMessagingService
) extends ProductUtils
    with ToolSet {

  private[util] def transformPropertyBooking(
      requestContext: RequestContext,
      propertyItems: Seq[PropertyItem],
      propertyTokens: Map[ProductTokenKey, BAPIBooking],
      requestV2: CreateBookingRequestV2
  ): Seq[RoomInfo] =
    propertyItems
      .flatMap { room =>
        for {
          tokenKey      <- room.productTokenKey
          bapiBooking   <- propertyTokens.get(tokenKey)
          newBapiBooking = updateBapiBooking(bapiBooking, requestV2)
          roomUid <- newBapiBooking.booking.flatMap(
                       _.booking.headOption
                         .flatMap(_.hotel.headOption.flatMap(_.room.headOption.map(_.uid)))
                     )
          campaignInfo     = newBapiBooking.campaignInfo
          hotelFundingText = campaignInfo.flatMap(_.hotelFundingText)
          noOfGuests       = room.guests.getOrElse(Seq.empty).size
          localVoucher     = campaignInfo.flatMap(_.localVoucher)
          localVoucherText = GttUtils.buildLocalVoucherText(localVoucher, noOfGuests)
          specialRequests =
            room.specialRequestV2
              .map(x => x.toSpecialRequestString(hotelFundingText, localVoucherText))
              .orElse {
                if (requestContext.featureAware.exists(_.enableChangeHotelFundedTextFallback)) {
                  buildSpecialRequestFallbackText(hotelFundingText, room.specialRequest, localVoucherText)
                } else {
                  getSpecialRequestFallbackText(room.specialRequest, localVoucherText)
                }
              }
          roomIdentifier = newBapiBooking.masterRooms.flatMap(_.childrenRooms).headOption.flatMap(_.roomIdentifiers)
        } yield RoomInfo(
          roomUid,
          newBapiBooking,
          specialRequests,
          room.roomTypeName,
          room.greetingMessage,
          room.specialRequestV2,
          roomIdentifier
        )
      }

  // using the head option since hacker fare only have one item but token is changed from Map[K, FlightBookingToken]
  // to Map[K, Seq[FlightBookingToken]]
  private[util] def transformFlightBooking(
      flightItems: Seq[FlightItem],
      flightTokens: Map[ProductTokenKey, Seq[FlightBookingToken]]
  ): Seq[FlightBookingToken] =
    flightItems.flatMap { flight =>
      flight.productTokenKey.flatMap(flightTokens.get)
    }.flatten

  private def transformCarBooking(
      carItems: Seq[CarItem],
      carBookings: Map[ProductTokenKey, CarBookingToken]
  ): Seq[CarBookingToken] = {
    carItems.flatMap(car => car.productTokenKey.flatMap(carBookings.get))
  }

  def transformProtectionBooking(protectionBooking: TripProtectionModel): Seq[ProtectionAncillaryModel] = {
    protectionBooking
      .map(booking => ProtectionAncillaryModel(token = booking._2))
      .toSeq
  }

  def transformCegFastTrackBooking(
      addOnBookingModel: AddOnBookingModel
  ): Seq[AddOnBookingToken] = {
    addOnBookingModel.values.toSeq
  }

  def transformAddOnBooking(
      addOnBookingModel: GenericAddOnBookingModel
  ): Seq[addOn.AddOnBookingToken] = {
    addOnBookingModel.values.toSeq
  }

  private def transformActivitiesBooking(
      activitiesItems: Seq[ActivitiesItem],
      activitiesBooking: Map[ProductTokenKey, ActivityBookingToken]
  ): Seq[ActivityBookingToken] = {
    activitiesItems.flatMap(activity => activity.productTokenKey.flatMap(activitiesBooking.get))
  }

  override def createRequestWithProducts(
      createBookingRequest: CreateBookingRequest,
      requestContext: RequestContext,
      requestV2: CreateBookingRequestV2,
      attemptedDecryptedCreationToken: Try[MultiProductCreationBookingToken]
  ): Try[RequestWithProducts] = {

    val products        = createBookingRequest.products
    val hotelsItems     = products.propertyItems.getOrElse(Seq.empty)
    val flightItems     = products.flightItems.getOrElse(Seq.empty)
    val carItems        = products.carItems.getOrElse(Seq.empty)
    val activitiesItems = products.activitiesItems.getOrElse(Seq.empty)

    (for {
      creationObj        <- attemptedDecryptedCreationToken
      propertyBookings   <- BookingTokenHelper.extractPropertyCreationObjects(creationObj)
      flightBookings     <- BookingTokenHelper.extractCreationObjects[Seq[FlightBookingToken]](creationObj.flights)
      protectionBooking  <- BookingTokenHelper.extractCreationObjects[TripProtectionToken](creationObj.tripProtections)
      carBookings        <- BookingTokenHelper.extractCreationObjects[CarBookingToken](creationObj.cars)
      activitiesBookings <- BookingTokenHelper.extractCreationObjects[ActivityBookingToken](creationObj.activities)
      cegFastTrackBooking <-
        BookingTokenHelper.extractCreationObjects[AddOnBookingToken](creationObj.cegFastTracks)
      addOnBookings <- BookingTokenHelper.extractCreationObjects[addOn.AddOnBookingToken](creationObj.addOns)
    } yield {
      val rooms =
        transformPropertyBooking(requestContext, hotelsItems, propertyBookings, requestV2)
      val flights       = transformFlightBooking(flightItems, flightBookings)
      val cars          = transformCarBooking(carItems, carBookings)
      val protections   = transformProtectionBooking(protectionBooking)
      val activities    = transformActivitiesBooking(activitiesItems, activitiesBookings)
      val cegFastTracks = transformCegFastTrackBooking(cegFastTrackBooking)
      val addOns        = transformAddOnBooking(addOnBookings)

      val enrichedCreditCard = enrichCreditCardInfo(createBookingRequest, creationObj)
      val enrichRequestWithCreditCardAndProducts = createBookingRequest.copy(
        payment = createBookingRequest.payment.copy(
          amount = creationObj.payment,
          creditCard = enrichedCreditCard,
          requiredFieldMetadata = creationObj.requiredFieldMetadata
        ),
        products = createBookingRequest.products.copy(
          totalPriceDisplay = creationObj.priceBreakdown,
          priceDisplayVersion = creationObj.priceDisplayVersion
        )
      )
      val enrichRequestWithInstallmentInfo = enrichRequestWithCreditCardAndProducts.copy(
        payment = enrichRequestWithCreditCardAndProducts.payment.copy(
          creditCard = enrichCreditCardInstallmentInfo(
            enrichRequestWithCreditCardAndProducts.payment.creditCard,
            creationObj
          )
        )
      )

      RequestWithProducts(
        request = enrichRequestWithInstallmentInfo,
        requestContext = requestContext,
        rooms = rooms.map(info => (info.roomUid, info)).toMap,
        flights = flights,
        car = cars.headOption,
        protections = protections,
        bookingFlow = creationObj.bookingFlowType,
        products = MultiProduct(
          properties = rooms,
          flights = flights,
          vehicles = cars,
          cegFastTracks = cegFastTracks,
          addOns = addOns,
          protections = protections,
          commonPayment = creationObj.commonPayment,
          activities = activities,
          isBookingFromCart = creationObj.isBookingFromCart,
          partnerExternalInfo = creationObj.partnerExternalInfo,
          isPartialSuccessAllowed = creationObj.isPartialSuccessAllowed,
          userTaxCountryId = creationObj.userTaxCountryId,
          userTaxCountry = creationObj.userTaxCountry,
          aabInfo = creationObj.aabInfo,
          rebookAndCancelData = creationObj.rebookAndCancelData
        ),
        cartItineraryInfo = CartUtils.getCartItineraryInfo(creationObj.cartContext),
        requestV2 = Some(requestV2),
        itineraryContext = ItineraryContextUtils.fromCreationToken(creationObj)
      )
    }) match {
      case Success(result) => Success(result)
      case Failure(exception) =>
        Failure(new DecodeBookingTokenFailed(exception))
    }
  }

  private[util] def enrichCreditCardInstallmentInfo(
      creditCard: Option[CreditCard],
      creationObj: MultiProductCreationBookingToken
  ): Option[CreditCard] = {
    (creditCard, creationObj.installmentPlanCode, creditCard.flatMap(_.installmentPlanCode)) match {
      case (Some(cCard), Some(creationObjInstallmentPlanCode), Some(ccInstallmentPlanCode))
          if creationObjInstallmentPlanCode == ccInstallmentPlanCode =>
        Some(
          cCard.copy(
            installmentPlanToken = creationObj.installmentPlanToken,
            installmentPlanId = allCatch.opt(creationObjInstallmentPlanCode.toInt)
          )
        )
      case (Some(cCard), _, _) =>
        Some(
          cCard.copy(
            installmentPlanToken = None,
            installmentPlanCode = None
          )
        )
      case _ => None
    }
  }

  private[util] def updateBapiBooking(bapiBooking: BAPIBooking, requestV2: CreateBookingRequestV2): BAPIBooking = {
    val bapiBookingRoom =
      bapiBooking.booking.flatMap(
        _.booking.headOption
          .flatMap(_.hotel.headOption.flatMap(_.room.headOption))
      )

    val newPartnerLoyaltyPoint = getPartnerLoyaltyPoint(bapiBookingRoom.flatMap(_.partnerLoyaltyPoint), requestV2)

    val updatedRoomWithPartnerLoyaltyPoint =
      bapiBookingRoom.map(_.withPartnerLoyaltyPoint(newPartnerLoyaltyPoint))

    val externalLoyaltyDisplayTierId =
      bapiBooking.masterRooms.headOption.flatMap(_.childrenRooms.headOption.flatMap(_.externalLoyaltyDisplayTierId))
    val updatedRoomWithExternalLoyaltyDisplayTierId =
      updatedRoomWithPartnerLoyaltyPoint.map(_.withExternalLoyaltyDisplayTierId(externalLoyaltyDisplayTierId))

    val updatedHotel = bapiBooking.booking.flatMap(
      _.booking.headOption
        .flatMap(_.hotel.headOption.map(_.withRoom(newRoom = updatedRoomWithExternalLoyaltyDisplayTierId.toList)))
    )

    val updatedEbeBooking =
      bapiBooking.booking.flatMap(
        _.booking.headOption
          .map(_.withEbeHotel(newHotel = updatedHotel.toList))
      )

    val updatedBooking = bapiBooking.booking.map(_.withEbeBooking(newEbeBooking = updatedEbeBooking.toList))

    bapiBooking.withBooking(newBooking = updatedBooking)
  }

  private[util] def getPartnerLoyaltyPoint(
      roomPartnerLoyaltyPoint: Option[PartnerLoyaltyPoint],
      requestV2: CreateBookingRequestV2
  ): Option[PartnerLoyaltyPoint] = {

    val requestPartnerLoyaltyPoint =
      requestV2.products.propertyItems.headOption.flatMap(_.partnerLoyalty)

    (requestPartnerLoyaltyPoint, roomPartnerLoyaltyPoint) match {
      case (Some(requestPartnerPoint), Some(roomPartnerPoint))
          if requestPartnerPoint.programId == roomPartnerPoint.programId =>
        Some(roomPartnerPoint.copy(membershipId = Some(requestPartnerPoint.membershipId)))
      case _ =>
        None
    }
  }

  private[util] def getSpecialRequestFallbackText(
      specialRequest: Option[String],
      localVoucherText: Option[String]
  ): Option[String] = {
    specialRequest match {
      case Some(value)                        => Some(Seq(value, localVoucherText.getOrElse("")).mkString(","))
      case None if localVoucherText.isDefined => localVoucherText
      case _                                  => specialRequest
    }
  }

  private[util] def buildSpecialRequestFallbackText(
      hotelFundingText: Option[String],
      specialRequest: Option[String],
      localVoucherText: Option[String]
  ): Option[String] = {
    Option(
      Seq(
        specialRequest,
        hotelFundingText,
        localVoucherText
      ).collect { case Some(value) if value.nonEmpty => value }
        .mkString(",")
    ).filter(_.nonEmpty)
  }

  private def enrichCreditCardFromPaymentRequestInfo(
      paymentRequestInfo: Option[PaymentRequestInfo],
      request: Option[CreditCard]
  ): Option[CreditCard] =
    (for {
      paymentRequestInfo   <- paymentRequestInfo
      selectedChargeOption <- paymentRequestInfo.selectedChargeOption
      fullyAuthDate        <- paymentRequestInfo.fullyAuthDate
      fullyChargeDate      <- paymentRequestInfo.fullyChargeDate
      request              <- request
    } yield request.copy(
      chargeOption = selectedChargeOption,
      fullyAuthDate = fullyAuthDate,
      fullyChargeDate = fullyChargeDate
    )).orElse(request)

  private[util] def enrichCreditCardInfo(
      createBookingRequest: CreateBookingRequest,
      creationObj: MultiProductCreationBookingToken
  ): Option[CreditCard] = {
    val issueBankCountryId =
      creationObj.creditCardInfo.map(_.issueBankCountryId).getOrElse(0)

    val selectedChargeOption = defaultChargeOption(
      creationObj.paymentRequestInfo
        .flatMap(_.selectedChargeOption)
        .getOrElse(ChargeOption.None)
    )

    val isSaveCCOF = createBookingRequest.payment.creditCard.exists(_.isSaveCCOF) &&
      (creationObj.creditCardInfo.flatMap(_.isSaveCcofEligible) match {
        case Some(false) =>
          logger.error(s"save credit card attempt for non eligible card. UserId: ${createBookingRequest.userId}")
          false
        case _ => true
      })

    val updatedCreateBookingRequest = createBookingRequest.payment.creditCard
      .map(
        _.copy(
          issueBankCountryId = issueBankCountryId,
          chargeOption = selectedChargeOption,
          isNoCvc = creationObj.creditCardInfo.flatMap(_.isNoCvc),
          isBankNameRequired = creationObj.creditCardInfo.flatMap(_.isBankNameRequired),
          isSaveCCOF = isSaveCCOF
        )
      )

    creationObj.bookingFlowType match {
      case BookingFlow.SingleProperty | BookingFlow.Cart =>
        enrichCreditCardFromPaymentRequestInfo(creationObj.paymentRequestInfo, updatedCreateBookingRequest)
      case _ => updatedCreateBookingRequest
    }
  }

  private def defaultChargeOption(
      chargeOption: ChargeOption
  ): ChargeOption = {
    if (chargeOption == ChargeOption.None)
      ChargeOption.PayNow
    else
      chargeOption
  }

}
