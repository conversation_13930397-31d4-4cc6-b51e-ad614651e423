package com.agoda.bapi.creation.mapper.ebe

import com.agoda.bapi.agent.common.schema.BookingActionMessageTopic.{BAM_Topic_PreAuth3ds2Required, BAM_Topic_PreAuth3dsRequired, BAM_Topic_Redirect}
import com.agoda.bapi.common.ToolSet
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.common.Payment3DSRequest
import com.agoda.bapi.common.message.creation.{BookingPayment, CreateBookingRequest, Customer}
import com.agoda.bapi.common.model.creation.AccountingEntity
import com.agoda.bapi.common.model.payment.{GatewayReferenceRedirect, SupplierPaymentMethod}
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.product.BookingFlow.BookingFlow
import com.agoda.bapi.common.model.{ExperimentN<PERSON>s, ItineraryId, PaymentCategory, PaymentMethodFromDB, StatusToken, User<PERSON>ontex<PERSON>, WhiteLabelInfo}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.mapper.ebe.BookingActionStateMapper._
import com.agoda.bapi.creation.mapper.ebe.MPMasterMapperConstants.{PlaceHolderItineraryId, PlaceHolderStatusToken, formatStringWithParameter}
import com.agoda.bapi.creation.model.PointsType
import com.agoda.bapi.creation.model.db._
import com.agoda.bapi.creation.model.multi.MultiProductsRequest
import com.agoda.bapi.creation.util.BookingActionStateHelper.getAllowRetryPayment
import com.agoda.bapi.creation.util.{ActivityCartBFUtils, CustomerApiTokenUtils, InstantBookUtils}
import com.agoda.finance.tax.common.Types.SupplierId
import com.agoda.mpb.common.PaymentTokenType
import com.agoda.mpb.common.models.state.{CommonPaymentInfo, PaymentRedirectInfo, PaymentToken, PointsAttributes}
import com.agoda.mpbe.state.common.enums.PaymentMethod.{PaymentMethod => MPBPaymentMethod}

import scala.util.Try

trait BookingActionStateMapper extends ToolSet {

  protected[ebe] def mapCreateBookingRequestToActionState(
      request: CreateBookingRequest,
      requestContext: RequestContext,
      isBookingFromCart: Option[Boolean] = None,
      isPartialSuccessAllowed: Option[Boolean] = None,
      allowRetryPayment: Option[Boolean] = None
  ): CreationRequest = {

    val headersOpt = if (requestContext.agHeaders.isEmpty) None else Some(requestContext.agHeaders)

    CreationRequest(
      affiliateModel = request.affiliateModel,
      userId = request.userId,
      storefrontId = request.storefrontId,
      siteId = request.siteId,
      affiliateId = request.userContext
        .flatMap(_.experimentData.flatMap(_.aId))
        .flatMap(s => Try(s.toInt).toOption),
      referralUrl = request.referralUrl,
      platformId = request.platformId,
      trackingCookieId = request.trackingCookieId,
      sessionId = request.sessionId,
      analyticsSessionId = request.analyticsSessionId,
      userAgent = request.userAgent,
      clientIp = request.clientIp,
      userContext = request.userContext,
      isNewsletterOptIn = request.customer.newsletter,
      tmSessionId = request.tmSessionId,
      acceptHeader = request.acceptHeader,
      fraudInfo = Some(
        FraudInfo(
          deviceFingerprintData = request.fraudInfo.deviceFingerprintData,
          tmxSessionId = request.fraudInfo.tmxSessionId,
          forterToken = request.fraudInfo.forterToken
        )
      ),
      requestedTime = Some(requestContext.requestedTime),
      instantBookSetting = InstantBookUtils.toInstantBookSetting(request, requestContext),
      bookingSessionId = requestContext.bookingCreationContext.flatMap(_.bookingSessionId),
      isCartFlow = isBookingFromCart,
      isPartialSuccessAllowed = isPartialSuccessAllowed,
      browserUserAgent = requestContext.bookingCreationContext.flatMap(_.browserUserAgent),
      appUserAgent = requestContext.bookingCreationContext.flatMap(_.appUserAgent),
      headers = headersOpt,
      allowRetryPayment = allowRetryPayment,
      setupCorrelationId = requestContext.setupCorrelationId
    )
  }

  protected[ebe] def mapCreateBookingRequestToActionState(
      multiProductRequest: MultiProductsRequest,
      customerApiTokenUtils: CustomerApiTokenUtils
  ): CreationRequest = {
    implicit val featureAware = multiProductRequest.requestContext.featureAware
    val forceExperiments      = getForceExperiments(multiProductRequest, customerApiTokenUtils)
    val request               = addForceExperimentInCreateBookingRequest(multiProductRequest, forceExperiments)
    val requestContext        = multiProductRequest.requestContext
    val requestWithLocale =
      mapUserContextLocaleFromRequestContext(request, requestContext)
    val allowRetryPayment =
      getAllowRetryPayment(multiProductRequest.hasFlightsMainProductOnly, multiProductRequest.requestContext)
    val creationRequest = mapCreateBookingRequestToActionState(
      request = requestWithLocale,
      requestContext = requestContext,
      isBookingFromCart = multiProductRequest.isBookingFromCart,
      isPartialSuccessAllowed = multiProductRequest.isPartialSuccessAllowed,
      allowRetryPayment = allowRetryPayment
    )
    mapIsAgentAssistedBookingToCreationRequest(
      multiProductRequest,
      creationRequest,
      customerApiTokenUtils
    )
  }

  protected[ebe] def mapAdditionalBookingInfo(
      affiliateAdditionalSupplierInfo: Map[String, String]
  )(implicit context: RequestContext): Option[AdditionalBookingInfo] = {
    affiliateAdditionalSupplierInfo match {
      case info if info.nonEmpty && context.featureAware.exists(_.enablePassingBookingSource) =>
        Some(AdditionalBookingInfo(affiliateAdditionalSupplierInfo = info))
      case _ => None
    }
  }

  private def mapIsAgentAssistedBookingToCreationRequest(
      multiProductsRequest: MultiProductsRequest,
      creationRequest: CreationRequest,
      customerApiTokenUtils: CustomerApiTokenUtils
  ): CreationRequest = {
    val capiToken = multiProductsRequest.requestContext.userContext
      .flatMap(_.capiToken)
      .orElse(multiProductsRequest.request.userContext.flatMap(_.capiToken))
    val isAgentAssistBooking = capiToken.map(customerApiTokenUtils.isAgentAssistantBooking)
    creationRequest.copy(isAgentAssistedBooking = isAgentAssistBooking)
  }

  /**
    * IMPORTANT: Always use forceSingleExperiment() or forceExperiments() to get forced exp for both A and B variant
    * This is to avoid inconsistent variant across same itinerary in case experiment is being turn on while itinerary is
    * still processing in Workflow-Agents.
    */
  private def getForceExperiments(
      multiProductRequest: MultiProductsRequest,
      customerApiTokenUtils: CustomerApiTokenUtils
  ): Map[String, String] = {
    flightPayoutExperiments(multiProductRequest) ++
      getICTTimeZoneExperiment(multiProductRequest) ++
      getPreProcessPaymentSettlement(multiProductRequest) ++
      getUsePayoutUuidFromPayoutApiExperiment(multiProductRequest) ++
      getMigrateCancelFromUpcToPayoutApiExperiment(multiProductRequest) ++
      getEncryptCreditCardDetailsFromPayoutApiExperiment(multiProductRequest) ++
      getMigrateFetchPayoutMethodToPayoutApiExperiment(multiProductRequest) ++
      getMigrateRetrieveFromUpcToPayoutApiExperiment(multiProductRequest) ++
      getReduceAllotmentAlertRateForOfflinePaymentExperiment(multiProductRequest) ++
      getMovePaymentLogicFromBookingSystemsToPayoutApiExperiment(multiProductRequest) ++
      forceDelayAllotmentExperiments(multiProductRequest) ++
      forceProvisioningDistributorForExistingScalaAgents(multiProductRequest) ++
      vehiclePayoutMigrationExperiments(multiProductRequest) ++
      propertyPayoutMigrationExperiments(multiProductRequest) ++
      propertyProvisioningMigrationExperiments(multiProductRequest) ++
      isEnableLocalProvisioningSkipForAAB(multiProductRequest, customerApiTokenUtils) ++
      isForcedActivityMigration(multiProductRequest) ++
      isTechnicalFailureNoManualBoxActionEnabled(multiProductRequest) ++
      isUpdateBookingRejectAgentToBeMonitoringAgent(multiProductRequest) ++
      isEnableVehicleCancelProvisioning(multiProductRequest) ++
      forceSaveB2bInvoiceAtPreAuthInsteadOfSettlementExperiment(multiProductRequest) ++
      forceEnableJava11HttpBackendForNusaFlightSupplier(multiProductRequest) ++
      propertyInstantTransferExperiments(multiProductRequest) ++
      isSelectionOfBASForExternalCampaignServiceBasedOnBIDEnabledForced(multiProductRequest) ++
      isEnableCCTokenInBookingActionState(multiProductRequest) ++
      isEnableFixHackerFareBookingStuckWithFailedProtectionBooking(multiProductRequest) ++
      isFixPromotionDiscountsInfo(multiProductRequest) ++
      forceFlightMultiProductPMCExperiment(multiProductRequest) ++
      forceEnableMultiCityFlightsExperiment(multiProductRequest) ++
      forceFlightHWExperiment(multiProductRequest) ++
      forceEnableThaiVietJetFlightSupplierMigration(multiProductRequest) ++
      forceSwapFinNodeWithEmailNodeForBkgRejectionExperiment(multiProductRequest) ++
      forceFlightReverseBreakdownExperiment(multiProductRequest) ++
      forceEnableRurubuDirectSupply(multiProductRequest) ++
      forceEnableAddSearchClusterUserAgentModel(multiProductRequest) ++
      forceEnableLionAirMultiSession(multiProductRequest)
  }

  private def isSelectionOfBASForExternalCampaignServiceBasedOnBIDEnabledForced(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] =
    forceSingleExperiment(
      multiProductsRequest,
      featureAwareCondition = _.exists(_.isSelectionOfBASForExternalCampaignServiceBasedOnBIDEnabled),
      experimentName = ExperimentNames.isSelectionOfBASForExternalCampaignServiceBasedOnBIDEnabled
    )

  private def isForcedActivityMigration(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {

    forceSingleExperiment(
      multiProductsRequest,
      featureAwareCondition = _ => true,
      experimentName = ExperimentNames.enableActivityCartBFMigration,
      condition = _.request.products.activitiesItems.exists(_.nonEmpty) && ActivityCartBFUtils
        .getIsActivityCartBFMultiRequest(multiProductsRequest.request.platformId)
    )

  }

  private def forceProvisioningDistributorForExistingScalaAgents(
      multiProductRequest: MultiProductsRequest
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductRequest,
      featureAwareCondition = _.exists(_.enableProvisioningDistributor),
      experimentName = ExperimentNames.enableProvisioningDistributor,
      condition = mpr =>
        mpr.request.products.carItems.exists(_.nonEmpty) || mpr.request.products.activitiesItems.exists(_.nonEmpty)
    )
  }

  private def vehiclePayoutMigrationExperiments(multiProductsRequest: MultiProductsRequest): Map[String, String] =
    forceSingleExperiment(
      multiProductsRequest,
      _.exists(_.enableVehiclePayoutAgent),
      ExperimentNames.enableVehiclePayoutAgent
    )

  private def propertyInstantTransferExperiments(multiProductsRequest: MultiProductsRequest): Map[String, String] = {
    val enablePropertyInstantTransferExperiment = forceSingleExperiment(
      multiProductRequest = multiProductsRequest,
      featureAwareCondition =
        !isRedirectPayment(multiProductsRequest) && _.exists(_.enablePropertyInstantTransferForNonRedirectPayment),
      experimentName = ExperimentNames.enablePropertyInstantTransferForNonRedirectPayment,
      condition = _.request.products.propertyItems.exists(_.nonEmpty)
    )

    val enablePropertyInstantTransferForRedirectPaymentExperiment = forceSingleExperiment(
      multiProductRequest = multiProductsRequest,
      featureAwareCondition =
        isRedirectPayment(multiProductsRequest) && _.exists(_.enablePropertyInstantTransferForRedirectPayment),
      experimentName = ExperimentNames.enablePropertyInstantTransferForRedirectPayment,
      condition = _.request.products.propertyItems.exists(_.nonEmpty)
    )

    enablePropertyInstantTransferExperiment ++ enablePropertyInstantTransferForRedirectPaymentExperiment
  }

  private def isRedirectPayment(multiProductsRequest: MultiProductsRequest): Boolean = {
    multiProductsRequest.request.payment.redirect.nonEmpty
  }

  private def propertyPayoutMigrationExperiments(multiProductsRequest: MultiProductsRequest): Map[String, String] = {
    val enablePropertyPayoutAgentExperiments = forceSingleExperiment(
      multiProductsRequest,
      featureAwareCondition = _.exists(_.enablePropertyPayoutAgent),
      experimentName = ExperimentNames.enablePropertyPayoutAgent,
      condition = _.request.products.propertyItems.exists(_.nonEmpty)
    )

    if (
      enablePropertyPayoutAgentExperiments.get(ExperimentNames.enablePropertyPayoutAgent).contains(experimentVariantA)
    ) {
      forceSingleExperiment(
        multiProductsRequest,
        featureAwareCondition = _.exists(_.enablePropertyPayoutAccuracyChecker),
        experimentName = ExperimentNames.enablePropertyPayoutAccuracyChecker,
        condition = isSingleHotelOnlyRequestPredicate
      ) ++ enablePropertyPayoutAgentExperiments
    } else
      enablePropertyPayoutAgentExperiments ++ Map(
        ExperimentNames.enablePropertyPayoutAccuracyChecker -> experimentVariantA
      )
  }

  private def flightPayoutExperiments(multiProductsRequest: MultiProductsRequest): Map[String, String] = {
    val isFlightPayoutEnable = (req: MultiProductsRequest) => {
      val isRequire =
        req.flights
          .flatMap(_.info.info.flatMap(_.supplierPaymentMethod))
          .exists(_ != SupplierPaymentMethod.None)
      isRequire || req.requestContext.featureAware.exists(_.enableFlightPayoutAgent)
    }
    forceSingleExperiment(
      multiProductsRequest,
      _ => true,
      ExperimentNames.enableFlightPayoutAgent,
      isFlightPayoutEnable
    )
  }

  /**
    * @return
    *   a [[Boolean]] flag suggesting if the request contains a <b>single</b> hotel related product
    */
  private def isSingleHotelOnlyRequestPredicate: MultiProductsRequest => Boolean =
    multiProductsRequest =>
      multiProductsRequest.request.products.protectionItems.forall(_.isEmpty) &&
        multiProductsRequest.request.products.activitiesItems.forall(_.isEmpty) &&
        multiProductsRequest.request.products.flightItems.forall(_.isEmpty) &&
        multiProductsRequest.request.products.carItems.forall(_.isEmpty) &&
        multiProductsRequest.request.products.propertyItems.exists(_.size == 1)

  private def propertyProvisioningMigrationExperiments(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    val enablePropertyProvisioningAgentExperiments = forceSingleExperiment(
      multiProductsRequest,
      featureAwareCondition = _.exists(_.enablePropertyProvisioningAgent),
      experimentName = ExperimentNames.enablePropertyProvisioningAgent,
      condition = _.request.products.propertyItems.nonEmpty
    )

    if (
      enablePropertyProvisioningAgentExperiments
        .get(ExperimentNames.enablePropertyProvisioningAgent)
        .contains(experimentVariantA)
    ) {
      forceSingleExperiment(
        multiProductsRequest,
        featureAwareCondition = _.exists(_.enablePropertyProvisioningAccuracyChecker),
        experimentName = ExperimentNames.enablePropertyProvisioningAccuracyChecker,
        condition = _.request.products.propertyItems.nonEmpty
      ) ++ enablePropertyProvisioningAgentExperiments
    } else
      enablePropertyProvisioningAgentExperiments ++ Map(
        ExperimentNames.enablePropertyProvisioningAccuracyChecker -> experimentVariantA
      )
  }

  private def forceSingleExperiment(
      multiProductRequest: MultiProductsRequest,
      featureAwareCondition: Option[FeatureAware] => Boolean,
      experimentName: String,
      condition: MultiProductsRequest => Boolean = _ => true
  ): Map[String, String] = {
    forceExperiments(multiProductRequest, featureAwareCondition, Seq(experimentName), condition)
  }

  private def forceExperiments(
      multiProductRequest: MultiProductsRequest,
      featureAwareCondition: Option[FeatureAware] => Boolean,
      experimentNames: Seq[String],
      condition: MultiProductsRequest => Boolean = _ => true
  ): Map[String, String] = {
    if (
      condition(multiProductRequest) && featureAwareCondition(
        multiProductRequest.requestContext.featureAware
      )
    )
      experimentNames.map(exp => exp -> experimentVariantB).toMap
    else experimentNames.map(exp => exp -> experimentVariantA).toMap
  }

  private def forceDelayAllotmentExperiments(multiProductsRequest: MultiProductsRequest): Map[String, String] =
    forceExperiments(
      multiProductsRequest,
      featureAwareCondition = _ => true,
      experimentNames = Seq(ExperimentNames.enableDelayAllotmentForProperty),
      condition = _.request.addDelay
    )

  private def getReduceAllotmentAlertRateForOfflinePaymentExperiment(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductsRequest,
      _.exists(_.isReduceAllotmentAlertRateForOfflinePaymentEnabled),
      ExperimentNames.reduceAllotmentAlertRateForOfflinePayment
    )
  }

  private def getICTTimeZoneExperiment(multiProductsRequest: MultiProductsRequest): Map[String, String] = {
    forceSingleExperiment(
      multiProductsRequest,
      _.exists(_.isTimeZoneICTEnabled),
      ExperimentNames.isTimzeZoneICTExperiment
    )
  }

  private def getPreProcessPaymentSettlement(multiProductsRequest: MultiProductsRequest): Map[String, String] = {
    forceSingleExperiment(
      multiProductsRequest,
      _.exists(_.isPreProcessPaymentSettlement),
      ExperimentNames.preProcessPaymentSettlement
    )
  }

  private[ebe] def addForceExperimentInCreateBookingRequest(
      multiProductRequest: MultiProductsRequest,
      forceExpFromMultiProductRequest: Map[String, String]
  )(implicit featureAware: Option[FeatureAware]): CreateBookingRequest = {
    val request        = multiProductRequest.request
    val experimentData = request.userContext.flatMap(_.experimentData)
    val updatedExpDataFromMPRequest =
      experimentData.map(experimentData => experimentData.addForce(forceExpFromMultiProductRequest))
    val userContext =
      request.userContext.map(_.copy(experimentData = updatedExpDataFromMPRequest))
    val newCreateBookingRequest = request.copy(userContext = userContext)

    newCreateBookingRequest
  }

  private[ebe] def mapUserContextLocaleFromRequestContext(
      request: CreateBookingRequest,
      requestContext: RequestContext
  ): CreateBookingRequest = {
    val userContext = request.userContext
    request.copy(userContext = userContext.map(_.copy(locale = Some(requestContext.locale))))
  }

  private def getUsePayoutUuidFromPayoutApiExperiment(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductsRequest,
      _.exists(_.isUsePayoutUuidFromPayoutApi),
      ExperimentNames.usePayoutUuidFromPayoutApi
    )
  }

  private def getMigrateCancelFromUpcToPayoutApiExperiment(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductsRequest,
      _.exists(_.isMigrateCancelFromUpcToPayoutApi),
      ExperimentNames.migrateCancelFromUpcToPayoutApi
    )
  }

  private def getEncryptCreditCardDetailsFromPayoutApiExperiment(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductsRequest,
      _.exists(_.isEncryptCreditCardDetailsFromPayoutApi),
      ExperimentNames.encryptCreditCardDetailsFromPayoutApi
    )
  }

  private def getMigrateFetchPayoutMethodToPayoutApiExperiment(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductsRequest,
      _.exists(_.isMigrateFetchPayoutMethodToPayoutApi),
      ExperimentNames.migrateFetchPayoutMethodToPayoutApi
    )
  }

  private def getMigrateRetrieveFromUpcToPayoutApiExperiment(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductsRequest,
      _.exists(_.isMigrateRetrieveFromUpcToPayoutApi),
      ExperimentNames.migrateRetrieveFromUpcToPayoutApi
    )
  }

  private def getMovePaymentLogicFromBookingSystemsToPayoutApiExperiment(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductsRequest,
      _.exists(_.movePaymentLogicFromBookingSystemsToPayoutApi),
      ExperimentNames.movePaymentLogicFromBookingSystemsToPayoutApi
    )
  }

  private def isEnableLocalProvisioningSkipForAAB(
      multiProductsRequest: MultiProductsRequest,
      customerApiTokenUtils: CustomerApiTokenUtils
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductsRequest,
      featureAwareCondition = _ => true,
      experimentName = ExperimentNames.enableLocalProvisioningSkipForAAB,
      condition = { mpr =>
        val capiToken = mpr.requestContext.userContext
          .flatMap(_.capiToken)
          .orElse(mpr.request.userContext.flatMap(_.capiToken))
        capiToken.map(customerApiTokenUtils.isAgentAssistantBooking).getOrElse(false)
      }
    )
  }

  private def isTechnicalFailureNoManualBoxActionEnabled(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductsRequest,
      _.exists(_.isTechnicalFailureNoManualBoxActionEnabled),
      ExperimentNames.isTechnicalFailureNoManualBoxActionEnabled
    )
  }

  private def isUpdateBookingRejectAgentToBeMonitoringAgent(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductRequest = multiProductsRequest,
      featureAwareCondition = _.exists(_.isUpdateBookingRejectAgentToBeMonitoringAgent),
      experimentName = ExperimentNames.isUpdateBookingRejectAgentToBeMonitoringAgent
    )
  }

  private def isEnableVehicleCancelProvisioning(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductRequest = multiProductsRequest,
      featureAwareCondition = _.exists(_.isEnableVehicleCancelProvisioning),
      experimentName = ExperimentNames.enableVehicleCancelProvisioning
    )
  }

  private def forceSaveB2bInvoiceAtPreAuthInsteadOfSettlementExperiment(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] =
    forceSingleExperiment(
      multiProductRequest = multiProductsRequest,
      featureAwareCondition = _.exists(_.saveB2bInvoiceAtPreAuthInsteadOfSettlement),
      experimentName = ExperimentNames.saveB2bInvoiceAtPreAuthInsteadOfSettlement
    )

  private def forceEnableJava11HttpBackendForNusaFlightSupplier(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] =
    forceSingleExperiment(
      multiProductRequest = multiProductsRequest,
      featureAwareCondition = _.exists(_.EnableJava11HttpBackendForNusaFlightSupplier),
      experimentName = ExperimentNames.EnableJava11HttpBackendForNusaFlightSupplier
    )

  private def isEnableCCTokenInBookingActionState(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] =
    forceSingleExperiment(
      multiProductRequest = multiProductsRequest,
      featureAwareCondition = _.exists(_.enableCCTokenInBookingActionState),
      experimentName = ExperimentNames.enableCCTokenInBookingActionState
    )

  private def isEnableFixHackerFareBookingStuckWithFailedProtectionBooking(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] =
    forceSingleExperiment(
      multiProductRequest = multiProductsRequest,
      featureAwareCondition = _.exists(_.enableFixHackerFareBookingStuckWithFailedProtectionBooking),
      experimentName = ExperimentNames.FixHackerFareBookingStuckWithFailedProtectionBooking
    )

  private[ebe] def isFixPromotionDiscountsInfo(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    val bookingRoom = multiProductsRequest.properties
      .flatMap(_.info.bapiBooking.booking)
      .flatMap(_.booking)
      .flatMap(_.hotel)
      .flatMap(_.room)
    if (
      bookingRoom.exists(_.promotionsBreakdown.exists(_.exists(_.discountAppliedDate.nonEmpty))) || bookingRoom.exists(
        _.channelDiscountBreakdown.exists(_.exists(_.discountAppliedDate.nonEmpty))
      )
    ) {
      Map(ExperimentNames.FixPromotionDiscountsInfo -> experimentVariantB)
    } else Map(ExperimentNames.FixPromotionDiscountsInfo -> experimentVariantA)
  }

  private def forceFlightMultiProductPMCExperiment(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductRequest = multiProductsRequest,
      featureAwareCondition = _.exists(_.enableFlightsMultiProductPMCMigration),
      experimentName = ExperimentNames.enableFlightsMultiProductPMCMigration
    )
  }

  private def forceEnableMultiCityFlightsExperiment(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductRequest = multiProductsRequest,
      featureAwareCondition = _.exists(_.enableMultiCityFlights),
      experimentName = ExperimentNames.enableMultiCityFlights
    )
  }

  private def forceFlightHWExperiment(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductRequest = multiProductsRequest,
      featureAwareCondition = _.exists(_.enableFlightsHWFlow),
      experimentName = ExperimentNames.isHyperWalletExperiment
    )
  }

  private def forceEnableRurubuDirectSupply(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductsRequest,
      _.exists(_.enableRurubuDirectSupply),
      ExperimentNames.EnableRurubuDirectSupply
    )
  }

  private def forceEnableAddSearchClusterUserAgentModel(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] = {
    forceSingleExperiment(
      multiProductRequest = multiProductsRequest,
      featureAwareCondition = _.exists(_.enableAddSearchClusterUserAgentModel),
      experimentName = ExperimentNames.addSearchClusterUserAgentModel
    )
  }

  private def forceEnableThaiVietJetFlightSupplierMigration(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] =
    forceSingleExperiment(
      multiProductRequest = multiProductsRequest,
      featureAwareCondition = _.exists(_.enableThaiVietJetMigration),
      experimentName = ExperimentNames.EnableThaiVietJetMigration
    )

  private def forceSwapFinNodeWithEmailNodeForBkgRejectionExperiment(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] =
    forceSingleExperiment(
      multiProductRequest = multiProductsRequest,
      featureAwareCondition = _.exists(_.enableSwapFinNodeWithEmailNodeForBkgRejection),
      experimentName = ExperimentNames.enableSwapFinNodeWithEmailNodeForBkgRejection
    )

  private def forceFlightReverseBreakdownExperiment(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] =
    forceSingleExperiment(
      multiProductRequest = multiProductsRequest,
      featureAwareCondition = _.exists(_.enableFlightReverseBreakdown),
      experimentName = ExperimentNames.enableFlightReverseBreakdown
    )

  private def forceEnableLionAirMultiSession(
      multiProductsRequest: MultiProductsRequest
  ): Map[String, String] =
    forceSingleExperiment(
      multiProductRequest = multiProductsRequest,
      featureAwareCondition = _.exists(_.enableLionAirMultiSession),
      experimentName = ExperimentNames.enableLionAirMultiSession
    )

  // Todo:Remove with product-level payment
  @deprecated("use mapPaymentRedirectInfo, this method will be removed with product level payment integration")
  protected def mapPaymentRedirect(redirect: GatewayReferenceRedirect, statusToken: StatusToken) = {
    val paramMapping = Map(
      PlaceHolderStatusToken -> statusToken.copy(topic = Some(BAM_Topic_Redirect.value)).serialize(log, withMeasure)
    ) ++ itineraryIdParam(statusToken.itineraryId)
    val formatSuccessURL = formatStringWithParameter(redirect.returnUrl, paramMapping)
    val formatCancelURL  = formatStringWithParameter(redirect.cancelUrl, paramMapping)

    PaymentRedirect(
      successUrl = formatSuccessURL,
      cancelUrl = formatCancelURL,
      requiredPostFields = redirect.requiredPostFields
    )

  }

  private def itineraryIdParam(itineraryId: ItineraryId) =
    Map(
      PlaceHolderItineraryId -> itineraryId.toString
    )

  protected def mapPaymentToPaymentInfo(
      payment: BookingPayment,
      bookingFlow: BookingFlow,
      paymentMethodInfo: Option[PaymentMethodFromDB],
      statusToken: StatusToken,
      supplierId: Option[SupplierId] = None,
      userContext: Option[UserContext] = None
  ): PaymentInfo = {
    val itineraryPayment = payment.amount
    val (pointsType, accEntity) =
      if (bookingFlow == BookingFlow.SingleVehicle)
        (PointsType.RMMiles, AccountingEntity.carAccountingEntity(supplierId.getOrElse(-1)))
      else (PointsType.AgodaCash, AccountingEntity.getPackagingEntity)

    val cashbackPoints =
      Vector(
        Points(
          pointType = PointsType.Cashback,
          pointAttributes = PointsAttributes(
            currency = "USD",
            amount = itineraryPayment.cashbackPayment.map(_.cashbackAmountUSD).getOrElse(BigDecimal(0)),
            localCurrency =
              if (itineraryPayment.cashbackPayment.map(_.cashbackAmount).getOrElse(BigDecimal(0)) > 0)
                Option(itineraryPayment.paymentCurrency)
              else None,
            localAmount =
              if (itineraryPayment.cashbackPayment.map(_.cashbackAmount).getOrElse(BigDecimal(0)) > 0)
                itineraryPayment.cashbackPayment.map(_.cashbackAmount)
              else None
          )
        )
      )

    val points = Vector(
      Points(
        pointType = pointsType,
        pointAttributes = PointsAttributes(
          currency = "USD",
          amount = itineraryPayment.giftcardAmountUSD,
          localCurrency = if (itineraryPayment.giftcardAmount > 0) Option(itineraryPayment.paymentCurrency) else None,
          localAmount = if (itineraryPayment.giftcardAmount > 0) Option(itineraryPayment.giftcardAmount) else None
        )
      )
    ) ++ cashbackPoints

    PaymentInfo(
      method = payment.method,
      paymentCurrency = itineraryPayment.paymentCurrency,
      paymentAmount = itineraryPayment.paymentAmount,
      paymentAmountUSD = itineraryPayment.paymentAmountUSD,
      accountingEntity = Some(accEntity),
      siteExchangeRate = Some(itineraryPayment.siteExchangeRate),
      destinationCurrency = itineraryPayment.destinationCurrency,
      destinationExchangeRate = Some(itineraryPayment.destinationExchangeRate),
      rateQuoteId = Some(itineraryPayment.rateQuoteId),
      paymentOption = payment.creditCard.map(_.paymentOption),
      gateway = payment.continuation.map(_.gatewayId),
      points = points,
      isRedirect = paymentMethodInfo.map(_.redirect),
      timeoutMinutes = paymentMethodInfo.flatMap(_.timeoutMin),
      paymentRedirect = payment.redirect.map(mapPaymentRedirect(_, statusToken)),
      paymentCategoryId =
        paymentMethodInfo.map(p => PaymentCategory.getPaymentGroupCategory(PaymentCategory.get(p.paymentCategory)).id),
      gatewayId = payment.continuation.map(_.gatewayId.value),
      gatewayInfoId = payment.continuation.flatMap(_.gatewayInfoId),
      mpiId = payment.continuation.flatMap(_.mpiId)
    )
  }

  protected def getPayment3DSRequestFrom(
      bookingPayment: BookingPayment,
      statusToken: StatusToken,
      whiteLabelInfo: WhiteLabelInfo
  ): Option[Payment3DSRequest] = {
    bookingPayment.creditCard.flatMap(creditCardInfo =>
      getAdditionalPayment3DSRequestFrom(creditCardInfo.payment3DS, statusToken, whiteLabelInfo)
    )
  }

  private def getAdditionalPayment3DSRequestFrom(
      payment3DSRequestOption: Option[Payment3DSRequest],
      statusToken: StatusToken,
      whiteLabelInfo: WhiteLabelInfo
  ): Option[Payment3DSRequest] = {
    val paramMappingWith3ds2Topic: Map[String, String] = Map(
      PlaceHolderStatusToken -> statusToken
        .copy(topic = Some(BAM_Topic_PreAuth3ds2Required.value))
        .serialize(log, withMeasure)
    ) ++ itineraryIdParam(statusToken.itineraryId)
    val statusToken3dsTopic =
      whiteLabelInfo.feature.overridePlaceHolder.statusToken3dsTopic.getOrElse(BAM_Topic_PreAuth3dsRequired.value)
    val paramMappingWith3ds1Topic: Map[String, String] = Map(
      PlaceHolderStatusToken -> statusToken
        .copy(topic = Some(statusToken3dsTopic))
        .serialize(log, withMeasure)
    ) ++ itineraryIdParam(statusToken.itineraryId)
    val deviceCollectionUrl: String          = payment3DSRequestOption.flatMap(_.deviceCollectionUrl).getOrElse("")
    val returnUrl: String                    = payment3DSRequestOption.flatMap(_.returnUrl).getOrElse("")
    val bankCallback3DS1Url: String          = payment3DSRequestOption.flatMap(_.bankCallback3DS1Url).getOrElse("")
    val deviceCollectionUrlFormatted: String = formatStringWithParameter(deviceCollectionUrl, paramMappingWith3ds2Topic)
    val returnUrlFormatted: String           = formatStringWithParameter(returnUrl, paramMappingWith3ds2Topic)
    val bankCallback3DS1UrlFormatted: String = formatStringWithParameter(bankCallback3DS1Url, paramMappingWith3ds1Topic)

    payment3DSRequestOption.map { payment3dsRequest =>
      payment3dsRequest.copy(
        returnUrl = Some(returnUrlFormatted),
        deviceCollectionUrl = Some(deviceCollectionUrlFormatted),
        bankCallback3DS1Url = Some(bankCallback3DS1UrlFormatted)
      )
    }
  }

  protected def enrichCommonProductPayment(
      payment: BookingPayment,
      commonPayment: CommonPaymentInfo,
      paymentMethod: MPBPaymentMethod,
      statusToken: StatusToken,
      paymentMethodInfo: Option[PaymentMethodFromDB]
  ): CommonPaymentInfo = {
    val paymentTokenOpt = payment.continuation.flatMap(_.paymentToken)
    commonPayment.copy(
      method = paymentMethod,
      timeoutMinutes = paymentMethodInfo.flatMap(_.timeoutMin) orElse commonPayment.timeoutMinutes,
      redirectInfo = payment.redirect.map(mapPaymentRedirectInfo(_, statusToken)),
      paymentCategoryId = paymentMethodInfo.map(_.paymentCategory),
      gatewayId = payment.continuation.map(_.gatewayId.value),
      gatewayInfoId = payment.continuation.flatMap(_.gatewayInfoId),
      mpiId = payment.continuation.flatMap(_.mpiId),
      paymentToken = paymentTokenOpt.map(paymentToken =>
        PaymentToken(
          tokenType = PaymentTokenType(paymentToken.tokenType.id),
          tokenValue = paymentToken.tokenValue,
          additionalInfo = paymentToken.additionalInfo
        )
      ),
      paymentChannel = payment.paymentChannel
    )
  }

  private[ebe] def mapPaymentRedirectInfo(redirect: GatewayReferenceRedirect, statusToken: StatusToken) = {
    val paramMapping = Map(
      PlaceHolderStatusToken -> statusToken.copy(topic = Some(BAM_Topic_Redirect.value)).serialize(log, withMeasure)
    ) ++ itineraryIdParam(statusToken.itineraryId)
    val formatSuccessURL = formatStringWithParameter(redirect.returnUrl, paramMapping)
    val formatCancelURL  = formatStringWithParameter(redirect.cancelUrl, paramMapping)

    PaymentRedirectInfo(
      successUrl = formatSuccessURL,
      cancelUrl = formatCancelURL,
      requiredPostFields = redirect.requiredPostFields
    )
  }

  protected def mapNonCardInfo(bookingPayment: BookingPayment) = {
    bookingPayment.nonCard.map(nonCard => NonCardInfo(ccId = nonCard.ccId, saveAsPermanent = nonCard.isSaveCCOF))
  }
}

object BookingActionStateMapper {
  private val experimentVariantA = "A"
  private val experimentVariantB = "B"

  def mapBookingActionStateCustomer(customer: Customer): BookingActionStateCustomer = {
    BookingActionStateCustomer(memberId = customer.memberId, isUserLoggedIn = customer.isUserLoggedIn)
  }
}
