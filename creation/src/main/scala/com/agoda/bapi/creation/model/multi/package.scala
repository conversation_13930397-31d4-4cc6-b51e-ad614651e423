package com.agoda.bapi.creation.model

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation._
import com.agoda.bapi.common.model.activity.ActivityBookingToken
import com.agoda.bapi.common.model.addOn.UserTaxCountry
import com.agoda.bapi.common.model.addOn.cegFastTrack.AddOnBookingToken
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.common.model.car.CarBookingToken
import com.agoda.bapi.common.model.cart.CartItineraryInfo
import com.agoda.bapi.common.model.flight.FlightBookingToken
import com.agoda.bapi.common.model.multiproduct.MultiProductInfoDBModel
import com.agoda.bapi.common.model.payment.PaymentModel
import com.agoda.bapi.common.model.product.BookingFlow.BookingFlow
import com.agoda.bapi.common.model.product.ProductTypeEnum
import com.agoda.bapi.common.model.product.ProductTypeEnum.ProductTypeEnum
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.token.{AgentAssistedBookingInfo, PartnerExternalInfo}
import com.agoda.bapi.common.token.common.ProductTokenKey
import com.agoda.bapi.common.model.addOn
import com.agoda.bapi.common.model.flight.flightModel.ItineraryHistory
import com.agoda.bapi.common.model.rebookAndCancel.RebookAndCancelData
import com.agoda.bapi.common.ProductInfo
import com.agoda.bapi.creation.model.db.{BookingActionState, MultiProductBookingInsertionModel, PropertyBookingCreationLocal}
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.bapi.creation.service.stage.presave.ProtectionAncillaryModel
import com.agoda.mpb.common.MultiProductType.MultiProductType
import com.agoda.common.itineraryContext.ItineraryContext
import com.agoda.mpb.common.BookingType.BookingType
import com.agoda.mpb.common.models.state.CommonPaymentInfo

import java.util.UUID
import org.joda.time.DateTime

package object multi {

  type MultiProductIdentifier = Option[MultiProductType]

  type RequestId = UUID

  trait DecodedFlights {
    def flights: Seq[Product[FlightBookingToken]]
  }

  trait DecodedHotels {
    def hotels: Seq[Product[RoomInfo]]
  }

  /**
    * Duplication
    */

  final case class Product[A <: ProductInfo](
      bookingType: BookingType,
      info: A,
      multiProductType: Option[MultiProductType] = None
  ) {
    // todo; clarify new identifier; when support multi-package in one itinerary
    def getMultiProductIdentifier: MultiProductIdentifier = multiProductType
  }

  final case class ItineraryPreSaveInfo(
      itineraryId: ItineraryId,
      actionId: ActionId,
      tripStart: Option[DateTime],
      tripEnd: Option[DateTime],
      cartId: CartId,
      packageId: Option[PackageId] = None,
      operationId: Option[Long] = None,
      oldHistory: Seq[ItineraryHistory] = Seq.empty
  )

  final case class ValidateProductRequest[T <: ProductInfo](
      request: CreateBookingRequest,
      requestContext: RequestContext,
      product: Product[T]
  ) extends CreateRequest

  final case class MultiProductsRequest(
      request: CreateBookingRequest,
      requestContext: RequestContext,
      properties: Seq[Product[RoomInfo]],
      flights: Seq[Product[FlightBookingToken]],
      vehicles: Seq[Product[CarBookingToken]],
      protections: Seq[Product[ProtectionAncillaryModel]],
      activities: Seq[Product[ActivityBookingToken]],
      cegFastTracks: Seq[Product[AddOnBookingToken]],
      addOns: Seq[Product[addOn.AddOnBookingToken]],
      bookingFlow: BookingFlow,
      commonPayment: Option[CommonPaymentInfo],
      isBookingFromCart: Option[Boolean] = None,
      partnerExternalInfo: Option[PartnerExternalInfo] = None,
      isPartialSuccessAllowed: Option[Boolean] = None,
      cartItineraryInfo: Option[CartItineraryInfo] = None,
      override val userTaxCountryId: Option[Int] = None,
      override val userTaxCountry: Option[UserTaxCountry] = None,
      aabInfo: Option[AgentAssistedBookingInfo] = None,
      rebookAndCancelData: Option[RebookAndCancelData] = None,
      itineraryContext: Option[ItineraryContext] = None
  ) extends CreateRequest {

    def getAllProductInfo: Seq[Product[_ <: ProductInfo]] = properties ++
      flights ++
      vehicles ++
      protections ++
      activities ++
      addOns ++
      cegFastTracks

    def hasFlightsMainProductOnly: Boolean =
      flights.nonEmpty && properties.isEmpty && vehicles.isEmpty && activities.isEmpty

    // Flag: isMixedPaymentModel
    // True: CreditCardAPI will not remove CVV data after pre authorization
    // False: CreditCardAPI will remove CVV data after pre authorization
    def isMixedPaymentModel: Boolean = {
      val paymentModelList =
        getAllProductInfo.map(_.info.productPaymentModel).filterNot(_ == PaymentModel.Unknown).toSet

      val agencyPaymentModel   = paymentModelList.contains(PaymentModel.Agency)
      val merchantPaymentModel = PaymentModel.MerchantModels.exists(paymentModelList.contains)

      // This function should return true if both Agency and merchantModels payment model are present
      agencyPaymentModel && merchantPaymentModel
    }
  }

  final case class PreSaveProductStageRequest[T <: ProductInfo](
      request: CreateBookingRequest,
      requestContext: RequestContext,
      itineraryInfo: ItineraryPreSaveInfo,
      multiProductId: Option[MultiProductInfoDBModel],
      product: Product[T],
      bookingFlow: BookingFlow,
      productIndex: Option[Int] = None,
      override val userTaxCountryId: Option[Int] = None,
      override val userTaxCountry: Option[UserTaxCountry] = None,
      aabInfo: Option[AgentAssistedBookingInfo] = None
  ) extends CreateRequest

  trait ProductSaveStageResponse extends scala.Product with Serializable {
    val productType: ProductTypeEnum
    def bookingAction: BookingWorkflowAction
    def productTokenKey: Option[ProductTokenKey]
    def refProductTokenKey: Seq[ProductTokenKey] = Seq.empty
  }

  final case class NonPropertySaveStageResponse(
      override val productType: ProductTypeEnum,
      bookingAction: BookingWorkflowAction,
      bookingActionState: BookingActionState,
      override val productTokenKey: Option[ProductTokenKey],
      override val refProductTokenKey: Seq[ProductTokenKey] = Seq.empty
  ) extends ProductSaveStageResponse {}

  final case class PropertySaveStageResponse(
      bookingAction: BookingWorkflowAction,
      propertyCreationLocal: PropertyBookingCreationLocal,
      override val productTokenKey: Option[ProductTokenKey],
      override val refProductTokenKey: Seq[ProductTokenKey] = Seq.empty
  ) extends ProductSaveStageResponse {
    val productType: ProductTypeEnum = ProductTypeEnum.Property
  }

  final case class MultiProductSaveStageRequest(
      request: CreateBookingRequest,
      requestContext: RequestContext,
      saveBookingModel: MultiProductBookingInsertionModel,
      statusToken: StatusToken,
      rebookAndCancelData: Option[RebookAndCancelData] = None
  ) extends CreateRequest {

    def hasFlightsMainProductOnly: Boolean =
      saveBookingModel.flightBookingActionStates.nonEmpty && saveBookingModel.propertyModels.isEmpty && saveBookingModel.vehicleBookingActionStates.isEmpty && saveBookingModel.activityBookingActionState.isEmpty
  }

  final case class ReservedIds[PI <: ProductInfo, PRI <: ProductReservedIds](
      bookingId: Long,
      actionId: ActionId,
      multiProductId: Option[MultiProductId] = None,
      product: Product[PI],
      offerId: Option[OfferId] = None,
      metaId: Option[MetaId] = None,
      geoId: Option[GeoId] = None,
      essInfoId: Option[EssInfoId] = None,
      breakdownIds: Seq[BreakdownId] = Seq.empty,
      itineraryPaymentIds: Seq[ItineraryPaymentId] = Seq.empty,
      productReservedIds: Option[PRI] = None
  )

  trait ProductReservedIds extends scala.Product with Serializable

  final case class VehicleReservedIds(
      vehicleBookingLocationPickUpId: Option[VehicleBookingLocationId],
      vehicleBookingLocationDropOffId: Option[VehicleBookingLocationId],
      vehicleInfoId: Option[VehicleInfoId]
  ) extends ProductReservedIds

  final case class FlightReservedIds(
      flightSliceIds: Vector[FlightSliceId] = Vector.empty,
      flightSegmentIds: Vector[FlightSegmentId] = Vector.empty,
      flightSegmentInfoIds: Vector[FlightSegmentInfoId] = Vector.empty,
      flightBaggageIds: Vector[FlightBaggageId] = Vector.empty,
      flightBaggageAllowanceIds: Vector[FlightBaggageAllowanceId] = Vector.empty,
      flightBrandSelectionIds: Vector[FlightBrandSelectionId] = Vector.empty,
      flightBrandAttributeIds: Vector[FlightBrandAttributeId] = Vector.empty,
      flightBrandAttributeParamIds: Vector[FlightBrandAttributeParamId] = Vector.empty,
      flightPolicyIds: Vector[FlightPolicyId] = Vector.empty,
      flightSeatSelectionIds: Vector[FlightSeatSelectionId] = Vector.empty,
      flightPaxBreakdownIds: Vector[FlightPaxBreakdownId] = Vector.empty
  ) extends ProductReservedIds

  final case class EmptyProductReservedIds() extends ProductReservedIds
}
