package com.agoda.bapi.creation.util

import com.agoda.bapi.common.model.activity.ActivityBookingToken
import com.agoda.bapi.common.model.car.CarBookingToken
import com.agoda.bapi.common.model.flight.FlightBookingToken
import com.agoda.bapi.common.model.product.ProductTypeEnum
import com.agoda.bapi.common.model.product.ProductTypeEnum.ProductTypeEnum
import com.agoda.bapi.common.token.MultiProductCreationBookingToken
import com.agoda.bapi.common.util.BookingTokenHelper
import com.agoda.bapi.common.util.TokenDeserializers.{ActivityBookingModelDeSerializer, CarBookingModelDeserializer, FlightBookingModelDeserializer}

import scala.util.{Failure, Success}

object RetryPaymentUtil {

  def getProductType(multiProductToken: MultiProductCreationBookingToken): ProductTypeEnum = {
    val result = for {
      propertyBookings <- BookingTokenHelper.extractPropertyCreationObjects(multiProductToken)
      flightBookings <-
        BookingTokenHelper.extractCreationObjects[Seq[FlightBookingToken]](multiProductToken.flights)
      carBookings <- BookingTokenHelper.extractCreationObjects[CarBookingToken](multiProductToken.cars)
      activitiesBookings <-
        BookingTokenHelper.extractCreationObjects[ActivityBookingToken](multiProductToken.activities)
    } yield {
      val allProducts = Seq(
        flightBookings.nonEmpty,
        propertyBookings.nonEmpty,
        carBookings.nonEmpty,
        activitiesBookings.nonEmpty
      )
      countNonEmptyProducts(allProducts) match {
        case 0 => throw new ProductsNotFoundInBookingToken()
        case 1 =>
          if (carBookings.nonEmpty) ProductTypeEnum.Car
          else if (activitiesBookings.nonEmpty) ProductTypeEnum.Activity
          else if (flightBookings.nonEmpty) ProductTypeEnum.Flight
          else if (propertyBookings.nonEmpty) ProductTypeEnum.Property
          else
            throw new ProductCountMismatch() // in case of new product OR priceFreezeItems.size > 1
        case _ => ProductTypeEnum.Multi
      }
    }
    result match {
      case Success(productType) =>
        productType
      case Failure(exception) =>
        throw exception
    }
  }

  private def countNonEmptyProducts(items: Seq[Boolean]): Int = items.count(_ == true)

}
