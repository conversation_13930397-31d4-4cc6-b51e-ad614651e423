package com.agoda.bapi.creation.proxy

import com.agoda.bapi.common.message.PendingBookingChange
import com.agoda.bapi.common.model.base.BaseBookingRelationshipInternal
import com.agoda.bapi.common.model.booking.local.WorkflowItineraryOperation
import com.agoda.bapi.common.model.creation.AccountingEntity
import com.agoda.bapi.common.model.farerulepolicy.{FareRulePolicyDBModel, FareRulePolicyType}
import com.agoda.bapi.common.model.flight.Supplier
import com.agoda.bapi.common.model.flight.flightModel._
import com.agoda.mpb.common.MultiProductType.MultiProductType
import com.agoda.bapi.common.model.payment.SupplierPaymentMethod
import com.agoda.bapi.common.model.{CommonBookingEventsInfo, CommonBookingInfo}
import com.agoda.bapi.creation.model.db.{DuplicateBookingRecord, FlightStateForContinueCreation}
import com.agoda.mpb.common.MultiProductType
import com.agoda.sql.ResultSetHelper

import java.sql.ResultSet
import scala.util.Try

/**
  * BFDB Local is used for Flights and Vehicles
  */
object BFDBLocalQueriedDataMapper extends ResultSetHelper {

  def toFlightBookingId(resultSet: ResultSet): Option[Long] = {
    resultSet.map(implicit r => long("flight_booking_id")).headOption
  }

  def toFlightBookingItem(implicit r: ResultCtx): FlightBookingState =
    FlightBookingState(
      flightBookingId = c("flight_booking_id"),
      itineraryId = c("itinerary_id"),
      multiProductId = ctooptlong(c("multi_product_id")),
      multiProductType = ctooptint(c("multi_product_type_id")).map(MultiProductType.apply),
      ticketingAirline = c("ticketing_airline"),
      pnr = c("pnr"),
      gdsPnr = c("gds_pnr"),
      version = c("version"),
      flightStateId = c("flight_state_id"),
      isCancelled = c("is_cancelled"),
      cancellationDate = c("cancellation_date"),
      voidWindowUntil = c("void_window_until"),
      paymentModel = c("payment_model"),
      supplierId = c("supplier_id"),
      subSupplierId = c("sub_supplier_id"),
      pointOfSale = c("point_of_sale"),
      experimentVariant = c("experiment_variant"),
      supplierSpecificData = c("supplier_specific_data"),
      accountingEntity = Some(AccountingEntity.jsonToAccountingEntity(c("accounting_entity"))),
      supplierStatusCode = c("supplier_status_code"),
      supplierReasonCode = c("supplier_reason_code"),
      supplierBookingId = c("supplier_booking_id"),
      supplierCommissionAmount = c("supplier_commission_amount"),
      supplierCommissionPercentage = c("supplier_commission_percentage"),
      fraudScore = c("fraud_score"),
      fraudAction = c("fraud_action"),
      fraudCheckIp = c("fraud_check_ip"),
      storefrontId = c("storefront_id"),
      platformId = c("platform_id"),
      languageId = c("language_id"),
      displayCurrency = c("display_currency"),
      serverName = c("server_name"),
      cid = c("cid"),
      sessionId = c("session_id"),
      clientIpAddress = c("client_ip_address"),
      trackingCookieId = c("tracking_cookie_id"),
      trackingCookieDate = c("tracking_cookie_date"),
      trackingTag = c("tracking_tag"),
      searchId = c("searchId"),
      searchRequestId = c("search_request_id"),
      flapiItineraryId = c("flapi_itinerary_id"),
      recCreatedWhen = c("rec_created_when"),
      whitelabelId = c("whitelabel_id"),
      recStatus = c("rec_status"),
      recModifiedWhen = c("rec_modified_when"),
      rejectReasonCode = c("reject_reason_code"),
      ancillaryData = c("ancillary_data"),
      flightPostbookingStateId = c("flight_postbooking_state_id"),
      tripStartDate = ctooptjodadatetime(c("trip_start_date")),
      tripEndDate = ctooptjodadatetime(c("trip_end_date")),
      commonBookingInfo = getCommonBookingInfo,
      commonBookingEventsInfo = getCommonBookingEventsInfo,
      virtualInterlining = ctooptbool(c("is_virtual_interline")),
      fareRuleScope = ctooptshort(c("fare_rule_scope")).map(s => Scope.get(s.toInt)),
      freeBagScope = ctooptshort(c("free_bag_scope")).map(s => Scope.get(s.toInt)),
      promotionCampaignId = ctooptint(c("promotion_campaign_id")).filterNot(_ == 0), // NULL in db will be returned as 0
      supplierPaymentMethod = ctooptshort(c("supplier_payment_method")).map(s => SupplierPaymentMethod.get(s.toInt)),
      facilitationFeeWaiverReasonId = ctooptint(c("facilitation_fee_waiver_reason_id")),
      supplierReservationExpireDateTime = ctooptjodadatetime(c("supplier_reservation_expire_time")),
      supplierReservationExpireInMinutes = ctooptint(c("supplier_reservation_timeout_mins")),
      originalAttemptBookingId = c("original_attempt_booking_id")
    )

  def toFlightBookingWithMember(resultSet: ResultSet): Option[FlightBookingWithMember] = {
    resultSet
      .map(implicit r => {
        val flightBooking = toFlightBookingItem(r)
        val memberId      = c("member_id")
        FlightBookingWithMember(flightBooking, memberId)
      })
      .headOption
  }

  def toFlightBooking(resultSet: ResultSet): List[FlightBookingState] =
    resultSet.map(implicit r => toFlightBookingItem(r))

  def toFlightPaxTicket(resultSet: ResultSet): Seq[FlightPaxTicketState] = {
    resultSet.map(implicit r =>
      FlightPaxTicketState(
        ticketNumberId = c("flight_pax_ticket_number_id"),
        flightPaxId = c("flight_pax_id"),
        ticketType = c("flight_pax_ticket_type"),
        ticketNumber = c("flight_pax_ticket_number"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        recModifiedWhen = c("rec_modified_when"),
        flightSegmentId = ctooptlong(c("flight_segment_id"))
      )
    )
  }

  def toFlightPax(resultSet: ResultSet): Seq[FlightPaxNoPii] = {
    resultSet.map(implicit r =>
      FlightPaxNoPii(
        referenceId = c("flight_pax_id"),
        flightPaxId = c("flight_pax_id"),
        flightBookingId = c("flight_booking_id"),
        passengerTypeCode = c("passenger_type_code"),
        piiHash = c("pii_hash"),
        supplierFlightPaxId = c("supplier_flight_pax_id"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        recModifiedWhen = c("rec_modified_when")
      )
    )
  }

  def toPendingBookingChange(resultSet: ResultSet): Seq[PendingBookingChange] =
    resultSet.map(implicit r =>
      PendingBookingChange(
        changeId = c("change_id"),
        bookingId = c("booking_id"),
        actionTypeId = c("action_type_id"),
        changeProtobuf = c("change_protobuf"),
        version = c("version"),
        statusId = c("status_id"),
        recCreatedWhen = c("rec_created_when"),
        replicatedFrom = c("replicated_from")
      )
    )

  def toFlightSlice(resultSet: ResultSet): Seq[FlightSlice] = {
    resultSet.map(implicit r =>
      FlightSlice(
        referenceId = c("flight_slice_id"),
        flightSliceId = c("flight_slice_id"),
        flightBookingId = c("flight_booking_id"),
        origin = c("origin"),
        destination = c("destination"),
        departure = c("departure"),
        arrival = c("arrival"),
        duration = c("duration"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        recModifiedWhen = c("rec_modified_when")
      )
    )
  }

  def toFlightSegment(resultSet: ResultSet): Seq[FlightSegment] = {
    resultSet.map(implicit r =>
      FlightSegment(
        referenceId = c("flight_segment_id"),
        sliceReferenceId = c("flight_slice_id"),
        flightSegmentId = c("flight_segment_id"),
        flightSliceId = c("flight_slice_id"),
        marketingAirline = c("marketing_airline"),
        operatingAirline = c("operating_airline"),
        flightNo = c("flight_no"),
        origin = c("origin"),
        departure = c("departure"),
        destination = c("destination"),
        arrival = c("arrival"),
        airEquipmentCode = c("air_equipment_code"),
        bookingClass = c("booking_class"),
        cabinClass = c("cabin_class"),
        cabinName = c("cabin_name"),
        fareBasisCode = c("fare_basis_code"),
        fareRules = c("fare_rules"),
        duration = c("duration"),
        carrierPnr = c("carrier_pnr"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        recModifiedWhen = c("rec_modified_when"),
        bagsRecheckRequired = c("bags_recheck_required"),
        departureTerminal = c("departure_terminal"),
        arrivalTerminal = c("arrival_terminal")
      )
    )
  }

  def toFlightSegmentInfoByPaxType(resultSet: ResultSet): Seq[SegmentInfoByPaxType] = {
    resultSet.map(implicit r =>
      SegmentInfoByPaxType(
        referenceId = c("flight_segment_info_id"),
        segmentReferenceId = c("flight_segment_id"),
        segmentInfoByPaxTypeId = c("flight_segment_info_id"),
        flightSegmentId = c("flight_segment_id"),
        passengerType = c("passenger_type"),
        bookingClass = c("booking_class"),
        fareBasisCode = c("fare_basis_code"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        recModifiedWhen = c("rec_modified_when"),
        fareRuleRevisionId = c("fare_rule_revision_id")
      )
    )
  }

  def toFlightBaggageAllowance(resultSet: ResultSet): Seq[FlightBaggageAllowance] = {
    resultSet.map(implicit r =>
      FlightBaggageAllowance(
        referenceId = c("flight_baggage_id"),
        sliceReferenceId = c("flight_slice_id"),
        segmentReferenceId = c("flight_segment_id"),
        flightBaggageId = c("flight_baggage_id"),
        flightSliceId = c("flight_slice_id"),
        flightSegmentId = c("flight_segment_id"),
        `type` = c("type"),
        count = c("count"),
        maxWeightKg = c("max_weight_kg"),
        maxWeightLbs = c("max_weight_lbs"),
        totalSizeCm = c("total_size_cm"),
        totalSizeIn = c("total_size_in"),
        lengthCm = c("length_cm"),
        lengthIn = c("length_in"),
        widthCm = c("width_cm"),
        widthIn = c("width_in"),
        heightCm = c("height_cm"),
        heightIn = c("height_in"),
        priceAmt = c("price_amt"),
        priceCurrency = c("price_currency"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        recModifiedWhen = c("rec_modified_when"),
        paxType = c("passenger_type_code"),
        source = c("source"),
        cmsMappingValue = c("cms_mapping_value")
      )
    )
  }

  def toFlightBaggage(resultSet: ResultSet): Seq[FlightBaggage] = {
    resultSet.map(implicit r =>
      FlightBaggage(
        referenceId = c("flight_baggage_id"),
        sliceReferenceId = c("flight_slice_id"),
        flightBaggageId = c("flight_baggage_id"),
        flightPaxId = c("flight_pax_id"),
        flightSliceId = c("flight_slice_id"),
        baggageTypeId = c("baggage_type_id"),
        quantity = c("quantity"),
        maxWeight = c("max_weight"),
        maxWeightUnit = c("max_weight_unit"),
        weightLimitPerBag = c("weight_limit_per_bag"),
        weightLimitPerBagUnit = c("weight_limit_per_bag_unit"),
        sizeLength = c("size_length"),
        sizeWidth = c("size_width"),
        sizeHeight = c("size_height"),
        sizeUnit = c("size_unit"),
        priceAmount = c("price_amount"),
        priceCurrency = c("price_currency"),
        supplierData = c("supplier_data"),
        baggageStatus = c("baggage_status"),
        isCarryOn = c("is_carry_on"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        recModifiedWhen = c("rec_modified_when"),
        scope = Scope(ctoshort(c("scope"))),
        isPartialSettlementRequired = c("is_partial_settlement_required")
      )
    )
  }

  def toBookingAttribution(resultSet: ResultSet): Seq[BookingAttributionState] = {
    resultSet.map(implicit r =>
      BookingAttributionState(
        modelId = c("model_id"),
        tag = c("tag"),
        siteId = c("site_id"),
        clickDateTime = c("click_date"),
        additionalData = c("additional_data"),
        createdWhen = c("created_when"),
        bookingId = c("booking_id")
      )
    )
  }

  def toItineraryActionHistory(resultSet: ResultSet): Seq[ItineraryHistory] = {
    resultSet.map(implicit r =>
      ItineraryHistory(
        actionId = c("action_id"),
        itineraryId = c("itinerary_id"),
        bookingType = c("booking_type"),
        bookingId = c("booking_id"),
        actionType = c("action_type_id"),
        version = c("version"),
        actionDate = c("action_date"),
        parameters = c("parameters"),
        description = c("description"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        replicatedFromDC = c("replicated_from")
      )
    )
  }

  def toMultiProductItinerary(resultSet: ResultSet): Option[MultiProductItinerary] = {
    resultSet
      .map(implicit r =>
        MultiProductItinerary(
          itineraryId = c("itinerary_id"),
          memberId = c("member_id"),
          recStatus = c("rec_status"),
          recCreatedWhen = c("rec_created_when"),
          recModifiedWhen = c("rec_modified_when")
        )
      )
      .headOption
  }

  def toItineraryOperation(resultSet: ResultSet): Option[WorkflowItineraryOperation] = {
    resultSet
      .map(implicit r =>
        WorkflowItineraryOperation(
          operationId = c("operation_id"),
          itineraryId = c("itinerary_id"),
          actionTypeId = c("action_type_id"),
          recCreatedWhen = c("rec_created_when"),
          recModifiedWhen = c("rec_modified_when")
        )
      )
      .headOption
  }

  def toFinancialBreakDown(resultSet: ResultSet): Seq[Breakdown] = {
    resultSet.map(implicit r =>
      Breakdown(
        referenceId = c("breakdown_id"),
        breakdownId = c("breakdown_id"),
        itineraryId = c("itinerary_id"),
        bookingType = c("booking_type"),
        bookingId = c("booking_id"),
        actionId = c("action_id"),
        eventDate = c("event_date"),
        itemId = c("item_id"),
        typeId = c("type_id"),
        taxFeeId = c("tax_fee_id"),
        quantity = c("quantity"),
        localCurrency = c("local_currency"),
        localAmount = c("local_amount"),
        exchangeRate = c("exchange_rate"),
        usdAmount = c("usd_amount"),
        requestedAmount = c("requested_amount"),
        refBreakdownId = c("ref_breakdown_id"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        vendorExchangeRate = Try {
          ctodouble(c("vendor_exchange_rate"))
        }.toOption.getOrElse(1.0),
        upcId = c("upc_id"),
        requestedCurrency = c("requested_currency")
      )
    )
  }

  def toFlightFinancialBreakDownPerPax(resultSet: ResultSet): Seq[BreakdownPerPax] = {
    resultSet.map(implicit r =>
      BreakdownPerPax(
        referenceId = c("breakdown_id"),
        breakdownId = c("breakdown_id"),
        itineraryId = c("itinerary_id"),
        bookingType = c("booking_type"),
        bookingId = c("booking_id"),
        actionId = c("action_id"),
        eventDate = c("event_date"),
        itemId = c("item_id"),
        typeId = c("type_id"),
        applyType = c("apply_type"),
        taxFeeId = c("tax_fee_id"),
        quantity = c("quantity"),
        localCurrency = c("local_currency"),
        localAmount = c("local_amount"),
        exchangeRate = c("exchange_rate"),
        usdAmount = c("usd_amount"),
        requestedAmount = c("requested_amount"),
        requestedCurrency = c("requested_currency"),
        refBreakdownId = c("ref_breakdown_id"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        vendorExchangeRate = Try {
          ctodouble(c("vendor_exchange_rate"))
        }.toOption.getOrElse(1.0),
        paxId = c("pax_id"),
        typeRefId = c("type_ref_id")
      )
    )
  }

  def toPaymentState(resultSet: ResultSet): Seq[PaymentState] = {
    resultSet.map(implicit r =>
      PaymentState(
        referenceId = c("payment_id"),
        paymentId = c("payment_id"),
        itineraryId = c("itinerary_id"),
        actionId = c("action_id"),
        creditCardId = c("creditcard_id"),
        transactionDate = c("transaction_date"),
        transactionType = c("transaction_type"),
        paymentState = c("payment_state"),
        referenceNo = c("ref_no"),
        referenceType = c("ref_type"),
        last4Digits = c("last_4_digits").trim,
        paymentMethodId = c("payment_method_id"),
        gatewayId = c("gateway_id"),
        transactionId = c("transaction_id"),
        paymentCurrency = c("payment_currency").trim,
        paymentAmount = c("payment_amount"),
        amountUsd = c("amount_usd"),
        supplierCurrency = c("supplier_currency").trim,
        supplierAmount = c("supplier_amount"),
        exchangeRateSupplierToPayment = c("exchange_rate_supplier_to_payment"),
        creditCardCurrency = c("creditcard_currency").trim,
        upliftAmount = c("uplift_amount"),
        siteExchangeRate = c("site_exchange_rate"),
        upliftExchangeRate = c("uplift_exchange_rate"),
        paymentTypeId = c("payment_type_id"),
        token = c("token"),
        installmentPlanId = c("installment_plan_id"),
        remark = c("remark"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        referencePaymentId = c("reference_payment_id"),
        points = c("rewards_unit")
      )
    )
  }

  def toBookingPaymentState(resultSet: ResultSet): Seq[BookingPaymentState] = {
    resultSet.map(implicit result =>
      BookingPaymentState(
        bookingPaymentId = c("booking_payment_id"),
        paymentId = c("payment_id"),
        paymentCurrency = c("payment_currency"),
        paymentAmount = c("payment_amount"),
        amountUsd = c("amount_usd"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        bookingId = c("booking_id"),
        fxiUplift = c("fxi_uplift"),
        loyaltyPoints = c("rewards_unit"),
        supplierCurrency = c("supplier_currency"),
        supplierExchangeRate = c("supplier_exchange_rate")
      )
    )
  }

  def toBookingRelationshipInternal(resultSet: ResultSet): Seq[BaseBookingRelationshipInternal] = {
    resultSet.map(implicit result =>
      BaseBookingRelationshipInternal(
        relationshipId = c("relationship_id"),
        sourceBookingId = c("source_booking_id"),
        targetBookingId = c("target_booking_id"),
        relationshipStatusId = c("relationship_status_id"),
        relationshipTypeId = c("relationship_type_id"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        recCreatedBy = c("rec_created_by"),
        recModifiedWhen = c("rec_modified_when"),
        recModifiedBy = c("rec_modified_by")
      )
    )
  }

  def toFlightBookingSummaryState(resultSet: ResultSet): Seq[FlightSummary] = {
    resultSet.map(implicit r =>
      FlightSummary(
        bookingId = c("booking_id"),
        currency = c("currency"),
        baseFare = c("base_fare"),
        taxAndFee = c("tax_and_fee"),
        baseDiscount = c("base_discount"),
        campaignDiscount = c("campaign_discount"),
        totalFare = c("total_fare"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        recModifiedWhen = c("rec_modified_when")
      )
    )
  }

  def toFlightBookingUserAgent(resultSet: ResultSet): Option[UserAgentState] = {
    resultSet
      .map(implicit r =>
        UserAgentState(
          flightBookingId = c("flight_booking_id"),
          origin = c("origin"),
          osName = c("os_name"),
          osVersion = c("os_version"),
          browserName = c("browser_name"),
          browserLanguage = c("browser_language"),
          browserVersion = c("browser_version"),
          browserSubVersion = c("browser_subversion"),
          browserBuildNumber = c("browser_build_number"),
          deviceBrand = c("device_brand"),
          deviceModel = c("device_model"),
          deviceTypeId = c("device_type_id"),
          isMobile = c("is_mobile"),
          isTouch = c("is_touch"),
          additionalInfo = c("additional_info"),
          recStatus = c("rec_status"),
          recCreatedWhen = c("rec_created_when"),
          recModifiedWhen = c("rec_modify_when")
        )
      )
      .headOption
  }

  def toFareRulePolicy(resultSet: ResultSet): Seq[FareRulePolicyDBModel] =
    resultSet.map { implicit r =>
      FareRulePolicyDBModel(
        policyId = c("policy_id"),
        bookingId = c("flight_booking_id"),
        paxType = c("pax_type"),
        fareRulePolicyType = FareRulePolicyType(c("fare_rule_policy_type")),
        allowed = c("allowed"),
        penalty = c("penalty"),
        currencyCode = c("currency_code"),
        validUntilHoursBeforeBoarding = c("valid_until_hours_before_boarding"),
        validFromHoursBeforeBoarding = c("valid_from_hours_before_boarding"),
        flightSliceId = ctooptlong(c("flight_slice_id")),
        flightSegmentId = ctooptlong(c("flight_segment_id")),
        source = c("source"),
        cmsMappingValue = c("cms_mapping_value")
      )
    }

  def toFlightBookingRejectedReason(resultSet: ResultSet): Option[FlightBookingRejectReason] = {
    resultSet
      .map(implicit r =>
        FlightBookingRejectReason(
          subErrorCode = c("sub_error_code"),
          clientErrorCode = c("client_code"),
          errorTypeCode = c("error_type_code"),
          recStatus = c("rec_status"),
          recCreatedBy = c("rec_created_by"),
          recCreatedWhen = c("rec_created_when"),
          recModifiedWhen = c("rec_modified_when"),
          recModifiedBy = c("rec_modified_by")
        )
      )
      .headOption
  }

  def toLongIds(resultSet: ResultSet): Seq[Long] = {
    resultSet.map { implicit r =>
      long("id")
    }
  }

  def toSubSupplier(resultSet: ResultSet): Seq[Supplier] = {
    resultSet.map(implicit r =>
      Supplier(
        id = c("id"),
        code = c("code"),
        name = c("name"),
        dmcType = c("dmc_type"),
        isPull = ctooptbool(c("is_pull"))
      )
    )
  }

  def toFlightVersionConflict(resultSet: ResultSet): List[FlightVersionConflict] =
    resultSet.map(implicit r => toFlightVersionConflictItem(r))

  def toFlightVersionConflictItem(implicit r: ResultCtx): FlightVersionConflict =
    FlightVersionConflict(
      flightVersionConflictId = c("flight_version_conflict_id"),
      flightBookingId = c("flight_booking_id"),
      itineraryId = c("itinerary_id"),
      dc = c("dc"),
      currentVersion = c("current_version"),
      currentJson = c("current_json"),
      incomingVersion = c("incoming_version"),
      incomingJson = c("incoming_json"),
      resolutionVersion = c("resolution_version"),
      resolutionJson = c("resolution_json"),
      recStatus = c("rec_status"),
      recCreatedWhen = c("rec_created_when"),
      recModifiedWhen = c("rec_modified_when")
    )

  def toFlightFareRulePolicy(resultSet: ResultSet): Seq[FareRulePolicyModelInternal] =
    resultSet.map(implicit r =>
      FareRulePolicyModelInternal(
        policyId = c("policy_id"),
        paxType = c("pax_type"),
        flightBookingId = c("flight_booking_id"),
        fareRulePolicyType = c("fare_rule_policy_type"),
        allowed = c("allowed"),
        penalty = c("penalty"),
        currencyCode = c("currency_code"),
        validUntilHoursBeforeBoarding = c("valid_until_hours_before_boarding"),
        validFromHoursBeforeBoarding = c("valid_from_hours_before_boarding"),
        recCreatedWhen = c("rec_created_when"),
        recModifyWhen = c("rec_modify_when"),
        sliceId = ctooptlong(c("flight_slice_id")),
        segmentId = ctooptlong(c("flight_segment_id")),
        source = c("source"),
        cmsMappingValue = c("cms_mapping_value")
      )
    )

  def toFlightSeatSelection(resultSet: ResultSet): Seq[FlightSeatSelectionDetail] =
    resultSet.map(implicit r =>
      FlightSeatSelectionDetail(
        referenceId = c("flight_seat_selection_id"),
        segmentReferenceId = c("flight_segment_id"),
        flightSeatSelectionId = c("flight_seat_selection_id"),
        flightSegmentId = c("flight_segment_id"),
        flightSegmentIndex = c("flight_segment_index"),
        flightPaxId = c("flight_pax_id"),
        seatRow = c("seat_row"),
        seatColumn = c("seat_column"),
        flightSeatState = c("flight_seat_state"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        recModifiedWhen = c("rec_modified_when"),
        priceAmount = c("price_amount"),
        priceCurrency = c("price_currency"),
        supplierData = c("supplier_data"),
        isPartialSettlementRequired = c("is_partial_settlement_required")
      )
    )

  def toFlightBrandSelection(resultSet: ResultSet): Seq[FlightBrandSelection] =
    resultSet.map(implicit r =>
      FlightBrandSelection(
        referenceId = c("flight_brand_selection_id"),
        sliceReferenceId = c("flight_slice_id"),
        flightBrandSelectionId = c("flight_brand_selection_id"),
        flightSliceId = c("flight_slice_id"),
        brandName = c("brand_name"),
        isUpsell = c("is_upsell"),
        upsellMargin = c("upsell_margin"),
        upsellPrice = c("upsell_price"),
        upsellCurrency = c("upsell_currency"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        recModifiedWhen = c("rec_modified_when"),
        scope = Scope(ctoshort(c("scope")))
      )
    )

  def toFlightBrandAttribute(resultSet: ResultSet): Seq[FlightBrandAttribute] =
    resultSet.map(implicit r =>
      FlightBrandAttribute(
        referenceId = c("flight_brand_attribute_id"),
        brandSelectionReferenceId = c("flight_brand_selection_id"),
        flightBrandAttributeId = c("flight_brand_attribute_id"),
        flightBrandSelectionId = c("flight_brand_selection_id"),
        brandAttributeType = c("brand_attribute_type"),
        brandAttributeInclusion = InclusionType(ctoshort(c("brand_attribute_inclusion"))),
        brandAttributeDetailType = c("brand_attribute_detail_type"),
        brandAttributeDetailInclusion = ctooptshort(c("brand_attribute_detail_inclusion"))(r)
          .flatMap(i => InclusionType.getOption(i.toInt)),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        recModifiedWhen = c("rec_modified_when")
      )
    )

  def toFlightBrandAttributeParam(resultSet: ResultSet): Seq[FlightBrandAttributeParam] =
    resultSet.map(implicit r =>
      FlightBrandAttributeParam(
        referenceId = c("flight_brand_attribute_param_id"),
        brandAttributeReferenceId = c("flight_brand_attribute_id"),
        flightBrandAttributeParamId = c("flight_brand_attribute_param_id"),
        flightBrandAttributeId = c("flight_brand_attribute_id"),
        name = c("name"),
        code = c("code"),
        recStatus = c("rec_status"),
        recCreatedWhen = c("rec_created_when"),
        recModifiedWhen = c("rec_modified_when")
      )
    )

  def toDuplicateFlightBookingRecord(resultSet: ResultSet): Seq[DuplicateBookingRecord] =
    resultSet.map { implicit r =>
      DuplicateBookingRecord(
        bookingId = c("flight_booking_id"),
        bookingDate = c("rec_created_when"),
        storeFrontId = 1,
        languageId = 1
      )
    }

  def toDuplicateActivityBookingRecord(resultSet: ResultSet): Seq[DuplicateBookingRecord] =
    resultSet.map { implicit r =>
      val bookingIdLong =
        try long("booking_id")
        catch { case e: Exception => int("booking_id").longValue }
      DuplicateBookingRecord(
        bookingId = bookingIdLong,
        bookingDate = c("booking_date"),
        storeFrontId = -1,
        languageId = -1
      )
    }

  def toFlightPostBookingFee(resultSet: ResultSet): Seq[PostBookingFee] =
    resultSet.map { implicit r =>
      PostBookingFee(
        feeType = ctooptshort(c("fee_type_id"))(r)
          .flatMap(i => FeeTypeId.getOption(i))
          .getOrElse(FeeTypeId.UNKNOWN),
        currencyCode = c("currency"),
        amount = c("amount"),
        exchangeRate = c("exchange_rate"),
        usdAmount = c("usd_amount")
      )
    }

  def toFlightStateForContinueCreation(resultSet: ResultSet): Option[FlightStateForContinueCreation] =
    resultSet
      .map(implicit r =>
        FlightStateForContinueCreation(
          flightBookingId = c("flight_booking_id"),
          flightStateId = c("flight_state_id"),
          subSupplierId = c("sub_supplier_id")
        )
      )
      .headOption

  private def getCommonBookingInfo(implicit r: ResultCtx): Option[CommonBookingInfo] = {
    val origin        = ctooptstr(c("origin"))
    val isTestBooking = ctooptint(c("test_flag"))
    origin.map(_ =>
      CommonBookingInfo(origin = origin, isTestBooking = isTestBooking.contains(CommonBookingInfo.TEST_BOOKING))
    )
  }

  private def getCommonBookingEventsInfo(implicit r: ResultCtx): Option[CommonBookingEventsInfo] = {
    val confirmationDate = ctooptjodadatetime(c("confirmation_date"))
    val cancellationDate = ctooptjodadatetime(c("cancellation_date"))
    if (confirmationDate.isDefined || cancellationDate.isDefined)
      Some(
        CommonBookingEventsInfo(
          confirmationDate = confirmationDate,
          cancellationDate = cancellationDate
        )
      )
    else None
  }

}
