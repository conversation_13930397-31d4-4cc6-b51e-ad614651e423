package com.agoda.bapi.creation.proxy

import com.agoda.bapi.common.database.{AGDB, CallableQuery, SqlSupport}
import com.agoda.bapi.common.exception.DbException
import com.agoda.bapi.common.message.creation.CreatedBookingStatus.CreatedBookingStatus
import com.agoda.bapi.common.message.creation.DuplicateCandidateStatus
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.db.CountryInfo
import com.agoda.bapi.common.proxy.DependencyNames.Dependency
import com.agoda.bapi.common.proxy.{DBConnectionGroup, DbProxy, DependencyNames}
import com.agoda.bapi.creation.model.db._
import com.agoda.bapi.creation.model.properties.PropertyBookingDuplicationCriteria
import com.agoda.bapi.creation.model.PaymentLimitationInfo
import com.agoda.bapi.creation.util.ToolSet
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.sql.ResultSetHelper
import org.joda.time.DateTime

import java.sql._
import javax.inject.Inject
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal

trait DefaultDbProxy extends DbProxy {
  def getEbeLiteBookingObjects(itineraryId: ItineraryId): Future[Seq[EbeLiteBookingObject]]

  def getEbeLiteBookingObjectsByBookingId(bookingId: BookingId): Future[Seq[EbeLiteBookingObject]]

  def getBinRangeData(bin: String): Future[Option[CardBinRangeInfo]]

  def getBinLength(): Future[Int]

  def getCountryInfos(): Future[List[CountryInfo]]

  def findAllDuplicatedBookingCandidatesBFDB(
      propertyBookingDuplicationCriteria: PropertyBookingDuplicationCriteria,
      dayOffset: Int,
      isTagIdCheckEnabled: Option[Boolean]
  ): Future[Seq[PropertyDuplicatedBookingCandidate]]

  def getGuestRestrictedNationality(countryId: Int): Future[Option[Int]]

  def checkDuplicateRequestAndInsertNewCandidate(
      hashedRequest: String,
      requestTypeName: String,
      expiryDatetime: DateTime
  ): Future[Int]

  def isHotelNationalityRestriction(hotelId: Int, countryId: Int): Future[Option[Boolean]]

  def isAirportNationalityRestriction(airportCode: String, countryId: Int): Future[Option[Boolean]]

  def getHotelCountryIdWithRecStatus(hotelId: Int): Future[Option[Int]]

  def getAirportCountryId(airportCode: String): Future[Option[Int]]

  def isCurrencyOffered(currencyCode: String): Future[Boolean]

  def getConfigurations(groupId: Int, key: String): Future[Option[String]]

  def isRedirectCard(paymentMethodId: Int): Future[Boolean]

  def updatePublishStatus(itineraryId: Int): Future[Boolean]

  def isPaymentTokenEnabled(whitelabelId: Int, paymentMethodId: Int, productTypeId: Int): Future[Boolean]

  def setPropertyBookingState(bookingId: Long, state: CreatedBookingStatus): Future[Int]

  // For reserve BookingId and Itinerary without saving the Booking
  def getNextItineraryNumber: Future[Long]

  def getNextBookingId: Future[BookingId]

  // For payment limitation
  def getPaymentLimitation(siteId: SiteId): Future[Option[PaymentLimitationInfo]]

  override protected def dependency: Dependency = DependencyNames.BfdbBcreMetric
}

object EbeLiteDbProxy {
  val DefaultBinLen: SiteId = 10
}

class EbeLiteDbProxyImpl @Inject() (
    val db: AGDB,
    dfdbExecutionContext: BFDBExecutionContext
) extends DefaultDbProxy
    with ItineraryDbProxyAction
    with SqlSupport
    with ToolSet
    with ResultSetHelper {

  import EbeLiteDbProxyImpl._

  private val bcreDbConnGroup = DBConnectionGroup.BFDB_BCRE

  implicit val dbDispatcher: ExecutionContext = dfdbExecutionContext.executionContext

  override def getEbeLiteBookingObjects(itineraryId: ItineraryId): Future[Seq[EbeLiteBookingObject]] = {
    val queryName      = "bcre_get_ebe_lite_booking_v1"
    val queryToExecute = query(s"EXEC dbo.$queryName @itineraryId = ?", itineraryId)
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapEbeLiteBookingQueryResults
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def getEbeLiteBookingObjectsByBookingId(bookingId: BookingId): Future[Seq[EbeLiteBookingObject]] = {
    val queryName      = "bcre_get_ebe_lite_booking_by_booking_id_v1"
    val queryToExecute = query(s"EXEC dbo.$queryName @bookingId = ?", bookingId)
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapEbeLiteBookingQueryResults
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def findAllDuplicatedBookingCandidatesBFDB(
      propertyBookingDuplicationCriteria: PropertyBookingDuplicationCriteria,
      dayOffset: Int,
      isTagIdCheckEnabled: Option[Boolean]
  ): Future[Seq[PropertyDuplicatedBookingCandidate]] = {
    val queryName = "bcre_check_duplicate_booking_v6"
    val queryToExecute = query(
      s"EXEC dbo.$queryName @CheckInDate = ?, @CheckOutDate = ?, @DayOffset = ?, @WhitelabelId = ?, @StorefrontId = ?, @AffiliateTagId = ?, @HashedPii = ?, @IsTagIdCheckEnabled = ?, @PropertyId = ?, @MemberId = ?",
      new Timestamp(propertyBookingDuplicationCriteria.checkInDate.toDateTimeAtStartOfDay.getMillis),
      new Timestamp(propertyBookingDuplicationCriteria.checkOutDate.toDateTimeAtStartOfDay.getMillis),
      dayOffset,
      propertyBookingDuplicationCriteria.whiteLabel.id,
      propertyBookingDuplicationCriteria.storefrontId,
      propertyBookingDuplicationCriteria.affiliateTagId,
      propertyBookingDuplicationCriteria.hashedPII,
      if (isTagIdCheckEnabled.getOrElse(false)) 1 else 0,
      propertyBookingDuplicationCriteria.propertyId,
      propertyBookingDuplicationCriteria.memberId
    )
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapDuplicateBookingQueryResultsWithStatus
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  def updatePublishStatus(itineraryId: Int): Future[Boolean] = {
    val queryName = "bcre_creation_reset_itinerary_publish_status_v1"
    val sqlQuery  = query(s"EXEC dbo.$queryName @itineraryId = ?", itineraryId)

    withExecutionTimeMeasure(metricName(), getMethodTag(queryName)) {
      Future {
        db.withConnectionGroup(bcreDbConnGroup.value) { implicit conn =>
          val statement = getStatement(sqlQuery)
          statement.executeUpdate()
          ()
        }
      }.andThen {
        handleFailure(queryName, sqlQuery)
      }
    }.map(_ => true).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def getBinRangeData(bin: String): Future[Option[CardBinRangeInfo]] = {
    val queryName = "bcre_select_binrange_by_creditcard_number_v1"
    val queryToExecute = query(
      s"EXEC dbo.$queryName @cc_number = ?",
      bin
    )
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapBinRangeCardInfoQueryResults
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  def getBinLength(): Future[Int] = {
    val queryName      = "bcre_select_binrange_maxbinlength_v1"
    val queryToExecute = query(s"EXEC dbo.$queryName")
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapBinLengthQueryResults
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def getCountryInfos(): Future[List[CountryInfo]] = {
    val queryName      = "bcre_select_geo2_country_v1"
    val queryToExecute = query(s"EXEC dbo.$queryName")
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapCountryInfoQueryResults
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def getNextItineraryNumber: Future[Long] = {
    val queryName      = "bcre_select_next_itinerary_id_v1"
    val queryToExecute = query(s"EXEC dbo.$queryName")
    val errMsg         = "Unable to get next itinerary number"
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapNextSequenceNumberQueryResult(errMsg)
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def getNextBookingId: Future[BookingId] = {
    val queryName      = "bcre_select_next_booking_id_v1"
    val queryToExecute = query(s"EXEC dbo.$queryName")
    val errMsg         = "Unable to get next booking id"
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapNextSequenceNumberQueryResult(errMsg)
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def getGuestRestrictedNationality(countryId: Int): Future[Option[Int]] = {
    val queryName = "bcre_select_geo2_country_by_id_v1"
    val queryToExecute = query(
      s"EXEC dbo.$queryName @country_id = ?",
      countryId
    )
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapRestrictionStatusQueryStatus
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def checkDuplicateRequestAndInsertNewCandidate(
      hashedRequest: String,
      requestTypeName: String,
      expiryDatetime: DateTime
  ): Future[Int] = {
    val callableStatementExecBlock = { statement: CallableStatement =>
      statement.setString(2, hashedRequest)
      statement.setString(3, requestTypeName)
      statement.setTimestamp(4, new Timestamp(expiryDatetime.getMillis))

      statement.registerOutParameter(1, java.sql.Types.INTEGER)
      statement.execute()
      val lockingResult = statement.getInt(1)
      lockingResult
    }

    val queryName      = "bcre_check_duplicate_request_and_insert_new_candidate_v1"
    val queryToExecute = s"{ ? = call dbo.$queryName (?,?,?) }"
    executeCallableStatement(
      bcreDbConnGroup,
      queryName,
      CallableQuery(queryToExecute),
      callableStatementExecBlock
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def isHotelNationalityRestriction(hotelId: Int, countryId: Int): Future[Option[Boolean]] = {
    val callableStatementExecBlock = { statement: CallableStatement =>
      statement.setInt(2, hotelId)
      statement.setInt(3, countryId)
      statement.registerOutParameter(1, java.sql.Types.INTEGER)
      statement.execute()
      val status = statement.getInt(1)
      Some(status > 0)
    }

    val queryName      = "bcre_select_hotel_nationality_restriction_v1"
    val queryToExecute = s"{ ? = call dbo.$queryName (?,?) }"
    executeCallableStatement(
      bcreDbConnGroup,
      queryName,
      CallableQuery(queryToExecute),
      callableStatementExecBlock
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def isAirportNationalityRestriction(airportCode: String, countryId: Int): Future[Option[Boolean]] = {
    val callableStmtExecBlock = { statement: CallableStatement =>
      statement.setString(2, airportCode)
      statement.setInt(3, countryId)
      statement.registerOutParameter(1, java.sql.Types.INTEGER)
      statement.execute()
      val status = statement.getInt(1)
      Some(status > 0)
    }

    val queryName      = "bcre_select_airport_nationality_restriction_v1"
    val queryToExecute = s"""{ ? = call dbo.$queryName (?,?) }"""
    executeCallableStatement(
      bcreDbConnGroup,
      queryName,
      CallableQuery(queryToExecute),
      callableStmtExecBlock
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def getHotelCountryIdWithRecStatus(hotelId: Int): Future[Option[Int]] = {
    val queryName      = "bcre_select_product_hotels_countryid_v2"
    val queryToExecute = query(s"EXEC dbo.$queryName @hotel_id = ?", hotelId)
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapCountryIdForQueryResult
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def getAirportCountryId(airportCode: String): Future[Option[Int]] = {
    val queryName      = "bcre_select_product_airports_countryid_v1"
    val queryToExecute = query(s"EXEC dbo.$queryName @airport_code = ?", airportCode)
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapCountryIdForQueryResult
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def isCurrencyOffered(currencyCode: String): Future[Boolean] = {
    val queryName      = "bcre_currency_offer_validation_v1"
    val queryToExecute = query(s"EXEC dbo.$queryName @currency_code = ?", currencyCode)
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapCurrencyOfferedQueryResult
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def getConfigurations(groupId: Int, key: String): Future[Option[String]] = {
    val queryName = "bcre_afm_configuration_select_v1"
    val queryToExecute =
      query(s"""EXEC dbo.$queryName @configuration_group_id = ?, @configuration_key = ?""", groupId, key)
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapConfigurationQueryResult
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def isRedirectCard(paymentMethodId: Int): Future[Boolean] = {
    val queryName      = "bcre_select_payment_methods_isredirect_v1"
    val queryToExecute = query(s"EXEC dbo.$queryName @payment_method_id = ?", paymentMethodId)
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapRedirectPaymentMethodQueryResult
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def isPaymentTokenEnabled(whitelabelId: Int, paymentMethodId: Int, productTypeId: Int): Future[Boolean] = {
    val queryName = "bcre_get_payment_methods_display_token_enabled_v1"
    val queryToExecute = query(
      s"EXEC dbo.$queryName @payment_method_id = ?, @whitelabel_id = ?, @product_type_id = ?",
      paymentMethodId,
      whitelabelId,
      productTypeId
    )
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapDisplayPaymentMethodTokenQueryResults
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def setPropertyBookingState(bookingId: Long, state: CreatedBookingStatus): Future[Int] = {

    val queryName = "bcre_update_property_state_v1"
    val sqlQuery  = query(s"EXEC dbo.$queryName @bookingId = ?, @stateId = ?", bookingId, state.id)

    executeQuery(
      bcreDbConnGroup,
      queryName,
      sqlQuery,
      mapSetPropertyBookingStateResult
    ) recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

  override def getPaymentLimitation(siteId: SiteId): Future[Option[PaymentLimitationInfo]] = {
    val queryName      = "bcre_get_payment_limitation_v2"
    val queryToExecute = query(s"EXEC dbo.$queryName @site_id = ?", siteId)
    executeQuery(
      bcreDbConnGroup,
      queryName,
      queryToExecute,
      mapPaymentLimitationQueryResults
    ).recoverWith {
      case NonFatal(e) =>
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(e)))
    }
  }

}

object EbeLiteDbProxyImpl {

  import ResultSetHelper._

  def mapEbeLiteBookingQueryResults(resultSet: ResultSet): List[EbeLiteBookingObject] = {
    resultSet.map(implicit r => {
      val bookingIdLong =
        try long("booking_id")
        catch { case e: Exception => int("booking_id").longValue }
      EbeLiteBookingObject(c("itinerary_id"), bookingIdLong, c("booking_data"), c("state_id"))
    })
  }

  def mapBinRangeCardInfoQueryResults(resultSet: ResultSet): Option[CardBinRangeInfo] = {
    resultSet.map { implicit r =>
      CardBinRangeInfo(CardClass.withName(c("card_class").trim))
    }.headOption
  }

  def mapBinLengthQueryResults(resultSet: ResultSet): Int = {
    if (resultSet.next())
      resultSet.getInt("max_bin_length")
    else EbeLiteDbProxy.DefaultBinLen
  }

  def mapCountryInfoQueryResults(resultSet: ResultSet): List[CountryInfo] = {
    resultSet.map(implicit r =>
      CountryInfo(c("country_id"), c("country_name"), c("country_nationality"), c("country_iso"), c("country_iso2"))
    )
  }

  def mapDuplicateBookingQueryResults(resultSet: ResultSet): List[PropertyDuplicatedBookingCandidate] = {
    resultSet.map { implicit r =>
      PropertyDuplicatedBookingCandidate(
        c("booking_id"),
        c("booking_date"),
        c("storefront_id"),
        c("language_id"),
        roomTypeId = 0L,
        workflowPhaseId = None
      )
    }
  }

  def mapDuplicateBookingQueryResultsWithStatus(resultSet: ResultSet): List[PropertyDuplicatedBookingCandidate] = {
    resultSet.map { implicit r =>
      PropertyDuplicatedBookingCandidate(
        c("booking_id"),
        c("booking_date"),
        c("storefront_id"),
        c("language_id"),
        roomTypeId = 0L,
        workflowPhaseId = c("workflow_phase_id"),
        itineraryId = c("itinerary_id"),
        candidateStatus = DuplicateCandidateStatus.withIdOpt(c("candidate_status"))
      )
    }
  }

  def mapRestrictionStatusQueryStatus(resultSet: ResultSet): Option[Int] = {
    if (resultSet.next()) {
      val status = resultSet.getInt("rec_status")
      Some(status)
    } else None
  }

  def mapCountryIdForQueryResult(resultSet: ResultSet): Option[Int] = {
    if (resultSet.next()) {
      val status = resultSet.getInt("country_id")
      Some(status)
    } else None
  }

  def mapCurrencyOfferedQueryResult(resultSet: ResultSet): Boolean = {
    if (resultSet.next())
      resultSet.getBoolean(1)
    else false
  }

  def mapConfigurationQueryResult(resultSet: ResultSet): Option[String] = {
    if (resultSet.next())
      Some(resultSet.getString(1))
    else None
  }

  def mapRedirectPaymentMethodQueryResult(resultSet: ResultSet): Boolean = {
    if (resultSet.next())
      resultSet.getBoolean(1)
    else false
  }

  def mapDisplayPaymentMethodTokenQueryResults(resultSet: ResultSet): Boolean = {
    if (resultSet.next())
      resultSet.getBoolean(1)
    else false
  }

  def mapNextSequenceNumberQueryResult(errMsg: String)(resultSet: ResultSet): Long = {
    if (resultSet.next()) {
      try resultSet.getLong(1)
      catch { case e: Exception => resultSet.getInt(1).longValue }
    } else
      throw new SQLException(errMsg)
  }

  def mapSetPropertyBookingStateResult(resultSet: ResultSet): Int = {
    if (resultSet.next())
      resultSet.getInt(1)
    else 0
  }

  def mapPaymentLimitationQueryResults(resultSet: ResultSet): Option[PaymentLimitationInfo] = {
    resultSet.map { implicit r =>
      PaymentLimitationInfo(
        siteId = c("site_id"),
        paymentMethodIds = splitStringToInt(str("payment_method_ids"), ","),
        paymentMethodNames = splitString(str("payment_method_names"), ",").map(_.map(_.toString)),
        bookingStartDate = jodadatetime("booking_start_date").toString(),
        bookingEndDate = jodadatetime("booking_end_date").toString(),
        errorCMSId = c("error_cms_id"),
        alternatePaymentCMSId = c("alternate_payment_cms_id"),
        platformIds = splitStringToInt(str("platform_ids"), ","),
        binList = splitStringToInt(str("bin_no"), ",")
      )
    }.headOption
  }

  private[proxy] def splitStringToInt(s: String, separator: String): Option[List[Int]] = {
    splitString(s, separator).map(_.map(_.toInt).toList)
  }

  private[proxy] def splitString(s: String, separator: String): Option[List[String]] = {
    if (Option(s).forall(_.trim.isEmpty)) None
    else if (s == "0") Some(List.empty)
    else Some(s.split(separator).map(_.trim).toList)
  }
}
