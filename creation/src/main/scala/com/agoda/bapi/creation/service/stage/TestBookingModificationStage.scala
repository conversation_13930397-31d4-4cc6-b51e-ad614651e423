package com.agoda.bapi.creation.service.stage

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.{AffiliateModel, CreateBookingResponse}
import com.agoda.bapi.creation.config.TestBookingConfig
import com.agoda.bapi.creation.model.RequestWithProducts
import com.agoda.bapi.creation.service.flow.ProcessStage
import com.agoda.bapi.creation.util.{ToolSet, WhitelabelUtils}
import com.softwaremill.quicklens._

import javax.inject.Inject
import scala.concurrent.Future

class TestBookingModificationStage @Inject() (testBookingConfig: TestBookingConfig)
    extends ProcessStage[RequestWithProducts, RequestWithProducts, CreateBookingResponse]
    with ToolSet {

  override def process(
      requestWithProducts: RequestWithProducts
  ): Future[Either[CreateBookingResponse, RequestWithProducts]] = {
    implicit val requestContext = requestWithProducts.requestContext

    val request  = requestWithProducts.request
    val memberId = request.userContext.flatMap(_.memberId)

    val testEmail = getTestBookingEmail(request.customer.email, memberId)
      .orElse(getAffiliateTestBookingEmail(request.isTestBooking, request.affiliateModel))

    val overridedRequest = testEmail match {
      case None => request.copy(isTestBooking = false)
      case Some(email) =>
        val updatedRequest = request.modify(_.isTestBooking).setTo(true)
        if (WhitelabelUtils.doNotOverrideEmailForWhiteLabels(requestContext)) {
          updatedRequest
        } else {
          updatedRequest.modify(_.customer.email).setTo(email)
        }
    }
    Future.successful(Right(requestWithProducts.copy(request = overridedRequest)))
  }

  private[stage] def getTestBookingEmail(
      email: String,
      memberId: Option[Int]
  )(implicit requestContext: RequestContext): Option[String] = {
    val isWhitelabelForceTestBooking =
      WhitelabelUtils.isForceTestBooking()(requestContext.whiteLabelInfo)
    val isEmailForceTestBooking = testBookingConfig.emails.contains(email)
    val isMemberIdInWhitelist   = testBookingConfig.whitelistMemberIds.contains(memberId.getOrElse(-1))
    val isExpOpen               = requestContext.featureAware.exists(_.enableIsTestBooking)
    val isTestEnv               = requestContext.isTestEnvironment()

    val isTestBooking =
      (isWhitelabelForceTestBooking || isEmailForceTestBooking || isTestEnv) && !isMemberIdInWhitelist && isExpOpen

    if (isTestBooking) {
      Some("<EMAIL>")
    } else {
      None
    }
  }

  private[stage] def getAffiliateTestBookingEmail(
      isTestBooking: Boolean,
      affiliateModel: Option[AffiliateModel.AffiliateModel]
  ): Option[String] =
    if (isTestBooking && affiliateModel.contains(AffiliateModel.B2B)) {
      Some("<EMAIL>")
    } else {
      None
    }
}
