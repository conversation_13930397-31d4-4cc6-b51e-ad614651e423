package com.agoda.bapi.creation.repository

import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.booking.local.{BookingActionWorkflowPhase, BookingActionWorkflowState, BookingWorkflowAction}
import com.agoda.bapi.creation.model.db.BookingActionIdentifier
import com.agoda.bapi.creation.proxy.WorkflowDbProxy
import com.agoda.bapi.creation.service.BookingActionMessage

import javax.inject.Inject
import scala.concurrent.Future

trait WorkflowRepository {
  def insertBookingAction(bookingAction: BookingWorkflowAction): Future[BookingWorkflowAction]
  def getBookingAction(actionId: FlightBookingId): Future[Option[BookingWorkflowAction]]
  def getBookingActionBySupplierBookingId(
      supplierBookingId: String,
      topicId: TopicId,
      uuid: String
  ): Future[Option[BookingWorkflowAction]]
  def getBookingActionByWorkflowId(
      itineraryId: ItineraryId,
      workflowId: WorkflowId = 0
  ): Future[Option[BookingWorkflowAction]]

  def insertBookingActionMessage(message: BookingActionMessage): Future[Long]
  def getBookingActionMessage(actionId: FlightBookingId): Future[Seq[BookingActionMessage]]
  def getBookingActionByBookingId(bookingId: FlightBookingId): Future[Option[BookingWorkflowAction]]
  def getMasterBookingActionByItineraryId(itineraryId: ItineraryId): Future[Option[BookingWorkflowAction]]
  def getBookingActionByItineraryId(itineraryId: ItineraryId): Future[Seq[BookingWorkflowAction]]
  def getBaseBookingActionBySupplierBookingId(
      supplierBookingId: String,
      topicId: TopicId,
      uuid: String
  ): Future[Option[BookingWorkflowAction]]

  def updateBookingActionMessage(messageId: Long, content: String): Future[Int]

  def getBookingActionIdentifierByBookingIdAndTopicId(
      bookingId: FlightBookingId,
      topicId: TopicId,
      recCreatedBy: String
  ): Future[Option[BookingActionIdentifier]]
}

class WorkflowRepositoryImpl @Inject() (workflowDBProxy: WorkflowDbProxy) extends WorkflowRepository {

  override def insertBookingAction(bookingAction: BookingWorkflowAction): Future[BookingWorkflowAction] = {
    workflowDBProxy.insertBookingAction(bookingAction)
  }

  override def getBookingAction(actionId: FlightBookingId): Future[Option[BookingWorkflowAction]] = {
    workflowDBProxy.getBookingAction(actionId)
  }

  override def getBookingActionByWorkflowId(
      itineraryId: ItineraryId,
      workflowId: WorkflowId
  ): Future[Option[BookingWorkflowAction]] = {
    workflowDBProxy.getBookingActionByWorkflowId(itineraryId, workflowId)
  }

  override def insertBookingActionMessage(message: BookingActionMessage): Future[Long] =
    workflowDBProxy.insertBookingActionMessage(message)

  override def getBookingActionMessage(actionId: FlightBookingId): Future[Seq[BookingActionMessage]] = {
    workflowDBProxy.getBookingActionMessage(actionId)
  }

  override def getBookingActionBySupplierBookingId(
      supplierBookingId: String,
      topicId: TopicId,
      uuid: String
  ): Future[Option[BookingWorkflowAction]] = {
    workflowDBProxy.getBookingActionBySupplierBookingId(supplierBookingId, topicId, uuid)
  }

  override def getBookingActionByBookingId(bookingId: FlightBookingId): Future[Option[BookingWorkflowAction]] =
    workflowDBProxy.getBookingActionByBookingId(bookingId)

  def getMasterBookingActionByItineraryId(itineraryId: ItineraryId): Future[Option[BookingWorkflowAction]] = {
    workflowDBProxy.getMasterBookingActionByItineraryId(itineraryId)
  }

  override def getBookingActionByItineraryId(itineraryId: ItineraryId): Future[Seq[BookingWorkflowAction]] = {
    workflowDBProxy.getBookingActionByItineraryId(itineraryId)
  }

  override def getBaseBookingActionBySupplierBookingId(
      supplierBookingId: PNR,
      topicId: TopicId,
      uuid: PNR
  ): Future[Option[BookingWorkflowAction]] = {
    workflowDBProxy.getBaseBookingActionBySupplierBookingId(supplierBookingId, topicId, uuid)
  }

  override def updateBookingActionMessage(messageId: Long, content: String): Future[Int] = {
    workflowDBProxy.updateBookingActionMessage(messageId, content)
  }

  override def getBookingActionIdentifierByBookingIdAndTopicId(
      bookingId: FlightBookingId,
      topicId: TopicId,
      recCreatedBy: String
  ): Future[Option[BookingActionIdentifier]] = {
    workflowDBProxy.getBookingActionIdentifierByBookingIdAndTopicId(bookingId, topicId, recCreatedBy)
  }
}
