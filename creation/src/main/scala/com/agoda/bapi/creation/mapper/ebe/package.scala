package com.agoda.bapi.creation.mapper

import com.agoda.mpb.common.models.state.ProductType

package object ebe {

  object RecStatus {
    val Active: Int   = 1
    val Inactive: Int = 0
    val Deleted: Int  = -1
  }

  object WorkflowActionId {
    val Default: Int = 1
  }

  object StateSchemaVersion {
    val Default: Int = 1
  }

  object WorkFlowStateId {
    val Master: Int       = 0
    val Flight: Int       = 1001
    val Vehicle: Int      = 3001
    val Activity: Int     = 4001
    val PriceFreeze: Int  = 6001
    val CegFastTrack: Int = 7001

    val ProvisioningCancellationInitialized: Int = 2550
  }

  object BookingSettingType {
    val BoRSupplier: Int = 1
  }

  object ProvisioningMethodId {
    val CreateBooking: Int = 1
  }

  object HotelPaymentMethodId {
    val Default: Int = 0
  }

  object AttributionModelId {
    val FESession: Int = 19
    val LPC30: Int     = 20
  }

  object FlightSeatStatus {
    val Processing: Int     = 0
    val Received: Int       = 1
    val Confirmed: Int      = 2
    val Rejected: Int       = 3
    val TechnicalError: Int = 4
  }

  object FlightAddOnState {
    val Processing: Int     = 0
    val Received: Int       = 1
    val Confirmed: Int      = 2
    val Rejected: Int       = 3
    val TechnicalError: Int = 4
  }

  object SupplierId {
    val PCLN: Int = 30001
    val Kiwi: Int = 30010
  }

  class RoomTypeNotFound() extends Exception("EBERoomType object not found")

  class HotelInfoNotFound() extends Exception("hotel information for create booking not found")

  class USDCurrencyInfoNotFound() extends Exception("usd currency info not found")

  class EBEHotelNotFound() extends Exception("EBEHotel object not found")

  class EBEBookingRoomNotFound() extends Exception("EBEBookingRoom object not found")

  class EBEChildRoomNotFound() extends Exception("EBEChildRoom object not found")

  class PaymentModelNotFound() extends Exception("PaymentModel is unknown")

  class AvailabilityTypeNotFound() extends Exception("AvailabilityType is unknown")

  class PaymentAmountNotFound() extends Exception("PaymentAmount not found")
}
