package com.agoda.bapi.creation.util

import com.agoda.bapi.common.model.creation.PropertyCampaignInfo
import com.agoda.bapi.common.model.flight.flightModel.BookingPaymentState
import com.agoda.bapi.common.model.multiproduct.MultiProductBookingGroupDBModel
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.product.BookingFlow.BookingFlow
import com.agoda.bapi.common.model.product.BookingRequestTypeResolver.MultiProductFlowDefinition
import com.agoda.bapi.creation.model.MultiProduct
import com.agoda.bapi.creation.model.multi.MultiProductsRequest
import com.agoda.mpb.common.MultiProductType
import com.agoda.mpb.common.MultiProductType.MultiProductType
import com.agoda.mpb.common.models.state.ProductType
import models.starfruit.LocalVoucher

object MultiProductUtils {
  def getMultiProductType(
      bookingFlow: BookingFlow,
      isMigrateFP2Cart: Boolean,
      isMigrateHF2Cart: Boolean
  )(implicit mpDefinition: MultiProductFlowDefinition): MultiProductType = {
    bookingFlow match {
      case BookingFlow.SingleProperty if mpDefinition.numberOfCegFastTrack > 0 || mpDefinition.numberOfAddOns > 0 =>
        MultiProductType.Cart
      case BookingFlow.SingleProperty             => MultiProductType.SingleProperty
      case BookingFlow.SingleFlight               => MultiProductType.SingleFlight
      case BookingFlow.SingleVehicle              => MultiProductType.SingleVehicle
      case BookingFlow.MixAndSave                 => MultiProductType.MixAndSave
      case BookingFlow.Hackerfare                 => MultiProductType.HackerFare
      case BookingFlow.FlightWithProtection       => MultiProductType.FlightWithProtection
      case BookingFlow.Package                    => MultiProductType.Package
      case BookingFlow.MultiHotel                 => MultiProductType.MultiProperties
      case BookingFlow.MultiFlightsWithProtection => MultiProductType.MultiFlightsWithProtection
      case BookingFlow.SingleActivity             => MultiProductType.SingleActivity
      case BookingFlow.Cart                       => getCartMultiProductType(isMigrateFP2Cart, isMigrateHF2Cart)
      case _                                      => MultiProductType.None
    }
  }

  def buildMultiProductFlowDefinition(
      multiProductsRequest: MultiProductsRequest
  ): MultiProductFlowDefinition = {
    MultiProductFlowDefinition(
      numberOfProperty = multiProductsRequest.properties.size,
      numberOfFlight = multiProductsRequest.flights.size,
      numberOfCar = multiProductsRequest.vehicles.size,
      numberOfActivity = multiProductsRequest.activities.size,
      numberOfTripProtection = multiProductsRequest.protections.size + multiProductsRequest.addOns.count(
        _.info.getProductType == ProductType.TripProtection
      ),
      numberOfCegFastTrack = multiProductsRequest.cegFastTracks.size,
      numberOfAddOns = multiProductsRequest.addOns.size
    )
  }

  private def getCartMultiProductType(isMigrateToCart: Boolean, isMigrateHF2Cart: Boolean)(implicit
      mpDefinition: MultiProductFlowDefinition
  ): MultiProductType = {
    mpDefinition match {
      case MultiProductFlowDefinition.singlePropertyFlowDefinition       => MultiProductType.SingleProperty
      case MultiProductFlowDefinition.singleFlightFlowDefinition         => MultiProductType.SingleFlight
      case MultiProductFlowDefinition.singleTripProtectionFlowDefinition => MultiProductType.SingleProtection
      case MultiProductFlowDefinition.singleVehicleFlowDefinition        => MultiProductType.SingleVehicle
      case MultiProductFlowDefinition.singleActivityFlowDefinition       => MultiProductType.SingleActivity
      case MultiProductFlowDefinition.hackerFareDefinition if isMigrateToCart && !isMigrateHF2Cart =>
        MultiProductType.HackerFare
      case MultiProductFlowDefinition.multiFlightsWithProtection if isMigrateToCart && !isMigrateHF2Cart =>
        MultiProductType.MultiFlightsWithProtection
      case MultiProductFlowDefinition.emptyProducts => MultiProductType.None
      // Note: keep below deconstructed to signify handling for future product additions
      case MultiProductFlowDefinition(_, _, _, _, _, _, _, _) => MultiProductType.Cart // more than 1 item in cart
    }
  }

  def isMultiProductItineraryFromSetState(
      bookingPayments: Seq[BookingPaymentState],
      multiProductBookingGroups: Seq[MultiProductBookingGroupDBModel]
  ): Boolean = {
    bookingPayments.map(_.bookingId).distinct.size > 1 || multiProductBookingGroups
      .map(_.bookingId)
      .distinct
      .size > 1 // sometime bookingPayments is existed in set state request and sometime multiProductBookingGroups is existed but bookingPayments is not

  }

  def getLocalVoucher(products: MultiProduct): Option[LocalVoucher] = {
    products.properties.headOption.flatMap(_.bapiBooking.campaignInfo.flatMap(_.localVoucher))
  }

  def getPropertyCampaignInfo(products: MultiProduct): Option[PropertyCampaignInfo] = {
    products.properties.headOption.flatMap(_.bapiBooking.campaignInfo)
  }
}
