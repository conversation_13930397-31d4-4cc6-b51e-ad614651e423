package com.agoda.bapi.creation.service.stage

import com.agoda.bapi.common.config.KillSwitches
import com.agoda.bapi.common.constants.RebookAndCancelConstants
import com.agoda.bapi.common.handler.{MeasurementsContext, RequestContext}
import com.agoda.bapi.common.logging.PreSaveStageLog
import com.agoda.bapi.common.message.creation.{CreateBookingResponse, CreditCard}
import com.agoda.bapi.common.model.flight.flightModel.ItineraryHistory
import com.agoda.bapi.common.model.itinerary.ItineraryInternalModel
import com.agoda.bapi.common.model.multiproduct.{MultiProductBookingGroupDBModel, MultiProductInfoDBModel}
import com.agoda.bapi.common.model.product.ProductTypeEnum.ProductTypeEnum
import com.agoda.bapi.common.model.product.{BookingFlow, BookingRequestTypeResolver, ProductTypeEnum}
import com.agoda.bapi.common.model.rebookAndCancel.RebookAndCancelData
import com.agoda.bapi.common.model.relationship.RelationshipTypes
import com.agoda.bapi.common.model.{ActionId, CartId, ItineraryId, PackageId, PaymentMethodFromDB, StatusToken}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.util.CollectionUtils._
import com.agoda.bapi.common.util.ServerUtils
import com.agoda.bapi.creation.mapper.BookingRelationshipHelper
import com.agoda.bapi.creation.mapper.ebe.MPMasterMapper
import com.agoda.bapi.creation.model.db.{BookingActionState, MultiProductBookingInsertionModel}
import com.agoda.bapi.creation.model.multi._
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.bapi.creation.model.multi
import com.agoda.bapi.creation.proxy.CreditCardApiLocalProxyV2
import com.agoda.bapi.creation.repository.{FlightBookingRepository, MultiProductRepository, PaymentMethodRepository}
import com.agoda.bapi.creation.service.flow.ProcessStage
import com.agoda.bapi.creation.service.stage.presave.{MultiProductPreSaveRequest, NonHotelPreSaveFacade, ProductPreSaveStage}
import com.agoda.bapi.creation.service.HadoopMessagingService
import com.agoda.bapi.creation.util.{StatusTokenUtils, ToolSet, WhitelabelUtils}
import com.agoda.bapi.execution.context.NonBlockingExecutionContext.default
import com.agoda.common.itineraryContext.{ItineraryContext, ProductGroupItem, ProductGroupType}
import com.agoda.creditcardapi.client.v2.common.model.AddTransientCCResponse
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpbe.state.booking.BaseBookingRelationship
import com.softwaremill.quicklens._

import scala.concurrent.Future

class MultiProductPreSaveStage(
    paymentMethodRepository: PaymentMethodRepository,
    flightBookingRepository: FlightBookingRepository,
    multiProductRepository: MultiProductRepository,
    itineraryMapper: MPMasterMapper,
    hotelPreSaveProcess: ProductPreSaveStage[
      MultiProductPreSaveRequest,
      RoomInfo,
      PropertySaveStageResponse,
      EmptyProductReservedIds
    ],
    nonHotelPreSaveFacade: NonHotelPreSaveFacade,
    creditCardLocalProxy: CreditCardApiLocalProxyV2,
    hadoopMessaging: HadoopMessagingService,
    relationshipMapper: BookingRelationshipHelper
)(implicit killSwitches: KillSwitches, requestContext: RequestContext)
    extends ProcessStage[MultiProductsRequest, MultiProductSaveStageRequest, CreateBookingResponse]
    with ToolSet {

  override def process(
      request: MultiProductsRequest
  ): Future[Either[CreateBookingResponse, MultiProductSaveStageRequest]] = {
    implicit val measurementCxt: MeasurementsContext = request.measurementsContext
    implicit val requestContext: RequestContext      = request.requestContext
    withMeasureAndLogWithContext(measurementCxt)(creationMeasurementName("PreSaveStage")) {
      (for {
        paymentMethodInfo <- paymentMethodRepository.getPaymentMethodInfo(
                               request.request.payment.method.value,
                               BookingRequestTypeResolver.getProductType(request.request).id,
                               request.requestContext.whiteLabelInfo.whiteLabelId.id
                             )
        itineraryId <- getNextItinerarySequenceNumber(request)
        cartId      <- multiProductRepository.getNextCartSequenceNumber()
        actionId    <- flightBookingRepository.getNextActionIdSequenceNumber
        packageId <- if (request.bookingFlow == BookingFlow.Package || request.bookingFlow == BookingFlow.MultiHotel)
                       multiProductRepository.getNextPackageSequenceNumber().map(packageId => Some(packageId))
                     else Future.successful(None)
        operationId <- multiProductRepository.getNextOperationIdSequenceNumber().map(Option(_))
        productInfos = request.getAllProductInfo

        multiProductIds <- generateMultiProductIdMapping(productInfos)
        history <- if (killSwitches.enableRebookAndCancelFlow && request.rebookAndCancelData.isDefined)
                     getItineraryActionHistory(itineraryId)
                   else Future.successful(Seq.empty)
        itineraryPreSaveInfo =
          createItineraryPreSaveInfo(
            request,
            itineraryId,
            actionId,
            cartId,
            packageId,
            operationId,
            history
          )
        requestWithItineraryId =
          request.copy(requestContext =
            request.requestContext.copy(bookingCreationContext =
              request.requestContext.bookingCreationContext.map(_.copy(itineraryId = Option(itineraryId)))
            )
          )
        preSaveRequest       = MultiProductPreSaveRequest(requestWithItineraryId, itineraryPreSaveInfo, multiProductIds)
        nonHotelProducts    <- nonHotelPreSaveFacade.preSave(preSaveRequest)
        hotelPreSaveResults <- hotelPreSaveProcess.process(preSaveRequest)

        addTransientCCResponse <- if (
                                    requestWithItineraryId.request.payment.ccToken.nonEmpty &&
                                    requestContext.featureAware.exists(_.enableCCTokenInBookingActionState)
                                  )
                                    Future.successful(
                                      AddTransientCCResponse(
                                        CCId = Some(0),
                                        StatusCode = Some("Success"),
                                        Message = Some("Skip TransientCCAPI because of BCT-667 = B")
                                      )
                                    )
                                  else {
                                    creditCardLocalProxy.addTransientCreditCard(
                                      requestWithItineraryId.request.payment.creditCard,
                                      request.isMixedPaymentModel,
                                      requestWithItineraryId.request.payment.ccToken
                                    )
                                  }

        bookingRelationships <-
          constructBookingRelationships(hotelPreSaveResults, nonHotelProducts, request.rebookAndCancelData)

      } yield gatherPreSaveResults(
        request,
        itineraryPreSaveInfo,
        paymentMethodInfo,
        multiProductIds,
        hotelPreSaveResults,
        nonHotelProducts,
        addTransientCCResponse,
        request.requestContext.featureAware,
        bookingRelationships
      ) match {
        case Right(success) =>
          Right(success)
        case Left(failed) =>
          hadoopMessaging.sendBapiCreateFactLogMessage(requestWithItineraryId, failed, "MultiProductPreSaveStage")
          Left(failed)
      }).recover {
        case exception: Exception =>
          logError(exception)
          // todo; have to clarify with error code we gonna used for each stage
          val recoveredResponse = CreateBookingResponse.technical(ErrorCode.UnexpectedInternalError, Some(exception))
          hadoopMessaging.sendBapiCreateFactLogMessage(request, recoveredResponse, "MultiProductPreSaveStage")
          Left(recoveredResponse)
      }
    }
  }

  private[stage] def getItineraryActionHistory(itineraryId: ItineraryId): Future[Seq[ItineraryHistory]] = {
    flightBookingRepository.getItineraryActionHistory(itineraryId = itineraryId)
  }

  private[stage] def createItineraryPreSaveInfo(
      request: MultiProductsRequest,
      itineraryId: ItineraryId,
      actionId: ActionId,
      cartId: CartId,
      packageId: Option[PackageId] = None,
      operationId: Option[Long] = None,
      oldHistory: Seq[ItineraryHistory] = Seq.empty
  ): ItineraryPreSaveInfo = {
    val hotelEBEList =
      request.properties.flatMap(_.info.bapiBooking.booking.flatMap(_.booking.headOption.flatMap(_.hotel.headOption)))
    val tripStart = hotelEBEList.map(_.checkIn).minOptionBy(_.getMillis).map(_.withMillisOfSecond(0))
    val tripEnd   = hotelEBEList.map(_.checkOut).maxOptionBy(_.getMillis).map(_.withMillisOfSecond(0))

    ItineraryPreSaveInfo(
      itineraryId = itineraryId,
      actionId = actionId,
      tripStart = tripStart,
      tripEnd = tripEnd,
      cartId = cartId,
      packageId = packageId,
      operationId = operationId,
      oldHistory = oldHistory
    )
  }

  private[stage] def generateMultiProductIdMapping(
      products: Seq[multi.Product[_]]
  )(implicit measurementCxt: MeasurementsContext): Future[Map[MultiProductIdentifier, MultiProductInfoDBModel]] = {
    withMeasureAndLogWithContext(measurementCxt)(creationMeasurementName("GenerateMultiProductIdMapping")) {
      val multiProductTypeIdentifiers = products
        .groupBy(x => x.getMultiProductIdentifier)
        .flatMap {
          case (key, product) => key.flatMap(_ => product.headOption.flatMap(_.multiProductType).map((key, _)))
        }
      Future
        .traverse(multiProductTypeIdentifiers.toList) {
          case (key, multiProductType) =>
            multiProductRepository
              .getNextMultiBookingSequenceNumber()
              .map(multiProductId => (key, MultiProductInfoDBModel(multiProductId, multiProductType)))
        }
        .map(_.toMap)
    }
  }

  private[stage] def constructBookingRelationships(
      propertyProductsE: Either[CreateBookingResponse, Seq[PropertySaveStageResponse]],
      nonPropertyProductsE: Either[CreateBookingResponse, Seq[NonPropertySaveStageResponse]],
      rebookAndCancelData: Option[RebookAndCancelData]
  ): Future[Seq[BaseBookingRelationship]] = {
    (for {
      propertyProducts    <- propertyProductsE
      nonPropertyProducts <- nonPropertyProductsE
    } yield {
      val totalProductSaveStageResponses = propertyProducts ++ nonPropertyProducts
      val addOnRelationshipsWithoutId    = relationshipMapper.constructAddOnRelationships(totalProductSaveStageResponses)
      val rebookAndCancelRelationshipsWithoutId =
        relationshipMapper.constructRebookAndCancelRelationships(totalProductSaveStageResponses, rebookAndCancelData)

      if (rebookAndCancelRelationshipsWithoutId.nonEmpty)
        withMeasure(RebookAndCancelConstants.relationshipCreatedMetric, 1)

      val relationshipsWithoutId = addOnRelationshipsWithoutId ++ rebookAndCancelRelationshipsWithoutId
      val nextRelationshipIdsF =
        multiProductRepository.getNextBaseBookingRelationshipIdSequenceNumbers(relationshipsWithoutId.size)
      nextRelationshipIdsF.map { nextRelationshipIds =>
        {
          relationshipsWithoutId.zip(nextRelationshipIds).map {
            case (relationship, nextRelationshipId) => relationship.copy(relationshipId = nextRelationshipId)
          }
        }
      }
    }).left.map(_ => Future.successful(Seq.empty)).merge
  }

  private[stage] def gatherPreSaveResults(
      request: MultiProductsRequest,
      itineraryPreSaveInfo: ItineraryPreSaveInfo,
      paymentMethodInfo: Option[PaymentMethodFromDB],
      multiProductIds: Map[MultiProductIdentifier, MultiProductInfoDBModel],
      propertyProductsE: Either[CreateBookingResponse, Seq[PropertySaveStageResponse]],
      nonPropertyProductsE: Either[CreateBookingResponse, Seq[NonPropertySaveStageResponse]],
      addTransientCCResponse: AddTransientCCResponse,
      featureAware: Option[FeatureAware],
      bookingRelationships: Seq[BaseBookingRelationship]
  ): Either[CreateBookingResponse, MultiProductSaveStageRequest] = {
    for {
      nonHotelProducts <- nonPropertyProductsE
      propertyProducts <- propertyProductsE
    } yield {
      val productActions = propertyProducts.map(_.bookingAction) ++ nonHotelProducts.map(_.bookingAction)

      val productBookingIdList = productActions.flatMap(_.bookingId)
      val multiProductBookingGroups: Seq[MultiProductBookingGroupDBModel] =
        itineraryMapper.getMultiProductBookingGroupDBModel(itineraryPreSaveInfo, productBookingIdList)
      val itineraryModel: ItineraryInternalModel = itineraryMapper.mapItineraryDbModel(
        request = request,
        itineraryId = itineraryPreSaveInfo.itineraryId,
        actionId = itineraryPreSaveInfo.actionId,
        transientCCId = addTransientCCResponse.CCId,
        relationships = bookingRelationships,
        multiProductBookingGroups = multiProductBookingGroups,
        ccToken =
          if (requestContext.featureAware.exists(_.enableCCTokenInBookingActionState))
            request.request.payment.ccToken
          else
            None,
        oldHistory = itineraryPreSaveInfo.oldHistory
      )

      val isRetrieveCCInfoAfterFraud =
        WhitelabelUtils.isRetrieveCcInfoAfterFraud()(Some(request.requestContext.whiteLabelInfo))
      val isPartialData = isRetrieveCCInfoAfterFraud
      log(
        PreSaveStageLog(
          s"[gatherPreSaveResults] transientCCResponse: ${addTransientCCResponse.CCId}, transientCCMessage: ${addTransientCCResponse.Message}, transientCCStatusCode: ${addTransientCCResponse.StatusCode}, isRetrieveCCInfoAfterFraud: $isRetrieveCCInfoAfterFraud, isPartialData: $isPartialData",
          itineraryPreSaveInfo.itineraryId.toString
        )
      )

      val whiteLabelId = request.requestContext.whiteLabelInfo.whiteLabelId

      val productTypes =
        nonHotelProducts.map(_.productType.toString).toSet ++ propertyProducts.map(_.productType.toString).toSet

      val topic = StatusTokenUtils.getBookingActionMessageTopic(whiteLabelId)(request.requestContext)

      val (version, operationId) =
        StatusTokenUtils.getStatusTokenVersionAndOperationId(itineraryPreSaveInfo.operationId)

      val statusToken = StatusToken(
        itineraryId = itineraryPreSaveInfo.itineraryId,
        actionId = itineraryPreSaveInfo.actionId,
        productType = productTypes,
        dc = ServerUtils.serverDc(),
        whitelabelId = Some(whiteLabelId.id),
        topic = topic,
        version = Some(version),
        operationId = operationId
      )

      val updatedItineraryContext =
        request.itineraryContext.map(updatedItineraryContextWithSaveResult(_, propertyProducts, nonHotelProducts))

      val itineraryBookingAction = itineraryMapper.mapBookingAction(
        request,
        itineraryPreSaveInfo.itineraryId,
        itineraryPreSaveInfo.actionId,
        itineraryModel,
        paymentMethodInfo,
        statusToken,
        featureAware,
        updatedItineraryContext
      )

      val allActions = Seq(itineraryBookingAction) ++ productActions

      val updatedAllActions =
        allActions.map(
          _.copy(
            whiteLabelId = Some(whiteLabelId.id),
            operationId = itineraryPreSaveInfo.operationId
          )
        )

      val nonPropertySaveStageResponseByProductType = nonHotelProducts.groupBy(_.productType)

      val oldProtectionBookingActionStates =
        getBookingActionState(nonPropertySaveStageResponseByProductType, ProductTypeEnum.Protection).filter(
          _.tripProtectionBookingState.nonEmpty
        )
      val addOnFromOldFlow = oldProtectionBookingActionStates
      val multiProductSaveStageRequest = MultiProductSaveStageRequest(
        request = request.request,
        requestContext = request.requestContext,
        saveBookingModel = MultiProductBookingInsertionModel(
          multiProductsInfo = multiProductIds.values.toSeq,
          workflowActions = updatedAllActions,
          itineraryModel = itineraryModel,
          flightBookingActionStates =
            getBookingActionState(nonPropertySaveStageResponseByProductType, ProductTypeEnum.Flight),
          propertyModels = propertyProducts.map(_.propertyCreationLocal),
          vehicleBookingActionStates =
            getBookingActionState(nonPropertySaveStageResponseByProductType, ProductTypeEnum.Car),
          protectionBookingActionStates = oldProtectionBookingActionStates,
          activityBookingActionState =
            getBookingActionState(nonPropertySaveStageResponseByProductType, ProductTypeEnum.Activity),
          cegFastTrackBookingActionState =
            if (isCegUpsellMigrationEnabled(request)) Seq.empty
            else getBookingActionState(nonPropertySaveStageResponseByProductType, ProductTypeEnum.CegFastTrack),
          addOnsBookingActionState = getBookingActionStateUsingGenericAddOnFlow(
            nonPropertySaveStageResponseByProductType,
            addOnFromOldFlow,
            isCegUpsellMigrationEnabled(request)
          ),
          multiProductBookingGroups = multiProductBookingGroups,
          operationId = itineraryPreSaveInfo.operationId
        ),
        statusToken = statusToken
      )
      multiProductSaveStageRequest
    }
  }

  private def isCegUpsellMigrationEnabled(request: MultiProductsRequest)(implicit requestContext: RequestContext) = {
    val hasFlights  = request.flights.nonEmpty
    val hasProperty = request.properties.nonEmpty
    (hasProperty && requestContext.featureAware.exists(
      _.cegFastTrackMigrationCreateFlow
    ) && request.cegFastTracks.isEmpty) ||
    (hasFlights && requestContext.featureAware.exists(_.isCEGUpsellAddOnV2Enabled))
  }

  private def getBookingActionStateUsingGenericAddOnFlow(
      nonPropertySaveStageResponseByProductType: Map[ProductTypeEnum, Seq[NonPropertySaveStageResponse]],
      addOnFromOldFlow: Seq[BookingActionState],
      isCegUpsellMigrationEnabled: Boolean
  ): Seq[BookingActionState] = {
    val baseTypesUsingGenericAddOnFlow = Seq(ProductTypeEnum.Protection)
    val cegUpSell                      = if (isCegUpsellMigrationEnabled) Seq(ProductTypeEnum.CegFastTrack) else Seq.empty
    val checkIn =
      if (requestContext.featureAware.exists(_.EnableOnboardCheckIn)) Seq(ProductTypeEnum.FlightCheckIn) else Seq.empty
    val productTypeUsingGenericAddOnFlow = baseTypesUsingGenericAddOnFlow ++ cegUpSell ++ checkIn

    val allAddOnBookingActionState = productTypeUsingGenericAddOnFlow
      .map(getBookingActionState(nonPropertySaveStageResponseByProductType, _))
      .flatMap(_.value) // should return all protection actions
    allAddOnBookingActionState.filterNot(
      addOnFromOldFlow.contains(_)
    ) // filter out old protection action (the one that uses protection internal model)
  }

  private def getBookingActionState(
      nonPropertySaveStageResponseByProductType: Map[ProductTypeEnum, Seq[NonPropertySaveStageResponse]],
      productTypeEnum: ProductTypeEnum
  ): Seq[BookingActionState] =
    nonPropertySaveStageResponseByProductType.getOrElse(productTypeEnum, Seq.empty).map(_.bookingActionState)

  private[stage] def getNextItinerarySequenceNumber(
      request: MultiProductsRequest
  ): Future[ItineraryId] = {

    def getCancelAndRebookItineraryId: Option[ItineraryId] =
      request.properties.headOption
        .flatMap(_.info.bapiBooking.consumerFintechDetails)
        .flatMap(_.products.cancelAndRebookV3)
        .map(_.originalItineraryId)

    def getRebookAndCancelItineraryId: Option[ItineraryId] =
      request.rebookAndCancelData.map(_.originalItineraryId)

    val overriddenItineraryIdOpt: Option[ItineraryId] = getCancelAndRebookItineraryId
      .orElse(getRebookAndCancelItineraryId)

    overriddenItineraryIdOpt
      .map(Future.successful)
      .getOrElse(
        flightBookingRepository.getNextItinerarySequenceNumber
      )
  }

  private[stage] def updatedItineraryContextWithSaveResult(
      itineraryContext: ItineraryContext,
      propertySaveResults: Seq[PropertySaveStageResponse],
      nonPropertySaveResults: Seq[NonPropertySaveStageResponse]
  ): ItineraryContext = {
    val groupsWithBookingIds =
      injectProductGroupsWithBookingId(itineraryContext.productGroups, propertySaveResults, nonPropertySaveResults)

    itineraryContext.copy(productGroups = groupsWithBookingIds)
  }

  private def injectProductGroupsWithBookingId(
      productGroups: Seq[ProductGroupItem],
      propertySaveResults: Seq[PropertySaveStageResponse],
      nonPropertySaveResults: Seq[NonPropertySaveStageResponse]
  ): Seq[ProductGroupItem] = {
    val normalizedResults = (propertySaveResults ++ nonPropertySaveResults).collect {
      case item: NonPropertySaveStageResponse =>
        (item.productTokenKey.getOrElse("unknown"), item.bookingAction.bookingId)
      case item: PropertySaveStageResponse =>
        (
          item.propertyCreationLocal.reservedIds.product.info.roomIdentifier.getOrElse("unknown"),
          item.bookingAction.bookingId
        )
    }

    productGroups.foldLeft(Seq.empty[ProductGroupItem])((updatedGroups, productGroupItem) => {
      val hackerFareBookingIds =
        updatedGroups.filter(_.productGroupType == ProductGroupType.HackerFare).flatMap(_.bookingId)
      val item = normalizedResults.find(pidToBid => {
        val hasProductId             = pidToBid._1 == productGroupItem.productId
        val bookingIdIsNotDuplicated = !pidToBid._2.exists(hackerFareBookingIds.contains(_))
        hasProductId && bookingIdIsNotDuplicated
      })
      val bookingId = item.flatMap(_._2)
      updatedGroups :+ ProductGroupItem.of(
        productGroupItem.productId,
        bookingId,
        productGroupItem.productGroupId,
        productGroupItem.productGroupType
      )
    })
  }
}
