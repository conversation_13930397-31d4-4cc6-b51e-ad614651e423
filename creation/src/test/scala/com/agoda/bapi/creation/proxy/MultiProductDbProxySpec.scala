package com.agoda.bapi.creation.proxy

import com.agoda.bapi.common.config.KillSwitches
import com.agoda.bapi.common.database.AGDBStub
import com.agoda.bapi.common.db.execution.context.BFDBExecutionContext
import com.agoda.bapi.common.exception.DbException
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.model.{BFDBExecutionContext, OperationActionTypes}
import com.agoda.bapi.common.model.booking.local.{BookingWorkflowAction, WorkflowItineraryOperation}
import com.agoda.bapi.common.model.car.VehicleBookingStateModel.VehicleModelInternal
import com.agoda.bapi.common.model.flight.flightModel.FlightModelInternal
import com.agoda.bapi.common.model.itinerary.ItineraryInternalModel
import com.agoda.bapi.common.model.multiproduct.{MultiProductBookingGroupDBModel, MultiProductInfoDBModel}
import com.agoda.bapi.common.model.protection.ProtectionBookingStateModel.{ProtectionCfar, ProtectionModelInternal}
import com.agoda.bapi.common.proxy.{DBConnectionGroup, DependencyNames, ResultSetStub}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.util.converters.ItineraryStateConverters._
import com.agoda.mpb.common.WorkflowId
import com.agoda.bapi.creation.model.db._
import com.agoda.bapi.creation.{DBProxyTestHelper, WithMetricsCapture}
import com.agoda.mpb.common.MultiProductType
import com.agoda.mpb.common.models.state.ProductType
import com.agoda.mpbe.state.booking.BaseProductModel
import mocks.DBBookingModelHelper
import org.mockito.AdditionalAnswers.returnsFirstArg
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.{any, argThat, eq => argsEq}
import org.mockito.Mockito._
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec

import java.sql.{Connection, ResultSet, SQLException}

class MultiProductDbProxySpec extends AsyncWordSpec with Matchers with DBBookingModelHelper {

  "getNextMultiBookingSequenceNumber" should {
    val expectedSeqId = 100000000000000010L
    val expectedData: Array[Map[String, Any]] = Array(
      Map("1" -> expectedSeqId)
    )
    "return next multiproduct sequence id" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_next_multi_product_id_v1"
        val sqlQuery      = s"EXEC dbo.$queryName"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.getNextMultiProductSequenceNumber.map { results =>
        verifyInteractionsWithUnderlyingDb()
        results shouldBe expectedSeqId
      }
    }
  }

  "getMultiProductInfo" should {
    val multiProductId = 200002L
    val productInfoDbModel =
      MultiProductInfoDBModel(multiProductId, multiProductType = MultiProductType.Package)
    val expectedData: Array[Map[String, Any]] = Array(
      Map(
        "multi_product_id"      -> productInfoDbModel.multiProductId,
        "multi_product_type_id" -> productInfoDbModel.multiProductType.id
      )
    )

    "return the product type correctly" in {
      val fixture = new Fixture {
        val queryName     = "bcre_get_multi_product_info_by_multi_product_id_v1"
        val sqlQuery      = s"EXEC dbo.$queryName @multiProductId = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.getMultiProductInfo(multiProductId).map { results =>
        verifyInteractionsWithUnderlyingDb()
        verify(mockPreparedStatement).setObject(1, multiProductId)
        results shouldBe Some(productInfoDbModel)
      }
    }
  }

  "getMultiProductBookingGroup" should {
    val multiProductIngoDbModel =
      MultiProductBookingGroupDBModel(bookingId = 11L, itineraryId = 12L, cartId = 1L, packageId = Some(5L))
    val expectedData: Array[Map[String, Any]] = Array(
      Map(
        "booking_id"   -> multiProductIngoDbModel.bookingId,
        "itinerary_id" -> multiProductIngoDbModel.itineraryId,
        "cart_id"      -> multiProductIngoDbModel.cartId,
        "package_id"   -> multiProductIngoDbModel.packageId.get
      )
    )

    "call SP correctly and return MultiProductBookingGroupDBModel correctly" in {
      val fixture = new Fixture {
        val queryName     = "bcre_get_multi_product_booking_group_v1"
        val sqlQuery      = s"EXEC dbo.$queryName @bookingId = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.getMultiProductBookingGroup(multiProductIngoDbModel.bookingId).map { results =>
        verifyInteractionsWithUnderlyingDb()
        verify(mockPreparedStatement).setObject(1, multiProductIngoDbModel.bookingId)
        results shouldBe Some(multiProductIngoDbModel)
      }
    }
  }

  "insertMultiProductInfo" should {
    val mockMultiProductInfoDBModel = MultiProductInfoDBModel(1L, MultiProductType.Package)
    val expectedData: Array[Map[String, Any]] = Array(
      Map(
        "multi_product_id"      -> mockMultiProductInfoDBModel.multiProductId,
        "multi_product_type_id" -> mockMultiProductInfoDBModel.multiProductType.id
      )
    )

    "return multi product info" in {
      val fixture = new Fixture {
        val queryName     = "multi_product_info_insert_v1"
        val sqlQuery      = s"""{ call dbo.$queryName (?,?) }"""
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.insertMultiProductInfo(mockMultiProductInfoDBModel).map { results =>
        verifyInteractionsWithUnderlyingDb(true)
        verify(mockCallableStatement).setLong(1, mockMultiProductInfoDBModel.multiProductId)
        verify(mockCallableStatement).setInt(2, mockMultiProductInfoDBModel.multiProductType.id)
        results shouldBe mockMultiProductInfoDBModel
      }

    }
  }
  "insertMultiProductBookingGroup" should {
    val mockMultiProductBookingGroupDBModel = MultiProductBookingGroupDBModel(
      bookingId = 11L,
      itineraryId = 12L,
      cartId = 1L,
      packageId = Some(5L)
    )
    val expectedData: Array[Map[String, Any]] = Array(
      Map(
        "booking_id"   -> mockMultiProductBookingGroupDBModel.bookingId,
        "itinerary_id" -> mockMultiProductBookingGroupDBModel.itineraryId,
        "cart_id"      -> mockMultiProductBookingGroupDBModel.cartId,
        "package_id"   -> mockMultiProductBookingGroupDBModel.packageId.get
      )
    )

    "return multi product booking group" in {
      val fixture = new Fixture {
        val queryName     = "multi_product_booking_grouping_insert_v1"
        val sqlQuery      = s"""{ call dbo.$queryName (?,?,?,?) }"""
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.insertMultiProductBookingGroup(mockMultiProductBookingGroupDBModel).map { results =>
        verifyInteractionsWithUnderlyingDb(true)
        verify(mockCallableStatement).setLong(1, mockMultiProductBookingGroupDBModel.bookingId)
        verify(mockCallableStatement).setLong(2, mockMultiProductBookingGroupDBModel.itineraryId)
        verify(mockCallableStatement).setLong(3, mockMultiProductBookingGroupDBModel.cartId)
        verify(mockCallableStatement).setLong(4, mockMultiProductBookingGroupDBModel.packageId.get)
        results shouldBe mockMultiProductBookingGroupDBModel
      }

    }
  }

  "insertMultiBooking" should {
    val mockCreationRequest            = mock(classOf[CreationRequest])
    val mockBookingActionStateCustomer = mock(classOf[BookingActionStateCustomer])
    val mockPaymentInfo                = mock(classOf[PaymentInfo])
    val mockFlightInternal             = mockFlightModelInternal

    val mockFlightBookingActionState = BookingActionState(
      mockCreationRequest,
      mockBookingActionStateCustomer,
      mockPaymentInfo,
      bookingState = Some(mockFlightInternal),
      campaignInfo = None
    )

    val mockActivityBookingActionState = BookingActionState(
      mockCreationRequest,
      mockBookingActionStateCustomer,
      mockPaymentInfo,
      bookingState = None,
      campaignInfo = None,
      itineraryProtoState = toItineraryState(mockActivityProductModel).toBase64OptString
    )

    val mockCegFastTrackBookingActionState = BookingActionState(
      mockCreationRequest,
      mockBookingActionStateCustomer,
      mockPaymentInfo,
      bookingState = None,
      campaignInfo = None,
      itineraryProtoState = toItineraryState(mockCegFastTrackProductModel).toBase64OptString
    )

    val mockProtectionAddOnBookingActionState = BookingActionState(
      mockCreationRequest,
      mockBookingActionStateCustomer,
      mockPaymentInfo,
      bookingState = None,
      campaignInfo = None,
      itineraryProtoState = toItineraryState(mockProtectionAddOnProductModel).toBase64OptString
    )

    val mockVehicleBookingActionState = BookingActionState(
      mockCreationRequest,
      mockBookingActionStateCustomer,
      mockPaymentInfo,
      bookingState = None,
      campaignInfo = None,
      vehicleBookingState = Some(mockVehicleModelInternal)
    )
    val mockProtectionBookingActionState = mock(classOf[BookingActionState])
    val insertModel = MultiProductBookingInsertionModel(
      multiProductsInfo = Seq(mockMultiProductInfo),
      workflowActions = Seq(
        mockBookingWorkflowAction,
        mockBookingWorkflowAction.copy(
          workflowId = WorkflowId.Flight.id,
          productTypeId = Some(ProductType.Flights.id),
          bookingId = Some(mockFlightModelInternal.bookingId),
          state = mockFlightBookingActionState.toJson
        ),
        mockBookingWorkflowAction.copy(
          workflowId = WorkflowId.Property.id,
          productTypeId = Some(ProductType.Hotel.id),
          bookingId = Some(mockNewPropertyBookingObjectForEbeLite.booking.bookingId)
        )
      ),
      itineraryModel = mockItineraryInternalModel,
      flightBookingActionStates = Seq(mockFlightBookingActionState),
      propertyModels = Seq(mockNewPropertyBookingObjectForEbeLite),
      vehicleBookingActionStates = Seq(mockVehicleBookingActionState),
      protectionBookingActionStates = Seq(mockProtectionBookingActionState),
      activityBookingActionState = Seq(mockActivityBookingActionState),
      cegFastTrackBookingActionState = Seq(mockCegFastTrackBookingActionState),
      addOnsBookingActionState = Seq(mockProtectionAddOnBookingActionState),
      multiProductBookingGroups = Seq(mockMultiProductBookingGroup)
    )

    def initInsertMultiProductBehavior(
        dbProxy: MultiProductDbProxyImpl,
        mockGenericProductActionDbProxy: GenericProductActionDbProxy
    ) = {
      doAnswer(_ => mockFlightBookingActionState.bookingState.get)
        .when(dbProxy)
        .insertFlightsWithoutItinerary(any(), any())(any())
      doAnswer(_ => mockVehicleBookingActionState.vehicleBookingState.get)
        .when(dbProxy)
        .insertVehicleWithoutItinerary(any())(any())
      doAnswer(_ => mockNewPropertyBookingObjectForEbeLite)
        .when(dbProxy)
        .insertPropertyCreationBookingTable(any())(any(), any())
      doAnswer(returnsFirstArg()).when(dbProxy).insertBookingAction(any())(any())
      doAnswer(returnsFirstArg()).when(mockGenericProductActionDbProxy).insertProductModelWithoutItinerary(any())(any())
      doAnswer(_ => mockMultiProductInfo).when(dbProxy).insertMultiProductInfoAction(any())(any())
      doAnswer(_ => mockMultiProductBookingGroup).when(dbProxy).insertMultiProductBookingGroupAction(any())(any())
      doAnswer(_ => mockItineraryInternalModel).when(dbProxy).insertItinerary(any())(any(), any())
      doAnswer(_ => mockProtectionBookingActionState.tripProtectionBookingState.get)
        .when(dbProxy)
        .upsertProtection(any())(any())
      when(
        mockProtectionBookingActionState.copy(
          tripProtectionBookingState = Some(mockNewProtectionModelInternal),
          itineraryProtoState = toItineraryState(mockNewProtectionModelInternal).toBase64OptString
        )
      ).thenReturn(mockProtectionBookingActionState)
      when(mockProtectionBookingActionState.tripProtectionBookingState).thenReturn(Some(mockNewProtectionModelInternal))
      doAnswer(_ => mockWorkflowItineraryOperation).when(dbProxy).insertItineraryOperation(any())(any())
    }

    "call function correctly when MPBE-4506 is A" in {
      val fixture = new InsertMultiProductFixtureTest {

        val queryName: String = "insertMultiProduct"

      }
      import fixture._
      initMockedBehaviour()
      initInsertMultiProductBehavior(dbProxy, mockGenericProductActionDbProxy)

      when(featureAware.stopBfdbReplication).thenReturn(false)

      dbProxy.insertMultiBooking(insertModel).map { result =>
        verifyFunctionCalled()
        verifyInteractionsWithUnderlyingDb()
        val expectedFlightStates = insertModel.flightBookingActionStates
          .map(_.copy(itineraryProtoState = toItineraryState(mockFlightInternal).toBase64OptString))

        val modifiedMockBookingWorkflowAction = mockBookingWorkflowAction.copy(
          operationId = Some(mockWorkflowItineraryOperation.operationId)
        )

        val expectedWorkflowActions = Seq(
          modifiedMockBookingWorkflowAction,
          modifiedMockBookingWorkflowAction.copy(
            workflowId = WorkflowId.Flight.id,
            productTypeId = Some(ProductType.Flights.id),
            bookingId = Some(mockFlightInternal.bookingId),
            state = expectedFlightStates.head.toJson
          ),
          modifiedMockBookingWorkflowAction.copy(
            workflowId = WorkflowId.Property.id,
            productTypeId = Some(ProductType.Hotel.id),
            bookingId = Some(mockNewPropertyBookingObjectForEbeLite.booking.bookingId)
          )
        )

        val actual = result.copy(
          //  Why? because we generate itineraryProtoState and it's not deterministic due to recCreatedWhen
          //  Remove this once https://github.agodadev.io/bookingapi/booking-creation/pull/186 will be merged
          vehicleBookingActionStates = Seq(mockVehicleBookingActionState),
          activityBookingActionState = Seq(mockActivityBookingActionState),
          protectionBookingActionStates = Seq(mockProtectionBookingActionState),
          cegFastTrackBookingActionState = Seq(mockCegFastTrackBookingActionState),
          addOnsBookingActionState = Seq(mockProtectionAddOnBookingActionState)
        )
        val expected = insertModel.copy(
          flightBookingActionStates = expectedFlightStates,
          workflowActions = expectedWorkflowActions,
          operationId = Some(mockWorkflowItineraryOperation.operationId)
        )
        actual shouldBe expected
      }
    }

    "only call functions to update BFDB temp and workaround tables when MPBE-4506 is B" in {
      val fixture = new InsertMultiProductFixtureTest {
        val queryName: String = "insertMultiProduct"
      }
      import fixture._
      initMockedBehaviour()
      initInsertMultiProductBehavior(dbProxy, mockGenericProductActionDbProxy)

      when(featureAware.stopBfdbReplication).thenReturn(true)

      dbProxy.insertMultiBooking(insertModel).map { actual =>
        verifyFunctionCalledWhenStopSavingBfdb()
        verifyInteractionsWithUnderlyingDb()

        actual shouldBe insertModel.copy(operationId = Some(mockWorkflowItineraryOperation.operationId))
      }
    }

    "call updateWorkflowActionByWorkflowId when MPBE-4506 is A and CFB-1292=A" in {
      val fixture = new InsertMultiProductFixtureTest {
        val queryName: String = "insertMultiProduct"
      }
      import fixture._
      initMockedBehaviour()
      initInsertMultiProductBehavior(dbProxy, mockGenericProductActionDbProxy)

      when(featureAware.enableDeprecateWorkflowId).thenReturn(false)
      when(featureAware.stopBfdbReplication).thenReturn(false)

      val model = insertModel.copy(
        workflowActions = Seq(
          mockBookingWorkflowAction,
          mockBookingWorkflowAction.copy(
            workflowId = WorkflowId.Flight.id,
            productTypeId = Some(ProductType.Flights.id),
            bookingId = Some(mockFlightModelInternal.bookingId),
            state = mockFlightBookingActionState.toJson
          ),
          mockBookingWorkflowAction.copy(
            workflowId = WorkflowId.Property.id,
            productTypeId = Some(ProductType.Hotel.id),
            bookingId = Some(mockNewPropertyBookingObjectForEbeLite.booking.bookingId)
          )
        )
      )

      dbProxy.insertMultiBooking(model).map { result =>
        verify(dbProxy, times(3))
          .updateWorkflowActionByWorkflowId(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )

        verify(dbProxy, times(0))
          .updateWorkflowActionByProductTypeId(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )
        val expectedFlightStates = insertModel.flightBookingActionStates
          .map(_.copy(itineraryProtoState = toItineraryState(mockFlightInternal).toBase64OptString))

        val expectedVehicleStates = insertModel.vehicleBookingActionStates
          .map(_.copy(itineraryProtoState = toItineraryState(mockVehicleModelInternal).toBase64OptString))

        val modifiedMockBookingWorkflowAction = mockBookingWorkflowAction.copy(
          operationId = Some(mockWorkflowItineraryOperation.operationId)
        )

        val expectedWorkflowActions = Seq(
          modifiedMockBookingWorkflowAction,
          modifiedMockBookingWorkflowAction.copy(
            workflowId = WorkflowId.Flight.id,
            productTypeId = Some(ProductType.Flights.id),
            bookingId = Some(mockFlightInternal.bookingId),
            state = expectedFlightStates.head.toJson
          ),
          modifiedMockBookingWorkflowAction.copy(
            workflowId = WorkflowId.Property.id,
            productTypeId = Some(ProductType.Hotel.id),
            bookingId = Some(mockNewPropertyBookingObjectForEbeLite.booking.bookingId)
          )
        )

        val actual = result.copy(
          vehicleBookingActionStates = Seq(mockVehicleBookingActionState),
          activityBookingActionState = Seq(mockActivityBookingActionState),
          protectionBookingActionStates = Seq(mockProtectionBookingActionState),
          cegFastTrackBookingActionState = Seq(mockCegFastTrackBookingActionState),
          addOnsBookingActionState = Seq(mockProtectionAddOnBookingActionState)
        )
        val expected = insertModel.copy(
          flightBookingActionStates = expectedFlightStates,
          workflowActions = expectedWorkflowActions,
          operationId = Some(mockWorkflowItineraryOperation.operationId)
        )

        actual shouldBe expected
      }
    }

    "call updateWorkflowActionByProductTypeId when MPBE-4506 is A and CFB-1292=B" in {
      val fixture = new InsertMultiProductFixtureTest {
        val queryName: String = "insertMultiProduct"
      }
      import fixture._
      initMockedBehaviour()
      initInsertMultiProductBehavior(dbProxy, mockGenericProductActionDbProxy)

      when(featureAware.enableDeprecateWorkflowId).thenReturn(true)
      when(featureAware.stopBfdbReplication).thenReturn(false)

      val model = insertModel.copy(
        workflowActions = Seq(
          mockBookingWorkflowAction,
          mockBookingWorkflowAction.copy(
            workflowId = WorkflowId.Flight.id,
            productTypeId = Some(ProductType.Flights.id),
            bookingId = Some(mockFlightModelInternal.bookingId),
            state = mockFlightBookingActionState.toJson
          ),
          mockBookingWorkflowAction.copy(
            workflowId = WorkflowId.Property.id,
            productTypeId = Some(ProductType.Hotel.id),
            bookingId = Some(mockNewPropertyBookingObjectForEbeLite.booking.bookingId)
          )
        )
      )

      dbProxy.insertMultiBooking(model).map { result =>
        verify(dbProxy, times(0))
          .updateWorkflowActionByWorkflowId(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )

        verify(dbProxy, times(3))
          .updateWorkflowActionByProductTypeId(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any()
          )

        val expectedFlightStates = insertModel.flightBookingActionStates
          .map(_.copy(itineraryProtoState = toItineraryState(mockFlightInternal).toBase64OptString))

        val modifiedMockBookingWorkflowAction = mockBookingWorkflowAction.copy(
          operationId = Some(mockWorkflowItineraryOperation.operationId)
        )

        val expectedWorkflowActions = Seq(
          modifiedMockBookingWorkflowAction,
          modifiedMockBookingWorkflowAction.copy(
            workflowId = WorkflowId.Flight.id,
            productTypeId = Some(ProductType.Flights.id),
            bookingId = Some(mockFlightInternal.bookingId),
            state = expectedFlightStates.head.toJson
          ),
          modifiedMockBookingWorkflowAction.copy(
            workflowId = WorkflowId.Property.id,
            productTypeId = Some(ProductType.Hotel.id),
            bookingId = Some(mockNewPropertyBookingObjectForEbeLite.booking.bookingId)
          )
        )

        val actual = result.copy(
          vehicleBookingActionStates = Seq(mockVehicleBookingActionState),
          activityBookingActionState = Seq(mockActivityBookingActionState),
          protectionBookingActionStates = Seq(mockProtectionBookingActionState),
          cegFastTrackBookingActionState = Seq(mockCegFastTrackBookingActionState),
          addOnsBookingActionState = Seq(mockProtectionAddOnBookingActionState)
        )

        val expected = insertModel.copy(
          flightBookingActionStates = expectedFlightStates,
          workflowActions = expectedWorkflowActions,
          operationId = Some(mockWorkflowItineraryOperation.operationId)
        )

        actual shouldBe expected
      }
    }

    "throw error when workflow flight bookingId not existing when MPBE-4506 is A" in {
      val fixture = new InsertMultiProductFixtureTest {
        val queryName: String = "insertMultiProduct"
      }

      import fixture._

      when(featureAware.stopBfdbReplication).thenReturn(false)

      val insertModel = MultiProductBookingInsertionModel(
        Seq(mockMultiProductInfo),
        Seq(
          mockBookingWorkflowAction,
          mockBookingWorkflowAction.copy(
            workflowId = WorkflowId.Flight.id,
            bookingId = Some(mockFlightModelInternal.bookingId + 1),
            state = mockFlightBookingActionState.toJson
          ),
          mockBookingWorkflowAction.copy(
            workflowId = WorkflowId.Property.id,
            bookingId = Some(mockNewPropertyBookingObjectForEbeLite.booking.bookingId)
          )
        ),
        mockItineraryInternalModel,
        Seq(mockFlightBookingActionState),
        Seq(mockNewPropertyBookingObjectForEbeLite),
        Seq(mockVehicleBookingActionState),
        Seq(mockProtectionBookingActionState),
        Seq(mockActivityBookingActionState),
        Seq(mockCegFastTrackBookingActionState),
        Seq(mockProtectionAddOnBookingActionState),
        Seq(mockMultiProductBookingGroup)
      )
      initMockedBehaviour()
      initInsertMultiProductBehavior(dbProxy, mockGenericProductActionDbProxy)

      dbProxy.insertMultiBooking(insertModel).failed.map { result =>
        result shouldBe a[WorkflowActionNotFound]
      }
    }

    "throw error when booking action state size exceeds allowed limit" in {
      val fixture = new InsertMultiProductFixtureTest {

        val queryName: String = "insertMultiProduct"

      }
      import fixture._
      initMockedBehaviour()
      initInsertMultiProductBehavior(dbProxy, mockGenericProductActionDbProxy)

      val basChars                    = (1 to 7000000).map(_ => 'x').toArray
      val bookingActionWithLargeState = mockBookingWorkflowAction.copy(state = new String(basChars))
      val insertModelWithLargeState   = insertModel.copy(workflowActions = Seq(bookingActionWithLargeState))

      dbProxy.insertMultiBooking(insertModelWithLargeState).failed.map { ex =>
        dbProxy.sentMetrics.get(s"bcre.db.${DependencyNames.BfdbBcreMetric}.action") should contain(
          "size" -> "> 12.0 MB"
        )
        ex shouldBe a[DbException]
      }
    }

    "update booking action state for vehicle product when MPBE-4506 is A" should {
      "for CFB-1292=A correctly" in {
        val fixture = new InsertMultiProductFixtureTest {
          val queryName: String = "insertMultiProduct"
        }
        import fixture._

        val vehicleProductAction = mockBookingWorkflowAction.copy(
          workflowId = WorkflowId.Vehicle.id,
          productTypeId = Some(ProductType.Cars.id),
          bookingId = mockVehicleBookingActionState.vehicleBookingState.map(_.vehicleBooking.vehicleBookingId)
        )

        when(featureAware.enableDeprecateWorkflowId).thenReturn(false)
        when(featureAware.stopBfdbReplication).thenReturn(false)

        val result = dbProxy.updateWorkflowAction(
          workflowAction = vehicleProductAction,
          updatedFlightBookingStates = Seq.empty,
          updatedVehicleBookingStates = Seq(mockVehicleBookingActionState),
          updatedProtectionBookingStates = Seq.empty,
          updatedActivityBookingStates = Seq.empty,
          updatedCegFastTrackBookingStates = Seq.empty,
          updatedAddOnsBookingStates = Seq.empty
        )

        result.state shouldEqual mockVehicleBookingActionState.toJson
      }

      "for CFB-1292=B correctly" in {
        val fixture = new InsertMultiProductFixtureTest {
          val queryName: String = "insertMultiProduct"
        }
        import fixture._

        val vehicleProductAction = mockBookingWorkflowAction.copy(
          workflowId = WorkflowId.Vehicle.id,
          productTypeId = Some(ProductType.Cars.id),
          bookingId = mockVehicleBookingActionState.vehicleBookingState.map(_.vehicleBooking.vehicleBookingId)
        )

        when(featureAware.enableDeprecateWorkflowId).thenReturn(true)
        when(featureAware.stopBfdbReplication).thenReturn(false)

        val result = dbProxy.updateWorkflowAction(
          workflowAction = vehicleProductAction,
          updatedFlightBookingStates = Seq.empty,
          updatedVehicleBookingStates = Seq(mockVehicleBookingActionState),
          updatedProtectionBookingStates = Seq.empty,
          updatedActivityBookingStates = Seq.empty,
          updatedCegFastTrackBookingStates = Seq.empty,
          updatedAddOnsBookingStates = Seq.empty
        )

        result.state shouldEqual mockVehicleBookingActionState.toJson
      }
    }

    "update booking action state for flight product when MPBE-4506 is A" should {
      "for CFB-1292=A correctly" in {
        val fixture = new InsertMultiProductFixtureTest {
          val queryName: String = "insertMultiProduct"
        }
        import fixture._
        val vehicleProductAction = mockBookingWorkflowAction.copy(
          workflowId = WorkflowId.Flight.id,
          productTypeId = Some(ProductType.Flights.id),
          bookingId = mockFlightBookingActionState.bookingState.map(_.bookingId)
        )

        when(featureAware.enableDeprecateWorkflowId).thenReturn(false)
        when(featureAware.stopBfdbReplication).thenReturn(false)

        val result =
          dbProxy.updateWorkflowAction(
            workflowAction = vehicleProductAction,
            updatedFlightBookingStates = Seq(mockFlightBookingActionState),
            updatedVehicleBookingStates = Seq.empty,
            updatedProtectionBookingStates = Seq.empty,
            updatedActivityBookingStates = Seq.empty,
            updatedCegFastTrackBookingStates = Seq.empty,
            updatedAddOnsBookingStates = Seq.empty
          )
        result.state shouldEqual mockFlightBookingActionState.toJson
      }

      "for CFB-1292=B correctly" in {
        val fixture = new InsertMultiProductFixtureTest {
          val queryName: String = "insertMultiProduct"
        }
        import fixture._
        val vehicleProductAction = mockBookingWorkflowAction.copy(
          workflowId = WorkflowId.Flight.id,
          productTypeId = Some(ProductType.Flights.id),
          bookingId = mockFlightBookingActionState.bookingState.map(_.bookingId)
        )

        when(featureAware.enableDeprecateWorkflowId).thenReturn(true)
        when(featureAware.stopBfdbReplication).thenReturn(false)

        val result =
          dbProxy.updateWorkflowAction(
            workflowAction = vehicleProductAction,
            updatedFlightBookingStates = Seq(mockFlightBookingActionState),
            updatedVehicleBookingStates = Seq.empty,
            updatedProtectionBookingStates = Seq.empty,
            updatedActivityBookingStates = Seq.empty,
            updatedCegFastTrackBookingStates = Seq.empty,
            updatedAddOnsBookingStates = Seq.empty
          )
        result.state shouldEqual mockFlightBookingActionState.toJson
      }
    }

    "update booking action state for protection product when MPBE-4506 is A" should {
      "for CFB-1292=A correctly" in {
        val fixture = new InsertMultiProductFixtureTest {
          val queryName: String = "insertMultiProduct"
        }
        when(mockProtectionBookingActionState.tripProtectionBookingState).thenReturn(
          Some(mockNewProtectionModelInternal)
        )
        import fixture._
        val protectionProductAction = mockBookingWorkflowAction.copy(
          workflowId = WorkflowId.Protection.id,
          productTypeId = Some(ProductType.TripProtection.id),
          bookingId = mockProtectionBookingActionState.tripProtectionBookingState.map(_.protectionBookingId)
        )

        when(featureAware.enableDeprecateWorkflowId).thenReturn(false)
        when(featureAware.stopBfdbReplication).thenReturn(false)

        val result = dbProxy.updateWorkflowAction(
          workflowAction = protectionProductAction,
          updatedFlightBookingStates = Seq.empty,
          updatedVehicleBookingStates = Seq.empty,
          updatedProtectionBookingStates = Seq(mockProtectionBookingActionState),
          updatedActivityBookingStates = Seq.empty,
          updatedCegFastTrackBookingStates = Seq.empty,
          updatedAddOnsBookingStates = Seq.empty
        )

        result.state shouldEqual mockProtectionBookingActionState.toJson
      }

      "for CFB-1292=B correctly" in {
        val fixture = new InsertMultiProductFixtureTest {
          val queryName: String = "insertMultiProduct"
        }
        when(mockProtectionBookingActionState.tripProtectionBookingState).thenReturn(
          Some(mockNewProtectionModelInternal)
        )
        import fixture._
        val protectionProductAction = mockBookingWorkflowAction.copy(
          workflowId = WorkflowId.Protection.id,
          productTypeId = Some(ProductType.TripProtection.id),
          bookingId = mockProtectionBookingActionState.tripProtectionBookingState.map(_.protectionBookingId)
        )

        when(featureAware.enableDeprecateWorkflowId).thenReturn(true)
        when(featureAware.stopBfdbReplication).thenReturn(false)

        val result = dbProxy.updateWorkflowAction(
          workflowAction = protectionProductAction,
          updatedFlightBookingStates = Seq.empty,
          updatedVehicleBookingStates = Seq.empty,
          updatedProtectionBookingStates = Seq(mockProtectionBookingActionState),
          updatedActivityBookingStates = Seq.empty,
          updatedCegFastTrackBookingStates = Seq.empty,
          updatedAddOnsBookingStates = Seq.empty
        )

        result.state shouldEqual mockProtectionBookingActionState.toJson
      }
    }

    "update booking action state for activity product when MPBE-4506 is A" should {
      "for CFB-1292=A correctly" in {
        val fixture = new InsertMultiProductFixtureTest {
          val queryName: String = "insertMultiProduct"
        }
        import fixture._

        val activityProductAction = mockBookingWorkflowAction.copy(
          workflowId = WorkflowId.Activity.id,
          productTypeId = Some(ProductType.Activity.id),
          bookingId = Some(mockActivityProductModel.product.booking.bookingId)
        )

        when(featureAware.enableDeprecateWorkflowId).thenReturn(false)
        when(featureAware.stopBfdbReplication).thenReturn(false)

        val result = dbProxy.updateWorkflowAction(
          workflowAction = activityProductAction,
          updatedFlightBookingStates = Seq.empty,
          updatedVehicleBookingStates = Seq.empty,
          updatedProtectionBookingStates = Seq.empty,
          updatedActivityBookingStates = Seq(mockActivityBookingActionState),
          updatedCegFastTrackBookingStates = Seq.empty,
          updatedAddOnsBookingStates = Seq.empty
        )

        result.state shouldEqual mockActivityBookingActionState.toJson
      }

      "for CFB-1292=B correctly" in {
        val fixture = new InsertMultiProductFixtureTest {
          val queryName: String = "insertMultiProduct"
        }
        import fixture._

        val activityProductAction = mockBookingWorkflowAction.copy(
          workflowId = WorkflowId.Activity.id,
          productTypeId = Some(ProductType.Activity.id),
          bookingId = Some(mockActivityProductModel.product.booking.bookingId)
        )

        when(featureAware.enableDeprecateWorkflowId).thenReturn(true)
        when(featureAware.stopBfdbReplication).thenReturn(false)

        val result = dbProxy.updateWorkflowAction(
          workflowAction = activityProductAction,
          updatedFlightBookingStates = Seq.empty,
          updatedVehicleBookingStates = Seq.empty,
          updatedProtectionBookingStates = Seq.empty,
          updatedActivityBookingStates = Seq(mockActivityBookingActionState),
          updatedCegFastTrackBookingStates = Seq.empty,
          updatedAddOnsBookingStates = Seq.empty
        )

        result.state shouldEqual mockActivityBookingActionState.toJson
      }
    }

    "update booking action state for CegFastTrack product when MPBE-4506 is A" should {
      "for CFB-1292=A correctly" in {
        val fixture = new InsertMultiProductFixtureTest {
          val queryName: String = "insertMultiProduct"
        }
        import fixture._

        val cegFastTrackProductAction = mockBookingWorkflowAction.copy(
          workflowId = WorkflowId.CegFastTrack.id,
          productTypeId = Some(ProductType.CEGFastTrack.id),
          bookingId = Some(mockCegFastTrackProductModel.product.booking.bookingId)
        )

        when(featureAware.enableDeprecateWorkflowId).thenReturn(false)
        when(featureAware.stopBfdbReplication).thenReturn(false)

        val result = dbProxy.updateWorkflowAction(
          workflowAction = cegFastTrackProductAction,
          updatedFlightBookingStates = Seq.empty,
          updatedVehicleBookingStates = Seq.empty,
          updatedProtectionBookingStates = Seq.empty,
          updatedActivityBookingStates = Seq.empty,
          updatedCegFastTrackBookingStates = Seq(mockCegFastTrackBookingActionState),
          updatedAddOnsBookingStates = Seq.empty
        )

        result.state shouldEqual mockCegFastTrackBookingActionState.toJson
      }

      "for CFB-1292=B correctly" in {
        val fixture = new InsertMultiProductFixtureTest {
          val queryName: String = "insertMultiProduct"
        }
        import fixture._

        val cegFastTrackProductAction = mockBookingWorkflowAction.copy(
          workflowId = WorkflowId.CegFastTrack.id,
          productTypeId = Some(ProductType.CEGFastTrack.id),
          bookingId = Some(mockCegFastTrackProductModel.product.booking.bookingId)
        )

        when(featureAware.enableDeprecateWorkflowId).thenReturn(true)
        when(featureAware.stopBfdbReplication).thenReturn(false)

        val result = dbProxy.updateWorkflowAction(
          workflowAction = cegFastTrackProductAction,
          updatedFlightBookingStates = Seq.empty,
          updatedVehicleBookingStates = Seq.empty,
          updatedProtectionBookingStates = Seq.empty,
          updatedActivityBookingStates = Seq.empty,
          updatedCegFastTrackBookingStates = Seq(mockCegFastTrackBookingActionState),
          updatedAddOnsBookingStates = Seq.empty
        )

        result.state shouldEqual mockCegFastTrackBookingActionState.toJson
      }
    }

    "update booking action state for Protection AddOn product when MPBE-4506 is A" should {
      "for CFB-1292=A correctly " in {
        val fixture = new InsertMultiProductFixtureTest {
          val queryName: String = "insertMultiProduct"
        }
        import fixture._

        val protectionAddOnProductAction = mockBookingWorkflowAction.copy(
          workflowId = WorkflowId.Protection.id,
          productTypeId = Some(ProductType.TripProtection.id),
          bookingId = Some(mockProtectionAddOnProductModel.product.booking.bookingId)
        )

        when(featureAware.enableDeprecateWorkflowId).thenReturn(false)
        when(featureAware.stopBfdbReplication).thenReturn(false)

        val result = dbProxy.updateWorkflowAction(
          workflowAction = protectionAddOnProductAction,
          updatedFlightBookingStates = Seq.empty,
          updatedVehicleBookingStates = Seq.empty,
          updatedProtectionBookingStates = Seq.empty,
          updatedActivityBookingStates = Seq.empty,
          updatedCegFastTrackBookingStates = Seq.empty,
          updatedAddOnsBookingStates = Seq(mockProtectionAddOnBookingActionState)
        )

        result.state shouldEqual mockProtectionAddOnBookingActionState.toJson
      }

      "for CFB-1292=B correctly " in {
        val fixture = new InsertMultiProductFixtureTest {
          val queryName: String = "insertMultiProduct"
        }
        import fixture._

        val protectionAddOnProductAction = mockBookingWorkflowAction.copy(
          workflowId = WorkflowId.Protection.id,
          productTypeId = Some(ProductType.TripProtection.id),
          bookingId = Some(mockProtectionAddOnProductModel.product.booking.bookingId)
        )

        when(featureAware.enableDeprecateWorkflowId).thenReturn(true)
        when(featureAware.stopBfdbReplication).thenReturn(false)

        val result = dbProxy.updateWorkflowAction(
          workflowAction = protectionAddOnProductAction,
          updatedFlightBookingStates = Seq.empty,
          updatedVehicleBookingStates = Seq.empty,
          updatedProtectionBookingStates = Seq.empty,
          updatedActivityBookingStates = Seq.empty,
          updatedCegFastTrackBookingStates = Seq.empty,
          updatedAddOnsBookingStates = Seq(mockProtectionAddOnBookingActionState)
        )

        result.state shouldEqual mockProtectionAddOnBookingActionState.toJson
      }
    }
  }

  "getNextPackageSequenceNumber" should {
    val expectedValue = 1L
    val expectedData: Array[Map[String, Any]] = Array(
      Map("1" -> expectedValue)
    )
    "call SP correctly" in {
      val fixture = new Fixture {
        val queryName     = "bcre_sequence_next_package_id_v1"
        val sqlQuery      = s"EXEC dbo.$queryName"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.getNextPackageSequenceNumber.map { results =>
        verifyInteractionsWithUnderlyingDb()
        results shouldEqual expectedValue
      }
    }
    "throw SQLException when failed" in {
      val fixture = new Fixture {
        val queryName     = "bcre_sequence_next_package_id_v1"
        val sqlQuery      = s"EXEC dbo.$queryName"
        val resultSetStub = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      when(resultSetStub.next()).thenReturn(false)
      dbProxy.getNextPackageSequenceNumber().failed.map { result =>
        result shouldBe a[SQLException]
      }
    }
  }

  "getNextCartSequenceNumber" should {
    val expectedValue = 1L
    val expectedData: Array[Map[String, Any]] = Array(
      Map("1" -> expectedValue)
    )
    "call SP correctly" in {
      val fixture = new Fixture {
        val queryName     = "bcre_sequence_next_cart_id_v1"
        val sqlQuery      = s"EXEC dbo.$queryName"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.getNextCartSequenceNumber.map { results =>
        verifyInteractionsWithUnderlyingDb()
        results shouldEqual expectedValue
      }
    }
    "throw SQLException when failed" in {
      val fixture = new Fixture {
        val queryName     = "bcre_sequence_next_cart_id_v1"
        val sqlQuery      = s"EXEC dbo.$queryName"
        val resultSetStub = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      when(resultSetStub.next()).thenReturn(false)
      dbProxy.getNextCartSequenceNumber().failed.map { result =>
        result shouldBe a[SQLException]
      }
    }
  }

  "getNextOperationIdSequenceNumber" should {
    val expectedValue = 1L
    val expectedData: Array[Map[String, Any]] = Array(
      Map("1" -> expectedValue)
    )
    "call SP correctly" in {
      val fixture = new Fixture {
        val queryName     = "bcre_sequence_next_operation_id_v2"
        val sqlQuery      = s"EXEC dbo.$queryName"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.getNextOperationIdSequenceNumber.map { results =>
        verifyInteractionsWithUnderlyingDb()
        results shouldEqual expectedValue
      }
    }
    "throw SQLException when failed" in {
      val fixture = new Fixture {
        val queryName     = "bcre_sequence_next_operation_id_v2"
        val sqlQuery      = s"EXEC dbo.$queryName"
        val resultSetStub = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      when(resultSetStub.next()).thenReturn(false)
      dbProxy.getNextOperationIdSequenceNumber().failed.map { result =>
        result shouldBe a[SQLException]
      }
    }
  }

  trait Fixture extends DBProxyTestHelper {
    val agDbStub              = spy(new AGDBStub(mockConnection))
    implicit val killSwitches = mock[KillSwitches]
    val dbProxy = new MultiProductDbProxyImpl(
      agDbStub,
      new BFDBExecutionContext(BFDBExecutionContext.global),
      mockBaseBookingDBProxy,
      mockGenericProductActionDbProxy,
      mockAgodaConfig,
      killSwitches
    ) with WithMetricsCapture

    def queryName: String

    def sqlQuery: String

    def resultSetStub: ResultSet

    def initMockedBehaviour(): Unit = {
      reinitializeMockDbBehaviour
      when(mockPreparedStatement.executeQuery).thenReturn(resultSetStub)
      when(mockCallableStatement.executeQuery).thenReturn(resultSetStub)
    }

    def verifyInteractionsWithUnderlyingDb(asCallableStatement: Boolean = false): Unit = {
      verify(agDbStub).withConnectionGroup(argsEq(DBConnectionGroup.BFDB_BCRE.value))(any())
      if (asCallableStatement)
        verify(mockConnection).prepareCall(sqlQuery)
      else
        verify(mockConnection).prepareStatement(sqlQuery)
      verifyNoMoreInteractions(mockConnection)

      dbProxy.sentMetrics should have size 1
      dbProxy.sentMetrics should contain key s"bcre.db.${DependencyNames.BfdbBcreMetric}"
      dbProxy.sentMetrics.get(s"bcre.db.${DependencyNames.BfdbBcreMetric}") should contain("method" -> queryName)
    }
  }

  trait InsertMultiProductFixtureTest extends DBProxyTestHelper {
    val requestContext = mock[RequestContext]
    val connectionMock = mock[Connection]
    val agDbStub       = spy(new AGDBStub(connectionMock))
    val featureAware   = mock[FeatureAware]

    when(requestContext.featureAware).thenReturn(Some(featureAware))
    when(featureAware.stopBfdbReplication).thenReturn(true)

    implicit val killSwitches       = mock[KillSwitches]
    implicit val rc: RequestContext = requestContext

    // when(featureAware.stopBfdbReplication).thenReturn(true)

    val dbProxy = spy(
      new MultiProductDbProxyImpl(
        agDbStub,
        new BFDBExecutionContext(BFDBExecutionContext.global),
        mockBaseBookingDBProxy,
        mockGenericProductActionDbProxy,
        mockAgodaConfig,
        killSwitches
      ) with WithMetricsCapture
    )

    def queryName: String

    def initMockedBehaviour(): Unit = {
      when(connectionMock.getAutoCommit).thenReturn(true)
    }

    def verifyInteractionsWithUnderlyingDb(): Unit = {
      verify(agDbStub).withConnectionGroup(argsEq(DBConnectionGroup.BFDB_BCRE.value))(any())
      verify(connectionMock).getAutoCommit
      verify(connectionMock).setAutoCommit(false)
      verify(connectionMock).commit()
      verify(connectionMock).setAutoCommit(true)
      verifyNoMoreInteractions(connectionMock)

      dbProxy.sentMetrics should have size 2
      dbProxy.sentMetrics should contain key s"bcre.db.${DependencyNames.BfdbBcreMetric}"
      dbProxy.sentMetrics should contain key s"bcre.db.${DependencyNames.BfdbBcreMetric}.action"
      dbProxy.sentMetrics.get(s"bcre.db.${DependencyNames.BfdbBcreMetric}") should contain("method" -> queryName)
      val actionMetric = dbProxy.sentMetrics.get(s"bcre.db.${DependencyNames.BfdbBcreMetric}.action")
      actionMetric should contain("method" -> queryName)
    }

    def verifyFunctionCalled(): Unit = {
      verify(dbProxy)
        .insertMultiProductInfoAction(any[MultiProductInfoDBModel])(any[Connection])
      verify(dbProxy)
        .insertItineraryOperation(any[WorkflowItineraryOperation])(any[Connection])
      verify(dbProxy)
        .insertMultiProductBookingGroupAction(any[MultiProductBookingGroupDBModel])(any[Connection])
      verify(dbProxy).insertItinerary(any[ItineraryInternalModel])(any[Connection], any())
      verify(dbProxy, times(1)).insertItineraryOperation(any[WorkflowItineraryOperation])(any[Connection])
      verify(dbProxy)
        .insertFlightsWithoutItinerary(any[FlightModelInternal], argsEq(mockAgodaConfig))(any[Connection])
      verify(dbProxy)
        .insertVehicleWithoutItinerary(any[VehicleModelInternal])(any[Connection])
      verify(dbProxy)
        .insertPropertyCreationBookingTable(any[PropertyBookingCreationLocal])(any[Connection], any[KillSwitches])
      verify(dbProxy).upsertProtection(any[ProtectionModelInternal])(any[Connection])
      verify(dbProxy, times(3)).insertBookingAction(any[BookingWorkflowAction])(any[Connection])
      verify(mockGenericProductActionDbProxy, times(3))
        .insertProductModelWithoutItinerary(any[BaseProductModel])(any[Connection])
    }

    def verifyFunctionCalledWhenStopSavingBfdb(): Unit = {
      verify(dbProxy)
        .insertMultiProductInfoAction(argsEq(mockMultiProductInfo))(any[Connection])
      val insertItineraryOperationCaptor = ArgumentCaptor.forClass(classOf[WorkflowItineraryOperation])
      verify(dbProxy)
        .insertItineraryOperation(insertItineraryOperationCaptor.capture())(any[Connection])
      val actualInsertItineraryOperationInput = insertItineraryOperationCaptor.getValue
      actualInsertItineraryOperationInput.operationId shouldBe 0
      actualInsertItineraryOperationInput.itineraryId shouldBe mockItineraryInternalModel.itinerary.itineraryId
      actualInsertItineraryOperationInput.actionTypeId shouldBe OperationActionTypes.CreateBooking.id

      verify(dbProxy, never)
        .insertMultiProductBookingGroupAction(any[MultiProductBookingGroupDBModel])(any[Connection])
      verify(dbProxy, never).insertItinerary(any[ItineraryInternalModel])(any[Connection], any())
      verify(dbProxy, never)
        .insertFlightsWithoutItinerary(any[FlightModelInternal], argsEq(mockAgodaConfig))(any[Connection])
      verify(dbProxy, never)
        .insertVehicleWithoutItinerary(any[VehicleModelInternal])(any[Connection])
      verify(dbProxy)
        .insertPropertyCreationBookingTable(argsEq(mockNewPropertyBookingObjectForEbeLite))(
          any[Connection],
          any[KillSwitches]
        )
      verify(dbProxy, never).upsertProtection(any[ProtectionModelInternal])(any[Connection])
      verify(dbProxy).insertBookingAction(
        argThat[BookingWorkflowAction](_.productTypeId == mockBookingWorkflowAction.productTypeId)
      )(
        any[Connection]
      )
      verify(dbProxy).insertBookingAction(
        argThat[BookingWorkflowAction](_.productTypeId == Some(ProductType.Flights.id))
      )(
        any[Connection]
      )
      verify(dbProxy).insertBookingAction(
        argThat[BookingWorkflowAction](_.productTypeId == Some(ProductType.Hotel.id))
      )(
        any[Connection]
      )
      verify(mockGenericProductActionDbProxy, never)
        .insertProductModelWithoutItinerary(any[BaseProductModel])(any[Connection])
    }
  }
}
