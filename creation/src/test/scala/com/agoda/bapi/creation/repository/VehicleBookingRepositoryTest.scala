package com.agoda.bapi.creation.repository

import com.agoda.adp.messaging.message.Message
import com.agoda.bapi.common.MessageService
import com.agoda.bapi.common.exception.{DbException, VehicleUnableToFindBookingSummaryException, VehicleUnableToFindBookingTripException, VehicleUnableToFindItineraryException}
import com.agoda.bapi.common.mapper.booking.vehicle._
import com.agoda.bapi.common.message.creation.{BookingElement, DuplicateBooking}
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.common.model.booking.{BookingStateMessage, FlightItineraryForMessage, MultiProductBookingGroupModelMessage, VehiclesForMessage}
import com.agoda.bapi.common.model.car.VehicleBookingStateModel._
import com.agoda.bapi.common.model.car.{VehicleBookingState, VehicleBookingStateExtended}
import com.agoda.bapi.common.model.flight.flightModel.{BookingPaymentState, Breakdown, ItineraryHistory, MultiProductItinerary, PaymentState}
import com.agoda.bapi.common.model.multiproduct.MultiProductBookingGroupDBModel
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.util.CollectionUtils._
import com.agoda.bapi.creation.config.ReplicateStateConfig
import com.agoda.bapi.creation.model.db.DuplicateBookingRecord
import com.agoda.bapi.creation.proxy.VehicleDBProxy
import com.agoda.bapi.creation.proxy.db.vehicle.VehicleDbRead
import com.agoda.commons.config.dynamic.DynamicObject
import com.agoda.mpb.common.errors.ErrorCode
import mocks.{DBBookingModelHelper, VehicleModelMock}
import org.joda.time.DateTime
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{AppendedClues, BeforeAndAfterEach}
import org.scalatestplus.mockito.MockitoSugar

import java.sql.SQLException
import java.util.Date
import scala.concurrent.Future

class VehicleBookingRepositoryTest
    extends AsyncWordSpec
    with Matchers
    with MockitoSugar
    with AppendedClues
    with BeforeAndAfterEach
    with DBBookingModelHelper
    with VehicleModelMock {

  trait Fixture {
    val vehicleDbProxyMock: VehicleDBProxy             = mock[VehicleDBProxy]
    val vehicleDbReadProxyMock: VehicleDbRead          = mock[VehicleDbRead]
    val hadoopServiceMock: MessageService              = mock[MessageService]
    val multiProductRepository: MultiProductRepository = mock[MultiProductRepository]
    val replicateConfig                                = mock[ReplicateStateConfig]
    val featureAware                                   = mock[FeatureAware]
    implicit val featureAwareOps                       = Some(featureAware)

    val repository =
      spy[VehicleBookingRepositoryImpl](
        new VehicleBookingRepositoryImpl(
          vehicleDbProxyMock,
          vehicleDbReadProxyMock,
          vehicleDbReadProxyMock,
          multiProductRepository,
          hadoopServiceMock,
          replicateConfig
        )
      )
    val mockDynamicObj = mock[DynamicObject[Boolean]]

    when(replicateConfig.isEnableReplicateMultiProductWithCarAndActivity).thenReturn(mockDynamicObj)
    when(mockDynamicObj.get).thenReturn(true)
  }

  val baseItineraryHistory = random[ItineraryHistory].copy(actionId = 10, version = 0)

  private val baseVehicleBookingState = VehicleBookingState(
    Seq(mockVehicleModelInternal),
    Seq(random[PaymentState]),
    Seq(random[BookingPaymentState]),
    Seq(random[ItineraryHistory]),
    random[MultiProductItinerary],
    Seq.empty
  )
  private val itineraryId = baseVehicleBookingState.itinerary.itineraryId
  private val bookingId   = baseVehicleBookingState.vehicleModelsInternal.head.vehicleBooking.vehicleBookingId
  private val paymentId   = baseVehicleBookingState.payments.head.paymentId

  // set increasing Ids
  private val existingItineraryHistories = baseVehicleBookingState.itineraryHistories.zipWithIndex.map {
    case (itineraryHistory, index) =>
      itineraryHistory.copy(actionId = index, version = index)
  }

  private val existingVehiclePaymentState = baseVehicleBookingState.payments.zipWithIndex.map {
    case (paymentState, index) =>
      paymentState.copy(paymentId = index)
  }

  private val existingVehicleFinancialBreakdowns =
    baseVehicleBookingState.vehicleModelsInternal.map(
      _.vehicleFinancialBreakdowns.zipWithIndex
        .map { case (financialBreakDown, index) => financialBreakDown.copy(breakdownId = index, upcId = Some(3)) }
    )

  private val newFinancialBreakDownToSave = random[Breakdown].copy(breakdownId = 99)

  private val existingVehicleFinancialBreakdownsWithUpdatedUpcId =
    existingVehicleFinancialBreakdowns.flatten.head.copy(upcId = Some(1))

  private val updatedVehicleModelInternal = baseVehicleBookingState.vehicleModelsInternal
    .zip(existingVehicleFinancialBreakdowns)
    .map {
      case (vehicleModelInternal, financialBreakdowns) =>
        // 1 breakdown with updated upcId and 1 new financialBreakdown
        vehicleModelInternal.copy(vehicleFinancialBreakdowns =
          existingVehicleFinancialBreakdownsWithUpdatedUpcId +: financialBreakdowns.tail :+ newFinancialBreakDownToSave
        )
    }

  "saveVehicleBookingState saves only new ItineraryActionHistory, Payments, FinancialBreakdowns and updates FinancialBreakdowns if UPC_ID was changed" in {
    val fixture = new Fixture {}
    import fixture._

    val newItineraryHistory      = random[ItineraryHistory].copy(actionId = 10, version = 2)
    val existingPayment          = random[PaymentState].copy(paymentId = 1)
    val newPayment               = random[PaymentState].copy(paymentId = 0)
    val newPaymentAfterInsertion = newPayment.copy(paymentId = 2, recCreatedWhen = Some(DateTime.now))
    val payments                 = Seq(newPayment, existingPayment)
    val insertedPayments         = Seq(existingPayment, newPaymentAfterInsertion)

    val vehicleBookingStateToSave = baseVehicleBookingState.copy(
      itineraryHistories = existingItineraryHistories :+ newItineraryHistory,
      payments = payments,
      vehicleModelsInternal = updatedVehicleModelInternal,
      multiProductBookingGroups = Seq(mockMultiProductBookingGroup)
    )

    when(vehicleDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
      .thenReturn(Future.successful(existingItineraryHistories))
    when(vehicleDbProxyMock.getItineraryPaymentByItineraryId(itineraryId))
      .thenReturn(Future.successful(existingVehiclePaymentState))
    when(vehicleDbProxyMock.getFinancialBreakdown(bookingId))
      .thenReturn(Future.successful(existingVehicleFinancialBreakdowns.flatten))
    val mpGroup = random[MultiProductBookingGroupDBModel]
    when(multiProductRepository.getMultiProductBookingGroupByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(mpGroup)))

    val vehicleBookingStateToSaveWithoutExistingItineraryHistories =
      vehicleBookingStateToSave.copy(
        itineraryHistories = Seq(newItineraryHistory),
        payments = payments,
        vehicleModelsInternal =
          Seq(mockVehicleModelInternal.copy(vehicleFinancialBreakdowns = Seq(newFinancialBreakDownToSave)))
      )

    val vehicleBookingStateAfterInsertion =
      vehicleBookingStateToSave.copy(
        itineraryHistories = Seq(newItineraryHistory),
        payments = insertedPayments,
        vehicleModelsInternal =
          Seq(mockVehicleModelInternal.copy(vehicleFinancialBreakdowns = Seq(newFinancialBreakDownToSave)))
      )

    val vehicleBookingStateExtended = VehicleBookingStateExtended(
      vehicleBookingStateToSaveWithoutExistingItineraryHistories,
      Seq(existingVehicleFinancialBreakdownsWithUpdatedUpcId)
    )

    when(vehicleDbProxyMock.insertVehicle(vehicleBookingStateExtended))
      .thenReturn(Future.successful(vehicleBookingStateAfterInsertion))

    when(multiProductRepository.saveMultiProductBookingGroupIfNotExist(mockMultiProductBookingGroup))
      .thenReturn(Future.successful(mockMultiProductBookingGroup))

    val expectedVehicleModelInternal =
      vehicleBookingStateToSaveWithoutExistingItineraryHistories.vehicleModelsInternal
        .zip(existingVehicleFinancialBreakdowns)
        .map {
          case (vehicleModelInternal, breakdowns) =>
            vehicleModelInternal.copy(vehicleFinancialBreakdowns =
              (vehicleModelInternal.vehicleFinancialBreakdowns ++ breakdowns)
                .filter(_.breakdownId > 0)
                .distinctBy(_.breakdownId)
            ) // filter out not persisted breakdowns
        }

    repository.saveVehicleBookingState(vehicleBookingStateToSave).map { result =>
      verify(vehicleDbProxyMock, times(2)).getItineraryActionHistoryByItineraryId(itineraryId)
      verify(vehicleDbProxyMock).getFinancialBreakdown(bookingId)
      verify(multiProductRepository).saveMultiProductBookingGroupIfNotExist(mockMultiProductBookingGroup)

      result shouldEqual vehicleBookingStateAfterInsertion.copy(vehicleModelsInternal = expectedVehicleModelInternal)
    }
  }

  "saveVehicleBookingState not save itinerary info(ItineraryActionHistory, Payments, BookingPayments) when it's part of Multi product and only updates FinancialBreakdowns if UPC_ID was changed" in {
    val fixture = new Fixture {}
    import fixture._

    val multiProductGroup1    = mockMultiProductBookingGroup.copy(bookingId = 1)
    val multiProductGroup2    = mockMultiProductBookingGroup.copy(bookingId = 2)
    val multiProductGroupings = Seq(multiProductGroup1, multiProductGroup2)

    val existingPayment = Seq(existingVehiclePaymentState.head.copy(paymentId = 1))
    val existingHistory = Seq(existingItineraryHistories.head.copy(actionId = 1))

    val vehicleBookingStateToSave = baseVehicleBookingState.copy(
      itineraryHistories = existingHistory,
      payments = existingPayment,
      vehicleModelsInternal = updatedVehicleModelInternal,
      multiProductBookingGroups = multiProductGroupings
    )
    when(multiProductRepository.getMultiProductBookingGroupByItineraryId(itineraryId))
      .thenReturn(Future.successful(multiProductGroupings))
    when(vehicleDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
      .thenReturn(Future.successful(existingHistory))
    when(vehicleDbProxyMock.getItineraryPaymentByItineraryId(itineraryId))
      .thenReturn(Future.successful(existingPayment))
    when(vehicleDbProxyMock.getFinancialBreakdown(bookingId))
      .thenReturn(Future.successful(existingVehicleFinancialBreakdowns.flatten))

    val vehicleBookingStateToSaveWithoutItineraryInfo =
      vehicleBookingStateToSave.copy(
        itineraryHistories = Seq.empty,
        payments =
          vehicleBookingStateToSave.payments, // this doesnt get filtered out yet, will be filtered out in VehicleDBProxy.insertVehicle
        bookingPayments =
          vehicleBookingStateToSave.bookingPayments, // this doesnt get filtered out yet, will be filtered out in VehicleDBProxy.insertVehicle
        multiProductBookingGroups = multiProductGroupings,
        vehicleModelsInternal =
          Seq(mockVehicleModelInternal.copy(vehicleFinancialBreakdowns = Seq(newFinancialBreakDownToSave)))
      )

    val vehicleBookingStateAfterSaved =
      vehicleBookingStateToSave.copy(
        itineraryHistories = Seq.empty,
        payments = Seq.empty,
        bookingPayments = Seq.empty,
        multiProductBookingGroups = multiProductGroupings,
        vehicleModelsInternal =
          Seq(mockVehicleModelInternal.copy(vehicleFinancialBreakdowns = Seq(newFinancialBreakDownToSave)))
      )

    val vehicleBookingStateAfterInsertion =
      vehicleBookingStateToSave.copy(
        vehicleModelsInternal =
          Seq(mockVehicleModelInternal.copy(vehicleFinancialBreakdowns = Seq(newFinancialBreakDownToSave)))
      )

    val vehicleBookingStateExtended = VehicleBookingStateExtended(
      vehicleBookingStateToSaveWithoutItineraryInfo,
      Seq(existingVehicleFinancialBreakdownsWithUpdatedUpcId)
    )

    when(vehicleDbProxyMock.insertVehicle(vehicleBookingStateExtended))
      .thenReturn(Future.successful(vehicleBookingStateAfterSaved))

    when(multiProductRepository.saveMultiProductBookingGroupIfNotExist(multiProductGroup1))
      .thenReturn(Future.successful(multiProductGroup1))

    when(multiProductRepository.saveMultiProductBookingGroupIfNotExist(multiProductGroup2))
      .thenReturn(Future.successful(multiProductGroup2))

    val expectedVehicleModelInternal =
      vehicleBookingStateAfterSaved.vehicleModelsInternal
        .zip(existingVehicleFinancialBreakdowns)
        .map {
          case (vehicleModelInternal, breakdowns) =>
            vehicleModelInternal.copy(vehicleFinancialBreakdowns =
              (vehicleModelInternal.vehicleFinancialBreakdowns ++ breakdowns)
                .filter(_.breakdownId > 0)
                .distinctBy(_.breakdownId)
            ) // filter out not persisted breakdowns
        }

    val expected = vehicleBookingStateAfterInsertion.copy(
      vehicleModelsInternal = expectedVehicleModelInternal,
      bookingPayments = Seq.empty
    ) // Because it's already saved and we dont need to return to build replication model

    repository.saveVehicleBookingState(vehicleBookingStateToSave).map { result =>
      verify(vehicleDbProxyMock, times(2)).getItineraryActionHistoryByItineraryId(itineraryId)
      verify(vehicleDbProxyMock).getFinancialBreakdown(bookingId)

      result shouldEqual expected
    }
  }

  "getVehicleBookingByItineraryId" should {
    "should take itinerary id and return VehicleBooking" in {
      val fixture = new Fixture {}
      import fixture._

      val mockVehicleModelBooking = mock[VehicleModelBooking]
      when(vehicleDbProxyMock.getVehicleBookingByItineraryId(any()))
        .thenReturn(Future.successful(List(mockVehicleModelBooking)))

      repository.getVehicleBookingByItineraryId(112L).map { _ =>
        verify(vehicleDbProxyMock, times(1)).getVehicleBookingByItineraryId(any[Long])
        verifyNoInteractions(vehicleDbReadProxyMock)
        succeed
      }
    }
  }

  "getVehicleBookingByItineraryIdV2" should {
    "return VehicleBooking from primary on success" in {
      val fixture = new Fixture {}
      import fixture._

      val mockVehicleModelBooking = mock[VehicleModelBooking]
      when(vehicleDbReadProxyMock.getVehicleBookingByItineraryId(any()))
        .thenReturn(Future.successful(List(mockVehicleModelBooking)))

      repository.getVehicleBookingByItineraryIdV2(112L).map { results =>
        verify(vehicleDbReadProxyMock, times(1)).getVehicleBookingByItineraryId(112L)
        verifyNoInteractions(vehicleDbProxyMock)

        results shouldBe List(mockVehicleModelBooking)
      }
    }

    "return VehicleBooking from fallback when primary fails" in {
      val fixture = new Fixture {}
      import fixture._

      val mockVehicleModelBooking = mock[VehicleModelBooking]
      when(vehicleDbReadProxyMock.getVehicleBookingByItineraryId(any()))
        .thenReturn(
          Future.failed(
            DbException(ErrorCode.UnexpectedDbError, Option(new SQLException("simulated primary db timeout")))
          ),
          Future.successful(List(mockVehicleModelBooking))
        )

      repository.getVehicleBookingByItineraryIdV2(112L).map { results =>
        verify(vehicleDbReadProxyMock, times(2)).getVehicleBookingByItineraryId(112L)
        verifyNoInteractions(vehicleDbProxyMock)
        results shouldBe List(mockVehicleModelBooking)
      }

    }

    "fail when both data sources fail" in {
      val fixture = new Fixture {}
      import fixture._

      val mockVehicleModelBooking = mock[VehicleModelBooking]
      when(vehicleDbReadProxyMock.getVehicleBookingByItineraryId(any()))
        .thenReturn(
          Future.failed(
            DbException(ErrorCode.UnexpectedDbError, Option(new SQLException("simulated primary db timeout")))
          ),
          Future.failed(
            DbException(ErrorCode.UnexpectedDbError, Option(new SQLException("simulated secondary db timeout")))
          )
        )

      recoverToSucceededIf[DbException](repository.getVehicleBookingByItineraryIdV2(112L)).map { _ =>
        verify(vehicleDbReadProxyMock, times(2)).getVehicleBookingByItineraryId(112L)
        verifyNoInteractions(vehicleDbProxyMock)
        succeed
      }
    }
  }

  "getNextItinerarySequenceNumber" should {
    "call proxy" in {
      val fixture = new Fixture {}
      import fixture._
      // Arrange
      when(vehicleDbProxyMock.getNextItinerarySequenceNumberWithConnection).thenReturn(Future.successful(1L))

      // Act
      repository.getNextItinerarySequenceNumber map { _ =>
        // Assert
        verify(vehicleDbProxyMock, times(1)).getNextItinerarySequenceNumberWithConnection
        succeed
      }
    }
  }

  "getNextBookingSequenceNumber" should {
    "call proxy" in {
      val fixture = new Fixture {}
      import fixture._

      // Arrange
      when(vehicleDbProxyMock.getNextBookingSequenceNumberWithConnection).thenReturn(Future.successful(1L))

      // Act
      repository.getNextBookingSequenceNumber map { _ =>
        // Assert
        verify(vehicleDbProxyMock, times(1)).getNextBookingSequenceNumberWithConnection
        succeed
      }
    }
  }

  "getNextVehicleSequenceNumber" should {
    "call proxy" in {
      val fixture = new Fixture {}
      import fixture._

      // Arrange
      when(vehicleDbProxyMock.getNextVehicleSequenceNumber).thenReturn(Future.successful(1L))

      // Act
      repository.getNextVehicleSequenceNumber map { _ =>
        // Assert
        verify(vehicleDbProxyMock, times(1)).getNextVehicleSequenceNumber
        succeed
      }
    }
  }

  "getNextVehicleBookingLocationSequenceNumber" should {
    "call proxy" in {
      val fixture = new Fixture {}
      import fixture._

      // Arrange
      when(vehicleDbProxyMock.getNextVehicleBookingLocationSequenceNumber).thenReturn(Future.successful(1L))

      // Act
      repository.getNextVehicleBookingLocationSequenceNumber map { result =>
        // Assert
        verify(vehicleDbProxyMock, times(1)).getNextVehicleBookingLocationSequenceNumber
        result shouldBe 1L
      }
    }
  }

  "getNextVehicleInfoSequenceNumber" should {
    "call proxy" in {
      val fixture = new Fixture {}
      import fixture._

      // Arrange
      when(vehicleDbProxyMock.getNextVehicleInfoSequenceNumber).thenReturn(Future.successful(1L))

      // Act
      repository.getNextVehicleInfoSequenceNumber map { result =>
        // Assert
        verify(vehicleDbProxyMock, times(1)).getNextVehicleInfoSequenceNumber
        result shouldBe 1L
      }
    }
  }

  "sendVehicleModelForReplication" should { // This test will fail if current date changes during execution
    "call messaging send" in {
      val fixture = new Fixture {}
      import fixture._

      when(hadoopServiceMock.sendMessage(any[Message])).thenReturn(Future.successful(()))
      val vehicleModelInternal = VehicleModelInternal(
        vehicleBooking = VehicleModelBooking(
          vehicleBookingId = 1,
          itineraryId = 1,
          multiProductId = Some(1),
          bookingDate = DateTime.parse("2019-08-02T16:01"),
          paymentModel = 1,
          displayCurrency = "USD",
          supplierId = 1,
          providerCode = "123",
          supplierBookingId = "123",
          supplierSpecificData = "BKK",
          supplierStatusCode = Some("1"),
          supplierCommissionAmount = 0.0,
          supplierCommissionPercentage = 0.0,
          whitelabelId = 1,
          isCancelled = false,
          cancellationPolicy = "",
          cancellationDate = Some(DateTime.parse("2019-08-02T17:01")),
          fraudScore = None,
          fraudAction = None,
          fraudCheckIp = "",
          storefrontId = 1,
          platformId = Some(1),
          languageId = Some(1),
          serverName = Some("dev"),
          cid = None,
          searchId = None,
          searchRequestId = "123",
          sessionId = "123",
          clientIpAddress = "123",
          trackingCookieId = Some("123"),
          trackingCookieDate = None,
          trackingTag = Some("123"),
          recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
          recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
          recStatus = 1,
          vehicleStateId = 1,
          rejectReasonMessage = None,
          rejectReasonCode = None,
          accountingEntityStr = None,
          postBookingStateId = None
        ),
        vehicleBookingLocation = VehicleModelBookingPickUpDropOffLocation(
          pickUp = VehicleModelBookingLocation(
            vehicleBookingLocationId = 1,
            vehicleBookingId = 1,
            countryId = 1,
            cityId = 1,
            addressLine = "123",
            postalCode = "123",
            airportCode = Some("123"),
            locationName = "abc",
            isAirport = true,
            airportProviderLocation = None,
            extraInfo = "",
            supplierLocationCode = None,
            phoneNumber = Some("1234"),
            locationType = Some("OnAirport"),
            recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
            recModifiedWhen = DateTime.parse("2019-08-02T16:01")
          ),
          dropOff = VehicleModelBookingLocation(
            vehicleBookingLocationId = 1,
            vehicleBookingId = 1,
            countryId = 1,
            cityId = 1,
            addressLine = "123",
            postalCode = "123",
            airportCode = Some("123"),
            locationName = "abc",
            isAirport = true,
            airportProviderLocation = None,
            extraInfo = "",
            supplierLocationCode = None,
            phoneNumber = Some("1234"),
            locationType = Some("OnAirport"),
            recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
            recModifiedWhen = DateTime.parse("2019-08-02T16:01")
          )
        ),
        vehicleBookingSummary = VehicleModelBookingSummary(
          vehicleBookingId = 1,
          displayCurrency = "USD",
          totalSurcharge = 0.0,
          surchargeDetails = "",
          baseDiscount = 0.0,
          campaignDiscount = 0.0,
          totalFare = 0.0,
          agodaFee = 0.0,
          recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
          recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
          recStatus = 1,
          policyChargeDetails = None,
          paymentModel = Some(1),
          baseFare = Some(0.0),
          taxAndFee = Some(0.0),
          extraChargeDetails = Some("THIS IS JSON STRING"),
          postBookingMetadata = Some("THIS IS JSON STRING")
        ),
        vehicleBookingTrip = VehicleModelBookingTrip(
          vehicleBookingId = 1,
          vehicleCode = "abc",
          vehicleName = "abc",
          classification = "abc",
          pickupDatetime = DateTime.parse("2019-08-02T16:01"),
          dropOffDatetime = DateTime.parse("2019-08-03T16:01"),
          pickupLocationId = 1,
          dropOffLocationId = 1,
          driverAge = 30,
          flightNo = None,
          recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
          recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
          supplierConfirmationCode = Some("abc"),
          customerAgeGroup = Some("driver-age"),
          securityDepositType = Some("security-deposit"),
          localRenter = Some("local-renter")
        ),
        vehicleFinancialBreakdowns = Seq.empty,
        vehicleBookingCancellation = Some(
          VehicleModelBookingCancellation(
            vehicleCancellationId = 112L,
            vehicleBookingId = 456L,
            actionId = 321L,
            cancellationReason = "Normal Cancellation flow",
            isForcedRefunded = false,
            forcedRefundReason = None,
            isPastDeparted = false,
            cancelledOnSupplier = true,
            isSupplierAcceptedRefund = true,
            supplierErrorMessage = None,
            recStatus = 1,
            recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
            recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
            recCreatedBy = Some("as-bcrepci-3a01"),
            recModifiedBy = "as-bcrepci-3a01"
          )
        ),
        vehicleInfo = Some(
          VehicleInfo(
            vehicleInfoId = 123L,
            vehicleBookingId = 321L,
            vehicleCode = "CAR-001",
            vehicleName = "Honda",
            vehicleClassification = "Mini",
            vehicleDoors = Some(4),
            vehicleSeats = Some(4),
            vehicleSuitcases = Some(1),
            vehicleTransmission = Some("vehicleTransmission"),
            vehicleIsAircon = Some(true),
            vehicleIsAirbag = Some(true),
            vehicleFuelType = Some("vehicleFuelType"),
            recCreatedWhen = Some(DateTime.parse("2019-08-02T16:01")),
            recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
            vehicleMileagePolicy = Some(
              VehicleMileagePolicy(
                freeDistance = 0,
                code = "",
                description = "Mock Car Fuel Info",
                charge = None,
                isFreeCoverage = true
              )
            ),
            vehicleFuelPolicy = Some(
              VehicleFuelPolicy(
                coverageType = "Full_To_Full",
                code = "",
                description = "fuel policy",
                charge = None,
                isFreeCoverage = true
              )
            ),
            imageUrl = Some("imageUrl"),
            pickUpSupplierOperationHours =
              Some(Seq(VehicleOperationSchedule("Monday", Seq(VehicleSupplierOperationHour("06:00", "00:00"))))),
            dropOffSupplierOperationHours =
              Some(Seq(VehicleOperationSchedule("Monday", Seq(VehicleSupplierOperationHour("06:00", "00:00"))))),
            providerIconUrl = Some("iconUrl"),
            acrissCode = Some("acrissCode")
          )
        ),
        baseBooking = None
      )
      val vehicleBookingState = VehicleBookingState(
        vehicleModelsInternal = Seq(vehicleModelInternal),
        payments = Seq.empty,
        bookingPayments = Seq.empty,
        itinerary = MultiProductItinerary(itineraryId = 1, memberId = 1),
        itineraryHistories = Seq.empty,
        multiProductBookingGroups = Seq(mockMultiProductBookingGroup)
      )
      val bookingWorkflowAction = BookingWorkflowAction(
        actionId = 1,
        itineraryId = 1,
        bookingType = Some(1),
        bookingId = Some(1),
        memberId = 1,
        actionTypeId = 1,
        correlationId = "1",
        requestId = "1",
        workflowId = 1,
        workflowStateId = 1,
        productTypeId = Some(1),
        stateSchemaVersion = 1,
        state = "",
        storefrontId = Some(1),
        languageId = Some(1)
      )
      val multiProductInfos = Seq()
      val bookingStateMessageCaptor: ArgumentCaptor[BookingStateMessage] =
        ArgumentCaptor.forClass(classOf[BookingStateMessage])
      repository.sendVehicleModelForReplication(vehicleBookingState, bookingWorkflowAction, multiProductInfos).map {
        _ =>
          {
            verify(hadoopServiceMock, times(1))
              .sendMessage(
                bookingStateMessageCaptor.capture()
              )
            succeed
          }
          val message = bookingStateMessageCaptor.getValue
          val expected = BookingStateMessage(
            actionType = 1,
            actionId = 1,
            bookingType = None,
            bookingId = 0, // legacy field, we replicate itinerary which can contain multiple bookingIds
            schemaVersion = "1",
            flights = Seq.empty,
            slices = Seq.empty,
            segments = Seq.empty,
            passengers = Seq.empty,
            payments = Seq.empty,
            bookingPayments = Seq.empty,
            bookingRelationships = Seq.empty,
            breakdown = Seq.empty, // vehicle's breakdowns are in `vehicles.vehicleFinancialBreakdowns`
            breakdownPerPax = Seq.empty,
            baggageAllowance = Seq.empty,
            baggage = Seq.empty,
            history = Seq.empty,
            summary = Seq.empty,
            paxTickets = Seq.empty,
            itinerary = FlightItineraryForMessage(
              itineraryId = 1,
              memberId = 1,
              recStatus = None,
              recCreatedWhen = None,
              recModifiedWhen = None
            ),
            userAgent = None,
            bookingAttribution = Seq.empty,
            itineraryDate = new Date(), // this field is not used by vehicles, defaults to current time
            protectionModels = None,
            multiProductInfos = None,
            flightSegmentInfoByPaxType = Seq.empty,
            segmentInfoByPaxType = Seq.empty,
            fareRulePolicies = None,
            flightSeatSelection = Seq.empty,
            vehicle = Some(
              Seq(
                VehiclesForMessage(
                  vehicleBooking =
                    VehicleModelBookingToVehicleBookingForMessageConverter(vehicleModelInternal.vehicleBooking),
                  vehicleBookingLocation = VehicleModelBookingLocationsToVehicleBookingLocationsForMessageConverter(
                    vehicleModelInternal.vehicleBookingLocation
                  ),
                  vehicleBookingSummary = VehicleModelBookingSummaryToVehicleBookingSummaryForMessageConverter(
                    vehicleModelInternal.vehicleBookingSummary
                  ),
                  vehicleBookingTrip = VehicleModelBookingTripToVehicleBookingTripForMessageConverter(
                    vehicleModelInternal.vehicleBookingTrip
                  ),
                  vehicleFinancialBreakdowns = BookingStateMessage
                    .mapBreakdownToBreakdownForMessage(vehicleModelInternal.vehicleFinancialBreakdowns),
                  vehicleBookingCancellation = vehicleModelInternal.vehicleBookingCancellation.map(cancellation =>
                    VehicleModelBookingCancellationToVehicleBookingCancellationForMessageConverter(cancellation)
                  ),
                  vehicleInfo =
                    vehicleModelInternal.vehicleInfo.map(info => VehicleModelInfoToVehicleInfoMessageConverter(info))
                )
              )
            ),
            activities = None,
            properties = None,
            cegFastTracks = None,
            addOns = None,
            multiProductBookingGroups = Some(
              Seq(
                MultiProductBookingGroupModelMessage(
                  bookingId = mockMultiProductBookingGroup.bookingId,
                  itineraryId = mockMultiProductBookingGroup.itineraryId,
                  cartId = mockMultiProductBookingGroup.cartId,
                  packageId = mockMultiProductBookingGroup.packageId
                )
              )
            ),
            flightBrandSelections = None,
            flightBrandAttributes = None,
            flightBrandAttributeParams = None,
            flightBaseBooking = None,
            flightBaseCancellationInfo = None,
            crossProductIsolatedFeature = None
          )
          message shouldBe expected.copy(itineraryDate = message.itineraryDate)
      }
    }
  }

  trait GetVehicleBookingStateFixture extends Fixture {
    val vehicleBookingLocation = baseVehicleBookingState.vehicleModelsInternal.head.vehicleBookingLocation
    val updatedVehicleBookingTrip = baseVehicleBookingState.vehicleModelsInternal.head.vehicleBookingTrip.copy(
      pickupLocationId = vehicleBookingLocation.pickUp.vehicleBookingLocationId,
      dropOffLocationId = vehicleBookingLocation.dropOff.vehicleBookingLocationId
    )

    val updatedVehicleModelsInternal =
      baseVehicleBookingState.vehicleModelsInternal.head.copy(vehicleBookingTrip = updatedVehicleBookingTrip)

    val bookingState = baseVehicleBookingState.copy(
      vehicleModelsInternal = Seq(updatedVehicleModelsInternal),
      multiProductBookingGroups = Seq(mockMultiProductBookingGroup)
    )
  }

  "successful getVehicleBookingState" in {
    val fixture = new GetVehicleBookingStateFixture {}
    import fixture._

    when(vehicleDbProxyMock.getVehicleBookingByItineraryId(itineraryId))
      .thenReturn(Future.successful(bookingState.vehicleModelsInternal.map(_.vehicleBooking).toList))

    when(vehicleDbProxyMock.getItineraryPaymentByItineraryId(itineraryId))
      .thenReturn(Future.successful(bookingState.payments))
    when(vehicleDbProxyMock.getBookingPaymentsByPaymentIds(Seq(paymentId)))
      .thenReturn(Future.successful(bookingState.bookingPayments))
    when(vehicleDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
      .thenReturn(Future.successful(bookingState.itineraryHistories))
    when(vehicleDbProxyMock.getMultiProductItinerary(itineraryId))
      .thenReturn(Future.successful(Some(bookingState.itinerary)))

    when(vehicleDbProxyMock.getVehicleBookingSummaryByBookingId(bookingId))
      .thenReturn(Future.successful(Some(bookingState.vehicleModelsInternal.head.vehicleBookingSummary)))

    when(vehicleDbProxyMock.getVehicleBookingTripByBookingId(bookingId))
      .thenReturn(Future.successful(Some(bookingState.vehicleModelsInternal.head.vehicleBookingTrip)))
    when(vehicleDbProxyMock.getVehicleBookingLocationsByBookingId(bookingId)).thenReturn(
      Future.successful(
        List(
          bookingState.vehicleModelsInternal.head.vehicleBookingLocation.pickUp,
          bookingState.vehicleModelsInternal.head.vehicleBookingLocation.dropOff
        )
      )
    )
    when(vehicleDbProxyMock.getFinancialBreakdown(bookingId))
      .thenReturn(Future.successful(bookingState.vehicleModelsInternal.head.vehicleFinancialBreakdowns))

    when(vehicleDbProxyMock.getVehicleBookingCancellationByBookingId(bookingId))
      .thenReturn(Future.successful(bookingState.vehicleModelsInternal.head.vehicleBookingCancellation))

    when(multiProductRepository.getMultiProductBookingGroup(bookingId))
      .thenReturn(Future.successful(Some(mockMultiProductBookingGroup)))

    when(vehicleDbProxyMock.getVehicleInfoByBookingId(bookingId))
      .thenReturn(Future.successful(bookingState.vehicleModelsInternal.head.vehicleInfo))

    repository.getVehicleBookingState(bookingState.itinerary.itineraryId).map { result =>
      verify(vehicleDbProxyMock).getVehicleBookingByItineraryId(itineraryId)
      verify(vehicleDbProxyMock).getItineraryPaymentByItineraryId(itineraryId)
      verify(vehicleDbProxyMock).getBookingPaymentsByPaymentIds(Seq(paymentId))
      verify(vehicleDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
      verify(vehicleDbProxyMock).getMultiProductItinerary(itineraryId)
      verify(vehicleDbProxyMock).getVehicleBookingSummaryByBookingId(bookingId)
      verify(vehicleDbProxyMock).getVehicleBookingTripByBookingId(bookingId)
      verify(vehicleDbProxyMock).getVehicleBookingLocationsByBookingId(bookingId)
      verify(vehicleDbProxyMock).getFinancialBreakdown(bookingId)
      verify(vehicleDbProxyMock).getFinancialBreakdown(bookingId)
      verify(multiProductRepository).getMultiProductBookingGroup(bookingId)

      result shouldEqual bookingState
    }
  }

  "getVehicleBookingState with VehicleUnableToFindBookingSummaryException" in {
    val fixture = new GetVehicleBookingStateFixture {}
    import fixture._

    when(vehicleDbProxyMock.getVehicleBookingByItineraryId(itineraryId))
      .thenReturn(Future.successful(bookingState.vehicleModelsInternal.map(_.vehicleBooking).toList))

    when(vehicleDbProxyMock.getItineraryPaymentByItineraryId(itineraryId))
      .thenReturn(Future.successful(bookingState.payments))
    when(vehicleDbProxyMock.getBookingPaymentsByPaymentIds(Seq(paymentId)))
      .thenReturn(Future.successful(bookingState.bookingPayments))
    when(vehicleDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
      .thenReturn(Future.successful(bookingState.itineraryHistories))
    when(vehicleDbProxyMock.getMultiProductItinerary(itineraryId))
      .thenReturn(Future.successful(Some(bookingState.itinerary)))

    when(vehicleDbProxyMock.getVehicleBookingSummaryByBookingId(bookingId))
      .thenReturn(Future.successful(None))

    when(vehicleDbProxyMock.getVehicleBookingTripByBookingId(bookingId))
      .thenReturn(Future.successful(Some(bookingState.vehicleModelsInternal.head.vehicleBookingTrip)))
    when(vehicleDbProxyMock.getVehicleBookingLocationsByBookingId(bookingId)).thenReturn(
      Future.successful(
        List(
          bookingState.vehicleModelsInternal.head.vehicleBookingLocation.pickUp,
          bookingState.vehicleModelsInternal.head.vehicleBookingLocation.dropOff
        )
      )
    )
    when(vehicleDbProxyMock.getFinancialBreakdown(bookingId))
      .thenReturn(Future.successful(bookingState.vehicleModelsInternal.head.vehicleFinancialBreakdowns))
    when(vehicleDbProxyMock.getVehicleInfoByBookingId(bookingId))
      .thenReturn(Future.successful(bookingState.vehicleModelsInternal.head.vehicleInfo))

    repository.getVehicleBookingState(bookingState.itinerary.itineraryId).failed.map { failure =>
      verify(vehicleDbProxyMock).getVehicleBookingByItineraryId(itineraryId)
      verify(vehicleDbProxyMock).getItineraryPaymentByItineraryId(itineraryId)
      verify(vehicleDbProxyMock).getBookingPaymentsByPaymentIds(Seq(paymentId))
      verify(vehicleDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
      verify(vehicleDbProxyMock).getMultiProductItinerary(itineraryId)
      verify(vehicleDbProxyMock).getVehicleBookingSummaryByBookingId(bookingId)
      verify(vehicleDbProxyMock).getVehicleInfoByBookingId(bookingId)

      failure shouldBe a[VehicleUnableToFindBookingSummaryException]
    }
  }

  "getVehicleBookingState with VehicleUnableToFindItineraryException" in {
    val fixture = new GetVehicleBookingStateFixture {}
    import fixture._

    when(vehicleDbProxyMock.getVehicleBookingByItineraryId(itineraryId))
      .thenReturn(Future.successful(bookingState.vehicleModelsInternal.map(_.vehicleBooking).toList))
    when(vehicleDbProxyMock.getItineraryPaymentByItineraryId(itineraryId))
      .thenReturn(Future.successful(bookingState.payments))
    when(vehicleDbProxyMock.getBookingPaymentsByPaymentIds(Seq(paymentId)))
      .thenReturn(Future.successful(bookingState.bookingPayments))
    when(vehicleDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
      .thenReturn(Future.successful(bookingState.itineraryHistories))
    when(vehicleDbProxyMock.getMultiProductItinerary(itineraryId))
      .thenReturn(Future.successful(None))

    repository.getVehicleBookingState(bookingState.itinerary.itineraryId).failed.map { failure =>
      verify(vehicleDbProxyMock).getVehicleBookingByItineraryId(itineraryId)
      verify(vehicleDbProxyMock).getItineraryPaymentByItineraryId(itineraryId)
      verify(vehicleDbProxyMock).getBookingPaymentsByPaymentIds(Seq(paymentId))
      verify(vehicleDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
      verify(vehicleDbProxyMock).getMultiProductItinerary(itineraryId)

      failure shouldBe a[VehicleUnableToFindItineraryException]
    }
  }

  "getVehicleBookingState with VehicleUnableToFindBookingTripException" in {
    val fixture = new GetVehicleBookingStateFixture {}
    import fixture._

    when(vehicleDbProxyMock.getVehicleBookingByItineraryId(itineraryId))
      .thenReturn(Future.successful(bookingState.vehicleModelsInternal.map(_.vehicleBooking).toList))

    when(vehicleDbProxyMock.getItineraryPaymentByItineraryId(itineraryId))
      .thenReturn(Future.successful(bookingState.payments))
    when(vehicleDbProxyMock.getBookingPaymentsByPaymentIds(Seq(paymentId)))
      .thenReturn(Future.successful(bookingState.bookingPayments))
    when(vehicleDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
      .thenReturn(Future.successful(bookingState.itineraryHistories))
    when(vehicleDbProxyMock.getMultiProductItinerary(itineraryId))
      .thenReturn(Future.successful(Some(bookingState.itinerary)))

    when(vehicleDbProxyMock.getVehicleBookingSummaryByBookingId(bookingId))
      .thenReturn(Future.successful(Some(bookingState.vehicleModelsInternal.head.vehicleBookingSummary)))

    when(vehicleDbProxyMock.getVehicleBookingTripByBookingId(bookingId))
      .thenReturn(Future.successful(None))
    when(vehicleDbProxyMock.getVehicleBookingLocationsByBookingId(bookingId)).thenReturn(
      Future.successful(
        List(
          bookingState.vehicleModelsInternal.head.vehicleBookingLocation.pickUp,
          bookingState.vehicleModelsInternal.head.vehicleBookingLocation.dropOff
        )
      )
    )
    when(vehicleDbProxyMock.getFinancialBreakdown(bookingId))
      .thenReturn(Future.successful(bookingState.vehicleModelsInternal.head.vehicleFinancialBreakdowns))
    when(vehicleDbProxyMock.getVehicleInfoByBookingId(bookingId))
      .thenReturn(Future.successful(bookingState.vehicleModelsInternal.head.vehicleInfo))

    repository.getVehicleBookingState(bookingState.itinerary.itineraryId).failed.map { failure =>
      verify(vehicleDbProxyMock).getVehicleBookingByItineraryId(itineraryId)
      verify(vehicleDbProxyMock).getItineraryPaymentByItineraryId(itineraryId)
      verify(vehicleDbProxyMock).getBookingPaymentsByPaymentIds(Seq(paymentId))
      verify(vehicleDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
      verify(vehicleDbProxyMock).getMultiProductItinerary(itineraryId)
      verify(vehicleDbProxyMock).getVehicleBookingTripByBookingId(bookingId)
      verify(vehicleDbProxyMock).getVehicleInfoByBookingId(bookingId)

      failure shouldBe a[VehicleUnableToFindBookingTripException]
    }
  }

  "save vehicle state with stale update" in {
    val fixture = new Fixture {}
    import fixture._

    val vehicleBookingStateToSave = baseVehicleBookingState.copy(
      itineraryHistories = Seq(random[ItineraryHistory].copy(actionId = 10, version = 0)),
      vehicleModelsInternal = updatedVehicleModelInternal
    )

    val mpGroup = random[MultiProductBookingGroupDBModel]
    when(multiProductRepository.getMultiProductBookingGroupByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(mpGroup)))
    when(vehicleDbProxyMock.getItineraryActionHistoryByItineraryId(any()))
      .thenReturn(Future.successful(Seq(baseItineraryHistory.copy(version = 1))))

    doReturn(Future.failed(new Exception("stale update was detected")), null)
      .when(repository)
      .getVehicleBookingState(any())
    doReturn(Future.failed(new Exception("version collision detected")), null)
      .when(repository)
      .reportVersionCollision(any())

    recoverToExceptionIf[Exception](repository.saveVehicleBookingState(vehicleBookingStateToSave)).map { failure =>
      verify(vehicleDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
      failure.getMessage shouldEqual "stale update was detected"
    }
  }

  "save vehicle state with version collision" in {
    val fixture = new Fixture {}
    import fixture._

    val vehicleBookingStateToSave = baseVehicleBookingState.copy(
      itineraryHistories = Seq(random[ItineraryHistory].copy(actionId = 10, version = 1)),
      vehicleModelsInternal = updatedVehicleModelInternal
    )
    val mpGroup = random[MultiProductBookingGroupDBModel]
    when(multiProductRepository.getMultiProductBookingGroupByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(mpGroup)))

    when(vehicleDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
      .thenReturn(Future.successful(Seq(baseItineraryHistory.copy(version = 1))))

    doReturn(Future.failed(new Exception("version collision detected")), null)
      .when(repository)
      .reportVersionCollision(any())

    recoverToExceptionIf[Exception](repository.saveVehicleBookingState(vehicleBookingStateToSave)).map { failure =>
      verify(vehicleDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
      failure.getMessage shouldEqual "version collision detected"
    }
  }

  "checkDuplicateVehicleBooking" should {

    trait DuplicatedCheckFixture extends Fixture {
      val identifier          = "12345"
      val pickUpDateTime      = DateTime.parse("2019-08-02T16:00")
      val dropOffDateTime     = DateTime.parse("2019-08-06T16:00")
      val pickUpLocationCode  = "1234567"
      val dropOffLocationCode = "7654321"

      val duplicatedBookingList = List(
        DuplicateBooking(
          bookingElement = BookingElement.Vehicle,
          bookingId = 1,
          bookingDate = DateTime.parse("2019-08-01T16:00")
        )
      )

      val duplicatedBookingRecord = DuplicateBookingRecord(
        bookingId = 1,
        bookingDate = DateTime.parse("2019-08-01T16:00"),
        storeFrontId = 1,
        languageId = 1
      )
    }

    "return result correctly when found duplicate booking in bfdb" in {
      val fixture = new DuplicatedCheckFixture {}
      import fixture._

      when(
        vehicleDbProxyMock
          .getDuplicateVehicleBookings(
            identifier,
            pickUpDateTime,
            dropOffDateTime,
            pickUpLocationCode,
            dropOffLocationCode
          )
      ).thenReturn(Future.successful(Seq(duplicatedBookingRecord)))

      repository
        .checkVehicleDuplicate(identifier, pickUpDateTime, dropOffDateTime, pickUpLocationCode, dropOffLocationCode)
        .map { result =>
          verify(vehicleDbProxyMock)
            .getDuplicateVehicleBookings(
              identifier,
              pickUpDateTime,
              dropOffDateTime,
              pickUpLocationCode,
              dropOffLocationCode
            )
          result shouldEqual duplicatedBookingList
        }
    }

    "return empty result when not found any duplicate booking in db" in {
      val fixture = new DuplicatedCheckFixture {}
      import fixture._

      when(
        vehicleDbProxyMock
          .getDuplicateVehicleBookings(
            identifier,
            pickUpDateTime,
            dropOffDateTime,
            pickUpLocationCode,
            dropOffLocationCode
          )
      ).thenReturn(Future.successful(Seq.empty))

      repository
        .checkVehicleDuplicate(identifier, pickUpDateTime, dropOffDateTime, pickUpLocationCode, dropOffLocationCode)
        .map { result =>
          verify(vehicleDbProxyMock)
            .getDuplicateVehicleBookings(
              identifier,
              pickUpDateTime,
              dropOffDateTime,
              pickUpLocationCode,
              dropOffLocationCode
            )
          result shouldEqual Seq.empty
        }
    }
  }

  "checkDuplicateVehicleBookingV2" should {

    trait DuplicatedCheckFixture extends Fixture {
      val identifier          = "12345"
      val pickUpDateTime      = DateTime.parse("2019-08-02T16:00")
      val dropOffDateTime     = DateTime.parse("2019-08-06T16:00")
      val pickUpLocationCode  = "1234567"
      val dropOffLocationCode = "7654321"

      val duplicatedBookingList = List(
        DuplicateBooking(
          bookingElement = BookingElement.Vehicle,
          bookingId = 1,
          bookingDate = DateTime.parse("2019-08-01T16:00")
        )
      )

      val duplicatedBookingRecord = DuplicateBookingRecord(
        bookingId = 1,
        bookingDate = DateTime.parse("2019-08-01T16:00"),
        storeFrontId = 1,
        languageId = 1
      )
    }

    "return result correctly when found duplicate booking in bkgdb" in {
      val fixture = new DuplicatedCheckFixture {}
      import fixture._

      when(
        vehicleDbReadProxyMock
          .getDuplicateVehicleBookings(
            identifier,
            pickUpDateTime,
            dropOffDateTime,
            pickUpLocationCode,
            dropOffLocationCode
          )
      ).thenReturn(Future.successful(Seq(duplicatedBookingRecord)))

      repository
        .checkVehicleDuplicateV2(identifier, pickUpDateTime, dropOffDateTime, pickUpLocationCode, dropOffLocationCode)
        .map { result =>
          verify(vehicleDbReadProxyMock)
            .getDuplicateVehicleBookings(
              identifier,
              pickUpDateTime,
              dropOffDateTime,
              pickUpLocationCode,
              dropOffLocationCode
            )
          verifyNoInteractions(vehicleDbProxyMock)

          result shouldEqual duplicatedBookingList
        }
    }

    "return result correctly when found duplicate booking in fallback db" in {
      val fixture = new DuplicatedCheckFixture {}
      import fixture._

      when(
        vehicleDbReadProxyMock
          .getDuplicateVehicleBookings(
            identifier,
            pickUpDateTime,
            dropOffDateTime,
            pickUpLocationCode,
            dropOffLocationCode
          )
      ).thenReturn(
        Future.failed(DbException(ErrorCode.UnexpectedDbError, None)),
        Future.successful(Seq(duplicatedBookingRecord))
      )

      repository
        .checkVehicleDuplicateV2(identifier, pickUpDateTime, dropOffDateTime, pickUpLocationCode, dropOffLocationCode)
        .map { result =>
          verify(vehicleDbReadProxyMock, times(2))
            .getDuplicateVehicleBookings(
              identifier,
              pickUpDateTime,
              dropOffDateTime,
              pickUpLocationCode,
              dropOffLocationCode
            )
          verifyNoInteractions(vehicleDbProxyMock)

          result shouldEqual duplicatedBookingList
        }
    }

    "return empty result when not found any duplicate booking in db" in {
      val fixture = new DuplicatedCheckFixture {}
      import fixture._

      when(
        vehicleDbReadProxyMock
          .getDuplicateVehicleBookings(
            identifier,
            pickUpDateTime,
            dropOffDateTime,
            pickUpLocationCode,
            dropOffLocationCode
          )
      ).thenReturn(Future.successful(Seq.empty), Future.successful(Seq.empty))

      repository
        .checkVehicleDuplicateV2(identifier, pickUpDateTime, dropOffDateTime, pickUpLocationCode, dropOffLocationCode)
        .map { result =>
          verify(vehicleDbReadProxyMock)
            .getDuplicateVehicleBookings(
              identifier,
              pickUpDateTime,
              dropOffDateTime,
              pickUpLocationCode,
              dropOffLocationCode
            )
          verifyNoInteractions(vehicleDbProxyMock)

          result shouldBe empty
        }
    }

    "return failure when both dbs go down" in {
      val fixture = new DuplicatedCheckFixture {}
      import fixture._

      when(
        vehicleDbReadProxyMock
          .getDuplicateVehicleBookings(
            identifier,
            pickUpDateTime,
            dropOffDateTime,
            pickUpLocationCode,
            dropOffLocationCode
          )
      ).thenReturn(
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(new SQLException("bkgdb simulated failure")))),
        Future.failed(DbException(ErrorCode.UnexpectedDbError, Some(new SQLException("bapidb simulated failure"))))
      )

      recoverToSucceededIf[DbException](
        repository.checkVehicleDuplicateV2(
          identifier,
          pickUpDateTime,
          dropOffDateTime,
          pickUpLocationCode,
          dropOffLocationCode
        )
      ).map { success =>
        verify(vehicleDbReadProxyMock, times(2))
          .getDuplicateVehicleBookings(
            identifier,
            pickUpDateTime,
            dropOffDateTime,
            pickUpLocationCode,
            dropOffLocationCode
          )
        verifyNoInteractions(vehicleDbProxyMock)
        success
      }
    }
  }
}
