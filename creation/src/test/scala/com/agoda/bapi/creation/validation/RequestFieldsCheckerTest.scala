package com.agoda.bapi.creation.validation

import com.agoda.bapi.common.MockRequestContext
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation._
import com.agoda.bapi.common.model.booking.RequiredFieldMetadata
import com.agoda.bapi.common.model.payment.PaymentContinuation
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{<PERSON>quired<PERSON><PERSON>, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.CreateMultiBookingHelper
import com.agoda.bapi.creation.model.{MultiProduct, RequestWithProducts}
import com.agoda.mpb.common.DmcId
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpbe.state.common.enums.Gateway.Gateway
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import com.softwaremill.quicklens._
import generated.model.DmcSettingModel
import org.joda.time.LocalDate
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito
import org.mockito.Mockito.{times, verify, when}
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.{BeforeAndAfterAll, OptionValues}
import org.scalatestplus.mockito.MockitoSugar

import java.nio.charset.StandardCharsets
import java.util.Base64
import scala.util.Random

class RequestFieldsCheckerTest
    extends AnyWordSpec
    with Matchers
    with MockitoSugar
    with BeforeAndAfterAll
    with CreateMultiBookingHelper
    with WhiteLabelFeatureMock
    with OptionValues {

  val requestChecker                 = new RequestFieldsCheckerImpl
  val requestContext: RequestContext = MockRequestContext.create()

  val mockRurubuDmcSetting = DmcSettingModel(
    dmcId = Some(29014),
    customerName = Some("^([\\u3000-\\u303F\\u3041-\\u309F\\u30A0-\\u30FF\\u4E00-\\u9FAF]+)$")
  )

  val mockFeaturesConfigurationForRurubu = featuresConfiguration(
    customerNameRegex = Some("^([\\u3000-\\u303F\\u3041-\\u309F\\u30A0-\\u30FF\\u4E00-\\u9FAF]+)$"),
    dmcSetting = Some(List(mockRurubuDmcSetting))
  )

  val mockJTBDmcSetting =
    DmcSettingModel(dmcId = Some(29014), customerName = Some("[\\p{L}\\p{N}\\p{P}\\p{Z}\\s\\-\\.()&\\'`]+"))

  val mockFeaturesConfigurationForJTB =
    featuresConfiguration(
      customerNameRegex = Some("[\\p{L}\\p{N}\\p{P}\\p{Z}\\s\\-\\.()&\\'`]+"),
      dmcSetting = Some(List(mockJTBDmcSetting))
    )

  val mockFeaturesConfigurationForClubTravel =
    featuresConfiguration(customerNameRegex = None)

  "Unit Test of Creation Booking Request Fields" should {

    "Guests" in {
      // Empty Guest List
      val mockRequest1 = mock[CreateBookingRequest]

      when(mockRequest1.guestList).thenReturn(Nil)
      assert(requestChecker.validateGuestList(mockRequest1) == List(ErrorCode.GuestListIsEmpty))

      // No primary Guest
      val mockRequest2 = mock[CreateBookingRequest]

      when(mockRequest2.guestList).thenReturn(
        List(
          HotelGuest(1, "xxx", "James", "Bound", primary = false, email = Some("<EMAIL>"), nationalityId = 106)
        )
      )
      assert(requestChecker.validateGuestList(mockRequest2) == List(ErrorCode.InvalidPrimaryGuest))

      // Primary Guest is not adult
      val mockRequest3 = mock[CreateBookingRequest]

      when(mockRequest3.guestList).thenReturn(
        List(
          HotelGuest(
            1,
            "xxx",
            "James",
            "Bound",
            primary = true,
            isAdult = false,
            email = Some("<EMAIL>"),
            nationalityId = 106
          )
        )
      )
      assert(requestChecker.validateGuestList(mockRequest3) == List(ErrorCode.PrimaryGuestMustBeAdult))

      // Duplicate primary guest
      val mockRequest4 = mock[CreateBookingRequest]

      when(mockRequest4.guestList).thenReturn(
        List(
          HotelGuest(1, "xxx", "James", "Bound", primary = true, email = Some("<EMAIL>"), nationalityId = 106),
          HotelGuest(1, "xxx", "Tom", "Cruise", primary = true, email = Some("<EMAIL>"), nationalityId = 106)
        )
      )
      assert(requestChecker.validateGuestList(mockRequest4) == List(ErrorCode.DuplicatedPrimaryGuest))

      // Guest First Name Is Empty
      val mockRequest5 = mock[CreateBookingRequest]

      when(mockRequest5.guestList)
        .thenReturn(
          List(HotelGuest(1, "xxx", "", "Bound", primary = true, email = Some("<EMAIL>"), nationalityId = 106))
        )
      assert(requestChecker.validateGuestList(mockRequest5) == List(ErrorCode.GuestFirstNameIsEmpty))

      when(mockRequest5.guestList)
        .thenReturn(
          List(HotelGuest(1, "xxx", null, "Bound", primary = true, email = Some("<EMAIL>"), nationalityId = 106))
        )
      assert(requestChecker.validateGuestList(mockRequest5) == List(ErrorCode.GuestFirstNameIsEmpty))

      // Guest Last Name Is Empty
      val mockRequest6 = mock[CreateBookingRequest]

      when(mockRequest6.guestList)
        .thenReturn(
          List(HotelGuest(1, "xxx", "James", "", primary = true, email = Some("<EMAIL>"), nationalityId = 106))
        )
      assert(requestChecker.validateGuestList(mockRequest6) == List(ErrorCode.GuestLastNameIsEmpty))

      when(mockRequest6.guestList)
        .thenReturn(
          List(HotelGuest(1, "xxx", "James", null, primary = true, email = Some("<EMAIL>"), nationalityId = 106))
        )
      assert(requestChecker.validateGuestList(mockRequest6) == List(ErrorCode.GuestLastNameIsEmpty))

      // Invalid Guest Email Format
      val mockRequest7 = mock[CreateBookingRequest]

      when(mockRequest7.guestList)
        .thenReturn(
          List(HotelGuest(1, "xxx", "James", "Bound", primary = true, email = Some("test.com"), nationalityId = 106))
        )
      assert(requestChecker.validateGuestList(mockRequest7) == List(ErrorCode.InvalidGuestEmailFormat))

      // Invalid Nationality Id
      val mockRequest8 = mock[CreateBookingRequest]

      when(mockRequest8.guestList).thenReturn(
        List(HotelGuest(1, "xxx", "James", "Bound", primary = true, email = Some("<EMAIL>"), nationalityId = 0))
      )
      assert(requestChecker.validateGuestList(mockRequest8) == List(ErrorCode.InvalidNationalityId))

      // Email with hyphen suffix
      val mockRequest9 = mock[CreateBookingRequest]
      when(mockRequest9.guestList)
        .thenReturn(
          List(
            HotelGuest(1, "xxx", "James", "Bound", primary = true, email = Some("<EMAIL>"), nationalityId = 106)
          )
        )
      assert(requestChecker.validateGuestList(mockRequest9, acceptEmailWithHyphenSuffix = true) == List.empty)

      // Email with special character
      val mockRequest10 = mock[CreateBookingRequest]
      when(mockRequest10.guestList)
        .thenReturn(
          List(
            HotelGuest(
              1,
              "xxx",
              "James",
              "Bound",
              primary = true,
              email = Some("<EMAIL>"),
              nationalityId = 106
            )
          )
        )
      assert(
        requestChecker.validateGuestList(
          mockRequest10,
          acceptEmailWithHyphenSuffix = true
        ) == List.empty
      )
    }

    "Products" in {
      // Product List Is Empty
      val requestWithProducts  = mock[RequestWithProducts]
      val createBookingRequest = mock[CreateBookingRequest]

      when(requestWithProducts.request).thenReturn(createBookingRequest)
      when(createBookingRequest.products).thenReturn(Products(Some(Nil)))
      assert(requestChecker.validateProduct(requestWithProducts) == List(ErrorCode.PropertyListIsEmpty))
    }

    "Allow having only flight product in cart" in {
      val requestWithProducts = mock[RequestWithProducts]

      when(requestWithProducts.bookingFlow).thenReturn(BookingFlow.Cart)
      when(requestWithProducts.request).thenReturn(baseFlightRequest)

      assert(requestChecker.validateProduct(requestWithProducts) == List())
    }

    "Credit Card" in {

      val mockCreditCard = mock[CreditCard]
      val mockRequest1   = Mockito.mock(classOf[CreateBookingRequest], Mockito.RETURNS_DEEP_STUBS)

      when(mockRequest1.payment.creditCard).thenReturn(Some(mockCreditCard))
      when(mockCreditCard.issueBankCountryId).thenReturn(179) // Invalid Country Id
      when(mockCreditCard.billingCountryId).thenReturn(Some(106))
      assert(requestChecker.validateCreditCard(mockRequest1) == List(ErrorCode.IssuedBankCountryIsRestricted))

      when(mockCreditCard.issueBankCountryId).thenReturn(106)
      when(mockCreditCard.billingCountryId).thenReturn(Some(179)) // Invalid Country Id
      assert(requestChecker.validateCreditCard(mockRequest1) == List(ErrorCode.BillingCountryIsRestricted))
    }

    "Payment Continuation" in {
      val mockRequest = Mockito.mock(classOf[CreateBookingRequest], Mockito.RETURNS_DEEP_STUBS)

      val paymentContinuation = mock[PaymentContinuation]

      when(paymentContinuation.transactionId).thenReturn("")
      when(paymentContinuation.gatewayId).thenReturn(Gateway.None)
      when(mockRequest.payment.continuation).thenReturn(Some(paymentContinuation))
      assert(requestChecker.validateGateWayId(mockRequest).isEmpty)

      when(paymentContinuation.transactionId).thenReturn("")
      when(paymentContinuation.gatewayId).thenReturn(Gateway.Alipay)
      assert(requestChecker.validateGateWayId(mockRequest).isEmpty)

      when(paymentContinuation.transactionId).thenReturn("ANY_TRANSACTION_ID")
      when(paymentContinuation.gatewayId).thenReturn(Gateway.Alipay)
      assert(requestChecker.validateGateWayId(mockRequest).isEmpty)

      when(paymentContinuation.transactionId).thenReturn("ANY_TRANSACTION_ID")
      when(paymentContinuation.gatewayId).thenReturn(Gateway.None)
      assert(requestChecker.validateGateWayId(mockRequest).contains(ErrorCode.InvalidGatewayID))
    }

    "Customer" in {
      val mockRequest = mock[CreateBookingRequest]
      when(mockRequest.customer)
        .thenReturn(Customer(firstname = "", lastname = "Bound", email = "<EMAIL>", countryId = 106))
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List(
          ErrorCode.InvalidCustomerFirstName
        )
      )

      when(mockRequest.customer)
        .thenReturn(Customer(firstname = "James", lastname = "", email = "<EMAIL>", countryId = 106))
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List(
          ErrorCode.InvalidCustomerLastName
        )
      )

      when(mockRequest.customer).thenReturn(Customer(firstname = "James", lastname = "Bound", countryId = 106))
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List(
          ErrorCode.RequiredCustomerEmail
        )
      )

      when(mockRequest.customer)
        .thenReturn(Customer(firstname = "James", lastname = "Bound", email = "test.com", countryId = 106))
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List(
          ErrorCode.InvalidCustomerEmailFormat
        )
      )

      // TODO: Get the invalid cases of CustomerFirstNameIsNotEnglish and CustomerLastNameIsNotEnglish

      when(mockRequest.customer)
        .thenReturn(Customer(firstname = "James", lastname = "Bound", email = "<EMAIL>", countryId = 179))
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List(
          ErrorCode.CounryIsRestricted
        )
      )

      when(mockRequest.customer)
        .thenReturn(Customer(firstname = "James", lastname = "Bound", email = "<EMAIL>", countryId = 0))
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List(
          ErrorCode.InvalidCustomerCountryID
        )
      )

      when(mockRequest.customer)
        .thenReturn(Customer(firstname = "James", lastname = "Bound", email = "<EMAIL>", countryId = 179))
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = true
        ) == List.empty
      )

      when(mockRequest.customer)
        .thenReturn(Customer(firstname = "James", lastname = "Bound", email = "<EMAIL>", countryId = 0))
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = true
        ) == List.empty
      )

      when(mockRequest.customer).thenReturn(
        Customer(memberId = 1234, firstname = "James", lastname = "Bound", countryId = 106)
      )
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List(
          ErrorCode.RequiredCustomerEmail
        )
      )

      when(mockRequest.customer).thenReturn(
        Customer(memberId = 1234, firstname = "James", lastname = "Bound", countryId = 106, email = "<EMAIL>")
      )
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List.empty
      )

      when(mockRequest.customer).thenReturn(
        Customer(memberId = 1234, firstname = "James", lastname = "Bound", countryId = 106, phoneFormat = "6621342134")
      )
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List.empty
      )

      when(mockRequest.customer).thenReturn(
        Customer(
          memberId = 1234,
          firstname = "<a href='https://www.scorpiones.io'>Click here!</a>",
          lastname = "Bound",
          countryId = 106,
          phoneFormat = "6621342134"
        )
      )
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List(
          ErrorCode.CustomerFirstNameIsNotEnglish
        )
      )

      when(mockRequest.customer).thenReturn(
        Customer(
          memberId = 1234,
          firstname = "James",
          lastname = "<a href='https://www.scorpiones.io'>Click here!</a>",
          countryId = 106,
          phoneFormat = "6621342134"
        )
      )
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List(
          ErrorCode.CustomerLastNameIsNotEnglish
        )
      )

      when(mockRequest.customer).thenReturn(
        Customer(
          memberId = 1234,
          firstname = "保全０１２３４５６７８９Ｊａｍｅｓ*********9James",
          lastname = "Bound",
          countryId = 106,
          phoneFormat = "6621342134"
        )
      )
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List.empty
      )

      when(mockRequest.customer).thenReturn(
        Customer(
          memberId = 1234,
          firstname = "James",
          lastname = "保全０１２３４５６７８９Ｂｏｕｎｄ*********9Bound",
          countryId = 106,
          phoneFormat = "6621342134"
        )
      )
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List.empty
      )

      when(mockRequest.customer).thenReturn(
        Customer(
          memberId = 1234,
          firstname = "Jānis",
          lastname = "Bērziņš",
          countryId = 106,
          phoneFormat = "6621342134"
        )
      )
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List.empty
      )

      when(mockRequest.customer).thenReturn(
        Customer(
          memberId = 1234,
          firstname = "幸子",
          lastname = "幸子",
          countryId = 106,
          phoneFormat = "6621342134"
        )
      )
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List.empty
      )

      when(mockRequest.customer).thenReturn(
        Customer(
          memberId = 1234,
          firstname = "アドッシー",
          lastname = "アドッシー",
          countryId = 106,
          phoneFormat = "6621342134"
        )
      )
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List.empty
      )

      when(mockRequest.customer).thenReturn(
        Customer(firstname = "James", lastname = "Bound", email = "<EMAIL>", countryId = 106)
      )
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          acceptEmailWithHyphenSuffix = true,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List.empty
      )

      when(mockRequest.customer).thenReturn(
        Customer(firstname = "James", lastname = "Bound", email = "<EMAIL>", countryId = 106)
      )
      assert(
        requestChecker.validateCustomer(
          mockRequest,
          acceptEmailWithHyphenSuffix = true,
          whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id,
          enableCountryOfResidenceValidation = false
        ) == List.empty
      )
    }
  }

  "validatePaxList" should {
    val mockValidator = mock[RequestFieldsCheckerImpl]
    "call all underlying validations" in {
      when(mockValidator.validatePaxList(any[CreateBookingRequest])(any[RequestContext])).thenCallRealMethod()
      when(mockValidator.validatePax(any[FlightPax])(any[RequestContext])).thenReturn(List.empty)
      when(mockValidator.isPaxListValid(any[Seq[FlightPax]])).thenReturn(None)
      when(mockValidator.isPaxPrimaryValid(any[Seq[FlightPax]])).thenReturn(None)
      val request = mock[CreateBookingRequest]
      val paxList = Seq(mock[FlightPax], mock[FlightPax])
      when(request.paxList).thenReturn(paxList)
      mockValidator.validatePaxList(request)(requestContext)
      verify(mockValidator, times(paxList.size)).validatePax(any[FlightPax])(any[RequestContext])
      verify(mockValidator, times(1)).isPaxListValid(any[Seq[FlightPax]])
      verify(mockValidator, times(1)).isPaxPrimaryValid(any[Seq[FlightPax]])
    }
  }
  "isPaxListValid" should {
    "return InvalidFlightPaxAmount when pax list is empty" in {
      requestChecker.isPaxListValid(Nil) shouldBe Some(ErrorCode.InvalidFlightPaxAmount)
    }
    "return None when pax list is not empty" in {
      val paxList = Seq.fill(20)(mock[FlightPax])
      requestChecker.isPaxListValid(paxList) shouldBe None
    }
  }

  "isPaxPrimaryValid" should {
    val mockValidator = mock[RequestFieldsCheckerImpl]
    when(mockValidator.isPaxPrimaryValid(any[Seq[FlightPax]])).thenCallRealMethod()

    "return InvalidPrimaryPax on empty primary list" in {
      val flightPax = mock[FlightPax]
      when(flightPax.primary).thenReturn(false)
      val paxList = Seq(flightPax)
      val result  = mockValidator.isPaxPrimaryValid(paxList)
      result shouldBe Some(ErrorCode.InvalidPrimaryPax)
    }

    "return InvalidPrimaryPax on two more primary pax" in {
      val flightPax1 = mock[FlightPax]
      val flightPax2 = mock[FlightPax]
      when(flightPax1.primary).thenReturn(true)
      when(flightPax2.primary).thenReturn(true)
      val paxList = Seq(flightPax1, flightPax2)
      val result  = mockValidator.isPaxPrimaryValid(paxList)
      result shouldBe Some(ErrorCode.DuplicatedPrimaryPax)
    }

    "return PrimaryPaxMustBeAdult on child primary list" in {
      val flightPax = mock[FlightPax]
      when(flightPax.primary).thenReturn(true)
      when(flightPax.isAdult).thenReturn(false)
      val paxList = Seq(flightPax)
      val result  = mockValidator.isPaxPrimaryValid(paxList)
      result shouldBe Some(ErrorCode.PrimaryPaxMustBeAdult)
    }
  }

  val defaultPax = FlightPax(birthDate = LocalDate.now())

  "validatePax" should {
    "call underlying validations and return result" in {
      val mockChecker = mock[RequestFieldsCheckerImpl]
      when(mockChecker.validatePax(any[FlightPax])(any[RequestContext])).thenCallRealMethod()
      when(mockChecker.isPaxFirstNameValid(any[FlightPax]))
        .thenReturn(Some(ErrorCode.InvalidFlightPaxFirstName)) // to check
      when(mockChecker.isPaxLastNameValid(any[FlightPax])).thenReturn(None)
      when(mockChecker.isPaxGenderValid(any[FlightPax])).thenReturn(None)
      // when(mockChecker.isPaxBirthDateValid(any[FlightPax])).thenReturn(None)
      when(mockChecker.isPaxNationalityValid(any[FlightPax])).thenReturn(None)
      when(mockChecker.isPaxPassportNumberValid(any[FlightPax])(any[RequestContext])).thenReturn(None)
      when(mockChecker.isPaxPassportExpiresDate(any[FlightPax])).thenReturn(None)
      when(mockChecker.isPaxKnownTravelerNumberValid(any[FlightPax])).thenReturn(None)
      val result = mockChecker.validatePax(defaultPax)(requestContext)
      verify(mockChecker).isPaxFirstNameValid(any[FlightPax])
      verify(mockChecker).isPaxLastNameValid(any[FlightPax])
      verify(mockChecker).isPaxGenderValid(any[FlightPax])
      // verify(mockChecker).isPaxBirthDateValid(any[FlightPax])
      verify(mockChecker).isPaxNationalityValid(any[FlightPax])
      verify(mockChecker).isPaxPassportNumberValid(any[FlightPax])(any[RequestContext])
      verify(mockChecker).isPaxPassportExpiresDate(any[FlightPax])
      verify(mockChecker).isPaxKnownTravelerNumberValid(any[FlightPax])
      result should contain(ErrorCode.InvalidFlightPaxFirstName)
    }
  }

  "isPaxFirstNameValid" should {
    "reject if empty" in {
      requestChecker.isPaxFirstNameValid(defaultPax.copy(firstname = "")) shouldBe Some(
        ErrorCode.InvalidFlightPaxFirstName
      )
    }
    // Keep for reference to return after MVP
    /* "reject if not match regex" in { val name = Random.nextString(201)
     * requestChecker.isPaxFirstNameValid(defaultPax.copy(firstname = name)) shouldBe
     * Some(ErrorCode.InvalidFlightPaxFirstName) } */
    "pass otherwise" in {
      requestChecker.isPaxFirstNameValid(defaultPax.copy(firstname = "ANDREY")) shouldBe empty
    }
  }

  "isPaxLastNameValid" should {
    "reject if empty" in {
      requestChecker.isPaxLastNameValid(defaultPax.copy(lastname = "")) shouldBe Some(
        ErrorCode.InvalidFlightPaxLastName
      )
    }
    // Keep for reference to return after MVP
    /* "reject if not match regex" in { val name = Random.nextString(201)
     * requestChecker.isPaxLastNameValid(defaultPax.copy(lastname = name)) shouldBe
     * Some(ErrorCode.InvalidFlightPaxLastName) } */
    "pass otherwise" in {
      requestChecker.isPaxLastNameValid(defaultPax.copy(lastname = "SMIRNOV")) shouldBe empty
    }
  }

  "isPaxGenderValid" should {
    "reject if empty" in {
      requestChecker.isPaxGenderValid(defaultPax.copy(gender = "")) shouldBe Some(ErrorCode.InvalidFlightPaxGender)
    }
    "reject if not match regex" in {
      requestChecker.isPaxGenderValid(defaultPax.copy(gender = "Z")) shouldBe Some(ErrorCode.InvalidFlightPaxGender)
    }
    "pass otherwise" in {
      requestChecker.isPaxGenderValid(defaultPax.copy(gender = "M")) shouldBe empty
    }
  }

  "isPaxBirthDateValid" should {
    "reject if adult < 18 years old" in {
      val date = LocalDate.now().minusYears(1)
      requestChecker.isPaxBirthDateValid(defaultPax.copy(birthDate = date, isAdult = true)) shouldBe Some(
        ErrorCode.InvalidFlightPaxBirthDate
      )
    }
    "reject if child > 18 years old" in {
      val date = LocalDate.now().minusYears(20)
      requestChecker.isPaxBirthDateValid(defaultPax.copy(birthDate = date, isAdult = false)) shouldBe Some(
        ErrorCode.InvalidFlightPaxBirthDate
      )
    }
    "pass otherwise" in {
      val date = LocalDate.now().minusYears(20)
      requestChecker.isPaxBirthDateValid(defaultPax.copy(birthDate = date, isAdult = true)) shouldBe empty
    }
  }
  "isPaxNationalityValid" should {
    "reject if 0" in {
      requestChecker.isPaxNationalityValid(defaultPax.copy(nationalityId = 0)) shouldBe Some(
        ErrorCode.InvalidFlightPaxNationality
      )
    }
    "pass otherwise" in {
      requestChecker.isPaxNationalityValid(defaultPax.copy(nationalityId = 1)) shouldBe empty
    }
  }

  "isPaxPassportNumberValid" should {
    "pass if empty" in {
      requestChecker.isPaxPassportNumberValid(defaultPax.copy(passportNumber = None))(requestContext) shouldBe empty
    }
    "reject if empty string" in {
      requestChecker.isPaxPassportNumberValid(defaultPax.copy(passportNumber = Some("")))(requestContext) shouldBe Some(
        ErrorCode.InvalidFlightPaxPassportNumber
      )
    }
    "reject if length more than 9 when SKYC-10597|A" in {
      val number = Random.nextString(10)
      requestChecker.isPaxPassportNumberValid(defaultPax.copy(passportNumber = Some(number)))(
        requestContext
      ) shouldBe Some(
        ErrorCode.InvalidFlightPaxPassportNumber
      )
    }
    "pass if length is 10 when SKYC-10597|B" in {
      val featureAware = mock[FeatureAware]
      when(featureAware.isUpdatePassportNoLengthTo10).thenReturn(true)
      val requestContextWithFeature = requestContext.copy(featureAware = Some(featureAware))
      val number                    = "1234567890"
      requestChecker.isPaxPassportNumberValid(defaultPax.copy(passportNumber = Some(number)))(
        requestContextWithFeature
      ) shouldBe empty
    }
    "reject if length more than 10 when SKYC-10597|B" in {
      val featureAware = mock[FeatureAware]
      when(featureAware.isUpdatePassportNoLengthTo10).thenReturn(true)
      val requestContextWithFeature = requestContext.copy(featureAware = Some(featureAware))
      val number                    = Random.nextString(11)
      requestChecker.isPaxPassportNumberValid(defaultPax.copy(passportNumber = Some(number)))(
        requestContextWithFeature
      ) shouldBe Some(
        ErrorCode.InvalidFlightPaxPassportNumber
      )
    }
    "reject if length less than 6" in {
      val number = Random.nextString(5)
      requestChecker.isPaxPassportNumberValid(defaultPax.copy(passportNumber = Some(number)))(
        requestContext
      ) shouldBe Some(
        ErrorCode.InvalidFlightPaxPassportNumber
      )
    }
    "pass if input is number" in {
      requestChecker.isPaxPassportNumberValid(defaultPax.copy(passportNumber = Some("123456")))(
        requestContext
      ) shouldBe empty
    }
    "pass if input is alphanumeric" in {
      requestChecker.isPaxPassportNumberValid(defaultPax.copy(passportNumber = Some("aa12345ZZ")))(
        requestContext
      ) shouldBe empty
    }
    "pass if input is 10-digit alphanumeric when SKYC-10597|B" in {
      val featureAware = mock[FeatureAware]
      when(featureAware.isUpdatePassportNoLengthTo10).thenReturn(true)
      val requestContextWithFeature = requestContext.copy(featureAware = Some(featureAware))
      requestChecker.isPaxPassportNumberValid(defaultPax.copy(passportNumber = Some("aa123456ZZ")))(
        requestContextWithFeature
      ) shouldBe empty
    }
  }

  "isPaxPassportExpiresDate" should {
    "pass if empty" in {
      requestChecker.isPaxPassportExpiresDate(defaultPax.copy(passportExpires = None)) shouldBe empty
    }
    "reject if in past" in {
      val date = LocalDate.now().minusYears(1)
      requestChecker.isPaxPassportExpiresDate(defaultPax.copy(passportExpires = Some(date))) shouldBe Some(
        ErrorCode.InvalidFlightPaxPassportExpirationDate
      )
    }
    "pass otherwise" in {
      val date = LocalDate.now().plusYears(1)
      requestChecker.isPaxPassportExpiresDate(defaultPax.copy(passportExpires = Some(date))) shouldBe empty
    }
  }

  "isPaxKnownTravelerNumberValid" should {
    "pass if empty" in {
      requestChecker.isPaxKnownTravelerNumberValid(defaultPax.copy(knownTravelerNumber = None)) shouldBe empty
    }

    "pass for 9 letters alphanumeric characters" in {
      requestChecker.isPaxKnownTravelerNumberValid(
        defaultPax.copy(knownTravelerNumber = Some("987654a21"))
      ) shouldBe empty
    }

    "pass for 10 letters alphanumeric characters" in {
      requestChecker.isPaxKnownTravelerNumberValid(
        defaultPax.copy(knownTravelerNumber = Some("*********a"))
      ) shouldBe empty
    }

    "reject for 11 letters" in {
      requestChecker.isPaxKnownTravelerNumberValid(
        defaultPax.copy(knownTravelerNumber = Some("*********ab"))
      ) shouldBe Some(ErrorCode.InvalidFlightPaxKnownTravelerNumber)
    }

    "reject for 8 letters" in {
      requestChecker.isPaxKnownTravelerNumberValid(
        defaultPax.copy(knownTravelerNumber = Some("9876543a"))
      ) shouldBe Some(ErrorCode.InvalidFlightPaxKnownTravelerNumber)
    }

    "reject for non alpha-numeric characters" in {
      requestChecker.isPaxKnownTravelerNumberValid(
        defaultPax.copy(knownTravelerNumber = Some("987654@21"))
      ) shouldBe Some(ErrorCode.InvalidFlightPaxKnownTravelerNumber)
    }

    "pass for all 9 digits" in {
      requestChecker.isPaxKnownTravelerNumberValid(
        defaultPax.copy(knownTravelerNumber = Some("*********"))
      ) shouldBe empty
    }

    "pass for all 10 digits" in {
      requestChecker.isPaxKnownTravelerNumberValid(
        defaultPax.copy(knownTravelerNumber = Some("*********0"))
      ) shouldBe empty
    }

    "pass for all 9 lower case alphabets" in {
      requestChecker.isPaxKnownTravelerNumberValid(
        defaultPax.copy(knownTravelerNumber = Some("abcdefghi"))
      ) shouldBe empty
    }

    "pass for all 10 lower case alphabets" in {
      requestChecker.isPaxKnownTravelerNumberValid(
        defaultPax.copy(knownTravelerNumber = Some("abcdefghij"))
      ) shouldBe empty
    }

    "pass for all 9 upper case alphabets" in {
      requestChecker.isPaxKnownTravelerNumberValid(
        defaultPax.copy(knownTravelerNumber = Some("ABCDEFGHI"))
      ) shouldBe empty
    }

    "pass for all 10 upper case alphabets" in {
      requestChecker.isPaxKnownTravelerNumberValid(
        defaultPax.copy(knownTravelerNumber = Some("ABCDEFGHIJ"))
      ) shouldBe empty
    }
  }

  "isDriverValid" should {

    val mockValidCarDriver = CarDriver(
      "Mr",
      "John",
      "Doe",
      Some("<EMAIL>"),
      Some("*********"),
      None,
      Some("Male"),
      None,
      None,
      None
    )

    "pass if driver is valid for every check" in {
      requestChecker.isDriverValid(mockValidCarDriver) shouldBe empty
    }

    "failed if driver has some empty in name" in {
      val mockInvalidCarDriver = mockValidCarDriver.copy(firstName = "")
      requestChecker.isDriverValid(mockInvalidCarDriver) shouldBe List(ErrorCode.InvalidDriverFirstName)
    }
  }

  "validatePropertyRequestItem" should {
    val room                = baseRoomInfo.get("1").toSeq
    val baseSingleProperty  = baseSinglePropertyReq
    val propertyItemRequest = baseSinglePropertyReq.products.propertyItems
    val propertyRequest = RequestWithProducts(
      baseSingleProperty,
      requestContext,
      Map.empty,
      Nil,
      None,
      Nil,
      BookingFlow.MultiHotel,
      MultiProduct(properties = room),
      requestV2 = None
    )

    "succeeds if property items in Token and request match" in {
      val propertyItem1  = propertyItemRequest.modify(_.each.each.productTokenKey).setTo(Some("1")).toSeq
      val propertyToken1 = room.modify(_.each.bapiBooking.productTokenKey).setTo(Some("1"))
      val propertyItem2  = propertyItemRequest.modify(_.each.each.productTokenKey).setTo(Some("2")).toSeq
      val propertyToken2 = room.modify(_.each.bapiBooking.productTokenKey).setTo(Some("2"))
      val propertyProductRequest = propertyRequest
        .modify(_.request.products.propertyItems)
        .setTo(Some((propertyItem1 ++ propertyItem2).flatten))
        .modify(_.products.properties)
        .setTo(propertyToken1 ++ propertyToken2)
      requestChecker.validatePropertyRequest(propertyProductRequest) shouldBe None
    }
    "fails if property items in Token and request are different case: item different " in {
      val propertyItem1  = propertyItemRequest.modify(_.each.each.productTokenKey).setTo(Some("121")).toSeq
      val propertyToken1 = room.modify(_.each.bapiBooking.productTokenKey).setTo(Some("1"))
      val propertyItem2  = propertyItemRequest.modify(_.each.each.productTokenKey).setTo(Some("2")).toSeq
      val propertyToken2 = room.modify(_.each.bapiBooking.productTokenKey).setTo(Some("2"))
      val propertyProductRequest = propertyRequest
        .modify(_.request.products.propertyItems)
        .setTo(Some((propertyItem1 ++ propertyItem2).flatten))
        .modify(_.products.properties)
        .setTo(propertyToken1 ++ propertyToken2)
      requestChecker.validatePropertyRequest(propertyProductRequest) shouldBe Some(ErrorCode.MissingPropertyItems)
    }

    "fails if property items in Token and request are different case: token key different " in {
      val propertyItem1  = propertyItemRequest.modify(_.each.each.productTokenKey).setTo(Some("1")).toSeq
      val propertyToken1 = room.modify(_.each.bapiBooking.productTokenKey).setTo(Some("121"))
      val propertyItem2  = propertyItemRequest.modify(_.each.each.productTokenKey).setTo(Some("2")).toSeq
      val propertyToken2 = room.modify(_.each.bapiBooking.productTokenKey).setTo(Some("2"))
      val propertyProductRequest = propertyRequest
        .modify(_.request.products.propertyItems)
        .setTo(Some((propertyItem1 ++ propertyItem2).flatten))
        .modify(_.products.properties)
        .setTo(propertyToken1 ++ propertyToken2)
      requestChecker.validatePropertyRequest(propertyProductRequest) shouldBe Some(ErrorCode.MissingPropertyItems)
    }

    "passes if property items in request is a Some of empty Seq and no tokens" in {
      val propertyProductRequest = propertyRequest
        .modify(_.request.products.propertyItems)
        .setTo(Some(Nil))
        .modify(_.products.properties)
        .setTo(Nil)
      requestChecker.validatePropertyRequest(propertyProductRequest) shouldBe None
    }

    "fails if property items in request is a Some of empty Seq but there are tokens" in {
      val propertyToken1 = room.modify(_.each.bapiBooking.productTokenKey).setTo(Some("121"))
      val propertyToken2 = room.modify(_.each.bapiBooking.productTokenKey).setTo(Some("2"))
      val propertyProductRequest = propertyRequest
        .modify(_.request.products.propertyItems)
        .setTo(Some(Nil))
        .modify(_.products.properties)
        .setTo(propertyToken1 ++ propertyToken2)
      requestChecker.validatePropertyRequest(propertyProductRequest) shouldBe Some(ErrorCode.MissingPropertyItems)
    }
  }

  "validateFlightRequestItem" should {
    val flightRequest = RequestWithProducts(
      baseFlightRequest,
      requestContext,
      Map.empty,
      Nil,
      None,
      Nil,
      BookingFlow.SingleFlight,
      MultiProduct(flights = baseFlightBookingToken),
      requestV2 = None
    )

    "succeeds if flight items in Token and request match" in {
      val flightItem1  = flightItem.copy(productTokenKey = Some("1"))
      val flightToken1 = baseFlightBookingToken.modify(_.each.productTokenKey).setTo(Some("1"))
      val flightItem2  = flightItem.copy(productTokenKey = Some("2"))
      val flightToken2 = baseFlightBookingToken.modify(_.each.productTokenKey).setTo(Some("2"))
      val flightProducts = flightRequest
        .modify(_.request.products.flightItems)
        .setTo(Some(Seq(flightItem1, flightItem2)))
        .modify(_.products.flights)
        .setTo(flightToken1 ++ flightToken2)
      requestChecker.validateFlightRequestItem(flightProducts) shouldBe None
    }
    "fails if flight items in Token and request are different case: item different " in {
      val flightItem1  = flightItem.copy(productTokenKey = Some("121"))
      val flightToken1 = baseFlightBookingToken.modify(_.each.productTokenKey).setTo(Some("1"))
      val flightItem2  = flightItem.copy(productTokenKey = Some("2"))
      val flightToken2 = baseFlightBookingToken.modify(_.each.productTokenKey).setTo(Some("2"))
      val flightProducts = flightRequest
        .modify(_.request.products.flightItems)
        .setTo(Some(Seq(flightItem1, flightItem2)))
        .modify(_.products.flights)
        .setTo(flightToken1 ++ flightToken2)
      requestChecker.validateFlightRequestItem(flightProducts) shouldBe Some(ErrorCode.MissingFlightItems)
    }

    "fails if flight items in Token and request are different case: token key different " in {
      val flightItem1  = flightItem.copy(productTokenKey = Some("1"))
      val flightToken1 = baseFlightBookingToken.modify(_.each.productTokenKey).setTo(Some("121"))
      val flightItem2  = flightItem.copy(productTokenKey = Some("2"))
      val flightToken2 = baseFlightBookingToken.modify(_.each.productTokenKey).setTo(Some("2"))
      val flightProducts = flightRequest
        .modify(_.request.products.flightItems)
        .setTo(Some(Seq(flightItem1, flightItem2)))
        .modify(_.products.flights)
        .setTo(flightToken1 ++ flightToken2)
      requestChecker.validateFlightRequestItem(flightProducts) shouldBe Some(ErrorCode.MissingFlightItems)
    }

    "passes if flight items is a Some of empty Seq and no Tokens " in {
      val flightProducts = flightRequest
        .modify(_.request.products.flightItems)
        .setTo(Some(Nil))
        .modify(_.products.flights)
        .setTo(Nil)
      requestChecker.validateFlightRequestItem(flightProducts) shouldBe None
    }

    "fails if flight items is a Some of empty Seq but there are Tokens" in {
      val flightToken1 = baseFlightBookingToken.modify(_.each.productTokenKey).setTo(Some("121"))
      val flightToken2 = baseFlightBookingToken.modify(_.each.productTokenKey).setTo(Some("2"))
      val flightProducts = flightRequest
        .modify(_.request.products.flightItems)
        .setTo(Some(Nil))
        .modify(_.products.flights)
        .setTo(flightToken1 ++ flightToken2)
      requestChecker.validateFlightRequestItem(flightProducts) shouldBe Some(ErrorCode.MissingFlightItems)
    }
  }

  "validateCarRequestItem" should {
    val carRequest = RequestWithProducts(
      baseFlightRequest,
      requestContext,
      Map.empty,
      Nil,
      None,
      Nil,
      BookingFlow.SingleVehicle,
      MultiProduct(vehicles = Seq(baseCarToken)),
      requestV2 = None
    )

    "succeeds if car items in Token and request match" in {
      val carItem1  = baseCarItem.copy(productTokenKey = Some("1"))
      val carToken1 = baseCarToken.modify(_.productTokenKey).setTo(Some("1"))
      val carItem2  = baseCarItem.copy(productTokenKey = Some("2"))
      val carToken2 = baseCarToken.modify(_.productTokenKey).setTo(Some("2"))
      val carProducts = carRequest
        .modify(_.request.products.carItems)
        .setTo(Some(Seq(carItem1, carItem2)))
        .modify(_.products.vehicles)
        .setTo(Seq(carToken1, carToken2))
      requestChecker.validateVehicleRequestItem(carProducts) shouldBe None
    }
    "fails if car items in Token and request are different case: item different " in {
      val carItem1  = baseCarItem.copy(productTokenKey = Some("121"))
      val carToken1 = baseCarToken.modify(_.productTokenKey).setTo(Some("1"))
      val carItem2  = baseCarItem.copy(productTokenKey = Some("2"))
      val carToken2 = baseCarToken.modify(_.productTokenKey).setTo(Some("2"))
      val carProducts = carRequest
        .modify(_.request.products.carItems)
        .setTo(Some(Seq(carItem1, carItem2)))
        .modify(_.products.vehicles)
        .setTo(Seq(carToken1, carToken2))
      requestChecker.validateVehicleRequestItem(carProducts) shouldBe Some(ErrorCode.MissingVehicleItems)
    }

    "fails if car items in Token and request are different case: token key different " in {
      val carItem1  = baseCarItem.copy(productTokenKey = Some("1"))
      val carToken1 = baseCarToken.modify(_.productTokenKey).setTo(Some("121"))
      val carItem2  = baseCarItem.copy(productTokenKey = Some("2"))
      val carToken2 = baseCarToken.modify(_.productTokenKey).setTo(Some("2"))
      val carProducts = carRequest
        .modify(_.request.products.carItems)
        .setTo(Some(Seq(carItem1, carItem2)))
        .modify(_.products.vehicles)
        .setTo(Seq(carToken1, carToken2))
      requestChecker.validateVehicleRequestItem(carProducts) shouldBe Some(ErrorCode.MissingVehicleItems)
    }

    "passes if car items is a Some of empty Seq and no Tokens " in {
      val carProducts = carRequest
        .modify(_.request.products.carItems)
        .setTo(Some(Nil))
        .modify(_.products.vehicles)
        .setTo(Nil)
      requestChecker.validateVehicleRequestItem(carProducts) shouldBe None
    }

    "fails if car items is a Some of empty Seq but there are Tokens" in {
      val carToken1 = baseCarToken.modify(_.productTokenKey).setTo(Some("121"))
      val carToken2 = baseCarToken.modify(_.productTokenKey).setTo(Some("2"))
      val flightProducts = carRequest
        .modify(_.request.products.carItems)
        .setTo(Some(Nil))
        .modify(_.products.vehicles)
        .setTo(Seq(carToken1, carToken2))
      requestChecker.validateVehicleRequestItem(flightProducts) shouldBe Some(ErrorCode.MissingVehicleItems)
    }
  }

  "validateProductKeys" should {
    val multiProductRequest = RequestWithProducts(
      baseMultiProductReq,
      requestContext,
      Map.empty,
      Nil,
      None,
      Nil,
      BookingFlow.Unknown,
      MultiProduct(
        vehicles = Seq(baseCarToken),
        properties = baseRoomInfo.get("1").toSeq,
        flights = Seq(baseFlightToken),
        activities = Seq(baseActivityToken)
      ),
      requestV2 = None
    )
    "be Nil if tokens match with Products" in {
      val requestWithFeature = multiProductRequest
      requestChecker.validateProductKeys(requestWithFeature) shouldBe Nil
    }

    "Error if tokens dont match with Products " in {
      val requestWithFeatureNoVehicleItems = multiProductRequest
        .modify(_.request.products.carItems)
        .setTo(None)
      requestChecker.validateProductKeys(requestWithFeatureNoVehicleItems) shouldBe List(
        ErrorCode.MissingVehicleItems
      )

    }

    "get flight and car failure if tokens dont match with Products" in {
      val requestWithFeatureNoVehicleFlights = multiProductRequest
        .modify(_.request.products.carItems)
        .setTo(None)
        .modify(_.request.products.flightItems)
        .setTo(None)
      requestChecker.validateProductKeys(requestWithFeatureNoVehicleFlights).toSet shouldBe Set(
        ErrorCode.MissingVehicleItems,
        ErrorCode.MissingFlightItems
      )
    }

    "get activity failure if tokens dont match with Products" in {
      val requestWithFeatureNoVehicleFlights = multiProductRequest
        .modify(_.request.products.activitiesItems)
        .setTo(None)
      requestChecker.validateProductKeys(requestWithFeatureNoVehicleFlights) shouldBe Seq(
        ErrorCode.MissingActivityItems
      )
    }
  }

  "validateRequiredFields" should {
    val postalCodeKey             = RequiredField.PostalCode
    val unitedStatesPostCodeRegex = "^\\d{5}(?:-\\d{4})?$"

    def encodeBase64(input: String): String = {
      Base64.getEncoder.encodeToString(input.getBytes(StandardCharsets.UTF_8))
    }

    "dont return InvalidRequiredField when validator is v2 the required field and required field metadata size aren't matched" in {
      val mockRequiredFieldMetadata = Map.empty[String, RequiredFieldMetadata]
      val mockRequiredField         = Map("field1" -> "value1")
      val mockFeatureAware          = mock[FeatureAware]
      val mockRequest               = mock[CreateBookingRequest]
      val mockPayment               = mock[BookingPayment]
      when(mockPayment.requiredFields).thenReturn(Some(mockRequiredField))
      when(mockPayment.requiredFieldMetadata).thenReturn(Some(mockRequiredFieldMetadata))
      when(mockRequest.payment).thenReturn(mockPayment)

      requestChecker.validateRequiredFields(mockRequest, Some(mockFeatureAware)) shouldNot be(
        Some(ErrorCode.InvalidRequiredFieldError)
      )
    }

    "return InvalidRequiredField when the required field doesn't have metadata" in {
      val mockRequiredFieldMetadata = Map("field0" -> mock[RequiredFieldMetadata])
      val mockRequiredField         = Map("field1" -> "value1")
      val mockFeatureAware          = mock[FeatureAware]

      val mockRequest = mock[CreateBookingRequest]
      val mockPayment = mock[BookingPayment]
      when(mockPayment.requiredFields).thenReturn(Some(mockRequiredField))
      when(mockPayment.requiredFieldMetadata).thenReturn(Some(mockRequiredFieldMetadata))
      when(mockRequest.payment).thenReturn(mockPayment)

      requestChecker.validateRequiredFields(mockRequest) shouldBe Some(ErrorCode.InvalidRequiredFieldError)
      requestChecker.validateRequiredFields(mockRequest, Some(mockFeatureAware)) shouldBe Some(
        ErrorCode.InvalidRequiredFieldError
      )
    }

    "return InvalidPostalCode when the postalCode value doesn't match regex pattern" in {
      val requiredFieldMetadata = mock[RequiredFieldMetadata]
      when(requiredFieldMetadata.regex).thenReturn(encodeBase64(unitedStatesPostCodeRegex))
      val mockFeatureAware = mock[FeatureAware]

      val mockRequiredFieldMetadata = Map("field1" -> requiredFieldMetadata, postalCodeKey -> requiredFieldMetadata)
      val mockRequiredField         = Map("field1" -> "12345", postalCodeKey -> "12345-xxxx")

      val mockRequest = mock[CreateBookingRequest]
      val mockPayment = mock[BookingPayment]
      when(mockPayment.requiredFields).thenReturn(Some(mockRequiredField))
      when(mockPayment.requiredFieldMetadata).thenReturn(Some(mockRequiredFieldMetadata))
      when(mockRequest.payment).thenReturn(mockPayment)

      requestChecker.validateRequiredFields(mockRequest) shouldBe Some(ErrorCode.InvalidPostalCode)
      requestChecker.validateRequiredFields(mockRequest, Some(mockFeatureAware)) shouldBe Some(
        ErrorCode.InvalidPostalCode
      )
    }
    "return None when the required field value matches regex pattern" in {
      val requiredFieldMetadata = mock[RequiredFieldMetadata]
      when(requiredFieldMetadata.regex).thenReturn(encodeBase64(unitedStatesPostCodeRegex))
      val mockFeatureAware = mock[FeatureAware]

      val mockRequiredFieldMetadata = Map("field1" -> requiredFieldMetadata, postalCodeKey -> requiredFieldMetadata)
      val mockRequiredField         = Map("field1" -> "12345", postalCodeKey -> "12345-6789")

      val mockRequest = mock[CreateBookingRequest]
      val mockPayment = mock[BookingPayment]
      when(mockPayment.requiredFields).thenReturn(Some(mockRequiredField))
      when(mockPayment.requiredFieldMetadata).thenReturn(Some(mockRequiredFieldMetadata))
      when(mockRequest.payment).thenReturn(mockPayment)

      requestChecker.validateRequiredFields(mockRequest) shouldBe None
      requestChecker.validateRequiredFields(mockRequest, Some(mockFeatureAware)) shouldBe None
    }
    "return Error when the metadata does not contains selected payment option" in {
      val requiredFieldMetadata = mock[RequiredFieldMetadata]
      when(requiredFieldMetadata.regex).thenReturn(encodeBase64(encodeBase64(".*")))

      val mockRequiredFieldMetadata = Map(postalCodeKey -> requiredFieldMetadata)
      val mockRequiredField         = Map(RequiredField.AppOption -> "app.phonepe")

      val mockRequest = mock[CreateBookingRequest]
      val mockPayment = mock[BookingPayment]
      when(mockPayment.requiredFields).thenReturn(Some(mockRequiredField))
      when(mockPayment.requiredFieldMetadata).thenReturn(Some(mockRequiredFieldMetadata))
      when(mockRequest.payment).thenReturn(mockPayment)

      requestChecker.validateRequiredFields(mockRequest) shouldBe Some(
        ErrorCode.InvalidRequiredFieldError
      )
    }
    "return None when the metadata contains selected payment option" in {
      val requiredFieldMetadata = mock[RequiredFieldMetadata]
      when(requiredFieldMetadata.regex).thenReturn(encodeBase64(".*"))

      val mockRequiredFieldMetadata = Map(RequiredField.AppOption -> requiredFieldMetadata)
      val mockRequiredField         = Map(RequiredField.AppOption -> "app.phonepe")

      val mockRequest = mock[CreateBookingRequest]
      val mockPayment = mock[BookingPayment]
      when(mockPayment.requiredFields).thenReturn(Some(mockRequiredField))
      when(mockPayment.requiredFieldMetadata).thenReturn(Some(mockRequiredFieldMetadata))
      when(mockRequest.payment).thenReturn(mockPayment)

      requestChecker.validateRequiredFields(mockRequest) shouldBe None
    }
  }

  "validateActivityRequestItem" should {
    val activityRequest = RequestWithProducts(
      request = baseFlightRequest,
      requestContext = requestContext,
      rooms = Map.empty,
      flights = Nil,
      car = None,
      protections = Nil,
      bookingFlow = BookingFlow.SingleActivity,
      products = MultiProduct(activities = Seq(baseActivityToken)),
      requestV2 = None
    )

    "succeeds if activity items in Token and request match" in {
      val activityItem1  = baseActivityItem.copy(productTokenKey = Some("1"))
      val activityToken1 = baseActivityToken.modify(_.productTokenKey).setTo(Some("1"))
      val activityProducts = activityRequest
        .modify(_.request.products.activitiesItems)
        .setTo(Some(Seq(activityItem1)))
        .modify(_.products.activities)
        .setTo(Seq(activityToken1))
      requestChecker.validateActivityRequestItem(activityProducts) shouldBe None
    }

    "fail if activity items in Token and request doesn't match" in {
      val activityItem1  = baseActivityItem.copy(productTokenKey = Some("0"))
      val activityToken1 = baseActivityToken.modify(_.productTokenKey).setTo(Some("1"))
      val activityProducts = activityRequest
        .modify(_.request.products.activitiesItems)
        .setTo(Some(Seq(activityItem1)))
        .modify(_.products.activities)
        .setTo(Seq(activityToken1))

      val validationResult = requestChecker.validateActivityRequestItem(activityProducts)
      validationResult.value shouldBe ErrorCode.MissingActivityItems
    }
  }

  "isNameValid" when {
    "WhiteLabel is Rurubu" should {
      "return true when name is valid with Japanese name pattern" in {
        val name = "保全"

        requestChecker.isNameValid(
          name,
          WhiteLabelInfo(WhiteLabel.Rurubu, mockFeaturesConfigurationForRurubu),
          roomDmcId = DmcId.JtbWl.id
        ) shouldBe true
      }

      "return true when name contains some unicode character specific to Japanese name pattern" in {
        val name =
          "\u3000\u3004\u3012\u3013\u3020\u302a\u302b\u302c\u302d\u302e\u302f\u3036\u3037\u303e\u303f\u3097\u3098\u3099\u309a\u309b\u309c"

        requestChecker.isNameValid(
          name,
          WhiteLabelInfo(WhiteLabel.Rurubu, mockFeaturesConfigurationForRurubu),
          roomDmcId = DmcId.JtbWl.id
        ) shouldBe true
      }

      "return false when name is invalid with Japanese name pattern" in {
        val name = "John"

        requestChecker.isNameValid(
          name,
          WhiteLabelInfo(WhiteLabel.Rurubu, mockFeaturesConfigurationForRurubu),
          roomDmcId = DmcId.JtbWl.id
        ) shouldBe false
      }

      "return true when Japanese name and room dmc id is YCS" in {
        val name = "保全"

        requestChecker.isNameValid(
          name,
          WhiteLabelInfo(WhiteLabel.Rurubu, mockFeaturesConfigurationForRurubu),
          roomDmcId = DmcId.YCS.id
        ) shouldBe true
      }
    }
    "WhiteLabel is Agoda" should {
      "return true when name is valid with Regular name pattern" in {
        val name = "保全０１２３４５６７８９Ｊａｍｅｓ*********9James"

        requestChecker.isNameValid(
          name,
          WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id
        ) shouldBe true
      }

      "return false when name is invalid with Regular name pattern" in {
        val name = "<a href='https://www.scorpiones.io'>Click here!</a>"

        requestChecker.isNameValid(
          name,
          WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration()),
          roomDmcId = DmcId.YCS.id
        ) shouldBe false
      }
    }
    "WhiteLabel is JTB" should {
      "return true when name is valid with JTB name pattern" in {
        val name = "保全０１２３４５６７８９Ｊａｍｅｓ*********9James（）ー＆／・．＿’　＊＊＊"

        requestChecker.isNameValid(
          name,
          WhiteLabelInfo(WhiteLabel.Jtb, mockFeaturesConfigurationForJTB),
          roomDmcId = DmcId.JtbWl.id
        ) shouldBe true
      }

      "return false when name is invalid with Regular name pattern" in {
        val name = "<a href='https://www.scorpiones.io'>Click here!</a>"

        requestChecker.isNameValid(
          name,
          WhiteLabelInfo(WhiteLabel.Jtb, mockFeaturesConfigurationForJTB),
          roomDmcId = DmcId.JtbWl.id
        ) shouldBe false
      }
    }
    "WhiteLabel is ClubTravel" should {
      "return true when name is valid with Regular name pattern" in {
        val name = "保全０１２３４５６７８９Ｊａｍｅｓ*********9James"

        requestChecker.isNameValid(
          name,
          WhiteLabelInfo(WhiteLabel.ClubTravel, mockFeaturesConfigurationForClubTravel),
          roomDmcId = DmcId.YCS.id
        ) shouldBe true
      }

      "return true when name is valid with Regular name pattern and dmc room id is JTB" in {
        val name = "保全０１２３４５６７８９Ｊａｍｅｓ*********9James"

        requestChecker.isNameValid(
          name,
          WhiteLabelInfo(WhiteLabel.ClubTravel, mockFeaturesConfigurationForClubTravel),
          roomDmcId = DmcId.JtbWl.id
        ) shouldBe true
      }

      "return false when name is invalid with Regular name pattern" in {
        val name = "<a href='https://www.scorpiones.io'>Click here!</a>"

        requestChecker.isNameValid(
          name,
          WhiteLabelInfo(WhiteLabel.ClubTravel, mockFeaturesConfigurationForClubTravel),
          roomDmcId = DmcId.YCS.id
        ) shouldBe false
      }

      "return false when name is invalid with Regular name pattern and dmc room id is JTB" in {
        val name = "<a href='https://www.scorpiones.io'>Click here!</a>"

        requestChecker.isNameValid(
          name,
          WhiteLabelInfo(WhiteLabel.ClubTravel, mockFeaturesConfigurationForClubTravel),
          roomDmcId = DmcId.JtbWl.id
        ) shouldBe false
      }
    }
  }
}
