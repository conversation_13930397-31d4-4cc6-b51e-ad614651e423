package com.agoda.bapi.creation.service.stage

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.CustomerRiskStatus
import com.agoda.bapi.common.message.creation.{CreateBookingResponse, Customer, DuplicateBooking}
import com.agoda.bapi.common.model.activity.ActivityBookingToken
import com.agoda.bapi.common.model.car.CarBookingToken
import com.agoda.bapi.common.model.flight.FlightBookingToken
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.product.BookingRequestTypeResolver.MultiProductFlowDefinition
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.model.{WhiteL<PERSON><PERSON>, WhiteLabelInfo}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.CreateMultiBookingHelper
import com.agoda.bapi.creation.model.{MultiProduct, RequestWithProductsNType}
import com.agoda.bapi.creation.model.multi.ValidateProductRequest
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.bapi.creation.service.flow.ProcessStage
import com.softwaremill.quicklens._
import com.agoda.bapi.creation.service.{HadoopMessagingService, TPRMService}
import com.agoda.mpb.common.BookingType
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpb.common.models.state.ProductType
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfterEach
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future

class MultiProductsValidateStageTest
    extends AsyncWordSpec
    with MockitoSugar
    with BeforeAndAfterEach
    with Matchers
    with CreateMultiBookingHelper {
  implicit val mpDefinition: MultiProductFlowDefinition = MultiProductFlowDefinition()
  val mockRequest                                       = baseRequestWithProductsNType
  val mockResponse                                      = baseRequestWithProductsNType.toMultiProductsRequest()

  val mockHotelResponse    = ValidateProductRequest(mockRequest.request, mockRequest.requestContext, baseHotelProduct)
  val mockFlightResponse   = ValidateProductRequest(mockRequest.request, mockRequest.requestContext, baseFlightProduct)
  val mockDuplicateBooking = mock[DuplicateBooking]
  val mockTPRMService      = mock[TPRMService]
  val mockHotelState =
    mock[ProcessStage[ValidateProductRequest[RoomInfo], ValidateProductRequest[RoomInfo], CreateBookingResponse]]
  val mockFlightState = mock[ProcessStage[ValidateProductRequest[FlightBookingToken], ValidateProductRequest[
    FlightBookingToken
  ], CreateBookingResponse]]
  val mockVehicleState = mock[ProcessStage[ValidateProductRequest[CarBookingToken], ValidateProductRequest[
    CarBookingToken
  ], CreateBookingResponse]]
  val mockActivityState = mock[ProcessStage[ValidateProductRequest[ActivityBookingToken], ValidateProductRequest[
    ActivityBookingToken
  ], CreateBookingResponse]]
  val hadoopMessaging = mock[HadoopMessagingService]
  when(hadoopMessaging.sendBapiCreateFactLogMessage(any, any, any)).thenReturn(Future.successful(()))
  val processor =
    new MultiProductsValidateStage(
      mockTPRMService,
      mockHotelState,
      mockFlightState,
      mockVehicleState,
      mockActivityState,
      hadoopMessaging
    )

  override def beforeEach(): Unit = {
    reset(mockTPRMService)
    reset(mockHotelState)
    reset(mockFlightState)
  }

  "MultiProductsDuplicateStage" should {
    "process success" in {
      when(mockTPRMService.findCustomerTerrorists(any[Customer])(any[RequestContext]))
        .thenReturn(Future.successful(CustomerRiskStatus.Safe))
      when(mockHotelState.process(any[ValidateProductRequest[RoomInfo]]))
        .thenReturn(Future.successful(Right(mockHotelResponse)))
      when(mockFlightState.process(any[ValidateProductRequest[FlightBookingToken]]))
        .thenReturn(Future.successful(Right(mockFlightResponse)))

      processor.process(mockRequest).map { response =>
        response shouldBe Right(mockResponse)
      }
    }

    "process success when risk status was unknown due to unexpected exception from client" in {
      when(mockTPRMService.findCustomerTerrorists(any[Customer])(any[RequestContext]))
        .thenReturn(Future.successful(CustomerRiskStatus.Unknown))
      when(mockHotelState.process(any[ValidateProductRequest[RoomInfo]]))
        .thenReturn(Future.successful(Right(mockHotelResponse)))
      when(mockFlightState.process(any[ValidateProductRequest[FlightBookingToken]]))
        .thenReturn(Future.successful(Right(mockFlightResponse)))

      processor.process(mockRequest).map { response =>
        response shouldBe Right(mockResponse)
      }
    }

    "process failed with some unknown exception" in {
      val exception      = new Exception("mockError")
      val failedResponse = CreateBookingResponse.technical(ErrorCode.UnexpectedInternalError, Some(exception))
      when(mockTPRMService.findCustomerTerrorists(any[Customer])(any[RequestContext]))
        .thenReturn(Future.failed(exception))
      val featureAware = mock[FeatureAware]
      val wlInfo       = mock[WhiteLabelInfo]
      when(wlInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
      when(wlInfo.feature).thenReturn(new FeaturesConfiguration)
      when(wlInfo.isFeatureEnabled(WhiteLabelFeatureName.TPRMCheck)).thenReturn(true)
      val requestContext = mockRequest.requestContext
        .copy(
          featureAware = Some(featureAware),
          whiteLabelInfo = wlInfo
        )
      val request = mockRequest.copy(requestContext = requestContext)

      processor.process(request).map { response =>
        verify(mockHotelState, never).process(any[ValidateProductRequest[RoomInfo]])
        verify(mockFlightState, never).process(any[ValidateProductRequest[FlightBookingToken]])
        response shouldBe Left(failedResponse)
      }
    }

    "process failed when customer terrorist check failed" in {
      val featureAware = mock[FeatureAware]
      val wlInfo       = mock[WhiteLabelInfo]
      when(wlInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
      when(wlInfo.feature).thenReturn(new FeaturesConfiguration)
      when(wlInfo.isFeatureEnabled(WhiteLabelFeatureName.TPRMCheck)).thenReturn(true)
      val requestContext = mockRequest.requestContext
        .copy(
          featureAware = Some(featureAware),
          whiteLabelInfo = wlInfo
        )
      val request        = mockRequest.copy(requestContext = requestContext)
      val failedResponse = CreateBookingResponse.error(ErrorCode.TerroristIdentifiedInBooking)
      when(mockTPRMService.findCustomerTerrorists(any[Customer])(any[RequestContext]))
        .thenReturn(Future.successful(CustomerRiskStatus.Risk))
      when(mockHotelState.process(any[ValidateProductRequest[RoomInfo]]))
        .thenReturn(Future.successful(Right(mockHotelResponse)))
      when(mockFlightState.process(any[ValidateProductRequest[FlightBookingToken]]))
        .thenReturn(Future.successful(Left(failedResponse)))

      processor.process(request).map { response =>
        verify(mockHotelState, never).process(any[ValidateProductRequest[RoomInfo]])
        verify(mockFlightState, never).process(any[ValidateProductRequest[FlightBookingToken]])
        response shouldBe Left(failedResponse)
      }
    }

    "process when customer terrorist check was suspicious (single property)" in {
      // val failedResponse = CreateBookingResponse.error(ErrorCode.TerroristIdentifiedInBooking)
      when(mockTPRMService.findCustomerTerrorists(any[Customer])(any[RequestContext]))
        .thenReturn(Future.successful(CustomerRiskStatus.AskMoreInfo))
      when(mockHotelState.process(any[ValidateProductRequest[RoomInfo]]))
        .thenReturn(Future.successful(Right(mockHotelResponse)))
      when(mockFlightState.process(any[ValidateProductRequest[FlightBookingToken]]))
        .thenReturn(Future.successful(Right(mockFlightResponse)))

      val singlePropertyReq = mockRequest.copy(
        products = mockRequest.products.copy(
          flights = Seq.empty,
          vehicles = Seq.empty,
          protections = Seq.empty
        ),
        request = mockRequest.request.copy(
          products = mockRequest.request.products.copy(
            carItems = Some(Seq.empty),
            flightItems = Some(Seq.empty),
            protectionItems = Some(Seq.empty)
          )
        ),
        bookingFlow = BookingFlow.SingleProperty
      )

      processor.process(singlePropertyReq).map { response =>
        verify(mockHotelState, atLeastOnce()).process(any[ValidateProductRequest[RoomInfo]])
        verify(mockFlightState, never).process(any[ValidateProductRequest[FlightBookingToken]])
        response.isRight shouldBe true
      }
    }

    "process failed if flight have duplicate booking" in {
      val failedResponse = CreateBookingResponse.duplicate(Seq(mockDuplicateBooking))
      when(mockTPRMService.findCustomerTerrorists(any[Customer])(any[RequestContext]))
        .thenReturn(Future.successful(CustomerRiskStatus.Safe))
      when(mockHotelState.process(any[ValidateProductRequest[RoomInfo]]))
        .thenReturn(Future.successful(Right(mockHotelResponse)))
      when(mockFlightState.process(any[ValidateProductRequest[FlightBookingToken]]))
        .thenReturn(Future.successful(Left(failedResponse)))

      processor.process(mockRequest).map { response =>
        response shouldBe Left(failedResponse)
      }
    }

    "process failed if hotel have duplicate booking" in {
      val failedResponse = CreateBookingResponse.duplicate(Seq(mockDuplicateBooking))
      when(mockTPRMService.findCustomerTerrorists(any[Customer])(any[RequestContext]))
        .thenReturn(Future.successful(CustomerRiskStatus.Safe))
      when(mockHotelState.process(any[ValidateProductRequest[RoomInfo]]))
        .thenReturn(Future.successful(Left(failedResponse)))
      when(mockFlightState.process(any[ValidateProductRequest[FlightBookingToken]]))
        .thenReturn(Future.successful(Right(mockFlightResponse)))

      processor.process(mockRequest).map { response =>
        response shouldBe Left(failedResponse)
      }
    }

    "process failed if both hotel and flight have duplicate booking" in {
      val failedResponse = CreateBookingResponse.duplicate(Seq(mockDuplicateBooking))
      when(mockTPRMService.findCustomerTerrorists(any[Customer])(any[RequestContext]))
        .thenReturn(Future.successful(CustomerRiskStatus.Safe))
      when(mockHotelState.process(any[ValidateProductRequest[RoomInfo]]))
        .thenReturn(Future.successful(Left(failedResponse)))
      when(mockFlightState.process(any[ValidateProductRequest[FlightBookingToken]]))
        .thenReturn(Future.successful(Left(failedResponse)))

      processor.process(mockRequest).map { response =>
        response shouldBe Left(CreateBookingResponse.duplicate(failedResponse.duplicateBookings))
      }
    }

    "not call TPRM Check and process success if TPRMCheck feature is off (Skip TPRM check)" in {
      val featureAware = mock[FeatureAware]
      val wlInfo       = mock[WhiteLabelInfo]
      when(wlInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
      when(wlInfo.feature).thenReturn(new FeaturesConfiguration)
      val requestContext = mockRequest.requestContext
        .copy(
          featureAware = Some(featureAware),
          whiteLabelInfo = wlInfo
        )
      val request = mockRequest.copy(requestContext = requestContext)
      when(mockHotelState.process(any[ValidateProductRequest[RoomInfo]]))
        .thenReturn(Future.successful(Right(mockHotelResponse)))
      when(mockFlightState.process(any[ValidateProductRequest[FlightBookingToken]]))
        .thenReturn(Future.successful(Right(mockFlightResponse)))

      verify(mockTPRMService, never()).findCustomerTerrorists(any())(any())
      processor.process(request).map { response =>
        response.isRight shouldBe true
      }
    }
    "call TPRM Check and process success if TPRMCheck feature is on (Skip TPRM check)" in {
      val featureAware = mock[FeatureAware]
      val wlInfo       = mock[WhiteLabelInfo]
      when(wlInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
      when(wlInfo.feature).thenReturn(new FeaturesConfiguration)
      when(wlInfo.isFeatureEnabled(WhiteLabelFeatureName.TPRMCheck)).thenReturn(true)
      val requestContext = mockRequest.requestContext
        .copy(
          featureAware = Some(featureAware),
          whiteLabelInfo = wlInfo
        )
      val request = mockRequest.copy(requestContext = requestContext)
      when(mockHotelState.process(any[ValidateProductRequest[RoomInfo]]))
        .thenReturn(Future.successful(Right(mockHotelResponse)))
      when(mockFlightState.process(any[ValidateProductRequest[FlightBookingToken]]))
        .thenReturn(Future.successful(Right(mockFlightResponse)))
      when(mockTPRMService.findCustomerTerrorists(any[Customer])(any[RequestContext]))
        .thenReturn(Future.successful(CustomerRiskStatus.Safe))

      processor.process(request).map { response =>
        verify(mockTPRMService, times(1)).findCustomerTerrorists(any())(any())
        response.isRight shouldBe true
      }
    }
    "build multi product request with addOnProtection " in {
      val flightWithProtectionAddOn = RequestWithProductsNType(
        baseMultiProductReq,
        baseRequestContext,
        Map.empty,
        Nil,
        Nil,
        BookingType.CreditCard,
        BookingFlow.Package,
        MultiProduct(flights = Seq(baseFlightToken), addOns = Seq(baseProtectionAddOnToken))
      )
      processor.buildMultiProductFlowDefinition(flightWithProtectionAddOn) shouldBe MultiProductFlowDefinition(
        numberOfProperty = 0,
        numberOfFlight = 1,
        numberOfCar = 0,
        numberOfActivity = 0,
        numberOfTripProtection = 1,
        numberOfCegFastTrack = 0,
        numberOfAddOns = 1
      )
    }
    "build multi product request with TP addons " in {
      val flightWithProtectionAddOn = RequestWithProductsNType(
        baseMultiProductReq,
        baseRequestContext,
        Map.empty,
        Nil,
        Nil,
        BookingType.CreditCard,
        BookingFlow.Package,
        MultiProduct(flights = Seq(baseFlightToken), addOns = Seq(baseProtectionAddOnToken))
      )
      processor.buildMultiProductFlowDefinition(flightWithProtectionAddOn) shouldBe MultiProductFlowDefinition(
        numberOfProperty = 0,
        numberOfFlight = 1,
        numberOfCar = 0,
        numberOfActivity = 0,
        numberOfTripProtection = 1,
        numberOfCegFastTrack = 0,
        numberOfAddOns = 1
      )
    }
    "build multi product request with addon which is not TP " in {
      val flightWithProtectionAddOn = RequestWithProductsNType(
        baseMultiProductReq,
        baseRequestContext,
        Map.empty,
        Nil,
        Nil,
        BookingType.CreditCard,
        BookingFlow.Package,
        MultiProduct(
          flights = Seq(baseFlightToken),
          addOns = Seq(
            baseProtectionAddOnToken.modify(_.characteristics.productTypeId).setTo(ProductType.CancelForAnyReason.id)
          )
        )
      )
      processor.buildMultiProductFlowDefinition(flightWithProtectionAddOn) shouldBe MultiProductFlowDefinition(
        numberOfProperty = 0,
        numberOfFlight = 1,
        numberOfCar = 0,
        numberOfActivity = 0,
        numberOfTripProtection = 0,
        numberOfCegFastTrack = 0,
        numberOfAddOns = 1
      )
    }
  }
}
