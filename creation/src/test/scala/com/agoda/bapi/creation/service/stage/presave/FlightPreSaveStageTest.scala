package com.agoda.bapi.creation.service.stage.presave

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.{Customer, FlightPax}
import com.agoda.bapi.common.model.flight.flightModel.Scope.Scope
import com.agoda.bapi.common.model.flight.{BaggageAllowance, BaggageInfo, BaggageSelection, BrandAttribute, BrandAttributeDetail, BrandParam, BrandSelection, FareRulePoliciesInToken, FareRulePolicyInToken, FlightItemBreakdown, FlightItemBreakdownPerPax, SeatSelection, Segment, SegmentBookingInfoByPaxType, Slice}
import com.agoda.bapi.common.model.flight.flightModel.Scope
import com.agoda.bapi.common.model.multiproduct.MultiProductInfoDBModel
import com.agoda.bapi.common.model.payment.CreditCardBillingInfo
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.proxy.EnigmaApiProxy
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.util.JodaDateTimeUtils
import com.agoda.bapi.creation.mapper.ebe.{FlightMapperImpl, FlightSeatSelectionDetailMapperImpl}
import com.agoda.bapi.creation.model.CreateRequest
import com.agoda.bapi.creation.model.multi.{FlightReservedIds, MultiProductsRequest, PreSaveProductStageRequest, Product, ReservedIds}
import com.agoda.bapi.creation.repository.{BaseBookingRepository, FlightBookingRepository, PaymentMethodRepository}
import com.agoda.bapi.creation.{CreateMultiBookingHelper, ExternalDIHelper}
import com.agoda.capi.enigma.shared_model.booking.flight.{FlightCustomer, FlightPassengers}
import com.agoda.capi.enigma.shared_model.itinerary.billinginfo.ItineraryBillingInfo
import com.agoda.mpb.common.MultiProductType
import mocks.DBBookingModelHelper
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers.{eq => eqTo, _}
import org.mockito.Mockito._
import org.mockito.invocation.InvocationOnMock
import org.scalatest
import org.scalatest.BeforeAndAfterEach
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future

class FlightPreSaveStageTest
    extends AsyncWordSpec
    with MockitoSugar
    with Matchers
    with BeforeAndAfterEach
    with CreateMultiBookingHelper
    with DBBookingModelHelper
    with ExternalDIHelper {

  val flightBookingRepository = mock[FlightBookingRepository]
  val baseBookingRepository   = mock[BaseBookingRepository]
  val enigmaApiProxy          = mock[EnigmaApiProxy]
  val dateTimeUtils           = mock[JodaDateTimeUtils]
  val currentDate             = DateTime.now
  when(dateTimeUtils.getCurrentDateTime).thenReturn(currentDate)
  val flightMapper            = spy(new FlightMapperImpl(dateTimeUtils, new FlightSeatSelectionDetailMapperImpl))
  val paymentMethodRepository = mock[PaymentMethodRepository]
  val featureAware            = mock[FeatureAware]

  val processor = spy(
    new FlightPreSaveStage(flightBookingRepository, baseBookingRepository, enigmaApiProxy, flightMapper)
  )

  val baseRequest = PreSaveProductStageRequest(
    request = baseMultiProductReq,
    requestContext = baseRequestContext,
    itineraryInfo = baseItineraryPreSaveInfo,
    multiProductId = Some(baseMultiProductInfo),
    product = baseFlightProduct,
    bookingFlow = BookingFlow.Package
  )

  val bookingId            = 100L
  val actionId             = 10L
  val enigmaFlightCustomer = mock[FlightCustomer]
  val enigmaFlightPax      = mock[FlightPassengers]
  val capiFlightPax        = mock[FlightPax]
  val essInfoId            = 123L

  val multiProductsRequest = MultiProductsRequest(
    request = baseMultiProductReq,
    requestContext = baseRequestContext,
    properties = Seq.empty,
    flights = Seq(baseFlightProduct),
    vehicles = Seq.empty,
    protections = Seq.empty,
    activities = Seq.empty,
    cegFastTracks = Seq.empty,
    addOns = Seq.empty,
    bookingFlow = BookingFlow.Package,
    commonPayment = None,
    isBookingFromCart = None
  )

  override def beforeEach: Unit = {
    reset(
      flightBookingRepository,
      baseBookingRepository,
      enigmaApiProxy,
      flightMapper,
      paymentMethodRepository,
      featureAware
    )
  }

  "process" should {
    "return correct result" in {
      val createRequest: CreateRequest = baseRequest
      val pax                          = baseRequest.request.getFlightPaxList(baseRequest.product.info.productTokenKey)
      val paxIds                       = pax.map(_.guestNo.toLong)
      val itineraryId                  = baseRequest.itineraryInfo.itineraryId

      when(flightBookingRepository.getNextFlightPaxIdSequenceNumbers(any())).thenReturn(Future.successful(Seq(1L, 2L)))

      when(
        flightBookingRepository.saveFavoritePassengersToCAPI(any[Int], any[List[FlightPax]], any[Int])(
          any[RequestContext]
        )
      )
        .thenReturn(Future.successful(()))

      when(flightBookingRepository.getNextActionIdSequenceNumber).thenReturn(Future.successful(actionId))
      when(flightBookingRepository.getNextBookingSequenceNumber)
        .thenReturn(Future.successful(bookingId))
      when(baseBookingRepository.getNextBaseBookingEssInfoSequenceNumber).thenReturn(Future.successful(essInfoId))

      when(enigmaApiProxy.saveFlightCustomer(any[Int], any[CreditCardBillingInfo], any[Customer])(any[RequestContext]))
        .thenReturn(Future.successful(enigmaFlightCustomer))
      when(enigmaApiProxy.saveFlightPassengers(any[Int], any[Seq[FlightPax]], any[Seq[Long]])(any[RequestContext]))
        .thenReturn(Future.successful(enigmaFlightPax))
      when(enigmaApiProxy.saveItineraryBillingInfo(any[Int], any[String])(any[RequestContext]))
        .thenReturn(Future.successful(mock[ItineraryBillingInfo]))

      def fakeGenerateSequenceForTests(invocationOnMock: InvocationOnMock): Future[Seq[Int]] = {
        val SequenceMultiplier = 10
        val count: Int         = invocationOnMock.getArgument(0)
        val seq                = (1 to count).map(_ * SequenceMultiplier)
        Future.successful(seq)
      }

      when(flightBookingRepository.getNextBreakdownIdSequenceNumbers(any())).thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightSliceIdSequenceNumbers(any())).thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightSegmentIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightSegmentInfoIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightBaggageIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightBaggageAllowanceIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightBrandSelectionIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightBrandAttributeIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightBrandAttributeParamIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightPolicyIdSequenceNumbers(any())).thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightSeatSelectionIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightPaxBreakdownIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)

      val multiProductPreSaveRequest = MultiProductPreSaveRequest(
        multiProductsRequest,
        baseItineraryPreSaveInfo,
        Map(
          Some(MultiProductType.Package) -> MultiProductInfoDBModel(10, MultiProductType.Package)
        )
      )

      processor.process(multiProductPreSaveRequest).map { result =>
        verify(enigmaApiProxy).saveFlightPassengers(eqTo(bookingId.toInt), eqTo(pax), eqTo(paxIds))(
          eqTo(createRequest.requestContext)
        )
        verify(processor).measuredReservedProductId(eqTo(baseRequest))(eqTo(baseRequest.measurementsContext))
        verify(processor).saveFavoritePassengers(eqTo(createRequest), eqTo(pax))
        verify(processor)
          .saveDataToEnigma(eqTo(createRequest), eqTo(bookingId), eqTo(pax), eqTo(paxIds), eqTo(itineraryId))
        succeed
      }
    }
  }

  "reservedProductId" should {
    val requestContextWithFeature = baseRequestContext.copy(featureAware = Some(featureAware))
    val requestWithFeature = baseRequest.copy(
      requestContext = requestContextWithFeature
    )

    val fareRulePolicies = mock[FareRulePoliciesInToken]
    when(fareRulePolicies.cancellationPolicies).thenReturn(
      Seq(mock[FareRulePolicyInToken], mock[FareRulePolicyInToken])
    )
    when(fareRulePolicies.exchangePolicies).thenReturn(Seq(mock[FareRulePolicyInToken]))

    val segment1 = mock[Segment]
    when(segment1.baggageAllowances).thenReturn(
      Seq(mock[BaggageAllowance], mock[BaggageAllowance], mock[BaggageAllowance])
    )
    when(segment1.segmentInfoByPaxType).thenReturn(
      Some(Seq(mock[SegmentBookingInfoByPaxType], mock[SegmentBookingInfoByPaxType]))
    )
    when(segment1.fareRulePolicies).thenReturn(Some(Seq(fareRulePolicies, fareRulePolicies)))

    val slice1 = mock[Slice]
    when(slice1.baggageAllowances).thenReturn(Seq(mock[BaggageAllowance], mock[BaggageAllowance]))

    when(slice1.fareRulePolicies).thenReturn(Some(Seq(fareRulePolicies, fareRulePolicies)))
    when(slice1.segments).thenReturn(Seq(segment1, segment1))

    val breakdown1    = mock[FlightItemBreakdown]
    val paxBreakdown1 = mock[FlightItemBreakdownPerPax]

    val flightInfo = baseFlightToken.info.get.copy(
      slices = Seq(slice1, slice1),
      priceBreakdowns = List(breakdown1, breakdown1, breakdown1, breakdown1),
      priceBreakdownsPerPax = Some(List(paxBreakdown1, paxBreakdown1, paxBreakdown1, paxBreakdown1, paxBreakdown1)),
      fareRulePolicies = Some(Seq(fareRulePolicies, fareRulePolicies))
    )

    val brandSelection           = mock[BrandSelection]
    val brandAttributeWithDetail = mock[BrandAttribute]
    val brandAttributeDetail     = mock[BrandAttributeDetail]
    when(brandAttributeDetail.parameters).thenReturn(Seq(mock[BrandParam], mock[BrandParam]))
    when(brandAttributeWithDetail.attributeDetails).thenReturn(
      Seq(brandAttributeDetail, brandAttributeDetail)
    )
    val brandAttributeWithoutDetail = mock[BrandAttribute]
    when(brandAttributeWithoutDetail.attributeDetails).thenReturn(Seq.empty)
    when(brandSelection.brandAttributes).thenReturn(Seq(brandAttributeWithDetail, brandAttributeWithoutDetail))

    val baggageSelection = mock[BaggageSelection]
    when(baggageSelection.baggage).thenReturn(Seq(mock[BaggageInfo], mock[BaggageInfo]))
    val flightToken = baseFlightToken.copy(
      seatSelection = Seq(mock[SeatSelection], mock[SeatSelection]),
      brandSelection = Some(Seq(brandSelection, brandSelection)),
      baggageSelection = Seq(baggageSelection, baggageSelection),
      fareRuleScope = Some(Scope.SLICE),
      info = Some(flightInfo)
    )

    def testReservedIdsFixture(fareRuleScope: Scope): Future[scalatest.Assertion] = {
      val flightTokenWithScope = flightToken.copy(fareRuleScope = Some(fareRuleScope))
      val request              = requestWithFeature.copy(product = baseFlightProduct.copy(info = flightTokenWithScope))
      val expectedPolicyIds = fareRuleScope match {
        case Scope.SLICE => Vector(10L, 20L, 30L, 40L, 50L, 60L, 70L, 80L, 90L, 100L, 110L, 120L)
        case Scope.SEGMENT =>
          Vector(10L, 20L, 30L, 40L, 50L, 60L, 70L, 80L, 90L, 100L, 110L, 120L, 130L, 140L, 150L, 160L, 170L, 180L,
            190L, 200L, 210L, 220L, 230L, 240L)
        case Scope.ITINERARY => Vector(10L, 20L, 30L, 40L, 50L, 60L)
      }
      val expected =
        ReservedIds(
          bookingId = bookingId,
          actionId = actionId,
          multiProductId = request.multiProductId.map(_.multiProductId),
          product = request.product,
          breakdownIds = Seq(10, 20, 30, 40),
          essInfoId = Some(essInfoId),
          productReservedIds = Some(
            FlightReservedIds(
              flightSliceIds = Vector(10, 20),
              flightSegmentIds = Vector(10, 20, 30, 40),                     // slice x 2, segment x 2
              flightSegmentInfoIds = Vector(10, 20, 30, 40, 50, 60, 70, 80), // slice x 2, segment x 2, infoByPax x 2
              flightBaggageIds = Vector(10, 20, 30, 40),                     // baggageSelection x 2, baggageInfo x 2
              // segment baggage allowance: slice x 2, segment x 2, segmentAllowance x 3 = 12
              // slice baggage allowance: slice x 2, sliceAllowance x 2 = 4
              flightBaggageAllowanceIds = Vector(10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130, 140, 150, 160),
              flightBrandSelectionIds = Vector(10, 20),
              // brandSelection x 2, (brandAttribute no detail x 1 + brandAttribute with detail x 1, details x 2)
              flightBrandAttributeIds = Vector(10, 20, 30, 40, 50, 60),
              // brandSelection x 2, brandAttribute with detail x 1, details x 2, parameters x 2
              flightBrandAttributeParamIds = Vector(10, 20, 30, 40, 50, 60, 70, 80),
              flightPolicyIds = expectedPolicyIds,
              flightSeatSelectionIds = Vector(10, 20),
              flightPaxBreakdownIds = Vector(10, 20, 30, 40, 50)
            )
          )
        )

      when(flightBookingRepository.getNextActionIdSequenceNumber).thenReturn(Future.successful(actionId))
      when(flightBookingRepository.getNextBookingSequenceNumber)
        .thenReturn(Future.successful(bookingId))
      when(baseBookingRepository.getNextBaseBookingEssInfoSequenceNumber).thenReturn(Future.successful(essInfoId))

      def fakeGenerateSequenceForTests(invocationOnMock: InvocationOnMock): Future[Seq[Int]] = {
        val SequenceMultiplier = 10
        val count: Int         = invocationOnMock.getArgument(0)
        val seq                = (1 to count).map(_ * SequenceMultiplier)
        Future.successful(seq)
      }

      when(flightBookingRepository.getNextBreakdownIdSequenceNumbers(any())).thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightSliceIdSequenceNumbers(any())).thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightSegmentIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightSegmentInfoIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightBaggageIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightBaggageAllowanceIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightBrandSelectionIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightBrandAttributeIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightBrandAttributeParamIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightPolicyIdSequenceNumbers(any())).thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightSeatSelectionIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)
      when(flightBookingRepository.getNextFlightPaxBreakdownIdSequenceNumbers(any()))
        .thenAnswer(fakeGenerateSequenceForTests)

      processor.measuredReservedProductId(request)(request.measurementsContext).map { actualIds =>
        actualIds.bookingId shouldBe expected.bookingId
        actualIds.actionId shouldBe expected.actionId
        actualIds.multiProductId shouldBe expected.multiProductId
        actualIds.breakdownIds shouldBe expected.breakdownIds
        actualIds.productReservedIds shouldBe expected.productReservedIds
        actualIds.essInfoId shouldBe expected.essInfoId
      }
    }

    "return correct result when fare rule scope is itinerary" in
      testReservedIdsFixture(Scope.ITINERARY)

    "return correct result when fare rule scope is slice" in
      testReservedIdsFixture(Scope.SLICE)

    "return correct result when fare rule scope is segment" in
      testReservedIdsFixture(Scope.SEGMENT)
  }

  "saveFavoritePassengers" should {
    val favPax       = genFlightPax(1, true, true)
    val pax          = Seq(favPax, genFlightPax(2))
    val capiResponse = List(capiFlightPax)

    "return correct result" in {
      when(
        flightBookingRepository.saveFavoritePassengersToCAPI(any[Int], any[List[FlightPax]], any[Int])(
          any[RequestContext]
        )
      )
        .thenReturn(Future.successful(()))
      processor.saveFavoritePassengers(baseRequest, pax).map { _ =>
        verify(flightBookingRepository).saveFavoritePassengersToCAPI(
          eqTo(baseRequest.request.customer.memberId),
          eqTo(List(favPax)),
          eqTo(baseRequest.requestContext.whiteLabelInfo.whiteLabelId.id)
        )(eqTo(baseRequest.requestContext))
        succeed
      }
    }

    "return success even though CAPI down" in {
      val capiResponse = new Exception("mockException")
      when(
        flightBookingRepository.saveFavoritePassengersToCAPI(any[Int], any[List[FlightPax]], any[Int])(
          any[RequestContext]
        )
      )
        .thenReturn(Future.failed(capiResponse))
      processor.saveFavoritePassengers(baseRequest, pax).map { _ =>
        verify(flightBookingRepository).saveFavoritePassengersToCAPI(
          eqTo(baseRequest.request.customer.memberId),
          eqTo(List(favPax)),
          eqTo(baseRequest.requestContext.whiteLabelInfo.whiteLabelId.id)
        )(eqTo(baseRequest.requestContext))
        verify(processor).logError(eqTo(capiResponse))(eqTo(baseRequest.measurementsContext))
        succeed
      }
    }

    "return Nil if no favorite" in {
      val pax = Seq(genFlightPax(1, true), genFlightPax(2))
      when(
        flightBookingRepository.saveFavoritePassengersToCAPI(any[Int], any[List[FlightPax]], any[Int])(
          any[RequestContext]
        )
      )
        .thenReturn(Future.successful(()))
      processor.saveFavoritePassengers(baseRequest, pax).map { _ =>
        verify(flightBookingRepository, never).saveFavoritePassengersToCAPI(any[Int], any[List[FlightPax]], any[Int])(
          any[RequestContext]
        )
        succeed
      }
    }
  }

  "saveDataToEnigma" should {
    val pax                          = Seq(genFlightPax(1, true), genFlightPax(2))
    val paxIds                       = pax.map(_.guestNo.toLong)
    val createRequest: CreateRequest = baseRequest
    val billInfo                     = CreditCardBillingInfo(createRequest.request.payment.creditCard)
    val itineraryId                  = baseRequest.itineraryInfo.itineraryId

    "return success result" in {
      when(enigmaApiProxy.saveFlightCustomer(any[Int], any[CreditCardBillingInfo], any[Customer])(any[RequestContext]))
        .thenReturn(Future.successful(enigmaFlightCustomer))
      when(enigmaApiProxy.saveFlightPassengers(any[Int], any[Seq[FlightPax]], any[Seq[Long]])(any[RequestContext]))
        .thenReturn(Future.successful(enigmaFlightPax))
      when(enigmaApiProxy.saveItineraryBillingInfo(any[Int], any[String])(any[RequestContext]))
        .thenReturn(Future.successful(mock[ItineraryBillingInfo]))

      processor.saveDataToEnigma(createRequest, bookingId, pax, paxIds, itineraryId).map { _ =>
        verify(enigmaApiProxy)
          .saveFlightCustomer(eqTo(bookingId.toInt), eqTo(billInfo), eqTo(createRequest.request.customer))(
            eqTo(createRequest.requestContext)
          )
        verify(enigmaApiProxy).saveFlightPassengers(eqTo(bookingId.toInt), eqTo(pax), eqTo(paxIds))(any[RequestContext])
        succeed
      }
    }

    "save BillingItineraryInfo" in {
      val createRequest = baseRequest.copy(
        request = baseRequest.request.copy(payment =
          defaultBookingPayment().copy(requiredFields = Some(Map("appUserId" -> "abc@paytm")))
        )
      )
      val itineraryId = baseRequest.itineraryInfo.itineraryId

      when(enigmaApiProxy.saveFlightCustomer(any[Int], any[CreditCardBillingInfo], any[Customer])(any[RequestContext]))
        .thenReturn(Future.successful(enigmaFlightCustomer))
      when(enigmaApiProxy.saveFlightPassengers(any[Int], any[Seq[FlightPax]], any[Seq[Long]])(any[RequestContext]))
        .thenReturn(Future.successful(enigmaFlightPax))
      when(enigmaApiProxy.saveItineraryBillingInfo(any[Int], any[String])(any[RequestContext]))
        .thenReturn(Future.successful(mock[ItineraryBillingInfo]))

      processor.saveDataToEnigma(createRequest, bookingId, pax, paxIds, itineraryId).map { _ =>
        verify(enigmaApiProxy)
          .saveFlightCustomer(eqTo(bookingId.toInt), eqTo(billInfo), eqTo(createRequest.request.customer))(
            eqTo(createRequest.requestContext)
          )
        verify(enigmaApiProxy).saveFlightPassengers(eqTo(bookingId.toInt), eqTo(pax), eqTo(paxIds))(any[RequestContext])
        verify(enigmaApiProxy)
          .saveItineraryBillingInfo(eqTo(itineraryId.toInt), eqTo("{\"appUserId\":\"abc@paytm\"}"))(
            eqTo(createRequest.requestContext)
          )
        succeed
      }
    }

    "return failed result" in {
      val enigmaResponse = new Exception("mockException")

      when(enigmaApiProxy.saveFlightCustomer(any[Int], any[CreditCardBillingInfo], any[Customer])(any[RequestContext]))
        .thenReturn(Future.failed(enigmaResponse))
      when(enigmaApiProxy.saveFlightPassengers(any[Int], any[Seq[FlightPax]], any[Seq[Long]])(any[RequestContext]))
        .thenReturn(Future.successful(enigmaFlightPax))

      processor.saveDataToEnigma(createRequest, bookingId, pax, paxIds, itineraryId).failed.map { _ =>
        verify(enigmaApiProxy)
          .saveFlightCustomer(eqTo(bookingId.toInt), eqTo(billInfo), eqTo(createRequest.request.customer))(
            eqTo(createRequest.requestContext)
          )
        verify(enigmaApiProxy, never).saveFlightPassengers(eqTo(bookingId.toInt), eqTo(pax), eqTo(paxIds))(
          any[RequestContext]
        )
        succeed
      }
    }
  }
}
