package com.agoda.bapi.creation.model

import com.agoda.bapi.common.exception.BookingTypeNotFoundException
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.{Products => CreationProduct, _}
import com.agoda.bapi.common.model.creation.BookingDcProcessType
import com.agoda.mpb.common.MultiProductType.MultiProductType
import com.agoda.bapi.common.model.payment.PaymentModel
import com.agoda.bapi.common.model.product.BookingRequestTypeResolver.MultiProductFlowDefinition
import com.agoda.bapi.common.model.product.{BookingFlow, BookingRequestTypeResolver, ProductTypeEnum}
import com.agoda.bapi.common.model.{WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.MockRequestContext
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.model.multi.{MultiProductsRequest, Product}
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.bapi.creation.{CreateBookingHelper, CreateFlightBookingHelper}
import com.agoda.mpb.common.{BookingType, MultiProductType}
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import generated.model.InstantBookingV4
import org.scalatestplus.mockito.MockitoSugar
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.mockito.Mockito.when

class ModelSpec
    extends AnyWordSpec
    with MockitoSugar
    with Matchers
    with CreateFlightBookingHelper
    with CreateBookingHelper {

  "CreateRequest" should {
    "getInstallmentPlanId" should {
      "return credit installmentPlanId from CreateBookingRequest if bookingType is CreditCard or BoRSupplier" in {
        val request: CreateRequest = reqWithProducts
        val expected               = request.request.payment.creditCard.map(_.installmentPlanId).getOrElse(Some(0))
        request.getInstallmentPlanId(BookingType.CreditCard) shouldBe expected
        request.getInstallmentPlanId(BookingType.BoRSupplier) shouldBe expected
      }
      "return credit installmentPlanId from CreateBookingRequest if bookingType is not CreditCard and BoRSupplier" in {
        val request: CreateRequest = reqWithProducts
        val expected               = Some(0)
        BookingType.values.filterNot(b => b == BookingType.CreditCard || b == BookingType.BoRSupplier).map { b =>
          request.getInstallmentPlanId(b) shouldBe expected
        }
      }
    }

    "measurementsContext setup" should {
      "label whitelabel instant booking correctly" in {
        val expectedWhiteLabelPartnerInfo = WhiteLabelInfo(
          WhiteLabel.Rurubu,
          FeaturesConfiguration(instantBooking =
            InstantBookingV4(checkTimeoutFromBAPIinSeconds = None, recheckAllotmentOnCentralDC = Some(false))
          )
        )
        val request: CreateRequest =
          reqWithProducts.copy(requestContext =
            defaultRequestContext.copy(whiteLabelInfo = expectedWhiteLabelPartnerInfo)
          )
        request.measurementsContext.whiteLabel shouldEqual expectedWhiteLabelPartnerInfo.whiteLabelId
        request.measurementsContext.bookingDcProcessType shouldEqual BookingDcProcessType.Local
      }

      "label agoda normal booking correctly" in {
        val expectedWhiteLabelPartnerInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration())
        val request: CreateRequest = reqWithProducts.copy(
          request = baseReq,
          requestContext = defaultRequestContext.copy(whiteLabelInfo = expectedWhiteLabelPartnerInfo)
        )
        request.measurementsContext.whiteLabel shouldEqual expectedWhiteLabelPartnerInfo.whiteLabelId
        request.measurementsContext.bookingDcProcessType shouldEqual BookingDcProcessType.Central
      }

      "label agoda instant booking correctly" in {
        val expectedWhiteLabelPartnerInfo = WhiteLabelInfo(WhiteLabel.Agoda, FeaturesConfiguration())
        val request: CreateRequest =
          reqWithProducts.copy(
            requestContext = defaultRequestContext.copy(whiteLabelInfo = expectedWhiteLabelPartnerInfo),
            request = baseInstantBookReq
          )
        request.measurementsContext.whiteLabel shouldEqual expectedWhiteLabelPartnerInfo.whiteLabelId
        request.measurementsContext.bookingDcProcessType shouldEqual BookingDcProcessType.Local
      }
    }
  }

  "MultiProductsRequest" should {
    val request        = mock[CreateBookingRequest]
    val requestContext = mock[RequestContext]
    val room1          = mock[RoomInfo]
    val room2          = mock[RoomInfo]
    val bookingType    = mock[BookingType.BookingType]

    implicit val mpDefinition: MultiProductFlowDefinition = MultiProductFlowDefinition()
    "return correct multiproduct request if productType is MixAndSave" in {
      val bookingFlow = BookingFlow.MixAndSave
      val products = MultiProduct(
        properties = Seq(room1, room2),
        flights = Seq.empty
      )
      val roomMapping = Map("K1" -> room1, "K2" -> room2)
      val input = RequestWithProductsNType(
        request,
        requestContext,
        roomMapping,
        Seq.empty,
        Seq.empty,
        bookingType,
        bookingFlow,
        products
      )
      val result = input.toMultiProductsRequest()

      val expectedRoom1 = Product(bookingType, room1, Some(MultiProductType.MixAndSave))
      val expectedRoom2 = Product(bookingType, room2, Some(MultiProductType.MixAndSave))

      result shouldBe MultiProductsRequest(
        request = request,
        requestContext = requestContext,
        properties = Seq(expectedRoom1, expectedRoom2),
        flights = Seq.empty,
        vehicles = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        cegFastTracks = Seq.empty,
        addOns = Seq.empty,
        bookingFlow = bookingFlow,
        commonPayment = None,
        isBookingFromCart = None
      )
    }
    "return correct multiproduct request if productType is SingleFlight but has 2 flights" in {
      val bookingFlow       = BookingFlow.SingleFlight
      val multiFlightTokens = baseFlightBookingToken.flatMap(f => List.fill(2)(f))
      val products = MultiProduct(
        properties = Seq.empty,
        flights = multiFlightTokens
      )
      val input = RequestWithProductsNType(
        request,
        requestContext,
        Map.empty,
        multiFlightTokens,
        Seq.empty,
        bookingType,
        bookingFlow,
        products
      )
      val result = input.toMultiProductsRequest()

      val expectedFlight1 = Product(BookingType.Flight, multiFlightTokens.head, Some(MultiProductType.HackerFare))
      val expectedFlight2 = Product(BookingType.Flight, multiFlightTokens.last, Some(MultiProductType.HackerFare))
      result shouldBe MultiProductsRequest(
        request = request,
        requestContext = requestContext,
        properties = Seq.empty,
        flights = Seq(expectedFlight1, expectedFlight2),
        vehicles = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        cegFastTracks = Seq.empty,
        addOns = Seq.empty,
        bookingFlow = bookingFlow,
        commonPayment = None,
        isBookingFromCart = None
      )
    }
    "return correct multiproduct request if productType is HackerFare" in {
      val bookingFlow       = BookingFlow.Hackerfare
      val multiFlightTokens = baseFlightBookingToken.flatMap(f => List.fill(2)(f))
      val products = MultiProduct(
        properties = Seq.empty,
        flights = multiFlightTokens
      )
      val input = RequestWithProductsNType(
        request,
        requestContext,
        Map.empty,
        Seq.empty,
        Seq.empty,
        bookingType,
        bookingFlow,
        products
      )
      val result = input.toMultiProductsRequest()

      val expectedFlight1 = Product(BookingType.Flight, multiFlightTokens.head, Some(MultiProductType.HackerFare))
      val expectedFlight2 = Product(BookingType.Flight, multiFlightTokens.last, Some(MultiProductType.HackerFare))
      result shouldBe MultiProductsRequest(
        request = request,
        requestContext = requestContext,
        properties = Seq.empty,
        flights = Seq(expectedFlight1, expectedFlight2),
        vehicles = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        cegFastTracks = Seq.empty,
        addOns = Seq.empty,
        bookingFlow = bookingFlow,
        commonPayment = None,
        isBookingFromCart = None
      )
    }
    "return correct multiproduct request and multi product type = SingleProperty if booking flow is Cart and have only 1 property in cart" in {
      val bookingFlow = BookingFlow.Cart
      val products = MultiProduct(
        properties = Seq(room1),
        flights = Seq.empty
      )
      implicit val mpDefinition: MultiProductFlowDefinition = MultiProductFlowDefinition(numberOfProperty = 1)
      val roomMapping                                       = Map("K1" -> room1)
      val featureAware                                      = mock[FeatureAware]
      val whiteLabelInfo                                    = mock[WhiteLabelInfo]
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.FlightToCartMigration)).thenReturn(true)
      when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(true)
      when(requestContext.featureAware).thenReturn(Some(featureAware))
      when(requestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
      val input = RequestWithProductsNType(
        request = request,
        requestContext = requestContext,
        rooms = roomMapping,
        flights = Seq.empty,
        protections = Seq.empty,
        bookingType = bookingType,
        bookingFlow = bookingFlow,
        products = products
      )
      val result        = input.toMultiProductsRequest()
      val expectedRoom1 = Product(bookingType, room1, Some(MultiProductType.SingleProperty))

      result shouldBe MultiProductsRequest(
        request = request,
        requestContext = requestContext,
        properties = Seq(expectedRoom1),
        flights = Seq.empty,
        vehicles = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        cegFastTracks = Seq.empty,
        addOns = Seq.empty,
        bookingFlow = bookingFlow,
        commonPayment = None,
        isBookingFromCart = None
      )
    }
    "return correct multiproduct request and multi product type = Cart if booking flow is Cart and have more thab 1 properties in cart" in {
      implicit val mpDefinition: MultiProductFlowDefinition = MultiProductFlowDefinition(numberOfProperty = 2)
      val bookingFlow                                       = BookingFlow.Cart
      val products = MultiProduct(
        properties = Seq(room1, room2),
        flights = Seq.empty
      )
      val roomMapping    = Map("K1" -> room1, "K2" -> room2)
      val featureAware   = mock[FeatureAware]
      val whiteLabelInfo = mock[WhiteLabelInfo]
      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.FlightToCartMigration)).thenReturn(true)
      when(featureAware.migrateFlightToCartFlow(whiteLabelInfo)).thenReturn(true)
      when(requestContext.featureAware).thenReturn(Some(featureAware))
      when(requestContext.whiteLabelInfo).thenReturn(whiteLabelInfo)
      val input = RequestWithProductsNType(
        request = request,
        requestContext = requestContext,
        rooms = roomMapping,
        flights = Seq.empty,
        protections = Seq.empty,
        bookingType = bookingType,
        bookingFlow = bookingFlow,
        products = products
      )
      val result = input.toMultiProductsRequest()

      val expectedRoom1 = Product(bookingType, room1, Some(MultiProductType.Cart))
      val expectedRoom2 = Product(bookingType, room2, Some(MultiProductType.Cart))

      result shouldBe MultiProductsRequest(
        request = request,
        requestContext = requestContext,
        properties = Seq(expectedRoom1, expectedRoom2),
        flights = Seq.empty,
        vehicles = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        cegFastTracks = Seq.empty,
        addOns = Seq.empty,
        bookingFlow = bookingFlow,
        commonPayment = None,
        isBookingFromCart = None
      )
    }
  }

  "RequestWithProducts" should {
    val requestContext = MockRequestContext.create()
    "return ProductTypeEnum.Flight if flight is not empty" in {
      val room = Map[RoomUid, RoomInfo]()
      val request = RequestWithProducts(
        baseReq,
        requestContext,
        room,
        baseFlightBookingToken,
        None,
        Seq.empty,
        BookingFlow.SingleFlight,
        requestV2 = None
      )
      val result = BookingRequestTypeResolver.getProductType(
        request.request.copy(products =
          CreationProduct(
            propertyItems = None,
            flightItems = Some(Seq(FlightItem("", None)))
          )
        )
      )
      result shouldBe ProductTypeEnum.Flight
    }

    "return ProductTypeEnum.Property if room is not empty" in {
      val room   = defaultRoomWithDmc(Some(29014))
      val flight = Seq.empty
      val request =
        RequestWithProducts(
          baseReq,
          requestContext,
          room,
          flight,
          None,
          Seq.empty,
          BookingFlow.SingleProperty,
          requestV2 = None
        )
      val result = BookingRequestTypeResolver.getProductType(request.request)
      result shouldBe ProductTypeEnum.Property
    }

    "return ProductTypeEnum.Multi if room is not empty and flight is not empty" in {
      val room = defaultRoomWithDmc(Some(29014))
      val request =
        RequestWithProducts(
          baseReq,
          requestContext,
          room,
          baseFlightBookingToken,
          None,
          Seq.empty,
          BookingFlow.Package,
          requestV2 = None
        )
      val result = BookingRequestTypeResolver.getProductType(
        request.request.copy(products =
          CreationProduct(
            propertyItems = Some(Seq(PropertyItem())),
            flightItems = Some(Seq(FlightItem("", None)))
          )
        )
      )
      result shouldBe ProductTypeEnum.Multi
    }

    "return BookingTypeNotFoundException if the request does not contain room and flight" in {
      val room   = Map.empty[RoomUid, RoomInfo]
      val flight = Seq.empty
      val request =
        RequestWithProducts(
          baseReq,
          requestContext,
          room,
          flight,
          None,
          Seq.empty,
          BookingFlow.SingleFlight,
          requestV2 = None
        )
      an[BookingTypeNotFoundException] should be thrownBy BookingRequestTypeResolver.getProductType(
        request.request.copy(products = CreationProduct())
      )
    }

    "return correct bookingType" in {
      val room   = defaultRoomWithDmc(Some(29014))
      val flight = Seq.empty
      val request =
        RequestWithProducts(
          baseReq,
          requestContext,
          room,
          flight,
          None,
          Seq.empty,
          BookingFlow.SingleProperty,
          requestV2 = None
        )
      val result = request.withType(BookingType.AgencyBooking)
      result.bookingType shouldBe BookingType.AgencyBooking
    }

    "return measurementContext with paymentModel when bookingFlow is SingleFlight" in {
      val inputFlightToken = baseFlightBookingToken.map(token =>
        token.copy(
          info = token.info.map(info => info.copy(paymentModel = PaymentModel.Agency))
        )
      )
      val request = RequestWithProducts(
        baseReq,
        requestContext = requestContext,
        rooms = Map.empty,
        flights = inputFlightToken,
        car = None,
        protections = Seq.empty,
        bookingFlow = BookingFlow.SingleFlight,
        requestV2 = None
      )

      request.measurementsContext.paymentModel shouldBe inputFlightToken.headOption.flatMap(
        _.info.map(_.paymentModel.id)
      )
    }
  }
}
