package com.agoda.bapi.creation.model

import com.agoda.bapi.common.exception.{StatusTokenDeserializationException, UnSupportedStatusTokenVersionException}
import com.agoda.bapi.common.model.StatusToken
import com.agoda.bapi.common.model.StatusToken.{Version4, Version6}
import com.agoda.bapi.common.exception.BookingCreationLogMessageBase
import org.json4s.{DefaultFormats, Formats}
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.must.Matchers
import mocks.MeasurementStubs.{logBookingCreationLogMessageBaseStub, measureStub}

import scala.collection.mutable

class StatusTokenSpec extends AnyFunSpec with Matchers {
  implicit private val formats: Formats = DefaultFormats

  trait Fixture {
    val sentMetrics = mutable.Set[(String, Long)]()
    def measure(metricName: String, value: Long, tags: Map[String, String]): Unit = {
      sentMetrics += (metricName -> value)
    }
  }

  private val defaultStatusToken = StatusToken(
    itineraryId = 647534604,
    actionId = 380061537,
    productType = Set("Property"),
    dc = "hk",
    whitelabelId = Some(1),
    actionIds = None,
    topic = Some(1),
    operationType = None,
    bookingId = None,
    version = None
  )

  describe("StatusToken") {
    val stringStatusTokenV4 =
      "CAQSQDdYMmlVOC9sQ3ZvQTVIWTJRY2k0ekRjVHp4YmdjckRKbFl1UFAxeTdLSTRvMW4rTjRIT2hmYTBmdnB3MlcwcjA="

    val stringStatusTokenV6 =
      "CAYSQDNyOEdVTWFpdG9OMkE2Z01wTlR6am9rRklxRVFXZWZEZWpyL1BoaHFlbEdvaXprRHo2N0NDTU9VL1JoSzJXZGg="

    describe("deserialize - exception scenarios") {
      it("deserialize v1 is not supported anymore") {
        val serializedStatusTokenV1 =
          "{\"itineraryId\":647534604,\"actionId\":380061537,\"productType\":[\"Property\"],\"dc\":\"hk\",\"whitelabelId\":1,\"topic\":1}"

        val fixture = new Fixture {}
        import fixture._

        intercept[StatusTokenDeserializationException](
          StatusToken.deserialize(serializedStatusTokenV1, logBookingCreationLogMessageBaseStub, measure).get
        )
        assert(sentMetrics.head == "bcre.statustoken.deserialize" -> 1)
      }

      it("deserialize v2 is not supported anymore") {
        val serializedStatusTokenV2 =
          "eyJpdGluZXJhcnlJZCI6NjQ3NTM0NjA0LCJhY3Rpb25JZCI6MzgwMDYxNTM3LCJwcm9kdWN0VHlwZSI6WyJQcm9wZXJ0eSJdLCJkYyI6ImhrIiwid2hpdGVsYWJlbElkIjoxLCJ0b3BpYyI6MX0="
        val fixture = new Fixture {}
        import fixture._

        intercept[StatusTokenDeserializationException](
          StatusToken.deserialize(serializedStatusTokenV2, logBookingCreationLogMessageBaseStub, measure).get
        )
        assert(sentMetrics.head == "bcre.statustoken.deserialize" -> 1)
      }

      it("deserialize v3 is not supported anymore") {
        val serializedStatusTokenV3 = "CAMSJENJeXc0clFDRU9HT25iVUJHZ0VCSWdKb2F5b0NDQUU2QWdnQg=="
        val fixture                 = new Fixture {}
        import fixture._

        val failure = intercept[StatusTokenDeserializationException](
          StatusToken.deserialize(serializedStatusTokenV3, logBookingCreationLogMessageBaseStub, measure).get
        )
        assert(failure.getCause.isInstanceOf[UnSupportedStatusTokenVersionException])
        assert(sentMetrics.head == "bcre.statustoken.deserialize" -> 1)
      }

      it("should not break if token is empty string") {
        val logFunction = { message: BookingCreationLogMessageBase =>
          assert(message.getMessage == "[StatusToken] deserialize")
          ()
        }
        intercept[StatusTokenDeserializationException](
          StatusToken.deserialize("", logFunction, measureStub).get
        )
      }
      it("should not break if token is null") {
        intercept[StatusTokenDeserializationException](
          StatusToken.deserialize(null, logBookingCreationLogMessageBaseStub, measureStub).get
        )
      }
      it("should not break if token is foo") {
        intercept[StatusTokenDeserializationException](
          StatusToken.deserialize("foo", logBookingCreationLogMessageBaseStub, measureStub).get
        )
      }
    }

    describe("v4") {
      val statusTokenV4 = defaultStatusToken.copy(version = Some(Version4))

      it("deserialize correctly") {
        val fixture = new Fixture {}
        import fixture._
        val actualStatusToken =
          StatusToken.deserialize(stringStatusTokenV4, logBookingCreationLogMessageBaseStub, measure)
        assert(actualStatusToken.get == statusTokenV4)
        assert(sentMetrics.head == "bcre.statustoken.deserialize" -> 1)
      }

      it("serialize - should convert to tokenString correctly") {
        val fixture = new Fixture {}
        import fixture._
        val actualStringStatusToken = statusTokenV4.serialize(logBookingCreationLogMessageBaseStub, measure)
        assert(actualStringStatusToken == stringStatusTokenV4)
        assert(sentMetrics.head == "bcre.statustoken.serialize" -> 1)
      }
    }

    describe("v6") {
      val statusTokenV6 = defaultStatusToken.copy(version = Some(Version6), operationId = Some(29384729387L))

      it("deserialize correctly") {
        val fixture = new Fixture {}
        import fixture._
        val actualStatusToken =
          StatusToken.deserialize(stringStatusTokenV6, logBookingCreationLogMessageBaseStub, measure)
        assert(actualStatusToken.get == statusTokenV6)
        assert(sentMetrics.head == "bcre.statustoken.deserialize" -> 1)
      }

      it("serialize - should convert to tokenString correctly") {
        val fixture = new Fixture {}
        import fixture._
        val actualStringStatusToken = statusTokenV6.serialize(logBookingCreationLogMessageBaseStub, measure)
        assert(actualStringStatusToken == stringStatusTokenV6)
        assert(sentMetrics.head == "bcre.statustoken.serialize" -> 1)
      }
    }
  }
}
