package com.agoda.bapi.creation.mapper.ebe

import com.agoda.bapi.common.message.creation.PaymentAmount
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.common.model.car.VehicleBookingStateModel.{VehicleInfo, _}
import com.agoda.bapi.common.model.car._
import com.agoda.bapi.common.model.cart.{CartItemContext, CartProductInfo}
import com.agoda.bapi.common.model.flight.{FlightPax => _}
import com.agoda.bapi.common.model.multiproduct.AccountingEntities
import com.agoda.bapi.common.model.payment.PaymentModel
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{CancellationClass, CommonBookingInfo, VehicleEnigmaDriverId}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.util.JodaDateTimeUtils
import com.agoda.bapi.creation.model.PointsType
import com.agoda.bapi.creation.model.db.{BookingActionState, Points, RiskInfo}
import com.agoda.bapi.creation.model.flights.RequestWithFlightsNEnigmaInfo
import com.agoda.bapi.creation.model.multi.{MultiProductsRequest, Product, ReservedIds, VehicleReservedIds}
import com.agoda.bapi.creation.{CreateBookingHelper, CreateFlightBookingHelper, CreateMultiBookingHelper}
import com.agoda.mpb.common.models.state._
import com.agoda.mpbe.state.common.enums.Gateway.Gateway
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.agoda.mpb.common.{BookingType, WorkflowId}
import org.joda.time.DateTime
import org.mockito.Mockito
import org.mockito.Mockito.when
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.{AppendedClues, BeforeAndAfter}
import org.scalatestplus.mockito.MockitoSugar

class VehicleMapperTest
    extends AnyWordSpec
    with Matchers
    with MockitoSugar
    with AppendedClues
    with BeforeAndAfter
    with CreateFlightBookingHelper
    with CreateBookingHelper
    with CreateMultiBookingHelper {

  val expectedCurrentDateTime: DateTime = DateTime.now()
  val timeUtils: JodaDateTimeUtils = new JodaDateTimeUtils {
    override def getCurrentDateTime: DateTime = expectedCurrentDateTime

    override def toDateTimeWithoutMillis(input: DateTime): DateTime = input.withMillisOfSecond(0)
  }

  val vehicleMapper = new VehicleMapperImpl(dateTimeUtils = timeUtils)
  val baseInput = MultiProductsRequest(
    request = baseVehicleReqWithLoyalty,
    requestContext = baseRequestContext,
    properties = Seq(baseHotelProduct),
    flights = Seq(baseFlightProduct),
    vehicles = Seq(baseVehicleProduct),
    protections = Seq.empty,
    activities = Seq.empty,
    cegFastTracks = Seq.empty,
    addOns = Seq.empty,
    bookingFlow = BookingFlow.SingleVehicle,
    commonPayment = None,
    isBookingFromCart = None
  )

  "ToInternalModel" should {

    "return internalModel properly" in {
      // Arrange
      val expectedItineraryId       = 12345
      val expectedBookingId         = 54321L
      val actionId                  = 0
      val masterActionId            = 1
      val expectedDisplayCurrency   = "THB"
      val expectedCountryId         = 4321
      val expectedCommonBookingInfo = Some(CommonBookingInfo(Some("TH"), false))
      val breakdownSeqIds           = Seq(1111L, 1112L)
      val vehicleLocationId1        = 50001L
      val vehicleLocationId2        = 50002L
      val vehicleInfoId             = 60001L
      val reservationIds = ReservedIds(
        expectedBookingId,
        actionId,
        None,
        Product(
          bookingType = BookingType.Vehicle,
          baseCarToken.copy(
            info = CarBookingInfo(
              paymentModel = PaymentModel.MerchantCommission,
              carItemPriceSummary = CarPricingSummary(
                CarChargeDisplay(
                  currency = expectedDisplayCurrency,
                  baseFare = 3000,
                  taxAndFee = 600,
                  baseDiscount = 0,
                  campaignDiscount = 0,
                  totalFare = 6000,
                  totalSurcharge = 1000,
                  surchargeDetails = "THIS IS JSON OF SURCHARGE DETAILS",
                  agodaFee = 0,
                  policyChargesDetails = Some("THIS IS JSON OF POLICY CHARGE DETAILS"),
                  paymentModel = 2,
                  extraChargesDetails = Some("THIS IS JSON OF extraChargeDetails")
                )
              ),
              priceBreakdowns = Seq(
                CarItemBreakdown(
                  expectedCurrentDateTime,
                  itemId = 1,
                  typeId = 2,
                  taxFeeId = Some(3),
                  quantity = 1,
                  localCurrency = expectedDisplayCurrency,
                  localAmount = 20.0,
                  exchangeRate = 30.0,
                  usdAmount = 30.0,
                  reqAmount = 900.0,
                  vendorExchangeRate = 1,
                  requestedCurrency = "INR"
                ),
                CarItemBreakdown(
                  expectedCurrentDateTime,
                  itemId = 12,
                  typeId = 1,
                  taxFeeId = Some(3),
                  quantity = 1,
                  localCurrency = "THB",
                  localAmount = 30.0,
                  exchangeRate = 40.0,
                  usdAmount = 50.0,
                  reqAmount = 60.0,
                  vendorExchangeRate = 1,
                  requestedCurrency = "INR"
                )
              ),
              pickUp = LocationInfo(
                airportInfo = None,
                cityId = 1234,
                countryId = expectedCountryId,
                dateTime = expectedCurrentDateTime,
                addressLine = "THIS IS ADDRESS LINE",
                postalCode = "THIS IS POSTAL CODE",
                extraLocationInfo = "THIS IS EXTRA LOCATION INFO",
                locationName = "THIS IS LOCATION NAME",
                supplierLocationCode = "THIS IS SUPPLIER LOCATION CODE",
                locationType = Some("THIS IS LOCATION TYPE"),
                phoneNumbers = Seq("1234")
              ),
              dropOff = LocationInfo(
                airportInfo = None,
                cityId = 8888,
                countryId = expectedCountryId,
                dateTime = expectedCurrentDateTime,
                addressLine = "THIS IS ADDRESS LINE",
                postalCode = "THIS IS POSTAL CODE",
                extraLocationInfo = "THIS IS EXTRA LOCATION INFO",
                locationName = "THIS IS LOCATION NAME",
                supplierLocationCode = "THIS IS SUPPLIER LOCATION CODE",
                locationType = Some("THIS IS LOCATION TYPE"),
                phoneNumbers = Seq("1234")
              ),
              vehicleCode = "CAR-001",
              vehicleName = "Honda",
              classification = "Sedan",
              cancellationPolicy = "3D50P_100P",
              vehicleExtraInfo = VehicleExtraInfo(
                vehicleDoors = Some(4),
                vehicleSeats = Some(1),
                vehicleSuitcases = Some(1),
                vehicleTransmission = Some("transmission"),
                vehicleIsAircon = Some(true),
                vehicleIsAirbag = Some(true),
                vehicleFuelType = Some("fuelType"),
                imageUrl = Some("imageUrl"),
                pickUpSupplierOperationOfHours =
                  Some(Seq(OperationSchedule("Monday", Seq(SupplierOperationHour("06:00", "00:00"))))),
                dropOffSupplierOperationOfHours =
                  Some(Seq(OperationSchedule("Monday", Seq(SupplierOperationHour("06:00", "00:00"))))),
                providerIconUrl = Some("iconUrl"),
                acrissCode = Some("acrissCode")
              ),
              vehicleMileagePolicy = Some(
                MileagePolicy(
                  freeDistance = 0,
                  code = "",
                  description = "Mock Car Fuel Info",
                  charge = None,
                  isFreeCoverage = true
                )
              ),
              vehicleFuelPolicy = Some(
                FuelPolicy(
                  coverageType = "Full_To_Full",
                  code = "",
                  description = "fuel policy",
                  charge = None,
                  isFreeCoverage = true
                )
              ),
              vehicleCustomerPolicyInfo = Some(
                VehicleTokenCustomerPolicyInfo(
                  driverAgeGroup = Some("age-group"),
                  securityDepositOption = Some("security-deposit"),
                  localRenter = Some("local-renter")
                )
              )
            ),
            driverInfo = Some(DriverInfo(driverAge = 35))
          )
        ),
        breakdownIds = breakdownSeqIds,
        productReservedIds = Some(
          VehicleReservedIds(
            vehicleBookingLocationPickUpId = Some(vehicleLocationId1),
            vehicleBookingLocationDropOffId = Some(vehicleLocationId2),
            vehicleInfoId = Some(vehicleInfoId)
          )
        )
      )
      val enigmaPassengerId = Seq[VehicleEnigmaDriverId](9L)

      val baseInputWithFinanceFeature = mock[RequestWithFlightsNEnigmaInfo](Mockito.RETURNS_DEEP_STUBS)
      val mockFeatureAware            = mock[FeatureAware]

      when(baseInputWithFinanceFeature.request).thenReturn(baseInput.request)
      when(baseInputWithFinanceFeature.requestContext.featureAware).thenReturn(Some(mockFeatureAware))

      // Act
      val actualInternalModel = vehicleMapper.toInternalModel(
        baseInputWithFinanceFeature,
        expectedItineraryId,
        masterActionId,
        reservationIds,
        enigmaPassengerId
      )

      // Assert
      actualInternalModel.vehicleBooking.vehicleBookingId shouldEqual expectedBookingId
      actualInternalModel.vehicleBooking.itineraryId shouldEqual expectedItineraryId
      actualInternalModel.vehicleBooking.displayCurrency shouldEqual expectedDisplayCurrency
      actualInternalModel.vehicleBooking.recStatus shouldEqual RecStatus.Active
      actualInternalModel.vehicleBooking.paymentModel shouldEqual PaymentModel.MerchantCommission.id
      actualInternalModel.vehicleBooking.commonBookingInfo shouldEqual expectedCommonBookingInfo
      actualInternalModel.vehicleBookingTrip.driverAge shouldEqual 35 // Expected 35 from CarToken not 30 from CarItem Req

      val pickUpLocation  = actualInternalModel.vehicleBookingLocation.pickUp
      val dropOffLocation = actualInternalModel.vehicleBookingLocation.dropOff

      pickUpLocation.vehicleBookingId shouldEqual expectedBookingId
      dropOffLocation.vehicleBookingId shouldEqual expectedBookingId
      pickUpLocation.countryId shouldEqual expectedCountryId
      dropOffLocation.countryId shouldEqual expectedCountryId

      // Verify generated IDs
      actualInternalModel.vehicleFinancialBreakdowns.size shouldBe 2
      actualInternalModel.vehicleFinancialBreakdowns.map(_.breakdownId) shouldBe breakdownSeqIds
      pickUpLocation.vehicleBookingLocationId shouldBe vehicleLocationId1
      dropOffLocation.vehicleBookingLocationId shouldBe vehicleLocationId2
      actualInternalModel.vehicleInfo.map(_.vehicleInfoId) shouldBe Some(vehicleInfoId)
      val trip = actualInternalModel.vehicleBookingTrip
      trip.pickupLocationId shouldBe vehicleLocationId1
      trip.dropOffLocationId shouldBe vehicleLocationId2
    }
  }

  "ToBookingActionState" should {
    val location = VehicleModelBookingLocation(
      vehicleBookingLocationId = 1,
      vehicleBookingId = 2,
      countryId = 3,
      cityId = 4,
      addressLine = "",
      postalCode = "",
      airportCode = None,
      locationName = "",
      isAirport = false,
      airportProviderLocation = None,
      extraInfo = "",
      supplierLocationCode = None,
      phoneNumber = Some("1234"),
      locationType = Some("OnAirport"),
      recCreatedWhen = Option(expectedCurrentDateTime),
      recModifiedWhen = expectedCurrentDateTime
    )
    val itineraryId = 12345
    val bookingId   = 54321L
    val actionId    = 0

    val reservationIds: ReservedIds[CarBookingToken, VehicleReservedIds] = ReservedIds(
      bookingId,
      actionId,
      None,
      Product(
        bookingType = BookingType.Vehicle,
        baseCarToken,
        None
      )
    )

    val vehicleInternalModel = VehicleModelInternal(
      vehicleBooking = VehicleModelBooking(
        vehicleBookingId = bookingId,
        itineraryId = itineraryId,
        multiProductId = None,
        bookingDate = expectedCurrentDateTime,
        paymentModel = 1,
        displayCurrency = "USD",
        supplierId = 3003,
        providerCode = "CAR-001",
        supplierBookingId = "",
        supplierSpecificData = "",
        supplierStatusCode = None,
        supplierCommissionAmount = 0,
        supplierCommissionPercentage = 0,
        whitelabelId = 1,
        isCancelled = false,
        cancellationPolicy = "20D50P_100P",
        cancellationDate = None,
        fraudScore = None,
        fraudAction = None,
        fraudCheckIp = "",
        storefrontId = 1,
        platformId = None,
        languageId = None,
        serverName = None,
        cid = None,
        searchId = None,
        searchRequestId = "",
        sessionId = "",
        clientIpAddress = "",
        trackingCookieId = None,
        trackingCookieDate = None,
        trackingTag = None,
        recCreatedWhen = Some(expectedCurrentDateTime),
        recModifiedWhen = expectedCurrentDateTime,
        recStatus = RecStatus.Active,
        vehicleStateId = 0,
        rejectReasonMessage = None,
        rejectReasonCode = None,
        accountingEntityStr = Some("""{"merchantOfRecord":5632,"rateContract":31001,"revenue":5674}"""),
        postBookingStateId = Some(1),
        commonBookingInfo = None,
        commonBookingEventsInfo = None
      ),
      vehicleBookingLocation = VehicleModelBookingPickUpDropOffLocation(
        pickUp = location,
        dropOff = location
      ),
      vehicleBookingSummary = VehicleModelBookingSummary(
        vehicleBookingId = bookingId,
        displayCurrency = "THB",
        totalSurcharge = 900,
        surchargeDetails = "THIS IS SURCHARGE DEATILS",
        baseDiscount = 0.0,
        campaignDiscount = 0.0,
        totalFare = 0.0,
        agodaFee = 0.0,
        recCreatedWhen = Some(expectedCurrentDateTime),
        recModifiedWhen = expectedCurrentDateTime,
        recStatus = RecStatus.Active,
        policyChargeDetails = Some("THIS IS POLICY CHARGE DETAILS"),
        paymentModel = Some(1),
        baseFare = Some(0.0),
        taxAndFee = Some(0.0),
        extraChargeDetails = Some("THIS IS JSON STRING"),
        postBookingMetadata = Some("THIS IS JSON STRING")
      ),
      vehicleBookingTrip = VehicleModelBookingTrip(
        vehicleBookingId = bookingId,
        vehicleCode = "CAR-001",
        vehicleName = "Honda",
        classification = "Mini",
        pickupDatetime = expectedCurrentDateTime,
        dropOffDatetime = expectedCurrentDateTime,
        pickupLocationId = -1,
        dropOffLocationId = -1,
        driverAge = 35,
        flightNo = None,
        recCreatedWhen = Some(expectedCurrentDateTime),
        recModifiedWhen = expectedCurrentDateTime,
        supplierConfirmationCode = Some("abc"),
        customerAgeGroup = None,
        securityDepositType = None,
        localRenter = None
      ),
      vehicleFinancialBreakdowns = Seq.empty,
      vehicleBookingCancellation = Some(
        VehicleModelBookingCancellation(
          vehicleCancellationId = 112L,
          vehicleBookingId = 456L,
          actionId = 321L,
          cancellationReason = "Normal Cancellation flow",
          isForcedRefunded = false,
          forcedRefundReason = None,
          isPastDeparted = false,
          cancelledOnSupplier = true,
          isSupplierAcceptedRefund = true,
          supplierErrorMessage = None,
          recStatus = 1,
          recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
          recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
          recCreatedBy = Some("as-bcrepci-3a01"),
          recModifiedBy = "as-bcrepci-3a01"
        )
      ),
      vehicleInfo = Some(
        VehicleInfo(
          vehicleInfoId = 123L,
          vehicleBookingId = 321L,
          vehicleCode = "CAR-001",
          vehicleName = "Honda",
          vehicleClassification = "Mini",
          vehicleDoors = Some(4),
          vehicleSeats = Some(4),
          vehicleSuitcases = Some(1),
          vehicleTransmission = Some("vehicleTransmission"),
          vehicleIsAircon = Some(true),
          vehicleIsAirbag = Some(true),
          vehicleFuelType = Some("vehicleFuelType"),
          recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
          recModifiedWhen = DateTime.parse("2019-08-01T16:01"),
          vehicleMileagePolicy = Some(
            VehicleMileagePolicy(
              freeDistance = 0,
              code = "",
              description = "Mock Car Fuel Info",
              charge = None,
              isFreeCoverage = true
            )
          ),
          vehicleFuelPolicy = Some(
            VehicleFuelPolicy(
              coverageType = "Full_To_Full",
              code = "",
              description = "fuel policy",
              charge = None,
              isFreeCoverage = true
            )
          ),
          imageUrl = Some("imageUrl"),
          pickUpSupplierOperationHours =
            Some(Seq(VehicleOperationSchedule("Monday", Seq(VehicleSupplierOperationHour("06:00", "00:00"))))),
          dropOffSupplierOperationHours =
            Some(Seq(VehicleOperationSchedule("Monday", Seq(VehicleSupplierOperationHour("06:00", "00:00"))))),
          providerIconUrl = Some("iconUrl"),
          acrissCode = Some("acrissCode")
        )
      ),
      baseBooking = None
    )

    "return a correct bookingActionState" in {
      // Arrange
      val expectedItineraryId     = 12345
      val expectedBookingId       = 54321L
      val expectedActionId        = 0
      val expectedDisplayCurrency = "THB"
      val expectedCountryId       = 4321
      val initialProductPayment = ProductPaymentInfo(
        agency = None,
        payNow = Some(
          PayNowProductPayment(
            ProductPayment(
              900,
              30,
              expectedDisplayCurrency,
              Some(1)
            )
          )
        ),
        payLater = None,
        accountingEntity = Some(AccountingEntities.carEntity),
        points = Vector(
          com.agoda.mpb.common.models.state.Points(
            com.agoda.mpb.common.PointsType.RMMiles,
            PointsAttributes(
              currency = "USD",
              amount = 30,
              localCurrency = Some("THB"),
              localAmount = Some(900),
              externalLoyaltyInfo = Some(
                ExternalLoyaltyInfo(
                  loyaltyToken = Some("loyaltyToken"),
                  fiatUserPayable = None,
                  userPointsPayable = None,
                  partnerClaim = Some("partnerClaimToken"),
                  pointsToEarn = None
                )
              )
            )
          )
        )
      )
      val expectedProductPayment = ProductPaymentInfo(
        agency = None,
        payNow = Some(
          PayNowProductPayment(
            ProductPayment(
              900,
              30,
              expectedDisplayCurrency,
              Some(1)
            )
          )
        ),
        payLater = None,
        accountingEntity = Some(AccountingEntities.carEntity),
        points = Vector(
          com.agoda.mpb.common.models.state.Points(
            com.agoda.mpb.common.PointsType.RMMiles,
            PointsAttributes(
              currency = "USD",
              amount = 30,
              localCurrency = Some("THB"),
              localAmount = Some(900),
              externalLoyaltyInfo = Some(
                ExternalLoyaltyInfo(
                  loyaltyToken = Some("loyaltyToken"),
                  fiatUserPayable = None,
                  userPointsPayable = None,
                  partnerClaim = Some("partnerClaimToken"),
                  pointsToEarn = None
                )
              )
            )
          )
        )
      )
      val reservationIds: ReservedIds[CarBookingToken, VehicleReservedIds] = ReservedIds(
        expectedBookingId,
        expectedActionId,
        None,
        Product(
          bookingType = BookingType.Vehicle,
          baseCarToken.copy(
            info = CarBookingInfo(
              paymentModel = PaymentModel.MerchantCommission,
              carItemPriceSummary = CarPricingSummary(
                CarChargeDisplay(
                  currency = expectedDisplayCurrency,
                  baseFare = 3000,
                  taxAndFee = 600,
                  baseDiscount = 0,
                  campaignDiscount = 0,
                  totalFare = 6000,
                  totalSurcharge = 900,
                  surchargeDetails = "THIS IS JSON OF SURCHARGE DETAILS",
                  agodaFee = 0,
                  policyChargesDetails = Some("THIS IS JSON OF POLICY CHARGE DETAILS"),
                  paymentModel = 2,
                  extraChargesDetails = Some("THIS IS JSON OF extraChargeDetails")
                )
              ),
              priceBreakdowns = Seq(
                CarItemBreakdown(
                  expectedCurrentDateTime,
                  itemId = 12,
                  typeId = 2,
                  taxFeeId = Some(3),
                  quantity = 1,
                  localCurrency = expectedDisplayCurrency,
                  localAmount = 20.0,
                  exchangeRate = 30.0,
                  usdAmount = 30.0,
                  reqAmount = 900.0,
                  vendorExchangeRate = 1,
                  requestedCurrency = "INR"
                )
              ),
              pickUp = LocationInfo(
                airportInfo = None,
                cityId = 1234,
                countryId = expectedCountryId,
                dateTime = expectedCurrentDateTime,
                addressLine = "THIS IS ADDRESS LINE",
                postalCode = "THIS IS POSTAL CODE",
                extraLocationInfo = "THIS IS EXTRA LOCATION INFO",
                locationName = "THIS IS LOCATION NAME",
                supplierLocationCode = "THIS IS SUPPLIER LOCATION CODE",
                locationType = Some("THIS IS LOCATION TYPE"),
                phoneNumbers = Seq("1234")
              ),
              dropOff = LocationInfo(
                airportInfo = None,
                cityId = 8888,
                countryId = expectedCountryId,
                dateTime = expectedCurrentDateTime,
                addressLine = "THIS IS ADDRESS LINE",
                postalCode = "THIS IS POSTAL CODE",
                extraLocationInfo = "THIS IS EXTRA LOCATION INFO",
                locationName = "THIS IS LOCATION NAME",
                supplierLocationCode = "THIS IS SUPPLIER LOCATION CODE",
                locationType = Some("THIS IS LOCATION TYPE"),
                phoneNumbers = Seq("1234")
              ),
              vehicleCode = "CAR-001",
              vehicleName = "Honda",
              classification = "Sedan",
              cancellationPolicy = "3D50P_100P",
              vehicleExtraInfo = VehicleExtraInfo(
                vehicleDoors = Some(4),
                vehicleSeats = Some(1),
                vehicleSuitcases = Some(1),
                vehicleTransmission = Some("transmission"),
                vehicleIsAircon = Some(true),
                vehicleIsAirbag = Some(true),
                vehicleFuelType = Some("fuelType"),
                imageUrl = Some("imageUrl"),
                pickUpSupplierOperationOfHours =
                  Some(Seq(OperationSchedule("Monday", Seq(SupplierOperationHour("06:00", "00:00"))))),
                dropOffSupplierOperationOfHours =
                  Some(Seq(OperationSchedule("Monday", Seq(SupplierOperationHour("06:00", "00:00"))))),
                providerIconUrl = Some("iconUrl"),
                acrissCode = Some("acrissCode")
              ),
              vehicleMileagePolicy = Some(
                MileagePolicy(
                  freeDistance = 0,
                  code = "",
                  description = "Mock Car Fuel Info",
                  charge = None,
                  isFreeCoverage = true
                )
              ),
              vehicleFuelPolicy = Some(
                FuelPolicy(
                  coverageType = "Full_To_Full",
                  code = "",
                  description = "fuel policy",
                  charge = None,
                  isFreeCoverage = true
                )
              ),
              vehicleCustomerPolicyInfo = None
            ),
            paymentAmount = Some(
              PaymentAmount(
                paymentCurrency = expectedDisplayCurrency,
                paymentAmount = 900,
                paymentAmountUSD = 30,
                exchangeRate = 1,
                upliftAmount = 30,
                siteExchangeRate = 1,
                upliftExchangeRate = 1,
                destinationExchangeRate = 0,
                giftcardAmount = 900,
                giftcardAmountUSD = 30
              )
            ),
            productPayment = Some(initialProductPayment),
            currentCancellationPolicy = Some(CarCancellationPolicyType.FREE_CANCEL)
          ),
          None
        )
      )

      val vehicleInternalModel = VehicleModelInternal(
        vehicleBooking = VehicleModelBooking(
          vehicleBookingId = expectedBookingId,
          itineraryId = expectedItineraryId,
          multiProductId = None,
          bookingDate = expectedCurrentDateTime,
          paymentModel = 1,
          displayCurrency = "THB",
          supplierId = 3003,
          providerCode = "CAR-001",
          supplierBookingId = "",
          supplierSpecificData = "",
          supplierStatusCode = None,
          supplierCommissionAmount = 0,
          supplierCommissionPercentage = 0,
          whitelabelId = 1,
          isCancelled = false,
          cancellationPolicy = "20D50P_100P",
          cancellationDate = None,
          fraudScore = None,
          fraudAction = None,
          fraudCheckIp = "",
          storefrontId = 1,
          platformId = None,
          languageId = None,
          serverName = None,
          cid = None,
          searchId = None,
          searchRequestId = "",
          sessionId = "",
          clientIpAddress = "",
          trackingCookieId = None,
          trackingCookieDate = None,
          trackingTag = None,
          recCreatedWhen = Some(expectedCurrentDateTime),
          recModifiedWhen = expectedCurrentDateTime,
          recStatus = RecStatus.Active,
          vehicleStateId = 0,
          rejectReasonMessage = None,
          rejectReasonCode = None,
          accountingEntityStr = Some("""{"merchantOfRecord":5632,"rateContract":31001,"revenue":5674}"""),
          postBookingStateId = Some(1),
          commonBookingInfo = None,
          commonBookingEventsInfo = None
        ),
        vehicleBookingLocation = VehicleModelBookingPickUpDropOffLocation(
          pickUp = location,
          dropOff = location
        ),
        vehicleBookingSummary = VehicleModelBookingSummary(
          vehicleBookingId = expectedBookingId,
          displayCurrency = "THB",
          totalSurcharge = 900,
          surchargeDetails = "THIS IS SURCHARGE DEATILS",
          baseDiscount = 0.0,
          campaignDiscount = 0.0,
          totalFare = 0.0,
          agodaFee = 0.0,
          recCreatedWhen = Some(expectedCurrentDateTime),
          recModifiedWhen = expectedCurrentDateTime,
          recStatus = RecStatus.Active,
          policyChargeDetails = Some("THIS IS POLICY CHARGE DETAILS"),
          paymentModel = Some(1),
          baseFare = Some(0.0),
          taxAndFee = Some(0.0),
          extraChargeDetails = Some("THIS IS JSON STRING"),
          postBookingMetadata = Some("THIS IS JSON STRING")
        ),
        vehicleBookingTrip = VehicleModelBookingTrip(
          vehicleBookingId = expectedBookingId,
          vehicleCode = "CAR-001",
          vehicleName = "Honda",
          classification = "Mini",
          pickupDatetime = expectedCurrentDateTime,
          dropOffDatetime = expectedCurrentDateTime,
          pickupLocationId = -1,
          dropOffLocationId = -1,
          driverAge = 35,
          flightNo = None,
          recCreatedWhen = Some(expectedCurrentDateTime),
          recModifiedWhen = expectedCurrentDateTime,
          supplierConfirmationCode = Some("abc"),
          customerAgeGroup = Some("age-group"),
          securityDepositType = Some("security-depoisit"),
          localRenter = Some("local-renter")
        ),
        vehicleFinancialBreakdowns = Seq.empty,
        vehicleBookingCancellation = Some(
          VehicleModelBookingCancellation(
            vehicleCancellationId = 112L,
            vehicleBookingId = 456L,
            actionId = 321L,
            cancellationReason = "Normal Cancellation flow",
            isForcedRefunded = false,
            forcedRefundReason = None,
            isPastDeparted = false,
            cancelledOnSupplier = true,
            isSupplierAcceptedRefund = true,
            supplierErrorMessage = None,
            recStatus = 1,
            recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
            recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
            recCreatedBy = Some("as-bcrepci-3a01"),
            recModifiedBy = "as-bcrepci-3a01"
          )
        ),
        vehicleInfo = Some(
          VehicleInfo(
            vehicleInfoId = 123L,
            vehicleBookingId = 312L,
            vehicleCode = "CAR-001",
            vehicleName = "Honda",
            vehicleClassification = "Mini",
            vehicleDoors = Some(4),
            vehicleSeats = Some(4),
            vehicleSuitcases = Some(1),
            vehicleTransmission = Some("vehicleTransmission"),
            vehicleIsAircon = Some(true),
            vehicleIsAirbag = Some(true),
            vehicleFuelType = Some("vehicleFuelType"),
            recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
            recModifiedWhen = DateTime.parse("2019-08-01T16:01"),
            vehicleMileagePolicy = Some(
              VehicleMileagePolicy(
                freeDistance = 0,
                code = "",
                description = "Mock Car Fuel Info",
                charge = None,
                isFreeCoverage = true
              )
            ),
            vehicleFuelPolicy = Some(
              VehicleFuelPolicy(
                coverageType = "Full_To_Full",
                code = "",
                description = "fuel policy",
                charge = None,
                isFreeCoverage = true
              )
            ),
            imageUrl = Some("imageUrl"),
            pickUpSupplierOperationHours =
              Some(Seq(VehicleOperationSchedule("Monday", Seq(VehicleSupplierOperationHour("06:00", "00:00"))))),
            dropOffSupplierOperationHours =
              Some(Seq(VehicleOperationSchedule("Monday", Seq(VehicleSupplierOperationHour("06:00", "00:00"))))),
            providerIconUrl = Some("iconUrl"),
            acrissCode = Some("acrissCode")
          )
        ),
        baseBooking = None
      )

      // Act
      val actualBookingActionState: BookingActionState =
        vehicleMapper.toBookingActionState(baseInput, expectedItineraryId, reservationIds, vehicleInternalModel)

      // Assert
      actualBookingActionState.vehicleBookingState.nonEmpty shouldEqual true
      val vehicleBookingActionState = actualBookingActionState.vehicleBookingState.get

      vehicleBookingActionState.vehicleBooking.vehicleBookingId shouldEqual expectedBookingId
      vehicleBookingActionState.vehicleBooking.itineraryId shouldEqual expectedItineraryId
      vehicleBookingActionState.vehicleBooking.displayCurrency shouldEqual expectedDisplayCurrency
      vehicleBookingActionState.vehicleBooking.recStatus shouldEqual RecStatus.Active
      actualBookingActionState.paymentInfo.method shouldEqual PaymentMethod.Visa
      actualBookingActionState.paymentInfo.paymentCurrency shouldEqual "THB"
      actualBookingActionState.paymentInfo.paymentAmount shouldEqual 900
      actualBookingActionState.paymentInfo.paymentAmountUSD shouldEqual 30
      actualBookingActionState.paymentInfo.gateway shouldEqual Some(Gateway.GMO)
      actualBookingActionState.paymentInfo.siteExchangeRate shouldEqual Some(30)
      actualBookingActionState.paymentInfo.destinationCurrency shouldEqual Some("string")
      actualBookingActionState.paymentInfo.destinationExchangeRate shouldEqual Some(0)
      actualBookingActionState.paymentInfo.points.head shouldEqual Points(
        PointsType.RMMiles,
        PointsAttributes("USD", 30, Some("THB"), Some(900))
      )
      actualBookingActionState.paymentInfo.accountingEntity shouldEqual None // in agent this value should read from master action
      actualBookingActionState.commonPaymentInfo shouldEqual None
      actualBookingActionState.productPaymentInfo shouldEqual Some(expectedProductPayment)
      actualBookingActionState.productPaymentInfo.map(
        _.points.flatMap(_.pointAttributes.externalLoyaltyInfo.flatMap(_.partnerClaim))
      ) shouldEqual Some(Vector("partnerClaimToken"))
      actualBookingActionState.riskInfo shouldBe Some(RiskInfo(Some(CancellationClass.FreeCancellation)))
    }

    "return BAS with Cart Product Info present" when {
      "Cart Item Context is provided" in {
        // Arrange
        val cartItemContext: CartItemContext = CartItemContext("e0049b61-a70a-40af-b8d8-a2a42bd2c076")
        val reservationIdsLocal = reservationIds.copy(
          product = reservationIds.product.copy(
            info = reservationIds.product.info.copy(
              cartItemContext = Some(cartItemContext)
            )
          )
        )
        val expectedCartProductInfo: CartProductInfo = CartProductInfo(cartItemContext)

        // Act
        val actualBookingActionState: BookingActionState =
          vehicleMapper.toBookingActionState(baseInput, itineraryId, reservationIdsLocal, vehicleInternalModel)

        // Assert
        actualBookingActionState.cartProductInfo shouldEqual Some(expectedCartProductInfo)
      }
    }

    "return BAS with Cart Product Info absent" when {
      "Cart Item Context is not provided" in {
        // Act
        val actualBookingActionState: BookingActionState =
          vehicleMapper.toBookingActionState(baseInput, itineraryId, reservationIds, vehicleInternalModel)

        // Assert
        actualBookingActionState.cartProductInfo shouldEqual None

      }
    }

    "ToBookingAction" should {
      "return a correct bookingAction" in {
        // Arrange
        val expectedItineraryId     = 12345
        val expectedBookingId       = 54321L
        val expectedActionId        = 0
        val expectedDisplayCurrency = "THB"
        val expectedCountryId       = 4321
        val reservationIds: ReservedIds[CarBookingToken, VehicleReservedIds] = ReservedIds(
          expectedBookingId,
          expectedActionId,
          None,
          Product(
            bookingType = BookingType.Vehicle,
            baseCarToken.copy(info =
              CarBookingInfo(
                paymentModel = PaymentModel.MerchantCommission,
                carItemPriceSummary = CarPricingSummary(
                  CarChargeDisplay(
                    currency = expectedDisplayCurrency,
                    baseFare = 3000,
                    taxAndFee = 600,
                    baseDiscount = 0,
                    campaignDiscount = 0,
                    totalFare = 6000,
                    totalSurcharge = 900,
                    surchargeDetails = "THIS IS JSON OF SURCHARGE DETAILS",
                    agodaFee = 0,
                    policyChargesDetails = Some("THIS IS JSON OF POLICY CHARGE DETAILS"),
                    paymentModel = 2,
                    extraChargesDetails = Some("THIS IS JSON OF extraChargeDetails")
                  )
                ),
                priceBreakdowns = Seq(
                  CarItemBreakdown(
                    expectedCurrentDateTime,
                    itemId = 1,
                    typeId = 2,
                    taxFeeId = Some(3),
                    quantity = 1,
                    localCurrency = expectedDisplayCurrency,
                    localAmount = 20.0,
                    exchangeRate = 30.0,
                    usdAmount = 30.0,
                    reqAmount = 900.0,
                    vendorExchangeRate = 1,
                    requestedCurrency = "INR"
                  )
                ),
                pickUp = LocationInfo(
                  airportInfo = None,
                  cityId = 1234,
                  countryId = expectedCountryId,
                  dateTime = expectedCurrentDateTime,
                  addressLine = "THIS IS ADDRESS LINE",
                  postalCode = "THIS IS POSTAL CODE",
                  extraLocationInfo = "THIS IS EXTRA LOCATION INFO",
                  locationName = "THIS IS LOCATION NAME",
                  supplierLocationCode = "THIS IS SUPPLIER LOCATION CODE",
                  locationType = Some("THIS IS LOCATION TYPE"),
                  phoneNumbers = Seq("1234")
                ),
                dropOff = LocationInfo(
                  airportInfo = None,
                  cityId = 8888,
                  countryId = expectedCountryId,
                  dateTime = expectedCurrentDateTime,
                  addressLine = "THIS IS ADDRESS LINE",
                  postalCode = "THIS IS POSTAL CODE",
                  extraLocationInfo = "THIS IS EXTRA LOCATION INFO",
                  locationName = "THIS IS LOCATION NAME",
                  supplierLocationCode = "THIS IS SUPPLIER LOCATION CODE",
                  locationType = Some("THIS IS LOCATION TYPE"),
                  phoneNumbers = Seq("1234")
                ),
                vehicleCode = "CAR-001",
                vehicleName = "Honda",
                classification = "Sedan",
                cancellationPolicy = "3D50P_100P",
                vehicleExtraInfo = VehicleExtraInfo(
                  vehicleDoors = Some(4),
                  vehicleSeats = Some(1),
                  vehicleSuitcases = Some(1),
                  vehicleTransmission = Some("transmission"),
                  vehicleIsAircon = Some(true),
                  vehicleIsAirbag = Some(true),
                  vehicleFuelType = Some("fuelType"),
                  imageUrl = Some("imageUrl"),
                  pickUpSupplierOperationOfHours =
                    Some(Seq(OperationSchedule("Monday", Seq(SupplierOperationHour("06:00", "00:00"))))),
                  dropOffSupplierOperationOfHours =
                    Some(Seq(OperationSchedule("Monday", Seq(SupplierOperationHour("06:00", "00:00"))))),
                  providerIconUrl = Some("iconUrl"),
                  acrissCode = Some("acrissCode")
                ),
                vehicleMileagePolicy = Some(
                  MileagePolicy(
                    freeDistance = 0,
                    code = "",
                    description = "Mock Car Fuel Info",
                    charge = None,
                    isFreeCoverage = true
                  )
                ),
                vehicleFuelPolicy = Some(
                  FuelPolicy(
                    coverageType = "Full_To_Full",
                    code = "",
                    description = "fuel policy",
                    charge = None,
                    isFreeCoverage = true
                  )
                ),
                vehicleCustomerPolicyInfo = None
              )
            ),
            None
          )
        )
        val vehicleInternalModel = VehicleModelInternal(
          vehicleBooking = VehicleModelBooking(
            vehicleBookingId = expectedBookingId,
            itineraryId = expectedItineraryId,
            multiProductId = None,
            bookingDate = expectedCurrentDateTime,
            paymentModel = 1,
            displayCurrency = "USD",
            supplierId = 3003,
            providerCode = "CAR-001",
            supplierBookingId = "",
            supplierSpecificData = "",
            supplierStatusCode = None,
            supplierCommissionAmount = 0,
            supplierCommissionPercentage = 0,
            whitelabelId = 1,
            isCancelled = false,
            cancellationPolicy = "20D50P_100P",
            cancellationDate = None,
            fraudScore = None,
            fraudAction = None,
            fraudCheckIp = "",
            storefrontId = 1,
            platformId = None,
            languageId = None,
            serverName = None,
            cid = None,
            searchId = None,
            searchRequestId = "",
            sessionId = "",
            clientIpAddress = "",
            trackingCookieId = None,
            trackingCookieDate = None,
            trackingTag = None,
            recCreatedWhen = Some(expectedCurrentDateTime),
            recModifiedWhen = expectedCurrentDateTime,
            recStatus = RecStatus.Active,
            vehicleStateId = 0,
            rejectReasonMessage = None,
            rejectReasonCode = None,
            accountingEntityStr = Some("""{"merchantOfRecord":5632,"rateContract":31001,"revenue":5674}"""),
            postBookingStateId = Some(1),
            commonBookingInfo = None,
            commonBookingEventsInfo = None
          ),
          vehicleBookingLocation = VehicleModelBookingPickUpDropOffLocation(
            pickUp = location,
            dropOff = location
          ),
          vehicleBookingSummary = VehicleModelBookingSummary(
            vehicleBookingId = expectedBookingId,
            displayCurrency = "THB",
            totalSurcharge = 900,
            surchargeDetails = "THIS IS SURCHARGE DEATILS",
            baseDiscount = 0.0,
            campaignDiscount = 0.0,
            totalFare = 0.0,
            agodaFee = 0.0,
            recCreatedWhen = Some(expectedCurrentDateTime),
            recModifiedWhen = expectedCurrentDateTime,
            recStatus = RecStatus.Active,
            policyChargeDetails = Some("THIS IS POLICY CHARGE DETAILS"),
            paymentModel = Some(1),
            baseFare = Some(0.0),
            taxAndFee = Some(0.0),
            extraChargeDetails = Some("THIS IS JSON STRING"),
            postBookingMetadata = Some("THIS IS JSON STRING")
          ),
          vehicleBookingTrip = VehicleModelBookingTrip(
            vehicleBookingId = expectedBookingId,
            vehicleCode = "CAR-001",
            vehicleName = "Honda",
            classification = "Mini",
            pickupDatetime = expectedCurrentDateTime,
            dropOffDatetime = expectedCurrentDateTime,
            pickupLocationId = -1,
            dropOffLocationId = -1,
            driverAge = 35,
            flightNo = None,
            recCreatedWhen = Some(expectedCurrentDateTime),
            recModifiedWhen = expectedCurrentDateTime,
            supplierConfirmationCode = Some("abc"),
            customerAgeGroup = None,
            securityDepositType = None,
            localRenter = None
          ),
          vehicleFinancialBreakdowns = Seq.empty,
          vehicleBookingCancellation = Some(
            VehicleModelBookingCancellation(
              vehicleCancellationId = 112L,
              vehicleBookingId = 456L,
              actionId = 321L,
              cancellationReason = "Normal Cancellation flow",
              isForcedRefunded = false,
              forcedRefundReason = None,
              isPastDeparted = false,
              cancelledOnSupplier = true,
              isSupplierAcceptedRefund = true,
              supplierErrorMessage = None,
              recStatus = 1,
              recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
              recModifiedWhen = DateTime.parse("2019-08-02T16:01"),
              recCreatedBy = Some("as-bcrepci-3a01"),
              recModifiedBy = "as-bcrepci-3a01"
            )
          ),
          vehicleInfo = Some(
            VehicleInfo(
              vehicleInfoId = 123L,
              vehicleBookingId = 321L,
              vehicleCode = "CAR-001",
              vehicleName = "Honda",
              vehicleClassification = "Mini",
              vehicleDoors = Some(4),
              vehicleSeats = Some(4),
              vehicleSuitcases = Some(1),
              vehicleTransmission = Some("vehicleTransmission"),
              vehicleIsAircon = Some(true),
              vehicleIsAirbag = Some(true),
              vehicleFuelType = Some("vehicleFuelType"),
              recCreatedWhen = Some(DateTime.parse("2019-08-01T16:01")),
              recModifiedWhen = DateTime.parse("2019-08-01T16:01"),
              vehicleMileagePolicy = Some(
                VehicleMileagePolicy(
                  freeDistance = 0,
                  code = "",
                  description = "Mock Car Fuel Info",
                  charge = None,
                  isFreeCoverage = true
                )
              ),
              vehicleFuelPolicy = Some(
                VehicleFuelPolicy(
                  coverageType = "Full_To_Full",
                  code = "",
                  description = "fuel policy",
                  charge = None,
                  isFreeCoverage = true
                )
              ),
              imageUrl = Some("imageUrl"),
              pickUpSupplierOperationHours =
                Some(Seq(VehicleOperationSchedule("Monday", Seq(VehicleSupplierOperationHour("06:00", "00:00"))))),
              dropOffSupplierOperationHours =
                Some(Seq(VehicleOperationSchedule("Monday", Seq(VehicleSupplierOperationHour("06:00", "00:00"))))),
              providerIconUrl = Some("iconUrl"),
              acrissCode = Some("acrissCode")
            )
          ),
          baseBooking = None
        )
        val expectedBookingActionState =
          vehicleMapper.toBookingActionState(baseInput, expectedItineraryId, reservationIds, vehicleInternalModel)

        // Act
        val actualBookingAction: BookingWorkflowAction =
          vehicleMapper.toBookingAction(baseInput, expectedItineraryId, reservationIds, expectedBookingActionState)

        // Assert
        actualBookingAction.actionId shouldEqual expectedActionId
        actualBookingAction.itineraryId shouldEqual expectedItineraryId
        actualBookingAction.bookingType shouldEqual Some(BookingType.Vehicle.id)
        actualBookingAction.bookingId shouldEqual Some(expectedBookingId)
        actualBookingAction.memberId shouldEqual baseInput.getMemberId
        actualBookingAction.actionTypeId shouldEqual VehicleActionType.Created.id
        actualBookingAction.correlationId shouldEqual baseInput.requestContext.getCorrelationId()
        actualBookingAction.requestId shouldEqual baseInput.requestContext.getCorrelationId()
        actualBookingAction.workflowId shouldEqual WorkflowId.Vehicle.id
        actualBookingAction.workflowStateId shouldEqual 3001
        actualBookingAction.state shouldEqual expectedBookingActionState.toJson
      }
    }
  }
}
