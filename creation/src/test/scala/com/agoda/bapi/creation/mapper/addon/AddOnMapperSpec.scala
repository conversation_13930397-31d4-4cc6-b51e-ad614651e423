package com.agoda.bapi.creation.mapper.addon

import com.agoda.bapi.common.config.AgodaConfig
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.MpbWorkflowPhase
import com.agoda.bapi.common.model.addOn
import com.agoda.bapi.common.model.addOn.{AddOnBookingToken, UserTaxCountry}
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.common.model.flight.history.ActionType
import com.agoda.mpb.common.MultiProductType.MultiProductType
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.MockRequestContext
import com.agoda.bapi.creation.mapper.ebe.StateSchemaVersion
import com.agoda.bapi.creation.model.db.{BookingActionState, BookingActionStateCustomer, CreationRequest, PaymentInfo}
import com.agoda.bapi.creation.model.multi.{EmptyProductReservedIds, MultiProductsRequest, Product, ReservedIds}
import com.agoda.bapi.creation.model.protections.ProtectionState
import com.agoda.capi.enigma.shared_model.common.RecStatus
import com.agoda.commons.agprotobuf.meta.{MetaNames, MetaType => UniMetaType}
import com.agoda.mpb.common.{BookingType, MultiProductType, WorkflowId}
import com.agoda.mpbe.state.booking._
import com.agoda.mpbe.state.common.enums.Gateway.Gateway
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.agoda.mpbe.state.product.addOn.AddOnProductModel
import com.softwaremill.diffx.Diff
import com.softwaremill.diffx.generic.auto._
import com.softwaremill.diffx.scalatest.DiffShouldMatcher._
import mocks.AddOnModelMock
import org.mockito.Mockito.when
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{AppendedClues, BeforeAndAfter, OptionValues}
import org.scalatestplus.mockito.MockitoSugar

class AddOnMapperSpec
    extends AsyncWordSpec
    with Matchers
    with AppendedClues
    with BeforeAndAfter
    with OptionValues
    with MockitoSugar {

  "toProductModel" should {
    "return productModel properly when all reserved IDs are generated" in {
      val featureAware                     = mock[FeatureAware]
      implicit val context: RequestContext = MockRequestContext.create().copy(featureAware = Some(featureAware))
      implicit val baseBookingMetaDiff: Diff[BaseBookingEssInfo] = Diff
        .derived[BaseBookingEssInfo]
        .ignore(_.recCreatedWhen)

      val scope = new AddOnMapperSpecScope {}
      import scope._

      val expectedEssInfo = BaseBookingEssInfo(
        bookingEssInfoId = 55555L,
        bookingId = 54321,
        userTaxCountryId = 9999,
        recStatus = RecStatus.Active.value,
        recCreatedWhen = baseBookingLocalDateTime,
        recCreatedBy = bapiIdentifierUuid,
        bookerResidenceCountryId = Some(9998),
        paymentInstrumentCountryId = Some(9997),
        ipAddressCountryId = Some(9996)
      )

      val expectedFinancialBreakdowns = defaulProtectionAddOnProductModel.product.breakdowns.zipWithIndex.map {
        case (breakdown, index) =>
          breakdown.copy(breakdownId = index.toLong + 1L)
      }
      val expectedSupplierInfo = Some(
        BaseSupplierInfo(
          bookingId = 54321,
          supplierId = 100,
          supplierSpecificData = Some("SupplierSpecificData"),
          recCreatedWhen = Some(baseBookingLocalDateTime),
          recCreatedBy = Some(bapiIdentifierUuid)
        )
      )
      val expectedProductModel: AddOnProductModel = defaulProtectionAddOnProductModel.copy(product =
        defaulProtectionAddOnProductModel.product.copy(
          essInfo = Some(expectedEssInfo),
          breakdowns = expectedFinancialBreakdowns,
          supplierInfo = expectedSupplierInfo
        )
      )
      val resultProductModel: AddOnProductModel = addOnMapper
        .toProductModel(
          request = defaultMultiProductsRequest,
          itineraryId = defaultItineraryId,
          masterActionId = defaultActionId,
          reservedIds = reservedIdsWithBreakdownKeys,
          baseBookingMetas = defaultProtectionAddOnBaseBookingMetas,
          bookingDateTime = baseBookingDateTime,
          userTaxCountry = Some(
            UserTaxCountry(
              userTaxCountryId = Some(9999),
              bookerResidenceCountryId = Some(9998),
              paymentInstrumentCountryId = Some(9997),
              ipAddressCountryId = Some(9996)
            )
          )
        )

      resultProductModel shouldMatchTo expectedProductModel
    }
  }

  "toBookingAction" should {
    "return bookingAction properly" in {
      val scope = new AddOnMapperSpecScope {}
      import com.agoda.bapi.common.util.converters.ItineraryStateConverters._
      import scope._
      val expectedBookingAction: BookingWorkflowAction = BookingWorkflowAction(
        actionId = 1234,
        itineraryId = 12345,
        bookingType = Some(BookingType.Protection.id),
        bookingId = Some(defaultBookingId),
        memberId = 0,
        actionTypeId = ActionType.Created.id,
        correlationId = "",
        requestId = "",
        workflowId = WorkflowId.Protection.id,
        workflowStateId = ProtectionState.CreateBooking.id,
        productTypeId = Some(MultiProductType.SingleProtection.id),
        stateSchemaVersion = StateSchemaVersion.Default,
        state = "",
        storefrontId = Some(3),
        languageId = Some(1),
        workflowPhaseId = Option(MpbWorkflowPhase.Received.id)
      )
      val resultBookingAction: BookingWorkflowAction = addOnMapper
        .toBookingAction(
          request = defaultMultiProductsRequest,
          itineraryId = defaultItineraryId,
          reservedIds = defaultReservedIds,
          bookingActionState = BookingActionState(
            mockCreationRequest,
            BookingActionStateCustomer(isUserLoggedIn = false),
            PaymentInfo(
              method = PaymentMethod.Visa,
              paymentCurrency = "THB",
              paymentAmount = 2150.0,
              paymentAmountUSD = 60.0,
              siteExchangeRate = Some(0.5),
              gateway = Some(Gateway.GMO),
              destinationCurrency = None,
              destinationExchangeRate = None,
              points = Vector.empty
            ),
            creditCardInfo = defaultMultiProductsRequest.getCreditCardInfo,
            bookingState = None,
            campaignInfo = None,
            itineraryProtoState = toItineraryState(defaulProtectionAddOnProductModel).toBase64OptString
          )
        )
        .copy(state = "")

      resultBookingAction shouldBe expectedBookingAction
    }
  }

  "toBookingActionState" should {
    "return BookingActionState properly" in {
      val scope = new AddOnMapperSpecScope {}
      import com.agoda.bapi.common.util.converters.ItineraryStateConverters._
      import scope._

      val expectedBookingActionState: BookingActionState = BookingActionState(
        mockCreationRequest,
        BookingActionStateCustomer(),
        PaymentInfo(
          method = PaymentMethod.Visa,
          paymentCurrency = "THB",
          paymentAmount = 2150,
          paymentAmountUSD = 60,
          siteExchangeRate = Some(0.5),
          gateway = Some(Gateway.GMO),
          destinationCurrency = None,
          destinationExchangeRate = None,
          points = Vector.empty
        ),
        creditCardInfo = defaultMultiProductsRequest.getCreditCardInfo,
        campaignInfo = None,
        bookingState = None,
        itineraryProtoState = toItineraryState(defaulProtectionAddOnProductModel).toBase64OptString,
        productPaymentInfo = baseProtectionAddOnToken.productPayment,
        whitelabelToken = baseRequestContext.whiteLabelInfo.token
      )
      val resultBookingActionState: BookingActionState = addOnMapper.toBookingActionState(
        request = defaultMultiProductsRequest,
        itineraryId = defaultItineraryId,
        reservedIds = defaultReservedIds,
        productModel = defaulProtectionAddOnProductModel,
        bookingDateTime = baseBookingDateTime
      )

      resultBookingActionState shouldBe expectedBookingActionState
    }
  }

  "getMetaContents" should {
    "return correct mapped meta" in {
      val scope = new AddOnMapperSpecScope {}
      import scope._
      val expectedMetas: Seq[BaseBookingMeta] = Seq(
        BaseBookingMeta(
          metaName = Some("TRIP_PROTECTION_TYPE"),
          metaType = MetaType.MetaType.STRING,
          metaValue = Some(
            "FLIGHTS"
          ),
          version = 1,
          metaTypeString = Some(UniMetaType.STRING.toString())
        )
      )
      val resF = addOnMapper.getMetaContents(defaultReservedIds)
      resF.map(res => res shouldBe expectedMetas)
    }
  }

  trait AddOnMapperSpecScope extends MockitoSugar with AddOnModelMock {

    val defaultMultiProductsRequest: MultiProductsRequest = MultiProductsRequest(
      request = baseMultiProductReq.copy(userContext = baseUserContext),
      requestContext = baseRequestContext,
      properties = Seq.empty,
      flights = Seq(baseFlightProduct),
      vehicles = Seq.empty,
      protections = Seq.empty,
      activities = Seq.empty,
      cegFastTracks = Seq.empty,
      addOns = Seq(baseProtectionAddOnProduct),
      bookingFlow = BookingFlow.Cart,
      commonPayment = None,
      isBookingFromCart = None
    )

    val mockCreationRequest: CreationRequest = defaultMultiProductsRequest.toCreationRequest

    val defaultReservedIds: ReservedIds[addOn.AddOnBookingToken, EmptyProductReservedIds] = ReservedIds(
      bookingId = defaultBookingId,
      actionId = defaultActionId,
      multiProductId = Some(7768),
      product = Product(
        bookingType = BookingType.Protection,
        info = baseProtectionAddOnToken,
        multiProductType = Some(MultiProductType.Cart)
      ),
      essInfoId = Some(defaultEssInfoId)
    )

    val reservedIdsWithBreakdownKeys: ReservedIds[AddOnBookingToken, EmptyProductReservedIds] = defaultReservedIds.copy(
      breakdownIds = Seq(1L, 2L, 3L)
    )

    val mockAgodaConfig: AgodaConfig = mock[AgodaConfig]
    when(mockAgodaConfig.bapiIdentifierUuid).thenReturn(bapiIdentifierUuid)
    val addOnMapper = new AddOnMapperImpl(mockAgodaConfig)
  }
}
