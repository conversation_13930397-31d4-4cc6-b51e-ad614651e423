package com.agoda.bapi.creation.util

import com.agoda.bapi.agent.common.schema.BookingActionMessageTopic
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.model.{StatusToken, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.service.FeatureAware
import org.mockito.Mockito.when
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

class StatusTokenUtilsTest extends AsyncWordSpec with Matchers with MockitoSugar {
  val operationIdMock: Option[Long] = Some(12345L)

  "getBookingActionMessageTopic" should {
    "return no topic for whitelabel that is not JTB" in {
      implicit val mockRequestContext: RequestContext = mock[RequestContext]
      val mockFeatureAware: FeatureAware              = mock[FeatureAware]
      val mockWhiteLabelInfo                          = mock[WhiteLabelInfo]
      when(mockRequestContext.featureAware).thenReturn(Some(mockFeatureAware))
      when(mockRequestContext.whiteLabelInfo).thenReturn(mockWhiteLabelInfo)
      when(mockWhiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
      StatusTokenUtils.getBookingActionMessageTopic(WhiteLabel.Agoda) shouldEqual None
    }

    "return JTBPartnerProvisioningResult topic for JTB whitelabel" in {
      implicit val mockRequestContext: RequestContext = mock[RequestContext]
      val mockFeatureAware: FeatureAware              = mock[FeatureAware]
      val mockWhiteLabelInfo                          = mock[WhiteLabelInfo]
      when(mockRequestContext.featureAware).thenReturn(Some(mockFeatureAware))
      when(mockRequestContext.whiteLabelInfo).thenReturn(mockWhiteLabelInfo)
      when(mockWhiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Jtb)
      when(mockWhiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsJtbWl)).thenReturn(true)
      StatusTokenUtils.getBookingActionMessageTopic(WhiteLabel.Jtb) shouldEqual Some(
        BookingActionMessageTopic.BAM_Topic_JTBPartnerProvisioningResult.value
      )
    }
  }

  "getStatusTokenVersionAndOperationId" should {

    implicit val requestContext = mock[RequestContext]
    val featureAware            = mock[FeatureAware]
    when(requestContext.featureAware).thenReturn(Some(featureAware))
    "return version4" in {
      when(featureAware.enableStatusTokenV6).thenReturn(false)
      StatusTokenUtils.getStatusTokenVersionAndOperationId(None) shouldEqual (
        StatusToken.Version4,
        None
      )
    }
    "return version6" in {
      when(featureAware.enableStatusTokenV6).thenReturn(true)
      StatusTokenUtils.getStatusTokenVersionAndOperationId(Some(1)) shouldEqual (
        StatusToken.Version6,
        Some(1)
      )
    }
  }
}
