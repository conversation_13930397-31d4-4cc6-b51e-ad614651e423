package com.agoda.bapi.creation.proxy

import com.agoda.bapi.agent.common.schema.BookingActionMessageTopic
import com.agoda.bapi.common.database.AGDBStub
import com.agoda.bapi.common.db.execution.context.BFDBExecutionContext
import com.agoda.bapi.common.exception.DbException
import com.agoda.bapi.common.model.BFDBExecutionContext
import com.agoda.bapi.common.model.booking.local.{BookingActionWorkflowPhase, BookingActionWorkflowState, BookingWorkflowAction}
import com.agoda.bapi.common.proxy.{DBConnectionGroup, DependencyNames, ResultSetStub}
import com.agoda.bapi.creation.WithMetricsCapture
import com.agoda.bapi.creation.model.db.BookingActionIdentifier
import com.agoda.bapi.creation.service.BookingActionMessage
import com.typesafe.config.Config
import mocks.FlightModelMock
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers.{any, anyInt, eq => eqTo}
import org.mockito.Mockito._
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{AppendedClues, OptionValues}
import org.scalatestplus.mockito.MockitoSugar

import java.sql.{CallableStatement, Connection, ResultSet, Timestamp}
import java.util.UUID

class WorkflowDbProxySpec
    extends AsyncWordSpec
    with Matchers
    with OptionValues
    with AppendedClues
    with FlightModelMock {

  "getMasterBookingActionByItineraryId" should {
    val timestamp = new Timestamp(120, 0, 1, 0, 0, 0, 0)
    val datetime  = Some(new DateTime(2020, 1, 1, 0, 0, 0, 0))

    val expected = defaultBookingAction
      .copy(actionId = 1)
      .copy(itineraryId = defaultBookingAction.itineraryId)
      .copy(bookingType = Some(1))
      .copy(bookingId = Some(0))
      .copy(memberId = defaultBookingAction.memberId)
      .copy(actionTypeId = defaultBookingAction.actionTypeId)
      .copy(correlationId = defaultBookingAction.correlationId)
      .copy(requestId = defaultBookingAction.requestId)
      .copy(workflowId = defaultBookingAction.workflowId)
      .copy(workflowStateId = defaultBookingAction.workflowStateId)
      .copy(productTypeId = Some(1))
      .copy(operationId = Some(15))
      .copy(stateSchemaVersion = defaultBookingAction.stateSchemaVersion)
      .copy(state = defaultBookingAction.state)
      .copy(storefrontId = Some(0))
      .copy(languageId = Some(0))
      .copy(workflowPhaseId = Some(19))
      .copy(whiteLabelId = Some(20))
      .copy(recStatus = Some(0))
      .copy(recCreatedWhen = datetime)
      .copy(recModifiedWhen = datetime)

    "call DB and get master booking action by itinerary id" in {
      val fixture = new Fixture {
        val queryName        = "bcre_get_master_booking_action_by_itinerary_id_v3"
        val sqlQuery: String = s"EXEC dbo.$queryName @itinerary_id = ?"
        val resultSetStub    = mock[ResultSet]
      }

      import fixture._
      initMockedBehaviour()
      when(resultSetStub.next()).thenReturn(true, false)
      when(resultSetStub.wasNull()).thenReturn(false)

      when(resultSetStub.getLong("action_id")).thenReturn(expected.actionId)
      when(resultSetStub.getLong("itinerary_id")).thenReturn(expected.itineraryId)
      when(resultSetStub.getInt("booking_type")).thenReturn(expected.bookingType.get)
      when(resultSetStub.getLong("booking_id")).thenReturn(expected.bookingId.get)
      when(resultSetStub.getInt("member_id")).thenReturn(expected.memberId)
      when(resultSetStub.getInt("action_type_id")).thenReturn(expected.actionTypeId)
      when(resultSetStub.getString("correlationId")).thenReturn(expected.correlationId)
      when(resultSetStub.getString("requestId")).thenReturn(expected.requestId)
      when(resultSetStub.getInt("workflow_id")).thenReturn(expected.workflowId)
      when(resultSetStub.getInt("workflow_state_id")).thenReturn(expected.workflowStateId)
      when(resultSetStub.getInt("product_type_id")).thenReturn(expected.productTypeId.get)
      when(resultSetStub.getInt("state_schema_version")).thenReturn(expected.stateSchemaVersion)
      when(resultSetStub.getLong("operation_id")).thenReturn(expected.operationId.get)
      when(resultSetStub.getString("state")).thenReturn(expected.state)
      when(resultSetStub.getInt("storefront_id")).thenReturn(expected.storefrontId.get)
      when(resultSetStub.getInt("language_id")).thenReturn(expected.languageId.get)
      when(resultSetStub.getInt("workflow_phase_id")).thenReturn(expected.workflowPhaseId.get)
      when(resultSetStub.getInt("whitelabel_id")).thenReturn(expected.whiteLabelId.get)
      when(resultSetStub.getInt("rec_status")).thenReturn(expected.recStatus.get)
      when(resultSetStub.getTimestamp("rec_created_when")).thenReturn(timestamp)
      when(resultSetStub.getTimestamp("rec_modified_when")).thenReturn(timestamp)
      dbProxy.getMasterBookingActionByItineraryId(expected.itineraryId).map { results =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, expected.itineraryId)
        results shouldBe Some(expected)
      }
    }

    "call DB and get no master booking action by itinerary id" in {
      val fixture = new Fixture {
        val queryName        = "bcre_get_master_booking_action_by_itinerary_id_v3"
        val sqlQuery: String = s"EXEC dbo.$queryName @itinerary_id = ?"
        val resultSetStub    = mock[ResultSet]
      }

      import fixture._
      initMockedBehaviour()
      when(resultSetStub.next()).thenReturn(false)
      dbProxy.getMasterBookingActionByItineraryId(0).map { results =>
        verifyInteractionsWithUnderlyingDb()
        results shouldBe None
      }
    }
  }

  "getBookingAction" should {
    "call DB and no booking action" in {
      val fixture = new Fixture {
        override val queryName: String        = "bcre_get_booking_action_v2"
        override val sqlQuery: String         = s"EXEC dbo.$queryName @actionId = ?"
        override val resultSetStub: ResultSet = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()
      when(resultSetStub.next()).thenReturn(false)

      dbProxy.getBookingAction(1).map { result =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, 1L)
        result shouldBe None
      }
    }

    "call DB and get booking action" in {
      val timestamp = new Timestamp(120, 0, 1, 0, 0, 0, 0)
      val datetime  = Some(new DateTime(2020, 1, 1, 0, 0, 0, 0))

      val expected = defaultBookingAction
        .copy(actionId = 1)
        .copy(itineraryId = defaultBookingAction.itineraryId)
        .copy(bookingType = Some(1))
        .copy(bookingId = Some(1234))
        .copy(memberId = defaultBookingAction.memberId)
        .copy(actionTypeId = defaultBookingAction.actionTypeId)
        .copy(correlationId = defaultBookingAction.correlationId)
        .copy(requestId = defaultBookingAction.requestId)
        .copy(workflowId = defaultBookingAction.workflowId)
        .copy(workflowStateId = defaultBookingAction.workflowStateId)
        .copy(productTypeId = Some(1))
        .copy(operationId = Some(15))
        .copy(stateSchemaVersion = defaultBookingAction.stateSchemaVersion)
        .copy(state = defaultBookingAction.state)
        .copy(storefrontId = Some(0))
        .copy(languageId = Some(0))
        .copy(workflowPhaseId = Some(19))
        .copy(whiteLabelId = Some(20))
        .copy(recStatus = Some(0))
        .copy(recCreatedWhen = datetime)
        .copy(recModifiedWhen = datetime)

      val fixture = new Fixture {
        override val queryName: String        = "bcre_get_booking_action_v2"
        override val sqlQuery: String         = s"EXEC dbo.$queryName @actionId = ?"
        override val resultSetStub: ResultSet = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()
      when(resultSetStub.next()).thenReturn(true, false)
      when(resultSetStub.wasNull()).thenReturn(false)

      when(resultSetStub.getLong("action_id")).thenReturn(expected.actionId)
      when(resultSetStub.getLong("itinerary_id")).thenReturn(expected.itineraryId)
      when(resultSetStub.getInt("booking_type")).thenReturn(expected.bookingType.get)
      when(resultSetStub.getLong("booking_id")).thenReturn(expected.bookingId.get)
      when(resultSetStub.getInt("member_id")).thenReturn(expected.memberId)
      when(resultSetStub.getInt("action_type_id")).thenReturn(expected.actionTypeId)
      when(resultSetStub.getString("correlationId")).thenReturn(expected.correlationId)
      when(resultSetStub.getString("requestId")).thenReturn(expected.requestId)
      when(resultSetStub.getInt("workflow_id")).thenReturn(expected.workflowId)
      when(resultSetStub.getInt("workflow_state_id")).thenReturn(expected.workflowStateId)
      when(resultSetStub.getInt("product_type_id")).thenReturn(expected.productTypeId.get)
      when(resultSetStub.getInt("state_schema_version")).thenReturn(expected.stateSchemaVersion)
      when(resultSetStub.getLong("operation_id")).thenReturn(expected.operationId.get)
      when(resultSetStub.getString("state")).thenReturn(expected.state)
      when(resultSetStub.getInt("storefront_id")).thenReturn(expected.storefrontId.get)
      when(resultSetStub.getInt("language_id")).thenReturn(expected.languageId.get)
      when(resultSetStub.getInt("workflow_phase_id")).thenReturn(expected.workflowPhaseId.get)
      when(resultSetStub.getInt("whitelabel_id")).thenReturn(expected.whiteLabelId.get)
      when(resultSetStub.getInt("rec_status")).thenReturn(expected.recStatus.get)
      when(resultSetStub.getTimestamp("rec_created_when")).thenReturn(timestamp)
      when(resultSetStub.getTimestamp("rec_modified_when")).thenReturn(timestamp)

      dbProxy.getBookingAction(expected.actionId).map { result =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, expected.actionId)
        result shouldBe Some(expected)
      }
    }
  }

  "getBookingActionByWorkflowId" should {
    "call DB and no booking action by workflow id" in {
      val fixture = new Fixture {
        override val queryName                = "bcre_get_booking_action_by_workflow_id_v2"
        override val sqlQuery                 = s"EXEC dbo.$queryName @itineraryId = ?, @workflowId = ?"
        override val resultSetStub: ResultSet = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      when(resultSetStub.next()).thenReturn(false)
      dbProxy.getBookingActionByWorkflowId(1L, 1).map { result =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, 1L)
        verify(preparedStatementMock).setObject(2, 1)
        result shouldBe None
      }
    }

    "call DB and get booking action by workflow Id" in {
      val fixture = new Fixture {
        override val queryName                = "bcre_get_booking_action_by_workflow_id_v2"
        override val sqlQuery                 = s"EXEC dbo.$queryName @itineraryId = ?, @workflowId = ?"
        override val resultSetStub: ResultSet = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      val timestamp = new Timestamp(120, 0, 1, 0, 0, 0, 0)
      val datetime  = Some(new DateTime(2020, 1, 1, 0, 0, 0, 0))

      val expected = defaultBookingAction
        .copy(actionId = 1)
        .copy(itineraryId = defaultBookingAction.itineraryId)
        .copy(bookingType = Some(1))
        .copy(bookingId = Some(1234))
        .copy(memberId = defaultBookingAction.memberId)
        .copy(actionTypeId = defaultBookingAction.actionTypeId)
        .copy(correlationId = defaultBookingAction.correlationId)
        .copy(requestId = defaultBookingAction.requestId)
        .copy(workflowId = defaultBookingAction.workflowId)
        .copy(workflowStateId = defaultBookingAction.workflowStateId)
        .copy(productTypeId = Some(1))
        .copy(operationId = Some(15))
        .copy(stateSchemaVersion = defaultBookingAction.stateSchemaVersion)
        .copy(state = defaultBookingAction.state)
        .copy(storefrontId = Some(0))
        .copy(languageId = Some(0))
        .copy(workflowPhaseId = Some(19))
        .copy(whiteLabelId = Some(20))
        .copy(recStatus = Some(0))
        .copy(recCreatedWhen = datetime)
        .copy(recModifiedWhen = datetime)

      when(resultSetStub.next()).thenReturn(true, false)
      when(resultSetStub.wasNull()).thenReturn(false)

      when(resultSetStub.getLong("action_id")).thenReturn(expected.actionId)
      when(resultSetStub.getLong("itinerary_id")).thenReturn(expected.itineraryId)
      when(resultSetStub.getInt("booking_type")).thenReturn(expected.bookingType.get)
      when(resultSetStub.getLong("booking_id")).thenReturn(expected.bookingId.get)
      when(resultSetStub.getInt("member_id")).thenReturn(expected.memberId)
      when(resultSetStub.getInt("action_type_id")).thenReturn(expected.actionTypeId)
      when(resultSetStub.getString("correlationId")).thenReturn(expected.correlationId)
      when(resultSetStub.getString("requestId")).thenReturn(expected.requestId)
      when(resultSetStub.getInt("workflow_id")).thenReturn(expected.workflowId)
      when(resultSetStub.getInt("workflow_state_id")).thenReturn(expected.workflowStateId)
      when(resultSetStub.getInt("product_type_id")).thenReturn(expected.productTypeId.get)
      when(resultSetStub.getLong("operation_id")).thenReturn(expected.operationId.get)
      when(resultSetStub.getInt("state_schema_version")).thenReturn(expected.stateSchemaVersion)
      when(resultSetStub.getString("state")).thenReturn(expected.state)
      when(resultSetStub.getInt("storefront_id")).thenReturn(expected.storefrontId.get)
      when(resultSetStub.getInt("language_id")).thenReturn(expected.languageId.get)
      when(resultSetStub.getInt("workflow_phase_id")).thenReturn(expected.workflowPhaseId.get)
      when(resultSetStub.getInt("whitelabel_id")).thenReturn(expected.whiteLabelId.get)
      when(resultSetStub.getInt("rec_status")).thenReturn(expected.recStatus.get)
      when(resultSetStub.getTimestamp("rec_created_when")).thenReturn(timestamp)
      when(resultSetStub.getTimestamp("rec_modified_when")).thenReturn(timestamp)

      dbProxy.getBookingActionByWorkflowId(expected.itineraryId, expected.workflowId).map { result =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, expected.itineraryId)
        verify(preparedStatementMock).setObject(2, expected.workflowId)
        result shouldBe Some(expected)
      }
    }
  }

  "insertBookingAction" should {
    val recCreatedWhen  = DateTime.parse("2004-08-15T16:23:42.001")
    val recModifiedWhen = DateTime.parse("2004-08-15T16:23:42.001")
    val bookingAction = BookingWorkflowAction(
      actionId = 1L,
      itineraryId = 2L,
      bookingType = Some(3),
      bookingId = Some(4L),
      memberId = 5,
      actionTypeId = 6,
      correlationId = "correlationId",
      requestId = "requestId",
      workflowId = 7,
      workflowStateId = 8,
      productTypeId = Some(17),
      operationId = Some(15),
      stateSchemaVersion = 9,
      state = "state",
      storefrontId = Some(10),
      languageId = Some(11),
      workflowPhaseId = Some(19),
      whiteLabelId = Some(20),
      recStatus = Some(12),
      recCreatedWhen = Some(recCreatedWhen),
      recModifiedWhen = Some(recModifiedWhen)
    )

    "call DB and return ID" in {
      val fixture = new Fixture {
        override val queryName                = "bcre_booking_action_insert_v2"
        override val sqlQuery                 = s"{ call dbo.$queryName (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) }"
        override val resultSetStub: ResultSet = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      when(resultSetStub.isBeforeFirst).thenReturn(true)
      when(resultSetStub.next()).thenReturn(true, false)

      when(resultSetStub.getLong("action_id")).thenReturn(1L)
      when(resultSetStub.getLong("itinerary_id")).thenReturn(2L)
      when(resultSetStub.getInt("booking_type")).thenReturn(3)
      when(resultSetStub.getLong("booking_id")).thenReturn(4L)
      when(resultSetStub.getInt("member_id")).thenReturn(5)
      when(resultSetStub.getInt("action_type_id")).thenReturn(6)
      when(resultSetStub.getString("correlationId")).thenReturn("correlationId")
      when(resultSetStub.getString("requestId")).thenReturn("requestId")
      when(resultSetStub.getInt("workflow_id")).thenReturn(7)
      when(resultSetStub.getInt("workflow_state_id")).thenReturn(8)
      when(resultSetStub.getInt("product_type_id")).thenReturn(17)
      when(resultSetStub.getLong("operation_id")).thenReturn(15)
      when(resultSetStub.getInt("state_schema_version")).thenReturn(9)
      when(resultSetStub.getString("state")).thenReturn("state")
      when(resultSetStub.getInt("storefront_id")).thenReturn(10)
      when(resultSetStub.getInt("language_id")).thenReturn(11)
      when(resultSetStub.getInt("workflow_phase_id")).thenReturn(19)
      when(resultSetStub.getInt("whitelabel_id")).thenReturn(20)
      when(resultSetStub.getInt("rec_status")).thenReturn(12)
      when(resultSetStub.getTimestamp("rec_created_when")).thenReturn(new Timestamp(recCreatedWhen.getMillis))
      when(resultSetStub.getTimestamp("rec_modified_when")).thenReturn(new Timestamp(recModifiedWhen.getMillis))

      dbProxy.insertBookingAction(bookingAction).map { result =>
        verifyInteractionsWithUnderlyingDb(asCallableStatement = true)
        verify(preparedStatementMock)
          .setLong(eqTo(1), eqTo(bookingAction.actionId)) withClue "as setLong(1) for actionId"
        verify(preparedStatementMock)
          .setLong(eqTo(2), eqTo(bookingAction.itineraryId)) withClue "as setLong(2) for itineraryId"
        verify(preparedStatementMock)
          .setInt(eqTo(3), eqTo(bookingAction.bookingType.get)) withClue "as setInt(3) for bookingType"
        verify(preparedStatementMock)
          .setLong(eqTo(4), eqTo(bookingAction.bookingId.get)) withClue "as setLong(4) for bookingId"
        verify(preparedStatementMock)
          .setInt(eqTo(5), eqTo(bookingAction.memberId)) withClue "as setLong(5) for memberId"
        verify(preparedStatementMock)
          .setInt(eqTo(6), eqTo(bookingAction.actionTypeId)) withClue "as setLong(6) for actionTypeId"
        verify(preparedStatementMock)
          .setString(eqTo(7), eqTo(bookingAction.correlationId)) withClue "as setString(7) for correlationId"
        verify(preparedStatementMock)
          .setString(eqTo(8), eqTo(bookingAction.requestId)) withClue "as setString(8) for requestId"
        verify(preparedStatementMock)
          .setInt(eqTo(9), eqTo(bookingAction.workflowId)) withClue "as setInt(9) for workflowId"
        verify(preparedStatementMock)
          .setInt(eqTo(10), eqTo(bookingAction.workflowStateId)) withClue "as setInt(10) for workflowStateId"
        verify(preparedStatementMock)
          .setInt(eqTo(11), eqTo(bookingAction.stateSchemaVersion)) withClue "as setInt(11) for stateSchemaVersion"
        verify(preparedStatementMock)
          .setString(eqTo(12), eqTo(bookingAction.state)) withClue "as setString(12) for state"
        verify(preparedStatementMock)
          .setInt(eqTo(13), eqTo(bookingAction.storefrontId.get)) withClue "as setInt(13) for storefrontId"
        verify(preparedStatementMock)
          .setInt(eqTo(14), eqTo(bookingAction.languageId.get)) withClue "as setInt(14) for languageId"
        verify(preparedStatementMock)
          .setInt(eqTo(15), eqTo(bookingAction.recStatus.get)) withClue "as setInt(15) for recStatus"
        verify(preparedStatementMock).setTimestamp(
          eqTo(16),
          eqTo(new Timestamp(recCreatedWhen.getMillis))
        ) withClue "as setTimestamp(16) for recCreatedWhen"

        verify(preparedStatementMock)
          .setInt(eqTo(17), eqTo(bookingAction.productTypeId.get)) withClue "as setInt(17) for productTypeId"

        verify(preparedStatementMock)
          .setLong(eqTo(18), eqTo(bookingAction.operationId.get)) withClue "as setLong(18) for operationId"
        verify(preparedStatementMock)
          .setInt(eqTo(19), eqTo(bookingAction.workflowPhaseId.get)) withClue "as setInt(19) for workflowPhaseId"
        verify(preparedStatementMock)
          .setInt(eqTo(20), eqTo(bookingAction.whiteLabelId.get)) withClue "as setInt(20) for whiteLabelId"

        verify(preparedStatementMock).executeQuery() withClue "as executeQuery"

        result shouldBe bookingAction
      }
    }

    "call DB with None for optional fields and not return" in {
      val fixture = new Fixture {
        override val queryName                = "bcre_booking_action_insert_v2"
        override val sqlQuery                 = s"{ call dbo.$queryName (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) }"
        override val resultSetStub: ResultSet = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      val bookingActionWithNone = bookingAction.copy(
        bookingType = None,
        bookingId = None,
        storefrontId = None,
        languageId = None,
        recStatus = None,
        recCreatedWhen = None,
        productTypeId = None,
        operationId = None,
        workflowPhaseId = None,
        whiteLabelId = None
      )

      when(resultSetStub.isBeforeFirst).thenReturn(true)
      when(resultSetStub.next()).thenReturn(false)

      recoverToSucceededIf[DbException](dbProxy.insertBookingAction(bookingActionWithNone))
    }
  }

  "getBookingActionMessage" should {

    "call DB and no booking action" in {
      val fixture = new Fixture {
        override val queryName                = "bcre_get_booking_action_message_v2"
        override val sqlQuery                 = s"EXEC dbo.$queryName @actionId = ?"
        override val resultSetStub: ResultSet = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      when(resultSetStub.next()).thenReturn(false)
      dbProxy.getBookingActionMessage(1).map { result =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, 1L)
        result shouldBe empty
      }
    }

    "call DB and get booking action message" in {
      val fixture = new Fixture {
        override val queryName                = "bcre_get_booking_action_message_v2"
        override val sqlQuery                 = s"EXEC dbo.$queryName @actionId = ?"
        override val resultSetStub: ResultSet = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      when(resultSetStub.next()).thenReturn(true, false)
      when(resultSetStub.wasNull()).thenReturn(true)

      when(resultSetStub.getLong("action_id")).thenReturn(defaultBookingActionMessage.actionId)
      when(resultSetStub.getString("content")).thenReturn(defaultBookingActionMessage.content)
      when(resultSetStub.getString("rec_created_by")).thenReturn(defaultBookingActionMessage.recCreatedBy)
      when(resultSetStub.getTimestamp("rec_created_when"))
        .thenReturn(new Timestamp(defaultBookingActionMessage.recCreatedWhen.getMillis))
      dbProxy.getBookingActionMessage(1).map { result =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, 1L)
        result shouldBe Seq(defaultBookingActionMessage)
      }
    }
  }

  "getBookingActionByBookingId" should {
    "call DB and no booking action for booking id" in {
      val fixture = new Fixture {
        override val queryName                = "bcre_get_booking_action_by_booking_id_v2"
        override val sqlQuery                 = s"EXEC dbo.$queryName @bookingId = ?"
        override val resultSetStub: ResultSet = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      when(resultSetStub.next()).thenReturn(false)
      dbProxy.getBookingActionByBookingId(1).map { result =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, 1L)
        result shouldBe None
      }
    }

    "call DB and get booking action map" in {
      val fixture = new Fixture {
        override val queryName                = "bcre_get_booking_action_by_booking_id_v2"
        override val sqlQuery                 = s"EXEC dbo.$queryName @bookingId = ?"
        override val resultSetStub: ResultSet = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      val bookingId = 1
      val timestamp = new Timestamp(120, 0, 1, 0, 0, 0, 0)
      val datetime  = Some(new DateTime(2020, 1, 1, 0, 0, 0, 0))

      val expected = defaultBookingAction
        .copy(actionId = 1)
        .copy(itineraryId = defaultBookingAction.itineraryId)
        .copy(bookingType = Some(1))
        .copy(bookingId = Some(1234))
        .copy(memberId = defaultBookingAction.memberId)
        .copy(actionTypeId = defaultBookingAction.actionTypeId)
        .copy(correlationId = defaultBookingAction.correlationId)
        .copy(requestId = defaultBookingAction.requestId)
        .copy(workflowId = defaultBookingAction.workflowId)
        .copy(workflowStateId = defaultBookingAction.workflowStateId)
        .copy(productTypeId = Some(1))
        .copy(operationId = Some(15))
        .copy(stateSchemaVersion = defaultBookingAction.stateSchemaVersion)
        .copy(state = defaultBookingAction.state)
        .copy(storefrontId = Some(0))
        .copy(languageId = Some(0))
        .copy(workflowPhaseId = Some(19))
        .copy(whiteLabelId = Some(20))
        .copy(recStatus = Some(0))
        .copy(recCreatedWhen = datetime)
        .copy(recModifiedWhen = datetime)

      when(resultSetStub.next()).thenReturn(true, false)
      when(resultSetStub.wasNull()).thenReturn(false)

      when(resultSetStub.getLong("action_id")).thenReturn(expected.actionId)
      when(resultSetStub.getLong("itinerary_id")).thenReturn(expected.itineraryId)
      when(resultSetStub.getInt("booking_type")).thenReturn(expected.bookingType.get)
      when(resultSetStub.getLong("booking_id")).thenReturn(expected.bookingId.get)
      when(resultSetStub.getInt("member_id")).thenReturn(expected.memberId)
      when(resultSetStub.getInt("action_type_id")).thenReturn(expected.actionTypeId)
      when(resultSetStub.getString("correlationId")).thenReturn(expected.correlationId)
      when(resultSetStub.getString("requestId")).thenReturn(expected.requestId)
      when(resultSetStub.getInt("workflow_id")).thenReturn(expected.workflowId)
      when(resultSetStub.getInt("workflow_state_id")).thenReturn(expected.workflowStateId)
      when(resultSetStub.getInt("product_type_id")).thenReturn(expected.productTypeId.get)
      when(resultSetStub.getLong("operation_id")).thenReturn(expected.operationId.get)
      when(resultSetStub.getInt("state_schema_version")).thenReturn(expected.stateSchemaVersion)
      when(resultSetStub.getString("state")).thenReturn(expected.state)
      when(resultSetStub.getInt("storefront_id")).thenReturn(expected.storefrontId.get)
      when(resultSetStub.getInt("language_id")).thenReturn(expected.languageId.get)
      when(resultSetStub.getInt("workflow_phase_id")).thenReturn(expected.workflowPhaseId.get)
      when(resultSetStub.getInt("whitelabel_id")).thenReturn(expected.whiteLabelId.get)
      when(resultSetStub.getInt("rec_status")).thenReturn(expected.recStatus.get)
      when(resultSetStub.getTimestamp("rec_created_when")).thenReturn(timestamp)
      when(resultSetStub.getTimestamp("rec_modified_when"))
        .thenReturn(timestamp)
        .thenReturn(new Timestamp(defaultBookingActionMessage.recCreatedWhen.getMillis))
      dbProxy.getBookingActionByBookingId(bookingId).map { result =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, bookingId.toLong)
        result shouldBe Some(expected)
      }
    }
  }

  "getBookingActionBySupplierBookingId" should {
    val topic             = BookingActionMessageTopic.BAM_Topic_Ticketing.value
    val supplierBookingId = "1"
    val uuid              = "BD811DA2-B3D0-470E-B7CD-F15B8A460EB0"

    "call DB and no booking action" in {
      val fixture = new Fixture {
        val queryName                         = "bcre_get_booking_action_by_supplier_booking_id_v2"
        val sqlQuery                          = s"EXEC dbo.$queryName @supplierBookingId = ?, @topicId = ?, @recCreatedBy = ?"
        override val resultSetStub: ResultSet = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      when(resultSetStub.next()).thenReturn(false)
      dbProxy.getBookingActionBySupplierBookingId(supplierBookingId, topic, uuid).map { result =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, supplierBookingId)
        verify(preparedStatementMock).setObject(2, topic)
        verify(preparedStatementMock).setObject(3, uuid)
        result shouldBe None
      }
    }

    "call DB and get booking action" in {
      val fixture = new Fixture {
        val queryName                         = "bcre_get_booking_action_by_supplier_booking_id_v2"
        val sqlQuery                          = s"EXEC dbo.$queryName @supplierBookingId = ?, @topicId = ?, @recCreatedBy = ?"
        override val resultSetStub: ResultSet = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      val timestamp = new Timestamp(120, 0, 1, 0, 0, 0, 0)
      val datetime  = Some(new DateTime(2020, 1, 1, 0, 0, 0, 0))

      val expected = defaultBookingAction
        .copy(actionId = 1)
        .copy(itineraryId = defaultBookingAction.itineraryId)
        .copy(bookingType = Some(1))
        .copy(bookingId = Some(1234))
        .copy(memberId = defaultBookingAction.memberId)
        .copy(actionTypeId = defaultBookingAction.actionTypeId)
        .copy(correlationId = defaultBookingAction.correlationId)
        .copy(requestId = defaultBookingAction.requestId)
        .copy(workflowId = defaultBookingAction.workflowId)
        .copy(workflowStateId = defaultBookingAction.workflowStateId)
        .copy(productTypeId = Some(1))
        .copy(operationId = Some(15))
        .copy(stateSchemaVersion = defaultBookingAction.stateSchemaVersion)
        .copy(state = defaultBookingAction.state)
        .copy(storefrontId = Some(0))
        .copy(languageId = Some(0))
        .copy(workflowPhaseId = Some(19))
        .copy(whiteLabelId = Some(20))
        .copy(recStatus = Some(0))
        .copy(recCreatedWhen = datetime)
        .copy(recModifiedWhen = datetime)

      when(resultSetStub.next()).thenReturn(true, false)
      when(resultSetStub.wasNull()).thenReturn(false)

      when(resultSetStub.getLong("action_id")).thenReturn(expected.actionId)
      when(resultSetStub.getLong("itinerary_id")).thenReturn(expected.itineraryId)
      when(resultSetStub.getInt("booking_type")).thenReturn(expected.bookingType.get)
      when(resultSetStub.getLong("booking_id")).thenReturn(expected.bookingId.get)
      when(resultSetStub.getInt("member_id")).thenReturn(expected.memberId)
      when(resultSetStub.getInt("action_type_id")).thenReturn(expected.actionTypeId)
      when(resultSetStub.getString("correlationId")).thenReturn(expected.correlationId)
      when(resultSetStub.getString("requestId")).thenReturn(expected.requestId)
      when(resultSetStub.getInt("workflow_id")).thenReturn(expected.workflowId)
      when(resultSetStub.getInt("workflow_state_id")).thenReturn(expected.workflowStateId)
      when(resultSetStub.getInt("product_type_id")).thenReturn(expected.productTypeId.get)
      when(resultSetStub.getLong("operation_id")).thenReturn(expected.operationId.get)
      when(resultSetStub.getInt("state_schema_version")).thenReturn(expected.stateSchemaVersion)
      when(resultSetStub.getString("state")).thenReturn(expected.state)
      when(resultSetStub.getInt("storefront_id")).thenReturn(expected.storefrontId.get)
      when(resultSetStub.getInt("language_id")).thenReturn(expected.languageId.get)
      when(resultSetStub.getInt("workflow_phase_id")).thenReturn(expected.workflowPhaseId.get)
      when(resultSetStub.getInt("whitelabel_id")).thenReturn(expected.whiteLabelId.get)
      when(resultSetStub.getInt("rec_status")).thenReturn(expected.recStatus.get)
      when(resultSetStub.getTimestamp("rec_created_when")).thenReturn(timestamp)
      when(resultSetStub.getTimestamp("rec_modified_when")).thenReturn(timestamp)

      dbProxy.getBookingActionBySupplierBookingId(supplierBookingId, topic, uuid).map { result =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, supplierBookingId)
        verify(preparedStatementMock).setObject(2, topic)
        verify(preparedStatementMock).setObject(3, uuid)
        result shouldBe Some(expected)
      }
    }
  }

  "getBookingActionByItineraryId" should {
    val timestamp = new Timestamp(120, 0, 1, 0, 0, 0, 0)
    val datetime  = Some(new DateTime(2020, 1, 1, 0, 0, 0, 0))

    val expected = defaultBookingAction
      .copy(actionId = 1)
      .copy(itineraryId = defaultBookingAction.itineraryId)
      .copy(bookingType = Some(1))
      .copy(bookingId = Some(1234))
      .copy(memberId = defaultBookingAction.memberId)
      .copy(actionTypeId = defaultBookingAction.actionTypeId)
      .copy(correlationId = defaultBookingAction.correlationId)
      .copy(requestId = defaultBookingAction.requestId)
      .copy(workflowId = defaultBookingAction.workflowId)
      .copy(workflowStateId = defaultBookingAction.workflowStateId)
      .copy(productTypeId = Some(1))
      .copy(operationId = Some(15))
      .copy(stateSchemaVersion = defaultBookingAction.stateSchemaVersion)
      .copy(state = defaultBookingAction.state)
      .copy(storefrontId = Some(0))
      .copy(languageId = Some(0))
      .copy(workflowPhaseId = Some(19))
      .copy(whiteLabelId = Some(20))
      .copy(recStatus = Some(0))
      .copy(recCreatedWhen = datetime)
      .copy(recModifiedWhen = datetime)

    "call DB and get booking action by itinerary id" in {
      val fixture = new Fixture {
        val queryName        = "bcre_get_booking_action_by_itinerary_id_v2"
        val sqlQuery: String = s"EXEC dbo.$queryName @itinerary_id = ?"
        val resultSetStub    = mock[ResultSet]
      }

      import fixture._
      initMockedBehaviour()
      when(resultSetStub.next()).thenReturn(true, false)
      when(resultSetStub.wasNull()).thenReturn(false)

      when(resultSetStub.getLong("action_id")).thenReturn(expected.actionId)
      when(resultSetStub.getLong("itinerary_id")).thenReturn(expected.itineraryId)
      when(resultSetStub.getInt("booking_type")).thenReturn(expected.bookingType.get)
      when(resultSetStub.getLong("booking_id")).thenReturn(expected.bookingId.get)
      when(resultSetStub.getInt("member_id")).thenReturn(expected.memberId)
      when(resultSetStub.getInt("action_type_id")).thenReturn(expected.actionTypeId)
      when(resultSetStub.getString("correlationId")).thenReturn(expected.correlationId)
      when(resultSetStub.getString("requestId")).thenReturn(expected.requestId)
      when(resultSetStub.getInt("workflow_id")).thenReturn(expected.workflowId)
      when(resultSetStub.getInt("workflow_state_id")).thenReturn(expected.workflowStateId)
      when(resultSetStub.getInt("product_type_id")).thenReturn(expected.productTypeId.get)
      when(resultSetStub.getInt("state_schema_version")).thenReturn(expected.stateSchemaVersion)
      when(resultSetStub.getLong("operation_id")).thenReturn(expected.operationId.get)
      when(resultSetStub.getString("state")).thenReturn(expected.state)
      when(resultSetStub.getInt("storefront_id")).thenReturn(expected.storefrontId.get)
      when(resultSetStub.getInt("language_id")).thenReturn(expected.languageId.get)
      when(resultSetStub.getInt("workflow_phase_id")).thenReturn(expected.workflowPhaseId.get)
      when(resultSetStub.getInt("whitelabel_id")).thenReturn(expected.whiteLabelId.get)
      when(resultSetStub.getInt("rec_status")).thenReturn(expected.recStatus.get)
      when(resultSetStub.getTimestamp("rec_created_when")).thenReturn(timestamp)
      when(resultSetStub.getTimestamp("rec_modified_when")).thenReturn(timestamp)
      dbProxy.getBookingActionByItineraryId(expected.itineraryId).map { results =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, expected.itineraryId)
        results shouldBe Seq(expected)
      }
    }

    "call DB and get no master booking action by itinerary id" in {
      val fixture = new Fixture {
        val queryName        = "bcre_get_booking_action_by_itinerary_id_v2"
        val sqlQuery: String = s"EXEC dbo.$queryName @itinerary_id = ?"
        val resultSetStub    = mock[ResultSet]
      }

      import fixture._
      initMockedBehaviour()
      when(resultSetStub.next()).thenReturn(false)
      dbProxy.getBookingActionByItineraryId(0).map { results =>
        verifyInteractionsWithUnderlyingDb()
        results shouldBe Seq.empty
      }
    }
  }

  "insertBookingActionMessageWithConnection" should {
    val actionId    = 5
    val topic       = 1
    val content     = "{ TestKey : TestValue }"
    val createdBy   = UUID.randomUUID().toString
    val createdWhen = DateTime.now
    val message     = BookingActionMessage(None, actionId, topic, content, createdBy, createdWhen)

    val recordCount = 1

    def verifyPrepareStatementMockForInsertBookingActionMessageWithConnection(
        preparedStatementMock: CallableStatement
    ) = {
      verify(preparedStatementMock).setLong(1, message.actionId)
      verify(preparedStatementMock).setInt(2, message.topic)
      verify(preparedStatementMock).setString(3, message.content)
      verify(preparedStatementMock).setString(4, message.recCreatedBy)
      verify(preparedStatementMock).setTimestamp(5, new Timestamp(message.recCreatedWhen.getMillis))
      verify(preparedStatementMock).registerOutParameter(6, java.sql.Types.INTEGER)
      verify(preparedStatementMock).execute()
    }

    "call DB and return ID" in {
      val fixture = new Fixture {
        val queryName        = "bcre_creation_booking_action_message_insert_v1"
        val sqlQuery: String = s"""{ call dbo.$queryName (?,?,?,?,?,?) }"""
        val resultSetStub    = mock[ResultSet]
      }

      import fixture._
      initMockedBehaviour()
      when(preparedStatementMock.getLong(6)).thenReturn(recordCount)
      dbProxy.insertBookingActionMessage(message).map { results =>
        verifyInteractionsWithUnderlyingDb(true)
        verifyPrepareStatementMockForInsertBookingActionMessageWithConnection(preparedStatementMock)
        results shouldBe recordCount
      }
    }
  }

  "insertBookingActionWithConnection" should {
    val recCreatedWhen  = DateTime.parse("2004-08-15T16:23:42.001")
    val recModifiedWhen = DateTime.parse("2004-08-15T16:23:42.001")
    val bookingAction = BookingWorkflowAction(
      actionId = 1L,
      itineraryId = 2L,
      bookingType = Some(3),
      bookingId = Some(4L),
      memberId = 5,
      actionTypeId = 6,
      correlationId = "correlationId",
      requestId = "requestId",
      workflowId = 7,
      workflowStateId = 8,
      productTypeId = Some(17),
      operationId = Some(15),
      stateSchemaVersion = 9,
      state = "state",
      storefrontId = Some(10),
      languageId = Some(11),
      workflowPhaseId = Some(19),
      whiteLabelId = Some(20),
      recStatus = Some(12),
      recCreatedWhen = Some(recCreatedWhen),
      recModifiedWhen = Some(recModifiedWhen)
    )

    val expectedData: Array[Map[String, Any]] = Array(
      Map(
        "action_id"            -> bookingAction.actionId,
        "itinerary_id"         -> bookingAction.itineraryId,
        "booking_type"         -> bookingAction.bookingType.value,
        "booking_id"           -> bookingAction.bookingId.value,
        "member_id"            -> bookingAction.memberId,
        "action_type_id"       -> bookingAction.actionTypeId,
        "correlationId"        -> bookingAction.correlationId,
        "requestId"            -> bookingAction.requestId,
        "workflow_id"          -> bookingAction.workflowId,
        "workflow_state_id"    -> bookingAction.workflowStateId,
        "product_type_id"      -> bookingAction.productTypeId.value,
        "state_schema_version" -> bookingAction.stateSchemaVersion,
        "state"                -> bookingAction.state,
        "storefront_id"        -> bookingAction.storefrontId.value,
        "language_id"          -> bookingAction.languageId.value,
        "rec_status"           -> bookingAction.recStatus.value,
        "rec_created_when"     -> new Timestamp(bookingAction.recCreatedWhen.value.getMillis),
        "rec_modified_when"    -> new Timestamp(bookingAction.recModifiedWhen.value.getMillis),
        "operation_id"         -> bookingAction.operationId.value,
        "workflow_phase_id"    -> bookingAction.workflowPhaseId.value,
        "whitelabel_id"        -> bookingAction.whiteLabelId.value
      )
    )

    def verifyPrepareStatementMockForInsertBookingActionWithConnection(preparedStatementMock: CallableStatement) = {
      verify(preparedStatementMock).setLong(1, bookingAction.actionId)
      verify(preparedStatementMock).setLong(2, bookingAction.itineraryId)
      verify(preparedStatementMock).setInt(3, bookingAction.bookingType.value)
      verify(preparedStatementMock).setLong(4, bookingAction.bookingId.value)
      verify(preparedStatementMock).setInt(5, bookingAction.memberId)
      verify(preparedStatementMock).setInt(6, bookingAction.actionTypeId)
      verify(preparedStatementMock).setString(7, bookingAction.correlationId)
      verify(preparedStatementMock).setString(8, bookingAction.requestId)
      verify(preparedStatementMock).setInt(9, bookingAction.workflowId)
      verify(preparedStatementMock).setInt(10, bookingAction.workflowStateId)
      verify(preparedStatementMock).setInt(11, bookingAction.stateSchemaVersion)
      verify(preparedStatementMock).setString(12, bookingAction.state)
      verify(preparedStatementMock).setInt(13, bookingAction.storefrontId.value)
      verify(preparedStatementMock).setInt(14, bookingAction.languageId.value)
      verify(preparedStatementMock).setInt(15, bookingAction.recStatus.value)
      verify(preparedStatementMock).setTimestamp(16, new Timestamp(bookingAction.recCreatedWhen.value.getMillis))
      verify(preparedStatementMock).setInt(17, bookingAction.productTypeId.value)
      verify(preparedStatementMock).setLong(18, bookingAction.operationId.value)
      verify(preparedStatementMock).setInt(19, bookingAction.workflowPhaseId.value)
      verify(preparedStatementMock).setInt(20, bookingAction.whiteLabelId.value)
      verify(preparedStatementMock).executeQuery()
    }

    "call DB and return ID" in {
      val fixture = new Fixture {
        val queryName        = "bcre_booking_action_insert_v2"
        val sqlQuery: String = s"""{ call dbo.$queryName (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) }"""
        val resultSetStub    = new ResultSetStub(expectedData)
      }

      import fixture._
      initMockedBehaviour()
      dbProxy.insertBookingAction(bookingAction).map { results =>
        verifyInteractionsWithUnderlyingDb(true)
        verifyPrepareStatementMockForInsertBookingActionWithConnection(preparedStatementMock)
        results shouldBe bookingAction
      }
    }
  }

  "getBaseBookingActionBySupplierBookingId" should {
    val topic             = BookingActionMessageTopic.BAM_Topic_ActivityProvisioningResult.value
    val supplierBookingId = "1"
    val uuid              = "BD811DA2-B3D0-470E-B7CD-F15B8A460EB0"

    "call DB and no booking action" in {
      val fixture = new Fixture {
        val queryName                         = "bcre_get_base_booking_action_by_supplier_booking_id_v2"
        val sqlQuery                          = s"EXEC dbo.$queryName @supplierBookingId = ?, @topicId = ?, @recCreatedBy = ?"
        override val resultSetStub: ResultSet = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      when(resultSetStub.next()).thenReturn(false)
      dbProxy.getBaseBookingActionBySupplierBookingId(supplierBookingId, topic, uuid).map { result =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, supplierBookingId)
        verify(preparedStatementMock).setObject(2, topic)
        verify(preparedStatementMock).setObject(3, uuid)
        result shouldBe None
      }
    }

    "call DB and get booking action" in {
      val fixture = new Fixture {
        val queryName                         = "bcre_get_base_booking_action_by_supplier_booking_id_v2"
        val sqlQuery                          = s"EXEC dbo.$queryName @supplierBookingId = ?, @topicId = ?, @recCreatedBy = ?"
        override val resultSetStub: ResultSet = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      val timestamp = new Timestamp(120, 0, 1, 0, 0, 0, 0)
      val datetime  = Some(new DateTime(2020, 1, 1, 0, 0, 0, 0))

      val expected = defaultBookingAction
        .copy(actionId = 1)
        .copy(itineraryId = defaultBookingAction.itineraryId)
        .copy(bookingType = Some(1))
        .copy(bookingId = Some(1234))
        .copy(memberId = defaultBookingAction.memberId)
        .copy(actionTypeId = defaultBookingAction.actionTypeId)
        .copy(correlationId = defaultBookingAction.correlationId)
        .copy(requestId = defaultBookingAction.requestId)
        .copy(workflowId = defaultBookingAction.workflowId)
        .copy(workflowStateId = defaultBookingAction.workflowStateId)
        .copy(productTypeId = Some(1))
        .copy(operationId = Some(15))
        .copy(stateSchemaVersion = defaultBookingAction.stateSchemaVersion)
        .copy(state = defaultBookingAction.state)
        .copy(storefrontId = Some(0))
        .copy(languageId = Some(0))
        .copy(workflowPhaseId = Some(19))
        .copy(whiteLabelId = Some(20))
        .copy(recStatus = Some(0))
        .copy(recCreatedWhen = datetime)
        .copy(recModifiedWhen = datetime)

      when(resultSetStub.next()).thenReturn(true, false)
      when(resultSetStub.wasNull()).thenReturn(false)

      when(resultSetStub.getLong("action_id")).thenReturn(expected.actionId)
      when(resultSetStub.getLong("itinerary_id")).thenReturn(expected.itineraryId)
      when(resultSetStub.getInt("booking_type")).thenReturn(expected.bookingType.get)
      when(resultSetStub.getLong("booking_id")).thenReturn(expected.bookingId.get)
      when(resultSetStub.getInt("member_id")).thenReturn(expected.memberId)
      when(resultSetStub.getInt("action_type_id")).thenReturn(expected.actionTypeId)
      when(resultSetStub.getString("correlationId")).thenReturn(expected.correlationId)
      when(resultSetStub.getString("requestId")).thenReturn(expected.requestId)
      when(resultSetStub.getInt("workflow_id")).thenReturn(expected.workflowId)
      when(resultSetStub.getInt("workflow_state_id")).thenReturn(expected.workflowStateId)
      when(resultSetStub.getInt("product_type_id")).thenReturn(expected.productTypeId.get)
      when(resultSetStub.getLong("operation_id")).thenReturn(expected.operationId.get)
      when(resultSetStub.getInt("state_schema_version")).thenReturn(expected.stateSchemaVersion)
      when(resultSetStub.getString("state")).thenReturn(expected.state)
      when(resultSetStub.getInt("storefront_id")).thenReturn(expected.storefrontId.get)
      when(resultSetStub.getInt("language_id")).thenReturn(expected.languageId.get)
      when(resultSetStub.getInt("workflow_phase_id")).thenReturn(expected.workflowPhaseId.get)
      when(resultSetStub.getInt("whitelabel_id")).thenReturn(expected.whiteLabelId.get)
      when(resultSetStub.getInt("rec_status")).thenReturn(expected.recStatus.get)
      when(resultSetStub.getTimestamp("rec_created_when")).thenReturn(timestamp)
      when(resultSetStub.getTimestamp("rec_modified_when")).thenReturn(timestamp)

      dbProxy.getBaseBookingActionBySupplierBookingId(supplierBookingId, topic, uuid).map { result =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, supplierBookingId)
        verify(preparedStatementMock).setObject(2, topic)
        verify(preparedStatementMock).setObject(3, uuid)
        result shouldBe Some(expected)
      }
    }

    "call DB and update booking action message" in {
      val messageId = 1L
      val content   = "{}"
      val fixture = new Fixture {
        override val queryName = "bcre_update_booking_action_message_v1"
        override val sqlQuery  = s"EXEC dbo.$queryName @messageId = ?, @content = ?"
        val expectedData: Array[Map[String, Any]] = Array(
          Map("1" -> 1)
        )
        override val resultSetStub: ResultSet = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.updateBookingActionMessage(messageId, content).map { results =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, messageId)
        verify(preparedStatementMock).setObject(2, content)
        results shouldBe 1
      }
    }

    "update booking action message affected row as 0 when there's no result next" in {
      val messageId = 1L
      val content   = "{}"
      val fixture = new Fixture {
        override val queryName                = "bcre_update_booking_action_message_v1"
        override val sqlQuery                 = s"EXEC dbo.$queryName @messageId = ?, @content = ?"
        override val resultSetStub: ResultSet = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()
      when(resultSetStub.next()).thenReturn(false)

      dbProxy.updateBookingActionMessage(messageId, content).map { results =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, messageId)
        verify(preparedStatementMock).setObject(2, content)
        results shouldBe 0
      }
    }
  }

  "getBookingActionIdentifierByBookingIdAndTopicId" should {
    "call DB and get bookingAction" in {
      val expectedBookingActionIdentifier = BookingActionIdentifier(
        actionId = 101L,
        itineraryId = 102L,
        bookingId = 103L
      )

      val expectedResultSetStub: Array[Map[String, Any]] = Array(
        Map(
          "action_id"    -> expectedBookingActionIdentifier.actionId,
          "itinerary_id" -> expectedBookingActionIdentifier.itineraryId,
          "booking_id"   -> expectedBookingActionIdentifier.bookingId
        )
      )

      val fixture = new Fixture {
        val queryName                         = "bcre_get_booking_action_identifier_by_booking_id_and_topic_id_v1"
        val sqlQuery                          = s"EXEC dbo.$queryName @bookingId = ?, @topicId = ?, @recCreatedBy = ?"
        override val resultSetStub: ResultSet = new ResultSetStub(expectedResultSetStub)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy
        .getBookingActionIdentifierByBookingIdAndTopicId(
          bookingId = 1L,
          topicId = BookingActionMessageTopic.BAM_Topic_Ticketing.value,
          recCreatedBy = "testUser"
        )
        .map { result =>
          verifyInteractionsWithUnderlyingDb()
          verify(preparedStatementMock).setObject(1, 1L)
          verify(preparedStatementMock).setObject(2, BookingActionMessageTopic.BAM_Topic_Ticketing.value)
          verify(preparedStatementMock).setObject(3, "testUser")

          result shouldBe Some(expectedBookingActionIdentifier)
        }
    }

    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName                         = "bcre_get_booking_action_identifier_by_booking_id_and_topic_id_v1"
        val sqlQuery                          = s"EXEC dbo.$queryName @bookingId = ?, @topicId = ?, @recCreatedBy = ?"
        override val resultSetStub: ResultSet = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[RuntimeException] {
        dbProxy.getBookingActionIdentifierByBookingIdAndTopicId(
          bookingId = 1L,
          topicId = BookingActionMessageTopic.BAM_Topic_Ticketing.value,
          recCreatedBy = "testUser"
        )
      }
    }
  }

  trait Fixture extends MockitoSugar {
    val connectionMock        = mock[Connection]
    val preparedStatementMock = mock[CallableStatement]
    val agDbStub              = spy(new AGDBStub(connectionMock))
    val config                = mock[Config]

    val dbProxy = new WorkflowDbProxyImpl(
      agDbStub,
      new BFDBExecutionContext(BFDBExecutionContext.global),
      config
    ) with WithMetricsCapture

    def queryName: String

    def sqlQuery: String

    def resultSetStub: ResultSet

    def initMockedBehaviour(): Unit = {
      doNothing().when(preparedStatementMock).setQueryTimeout(anyInt)
      when(connectionMock.prepareStatement(any())).thenReturn(preparedStatementMock)
      when(connectionMock.prepareCall(any())).thenReturn(preparedStatementMock)
      when(preparedStatementMock.executeQuery).thenReturn(resultSetStub)
      when(preparedStatementMock.executeUpdate).thenReturn(0)
      when(preparedStatementMock.execute()).thenReturn(true)
    }

    def verifyInteractionsWithUnderlyingDb(asCallableStatement: Boolean = false): Unit = {
      verify(agDbStub).withConnectionGroup(eqTo(DBConnectionGroup.BFDB_BCRE.value))(any())
      if (asCallableStatement)
        verify(connectionMock).prepareCall(sqlQuery)
      else
        verify(connectionMock).prepareStatement(sqlQuery)
      verifyNoMoreInteractions(connectionMock)

      dbProxy.sentMetrics should have size 1
      dbProxy.sentMetrics should contain key s"bcre.db.${DependencyNames.BfdbBcreMetric}"
      dbProxy.sentMetrics.get(s"bcre.db.${DependencyNames.BfdbBcreMetric}") should contain("method" -> queryName)
    }
  }

}
