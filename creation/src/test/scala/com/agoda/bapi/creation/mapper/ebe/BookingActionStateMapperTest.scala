package com.agoda.bapi.creation.mapper.ebe

import com.agoda.bapi.agent.common.schema.BookingActionMessageTopic.BAM_Topic_AwaitPaymentToken
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.ExperimentDataMock
import com.agoda.bapi.common.message.creation.common.Payment3DSRequest
import com.agoda.bapi.common.message.creation.{ActivitiesItem, BookingPayment, CreateBookingRequest, CreditCard, Customer, PaymentAmount, Products, PropertyItem}
import com.agoda.bapi.common.model.creation.common.AccountingEntity
import com.agoda.bapi.common.model.creation.{BAPIBooking, BookingChannelDiscountBreakdown, BookingItem, BookingRoom, BookingYCSPromotionBreakdown, EBEBooking, EBEHotel, Payment3DSOption}
import com.agoda.bapi.common.model.creation.mocks.CreateBookingRequestMock
import com.agoda.bapi.common.model.payment.{GatewayReferenceRedirect, PaymentContinuation, SupplierPaymentMethod}
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{CashbackPayment, ExperimentNames, PaymentMethodFromDB, StatusToken, UserContext, WhiteLabel, WhiteLabelInfo}
import com.agoda.bapi.common.service.{FeatureAware, MessagesBag}
import com.agoda.bapi.common.ProductTokenMockHelper
import com.agoda.bapi.creation.CreateBookingHelper
import com.agoda.bapi.creation.model.{PointsType, db}
import com.agoda.bapi.creation.model.db.{BookingActionStateCustomer, PaymentInfo, PaymentRedirect, Points}
import com.agoda.bapi.creation.model.multi.{MultiProductsRequest, Product}
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.bapi.creation.util.CustomerApiTokenUtils
import com.agoda.mpb.common.BookingType
import com.agoda.mpb.common.models.state.PointsAttributes
import com.agoda.mpbe.state.common.enums.Gateway.Gateway
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import generated.model.{MPBE, OverridePlaceHolder}
import mocks.MeasurementStubs.{logBookingCreationLogMessageBaseStub, measureStub}
import mocks.PropertyBookingDuplicateCheckMock.baseCegFastTrackProduct
import mocks.RequestContextMock
import org.mockito.Mockito.{spy, when}
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.TableDrivenPropertyChecks
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar
import org.joda.time.LocalDate
import org.mockito.Mockito

class BookingActionStateMapperTest
    extends AsyncWordSpec
    with Matchers
    with MockitoSugar
    with BookingActionStateMapper
    with CreateBookingHelper
    with RequestContextMock
    with ProductTokenMockHelper
    with TableDrivenPropertyChecks {
  val mockCustomerApiTokenUtils = mock[CustomerApiTokenUtils]

  def createMultiProductRequest: MultiProductsRequest = {
    val request = mock[MultiProductsRequest]
    when(request.cegFastTracks).thenReturn(Seq.empty)
    when(request.flights).thenReturn(Seq.empty)
    when(request.request).thenReturn(baseReq)
    request
  }

  "BookingActionStateMapper" should {
    val experimentNames = "FLCO-XXX"
    "mapBookingActionStateCustomer" should {
      "map properly" in {
        val defaultCustomer = Customer(memberId = 10, isUserLoggedIn = true)
        val expect          = BookingActionStateCustomer(memberId = 10, isUserLoggedIn = true)
        val actual          = BookingActionStateMapper.mapBookingActionStateCustomer(defaultCustomer)
        actual shouldBe expect
      }
    }

    "addForceExperimentInCreateBookingRequest" should {
      val mockFeatureAware              = mock[FeatureAware]
      implicit val featureAwareImplicit = Some(mockFeatureAware)
      val mockMultiProductRequest       = createMultiProductRequest
      "add new force in base request" in {
        when(mockMultiProductRequest.request).thenReturn(baseReq)
        when(mockMultiProductRequest.bookingFlow).thenReturn(BookingFlow.SingleProperty)
        val request = addForceExperimentInCreateBookingRequest(
          mockMultiProductRequest,
          Map(experimentNames -> "A")
        )
        val expect = Some(
          Map(
            "additionalProp1" -> "string",
            "additionalProp2" -> "string",
            "additionalProp3" -> "string",
            experimentNames   -> "A"
          )
        )

        val actual = request.userContext.flatMap(_.experimentData.flatMap(_.force))

        actual shouldBe expect
      }
      "add force in none force base " in {
        val request = baseReq.copy(userContext =
          baseReq.userContext.map(_.copy(experimentData = Some(ExperimentDataMock.valueForceNone)))
        )
        when(mockMultiProductRequest.request).thenReturn(request)
        when(mockMultiProductRequest.bookingFlow).thenReturn(BookingFlow.SingleProperty)
        val requestAdd: CreateBookingRequest =
          addForceExperimentInCreateBookingRequest(
            mockMultiProductRequest,
            Map(experimentNames -> "A")
          )

        val expect = Some(Map(experimentNames -> "A"))
        val actual = requestAdd.userContext.flatMap(_.experimentData.flatMap(_.force))
        actual shouldBe expect
      }

      "Return correct forced experiment list" when {
        val bookingActionStateMapper = spy(new BookingActionStateMapper {})
        val multiProductsRequest     = createMultiProductRequest
        val request = CreateBookingRequestMock
          .getMock()
          .copy(products =
            new Products(
              propertyItems = Some(Seq(mock[PropertyItem])),
              flightItems = Some(Seq.empty),
              carItems = Some(Seq.empty),
              protectionItems = None,
              activitiesItems = Some(Seq.empty)
            )
          )
        val emptyProductRequest = request.copy(products =
          new Products(
            propertyItems = Some(Seq.empty),
            flightItems = Some(Seq.empty),
            carItems = Some(Seq.empty),
            protectionItems = None,
            activitiesItems = Some(Seq.empty)
          )
        )
        val featureAware = mock[FeatureAware]
        val userContext = Some(
          UserContext(
            languageId = 1,
            requestOrigin = "A1",
            currency = "THB",
            nationalityId = 0,
            experimentData = Some(ExperimentDataMock.valueForceNone),
            isLoggedInUser = true
          )
        )
        val requestCtx = requestContext(mock[MessagesBag], userContext).copy(featureAware = Some(featureAware))

        when(multiProductsRequest.request).thenReturn(request)
        when(multiProductsRequest.requestContext).thenReturn(requestCtx)

        val mockProductRoomInfo = mock[Product[RoomInfo]]
        val mockRoomInfo        = mock[RoomInfo]
        val mockBapiBooking     = mock[BAPIBooking]
        val mockBookingItem     = mock[BookingItem]
        val mockEbeBooking      = mock[EBEBooking]
        val mockEbeHotel        = mock[EBEHotel]
        val mockBookingRoom     = mock[BookingRoom]

        when(multiProductsRequest.properties).thenReturn(Seq(mockProductRoomInfo))
        when(mockProductRoomInfo.info).thenReturn(mockRoomInfo)
        when(mockRoomInfo.bapiBooking).thenReturn(mockBapiBooking)
        when(mockBapiBooking.booking).thenReturn(Some(mockBookingItem))
        when(mockBookingItem.booking).thenReturn(List(mockEbeBooking))
        when(mockEbeBooking.hotel).thenReturn(List(mockEbeHotel))
        when(mockEbeHotel.room).thenReturn(List(mockBookingRoom))
        when(mockBookingRoom.promotionsBreakdown).thenReturn(None)
        when(mockBookingRoom.channelDiscountBreakdown).thenReturn(None)

        "force CEGFA-996 to A when feature aware returns false" in {
          when(featureAware.enableFlightsHWFlow).thenReturn(false)

          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          val forcedExperimentMap = creationRequest.userContext.get.experimentData.get.force.get
          forcedExperimentMap should contain(ExperimentNames.isHyperWalletExperiment -> "A")
        }

        "force CEGFA-996 to B when feature aware returns false" in {
          when(featureAware.enableFlightsHWFlow).thenReturn(true)

          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          val forcedExperimentMap = creationRequest.userContext.get.experimentData.get.force.get
          forcedExperimentMap should contain(ExperimentNames.isHyperWalletExperiment -> "B")
        }

        "force CEGFA-1049 to A when feature aware returns false" in {
          when(featureAware.enableSwapFinNodeWithEmailNodeForBkgRejection).thenReturn(false)

          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          val forcedExperimentMap = creationRequest.userContext.get.experimentData.get.force.get
          forcedExperimentMap should contain(ExperimentNames.enableSwapFinNodeWithEmailNodeForBkgRejection -> "A")
        }

        "force CEGFA-1049 to B when feature aware returns true" in {
          when(featureAware.enableSwapFinNodeWithEmailNodeForBkgRejection).thenReturn(true)

          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          val forcedExperimentMap = creationRequest.userContext.get.experimentData.get.force.get
          forcedExperimentMap should contain(ExperimentNames.enableSwapFinNodeWithEmailNodeForBkgRejection -> "B")
        }

        "force CEGFA-1093 to A when feature aware returns false" in {
          when(featureAware.enableFlightReverseBreakdown).thenReturn(false)

          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          val forcedExperimentMap = creationRequest.userContext.get.experimentData.get.force.get
          forcedExperimentMap should contain(ExperimentNames.enableFlightReverseBreakdown -> "A")
        }

        "force CEGFA-1093 to B when feature aware returns true" in {
          when(featureAware.enableFlightReverseBreakdown).thenReturn(true)

          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          val forcedExperimentMap = creationRequest.userContext.get.experimentData.get.force.get
          forcedExperimentMap should contain(ExperimentNames.enableFlightReverseBreakdown -> "B")
        }

        "force SKYC-8672 to A when feature aware returns false" in {
          when(featureAware.isUpdateBookingRejectAgentToBeMonitoringAgent).thenReturn(false)

          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          val forcedExperimentMap = creationRequest.userContext.get.experimentData.get.force.get
          forcedExperimentMap should contain("SKYC-8672" -> "A")
        }

        "force SKYC-8672 to B when feature aware returns true" in {
          when(featureAware.isUpdateBookingRejectAgentToBeMonitoringAgent).thenReturn(true)

          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          val forcedExperimentMap = creationRequest.userContext.get.experimentData.get.force.get
          forcedExperimentMap should contain("SKYC-8672" -> "B")
        }

        "force FlightsMultiProductPMCMigration to A when feature aware returns false" in {
          when(featureAware.enableFlightsMultiProductPMCMigration).thenReturn(false)

          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          val forcedExperimentMap = creationRequest.userContext.get.experimentData.get.force.get
          forcedExperimentMap should contain(ExperimentNames.enableFlightsMultiProductPMCMigration -> "A")
        }

        "force EnableMultiCityFlightsExperiment to B when feature aware returns true" in {
          when(featureAware.enableMultiCityFlights).thenReturn(true)

          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          val forcedExperimentMap = creationRequest.userContext.get.experimentData.get.force.get
          forcedExperimentMap should contain(ExperimentNames.enableMultiCityFlights -> "B")
        }

        "force EnableMultiCityFlightsExperiment to A when feature aware returns false" in {
          when(featureAware.enableMultiCityFlights).thenReturn(false)

          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          val forcedExperimentMap = creationRequest.userContext.get.experimentData.get.force.get
          forcedExperimentMap should contain(ExperimentNames.enableMultiCityFlights -> "A")
        }

        "force FlightsMultiProductPMCMigration to B when feature aware returns true" in {
          when(featureAware.enableFlightsMultiProductPMCMigration).thenReturn(true)

          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          val forcedExperimentMap = creationRequest.userContext.get.experimentData.get.force.get
          forcedExperimentMap should contain(ExperimentNames.enableFlightsMultiProductPMCMigration -> "B")
        }

        "force JTBB-6117 to A when feature aware returns false" in {
          when(featureAware.enableRurubuDirectSupply).thenReturn(false)

          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          val forcedExperimentMap = creationRequest.userContext.get.experimentData.get.force.get
          forcedExperimentMap should contain(ExperimentNames.EnableRurubuDirectSupply -> "A")
        }

        "force JTBB-6117 to B when feature aware returns true" in {
          when(featureAware.enableRurubuDirectSupply).thenReturn(true)

          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          val forcedExperimentMap = creationRequest.userContext.get.experimentData.get.force.get
          forcedExperimentMap should contain(ExperimentNames.EnableRurubuDirectSupply -> "B")
        }

        "force instant hotel transfer experiments" when {
          "payment method is non redirect payment" in {
            when(featureAware.enablePropertyInstantTransferForRedirectPayment).thenReturn(true)
            when(featureAware.enablePropertyInstantTransferForNonRedirectPayment).thenReturn(true)
            val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
              multiProductsRequest,
              mockCustomerApiTokenUtils
            )
            for {
              userContext         <- creationRequest.userContext
              experimentData      <- userContext.experimentData
              forcedExperimentMap <- experimentData.force
            } yield {
              forcedExperimentMap.get("MPBE-5577").contains("B") shouldBe true
              forcedExperimentMap.get("MPBE-5591").contains("A") shouldBe true
            }
            succeed
          }

          "payment method is redirect payment" in {
            when(featureAware.enablePropertyInstantTransferForRedirectPayment).thenReturn(true)
            when(featureAware.enablePropertyInstantTransferForNonRedirectPayment).thenReturn(true)
            val redirectRequest =
              baseReq.copy(payment = baseReq.payment.copy(redirect = Some(mock[GatewayReferenceRedirect])))
            when(multiProductsRequest.request).thenReturn(redirectRequest)
            val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
              multiProductsRequest,
              mockCustomerApiTokenUtils
            )
            for {
              userContext         <- creationRequest.userContext
              experimentData      <- userContext.experimentData
              forcedExperimentMap <- experimentData.force
            } yield {
              forcedExperimentMap.get("MPBE-5577").contains("A") shouldBe true
              forcedExperimentMap.get("MPBE-5591").contains("B") shouldBe true
            }
            succeed
          }
        }

        "payout accuracy check exp is ON but real payout hotel exp is OFF" in {
          when(featureAware.enablePropertyPayoutAccuracyChecker).thenReturn(true)
          when(featureAware.enablePropertyPayoutAgent).thenReturn(false)
          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          for {
            userContext         <- creationRequest.userContext
            experimentData      <- userContext.experimentData
            forcedExperimentMap <- experimentData.force
          } yield forcedExperimentMap.get("MPBE-3404").contains("B") shouldBe true
          succeed
        }

        "payout accuracy check exp is ON but real payout hotel exp is OFF for multi-hotel flow" in {
          when(featureAware.enablePropertyPayoutAccuracyChecker).thenReturn(true)
          when(featureAware.enablePropertyPayoutAgent).thenReturn(false)
          val multiPropertyCreateRequest: CreateBookingRequest =
            request.copy(products =
              request.products.copy(propertyItems = Option(Seq(mock[PropertyItem], mock[PropertyItem])))
            )
          when(multiProductsRequest.request).thenReturn(multiPropertyCreateRequest)
          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          for {
            userContext         <- creationRequest.userContext
            experimentData      <- userContext.experimentData
            forcedExperimentMap <- experimentData.force
          } yield forcedExperimentMap.get("MPBE-3404").contains("A") shouldBe true
          succeed
        }

        "is IOS device and has activity items" in {
          val requestActivity = CreateBookingRequestMock
            .getMock()
            .copy(
              products = new Products(
                propertyItems = Some(Seq.empty),
                flightItems = Some(Seq.empty),
                carItems = Some(Seq.empty),
                protectionItems = None,
                activitiesItems = Some(Seq(mock[ActivitiesItem]))
              ),
              platformId = Some(34)
            )
          when(multiProductsRequest.request).thenReturn(requestActivity)
          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          for {
            userContext         <- creationRequest.userContext
            experimentData      <- userContext.experimentData
            forcedExperimentMap <- experimentData.force
          } yield forcedExperimentMap.get("AFT-149").contains("B") shouldBe true
          succeed
        }

        "NOT IOS device and has activity items" in {
          val requestActivity = CreateBookingRequestMock
            .getMock()
            .copy(
              products = new Products(
                propertyItems = Some(Seq.empty),
                flightItems = Some(Seq.empty),
                carItems = Some(Seq.empty),
                protectionItems = None,
                activitiesItems = Some(Seq(mock[ActivitiesItem]))
              ),
              platformId = Some(10009)
            )
          when(multiProductsRequest.request).thenReturn(requestActivity)
          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          for {
            userContext         <- creationRequest.userContext
            experimentData      <- userContext.experimentData
            forcedExperimentMap <- experimentData.force
          } yield forcedExperimentMap.get("AFT-149").contains("B") shouldBe false
          succeed
        }

        "enableActivityCartBFAndroid and is NOT IOS device and has activity items" in {
          val requestActivity = CreateBookingRequestMock
            .getMock()
            .copy(
              products = new Products(
                propertyItems = Some(Seq.empty),
                flightItems = Some(Seq.empty),
                carItems = Some(Seq.empty),
                protectionItems = None,
                activitiesItems = Some(Seq(mock[ActivitiesItem]))
              ),
              platformId = Some(10009)
            )
          when(multiProductsRequest.request).thenReturn(requestActivity)
          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          for {
            userContext         <- creationRequest.userContext
            experimentData      <- userContext.experimentData
            forcedExperimentMap <- experimentData.force
          } yield forcedExperimentMap.get("AFT-149").contains("B") shouldBe false
          succeed
        }

        "has no activity items" in {
          val creationRequest = bookingActionStateMapper.mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          )
          for {
            userContext         <- creationRequest.userContext
            experimentData      <- userContext.experimentData
            forcedExperimentMap <- experimentData.force
          } yield forcedExperimentMap.get("AFT-149") shouldBe Some("A")
          succeed
        }

        "enableFlightPayout" should {
          val testcase = Table(
            ("supplierPaymentMethod", "enableFeature", "expected"),
            (None, true, Some("B")),
            (Some(SupplierPaymentMethod.None), true, Some("B")),
            (Some(SupplierPaymentMethod.TelexTransfer), true, Some("B")),
            (Some(SupplierPaymentMethod.PlasticCard), true, Some("B")),
            (Some(SupplierPaymentMethod.UniversalPlasticCard), true, Some("B")),
            (Some(SupplierPaymentMethod.Manual), true, Some("B")),
            (Some(SupplierPaymentMethod.UpcOnEPass), true, Some("B")),
            (Some(SupplierPaymentMethod.Paypal), true, Some("B")),
            (None, false, Some("A")),
            (Some(SupplierPaymentMethod.None), false, Some("A")),
            (Some(SupplierPaymentMethod.TelexTransfer), false, Some("B")),
            (Some(SupplierPaymentMethod.PlasticCard), false, Some("B")),
            (Some(SupplierPaymentMethod.UniversalPlasticCard), false, Some("B")),
            (Some(SupplierPaymentMethod.Manual), false, Some("B")),
            (Some(SupplierPaymentMethod.UpcOnEPass), false, Some("B")),
            (Some(SupplierPaymentMethod.Paypal), false, Some("B"))
          )
          forAll(testcase) { (supplierPaymentMethod, enableFeature, expected) =>
            s"when flightItem supplierPaymentMethod equal $supplierPaymentMethod with featureAwareness ${enableFeature} then force enableFlightPayout $expected" in {
              val flightItem =
                Product(
                  BookingType.Flight,
                  defaultFlightBookingToken.copy(
                    info = defaultFlightBookingToken.info.map(_.copy(supplierPaymentMethod = supplierPaymentMethod))
                  )
                )
              when(featureAware.enableFlightPayoutAgent).thenReturn(enableFeature)
              when(multiProductsRequest.requestContext).thenReturn(requestCtx)
              when(multiProductsRequest.flights).thenReturn(Seq(flightItem))
              when(mockMultiProductRequest.bookingFlow).thenReturn(BookingFlow.SingleFlight)
              val mockProductRoomInfo = mock[Product[RoomInfo]]
              val mockRoomInfo        = mock[RoomInfo]
              val mockBapiBooking     = mock[BAPIBooking]
              val mockBookingItem     = mock[BookingItem]
              val mockEbeBooking      = mock[EBEBooking]
              val mockEbeHotel        = mock[EBEHotel]
              val mockBookingRoom     = mock[BookingRoom]

              when(multiProductsRequest.properties).thenReturn(Seq(mockProductRoomInfo))
              when(mockProductRoomInfo.info).thenReturn(mockRoomInfo)
              when(mockRoomInfo.bapiBooking).thenReturn(mockBapiBooking)
              when(mockBapiBooking.booking).thenReturn(Some(mockBookingItem))
              when(mockBookingItem.booking).thenReturn(List(mockEbeBooking))
              when(mockEbeBooking.hotel).thenReturn(List(mockEbeHotel))
              when(mockEbeHotel.room).thenReturn(List(mockBookingRoom))
              when(mockBookingRoom.promotionsBreakdown).thenReturn(None)
              when(mockBookingRoom.channelDiscountBreakdown).thenReturn(None)
              val creationRequest =
                bookingActionStateMapper.mapCreateBookingRequestToActionState(
                  multiProductsRequest,
                  mockCustomerApiTokenUtils
                )
              val result = for {
                userContext         <- creationRequest.userContext
                experimentData      <- userContext.experimentData
                forcedExperimentMap <- experimentData.force
                result              <- forcedExperimentMap.get(ExperimentNames.enableFlightPayoutAgent)
              } yield result

              result shouldBe expected
              succeed
            }
          }

        }

      }

      "isAgentAssist true, isEnableLocalProvisioningSkipForAAB exp is ON" in {
        val bookingActionStateMapper = spy(new BookingActionStateMapper {})
        val multiProductsRequest     = createMultiProductRequest
        val request = CreateBookingRequestMock
          .getMock()
          .copy(products =
            new Products(
              propertyItems = Some(Seq(mock[PropertyItem])),
              flightItems = Some(Seq.empty),
              carItems = Some(Seq.empty),
              protectionItems = None,
              activitiesItems = Some(Seq.empty)
            )
          )
        val featureAware = mock[FeatureAware]
        val userContext = Some(
          UserContext(
            languageId = 1,
            requestOrigin = "A1",
            currency = "THB",
            nationalityId = 0,
            experimentData = None,
            isLoggedInUser = true,
            capiToken = Some("token")
          )
        )
        val requestCtx = requestContext(mock[MessagesBag], userContext).copy(featureAware = Some(featureAware))
        when(multiProductsRequest.request).thenReturn(request)
        when(multiProductsRequest.requestContext).thenReturn(requestCtx)
        val mockProductRoomInfo = mock[Product[RoomInfo]]
        val mockRoomInfo        = mock[RoomInfo]
        val mockBapiBooking     = mock[BAPIBooking]
        val mockBookingItem     = mock[BookingItem]
        val mockEbeBooking      = mock[EBEBooking]
        val mockEbeHotel        = mock[EBEHotel]
        val mockBookingRoom     = mock[BookingRoom]

        when(multiProductsRequest.properties).thenReturn(Seq(mockProductRoomInfo))
        when(mockProductRoomInfo.info).thenReturn(mockRoomInfo)
        when(mockRoomInfo.bapiBooking).thenReturn(mockBapiBooking)
        when(mockBapiBooking.booking).thenReturn(Some(mockBookingItem))
        when(mockBookingItem.booking).thenReturn(List(mockEbeBooking))
        when(mockEbeBooking.hotel).thenReturn(List(mockEbeHotel))
        when(mockEbeHotel.room).thenReturn(List(mockBookingRoom))
        when(mockBookingRoom.promotionsBreakdown).thenReturn(None)
        when(mockBookingRoom.channelDiscountBreakdown).thenReturn(None)
        when(mockCustomerApiTokenUtils.isAgentAssistantBooking("token")).thenReturn(true)
        val creationRequest =
          bookingActionStateMapper.mapCreateBookingRequestToActionState(multiProductsRequest, mockCustomerApiTokenUtils)
        for {
          userContext         <- creationRequest.userContext
          experimentData      <- userContext.experimentData
          forcedExperimentMap <- experimentData.force
        } yield forcedExperimentMap.get("MPBE-AAB").contains("B") shouldBe true
        creationRequest.isAgentAssistedBooking shouldBe Some(true)
        succeed
      }

      "isAgentAssist false, isEnableLocalProvisioningSkipForAAB exp is OFF" in {
        val bookingActionStateMapper = spy(new BookingActionStateMapper {})
        val multiProductsRequest     = createMultiProductRequest
        val request = CreateBookingRequestMock
          .getMock()
          .copy(products =
            new Products(
              propertyItems = Some(Seq(mock[PropertyItem])),
              flightItems = Some(Seq.empty),
              carItems = Some(Seq.empty),
              protectionItems = None,
              activitiesItems = Some(Seq.empty)
            )
          )
        val featureAware = mock[FeatureAware]
        val userContext = Some(
          UserContext(
            languageId = 1,
            requestOrigin = "A1",
            currency = "THB",
            nationalityId = 0,
            experimentData = None,
            isLoggedInUser = true,
            capiToken = Some("token")
          )
        )
        val requestCtx = requestContext(mock[MessagesBag], userContext).copy(featureAware = Some(featureAware))

        when(multiProductsRequest.request).thenReturn(request)
        when(multiProductsRequest.requestContext).thenReturn(requestCtx)
        val mockProductRoomInfo = mock[Product[RoomInfo]]
        val mockRoomInfo        = mock[RoomInfo]
        val mockBapiBooking     = mock[BAPIBooking]
        val mockBookingItem     = mock[BookingItem]
        val mockEbeBooking      = mock[EBEBooking]
        val mockEbeHotel        = mock[EBEHotel]
        val mockBookingRoom     = mock[BookingRoom]

        when(multiProductsRequest.properties).thenReturn(Seq(mockProductRoomInfo))
        when(mockProductRoomInfo.info).thenReturn(mockRoomInfo)
        when(mockRoomInfo.bapiBooking).thenReturn(mockBapiBooking)
        when(mockBapiBooking.booking).thenReturn(Some(mockBookingItem))
        when(mockBookingItem.booking).thenReturn(List(mockEbeBooking))
        when(mockEbeBooking.hotel).thenReturn(List(mockEbeHotel))
        when(mockEbeHotel.room).thenReturn(List(mockBookingRoom))
        when(mockBookingRoom.promotionsBreakdown).thenReturn(None)
        when(mockBookingRoom.channelDiscountBreakdown).thenReturn(None)
        when(mockCustomerApiTokenUtils.isAgentAssistantBooking("token")).thenReturn(false)
        val creationRequest =
          bookingActionStateMapper.mapCreateBookingRequestToActionState(multiProductsRequest, mockCustomerApiTokenUtils)
        for {
          userContext         <- creationRequest.userContext
          experimentData      <- userContext.experimentData
          forcedExperimentMap <- experimentData.force
        } yield forcedExperimentMap.get("MPBE-AAB").contains("B") shouldBe false
        creationRequest.isAgentAssistedBooking shouldBe Some(false)
        succeed

      }
    }

    "mapAllowPaymentRetry" should {
      val flightsOnlyMultiProductRequest = mock[MultiProductsRequest]
      when(flightsOnlyMultiProductRequest.hasFlightsMainProductOnly).thenReturn(true)
      val nonFlightMultiProductsRequest = mock[MultiProductsRequest]
      when(nonFlightMultiProductsRequest.hasFlightsMainProductOnly).thenReturn(false)
      val allowRetryMpbeFeature = mock[FeaturesConfiguration]
      when(allowRetryMpbeFeature.mpbe).thenReturn(MPBE(allowPaymentRetry = Some(true)))
      val defaultMPBEFeature = mock[FeaturesConfiguration]
      when(defaultMPBEFeature.mpbe).thenReturn(MPBE())
      val expEnabledFeature = mock[FeatureAware]
      when(expEnabledFeature.allowRetryPayment).thenReturn(true)

      val testCases = Table(
        ("description", "multiProductsRequest", "whiteLabelFeature", "featureAware", "expected"),
        (
          "flightsOnlyMultiProductRequest with allowRetryMpbeFeature and expEnabledFeature",
          flightsOnlyMultiProductRequest,
          allowRetryMpbeFeature,
          expEnabledFeature,
          Some(true)
        ),
        (
          "flightsOnlyMultiProductRequest with defaultMPBEFeature and expEnabledFeature",
          flightsOnlyMultiProductRequest,
          defaultMPBEFeature,
          expEnabledFeature,
          None
        ),
        (
          "flightsOnlyMultiProductRequest with defaultMPBEFeature and exp not enabled",
          flightsOnlyMultiProductRequest,
          defaultMPBEFeature,
          mock[FeatureAware],
          None
        ),
        (
          "nonFlightMultiProductsRequest with allowRetryMpbeFeature and expEnabledFeature",
          nonFlightMultiProductsRequest,
          allowRetryMpbeFeature,
          expEnabledFeature,
          None
        )
      )

      forAll(testCases) { (description, multiProductsRequest, whiteLabelFeature, featureAware, expected) =>
        s"return $expected when $description" in {
          val requestContext = mock[RequestContext]
          when(multiProductsRequest.requestContext).thenReturn(requestContext)
          when(requestContext.featureAware).thenReturn(Some(featureAware))
          when(multiProductsRequest.request).thenReturn(baseReq)
          val mockWhiteLabelInfo = mock[WhiteLabelInfo]
          when(mockWhiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Agoda)
          when(mockWhiteLabelInfo.feature).thenReturn(whiteLabelFeature)
          when(requestContext.whiteLabelInfo).thenReturn(mockWhiteLabelInfo)
          when(requestContext.userContext).thenReturn(None)
          when(requestContext.agHeaders).thenReturn(Nil)
          when(requestContext.bookingCreationContext).thenReturn(None)
          val mockProductRoomInfo = mock[Product[RoomInfo]]
          val mockRoomInfo        = mock[RoomInfo]
          val mockBapiBooking     = mock[BAPIBooking]
          val mockBookingItem     = mock[BookingItem]
          val mockEbeBooking      = mock[EBEBooking]
          val mockEbeHotel        = mock[EBEHotel]
          val mockBookingRoom     = mock[BookingRoom]

          when(multiProductsRequest.properties).thenReturn(Seq(mockProductRoomInfo))
          when(mockProductRoomInfo.info).thenReturn(mockRoomInfo)
          when(mockRoomInfo.bapiBooking).thenReturn(mockBapiBooking)
          when(mockBapiBooking.booking).thenReturn(Some(mockBookingItem))
          when(mockBookingItem.booking).thenReturn(List(mockEbeBooking))
          when(mockEbeBooking.hotel).thenReturn(List(mockEbeHotel))
          when(mockEbeHotel.room).thenReturn(List(mockBookingRoom))
          when(mockBookingRoom.promotionsBreakdown).thenReturn(None)
          when(mockBookingRoom.channelDiscountBreakdown).thenReturn(None)

          when(multiProductsRequest.cegFastTracks).thenReturn(Seq.empty)
          when(multiProductsRequest.flights).thenReturn(Seq.empty)
          mapCreateBookingRequestToActionState(
            multiProductsRequest,
            mockCustomerApiTokenUtils
          ).allowRetryPayment shouldBe expected
        }
      }

    }

    "isFixPromotionDiscountsInfo" should {
      val multiProductsRequest = createMultiProductRequest
      val mockProductRoomInfo  = mock[Product[RoomInfo]]
      val mockRoomInfo         = mock[RoomInfo]
      val mockBapiBooking      = mock[BAPIBooking]
      val mockBookingItem      = mock[BookingItem]
      val mockEbeBooking       = mock[EBEBooking]
      val mockEbeHotel         = mock[EBEHotel]
      val mockBookingRoom      = mock[BookingRoom]

      when(multiProductsRequest.properties).thenReturn(Seq(mockProductRoomInfo))
      when(mockProductRoomInfo.info).thenReturn(mockRoomInfo)
      when(mockRoomInfo.bapiBooking).thenReturn(mockBapiBooking)
      when(mockBapiBooking.booking).thenReturn(Some(mockBookingItem))
      when(mockBookingItem.booking).thenReturn(List(mockEbeBooking))
      when(mockEbeBooking.hotel).thenReturn(List(mockEbeHotel))
      when(mockEbeHotel.room).thenReturn(List(mockBookingRoom))

      "force A variant when no promotion and channel applied" in {
        when(mockBookingRoom.promotionsBreakdown).thenReturn(None)
        when(mockBookingRoom.channelDiscountBreakdown).thenReturn(None)

        val actual = isFixPromotionDiscountsInfo(multiProductsRequest)
        actual shouldBe Map(ExperimentNames.FixPromotionDiscountsInfo -> "A")
      }

      "force A variant when discountAppliedDate is empty" in {

        val mockBookingYCSPromotionBreakdown = BookingYCSPromotionBreakdown(
          id = 10024,
          name = "discount promo 1",
          discountValue = 10,
          discountType = 1,
          appliedDate = List(LocalDate.parse("2022-11-04"), LocalDate.parse("2022-11-05")),
          discountAppliedDate = List.empty
        )
        val mockBookingChannelDiscountBreakdown = BookingChannelDiscountBreakdown(
          channelId = 6,
          name = "Domestic",
          discountPercent = 0.1,
          appliedDate = List(LocalDate.parse("2022-11-04"), LocalDate.parse("2022-11-05")),
          discountAppliedDate = List.empty
        )
        when(mockBookingRoom.promotionsBreakdown).thenReturn(Some(List(mockBookingYCSPromotionBreakdown)))
        when(mockBookingRoom.channelDiscountBreakdown).thenReturn(Some(List(mockBookingChannelDiscountBreakdown)))

        val actual = isFixPromotionDiscountsInfo(multiProductsRequest)
        actual shouldBe Map(ExperimentNames.FixPromotionDiscountsInfo -> "A")
      }

      "force A variant when discountAppliedDate is empty only promotion discount applied" in {

        val mockBookingYCSPromotionBreakdown = BookingYCSPromotionBreakdown(
          id = 10024,
          name = "discount promo 1",
          discountValue = 10,
          discountType = 1,
          appliedDate = List(LocalDate.parse("2022-11-04"), LocalDate.parse("2022-11-05")),
          discountAppliedDate = List.empty
        )
        when(mockBookingRoom.promotionsBreakdown).thenReturn(Some(List(mockBookingYCSPromotionBreakdown)))
        when(mockBookingRoom.channelDiscountBreakdown).thenReturn(None)

        val actual = isFixPromotionDiscountsInfo(multiProductsRequest)
        actual shouldBe Map(ExperimentNames.FixPromotionDiscountsInfo -> "A")
      }

      "force A variant when discountAppliedDate is empty only channel discount applied" in {

        val mockBookingChannelDiscountBreakdown = BookingChannelDiscountBreakdown(
          channelId = 6,
          name = "Domestic",
          discountPercent = 0.1,
          appliedDate = List(LocalDate.parse("2022-11-04"), LocalDate.parse("2022-11-05")),
          discountAppliedDate = List.empty
        )
        when(mockBookingRoom.promotionsBreakdown).thenReturn(None)
        when(mockBookingRoom.channelDiscountBreakdown).thenReturn(Some(List(mockBookingChannelDiscountBreakdown)))

        val actual = isFixPromotionDiscountsInfo(multiProductsRequest)
        actual shouldBe Map(ExperimentNames.FixPromotionDiscountsInfo -> "A")
      }

      "force B variant when discountAppliedDate is non-empty" in {
        val mockBookingYCSPromotionBreakdown = BookingYCSPromotionBreakdown(
          id = 10024,
          name = "discount promo 1",
          discountValue = 10,
          discountType = 1,
          appliedDate = List.empty,
          discountAppliedDate = List(LocalDate.parse("2022-11-04"))
        )
        val mockBookingChannelDiscountBreakdown = BookingChannelDiscountBreakdown(
          channelId = 6,
          name = "Domestic",
          discountPercent = 0.1,
          appliedDate = List.empty,
          discountAppliedDate = List(LocalDate.parse("2022-11-04"))
        )
        when(mockBookingRoom.promotionsBreakdown).thenReturn(Some(List(mockBookingYCSPromotionBreakdown)))
        when(mockBookingRoom.channelDiscountBreakdown).thenReturn(Some(List(mockBookingChannelDiscountBreakdown)))

        val actual = isFixPromotionDiscountsInfo(multiProductsRequest)
        actual shouldBe Map(ExperimentNames.FixPromotionDiscountsInfo -> "B")
      }

      "force B variant when discountAppliedDate is non-empty only promotion discount applied" in {
        val mockBookingYCSPromotionBreakdown = BookingYCSPromotionBreakdown(
          id = 10024,
          name = "discount promo 1",
          discountValue = 10,
          discountType = 1,
          appliedDate = List.empty,
          discountAppliedDate = List(LocalDate.parse("2022-11-04"))
        )
        when(mockBookingRoom.promotionsBreakdown).thenReturn(Some(List(mockBookingYCSPromotionBreakdown)))
        when(mockBookingRoom.channelDiscountBreakdown).thenReturn(None)

        val actual = isFixPromotionDiscountsInfo(multiProductsRequest)
        actual shouldBe Map(ExperimentNames.FixPromotionDiscountsInfo -> "B")
      }

      "force B variant when discountAppliedDate is non-empty only channel discount applied" in {
        val mockBookingChannelDiscountBreakdown = BookingChannelDiscountBreakdown(
          channelId = 6,
          name = "Domestic",
          discountPercent = 0.1,
          appliedDate = List.empty,
          discountAppliedDate = List(LocalDate.parse("2022-11-04"))
        )
        when(mockBookingRoom.promotionsBreakdown).thenReturn(None)
        when(mockBookingRoom.channelDiscountBreakdown).thenReturn(Some(List(mockBookingChannelDiscountBreakdown)))

        val actual = isFixPromotionDiscountsInfo(multiProductsRequest)
        actual shouldBe Map(ExperimentNames.FixPromotionDiscountsInfo -> "B")
      }
    }
    "mapPaymentToPaymentInfo" should {
      val mockPaymentMethod = PaymentMethod.Visa
      val paymentMethodInfo = mock[PaymentMethodFromDB]

      val mockItineraryId   = 1L
      val mockMultiActionId = 1L
      val mockStatusToken =
        StatusToken(
          mockItineraryId,
          mockMultiActionId,
          Set.empty,
          "local",
          Some(1),
          Some(Seq(1)),
          Some(1),
          Some("a"),
          Some(1),
          Some(1),
          Some(1)
        )
      "aggregate hotel payment and flight payment together" in {
        val mockPayment = mock[BookingPayment]

        val mockCreditCard = mock[CreditCard](Mockito.RETURNS_DEEP_STUBS)

        val mockPaymentAmount = PaymentAmount(
          paymentCurrency = "THB",
          paymentAmount = 3100,
          paymentAmountUSD = 300,
          siteExchangeRate = 3,
          upliftExchangeRate = 4,
          destinationExchangeRate = 5,
          giftcardAmountUSD = 50,
          giftcardAmount = 1600
        )
        val redirectRequest =
          GatewayReferenceRedirect("cancelUrl", "successUrl", "")
        when(mockPayment.amount).thenReturn(mockPaymentAmount)
        when(mockPayment.method).thenReturn(mockPaymentMethod)
        when(mockPayment.creditCard).thenReturn(Some(mockCreditCard))
        when(mockPayment.continuation).thenReturn(None)
        when(mockPayment.redirect).thenReturn(Some(redirectRequest))
        when(mockCreditCard.paymentOption).thenReturn(1)

        val result =
          mapPaymentToPaymentInfo(
            mockPayment,
            BookingFlow.Package,
            Some(paymentMethodInfo),
            mockStatusToken
          )

        val expected = PaymentInfo(
          method = mockPaymentMethod,
          paymentCurrency = "THB",
          paymentAmount = 3100,
          paymentAmountUSD = 300,
          accountingEntity = Some(com.agoda.bapi.common.model.creation.AccountingEntity.getPackagingEntity),
          siteExchangeRate = Some(3),
          destinationCurrency = None,
          destinationExchangeRate = Some(5),
          rateQuoteId = Some(0),
          paymentOption = Some(1),
          gateway = None,
          points = Vector(
            Points(
              pointType = PointsType.AgodaCash,
              pointAttributes = PointsAttributes(currency = "USD", amount = 50, Some("THB"), Some(1600))
            ),
            Points(
              pointType = PointsType.Cashback,
              pointAttributes = PointsAttributes(currency = "USD", amount = 0, None, None)
            )
          ),
          paymentRedirect = Some(db.PaymentRedirect(redirectRequest.returnUrl, redirectRequest.cancelUrl)),
          isRedirect = Some(paymentMethodInfo.redirect),
          timeoutMinutes = paymentMethodInfo.timeoutMin,
          paymentCategoryId = Some(paymentMethodInfo.paymentCategory)
        )

        result shouldBe expected
      }

      "map cashback points properly" in {
        val mockPayment = mock[BookingPayment]

        val mockPaymentMethod = PaymentMethod.Visa

        val mockCreditCard = mock[CreditCard](Mockito.RETURNS_DEEP_STUBS)

        val mockPaymentAmount = PaymentAmount(
          paymentCurrency = "THB",
          paymentAmount = 3100,
          paymentAmountUSD = 300,
          siteExchangeRate = 3,
          upliftExchangeRate = 4,
          destinationExchangeRate = 5,
          giftcardAmountUSD = 50,
          giftcardAmount = 1600,
          cashbackPayment = Some(CashbackPayment(cashbackAmount = 30, cashbackAmountUSD = 1))
        )
        val redirectRequest =
          GatewayReferenceRedirect("cancelUrl", "successUrl", "")
        when(mockPayment.amount).thenReturn(mockPaymentAmount)
        when(mockPayment.method).thenReturn(mockPaymentMethod)
        when(mockPayment.creditCard).thenReturn(Some(mockCreditCard))
        when(mockPayment.continuation).thenReturn(None)
        when(mockPayment.redirect).thenReturn(Some(redirectRequest))
        when(mockCreditCard.paymentOption).thenReturn(1)

        val userContext = mock[UserContext]

        val result =
          mapPaymentToPaymentInfo(
            mockPayment,
            BookingFlow.Package,
            Some(paymentMethodInfo),
            mockStatusToken,
            userContext = Some(userContext)
          )

        val expected = Vector(
          Points(
            pointType = PointsType.AgodaCash,
            pointAttributes = PointsAttributes(currency = "USD", amount = 50, Some("THB"), Some(1600))
          ),
          Points(
            pointType = PointsType.Cashback,
            pointAttributes = PointsAttributes(currency = "USD", amount = 1, Some("THB"), Some(30))
          )
        )

        result.points shouldBe expected
      }

      "map vehicle payment" in {
        val mockPayment = mock[BookingPayment]

        val mockPaymentMethod = PaymentMethod.Visa

        val mockCreditCard = mock[CreditCard](Mockito.RETURNS_DEEP_STUBS)

        val mockPaymentAmount = PaymentAmount(
          paymentCurrency = "USD",
          paymentAmount = 100,
          paymentAmountUSD = 300,
          siteExchangeRate = 3,
          upliftExchangeRate = 4,
          destinationExchangeRate = 5,
          giftcardAmountUSD = 50,
          giftcardAmount = 20
        )

        when(mockPayment.amount).thenReturn(mockPaymentAmount)
        when(mockPayment.method).thenReturn(mockPaymentMethod)
        when(mockPayment.creditCard).thenReturn(Some(mockCreditCard))
        when(mockPayment.continuation).thenReturn(None)
        when(mockPayment.redirect).thenReturn(None)
        when(mockCreditCard.paymentOption).thenReturn(1)

        val result =
          mapPaymentToPaymentInfo(
            mockPayment,
            BookingFlow.SingleVehicle,
            Some(paymentMethodInfo),
            mockStatusToken,
            Some(31001)
          )

        val expected = PaymentInfo(
          method = mockPaymentMethod,
          paymentCurrency = "USD",
          paymentAmount = 100,
          paymentAmountUSD = 300,
          accountingEntity =
            Some(AccountingEntity(merchantOfRecord = 5632, rateContract = 31001, revenue = 5674, None)),
          siteExchangeRate = Some(3),
          destinationCurrency = None,
          destinationExchangeRate = Some(5),
          rateQuoteId = Some(0),
          paymentOption = Some(1),
          gateway = None,
          points = Vector(
            Points(
              pointType = PointsType.RMMiles,
              pointAttributes = PointsAttributes(currency = "USD", amount = 50, Some("USD"), Some(20))
            ),
            Points(
              pointType = PointsType.Cashback,
              pointAttributes = PointsAttributes(currency = "USD", amount = 0, None, None)
            )
          ),
          isRedirect = Some(paymentMethodInfo.redirect),
          timeoutMinutes = paymentMethodInfo.timeoutMin,
          paymentCategoryId = Some(paymentMethodInfo.paymentCategory)
        )

        result shouldBe expected
      }

      "map payment category" in {
        val mockPayment = mock[BookingPayment]

        val mockPaymentMethod = PaymentMethod.Visa

        val mockCreditCard = mock[CreditCard](Mockito.RETURNS_DEEP_STUBS)

        val mockPaymentAmount = PaymentAmount(
          paymentCurrency = "USD",
          paymentAmount = 100,
          paymentAmountUSD = 300,
          siteExchangeRate = 3,
          upliftExchangeRate = 4,
          destinationExchangeRate = 5,
          giftcardAmountUSD = 50
        )

        when(mockPayment.amount).thenReturn(mockPaymentAmount)
        when(mockPayment.method).thenReturn(mockPaymentMethod)
        when(mockPayment.creditCard).thenReturn(Some(mockCreditCard))
        when(mockPayment.continuation).thenReturn(None)
        when(mockPayment.redirect).thenReturn(None)
        when(mockCreditCard.paymentOption).thenReturn(1)

        when(paymentMethodInfo.paymentCategory).thenReturn(3)
        val result =
          mapPaymentToPaymentInfo(
            mockPayment,
            BookingFlow.SingleVehicle,
            Some(paymentMethodInfo),
            mockStatusToken,
            Some(31001)
          )

        val expected = PaymentInfo(
          method = mockPaymentMethod,
          paymentCurrency = "USD",
          paymentAmount = 100,
          paymentAmountUSD = 300,
          accountingEntity =
            Some(AccountingEntity(merchantOfRecord = 5632, rateContract = 31001, revenue = 5674, None)),
          siteExchangeRate = Some(3),
          destinationCurrency = None,
          destinationExchangeRate = Some(5),
          rateQuoteId = Some(0),
          paymentOption = Some(1),
          gateway = None,
          points = Vector(
            Points(
              pointType = PointsType.RMMiles,
              pointAttributes = PointsAttributes(currency = "USD", amount = 50, None, None)
            ),
            Points(
              pointType = PointsType.Cashback,
              pointAttributes = PointsAttributes(currency = "USD", amount = 0, None, None)
            )
          ),
          isRedirect = Some(paymentMethodInfo.redirect),
          timeoutMinutes = paymentMethodInfo.timeoutMin,
          paymentCategoryId = Some(2)
        )

        result shouldBe expected
      }

      "map payment category when no category available" in {
        val mockPayment = mock[BookingPayment]

        val mockPaymentMethod = PaymentMethod.Visa

        val mockCreditCard = mock[CreditCard](Mockito.RETURNS_DEEP_STUBS)

        val mockPaymentAmount = PaymentAmount(
          paymentCurrency = "USD",
          paymentAmount = 100,
          paymentAmountUSD = 300,
          siteExchangeRate = 3,
          upliftExchangeRate = 4,
          destinationExchangeRate = 5,
          giftcardAmountUSD = 50
        )

        when(mockPayment.amount).thenReturn(mockPaymentAmount)
        when(mockPayment.method).thenReturn(mockPaymentMethod)
        when(mockPayment.creditCard).thenReturn(Some(mockCreditCard))
        when(mockPayment.continuation).thenReturn(None)
        when(mockPayment.redirect).thenReturn(None)
        when(mockCreditCard.paymentOption).thenReturn(1)

        when(paymentMethodInfo.paymentCategory).thenReturn(100)
        val result =
          mapPaymentToPaymentInfo(
            mockPayment,
            BookingFlow.SingleVehicle,
            Some(paymentMethodInfo),
            mockStatusToken,
            Some(31001)
          )

        val expected = PaymentInfo(
          method = mockPaymentMethod,
          paymentCurrency = "USD",
          paymentAmount = 100,
          paymentAmountUSD = 300,
          accountingEntity =
            Some(AccountingEntity(merchantOfRecord = 5632, rateContract = 31001, revenue = 5674, None)),
          siteExchangeRate = Some(3),
          destinationCurrency = None,
          destinationExchangeRate = Some(5),
          rateQuoteId = Some(0),
          paymentOption = Some(1),
          gateway = None,
          points = Vector(
            Points(
              pointType = PointsType.RMMiles,
              pointAttributes = PointsAttributes(currency = "USD", amount = 50, None, None)
            ),
            Points(
              pointType = PointsType.Cashback,
              pointAttributes = PointsAttributes(currency = "USD", amount = 0, None, None)
            )
          ),
          isRedirect = Some(paymentMethodInfo.redirect),
          timeoutMinutes = paymentMethodInfo.timeoutMin,
          paymentCategoryId = Some(0)
        )

        result shouldBe expected
      }

      "map payment gatewayId, gatewayInfoId and mpiId" in {
        val mockPayment = mock[BookingPayment]

        val mockPaymentMethod = PaymentMethod.Visa

        val mockCreditCard = mock[CreditCard](Mockito.RETURNS_DEEP_STUBS)

        val mockPaymentAmount = PaymentAmount(
          paymentCurrency = "USD",
          paymentAmount = 100,
          paymentAmountUSD = 300,
          siteExchangeRate = 3,
          upliftExchangeRate = 4,
          destinationExchangeRate = 5,
          giftcardAmountUSD = 50
        )
        val mockPaymentContinuation = Some(
          PaymentContinuation(
            gatewayId = Gateway.Adyen,
            gatewayInfoId = Some(123),
            mpiId = Some(101)
          )
        )
        // add mock to test experiment to Payout-API
        when(mockPayment.amount).thenReturn(mockPaymentAmount)
        when(mockPayment.method).thenReturn(mockPaymentMethod)
        when(mockPayment.creditCard).thenReturn(Some(mockCreditCard))
        when(mockPayment.continuation).thenReturn(None)
        when(mockPayment.redirect).thenReturn(None)
        when(mockCreditCard.paymentOption).thenReturn(1)
        when(mockPayment.continuation).thenReturn(mockPaymentContinuation)

        when(paymentMethodInfo.paymentCategory).thenReturn(3)
        val result =
          mapPaymentToPaymentInfo(
            mockPayment,
            BookingFlow.SingleVehicle,
            Some(paymentMethodInfo),
            mockStatusToken
          )

        val expected = PaymentInfo(
          method = mockPaymentMethod,
          paymentCurrency = "USD",
          paymentAmount = 100,
          paymentAmountUSD = 300,
          accountingEntity =
            Some(AccountingEntity(merchantOfRecord = 5632, rateContract = 31001, revenue = 5674, None)),
          siteExchangeRate = Some(3),
          destinationCurrency = None,
          destinationExchangeRate = Some(5),
          rateQuoteId = Some(0),
          paymentOption = Some(1),
          gateway = Some(Gateway.Adyen),
          points = Vector(
            Points(
              pointType = PointsType.RMMiles,
              pointAttributes = PointsAttributes(currency = "USD", amount = 50, None, None)
            )
          ),
          isRedirect = Some(paymentMethodInfo.redirect),
          timeoutMinutes = paymentMethodInfo.timeoutMin,
          paymentCategoryId = Some(2),
          gatewayId = Some(8),
          gatewayInfoId = Some(123),
          mpiId = Some(101)
        )

        result.gateway shouldBe expected.gateway
        result.gatewayId shouldBe expected.gatewayId
        result.gatewayInfoId shouldBe expected.gatewayInfoId
        result.mpiId shouldBe expected.mpiId
      }
    }
    "mapPaymentRedirect" should {
      val gatewayRedirectWithItneraryId = GatewayReferenceRedirect(
        returnUrl =
          "http://www.agoda.com/success?statusToken=PlaceHolderStatusToken&param2={param2}&itineraryId=PlaceHolderItineraryId",
        cancelUrl =
          "http://www.agoda.com/cancel?itineraryId=PlaceHolderItineraryId&statusToken=PlaceHolderStatusToken&param2={param2}",
        productName = "hotel"
      )

      val statusToken = StatusToken(1L, 2L, Set.empty, "dev", topic = Some(1))
      val gatewayRedirect = GatewayReferenceRedirect(
        returnUrl = "http://www.agoda.com/success?statusToken=PlaceHolderStatusToken&param2={param2}",
        cancelUrl = "http://www.agoda.com/cancel?statusToken=PlaceHolderStatusToken&param2={param2}",
        productName = "flight"
      )

      "return correct formatted URL" in {
        val expected = PaymentRedirect(
          successUrl =
            s"http://www.agoda.com/success?statusToken=${statusToken.serialize(logBookingCreationLogMessageBaseStub, measureStub)}&param2={param2}",
          cancelUrl =
            s"http://www.agoda.com/cancel?statusToken=${statusToken.serialize(logBookingCreationLogMessageBaseStub, measureStub)}&param2={param2}"
        )
        val result = mapPaymentRedirect(gatewayRedirect, statusToken)
        result shouldBe expected
      }

      "return correct formatted URL with PlaceHolderItineraryId" in {
        val expected = PaymentRedirect(
          successUrl = s"http://www.agoda.com/success?statusToken=${statusToken
              .serialize(logBookingCreationLogMessageBaseStub, measureStub)}&param2={param2}&itineraryId=${statusToken.itineraryId.toString}",
          cancelUrl =
            s"http://www.agoda.com/cancel?itineraryId=${statusToken.itineraryId.toString}&statusToken=${statusToken
                .serialize(logBookingCreationLogMessageBaseStub, measureStub)}&param2={param2}"
        )
        val result = mapPaymentRedirect(gatewayRedirectWithItneraryId, statusToken)
        result shouldBe expected
      }
    }

    "getPayment3DSRequestFrom bookingPayment and statusToken" should {
      val mockPayment                  = mock[BookingPayment]
      val mockPaymentMethod            = PaymentMethod.Visa
      val mockCreditCard               = mock[CreditCard](Mockito.RETURNS_DEEP_STUBS)
      val mockWhiteLabelInfo           = mock[WhiteLabelInfo]
      val mockWhiteLabelFeaturesConfig = mock[FeaturesConfiguration]
      val mockOverridePlaceHolder      = mock[OverridePlaceHolder]

      when(mockWhiteLabelInfo.feature).thenReturn(mockWhiteLabelFeaturesConfig)
      when(mockWhiteLabelFeaturesConfig.overridePlaceHolder).thenReturn(mockOverridePlaceHolder)
      when(mockOverridePlaceHolder.statusToken3dsTopic).thenReturn(None)

      val statusTokenWithAnyTopic =
        StatusToken(itineraryId = 1L, actionId = 2L, productType = Set.empty, dc = "dev", topic = Some(1))
      val statusTokenWithRequired3ds2Topic =
        StatusToken(itineraryId = 1L, actionId = 2L, productType = Set.empty, dc = "dev", topic = Some(5))
      val statusTokenWithRequired3ds1Topic =
        StatusToken(itineraryId = 1L, actionId = 2L, productType = Set.empty, dc = "dev", topic = Some(2))

      val defaultPayment3DSRequest = Payment3DSRequest(
        payment3DSOption = Payment3DSOption.Auto_3DS2,
        acceptHeader = "*/*",
        userAgent =
          "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_4) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1 Safari/605.1.15",
        language = Some(Some("TH")),
        colorDepth = Some(24),
        javaEnabled = Some(false),
        javaScriptEnabled = Some(false),
        screenWidth = Some(1536),
        screenHeight = Some(723),
        timeZoneOffset = Some(0),
        returnUrl = Some(
          "https://www.agoda.com/threeDS2ChallengeNotification?statusToken=PlaceHolderStatusToken&params={randomParams}"
        ),
        deviceCollectionUrl = Some(
          "https://www.agoda.com/threeDS2DeviceCollection?statusToken=PlaceHolderStatusToken&params={randomParams}"
        ),
        bankCallback3DS1Url =
          Some("https://www.agoda.com/threeDS1Notification?statusToken=PlaceHolderStatusToken&params={randomParams}")
      )

      val defaultExpectedPayment3DSRequest = Payment3DSRequest(
        payment3DSOption = Payment3DSOption.Auto_3DS2,
        acceptHeader = "*/*",
        userAgent =
          "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_4) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1 Safari/605.1.15",
        language = Some(Some("TH")),
        colorDepth = Some(24),
        javaEnabled = Some(false),
        javaScriptEnabled = Some(false),
        screenWidth = Some(1536),
        screenHeight = Some(723),
        timeZoneOffset = Some(0),
        returnUrl = Some(
          s"https://www.agoda.com/threeDS2ChallengeNotification?statusToken=${statusTokenWithRequired3ds2Topic
              .serialize(logBookingCreationLogMessageBaseStub, measureStub)}&params={randomParams}"
        ),
        deviceCollectionUrl = Some(
          s"https://www.agoda.com/threeDS2DeviceCollection?statusToken=${statusTokenWithRequired3ds2Topic.serialize(logBookingCreationLogMessageBaseStub, measureStub)}&params={randomParams}"
        ),
        bankCallback3DS1Url = Some(
          s"https://www.agoda.com/threeDS1Notification?statusToken=${statusTokenWithRequired3ds1Topic.serialize(logBookingCreationLogMessageBaseStub, measureStub)}&params={randomParams}"
        )
      )

      "return Payment3DSRequest with formatted Urls" in {

        when(mockCreditCard.payment3DS).thenReturn(Some(defaultPayment3DSRequest))
        when(mockPayment.creditCard).thenReturn(Some(mockCreditCard))

        val actualPayment3DSRequest: Option[Payment3DSRequest] =
          getPayment3DSRequestFrom(mockPayment, statusTokenWithAnyTopic, mockWhiteLabelInfo)

        actualPayment3DSRequest shouldBe Some(defaultExpectedPayment3DSRequest)
      }

      "return Payment3DSRequest with formatted Urls and override statusTokenTopic from whitelabel service" in {

        when(mockCreditCard.payment3DS).thenReturn(Some(defaultPayment3DSRequest))
        when(mockPayment.creditCard).thenReturn(Some(mockCreditCard))
        when(mockOverridePlaceHolder.statusToken3dsTopic).thenReturn(Some(BAM_Topic_AwaitPaymentToken.value))

        val statusTokenWithRequired3ds1Topic =
          StatusToken(itineraryId = 1L, actionId = 2L, productType = Set.empty, dc = "dev", topic = Some(9))

        val expectedPayment3DSRequest: Option[Payment3DSRequest] = Some(
          defaultExpectedPayment3DSRequest.copy(
            returnUrl = Some(
              s"https://www.agoda.com/threeDS2ChallengeNotification?statusToken=${statusTokenWithRequired3ds2Topic
                  .serialize(logBookingCreationLogMessageBaseStub, measureStub)}&params={randomParams}"
            ),
            deviceCollectionUrl = Some(
              s"https://www.agoda.com/threeDS2DeviceCollection?statusToken=${statusTokenWithRequired3ds2Topic.serialize(logBookingCreationLogMessageBaseStub, measureStub)}&params={randomParams}"
            ),
            bankCallback3DS1Url = Some(
              s"https://www.agoda.com/threeDS1Notification?statusToken=${statusTokenWithRequired3ds1Topic.serialize(logBookingCreationLogMessageBaseStub, measureStub)}&params={randomParams}"
            )
          )
        )

        val actualPayment3DSRequest: Option[Payment3DSRequest] =
          getPayment3DSRequestFrom(mockPayment, statusTokenWithAnyTopic, mockWhiteLabelInfo)

        actualPayment3DSRequest shouldBe expectedPayment3DSRequest
      }

      "return Payment3DSRequest with PlaceHolderItineraryId Urls" in {

        val payment3DSRequestWithItineraryPlaceHolder = Some(
          defaultPayment3DSRequest.copy(
            returnUrl = Some(
              "https://www.agoda.com/threeDS2ChallengeNotification?statusToken=PlaceHolderStatusToken&params={randomParams}&itineraryId=PlaceHolderItineraryId"
            ),
            deviceCollectionUrl = Some(
              "https://www.agoda.com/threeDS2DeviceCollection?statusToken=PlaceHolderStatusToken&itineraryId=PlaceHolderItineraryId&params={randomParams}"
            ),
            bankCallback3DS1Url = Some(
              "https://www.agoda.com/threeDS1Notification?itineraryId=PlaceHolderItineraryId&statusToken=PlaceHolderStatusToken&params={randomParams}"
            )
          )
        )

        when(mockCreditCard.payment3DS).thenReturn(payment3DSRequestWithItineraryPlaceHolder)
        when(mockPayment.creditCard).thenReturn(Some(mockCreditCard))
        when(mockOverridePlaceHolder.statusToken3dsTopic).thenReturn(None)

        val expectedWithItineraryId = Some(
          defaultExpectedPayment3DSRequest.copy(
            returnUrl = Some(
              s"https://www.agoda.com/threeDS2ChallengeNotification?statusToken=${statusTokenWithRequired3ds2Topic
                  .serialize(logBookingCreationLogMessageBaseStub, measureStub)}&params={randomParams}&itineraryId=${statusTokenWithRequired3ds2Topic.itineraryId}"
            ),
            deviceCollectionUrl = Some(
              s"https://www.agoda.com/threeDS2DeviceCollection?statusToken=${statusTokenWithRequired3ds2Topic
                  .serialize(logBookingCreationLogMessageBaseStub, measureStub)}&itineraryId=${statusTokenWithRequired3ds2Topic.itineraryId}&params={randomParams}"
            ),
            bankCallback3DS1Url = Some(
              s"https://www.agoda.com/threeDS1Notification?itineraryId=${statusTokenWithRequired3ds2Topic.itineraryId}&statusToken=${statusTokenWithRequired3ds1Topic
                  .serialize(logBookingCreationLogMessageBaseStub, measureStub)}&params={randomParams}"
            )
          )
        )

        val actualPayment3DSRequest: Option[Payment3DSRequest] =
          getPayment3DSRequestFrom(mockPayment, statusTokenWithAnyTopic, mockWhiteLabelInfo)

        actualPayment3DSRequest shouldBe expectedWithItineraryId
      }

      "return Payment3DSRequest with Urls without statusToken" in {
        val payment3DSRequest: Option[Payment3DSRequest] = Some(
          defaultPayment3DSRequest.copy(
            returnUrl = Some("https://www.agoda.com/threeDS2ChallengeNotification?params={randomParams}"),
            deviceCollectionUrl = Some("https://www.agoda.com/threeDS2DeviceCollection?params={randomParams}"),
            bankCallback3DS1Url = Some("https://www.agoda.com/threeDS1Notification?params={randomParams}")
          )
        )
        when(mockCreditCard.payment3DS).thenReturn(payment3DSRequest)
        when(mockPayment.creditCard).thenReturn(Some(mockCreditCard))
        val expectedPayment3DSRequest: Option[Payment3DSRequest] = Some(
          defaultExpectedPayment3DSRequest.copy(
            returnUrl = Some(s"https://www.agoda.com/threeDS2ChallengeNotification?params={randomParams}"),
            deviceCollectionUrl = Some(s"https://www.agoda.com/threeDS2DeviceCollection?params={randomParams}"),
            bankCallback3DS1Url = Some(s"https://www.agoda.com/threeDS1Notification?params={randomParams}")
          )
        )

        val actualPayment3DSRequest: Option[Payment3DSRequest] =
          getPayment3DSRequestFrom(mockPayment, statusTokenWithAnyTopic, mockWhiteLabelInfo)

        actualPayment3DSRequest shouldBe expectedPayment3DSRequest
      }

      "return Payment3DSRequest with empty urls" in {
        val payment3DSRequest: Option[Payment3DSRequest] = Some(
          defaultPayment3DSRequest.copy(
            returnUrl = None,
            deviceCollectionUrl = None,
            bankCallback3DS1Url = None
          )
        )
        when(mockCreditCard.payment3DS).thenReturn(payment3DSRequest)
        when(mockPayment.creditCard).thenReturn(Some(mockCreditCard))
        val expectedPayment3DSRequest: Option[Payment3DSRequest] = Some(
          defaultExpectedPayment3DSRequest.copy(
            returnUrl = Some(""),
            deviceCollectionUrl = Some(""),
            bankCallback3DS1Url = Some("")
          )
        )

        val actualPayment3DSRequest: Option[Payment3DSRequest] =
          getPayment3DSRequestFrom(mockPayment, statusTokenWithAnyTopic, mockWhiteLabelInfo)

        actualPayment3DSRequest shouldBe expectedPayment3DSRequest
      }

    }
  }
}
