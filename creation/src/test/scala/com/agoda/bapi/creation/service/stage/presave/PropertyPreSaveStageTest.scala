package com.agoda.bapi.creation.service.stage.presave

import com.agoda.bapi.common.config.{CancellationChargeConfig, DmcCancellationChargeConfig, KillSwitches, NhaHotelIdMappingConfig}
import com.agoda.bapi.common.handler.{MeasurementsContext, RequestContext}
import com.agoda.bapi.common.message.creation.{CreateBookingResponse, Customer, HotelGuest, Products, PropertyItem, SpecialRequests}
import com.agoda.bapi.common.model.WhiteLabel.WhiteLabel
import com.agoda.bapi.common.model.multiproduct.MultiProductInfoDBModel
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{ActionId, ItineraryId, PaymentMethodFromDB, WhiteLabel}
import com.agoda.bapi.common.proxy.EnigmaApiProxy
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.exception.BillingInfoSerializationException
import com.agoda.bapi.creation.mapper.ebe.SaveBookingMapper
import com.agoda.bapi.creation.model.multi.{EmptyProductReservedIds, MultiProductsRequest, PreSaveProductStageRequest, PropertySaveStageResponse, ReservedIds}
import com.agoda.bapi.creation.model.payment.SupplierSpecificData
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.bapi.creation.model.CreateRequest
import com.agoda.bapi.creation.repository.{BaseBookingRepository, CreationCdbRepository, FlightBookingRepository, PaymentMethodRepository}
import com.agoda.bapi.creation.service.HadoopMessagingService
import com.agoda.bapi.creation.{CreateMultiBookingHelper, ExternalDIHelper}
import com.agoda.capi.enigma.shared_model.booking.{BookingCustomer, BookingPax}
import com.agoda.capi.enigma.shared_model.itinerary.billinginfo.ItineraryBillingInfo
import com.agoda.mpb.common.BookingType.BookingType
import com.agoda.mpb.common.MultiProductType
import com.agoda.mpb.common.errors.ErrorCode
import com.softwaremill.quicklens._
import mocks.DBBookingModelHelper
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers.{eq => eqTo, _}
import org.mockito.Mockito
import org.mockito.Mockito._
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{BeforeAndAfterEach, OptionValues}
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Failure

class PropertyPreSaveStageTest
    extends AsyncWordSpec
    with MockitoSugar
    with Matchers
    with BeforeAndAfterEach
    with CreateMultiBookingHelper
    with DBBookingModelHelper
    with ExternalDIHelper
    with OptionValues {
  implicit val killSwitches: KillSwitches                       = mock[KillSwitches]
  implicit val nhaHotelIdMappingConfig: NhaHotelIdMappingConfig = mock[NhaHotelIdMappingConfig]
  val ycsCancellationConfig                                     = DmcCancellationChargeConfig(332, 2)
  val cancellationChargeConfig                                  = CancellationChargeConfig(1, Seq(ycsCancellationConfig))
  val propertyMapper                                            = mock[SaveBookingMapper]
  val workflowRepository                                        = mock[FlightBookingRepository]
  val enigmaApiProxy                                            = mock[EnigmaApiProxy]
  val paymentMethodRepository                                   = mock[PaymentMethodRepository]
  val hadoopMessaging                                           = mock[HadoopMessagingService]
  val baseBookingRepository                                     = mock[BaseBookingRepository]
  val creationCdbRepository                                     = mock[CreationCdbRepository]
  val featureAware                                              = mock[FeatureAware]

  when(nhaHotelIdMappingConfig.getHotelMappings).thenReturn(Map(1L -> 2L))
  baseRequestContext.copy(featureAware = Some(featureAware))
  when(hadoopMessaging.sendBapiCreateFactLogMessage(any, any, any)).thenReturn(Future.successful(()))
  when(hadoopMessaging.sendBapiCreateBookingMissingGuests(anyInt(), any[Seq[HotelGuest]]()))
    .thenReturn(Future.successful())
  when(
    hadoopMessaging.sendBapiCreateCustomerNameLengthExceeded(
      any[Long],
      any[Long],
      any[Option[Int]],
      any[Int],
      any[Int],
      any[Int],
      any[String],
      any[Boolean]
    )
  )
    .thenReturn(Future.successful(()))
  when(creationCdbRepository.getMasterHotelIdFromJTBMapping(any())).thenReturn(Future.successful(Some(1L)))

  val processor = spy(
    new PropertyPreSaveStage(
      cancellationChargeConfig,
      propertyMapper,
      workflowRepository,
      enigmaApiProxy,
      paymentMethodRepository,
      hadoopMessaging,
      baseBookingRepository,
      creationCdbRepository
    )(executionContext, killSwitches, nhaHotelIdMappingConfig)
  )

  val bookingId   = 100L
  val actionId    = 10L
  val itineraryId = 1000L
  val essInfoId   = 101L

  val baseRequest = PreSaveProductStageRequest(
    request = baseMultiProductReq,
    requestContext = baseRequestContext,
    itineraryInfo = baseItineraryPreSaveInfo,
    multiProductId = Some(baseMultiProductInfo),
    product = baseHotelProduct,
    bookingFlow = BookingFlow.Package,
    productIndex = Some(1)
  )

  implicit val measurementContext  = baseRequest.measurementsContext
  val createRequest: CreateRequest = baseRequest
  val productKey                   = baseRequest.product.info.bapiBooking.productTokenKey
  val guests                       = baseRequest.request.getGuestList(productKey)
  val primaryGuest                 = baseRequest.request.getPrimaryGuest(productKey)
  val enigmaCustomerResponse       = mock[BookingCustomer]
  val enigmaGuestsResponse         = mock[BookingPax]
  val enigmaBillingInfoResponse    = mock[ItineraryBillingInfo]
  val mockError                    = new Exception("mockError")
  val longGuestName =
    "[X]ThisIsRandomGuestNameThatIsVeryLongNaLike105CharactersLong55555555555555555555555555555555555555555555"
  val longFirstName  = longGuestName.replace("[X]", "[F]")
  val longMiddleName = longGuestName.replace("[X]", "[M]")
  val longLastName   = longGuestName.replace("[X]", "[L]")
  val longGuestNameTrimmed =
    "[X]ThisIsRandomGuestNameThatIsVeryLongNaLike105CharactersLong555555555555555555555555555555555555555"
  val longFirstNameTrimmed  = longGuestNameTrimmed.replace("[X]", "[F]")
  val longMiddleNameTrimmed = longGuestNameTrimmed.replace("[X]", "[M]")
  val longLasttNameTrimmed  = longGuestNameTrimmed.replace("[X]", "[L]")

  val bookerAnswer   = "This is a short string that will not be trimmed."
  val multiProductId = 10L
  override def beforeEach: Unit = {
    reset(processor, propertyMapper, workflowRepository, enigmaApiProxy, baseBookingRepository)
  }

  "process" should {
    "return right result correctly" in {
      val expected = PropertySaveStageResponse(
        mockBookingWorkflowAction,
        mockNewPropertyBookingObjectForEbeLite,
        productTokenKey = Some("1")
      )

      when(
        propertyMapper.mapPropertyBookingCreationLocal(
          any[Long],
          any[CreateRequest],
          any[BookingType],
          any[ReservedIds[RoomInfo, EmptyProductReservedIds]],
          any[Option[SupplierSpecificData]],
          any[Long]
        )(any[ExecutionContext])
      ).thenReturn(Future.successful(mockNewPropertyBookingObjectForEbeLite))
      when(
        propertyMapper.mapBookingAction(
          any[CreateRequest],
          any[ItineraryId],
          any[ReservedIds[RoomInfo, EmptyProductReservedIds]],
          any[Option[DateTime]],
          any[Option[DateTime]],
          any[Option[PaymentMethodFromDB]],
          any[Option[Int]],
          any[Option[Int]],
          any[ActionId]
        )(any[ExecutionContext], any[MeasurementsContext], any[RequestContext])
      ).thenReturn(Future.successful(mockBookingWorkflowAction))
      when(workflowRepository.getNextBookingSequenceNumber)
        .thenReturn(Future.successful(bookingId))
      when(workflowRepository.getNextActionIdSequenceNumber).thenReturn(Future.successful(actionId))
      when(baseBookingRepository.getNextBaseBookingEssInfoSequenceNumber).thenReturn(Future.successful(essInfoId))
      when(
        enigmaApiProxy.savePropertyCustomerContact(
          any[Int],
          any[Customer],
          any[HotelGuest],
          any[WhiteLabel],
          any[Option[String]]
        )(any[RequestContext])
      )
        .thenReturn(Future.successful(enigmaCustomerResponse))
      when(enigmaApiProxy.savePropertyGuests(any[Int], any[Seq[HotelGuest]], any[WhiteLabel])(any[RequestContext]))
        .thenReturn(Future.successful(Vector(enigmaGuestsResponse)))
      when(enigmaApiProxy.saveItineraryBillingInfo(any(), any())(any()))
        .thenReturn(Future.successful(enigmaBillingInfoResponse))
      when(paymentMethodRepository.getPaymentMethodInfo(any[Int], any[Int], any[Int]))
        .thenReturn(Future.successful(None))

      val multiProductsRequest = MultiProductsRequest(
        request = baseMultiProductReq,
        requestContext = baseRequestContext,
        properties = Seq(baseHotelProduct),
        flights = Seq.empty,
        vehicles = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        cegFastTracks = Seq.empty,
        addOns = Seq.empty,
        bookingFlow = BookingFlow.Package,
        commonPayment = None,
        isBookingFromCart = None
      )

      val multiProductPreSaveRequest = MultiProductPreSaveRequest(
        multiProductsRequest,
        baseItineraryPreSaveInfo,
        Map(
          Some(MultiProductType.Package) -> MultiProductInfoDBModel(
            multiProductId,
            MultiProductType.Package
          )
        )
      )

      processor.process(multiProductPreSaveRequest).map { result =>
        verify(processor).measuredReservedProductId(eqTo(baseRequest))(eqTo(baseRequest.measurementsContext))
        verify(processor).saveDataToEnigma(
          eqTo(createRequest.request),
          eqTo(createRequest.request.customer),
          eqTo(bookingId),
          eqTo(guests),
          eqTo(primaryGuest),
          eqTo(WhiteLabel.Agoda),
          any(),
          any()
        )(eqTo(baseRequest.measurementsContext), eqTo(baseRequest.requestContext))
        verify(processor)
          .getSupplierData(eqTo(baseRequest.product.info))(eqTo(baseRequest.measurementsContext))

        result.right.get shouldBe List(expected)
      }
    }
    "return left error response correctly" in {
      val expected = CreateBookingResponse.technical(ErrorCode.TechnicalError, Some(mockError))

      when(workflowRepository.getNextBookingSequenceNumber)
        .thenReturn(Future.failed(mockError))

      val multiProductsRequest = MultiProductsRequest(
        request = baseMultiProductReq,
        requestContext = baseRequestContext,
        properties = Seq(baseHotelProduct),
        flights = Seq.empty,
        vehicles = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        cegFastTracks = Seq.empty,
        addOns = Seq.empty,
        bookingFlow = BookingFlow.Package,
        commonPayment = None,
        isBookingFromCart = None
      )

      val multiProductPreSaveRequest = MultiProductPreSaveRequest(
        multiProductsRequest,
        baseItineraryPreSaveInfo,
        Map(
          Some(MultiProductType.Package) -> MultiProductInfoDBModel(
            multiProductId,
            MultiProductType.Package
          )
        )
      )

      processor.process(multiProductPreSaveRequest).map { result =>
        verify(processor).measuredReservedProductId(eqTo(baseRequest))(eqTo(baseRequest.measurementsContext))
        verify(processor, never).saveDataToEnigma(
          eqTo(createRequest.request),
          eqTo(createRequest.request.customer),
          eqTo(bookingId),
          eqTo(guests),
          eqTo(primaryGuest),
          eqTo(WhiteLabel.Agoda),
          any(),
          any()
        )(eqTo(baseRequest.measurementsContext), eqTo(baseRequest.requestContext))
        verify(processor, never).getSupplierData(eqTo(baseRequest.product.info))(eqTo(baseRequest.measurementsContext))

        result shouldBe Left(expected)
      }
    }
    "trim excess guest names before saving to Enigma " in {
      val expected = PropertySaveStageResponse(
        mockBookingWorkflowAction,
        mockNewPropertyBookingObjectForEbeLite,
        productTokenKey = Some("1")
      )
      when(
        propertyMapper.mapPropertyBookingCreationLocal(
          any[Long],
          any[CreateRequest],
          any[BookingType],
          any[ReservedIds[RoomInfo, EmptyProductReservedIds]],
          any[Option[SupplierSpecificData]],
          any[Long]
        )(any[ExecutionContext])
      ).thenReturn(Future.successful(mockNewPropertyBookingObjectForEbeLite))
      when(
        propertyMapper.mapBookingAction(
          any[CreateRequest],
          any[ItineraryId],
          any[ReservedIds[RoomInfo, EmptyProductReservedIds]],
          any[Option[DateTime]],
          any[Option[DateTime]],
          any[Option[PaymentMethodFromDB]],
          any[Option[Int]],
          any[Option[Int]],
          any[ActionId]
        )(any[ExecutionContext], any[MeasurementsContext], any[RequestContext])
      ).thenReturn(Future.successful(mockBookingWorkflowAction))
      when(workflowRepository.getNextBookingSequenceNumber)
        .thenReturn(Future.successful(bookingId))
      when(workflowRepository.getNextActionIdSequenceNumber).thenReturn(Future.successful(actionId))
      when(baseBookingRepository.getNextBaseBookingEssInfoSequenceNumber).thenReturn(Future.successful(essInfoId))
      when(
        enigmaApiProxy.savePropertyCustomerContact(
          any[Int],
          any[Customer],
          any[HotelGuest],
          any[WhiteLabel],
          any[Option[String]]
        )(any[RequestContext])
      )
        .thenReturn(Future.successful(enigmaCustomerResponse))
      when(enigmaApiProxy.savePropertyGuests(any[Int], any[Seq[HotelGuest]], any[WhiteLabel])(any[RequestContext]))
        .thenReturn(Future.successful(Vector(enigmaGuestsResponse)))
      when(enigmaApiProxy.saveItineraryBillingInfo(any(), any())(any()))
        .thenReturn(Future.successful(enigmaBillingInfoResponse))
      when(paymentMethodRepository.getPaymentMethodInfo(any[Int], any[Int], any[Int]))
        .thenReturn(Future.successful(None))
      val featureAwareMock = mock[FeatureAware]

      val guestsWithLongNames = guests
        .modify(_.each)
        .using(_.copy(firstname = longFirstName, middlename = longMiddleName, lastname = longLastName))
      val customerWithLongNames = baseRequest.request.customer.copy(
        firstname = longFirstName,
        middlename = longMiddleName,
        lastname = longLastName
      )
      val requestWithLongGuestNames = baseRequest
        .modify(_.request.customer)
        .setTo(customerWithLongNames)
        .modify(_.request.products.propertyItems.each.each.guests.each)
        .setTo(guestsWithLongNames)
        .modify(_.requestContext.featureAware)
        .setTo(Some(featureAwareMock))

      val expectedCustomerTrimmed = baseRequest.request.customer.copy(
        firstname = longFirstNameTrimmed,
        middlename = longMiddleNameTrimmed,
        lastname = longLasttNameTrimmed
      )
      val expectedPrimaryGuestTrimmed = primaryGuest.copy(
        firstname = longFirstNameTrimmed,
        middlename = longMiddleNameTrimmed,
        lastname = longLasttNameTrimmed
      )
      val expectedGuestsTrimmed = guests
        .modify(_.each)
        .using(
          _.copy(firstname = longFirstNameTrimmed, middlename = longMiddleNameTrimmed, lastname = longLasttNameTrimmed)
        )

      val multiProductsRequest = MultiProductsRequest(
        request = baseMultiProductReq,
        requestContext = baseRequestContext,
        properties = Seq(requestWithLongGuestNames.product),
        flights = Seq.empty,
        vehicles = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        cegFastTracks = Seq.empty,
        addOns = Seq.empty,
        bookingFlow = BookingFlow.Package,
        commonPayment = None,
        isBookingFromCart = None
      )

      val multiProductPreSaveRequest = MultiProductPreSaveRequest(
        multiProductsRequest.copy(request = requestWithLongGuestNames.request),
        baseItineraryPreSaveInfo,
        Map(
          Some(MultiProductType.Package) -> MultiProductInfoDBModel(
            multiProductId,
            MultiProductType.Package
          )
        )
      )

      processor.process(multiProductPreSaveRequest).map { result =>
        val measurementsContext = requestWithLongGuestNames.measurementsContext
        verify(processor).saveDataToEnigma(
          any(),
          eqTo(expectedCustomerTrimmed),
          eqTo(bookingId),
          eqTo(expectedGuestsTrimmed),
          eqTo(expectedPrimaryGuestTrimmed),
          eqTo(WhiteLabel.Agoda),
          any(),
          any()
        )(eqTo(measurementsContext), any())

        result.right.get shouldBe Seq(expected)
      }
    }
  }

  "reservedProductId" should {
    "return correct response" in {
      val expected = ReservedIds(
        bookingId = bookingId,
        actionId = actionId,
        multiProductId = baseRequest.multiProductId.map(_.multiProductId),
        product = baseRequest.product,
        essInfoId = Some(essInfoId)
      )
      when(workflowRepository.getNextBookingSequenceNumber)
        .thenReturn(Future.successful(bookingId))
      when(workflowRepository.getNextActionIdSequenceNumber).thenReturn(Future.successful(actionId))
      when(baseBookingRepository.getNextBaseBookingEssInfoSequenceNumber).thenReturn(Future.successful(essInfoId))
      processor.measuredReservedProductId(baseRequest).map { result =>
        verify(workflowRepository).getNextBookingSequenceNumber
        verify(
          workflowRepository
        ).getNextActionIdSequenceNumber
        verify(baseBookingRepository).getNextBaseBookingEssInfoSequenceNumber
        result shouldBe expected
      }
    }
    "return failed if DB error" in {
      val expected = ReservedIds(
        bookingId = bookingId,
        actionId = actionId,
        multiProductId = baseRequest.multiProductId.map(_.multiProductId),
        product = baseRequest.product,
        essInfoId = Some(essInfoId)
      )
      when(workflowRepository.getNextBookingSequenceNumber)
        .thenReturn(Future.failed(mockError))
      when(workflowRepository.getNextActionIdSequenceNumber).thenReturn(Future.successful(actionId))
      when(baseBookingRepository.getNextBaseBookingEssInfoSequenceNumber).thenReturn(Future.successful(essInfoId))
      processor.measuredReservedProductId(baseRequest).failed.map { result =>
        verify(workflowRepository).getNextBookingSequenceNumber
        verify(
          workflowRepository,
          never
        ).getNextActionIdSequenceNumber
        verify(baseBookingRepository, never).getNextBaseBookingEssInfoSequenceNumber
        result shouldBe mockError
      }
    }
  }

  "saveDataToEnigma" should {
    "return correct response" in {
      when(
        enigmaApiProxy.savePropertyCustomerContact(
          any[Int],
          any[Customer],
          any[HotelGuest],
          any[WhiteLabel],
          any[Option[String]]
        )(any[RequestContext])
      )
        .thenReturn(Future.successful(enigmaCustomerResponse))
      when(enigmaApiProxy.savePropertyGuests(any[Int], any[Seq[HotelGuest]], any[WhiteLabel])(any[RequestContext]))
        .thenReturn(Future.successful(Vector(enigmaGuestsResponse)))
      when(enigmaApiProxy.saveItineraryBillingInfo(any(), any())(any()))
        .thenReturn(Future.successful(enigmaBillingInfoResponse))
      val request = baseRequest.copy(request =
        baseRequest.request
          .copy(payment =
            baseRequest.request.payment.copy(
              requiredFields = Some(Map("postalCode" -> "10200")),
              creditCard = Some(baseRequest.request.payment.creditCard.get.copy(phoneNumber = Some("+6612345678")))
            )
          )
      )
      val expectedBillingEntity = "{\"postalCode\":\"10200\",\"paymentPhone\":\"+6612345678\"}"

      processor
        .saveDataToEnigma(
          request.request,
          request.request.customer,
          bookingId,
          guests,
          primaryGuest,
          WhiteLabel.Agoda,
          itineraryId,
          Some(bookerAnswer)
        )(request.measurementsContext, request.requestContext)
        .map { _ =>
          verify(enigmaApiProxy)
            .savePropertyCustomerContact(
              eqTo(bookingId.toInt),
              eqTo(request.request.customer),
              eqTo(primaryGuest),
              eqTo(WhiteLabel.Agoda),
              eqTo(Some(bookerAnswer))
            )(eqTo(request.requestContext))
          verify(enigmaApiProxy).savePropertyGuests(eqTo(bookingId.toInt), eqTo(guests), any[WhiteLabel])(
            eqTo(request.requestContext)
          )
          verify(enigmaApiProxy)
            .saveItineraryBillingInfo(eqTo(itineraryId.toInt), eqTo(expectedBillingEntity))(
              eqTo(request.requestContext)
            )
          succeed
        }
    }
    "return failed if enigma error" in {
      when(
        enigmaApiProxy.savePropertyCustomerContact(
          any[Int],
          any[Customer],
          any[HotelGuest],
          any[WhiteLabel],
          any[Option[String]]
        )(any[RequestContext])
      )
        .thenReturn(Future.failed(mockError))
      when(enigmaApiProxy.savePropertyGuests(any[Int], any[Seq[HotelGuest]], any[WhiteLabel])(any[RequestContext]))
        .thenReturn(Future.successful(Vector(enigmaGuestsResponse)))

      processor
        .saveDataToEnigma(
          createRequest.request,
          createRequest.request.customer,
          bookingId,
          guests,
          primaryGuest,
          WhiteLabel.Agoda,
          itineraryId,
          Some(bookerAnswer)
        )(createRequest.measurementsContext, createRequest.requestContext)
        .failed
        .map { _ =>
          verify(enigmaApiProxy)
            .savePropertyCustomerContact(
              eqTo(bookingId.toInt),
              eqTo(createRequest.request.customer),
              eqTo(primaryGuest),
              eqTo(WhiteLabel.Agoda),
              eqTo(Some(bookerAnswer))
            )(eqTo(createRequest.requestContext))
          verify(enigmaApiProxy, never).savePropertyGuests(eqTo(bookingId.toInt), eqTo(guests), eqTo(WhiteLabel.Agoda))(
            eqTo(createRequest.requestContext)
          )
          succeed
        }
    }
  }

  "getSupplierData" should {
    "return BreakdownItemId when with dmcConfig" in {
      val expected = Map("1" -> ycsCancellationConfig.breakdownItemIdForCxlCharge)
      val result =
        new PropertyPreSaveStage(
          cancellationChargeConfig,
          propertyMapper,
          workflowRepository,
          enigmaApiProxy,
          paymentMethodRepository,
          hadoopMessaging,
          baseBookingRepository,
          creationCdbRepository
        ).getSupplierData(baseHotelProduct.info)
      result shouldBe SupplierSpecificData(expected)
    }
    "return default BreakdownItemId when without dmcConfig" in {
      val config   = cancellationChargeConfig.copy(dmcConfig = Nil)
      val expected = Map("1" -> cancellationChargeConfig.defaultBreakdownItemIdForCxlCharge)
      val result =
        new PropertyPreSaveStage(
          config,
          propertyMapper,
          workflowRepository,
          enigmaApiProxy,
          paymentMethodRepository,
          hadoopMessaging,
          baseBookingRepository,
          creationCdbRepository
        ).getSupplierData(baseHotelProduct.info)
      result shouldBe SupplierSpecificData(expected)
    }
  }

  "getcancellationChargeConfig" should {
    "return cancellationConfig when with dmcConfig" in {
      val result =
        new PropertyPreSaveStage(
          cancellationChargeConfig,
          propertyMapper,
          workflowRepository,
          enigmaApiProxy,
          paymentMethodRepository,
          hadoopMessaging,
          baseBookingRepository,
          creationCdbRepository
        ).getCancellationChargeItemIdFromConfig(baseHotelProduct.info)
      result shouldBe Some(2)
    }

    "return default cancellationConfig when without dmcConfig" in {
      val config = cancellationChargeConfig.copy(dmcConfig = Nil)
      val result =
        new PropertyPreSaveStage(
          config,
          propertyMapper,
          workflowRepository,
          enigmaApiProxy,
          paymentMethodRepository,
          hadoopMessaging,
          baseBookingRepository,
          creationCdbRepository
        ).getCancellationChargeItemIdFromConfig(baseHotelProduct.info)
      result shouldBe Some(1)
    }
  }

  "saveItineraryBillingInfo" should {
    "call enigma when requiredFields defined" in {
      val requiredFields        = Option(Map("postalCode" -> "10200"))
      val itineraryId           = 1000L
      val expectedBillingEntity = "{\"postalCode\":\"10200\",\"paymentPhone\":\"+6612345678\"}"
      val paymentPhone          = Some("+6612345678")

      when(enigmaApiProxy.saveItineraryBillingInfo(any(), any())(any()))
        .thenReturn(Future.successful(enigmaBillingInfoResponse))

      processor.saveItineraryBillingInfo(requiredFields, itineraryId, paymentPhone)(baseRequestContext).map { _ =>
        verify(enigmaApiProxy).saveItineraryBillingInfo(itineraryId.toInt, expectedBillingEntity)(baseRequestContext)
        succeed
      }
    }

    "call enigma when requiredFields are empty" in {
      val requiredFields        = None
      val itineraryId           = 1000L
      val expectedBillingEntity = "{}"

      when(enigmaApiProxy.saveItineraryBillingInfo(any(), any())(any()))
        .thenReturn(Future.successful(enigmaBillingInfoResponse))

      processor.saveItineraryBillingInfo(requiredFields, itineraryId, paymentPhone = None)(baseRequestContext).map {
        _ =>
          verify(enigmaApiProxy).saveItineraryBillingInfo(itineraryId.toInt, expectedBillingEntity)(baseRequestContext)
          succeed
      }
    }

    "throw BillingInfoSerializationException when serializer error" in {
      val requiredFields = Option(Map("postalCode" -> "error"))
      val itineraryId    = 1000L

      Mockito.doReturn(Failure(new Exception("fail")), Nil: _*).when(processor).toJsonString(any())

      assertThrows[BillingInfoSerializationException](
        processor.saveItineraryBillingInfo(requiredFields, itineraryId, None)(baseRequestContext)
      )
    }
  }

  "getTrimmerBookerAnswer" when {
    val specialRequest = mock[SpecialRequests]

    val propertyItem = mock[PropertyItem]
    when(propertyItem.specialRequestV2) thenReturn Some(specialRequest)

    val products = mock[Products]
    when(products.propertyItems) thenReturn Some(Seq(propertyItem))

    "provided short booker answer (<= 512 characters)" should {
      "return just booker answer" in {
        when(specialRequest.bookerAnswer) thenReturn Some(bookerAnswer)

        val result = processor.getTrimmedBookerAnswer(products)

        result.value shouldBe bookerAnswer
      }
    }

    "provided long booker answer (> 512 characters)" should {
      "return trimmed booker answer" in {
        val longBookerAnswer =
          Stream.continually("This is a long string that will be trimmed.").flatten.take(513).mkString("")

        when(specialRequest.bookerAnswer) thenReturn Some(longBookerAnswer)

        val result = processor.getTrimmedBookerAnswer(products)

        result.value shouldBe longBookerAnswer.dropRight(1)
        result.value.length shouldBe 512
      }
    }
  }

  "swapHotelIdInReservedIds" should {
    "return original reservedIds when hotel ID is not found in mappings" in {
      val originalPropertyId = 999L
      val reservedIds: ReservedIds[RoomInfo, EmptyProductReservedIds] = ReservedIds(
        bookingId = bookingId,
        actionId = actionId,
        multiProductId = Some(multiProductId),
        product = baseHotelProduct.modify(_.info.bapiBooking.propertyId).setTo(originalPropertyId),
        essInfoId = Some(essInfoId)
      )
      when(nhaHotelIdMappingConfig.getHotelMappings).thenReturn(Map(1L -> 2L))

      val result = processor.swapHotelIdInReservedIds(reservedIds)

      result shouldBe reservedIds
      result.product.info.bapiBooking.propertyId shouldBe originalPropertyId
    }

    "swap hotel ID when mapping exists" in {
      val masterHotelId = 1L
      val childHotelId  = 2L
      val reservedIds: ReservedIds[RoomInfo, EmptyProductReservedIds] = ReservedIds(
        bookingId = bookingId,
        actionId = actionId,
        multiProductId = Some(multiProductId),
        product = baseHotelProduct.modify(_.info.bapiBooking.propertyId).setTo(masterHotelId),
        essInfoId = Some(essInfoId)
      )
      when(nhaHotelIdMappingConfig.getHotelMappings).thenReturn(Map(masterHotelId -> childHotelId))

      val expectedBooking = reservedIds.product.info.bapiBooking.booking.map(bookingItem =>
        bookingItem.copy(
          booking = bookingItem.booking.map(ebeBooking =>
            ebeBooking.copy(
              hotel = ebeBooking.hotel.map(ebeHotel =>
                ebeHotel.copy(
                  room = ebeHotel.room.map(room => room.copy(hotelId = childHotelId))
                )
              )
            )
          )
        )
      )

      val result = processor.swapHotelIdInReservedIds(reservedIds)

      result.product.info.bapiBooking.propertyId shouldBe childHotelId
      result.product.info.bapiBooking.booking shouldBe expectedBooking
    }

    "preserve other fields when swapping hotel ID" in {
      val masterHotelId = 1L
      val childHotelId  = 2L
      val originalReservedIds: ReservedIds[RoomInfo, EmptyProductReservedIds] = ReservedIds(
        bookingId = bookingId,
        actionId = actionId,
        multiProductId = Some(multiProductId),
        product = baseHotelProduct.modify(_.info.bapiBooking.propertyId).setTo(masterHotelId),
        essInfoId = Some(essInfoId)
      )
      when(nhaHotelIdMappingConfig.getHotelMappings).thenReturn(Map(masterHotelId -> childHotelId))

      val expectedBooking = originalReservedIds.product.info.bapiBooking.booking.map(bookingItem =>
        bookingItem.copy(
          booking = bookingItem.booking.map(ebeBooking =>
            ebeBooking.copy(
              hotel = ebeBooking.hotel.map(ebeHotel =>
                ebeHotel.copy(
                  room = ebeHotel.room.map(room => room.copy(hotelId = childHotelId))
                )
              )
            )
          )
        )
      )

      val result = processor.swapHotelIdInReservedIds(originalReservedIds)

      result.bookingId shouldBe originalReservedIds.bookingId
      result.actionId shouldBe originalReservedIds.actionId
      result.multiProductId shouldBe originalReservedIds.multiProductId
      result.essInfoId shouldBe originalReservedIds.essInfoId
      result.product.info.bapiBooking.booking shouldBe expectedBooking
    }
  }
}
