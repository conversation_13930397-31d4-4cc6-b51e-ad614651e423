package com.agoda.bapi.creation.repository

import com.agoda.adp.messaging.message.Message
import com.agoda.bapi.common.exception.DbException
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.{ActivityPaxMetaInfo, BookingElement, DuplicateBooking}
import com.agoda.bapi.common.message.{ActivityBookingStateWithItinerary, ProductStateWithItinerary}
import com.agoda.bapi.common.model.ItineraryId
import com.agoda.bapi.common.model.booking._
import com.agoda.bapi.common.model.flight.flightModel.Breakdown
import com.agoda.bapi.common.model.multiproduct.{MultiProductBookingGroupDBModel, MultiProductInfoDBModel}
import com.agoda.bapi.common.proxy.EnigmaApiProxy
import com.agoda.bapi.common.util.converters.CommonProductConverters
import com.agoda.bapi.common.{MessageService, MockRequestContext}
import com.agoda.bapi.creation.config.ReplicateStateConfig
import com.agoda.bapi.creation.model.activity.messaging.ActivityBookingMessage
import com.agoda.bapi.creation.model.db.DuplicateBookingRecord
import com.agoda.bapi.creation.proxy.db.basebooking.ActivityDbReadProxy
import com.agoda.bapi.creation.proxy.{ActivityDbProxy, BaseBookingDBProxy}
import com.agoda.capi.enigma.client.booking.BaseBookingPaxService
import com.agoda.capi.enigma.shared_model.booking.CustomerBillingInformation
import com.agoda.capi.enigma.shared_model.booking.pax.BaseBookingPax
import com.agoda.commons.agprotobuf.scalapb.utils.ProtoConverter
import com.agoda.commons.config.dynamic.DynamicObject
import com.agoda.mpb.common.MultiProductType
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpbe.state.booking.BaseBooking
import com.agoda.mpbe.state.itinerary.ItineraryState
import com.agoda.mpbe.state.product.ProductModel
import com.agoda.mpbe.state.product.common.FinancialBreakdown
import com.softwaremill.quicklens._
import mocks.FlightModelMock.defaultFlightModel.bookingId
import mocks.{ActivityModelMock, DBBookingModelHelper}
import org.joda.time.DateTime
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.read
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{AppendedClues, BeforeAndAfterEach, OptionValues}
import org.scalatestplus.mockito.MockitoSugar

import java.sql.{SQLException, SQLTimeoutException}
import java.time.LocalDate
import java.util.Date
import scala.concurrent.Future

class ActivityBookingRepositoryTest
    extends AsyncWordSpec
    with Matchers
    with MockitoSugar
    with AppendedClues
    with BeforeAndAfterEach
    with DBBookingModelHelper
    with ActivityModelMock
    with OptionValues {

  val activityDbProxyMock: ActivityDbProxy               = mock[ActivityDbProxy]
  val hadoopServiceMock: MessageService                  = mock[MessageService]
  val enigmaApiProxy: EnigmaApiProxy                     = mock[EnigmaApiProxy]
  val baseBookingDbProxy: BaseBookingDBProxy             = mock[BaseBookingDBProxy]
  val activityBkgDbProxy: ActivityDbReadProxy            = mock[ActivityDbReadProxy]
  val activityLocalBkgDbProxy: ActivityDbReadProxy       = mock[ActivityDbReadProxy]
  val multiProductRepositoryMock: MultiProductRepository = mock[MultiProductRepository]
  val mockKillSwitch: DynamicObject[Boolean]             = mock[DynamicObject[Boolean]]
  val replicateConfig: ReplicateStateConfig              = ReplicateStateConfig(mockKillSwitch)

  implicit val requestContext: RequestContext = MockRequestContext.create()

  val baseBookingPaxClientMock: BaseBookingPaxService = mock[BaseBookingPaxService]

  val nextBookingId  = 51229064L
  val nextActivityId = 62330075L

  val baseBooking: BaseBooking = BaseBooking.defaultInstance

  val mockItineraryState: ItineraryState =
    ItineraryState(product = ProductModel(activities = Seq(mockActivityProductModel)))

  val multiProductBookingGroupDBModel = MultiProductBookingGroupDBModel(
    bookingId = defaultBookingId,
    itineraryId = -2,
    cartId = -3,
    packageId = Some(-4)
  )

  val mockMultiProductInfos = Seq(MultiProductInfoDBModel(1, MultiProductType.SingleActivity))
  val mockActivityState: ActivityBookingStateWithItinerary = ActivityBookingStateWithItinerary(
    itinerary = mockItineraryInternalModel.itinerary,
    itineraryHistories = mockItineraryInternalModel.history,
    payments = mockItineraryInternalModel.payments,
    bookingPayments = mockItineraryInternalModel.bookingPayments,
    activities = Seq(mockActivityProductModel),
    multiProductBookingGroups = Seq(multiProductBookingGroupDBModel),
    relationships = Nil
  )

  var activityBookingRepository: ActivityBookingRepositoryImpl = _

  override def beforeEach: Unit = {
    reset(
      activityDbProxyMock,
      enigmaApiProxy,
      multiProductRepositoryMock,
      baseBookingDbProxy,
      activityBkgDbProxy,
      activityLocalBkgDbProxy
    )

    when(mockKillSwitch.get).thenReturn(true)
    when(activityDbProxyMock.getAwaitingOrCancelledActivityBookingActions).thenReturn(Future.successful(Nil))

    activityBookingRepository = new ActivityBookingRepositoryImpl(
      activityDbProxyMock,
      enigmaApiProxy,
      baseBookingDbProxy,
      activityBkgDbProxy,
      activityLocalBkgDbProxy,
      multiProductRepositoryMock,
      hadoopServiceMock,
      replicateConfig
    )
  }

  "cacheMaximumSize" should {
    "return None always" in {
      activityBookingRepository.cacheMaximumSize shouldBe empty
    }
  }

  "saveBaseBookingPaxesToCAPI" should {
    "call enigmaApiProxy" in {
      val baseBookingPax: BaseBookingPax =
        activityBookingRepository.mapActivityToBasePax(defaultBookingId, defaultActivityPax)

      val argumentCaptor = ArgumentCaptor.forClass(classOf[Seq[BaseBookingPax]])
      when(enigmaApiProxy.saveBaseBookingPaxes(any[Seq[BaseBookingPax]])(any[RequestContext]))
        .thenReturn(Future.successful(Seq(baseBookingPax)))

      activityBookingRepository.savePaxToEnigma(defaultBookingId, Seq(defaultActivityPax)).map { _ =>
        verify(enigmaApiProxy).saveBaseBookingPaxes(argumentCaptor.capture())(any[RequestContext])
        val pax: Seq[BaseBookingPax] = argumentCaptor.getValue
        pax.head.copy(id = baseBookingPax.id) shouldBe baseBookingPax
        pax.head.passportExpiryLocal.value shouldEqual LocalDate.of(2020, 10, 15)
        pax.head.birthDateLocal.value shouldEqual LocalDate.of(2020, 10, 15)
      }
    }
  }

  "getPaxFromEnigma" should {
    "should take booking id and return Seq[BaseBookingPax] " in {
      val baseBookingPax: BaseBookingPax =
        activityBookingRepository.mapActivityToBasePax(defaultBookingId, defaultActivityPax)

      when(enigmaApiProxy.getBaseBookingPaxByBookingId(any[Int])(any[RequestContext]))
        .thenReturn(Future.successful(Seq(baseBookingPax)))

      activityBookingRepository.getPaxFromEnigma(defaultBookingId).map { result =>
        verify(enigmaApiProxy).getBaseBookingPaxByBookingId(defaultBookingId)
        result shouldBe Seq(baseBookingPax)
      }
    }
  }

  "saveBaseBookingPax" should {
    "map to Base Pax" in {
      val baseBookingPax = activityBookingRepository.mapActivityToBasePax(defaultBookingId, defaultActivityPax)
      baseBookingPax.bookingId shouldBe defaultBookingId
      baseBookingPax.emailAddress shouldBe defaultActivityPax.email
      baseBookingPax.paxType shouldBe defaultActivityPax.paxType.id
      baseBookingPax.passportCountryId shouldBe defaultActivityPax.nationalityId
    }

    "de/serialise meta" in {
      val baseBookingPax                        = activityBookingRepository.mapActivityToBasePax(defaultBookingId, defaultActivityPax)
      implicit val formats: DefaultFormats.type = DefaultFormats

      baseBookingPax.baseMetadata.get should (include("Height") and include("Weight"))
      val meta = read[Seq[ActivityPaxMetaInfo]](baseBookingPax.baseMetadata.get)
      meta shouldBe defaultActivityPax.meta
    }
  }

  "saveCustomerBillingInformation" should {
    "map to CustomerBookingInformation" in {
      val customerBillingInfo =
        activityBookingRepository.mapCustomerBillingInfo(
          defaultBookingId,
          defaultCustomer,
          defaultActivityBookingAnswers,
          defaultCreditCard
        )
      customerBillingInfo.bookingId shouldBe defaultBookingId
      customerBillingInfo.title.get shouldBe defaultCustomer.title
      customerBillingInfo.firstName.get shouldBe defaultCustomer.firstname
      customerBillingInfo.middleName.get shouldBe defaultCustomer.middlename
      customerBillingInfo.lastName.get shouldBe defaultCustomer.lastname
      customerBillingInfo.email.get shouldBe defaultCustomer.email
      customerBillingInfo.phone.get shouldBe defaultCustomer.phoneFormat
      customerBillingInfo.billingAddress1 shouldBe "some address"
      customerBillingInfo.billingAddress2 shouldBe defaultCreditCard.map(_.billingAddress2).getOrElse("")
      customerBillingInfo.billingPostalCode shouldBe "some zipcode"
      customerBillingInfo.billingCity shouldBe "some city"
      customerBillingInfo.billingState shouldBe defaultCreditCard.map(_.billingState).getOrElse("")
      customerBillingInfo.billingCountry shouldBe defaultCustomer.countryId.toString
    }

    "call enigmaApiProxy" in {
      val customerBillingInfo =
        activityBookingRepository.mapCustomerBillingInfo(
          defaultBookingId,
          defaultCustomer,
          defaultActivityBookingAnswers,
          defaultCreditCard
        )

      val argumentCaptor = ArgumentCaptor.forClass(classOf[CustomerBillingInformation])
      when(enigmaApiProxy.saveCustomerBillingInfo(any[CustomerBillingInformation])(any[RequestContext]))
        .thenReturn(Future.successful(customerBillingInfo))
      activityBookingRepository
        .saveCustomerBillingInfoToEnigma(
          defaultBookingId,
          defaultCustomer,
          defaultActivityBookingAnswers,
          defaultCreditCard
        )
        .map { _ =>
          verify(enigmaApiProxy).saveCustomerBillingInfo(argumentCaptor.capture())(any())
          val defaultCustomer: CustomerBillingInformation = argumentCaptor.getValue
          defaultCustomer.bookingId shouldBe defaultBookingId
        }
    }
  }

  "insertActivity" should {
    "call proxy" in {
      when(activityDbProxyMock.insertActivity(any())).thenReturn(Future.successful(mockActivityState))

      activityBookingRepository.insertActivity(mockActivityState) map { _ =>
        verify(activityDbProxyMock, times(1)).insertActivity(any())
        succeed
      }
    }
  }

  "sendActivityModelForReplication" should {
    "call messaging send" in {
      when(hadoopServiceMock.sendMessage(any[Message])).thenReturn(Future.successful(()))
      when(multiProductRepositoryMock.saveMultiProductBookingGroupIfNotExist(multiProductBookingGroupDBModel))
        .thenReturn(Future.successful(multiProductBookingGroupDBModel))

      val expected = BookingStateMessage(
        actionType = defaultBookingWorkflowAction.actionTypeId,
        actionId = defaultBookingWorkflowAction.actionId,
        bookingType = None,
        bookingId = 0,
        schemaVersion = "1",
        flights = Seq.empty,
        slices = Seq.empty,
        segments = Seq.empty,
        passengers = Seq.empty,
        payments = Seq(
          PaymentForMessage(
            referenceId = mockActivityState.payments.head.referenceId,
            paymentId = mockActivityState.payments.head.paymentId,
            itineraryId = mockActivityState.payments.head.itineraryId,
            actionId = mockActivityState.payments.head.actionId,
            creditCardId = mockActivityState.payments.head.creditCardId,
            transactionDate = mockActivityState.payments.head.transactionDate.toDate,
            transactionType = mockActivityState.payments.head.transactionType,
            paymentState = mockActivityState.payments.head.paymentState,
            referenceNo = mockActivityState.payments.head.referenceNo,
            referenceType = mockActivityState.payments.head.referenceType,
            last4Digits = mockActivityState.payments.head.last4Digits,
            paymentMethodId = mockActivityState.payments.head.paymentMethodId,
            gatewayId = mockActivityState.payments.head.gatewayId,
            transactionId = mockActivityState.payments.head.transactionId,
            paymentCurrency = mockActivityState.payments.head.paymentCurrency,
            paymentAmount = mockActivityState.payments.head.paymentAmount,
            amountUsd = mockActivityState.payments.head.amountUsd,
            supplierCurrency = mockActivityState.payments.head.supplierCurrency,
            supplierAmount = mockActivityState.payments.head.supplierAmount,
            exchangeRateSupplierToPayment = mockActivityState.payments.head.exchangeRateSupplierToPayment,
            creditCardCurrency = mockActivityState.payments.head.creditCardCurrency,
            upliftAmount = mockActivityState.payments.head.upliftAmount,
            siteExchangeRate = mockActivityState.payments.head.siteExchangeRate,
            upliftExchangeRate = mockActivityState.payments.head.upliftExchangeRate,
            remark = mockActivityState.payments.head.remark,
            paymentTypeId = mockActivityState.payments.head.paymentTypeId,
            token = mockActivityState.payments.head.token,
            recStatus = mockActivityState.payments.head.recStatus,
            recCreatedWhen = mockActivityState.payments.head.recCreatedWhen.map(_.toDate),
            referencePaymentId = mockActivityState.payments.head.referencePaymentId,
            points = mockActivityState.payments.head.points
          )
        ),
        bookingPayments = Seq(
          BookingPaymentForMessage(
            paymentId = mockActivityState.bookingPayments.head.paymentId,
            bookingId = mockActivityState.bookingPayments.head.bookingId,
            paymentCurrency = mockActivityState.bookingPayments.head.paymentCurrency,
            paymentAmount = mockActivityState.bookingPayments.head.paymentAmount.toDouble,
            amountUsd = mockActivityState.bookingPayments.head.amountUsd.toDouble,
            recStatus = mockActivityState.bookingPayments.head.recStatus,
            recCreatedWhen = mockActivityState.bookingPayments.head.recCreatedWhen.map(_.toDate),
            fxiUplift = mockActivityState.bookingPayments.head.fxiUplift.map(_.toDouble),
            loyaltyPoints = mockActivityState.bookingPayments.head.loyaltyPoints,
            supplierCurrency = mockActivityState.bookingPayments.head.supplierCurrency,
            supplierExchangeRate = mockActivityState.bookingPayments.head.supplierExchangeRate.map(_.toDouble),
            bookingPaymentId = mockActivityState.bookingPayments.head.bookingPaymentId
          )
        ),
        bookingRelationships = Seq.empty,
        breakdown = Seq.empty,
        breakdownPerPax = Seq.empty,
        baggageAllowance = Seq.empty,
        baggage = Seq.empty,
        history = Seq(
          ItineraryHistoryForMessage(
            actionId = mockActivityState.itineraryHistories.head.actionId,
            itineraryId = mockActivityState.itineraryHistories.head.itineraryId.toInt,
            bookingType = mockActivityState.itineraryHistories.head.bookingType,
            bookingId = mockActivityState.itineraryHistories.head.bookingId,
            actionType = mockActivityState.itineraryHistories.head.actionType,
            version = mockActivityState.itineraryHistories.head.version,
            actionDate = mockActivityState.itineraryHistories.head.actionDate.toDate,
            parameters = mockActivityState.itineraryHistories.head.parameters,
            description = mockActivityState.itineraryHistories.head.description,
            recStatus = mockActivityState.itineraryHistories.head.recStatus,
            recCreatedWhen = mockActivityState.itineraryHistories.head.recCreatedWhen.map(_.toDate)
          )
        ),
        summary = Seq.empty,
        paxTickets = Seq.empty,
        itinerary = FlightItineraryForMessage(
          itineraryId = mockActivityState.itinerary.itineraryId,
          memberId = mockActivityState.itinerary.memberId,
          recStatus = mockActivityState.itinerary.recStatus,
          recCreatedWhen = mockActivityState.itinerary.recCreatedWhen.map(_.toDate),
          recModifiedWhen = mockActivityState.itinerary.recModifiedWhen.map(_.toDate)
        ),
        userAgent = None,
        bookingAttribution = Seq.empty,
        itineraryDate = new Date(),
        protectionModels = None,
        multiProductInfos =
          Some(mockMultiProductInfos.map(pf => MultiProductInfoForMessage(pf.multiProductId, pf.multiProductType.id))),
        flightSegmentInfoByPaxType = Seq.empty,
        segmentInfoByPaxType = Seq.empty,
        fareRulePolicies = None,
        flightSeatSelection = Seq.empty,
        vehicle = None,
        activities = Some(
          Seq(
            ActivityForMessage(
              ProtoConverter.protoToString(mockActivityState.activities.head),
              mockActivityProductModel.product.booking.bookingId
            )
          )
        ),
        properties = None,
        cegFastTracks = None,
        addOns = None,
        multiProductBookingGroups = Some(
          Seq(
            MultiProductBookingGroupModelMessage(
              bookingId = defaultBookingId,
              itineraryId = -2,
              cartId = -3,
              packageId = Some(-4)
            )
          )
        ),
        flightBrandSelections = None,
        flightBrandAttributes = None,
        flightBrandAttributeParams = None,
        flightBaseBooking = None,
        flightBaseCancellationInfo = None,
        crossProductIsolatedFeature = None
      )

      val bookingStateMessageCaptor: ArgumentCaptor[BookingStateMessage] =
        ArgumentCaptor.forClass(classOf[BookingStateMessage])
      activityBookingRepository
        .sendModelForReplication(mockActivityState, defaultBookingWorkflowAction, mockMultiProductInfos)
        .map { _ =>
          verify(hadoopServiceMock, times(1))
            .sendMessage(
              bookingStateMessageCaptor.capture()
            )
          val message = bookingStateMessageCaptor.getValue
          message shouldBe expected.copy(itineraryDate = message.itineraryDate)
        }
    }
  }

  "getBaseBookingByItineraryId" should {
    "call proxy" in {
      when(baseBookingDbProxy.getBaseBookingByItineraryId(any())).thenReturn(Future.successful(List(baseBooking)))

      activityBookingRepository.getBaseBookingByItineraryId(defaultItineraryId) map { result =>
        verify(baseBookingDbProxy, times(1)).getBaseBookingByItineraryId(any())
        result shouldBe List(baseBooking)
      }
    }
  }

  "getBaseBookingByItineraryIdV2" should {
    "return booking from primary on success" in {
      when(activityBkgDbProxy.getBaseBookingByItineraryId(any())).thenReturn(
        Future.successful(List(baseBooking))
      )
      when(activityLocalBkgDbProxy.getBaseBookingByItineraryId(any())).thenReturn(
        Future.failed(
          DbException(ErrorCode.UnexpectedDbError, Option(new SQLException("simulated primary db timeout")))
        )
      )

      activityBookingRepository.getBaseBookingByItineraryIdV2(55L).map { result =>
        verifyNoInteractions(baseBookingDbProxy, activityLocalBkgDbProxy)
        verify(activityBkgDbProxy).getBaseBookingByItineraryId(55L)
        result should contain only baseBooking
      }
    }

    "return booking from fallback when primary fails" in {
      when(activityBkgDbProxy.getBaseBookingByItineraryId(any())).thenReturn(
        Future.failed(
          DbException(ErrorCode.UnexpectedDbError, Option(new SQLException("simulated primary db timeout")))
        )
      )
      when(activityLocalBkgDbProxy.getBaseBookingByItineraryId(any())).thenReturn(
        Future.successful(List(baseBooking))
      )

      activityBookingRepository.getBaseBookingByItineraryIdV2(55L).map { result =>
        verifyNoInteractions(baseBookingDbProxy)
        verify(activityBkgDbProxy).getBaseBookingByItineraryId(55L)
        verify(activityLocalBkgDbProxy).getBaseBookingByItineraryId(55L)
        result should contain only baseBooking
      }
    }

    "fail when both data sources fail" in {
      when(activityBkgDbProxy.getBaseBookingByItineraryId(any())).thenReturn(
        Future.failed(
          DbException(ErrorCode.UnexpectedDbError, Option(new SQLException("simulated primary db timeout")))
        )
      )
      when(activityLocalBkgDbProxy.getBaseBookingByItineraryId(any())).thenReturn(
        Future.failed(
          DbException(ErrorCode.UnexpectedDbError, Option(new SQLException("simulated secondary db timeout")))
        )
      )

      recoverToSucceededIf[DbException](activityBookingRepository.getBaseBookingByItineraryIdV2(44L)).map { _ =>
        verifyNoInteractions(baseBookingDbProxy)
        verify(activityBkgDbProxy).getBaseBookingByItineraryId(44L)
        verify(activityLocalBkgDbProxy).getBaseBookingByItineraryId(44L)
        succeed
      }
    }
  }

  "saveActivityBookingState" should {
    val baseActivityBookingState = ActivityBookingStateWithItinerary(
      itinerary = mockItineraryInternalModel.itinerary,
      itineraryHistories = mockItineraryInternalModel.history.map(_.copy(actionId = 10, version = 0)),
      payments = mockItineraryInternalModel.payments,
      bookingPayments = mockItineraryInternalModel.bookingPayments,
      activities = Seq(mockActivityProductModel),
      multiProductBookingGroups = Seq(multiProductBookingGroupDBModel),
      relationships = Nil
    )

    "save successfully with only new itineraryActionHistory, payment, bookingPayment and financialBreakdown" in {
      val existingItineraryHistories = baseActivityBookingState.itineraryHistories.zipWithIndex.map {
        case (itineraryHistory, index) =>
          itineraryHistory.copy(actionId = index, version = index)
      }

      val existingPaymentState = baseActivityBookingState.payments.zipWithIndex.map {
        case (paymentState, index) =>
          paymentState.copy(paymentId = index)
      }

      val existingBookingPaymentState = baseActivityBookingState.bookingPayments.map { bookingPaymentState =>
        bookingPaymentState.copy(paymentId = baseActivityBookingState.payments.head.paymentId)
      }

      val existingFinancialBreakdowns =
        baseActivityBookingState.activities.flatMap(
          _.product.breakdowns.zipWithIndex
            .map { case (financialBreakDown, index) => financialBreakDown.copy(breakdownId = index, upcId = Some(3)) }
        )

      val existingBapiFinancialBreakdowns = existingFinancialBreakdowns.map(CommonProductConverters.toBapiBreakdown)

      val itineraryId = baseActivityBookingState.itinerary.itineraryId
      val bookingId   = itineraryProductModel.activities.head.product.booking.bookingId

      when(activityDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
        .thenReturn(Future.successful(existingItineraryHistories))
      when(activityDbProxyMock.getItineraryPaymentByItineraryId(itineraryId))
        .thenReturn(Future.successful(existingPaymentState))
      when(activityDbProxyMock.getBookingPaymentsByPaymentIds(Seq(0, 9)))
        .thenReturn(Future.successful(existingBookingPaymentState))
      when(activityDbProxyMock.getFinancialBreakdown(bookingId))
        .thenReturn(Future.successful(existingBapiFinancialBreakdowns))

      val newItineraryHistory    = mockItineraryInternalModel.history.head.copy(actionId = 10, version = 2)
      val newPaymentState        = mockItineraryInternalModel.payments.head.copy(paymentId = 9)
      val newBookingPaymentState = mockItineraryInternalModel.bookingPayments.head.copy(paymentId = 9)
      val newFinancialBreakdowns = FinancialBreakdown.defaultInstance.copy(breakdownId = 10)

      val activityBookingStateToSave = baseActivityBookingState.copy(
        itineraryHistories = existingItineraryHistories :+ newItineraryHistory,
        payments = existingPaymentState :+ newPaymentState,
        bookingPayments = existingBookingPaymentState :+ newBookingPaymentState,
        activities = baseActivityBookingState.activities
          .modify(_.each.product.breakdowns)
          .setTo(existingFinancialBreakdowns :+ newFinancialBreakdowns)
      )

      val activityBookingStateToSaveWithoutExistingRecord = baseActivityBookingState.copy(
        itineraryHistories = Seq(newItineraryHistory),
        payments = Seq(newPaymentState),
        bookingPayments = Seq(newBookingPaymentState),
        activities = baseActivityBookingState.activities
          .modify(_.each.product.breakdowns)
          .setTo(Seq(newFinancialBreakdowns))
      )

      when(activityDbProxyMock.insertActivity(any()))
        .thenReturn(Future.successful(activityBookingStateToSaveWithoutExistingRecord))
      when(multiProductRepositoryMock.saveMultiProductBookingGroupIfNotExist(multiProductBookingGroupDBModel))
        .thenReturn(Future.successful(multiProductBookingGroupDBModel))
      when(multiProductRepositoryMock.getMultiProductBookingGroupByItineraryId(itineraryId))
        .thenReturn(Future.successful(Seq(multiProductBookingGroupDBModel)))

      activityBookingRepository.saveBookingState(activityBookingStateToSave).map { result =>
        verify(activityDbProxyMock, times(2)).getItineraryActionHistoryByItineraryId(itineraryId)
        verify(activityDbProxyMock).getItineraryPaymentByItineraryId(itineraryId)
        verify(activityDbProxyMock).getBookingPaymentsByPaymentIds(Seq(0, 9))
        verify(activityDbProxyMock).getFinancialBreakdown(bookingId)
        result shouldEqual activityBookingStateToSaveWithoutExistingRecord
      }
    }

    "save activity with stale update" in {
      val newItineraryHistory = baseActivityBookingState.itineraryHistories.head.copy(version = 1)
      val itineraryId         = baseActivityBookingState.itinerary.itineraryId
      when(activityDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
        .thenReturn(Future.successful(Seq(newItineraryHistory)))

      val repository = new ActivityBookingRepositoryImpl(
        activityDbProxyMock,
        enigmaApiProxy,
        baseBookingDbProxy,
        activityBkgDbProxy,
        activityLocalBkgDbProxy,
        multiProductRepositoryMock,
        hadoopServiceMock,
        replicateConfig
      ) {
        override def getBookingState(itineraryId: ItineraryId): Future[ActivityBookingStateWithItinerary] =
          Future.failed(new Exception("stale update was detected"))
      }

      when(multiProductRepositoryMock.getMultiProductBookingGroupByItineraryId(itineraryId))
        .thenReturn(Future.successful(Seq(multiProductBookingGroupDBModel)))

      repository.saveBookingState(baseActivityBookingState).failed.map { failure =>
        verify(activityDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
        failure.getMessage shouldEqual "stale update was detected"
      }
    }

    "save activity with version collision" in {
      val baseActivityBookingState = ActivityBookingStateWithItinerary(
        itinerary = mockItineraryInternalModel.itinerary,
        itineraryHistories = mockItineraryInternalModel.history.map(_.copy(actionId = 10, version = 1)),
        payments = mockItineraryInternalModel.payments,
        bookingPayments = mockItineraryInternalModel.bookingPayments,
        activities = Seq(mockActivityProductModel),
        multiProductBookingGroups = Seq.empty,
        relationships = Nil
      )

      val newItineraryHistory = baseActivityBookingState.itineraryHistories.head.copy(version = 1)
      val itineraryId         = baseActivityBookingState.itinerary.itineraryId
      when(activityDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
        .thenReturn(Future.successful(Seq(newItineraryHistory)))

      val repository = new ActivityBookingRepositoryImpl(
        activityDbProxyMock,
        enigmaApiProxy,
        baseBookingDbProxy,
        activityBkgDbProxy,
        activityLocalBkgDbProxy,
        multiProductRepositoryMock,
        hadoopServiceMock,
        replicateConfig
      ) {
        override protected def reportVersionCollision(
            activityBookingState: ProductStateWithItinerary
        ): Future[ActivityBookingStateWithItinerary] =
          Future.failed(new Exception("version collision detected"))
      }

      when(multiProductRepositoryMock.getMultiProductBookingGroupByItineraryId(itineraryId))
        .thenReturn(Future.successful(Seq(multiProductBookingGroupDBModel)))

      repository.saveBookingState(baseActivityBookingState).failed.map { failure =>
        verify(activityDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
        failure.getMessage shouldEqual "version collision detected"
      }
    }

    "successful getActivityBookingState" in {

      val itineraryId = baseActivityBookingState.itinerary.itineraryId
      val bid         = baseActivityBookingState.activities.head.product.booking.bookingId

      val paymentIds: Seq[ItineraryId] = baseActivityBookingState.payments.map(_.paymentId)

      when(activityDbProxyMock.getItineraryPaymentByItineraryId(itineraryId))
        .thenReturn(Future.successful(baseActivityBookingState.payments))
      when(activityDbProxyMock.getItineraryActionHistoryByItineraryId(itineraryId))
        .thenReturn(Future.successful(baseActivityBookingState.itineraryHistories))
      when(activityDbProxyMock.getMultiProductItinerary(itineraryId))
        .thenReturn(
          Future.successful(Some(baseActivityBookingState.itinerary))
        )
      when(activityDbProxyMock.getBookingPaymentsByPaymentIds(paymentIds))
        .thenReturn(Future.successful(baseActivityBookingState.bookingPayments))
      when(baseBookingDbProxy.getBaseBookingByItineraryId(itineraryId))
        .thenReturn(Future.successful(baseActivityBookingState.activities.map(_.product.booking).toList))
      when(activityDbProxyMock.getActivityBookingByBookingId(bid))
        .thenReturn(Future.successful(itineraryProductModel.activities.head))

      when(multiProductRepositoryMock.getMultiProductBookingGroup(bid))
        .thenReturn(Future.successful(Some(multiProductBookingGroupDBModel)))

      activityBookingRepository.getBookingState(itineraryId).map { result =>
        verify(activityDbProxyMock).getItineraryPaymentByItineraryId(itineraryId)
        verify(activityDbProxyMock).getItineraryActionHistoryByItineraryId(itineraryId)
        verify(activityDbProxyMock).getBookingPaymentsByPaymentIds(paymentIds)
        verify(activityDbProxyMock).getMultiProductItinerary(itineraryId)
        verify(baseBookingDbProxy).getBaseBookingByItineraryId(itineraryId)
        verify(activityDbProxyMock).getActivityBookingByBookingId(bid)
        verify(multiProductRepositoryMock).getMultiProductBookingGroup(bid)

        result shouldEqual baseActivityBookingState
      }
    }
  }

  "sendBapiCreateActivityBookingMessage" should {
    "call messaging send" in {
      reset(hadoopServiceMock)
      when(hadoopServiceMock.sendMessage(any[Message])).thenReturn(Future.successful())

      val activityModelMessageCaptor: ArgumentCaptor[ActivityBookingMessage] =
        ArgumentCaptor.forClass(classOf[ActivityBookingMessage])

      activityBookingRepository
        .sendBapiCreateBookingMessage(defaultActivityProductModel, defaultBookingWorkflowAction)
        .map { _ =>
          verify(hadoopServiceMock, times(1)).sendMessage(activityModelMessageCaptor.capture())
          val message = activityModelMessageCaptor.getValue
          message shouldBe defaultActivityBookingMessage
        }
    }
  }

  "filterFinancialBreakdowns" should {
    "return financial breakdowns that are not exist in the database" in {
      val defaultBreakdown = Breakdown(
        referenceId = 1,
        breakdownId = 1,
        itineraryId = 1,
        bookingType = None,
        bookingId = None,
        actionId = None,
        eventDate = DateTime.now,
        itemId = 1,
        typeId = 1,
        taxFeeId = None,
        quantity = 1,
        localCurrency = "USD",
        localAmount = 0.0,
        exchangeRate = 0.0,
        usdAmount = 0.0,
        requestedAmount = None,
        refBreakdownId = None,
        requestedCurrency = Some("INR")
      )
      val existingFBDs = Seq(
        Seq(
          defaultBreakdown.copy(breakdownId = 1234),
          defaultBreakdown.copy(breakdownId = 5678)
        )
      )

      val fbdToInsert = Seq(
        FinancialBreakdown(breakdownId = 1234),
        FinancialBreakdown(breakdownId = 3333),
        FinancialBreakdown(breakdownId = 5678)
      )

      val activityProductModel = mockActivityProductModel.modify(_.product.breakdowns).setTo(fbdToInsert)
      val activityState        = mockActivityState.modify(_.activities).setTo(Seq(activityProductModel))
      val result = activityBookingRepository.filterFinancialBreakdowns(
        activityState,
        existingFBDs
      )
      result.head.product.breakdowns.size shouldBe 1
      result.head.product.breakdowns.head.breakdownId shouldBe 3333
    }
  }

  "checkDuplicateActivityBookingV2" should {
    val bookingDate = DateTime.parse("2022-11-24T23:59")
    val duplicateBookingRecord = DuplicateBookingRecord(
      bookingId = 1235,
      bookingDate = bookingDate,
      storeFrontId = -1,
      languageId = -1
    )
    val mappedDuplicateBooking = DuplicateBooking(
      BookingElement.Activity,
      duplicateBookingRecord.bookingId,
      duplicateBookingRecord.bookingDate
    )

    "return matched bookings after filter of in-progress bookings from bfdb" in {
      val inAwaitStateBookingActionId = 1114
      when(activityDbProxyMock.getAwaitingOrCancelledActivityBookingActions)
        .thenReturn(Future.successful(Seq(duplicateBookingRecord.copy(bookingId = inAwaitStateBookingActionId))))
      when(activityBkgDbProxy.getDuplicateActivityBooking(any(), any(), any(), any(), any()))
        .thenReturn(
          Future.successful(
            Seq(duplicateBookingRecord, duplicateBookingRecord.copy(bookingId = inAwaitStateBookingActionId))
          )
        )

      activityBookingRepository
        .checkDuplicateActivityBookingV2(
          productId = 1,
          bookingStartDate = bookingDate,
          offerId = 22,
          offerStartTime = None,
          offerEndTime = None
        )
        .map { results =>
          verify(activityBkgDbProxy).getDuplicateActivityBooking(1, bookingDate, 22, None, None)
          verify(activityDbProxyMock).getAwaitingOrCancelledActivityBookingActions
          verifyNoInteractions(activityLocalBkgDbProxy)
          results should contain theSameElementsAs Seq(mappedDuplicateBooking)
        }
    }

    "return duplicate booking from primary" in {
      when(activityBkgDbProxy.getDuplicateActivityBooking(any(), any(), any(), any(), any()))
        .thenReturn(Future.successful(Seq(duplicateBookingRecord)))
      when(activityLocalBkgDbProxy.getDuplicateActivityBooking(any(), any(), any(), any(), any()))
        .thenReturn(Future.successful(Seq(duplicateBookingRecord)))

      activityBookingRepository
        .checkDuplicateActivityBookingV2(
          productId = 1,
          bookingStartDate = bookingDate,
          offerId = 22,
          offerStartTime = None,
          offerEndTime = None
        )
        .map { results =>
          verify(activityBkgDbProxy).getDuplicateActivityBooking(1, bookingDate, 22, None, None)
          verify(activityDbProxyMock).getAwaitingOrCancelledActivityBookingActions
          verifyNoInteractions(activityLocalBkgDbProxy)
          results should contain theSameElementsAs Seq(mappedDuplicateBooking)
        }
    }

    "return duplicate booking from fallback when primary fails" in {
      when(activityBkgDbProxy.getDuplicateActivityBooking(any(), any(), any(), any(), any()))
        .thenReturn(
          Future.failed(
            DbException(ErrorCode.UnexpectedDbError, cause = Some(new RuntimeException("simulated db failure")))
          )
        )
      when(activityLocalBkgDbProxy.getDuplicateActivityBooking(any(), any(), any(), any(), any()))
        .thenReturn(Future.successful(Seq(duplicateBookingRecord)))

      activityBookingRepository
        .checkDuplicateActivityBookingV2(
          productId = 1,
          bookingStartDate = bookingDate,
          offerId = 22,
          offerStartTime = None,
          offerEndTime = None
        )
        .map { results =>
          verify(activityBkgDbProxy).getDuplicateActivityBooking(1, bookingDate, 22, None, None)
          verify(activityLocalBkgDbProxy).getDuplicateActivityBooking(1, bookingDate, 22, None, None)
          verify(activityDbProxyMock).getAwaitingOrCancelledActivityBookingActions
          results should contain theSameElementsAs Seq(mappedDuplicateBooking)
        }
    }

    "fail when both data sources fail" in {
      when(activityBkgDbProxy.getDuplicateActivityBooking(any(), any(), any(), any(), any()))
        .thenReturn(
          Future.failed(
            DbException(ErrorCode.UnexpectedDbError, cause = Some(new RuntimeException("simulated db failure")))
          )
        )
      when(activityLocalBkgDbProxy.getDuplicateActivityBooking(any(), any(), any(), any(), any()))
        .thenReturn(
          Future.failed(
            DbException(ErrorCode.UnexpectedDbError, cause = Some(new RuntimeException("simulated db failure")))
          )
        )

      recoverToSucceededIf[DbException](
        activityBookingRepository.checkDuplicateActivityBookingV2(
          productId = 1,
          bookingStartDate = bookingDate,
          offerId = 22,
          offerStartTime = None,
          offerEndTime = None
        )
      ).map { result =>
        verifyNoInteractions(activityDbProxyMock)
        result
      }
    }
  }

  "getBaseBookingIdBySupplierBookingId" should {
    val dbException = (db: String) =>
      DbException(ErrorCode.UnexpectedDbError, Some(new SQLTimeoutException(s"Simulated ${db} failure")))

    "return bookingId from BkgDb when supplierBookingId exists" in {
      val supplierBookingId = "SUPPLIER_BOOKING_ID_101"
      val bookingId         = 101L

      when(activityBkgDbProxy.getBaseBookingIdBySupplierBookingId(supplierBookingId))
        .thenReturn(Future.successful(Some(bookingId)))

      activityBookingRepository.getBaseBookingIdBySupplierBookingId(supplierBookingId).map { result =>
        verify(activityBkgDbProxy).getBaseBookingIdBySupplierBookingId(supplierBookingId)
        verify(activityLocalBkgDbProxy, never).getBaseBookingIdBySupplierBookingId(any())

        result shouldBe Some(bookingId)
      }
    }

    "return None when supplierBookingId does not exist" in {
      val supplierBookingId = "SUPPLIER_BOOKING_ID_999"

      when(activityBkgDbProxy.getBaseBookingIdBySupplierBookingId(supplierBookingId))
        .thenReturn(Future.successful(None))

      activityBookingRepository.getBaseBookingIdBySupplierBookingId(supplierBookingId).map { result =>
        verify(activityBkgDbProxy).getBaseBookingIdBySupplierBookingId(supplierBookingId)
        verify(activityLocalBkgDbProxy, never).getBaseBookingIdBySupplierBookingId(any())

        result shouldBe None
      }
    }

    "fallback to BapiDbProxy when BkgDbProxy fails" in {
      val supplierBookingId = "SUPPLIER_BOOKING_ID_101"
      val bookingId         = 101L

      when(activityBkgDbProxy.getBaseBookingIdBySupplierBookingId(supplierBookingId))
        .thenReturn(Future.failed(dbException("BkgDb")))
      when(activityLocalBkgDbProxy.getBaseBookingIdBySupplierBookingId(supplierBookingId))
        .thenReturn(Future.successful(Some(bookingId)))

      activityBookingRepository.getBaseBookingIdBySupplierBookingId(supplierBookingId).map { result =>
        verify(activityBkgDbProxy).getBaseBookingIdBySupplierBookingId(supplierBookingId)
        verify(activityLocalBkgDbProxy).getBaseBookingIdBySupplierBookingId(supplierBookingId)

        result shouldBe Some(bookingId)
      }
    }

    "return None when BkgdbProxy fails, and supplier_booking_id does not exist in BapiDb" in {
      val supplierBookingId = "SUPPLIER_BOOKING_ID_102"

      when(activityBkgDbProxy.getBaseBookingIdBySupplierBookingId(supplierBookingId))
        .thenReturn(Future.failed(dbException("BkgDb")))
      when(activityLocalBkgDbProxy.getBaseBookingIdBySupplierBookingId(supplierBookingId))
        .thenReturn(Future.successful(None))

      activityBookingRepository.getBaseBookingIdBySupplierBookingId(supplierBookingId).map { result =>
        verify(activityBkgDbProxy).getBaseBookingIdBySupplierBookingId(supplierBookingId)
        verify(activityLocalBkgDbProxy).getBaseBookingIdBySupplierBookingId(supplierBookingId)

        result shouldBe None
      }
    }

    "throw an exception when both BkgDbProxy and BapiDbProxy fail" in {
      val supplierBookingId = "SUPPLIER_BOOKING_ID_103"

      val bkgDbException  = dbException("BkgDb")
      val bapiDbException = dbException("BapiDb")

      when(activityBkgDbProxy.getBaseBookingIdBySupplierBookingId(supplierBookingId))
        .thenReturn(Future.failed(bkgDbException))
      when(activityLocalBkgDbProxy.getBaseBookingIdBySupplierBookingId(supplierBookingId))
        .thenReturn(Future.failed(bapiDbException))

      activityBookingRepository.getBaseBookingIdBySupplierBookingId(supplierBookingId).failed.map { ex =>
        verify(activityBkgDbProxy).getBaseBookingIdBySupplierBookingId(supplierBookingId)
        verify(activityLocalBkgDbProxy).getBaseBookingIdBySupplierBookingId(supplierBookingId)

        ex shouldBe bapiDbException
      }
    }
  }
}
