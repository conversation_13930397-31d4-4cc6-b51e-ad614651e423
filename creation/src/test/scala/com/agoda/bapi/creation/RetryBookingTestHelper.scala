package com.agoda.bapi.creation

import com.agoda.bapi.common.{ProductTokenMockHelper => CommonTokenHelper}
import com.agoda.bapi.common.message.creation.ContinueAction.ContinueAction
import com.agoda.bapi.common.message.creation.{BookingPaymentV2, ContinuePaymentRequest, CreditCardV2, NonCardV2, Payment3DSRequestV2, PaymentAmount}
import com.agoda.bapi.common.message.pricebreakdown.{PriceBreakdownNode, PriceBreakdownResponse, PriceBreakdownType}
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.model.StatusToken
import com.agoda.bapi.common.model.creation.Payment3DSOption
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.product.BookingFlow.BookingFlow
import com.agoda.bapi.common.token.{ActivityBookingModel, BookingTokenEncryptionHelper, <PERSON><PERSON><PERSON>ing<PERSON>ode<PERSON>, FlightBookingModel, Money => T<PERSON><PERSON>, MultiProductBookingToken, MultiProductCreationBookingToken, PropertyBookingModel}
import com.agoda.bapi.common.token.property.Token
import com.agoda.bapi.common.util.TokenSerializers
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.agoda.bapi.common.util.TokenSerializers._
import com.agoda.bapi.creation.service.helper.JtbCreateBookingHelper.serializeMultiProductBooking
import com.agoda.creditcardapi.client.v2.common.model.{AddTransientCCResponse, BinInfo, CreditCardNonPci, RetrieveCCNonPciResponse}
import com.agoda.mpb.common.models.state.{CommonPaymentInfo, DisplayPrice => MPBDisplayPrice, PriceBreakdownNode => MPBPriceBreakdown}

trait RetryBookingTestHelper extends CommonTokenHelper {

  private val dummyToken = Token(
    content = Some("{}"),
    version = Some(1),
    timestamp = Some(1571199718191L),
    expiresAfterMinutes = Some(20)
  )

  private val mockPropertyToken = TokenSerializers[PropertyBookingModel]
    .serialize(Map(productKey -> defaultPropertyBookingToken), Some(1571199718191L), None)
    .toOption

  private val mockFlightToken = TokenSerializers[FlightBookingModel]
    .serialize(Map(productKey -> Seq(defaultFlightBookingToken)), Some(1571199718017L), None)
    .toOption

  private val mockCarToken = TokenSerializers[CarBookingModel]
    .serialize(Map(productKey -> defaultCarToken), Some(1571199718017L), None)
    .toOption

  private val mockActivityToken = TokenSerializers[ActivityBookingModel]
    .serialize(Map(productKey -> defaultActivityToken), Some(1571199718017L), None)
    .toOption

  private val externalLoyaltyNode = PriceBreakdownNode(
    value = Option(
      PriceBreakdownResponse(
        `type` = PriceBreakdownType.ExternalLoyalty,
        amount = TMoney(amount = 100d, currencyCode = "THB"),
        points = Option(100d)
      )
    )
  )

  val priceBreakdown =
    PriceBreakdownNode(
      value = Option(
        PriceBreakdownResponse(
          `type` = PriceBreakdownType.TotalPrice,
          amount = TMoney(amount = 2000d, currencyCode = "THB"),
          amountBeforeDiscount = Some(TMoney(2300d, currencyCode = "THB")),
          discountType = Some(1)
        )
      ),
      breakdowns = Some(
        Seq(
          PriceBreakdownNode(
            value = Option(
              PriceBreakdownResponse(
                `type` = PriceBreakdownType.PayAgoda,
                amount = TMoney(amount = 2300d, currencyCode = "THB")
              )
            ),
            breakdowns = None
          ),
          PriceBreakdownNode(
            value = Option(
              PriceBreakdownResponse(
                `type` = PriceBreakdownType.TotalSavings,
                amount = TMoney(amount = 300d, currencyCode = "THB")
              )
            ),
            breakdowns = Option(Seq(externalLoyaltyNode))
          )
        )
      )
    )

  def continuePaymentRequest(
      statusTokenStr: String = "someToken",
      bookingToken: Option[TokenMessage],
      continueAction: ContinueAction,
      payment: Option[BookingPaymentV2] = None
  ) = {
    ContinuePaymentRequest(
      statusToken = statusTokenStr,
      internalToken = None,
      correlationId = None,
      userContext = None,
      bookingContext = None,
      securityCode = None,
      payment = payment,
      continueAction = Some(continueAction),
      bookingToken = bookingToken
    )
  }

  def bookingPaymentV2() = {
    BookingPaymentV2(
      method = PaymentMethod.MasterCard,
      creditCard = Option(
        CreditCardV2(
          detail = None,
          shouldSave = true,
          payment3DS = Some(
            Payment3DSRequestV2(
              payment3DSOption = Payment3DSOption.Auto_3DS2,
              acceptHeader = "abc",
              userAgent = "userAgent",
              language = Some(Some("en")),
              colorDepth = Some(24),
              screenHeight = Some(723),
              screenWidth = Some(1536),
              timeZoneOffset = Some(7),
              javaEnabled = Some(true),
              javaScriptEnabled = Some(true),
              deviceCollectionUrl = Some("https://agodaUrl/deviceCollectionResultfor"),
              bankCallback3DS1Url = Some("https://agodaUrl/handle3dsecure1"),
              returnUrl = Some("https://agodaUrk/notifychallenge"),
              supportedMPIs = Some(Seq(1, 2)),
              affiliatePaymentModel = Some(1)
            )
          )
        )
      ),
      nonCard = Some(NonCardV2(ccofId = Some(111111), shouldSave = Some(true))),
      ccToken = Some("Some CC Token")
    )
  }

  def serializeMultiProductCreationBookingToken(
      multiProductCreation: MultiProductCreationBookingToken
  ): TokenMessage = {
    val multiProductCreationToken =
      TokenSerializers[MultiProductCreationBookingToken]
        .serialize(multiProductCreation, None, None)
        .toOption

    val multiProductBooking =
      MultiProductBookingToken(
        setupBookingToken = None,
        creationBookingToken = multiProductCreationToken,
        retryPaymentBookingToken = None
      )
    val multiProductBookingSerialized =
      TokenSerializers[MultiProductBookingToken].serialize(multiProductBooking, None, None).toOption
    val token = serializeMultiProductBooking(multiProductBookingSerialized).get
    BookingTokenEncryptionHelper.encryptToken(token).toOption.get
  }
  def genMultiProductCreationBookingToken(
      bookingFlowType: BookingFlow = BookingFlow.Cart,
      hasFlight: Boolean = false,
      hasProperty: Boolean = false,
      hasPriceFreeze: Boolean = false,
      hasCar: Boolean = false,
      hasActivity: Boolean = false
  ): MultiProductCreationBookingToken = {
    MultiProductCreationBookingToken(
      properties = if (hasProperty) mockPropertyToken else Some(dummyToken),
      flights = if (hasFlight) mockFlightToken else Some(dummyToken),
      tripProtections = Some(dummyToken),
      cars = if (hasCar) mockCarToken else Some(dummyToken),
      activities = if (hasActivity) mockActivityToken else Some(dummyToken),
      priceFreezes = None,
      cegFastTracks = Some(dummyToken),
      addOns = Some(dummyToken),
      payment = PaymentAmount(siteExchangeRate = 0, upliftExchangeRate = 0, destinationExchangeRate = 0),
      bookingFlowType = bookingFlowType,
      priceBreakdown = Some(priceBreakdown),
      commonPayment = Some(
        CommonPaymentInfo(
          method = PaymentMethod.MasterCard,
          timeoutMinutes = None,
          redirectInfo = None,
          paymentCategoryId = None,
          gatewayId = None,
          gatewayInfoId = None,
          mpiId = None,
          displayPrice = Some(
            MPBDisplayPrice(
              breakdown = MPBPriceBreakdown(
                value = None,
                breakdowns = None
              ),
              version = 0
            )
          ),
          paymentToken = None,
          kycToken = None,
          paymentChannel = None
        )
      )
    )
  }

  def mockStatusToken() = {
    StatusToken(
      itineraryId = 1,
      actionId = 1,
      productType = Set.empty,
      dc = ""
    )
  }
  def mockCcNonPciResponse: RetrieveCCNonPciResponse =
    RetrieveCCNonPciResponse(
      CreditCard = Some(
        CreditCardNonPci(
          CardholderName = Some("Credit Card Holder Name"),
          LastFour = Some("1234"),
          CardTypeId = Some(1),
          ExpiryMonth = Some(12),
          ExpiryYear = Some(2025),
          IssuingBankName = Some("Issuing Bank Name")
        )
      ),
      IsCcOF = Some(false),
      BinInfo = Some(
        BinInfo(
          CardClass = Some("Card Class"),
          CardScheme = Some("Card Scheme"),
          CardLevel = Some("Card Level"),
          CountryIso3 = Some("CountryIso3"),
          CardTypeId = Some(2),
          TopLevelCardTypeId = Some(1),
          CardLength = Some(12)
        )
      )
    )

  def mockAddTransientCCResponse: AddTransientCCResponse =
    AddTransientCCResponse(
      CCId = Some(1234)
    )

}
