package com.agoda.bapi.creation

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.{Products => CreationProducts, _}
import com.agoda.bapi.common.message.setupBooking.LoyaltyRequest
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.activity.SummaryElementEnums.DisplayType
import com.agoda.bapi.common.model.activity._
import com.agoda.bapi.common.model.addOn._
import com.agoda.bapi.common.model.addOn.cegFastTrack.{AddOnBookingToken, CEGFastTrackTiers}
import com.agoda.bapi.common.model.car._
import com.agoda.bapi.common.model.flight.flightModel.{ItineraryHistory, MultiProductItinerary, Scope}
import com.agoda.bapi.common.model.flight.history.ActionType
import com.agoda.bapi.common.model.flight.{Price, _}
import com.agoda.bapi.common.model.itinerary.ItineraryInternalModel
import com.agoda.bapi.common.model.multiproduct.{MultiProductBookingGroupDBModel, MultiProductInfoDBModel}
import com.agoda.bapi.common.model.payment.{PaymentModel, SupplierPaymentMethod}
import com.agoda.bapi.common.model.product.{BookingFlow, ProductTypeEnum}
import com.agoda.bapi.common.model.tripProtection.{ProtectionProductIds, SupplierData, TripProtectionItemBreakdown, TripProtectionToken}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.MockRequestContext
import com.agoda.bapi.creation.model.multi.{EmptyProductReservedIds, ItineraryPreSaveInfo, Product, ReservedIds}
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.bapi.creation.model.{MultiProduct, RequestWithProductsNType}
import com.agoda.bapi.creation.service.stage.presave.ProtectionAncillaryModel
import com.agoda.commons.agprotobuf.meta.{MetaNames, MetaType => UniMetaType}
import com.agoda.mpb.common.models.state.{AccountingEntity, CancellationClass => _, _}
import com.agoda.mpb.common.{BookingType, MultiProductType, PointsType, PriceType}
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.danielasfregola.randomdatagenerator.RandomDataGenerator._
import org.joda.time.{DateTime, LocalDate}

import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter

trait CreateMultiBookingHelper extends CreateFlightBookingHelper with CreateBookingHelper {

  val baseProductTokenKey = Some("1")

  val baseGuests = Seq(
    genHotelGuest(1, true),
    genHotelGuest(2)
  )

  val basePax = Seq(
    genFlightPax(1, true, true),
    genFlightPax(2, false, false)
  )

  val baseCarDriver = Some(
    CarDriver(
      "Ms",
      "Jane",
      "Doe",
      Some("<EMAIL>"),
      Some("*********"),
      None,
      Some("Female"),
      None,
      None,
      None
    )
  )

  val baseCarItem = CarItem(driver = baseCarDriver, productTokenKey = baseProductTokenKey)

  val baseUserContext = Some(
    UserContext(
      languageId = 1,
      requestOrigin = "TH",
      currency = "",
      nationalityId = 106
    )
  )

  val baseActivityPax =
    ActivityPax(
      id = 5,
      title = Some("Mr"),
      firstname = Some("John"),
      lastname = Some("Doe"),
      primary = true,
      middlename = Some("Lennon"),
      paxType = PaxType.Adult,
      birthDate = Some(LocalDate.now()),
      gender = Some("Male"),
      nationalityId = Some(10),
      passportNumber = Some("gubasdljfnsf"),
      passportExpires = Some(LocalDate.now()),
      email = Some("<EMAIL>"),
      phoneNumber = Some("08123456789"),
      phoneCountryCode = Some("66")
    )

  val baseBookingAnswers = Some(
    Seq(
      ActivityBookingAnswer(
        bookingQuestionCode = "PICKUP_POINT",
        questionAns =
          "ewogICJpZCIgOiA5OSwKICAibG9jYXRpb25Qcm92aWRlciIgOiAiR09PR0xFIiwKICAicHJvdmlkZXJMb2NSZWYiIDogIkdPT0dMRSBQUk9WSURFRCBSRUZFUkVOQ0UiLAogICJhZGRyZXNzIiA6IHsKICAgICJhZGRyZXNzTGluZSIgOiAibGluZTEiLAogICAgInJlZ2lvbiIgOiAicmVnaW9uIiwKICAgICJsYXRMb25nIiA6IHsKICAgICAgImxhdGl0dWRlIiA6IDEwMy41LAogICAgICAibG9uZ2l0dWRlIiA6IDc0Ljg3CiAgICB9LAogICAgInBvc3RhbENvZGUiIDogInBvc3RhbGNvZGUiLAogICAgImNvdW50cnlDb2RlIiA6ICJjb3VudHJ5Y29kZSIKICB9LAogICJzdXBwbGllckxvY1JlZiIgOiAiVklBVE9SIFBST1ZJREVEIFJFRkVSRU5DRSIKfQ==",
        ansUnit = "LOCATION_REFERENCE"
      ),
      ActivityBookingAnswer(
        bookingQuestionCode = "ARRIVAL_DROP_OFF",
        questionAns =
          "ewogICJpZCIgOiA5OSwKICAibG9jYXRpb25Qcm92aWRlciIgOiAiU1VQUExJRVIiLAogICJwcm92aWRlckxvY1JlZiIgOiAiU1VQUExJRVIgUFJPVklERUQgUkVGRVJFTkNFIiwKICAiYWRkcmVzcyIgOiB7CiAgICAiYWRkcmVzc0xpbmUiIDogImxpbmUxIiwKICAgICJyZWdpb24iIDogInJlZ2lvbiIsCiAgICAibGF0TG9uZyIgOiB7CiAgICAgICJsYXRpdHVkZSIgOiAxMDMuNSwKICAgICAgImxvbmdpdHVkZSIgOiA3NC44NwogICAgfSwKICAgICJwb3N0YWxDb2RlIiA6ICJwb3N0YWxjb2RlIiwKICAgICJjb3VudHJ5Q29kZSIgOiAiY291bnRyeWNvZGUiCiAgfSwKICAic3VwcGxpZXJMb2NSZWYiIDogIlNVUFBMSUVSIFBST1ZJREVEIFJFRkVSRU5DRSIKfQ",
        ansUnit = "LOCATION_REFERENCE"
      ),
      ActivityBookingAnswer(
        bookingQuestionCode = "UNKNOWN",
        questionAns =
          "ewogICJpZCIgOiAzMTksCiAgImxvY2F0aW9uUHJvdmlkZXIiIDogIlNVUFBMSUVSIiwKICAicHJvdmlkZXJMb2NSZWYiIDogIkxPQy02ZUtKK29yNXk4bzk5UXcwQzh4V3lJZ1o3U0hmREdXbGcxMGN0Q0V1dXcwPSIsCiAgImFkZHJlc3MiIDogewogICAgImFkZHJlc3NMaW5lIiA6ICIzNiBQaGV0aGJ1cmkgU29pIDEzICwgUGhldGhidXJpIFJvYWQsIFBoYXlhIFRoYWksIFJhanRoZXdlZSIsCiAgICAicmVnaW9uIiA6ICJCYW5na29rIiwKICAgICJsYXRMb25nIiA6IHsKICAgICAgImxhdGl0dWRlIiA6IDEzLjc1NTQyMywKICAgICAgImxvbmdpdHVkZSIgOiAxMDAuNTM2NzQKICAgIH0sCiAgICAicG9zdGFsQ29kZSIgOiAiMTA0MDAiLAogICAgImNvdW50cnlDb2RlIiA6ICJUSCIKICB9LAogICJzdXBwbGllckxvY1JlZiIgOiAiTE9DLTZlS0orb3I1eThvOTlRdzBDOHhXeUlnWjdTSGZER1dsZzEwY3RDRXV1dzA9IiwKICAibmFtZSIgOiAiaUNoZWNrIGlubiBNYXlmYWlyIFByYXR1bmFtIgp9",
        ansUnit = "LOCATION_REFERENCE"
      ),
      ActivityBookingAnswer(
        bookingQuestionCode = "DEPARTURE_PICKUP",
        questionAns = "SOMEWHERE I BELONG",
        ansUnit = "FREETEXT"
      ),
      ActivityBookingAnswer(
        bookingQuestionCode = "FIRST_NAME",
        questionAns = "JOHN",
        ansUnit = "TEXT"
      ),
      ActivityBookingAnswer(
        bookingQuestionCode = "SPECIAL_REQUIREMENT",
        questionAns = "Need wheelchair support",
        ansUnit = ""
      )
    )
  )

  val baseActivityLanguageGuide = Some(ActivityLanguageGuideAnswer(`type` = "GUIDE", language = "en"))

  val baseActivityItem =
    ActivitiesItem(
      pax = Some(Seq(baseActivityPax)),
      bookingAnswers = baseBookingAnswers,
      languageGuide = baseActivityLanguageGuide,
      productTokenKey = baseProductTokenKey
    )

  val baseMultiProductReq = baseReq.copy(
    products = CreationProducts(
      Some(Seq(random[PropertyItem].copy(productTokenKey = baseProductTokenKey, guests = Some(baseGuests)))),
      Some(
        Seq(FlightItem(productTokenKey = baseProductTokenKey, flapiJsonToken = "mockFlapiToken", pax = Option(basePax)))
      ),
      Some(Seq(baseCarItem)),
      activitiesItems = Some(Seq(baseActivityItem))
    )
  )

  val baseMultiActivityProductReq = baseReq.copy(
    products = CreationProducts(
      Some(Seq(random[PropertyItem].copy(productTokenKey = baseProductTokenKey, guests = Some(baseGuests)))),
      Some(
        Seq(FlightItem(productTokenKey = baseProductTokenKey, flapiJsonToken = "mockFlapiToken", pax = Option(basePax)))
      ),
      Some(Seq(baseCarItem)),
      activitiesItems = Some(
        Seq(
          baseActivityItem,
          baseActivityItem.copy(
            productTokenKey = Some("2"),
            languageGuide = Some(ActivityLanguageGuideAnswer(`type` = "GUIDE", language = "th-th"))
          )
        )
      )
    )
  )

  val baseVehicleReq = baseMultiProductReq.copy(
    userContext = baseUserContext
  )

  val baseVehicleReqWithLoyalty = baseVehicleReq.copy(
    externalLoyaltyRequest = Some(
      LoyaltyRequest(
        partnerClaimToken = Some("partnerClaimToken"),
        selectedOfferIdentifier = None,
        points = Some(500.0),
        loyaltySearchType = None
      )
    )
  )

  val baseActivityReq = baseMultiProductReq.copy(
    userContext = baseUserContext,
    externalLoyaltyRequest = Some(LoyaltyRequest(Some("partnerClaim")))
  )

  val baseMultiActivityReq = baseMultiActivityProductReq.copy(
    userContext = baseUserContext,
    externalLoyaltyRequest = Some(LoyaltyRequest(Some("partnerClaim")))
  )

  val baseCegFastTrackReq = baseMultiProductReq.copy(
    userContext = baseUserContext
  )

  val baseSinglePropertyReq = baseNoCvcReq.copy(
    products = CreationProducts(
      Some(Seq(random[PropertyItem].copy(productTokenKey = baseProductTokenKey, guests = Some(baseGuests))))
    )
  )

  val baseRequestContext = MockRequestContext.create()

  val baseRoomInfo = defaultRoom()

  val baggageSelection =
    BaggageSelection(
      passengerId = 1,
      sliceId = Some(1),
      baggage = Seq(
        BaggageInfo(
          baggageType = 1,
          weightLimitPerBag = Some(Weight("KG", 25.0)),
          sizeLimitPerBag = Some(Size("IN", 14.0, 21.0, 27.0)),
          purchaseOption = Some(
            BaggagePurchaseOption(
              baggageOptionId = "baggageId1",
              supplierData = "exampleBaggageSupplierData",
              totalWeightAllowance = Some(Weight("KG", 25.0)),
              quantity = Some(1),
              price = Some(Price("USD", 15.0, 10.0, 4.0, 1.0))
            )
          ),
          isCarryOnBaggage = true,
          index = 1
        )
      ),
      scope = Scope.SLICE,
      scopeRefId = 1,
      isPartialSettlementRequired = Some(false)
    )

  val baseFlightToken = FlightBookingToken(
    0,
    0,
    "",
    DateTime.now(),
    Option(
      FlightInfo(
        1,
        1,
        Some(0),
        "ABC",
        None,
        PaymentModel.Merchant,
        Seq(flightSlice),
        List.empty,
        false,
        false,
        None,
        None,
        None,
        None,
        supplierPaymentMethod = Some(SupplierPaymentMethod.None)
      )
    ),
    searchId = "8463d781-6c32-4416-a253-1c9cc0c4cec2",
    itineraryId = "1154072730",
    productTokenKey = Some("1"),
    pointOfSale = Some("US"),
    experimentVariant = None,
    ancillaryAddOns = None,
    baggageSelection = Seq(baggageSelection),
    priceId = "0"
  )

  val baseKiwiFlightToken = FlightBookingToken(
    supplierId = 30010,
    subSupplierId = 0,
    supplierData = "",
    expiresAt = DateTime.now(),
    Option(
      FlightInfo(
        numberOfAdults = 1,
        numberOfChildren = 1,
        numberOfLapInfants = Some(0),
        ticketingAirline = "ABC",
        voidWindowClose = None,
        paymentModel = PaymentModel.Merchant,
        slices = Seq(flightSlice),
        priceBreakdowns = List.empty,
        isPassportRequired = false,
        isNationalityRequired = false,
        pricing = None,
        priceBreakdownsPerPax = None,
        passengerMinAge = None,
        fareRulePolicies = None,
        supplierPaymentMethod = Some(SupplierPaymentMethod.None)
      )
    ),
    searchId = "8463d781-6c32-4416-a253-1c9cc0c4cec2",
    itineraryId = "1154072730",
    productTokenKey = Some("1"),
    pointOfSale = Some("US"),
    experimentVariant = None,
    ancillaryAddOns = None,
    priceId = "30010"
  )
  val baseCarInfo = CarBookingInfo(
    paymentModel = PaymentModel.MerchantCommission,
    carItemPriceSummary = CarPricingSummary(
      CarChargeDisplay(
        currency = "USD",
        baseFare = 100,
        taxAndFee = 20,
        baseDiscount = 0,
        campaignDiscount = 0,
        totalFare = 200,
        totalSurcharge = 30,
        surchargeDetails = "THIS IS JSON OF SURCHARGE DETAILS",
        agodaFee = 0,
        policyChargesDetails = Some("THIS IS JSON OF POLICY CHARGE DETAILS"),
        paymentModel = 2,
        extraChargesDetails = Some("THIS IS JSON OF extraChargeDetails")
      )
    ),
    priceBreakdowns = Seq(
      CarItemBreakdown(
        DateTime.now(),
        itemId = 1,
        typeId = 2,
        taxFeeId = Some(3),
        quantity = 1,
        localCurrency = "THB",
        localAmount = 20.0,
        exchangeRate = 30.0,
        usdAmount = 30.0,
        reqAmount = 30.0,
        vendorExchangeRate = 1,
        requestedCurrency = "INR"
      ),
      CarItemBreakdown(
        DateTime.now(),
        itemId = 12,
        typeId = 1,
        taxFeeId = Some(3),
        quantity = 1,
        localCurrency = "THB",
        localAmount = 30.0,
        exchangeRate = 40.0,
        usdAmount = 50.0,
        reqAmount = 60.0,
        vendorExchangeRate = 1,
        requestedCurrency = "INR"
      )
    ),
    pickUp = LocationInfo(
      airportInfo = None,
      cityId = 1234,
      countryId = 1016,
      dateTime = DateTime.now(),
      addressLine = "THIS IS ADDRESS LINE",
      postalCode = "THIS IS POSTAL CODE",
      extraLocationInfo = "THIS IS EXTRA LOCATION INFO",
      locationName = "THIS IS LOCATION NAME",
      supplierLocationCode = "THIS IS SUPPLIER LOCATION CODE",
      locationType = Some("This is locationType"),
      phoneNumbers = Seq("1234")
    ),
    dropOff = LocationInfo(
      airportInfo = None,
      cityId = 8888,
      countryId = 1016,
      dateTime = DateTime.now(),
      addressLine = "THIS IS ADDRESS LINE",
      postalCode = "THIS IS POSTAL CODE",
      extraLocationInfo = "THIS IS EXTRA LOCATION INFO",
      locationName = "THIS IS LOCATION NAME",
      supplierLocationCode = "THIS IS SUPPLIER LOCATION CODE",
      locationType = Some("This is locationType"),
      phoneNumbers = Seq("1234")
    ),
    vehicleCode = "CAR-001",
    vehicleName = "Honda",
    classification = "Sedan",
    cancellationPolicy = "3D50P_100P",
    vehicleExtraInfo = VehicleExtraInfo(
      vehicleDoors = Some(4),
      vehicleSeats = Some(4),
      vehicleSuitcases = Some(1),
      vehicleTransmission = Some("transmission"),
      vehicleIsAircon = Some(true),
      vehicleIsAirbag = Some(true),
      vehicleFuelType = Some("fuelType"),
      imageUrl = Some("imageUrl"),
      pickUpSupplierOperationOfHours =
        Some(Seq(OperationSchedule("Monday", Seq(SupplierOperationHour("06:00", "00:00"))))),
      dropOffSupplierOperationOfHours =
        Some(Seq(OperationSchedule("Monday", Seq(SupplierOperationHour("06:00", "00:00"))))),
      providerIconUrl = Some("iconUrl"),
      acrissCode = Some("acrissCode")
    ),
    vehicleMileagePolicy = Some(
      MileagePolicy(
        freeDistance = 0,
        code = "",
        description = "Mock Car Fuel Info",
        charge = None,
        isFreeCoverage = true
      )
    ),
    vehicleFuelPolicy = Some(
      FuelPolicy(
        coverageType = "Full_To_Full",
        code = "",
        description = "fuel policy",
        charge = None,
        isFreeCoverage = true
      )
    ),
    vehicleCustomerPolicyInfo = None
  )
  val baseCarToken =
    CarBookingToken(0, "supplierData", "providerCode", baseCarInfo, DateTime.now, "123", "456", None, Some("1"), None)

  val baseProtectionToken = TripProtectionToken(
    protectionType = 0,
    products = Seq(ProtectionProductIds("1", ProductTypeEnum.Flight)),
    priceAmount = CurrencyOption(0, "USD"),
    priceAmountUSD = 0,
    marginAmount = CurrencyOption(0, "USD"),
    marginAmountUSD = 0,
    marginPercentage = 0,
    quantity = 1,
    tripCost = CurrencyOption(0, "USD"),
    purchaseDate = OffsetDateTime.parse("2019-03-21T22:00:42.464Z", DateTimeFormatter.ISO_OFFSET_DATE_TIME),
    supplierId = 0,
    subSupplierId = 0,
    supplierData = SupplierData(""),
    supplierSearchId = "",
    financialBreakdowns = Seq(
      TripProtectionItemBreakdown(
        eventDate = DateTime.now(),
        itemId = 1,
        typeId = 2,
        taxFeeId = 3,
        quantity = 1,
        localCurrency = "THB",
        localAmount = 20.0,
        exchangeRate = 30.0,
        usdAmount = 30.0,
        reqAmount = 30.0,
        vendorExchangeRate = 1,
        requestedCurrency = "INR"
      ),
      TripProtectionItemBreakdown(
        eventDate = DateTime.now(),
        itemId = 12,
        typeId = 1,
        taxFeeId = 3,
        quantity = 1,
        localCurrency = "THB",
        localAmount = 30.0,
        exchangeRate = 40.0,
        usdAmount = 50.0,
        reqAmount = 60.0,
        vendorExchangeRate = 1,
        requestedCurrency = "INR"
      )
    ),
    paymentModel = PaymentModel.Unknown
  )

  val baseActivityToken = ActivityBookingToken(
    activityInfo = ActivityBookingInfo(
      activityId = 123,
      offerId = 456,
      activityTitle = "Activity Title",
      supplierInfo = ActivitySupplierInfo(
        supplierId = 50001,
        providerCode = "Good Provider",
        supplierSpecificData =
          "{\"activityCode\":\"ACT1234\",\"offerCode\":\"TG1\",\"currency\":\"USD\",\"otherSpecificData\":{}}",
        supplierCommissionAmount = 0
      ),
      paymentModel = PaymentModel.Merchant,
      cancellationInfo = ActivityCancellationInfo(
        cancellationPolicyCode = "[{\"penaltyCode\":\"0\",\"hoursFrom\":24,\"hoursUntil\":0}]",
        Some(CancellationClass.FreeCancellation)
      ),
      bookingPriceSummary = Seq(
        ActivityBookingPriceSummary( // Booking level
          priceType = PriceType.PerBooking,
          quantity = 1,
          displayCurrency = "USD",
          baseFare = 160,
          taxAmount = 11.2,
          feeAmount = 16.0,
          surchargeAmount = 0,
          baseDiscount = 0,
          totalFare = 187.2,
          surchargeDetails = ""
        ),
        ActivityBookingPriceSummary( // Pax - Adult
          priceType = PriceType.PerAdult,
          quantity = 2,
          displayCurrency = "USD",
          baseFare = 65,
          taxAmount = 4.55,
          feeAmount = 6.5,
          surchargeAmount = 0,
          baseDiscount = 0,
          totalFare = 76.05,
          surchargeDetails = ""
        ),
        ActivityBookingPriceSummary( // Pax - Child
          priceType = PriceType.PerChild,
          quantity = 1,
          displayCurrency = "USD",
          baseFare = 30,
          taxAmount = 2.1,
          feeAmount = 3.0,
          surchargeAmount = 0,
          baseDiscount = 0,
          totalFare = 35.1,
          surchargeDetails = ""
        )
      ),
      bookingOffer = ActivityBookingOffer(
        productOfferId = 456,
        productOfferTitle = "Activity Offer Title",
        offerType = OfferType.PerPerson,
        quantity = 3, // 2 Adult + 1 Child
        startDate = DateTime.now(),
        endDate = None,
        startTime = Some(ActivityTime(hours = 9, minutes = 0, seconds = 0)),
        endTime = None
      ),
      paxSummary = PaxSummary(
        paxInfo = Seq(
          PaxInfo(paxType = DisplayType.ADULT, quantity = 2),
          PaxInfo(paxType = DisplayType.CHILD, quantity = 1)
        ),
        totalPax = 3
      ),
      bookingQuestions = Seq(
        ActivityBookingQuestion(
          id = 1,
          code = ActivityBookingQuestionEnums.Code.SPECIAL_REQUIREMENT,
          dataType = ActivityBookingQuestionEnums.DataType.STRING,
          groupType = ActivityBookingQuestionEnums.GroupType.PER_BOOKING,
          isMasterQuestion = true,
          isOptional = true,
          isPII = false,
          maxLength = 1024,
          prefillAnswers = Seq.empty,
          units = Seq.empty,
          answerRanges = Seq.empty
        ),
        ActivityBookingQuestion(
          id = 2,
          code = ActivityBookingQuestionEnums.Code.PICKUP_POINT,
          dataType = ActivityBookingQuestionEnums.DataType.STRING,
          groupType = ActivityBookingQuestionEnums.GroupType.PER_BOOKING,
          isMasterQuestion = true,
          isOptional = false,
          isPII = false,
          maxLength = 1024,
          prefillAnswers = Seq.empty,
          units = Seq(ActivityBookingQuestionEnums.Unit.LOCATION_REFERENCE),
          answerRanges = Seq.empty
        ),
        ActivityBookingQuestion(
          id = 3,
          code = ActivityBookingQuestionEnums.Code.FIRSTNAME,
          dataType = ActivityBookingQuestionEnums.DataType.STRING,
          groupType = ActivityBookingQuestionEnums.GroupType.PER_PERSON,
          isMasterQuestion = true,
          isOptional = false,
          isPII = true,
          maxLength = 1024,
          prefillAnswers = Seq(
            ActivityPrefillAnswer(id = 1, code = "LOCATION-A", conditionalQuestionIds = Seq.empty),
            ActivityPrefillAnswer(id = 1, code = "LOCATION-B", conditionalQuestionIds = Seq.empty)
          ),
          units = Seq(ActivityBookingQuestionEnums.Unit.LOCATION_REFERENCE),
          answerRanges = Seq.empty
        ),
        ActivityBookingQuestion(
          id = 4,
          code = ActivityBookingQuestionEnums.Code.ARRIVAL_DROP_OFF,
          dataType = ActivityBookingQuestionEnums.DataType.STRING,
          groupType = ActivityBookingQuestionEnums.GroupType.PER_BOOKING,
          isMasterQuestion = false,
          isOptional = false,
          isPII = false,
          maxLength = 1024,
          prefillAnswers = Seq.empty,
          units = Seq(ActivityBookingQuestionEnums.Unit.LOCATION_REFERENCE),
          answerRanges = Seq.empty
        ),
        ActivityBookingQuestion(
          id = 5,
          code = ActivityBookingQuestionEnums.Code.DEPARTURE_PICKUP,
          dataType = ActivityBookingQuestionEnums.DataType.STRING,
          groupType = ActivityBookingQuestionEnums.GroupType.PER_BOOKING,
          isMasterQuestion = false,
          isOptional = false,
          isPII = false,
          maxLength = 1024,
          prefillAnswers = Seq.empty,
          units = Seq(ActivityBookingQuestionEnums.Unit.FREETEXT),
          answerRanges = Seq.empty
        ),
        ActivityBookingQuestion(
          id = 6,
          code = ActivityBookingQuestionEnums.Code.UNKNOWN,
          dataType = ActivityBookingQuestionEnums.DataType.STRING,
          groupType = ActivityBookingQuestionEnums.GroupType.PER_BOOKING,
          isMasterQuestion = false,
          isOptional = false,
          isPII = false,
          maxLength = 1024,
          prefillAnswers = Seq.empty,
          units = Seq(ActivityBookingQuestionEnums.Unit.LOCATION_REFERENCE),
          answerRanges = Seq.empty
        ),
        ActivityBookingQuestion(
          id = 1,
          code = ActivityBookingQuestionEnums.Code.FIRSTNAME,
          dataType = ActivityBookingQuestionEnums.DataType.STRING,
          groupType = ActivityBookingQuestionEnums.GroupType.PER_PERSON,
          isMasterQuestion = true,
          isOptional = false,
          isPII = true,
          maxLength = 255,
          prefillAnswers = Seq.empty,
          units = Seq.empty,
          answerRanges = Seq.empty
        ),
        ActivityBookingQuestion(
          id = 1,
          code = ActivityBookingQuestionEnums.Code.WEIGHT,
          dataType = ActivityBookingQuestionEnums.DataType.NUMBER,
          groupType = ActivityBookingQuestionEnums.GroupType.PER_PERSON,
          isMasterQuestion = true,
          isOptional = false,
          isPII = true,
          maxLength = 255,
          prefillAnswers = Seq.empty,
          units = Seq(ActivityBookingQuestionEnums.Unit.KG),
          answerRanges = Seq.empty
        )
      ),
      languageGuides = Seq(
        ActivityLanguageGuide(languageGuideType = ActivityLanguageGuideEnums.Type.GUIDE, languageCode = "en"),
        ActivityLanguageGuide(languageGuideType = ActivityLanguageGuideEnums.Type.GUIDE, languageCode = "ja")
      ),
      priceBreakdown = Seq(
        PriceBreakdown(
          itemId = 1,
          date = "2021-04-20",
          typeId = 1,
          quantity = 1,
          usdAmount = 100.0,
          requestAmount = 100.0,
          requestedCurrency = "USD",
          requestedExchangeRate = 1.0,
          taxFeeId = 1,
          vendorAmount = 120.0,
          vendorCurrency = "USD",
          vendorExchangeRate = 1.0
        ),
        PriceBreakdown(
          itemId = 1,
          date = "2021-04-20",
          typeId = BreakDownTypeID.EssFastTrack.id,
          quantity = 1,
          usdAmount = 1.0,
          requestAmount = 100.0,
          requestedCurrency = "THB",
          requestedExchangeRate = 1.0,
          taxFeeId = 1,
          vendorAmount = 120.0,
          vendorCurrency = "USD",
          vendorExchangeRate = 1.0
        )
      ),
      contentLanguageId = 1,
      confirmMinutes = 0,
      voucherMinutes = 0,
      countryId = 1,
      metadata = Some("""{"masterActivityId":"1234","masterSupplierId":50001}""")
    ),
    expiresAt = DateTime.now(),
    paymentAmount =
      Some(PaymentAmount("THB", 1000.0, 500.0, 0.5, 0, 0.5, 1.0, Some("GBP"), 2.0, 0, 0, 0, 100.0, 50.0, 0, None)),
    productPayment = Some(
      ProductPaymentInfo(
        agency = None,
        payNow = Some(
          PayNowProductPayment(
            ProductPayment(
              paymentAmount = 1000.0,
              paymentAmountUsd = 500.0,
              paymentCurrency = "THB",
              siteExchangeRate = Some(0.5),
              upliftAmount = None,
              upliftExchangeRate = None,
              exchangeRateOption = None
            ),
            isBundleCharge = true
          )
        ),
        payLater = None,
        accountingEntity = Some(
          AccountingEntity(merchantOfRecord = 5632, rateContract = 0, revenue = 0, argument = None)
        ), // change when values are confirmed
        points = Vector(
          Points(
            pointType = PointsType.RMMiles,
            pointAttributes = PointsAttributes(
              "USD",
              50.0,
              Some("THB"),
              Some(100.0),
              Some(ExternalLoyaltyInfo(Some("loyaltyToken123"), Some(500.0), Some(1000.0), Some("partnerClaim123")))
            )
          )
        ),
        isTokenEnabled = None
      )
    ),
    productTokenKey = Some("1")
  )

  val baseCegFastTrackToken = {
    val requestedCurrency = "THB"
    val usdAmount         = 60
    val paymentAmount     = 2150
    val exchangeRate      = 35.83 // paymentAmount/usdAmount

    AddOnBookingToken(
      tier = CEGFastTrackTiers.NormalHotelFastTrack,
      refProductItemId = Seq("hotel_1"),
      productPayment = Some(
        ProductPaymentInfo(
          agency = None,
          payNow = Some(
            PayNowProductPayment(
              ProductPayment(
                paymentAmount = paymentAmount,
                paymentAmountUsd = usdAmount,
                paymentCurrency = requestedCurrency,
                siteExchangeRate = Some(0.5),
                upliftAmount = None,
                upliftExchangeRate = None,
                exchangeRateOption = None
              ),
              isBundleCharge = true
            )
          ),
          payLater = None,
          accountingEntity = None, // TODO: CFB-399
          points = Vector(),
          isTokenEnabled = None
        )
      ),
      paymentAmount = Some(
        PaymentAmount(
          paymentCurrency = requestedCurrency,
          paymentAmount = paymentAmount,
          paymentAmountUSD = usdAmount,
          siteExchangeRate = exchangeRate,
          exchangeRate = exchangeRate,
          upliftExchangeRate = exchangeRate,
          destinationExchangeRate = PaymentAmount.ZERO
        )
      ),
      priceBreakdown = Seq(
        PriceBreakdown(
          itemId = 11,
          date = "2022-07-05",
          typeId = 1,
          quantity = 1,
          usdAmount = 10,
          requestAmount = 0,
          requestedCurrency = "JPY",
          requestedExchangeRate = 1.0,
          taxFeeId = 0,
          vendorAmount = 10,
          vendorCurrency = "USD",
          vendorExchangeRate = 1.0
        ),
        PriceBreakdown(
          itemId = 12,
          date = "2022-07-05",
          typeId = 1,
          quantity = 1,
          usdAmount = 10,
          requestAmount = 0,
          requestedCurrency = "JPY",
          requestedExchangeRate = 1.0,
          taxFeeId = 0,
          vendorAmount = 10,
          vendorCurrency = "USD",
          vendorExchangeRate = 1.0
        ),
        PriceBreakdown(
          itemId = 12,
          date = "2022-07-05",
          typeId = BreakDownTypeID.EssFastTrack.id,
          quantity = 1,
          usdAmount = 1,
          requestAmount = 100,
          requestedCurrency = "THB",
          requestedExchangeRate = 1.0,
          taxFeeId = 0,
          vendorAmount = 100,
          vendorCurrency = "THB",
          vendorExchangeRate = 1.0
        )
      ),
      startDateTime = DateTime.parse("2019-08-02T00:00:00"),
      endDateTime = DateTime.parse("2019-09-08T00:00:00"),
      productTokenKey = "CegFastTrack_1",
      metas = Seq(
        AddOnMeta(
          metaName = "CEG_FASTTRACK_TIER",
          metaValue = "NormalHotelFastTrack",
          metaType = MetaTypeInternals.String,
          version = Some(1),
          metaTypeAsString = Some(UniMetaType.STRING.toString)
        )
      )
    )
  }
  val baseProductPayment = Some(
    ProductPaymentInfo(
      agency = None,
      payNow = Some(
        PayNowProductPayment(
          ProductPayment(
            paymentAmount = 2150.0,
            paymentAmountUsd = 60.0,
            paymentCurrency = "THB",
            siteExchangeRate = Some(0.5),
            upliftAmount = None,
            upliftExchangeRate = None,
            exchangeRateOption = None
          ),
          isBundleCharge = true
        )
      ),
      payLater = None,
      accountingEntity = None,
      points = Vector(),
      isTokenEnabled = None
    )
  )

  val basePriceBreakdown = Seq(
    PriceBreakdown(
      itemId = 11,
      date = "2022-07-05",
      typeId = 1,
      quantity = 1,
      usdAmount = 10,
      requestAmount = 0,
      requestedCurrency = "REQ_USD",
      requestedExchangeRate = 1.0,
      taxFeeId = 0,
      vendorAmount = 10,
      vendorCurrency = "USD",
      vendorExchangeRate = 1.0
    ),
    PriceBreakdown(
      itemId = 12,
      date = "2022-07-05",
      typeId = 1,
      quantity = 1,
      usdAmount = 10,
      requestAmount = 0,
      requestedCurrency = "REQ_USD",
      requestedExchangeRate = 1.0,
      taxFeeId = 0,
      vendorAmount = 10,
      vendorCurrency = "USD",
      vendorExchangeRate = 1.0
    ),
    PriceBreakdown(
      itemId = 12,
      date = "2022-07-05",
      typeId = BreakDownTypeID.EssFastTrack.id,
      quantity = 1,
      usdAmount = 1,
      requestAmount = 100,
      requestedCurrency = "REQ_THB",
      requestedExchangeRate = 1.0,
      taxFeeId = 0,
      vendorAmount = 100,
      vendorCurrency = "THB",
      vendorExchangeRate = 1.0
    )
  )

  val baseProtectionAddOnToken = addOn.AddOnBookingToken(
    characteristics = AddOnCharacteristics(
      productTypeId = ProductType.TripProtection.id,
      startDate = Some(DateTime.parse("2019-08-02T00:00:00").toLocalDateTime),
      endDate = Some(DateTime.parse("2019-09-08T00:00:00").toLocalDateTime)
    ),
    bundle = Some(AddOnBundle(addOnProductIds = Seq(baseFlightToken.productTokenKey.getOrElse("")))),
    financials = Some(
      AddOnFinancials(
        productPaymentModel = PaymentModel.Agency,
        priceBreakdown = basePriceBreakdown,
        productPayment = baseProductPayment
      )
    ),
    supplier = Some(AddOnSupplier(supplierId = 100, supplierData = "SupplierSpecificData")),
    meta = Seq(
      AddOnMeta(
        metaName = "TRIP_PROTECTION_TYPE",
        metaValue = "FLIGHTS",
        metaType = MetaTypeInternals.String,
        version = Some(1),
        metaTypeAsString = Some(UniMetaType.STRING.toString)
      )
    ),
    productTokenKey = "PROTECTION_1"
  )

  val baseFlightProduct       = Product(BookingType.Flight, baseFlightToken, Some(MultiProductType.Package))
  val baseHotelProduct        = Product(BookingType.CreditCard, baseRoomInfo.get("1").get, Some(MultiProductType.Package))
  val baseVehicleProduct      = Product(BookingType.Vehicle, baseCarToken, Some(MultiProductType.Package))
  val baseActivityProduct     = Product(BookingType.Activity, baseActivityToken, Some(MultiProductType.Package))
  val baseCegFastTrackProduct = Product(BookingType.CEGFastTrack, baseCegFastTrackToken, Some(MultiProductType.Package))
  val baseProtectionAddOnProduct =
    Product(BookingType.Protection, baseProtectionAddOnToken, Some(MultiProductType.SingleProtection))

  val products = MultiProduct(
    properties = baseRoomInfo.get("1").toSeq,
    flights = Seq(baseFlightToken),
    commonPayment = Some(CommonPaymentInfo(PaymentMethod.Visa, None, None))
  )

  val productsKiwi = MultiProduct(
    properties = baseRoomInfo.get("1").toSeq,
    flights = Seq(baseKiwiFlightToken),
    commonPayment = Some(CommonPaymentInfo(PaymentMethod.Visa, None, None))
  )

  val singlePropertyProduct = MultiProduct(
    properties = baseRoomInfo.get("1").toSeq
  )

  val baseRequestWithProductsNType = RequestWithProductsNType(
    baseMultiProductReq,
    baseRequestContext,
    baseRoomInfo,
    Seq(baseFlightToken),
    Seq(ProtectionAncillaryModel(baseProtectionToken)),
    BookingType.CreditCard,
    BookingFlow.Package,
    products
  )

  val baseKiwiFlightRequestWithProductsNType = RequestWithProductsNType(
    baseMultiProductReq,
    baseRequestContext,
    baseRoomInfo,
    Seq(baseKiwiFlightToken),
    Seq(ProtectionAncillaryModel(baseProtectionToken)),
    BookingType.CreditCard,
    BookingFlow.Package,
    productsKiwi
  )

  def baseSingleHotelRequestWithFeatureAware(featureAware: FeatureAware) =
    RequestWithProductsNType(
      baseSinglePropertyReq,
      baseRequestContext.copy(featureAware = Some(featureAware)),
      baseRoomInfo,
      Seq.empty,
      Seq.empty,
      BookingType.CreditCard,
      BookingFlow.SingleProperty,
      singlePropertyProduct
    )

  def baseRequestWithProductsNTypeWithFeatureAware(featureAware: FeatureAware) =
    RequestWithProductsNType(
      baseMultiProductReq,
      baseRequestContext.copy(featureAware = Some(featureAware)),
      baseRoomInfo,
      Seq(baseFlightToken),
      Seq(ProtectionAncillaryModel(baseProtectionToken)),
      BookingType.CreditCard,
      BookingFlow.Package,
      products
    )

  def baseRequestWithProductsNTypeWithFeatureAwareAndWhiteLabelInfo(
      featureAware: FeatureAware,
      whiteLabelInfo: WhiteLabelInfo
  ) =
    RequestWithProductsNType(
      baseMultiProductReq,
      baseRequestContext.copy(featureAware = Some(featureAware), whiteLabelInfo = whiteLabelInfo),
      baseRoomInfo,
      Seq(baseFlightToken),
      Seq(ProtectionAncillaryModel(baseProtectionToken)),
      BookingType.CreditCard,
      BookingFlow.Package,
      products
    )

  def baseRequestWithProductsNTypeWithRequestContext(requestContext: RequestContext) =
    RequestWithProductsNType(
      baseMultiProductReq,
      requestContext,
      baseRoomInfo,
      Seq(baseFlightToken),
      Seq(ProtectionAncillaryModel(baseProtectionToken)),
      BookingType.CreditCard,
      BookingFlow.Package,
      products
    )

  val itineraryActionId = 4L
  val baseItineraryId   = 2L
  val baseCartId        = 9L
  val baseDuplicateSeq  = Seq(DuplicateBooking(BookingElement(1), 1, DateTime.parse("2019-01-01T00:00:00")))
  val baseFlightReservedIds: ReservedIds[FlightBookingToken, EmptyProductReservedIds] =
    ReservedIds(1L, 1L, Some(1L), baseFlightProduct)
  val baseHotelReservedIds: ReservedIds[RoomInfo, EmptyProductReservedIds] =
    ReservedIds(2L, 2L, Some(1L), baseHotelProduct)
  val baseMultiProductBookingGroup = MultiProductBookingGroupDBModel(
    bookingId = 1L,
    itineraryId = baseItineraryId,
    cartId = baseCartId,
    packageId = None
  )
  val baseItineraryDbModel = ItineraryInternalModel(
    payments = Seq.empty,
    bookingPayments = Seq.empty,
    history = Seq(
      ItineraryHistory(
        actionId = 1,
        itineraryId = baseItineraryId,
        bookingType = None,
        bookingId = None,
        actionType = ActionType.Created.id,
        version = 0,
        actionDate = DateTime.parse("2019-08-02T16:01"),
        parameters = "",
        description = "",
        recStatus = Some(1),
        recCreatedWhen = Some(DateTime.parse("2019-08-02T16:01"))
      )
    ),
    itinerary = MultiProductItinerary(baseItineraryId, 0),
    transientCCId = None,
    relationships = Nil,
    multiProductBookingGroups = Seq(baseMultiProductBookingGroup),
    ccToken = None
  )

  val baseMultiProductInfo = MultiProductInfoDBModel(
    multiProductId = 10L,
    multiProductType = MultiProductType.Package
  )

  val baseItineraryPreSaveInfo = ItineraryPreSaveInfo(
    itineraryId = baseItineraryId,
    actionId = itineraryActionId,
    tripStart = Some(DateTime.parse("2019-08-02T16:01")),
    tripEnd = Some(DateTime.parse("2019-08-20T16:01")),
    cartId = baseCartId,
    packageId = None
  )

}
