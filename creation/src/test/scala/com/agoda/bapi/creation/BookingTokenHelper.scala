package com.agoda.bapi.creation

import com.agoda.abspnx.client.http.response.{BookingResponse => AbsBookingResponse}
import com.agoda.abspnx.client.models.abs._
import com.agoda.bapi.common.message.creation._
import com.agoda.bapi.common.message.pricebreakdown.PriceBreakdownNode
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.model.creation.BAPIBooking
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{AbsConstants, AbsData, AbsResponseStatus}
import com.agoda.bapi.common.token.property.{Token, WhitelabelAffiliateBookingToken}
import com.agoda.bapi.common.token.{BookingTokenEncryptionHelper, ExternalLoyaltyRequestInfo, MultiProductBookingToken, MultiProductCreationBookingToken, PropertyBookingModel}
import com.agoda.bapi.common.util.{GZIPUtils, TokenSerializers}
import com.agoda.bapi.common.util.TokenSerializers._
import com.agoda.bapi.creation.service.helper.JtbCreateBookingHelper
import com.agoda.bapi.creation.service.helper.JtbCreateBookingHelper.serializeMultiProductBooking
import org.scalatestplus.mockito.MockitoSugar

import java.util.UUID

trait BookingTokenHelper extends CreateBookingHelper with MockitoSugar {
  val defaultPaymentAmount =
    PaymentAmount("THB", 2150, 60, 0.1, 0.1, 0.1, 0.1, Some("string"), 0, 0, 0, 0, 0, 0, 0, Some("string"))
  val mockReferenceToken = ReferenceToken(
    bookingId = 12345678L,
    sessionId = "test sessionId",
    itineraryId = 99999999L,
    externalBookingId = "ABC4235"
  )
  val hrStockInfo       = "ywe7864uioqwjead"
  val inventoryType     = "4"
  val supplierSiteId    = "SupplierSiteId"
  val supplierSubSiteId = "SupplierSubSiteId"

  def getMockAbsBookingResponse: AbsBookingResponse =
    AbsBookingResponse(
      resultInfo = ResultInfo(
        status = Status.Success,
        response = Response.AllotmentAvailable,
        remark = "test remark"
      ),
      supplierResponseInfo = Some(
        SupplierResponseInfo(
          currencyCode = "JPY",
          actualAmt = None,
          isPriceChanged = false,
          diffAmt = None,
          supplierSpecificData = Map(
            AbsConstants.SessionId             -> "2f2517d0-5f4c-43d6-b20c-f7ba3329a114",
            AbsConstants.SupplierSiteId        -> supplierSiteId,
            AbsConstants.SupplierSubSiteId     -> supplierSubSiteId,
            AbsConstants.SupplierTransactionId -> "2f2517d0-5f4c-43d6-b20c-f7ba3329a114",
            AbsConstants.HRStockInfo           -> hrStockInfo,
            AbsConstants.InventoryType         -> inventoryType
          ),
          originalAmount = None,
          supplierAmount = None
        )
      ),
      supplierBookingId = "",
      bookingId = ""
    )

  def getMockAbsResponse: AbsData =
    AbsData(
      absBookingResponse = Some(getMockAbsBookingResponse),
      absResponseStatus = AbsResponseStatus.AllotmentConfirmed,
      dmcId = 29014,
      remark = Some("Test"),
      isCustomerPaymentProcessed = false
    )

  def getMockBapiBooking: BAPIBooking = requestWithRoomAndPayment.rooms.head._2.bapiBooking

  def getMockReferenceTokenSerializer: Option[Token] =
    TokenSerializers.ReferenceTokenSerializer.serialize(mockReferenceToken, None, None).toOption

  def genBookingToken(
      referenceTokenSerialized: Option[Token] = None,
      absData: Option[AbsData] = None,
      bapiBookingToOption: Option[Seq[BAPIBooking]] = None,
      amount: PaymentAmount = defaultPaymentAmount,
      timestamp: Option[Long] = None,
      expiresAfterMinutes: Option[Long] = None,
      priceBreakdown: Option[PriceBreakdownNode] = None,
      externalLoyaltyRequestInfo: Option[ExternalLoyaltyRequestInfo] = None
  ): TokenMessage = {
    val absDataTokenSerialized = absData match {
      case Some(data) =>
        TokenSerializers.AbsDataTokenSerializer.serialize(data, timestamp, expiresAfterMinutes).toOption
      case _ => None
    }

    val bapiBookings: Seq[BAPIBooking] = {
      val mockBapiBooking = Seq(getMockBapiBooking.copy(productTokenKey = Some("0")))
      if (bapiBookingToOption.isDefined) bapiBookingToOption.getOrElse(mockBapiBooking)
      else mockBapiBooking
    }
    val propertyCreationBookings: PropertyBookingModel = bapiBookings
      .map(booking =>
        booking.productTokenKey
          .getOrElse(UUID.randomUUID().toString) -> booking
      )
      .toMap
    val whitelabelAffiliateBookingToken = WhitelabelAffiliateBookingToken(
      referenceToken = referenceTokenSerialized,
      absDataToken = absDataTokenSerialized
    )
    val whitelabelAffiliateBookingTokenSerialized =
      TokenSerializers.WhitelabelAffiliateBookingTokenSerializer
        .serialize(whitelabelAffiliateBookingToken, timestamp, expiresAfterMinutes)
        .toOption

    val propertyToken =
      TokenSerializers.PropertyBookingModelSerializer
        .serialize(propertyCreationBookings, timestamp, expiresAfterMinutes)
        .toOption
    val multiProductCreation = MultiProductCreationBookingToken(
      properties = propertyToken,
      flights = None,
      tripProtections = None,
      cars = None,
      activities = None,
      priceFreezes = None,
      cegFastTracks = None,
      addOns = None,
      payment = amount,
      bookingFlowType = BookingFlow.SingleProperty,
      whitelabelAffiliateBookingToken =
        if (referenceTokenSerialized.isEmpty && absData.isEmpty) None
        else whitelabelAffiliateBookingTokenSerialized,
      commonPayment = None,
      priceBreakdown = priceBreakdown,
      externalLoyaltyRequestInfo = externalLoyaltyRequestInfo
    )
    val multiProductCreationToken =
      TokenSerializers[MultiProductCreationBookingToken]
        .serialize(multiProductCreation, timestamp, expiresAfterMinutes)
        .toOption
    val multiProductBooking =
      MultiProductBookingToken(
        setupBookingToken = None,
        creationBookingToken = multiProductCreationToken,
        retryPaymentBookingToken = None
      )
    val multiProductBookingSerialized =
      TokenSerializers[MultiProductBookingToken].serialize(multiProductBooking, timestamp, expiresAfterMinutes).toOption
    val token = serializeMultiProductBooking(multiProductBookingSerialized).get
    BookingTokenEncryptionHelper.encryptToken(token).toOption.get
  }
}
