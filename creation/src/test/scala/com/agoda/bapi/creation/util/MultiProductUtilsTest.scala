package com.agoda.bapi.creation.util

import com.agoda.bapi.common.model.CampaignType
import com.agoda.bapi.common.model.creation.{BAPIBooking, PropertyCampaignInfo}
import com.agoda.bapi.common.model.flight.flightModel.BookingPaymentState
import com.agoda.bapi.common.model.multiproduct.MultiProductBookingGroupDBModel
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.product.BookingRequestTypeResolver.MultiProductFlowDefinition
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.model.MultiProduct
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.mpb.common.MultiProductType
import com.agoda.mpb.common.MultiProductType.MultiProductType
import models.starfruit.LocalVoucher
import org.mockito.Mockito.when
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatestplus.mockito.MockitoSugar

class MultiProductUtilsTest extends AnyWordSpec with Matchers with MockitoSugar {

  "MultiProductUtils" should {
    val featureAware: FeatureAware = mock[FeatureAware]

    "getMultiProductType work correctly" in {
      implicit val mpDefinition: MultiProductFlowDefinition = MultiProductFlowDefinition()
      def TestFunction(input: BookingFlow.Value): MultiProductType =
        MultiProductUtils.getMultiProductType(input, isMigrateFP2Cart = false, isMigrateHF2Cart = false)(mpDefinition)
      TestFunction(BookingFlow.SingleProperty) shouldBe MultiProductType.SingleProperty
      TestFunction(BookingFlow.MixAndSave) shouldBe MultiProductType.MixAndSave
      TestFunction(BookingFlow.SingleFlight) shouldBe MultiProductType.SingleFlight
      TestFunction(BookingFlow.Hackerfare) shouldBe MultiProductType.HackerFare
      TestFunction(BookingFlow.MultiFlightsWithProtection) shouldBe MultiProductType.MultiFlightsWithProtection
      TestFunction(BookingFlow.SingleVehicle) shouldBe MultiProductType.SingleVehicle
      TestFunction(BookingFlow.SingleProperty) shouldBe MultiProductType.SingleProperty
      TestFunction(BookingFlow.Package) shouldBe MultiProductType.Package
      TestFunction(BookingFlow.FlightWithProtection) shouldBe MultiProductType.FlightWithProtection
      TestFunction(BookingFlow.MultiHotel) shouldBe MultiProductType.MultiProperties
      TestFunction(BookingFlow.SingleActivity) shouldBe MultiProductType.SingleActivity
      TestFunction(BookingFlow.Unknown) shouldBe MultiProductType.None
    }

    "getMultiProductType correctly with CegFastTrack" in {
      MultiProductUtils.getMultiProductType(
        BookingFlow.SingleProperty,
        isMigrateFP2Cart = false,
        isMigrateHF2Cart = false
      )(
        MultiProductFlowDefinition(numberOfProperty = 1, numberOfCegFastTrack = 1)
      ) shouldBe MultiProductType.Cart
    }

    "getMultiProductType correctly with any add-ons" in {
      MultiProductUtils.getMultiProductType(
        BookingFlow.SingleProperty,
        isMigrateFP2Cart = false,
        isMigrateHF2Cart = false
      )(
        MultiProductFlowDefinition(numberOfProperty = 1, numberOfCegFastTrack = 0, numberOfAddOns = 1)
      ) shouldBe MultiProductType.Cart
    }

    "getMultiProductType correctly when BookingFlow is Cart and isMigrateToCart = false" in {
      MultiProductUtils.getMultiProductType(BookingFlow.Cart, isMigrateFP2Cart = false, isMigrateHF2Cart = false)(
        MultiProductFlowDefinition(numberOfProperty = 1)
      ) shouldBe MultiProductType.SingleProperty
      MultiProductUtils.getMultiProductType(BookingFlow.Cart, isMigrateFP2Cart = false, isMigrateHF2Cart = false)(
        MultiProductFlowDefinition(numberOfFlight = 1)
      ) shouldBe MultiProductType.SingleFlight
      MultiProductUtils.getMultiProductType(BookingFlow.Cart, isMigrateFP2Cart = false, isMigrateHF2Cart = false)(
        MultiProductFlowDefinition(numberOfCar = 1)
      ) shouldBe MultiProductType.SingleVehicle
      MultiProductUtils.getMultiProductType(BookingFlow.Cart, isMigrateFP2Cart = false, isMigrateHF2Cart = false)(
        MultiProductFlowDefinition(numberOfActivity = 1)
      ) shouldBe MultiProductType.SingleActivity
      MultiProductUtils.getMultiProductType(BookingFlow.Cart, isMigrateFP2Cart = false, isMigrateHF2Cart = false)(
        MultiProductFlowDefinition(numberOfProperty = 2)
      ) shouldBe MultiProductType.Cart
      MultiProductUtils.getMultiProductType(BookingFlow.Cart, isMigrateFP2Cart = false, isMigrateHF2Cart = false)(
        MultiProductFlowDefinition(numberOfTripProtection = 1)
      ) shouldBe MultiProductType.SingleProtection
      MultiProductUtils.getMultiProductType(BookingFlow.Cart, isMigrateFP2Cart = false, isMigrateHF2Cart = false)(
        MultiProductFlowDefinition(numberOfProperty = 1, numberOfCegFastTrack = 1)
      ) shouldBe MultiProductType.Cart
      MultiProductUtils.getMultiProductType(BookingFlow.Cart, isMigrateFP2Cart = false, isMigrateHF2Cart = false)(
        MultiProductFlowDefinition(numberOfFlight = 1, numberOfCegFastTrack = 1)
      ) shouldBe MultiProductType.Cart
      MultiProductUtils.getMultiProductType(BookingFlow.Cart, isMigrateFP2Cart = false, isMigrateHF2Cart = false)(
        MultiProductFlowDefinition()
      ) shouldBe MultiProductType.None
      MultiProductUtils.getMultiProductType(BookingFlow.Cart, isMigrateFP2Cart = false, isMigrateHF2Cart = false)(
        MultiProductFlowDefinition(numberOfFlight = 2)
      ) shouldBe MultiProductType.Cart
      MultiProductUtils.getMultiProductType(BookingFlow.Cart, isMigrateFP2Cart = false, isMigrateHF2Cart = false)(
        MultiProductFlowDefinition(numberOfFlight = 2, numberOfTripProtection = 1)
      ) shouldBe MultiProductType.Cart
    }

    "getMultiProductType correctly when BookingFlow is Cart and isMigrateToCart = true" in {
      val isMigrateToCart  = true
      val isMigrateHF2Cart = false
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfProperty = 1)
      ) shouldBe MultiProductType.SingleProperty
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfFlight = 1)
      ) shouldBe MultiProductType.SingleFlight
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfCar = 1)
      ) shouldBe MultiProductType.SingleVehicle
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfActivity = 1)
      ) shouldBe MultiProductType.SingleActivity
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfProperty = 2)
      ) shouldBe MultiProductType.Cart
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfTripProtection = 1)
      ) shouldBe MultiProductType.SingleProtection
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfProperty = 1, numberOfCegFastTrack = 1)
      ) shouldBe MultiProductType.Cart
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfFlight = 1, numberOfCegFastTrack = 1)
      ) shouldBe MultiProductType.Cart
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition()
      ) shouldBe MultiProductType.None
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfFlight = 2)
      ) shouldBe MultiProductType.HackerFare
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfFlight = 2, numberOfTripProtection = 1, numberOfAddOns = 1)
      ) shouldBe MultiProductType.MultiFlightsWithProtection
    }

    "getMultiProductType correctly when BookingFlow is Cart and isMigrateHackerFareToCart = true" in {
      val isMigrateToCart  = false
      val isMigrateHF2Cart = true
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfProperty = 1)
      ) shouldBe MultiProductType.SingleProperty
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfFlight = 1)
      ) shouldBe MultiProductType.SingleFlight
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfCar = 1)
      ) shouldBe MultiProductType.SingleVehicle
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfActivity = 1)
      ) shouldBe MultiProductType.SingleActivity
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfProperty = 2)
      ) shouldBe MultiProductType.Cart
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfTripProtection = 1)
      ) shouldBe MultiProductType.SingleProtection
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfProperty = 1, numberOfCegFastTrack = 1)
      ) shouldBe MultiProductType.Cart
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfFlight = 1, numberOfCegFastTrack = 1)
      ) shouldBe MultiProductType.Cart
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition()
      ) shouldBe MultiProductType.None
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfFlight = 2)
      ) shouldBe MultiProductType.Cart
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfFlight = 2, numberOfTripProtection = 1, numberOfAddOns = 1)
      ) shouldBe MultiProductType.Cart
    }

    "getMultiProductType correctly when BookingFlow is Cart when isMigrateToCart = true and isMigrateHackerFareToCart = true" in {
      val isMigrateToCart  = true
      val isMigrateHF2Cart = true
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfFlight = 1)
      ) shouldBe MultiProductType.SingleFlight
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfFlight = 1, numberOfTripProtection = 1, numberOfAddOns = 1)
      ) shouldBe MultiProductType.Cart
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfFlight = 2)
      ) shouldBe MultiProductType.Cart
      MultiProductUtils.getMultiProductType(
        BookingFlow.Cart,
        isMigrateFP2Cart = isMigrateToCart,
        isMigrateHF2Cart = isMigrateHF2Cart
      )(
        MultiProductFlowDefinition(numberOfFlight = 2, numberOfTripProtection = 1, numberOfAddOns = 1)
      ) shouldBe MultiProductType.Cart
    }

    "isMultiProductItineraryFromSetState return false when there is 1 element in bookingPayments" in {
      val bookingId1      = 1
      val bookingPayment1 = mock[BookingPaymentState]
      val bookingPayments = Seq(bookingPayment1)

      MultiProductUtils.isMultiProductItineraryFromSetState(bookingPayments, Seq.empty) shouldBe false
    }

    "isMultiProductItineraryFromSetState return false when there is >1 element in bookingPayments but booking ids are the same" in {
      val bookingId1      = 1
      val bookingPayment1 = mock[BookingPaymentState]
      val bookingPayment2 = mock[BookingPaymentState]
      val bookingPayments = Seq(bookingPayment1, bookingPayment2)

      when(bookingPayment1.bookingId).thenReturn(bookingId1)
      when(bookingPayment2.bookingId).thenReturn(bookingId1)

      MultiProductUtils.isMultiProductItineraryFromSetState(bookingPayments, Seq.empty) shouldBe false
    }

    "isMultiProductItineraryFromSetState return false when there are >1 element in bookingPayments and booking id is different" in {
      val bookingId1      = 1
      val bookingId2      = 2
      val bookingPayment1 = mock[BookingPaymentState]
      val bookingPayment2 = mock[BookingPaymentState]
      val bookingPayment3 = mock[BookingPaymentState]
      val bookingPayment4 = mock[BookingPaymentState]

      when(bookingPayment1.bookingId).thenReturn(bookingId1)
      when(bookingPayment2.bookingId).thenReturn(bookingId1)
      when(bookingPayment3.bookingId).thenReturn(bookingId2)
      when(bookingPayment4.bookingId).thenReturn(bookingId2)

      val bookingPayments = Seq(bookingPayment1, bookingPayment2, bookingPayment3, bookingPayment4)

      MultiProductUtils.isMultiProductItineraryFromSetState(bookingPayments, Seq.empty) shouldBe true
    }

    "isMultiProductItineraryFromSetState return false when bookingPayments is empty but multi product grouping has >1 element" in {
      val bookingId1            = 1
      val bookingId2            = 2
      val multiProductGrouping1 = mock[MultiProductBookingGroupDBModel]
      val multiProductGrouping2 = mock[MultiProductBookingGroupDBModel]

      when(multiProductGrouping1.bookingId).thenReturn(bookingId1)
      when(multiProductGrouping2.bookingId).thenReturn(bookingId2)

      val multiProductGrouping = Seq(multiProductGrouping1, multiProductGrouping2)

      MultiProductUtils.isMultiProductItineraryFromSetState(Seq.empty, multiProductGrouping) shouldBe true
    }

    "getLocalVoucher should return LocalVoucher correctly" in {
      val multiProducts = mock[MultiProduct]
      val roomInfo      = mock[RoomInfo]
      val bapiBooking   = mock[BAPIBooking]
      val campaignInfo  = mock[PropertyCampaignInfo]
      val localVoucher  = Some(LocalVoucher("JPY", 10))
      when(multiProducts.properties).thenReturn(Seq(roomInfo))
      when(roomInfo.bapiBooking).thenReturn(bapiBooking)
      when(bapiBooking.campaignInfo).thenReturn(Some(campaignInfo))
      when(campaignInfo.localVoucher).thenReturn(localVoucher)

      MultiProductUtils.getLocalVoucher(multiProducts) shouldBe localVoucher
    }

    "getPropertyCampaignInfo should return PropertyCampaignInfo correctly" in {
      val multiProducts = mock[MultiProduct]
      val roomInfo      = mock[RoomInfo]
      val bapiBooking   = mock[BAPIBooking]
      val campaignInfo = PropertyCampaignInfo(
        campaignId = 111111,
        promotionCode = "AOISNDFLK",
        CampaignType.PropertyPromotionCode,
        discountType = Some(1),
        discountAmount = Some(20),
        promotionText = Some("AOISNDFLK-Name"),
        hotelFundingText = Some("Hotel funding text!!"),
        localVoucher = None,
        isStateIdRequired = Some(true)
      )
      when(multiProducts.properties).thenReturn(Seq(roomInfo))
      when(roomInfo.bapiBooking).thenReturn(bapiBooking)
      when(bapiBooking.campaignInfo).thenReturn(Some(campaignInfo))

      MultiProductUtils.getPropertyCampaignInfo(multiProducts) shouldBe Some(campaignInfo)
    }
  }
}
