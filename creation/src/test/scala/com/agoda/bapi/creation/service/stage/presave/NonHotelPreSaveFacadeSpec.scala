package com.agoda.bapi.creation.service.stage.presave

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.MockRequestContext
import com.agoda.bapi.common.message.creation.CreateBookingResponse
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.mpb.common.MultiProductType.MultiProductType
import com.agoda.bapi.common.model.product.{BookingFlow, ProductTypeEnum}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.CreateMultiBookingHelper
import com.agoda.bapi.creation.model.db.BookingActionState
import com.agoda.bapi.creation.model.multi._
import com.agoda.mpb.common.{BookingType, MultiProductType}
import com.agoda.mpb.common.errors.ErrorCode
import org.mockito.Mockito.when
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future

class NonHotelPreSaveFacadeSpec extends AsyncWordSpec with Matchers with MockitoSugar with CreateMultiBookingHelper {
  trait Fixture {
    val activityPreSaveStageMock         = mock[ActivityPreSaveStage]
    val flightPreSaveStageMock           = mock[FlightPreSaveStage]
    val protectionPreSaveStageMock       = mock[ProtectionPreSaveStage]
    val vehiclePreSaveStageMock          = mock[VehiclePreSaveStage]
    val cegFastTrackPreSaveStageMock     = mock[CEGFastTrackPreSaveStage]
    val addOnPreSaveStageMock            = mock[AddOnPreSaveStage]
    val featureAware                     = mock[FeatureAware]
    implicit val context: RequestContext = MockRequestContext.create().copy(featureAware = Some(featureAware))

    val facade = new NonHotelPreSaveFacade(
      flightPreSaveStageMock,
      vehiclePreSaveStageMock,
      activityPreSaveStageMock,
      protectionPreSaveStageMock,
      cegFastTrackPreSaveStageMock,
      addOnPreSaveStageMock
    )

    val bookingWorkflowActionMock = mock[BookingWorkflowAction]
    val bookingActionStateMock    = mock[BookingActionState]

    val flightSaveStageResponse = NonPropertySaveStageResponse(
      productType = ProductTypeEnum.Flight,
      bookingAction = bookingWorkflowActionMock,
      bookingActionState = bookingActionStateMock,
      productTokenKey = Some("Flight_1")
    )
    val vehicleSaveStageResponse = NonPropertySaveStageResponse(
      productType = ProductTypeEnum.Car,
      bookingAction = bookingWorkflowActionMock,
      bookingActionState = bookingActionStateMock,
      productTokenKey = Some("Vehicle_1")
    )

    val protectionSaveStageResponse = NonPropertySaveStageResponse(
      productType = ProductTypeEnum.Protection,
      bookingAction = bookingWorkflowActionMock,
      bookingActionState = bookingActionStateMock,
      productTokenKey = Some("Protection_1")
    )

    val activitySaveStageResponse = NonPropertySaveStageResponse(
      productType = ProductTypeEnum.Activity,
      bookingAction = bookingWorkflowActionMock,
      bookingActionState = bookingActionStateMock,
      productTokenKey = Some("Activity_1")
    )

    val cegFastTrackSaveStageResponse = NonPropertySaveStageResponse(
      productType = ProductTypeEnum.CegFastTrack,
      bookingAction = bookingWorkflowActionMock,
      bookingActionState = bookingActionStateMock,
      productTokenKey = Some("CegFastTrack_1")
    )

    val protectionAddOnSaveStageResponse = NonPropertySaveStageResponse(
      productType = ProductTypeEnum.Protection,
      bookingAction = bookingWorkflowActionMock,
      bookingActionState = bookingActionStateMock,
      productTokenKey = Some("ProtectionAddOn_1")
    )

    val baseProtectionProduct: Product[ProtectionAncillaryModel] = Product(
      BookingType.Protection,
      ProtectionAncillaryModel(baseProtectionToken, Seq(flightSaveStageResponse)),
      Some(MultiProductType.Package)
    )

    val multiProductsRequest = MultiProductsRequest(
      request = baseMultiProductReq,
      requestContext = baseRequestContext,
      properties = Seq(baseHotelProduct),
      flights = Seq(baseFlightProduct),
      vehicles = Seq(baseVehicleProduct),
      protections = Seq(baseProtectionProduct),
      activities = Seq(baseActivityProduct),
      cegFastTracks = Seq(baseCegFastTrackProduct),
      addOns = Seq(baseProtectionAddOnProduct),
      bookingFlow = BookingFlow.Package,
      commonPayment = None,
      isBookingFromCart = None
    )

    val createBookingResponse =
      CreateBookingResponse.technical(ErrorCode.TechnicalError, Some(new Exception("expected error")))

    val multiProductPreSaveRequest = MultiProductPreSaveRequest(
      multiProductsRequest,
      baseItineraryPreSaveInfo,
      Map.empty
    )

    val multiProductPreSaveRequestWithDeps =
      multiProductPreSaveRequest.withDependencies(Seq(flightSaveStageResponse), Seq(vehicleSaveStageResponse))
    when(flightPreSaveStageMock.process(multiProductPreSaveRequest))
      .thenReturn(Future.successful(Right(Seq(flightSaveStageResponse))))
    when(vehiclePreSaveStageMock.process(multiProductPreSaveRequest))
      .thenReturn(Future.successful(Right(Seq(vehicleSaveStageResponse))))
    when(activityPreSaveStageMock.process(multiProductPreSaveRequest))
      .thenReturn(Future.successful(Right(Seq(activitySaveStageResponse))))
    when(protectionPreSaveStageMock.process(multiProductPreSaveRequestWithDeps))
      .thenReturn(Future.successful(Right(Seq(protectionSaveStageResponse))))
    when(cegFastTrackPreSaveStageMock.process(multiProductPreSaveRequest))
      .thenReturn(Future.successful(Right(Seq(cegFastTrackSaveStageResponse))))
    when(addOnPreSaveStageMock.process(multiProductPreSaveRequest))
      .thenReturn(Future.successful(Right(Seq(protectionAddOnSaveStageResponse))))
  }

  "every pre save stage returns successful response" in {
    val fixture = new Fixture {}
    import fixture._

    facade.preSave(multiProductPreSaveRequest).map { result =>
      val nonPropertySaveStageResponses = result.right.get
      nonPropertySaveStageResponses.size shouldEqual 6
      nonPropertySaveStageResponses should contain only (
        vehicleSaveStageResponse,
        flightSaveStageResponse,
        activitySaveStageResponse,
        protectionSaveStageResponse,
        cegFastTrackSaveStageResponse,
        protectionAddOnSaveStageResponse
      )
    }
  }

  "flightPreSaveStage fails with CreateBookingResponse" in {
    val fixture = new Fixture {}
    import fixture._

    when(flightPreSaveStageMock.process(multiProductPreSaveRequest))
      .thenReturn(Future.successful(Left(createBookingResponse)))
    facade.preSave(multiProductPreSaveRequest).map(_.left.get shouldEqual createBookingResponse)
  }

  "vehiclePreSaveStage fails with CreateBookingResponse" in {
    val fixture = new Fixture {}
    import fixture._

    when(vehiclePreSaveStageMock.process(multiProductPreSaveRequest))
      .thenReturn(Future.successful(Left(createBookingResponse)))
    facade.preSave(multiProductPreSaveRequest).map(_.left.get shouldEqual createBookingResponse)
  }

  "activityPreSaveStage fails with CreateBookingResponse" in {
    val fixture = new Fixture {}
    import fixture._

    when(activityPreSaveStageMock.process(multiProductPreSaveRequest))
      .thenReturn(Future.successful(Left(createBookingResponse)))
    facade.preSave(multiProductPreSaveRequest).map(_.left.get shouldEqual createBookingResponse)
  }

  "protectionPreSaveStage fails with CreateBookingResponse" in {
    val fixture = new Fixture {}
    import fixture._

    when(protectionPreSaveStageMock.process(multiProductPreSaveRequestWithDeps))
      .thenReturn(Future.successful(Left(createBookingResponse)))
    facade.preSave(multiProductPreSaveRequest).map(_.left.get shouldEqual createBookingResponse)
  }

  "cegFastTrackPreSaveStage fails with CreateBookingResponse" in {
    val fixture = new Fixture {}
    import fixture._

    when(cegFastTrackPreSaveStageMock.process(multiProductPreSaveRequest))
      .thenReturn(Future.successful(Left(createBookingResponse)))
    facade.preSave(multiProductPreSaveRequest).map(_.left.get shouldEqual createBookingResponse)
  }

  "addOnPreSaveStage fails with CreateBookingResponse" in {
    val fixture = new Fixture {}
    import fixture._

    when(addOnPreSaveStageMock.process(multiProductPreSaveRequest))
      .thenReturn(Future.successful(Left(createBookingResponse)))
    facade.preSave(multiProductPreSaveRequest).map(_.left.get shouldEqual createBookingResponse)
  }
}
