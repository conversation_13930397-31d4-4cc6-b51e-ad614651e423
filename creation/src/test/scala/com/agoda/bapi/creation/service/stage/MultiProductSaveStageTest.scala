package com.agoda.bapi.creation.service.stage

import akka.actor.Scheduler
import com.agoda.bapi.agent.common.schema.{AgentBookingActionMessage, BookingActionMessageTopic, ProvisioningResult}
import com.agoda.bapi.common.MockRequestContext
import com.agoda.bapi.common.config.KillSwitches
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.{ActivityBooking, CegFastTrackCreateResult, CreateBookingResponse, CreatedBookingStatus, FlightBooking, GenericProductCreateResult, HotelBooking, InstantBook, Itinerary, ProtectionBooking, VehicleBooking}
import com.agoda.bapi.common.message.token.TokenMessage
import com.agoda.bapi.common.message.{ActivityBookingStateWithItinerary, AddOnBookingStateWithItinerary, CegFastTrackBookingStateWithItinerary}
import com.agoda.bapi.common.model.WhiteLabel.{<PERSON>go<PERSON>, WhiteLabel}
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.common.model.car.VehicleBookingState
import com.agoda.bapi.common.model.car.VehicleBookingStateModel.VehicleModelBooking
import com.agoda.bapi.common.model.flight.flightModel.FlightModelInternal
import com.agoda.bapi.common.model.itinerary.ItineraryInternalModel
import com.agoda.bapi.common.model.multiproduct.{MultiProductBookingGroupDBModel, MultiProductInfoDBModel}
import com.agoda.bapi.common.model.product.ProductTypeEnum
import com.agoda.bapi.common.model.protection.ProtectionBookingStateModel.ProtectionModelInternal
import com.agoda.bapi.common.model.{ItineraryId, StatusToken}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.util.{ItineraryAssociatedBookingsTokenUtils, ServerUtils}
import com.agoda.bapi.creation.config.DuplicateConfig
import com.agoda.mpb.common.WorkflowId
import com.agoda.bapi.creation.model.CreateRequest
import com.agoda.bapi.creation.model.db.{BookingActionState, DuplicatedBookingCandidate, MultiProductBookingInsertionModel}
import com.agoda.bapi.creation.model.multi.MultiProductSaveStageRequest
import com.agoda.bapi.creation.repository._
import com.agoda.bapi.creation.service.{BookingActionMessageService, HadoopMessagingService, OrchestrationMessageService, UrlService}
import com.agoda.bapi.creation.util.{DuplicateCheckUtil, HashingUtils}
import com.agoda.bapi.creation.{CreateMultiBookingHelper, ExternalDIHelper}
import com.agoda.mpb.common.MultiProductType
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpbe.state.itinerary.ItineraryState
import com.agoda.mpbe.state.product.activity.ActivityProductModel
import com.agoda.mpbe.state.product.cegFastTrack.CegFastTrackProductModel
import mocks.DBBookingModelHelper
import mocks.MeasurementStubs.{logBookingCreationLogMessageBaseStub, measureStub}
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.{any, eq => eqTo, _}
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfterEach
import org.scalatest.concurrent.Eventually
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar
import scalapb.json4s.JsonFormat

import scala.concurrent.Future
import scala.concurrent.duration._
import scala.util.Try

class MultiProductSaveStageTest
    extends AsyncWordSpec
    with Matchers
    with MockitoSugar
    with BeforeAndAfterEach
    with CreateMultiBookingHelper
    with DBBookingModelHelper
    with ExternalDIHelper
    with Eventually {
  val mockFlightBookingActionState       = mock[BookingActionState]
  val mockVehicleBookingActionState      = mock[BookingActionState]
  val mockProtectionBookingActionState   = mock[BookingActionState]
  val mockActivityBookingActionState     = mock[BookingActionState]
  val mockCegFastTrackBookingActionState = mock[BookingActionState]
  val mockAddOnBookingActionState        = mock[BookingActionState]
  val mockItineraryState                 = mock[ItineraryState](RETURNS_DEEP_STUBS)
  val mockProtectionAddOnItineraryState  = mock[ItineraryState](RETURNS_DEEP_STUBS)
  val mockCegFastTrackItineraryState     = mock[ItineraryState](RETURNS_DEEP_STUBS)
  val mockFeatureAware                   = mock[FeatureAware]
  val killSwitches                       = mock[KillSwitches]
  val itineraryId                        = 1L
  val actionId                           = 100L
  val actionId2                          = 101L
  val multiProductId                     = 1000L
  val url                                = (_: Long) => "mockUrl"
  val masterAction                       = mockBookingWorkflowAction.copy(bookingId = None)
  val flightAction                       = mockBookingWorkflowAction.copy(bookingId = Some(mockFlightModelInternal.bookingId))
  val propertyAction =
    mockBookingWorkflowAction.copy(
      actionId = actionId,
      bookingId = Some(mockNewPropertyBookingObjectForEbeLite.booking.bookingId)
    )
  val propertyAction2 =
    mockBookingWorkflowAction.copy(
      actionId = actionId2,
      bookingId = Some(mockNewPropertyBookingObjectForEbeLite.booking.bookingId + 1)
    )
  val vehicleAction  = mockBookingWorkflowAction.copy(bookingId = Some(mockVehicleModelBooking.vehicleBookingId))
  val activityAction = mockBookingWorkflowAction.copy(bookingId = Some(mockActivityBaseBooking.bookingId))
  val cegFastTrackAction = mockBookingWorkflowAction.copy(
    bookingId = Some(mockCegFastTrackBaseBooking.bookingId),
    productTypeId = Some(MultiProductType.CEGFastTrack.id)
  )
  val protectionAddOnAction = mockBookingWorkflowAction.copy(
    bookingId = Some(mockProtectionBaseBooking.bookingId),
    productTypeId = Some(MultiProductType.SingleProtection.id)
  )
  val bookingDetailToken         = TokenMessage("mockbookingDetailToken", 1)
  val multiProductInfos          = Seq(MultiProductInfoDBModel(multiProductId, MultiProductType.Package))
  val singlePropertyProductInfos = Seq(MultiProductInfoDBModel(multiProductId, MultiProductType.SingleProperty))
  val multiProductBookingGroupSingleProperty =
    MultiProductBookingGroupDBModel(propertyAction.bookingId.get, propertyAction.itineraryId, 123456, Some(66666))
  val multiProductBookingGroupSingleProperty2 =
    MultiProductBookingGroupDBModel(propertyAction2.bookingId.get, propertyAction2.itineraryId, 123456, Some(66666))
  val multiProductBookingGroupSingleFlight =
    MultiProductBookingGroupDBModel(flightAction.bookingId.get, flightAction.itineraryId, 123456, Some(66666))
  val statusToken = StatusToken(
    itineraryId,
    masterAction.actionId,
    Set(
      ProductTypeEnum.Flight.toString,
      ProductTypeEnum.Property.toString,
      ProductTypeEnum.Protection.toString,
      ProductTypeEnum.Car.toString
    ),
    ServerUtils.serverDc()
  )
  val updatedItineraryModel =
    mockItineraryInternalModel.copy(itinerary = mockItineraryInternalModel.itinerary.copy(itineraryId = itineraryId))
  val baseRequest = MultiProductSaveStageRequest(
    request = baseMultiProductReq,
    requestContext = baseRequestContext.copy(featureAware = Some(mockFeatureAware)),
    saveBookingModel = MultiProductBookingInsertionModel(
      multiProductsInfo = multiProductInfos,
      workflowActions = Seq(
        masterAction,
        flightAction,
        propertyAction,
        activityAction,
        vehicleAction,
        cegFastTrackAction,
        protectionAddOnAction
      ),
      itineraryModel = updatedItineraryModel,
      flightBookingActionStates = Seq(mockFlightBookingActionState),
      propertyModels = Seq(mockNewPropertyBookingObjectForEbeLite),
      vehicleBookingActionStates = Seq(mockVehicleBookingActionState),
      protectionBookingActionStates = Seq(mockProtectionBookingActionState),
      multiProductBookingGroups = Seq(multiProductBookingGroupSingleFlight, multiProductBookingGroupSingleProperty),
      activityBookingActionState = Seq(mockActivityBookingActionState),
      cegFastTrackBookingActionState = Seq(mockCegFastTrackBookingActionState),
      addOnsBookingActionState = Seq(mockAddOnBookingActionState)
    ),
    statusToken = statusToken
  )
  val japanicanBaseRequest = MultiProductSaveStageRequest(
    request = baseMultiProductReq,
    requestContext = baseRequestContext
      .copy(featureAware = Some(mockFeatureAware), whiteLabelInfo = MockRequestContext.japanicanWhiteLabelInfo),
    saveBookingModel = MultiProductBookingInsertionModel(
      multiProductsInfo = multiProductInfos,
      workflowActions = Seq(
        masterAction,
        flightAction,
        propertyAction,
        activityAction,
        vehicleAction,
        cegFastTrackAction,
        protectionAddOnAction
      ),
      itineraryModel = mockItineraryInternalModel
        .copy(itinerary = mockItineraryInternalModel.itinerary.copy(itineraryId = itineraryId)),
      flightBookingActionStates = Seq(mockFlightBookingActionState),
      propertyModels = Seq(mockNewPropertyBookingObjectForEbeLite),
      vehicleBookingActionStates = Seq(mockVehicleBookingActionState),
      protectionBookingActionStates = Seq(mockProtectionBookingActionState),
      multiProductBookingGroups = Seq(multiProductBookingGroupSingleFlight, multiProductBookingGroupSingleProperty),
      activityBookingActionState = Seq(mockActivityBookingActionState),
      cegFastTrackBookingActionState = Seq(mockCegFastTrackBookingActionState),
      addOnsBookingActionState = Seq(mockAddOnBookingActionState)
    ),
    statusToken = statusToken
  )

  val rurubuBaseRequest = MultiProductSaveStageRequest(
    request = baseMultiProductReq,
    requestContext = baseRequestContext
      .copy(featureAware = Some(mockFeatureAware), whiteLabelInfo = MockRequestContext.rurubuWhiteLabelInfo),
    saveBookingModel = MultiProductBookingInsertionModel(
      multiProductsInfo = multiProductInfos,
      workflowActions = Seq(
        masterAction,
        flightAction,
        propertyAction,
        activityAction,
        vehicleAction,
        cegFastTrackAction,
        protectionAddOnAction
      ),
      itineraryModel = mockItineraryInternalModel
        .copy(itinerary = mockItineraryInternalModel.itinerary.copy(itineraryId = itineraryId)),
      flightBookingActionStates = Seq(mockFlightBookingActionState),
      propertyModels = Seq(mockNewPropertyBookingObjectForEbeLite),
      vehicleBookingActionStates = Seq(mockVehicleBookingActionState),
      protectionBookingActionStates = Seq(mockProtectionBookingActionState),
      multiProductBookingGroups = Seq(multiProductBookingGroupSingleFlight, multiProductBookingGroupSingleProperty),
      activityBookingActionState = Seq(mockActivityBookingActionState),
      cegFastTrackBookingActionState = Seq(mockCegFastTrackBookingActionState),
      addOnsBookingActionState = Seq(mockAddOnBookingActionState)
    ),
    statusToken = statusToken
  )

  val singlePropertyRequest = MultiProductSaveStageRequest(
    request = baseMultiProductReq.copy(instantBook = Some(InstantBook(true, 60000))),
    requestContext = baseRequestContext.copy(featureAware = Some(mockFeatureAware)),
    saveBookingModel = MultiProductBookingInsertionModel(
      multiProductsInfo = singlePropertyProductInfos,
      workflowActions =
        Seq(masterAction.copy(productTypeId = Some(MultiProductType.SingleProperty.id)), propertyAction),
      itineraryModel = mockItineraryInternalModel
        .copy(itinerary = mockItineraryInternalModel.itinerary.copy(itineraryId = itineraryId)),
      flightBookingActionStates = Seq.empty,
      propertyModels = Seq(mockNewPropertyBookingObjectForEbeLite),
      vehicleBookingActionStates = Seq.empty,
      protectionBookingActionStates = Seq.empty,
      multiProductBookingGroups = Seq(multiProductBookingGroupSingleProperty.copy(packageId = None)),
      activityBookingActionState = Seq.empty,
      cegFastTrackBookingActionState = Seq.empty,
      addOnsBookingActionState = Seq.empty
    ),
    statusToken = statusToken
  )

  val multiPropertyRequest = MultiProductSaveStageRequest(
    request = baseMultiProductReq,
    requestContext = baseRequestContext.copy(featureAware = Some(mockFeatureAware)),
    saveBookingModel = MultiProductBookingInsertionModel(
      multiProductsInfo = multiProductInfos,
      workflowActions = Seq(
        masterAction.copy(productTypeId = Some(MultiProductType.MultiProperties.id)),
        propertyAction,
        propertyAction2
      ),
      itineraryModel = mockItineraryInternalModel
        .copy(itinerary = mockItineraryInternalModel.itinerary.copy(itineraryId = itineraryId)),
      flightBookingActionStates = Seq.empty,
      propertyModels = Seq(mockNewPropertyBookingObjectForEbeLite),
      vehicleBookingActionStates = Seq.empty,
      protectionBookingActionStates = Seq.empty,
      multiProductBookingGroups = Seq(multiProductBookingGroupSingleProperty, multiProductBookingGroupSingleProperty2),
      activityBookingActionState = Seq.empty,
      cegFastTrackBookingActionState = Seq.empty,
      addOnsBookingActionState = Seq.empty
    ),
    statusToken = statusToken
  )

  override def beforeEach(): Unit = {
    reset(
      killSwitches,
      mockFeatureAware,
      mockFlightBookingActionState,
      mockVehicleBookingActionState,
      mockProtectionBookingActionState,
      mockActivityBookingActionState,
      mockCegFastTrackBookingActionState,
      mockAddOnBookingActionState,
      mockItineraryState,
      mockProtectionAddOnItineraryState,
      mockCegFastTrackItineraryState
    )

    when(mockFlightBookingActionState.bookingState).thenReturn(Some(mockFlightModelInternal))
    when(mockVehicleBookingActionState.vehicleBookingState).thenReturn(Some(mockVehicleModelInternal))
    when(mockProtectionBookingActionState.tripProtectionBookingState).thenReturn(Some(mockNewProtectionModelInternal))
    when(mockActivityBookingActionState.itineraryState).thenReturn(mockItineraryState)
    when(mockCegFastTrackBookingActionState.itineraryState).thenReturn(mockCegFastTrackItineraryState)
    when(mockAddOnBookingActionState.itineraryState).thenReturn(mockProtectionAddOnItineraryState)
    when(mockItineraryState.product).thenReturn(itineraryProductModel)
    when(mockProtectionAddOnItineraryState.product).thenReturn(addOnProductModel)
    when(mockCegFastTrackItineraryState.product).thenReturn(cegFastTrackProductModel)
    when(mockItineraryState.itinerary).thenReturn(com.agoda.mpbe.state.itinerary.ItineraryModel.defaultInstance)
    when(mockProtectionAddOnItineraryState.itinerary).thenReturn(
      com.agoda.mpbe.state.itinerary.ItineraryModel.defaultInstance
    )
    when(mockCegFastTrackItineraryState.itinerary).thenReturn(
      com.agoda.mpbe.state.itinerary.ItineraryModel.defaultInstance
    )
  }

  private trait MultiProductSaveStageTestFixture {
    val urlService                     = mock[UrlService]
    val flightBookingRepository        = mock[FlightBookingRepository]
    val multiProductRepository         = mock[MultiProductRepository]
    val orchestrationMessageService    = mock[OrchestrationMessageService]
    val bookingDetailTokenCreatorUtils = mock[ItineraryAssociatedBookingsTokenUtils]
    val vehicleBookingRepository       = mock[VehicleBookingRepository]
    val bookingActionMessageService    = mock[BookingActionMessageService]
    val hadoopMessaging                = mock[HadoopMessagingService]
    val activityBookingRepository      = mock[ActivityBookingRepository]
    val cegFastTrackRepository         = mock[CegFastTrackRepository]
    val genericAddOnRepository         = mock[GenericAddOnRepository]
    val itineraryBookingRepository     = mock[ItineraryBookingRepository]
    val creationMdbRepository          = mock[CreationMdbRepository]
    val scheduler                      = mock[Scheduler]
    val duplicateConfig                = mock[DuplicateConfig]
    val creationCdbRepository          = mock[CreationCdbRepository]

    val processor = spy(
      new MultiProductSaveStage(
        urlService,
        flightBookingRepository,
        multiProductRepository,
        orchestrationMessageService,
        bookingDetailTokenCreatorUtils,
        vehicleBookingRepository,
        bookingActionMessageService,
        hadoopMessaging,
        activityBookingRepository,
        itineraryBookingRepository,
        creationMdbRepository,
        cegFastTrackRepository,
        genericAddOnRepository,
        scheduler,
        duplicateConfig,
        creationCdbRepository
      )(killSwitches)
    )

    when(duplicateConfig.salt).thenReturn("salt")
    when(hadoopMessaging.sendBapiCreateFactLogMessage(any, any[CreateBookingResponse], any[String]))
      .thenReturn(Future.successful(()))
    when(hadoopMessaging.sendBookingActionSizeMessage(any(), any(), any(), any(), any(), any(), any()))
      .thenReturn(Future.unit)

    when(
      bookingDetailTokenCreatorUtils.create(
        itineraryId,
        Seq(mockFlightModelInternal.bookingId.toInt),
        Seq(mockNewPropertyBookingObjectForEbeLite.booking.bookingId.toInt)
      )
    ).thenReturn(Try(bookingDetailToken))

    when(
      bookingDetailTokenCreatorUtils.create(
        itineraryId,
        Seq.empty,
        Seq(mockNewPropertyBookingObjectForEbeLite.booking.bookingId.toInt)
      )
    ).thenReturn(Try(bookingDetailToken))

    when(bookingActionMessageService.replyMessage(any(), any(), any())).thenReturn(Future.unit)
    when(
      genericAddOnRepository.sendModelForReplication(
        any[AddOnBookingStateWithItinerary],
        any[BookingWorkflowAction],
        any[Seq[MultiProductInfoDBModel]]
      )
    ).thenReturn(Future.successful())
    when(duplicateConfig.salt).thenReturn("salt")
    when(creationCdbRepository.getMasterHotelIdFromJTBMapping(any()))
      .thenReturn(Future.successful(Some(mockNewPropertyBookingObjectForEbeLite.summary.get.masterHotelId)))
  }

  private trait InstantBookFixture {
    val expectedBAMTopic: Int = BookingActionMessageTopic.BAM_Topic_ProvisioningResult.value
    val expectedAgentBAMessage: AgentBookingActionMessage =
      AgentBookingActionMessage(provisioningResult = ProvisioningResult.ProvisioningUnconfirmed)
    val expectedBAMContent: String = JsonFormat.toJsonString(expectedAgentBAMessage)
  }

  "process" should {
    Seq(true, false).foreach { stopBfdbReplication =>
      s"return correctly when whitelable is Japanican when MPBE-4506 is $stopBfdbReplication" in {
        val fixture = new MultiProductSaveStageTestFixture {}
        import fixture._
        when(mockFeatureAware.stopBfdbReplication).thenReturn(stopBfdbReplication)
        implicit val measurement = japanicanBaseRequest.measurementsContext
        when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
          .thenReturn(Future.successful(url))
        if (!stopBfdbReplication) {
          when(
            flightBookingRepository
              .sendFlightModelForReplication(
                any[FlightModelInternal],
                any[Seq[MultiProductInfoDBModel]],
                any(),
                any[Seq[MultiProductBookingGroupDBModel]]
              )
          ).thenReturn(Future.successful())
          when(
            vehicleBookingRepository.sendVehicleModelForReplication(
              any[VehicleBookingState],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]],
              any()
            )
          ).thenReturn(Future.successful())
          when(
            activityBookingRepository.sendModelForReplication(
              any[ActivityBookingStateWithItinerary],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]]
            )
          ).thenReturn(Future.successful())
          when(
            cegFastTrackRepository.sendModelForReplication(
              any[CegFastTrackBookingStateWithItinerary],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]]
            )
          ).thenReturn(Future.successful())
        }

        when(
          vehicleBookingRepository.sendBapiCreateVehicleBookingMessage(
            any[VehicleModelBooking],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          flightBookingRepository.sendBapiCreateFlightBookingMessage(
            any[FlightModelInternal],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          flightBookingRepository.sendBapiCreateFlightBookingExperimentMessage(any[FlightModelInternal])
        ).thenReturn(Future.successful())
        when(
          activityBookingRepository.sendBapiCreateBookingMessage(
            any[ActivityProductModel],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          cegFastTrackRepository.sendBapiCreateBookingMessage(
            any[CegFastTrackProductModel],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())

        when(multiProductRepository.insertMultiBooking(any[MultiProductBookingInsertionModel])(any[RequestContext]))
          .thenReturn(Future.successful(japanicanBaseRequest.saveBookingModel))
        when(
          orchestrationMessageService.sendCreateBookingMessage(
            any[CreateRequest],
            any[ItineraryId],
            any[Option[Long]],
            any[Option[String]]
          )
        )
          .thenReturn(Future.successful())
        processor.process(japanicanBaseRequest).map { result =>
          verify(urlService, times(1))
            .getSelfServiceURLs(
              eqTo(japanicanBaseRequest.requestContext.whiteLabelInfo.whiteLabelId),
              eqTo(false),
              any()
            )(any())
          verify(multiProductRepository, times(1))
            .insertMultiBooking(eqTo(japanicanBaseRequest.saveBookingModel))(any[RequestContext])
          verify(processor, times(1))
            .sendFlightHadoopMessage(eqTo(japanicanBaseRequest.saveBookingModel), eqTo(stopBfdbReplication))(
              eqTo(measurement)
            )
          verify(processor, times(1)).mapSuccessCreateBookingResponse(eqTo(japanicanBaseRequest), eqTo(url))
          verify(orchestrationMessageService, times(1))
            .sendCreateBookingMessage(eqTo(japanicanBaseRequest), eqTo(itineraryId), any(), any())
          if (stopBfdbReplication)
            verify(flightBookingRepository, never())
              .sendFlightModelForReplication(any[FlightModelInternal], any(), any(), any())
          else
            verify(flightBookingRepository, times(1))
              .sendFlightModelForReplication(any[FlightModelInternal], eqTo(multiProductInfos), any(), any())
          verify(flightBookingRepository, times(1))
            .sendBapiCreateFlightBookingMessage(any[FlightModelInternal], any[BookingWorkflowAction])
          verify(flightBookingRepository, times(1))
            .sendBapiCreateFlightBookingExperimentMessage(any[FlightModelInternal])
          if (stopBfdbReplication)
            verify(cegFastTrackRepository, never())
              .sendModelForReplication(
                any[CegFastTrackBookingStateWithItinerary],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]]
              )
          else
            verify(cegFastTrackRepository, times(1))
              .sendModelForReplication(
                any[CegFastTrackBookingStateWithItinerary],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]]
              )
          verify(cegFastTrackRepository, times(1))
            .sendBapiCreateBookingMessage(any[CegFastTrackProductModel], any[BookingWorkflowAction])
          if (stopBfdbReplication)
            verify(genericAddOnRepository, never())
              .sendModelForReplication(
                any[AddOnBookingStateWithItinerary],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]]
              )
          else
            verify(genericAddOnRepository, times(1))
              .sendModelForReplication(
                any[AddOnBookingStateWithItinerary],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]]
              )
          verify(bookingActionMessageService, never())
            .replyMessage(any(), any(), any())

          result.isRight shouldBe true
        }
      }
    }

    Seq(true, false).foreach { stopBfdbReplication =>
      s"return correctly when whitelable is Rurubu when MPBE-4506 is $stopBfdbReplication" in {
        val fixture = new MultiProductSaveStageTestFixture {}
        import fixture._
        when(mockFeatureAware.stopBfdbReplication).thenReturn(stopBfdbReplication)
        implicit val measurement    = rurubuBaseRequest.measurementsContext
        implicit val requestContext = baseRequest.requestContext
        when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
          .thenReturn(Future.successful(url))
        if (!stopBfdbReplication) {
          when(
            flightBookingRepository
              .sendFlightModelForReplication(
                any[FlightModelInternal],
                any[Seq[MultiProductInfoDBModel]],
                any(),
                any[Seq[MultiProductBookingGroupDBModel]]
              )
          ).thenReturn(Future.successful())
          when(
            vehicleBookingRepository.sendVehicleModelForReplication(
              any[VehicleBookingState],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]],
              any()
            )
          ).thenReturn(Future.successful())
          when(
            activityBookingRepository.sendModelForReplication(
              any[ActivityBookingStateWithItinerary],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]]
            )
          ).thenReturn(Future.successful())
          when(
            cegFastTrackRepository.sendModelForReplication(
              any[CegFastTrackBookingStateWithItinerary],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]]
            )
          ).thenReturn(Future.successful())
        }

        when(
          vehicleBookingRepository.sendBapiCreateVehicleBookingMessage(
            any[VehicleModelBooking],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          flightBookingRepository.sendBapiCreateFlightBookingMessage(
            any[FlightModelInternal],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          flightBookingRepository.sendBapiCreateFlightBookingExperimentMessage(any[FlightModelInternal])
        ).thenReturn(Future.successful())
        when(
          activityBookingRepository.sendBapiCreateBookingMessage(
            any[ActivityProductModel],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          cegFastTrackRepository.sendBapiCreateBookingMessage(
            any[CegFastTrackProductModel],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())

        when(multiProductRepository.insertMultiBooking(any[MultiProductBookingInsertionModel])(any[RequestContext]))
          .thenReturn(Future.successful(rurubuBaseRequest.saveBookingModel))
        when(
          orchestrationMessageService.sendCreateBookingMessage(
            any[CreateRequest],
            any[ItineraryId],
            any[Option[Long]],
            any[Option[String]]
          )
        )
          .thenReturn(Future.successful())
        processor.process(rurubuBaseRequest).map { result =>
          verify(urlService, times(1))
            .getSelfServiceURLs(eqTo(rurubuBaseRequest.requestContext.whiteLabelInfo.whiteLabelId), eqTo(false), any())(
              any()
            )
          verify(multiProductRepository, times(1))
            .insertMultiBooking(eqTo(rurubuBaseRequest.saveBookingModel))(any[RequestContext])
          verify(processor, times(1))
            .sendFlightHadoopMessage(eqTo(rurubuBaseRequest.saveBookingModel), eqTo(stopBfdbReplication))(
              eqTo(measurement)
            )
          verify(processor, times(1)).mapSuccessCreateBookingResponse(eqTo(rurubuBaseRequest), eqTo(url))
          verify(orchestrationMessageService, times(1))
            .sendCreateBookingMessage(eqTo(rurubuBaseRequest), eqTo(itineraryId), any(), any())
          if (stopBfdbReplication)
            verify(flightBookingRepository, never())
              .sendFlightModelForReplication(any[FlightModelInternal], any(), any(), any())
          else
            verify(flightBookingRepository, times(1))
              .sendFlightModelForReplication(any[FlightModelInternal], eqTo(multiProductInfos), any(), any())
          verify(flightBookingRepository, times(1))
            .sendBapiCreateFlightBookingMessage(any[FlightModelInternal], any[BookingWorkflowAction])
          verify(flightBookingRepository, times(1))
            .sendBapiCreateFlightBookingExperimentMessage(any[FlightModelInternal])
          if (stopBfdbReplication)
            verify(cegFastTrackRepository, never())
              .sendModelForReplication(
                any[CegFastTrackBookingStateWithItinerary],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]]
              )
          else
            verify(cegFastTrackRepository, times(1))
              .sendModelForReplication(
                any[CegFastTrackBookingStateWithItinerary],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]]
              )
          if (stopBfdbReplication)
            verify(genericAddOnRepository, never())
              .sendModelForReplication(
                any[AddOnBookingStateWithItinerary],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]]
              )
          else
            verify(genericAddOnRepository, times(1))
              .sendModelForReplication(
                any[AddOnBookingStateWithItinerary],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]]
              )
          verify(cegFastTrackRepository, times(1))
            .sendBapiCreateBookingMessage(any[CegFastTrackProductModel], any[BookingWorkflowAction])
          verify(bookingActionMessageService, never())
            .replyMessage(any(), any(), any())
          result.isRight shouldBe true
        }
      }
    }

    Seq(true, false).foreach { stopBfdbReplication =>
      s"return correctly when MPBE-4506 is $stopBfdbReplication" in {
        val fixture = new MultiProductSaveStageTestFixture {}
        import fixture._
        when(mockFeatureAware.stopBfdbReplication).thenReturn(stopBfdbReplication)
        implicit val measurement    = baseRequest.measurementsContext
        implicit val requestContext = baseRequest.requestContext

        val summary = mockNewPropertyBookingObjectForEbeLite.summary.get

        val hashedString = DuplicateCheckUtil.hashPiiData(
          guestFirstName = summary.guestFirstName,
          guestLastName = summary.guestLastName,
          guestKanaFirstname = None,
          guestKanaLastname = None
        )("salt")

        when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
          .thenReturn(Future.successful(url))
        if (!stopBfdbReplication) {
          when(
            flightBookingRepository
              .sendFlightModelForReplication(
                any[FlightModelInternal],
                any[Seq[MultiProductInfoDBModel]],
                any(),
                any()
              )
          ).thenReturn(Future.successful())
          when(
            vehicleBookingRepository.sendVehicleModelForReplication(
              any[VehicleBookingState],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]],
              any()
            )
          ).thenReturn(Future.successful())
          when(
            activityBookingRepository.sendModelForReplication(
              any[ActivityBookingStateWithItinerary],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]]
            )
          ).thenReturn(Future.successful())
          when(
            cegFastTrackRepository.sendModelForReplication(
              any[CegFastTrackBookingStateWithItinerary],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]]
            )
          ).thenReturn(Future.successful())
        }

        when(
          vehicleBookingRepository.sendBapiCreateVehicleBookingMessage(
            any[VehicleModelBooking],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          flightBookingRepository.sendBapiCreateFlightBookingMessage(
            any[FlightModelInternal],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          flightBookingRepository.sendBapiCreateFlightBookingExperimentMessage(any[FlightModelInternal])
        ).thenReturn(Future.successful())

        when(
          activityBookingRepository.sendBapiCreateBookingMessage(
            any[ActivityProductModel],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          cegFastTrackRepository.sendBapiCreateBookingMessage(
            any[CegFastTrackProductModel],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())

        when(multiProductRepository.insertMultiBooking(any[MultiProductBookingInsertionModel])(any[RequestContext]))
          .thenReturn(Future.successful(baseRequest.saveBookingModel))
        when(
          orchestrationMessageService.sendCreateBookingMessage(
            any[CreateRequest],
            any[ItineraryId],
            any[Option[Long]],
            any[Option[String]]
          )
        )
          .thenReturn(Future.successful())
        processor.process(baseRequest).map { result =>
          eventually(timeout(5.seconds)) {
            implicit val requestContext = baseRequest.requestContext
            verify(urlService, times(1))
              .getSelfServiceURLs(eqTo(requestContext.whiteLabelInfo.whiteLabelId), eqTo(false), any())(any())
            verify(multiProductRepository, times(1))
              .insertMultiBooking(eqTo(baseRequest.saveBookingModel))(any[RequestContext])
            verify(processor, times(1))
              .sendFlightHadoopMessage(eqTo(baseRequest.saveBookingModel), eqTo(stopBfdbReplication))(
                eqTo(measurement)
              )
            verify(processor, times(1)).mapSuccessCreateBookingResponse(eqTo(baseRequest), eqTo(url))
            verify(processor, never())
              .sendItineraryReplicateMessage(any())(any())
            verify(orchestrationMessageService, times(1))
              .sendCreateBookingMessage(eqTo(baseRequest), eqTo(itineraryId), any(), any())
            if (stopBfdbReplication)
              verify(flightBookingRepository, never())
                .sendFlightModelForReplication(any[FlightModelInternal], any(), any(), any())
            else
              verify(flightBookingRepository, times(1))
                .sendFlightModelForReplication(any[FlightModelInternal], eqTo(multiProductInfos), any(), any())
            verify(flightBookingRepository, times(1))
              .sendBapiCreateFlightBookingMessage(any[FlightModelInternal], any[BookingWorkflowAction])
            verify(flightBookingRepository, times(1))
              .sendBapiCreateFlightBookingExperimentMessage(any[FlightModelInternal])
            if (stopBfdbReplication)
              verify(cegFastTrackRepository, never())
                .sendModelForReplication(
                  any[CegFastTrackBookingStateWithItinerary],
                  any[BookingWorkflowAction],
                  any[Seq[MultiProductInfoDBModel]]
                )
            else
              verify(cegFastTrackRepository, times(1))
                .sendModelForReplication(
                  any[CegFastTrackBookingStateWithItinerary],
                  any[BookingWorkflowAction],
                  any[Seq[MultiProductInfoDBModel]]
                )
            verify(cegFastTrackRepository, times(1))
              .sendBapiCreateBookingMessage(any[CegFastTrackProductModel], any[BookingWorkflowAction])
            if (stopBfdbReplication)
              verify(genericAddOnRepository, never())
                .sendModelForReplication(
                  any[AddOnBookingStateWithItinerary],
                  any[BookingWorkflowAction],
                  any[Seq[MultiProductInfoDBModel]]
                )
            else
              verify(genericAddOnRepository, times(1))
                .sendModelForReplication(
                  any[AddOnBookingStateWithItinerary],
                  any[BookingWorkflowAction],
                  any[Seq[MultiProductInfoDBModel]]
                )
            verify(itineraryBookingRepository, never()).sendItineraryModelForReplication(
              any[ItineraryInternalModel],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]],
              any[Seq[MultiProductBookingGroupDBModel]]
            )
            verify(bookingActionMessageService, never())
              .replyMessage(any(), any(), any())
            verify(hadoopMessaging).sendBookingActionSizeMessage(
              eqTo(baseRequest.measurementsContext.correlationId),
              eqTo(masterAction.itineraryId),
              eqTo(masterAction.bookingId),
              eqTo(masterAction.productTypeId),
              eqTo(masterAction.bookingType),
              eqTo(masterAction.state.size),
              eqTo(masterAction.state.size * 2)
            )
            verify(hadoopMessaging).sendBookingActionSizeMessage(
              eqTo(baseRequest.measurementsContext.correlationId),
              eqTo(flightAction.itineraryId),
              eqTo(flightAction.bookingId),
              eqTo(flightAction.productTypeId),
              eqTo(flightAction.bookingType),
              eqTo(flightAction.state.size),
              eqTo(flightAction.state.size * 2)
            )

            verify(creationMdbRepository).insertDuplicatedBookingCandidate(
              DuplicatedBookingCandidate(
                bookingId = summary.bookingId,
                guestFirstName = summary.guestFirstName,
                guestLastName = summary.guestLastName,
                whiteLabel = requestContext.whiteLabelInfo.whiteLabelId,
                bookingDateUntil = summary.bookingDateUtil,
                bookingDateFrom = summary.checkIn,
                bookingDate = requestContext.requestedDateTime.toString(MultiProductSaveStage.BookingDateFormat),
                storefrontId = summary.storefrontId,
                languageId = summary.languageId,
                roomTypeId = summary.roomTypeId,
                userId = multiPropertyRequest.request.userId,
                kanaFirstName = None,
                kanaLastName = None,
                itineraryId = Some(1),
                affiliateTagId = None,
                hashedPii = hashedString,
                memberId = 0,
                masterHotelId = summary.masterHotelId
              ),
              Some(mockFeatureAware),
              scheduler
            )

            result.isRight shouldBe true
          }
        }
      }
    }

    Seq(true, false).foreach { stopBfdbReplication =>
      s"return correctly and hash pii has value when MPBE-4506 is $stopBfdbReplication" in {
        val fixture = new MultiProductSaveStageTestFixture {}
        import fixture._
        when(mockFeatureAware.stopBfdbReplication).thenReturn(stopBfdbReplication)
        implicit val measurement    = baseRequest.measurementsContext
        implicit val requestContext = baseRequest.requestContext

        val summary = mockNewPropertyBookingObjectForEbeLite.summary.get
        val hashString = DuplicateCheckUtil.hashPiiData(
          guestFirstName = summary.guestFirstName,
          guestLastName = summary.guestLastName,
          guestKanaFirstname = None,
          guestKanaLastname = None
        )("salt")

        when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
          .thenReturn(Future.successful(url))
        if (!stopBfdbReplication) {
          when(
            flightBookingRepository
              .sendFlightModelForReplication(
                any[FlightModelInternal],
                any[Seq[MultiProductInfoDBModel]],
                any(),
                any()
              )
          ).thenReturn(Future.successful())
          when(
            vehicleBookingRepository.sendVehicleModelForReplication(
              any[VehicleBookingState],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]],
              any()
            )
          ).thenReturn(Future.successful())
          when(
            activityBookingRepository.sendModelForReplication(
              any[ActivityBookingStateWithItinerary],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]]
            )
          ).thenReturn(Future.successful())
          when(
            cegFastTrackRepository.sendModelForReplication(
              any[CegFastTrackBookingStateWithItinerary],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]]
            )
          ).thenReturn(Future.successful())
        }

        when(
          vehicleBookingRepository.sendBapiCreateVehicleBookingMessage(
            any[VehicleModelBooking],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          flightBookingRepository.sendBapiCreateFlightBookingMessage(
            any[FlightModelInternal],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          flightBookingRepository.sendBapiCreateFlightBookingExperimentMessage(any[FlightModelInternal])
        ).thenReturn(Future.successful())
        when(
          activityBookingRepository.sendBapiCreateBookingMessage(
            any[ActivityProductModel],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          cegFastTrackRepository.sendBapiCreateBookingMessage(
            any[CegFastTrackProductModel],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())

        when(multiProductRepository.insertMultiBooking(any[MultiProductBookingInsertionModel])(any[RequestContext]))
          .thenReturn(Future.successful(baseRequest.saveBookingModel))
        when(
          orchestrationMessageService.sendCreateBookingMessage(
            any[CreateRequest],
            any[ItineraryId],
            any[Option[Long]],
            any[Option[String]]
          )
        )
          .thenReturn(Future.successful())
        processor.process(baseRequest).map { result =>
          eventually(timeout(5.seconds)) {
            implicit val requestContext = baseRequest.requestContext
            verify(urlService, times(1))
              .getSelfServiceURLs(eqTo(requestContext.whiteLabelInfo.whiteLabelId), eqTo(false), any())(any())
            verify(multiProductRepository, times(1))
              .insertMultiBooking(eqTo(baseRequest.saveBookingModel))(any[RequestContext])
            verify(processor, times(1))
              .sendFlightHadoopMessage(eqTo(baseRequest.saveBookingModel), eqTo(stopBfdbReplication))(
                eqTo(measurement)
              )
            verify(processor, times(1)).mapSuccessCreateBookingResponse(eqTo(baseRequest), eqTo(url))
            verify(processor, never())
              .sendItineraryReplicateMessage(any())(any())
            verify(orchestrationMessageService, times(1))
              .sendCreateBookingMessage(eqTo(baseRequest), eqTo(itineraryId), any(), any())
            if (stopBfdbReplication)
              verify(flightBookingRepository, never())
                .sendFlightModelForReplication(any[FlightModelInternal], any(), any(), any())
            else
              verify(flightBookingRepository, times(1))
                .sendFlightModelForReplication(any[FlightModelInternal], eqTo(multiProductInfos), any(), any())
            verify(flightBookingRepository, times(1))
              .sendBapiCreateFlightBookingMessage(any[FlightModelInternal], any[BookingWorkflowAction])
            verify(flightBookingRepository, times(1))
              .sendBapiCreateFlightBookingExperimentMessage(any[FlightModelInternal])
            if (stopBfdbReplication)
              verify(cegFastTrackRepository, never())
                .sendModelForReplication(
                  any[CegFastTrackBookingStateWithItinerary],
                  any[BookingWorkflowAction],
                  any[Seq[MultiProductInfoDBModel]]
                )
            else
              verify(cegFastTrackRepository, times(1))
                .sendModelForReplication(
                  any[CegFastTrackBookingStateWithItinerary],
                  any[BookingWorkflowAction],
                  any[Seq[MultiProductInfoDBModel]]
                )
            verify(cegFastTrackRepository, times(1))
              .sendBapiCreateBookingMessage(any[CegFastTrackProductModel], any[BookingWorkflowAction])
            if (stopBfdbReplication)
              verify(genericAddOnRepository, never())
                .sendModelForReplication(
                  any[AddOnBookingStateWithItinerary],
                  any[BookingWorkflowAction],
                  any[Seq[MultiProductInfoDBModel]]
                )
            else
              verify(genericAddOnRepository, times(1))
                .sendModelForReplication(
                  any[AddOnBookingStateWithItinerary],
                  any[BookingWorkflowAction],
                  any[Seq[MultiProductInfoDBModel]]
                )
            verify(itineraryBookingRepository, never()).sendItineraryModelForReplication(
              any[ItineraryInternalModel],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]],
              any[Seq[MultiProductBookingGroupDBModel]]
            )
            verify(bookingActionMessageService, never())
              .replyMessage(any(), any(), any())
            verify(hadoopMessaging).sendBookingActionSizeMessage(
              eqTo(baseRequest.measurementsContext.correlationId),
              eqTo(masterAction.itineraryId),
              eqTo(masterAction.bookingId),
              eqTo(masterAction.productTypeId),
              eqTo(masterAction.bookingType),
              eqTo(masterAction.state.size),
              eqTo(masterAction.state.size * 2)
            )
            verify(hadoopMessaging).sendBookingActionSizeMessage(
              eqTo(baseRequest.measurementsContext.correlationId),
              eqTo(flightAction.itineraryId),
              eqTo(flightAction.bookingId),
              eqTo(flightAction.productTypeId),
              eqTo(flightAction.bookingType),
              eqTo(flightAction.state.size),
              eqTo(flightAction.state.size * 2)
            )
            verify(creationMdbRepository).insertDuplicatedBookingCandidate(
              DuplicatedBookingCandidate(
                summary.bookingId,
                summary.guestFirstName,
                summary.guestLastName,
                requestContext.whiteLabelInfo.whiteLabelId,
                summary.bookingDateUtil,
                summary.checkIn,
                requestContext.requestedDateTime.toString(MultiProductSaveStage.BookingDateFormat),
                summary.storefrontId,
                summary.languageId,
                summary.roomTypeId,
                multiPropertyRequest.request.userId,
                None,
                None,
                Some(1),
                None,
                hashString,
                0,
                masterHotelId = summary.masterHotelId
              ),
              Some(mockFeatureAware),
              scheduler
            )

            result.isRight shouldBe true
          }
        }
      }
    }

    Seq(true, false).foreach { stopBfdbReplication =>
      s"return correctly when saving updatedSaveBookingModel when MPBE-4506 is $stopBfdbReplication" in {
        val fixture = new MultiProductSaveStageTestFixture {}
        import fixture._
        when(mockFeatureAware.stopBfdbReplication).thenReturn(stopBfdbReplication)

        implicit val measurement    = baseRequest.measurementsContext
        implicit val requestContext = baseRequest.requestContext
        val multiProduct            = Seq(mockMultiProductInfo)
        val updatedSaveBookingModel = MultiProductBookingInsertionModel(
          multiProductsInfo = multiProduct,
          workflowActions = Seq(
            mockBookingWorkflowAction,
            mockBookingWorkflowAction.copy(
              workflowId = WorkflowId.Flight.id,
              bookingId = Some(mockFlightModelInternal.bookingId),
              state = mockFlightBookingActionState.toJson
            ),
            mockBookingWorkflowAction.copy(
              workflowId = WorkflowId.Property.id,
              bookingId = Some(mockNewPropertyBookingObjectForEbeLite.booking.bookingId)
            ),
            mockBookingWorkflowAction.copy(
              workflowId = WorkflowId.Vehicle.id,
              bookingId = Some(mockVehicleModelBooking.vehicleBookingId)
            ),
            mockBookingWorkflowAction.copy(
              workflowId = WorkflowId.Activity.id,
              bookingId = Some(mockActivityBaseBooking.bookingId)
            ),
            cegFastTrackAction,
            protectionAddOnAction
          ),
          itineraryModel = mockItineraryInternalModel,
          flightBookingActionStates = Seq(mockFlightBookingActionState),
          propertyModels = Seq(mockNewPropertyBookingObjectForEbeLite),
          vehicleBookingActionStates = Seq(mockVehicleBookingActionState),
          protectionBookingActionStates = Seq(mockProtectionBookingActionState),
          activityBookingActionState = Seq(mockActivityBookingActionState),
          cegFastTrackBookingActionState = Seq(mockCegFastTrackBookingActionState),
          addOnsBookingActionState = Seq(mockAddOnBookingActionState),
          multiProductBookingGroups = Seq(multiProductBookingGroupSingleFlight, multiProductBookingGroupSingleProperty)
        )
        when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
          .thenReturn(Future.successful(url))
        if (!stopBfdbReplication) {
          when(
            flightBookingRepository
              .sendFlightModelForReplication(
                any[FlightModelInternal],
                any[Seq[MultiProductInfoDBModel]],
                any(),
                any()
              )
          ).thenReturn(Future.successful())
          when(
            vehicleBookingRepository.sendVehicleModelForReplication(
              any[VehicleBookingState],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]],
              any()
            )
          ).thenReturn(Future.successful())
          when(
            activityBookingRepository.sendModelForReplication(
              any[ActivityBookingStateWithItinerary],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]]
            )
          ).thenReturn(Future.successful())
          when(
            cegFastTrackRepository.sendModelForReplication(
              any[CegFastTrackBookingStateWithItinerary],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]]
            )
          ).thenReturn(Future.successful())
        }

        when(
          vehicleBookingRepository.sendBapiCreateVehicleBookingMessage(
            any[VehicleModelBooking],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          flightBookingRepository.sendBapiCreateFlightBookingMessage(
            any[FlightModelInternal],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          flightBookingRepository.sendBapiCreateFlightBookingExperimentMessage(any[FlightModelInternal])
        ).thenReturn(Future.successful())
        when(
          activityBookingRepository.sendBapiCreateBookingMessage(
            any[ActivityProductModel],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          cegFastTrackRepository.sendBapiCreateBookingMessage(
            any[CegFastTrackProductModel],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())

        when(multiProductRepository.insertMultiBooking(any[MultiProductBookingInsertionModel])(any[RequestContext]))
          .thenReturn(Future.successful(updatedSaveBookingModel))

        when(
          orchestrationMessageService.sendCreateBookingMessage(
            any[CreateRequest],
            any[ItineraryId],
            any[Option[Long]],
            any[Option[String]]
          )
        )
          .thenReturn(Future.successful())

        processor.process(baseRequest).map { result =>
          verify(urlService, times(1))
            .getSelfServiceURLs(eqTo(baseRequest.requestContext.whiteLabelInfo.whiteLabelId), eqTo(false), any())(any())
          verify(multiProductRepository, times(1))
            .insertMultiBooking(eqTo(baseRequest.saveBookingModel))(any[RequestContext])
          verify(processor, times(1))
            .sendFlightHadoopMessage(eqTo(baseRequest.saveBookingModel), eqTo(stopBfdbReplication))(eqTo(measurement))
          verify(processor, times(1)).mapSuccessCreateBookingResponse(eqTo(baseRequest), eqTo(url))
          verify(orchestrationMessageService, times(1))
            .sendCreateBookingMessage(eqTo(baseRequest), eqTo(itineraryId), any(), any())
          if (stopBfdbReplication)
            verify(flightBookingRepository, never())
              .sendFlightModelForReplication(any[FlightModelInternal], any(), any(), any())
          else
            verify(flightBookingRepository, times(1))
              .sendFlightModelForReplication(
                any[FlightModelInternal],
                eqTo(baseRequest.saveBookingModel.multiProductsInfo),
                any(),
                any()
              )
          verify(flightBookingRepository, times(1))
            .sendBapiCreateFlightBookingMessage(any[FlightModelInternal], any[BookingWorkflowAction])
          verify(flightBookingRepository, times(1))
            .sendBapiCreateFlightBookingExperimentMessage(any[FlightModelInternal])
          if (stopBfdbReplication)
            verify(cegFastTrackRepository, never())
              .sendModelForReplication(
                any[CegFastTrackBookingStateWithItinerary],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]]
              )
          else
            verify(cegFastTrackRepository, times(1))
              .sendModelForReplication(
                any[CegFastTrackBookingStateWithItinerary],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]]
              )
          verify(cegFastTrackRepository, times(1))
            .sendBapiCreateBookingMessage(any[CegFastTrackProductModel], any[BookingWorkflowAction])
          if (stopBfdbReplication)
            verify(genericAddOnRepository, never())
              .sendModelForReplication(
                any[AddOnBookingStateWithItinerary],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]]
              )
          else
            verify(genericAddOnRepository, times(1))
              .sendModelForReplication(
                any[AddOnBookingStateWithItinerary],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]]
              )
          verify(bookingActionMessageService, never()).replyMessage(any(), any(), any())

          result.isRight shouldBe true
        }
      }
    }

    "log hadoop when a step throws an exception" in {
      implicit val requestContext = multiPropertyRequest.requestContext

      val fixture = new MultiProductSaveStageTestFixture {}
      import fixture._
      val mockException    = new Exception()
      val expectedResponse = CreateBookingResponse.technical(ErrorCode.UnexpectedInternalError, Some(mockException))

      when(multiProductRepository.insertMultiBooking(any[MultiProductBookingInsertionModel])(any[RequestContext]))
        .thenReturn(Future.failed(mockException))

      processor.process(baseRequest).map { result =>
        verify(hadoopMessaging, times(1)).sendBapiCreateFactLogMessage(any, any[CreateBookingResponse], any[String])
        result shouldBe Left(expectedResponse)
      }
    }

    Seq(true, false).foreach { stopBfdbReplication =>
      s"return correctly for Property Only case when MPBE-4506 is $stopBfdbReplication" in {
        val fixture = new MultiProductSaveStageTestFixture {}
        import fixture._
        when(mockFeatureAware.stopBfdbReplication).thenReturn(stopBfdbReplication)
        implicit val measurement = multiPropertyRequest.measurementsContext
        val summary              = mockNewPropertyBookingObjectForEbeLite.summary.get
        val hashedString = DuplicateCheckUtil.hashPiiData(
          guestFirstName = summary.guestFirstName,
          guestLastName = summary.guestLastName,
          guestKanaFirstname = None,
          guestKanaLastname = None
        )("salt")

        when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
          .thenReturn(Future.successful(url))
        if (!stopBfdbReplication) {
          when(
            itineraryBookingRepository.sendItineraryModelForReplication(
              eqTo(multiPropertyRequest.saveBookingModel.itineraryModel),
              any[BookingWorkflowAction],
              eqTo(multiPropertyRequest.saveBookingModel.multiProductsInfo),
              eqTo(multiPropertyRequest.saveBookingModel.multiProductBookingGroups)
            )
          ).thenReturn(Future.successful())
        }

        when(multiProductRepository.insertMultiBooking(any[MultiProductBookingInsertionModel])(any[RequestContext]))
          .thenReturn(Future.successful(multiPropertyRequest.saveBookingModel))
        when(
          orchestrationMessageService.sendCreateBookingMessage(
            any[CreateRequest],
            any[ItineraryId],
            any[Option[Long]],
            any[Option[String]]
          )
        )
          .thenReturn(Future.successful())
        processor.process(multiPropertyRequest).map { result =>
          eventually(timeout(5.seconds)) {
            implicit val requestContext = multiPropertyRequest.requestContext
            verify(urlService, times(1))
              .getSelfServiceURLs(eqTo(requestContext.whiteLabelInfo.whiteLabelId), eqTo(false), any())(any())
            verify(multiProductRepository, times(1))
              .insertMultiBooking(eqTo(multiPropertyRequest.saveBookingModel))(any[RequestContext])
            verify(processor, times(1)).mapSuccessCreateBookingResponse(eqTo(multiPropertyRequest), eqTo(url))
            verify(orchestrationMessageService, times(1))
              .sendCreateBookingMessage(eqTo(multiPropertyRequest), eqTo(itineraryId), any(), any())

            // Assert that each product function called
            verify(processor)
              .sendFlightHadoopMessage(eqTo(multiPropertyRequest.saveBookingModel), eqTo(stopBfdbReplication))(
                eqTo(measurement)
              )
            if (stopBfdbReplication)
              verify(processor, never)
                .sendItineraryReplicateMessage(any())(eqTo(measurement))
            else
              verify(processor)
                .sendItineraryReplicateMessage(eqTo(multiPropertyRequest.saveBookingModel))(eqTo(measurement))
            verify(processor)
              .sendVehicleHadoopMessage(eqTo(multiPropertyRequest.saveBookingModel), eqTo(stopBfdbReplication))(
                eqTo(measurement)
              )
            verify(processor)
              .sendActivityHadoopMessage(eqTo(multiPropertyRequest.saveBookingModel), eqTo(stopBfdbReplication))(
                eqTo(measurement)
              )

            // Assert that no repository was called except ItineraryRepository
            verify(flightBookingRepository, never())
              .sendFlightModelForReplication(any(), any(), any(), any())
            verify(flightBookingRepository, never())
              .sendBapiCreateFlightBookingMessage(any(), any())
            verify(flightBookingRepository, never())
              .sendBapiCreateFlightBookingExperimentMessage(any())
            verify(bookingActionMessageService, never())
              .replyMessage(any(), any(), any())
            verify(vehicleBookingRepository, never()).sendVehicleModelForReplication(any(), any(), any(), any())
            verify(vehicleBookingRepository, never()).sendBapiCreateVehicleBookingMessage(any(), any())
            verify(activityBookingRepository, never()).sendModelForReplication(any(), any(), any())
            verify(activityBookingRepository, never()).sendBapiCreateBookingMessage(any(), any())
            verify(genericAddOnRepository, never())
              .sendModelForReplication(
                any[AddOnBookingStateWithItinerary],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]]
              )
            if (stopBfdbReplication)
              verify(itineraryBookingRepository, never()).sendItineraryModelForReplication(
                any[ItineraryInternalModel],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]],
                any[Seq[MultiProductBookingGroupDBModel]]
              )
            else
              verify(itineraryBookingRepository).sendItineraryModelForReplication(
                any[ItineraryInternalModel],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]],
                any[Seq[MultiProductBookingGroupDBModel]]
              )

            verify(creationMdbRepository).insertDuplicatedBookingCandidate(
              DuplicatedBookingCandidate(
                summary.bookingId,
                summary.guestFirstName,
                summary.guestLastName,
                requestContext.whiteLabelInfo.whiteLabelId,
                summary.bookingDateUtil,
                summary.checkIn,
                requestContext.requestedDateTime.toString(MultiProductSaveStage.BookingDateFormat),
                summary.storefrontId,
                summary.languageId,
                summary.roomTypeId,
                multiPropertyRequest.request.userId,
                kanaFirstName = None,
                kanaLastName = None,
                itineraryId = Some(1),
                affiliateTagId = None,
                hashedPii = hashedString,
                memberId = 0,
                masterHotelId = summary.masterHotelId
              ),
              Some(mockFeatureAware),
              scheduler
            )

            result.isRight shouldBe true
          }
        }
      }
    }
  }

  "instant book process" should
    Seq(true, false).foreach { stopBfdbReplication =>
      s"process correctly for single property when MPBE-4506 is $stopBfdbReplication" in {
        val fixture = new MultiProductSaveStageTestFixture {}
        import fixture._
        when(mockFeatureAware.stopBfdbReplication).thenReturn(stopBfdbReplication)
        val request                 = singlePropertyRequest
        implicit val measurement    = request.measurementsContext
        implicit val requestContext = request.requestContext
        when(urlService.getSelfServiceURLs(any[WhiteLabel], any[Boolean], any())(any()))
          .thenReturn(Future.successful(url))
        if (!stopBfdbReplication) {
          when(
            flightBookingRepository
              .sendFlightModelForReplication(
                any[FlightModelInternal],
                any[Seq[MultiProductInfoDBModel]],
                any(),
                any()
              )
          ).thenReturn(Future.successful())
          when(
            vehicleBookingRepository.sendVehicleModelForReplication(
              any[VehicleBookingState],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]],
              any()
            )
          ).thenReturn(Future.successful())
          when(
            itineraryBookingRepository.sendItineraryModelForReplication(
              eqTo(request.saveBookingModel.itineraryModel),
              any[BookingWorkflowAction],
              eqTo(request.saveBookingModel.multiProductsInfo),
              eqTo(request.saveBookingModel.multiProductBookingGroups)
            )
          ).thenReturn(Future.successful())
        }

        when(
          flightBookingRepository.sendBapiCreateFlightBookingMessage(
            any[FlightModelInternal],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          flightBookingRepository.sendBapiCreateFlightBookingExperimentMessage(
            any[FlightModelInternal]
          )
        ).thenReturn(Future.successful())

        when(multiProductRepository.insertMultiBooking(any[MultiProductBookingInsertionModel])(any[RequestContext]))
          .thenReturn(Future.successful(request.saveBookingModel))
        when(
          orchestrationMessageService.sendCreateBookingMessage(
            any[CreateRequest],
            any[ItineraryId],
            any[Option[Long]],
            any[Option[String]]
          )
        )
          .thenReturn(Future.successful())
        processor.process(request).map { result =>
          verify(urlService, times(1))
            .getSelfServiceURLs(eqTo(baseRequest.requestContext.whiteLabelInfo.whiteLabelId), eqTo(false), any())(any())
          verify(multiProductRepository, times(1))
            .insertMultiBooking(eqTo(singlePropertyRequest.saveBookingModel))(any[RequestContext])
          verify(processor, times(1))
            .sendFlightHadoopMessage(eqTo(singlePropertyRequest.saveBookingModel), eqTo(stopBfdbReplication))(
              eqTo(measurement)
            )
          if (stopBfdbReplication)
            verify(processor, never()).sendItineraryReplicateMessage(any())(any())
          else
            verify(processor)
              .sendItineraryReplicateMessage(eqTo(singlePropertyRequest.saveBookingModel))(eqTo(measurement))
          verify(processor, times(1)).mapSuccessCreateBookingResponse(eqTo(singlePropertyRequest), eqTo(url))
          verify(orchestrationMessageService, times(1))
            .sendCreateBookingMessage(eqTo(singlePropertyRequest), eqTo(itineraryId), any(), any())
          if (stopBfdbReplication)
            verify(itineraryBookingRepository, never()).sendItineraryModelForReplication(
              any[ItineraryInternalModel],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]],
              any[Seq[MultiProductBookingGroupDBModel]]
            )
          else
            verify(itineraryBookingRepository).sendItineraryModelForReplication(
              any[ItineraryInternalModel],
              any[BookingWorkflowAction],
              any[Seq[MultiProductInfoDBModel]],
              any[Seq[MultiProductBookingGroupDBModel]]
            )

          verify(bookingActionMessageService, times(1)).replyMessage(any(), any(), any())

          result.isRight shouldBe true
        }
      }
    }

  "mapSuccessCreateBookingResponse" should {
    implicit val measurement = baseRequest.measurementsContext
    val expected = Itinerary(
      itineraryId,
      Seq(
        HotelBooking(
          lineItemId = 0,
          itineraryId = mockNewPropertyBookingObjectForEbeLite.itinerary.itineraryId.toInt,
          bookingId = mockNewPropertyBookingObjectForEbeLite.booking.bookingId.toInt,
          bookingStatus = CreatedBookingStatus.BookingProcessing,
          selfServiceUrl = "mockUrl",
          stayType = Some(0)
        )
      ),
      Seq(
        FlightBooking(
          mockFlightModelInternal.bookingId.toInt,
          CreatedBookingStatus.BookingProcessing,
          None,
          None
        )
      ),
      Seq(
        VehicleBooking(
          mockVehicleModelInternal.vehicleBooking.vehicleBookingId.toInt,
          CreatedBookingStatus.BookingProcessing
        )
      ),
      Seq(
        ProtectionBooking(
          mockNewProtectionModelInternal.protectionBookingId.toInt,
          mockNewProtectionModelInternal.protectionTypeId,
          CreatedBookingStatus.BookingProcessing
        )
      ),
      Seq(
        ActivityBooking(
          mockActivityProductModel.product.booking.bookingId.toInt,
          CreatedBookingStatus.BookingProcessing
        )
      ),
      Seq(),
      cegFastTracks = Seq(
        CegFastTrackCreateResult(
          mockCegFastTrackProductModel.product.booking.bookingId.toInt,
          CreatedBookingStatus.BookingProcessing
        )
      ),
      addOns = Seq(
        GenericProductCreateResult(
          bookingId = mockProtectionAddOnProductModel.product.booking.bookingId.toInt,
          bookingStatus = CreatedBookingStatus.BookingProcessing,
          productTypeId = mockProtectionAddOnProductModel.product.booking.productTypeId
        )
      ),
      statusToken.serialize(logBookingCreationLogMessageBaseStub, measureStub),
      Some(bookingDetailToken),
      itineraryDate = mockItineraryInternalModel.itinerary.recCreatedWhen
    )

    "return correctly" in {
      val fixture = new MultiProductSaveStageTestFixture {}
      import fixture._
      val result = processor.mapSuccessCreateBookingResponse(baseRequest, url)
      result shouldBe expected
    }
  }

  "sendFlightHadoopMessage" should {
    Seq(true, false).foreach { stopBfdbReplication =>
      s"return success when MPBE-4506 is $stopBfdbReplication" in {
        val fixture = new MultiProductSaveStageTestFixture {}
        import fixture._
        implicit val measurement = baseRequest.measurementsContext
        if (!stopBfdbReplication) {
          when(
            flightBookingRepository
              .sendFlightModelForReplication(
                any[FlightModelInternal],
                any[Seq[MultiProductInfoDBModel]],
                any(),
                any[Seq[MultiProductBookingGroupDBModel]]
              )
          ).thenReturn(Future.successful())
        }
        when(
          flightBookingRepository.sendBapiCreateFlightBookingMessage(
            any[FlightModelInternal],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())
        when(
          flightBookingRepository.sendBapiCreateFlightBookingExperimentMessage(any[FlightModelInternal])
        ).thenReturn(Future.successful())

        processor.sendFlightHadoopMessage(baseRequest.saveBookingModel, stopBfdbReplication).map { _ =>
          if (stopBfdbReplication)
            verify(flightBookingRepository, never())
              .sendFlightModelForReplication(any[FlightModelInternal], any(), any(), any())
          else
            verify(flightBookingRepository, times(1))
              .sendFlightModelForReplication(any[FlightModelInternal], eqTo(multiProductInfos), any(), any())
          verify(flightBookingRepository, times(1))
            .sendBapiCreateFlightBookingMessage(any[FlightModelInternal], any[BookingWorkflowAction])
          verify(flightBookingRepository, times(1))
            .sendBapiCreateFlightBookingExperimentMessage(any[FlightModelInternal])
          succeed
        }
      }
    }

    "return exception when cannot found BookingActionId" in {
      val fixture = new MultiProductSaveStageTestFixture {}
      import fixture._
      implicit val measurement = baseRequest.measurementsContext
      val workflowActions = baseRequest.saveBookingModel.workflowActions.zipWithIndex
        .map { case (wfAction, index) => wfAction.copy(bookingId = Some(index)) }

      val input = baseRequest.saveBookingModel.copy(workflowActions = workflowActions)
      when(mockFlightBookingActionState.bookingState).thenReturn(
        Some(mockFlightModelInternal.copy(bookingId = workflowActions.flatMap(_.bookingId).max + 1))
      )

      processor.sendFlightHadoopMessage(input, true).failed.map { ex =>
        verify(flightBookingRepository, never)
          .sendFlightModelForReplication(any[FlightModelInternal], eqTo(multiProductInfos), any(), any())
        verify(flightBookingRepository, never)
          .sendBapiCreateFlightBookingMessage(any[FlightModelInternal], any[BookingWorkflowAction])
        verify(flightBookingRepository, never)
          .sendBapiCreateFlightBookingExperimentMessage(any[FlightModelInternal])

        ex shouldBe a[BookingActionNotFound]
      }
    }
  }

  "sendVehicleHadoopMessage" should
    Seq(true, false).foreach { stopBfdbReplication =>
      s"return success when MPBE-4506 is $stopBfdbReplication" in {
        val fixture = new MultiProductSaveStageTestFixture {}
        import fixture._
        implicit val measurement = baseRequest.measurementsContext
        if (!stopBfdbReplication)
          when(
            vehicleBookingRepository
              .sendVehicleModelForReplication(
                any[VehicleBookingState],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]],
                any[Seq[ProtectionModelInternal]]
              )
          ).thenReturn(Future.successful())
        when(
          vehicleBookingRepository.sendBapiCreateVehicleBookingMessage(
            any[VehicleModelBooking],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())

        processor.sendVehicleHadoopMessage(baseRequest.saveBookingModel, stopBfdbReplication).map { _ =>
          if (stopBfdbReplication)
            verify(vehicleBookingRepository, never())
              .sendVehicleModelForReplication(any[VehicleBookingState], any[BookingWorkflowAction], any(), any())
          else
            verify(vehicleBookingRepository, times(1))
              .sendVehicleModelForReplication(
                any[VehicleBookingState],
                any[BookingWorkflowAction],
                eqTo(multiProductInfos),
                any[Seq[ProtectionModelInternal]]
              )
          succeed
        }
      }
    }

  "sendActivityHadoopMessage" should
    Seq(true, false).foreach { stopBfdbReplication =>
      s"return success when MPBE-4506 is $stopBfdbReplication" in {
        val fixture = new MultiProductSaveStageTestFixture {}
        import fixture._
        implicit val measurement = baseRequest.measurementsContext

        if (!stopBfdbReplication)
          when(
            activityBookingRepository
              .sendModelForReplication(
                any[ActivityBookingStateWithItinerary],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]]
              )
          ).thenReturn(Future.successful())
        when(
          activityBookingRepository.sendBapiCreateBookingMessage(
            any[ActivityProductModel],
            any[BookingWorkflowAction]
          )
        ).thenReturn(Future.successful())

        processor.sendActivityHadoopMessage(baseRequest.saveBookingModel, stopBfdbReplication).map { _ =>
          if (stopBfdbReplication)
            verify(activityBookingRepository, never())
              .sendModelForReplication(any[ActivityBookingStateWithItinerary], any[BookingWorkflowAction], any())
          else
            verify(activityBookingRepository, times(1))
              .sendModelForReplication(
                any[ActivityBookingStateWithItinerary],
                any[BookingWorkflowAction],
                any[Seq[MultiProductInfoDBModel]]
              )
          succeed
        }
      }
    }

  // Remove when integrate MPBE-4506
  "sendAddOnReplicateMessage" should {
    "return success" in {
      val fixture = new MultiProductSaveStageTestFixture {}
      import fixture._
      implicit val measurement = baseRequest.measurementsContext
      reset(genericAddOnRepository)

      when(
        genericAddOnRepository.sendModelForReplication(
          any[AddOnBookingStateWithItinerary],
          any[BookingWorkflowAction],
          any[Seq[MultiProductInfoDBModel]]
        )
      ).thenReturn(Future.successful())

      processor.sendAddOnReplicateMessage(baseRequest.saveBookingModel).map { _ =>
        verify(genericAddOnRepository, times(1))
          .sendModelForReplication(
            any[AddOnBookingStateWithItinerary],
            eqTo(protectionAddOnAction),
            any[Seq[MultiProductInfoDBModel]]
          )
        succeed
      }
    }
  }

  "composeFlightModelInternalWithItinerary" should {
    "return correct result" in {
      val fixture = new MultiProductSaveStageTestFixture {}
      import fixture._
      val itineraryModel = baseRequest.saveBookingModel.itineraryModel
      val result         = processor.composeFlightModelInternalWithItinerary(mockFlightModelInternal, itineraryModel)
      result shouldBe mockFlightModelInternal.copy(
        payments = itineraryModel.payments,
        bookingPayments = itineraryModel.bookingPayments,
        history = itineraryModel.history,
        itinerary = itineraryModel.itinerary
      )
    }
  }

  "composeVehicleBookingStateWithItinerary" should {
    "return correct result" in {
      val fixture = new MultiProductSaveStageTestFixture {}
      import fixture._
      val itineraryModel = baseRequest.saveBookingModel.itineraryModel
      val result =
        processor.composeVehicleBookingStateWithItinerary(mockVehicleModelInternal, baseRequest.saveBookingModel)
      result shouldBe VehicleBookingState(
        Seq(mockVehicleModelInternal),
        itineraryModel.payments,
        itineraryModel.bookingPayments,
        itineraryModel.history,
        itineraryModel.itinerary,
        Seq(multiProductBookingGroupSingleFlight, multiProductBookingGroupSingleProperty)
      )
    }
  }

  "processProvisioningMessage" should {
    "call reply message correctly data for single property" in {
      implicit val mockRequestContext: RequestContext = MockRequestContext.create()
      val fixture                                     = new MultiProductSaveStageTestFixture {}
      val instantBookFixture                          = new InstantBookFixture {}
      import fixture._
      import instantBookFixture._
      val productActions = Seq(propertyAction)
      processor.processProvisioningMessage(singlePropertyRequest.saveBookingModel, Agoda)

      productActions.map(action =>
        verify(
          bookingActionMessageService,
          times(1)
        ).replyMessage(action.actionId, expectedBAMTopic, expectedBAMContent)
      )
      succeed
    }

    "call reply messages correctly data for multiple property" in {
      implicit val mockRequestContext: RequestContext = MockRequestContext.create()
      val fixture                                     = new MultiProductSaveStageTestFixture {}
      val instantBookFixture                          = new InstantBookFixture {}
      import fixture._
      import instantBookFixture._
      val productActions = Seq(propertyAction, propertyAction2)
      processor.processProvisioningMessage(multiPropertyRequest.saveBookingModel, Agoda)
      productActions.map(action =>
        verify(
          bookingActionMessageService,
          times(1)
        ).replyMessage(action.actionId, expectedBAMTopic, expectedBAMContent)
      )
      succeed
    }
  }

  "insertMultiBooking" should {
    "return result from input and overwrite some specific fields from response from multi booking repository when MPBE-4506 is A" in {
      val fixture = new MultiProductSaveStageTestFixture {}
      import fixture._
      implicit val measurement    = baseRequest.measurementsContext
      implicit val requestContext = multiPropertyRequest.requestContext

      val itineraryModelResponse = baseRequest.saveBookingModel.itineraryModel.copy(
        itinerary = baseRequest.saveBookingModel.itineraryModel.itinerary.copy(
          itineraryId = itineraryId + 1L
        )
      )
      val expectedSaveBookingModelResponse = baseRequest.saveBookingModel.copy(itineraryModel = itineraryModelResponse)
      when(multiProductRepository.insertMultiBooking(any[MultiProductBookingInsertionModel])(any[RequestContext]))
        .thenReturn(Future.successful(expectedSaveBookingModelResponse))

      processor.insertMultiBooking(baseRequest).map { result =>
        verify(multiProductRepository).insertMultiBooking(eqTo(baseRequest.saveBookingModel))(any[RequestContext])
        result shouldBe baseRequest.saveBookingModel.copy(
          activityBookingActionState = result.activityBookingActionState,
          cegFastTrackBookingActionState = result.cegFastTrackBookingActionState,
          addOnsBookingActionState = result.addOnsBookingActionState
        )
      }
    }

    "return result from input without any overwrite when MPBE-4506 is B" in {
      val fixture = new MultiProductSaveStageTestFixture {}
      import fixture._
      implicit val measurement    = baseRequest.measurementsContext
      implicit val requestContext = multiPropertyRequest.requestContext

      val itineraryModelResponse = baseRequest.saveBookingModel.itineraryModel.copy(
        itinerary = baseRequest.saveBookingModel.itineraryModel.itinerary.copy(
          itineraryId = itineraryId + 1L
        )
      )
      val expectedSaveBookingModelResponse = baseRequest.saveBookingModel.copy(itineraryModel = itineraryModelResponse)
      when(multiProductRepository.insertMultiBooking(any[MultiProductBookingInsertionModel])(any[RequestContext]))
        .thenReturn(Future.successful(expectedSaveBookingModelResponse))

      processor.insertMultiBooking(baseRequest).map { result =>
        verify(multiProductRepository).insertMultiBooking(eqTo(baseRequest.saveBookingModel))(any[RequestContext])
        result shouldBe baseRequest.saveBookingModel
      }
    }
  }
}
