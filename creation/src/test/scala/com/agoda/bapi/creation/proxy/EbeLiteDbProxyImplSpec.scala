package com.agoda.bapi.creation.proxy

import com.agoda.bapi.common.database.AGDBStub
import com.agoda.bapi.common.db.execution.context.BFDBExecutionContext
import com.agoda.bapi.common.exception.DbException
import com.agoda.bapi.common.handler.{MeasurementsContext, RequestContext}
import com.agoda.bapi.common.message.creation.{CreatedBookingStatus, DuplicateCandidateStatus}
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.db.CountryInfo
import com.agoda.bapi.common.model.product.ProductTypeEnum
import com.agoda.bapi.common.proxy.{DBConnectionGroup, DependencyNames, ResultSetStub}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.WithMetricsCapture
import com.agoda.bapi.creation.model.db._
import com.agoda.bapi.creation.model.PaymentLimitationInfo
import com.agoda.bapi.creation.validation.LockingInsertionResult
import mocks.PropertyBookingDuplicateCheckMock
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers.{eq => argsEq, _}
import org.mockito.Mockito._
import org.scalatest.OptionValues
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import java.sql.{CallableStatement, Connection, ResultSet, Timestamp}
import scala.util.Random

class EbeLiteDbProxyImplSpec extends AsyncWordSpec with Matchers with OptionValues {

  "supporting getEbeLiteBookingObjects query" should {
    val itineraryId = 1L
    val ebeliteBooking1 =
      EbeLiteBookingObject(itineraryId, bookingId = 2, bookingData = "booking1", stateId = Some(3))
    val ebeliteBooking2 =
      EbeLiteBookingObject(itineraryId, bookingId = 4, bookingData = "booking2", stateId = Some(3))
    val expectedData: Array[Map[String, Any]] = Array(
      Map(
        "itinerary_id" -> new java.lang.Long(itineraryId),
        "booking_id"   -> new java.lang.Long(ebeliteBooking1.bookingId),
        "booking_data" -> ebeliteBooking1.bookingData,
        "state_id"     -> new Integer(ebeliteBooking1.stateId.value)
      ),
      Map(
        "itinerary_id" -> new java.lang.Long(itineraryId),
        "booking_id"   -> new java.lang.Long(ebeliteBooking2.bookingId),
        "booking_data" -> ebeliteBooking2.bookingData,
        "state_id"     -> new Integer(ebeliteBooking2.stateId.value)
      )
    )

    "return a sequence of booking objects" in {
      val fixture = new Fixture {
        val queryName        = "bcre_get_ebe_lite_booking_v1"
        val sqlQuery: String = s"EXEC dbo.$queryName @itineraryId = ?"
        val resultSetStub    = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.getEbeLiteBookingObjects(itineraryId).map { results =>
        verifyInteractionsWithUnderlyingDb()
        results should contain theSameElementsAs List(ebeliteBooking2, ebeliteBooking1)
      }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName        = "bcre_get_ebe_lite_booking_v1"
        val sqlQuery: String = s"EXEC dbo.$queryName @itineraryId = ?"
        val resultSetStub    = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.getEbeLiteBookingObjects(itineraryId))
    }
  }

  "supporting getEbeLiteBookingObjectsByBookingId" should {
    val bookingId = 35
    val ebeLiteBookingObject = EbeLiteBookingObject(
      itineraryId = 1,
      bookingId = 35,
      bookingData = "data",
      stateId = None
    )
    val expectedData: Array[Map[String, Any]] = Array(
      Map(
        "itinerary_id" -> new java.lang.Long(ebeLiteBookingObject.itineraryId),
        "booking_id"   -> new Integer(bookingId),
        "booking_data" -> ebeLiteBookingObject.bookingData,
        "state_id"     -> null
      )
    )

    "return booking object if exist" in {
      val fixture = new Fixture {
        val queryName        = "bcre_get_ebe_lite_booking_by_booking_id_v1"
        val sqlQuery: String = s"EXEC dbo.$queryName @bookingId = ?"
        val resultSetStub    = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.getEbeLiteBookingObjectsByBookingId(bookingId).map { results =>
        verifyInteractionsWithUnderlyingDb()
        results should contain theSameElementsAs List(ebeLiteBookingObject)
      }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName        = "bcre_get_ebe_lite_booking_by_booking_id_v1"
        val sqlQuery: String = s"EXEC dbo.$queryName @bookingId = ?"
        val resultSetStub    = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.getEbeLiteBookingObjectsByBookingId(bookingId))
    }
  }

  "getBinRangeData" should {
    val cardBinRangeInfo                      = CardBinRangeInfo(CardClass.Credit)
    val expectedData: Array[Map[String, Any]] = Array(Map("card_class" -> cardBinRangeInfo.cardClass.toString))

    "return card bin range info  if exists" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_binrange_by_creditcard_number_v1"
        val sqlQuery      = s"EXEC dbo.$queryName @cc_number = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.getBinRangeData("mybin").map { results =>
        verifyInteractionsWithUnderlyingDb()
        results shouldBe Some(cardBinRangeInfo)
      }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_binrange_by_creditcard_number_v1"
        val sqlQuery      = s"EXEC dbo.$queryName @cc_number = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.getBinRangeData("mybin"))
    }
  }

  "getBinLength" should {
    val binLength                             = 10
    val expectedData: Array[Map[String, Any]] = Array(Map("max_bin_length" -> binLength))
    "return default when not set in afm" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_binrange_maxbinlength_v1"
        val sqlQuery      = s"EXEC dbo.$queryName"
        val resultSetStub = new ResultSetStub(Array.empty)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.getBinLength().map { results =>
        verifyInteractionsWithUnderlyingDb()
        results shouldBe EbeLiteDbProxy.DefaultBinLen
      }
    }

    "return a length in Int format" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_binrange_maxbinlength_v1"
        val sqlQuery      = s"EXEC dbo.$queryName"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.getBinLength().map { results =>
        verifyInteractionsWithUnderlyingDb()
        results shouldBe binLength
      }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_binrange_maxbinlength_v1"
        val sqlQuery      = s"EXEC dbo.$queryName"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.getBinLength())
    }
  }

  "getCountryInfos" should {
    val thailand = CountryInfo(106, "Thailand", "Thailand", "THA", "TH")
    val taiwan   = CountryInfo(140, "Taiwan", "Taiwan", "TWN", "TW")
    val expectedData: Array[Map[String, Any]] = Array(
      Map(
        "country_id"          -> thailand.countryId,
        "country_name"        -> thailand.countryName,
        "country_nationality" -> thailand.countryNationality,
        "country_iso"         -> thailand.countryISO,
        "country_iso2"        -> thailand.countryISO2
      ),
      Map(
        "country_id"          -> taiwan.countryId,
        "country_name"        -> taiwan.countryName,
        "country_nationality" -> taiwan.countryNationality,
        "country_iso"         -> taiwan.countryISO,
        "country_iso2"        -> taiwan.countryISO2
      )
    )
    "return country info list" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_geo2_country_v1"
        val sqlQuery      = s"EXEC dbo.$queryName"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.getCountryInfos().map { results =>
        verifyInteractionsWithUnderlyingDb()
        results should contain theSameElementsAs List(taiwan, thailand)
      }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_geo2_country_v1"
        val sqlQuery      = s"EXEC dbo.$queryName"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.getCountryInfos())
    }
  }

  "findAllDuplicatedBookingCandidatesBFDB" should {
    val duplicateBookingRecord =
      PropertyDuplicatedBookingCandidate(
        bookingId = 430,
        bookingDate = DateTime.now,
        storeFrontId = 89,
        languageId = 1,
        roomTypeId = 0L,
        workflowPhaseId = None,
        itineraryId = Some(12345L),
        candidateStatus = Some(DuplicateCandidateStatus.Active)
      )
    val expectedData: Array[Map[String, Any]] = Array(
      Map(
        "booking_id"        -> duplicateBookingRecord.bookingId,
        "booking_date"      -> new java.sql.Timestamp(duplicateBookingRecord.bookingDate.getMillis),
        "storefront_id"     -> duplicateBookingRecord.storeFrontId,
        "language_id"       -> duplicateBookingRecord.languageId,
        "workflow_phase_id" -> null,
        "itinerary_id"      -> 12345L,
        "candidate_status"  -> DuplicateCandidateStatus.Active.id
      )
    )

    val dayOffset = 7

    "check duplicate booking from database records properly" in {
      val fixture = new Fixture {
        val queryName = "bcre_check_duplicate_booking_v6"
        val sqlQuery =
          s"""EXEC dbo.$queryName @CheckInDate = ?, @CheckOutDate = ?, @DayOffset = ?, @WhitelabelId = ?,
             | @StorefrontId = ?, @AffiliateTagId = ?, @HashedPii = ?, @IsTagIdCheckEnabled = ?, @PropertyId = ?, @MemberId = ?""".stripMargin
            .replaceAll("\n", "")
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()
      val propertyBookingDuplicationCriteria = PropertyBookingDuplicateCheckMock.propertyBookingDuplicationCriteria
      dbProxy
        .findAllDuplicatedBookingCandidatesBFDB(
          propertyBookingDuplicationCriteria,
          dayOffset,
          Some(true)
        )
        .map { results =>
          verifyInteractionsWithUnderlyingDb()

          val expectedSqlParameterData: Seq[Any] = Seq(
            new Timestamp(propertyBookingDuplicationCriteria.checkInDate.toDateTimeAtStartOfDay.getMillis),
            new Timestamp(propertyBookingDuplicationCriteria.checkOutDate.toDateTimeAtStartOfDay.getMillis),
            dayOffset,
            propertyBookingDuplicationCriteria.whiteLabel.id,
            propertyBookingDuplicationCriteria.storefrontId,
            propertyBookingDuplicationCriteria.affiliateTagId,
            propertyBookingDuplicationCriteria.hashedPII,
            1,
            propertyBookingDuplicationCriteria.propertyId,
            propertyBookingDuplicationCriteria.memberId
          )

          expectedSqlParameterData.zipWithIndex.foreach {
            case (anyEl, idx) =>
              anyEl match {
                case Some(el) => verify(preparedStatementMock).setObject(idx + 1, el)
                case None     => verify(preparedStatementMock).setNull(idx + 1, java.sql.Types.NULL)
                case _        => verify(preparedStatementMock).setObject(idx + 1, anyEl)
              }
          }
          results should contain theSameElementsAs List(duplicateBookingRecord)
        }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName = "bcre_check_duplicate_booking_v6"
        val sqlQuery =
          s"""EXEC dbo.$queryName @CheckInDate = ?, @CheckOutDate = ?, @DayOffset = ?, @WhitelabelId = ?,
             | @StorefrontId = ?, @AffiliateTagId = ?, @HashedPii = ?, @IsTagIdCheckEnabled = ?, @PropertyId = ?, @MemberId = ?""".stripMargin
            .replaceAll("\n", "")
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      val propertyBookingDuplicationCriteria = PropertyBookingDuplicateCheckMock.propertyBookingDuplicationCriteria
      recoverToSucceededIf[DbException](
        dbProxy.findAllDuplicatedBookingCandidatesBFDB(
          propertyBookingDuplicationCriteria,
          dayOffset,
          Some(true)
        )
      )
    }
  }

  "getGuestRestrictedNationality" should {
    val countryId                             = 106
    val countryRecStatus                      = 1
    val expectedData: Array[Map[String, Any]] = Array(Map("rec_status" -> countryRecStatus))

    "return latest encryption key info" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_geo2_country_by_id_v1"
        val sqlQuery      = s"EXEC dbo.$queryName @country_id = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy
        .getGuestRestrictedNationality(countryId)
        .map { results =>
          verifyInteractionsWithUnderlyingDb()
          verify(preparedStatementMock).setObject(1, countryId)
          results shouldBe Some(countryRecStatus)
        }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_geo2_country_by_id_v1"
        val sqlQuery      = s"EXEC dbo.$queryName @country_id = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.getGuestRestrictedNationality(countryId))
    }
  }

  "checkDuplicateRequestAndInsertNewCandidate" should {
    val hashedRequest: String                 = "3b03e354c935dbe5bcb5541538af38b5f8dfd09a454658cb3b07b71c898be076"
    val requestTypeName                       = "CreateItineraryRequest"
    val expiryDatetime                        = DateTime.now().plusSeconds(3)
    val expectedData: Array[Map[String, Any]] = Array.empty // not used for callable statement testing

    "return locking result" in {
      val fixture = new Fixture {
        val queryName     = "bcre_check_duplicate_request_and_insert_new_candidate_v1"
        val sqlQuery      = s"""{ ? = call dbo.$queryName (?,?,?) }"""
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()
      when(preparedStatementMock.getInt(any[Int])).thenReturn(LockingInsertionResult.InsertedLocking)

      dbProxy
        .checkDuplicateRequestAndInsertNewCandidate(hashedRequest, requestTypeName, expiryDatetime)
        .map { results =>
          verifyInteractionsWithUnderlyingDb(asCallableStatement = true)
          verify(preparedStatementMock).registerOutParameter(1, java.sql.Types.INTEGER)
          verify(preparedStatementMock).setString(2, hashedRequest)
          verify(preparedStatementMock).setString(3, requestTypeName)
          verify(preparedStatementMock).setTimestamp(4, new Timestamp(expiryDatetime.getMillis))
          verify(preparedStatementMock).getInt(1)
          results shouldBe LockingInsertionResult.InsertedLocking
        }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName     = "bcre_check_duplicate_request_and_insert_new_candidate_v1"
        val sqlQuery      = s"""{ ? = call dbo.$queryName (?,?,?) }"""
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](
        dbProxy.checkDuplicateRequestAndInsertNewCandidate(hashedRequest, requestTypeName, expiryDatetime)
      )
    }
  }

  "isHotelNationalityRestriction" should {
    val hotelId                               = 4205
    val countryId                             = 106
    val isRestricted                          = true
    val recStatus                             = 1
    val expectedData: Array[Map[String, Any]] = Array.empty // not used for callable statement testing

    "return hotel nationality restriction status" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_hotel_nationality_restriction_v1"
        val sqlQuery      = s"""{ ? = call dbo.$queryName (?,?) }"""
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()
      when(preparedStatementMock.getInt(any[Int])).thenReturn(recStatus)

      dbProxy
        .isHotelNationalityRestriction(hotelId, countryId)
        .map { results =>
          verifyInteractionsWithUnderlyingDb(asCallableStatement = true)
          verify(preparedStatementMock).registerOutParameter(1, java.sql.Types.INTEGER)
          verify(preparedStatementMock).setInt(2, hotelId)
          verify(preparedStatementMock).setInt(3, countryId)
          verify(preparedStatementMock).getInt(1)
          results shouldBe Some(isRestricted)
        }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_hotel_nationality_restriction_v1"
        val sqlQuery      = s"""{ ? = call dbo.$queryName (?,?) }"""
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.isHotelNationalityRestriction(hotelId, countryId))
    }
  }

  "isAirportNationalityRestriction" should {
    val airportCode                           = "BKK"
    val countryId                             = 106
    val isRestricted                          = true
    val recStatus                             = 1
    val expectedData: Array[Map[String, Any]] = Array.empty // not used for callable statement testing

    "return airport nationality restriction status" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_airport_nationality_restriction_v1"
        val sqlQuery      = s"""{ ? = call dbo.$queryName (?,?) }"""
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()
      when(preparedStatementMock.getInt(any[Int])).thenReturn(recStatus)

      dbProxy
        .isAirportNationalityRestriction(airportCode, countryId)
        .map { results =>
          verifyInteractionsWithUnderlyingDb(asCallableStatement = true)
          verify(preparedStatementMock).registerOutParameter(1, java.sql.Types.INTEGER)
          verify(preparedStatementMock).setString(2, airportCode)
          verify(preparedStatementMock).setInt(3, countryId)
          verify(preparedStatementMock).getInt(1)
          results shouldBe Some(isRestricted)
        }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_airport_nationality_restriction_v1"
        val sqlQuery      = s"""{ ? = call dbo.$queryName (?,?) }"""
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.isAirportNationalityRestriction(airportCode, countryId))
    }
  }

  "getHotelCountryIdWithRecStatus" should {
    val hotelId                               = 53342
    val countryId                             = 106
    val expectedData: Array[Map[String, Any]] = Array(Map("country_id" -> countryId))

    "return country id for hotel queried" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_product_hotels_countryid_v2"
        val sqlQuery      = s"EXEC dbo.$queryName @hotel_id = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy
        .getHotelCountryIdWithRecStatus(hotelId)
        .map { results =>
          verifyInteractionsWithUnderlyingDb()
          verify(preparedStatementMock).setObject(1, hotelId)
          results shouldBe Some(countryId)
        }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_product_hotels_countryid_v2"
        val sqlQuery      = s"EXEC dbo.$queryName @hotel_id = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.getHotelCountryIdWithRecStatus(hotelId))
    }
  }

  "getAirportCountryId" should {
    val airportCode = "BKK"
    val countryId   = 106
    val expectedData: Array[Map[String, Any]] =
      Array(Map("country_id" -> countryId)) // not used for callable statement testing

    "return countrycode of queried airport" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_product_airports_countryid_v1"
        val sqlQuery      = s"EXEC dbo.$queryName @airport_code = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy
        .getAirportCountryId(airportCode)
        .map { results =>
          verifyInteractionsWithUnderlyingDb()
          verify(preparedStatementMock).setObject(1, airportCode)
          results shouldBe Some(countryId)
        }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_product_airports_countryid_v1"
        val sqlQuery      = s"EXEC dbo.$queryName @airport_code = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.getAirportCountryId(airportCode))
    }
  }

  "isCurrencyOffered" should {
    val currencyCode                          = "THB"
    val isOffered                             = true
    val expectedData: Array[Map[String, Any]] = Array(Map("1" -> isOffered)) // datatype sql bit; uses column index fml

    "return currency isOffered status" in {
      val fixture = new Fixture {
        val queryName     = "bcre_currency_offer_validation_v1"
        val sqlQuery      = s"EXEC dbo.$queryName @currency_code = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy
        .isCurrencyOffered(currencyCode)
        .map { results =>
          verifyInteractionsWithUnderlyingDb()
          verify(preparedStatementMock).setObject(1, currencyCode)
          results shouldBe isOffered
        }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName     = "bcre_currency_offer_validation_v1"
        val sqlQuery      = s"EXEC dbo.$queryName @currency_code = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.isCurrencyOffered(currencyCode))
    }
  }

  "getConfigurations - afm" should {
    val afmKeyName = "afm_key12"
    val groupId    = 32
    val configStr  = "some-configs"
    val expectedData: Array[Map[String, Any]] =
      Array(Map("1" -> configStr)) // datatype sql bit; uses column index fml

    "return afm configuration string" in {
      val fixture = new Fixture {
        val queryName = "bcre_afm_configuration_select_v1"
        val sqlQuery =
          s"""EXEC dbo.$queryName
             | @configuration_group_id = ?,
             | @configuration_key = ?""".stripMargin.replaceAll("\n", "")
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy
        .getConfigurations(groupId, afmKeyName)
        .map { results =>
          verifyInteractionsWithUnderlyingDb()
          verify(preparedStatementMock).setObject(1, groupId)
          verify(preparedStatementMock).setObject(2, afmKeyName)
          results shouldBe Some(configStr)
        }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName = "bcre_afm_configuration_select_v1"
        val sqlQuery =
          s"""EXEC dbo.$queryName
             | @configuration_group_id = ?,
             | @configuration_key = ?""".stripMargin.replaceAll("\n", "")
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.getConfigurations(groupId, afmKeyName))
    }
  }

  "isRedirectCard" should {
    val paymentMethodId    = 212
    val redirectCardStatus = false
    val expectedData: Array[Map[String, Any]] =
      Array(Map("1" -> redirectCardStatus)) // datatype sql bit; uses column index fml

    "return isRedirect card status" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_payment_methods_isredirect_v1"
        val sqlQuery      = s"EXEC dbo.$queryName @payment_method_id = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy
        .isRedirectCard(paymentMethodId)
        .map { results =>
          verifyInteractionsWithUnderlyingDb()
          verify(preparedStatementMock).setObject(1, paymentMethodId)

          results shouldBe redirectCardStatus
        }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_payment_methods_isredirect_v1"
        val sqlQuery      = s"EXEC dbo.$queryName @payment_method_id = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.isRedirectCard(paymentMethodId))
    }
  }

  "isPaymentTokenEnabled" should {
    val whiteLabelId        = WhiteLabel.Agoda.id
    val productTypeId       = ProductTypeEnum.Property.id
    val paymentMethodId     = 212
    val paymentTokenEnabled = false
    val expectedData: Array[Map[String, Any]] =
      Array(Map("1" -> paymentTokenEnabled)) // datatype sql bit; uses column index fml

    "return payment token enabled status" in {
      val fixture = new Fixture {
        val queryName     = "bcre_get_payment_methods_display_token_enabled_v1"
        val sqlQuery      = s"EXEC dbo.$queryName @payment_method_id = ?, @whitelabel_id = ?, @product_type_id = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy
        .isPaymentTokenEnabled(whiteLabelId, paymentMethodId, productTypeId)
        .map { results =>
          verifyInteractionsWithUnderlyingDb()
          List(paymentMethodId, whiteLabelId, productTypeId).zipWithIndex.foreach {
            case (param, idx) => // order of param used for SP @param = ? substitution
              val oneBasedIdx = idx + 1
              verify(preparedStatementMock).setObject(oneBasedIdx, param)
          }
          results shouldBe paymentTokenEnabled
        }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName     = "bcre_get_payment_methods_display_token_enabled_v1"
        val sqlQuery      = s"EXEC dbo.$queryName @payment_method_id = ?, @whitelabel_id = ?, @product_type_id = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.isPaymentTokenEnabled(whiteLabelId, paymentMethodId, productTypeId))
    }
  }

  "getNextItineraryNumber" should {
    val nextId = Random.nextInt(1000000)
    val expectedData: Array[Map[String, Any]] =
      Array(Map("1" -> nextId)) // datatype sql bit; uses column index fml

    "return next value from db sequence" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_next_itinerary_id_v1"
        val sqlQuery      = s"EXEC dbo.$queryName"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.getNextItineraryNumber
        .map { results =>
          verifyInteractionsWithUnderlyingDb()
          results shouldBe nextId
        }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_next_itinerary_id_v1"
        val sqlQuery      = s"EXEC dbo.$queryName"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.getNextItineraryNumber)
    }
  }

  "getNextBookingId" should {
    val nextId = Random.nextInt(1000000)
    val expectedData: Array[Map[String, Any]] =
      Array(Map("1" -> nextId)) // datatype sql bit; uses column index fml

    "return next value from db sequence" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_next_booking_id_v1"
        val sqlQuery      = s"EXEC dbo.$queryName"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.getNextBookingId
        .map { results =>
          verifyInteractionsWithUnderlyingDb()
          results shouldBe nextId
        }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName     = "bcre_select_next_booking_id_v1"
        val sqlQuery      = s"EXEC dbo.$queryName"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.getNextBookingId)
    }
  }

  "getPaymentLimitation" should {
    val siteId      = 10234
    val currentDate = DateTime.now()
    val paymentLimitationInfo = PaymentLimitationInfo(
      siteId = siteId,
      paymentMethodIds = Some(Seq(2)),
      paymentMethodNames = Some(Seq("Mastercard")),
      bookingStartDate = currentDate.toString,
      bookingEndDate = currentDate.toString,
      errorCMSId = 12322,
      alternatePaymentCMSId = None,
      platformIds = Some(Seq(1, 1007)),
      binList = Some(Seq(378363, 378467))
    )
    val expectedData: Array[Map[String, Any]] = Array(
      Map(
        "site_id"                  -> siteId,
        "payment_method_ids"       -> "2",
        "payment_method_names"     -> "Mastercard",
        "booking_start_date"       -> new java.sql.Timestamp(currentDate.getMillis),
        "booking_end_date"         -> new java.sql.Timestamp(currentDate.getMillis),
        "error_cms_id"             -> 12322,
        "alternate_payment_cms_id" -> null,
        "platform_ids"             -> "1,1007",
        "bin_no"                   -> "378363,378467"
      )
    )

    "return payment limitation data based on site id" in {
      val fixture = new Fixture {
        val queryName     = "bcre_get_payment_limitation_v2"
        val sqlQuery      = s"EXEC dbo.$queryName @site_id = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy
        .getPaymentLimitation(siteId)
        .map { results =>
          verifyInteractionsWithUnderlyingDb()
          verify(preparedStatementMock).setObject(1, siteId)
          results.value shouldBe paymentLimitationInfo
        }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName     = "bcre_get_payment_limitation_v2"
        val sqlQuery      = s"EXEC dbo.$queryName @site_id = ?"
        val resultSetStub = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.getPaymentLimitation(siteId))
    }
  }

  "setPropertyBookingState" should {
    val bookingId = 1L
    val state     = CreatedBookingStatus.BookingProcessing

    val expectedData: Array[Map[String, Any]] = Array(
      Map("1" -> bookingId.toInt)
    )

    "set state_id column in ebe_lite_booking table and return affected row correctly" in {
      val fixture = new Fixture {
        val queryName        = "bcre_update_property_state_v1"
        val sqlQuery: String = s"EXEC dbo.$queryName @bookingId = ?, @stateId = ?"
        val resultSetStub    = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.setPropertyBookingState(bookingId, state).map { results =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, bookingId)
        verify(preparedStatementMock).setObject(2, state.id)
        results shouldBe bookingId
      }
    }

    "return affected row as 0 when there's no result next" in {
      val fixture = new Fixture {
        val queryName        = "bcre_update_property_state_v1"
        val sqlQuery: String = s"EXEC dbo.$queryName @bookingId = ?, @stateId = ?"
        val resultSetStub    = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()
      when(resultSetStub.next()).thenReturn(false)

      dbProxy.setPropertyBookingState(bookingId, state).map { results =>
        verifyInteractionsWithUnderlyingDb()
        results shouldBe 0
      }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName        = "bcre_update_property_state_v1"
        val sqlQuery: String = s"EXEC dbo.$queryName @bookingId = ?, @stateId = ?"
        val resultSetStub    = mock[ResultSet]
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.setPropertyBookingState(bookingId, state))
    }
  }

  "updatePublishStatus" should {
    val itineraryId = 1
    val expectedData: Array[Map[String, Any]] = Array(
      Map("true" -> true)
    )

    "migrated update successful" in {
      val fixture = new Fixture {
        val queryName        = "bcre_creation_reset_itinerary_publish_status_v1"
        val sqlQuery: String = s"EXEC dbo.$queryName @itineraryId = ?"
        val resultSetStub    = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      dbProxy.updatePublishStatus(itineraryId).map { results =>
        verifyInteractionsWithUnderlyingDb()
        verify(preparedStatementMock).setObject(1, itineraryId)
        results shouldBe true
      }
    }
    "throw exception to caller if db is down" in {
      val fixture = new Fixture {
        val queryName        = "bcre_creation_reset_itinerary_publish_status_v1"
        val sqlQuery: String = s"EXEC dbo.$queryName @itineraryId = ?"
        val resultSetStub    = new ResultSetStub(expectedData)
      }
      import fixture._
      initMockedBehaviour()

      doThrow(new RuntimeException("simulated db failure")).when(agDbStub).withConnectionGroup(any())(any())
      recoverToSucceededIf[DbException](dbProxy.updatePublishStatus(itineraryId))
    }
  }

  trait Fixture extends MockitoSugar {
    val connectionMock          = mock[Connection]
    val preparedStatementMock   = mock[CallableStatement]
    val agDbStub                = spy(new AGDBStub(connectionMock))
    implicit val requestContext = mock[RequestContext]
    val mockFeatureAware        = mock[FeatureAware]

    val dbProxy = new EbeLiteDbProxyImpl(
      agDbStub,
      new BFDBExecutionContext(BFDBExecutionContext.global)
    ) with WithMetricsCapture

    def queryName: String

    def sqlQuery: String

    def resultSetStub: ResultSet

    def initMockedBehaviour(): Unit = {
      doNothing().when(preparedStatementMock).setQueryTimeout(anyInt)
      when(connectionMock.prepareStatement(any())).thenReturn(preparedStatementMock)
      when(connectionMock.prepareCall(any())).thenReturn(preparedStatementMock)
      when(preparedStatementMock.executeQuery).thenReturn(resultSetStub)
      when(preparedStatementMock.executeUpdate).thenReturn(0)
      when(preparedStatementMock.execute()).thenReturn(true)
      when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))
    }

    def verifyInteractionsWithUnderlyingDb(asCallableStatement: Boolean = false): Unit = {
      verify(agDbStub).withConnectionGroup(argsEq(DBConnectionGroup.BFDB_BCRE.value))(any())
      if (asCallableStatement)
        verify(connectionMock).prepareCall(sqlQuery)
      else
        verify(connectionMock).prepareStatement(sqlQuery)
      verifyNoMoreInteractions(connectionMock)

      dbProxy.sentMetrics should have size 1
      dbProxy.sentMetrics should contain key s"bcre.db.${DependencyNames.BfdbBcreMetric}"
      dbProxy.sentMetrics.get(s"bcre.db.${DependencyNames.BfdbBcreMetric}") should contain("method" -> queryName)
    }
  }

  trait SaveBookingFixtureTest extends MockitoSugar {
    val connectionMock        = mock[Connection]
    val preparedStatementMock = mock[CallableStatement]
    val agDbStub              = spy(new AGDBStub(connectionMock))

    implicit val measurementContext = mock[MeasurementsContext]
    implicit val requestContext     = mock[RequestContext]
    when(measurementContext.tags).thenReturn(Map.empty[String, String])

    val dbProxy = spy(
      new EbeLiteDbProxyImpl(agDbStub, new BFDBExecutionContext(BFDBExecutionContext.global)) with WithMetricsCapture
    )

    def queryName: String

    def resultSetStub: ResultSet

    def initMockedBehaviour(): Unit = {
      doNothing().when(preparedStatementMock).setQueryTimeout(anyInt)
      when(connectionMock.prepareStatement(any())).thenReturn(preparedStatementMock)
      when(connectionMock.prepareCall(any())).thenReturn(preparedStatementMock)
      when(preparedStatementMock.executeQuery).thenReturn(resultSetStub)
      when(preparedStatementMock.executeUpdate).thenReturn(0)
      when(preparedStatementMock.execute()).thenReturn(true)
    }

    def verifyInteractionsWithUnderlyingDb(isExceptionThrow: Boolean = false): Unit = {
      verify(agDbStub).withConnectionGroup(argsEq(DBConnectionGroup.BFDB_BCRE.value))(any())
      verify(connectionMock).setAutoCommit(false)
      if (!isExceptionThrow) {
        verify(connectionMock).setAutoCommit(true)
        verify(connectionMock).commit()
        dbProxy.sentMetrics should have size 1
        dbProxy.sentMetrics should contain key s"bcre.db.${DependencyNames.BfdbBcreMetric}"
        dbProxy.sentMetrics.get(s"bcre.db.${DependencyNames.BfdbBcreMetric}") should contain("method" -> queryName)
      }
      verify(connectionMock, times(2)).prepareStatement("mockQuery")
      verifyNoMoreInteractions(connectionMock)
    }
  }

}
