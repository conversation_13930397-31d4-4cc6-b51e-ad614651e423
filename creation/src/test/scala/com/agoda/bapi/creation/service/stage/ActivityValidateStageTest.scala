package com.agoda.bapi.creation.service.stage

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation
import com.agoda.bapi.common.message.creation.PaxType.{Adult, Child}
import com.agoda.bapi.common.message.creation.{ActivityBookingAnswer, ActivityLanguageGuideAnswer, ActivityPax, ActivityPaxMetaInfo, BookingServerStatus, BookingStatus, BookingStatusCategory, CreateBookingResponse, DuplicateBooking, PaxType, Products}
import com.agoda.bapi.common.model.activity.ActivityBookingQuestionEnums.{Code, DataType, GroupType}
import com.agoda.bapi.common.model.activity.SummaryElementEnums.DisplayType
import com.agoda.bapi.common.model.activity._
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.token.activity.ActivityDateTimeHelper
import com.agoda.bapi.creation.CreateMultiBookingHelper
import com.agoda.bapi.creation.model.multi.ValidateProductRequest
import com.agoda.bapi.creation.service.stage.DuplicateBookingReporter.DuplicateBookingTimeMetric
import com.agoda.bapi.creation.service.{DuplicateCheckService, HadoopMessagingService}
import com.agoda.commons.logging.metrics.MetricsReporter
import com.softwaremill.quicklens._
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, LocalDate}
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.{any, eq => eqTo}
import org.mockito.Mockito._
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{BeforeAndAfterEach, EitherValues, OptionValues}
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.{ExecutionContext, Future}

class ActivityValidateStageTest
    extends AsyncWordSpec
    with MockitoSugar
    with Matchers
    with BeforeAndAfterEach
    with CreateMultiBookingHelper
    with OptionValues
    with EitherValues {

  val duplicateCheckService   = mock[DuplicateCheckService]
  val duplicateCheckServiceV2 = mock[DuplicateCheckService]
  val featureAware            = mock[FeatureAware]
  val hadoopMessaging         = mock[HadoopMessagingService]
  val metricsReporter         = mock[MetricsReporter]

  var activityValidateStage: ActivityValidateStage = _

  private val baseRequest: ValidateProductRequest[ActivityBookingToken] = ValidateProductRequest(
    request = baseMultiProductReq,
    requestContext = baseRequestContext.copy(featureAware = Some(featureAware)),
    product = baseActivityProduct
  )

  val paxMeta = Seq(ActivityPaxMetaInfo("WEIGHT", "60", "KG"))
  val pax     = baseActivityPax.modify(_.meta).setTo(paxMeta)
  val bookingAnswer = Seq(
    ActivityBookingAnswer("PICKUP_POINT", "LOCATION-A", "LOCATION_REFERENCE")
  )
  val activityItem = baseActivityItem
    .modify(_.pax)
    .setTo(Some(Seq(pax, pax, pax.copy(paxType = Child))))
    .modify(_.bookingAnswers)
    .setTo(Some(bookingAnswer))

  val activityItem2 = baseActivityItem
    .modify(_.productTokenKey)
    .setTo(Some("2"))

  val validRequest =
    baseRequest.modifyAll(_.request.products).setTo(Products(activitiesItems = Some(Seq(activityItem))))
  implicit val measurementContext = validRequest.measurementsContext
  implicit val requestContext     = validRequest.requestContext

  override def beforeEach: Unit = {
    reset(duplicateCheckService, duplicateCheckServiceV2, featureAware, hadoopMessaging, metricsReporter)

    when(hadoopMessaging.sendBapiCreateFactLogMessage(any, any, any)).thenReturn(Future.successful(()))

    when(
      duplicateCheckService.findActivityDuplicates(
        any[Int],
        any[DateTime],
        any[Int],
        any[Option[String]],
        any[Option[String]]
      )(
        any[ExecutionContext]
      )
    ).thenReturn(Future.successful(Nil))

    when(
      duplicateCheckService.findAllPaxMisMatchBookings(
        any[Seq[DuplicateBooking]],
        any[Seq[ActivityPax]]
      )(any[RequestContext])
    ).thenReturn(Future.successful(List.empty))

    when(
      duplicateCheckServiceV2.findActivityDuplicates(
        any[Int],
        any[DateTime],
        any[Int],
        any[Option[String]],
        any[Option[String]]
      )(
        any[ExecutionContext]
      )
    ).thenReturn(Future.successful(Nil))

    when(
      duplicateCheckServiceV2.findAllPaxMisMatchBookings(
        any[Seq[DuplicateBooking]],
        any[Seq[ActivityPax]]
      )(any[RequestContext])
    ).thenReturn(Future.successful(List.empty))

    activityValidateStage = spy[ActivityValidateStage](
      new ActivityValidateStage(duplicateCheckService, duplicateCheckServiceV2, hadoopMessaging) {
        override def reporter: MetricsReporter = metricsReporter
      }
    )
  }

  "process" should {
    "call function correctly" in
      activityValidateStage.process(validRequest).map { result =>
        val tagsCaptor = ArgumentCaptor.forClass(classOf[Map[String, String]])
        verify(activityValidateStage, times(1))
          .checkDuplicateBooking(eqTo(validRequest))(eqTo(measurementContext), eqTo(validRequest.requestContext))
        verify(activityValidateStage)
          .checkBookingQuestions(eqTo(validRequest))(eqTo(measurementContext), eqTo(validRequest.requestContext))
        verify(metricsReporter).report(eqTo(DuplicateBookingTimeMetric), any(), tagsCaptor.capture())

        tagsCaptor.getValue should contain allOf (
          (DuplicateBookingReporter.DuplicateBookingCheckServiceVersionTag, "v1"),
          ("productType", "Activity"),             // based on mocked base request settings
          ("allowDuplicateBooking", true.toString) // based on mocked base request settings
        )
        result shouldBe Right(validRequest)
      }
  }

  "checkBookingDateAndStartDate" should {
    val activityDateTimeHelper = new ActivityDateTimeHelper {}
    val formatter              = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZ")

    "[when exp. AFT-1362 Is ON] fail validation on invalid request if activity start date is before booking date" in {
      when(featureAware.isActivityStartTimeBeforeBookingTimeExperiment).thenReturn(true)
      val invalidRequest = baseRequest.copy(
        requestContext = requestContext.copy(
          requestedDateTime = formatter.parseDateTime("2025-03-13T10:45:41.922+07:00")
        ),
        product = baseRequest.product.copy(
          info = baseRequest.product.info.copy(
            activityInfo = baseRequest.product.info.activityInfo.copy(
              bookingOffer = baseRequest.product.info.activityInfo.bookingOffer.copy(
                startDate = activityDateTimeHelper
                  .getDateTimeFromTravelDate(
                    "2025-03-12"
                  )
                  .get,
                startTime = Some(ActivityTime(0, 0, 0))
              )
            )
          )
        )
      )

      activityValidateStage
        .checkBookingDateTimeAndStartDateTime(invalidRequest)
        .map { result =>
          result.left.value shouldBe creation.CreateBookingResponse(
            success = false,
            errorMessage = Some("ActivityValidateStage: Booking date cannot be after activity start date"),
            errorCode = Some("Processing Error"),
            subErrorCode = Some(12010),
            invalidRequestData = None,
            hotelRooms = None,
            itinerary = None,
            paymentResult = None,
            duplicateBookings = Seq.empty,
            status = Some(BookingServerStatus(BookingStatus.BookingError, BookingStatusCategory.BookingError)),
            supplierResponse = None
          )
        }
    }

    "[when exp. AFT-1362 Is OFF] validation should succeed on invalid request even when activity start date is before booking date" in {
      when(featureAware.isActivityStartTimeBeforeBookingTimeExperiment).thenReturn(false)
      val invalidRequest = baseRequest.copy(
        requestContext = requestContext.copy(
          requestedDateTime = formatter.parseDateTime("2025-03-13T10:45:41.922+07:00")
        ),
        product = baseRequest.product.copy(
          info = baseRequest.product.info.copy(
            activityInfo = baseRequest.product.info.activityInfo.copy(
              bookingOffer = baseRequest.product.info.activityInfo.bookingOffer.copy(
                startDate = activityDateTimeHelper
                  .getDateTimeFromTravelDate(
                    "2025-03-12"
                  )
                  .get,
                startTime = Some(ActivityTime(0, 0, 0))
              )
            )
          )
        )
      )

      activityValidateStage
        .checkBookingDateTimeAndStartDateTime(invalidRequest)
        .map(result => result.isRight shouldBe true)
    }

    "[when exp. AFT-1362 Is OFF] pass validation on valid request if booking start date is after booking date" in {
      when(featureAware.isActivityStartTimeBeforeBookingTimeExperiment).thenReturn(false)

      val request = baseRequest.copy(
        requestContext = requestContext.copy(
          requestedDateTime = formatter.parseDateTime("2025-03-13T10:45:41.922+07:00")
        ),
        product = baseRequest.product.copy(
          info = baseRequest.product.info.copy(
            activityInfo = baseRequest.product.info.activityInfo.copy(
              bookingOffer = baseRequest.product.info.activityInfo.bookingOffer.copy(
                startDate = activityDateTimeHelper
                  .getDateTimeFromTravelDate(
                    "2025-03-14"
                  )
                  .get
              )
            )
          )
        )
      )

      activityValidateStage
        .checkBookingDateTimeAndStartDateTime(request)
        .map(result => result.isRight shouldBe true)
    }

    "[when exp. AFT-1362 Is ON] pass validation on valid request if booking start date is after booking date" in {
      when(featureAware.isActivityStartTimeBeforeBookingTimeExperiment).thenReturn(true)

      val request = baseRequest.copy(
        requestContext = requestContext.copy(
          requestedDateTime = formatter.parseDateTime("2025-03-13T10:45:41.922+07:00")
        ),
        product = baseRequest.product.copy(
          info = baseRequest.product.info.copy(
            activityInfo = baseRequest.product.info.activityInfo.copy(
              bookingOffer = baseRequest.product.info.activityInfo.bookingOffer.copy(
                startDate = activityDateTimeHelper
                  .getDateTimeFromTravelDate(
                    "2025-03-14"
                  )
                  .get
              )
            )
          )
        )
      )

      activityValidateStage
        .checkBookingDateTimeAndStartDateTime(request)
        .map(result => result.isRight shouldBe true)
    }

    "[when exp. AFT-1362 Is OFF] pass validation on valid request if booking start date is equal booking date but activity start time is later" in {
      when(featureAware.isActivityStartTimeBeforeBookingTimeExperiment).thenReturn(false)

      val request = baseRequest.copy(
        requestContext = requestContext.copy(
          requestedDateTime = formatter.parseDateTime("2025-03-13T10:45:41.922+07:00")
        ),
        product = baseRequest.product.copy(
          info = baseRequest.product.info.copy(
            activityInfo = baseRequest.product.info.activityInfo.copy(
              bookingOffer = baseRequest.product.info.activityInfo.bookingOffer.copy(
                startDate = activityDateTimeHelper
                  .getDateTimeFromTravelDate("2025-03-13")
                  .get,
                startTime = Some(ActivityTime(11, 0, 0))
              )
            )
          )
        )
      )
      activityValidateStage
        .checkBookingDateTimeAndStartDateTime(request)
        .map(result => result.isRight shouldBe true)
    }

    "[when exp. AFT-1362 Is ON] pass validation on valid request if booking start date is equal booking date but activity start time is later" in {
      when(featureAware.isActivityStartTimeBeforeBookingTimeExperiment).thenReturn(true)

      val request = baseRequest.copy(
        requestContext = requestContext.copy(
          requestedDateTime = formatter.parseDateTime("2025-03-13T10:45:41.922+07:00")
        ),
        product = baseRequest.product.copy(
          info = baseRequest.product.info.copy(
            activityInfo = baseRequest.product.info.activityInfo.copy(
              bookingOffer = baseRequest.product.info.activityInfo.bookingOffer.copy(
                startDate = activityDateTimeHelper
                  .getDateTimeFromTravelDate("2025-03-13")
                  .get,
                startTime = Some(ActivityTime(11, 0, 0))
              )
            )
          )
        )
      )
      activityValidateStage
        .checkBookingDateTimeAndStartDateTime(request)
        .map(result => result.isRight shouldBe true)
    }

    "[when exp. AFT-1362 Is ON] fail validation on invalid request if booking start date is equal booking date but activity start time is before" in {
      when(featureAware.isActivityStartTimeBeforeBookingTimeExperiment).thenReturn(true)

      val invalidRequest = baseRequest.copy(
        requestContext = requestContext.copy(
          requestedDateTime = formatter.parseDateTime("2025-03-13T23:45:41.922+07:00")
        ),
        product = baseRequest.product.copy(
          info = baseRequest.product.info.copy(
            activityInfo = baseRequest.product.info.activityInfo.copy(
              bookingOffer = baseRequest.product.info.activityInfo.bookingOffer.copy(
                startDate = activityDateTimeHelper
                  .getDateTimeFromTravelDate("2025-03-13")
                  .get,
                startTime = Some(ActivityTime(2, 0, 0))
              )
            )
          )
        )
      )
      activityValidateStage
        .checkBookingDateTimeAndStartDateTime(invalidRequest)
        .map { result =>
          result.left.value shouldBe creation.CreateBookingResponse(
            success = false,
            errorMessage = Some("ActivityValidateStage: Booking date cannot be after activity start date"),
            errorCode = Some("Processing Error"),
            subErrorCode = Some(12010),
            invalidRequestData = None,
            hotelRooms = None,
            itinerary = None,
            paymentResult = None,
            duplicateBookings = Seq.empty,
            status = Some(BookingServerStatus(BookingStatus.BookingError, BookingStatusCategory.BookingError)),
            supplierResponse = None
          )
        }
    }

    "[when exp. AFT-1362 Is OFF] validation should pass even on invalid request - booking start date is equal booking date but activity start time is before" in {
      when(featureAware.isActivityStartTimeBeforeBookingTimeExperiment).thenReturn(false)

      val invalidRequest = baseRequest.copy(
        requestContext = requestContext.copy(
          requestedDateTime = formatter.parseDateTime("2025-03-13T12:45:41.922+07:00")
        ),
        product = baseRequest.product.copy(
          info = baseRequest.product.info.copy(
            activityInfo = baseRequest.product.info.activityInfo.copy(
              bookingOffer = baseRequest.product.info.activityInfo.bookingOffer.copy(
                startDate = activityDateTimeHelper
                  .getDateTimeFromTravelDate("2025-03-13")
                  .get,
                startTime = Some(ActivityTime(10, 0, 0))
              )
            )
          )
        )
      )
      activityValidateStage
        .checkBookingDateTimeAndStartDateTime(invalidRequest)
        .map(result => result.isRight shouldBe true)
    }
  }

  "checkBookingQuestions" should {
    "pass validation on valid request" in
      activityValidateStage.checkBookingQuestions(validRequest).map { result =>
        result.right.value shouldBe Seq.empty
      }

    "fail validation on invalid request if dynamicBookingQuestions is disabled" in {
      val featureAware = mock[FeatureAware]
      when(featureAware.dynamicBookingQuestions).thenReturn(false)
      activityValidateStage
        .checkBookingQuestions(
          baseRequest.copy(requestContext = requestContext.copy(featureAware = Some(featureAware)))
        )
        .map { result =>
          result.left.value shouldBe creation.CreateBookingResponse(
            success = false,
            errorMessage = Some(
              "ActivityValidateStage: pax request doesn't match paxSummary"
            ),
            errorCode = Some("Processing Error"),
            subErrorCode = Some(12006),
            invalidRequestData = None,
            hotelRooms = None,
            itinerary = None,
            paymentResult = None,
            duplicateBookings = Seq.empty,
            status = Some(BookingServerStatus(BookingStatus.BookingError, BookingStatusCategory.BookingError)),
            supplierResponse = None
          )
        }
    }

    "pass validation on invalid request if dynamicBookingQuestions is enabled" in {
      val featureAware = mock[FeatureAware]
      when(featureAware.dynamicBookingQuestions).thenReturn(true)
      activityValidateStage
        .checkBookingQuestions(
          baseRequest.copy(requestContext = requestContext.copy(featureAware = Some(featureAware)))
        )
        .map { result =>
          result.right.value shouldBe Seq.empty
        }
    }

    "failed if booking answer is not complete" in
      activityValidateStage.checkBookingQuestions(baseRequest).map { result =>
        result.left.value shouldBe creation.CreateBookingResponse(
          success = false,
          errorMessage = Some(
            "ActivityValidateStage: pax request doesn't match paxSummary"
          ),
          errorCode = Some("Processing Error"),
          subErrorCode = Some(12006),
          invalidRequestData = None,
          hotelRooms = None,
          itinerary = None,
          paymentResult = None,
          duplicateBookings = Seq.empty,
          status = Some(BookingServerStatus(BookingStatus.BookingError, BookingStatusCategory.BookingError)),
          supplierResponse = None
        )
      }
  }

  "checkPerBookingQuestionAnswer" should {
    val question = ActivityBookingQuestion(
      id = 0,
      code = ActivityBookingQuestionEnums.Code.PICKUP_POINT,
      dataType = DataType.STRING,
      groupType = GroupType.PER_PERSON,
      isMasterQuestion = true,
      isOptional = false,
      isPII = true,
      maxLength = 255,
      prefillAnswers = Seq(
        ActivityPrefillAnswer(id = 1, code = "LOCATION-A", conditionalQuestionIds = Seq.empty),
        ActivityPrefillAnswer(id = 1, code = "LOCATION-B", conditionalQuestionIds = Seq.empty)
      ),
      units = Seq(ActivityBookingQuestionEnums.Unit.LOCATION_REFERENCE),
      answerRanges = Seq.empty
    )

    val answer = ActivityBookingAnswer("PICKUP_POINT", "LOCATION-A", "LOCATION_REFERENCE")

    "skip when question is optional" in {
      val optionalQuestion = question.copy(isOptional = true)
      val result           = activityValidateStage.checkPerBookingQuestionAnswer(Seq(optionalQuestion), Seq(answer))

      result shouldBe empty
    }

    "skip when question is not master question (conditional)" in {
      val conditionalQuestion = question.copy(isMasterQuestion = false)
      val result              = activityValidateStage.checkPerBookingQuestionAnswer(Seq(conditionalQuestion), Seq(answer))

      result shouldBe empty
    }

    "failed if answer cannot be found" in {
      val result = activityValidateStage.checkPerBookingQuestionAnswer(Seq(question), Seq.empty)

      result shouldBe List(
        ActivityBookingQuestionValidationError(
          "validate PER_BOOK question: answer of question PICKUP_POINT is not found in List()"
        )
      )
    }

    "success if validation pass" in {
      val result = activityValidateStage.checkPerBookingQuestionAnswer(Seq(question), Seq(answer))

      result shouldBe empty
    }

    "failed if validation on contact info questions failed" in {
      val questions = Seq(question.copy(code = Code.CONTACT_INFO_CITY), question.copy(code = Code.CONTACT_INFO_ZIPCODE))
      val result    = activityValidateStage.checkPerBookingQuestionAnswer(questions, Seq(answer))
      result shouldBe Seq(
        ActivityBookingQuestionValidationError(
          "validate PER_BOOK question: answer of question CONTACT_INFO_CITY is not found in List(PICKUP_POINT)"
        ),
        ActivityBookingQuestionValidationError(
          "validate PER_BOOK question: answer of question CONTACT_INFO_ZIPCODE is not found in List(PICKUP_POINT)"
        )
      )
    }
  }

  "validatePrefillAnswer" should {
    val question = ActivityBookingQuestion(
      id = 0,
      code = ActivityBookingQuestionEnums.Code.PICKUP_POINT,
      dataType = DataType.STRING,
      groupType = GroupType.PER_PERSON,
      isMasterQuestion = true,
      isOptional = false,
      isPII = true,
      maxLength = 255,
      prefillAnswers = Seq(
        ActivityPrefillAnswer(id = 1, code = "LOCATION-A", conditionalQuestionIds = Seq.empty),
        ActivityPrefillAnswer(id = 1, code = "LOCATION-B", conditionalQuestionIds = Seq.empty)
      ),
      units = Seq(ActivityBookingQuestionEnums.Unit.LOCATION_REFERENCE),
      answerRanges = Seq.empty
    )
    val answer = ActivityBookingAnswer("PICKUP_POINT", "LOCATION-A", "LOCATION_REFERENCE")

    "pass if answer is the one of prefill answers" in {
      val result = activityValidateStage.validatePrefillAnswer(question.prefillAnswers, answer.questionAns)
      result shouldBe empty
    }

    "failed if answer is the not one of prefill answers" in {
      val result =
        activityValidateStage.validatePrefillAnswer(question.prefillAnswers, answer = "LOCATION-ZZZZ")
      result shouldBe Some(
        ActivityBookingQuestionValidationError(
          "validatePrefillAnswer: answer is not one of the prefill answer List(LOCATION-A, LOCATION-B) answer:LOCATION-ZZZZ"
        )
      )
    }

    "failed if answer is the not provided" in {
      val result =
        activityValidateStage.validatePrefillAnswer(question.prefillAnswers, answer = "")
      result shouldBe Some(
        ActivityBookingQuestionValidationError(
          "validatePrefillAnswer: answer is not one of the prefill answer List(LOCATION-A, LOCATION-B) answer:"
        )
      )
    }
  }

  "validateAnswerUnit" should {
    val question = ActivityBookingQuestion(
      id = 0,
      code = ActivityBookingQuestionEnums.Code.HEIGHT,
      dataType = DataType.NUMBER,
      groupType = GroupType.PER_PERSON,
      isMasterQuestion = true,
      isOptional = false,
      isPII = true,
      maxLength = 255,
      prefillAnswers = Seq.empty,
      units = Seq(ActivityBookingQuestionEnums.Unit.CM),
      answerRanges = Seq.empty
    )
    "pass if unit is one of provided units" in {
      val result = activityValidateStage.validateAnswerUnit(question, "CM")
      result shouldBe empty
    }

    "failed if unit cannot be found" in {
      val result = activityValidateStage.validateAnswerUnit(question, "")
      result.value shouldBe ActivityBookingQuestionValidationError(
        s"validateAnswerUnit: unit not found for question HEIGHT units: List(CM) input: UNKNOWN"
      )
    }

    "failed if unit is not valid" in {
      val result = activityValidateStage.validateAnswerUnit(question, "FT")
      result.value shouldBe ActivityBookingQuestionValidationError(
        s"validateAnswerUnit: unit not found for question HEIGHT units: List(CM) input: FT"
      )
    }
  }

  "checkPerPersonQuestionAnswer" should {
    val question = ActivityBookingQuestion(
      id = 0,
      code = ActivityBookingQuestionEnums.Code.FIRSTNAME,
      dataType = DataType.STRING,
      groupType = GroupType.PER_PERSON,
      isMasterQuestion = true,
      isOptional = false,
      isPII = true,
      maxLength = 255,
      prefillAnswers = Seq.empty,
      units = Seq.empty,
      answerRanges = Seq.empty
    )

    val baseActivityPax1 = ActivityPax(
      id = 5,
      title = Some("Mr"),
      firstname = Some("John"),
      lastname = Some("Doe"),
      primary = true,
      middlename = Some("Lennon"),
      paxType = PaxType.Adult,
      birthDate = Some(LocalDate.now().minusYears(20)),
      gender = Some("Male"),
      nationalityId = Some(10),
      passportNumber = Some("AC11111"),
      passportExpires = Some(LocalDate.now().plusYears(5)),
      email = Some("<EMAIL>"),
      phoneNumber = Some("08*********"),
      phoneCountryCode = Some("66")
    )

    val baseActivityPax2 = ActivityPax(
      id = 5,
      title = Some("Mr"),
      firstname = Some("Johnny"),
      lastname = Some("DoeDoe"),
      primary = true,
      middlename = Some("Lennons"),
      paxType = PaxType.Adult,
      birthDate = Some(LocalDate.now().minusYears(20)),
      gender = Some("Male"),
      nationalityId = Some(10),
      passportNumber = Some("AB11111"),
      passportExpires = Some(LocalDate.now().plusYears(5)),
      email = Some("<EMAIL>"),
      phoneNumber = Some("08123456897"),
      phoneCountryCode = Some("66")
    )

    val activityPaxList = Seq(baseActivityPax1, baseActivityPax2)

    "success if meta answer is present" in {
      val result = activityValidateStage.checkPerPersonQuestionAnswer(
        perPersonQuestion = Seq(
          question,
          question.copy(id = 1, code = Code.LASTNAME),
          question.copy(id = 2, code = Code.DATE_OF_BIRTH),
          question.copy(id = 3, code = Code.PASSPORT_NO),
          question.copy(id = 4, code = Code.PASSPORT_NATIONALITY),
          question.copy(id = 5, code = Code.PASSPORT_EXPIRY),
          question.copy(id = 6, code = Code.SHOE_SIZE, prefillAnswers = Seq(ActivityPrefillAnswer(1, "10", Seq.empty)))
        ),
        paxRequest = Seq(
          baseActivityPax1.copy(meta = Seq(ActivityPaxMetaInfo("SHOE_SIZE", "10", "US"))),
          baseActivityPax2.copy(meta = Seq(ActivityPaxMetaInfo("SHOE_SIZE", "10", "US")))
        )
      )(requestContext)
      result shouldBe empty
    }

    "failed if meta answer is not present" in {
      val result =
        activityValidateStage.checkPerPersonQuestionAnswer(
          perPersonQuestion = Seq(
            question,
            question.copy(id = 1, code = Code.LASTNAME),
            question.copy(id = 2, Code.DATE_OF_BIRTH),
            question.copy(id = 3, Code.PASSPORT_NO),
            question.copy(id = 4, Code.PASSPORT_NATIONALITY),
            question.copy(id = 5, Code.PASSPORT_EXPIRY),
            question.copy(id = 6, Code.SHOE_SIZE)
          ),
          paxRequest = activityPaxList
        )(requestContext)

      result shouldBe List(
        ActivityBookingQuestionValidationError("validateMetaAnswer: answer not found List() for question:SHOE_SIZE"),
        ActivityBookingQuestionValidationError("validateMetaAnswer: answer not found List() for question:SHOE_SIZE")
      )
    }
  }

  "validatePaxAnswer" should {
    val baseActivityPax = ActivityPax(
      id = 5,
      title = Some("Mr"),
      firstname = Some("John"),
      lastname = Some("Doe"),
      primary = true,
      middlename = Some("Lennon"),
      paxType = PaxType.Adult,
      birthDate = Some(LocalDate.now()),
      gender = Some("Male"),
      nationalityId = Some(10),
      passportNumber = Some("gubasdljfnsf"),
      passportExpires = Some(LocalDate.now()),
      email = Some("<EMAIL>"),
      phoneNumber = Some("08*********"),
      phoneCountryCode = Some("66")
    )

    val baseQuestion = ActivityBookingQuestion(
      id = 0,
      code = ActivityBookingQuestionEnums.Code.PICKUP_POINT,
      dataType = DataType.STRING,
      groupType = GroupType.PER_PERSON,
      isMasterQuestion = true,
      isOptional = false,
      isPII = true,
      maxLength = 255,
      prefillAnswers = Seq.empty,
      units = Seq.empty,
      answerRanges = Seq.empty
    )

    "skip validation if question is optional" in {
      val question = baseQuestion.copy(isOptional = true)
      val result = activityValidateStage.validatePaxAnswer(
        pax = baseActivityPax,
        question = question
      )(requestContext)

      result shouldBe None
    }

    "failed is firstname is not valid" in {
      val question = baseQuestion.copy(code = Code.FIRSTNAME)
      val pax      = baseActivityPax.copy(firstname = Some(""))
      val result =
        activityValidateStage.validatePaxAnswer(pax, question)(requestContext)

      result shouldBe Some(ActivityBookingQuestionValidationError("validatePaxAnswer: firstname is not valid"))
    }

    "failed is lastname is not valid" in {
      val question = baseQuestion.copy(code = Code.LASTNAME)
      val pax      = baseActivityPax.copy(lastname = Some(""))
      val result =
        activityValidateStage.validatePaxAnswer(pax, question)(requestContext)

      result shouldBe Some(ActivityBookingQuestionValidationError("validatePaxAnswer: lastname is not valid"))
    }

    "failed is birthDate is not valid" in {
      val question = baseQuestion.copy(code = Code.DATE_OF_BIRTH)
      val pax      = baseActivityPax.copy(birthDate = Some(LocalDate.now().plusYears(10)))
      val result =
        activityValidateStage.validatePaxAnswer(pax, question)(requestContext)

      result shouldBe Some(ActivityBookingQuestionValidationError("validatePaxAnswer: birthDate is not valid"))
    }

    "failed is passportExpires is not valid" in {
      val question = baseQuestion.copy(code = Code.PASSPORT_EXPIRY)
      val pax      = baseActivityPax.copy(passportExpires = None)
      val result   = activityValidateStage.validatePaxAnswer(pax, question)(requestContext)

      result shouldBe Some(
        ActivityBookingQuestionValidationError("validatePaxAnswer: passport expiry date is not valid")
      )
    }

    "failed is nationalityId is not valid" in {
      val question = baseQuestion.copy(code = Code.PASSPORT_NATIONALITY)
      val pax      = baseActivityPax.copy(nationalityId = None)
      val result   = activityValidateStage.validatePaxAnswer(pax, question)(requestContext)

      result shouldBe Some(ActivityBookingQuestionValidationError("validatePaxAnswer: nationality is not valid"))
    }

    "failed is passportNumber is not valid" in {
      val question = baseQuestion.copy(code = Code.PASSPORT_NO)
      val pax      = baseActivityPax.copy(passportNumber = None)
      val result   = activityValidateStage.validatePaxAnswer(pax, question)(requestContext)

      result shouldBe Some(ActivityBookingQuestionValidationError("validatePaxAnswer: passport number is not valid"))
    }

    "pass if passport number is 10 digits when SKYC-10597|B" in {
      val question     = baseQuestion.copy(code = Code.PASSPORT_NO)
      val featureAware = mock[FeatureAware]
      when(featureAware.isUpdatePassportNoLengthTo10).thenReturn(true)
      val requestContextWithFeature = requestContext.copy(featureAware = Some(featureAware))
      val pax                       = baseActivityPax.copy(passportNumber = Some("*********0"))
      val result                    = activityValidateStage.validatePaxAnswer(pax, question)(requestContextWithFeature)

      result shouldBe None
    }

    "fail if passport number is 10 digits when feature flag when SKYC-10597|A" in {
      val question     = baseQuestion.copy(code = Code.PASSPORT_NO)
      val featureAware = mock[FeatureAware]
      when(featureAware.isUpdatePassportNoLengthTo10).thenReturn(false)
      val requestContextWithFeature = requestContext.copy(featureAware = Some(featureAware))
      val pax                       = baseActivityPax.copy(passportNumber = Some("*********0"))
      val result                    = activityValidateStage.validatePaxAnswer(pax, question)(requestContextWithFeature)

      result shouldBe Some(ActivityBookingQuestionValidationError("validatePaxAnswer: passport number is not valid"))
    }

    "pass if passport number is 9 digits when SKYC-10597|A" in {
      val question     = baseQuestion.copy(code = Code.PASSPORT_NO)
      val featureAware = mock[FeatureAware]
      when(featureAware.isUpdatePassportNoLengthTo10).thenReturn(false)
      val requestContextWithFeature = requestContext.copy(featureAware = Some(featureAware))
      val pax                       = baseActivityPax.copy(passportNumber = Some("*********"))
      val result                    = activityValidateStage.validatePaxAnswer(pax, question)(requestContextWithFeature)

      result shouldBe None
    }

    "pass if passport number is 9 digits when SKYC-10597|B" in {
      val question     = baseQuestion.copy(code = Code.PASSPORT_NO)
      val featureAware = mock[FeatureAware]
      when(featureAware.isUpdatePassportNoLengthTo10).thenReturn(true)
      val requestContextWithFeature = requestContext.copy(featureAware = Some(featureAware))
      val pax                       = baseActivityPax.copy(passportNumber = Some("*********"))
      val result                    = activityValidateStage.validatePaxAnswer(pax, question)(requestContextWithFeature)

      result shouldBe None
    }

    "fail if passport number is 11 digits when SKYC-10597|B" in {
      val question     = baseQuestion.copy(code = Code.PASSPORT_NO)
      val featureAware = mock[FeatureAware]
      when(featureAware.isUpdatePassportNoLengthTo10).thenReturn(true)
      val requestContextWithFeature = requestContext.copy(featureAware = Some(featureAware))
      val pax                       = baseActivityPax.copy(passportNumber = Some("*********01"))
      val result                    = activityValidateStage.validatePaxAnswer(pax, question)(requestContextWithFeature)

      result shouldBe Some(ActivityBookingQuestionValidationError("validatePaxAnswer: passport number is not valid"))
    }

    "failed if question code is not valid" in {
      val question = baseQuestion.copy(code = Code.CONTACT_INFO_CITY)
      val pax      = baseActivityPax.copy(passportNumber = None)
      val result   = activityValidateStage.validatePaxAnswer(pax, question)(requestContext)

      result shouldBe Some(
        ActivityBookingQuestionValidationError(
          s"validatePaxAnswer: question code is not valid ${Code.CONTACT_INFO_CITY}"
        )
      )
    }
  }

  "validateMetaAnswer" should {
    def question(
        code: ActivityBookingQuestionEnums.Code = Code.HEIGHT,
        prefillAnswers: Seq[ActivityPrefillAnswer] = Seq.empty,
        units: Seq[ActivityBookingQuestionEnums.Unit] = Seq(ActivityBookingQuestionEnums.Unit.CM)
    ) = ActivityBookingQuestion(
      id = 0,
      code = code,
      dataType = DataType.STRING,
      groupType = GroupType.PER_PERSON,
      isMasterQuestion = true,
      isOptional = false,
      isPII = true,
      maxLength = 255,
      prefillAnswers = prefillAnswers,
      units = units,
      answerRanges = Seq.empty
    )
    "pass if answer is found and not empty - WEIGHT and HEIGHT questions" in {
      val meta = Seq(
        ActivityPaxMetaInfo(key = "HEIGHT", value = "160", unit = "CM"),
        ActivityPaxMetaInfo(key = "WEIGHT", value = "60", unit = "KG")
      )
      activityValidateStage.validateMetaAnswer(meta, question()) shouldBe empty
      activityValidateStage.validateMetaAnswer(
        meta,
        question(Code.WEIGHT, units = Seq(ActivityBookingQuestionEnums.Unit.KG))
      ) shouldBe empty
    }

    "pass if answer is found and not empty - ID_NO question" in {
      val meta = Seq(ActivityPaxMetaInfo(key = "ID_NO", value = "1234455", unit = ""))
      activityValidateStage.validateMetaAnswer(meta, question(Code.ID_NO, units = Seq.empty)) shouldBe empty
    }

    "pass if answer is found and not empty - MAINLAND_CHINA_TRAVEL_PERMIT_NO question" in {
      val meta = Seq(
        ActivityPaxMetaInfo(key = "MAINLAND_CHINA_TRAVEL_PERMIT_NO", value = "1234455", unit = "")
      )
      activityValidateStage.validateMetaAnswer(
        meta,
        question(Code.MAINLAND_CHINA_TRAVEL_PERMIT_NO, units = Seq.empty)
      ) shouldBe empty
    }

    "pass if answer is found and not empty - FOOD_ALLERGY question" in {
      val meta = Seq(ActivityPaxMetaInfo(key = "FOOD_ALLERGY", value = "Shrimp", unit = ""))
      activityValidateStage.validateMetaAnswer(meta, question(Code.FOOD_ALLERGY, units = Seq.empty)) shouldBe empty
    }

    "pass if answer is found and not empty - PREFERRED_MEAL question" in {
      val meta = Seq(
        ActivityPaxMetaInfo(key = "PREFERRED_MEAL", value = "VEGETARIAN", unit = "")
      )
      val mealPrefillAnswers =
        Seq(ActivityPrefillAnswer(-1, "VEGETARIAN", Seq.empty), ActivityPrefillAnswer(-1, "MUSLIM_MEAL", Seq.empty))
      activityValidateStage.validateMetaAnswer(
        meta,
        question(Code.PREFERRED_MEAL, prefillAnswers = mealPrefillAnswers, units = Seq.empty)
      ) shouldBe empty
    }

    "pass if answer is found and not empty - EYESIGHT_PRESCRIPTION question" in {
      val meta = Seq(
        ActivityPaxMetaInfo(key = "EYESIGHT_PRESCRIPTION", value = "100", unit = "DIOPTERS")
      )
      activityValidateStage.validateMetaAnswer(
        meta,
        question(Code.EYESIGHT_PRESCRIPTION, units = Seq(ActivityBookingQuestionEnums.Unit.DIOPTERS))
      ) shouldBe empty
    }

    "failed if meta is not found" in {
      val meta = Seq(
        ActivityPaxMetaInfo(key = "WEIGHT", value = "60", unit = "KG")
      )
      val result = activityValidateStage.validateMetaAnswer(meta, question())
      result shouldBe List(
        ActivityBookingQuestionValidationError(
          "validateMetaAnswer: answer not found List(ActivityPaxMetaInfo(WEIGHT,60,KG)) for question:HEIGHT"
        )
      )
    }

    "failed if answer is empty" in {
      val meta = Seq(
        ActivityPaxMetaInfo(key = "HEIGHT", value = "", unit = "CM"),
        ActivityPaxMetaInfo(key = "WEIGHT", value = "60", unit = "KG")
      )
      val result = activityValidateStage.validateMetaAnswer(meta, question())
      result shouldBe List(
        ActivityBookingQuestionValidationError(
          "validateMetaAnswer: answer is empty ActivityPaxMetaInfo(HEIGHT,,CM) HEIGHT"
        )
      )
    }

    "failed if unit is not found" in {
      val meta = Seq(
        ActivityPaxMetaInfo(key = "HEIGHT", value = "5.3", unit = "FT"),
        ActivityPaxMetaInfo(key = "WEIGHT", value = "60", unit = "KG")
      )
      val result = activityValidateStage.validateMetaAnswer(meta, question())
      result shouldBe List(
        ActivityBookingQuestionValidationError(
          "validateAnswerUnit: unit not found for question HEIGHT units: List(CM) input: FT"
        )
      )
    }

    "failed if prefill answer is invalid" in {
      val meta = Seq(ActivityPaxMetaInfo(key = "PREFERRED_MEAL", value = "invalid", unit = ""))
      val mealPrefillAnswers =
        Seq(ActivityPrefillAnswer(-1, "VEGETARIAN", Seq.empty), ActivityPrefillAnswer(-1, "MUSLIM_MEAL", Seq.empty))
      val result = activityValidateStage.validateMetaAnswer(
        meta,
        question(Code.PREFERRED_MEAL, prefillAnswers = mealPrefillAnswers, units = Seq.empty)
      )
      result shouldBe List(
        ActivityBookingQuestionValidationError(
          "validatePrefillAnswer: answer is not one of the prefill answer List(VEGETARIAN, MUSLIM_MEAL) answer:invalid"
        )
      )
    }
  }

  "checkPaxRequest" should {
    val paxSummary = PaxSummary(
      paxInfo = Seq(
        PaxInfo(paxType = DisplayType.ADULT, quantity = 2),
        PaxInfo(paxType = DisplayType.CHILD, quantity = 1)
      ),
      totalPax = 3
    )
    val basePax = baseActivityPax
    "pass if request match paxSummary" in {
      val result =
        activityValidateStage.checkPaxRequest(
          Seq(basePax.copy(paxType = Adult), basePax.copy(paxType = Adult), basePax.copy(paxType = Child)),
          paxSummary
        )
      result shouldBe Seq.empty
    }

    "fail if number of pax doesn't match or type doesn't match" in {
      val result = activityValidateStage.checkPaxRequest(Seq.empty, paxSummary)
      result shouldBe List(
        ActivityBookingQuestionValidationError("ActivityValidateStage: pax request doesn't match paxSummary"),
        ActivityBookingQuestionValidationError(
          "ActivityValidateStage: pax request doesn't match number of paxType : Adult"
        ),
        ActivityBookingQuestionValidationError(
          "ActivityValidateStage: pax request doesn't match number of paxType : Child"
        )
      )
    }
  }

  "checkLanguageGuide" should {
    "pass validation on valid request" in
      activityValidateStage.checkLanguageGuide(validRequest).map { result =>
        result.right.value shouldBe Seq.empty
      }

    "skip language guide validation when AFT-350 is B" in {
      when(featureAware.dynamicBookingQuestions).thenReturn(true)
      activityValidateStage.checkLanguageGuide(validRequest).map { result =>
        result.right.value shouldBe Seq.empty
      }
    }

    "not skip language guide validation when AFT-350 is A" in {
      when(featureAware.dynamicBookingQuestions).thenReturn(false)
      activityValidateStage.checkLanguageGuide(validRequest).map { result =>
        result.right.value shouldBe Seq.empty
      }
    }

    "pass validation on valid request - language guide options not exist" in {
      val request = baseRequest
        .modify(_.product.info.activityInfo.languageGuides)
        .setTo(Seq.empty)

      activityValidateStage.checkLanguageGuide(request).map { result =>
        result.right.value shouldBe Seq.empty
      }
    }

    "fail if language guide answer does not exist in the request" in {
      val invalidActivityItem = activityItem.modify(_.languageGuide).setTo(None)
      val request             = baseRequest.modify(_.request.products.activitiesItems).setTo(Some(Seq(invalidActivityItem)))

      activityValidateStage.checkLanguageGuide(request).map { result =>
        result.left.value shouldBe creation.CreateBookingResponse(
          success = false,
          errorMessage = Some(
            "checkLanguageGuide: no language guide answer provided"
          ),
          errorCode = Some("Processing Error"),
          subErrorCode = Some(12009),
          invalidRequestData = None,
          hotelRooms = None,
          itinerary = None,
          paymentResult = None,
          duplicateBookings = Seq.empty,
          status = Some(BookingServerStatus(BookingStatus.BookingError, BookingStatusCategory.BookingError)),
          supplierResponse = None
        )
      }
    }

    "fail if language guide answer is not valid" in {
      val invalidActivityItem = activityItem
        .modify(_.languageGuide)
        .setTo(
          Some(
            ActivityLanguageGuideAnswer(
              `type` = "WRITTEN",
              language = "en"
            )
          )
        )
      val request = baseRequest.modify(_.request.products.activitiesItems).setTo(Some(Seq(invalidActivityItem)))

      activityValidateStage.checkLanguageGuide(request).map { result =>
        result.left.value shouldBe creation.CreateBookingResponse(
          success = false,
          errorMessage = Some(
            "validateLanguageGuide: either language guide or language code is not valid"
          ),
          errorCode = Some("Processing Error"),
          subErrorCode = Some(12009),
          invalidRequestData = None,
          hotelRooms = None,
          itinerary = None,
          paymentResult = None,
          duplicateBookings = Seq.empty,
          status = Some(BookingServerStatus(BookingStatus.BookingError, BookingStatusCategory.BookingError)),
          supplierResponse = None
        )
      }
    }
  }

  "validateLanguageGuide" should {
    val activityLanguageGuides = Seq(
      ActivityLanguageGuide(ActivityLanguageGuideEnums.Type.WRITTEN, "ja"),
      ActivityLanguageGuide(ActivityLanguageGuideEnums.Type.WRITTEN, "en"),
      ActivityLanguageGuide(ActivityLanguageGuideEnums.Type.AUDIO, "es")
    )

    "pass if language type is valid" in {
      val answer = ActivityLanguageGuideAnswer("WRITTEN", "ja")
      val result = activityValidateStage.validateLanguageGuide(answer, activityLanguageGuides)

      result shouldBe None
    }

    "fail if language type is invalid - invalid type" in {
      val answer = ActivityLanguageGuideAnswer("RANDOM", "ja")
      val result = activityValidateStage.validateLanguageGuide(answer, activityLanguageGuides)

      result shouldBe Some(
        ActivityLanguageGuideValidationError(
          "validateLanguageGuide: language guide type is not valid"
        )
      )
    }

    "fail if languageCode is not one of the activity's language guide option" in {
      val answer = ActivityLanguageGuideAnswer("WRITTEN", "es")
      val result = activityValidateStage.validateLanguageGuide(answer, activityLanguageGuides)

      result shouldBe Some(
        ActivityLanguageGuideValidationError(
          "validateLanguageGuide: either language guide or language code is not valid"
        )
      )
    }
  }

  "MPBE-5895 is variant A - checkDuplicateBooking use bfdb" should {
    when(featureAware.enableDuplicateCheckDataFromBkgDb).thenReturn(false)
    "return Right when no duplicate booking" in {
      val validRequest = baseRequest.modify(_.request.products.activitiesItems).setTo(Some(Seq(activityItem)))

      activityValidateStage.checkDuplicateBooking(validRequest)(measurementContext, requestContext).map { result =>
        verifyNoInteractions(duplicateCheckServiceV2)
        result shouldBe Right(Nil)
      }
    }

    "return Right when has duplicate booking but has pax miss match" in {
      val validRequest     = baseRequest.modify(_.request.products.activitiesItems).setTo(Some(Seq(activityItem)))
      val duplicateBooking = mock[DuplicateBooking]
      when(duplicateBooking.bookingId).thenReturn(1L)
      when(
        duplicateCheckService.findActivityDuplicates(
          any[Int],
          any[DateTime],
          any[Int],
          any[Option[String]],
          any[Option[String]]
        )(
          any[ExecutionContext]
        )
      ).thenReturn(Future.successful(Seq(duplicateBooking)))

      when(
        duplicateCheckService.findAllPaxMisMatchBookings(
          any[Seq[DuplicateBooking]],
          any[Seq[ActivityPax]]
        )(any[RequestContext])
      ).thenReturn(Future.successful(Seq(1L)))

      activityValidateStage.checkDuplicateBooking(validRequest)(measurementContext, requestContext).map { result =>
        verifyNoInteractions(duplicateCheckServiceV2)
        result shouldBe Right(Nil)
      }
    }

    "return Left when has duplicate booking" in {
      val validRequest     = baseRequest.modify(_.request.products.activitiesItems).setTo(Some(Seq(activityItem)))
      val duplicateBooking = mock[DuplicateBooking]
      when(
        duplicateCheckService.findActivityDuplicates(
          any[Int],
          any[DateTime],
          any[Int],
          any[Option[String]],
          any[Option[String]]
        )(
          any[ExecutionContext]
        )
      ).thenReturn(Future.successful(Seq(duplicateBooking)))

      when(
        duplicateCheckService.findAllPaxMisMatchBookings(
          any[Seq[DuplicateBooking]],
          any[Seq[ActivityPax]]
        )(any[RequestContext])
      ).thenReturn(Future.successful(List.empty))

      activityValidateStage.checkDuplicateBooking(validRequest)(measurementContext, requestContext).map { result =>
        verifyNoInteractions(duplicateCheckServiceV2)
        result shouldBe Left(CreateBookingResponse.duplicate(Seq(duplicateBooking)))
      }
    }
  }

  "MPBE-5895 is variant B - checkDuplicateBooking use bkgdb" should {
    val expectedAdditionalTags = List(
      (DuplicateBookingReporter.DuplicateBookingCheckServiceVersionTag, "v2")
    )

    "return Right when no duplicate booking" in {
      val validRequest = baseRequest.modify(_.request.products.activitiesItems).setTo(Some(Seq(activityItem)))
      val tagsCaptor   = ArgumentCaptor.forClass(classOf[Map[String, String]])

      when(featureAware.enableDuplicateCheckDataFromBkgDb).thenReturn(true)

      activityValidateStage.checkDuplicateBooking(validRequest)(measurementContext, requestContext).map { result =>
        verify(duplicateCheckServiceV2).findActivityDuplicates(any(), any(), any(), any(), any())(any())
        verifyNoInteractions(duplicateCheckService)
        verify(activityValidateStage).withMeasureAndLogWithContext(eqTo(measurementContext))(
          eqTo(DuplicateBookingTimeMetric),
          tagsCaptor.capture()
        )(any())

        tagsCaptor.getValue should contain allElementsOf expectedAdditionalTags
        result shouldBe Right(Nil)
      }
    }

    "return Right when has duplicate booking but has pax miss match" in {
      val validRequest     = baseRequest.modify(_.request.products.activitiesItems).setTo(Some(Seq(activityItem)))
      val duplicateBooking = mock[DuplicateBooking]
      val tagsCaptor       = ArgumentCaptor.forClass(classOf[Map[String, String]])

      when(featureAware.enableDuplicateCheckDataFromBkgDb).thenReturn(true)
      when(duplicateBooking.bookingId).thenReturn(1L)

      when(
        duplicateCheckServiceV2.findActivityDuplicates(
          any[Int],
          any[DateTime],
          any[Int],
          any[Option[String]],
          any[Option[String]]
        )(
          any[ExecutionContext]
        )
      ).thenReturn(Future.successful(Seq(duplicateBooking)))

      when(
        duplicateCheckServiceV2.findAllPaxMisMatchBookings(
          any[Seq[DuplicateBooking]],
          any[Seq[ActivityPax]]
        )(any[RequestContext])
      ).thenReturn(Future.successful(Seq(1L)))

      activityValidateStage.checkDuplicateBooking(validRequest)(measurementContext, requestContext).map { result =>
        verify(duplicateCheckServiceV2).findActivityDuplicates(any(), any(), any(), any(), any())(any())
        verifyNoInteractions(duplicateCheckService)
        verify(activityValidateStage).withMeasureAndLogWithContext(eqTo(measurementContext))(
          eqTo(DuplicateBookingTimeMetric),
          tagsCaptor.capture()
        )(any())

        tagsCaptor.getValue should contain allElementsOf expectedAdditionalTags
        result shouldBe Right(Nil)
      }
    }

    "return Left when has duplicate booking" in {
      val validRequest     = baseRequest.modify(_.request.products.activitiesItems).setTo(Some(Seq(activityItem)))
      val duplicateBooking = mock[DuplicateBooking]
      val tagsCaptor       = ArgumentCaptor.forClass(classOf[Map[String, String]])

      when(featureAware.enableDuplicateCheckDataFromBkgDb).thenReturn(true)

      when(
        duplicateCheckServiceV2.findActivityDuplicates(
          any[Int],
          any[DateTime],
          any[Int],
          any[Option[String]],
          any[Option[String]]
        )(
          any[ExecutionContext]
        )
      ).thenReturn(Future.successful(Seq(duplicateBooking)))

      when(
        duplicateCheckServiceV2.findAllPaxMisMatchBookings(
          any[Seq[DuplicateBooking]],
          any[Seq[ActivityPax]]
        )(any[RequestContext])
      ).thenReturn(Future.successful(List.empty))

      activityValidateStage.checkDuplicateBooking(validRequest)(measurementContext, requestContext).map { result =>
        verify(duplicateCheckServiceV2).findActivityDuplicates(any(), any(), any(), any(), any())(any())
        verifyNoInteractions(duplicateCheckService)
        verify(activityValidateStage).withMeasureAndLogWithContext(eqTo(measurementContext))(
          eqTo(DuplicateBookingTimeMetric),
          tagsCaptor.capture()
        )(any())

        tagsCaptor.getValue should contain allElementsOf expectedAdditionalTags
        result shouldBe Left(CreateBookingResponse.duplicate(Seq(duplicateBooking)))
      }
    }
  }

  "getCurrentActivityItem" should {
    "return current activity item from request" in {
      val validRequest =
        baseRequest.modify(_.request.products.activitiesItems).setTo(Some(Seq(activityItem, activityItem2)))

      val result = activityValidateStage.getCurrentActivityItem(validRequest)
      result shouldBe Some(activityItem)
    }
  }

}
