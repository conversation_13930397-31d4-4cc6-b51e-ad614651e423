package com.agoda.bapi.creation.model.multi

import com.agoda.bapi.common.model.activity.ActivityBookingToken
import com.agoda.bapi.common.model.addOn.cegFastTrack.AddOnBookingToken
import com.agoda.bapi.common.model.car.CarBookingToken
import com.agoda.bapi.common.model.flight.FlightBookingToken
import com.agoda.bapi.common.model.addOn
import com.agoda.bapi.common.MockRequestContext
import com.agoda.bapi.common.model.payment.PaymentModel
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.bapi.creation.service.stage.presave.ProtectionAncillaryModel
import com.agoda.bapi.creation.{CreateBookingHelper, CreateFlightBookingHelper}
import com.agoda.mpb.common.{BookingType, MultiProductType}
import org.mockito.Mockito.when
import org.scalatestplus.mockito.MockitoSugar
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.prop.TableDrivenPropertyChecks

class MultiSpec
    extends AnyWordSpec
    with Matchers
    with MockitoSugar
    with CreateFlightBookingHelper
    with CreateBookingHelper
    with TableDrivenPropertyChecks {

  trait Fixture {
    val requestContext = MockRequestContext.create()

    val mockRoom                   = mock[RoomInfo]
    val mockAgencyRoom             = mock[RoomInfo]
    val mockMerchantRoom           = mock[RoomInfo]
    val mockMerchantCommissionRoom = mock[RoomInfo]

    val mockFlightBookingTokenMerchant      = mock[FlightBookingToken]
    val mockVehiclesBookingTokenMerchant    = mock[CarBookingToken]
    val mockProtectionsBookingTokenMerchant = mock[ProtectionAncillaryModel]
    val mockActivityBookingTokenMerchant    = mock[ActivityBookingToken]

    val mockCegFastTracksBookingToken            = mock[AddOnBookingToken]
    val mockProtectionsAddOnMerchantBookingToken = mock[addOn.AddOnBookingToken]

    // Properties
    when(mockRoom.productPaymentModel).thenReturn(PaymentModel.Unknown)
    when(mockAgencyRoom.productPaymentModel).thenReturn(PaymentModel.Agency)
    when(mockMerchantRoom.productPaymentModel).thenReturn(PaymentModel.Merchant)
    when(mockMerchantCommissionRoom.productPaymentModel).thenReturn(PaymentModel.MerchantCommission)

    // Non-Properties - Merchant
    when(mockFlightBookingTokenMerchant.productPaymentModel).thenReturn(PaymentModel.Merchant)
    when(mockVehiclesBookingTokenMerchant.productPaymentModel).thenReturn(PaymentModel.Merchant)
    when(mockProtectionsBookingTokenMerchant.productPaymentModel).thenReturn(PaymentModel.Merchant)
    when(mockActivityBookingTokenMerchant.productPaymentModel).thenReturn(PaymentModel.Merchant)
    when(mockProtectionsAddOnMerchantBookingToken.productPaymentModel).thenReturn(PaymentModel.Merchant)

    // Non-Properties - Unknown
    when(mockCegFastTracksBookingToken.productPaymentModel).thenReturn(PaymentModel.Unknown)

    val productRoomInfoUnknown = Product[RoomInfo](
      bookingType = BookingType.CreditCard,
      info = mockRoom,
      multiProductType = Some(MultiProductType.SingleProperty)
    )
    val productRoomInfoAgency = Product[RoomInfo](
      bookingType = BookingType.CreditCard,
      info = mockAgencyRoom,
      multiProductType = Some(MultiProductType.SingleProperty)
    )
    val productRoomInfoMerchant = Product[RoomInfo](
      bookingType = BookingType.CreditCard,
      info = mockMerchantRoom,
      multiProductType = Some(MultiProductType.SingleProperty)
    )
    val productRoomInfoMerchantCommission = Product[RoomInfo](
      bookingType = BookingType.CreditCard,
      info = mockMerchantCommissionRoom,
      multiProductType = Some(MultiProductType.SingleProperty)
    )

    val flightMerchant = Product[FlightBookingToken](
      bookingType = BookingType.Flight,
      info = mockFlightBookingTokenMerchant,
      multiProductType = Some(MultiProductType.SingleFlight)
    )
    val vehicleMerchant = Product[CarBookingToken](
      bookingType = BookingType.Vehicle,
      info = mockVehiclesBookingTokenMerchant,
      multiProductType = Some(MultiProductType.SingleVehicle)
    )
    val protectionMerchant = Product[ProtectionAncillaryModel](
      bookingType = BookingType.Protection,
      info = mockProtectionsBookingTokenMerchant,
      multiProductType = Some(MultiProductType.SingleProtection)
    )
    val activityMerchant = Product[ActivityBookingToken](
      bookingType = BookingType.Activity,
      info = mockActivityBookingTokenMerchant,
      multiProductType = Some(MultiProductType.SingleActivity)
    )
    val cegFastTrack = Product[AddOnBookingToken](
      bookingType = BookingType.CEGFastTrack,
      info = mockCegFastTracksBookingToken,
      multiProductType = Some(MultiProductType.CEGFastTrack)
    )
    val addOns = Product[addOn.AddOnBookingToken](
      bookingType = BookingType.Protection,
      info = mockProtectionsAddOnMerchantBookingToken,
      multiProductType = Some(MultiProductType.SingleProtection)
    )
  }

  "multiProductRequest" should {
    val fixture = new Fixture {}
    import fixture._

    "correctly resolve isMixedPaymentModel" in {

      val table = Table(
        (
          "expectedValue",
          "properties",
          "flights",
          "vehicles",
          "protections",
          "activities",
          "cegFastTracks",
          "addOns"
        ),
        (
          true,
          Seq(productRoomInfoAgency, productRoomInfoMerchant),
          Seq.empty[Product[FlightBookingToken]],
          Seq.empty[Product[CarBookingToken]],
          Seq.empty[Product[ProtectionAncillaryModel]],
          Seq.empty[Product[ActivityBookingToken]],
          Seq.empty[Product[AddOnBookingToken]],
          Seq.empty[Product[addOn.AddOnBookingToken]]
        ),
        (
          true,
          Seq(productRoomInfoAgency, productRoomInfoMerchant, productRoomInfoUnknown),
          Seq.empty[Product[FlightBookingToken]],
          Seq.empty[Product[CarBookingToken]],
          Seq.empty[Product[ProtectionAncillaryModel]],
          Seq.empty[Product[ActivityBookingToken]],
          Seq.empty[Product[AddOnBookingToken]],
          Seq.empty[Product[addOn.AddOnBookingToken]]
        ),
        (
          true,
          Seq(productRoomInfoAgency, productRoomInfoMerchantCommission),
          Seq.empty[Product[FlightBookingToken]],
          Seq.empty[Product[CarBookingToken]],
          Seq.empty[Product[ProtectionAncillaryModel]],
          Seq.empty[Product[ActivityBookingToken]],
          Seq.empty[Product[AddOnBookingToken]],
          Seq.empty[Product[addOn.AddOnBookingToken]]
        ),
        (
          true,
          Seq(productRoomInfoAgency, productRoomInfoMerchantCommission, productRoomInfoUnknown),
          Seq.empty[Product[FlightBookingToken]],
          Seq.empty[Product[CarBookingToken]],
          Seq.empty[Product[ProtectionAncillaryModel]],
          Seq.empty[Product[ActivityBookingToken]],
          Seq.empty[Product[AddOnBookingToken]],
          Seq.empty[Product[addOn.AddOnBookingToken]]
        ),
        (
          false,
          Seq(productRoomInfoAgency, productRoomInfoAgency),
          Seq.empty[Product[FlightBookingToken]],
          Seq.empty[Product[CarBookingToken]],
          Seq.empty[Product[ProtectionAncillaryModel]],
          Seq.empty[Product[ActivityBookingToken]],
          Seq.empty[Product[AddOnBookingToken]],
          Seq.empty[Product[addOn.AddOnBookingToken]]
        ),
        (
          false,
          Seq(productRoomInfoAgency, productRoomInfoAgency, productRoomInfoUnknown),
          Seq.empty[Product[FlightBookingToken]],
          Seq.empty[Product[CarBookingToken]],
          Seq.empty[Product[ProtectionAncillaryModel]],
          Seq.empty[Product[ActivityBookingToken]],
          Seq.empty[Product[AddOnBookingToken]],
          Seq.empty[Product[addOn.AddOnBookingToken]]
        ),
        (
          false,
          Seq(productRoomInfoMerchant, productRoomInfoMerchant),
          Seq.empty[Product[FlightBookingToken]],
          Seq.empty[Product[CarBookingToken]],
          Seq.empty[Product[ProtectionAncillaryModel]],
          Seq.empty[Product[ActivityBookingToken]],
          Seq.empty[Product[AddOnBookingToken]],
          Seq.empty[Product[addOn.AddOnBookingToken]]
        ),
        (
          false,
          Seq(productRoomInfoMerchant, productRoomInfoMerchant, productRoomInfoUnknown),
          Seq.empty[Product[FlightBookingToken]],
          Seq.empty[Product[CarBookingToken]],
          Seq.empty[Product[ProtectionAncillaryModel]],
          Seq.empty[Product[ActivityBookingToken]],
          Seq.empty[Product[AddOnBookingToken]],
          Seq.empty[Product[addOn.AddOnBookingToken]]
        ),
        (
          false,
          Seq(productRoomInfoMerchantCommission, productRoomInfoMerchantCommission),
          Seq.empty[Product[FlightBookingToken]],
          Seq.empty[Product[CarBookingToken]],
          Seq.empty[Product[ProtectionAncillaryModel]],
          Seq.empty[Product[ActivityBookingToken]],
          Seq.empty[Product[AddOnBookingToken]],
          Seq.empty[Product[addOn.AddOnBookingToken]]
        ),
        (
          false,
          Seq(productRoomInfoMerchantCommission, productRoomInfoMerchantCommission, productRoomInfoUnknown),
          Seq.empty[Product[FlightBookingToken]],
          Seq.empty[Product[CarBookingToken]],
          Seq.empty[Product[ProtectionAncillaryModel]],
          Seq.empty[Product[ActivityBookingToken]],
          Seq.empty[Product[AddOnBookingToken]],
          Seq.empty[Product[addOn.AddOnBookingToken]]
        ),
        (
          true,
          Seq(productRoomInfoAgency),
          Seq(flightMerchant),
          Seq.empty[Product[CarBookingToken]],
          Seq.empty[Product[ProtectionAncillaryModel]],
          Seq.empty[Product[ActivityBookingToken]],
          Seq.empty[Product[AddOnBookingToken]],
          Seq.empty[Product[addOn.AddOnBookingToken]]
        ),
        (
          true,
          Seq(productRoomInfoAgency),
          Seq.empty[Product[FlightBookingToken]],
          Seq(vehicleMerchant),
          Seq.empty[Product[ProtectionAncillaryModel]],
          Seq.empty[Product[ActivityBookingToken]],
          Seq.empty[Product[AddOnBookingToken]],
          Seq.empty[Product[addOn.AddOnBookingToken]]
        ),
        (
          true,
          Seq(productRoomInfoAgency),
          Seq.empty[Product[FlightBookingToken]],
          Seq.empty[Product[CarBookingToken]],
          Seq(protectionMerchant),
          Seq.empty[Product[ActivityBookingToken]],
          Seq.empty[Product[AddOnBookingToken]],
          Seq.empty[Product[addOn.AddOnBookingToken]]
        ),
        (
          true,
          Seq(productRoomInfoAgency),
          Seq.empty[Product[FlightBookingToken]],
          Seq.empty[Product[CarBookingToken]],
          Seq.empty[Product[ProtectionAncillaryModel]],
          Seq(activityMerchant),
          Seq.empty[Product[AddOnBookingToken]],
          Seq.empty[Product[addOn.AddOnBookingToken]]
        ),
        (
          false,
          Seq(productRoomInfoAgency),
          Seq.empty[Product[FlightBookingToken]],
          Seq.empty[Product[CarBookingToken]],
          Seq.empty[Product[ProtectionAncillaryModel]],
          Seq.empty[Product[ActivityBookingToken]],
          Seq(cegFastTrack),
          Seq.empty[Product[addOn.AddOnBookingToken]]
        ),
        (
          true,
          Seq(productRoomInfoAgency),
          Seq.empty[Product[FlightBookingToken]],
          Seq.empty[Product[CarBookingToken]],
          Seq.empty[Product[ProtectionAncillaryModel]],
          Seq.empty[Product[ActivityBookingToken]],
          Seq.empty[Product[AddOnBookingToken]],
          Seq(addOns)
        )
      )
      forAll(table) { (expectedValue, properties, flights, vehicles, protections, activities, cegFastTracks, addOns) =>
        {
          val multiProductRequest = MultiProductsRequest(
            request = baseReq,
            requestContext = requestContext,
            properties = properties,
            flights = flights,
            vehicles = vehicles,
            protections = protections,
            activities = activities,
            cegFastTracks = cegFastTracks,
            addOns = addOns,
            bookingFlow = BookingFlow.Cart,
            commonPayment = None,
            isBookingFromCart = None,
            partnerExternalInfo = None,
            isPartialSuccessAllowed = None,
            cartItineraryInfo = None
          )
          val result = multiProductRequest.isMixedPaymentModel
          result shouldBe expectedValue
        }
      }
    }
  }

  "multiProductRequest.hasFlightsMainProductOnly" should {
    val fixture = new Fixture {}
    import fixture._
    val table = Table(
      (
        "description",
        "expectedValue",
        "properties",
        "flights",
        "vehicles",
        "protections",
        "activities",
        "cegFastTracks",
        "addOns"
      ),
      (
        "Only Flight",
        true,
        Seq.empty[Product[RoomInfo]],
        Seq(flightMerchant),
        Seq.empty[Product[CarBookingToken]],
        Seq.empty[Product[ProtectionAncillaryModel]],
        Seq.empty[Product[ActivityBookingToken]],
        Seq.empty[Product[AddOnBookingToken]],
        Seq.empty[Product[addOn.AddOnBookingToken]]
      ),
      (
        "Only Property",
        false,
        Seq(productRoomInfoUnknown),
        Seq.empty[Product[FlightBookingToken]],
        Seq.empty[Product[CarBookingToken]],
        Seq.empty[Product[ProtectionAncillaryModel]],
        Seq.empty[Product[ActivityBookingToken]],
        Seq.empty[Product[AddOnBookingToken]],
        Seq.empty[Product[addOn.AddOnBookingToken]]
      ),
      (
        "Flight with Property",
        false,
        Seq(productRoomInfoUnknown),
        Seq(flightMerchant),
        Seq.empty[Product[CarBookingToken]],
        Seq.empty[Product[ProtectionAncillaryModel]],
        Seq.empty[Product[ActivityBookingToken]],
        Seq.empty[Product[AddOnBookingToken]],
        Seq.empty[Product[addOn.AddOnBookingToken]]
      ),
      (
        "Only Vehicle",
        false,
        Seq.empty[Product[RoomInfo]],
        Seq.empty[Product[FlightBookingToken]],
        Seq(vehicleMerchant),
        Seq.empty[Product[ProtectionAncillaryModel]],
        Seq.empty[Product[ActivityBookingToken]],
        Seq.empty[Product[AddOnBookingToken]],
        Seq.empty[Product[addOn.AddOnBookingToken]]
      ),
      (
        "Flight with Protection",
        true,
        Seq.empty[Product[RoomInfo]],
        Seq(flightMerchant),
        Seq.empty[Product[CarBookingToken]],
        Seq(protectionMerchant),
        Seq.empty[Product[ActivityBookingToken]],
        Seq.empty[Product[AddOnBookingToken]],
        Seq.empty[Product[addOn.AddOnBookingToken]]
      ),
      (
        "Flight with Activity",
        false,
        Seq.empty[Product[RoomInfo]],
        Seq(flightMerchant),
        Seq.empty[Product[CarBookingToken]],
        Seq.empty[Product[ProtectionAncillaryModel]],
        Seq(activityMerchant),
        Seq.empty[Product[AddOnBookingToken]],
        Seq.empty[Product[addOn.AddOnBookingToken]]
      ),
      (
        "Flight with PriceFreeze", // PFZ is deprecated but priceFreeze always has an associated main product
        true,
        Seq.empty[Product[RoomInfo]],
        Seq(flightMerchant),
        Seq.empty[Product[CarBookingToken]],
        Seq.empty[Product[ProtectionAncillaryModel]],
        Seq.empty[Product[ActivityBookingToken]],
        Seq.empty[Product[AddOnBookingToken]],
        Seq.empty[Product[addOn.AddOnBookingToken]]
      ),
      (
        "Flight with CEGFastTrack",
        true,
        Seq.empty[Product[RoomInfo]],
        Seq(flightMerchant),
        Seq.empty[Product[CarBookingToken]],
        Seq.empty[Product[ProtectionAncillaryModel]],
        Seq.empty[Product[ActivityBookingToken]],
        Seq(cegFastTrack),
        Seq.empty[Product[addOn.AddOnBookingToken]]
      ),
      (
        "Vehicle with Addon Protection",
        false,
        Seq.empty[Product[RoomInfo]],
        Seq.empty[Product[FlightBookingToken]],
        Seq(vehicleMerchant),
        Seq.empty[Product[ProtectionAncillaryModel]],
        Seq.empty[Product[ActivityBookingToken]],
        Seq.empty[Product[AddOnBookingToken]],
        Seq(addOns)
      ),
      (
        "Flight with Addon Protection",
        true,
        Seq.empty[Product[RoomInfo]],
        Seq(flightMerchant),
        Seq.empty[Product[CarBookingToken]],
        Seq.empty[Product[ProtectionAncillaryModel]],
        Seq.empty[Product[ActivityBookingToken]],
        Seq.empty[Product[AddOnBookingToken]],
        Seq(addOns)
      )
    )
    forAll(table) {
      (
          description,
          expectedValue,
          properties,
          flights,
          vehicles,
          protections,
          activities,
          cegFastTracks,
          addOns
      ) =>
        {
          s"$description" in {
            val multiProductRequest = MultiProductsRequest(
              request = baseReq,
              requestContext = requestContext,
              properties = properties,
              flights = flights,
              vehicles = vehicles,
              protections = protections,
              activities = activities,
              cegFastTracks = cegFastTracks,
              addOns = addOns,
              bookingFlow = BookingFlow.Cart,
              commonPayment = None,
              isBookingFromCart = None,
              partnerExternalInfo = None,
              isPartialSuccessAllowed = None,
              cartItineraryInfo = None
            )
            val result = multiProductRequest.hasFlightsMainProductOnly
            result shouldBe expectedValue
          }
        }
    }
  }
}
