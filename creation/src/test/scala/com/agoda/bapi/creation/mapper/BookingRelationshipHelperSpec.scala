package com.agoda.bapi.creation.mapper

import com.agoda.bapi.common.config.AgodaConfig
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.common.model.product.{BookingFlow, ProductTypeEnum}
import com.agoda.bapi.common.model.rebookAndCancel.RebookAndCancelData
import com.agoda.bapi.common.model.relationship.{RelationshipStatuses, RelationshipTypes}
import com.agoda.bapi.creation.model.db._
import com.agoda.bapi.creation.model.multi.{MultiProductsRequest, NonPropertySaveStageResponse, PropertySaveStageResponse}
import com.agoda.bapi.creation.{CreateBooking<PERSON>elper, CreateFlightBookingHelper, CreateMultiBookingHelper}
import com.agoda.mpbe.state.booking.BaseBookingRelationship
import com.agoda.mpbe.state.common.enums.Gateway.Gateway
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.softwaremill.diffx.Diff
import com.softwaremill.diffx.scalatest.DiffShouldMatcher._
import org.mockito.Mockito.when
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{AppendedClues, BeforeAndAfter, OptionValues}
import org.scalatestplus.mockito.MockitoSugar

class BookingRelationshipHelperSpec
    extends AsyncWordSpec
    with Matchers
    with AppendedClues
    with BeforeAndAfter
    with CreateFlightBookingHelper
    with CreateBookingHelper
    with OptionValues {

  implicit val baseBookingRelationshipDiff: Diff[BaseBookingRelationship] = Diff
    .derived[BaseBookingRelationship]
    .ignore(_.recCreatedWhen)

  "map" should {
    "return a relation correctly if there's relation between productTokenKey and refProductTokenKey matched" in {
      val scope = new RelationshipMapperSpecScope {}
      import scope._
      val res = mapper.constructAddOnRelationships(
        Seq(
          propertyProductThatCoveredByCegUpsellSaveStageResponse,
          cegUpsellProductSaveStageResponse.copy(refProductTokenKey = Seq("Property_1")),
          otherProductSaveStageResponseThatIsNotRelated
        )
      )
      res shouldMatchTo Seq(
        BaseBookingRelationship(
          sourceBookingId = cegUpsellBookingId,
          targetBookingId = propertyThatCoveredByCegUpsellBookingId,
          relationshipStatusId = RelationshipStatuses.Confirmed.id,
          relationshipTypeId = RelationshipTypes.AddOn.id,
          recCreatedBy = bapiIdentifierUuid,
          recStatus = 1
        )
      )
    }

    "return a relation correctly if there's relation between productTokenKey and refProductTokenKey matched for multiple inner references" in {
      // this is for flight hackerfare which have same productTokenKey for 2 flight booking
      val scope = new RelationshipMapperSpecScope {}
      import scope._
      val res = mapper.constructAddOnRelationships(
        Seq(
          flight_1_ProductSaveStageResponse,
          flight_2_ProductSaveStageResponse,
          cegUpsellProductSaveStageResponse.copy(refProductTokenKey = Seq("Flight_id")),
          otherProductSaveStageResponseThatIsNotRelated
        )
      )
      res shouldMatchTo Seq(
        BaseBookingRelationship(
          sourceBookingId = cegUpsellBookingId,
          targetBookingId = flight_1_BookingId,
          relationshipStatusId = RelationshipStatuses.Confirmed.id,
          relationshipTypeId = RelationshipTypes.AddOn.id,
          recCreatedBy = bapiIdentifierUuid,
          recStatus = 1
        ),
        BaseBookingRelationship(
          sourceBookingId = cegUpsellBookingId,
          targetBookingId = flight_2_BookingId,
          relationshipStatusId = RelationshipStatuses.Confirmed.id,
          relationshipTypeId = RelationshipTypes.AddOn.id,
          recCreatedBy = bapiIdentifierUuid,
          recStatus = 1
        )
      )
    }

    "return no relation if there's no productTokenKey in any saveStageResponse matched with refProductTokenKey" in {
      val scope = new RelationshipMapperSpecScope {}
      import scope._
      val res = mapper.constructAddOnRelationships(
        Seq(
          propertyProductAloneSaveStageResponse,
          cegUpsellProductSaveStageResponse,
          otherProductSaveStageResponseThatIsNotRelated
        )
      )
      res shouldMatchTo Nil
    }

    "return no rebook and cancel relationships if there's no rebookAndCancelData present" in {
      val scope = new RelationshipMapperSpecScope {}
      import scope._
      val res = mapper.constructRebookAndCancelRelationships(
        Seq(
          propertyProductAloneSaveStageResponse,
          cegUpsellProductSaveStageResponse,
          otherProductSaveStageResponseThatIsNotRelated
        ),
        None
      )
      res shouldMatchTo Nil
    }

    "return a rebook and cancel relationship for single property booking when rebookAndCancelData is present" in {
      val scope = new RelationshipMapperSpecScope {}
      import scope._
      val originalBookingId   = 1
      val originalItineraryId = 1
      val res = mapper.constructRebookAndCancelRelationships(
        Seq(
          propertyProductAloneSaveStageResponse
        ),
        Some(
          RebookAndCancelData(
            originalBookingId = originalBookingId,
            originalItineraryId = originalItineraryId
          )
        )
      )
      res shouldMatchTo Seq(
        BaseBookingRelationship(
          sourceBookingId = propertyAloneBookingId,
          targetBookingId = originalBookingId,
          relationshipStatusId = RelationshipStatuses.Pending.id,
          relationshipTypeId = RelationshipTypes.RebookAndCancel.id,
          recCreatedBy = bapiIdentifierUuid,
          recStatus = 1
        )
      )
    }

    "return a rebook and cancel relationship for single property booking when cegUpsell add on product is present and rebookAndCancelData is present" in {
      val scope = new RelationshipMapperSpecScope {}
      import scope._
      val originalBookingId   = 1
      val originalItineraryId = 1
      val res = mapper.constructRebookAndCancelRelationships(
        Seq(
          propertyProductThatCoveredByCegUpsellSaveStageResponse,
          cegUpsellProductSaveStageResponse.copy(refProductTokenKey = Seq("Property_1"))
        ),
        Some(
          RebookAndCancelData(
            originalBookingId = originalBookingId,
            originalItineraryId = originalItineraryId
          )
        )
      )
      res shouldMatchTo Seq(
        BaseBookingRelationship(
          sourceBookingId = propertyThatCoveredByCegUpsellBookingId,
          targetBookingId = originalBookingId,
          relationshipStatusId = RelationshipStatuses.Pending.id,
          relationshipTypeId = RelationshipTypes.RebookAndCancel.id,
          recCreatedBy = bapiIdentifierUuid,
          recStatus = 1
        )
      )
    }

    "return multiple rebook and cancel relationship for property and flight booking rebookAndCancelData is present" in {
      val scope = new RelationshipMapperSpecScope {}
      import scope._
      val originalBookingId   = 1
      val originalItineraryId = 1
      val res = mapper.constructRebookAndCancelRelationships(
        Seq(
          flight_1_ProductSaveStageResponse,
          propertyProductThatCoveredByCegUpsellSaveStageResponse,
          cegUpsellProductSaveStageResponse.copy(refProductTokenKey = Seq("Flight_id")),
          cegUpsellProductSaveStageResponse.copy(refProductTokenKey = Seq("Property_1"))
        ),
        Some(
          RebookAndCancelData(
            originalBookingId = originalBookingId,
            originalItineraryId = originalItineraryId
          )
        )
      )
      res shouldMatchTo Seq(
        BaseBookingRelationship(
          sourceBookingId = flight_1_BookingId,
          targetBookingId = originalBookingId,
          relationshipStatusId = RelationshipStatuses.Pending.id,
          relationshipTypeId = RelationshipTypes.RebookAndCancel.id,
          recCreatedBy = bapiIdentifierUuid,
          recStatus = 1
        ),
        BaseBookingRelationship(
          sourceBookingId = propertyThatCoveredByCegUpsellBookingId,
          targetBookingId = originalBookingId,
          relationshipStatusId = RelationshipStatuses.Pending.id,
          relationshipTypeId = RelationshipTypes.RebookAndCancel.id,
          recCreatedBy = bapiIdentifierUuid,
          recStatus = 1
        )
      )
    }
  }
  trait RelationshipMapperSpecScope extends MockitoSugar with CreateMultiBookingHelper {

    val mockAgodaConfig: AgodaConfig = mock[AgodaConfig]
    val bapiIdentifierUuid           = "c4b4acf8-9bf9-4d77-bf43-dc929cda582d"

    when(mockAgodaConfig.bapiIdentifierUuid).thenReturn(bapiIdentifierUuid)
    val mapper = new BookingRelationshipHelperImpl(mockAgodaConfig)
    val defaultMultiProductsRequest: MultiProductsRequest = MultiProductsRequest(
      request = baseMultiProductReq,
      requestContext = baseRequestContext,
      properties = Seq.empty,
      flights = Seq.empty,
      vehicles = Seq.empty,
      protections = Seq.empty,
      activities = Seq.empty,
      cegFastTracks = Seq.empty,
      addOns = Seq.empty,
      bookingFlow = BookingFlow.Cart,
      commonPayment = None,
      isBookingFromCart = None
    )
    val mockCreationRequest: CreationRequest = defaultMultiProductsRequest.toCreationRequest
    val defaultBookingActionState: BookingActionState = BookingActionState(
      request = mockCreationRequest,
      customer = BookingActionStateCustomer(isUserLoggedIn = false),
      paymentInfo = PaymentInfo(
        method = PaymentMethod.Visa,
        paymentCurrency = "THB",
        paymentAmount = 2150,
        paymentAmountUSD = 60,
        gateway = Some(Gateway.GMO),
        siteExchangeRate = None,
        destinationCurrency = None,
        destinationExchangeRate = None,
        points = Vector.empty
      ),
      bookingState = None,
      campaignInfo = None
    )
    val propertyThatCoveredByCegUpsellBookingId         = 127612L
    val mockPropertyThatCoveredByCegUpsellBookingAction = mock[BookingWorkflowAction]
    when(mockPropertyThatCoveredByCegUpsellBookingAction.bookingId).thenReturn(
      Some(propertyThatCoveredByCegUpsellBookingId)
    )
    when(mockPropertyThatCoveredByCegUpsellBookingAction.state).thenReturn(defaultBookingActionState.toJson)
    val propertyProductThatCoveredByCegUpsellSaveStageResponse = PropertySaveStageResponse(
      bookingAction = mockPropertyThatCoveredByCegUpsellBookingAction,
      propertyCreationLocal = mock[PropertyBookingCreationLocal],
      productTokenKey = Some("Property_1")
    )

    val propertyAloneBookingId         = 127612L
    val mockPropertyAloneBookingAction = mock[BookingWorkflowAction]
    when(mockPropertyAloneBookingAction.bookingId).thenReturn(Some(propertyAloneBookingId))
    when(mockPropertyAloneBookingAction.state).thenReturn(defaultBookingActionState.toJson)
    val propertyProductAloneSaveStageResponse = PropertySaveStageResponse(
      bookingAction = mockPropertyAloneBookingAction,
      propertyCreationLocal = mock[PropertyBookingCreationLocal],
      productTokenKey = Some("Property_2")
    )

    val cegUpsellBookingId         = 237842L
    val mockCegUpsellBookingAction = mock[BookingWorkflowAction]
    when(mockCegUpsellBookingAction.bookingId).thenReturn(Some(cegUpsellBookingId))
    val mockCegUpsellBookingActionState = defaultBookingActionState
    when(mockCegUpsellBookingAction.state).thenReturn(mockCegUpsellBookingActionState.toJson)
    val cegUpsellProductSaveStageResponse = NonPropertySaveStageResponse(
      productType = ProductTypeEnum.CegFastTrack,
      bookingAction = mockCegUpsellBookingAction,
      bookingActionState = mockCegUpsellBookingActionState,
      productTokenKey = Some("CegUpsell_1"),
      refProductTokenKey = Seq()
    )

    val otherProductBookingId         = 293720L
    val mockOtherProductBookingAction = mock[BookingWorkflowAction]
    when(mockOtherProductBookingAction.bookingId).thenReturn(Some(otherProductBookingId))
    val mockOtherProductBookingActionState = defaultBookingActionState
    when(mockOtherProductBookingAction.state).thenReturn(mockOtherProductBookingActionState.toJson)
    val otherProductSaveStageResponseThatIsNotRelated = NonPropertySaveStageResponse(
      productType = ProductTypeEnum.CegFastTrack,
      bookingAction = mockOtherProductBookingAction,
      bookingActionState = mockOtherProductBookingActionState,
      productTokenKey = Some("CegFastTrack_1")
    )

    val flight_1_BookingId                                       = 12345L
    val mockFlight_1_ProductBookingAction: BookingWorkflowAction = mock[BookingWorkflowAction]
    when(mockFlight_1_ProductBookingAction.bookingId).thenReturn(Some(flight_1_BookingId))
    val mockFlight_1_ProductBookingActionState: BookingActionState = defaultBookingActionState
    when(mockFlight_1_ProductBookingAction.state).thenReturn(mockFlight_1_ProductBookingActionState.toJson)

    val flight_1_ProductSaveStageResponse: NonPropertySaveStageResponse = NonPropertySaveStageResponse(
      productType = ProductTypeEnum.Flight,
      bookingAction = mockFlight_1_ProductBookingAction,
      bookingActionState = mockFlight_1_ProductBookingActionState,
      productTokenKey = Some("Flight_id")
    )

    val flight_2_BookingId                                       = 23456L
    val mockFlight_2_ProductBookingAction: BookingWorkflowAction = mock[BookingWorkflowAction]
    when(mockFlight_2_ProductBookingAction.bookingId).thenReturn(Some(flight_2_BookingId))
    val mockFlight_2_ProductBookingActionState: BookingActionState = defaultBookingActionState
    when(mockFlight_2_ProductBookingAction.state).thenReturn(mockFlight_2_ProductBookingActionState.toJson)

    val flight_2_ProductSaveStageResponse: NonPropertySaveStageResponse = NonPropertySaveStageResponse(
      productType = ProductTypeEnum.Flight,
      bookingAction = mockFlight_2_ProductBookingAction,
      bookingActionState = mockFlight_2_ProductBookingActionState,
      productTokenKey = Some("Flight_id")
    )

  }
}
