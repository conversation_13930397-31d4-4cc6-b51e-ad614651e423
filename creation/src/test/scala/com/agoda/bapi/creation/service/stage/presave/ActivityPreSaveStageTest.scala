package com.agoda.bapi.creation.service.stage.presave

import com.agoda.bapi.common.config.AgodaConfig
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.{ActivityPax, CreditCard, Customer}
import com.agoda.bapi.common.model.UserContextMock
import com.agoda.bapi.common.model.activity.ActivityBookingToken
import com.agoda.bapi.common.model.addOn.UserTaxCountry
import com.agoda.bapi.common.model.multiproduct.MultiProductInfoDBModel
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.service.{FeatureAware, MessagesBag}
import com.agoda.bapi.creation.CreateMultiBookingHelper
import com.agoda.bapi.creation.mapper.activity.ActivityMapperImpl
import com.agoda.bapi.creation.mapper.ebe.ActivityRiskInfoMapperImpl
import com.agoda.bapi.creation.model.multi.{MultiProductsRequest, PreSaveProductStageRequest, Product, ReservedIds}
import com.agoda.bapi.creation.repository.{ActivityBookingRepositoryImpl, BaseBookingRepository, FlightBookingRepositoryImpl}
import com.agoda.capi.enigma.shared_model.booking.CustomerBillingInformation
import com.agoda.capi.enigma.shared_model.booking.pax.BaseBookingPax
import com.agoda.mpb.common.{BookingType, MultiProductType}
import com.agoda.mpbe.state.booking.{BaseBookingMeta, MetaType}
import mocks.{ActivityModelMock, RequestContextMock}
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.{any, eq => eqTo}
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfterEach
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import java.time.LocalDateTime
import scala.concurrent.Future

class ActivityPreSaveStageTest
    extends AsyncWordSpec
    with MockitoSugar
    with CreateMultiBookingHelper
    with BeforeAndAfterEach
    with Matchers
    with ActivityModelMock
    with RequestContextMock {

  val activityProduct =
    Product(BookingType.Activity, defaultActivityBookingToken(), Some(MultiProductType.SingleActivity))
  val featureAware = mock[FeatureAware]

  val multiProductsRequest = MultiProductsRequest(
    request = baseMultiProductReq,
    requestContext = baseRequestContext.copy(featureAware = Some(featureAware)),
    properties = Seq.empty,
    flights = Seq.empty,
    vehicles = Seq.empty,
    protections = Seq.empty,
    activities = Seq(activityProduct),
    cegFastTracks = Seq.empty,
    addOns = Seq.empty,
    bookingFlow = BookingFlow.Package,
    commonPayment = None,
    isBookingFromCart = None,
    userTaxCountry = Some(
      UserTaxCountry(
        userTaxCountryId = Some(111),
        bookerResidenceCountryId = Some(8889),
        paymentInstrumentCountryId = Some(8890),
        ipAddressCountryId = Some(8891)
      )
    )
  )

  private val mockAgodaConfig = mock[AgodaConfig]
  when(mockAgodaConfig.bapiIdentifierUuid).thenReturn("c4b4acf8-9bf9-4d77-bf43-dc929cda582d")
  private val mockActivityRepository    = mock[ActivityBookingRepositoryImpl]
  private val mockFlightRepository      = mock[FlightBookingRepositoryImpl]
  private val mockRiskInfoMapper        = mock[ActivityRiskInfoMapperImpl]
  private val mockBaseBookingRepository = mock[BaseBookingRepository]
  private val activityMapper            = new ActivityMapperImpl(mockAgodaConfig, mockRiskInfoMapper)
  val messagesBag                       = mock[MessagesBag]
  val userContext                       = Some(UserContextMock.value.copy(requestOrigin = "TH"))
  implicit val context: RequestContext =
    requestContext(messagesBag, userContext).copy(featureAware = Some(featureAware))

  private val processor = spy(
    new ActivityPreSaveStage(mockActivityRepository, mockBaseBookingRepository, mockFlightRepository, activityMapper)
  )

  private val baseRequest = PreSaveProductStageRequest(
    request = baseMultiProductReq,
    requestContext = context,
    itineraryInfo = baseItineraryPreSaveInfo.copy(itineraryId = defaultItineraryId),
    multiProductId = Some(baseMultiProductInfo),
    product = Product(BookingType.Activity, defaultActivityBookingToken(), Some(MultiProductType.SingleActivity)),
    bookingFlow = BookingFlow.Package,
    userTaxCountry = Some(
      UserTaxCountry(
        userTaxCountryId = Some(111),
        bookerResidenceCountryId = Some(8889),
        paymentInstrumentCountryId = Some(8890),
        ipAddressCountryId = Some(8891)
      )
    )
  )

  val activityBookingId = 67890L

  val activityPaxes: Seq[ActivityPax] = baseMultiProductReq.activityPaxes.toSeq.flatten
  val basePaxes: Seq[BaseBookingPax] =
    activityPaxes.map(p => mockActivityRepository.mapActivityToBasePax(defaultBookingId, p))
  val customer: Customer             = baseMultiProductReq.customer
  val creditcard: Option[CreditCard] = baseMultiProductReq.payment.creditCard
  val customerBillingInfo: CustomerBillingInformation =
    mockActivityRepository.mapCustomerBillingInfo(defaultBookingId, customer, defaultActivityBookingAnswers, creditcard)

  val breakdownIds: Seq[Long] = (1 to baseRequest.product.info.activityInfo.priceBreakdown.size).map(_.toLong + 555L)

  override protected def beforeEach(): Unit = {
    reset(mockActivityRepository)
    reset(mockBaseBookingRepository)
    reset(mockFlightRepository)
    reset(processor)
    reset(featureAware)
    when(mockBaseBookingRepository.getNextBookingSequenceNumber)
      .thenReturn(Future.successful(defaultBookingId.toLong))
    when(mockActivityRepository.savePaxToEnigma(any(), any())(any())).thenReturn(Future.successful(basePaxes))
    when(mockActivityRepository.saveCustomerBillingInfoToEnigma(any(), any(), any(), any())(any()))
      .thenReturn(Future.successful(customerBillingInfo))
    when(mockBaseBookingRepository.getNextBaseBookingSequenceNumber).thenReturn(Future.successful(activityBookingId))
    when(mockBaseBookingRepository.getNextBaseBookingOfferSequenceNumber).thenReturn(Future.successful(defaultOfferId))
    when(mockBaseBookingRepository.getNextBaseBookingMetaSequenceNumber).thenReturn(
      Future.successful(defaultMetaId.toLong)
    )
    when(mockBaseBookingRepository.getNextBaseBookingGeoSequenceNumber).thenReturn(
      Future.successful(defaultGeoId.toLong)
    )
    when(mockBaseBookingRepository.getNextBaseBookingPaxSequenceNumber).thenReturn(
      Future.successful(defaultPaxId.toLong)
    )
    when(mockBaseBookingRepository.getNextBaseBookingEssInfoSequenceNumber).thenReturn(
      Future.successful(defaultEssInfoId)
    )
    when(mockFlightRepository.getNextBreakdownIdSequenceNumbers(any()))
      .thenReturn(Future.successful(breakdownIds))
    when(mockFlightRepository.getNextActionIdSequenceNumber)
      .thenReturn(Future.successful(defaultActionId))
    when(featureAware.buildSeenExperimentTags(any())).thenReturn(Map.empty[String, String])

  }
  val multiProductId = 10L

  "process" should {
    "return correct result" in {
      val multiProductPreSaveRequest = MultiProductPreSaveRequest(
        multiProductsRequest,
        baseItineraryPreSaveInfo.copy(itineraryId = defaultItineraryId),
        Map(
          Some(MultiProductType.SingleActivity) -> MultiProductInfoDBModel(
            multiProductId,
            MultiProductType.Package
          )
        )
      )
      processor.process(multiProductPreSaveRequest).map { result =>
        val preSaveStageRequestCaptor: ArgumentCaptor[PreSaveProductStageRequest[ActivityBookingToken]] =
          ArgumentCaptor.forClass(classOf[PreSaveProductStageRequest[ActivityBookingToken]])
        verify(processor, times(1)).measuredReservedProductId(preSaveStageRequestCaptor.capture())(any())
        preSaveStageRequestCaptor.getValue.request shouldBe baseRequest.request
        verify(mockBaseBookingRepository, times(1)).getNextBookingSequenceNumber
        verify(mockBaseBookingRepository, times(1)).getNextBaseBookingOfferSequenceNumber
        verify(mockBaseBookingRepository, times(basePaxes.size)).getNextBaseBookingPaxSequenceNumber
        verify(mockActivityRepository, times(1))
          .savePaxToEnigma(eqTo(defaultBookingId), eqTo(activityPaxes.map(_.copy(id = defaultPaxId))))(any())
        verify(mockActivityRepository, times(1)).saveCustomerBillingInfoToEnigma(any(), any(), any(), any())(any())
        verify(mockBaseBookingRepository, times(1)).getNextBaseBookingEssInfoSequenceNumber

        val actvitySaveStageResponses = result.right.get
        actvitySaveStageResponses.size shouldEqual 1
        val actvitySaveStageResponse = actvitySaveStageResponses.head

        actvitySaveStageResponse.bookingAction.bookingId shouldBe Some(defaultBookingId)
        val activities = actvitySaveStageResponse.bookingActionState.itineraryState.product.activities
        activities.map(activity => {
          val bookingExcludeTime = activity.product.booking.copy(
            bookingDate = defaultStaticDateTime,
            bookingStartDate = LocalDateTime.MIN,
            bookingEndDate = None,
            recCreatedWhen = LocalDateTime.MIN,
            recModifiedWhen = None
          )
          bookingExcludeTime shouldBe defaultActivityProductModel.product.booking
          val offersExcludeTime =
            activity.product.bookingOffers.map(
              _.copy(
                startDate = LocalDateTime.MIN,
                endDate = Some(LocalDateTime.MIN),
                recCreatedWhen = Some(LocalDateTime.MIN),
                recModifiedWhen = Some(LocalDateTime.MIN)
              )
            )
          offersExcludeTime.head shouldBe defaultActivityProductModel.product.bookingOffers.head
            .copy(bookingOfferId = defaultOfferId)

          val essInfoExcludeTime =
            activity.product.essInfo.map(
              _.copy(
                recCreatedWhen = LocalDateTime.MIN,
                recModifiedWhen = Some(LocalDateTime.MIN)
              )
            )
          essInfoExcludeTime.head shouldBe defaultActivityProductModel.product.essInfo.head
            .copy(
              bookingEssInfoId = defaultEssInfoId
            )

          activity.product.bookingGeos shouldBe defaultActivityProductModel.product.bookingGeos
            .map(_.withBookingOfferId(defaultOfferId))
          val metasExcludeTime = activity.product.bookingMetas.map(_.withRecModifiedWhen(LocalDateTime.MIN))

          val expectedMetaWithPromocode = (defaultActivityProductModel.product.bookingMetas ++ Seq(
            BaseBookingMeta(
              metaId = defaultMetaId + 9,
              bookingId = defaultBookingId,
              metaName = Some("PROMOCODE"),
              metaType = MetaType.MetaType.STRING,
              metaValue = Some("COUPON"),
              recStatus = 1,
              recModifiedWhen = LocalDateTime.MIN,
              recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d"),
              version = 1,
              metaTypeString = Some("STRING")
            ),
            BaseBookingMeta(
              metaId = defaultMetaId + 10,
              bookingId = defaultBookingId,
              metaName = Some("PROMOCODE_CAMPAIGN_ID"),
              metaType = MetaType.MetaType.NUMBER,
              metaValue = Some("101"),
              recStatus = 1,
              recModifiedWhen = LocalDateTime.MIN,
              recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d"),
              version = 1,
              metaTypeString = Some("NUMBER")
            )
          )).map(_.withBookingOfferId(defaultOfferId))

          metasExcludeTime shouldBe expectedMetaWithPromocode

          val paxesExcludeTime = activity.product.bookingPaxes
            .map(_.copy(recCreatedWhen = Some(LocalDateTime.MIN), recModifiedWhen = Some(LocalDateTime.MIN)))
          paxesExcludeTime shouldBe defaultActivityProductModel.product.bookingPaxes
            .map(_.withPaxId(defaultPaxId.toLong))

          val priceSummariesExcludeTime = activity.product.bookingPriceSummaries.map(_.copy(recModifiedWhen = None))
          priceSummariesExcludeTime shouldBe defaultActivityProductModel.product.bookingPriceSummaries

          val cancellationInfoExcludeTime = activity.product.cancellationInfo
            .map(_.copy(recCreatedWhen = Some(LocalDateTime.MIN), recModifiedWhen = Some(LocalDateTime.MIN)))
          cancellationInfoExcludeTime shouldBe defaultActivityProductModel.product.cancellationInfo
          val clientInfoExcludeTime = activity.product.clientInfo.map(_.withTrackingCookieDate(LocalDateTime.MIN))
          clientInfoExcludeTime shouldBe defaultActivityProductModel.product.clientInfo

          activity.product.financeInfo shouldBe defaultActivityProductModel.product.financeInfo
          activity.product.fraudInfo shouldBe defaultActivityProductModel.product.fraudInfo
          val supplierInfoExcludeTime = activity.product.supplierInfo
            .map(_.copy(recCreatedWhen = Some(LocalDateTime.MIN), recModifiedWhen = Some(LocalDateTime.MIN)))
          supplierInfoExcludeTime shouldBe defaultActivityProductModel.product.supplierInfo
        })

        actvitySaveStageResponse.bookingActionState.itineraryProtoState shouldBe defined
      }
    }
  }

  "reservedProductId" should {
    "return correct result" in {
      val expected =
        ReservedIds(
          defaultBookingId,
          defaultActionId,
          baseRequest.multiProductId.map(_.multiProductId),
          baseRequest.product,
          offerId = Some(defaultOfferId),
          metaId = Some(defaultMetaId),
          geoId = Some(defaultGeoId),
          essInfoId = Some(defaultEssInfoId),
          breakdownIds = breakdownIds
        )

      processor.measuredReservedProductId(baseRequest)(baseRequest.measurementsContext).map { result =>
        result shouldBe expected
      }
    }
  }

  "saveDataToEnigma" should {
    "return success result" in {
      val request = baseRequest.copy(product = baseActivityProduct)
      processor
        .saveDataToEnigma(
          defaultBookingId.toLong,
          activityPaxes,
          customer,
          defaultActivityBookingAnswers,
          creditcard,
          request.measurementsContext
        )(request.requestContext)
        .map { _ =>
          verify(mockActivityRepository, times(1))
            .savePaxToEnigma(eqTo(defaultBookingId.toLong), eqTo(activityPaxes))(eqTo(request.requestContext))
          verify(mockActivityRepository, times(1))
            .saveCustomerBillingInfoToEnigma(
              eqTo(defaultBookingId.toLong),
              eqTo(customer),
              eqTo(defaultActivityBookingAnswers),
              eqTo(creditcard)
            )(eqTo(request.requestContext))
          succeed
        }
    }
    "return success result for empty email/phone in lead traveller" in {
      val request               = baseRequest.copy(product = baseActivityProduct)
      val overrideCustomer      = customer.copy(email = "<EMAIL>", phoneFormat = "0888888888")
      val overrideActivityPaxes = activityPaxes.map(_.copy(email = None, phoneNumber = None))
      val expectedActivityPaxes = activityPaxes.map {
        case p if p.primary => p.copy(email = Some("<EMAIL>"), phoneNumber = Some("0888888888"))
        case p              => p
      }

      processor
        .saveDataToEnigma(
          defaultBookingId.toLong,
          overrideActivityPaxes,
          overrideCustomer,
          defaultActivityBookingAnswers,
          creditcard,
          request.measurementsContext
        )(request.requestContext)
        .map { _ =>
          verify(mockActivityRepository, times(1))
            .savePaxToEnigma(eqTo(defaultBookingId.toLong), eqTo(expectedActivityPaxes))(eqTo(request.requestContext))
          verify(mockActivityRepository, times(1))
            .saveCustomerBillingInfoToEnigma(
              eqTo(defaultBookingId.toLong),
              eqTo(overrideCustomer),
              eqTo(defaultActivityBookingAnswers),
              eqTo(creditcard)
            )(eqTo(request.requestContext))
          succeed
        }
    }
  }

  "getPaxListWithId" should {
    "update PaxList correctly" in {
      val paxIdList       = Seq(999L, 111L)
      val activityPaxList = activityPaxes ++ activityPaxes
      activityPaxList.map(_.id) should not be paxIdList
      val paxList = processor.getPaxListWithId(activityPaxList, paxIdList)
      paxList.map(_.id) shouldBe paxIdList
    }
  }
}
