package com.agoda.bapi.creation.repository

import com.agoda.bapi.common.model.base.BaseBookingRelationshipInternal
import com.agoda.bapi.common.model.booking.BookingStateMessage.mapBaseBookingRelationshipToBaseBookingRelationshipForMessage
import com.agoda.bapi.common.model.booking.{BookingPaymentForMessage, BookingStateMessage, FlightItineraryForMessage, ItineraryHistoryForMessage, MultiProductBookingGroupModelMessage, MultiProductInfoForMessage, PaymentForMessage}
import com.agoda.bapi.common.model.flight.flightModel.{BookingPaymentState, ItineraryHistory, MultiProductItinerary, PaymentState}
import com.agoda.bapi.common.model.itinerary.ItineraryInternalModel
import com.agoda.bapi.common.model.multiproduct.MultiProductInfoDBModel
import com.agoda.mpb.common.MultiProductType.MultiProductType
import com.agoda.bapi.common.MessageService
import com.agoda.mpb.common.MultiProductType
import mocks.DBBookingModelHelper
import org.joda.time.DateTime
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._
import org.scalatest.{AppendedClues, BeforeAndAfterEach}
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future

class ItineraryBookingRepositoryTest
    extends AsyncWordSpec
    with Matchers
    with MockitoSugar
    with AppendedClues
    with BeforeAndAfterEach
    with DBBookingModelHelper {

  trait Fixture {
    val hadoopServiceMock: MessageService = mock[MessageService]
    val repository                        = new ItineraryBookingRepositoryImpl(hadoopServiceMock)
  }

  "sendItineraryModelForReplication" should {
    "call messaging send" in {
      val fixture = new Fixture {}
      import fixture._

      when(hadoopServiceMock.sendMessage(any())).thenReturn(Future.successful(()))
      val inputItinerary = MultiProductItinerary(itineraryId = 1, memberId = 1, recCreatedWhen = Some(DateTime.now()))
      val inputHistory =
        ItineraryHistory(
          actionId = 1,
          itineraryId = 2,
          bookingType = Some(3),
          bookingId = Some(123),
          actionType = 1,
          version = 0,
          actionDate = DateTime.now(),
          parameters = "mockParam",
          description = "mockDescription",
          recStatus = Some(3),
          recCreatedWhen = Some(DateTime.now()),
          replicatedFromDC = Some("HK")
        )
      val inputPayment =
        PaymentState(
          referenceId = 1,
          paymentId = 1,
          itineraryId = 1,
          actionId = Some(1),
          creditCardId = Some(1),
          transactionDate = DateTime.now(),
          transactionType = 1,
          paymentState = 1,
          referenceNo = "1",
          referenceType = 1,
          last4Digits = "1234",
          paymentMethodId = 1,
          gatewayId = 1,
          transactionId = "1251",
          paymentCurrency = "USD",
          paymentAmount = 20.2,
          amountUsd = 20.2,
          supplierCurrency = "USD",
          supplierAmount = 0.0,
          exchangeRateSupplierToPayment = 0.0,
          creditCardCurrency = "USD",
          upliftAmount = 0.0,
          siteExchangeRate = 0.0,
          upliftExchangeRate = 0.0,
          paymentTypeId = Some(1),
          token = Some("mockToken"),
          remark = Some("mockRemark"),
          installmentPlanId = None,
          recStatus = None,
          recCreatedWhen = None,
          referencePaymentId = None,
          points = None
        )

      val inputBookingPayment = BookingPaymentState(
        paymentId = 1234,
        bookingId = 456,
        paymentCurrency = "USD",
        paymentAmount = 100,
        amountUsd = 100,
        bookingPaymentId = Some(111)
      )

      val baseBookingRelationshipInternal = BaseBookingRelationshipInternal(
        relationshipId = 1,
        sourceBookingId = 54321,
        targetBookingId = 54322,
        relationshipStatusId = 1,
        relationshipTypeId = 0,
        recStatus = 1,
        recCreatedWhen = DateTime.now(),
        recCreatedBy = "ABC",
        recModifiedWhen = Some(DateTime.now()),
        recModifiedBy = Some("XYZ")
      )

      val multiProductInfos = Seq(
        MultiProductInfoDBModel(
          multiProductId = 1,
          multiProductType = MultiProductType.SingleProperty
        )
      )

      val multiProductBookingGroupModelMessage = MultiProductBookingGroupModelMessage(
        bookingId = mockMultiProductBookingGroup.bookingId,
        itineraryId = mockMultiProductBookingGroup.itineraryId,
        cartId = mockMultiProductBookingGroup.cartId,
        packageId = mockMultiProductBookingGroup.packageId
      )

      val itineraryModel = ItineraryInternalModel(
        itinerary = inputItinerary,
        history = Seq(inputHistory),
        payments = Seq(inputPayment),
        bookingPayments = Seq(inputBookingPayment),
        transientCCId = None,
        relationships = Seq(baseBookingRelationshipInternal),
        multiProductBookingGroups = Seq(mockMultiProductBookingGroup),
        ccToken = None
      )
      val bookingWorkflowAction = mockBookingWorkflowAction.copy(actionTypeId = 1, actionId = 1)

      val bookingStateMessageCaptor: ArgumentCaptor[BookingStateMessage] =
        ArgumentCaptor.forClass(classOf[BookingStateMessage])
      repository
        .sendItineraryModelForReplication(
          itineraryDbModel = itineraryModel,
          bookingWorkflowAction = bookingWorkflowAction,
          multiProductInfos = multiProductInfos,
          multiProductBookingGroups = Seq(mockMultiProductBookingGroup)
        )
        .map { _ =>
          verify(hadoopServiceMock, times(1))
            .sendMessage(
              bookingStateMessageCaptor.capture()
            )
          val message = bookingStateMessageCaptor.getValue
          val expected = BookingStateMessage(
            actionType = bookingWorkflowAction.actionTypeId,
            actionId = bookingWorkflowAction.actionId,
            bookingType = None,
            bookingId = 0, // legacy field, we replicate itinerary which can contain multiple bookingIds
            schemaVersion = "1",
            flights = Seq.empty,
            slices = Seq.empty,
            segments = Seq.empty,
            passengers = Seq.empty,
            payments = Seq(
              PaymentForMessage(
                referenceId = itineraryModel.payments.head.referenceId,
                paymentId = itineraryModel.payments.head.paymentId,
                itineraryId = itineraryModel.payments.head.itineraryId,
                actionId = itineraryModel.payments.head.actionId,
                creditCardId = itineraryModel.payments.head.creditCardId,
                transactionDate = itineraryModel.payments.head.transactionDate.toDate,
                transactionType = itineraryModel.payments.head.transactionType,
                paymentState = itineraryModel.payments.head.paymentState,
                referenceNo = itineraryModel.payments.head.referenceNo,
                referenceType = itineraryModel.payments.head.referenceType,
                last4Digits = itineraryModel.payments.head.last4Digits,
                paymentMethodId = itineraryModel.payments.head.paymentMethodId,
                gatewayId = itineraryModel.payments.head.gatewayId,
                transactionId = itineraryModel.payments.head.transactionId,
                paymentCurrency = itineraryModel.payments.head.paymentCurrency,
                paymentAmount = itineraryModel.payments.head.paymentAmount,
                amountUsd = itineraryModel.payments.head.amountUsd,
                supplierCurrency = itineraryModel.payments.head.supplierCurrency,
                supplierAmount = itineraryModel.payments.head.supplierAmount,
                exchangeRateSupplierToPayment = itineraryModel.payments.head.exchangeRateSupplierToPayment,
                creditCardCurrency = itineraryModel.payments.head.creditCardCurrency,
                upliftAmount = itineraryModel.payments.head.upliftAmount,
                siteExchangeRate = itineraryModel.payments.head.siteExchangeRate,
                upliftExchangeRate = itineraryModel.payments.head.upliftExchangeRate,
                remark = itineraryModel.payments.head.remark,
                paymentTypeId = itineraryModel.payments.head.paymentTypeId,
                token = itineraryModel.payments.head.token,
                recStatus = itineraryModel.payments.head.recStatus,
                recCreatedWhen = itineraryModel.payments.head.recCreatedWhen.map(_.toDate),
                referencePaymentId = itineraryModel.payments.head.referencePaymentId,
                points = itineraryModel.payments.head.points
              )
            ),
            bookingPayments = Seq(
              BookingPaymentForMessage(
                paymentId = itineraryModel.bookingPayments.head.paymentId,
                bookingId = itineraryModel.bookingPayments.head.bookingId,
                paymentCurrency = itineraryModel.bookingPayments.head.paymentCurrency,
                paymentAmount = itineraryModel.bookingPayments.head.paymentAmount.toDouble,
                amountUsd = itineraryModel.bookingPayments.head.amountUsd.toDouble,
                recStatus = itineraryModel.bookingPayments.head.recStatus,
                recCreatedWhen = itineraryModel.bookingPayments.head.recCreatedWhen.map(_.toDate),
                fxiUplift = itineraryModel.bookingPayments.head.fxiUplift.map(_.toDouble),
                loyaltyPoints = itineraryModel.bookingPayments.head.loyaltyPoints,
                supplierCurrency = itineraryModel.bookingPayments.head.supplierCurrency,
                supplierExchangeRate = itineraryModel.bookingPayments.head.supplierExchangeRate.map(_.toDouble),
                bookingPaymentId = itineraryModel.bookingPayments.head.bookingPaymentId
              )
            ),
            bookingRelationships = itineraryModel.relationships.headOption
              .map(head => mapBaseBookingRelationshipToBaseBookingRelationshipForMessage(Seq(head)))
              .get,
            breakdown = Seq.empty,
            breakdownPerPax = Seq.empty,
            baggageAllowance = Seq.empty,
            baggage = Seq.empty,
            history = Seq(
              ItineraryHistoryForMessage(
                actionId = itineraryModel.history.head.actionId,
                itineraryId = itineraryModel.history.head.itineraryId.toInt,
                bookingType = itineraryModel.history.head.bookingType,
                bookingId = itineraryModel.history.head.bookingId,
                actionType = itineraryModel.history.head.actionType,
                version = itineraryModel.history.head.version,
                actionDate = itineraryModel.history.head.actionDate.toDate,
                parameters = itineraryModel.history.head.parameters,
                description = itineraryModel.history.head.description,
                recStatus = itineraryModel.history.head.recStatus,
                recCreatedWhen = itineraryModel.history.head.recCreatedWhen.map(_.toDate)
              )
            ),
            summary = Seq.empty,
            paxTickets = Seq.empty,
            itinerary = FlightItineraryForMessage(
              itineraryId = itineraryModel.itinerary.itineraryId,
              memberId = itineraryModel.itinerary.memberId,
              recStatus = itineraryModel.itinerary.recStatus,
              recCreatedWhen = itineraryModel.itinerary.recCreatedWhen.map(_.toDate),
              recModifiedWhen = itineraryModel.itinerary.recModifiedWhen.map(_.toDate)
            ),
            userAgent = None,
            bookingAttribution = Seq.empty,
            itineraryDate = itineraryModel.itinerary.recCreatedWhen.get.toDate,
            protectionModels = None,
            multiProductInfos =
              Some(multiProductInfos.map(pf => MultiProductInfoForMessage(pf.multiProductId, pf.multiProductType.id))),
            flightSegmentInfoByPaxType = Seq.empty,
            segmentInfoByPaxType = Seq.empty,
            fareRulePolicies = None,
            flightSeatSelection = Seq.empty,
            vehicle = None,
            activities = None,
            properties = None,
            cegFastTracks = None,
            addOns = None,
            multiProductBookingGroups = Some(Seq(multiProductBookingGroupModelMessage)),
            flightBrandSelections = None,
            flightBrandAttributes = None,
            flightBrandAttributeParams = None,
            flightBaseBooking = None,
            flightBaseCancellationInfo = None,
            crossProductIsolatedFeature = None
          )
          message shouldBe expected
        }
    }
  }
}
