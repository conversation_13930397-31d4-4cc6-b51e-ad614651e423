package com.agoda.bapi.creation.service.stage.presave

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.Customer
import com.agoda.bapi.common.model.car.{CarBookingToken, DriverInfo, EnigmaCarDriverInfo}
import com.agoda.bapi.common.model.multiproduct.MultiProductInfoDBModel
import com.agoda.mpb.common.MultiProductType.MultiProductType
import com.agoda.bapi.common.model.payment.CreditCardBillingInfo
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.proxy.EnigmaApiProxy
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.CreateMultiBookingHelper
import com.agoda.bapi.creation.mapper.ebe.VehicleMapper
import com.agoda.bapi.creation.model.CreateRequest
import com.agoda.bapi.creation.model.multi.{EmptyProductReservedIds, MultiProductsRequest, PreSaveProductStageRequest, ReservedIds, VehicleReservedIds}
import com.agoda.bapi.creation.repository.{FlightBookingRepository, VehicleBookingRepository}
import com.agoda.bapi.creation.validation.CreateVehicleBookingHelper
import com.agoda.capi.enigma.shared_model.car.billinginfo.CarBillingInfo
import com.agoda.capi.enigma.shared_model.car.driver.{CarDriver => CapiCarDrivers}
import com.agoda.mpb.common.MultiProductType
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.{eq => eqTo, _}
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfterEach
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future

class VehiclePreSaveStageTest
    extends AsyncWordSpec
    with MockitoSugar
    with CreateMultiBookingHelper
    with BeforeAndAfterEach
    with Matchers {

  val enigmaApiProxy           = mock[EnigmaApiProxy]
  val vehicleBookingRepository = mock[VehicleBookingRepository]
  val vehicleMapper            = mock[VehicleMapper]
  val workflowRepository       = mock[FlightBookingRepository]
  val mockFeatureAware         = mock[FeatureAware]

  val processor = spy(
    new VehiclePreSaveStage(enigmaApiProxy, vehicleBookingRepository, vehicleMapper, workflowRepository)
  )

  val baseRequest = PreSaveProductStageRequest(
    request = baseMultiProductReq,
    requestContext = baseRequestContext.copy(featureAware = Some(mockFeatureAware)),
    itineraryInfo = baseItineraryPreSaveInfo,
    multiProductId = Some(baseMultiProductInfo),
    product = baseVehicleProduct,
    bookingFlow = BookingFlow.Package
  )

  val multiProductsRequest = MultiProductsRequest(
    request = baseMultiProductReq,
    requestContext = baseRequestContext.copy(featureAware = Some(mockFeatureAware)),
    properties = Seq.empty,
    flights = Seq.empty,
    vehicles = Seq(baseVehicleProduct),
    protections = Seq.empty,
    activities = Seq.empty,
    cegFastTracks = Seq.empty,
    addOns = Seq.empty,
    bookingFlow = BookingFlow.Package,
    commonPayment = None,
    isBookingFromCart = None
  )

  val bookingId                = 12345L
  val vehicleId                = 67890L
  val actionId                 = 40001L
  val breakdownSeqIds          = Seq(1111L, 1112L)
  val vehicleLocationId1       = 50001L
  val vehicleLocationId2       = 50002L
  val vehicleInfoId            = 60001L
  val enigmaVehicleBillingInfo = mock[CarBillingInfo]
  val enigmaVehicleDriver      = mock[CapiCarDrivers]

  override def beforeEach: Unit = {
    reset(enigmaApiProxy)
    reset(workflowRepository)
    reset(vehicleBookingRepository)
    reset(mockFeatureAware)
    reset(processor)
  }

  "process" should {
    "return correct result with pre-generated reserved IDs into all models" in {
      val vehicleDrivers = baseRequest.request.getVehicleDriverList(baseRequest.product.info.productTokenKey)
      val updatedDrivers =
        if (vehicleDrivers.nonEmpty) vehicleDrivers.map(_.toEnigmaDriverInfo.copy(age = 0))
        else Seq.empty
      val vehicleIds = Seq(vehicleId)

      when(
        enigmaApiProxy.saveVehicleBillingInfo(any[Long], any[CreditCardBillingInfo], any[Customer])(any[RequestContext])
      )
        .thenReturn(Future.successful(enigmaVehicleBillingInfo))
      when(
        enigmaApiProxy.saveVehicleDriver(any[Long], any[Seq[EnigmaCarDriverInfo]], any[Seq[Long]])(any[RequestContext])
      )
        .thenReturn(Future.successful(Vector(enigmaVehicleDriver)))
      when(workflowRepository.getNextBookingSequenceNumber)
        .thenReturn(Future.successful(bookingId))
      when(vehicleBookingRepository.getNextVehicleBookingLocationSequenceNumber)
        .thenReturn(Future.successful(vehicleLocationId1))
        .thenReturn(Future.successful(vehicleLocationId2))
      when(vehicleBookingRepository.getNextVehicleInfoSequenceNumber).thenReturn(Future.successful(vehicleInfoId))
      when(vehicleBookingRepository.getNextVehicleSequenceNumber).thenReturn(Future.successful(vehicleId))
      when(workflowRepository.getNextActionIdSequenceNumber).thenReturn(Future.successful(actionId))
      when(workflowRepository.getNextBreakdownIdSequenceNumbers(any())).thenReturn(Future.successful(breakdownSeqIds))

      val multiProductPreSaveRequest = MultiProductPreSaveRequest(
        multiProductsRequest,
        baseItineraryPreSaveInfo,
        Map(
          Some(MultiProductType.Package) -> MultiProductInfoDBModel(10, MultiProductType.Package)
        )
      )

      processor.process(multiProductPreSaveRequest).map { _ =>
        val preSaveStageRequestCaptor: ArgumentCaptor[PreSaveProductStageRequest[CarBookingToken]] =
          ArgumentCaptor.forClass(classOf[PreSaveProductStageRequest[CarBookingToken]])
        verify(processor).measuredReservedProductId(preSaveStageRequestCaptor.capture())(any())
        val preSaveStageRequestActual = preSaveStageRequestCaptor.getValue
        preSaveStageRequestActual.request shouldBe baseRequest.request
        preSaveStageRequestActual.itineraryInfo shouldBe baseRequest.itineraryInfo
        preSaveStageRequestActual.multiProductId shouldBe baseRequest.multiProductId
        preSaveStageRequestActual.product shouldBe baseRequest.product
        preSaveStageRequestActual.bookingFlow shouldBe baseRequest.bookingFlow

        val createRequestCaptor: ArgumentCaptor[CreateRequest] = ArgumentCaptor.forClass(classOf[CreateRequest])
        verify(processor).saveDataToEnigma(
          createRequestCaptor.capture(),
          eqTo(bookingId),
          eqTo(updatedDrivers),
          eqTo(vehicleIds)
        )
        val createRequestActual = createRequestCaptor.getValue
        createRequestActual.request shouldBe baseRequest.request

        verify(workflowRepository).getNextBookingSequenceNumber
        verify(vehicleBookingRepository).getNextVehicleSequenceNumber
        verify(workflowRepository).getNextActionIdSequenceNumber
        verify(workflowRepository).getNextBreakdownIdSequenceNumbers(2)
        succeed
      }
    }

    "save driverAge from booking token not carItem request" in {
      val tokenDriverAge                                             = 55
      val updatedCarToken                                            = baseCarToken.copy(driverInfo = Some(DriverInfo(driverAge = tokenDriverAge)))
      val updatedProduct                                             = baseVehicleProduct.copy(info = updatedCarToken)
      val createRequest: PreSaveProductStageRequest[CarBookingToken] = baseRequest.copy(product = updatedProduct)

      val vehicleDrivers = createRequest.request
        .getVehicleDriverList(createRequest.product.info.productTokenKey)
        .map(_.toEnigmaDriverInfo.copy(age = tokenDriverAge))
      val vehicleIds = Seq(vehicleId)

      when(
        enigmaApiProxy.saveVehicleBillingInfo(any[Long], any[CreditCardBillingInfo], any[Customer])(any[RequestContext])
      )
        .thenReturn(Future.successful(enigmaVehicleBillingInfo))
      when(
        enigmaApiProxy.saveVehicleDriver(any[Long], any[Seq[EnigmaCarDriverInfo]], any[Seq[Long]])(any[RequestContext])
      )
        .thenReturn(Future.successful(Vector(enigmaVehicleDriver)))
      when(workflowRepository.getNextBookingSequenceNumber)
        .thenReturn(Future.successful(bookingId))
      when(vehicleBookingRepository.getNextVehicleBookingLocationSequenceNumber)
        .thenReturn(Future.successful(vehicleLocationId1))
        .thenReturn(Future.successful(vehicleLocationId2))
      when(vehicleBookingRepository.getNextVehicleInfoSequenceNumber).thenReturn(Future.successful(vehicleInfoId))
      when(vehicleBookingRepository.getNextVehicleSequenceNumber).thenReturn(Future.successful(vehicleId))
      when(workflowRepository.getNextActionIdSequenceNumber).thenReturn(Future.successful(actionId))
      when(workflowRepository.getNextBreakdownIdSequenceNumbers(any())).thenReturn(Future.successful(breakdownSeqIds))

      val multiProductPreSaveRequest = MultiProductPreSaveRequest(
        multiProductsRequest.copy(vehicles = Seq(updatedProduct)),
        baseItineraryPreSaveInfo,
        Map(
          Some(MultiProductType.Package) -> MultiProductInfoDBModel(10, MultiProductType.Package)
        )
      )

      processor.process(multiProductPreSaveRequest).map { _ =>
        val preSaveStageRequestCaptor: ArgumentCaptor[PreSaveProductStageRequest[CarBookingToken]] =
          ArgumentCaptor.forClass(classOf[PreSaveProductStageRequest[CarBookingToken]])
        verify(processor).measuredReservedProductId(preSaveStageRequestCaptor.capture())(any())
        val preSaveStageRequestActual = preSaveStageRequestCaptor.getValue
        preSaveStageRequestActual.request shouldBe createRequest.request
        preSaveStageRequestActual.itineraryInfo shouldBe createRequest.itineraryInfo
        preSaveStageRequestActual.multiProductId shouldBe createRequest.multiProductId
        preSaveStageRequestActual.product shouldBe createRequest.product
        preSaveStageRequestActual.bookingFlow shouldBe createRequest.bookingFlow

        val createRequestCaptor: ArgumentCaptor[CreateRequest] = ArgumentCaptor.forClass(classOf[CreateRequest])
        verify(processor).saveDataToEnigma(
          createRequestCaptor.capture(),
          eqTo(bookingId),
          eqTo(vehicleDrivers),
          eqTo(vehicleIds)
        )
        val createRequestActual = createRequestCaptor.getValue
        createRequestActual.request shouldBe createRequest.request

        verify(workflowRepository).getNextBookingSequenceNumber
        verify(workflowRepository).getNextActionIdSequenceNumber
        succeed
      }
    }
  }

  "reservedProductId" should {
    "return correct result" in {
      val expected: ReservedIds[CarBookingToken, VehicleReservedIds] =
        ReservedIds(
          bookingId,
          vehicleId,
          baseRequest.multiProductId.map(_.multiProductId),
          baseRequest.product,
          breakdownIds = breakdownSeqIds,
          productReservedIds = Some(
            VehicleReservedIds(
              vehicleBookingLocationPickUpId = Some(vehicleLocationId1),
              vehicleBookingLocationDropOffId = Some(vehicleLocationId2),
              vehicleInfoId = Some(vehicleInfoId)
            )
          )
        )

      when(workflowRepository.getNextBookingSequenceNumber)
        .thenReturn(Future.successful(bookingId))
      when(vehicleBookingRepository.getNextVehicleSequenceNumber).thenReturn(Future.successful(vehicleId))
      when(workflowRepository.getNextActionIdSequenceNumber).thenReturn(Future.successful(vehicleId)) // actionId
      when(vehicleBookingRepository.getNextVehicleBookingLocationSequenceNumber)
        .thenReturn(Future.successful(vehicleLocationId1))
        .thenReturn(Future.successful(vehicleLocationId2))
      when(vehicleBookingRepository.getNextVehicleInfoSequenceNumber).thenReturn(Future.successful(vehicleInfoId))
      when(workflowRepository.getNextBreakdownIdSequenceNumbers(any())).thenReturn(Future.successful(breakdownSeqIds))

      processor.measuredReservedProductId(baseRequest)(baseRequest.measurementsContext).map { result =>
        result shouldBe expected
      }
    }
  }

  "saveDataToEnigma" should {
    val createRequest: CreateRequest = baseRequest
    val carDriver                    = Seq(CreateVehicleBookingHelper.mockDefaultCarDriver)
    val billInfo                     = CreditCardBillingInfo(createRequest.request.payment.creditCard)
    val ids                          = Seq(100L)

    "return success result" in {
      when(
        enigmaApiProxy.saveVehicleBillingInfo(any[Long], any[CreditCardBillingInfo], any[Customer])(any[RequestContext])
      )
        .thenReturn(Future.successful(enigmaVehicleBillingInfo))
      when(
        enigmaApiProxy.saveVehicleDriver(any[Long], any[Seq[EnigmaCarDriverInfo]], any[Seq[Long]])(any[RequestContext])
      )
        .thenReturn(Future.successful(Vector(enigmaVehicleDriver)))

      processor.saveDataToEnigma(createRequest, bookingId, carDriver, ids).map { _ =>
        verify(enigmaApiProxy)
          .saveVehicleBillingInfo(eqTo(bookingId), eqTo(billInfo), eqTo(createRequest.request.customer))(
            eqTo(createRequest.requestContext)
          )
        verify(enigmaApiProxy).saveVehicleDriver(eqTo(bookingId), eqTo(carDriver), eqTo(ids))(
          eqTo(createRequest.requestContext)
        )
        succeed
      }
    }

    "return failed result" in {
      val enigmaResponse = new Exception("mockException")

      when(
        enigmaApiProxy.saveVehicleBillingInfo(any[Long], any[CreditCardBillingInfo], any[Customer])(any[RequestContext])
      )
        .thenReturn(Future.failed(enigmaResponse))
      when(
        enigmaApiProxy.saveVehicleDriver(any[Long], any[Seq[EnigmaCarDriverInfo]], any[Seq[Long]])(any[RequestContext])
      )
        .thenReturn(Future.successful(Vector(enigmaVehicleDriver)))

      processor.saveDataToEnigma(createRequest, bookingId, carDriver, ids).failed.map { _ =>
        verify(enigmaApiProxy)
          .saveVehicleBillingInfo(eqTo(bookingId), eqTo(billInfo), eqTo(createRequest.request.customer))(
            eqTo(createRequest.requestContext)
          )
        verify(enigmaApiProxy).saveVehicleDriver(eqTo(bookingId), eqTo(carDriver), eqTo(ids))(
          eqTo(createRequest.requestContext)
        )
        succeed
      }
    }
  }

}
