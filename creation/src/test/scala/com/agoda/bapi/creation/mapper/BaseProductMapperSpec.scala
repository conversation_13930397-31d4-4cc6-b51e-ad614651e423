package com.agoda.bapi.creation.mapper

import com.agoda.bapi.common.model.ItineraryId
import com.agoda.bapi.common.model.activity.ActivityBookingToken
import com.agoda.bapi.common.model.addOn.{AddOnMeta, MetaTypeInternals}
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.creation.model.CreateRequest
import com.agoda.bapi.creation.model.db.BookingActionState
import com.agoda.bapi.creation.model.multi.{EmptyProductReservedIds, ReservedIds}
import com.agoda.bapi.creation.{<PERSON>reate<PERSON>ooking<PERSON>elper, CreateFlightBookingHelper}
import com.agoda.commons.agprotobuf.meta.{MetaType => UniMetaType}
import com.agoda.mpbe.state.booking.BaseBookingMeta
import com.agoda.mpbe.state.booking.MetaType._
import com.agoda.mpbe.state.product.activity.ActivityProductModel
import com.softwaremill.diffx.Diff
import com.softwaremill.diffx.scalatest.DiffShouldMatcher._
import mocks.ActivityModelMock
import org.joda.time.DateTime
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{AppendedClues, BeforeAndAfter, OptionValues}
import org.scalatestplus.mockito.MockitoSugar

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}

class BaseProductMapperSpec
    extends AsyncWordSpec
    with Matchers
    with AppendedClues
    with BeforeAndAfter
    with CreateFlightBookingHelper
    with CreateBookingHelper
    with OptionValues
    with ActivityModelMock {

  "toBaseBookingMeta" should {
    "zip bookingMetaIds and content correctly" in {

      implicit val metaTypeDiff: Diff[MetaType] = Diff.derived[MetaType]
      implicit val baseBookingMetaDiff: Diff[BaseBookingMeta] = Diff
        .derived[BaseBookingMeta]
        .ignore(_.recModifiedWhen)

      val scope = new BaseProductMapperSpecScope {}
      import scope._
      val bookingId = 124344
      val expectedMetas = Seq(
        BaseBookingMeta(
          metaId = 717L,
          bookingId = bookingId,
          metaName = Some("MyFirstMeta"),
          metaType = MetaType.STRING,
          metaValue = Some("My First Meta Value"),
          recStatus = 1,
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d")
        ),
        BaseBookingMeta(
          metaId = 718L,
          bookingId = bookingId,
          metaName = Some("MySecondMeta"),
          metaType = MetaType.JSON,
          metaValue = Some("{\"Foo\": \"Bar\"}"),
          recStatus = 1,
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d")
        ),
        BaseBookingMeta(
          metaId = 719L,
          bookingId = bookingId,
          metaName = Some("MyThirdMeta"),
          metaType = MetaType.NUMBER,
          metaValue = Some("3"),
          recStatus = 1,
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d")
        )
      )

      baseProductMapper.toBaseBookingMeta(
        bookingMetaContents = Seq(
          BaseBookingMeta(
            bookingId = bookingId,
            metaName = Some("MyFirstMeta"),
            metaType = MetaType.STRING,
            metaValue = Some("My First Meta Value")
          ),
          BaseBookingMeta(
            bookingId = bookingId,
            metaName = Some("MySecondMeta"),
            metaType = MetaType.JSON,
            metaValue = Some("{\"Foo\": \"Bar\"}")
          ),
          BaseBookingMeta(
            bookingId = bookingId,
            metaName = Some("MyThirdMeta"),
            metaType = MetaType.NUMBER,
            metaValue = Some("3")
          )
        ),
        bookingMetaIds = Seq(717L, 718L, 719L),
        bookingId = bookingId,
        bookingDateTime = DateTime.parse("2022-02-18T15:00:00"),
        recModifiedBy = UUID.fromString("c4b4acf8-9bf9-4d77-bf43-dc929cda582d")
      ) shouldMatchTo expectedMetas
    }
  }

  "getMetaContents" should {
    "return correct mapped meta" in {
      val scope = new BaseProductMapperSpecScope {}
      import scope._
      val addOnMeta = Seq(
        AddOnMeta(
          metaName = "CEG_FASTTRACK_TIER",
          metaValue = "NormalHotelFastTrack",
          metaType = MetaTypeInternals.String,
          version = Some(1),
          metaTypeAsString = Some(UniMetaType.STRING.toString())
        )
      )
      val expectedMetas: Seq[BaseBookingMeta] = Seq(
        BaseBookingMeta(
          metaName = Some("CEG_FASTTRACK_TIER"),
          metaType = MetaType.STRING,
          metaValue = Some(
            "NormalHotelFastTrack"
          ),
          version = 1,
          metaTypeString = Some(UniMetaType.STRING.toString())
        )
      )
      val resF = baseProductMapper.getMetaContents(addOnMeta)
      resF.map(res => res shouldBe expectedMetas)
    }
  }

}

trait BaseProductMapperSpecScope extends MockitoSugar {
  val baseProductMapper: BaseProductMapper[ActivityBookingToken, ActivityProductModel, EmptyProductReservedIds] =
    new BaseProductMapper[ActivityBookingToken, ActivityProductModel, EmptyProductReservedIds] {
      def getMetaContents(reservedIds: ReservedIds[ActivityBookingToken, EmptyProductReservedIds])(implicit
          ec: ExecutionContext
      ): Future[Seq[BaseBookingMeta]] = Future.successful(Nil)

      def toBookingAction(
          request: CreateRequest,
          itineraryId: ItineraryId,
          reservedIds: ReservedIds[ActivityBookingToken, EmptyProductReservedIds],
          bookingActionState: BookingActionState
      ): BookingWorkflowAction = mock[BookingWorkflowAction]

      def toBookingActionState(
          request: CreateRequest,
          itineraryId: ItineraryId,
          reservedIds: ReservedIds[ActivityBookingToken, EmptyProductReservedIds],
          productModel: ActivityProductModel,
          bookingDateTime: DateTime
      ): BookingActionState = mock[BookingActionState]
    }
}
