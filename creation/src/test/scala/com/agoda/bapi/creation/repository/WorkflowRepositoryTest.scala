package com.agoda.bapi.creation.repository

import com.agoda.bapi.agent.common.schema.BookingActionMessageTopic
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.creation.model.db.BookingActionIdentifier
import com.agoda.bapi.creation.proxy.WorkflowDbProxy
import com.agoda.bapi.creation.service.BookingActionMessage
import mocks.WorkflowModelMock
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers.{any, eq => eqTo}
import org.mockito.Mockito.{times, verify, when}

import scala.concurrent.Future
import org.scalatestplus.mockito.MockitoSugar
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec

class WorkflowRepositoryTest extends AsyncWordSpec with MockitoSugar with Matchers with WorkflowModelMock {

  val dbProxy              = mock[WorkflowDbProxy]
  val bookingAction        = defaultBookingAction
  val bookingActionMessage = defaultBookingActionMessage
  val repository           = new WorkflowRepositoryImpl(dbProxy)

  "insertBookingAction" should {
    "call proxy" in {
      when(dbProxy.insertBookingAction(any[BookingWorkflowAction]))
        .thenReturn(Future.successful(bookingAction))
      repository.insertBookingAction(bookingAction).map { _ =>
        verify(dbProxy, times(1)).insertBookingAction(eqTo(bookingAction))
        succeed
      }
    }
  }

  "getBookingAction" should {
    "call proxy" in {
      val actionId = 1L
      when(dbProxy.getBookingAction(any[Long])).thenReturn(Future.successful(Some(bookingAction)))
      repository.getBookingAction(actionId).map { _ =>
        verify(dbProxy, times(1)).getBookingAction(eqTo(actionId))
        succeed
      }
    }
  }

  "getBookingActionBySupplierBookingId" should {
    val topic = BookingActionMessageTopic.BAM_Topic_Ticketing.value
    val uuid  = "BD811DA2-B3D0-470E-B7CD-F15B8A460EB0"
    "call proxy" in {
      val supplierBookingId = "1"
      when(dbProxy.getBookingActionBySupplierBookingId(any[String], any[Int], any[String]))
        .thenReturn(Future.successful(Some(bookingAction)))
      repository.getBookingActionBySupplierBookingId(supplierBookingId, topic, uuid).map { _ =>
        verify(dbProxy, times(1)).getBookingActionBySupplierBookingId(eqTo(supplierBookingId), eqTo(topic), eqTo(uuid))
        succeed
      }
    }
  }

  "getBookingActionByWorkflowId" should {
    "call proxy" in {
      val itineraryId = 2L
      val workflowId  = 2
      when(dbProxy.getBookingActionByWorkflowId(any[Long], any[Int])).thenReturn(Future.successful(Some(bookingAction)))
      repository.getBookingActionByWorkflowId(itineraryId, workflowId).map { _ =>
        verify(dbProxy, times(1)).getBookingActionByWorkflowId(eqTo(itineraryId), eqTo(workflowId))
        succeed
      }
    }
  }

  "insertBookingActionMessage" should {
    "call proxy" in {
      val message = BookingActionMessage(None, 1L, 1, "", "", DateTime.now)
      when(dbProxy.insertBookingActionMessage(message)).thenReturn(Future.successful(1L))
      repository.insertBookingActionMessage(message).map { _ =>
        verify(dbProxy, times(1)).insertBookingActionMessage(eqTo(message))
        succeed
      }
    }
  }

  "getBookingActionMessage" should {
    "call proxy" in {
      val actionId = 1L
      when(dbProxy.getBookingActionMessage(any[Long])).thenReturn(Future.successful(Seq(defaultBookingActionMessage)))
      repository.getBookingActionMessage(actionId).map { _ =>
        verify(dbProxy, times(1)).getBookingActionMessage(eqTo(actionId))
        succeed
      }
    }
  }

  "getBookingActionByBookingId" should {
    "call proxy" in {
      val bookingId = 1L
      when(dbProxy.getBookingActionByBookingId(any[Long])).thenReturn(Future.successful(Some(bookingAction)))
      repository.getBookingActionByBookingId(bookingId).map { _ =>
        verify(dbProxy, times(1)).getBookingActionByBookingId(eqTo(bookingId))
        succeed
      }
    }
  }

  "getMasterBookingActionByItineraryId" should {
    "call proxy" in {
      val itineraryId = 2L
      when(dbProxy.getMasterBookingActionByItineraryId(any[Long])).thenReturn(Future.successful(Some(bookingAction)))
      repository.getMasterBookingActionByItineraryId(itineraryId).map { _ =>
        verify(dbProxy, times(1)).getMasterBookingActionByItineraryId(eqTo(itineraryId))
        succeed
      }
    }
  }

  "getBookingActionByItineraryId" should {
    "call proxy" in {
      val itineraryId = 2L
      when(dbProxy.getBookingActionByItineraryId(any[Long])).thenReturn(Future.successful(Seq(bookingAction)))
      repository.getBookingActionByItineraryId(itineraryId).map { _ =>
        verify(dbProxy, times(1)).getBookingActionByItineraryId(eqTo(itineraryId))
        succeed
      }
    }
  }

  "getBaseBookingActionBySupplierBookingId" should {
    val topic = BookingActionMessageTopic.BAM_Topic_ActivityProvisioningResult.value
    val uuid  = "BD811DA2-B3D0-470E-B7CD-F15B8A460EB0"
    "call proxy" in {
      val supplierBookingId = "1"
      when(dbProxy.getBaseBookingActionBySupplierBookingId(any[String], any[Int], any[String]))
        .thenReturn(Future.successful(Some(bookingAction)))
      repository.getBaseBookingActionBySupplierBookingId(supplierBookingId, topic, uuid).map { _ =>
        verify(dbProxy, times(1))
          .getBaseBookingActionBySupplierBookingId(eqTo(supplierBookingId), eqTo(topic), eqTo(uuid))
        succeed
      }
    }
  }

  "updateBookingActionMessage" should {
    "call proxy" in {
      val messageId = 1L
      val content   = "{}"
      when(dbProxy.updateBookingActionMessage(any[Long], any[String])).thenReturn(Future.successful(1))
      repository.updateBookingActionMessage(messageId, content).map { _ =>
        verify(dbProxy, times(1)).updateBookingActionMessage(eqTo(messageId), eqTo(content))
        succeed
      }
    }
  }

  "getBookingActionIdentifierByBookingIdAndTopicId" should {
    "call proxy with the correct arguments" in {
      val expectedBookingActionIdentifier = BookingActionIdentifier(
        actionId = 101L,
        itineraryId = 102L,
        bookingId = 103L
      )

      when(dbProxy.getBookingActionIdentifierByBookingIdAndTopicId(any(), any(), any()))
        .thenReturn(Future.successful(Some(expectedBookingActionIdentifier)))

      repository
        .getBookingActionIdentifierByBookingIdAndTopicId(
          bookingId = 103L,
          topicId = BookingActionMessageTopic.BAM_Topic_ActivityProvisioningResult.value,
          recCreatedBy = "USER_UUID"
        )
        .map { result =>
          verify(dbProxy, times(1)).getBookingActionIdentifierByBookingIdAndTopicId(
            bookingId = 103L,
            topicId = BookingActionMessageTopic.BAM_Topic_ActivityProvisioningResult.value,
            recCreatedBy = "USER_UUID"
          )

          result shouldBe Some(expectedBookingActionIdentifier)
        }
    }
  }
}
