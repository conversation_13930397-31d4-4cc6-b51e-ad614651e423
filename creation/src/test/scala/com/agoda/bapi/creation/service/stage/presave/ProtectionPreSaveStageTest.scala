package com.agoda.bapi.creation.service.stage.presave

import com.agoda.bapi.common.model.flight.TripItem
import com.agoda.bapi.common.model.multiproduct.MultiProductInfoDBModel
import com.agoda.bapi.common.model.product.{BookingFlow, ProductTypeEnum}
import com.agoda.bapi.common.model.protection.ProtectionBookingStateModel.ProtectionModelInternal
import com.agoda.bapi.common.model.tripProtection.ProtectionProductIds
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.util.{JodaDateTimeUtils, TripProtectionTokenHelper}
import com.agoda.bapi.creation.mapper.ebe.ProtectionMapperImpl
import com.agoda.bapi.creation.model.CreateRequest
import com.agoda.bapi.creation.model.db.BookingActionState
import com.agoda.bapi.creation.model.multi._
import com.agoda.bapi.creation.repository.FlightBookingRepository
import com.agoda.bapi.creation.{CreateMultiBookingHelper, ExternalDIHelper}
import com.agoda.mpb.common.{BookingType, MultiProductType}
import mocks.{DBBookingModelHelper, FlightModelMock, ProtectionModelMock, VehicleModelMock}
import org.joda.time.DateTime
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.{eq => eqTo, _}
import org.mockito.Mockito._
import org.scalatest
import org.scalatest.BeforeAndAfterEach
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future

class ProtectionPreSaveStageTest
    extends AsyncWordSpec
    with MockitoSugar
    with Matchers
    with BeforeAndAfterEach
    with CreateMultiBookingHelper
    with ProtectionModelMock
    with DBBookingModelHelper
    with ExternalDIHelper
    with VehicleModelMock
    with FlightModelMock {
  val workflowRepository: FlightBookingRepository = mock[FlightBookingRepository]
  val dateTimeUtils: JodaDateTimeUtils            = mock[JodaDateTimeUtils]
  val currentDate: DateTime                       = DateTime.now
  when(dateTimeUtils.getCurrentDateTime).thenReturn(currentDate)
  val protectionTokenHelper: TripProtectionTokenHelper = mock[TripProtectionTokenHelper]

  val protectionMapper: ProtectionMapperImpl = spy(new ProtectionMapperImpl(dateTimeUtils, protectionTokenHelper))

  val processor: ProtectionPreSaveStage = spy(
    new ProtectionPreSaveStage(workflowRepository, protectionMapper)
  )

  val mockFlightBookingActionState: BookingActionState  = mock[BookingActionState]
  val mockVehicleBookingActionState: BookingActionState = mock[BookingActionState]

  val flightResponse = NonPropertySaveStageResponse(
    productType = ProductTypeEnum.Flight,
    bookingAction = mockBookingWorkflowAction,
    bookingActionState = mockFlightBookingActionState,
    productTokenKey = Some("Flight_1")
  )

  val vehicleSaveStageResponse = NonPropertySaveStageResponse(
    productType = ProductTypeEnum.Car,
    bookingAction = mockBookingWorkflowAction,
    bookingActionState = mockVehicleBookingActionState,
    productTokenKey = Some("Vehicle_1")
  )

  val baseProtectionProduct: Product[ProtectionAncillaryModel] = Product(
    BookingType.Protection,
    ProtectionAncillaryModel(baseProtectionToken, Seq(flightResponse)),
    Some(MultiProductType.Package)
  )

  val baseRequest: PreSaveProductStageRequest[ProtectionAncillaryModel] = PreSaveProductStageRequest(
    request = baseMultiProductReq,
    requestContext = baseRequestContext,
    itineraryInfo = baseItineraryPreSaveInfo,
    multiProductId = Some(baseMultiProductInfo),
    product = baseProtectionProduct,
    bookingFlow = BookingFlow.Package
  )
  val bookingId               = 100L
  val actionId                = 10L
  val expectedProductTokenKey = "~product_token_key~"

  val flightBooking = defaultFlightBooking.copy(
    flapiItineraryId = Some("flapiItineraryId"),
    flightBookingId = 123L,
    productTokenKey = Some(expectedProductTokenKey)
  )
  val multiProductId = 10L

  override def beforeEach: Unit = {
    reset(workflowRepository, protectionTokenHelper, protectionMapper)
  }

  "process" should {
    "return correct result" in {
      val flightModelInternal = defaultFlightModel.copy(
        flights = Seq(flightBooking)
      )
      when(mockFlightBookingActionState.bookingState).thenReturn(Some(flightModelInternal))

      val products = Seq(ProtectionProductIds(expectedProductTokenKey, ProductTypeEnum.Flight, Some("some data")))

      val token = baseProtectionToken.copy(products = products)
      val protectionProduct =
        baseProtectionProduct.copy(info = ProtectionAncillaryModel(token, Seq(flightResponse)))

      val request = baseRequest.copy(product = protectionProduct)

      when(workflowRepository.getNextBookingSequenceNumber)
        .thenReturn(Future.successful(bookingId))

      when(workflowRepository.getNextBreakdownIdSequenceNumbers(anyInt)).thenReturn(Future.successful(Seq(101L, 102L)))
      when(workflowRepository.getNextActionIdSequenceNumber).thenReturn(Future.successful(actionId))

      val multiProductsRequest = MultiProductsRequest(
        request = baseMultiProductReq,
        requestContext = baseRequestContext,
        properties = Seq.empty,
        flights = Seq.empty,
        vehicles = Seq.empty,
        protections = Seq(protectionProduct),
        activities = Seq.empty,
        cegFastTracks = Seq.empty,
        addOns = Seq.empty,
        bookingFlow = BookingFlow.Package,
        commonPayment = None,
        isBookingFromCart = None
      )

      val multiProductPreSaveRequest = MultiProductPreSaveRequest(
        multiProductsRequest,
        baseItineraryPreSaveInfo,
        Map(
          Some(MultiProductType.Package) -> MultiProductInfoDBModel(
            multiProductId,
            MultiProductType.Package
          )
        )
      ).withDependencies(Seq(flightResponse), Seq.empty)

      processor.process(multiProductPreSaveRequest).map { _ =>
        verify(processor, times(1)).measuredReservedProductId(eqTo(request))(eqTo(request.measurementsContext))
        verify(processor, times(1)).getFlightProductDetails(Seq(flightResponse), token)
        verify(protectionMapper, times(1)).mapBookingAction(
          any[CreateRequest],
          anyLong(),
          any[ReservedIds[ProtectionAncillaryModel, EmptyProductReservedIds]],
          any[BookingActionState]
        )
        verify(protectionMapper, times(1)).mapSingleProtectionBooking(
          any[CreateRequest],
          anyLong(),
          anyLong(),
          any[ReservedIds[ProtectionAncillaryModel, EmptyProductReservedIds]],
          eqTo(Seq(flightBooking.flightBookingId)),
          any(),
          any()
        )
        verify(protectionMapper, times(1)).mapBookingActionState(
          any[CreateRequest],
          anyLong(),
          any[ReservedIds[ProtectionAncillaryModel, EmptyProductReservedIds]],
          any[ProtectionModelInternal]
        )
        succeed
      }
    }
    "map correct date" in {
      val startDate = Option(DateTime.parse("2021-08-01T16:01"))
      val endDate   = Option(DateTime.parse("2021-08-03T16:01"))
      val flightModelInternal = defaultFlightModel.copy(
        flights = Seq(
          flightBooking.copy(tripStartDate = startDate)
        )
      )
      val secondFlightModelInteral = defaultFlightModel.copy(
        flights = Seq(
          flightBooking.copy(tripEndDate = endDate, flightBookingId = 124L)
        )
      )

      when(mockFlightBookingActionState.bookingState)
        .thenReturn(Some(flightModelInternal))
        .thenReturn(Some(secondFlightModelInteral))
      val products = Seq(ProtectionProductIds(expectedProductTokenKey, ProductTypeEnum.Flight, Some("some data")))
      val token    = baseProtectionToken.copy(products = products)
      val protectionProduct =
        baseProtectionProduct.copy(info = ProtectionAncillaryModel(token, Seq(flightResponse, flightResponse)))
      val request = baseRequest.copy(product = protectionProduct)
      when(workflowRepository.getNextBookingSequenceNumber)
        .thenReturn(Future.successful(bookingId))

      when(workflowRepository.getNextBreakdownIdSequenceNumbers(anyInt)).thenReturn(Future.successful(Seq(101L, 102L)))
      when(workflowRepository.getNextActionIdSequenceNumber).thenReturn(Future.successful(actionId))

      val captor: ArgumentCaptor[TripItem] = ArgumentCaptor.forClass(classOf[TripItem])

      val multiProductsRequest = MultiProductsRequest(
        request = baseMultiProductReq,
        requestContext = baseRequestContext,
        properties = Seq.empty,
        flights = Seq.empty,
        vehicles = Seq.empty,
        protections = Seq(protectionProduct),
        activities = Seq.empty,
        cegFastTracks = Seq.empty,
        addOns = Seq.empty,
        bookingFlow = BookingFlow.Package,
        commonPayment = None,
        isBookingFromCart = None
      )

      val multiProductPreSaveRequest = MultiProductPreSaveRequest(
        multiProductsRequest,
        baseItineraryPreSaveInfo,
        Map(
          Some(MultiProductType.Package) -> MultiProductInfoDBModel(
            multiProductId,
            MultiProductType.Package
          )
        )
      ).withDependencies(Seq(flightResponse, flightResponse), Seq.empty)

      processor.process(multiProductPreSaveRequest).map { _ =>
        verify(processor, times(1)).measuredReservedProductId(eqTo(request))(eqTo(request.measurementsContext))
        verify(processor, times(1)).getFlightProductDetails(Seq(flightResponse, flightResponse), token)
        verify(protectionMapper, times(1)).mapBookingAction(
          any[CreateRequest],
          anyLong(),
          any[ReservedIds[ProtectionAncillaryModel, EmptyProductReservedIds]],
          any[BookingActionState]
        )
        verify(protectionMapper, times(1)).mapSingleProtectionBooking(
          any[CreateRequest],
          anyLong(),
          anyLong(),
          any[ReservedIds[ProtectionAncillaryModel, EmptyProductReservedIds]],
          any(),
          captor.capture(),
          any()
        )
        captor.getValue shouldBe TripItem(startDate, endDate)
        verify(protectionMapper, times(1)).mapBookingActionState(
          any[CreateRequest],
          anyLong(),
          any[ReservedIds[ProtectionAncillaryModel, EmptyProductReservedIds]],
          any[ProtectionModelInternal]
        )
        succeed
      }
    }
    "return correct result for drive protection" in {
      when(mockVehicleBookingActionState.vehicleBookingState).thenReturn(Some(defaultMockVehicleModel))
      val products = Seq(ProtectionProductIds(expectedProductTokenKey, ProductTypeEnum.Car, Some("some data")))
      val token    = baseProtectionToken.copy(protectionType = 2, products = products)
      val protectionProduct =
        baseProtectionProduct.copy(info = ProtectionAncillaryModel(token, Seq.empty, Seq(vehicleSaveStageResponse)))
      val request = baseRequest.copy(product = protectionProduct)
      when(workflowRepository.getNextBookingSequenceNumber)
        .thenReturn(Future.successful(bookingId))

      when(workflowRepository.getNextBreakdownIdSequenceNumbers(anyInt)).thenReturn(Future.successful(Seq(101L, 102L)))
      when(workflowRepository.getNextActionIdSequenceNumber).thenReturn(Future.successful(actionId))

      val multiProductsRequest = MultiProductsRequest(
        request = baseMultiProductReq,
        requestContext = baseRequestContext,
        properties = Seq.empty,
        flights = Seq.empty,
        vehicles = Seq.empty,
        protections = Seq(protectionProduct),
        activities = Seq.empty,
        cegFastTracks = Seq.empty,
        addOns = Seq.empty,
        bookingFlow = BookingFlow.Package,
        commonPayment = None,
        isBookingFromCart = None
      )

      val multiProductPreSaveRequest = MultiProductPreSaveRequest(
        multiProductsRequest,
        baseItineraryPreSaveInfo,
        Map(
          Some(MultiProductType.Package) -> MultiProductInfoDBModel(
            multiProductId,
            MultiProductType.Package
          )
        )
      ).withDependencies(Seq.empty, Seq(vehicleSaveStageResponse))

      processor.process(multiProductPreSaveRequest).map { response =>
        verify(processor, times(1)).measuredReservedProductId(eqTo(request))(eqTo(request.measurementsContext))
        verify(processor, times(1)).getVehicleProductDetails(Seq(vehicleSaveStageResponse))
        verify(protectionMapper, times(1)).mapBookingAction(
          any[CreateRequest],
          anyLong(),
          any[ReservedIds[ProtectionAncillaryModel, EmptyProductReservedIds]],
          any[BookingActionState]
        )
        verify(protectionMapper, times(1)).mapSingleProtectionBooking(
          any[CreateRequest],
          anyLong(),
          anyLong(),
          any[ReservedIds[ProtectionAncillaryModel, EmptyProductReservedIds]],
          eqTo(Seq(defaultMockVehicleModel.vehicleBookingTrip.vehicleBookingId)),
          any(),
          eqTo(ProductTypeEnum.Car)
        )
        verify(protectionMapper, times(1)).mapBookingActionState(
          any[CreateRequest],
          anyLong(),
          any[ReservedIds[ProtectionAncillaryModel, EmptyProductReservedIds]],
          any[ProtectionModelInternal]
        )
        response.right.get.exists(_.bookingAction.actionId == actionId) shouldBe true
        response.right.get.exists(_.bookingAction.bookingId.getOrElse(0L) == bookingId) shouldBe true
        succeed
      }
    }
  }

  "getFlightProductDetails" should {
    "return correct result" in {
      val flightModelInternal = defaultFlightModel.copy(
        flights = Seq(flightBooking)
      )
      when(mockFlightBookingActionState.bookingState).thenReturn(Some(flightModelInternal))

      val products = Seq(ProtectionProductIds(expectedProductTokenKey, ProductTypeEnum.Flight, Some("some data")))
      val token    = baseProtectionToken.copy(products = products)
      processor
        .getFlightProductDetails(baseProtectionProduct.info.flightSaveStages, token)
        .head
        .flightBookingId shouldEqual 123L
    }
  }

  "getVehicleProductDetails" should {
    "return correct result" in {
      when(mockVehicleBookingActionState.vehicleBookingState).thenReturn(Some(defaultMockVehicleModel))
      val products = Seq(ProtectionProductIds(expectedProductTokenKey, ProductTypeEnum.Car, Some("some data")))
      val token    = baseProtectionToken.copy(products = products)
      val vehicleProtectionProduct =
        baseProtectionProduct.copy(info = ProtectionAncillaryModel(token, Seq.empty, Seq(vehicleSaveStageResponse)))
      processor
        .getVehicleProductDetails(vehicleProtectionProduct.info.vehicleSaveStages)
        .head
        .vehicleBookingId shouldEqual 1L
    }
  }

  "reservedProductId" should {
    "return correct result" in {
      val mockFeatureAware = mock[FeatureAware]
      val requestContextWithFeatureAware = baseRequestContext.copy(
        featureAware = Some(mockFeatureAware)
      )
      val requestWithFeatureAware = baseRequest.copy(
        requestContext = requestContextWithFeatureAware
      )

      val expected: ReservedIds[ProtectionAncillaryModel, EmptyProductReservedIds] =
        ReservedIds(
          bookingId,
          actionId,
          requestWithFeatureAware.multiProductId.map(_.multiProductId),
          requestWithFeatureAware.product,
          productReservedIds = None,
          breakdownIds = Seq(101L, 102L)
        )

      when(workflowRepository.getNextBookingSequenceNumber)
        .thenReturn(Future.successful(bookingId))

      when(workflowRepository.getNextBreakdownIdSequenceNumbers(anyInt)).thenReturn(Future.successful(Seq(101L, 102L)))
      when(workflowRepository.getNextActionIdSequenceNumber).thenReturn(Future.successful(actionId))

      processor.measuredReservedProductId(requestWithFeatureAware)(requestWithFeatureAware.measurementsContext).map {
        result =>
          result shouldBe expected
      }
    }
  }
}
