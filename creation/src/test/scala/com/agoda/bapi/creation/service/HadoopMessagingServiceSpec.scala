package com.agoda.bapi.creation.service

import com.agoda.bapi.common.config.KillSwitches
import com.agoda.bapi.common.logging.PriceDisplayLog
import com.agoda.bapi.common.message.creation.{CreateBookingRequest, CreateBookingResponse, CreatedBookingStatus, DefaultContinuePaymentRequest, DefaultGetStatusRequest, FlightBooking, GetStatusResponse, HotelBooking, HotelGuest, Itinerary, SpecialRequests, VoidBookingRequest}
import com.agoda.bapi.common.message.setupBooking._
import com.agoda.bapi.common.message.{ExperimentData, OccupancyRequest, PropertySearchCriteria}
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.creation.mocks.PropertyMock
import com.agoda.bapi.common.model.creation.{AlternativeRoomInfo, AvailabilityType}
import com.agoda.bapi.common.model.multiproduct.MultiProductInfoDBModel
import com.agoda.mpb.common.MultiProductType.MultiProductType
import com.agoda.bapi.common.model.product.{BookingFlow, ProductTypeEnum}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.util.{JacksonSerializer, ServerUtils}
import com.agoda.bapi.common.{MessageService, MockRequestContext, ToolSet}
import com.agoda.bapi.creation.mapper.ebe.SensitiveInfoMapper
import com.agoda.mpb.common.{BookingType, MultiProductType, WorkflowId}
import com.agoda.bapi.creation.model.db.MultiProductBookingInsertionModel
import com.agoda.bapi.creation.model.messages.{BapiCreateFact, BapiFlightSegmentValidateFact, BapiProductSetupFact}
import com.agoda.bapi.creation.model.multi.{MultiProductSaveStageRequest, MultiProductsRequest, Product, ValidateProductRequest}
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.bapi.creation.model.{MultiProduct, RequestWithProducts, RequestWithProductsNType}
import com.agoda.bapi.creation.service.HadoopMessagingService._
import com.agoda.bapi.creation.service.observability.LogContext
import com.agoda.bapi.creation.service.observability.setup.{PaymentLog, PropertyContentLog}
import com.agoda.bapi.creation.{CreateBookingHelper, ProductTokenMockHelper}
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpb.common.header.AgHeaderKey.AG_ENVIRONMENT_HEADER
import com.agoda.mpb.common.header.AgHttpHeader
import com.agoda.mpbe.state.common.ResponseHeader.ResponseErrorCode
import com.agoda.mpbe.state.common.{RequestHeader, ResponseHeader}
import com.agoda.mpbe.state.itinerary.{ItineraryModel, ItineraryState}
import com.agoda.mpbe.state.product.ProductModel
import com.agoda.mpbe.state.product.vehicle._
import com.agoda.mpbe.state.replication.{SetStateRequest, SetStateResponse}
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import mocks.DBBookingModelHelper
import org.joda.time.DateTime
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito._
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{AppendedClues, BeforeAndAfter}
import org.scalatestplus.mockito.MockitoSugar
import request.{PropertyContext, PropertyRequest}
import scalapb.json4s.JsonFormat

import scala.concurrent.Future
import scala.language.postfixOps

class HadoopMessagingServiceSpec
    extends AsyncWordSpec
    with MockitoSugar
    with BeforeAndAfter
    with CreateBookingHelper
    with Matchers
    with ToolSet
    with DefaultGetStatusRequest
    with DefaultContinuePaymentRequest
    with ProductTokenMockHelper
    with DBBookingModelHelper
    with PropertyMock
    with AppendedClues {

  val messageServiceMock     = mock[MessageService]
  val killSwitches           = mock[KillSwitches]
  val start                  = DateTime.now().getMillis
  val end                    = start + 20
  val hadoopMessagingService = new HadoopMessagingService(messageServiceMock, killSwitches)
  val request                = SensitiveInfoMapper.createJson("request")
  val response               = SensitiveInfoMapper.createJson("response")

  before {
    reset(messageServiceMock)
  }

  private val bookingCreationStage = "MockBookingCreationStage"
  private val roomInfo = RoomInfo(
    roomUid = "mockRoomUid",
    bapiBooking = defaultPropertyBookingToken,
    specialRequest = Some("mock special request"),
    roomTypeName = Some("mock roomTypeName"),
    greetingMessage = Some("mock greetingMessage"),
    unmodifiedSpecialRequest = Some(
      SpecialRequests(
        selectedRequests = Seq.empty,
        additionalNotes = Some("additionalNotes"),
        arrivalTime = Some("14:00"),
        bookerAnswer = Some("bookingAnswer")
      )
    )
  )
  private val productRoom = Product[RoomInfo](
    bookingType = BookingType.CreditCard,
    info = roomInfo
  )
  private val multiProduct = MultiProduct(
    properties = Seq(roomInfo),
    flights = defaultFlightBookingToken,
    vehicles = Seq.empty,
    protections = Seq.empty,
    commonPayment = None
  )

  "sendBapiCreateFactLogMessage" should {
    "send message correctly for MultiProductsRequest with 1 property" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateFact])
      val request = MultiProductsRequest(
        request = baseReq,
        requestContext = MockRequestContext.create(),
        properties = Seq(productRoom),
        flights = Seq.empty,
        vehicles = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        cegFastTracks = Seq.empty,
        addOns = Seq.empty,
        bookingFlow = BookingFlow.SingleProperty,
        commonPayment = None,
        isBookingFromCart = None
      )
      val response = CreateBookingResponse.technical(ErrorCode.UnexpectedInternalError, Some(new Exception()))

      when(messageServiceMock.sendMessage(any[BapiCreateFact])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBapiCreateFactLogMessage(
          request,
          response,
          bookingCreationStage
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateFact].bookingCreationStage == bookingCreationStage)
          assert(captor.getValue.asInstanceOf[BapiCreateFact].availabilityType == Some(AvailabilityType.Guarantee.id))
          assert(captor.getValue.asInstanceOf[BapiCreateFact].whiteLabelId == Some(WhiteLabel.Agoda.id))
        }
    }

    "send message correctly for MultiProductsRequest with 1 property and whiteLabel Rurubu" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateFact])
      val request = MultiProductsRequest(
        request = baseReq,
        requestContext =
          MockRequestContext.create(whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Rurubu, new FeaturesConfiguration)),
        properties = Seq(productRoom),
        flights = Seq.empty,
        vehicles = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        cegFastTracks = Seq.empty,
        addOns = Seq.empty,
        bookingFlow = BookingFlow.SingleProperty,
        commonPayment = None,
        isBookingFromCart = None
      )
      val response = CreateBookingResponse.technical(ErrorCode.UnexpectedInternalError, Some(new Exception()))

      when(messageServiceMock.sendMessage(any[BapiCreateFact])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBapiCreateFactLogMessage(
          request,
          response,
          bookingCreationStage
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateFact].whiteLabelId == Some(WhiteLabel.Rurubu.id))
        }
    }

    "send message correctly for not MultiProductsRequest with 1 property (null handling)" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateFact])
      val bapiBooking = productRoom.info.bapiBooking.copy(alternativeRoomInfo =
        Some(
          AlternativeRoomInfo(
            Some(1),
            null,
            false,
            true,
            false
          )
        )
      )
      val properties = Seq(productRoom.copy(info = productRoom.info.copy(bapiBooking = bapiBooking)))
      val request = MultiProductsRequest(
        request = baseReq,
        requestContext = MockRequestContext.create(),
        properties = properties,
        flights = Seq.empty,
        vehicles = Seq.empty,
        protections = Seq.empty,
        activities = Seq.empty,
        cegFastTracks = Seq.empty,
        addOns = Seq.empty,
        bookingFlow = BookingFlow.SingleProperty,
        commonPayment = None,
        isBookingFromCart = None
      )
      val response = CreateBookingResponse.technical(ErrorCode.UnexpectedInternalError, Some(new Exception()))

      when(messageServiceMock.sendMessage(any[BapiCreateFact])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBapiCreateFactLogMessage(
          request,
          response,
          bookingCreationStage
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateFact].bookingCreationStage == bookingCreationStage)
        }
    }
    "send message correctly for RequestWithProductsNType with 1 property and 1 flight" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateFact])
      val request = RequestWithProductsNType(
        request = baseReq,
        requestContext = MockRequestContext.create(),
        rooms = defaultRoom(),
        flights = defaultFlightBookingToken,
        protections = Seq.empty,
        bookingType = BookingType.CreditCard,
        bookingFlow = BookingFlow.Package,
        products = multiProduct
      )
      val response = CreateBookingResponse.technical(ErrorCode.UnexpectedInternalError, Some(new Exception()))

      when(messageServiceMock.sendMessage(any[BapiCreateFact])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBapiCreateFactLogMessage(
          request,
          response,
          bookingCreationStage
        )
        .map { _ =>
          verify(messageServiceMock, times(2)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateFact].bookingCreationStage == bookingCreationStage)
        }
    }
    "send message correctly for RequestWithProducts with 1 property and 1 flight" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateFact])
      val request: RequestWithProducts = RequestWithProducts(
        request = baseReq,
        requestContext = MockRequestContext.create(),
        rooms = defaultRoom(),
        flights = defaultFlightBookingToken,
        car = None,
        protections = Seq.empty,
        bookingFlow = BookingFlow.Package,
        products = multiProduct,
        requestV2 = None
      )
      val response = CreateBookingResponse.technical(ErrorCode.UnexpectedInternalError, Some(new Exception()))

      when(messageServiceMock.sendMessage(any[BapiCreateFact])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBapiCreateFactLogMessage(
          request,
          response,
          bookingCreationStage
        )
        .map { _ =>
          verify(messageServiceMock, times(2)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateFact].bookingCreationStage == bookingCreationStage)
        }
    }
    "send message correctly for MultiProductSaveStageRequest with 1 property" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateFact])
      val saveBookingModel = MultiProductBookingInsertionModel(
        multiProductsInfo = Seq(
          MultiProductInfoDBModel(
            multiProductId = 1L,
            multiProductType = MultiProductType.SingleProperty
          )
        ),
        workflowActions = Seq(
          mockBookingWorkflowAction.copy(bookingId = None),
          mockBookingWorkflowAction.copy(
            workflowId = WorkflowId.Property.id,
            bookingId = Some(mockNewPropertyBookingObjectForEbeLite.booking.bookingId)
          )
        ),
        itineraryModel = mockItineraryInternalModel,
        flightBookingActionStates = Seq.empty,
        propertyModels = Seq(mockNewPropertyBookingObjectForEbeLite),
        vehicleBookingActionStates = Seq.empty,
        protectionBookingActionStates = Seq.empty,
        multiProductBookingGroups = Seq.empty,
        activityBookingActionState = Seq.empty,
        cegFastTrackBookingActionState = Seq.empty,
        addOnsBookingActionState = Seq.empty
      )

      val request = MultiProductSaveStageRequest(
        request = baseReq,
        requestContext = MockRequestContext.create(),
        saveBookingModel = saveBookingModel,
        statusToken = StatusToken(
          1L,
          111L,
          Set(
            ProductTypeEnum.Flight.toString,
            ProductTypeEnum.Property.toString,
            ProductTypeEnum.Protection.toString,
            ProductTypeEnum.Car.toString
          ),
          ServerUtils.serverDc()
        )
      )
      val response = CreateBookingResponse.technical(ErrorCode.UnexpectedInternalError, Some(new Exception()))

      when(messageServiceMock.sendMessage(any[BapiCreateFact])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBapiCreateFactLogMessage(
          request,
          response,
          bookingCreationStage
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateFact].bookingCreationStage == bookingCreationStage)
          assert(captor.getValue.asInstanceOf[BapiCreateFact].availabilityType == Some(AvailabilityType.Guarantee.id))
          assert(
            captor.getValue.asInstanceOf[BapiCreateFact].bookingId == Some(
              mockNewPropertyBookingObjectForEbeLite.booking.bookingId
            )
          )
        }
    }
    "send message correctly for ValidateProductRequest[RoomInfo] with 1 property" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateFact])

      val request = ValidateProductRequest[RoomInfo](
        request = baseReq,
        requestContext = MockRequestContext.create(),
        product = productRoom
      )
      val response = CreateBookingResponse.technical(ErrorCode.UnexpectedInternalError, Some(new Exception()))

      when(messageServiceMock.sendMessage(any[BapiCreateFact])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBapiCreateFactLogMessage(
          request,
          response,
          bookingCreationStage
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateFact].bookingCreationStage == bookingCreationStage)
          assert(captor.getValue.asInstanceOf[BapiCreateFact].availabilityType == Some(AvailabilityType.Guarantee.id))
        }
    }
  }

  "sendBapiCreateFactLogMessageForHandler" should {
    "send message correctly for CreateBookingRequest with 1 property" in {
      val captor   = ArgumentCaptor.forClass(classOf[BapiCreateFact])
      val response = CreateBookingResponse.technical(ErrorCode.UnexpectedInternalError, Some(new Exception()))

      when(messageServiceMock.sendMessage(any[BapiCreateFact])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBapiCreateFactLogMessageForHandler(
          baseReq,
          response,
          com.agoda.mpb.common.MultiProductType.SingleProperty,
          bookingCreationStage,
          requestContext = MockRequestContext.create()
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateFact].bookingCreationStage == bookingCreationStage)
          assert(captor.getValue.asInstanceOf[BapiCreateFact].whiteLabelId == Some(WhiteLabel.Agoda.id))
        }
    }
    "send message correctly for CreateBookingRequest with 1 property and WhiteLabelId=4" in {
      val captor   = ArgumentCaptor.forClass(classOf[BapiCreateFact])
      val response = CreateBookingResponse.technical(ErrorCode.UnexpectedInternalError, Some(new Exception()))

      when(messageServiceMock.sendMessage(any[BapiCreateFact])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBapiCreateFactLogMessageForHandler(
          baseReq,
          response,
          com.agoda.mpb.common.MultiProductType.SingleProperty,
          bookingCreationStage,
          requestContext =
            MockRequestContext.create(whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Rurubu, new FeaturesConfiguration))
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateFact].whiteLabelId == Some(WhiteLabel.Rurubu.id))
        }
    }
  }

  "sendCreateBookingMessage" should {
    val correlationId: String = "mock_correlation_id"
    val clientId: Int         = 23

    "send message correctly" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])

      when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBookingCreateLogMessage(
          HadoopMessageProcessName.CreateBookingRequest,
          correlationId,
          clientId,
          baseReq,
          Some(baseReq.prebookingId)
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(
            captor.getValue == BapiCreateBookingLogMessage(
              correlationId,
              baseReq.prebookingId,
              clientId,
              "/v1/booking/create",
              "CreateBookingRequest",
              SensitiveInfoMapper.createJson(baseReq)
            )
          )
        }
    }

    "be able to send `CreateBookingRequest` message" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])

      when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBookingCreateLogMessage(
          HadoopMessageProcessName.CreateBookingRequest,
          correlationId,
          clientId,
          baseReq,
          Some(baseReq.prebookingId)
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateBookingLogMessage].processName == "CreateBookingRequest")
        }
    }

    "be able to send `CreateBookingResponse` message" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])

      when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBookingCreateLogMessage(
          HadoopMessageProcessName.CreateBookingResponse,
          correlationId,
          clientId,
          baseReq,
          Some(baseReq.prebookingId)
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateBookingLogMessage].processName == "CreateBookingResponse")
        }
    }
    "be able to send `PaymentApiRequest` message" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])

      when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBookingCreateLogMessage(
          HadoopMessageProcessName.PaymentApiRequest,
          correlationId,
          clientId,
          baseReq,
          Some(baseReq.prebookingId)
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateBookingLogMessage].processName == "PaymentApiRequest")
        }
    }
    "be able to send `PaymentApiResponse` message" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])

      when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBookingCreateLogMessage(
          HadoopMessageProcessName.PaymentApiResponse,
          correlationId,
          clientId,
          baseReq,
          Some(baseReq.prebookingId)
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateBookingLogMessage].processName == "PaymentApiResponse")
        }
    }
    "be able to send `PaymentApiRequest3DS` message" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])

      when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBookingCreateLogMessage(
          HadoopMessageProcessName.PaymentApiRequest3DS,
          correlationId,
          clientId,
          baseReq,
          Some(baseReq.prebookingId)
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateBookingLogMessage].processName == "3DSPaymentApiRequest")
        }
    }
    "be able to send `PaymentApiResponse3DS` message" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])

      when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBookingCreateLogMessage(
          HadoopMessageProcessName.PaymentApiResponse3DS,
          correlationId,
          clientId,
          baseReq,
          Some(baseReq.prebookingId)
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateBookingLogMessage].processName == "3DSPaymentApiResponse")
        }
    }
    "be able to send `CreateXmlBookingItem` message" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])

      when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBookingCreateLogMessage(
          HadoopMessageProcessName.CreateXmlBookingItem,
          correlationId,
          clientId,
          baseReq,
          Some(baseReq.prebookingId)
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateBookingLogMessage].processName == "CreateXmlBookingItem")
        }
    }
    "be able to send `CreateBookingRequestRaw` message" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])

      when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBookingCreateLogMessage(
          HadoopMessageProcessName.CreateBookingRequestRaw,
          correlationId,
          clientId,
          baseReq,
          Some(baseReq.prebookingId)
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateBookingLogMessage].processName == "CreateBookingRequestRaw")
        }
    }
    "be able to send `CreateBookingQueueResponse` message" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])

      when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBookingCreateLogMessage(
          HadoopMessageProcessName.CreateBookingQueueResponse,
          correlationId,
          clientId,
          baseReq,
          Some(baseReq.prebookingId)
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateBookingLogMessage].processName == "CreateBookingQueueResponse")
        }
    }

    "be able to send payment api V2 `PreAuthorizeRequest` message" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])

      when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBookingCreateLogMessage(
          HadoopMessageProcessName.PaymentApiV2Request,
          correlationId,
          clientId,
          baseReq,
          Some(baseReq.prebookingId)
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateBookingLogMessage].processName == "PaymentApiV2Request")
        }
    }

    "be able to send payment api V2 `PreAuthorizeResponse` message" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])

      when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBookingCreateLogMessage(
          HadoopMessageProcessName.PaymentApiV2Response,
          correlationId,
          clientId,
          baseReq,
          Some(baseReq.prebookingId)
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiCreateBookingLogMessage].processName == "PaymentApiV2Response")
        }
    }

    "be able to send create v2 message" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])

      when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBookingCreateV2LogMessage(
          HadoopMessageProcessName.CreateItineraryRequest,
          correlationId,
          clientId,
          baseReq,
          Some(baseReq.prebookingId)
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(
            captor.getValue == BapiCreateBookingLogMessage(
              correlationId,
              baseReq.prebookingId,
              clientId,
              "/v1/itinerary/create",
              "CreateItineraryRequest",
              SensitiveInfoMapper.createJson(baseReq)
            )
          )
        }
    }

    "be able to send create v2 message with bookingSessionId" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])

      when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBookingCreateV2LogMessage(
          HadoopMessageProcessName.CreateItineraryRequest,
          correlationId,
          clientId,
          baseReq,
          Some(baseReq.prebookingId),
          Some("bookingSessionId")
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(
            captor.getValue == BapiCreateBookingLogMessage(
              correlationId,
              baseReq.prebookingId,
              clientId,
              "/v1/itinerary/create",
              "CreateItineraryRequest",
              SensitiveInfoMapper.createJson(baseReq),
              Some("bookingSessionId")
            )
          )
        }
    }

    "be able to send create v2 message with whitelabelId and memberId" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])

      when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .sendBookingCreateV2LogMessage(
          HadoopMessageProcessName.CreateItineraryRequest,
          correlationId,
          clientId,
          baseReq,
          Some(baseReq.prebookingId),
          Some("bookingSessionId"),
          Some("Agoda"),
          Some(123456)
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(
            captor.getValue == BapiCreateBookingLogMessage(
              correlationId,
              baseReq.prebookingId,
              clientId,
              "/v1/itinerary/create",
              "CreateItineraryRequest",
              SensitiveInfoMapper.createJson(baseReq),
              Some("bookingSessionId"),
              Some("Agoda"),
              Some(123456)
            )
          )
        }
    }
  }

  "sendBookingGetStatusLogMessage" should {

    val expectedObj =
      "{\"statusToken\":\"{\\\"itineraryId\\\":60254671,\\\"actionId\\\":873090,\\\"productType\\\":[\\\"Property\\\",\\\"Flight\\\"],\\\"dc\\\":\\\"hk\\\"}\",\"userContext\":{\"languageId\":0,\"requestOrigin\":\"\",\"currency\":\"USD\",\"nationalityId\":0,\"experimentData\":{\"userId\":\"cb2416c1-432c-4809-9414-7a08496a9076\",\"deviceTypeId\":\"1\",\"memberId\":\"44651928\",\"trafficGroup\":\"1\",\"cId\":\"1844104\",\"aId\":\"130243\"},\"isLoggedInUser\":false},\"bookingContext\":{\"sessionId\":\"\",\"userAgent\":{\"origin\":\"\",\"osName\":\"\",\"osVersion\":\"\",\"browserName\":\"\",\"browserLanguage\":\"\",\"browserVersion\":\"\",\"browserSubVersion\":\"\",\"browserBuildNumber\":\"\",\"deviceBrand\":\"\",\"deviceModel\":\"\",\"deviceTypeId\":0}}}"

    "work properly" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiItineraryStatusLogMessage])
      when(messageServiceMock.sendMessage(any[BapiItineraryStatusLogMessage])).thenReturn(Future.successful(()))
      val correlationId = "123"
      val clientId      = 61
      val pollingId     = "test-polling-id"
      hadoopMessagingService
        .sendBookingGetStatusLogMessage(
          processName = HadoopMessageProcessName.GetItineraryStatusRequest,
          correlationId = correlationId,
          clientId = clientId,
          request = makeGetStatusRequest(),
          prebookingId = None,
          pollingId = Some(pollingId)
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(
            captor.getValue.asInstanceOf[BapiItineraryStatusLogMessage] == BapiItineraryStatusLogMessage(
              correlationId = correlationId,
              clientId = clientId,
              endpoint = "/v1/itinerary/status",
              processName = "CreateItineraryStatusRequest",
              request = expectedObj,
              pollingId = Some(pollingId)
            )
          )
        }
    }

    "work properly when bookingSessionId is present" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiItineraryStatusLogMessage])
      when(messageServiceMock.sendMessage(any[BapiItineraryStatusLogMessage])).thenReturn(Future.successful(()))
      val correlationId = "123"
      val clientId      = 61
      hadoopMessagingService
        .sendBookingGetStatusLogMessage(
          processName = HadoopMessageProcessName.GetItineraryStatusRequest,
          correlationId = correlationId,
          clientId = clientId,
          request = makeGetStatusRequest(),
          prebookingId = None,
          bookingSessionId = Some("bookingSessionId")
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(
            captor.getValue.asInstanceOf[BapiItineraryStatusLogMessage] == BapiItineraryStatusLogMessage(
              correlationId = correlationId,
              clientId = clientId,
              endpoint = "/v1/itinerary/status",
              processName = "CreateItineraryStatusRequest",
              request = expectedObj,
              bookingSessionId = Some("bookingSessionId")
            )
          )
        }
    }
  }

  "sendBookingGetStatusResponseLogMessage" should {

    "send via hadoop messaging with proper endpoint and process name" in {
      val statusResponse = GetStatusResponse(
        success = true,
        itinerary = Option(
          Itinerary(
            itineraryId = 1,
            bookings = Seq(
              HotelBooking(
                bookingStatus = CreatedBookingStatus.BookingConfirmed
              )
            ),
            flights = Seq(
              FlightBooking(
                bookingStatus = CreatedBookingStatus.BookingConfirmed
              )
            )
          )
        )
      )
      val expectedJson = JacksonSerializer.serialize(statusResponse).getOrElse("{}")
      val captor       = ArgumentCaptor.forClass(classOf[BapiItineraryStatusLogMessage])
      when(messageServiceMock.sendMessage(any[BapiItineraryStatusLogMessage])).thenReturn(Future.successful(()))
      val correlationId = "123"
      val clientId      = 61
      val pollingId     = "test-polling-id"
      hadoopMessagingService
        .sendBookingGetStatusResponseLogMessage(
          correlationId = correlationId,
          clientId = clientId,
          request = statusResponse,
          pollingId = Some(pollingId)
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(
            captor.getValue.asInstanceOf[BapiItineraryStatusLogMessage] == BapiItineraryStatusLogMessage(
              correlationId = correlationId,
              clientId = clientId,
              endpoint = "/v1/itinerary/status",
              processName = "CreateItineraryStatusResponse",
              request = expectedJson,
              pollingId = Some(pollingId)
            )
          )
        }
    }

    "serialize with implicit context correctly" in {
      val statusResponseWithContext = GetStatusResponse(
        success = true,
        itinerary = Option(
          Itinerary(
            itineraryId = 1,
            bookings = Seq(
              HotelBooking(
                bookingStatus = CreatedBookingStatus.BookingConfirmed
              )
            ),
            flights = Seq(
              FlightBooking(
                bookingStatus = CreatedBookingStatus.BookingConfirmed
              )
            )
          )
        )
      )
      val expectedJson = JacksonSerializer.serialize(statusResponseWithContext).getOrElse("{}")
      val captor       = ArgumentCaptor.forClass(classOf[BapiItineraryStatusLogMessage])
      when(messageServiceMock.sendMessage(any[BapiItineraryStatusLogMessage])).thenReturn(Future.successful(()))
      val correlationId = "123"
      val clientId      = 61
      hadoopMessagingService
        .sendBookingGetStatusResponseLogMessage(
          correlationId = correlationId,
          clientId = clientId,
          request = statusResponseWithContext
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(
            captor.getValue.asInstanceOf[BapiItineraryStatusLogMessage] == BapiItineraryStatusLogMessage(
              correlationId = correlationId,
              clientId = clientId,
              endpoint = "/v1/itinerary/status",
              processName = "CreateItineraryStatusResponse",
              request = expectedJson
            )
          )
        }
    }

  }

  "sendBookingContinueLogMessage" should {
    "ConfirmPaymentRequest as MsgObject" should {
      def expectedObj(expectedCvv: String, expectedPostBackField: String) =
        s"""{"statusToken":"{\\"itineraryId\\":1234,\\"actionId\\":903169,\\"dc\\":\\"dev\\",\\"productType\\":[\\"Flight\\"]}","postBackFields":{"additionalProp1":"$expectedPostBackField","additionalProp2":"$expectedPostBackField","additionalProp3":"$expectedPostBackField"},"internalToken":"dummyInternalToken","userContext":{"languageId":1,"requestOrigin":"","currency":"USD","nationalityId":0,"experimentData":{"userId":"userId","deviceTypeId":"device"},"isLoggedInUser":false},"bookingContext":{"sessionId":"","userAgent":{"origin":"","osName":"","osVersion":"","browserName":"","browserLanguage":"","browserVersion":"","browserSubVersion":"","browserBuildNumber":"","deviceBrand":"","deviceModel":"","deviceTypeId":0}},"securityCode":"$expectedCvv"}"""

      val expectedObjWithoutCvv =
        """{"statusToken":"{\"itineraryId\":1234,\"actionId\":903169,\"dc\":\"dev\",\"productType\":[\"Flight\"]}","postBackFields":{"additionalProp1":"******","additionalProp2":"******","additionalProp3":"******"},"internalToken":"dummyInternalToken","userContext":{"languageId":1,"requestOrigin":"","currency":"USD","nationalityId":0,"experimentData":{"userId":"userId","deviceTypeId":"device"},"isLoggedInUser":false},"bookingContext":{"sessionId":"","userAgent":{"origin":"","osName":"","osVersion":"","browserName":"","browserLanguage":"","browserVersion":"","browserSubVersion":"","browserBuildNumber":"","deviceBrand":"","deviceModel":"","deviceTypeId":0}}}"""

      def setup(securityCode: Option[String]) = {
        val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])
        when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))
        val correlationId = "123"
        val clientId      = 61
        val result = hadoopMessagingService
          .sendBookingContinueLogMessage(
            processName = HadoopMessageProcessName.ContinueItineraryRequest,
            correlationId = correlationId,
            clientId = clientId,
            msgObject = getContinuePaymentRequest(securityCode),
            prebookingId = None
          )
        (result, correlationId, clientId, captor)
      }

      "work properly when Cvv is present" in {
        val (result, correlationId, clientId, captor) = setup(Some("444"))

        result.map { _ =>
          val expectedCvv           = "***"
          val expectedPostBackField = "******"
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          println(captor.getValue.asInstanceOf[BapiCreateBookingLogMessage].obj)
          assert(
            captor.getValue.asInstanceOf[BapiCreateBookingLogMessage] == BapiCreateBookingLogMessage(
              correlationId = correlationId,
              clientId = clientId,
              prebookingId = 0,
              endpoint = "/v1/itinerary/continue",
              processName = "ContinueItineraryRequest",
              obj = expectedObj(expectedCvv, expectedPostBackField)
            )
          )
        }
      }

      "work properly when Cvv is NOT present" in {
        val (result, correlationId, clientId, captor) = setup(None)

        result.map { _ =>
          val expectedCvv = ""
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(
            captor.getValue.asInstanceOf[BapiCreateBookingLogMessage] == BapiCreateBookingLogMessage(
              correlationId = correlationId,
              clientId = clientId,
              prebookingId = 0,
              endpoint = "/v1/itinerary/continue",
              processName = "ContinueItineraryRequest",
              obj = expectedObjWithoutCvv
            )
          )
        }
      }

      "work properly when Cvv is blank" in {
        val (result, correlationId, clientId, captor) = setup(Some(""))

        result.map { _ =>
          val expectedCvv = ""
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(
            captor.getValue.asInstanceOf[BapiCreateBookingLogMessage] == BapiCreateBookingLogMessage(
              correlationId = correlationId,
              clientId = clientId,
              prebookingId = 0,
              endpoint = "/v1/itinerary/continue",
              processName = "ContinueItineraryRequest",
              obj = expectedObj(expectedCvv, "******")
            )
          )
        }
      }
    }

    "BapiQueueLogMessage as MsgObject" should {
      def expectedObj =
        s"""{"msgType":"MESSAGE_TYPE","exchange":"EXCHANGE_NAME","obj":"message","success":false,"errorMessage":"errorMessage"}"""

      def setup = {
        val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingLogMessage])
        when(messageServiceMock.sendMessage(any[BapiCreateBookingLogMessage])).thenReturn(Future.successful(()))
        val correlationId = "123"
        val clientId      = 61
        val result = hadoopMessagingService
          .sendBookingContinueLogMessage(
            processName = HadoopMessageProcessName.ContinueItineraryRequest,
            correlationId = correlationId,
            clientId = clientId,
            msgObject = BapiQueueLogMessage("MESSAGE_TYPE", "EXCHANGE_NAME", "message", false, Some("errorMessage")),
            prebookingId = None
          )
        (result, correlationId, clientId, captor)
      }

      "work properly for BapiQueueLogMessage" in {
        val (result, correlationId, clientId, captor) = setup
        result.map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(
            captor.getValue.asInstanceOf[BapiCreateBookingLogMessage] == BapiCreateBookingLogMessage(
              correlationId = correlationId,
              clientId = clientId,
              prebookingId = 0,
              endpoint = "/v1/itinerary/continue",
              processName = "ContinueItineraryRequest",
              obj = expectedObj
            )
          )
        }
      }
    }
  }

  "sendAmendmentLogMessage" should {
    "be able to send BapiAmendmentLogMessage correctly" in {
      val captor = ArgumentCaptor.forClass(classOf[BapiAmendmentLogMessage])
      when(messageServiceMock.sendMessage(any[BapiAmendmentLogMessage])).thenReturn(Future.successful(()))
      hadoopMessagingService
        .sendAmendmentLogMessage(
          processName = "sendAmendmentLogMessage",
          correlationId = "",
          clientId = 0,
          obj = ""
        )
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiAmendmentLogMessage].processName == "sendAmendmentLogMessage")
        }
    }
  }

  "BapiProductSetupFact" should {
    // roomidentifier belong to 28033
    "generateAndSendBapiSetupFactMessage and use supplierid from roomidentifier if supplierid is missing from propertycontent" in {
      val correlationId = "correlation:123"
      val captor        = ArgumentCaptor.forClass(classOf[BapiProductSetupFact])
      val logContext    = LogContext()
      val paymentLog    = PaymentLog(Some(2.1), Some("JPY"), Some(3.1), Some(mock[PriceDisplayLog]), Some(1))
      val productLog1 = PropertyContentLog(
        searchId = Some("1"),
        pricingSearchId = Some("pr1"),
        masterRoomTypeId = Some(12L),
        supplierId = None,
        paymentModel = Some(1),
        isNoCC = Some(false),
        breakfast = Some(true),
        isNha = Some(false),
        alternativeRooms = "[{...}]",
        isCrossSellAvailable = Some(true),
        capacityNoOfAdults = Some(10),
        capacityNoOfChildren = Some(20),
        capacityNoOfExtraBed = Some(30),
        capacityOccupancy = Some(40),
        capacityMaxExtraBed = Some(50),
        capacityAllowedFreeChildrenAndInfants = Some(60),
        stayNoOfAdults = Some(5),
        stayNoOfChildren = Some(3),
        stayListOfChildAge = Some(List[Int](1, 2, 4)),
        stayNoOfRoom = Some(3),
        availabilityType = Some(AvailabilityType.Realtime.id)
      )

      logContext.addPaymentLog(correlationId, paymentLog)
      logContext.addProductLog(correlationId, "1", productLog1)
      val propertyRequestItem1 = mock[PropertyRequestItem]
      when(propertyRequestItem1.id).thenReturn("1")
      val propertySearchCriteria = mock[PropertySearchCriteria]
      when(propertySearchCriteria.roomIdentifier).thenReturn(
        "CokBCMbFi7ACEAIgAjAESg0zNjVEMTAwUF8xMDBQUIK2A3poU29tZSgxMDE1MDAyNCl8MzE4ODYxNjY3fDF8MzI5Mjg0Njh8MnxST3xMaXN0KCl8MzY1RDEwMFBfMTAwUHxTb21lKDF8Uk98MTAxNTAwMjR8REN8MzI5Mjg0Njh8Mjk4NDIzOTEzfCkSAggBGgYgASgEMAE"
      )
      when(propertySearchCriteria.pricingRequest).thenReturn(None)
      when(propertySearchCriteria.occupancyRequest).thenReturn(OccupancyRequest(1, 2))
      when(propertySearchCriteria.simplifiedRoomSelectionRequest).thenReturn(None)
      when(propertyRequestItem1.propertySearchCriteria).thenReturn(propertySearchCriteria)
      when(propertyRequestItem1.payment).thenReturn(None)
      val request = SetupBookingRequest(
        correlationId = Some(correlationId),
        productsRequest = ProductsRequest(propertyRequests = Seq(propertyRequestItem1))
      )

      val response = SetupBookingResponse(
        bookingResponse = Some(
          SetupBookingResponseInfo(
            products = ProductItems(
              properties = Seq(
                PropertyProductItem("1", "{}", packageRequest = None)
              )
            ),
            paymentChargeOptions = Seq(
              PaymentChargeOption(
                currencyCode = CurrencyCode.USD,
                payToSupplierAmount = 12.7,
                payToAgodaAmount = 9.54,
                totalAmount = 22.24
              )
            )
          )
        )
      )

      when(messageServiceMock.sendMessage(any[BapiProductSetupFact])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .generateAndSendBapiSetupFactMessage(
          request,
          response,
          com.agoda.mpb.common.MultiProductType.SingleProperty,
          logContext,
          "bookSession:111",
          "session:11"
        )(MockRequestContext.create())
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiProductSetupFact].supplierId == Some(28033))
          assert(
            captor.getValue.asInstanceOf[BapiProductSetupFact].availabilityType == Some(AvailabilityType.Realtime.id)
          )
          assert(
            captor.getValue
              .asInstanceOf[BapiProductSetupFact]
              .paymentCharges
              .contains(
                Seq(
                  PaymentChargeOptionSerializable(
                    currencyCode = CurrencyCode.USD,
                    payToSupplierAmount = 12.7,
                    payToAgodaAmount = 9.54,
                    totalAmount = 22.24
                  )
                )
              )
          )
        }
    }

    "generateAndSendBapiSetupFactMessage build correctly message" in {
      val correlationId = "correlation:123"
      val captor        = ArgumentCaptor.forClass(classOf[BapiProductSetupFact])
      val logContext    = LogContext()
      val paymentLog    = PaymentLog(Some(2.1), Some("JPY"), Some(3.1), Some(mock[PriceDisplayLog]), Some(1))
      val productLog1 = PropertyContentLog(
        searchId = Some("1"),
        pricingSearchId = Some("pr1"),
        masterRoomTypeId = Some(12L),
        supplierId = Some(44L),
        paymentModel = Some(1),
        isNoCC = Some(false),
        breakfast = Some(true),
        isNha = Some(false),
        alternativeRooms = "[{...}]",
        isCrossSellAvailable = Some(true),
        capacityNoOfAdults = Some(10),
        capacityNoOfChildren = Some(20),
        capacityNoOfExtraBed = Some(30),
        capacityOccupancy = Some(40),
        capacityMaxExtraBed = Some(50),
        capacityAllowedFreeChildrenAndInfants = Some(60),
        stayNoOfAdults = Some(5),
        stayNoOfChildren = Some(3),
        stayListOfChildAge = Some(List[Int](1, 2, 4)),
        stayNoOfRoom = Some(3),
        availabilityType = Some(AvailabilityType.Guarantee.id)
      )
      val productLog2 = PropertyContentLog(
        searchId = Some("2"),
        pricingSearchId = Some("pr2"),
        masterRoomTypeId = Some(14L),
        supplierId = Some(45L),
        paymentModel = Some(2),
        isNoCC = Some(false),
        breakfast = Some(true),
        isNha = Some(false),
        alternativeRooms = "[{{...}}}]",
        isCrossSellAvailable = Some(true),
        capacityNoOfAdults = Some(10),
        capacityNoOfChildren = Some(20),
        capacityNoOfExtraBed = Some(30),
        capacityOccupancy = Some(40),
        capacityMaxExtraBed = Some(50),
        capacityAllowedFreeChildrenAndInfants = Some(60),
        stayNoOfAdults = Some(7),
        stayNoOfChildren = Some(1),
        stayListOfChildAge = Some(List[Int](8)),
        stayNoOfRoom = Some(1),
        availabilityType = Some(AvailabilityType.OnRequest.id)
      )

      logContext.addPaymentLog(correlationId, paymentLog)
      logContext.addProductLog(correlationId, "1", productLog1)
      logContext.addProductLog(correlationId, "2", productLog2)
      val propertyRequestItem1 = mock[PropertyRequestItem]
      when(propertyRequestItem1.id).thenReturn("1")
      val propertySearchCriteria = mock[PropertySearchCriteria]
      when(propertySearchCriteria.roomIdentifier).thenReturn("roomIdent:1")
      when(propertySearchCriteria.pricingRequest).thenReturn(None)
      when(propertySearchCriteria.occupancyRequest).thenReturn(OccupancyRequest(1, 2))
      when(propertySearchCriteria.simplifiedRoomSelectionRequest).thenReturn(None)
      when(propertyRequestItem1.propertySearchCriteria).thenReturn(propertySearchCriteria)
      when(propertyRequestItem1.payment).thenReturn(None)
      val propertyRequestItem2 = mock[PropertyRequestItem]
      when(propertyRequestItem2.id).thenReturn("2")
      when(propertyRequestItem2.propertySearchCriteria).thenReturn(propertySearchCriteria)
      when(propertyRequestItem2.payment).thenReturn(None)

      val campaignInfo = mock[CampaignInfoRequest]
      when(campaignInfo.promotionCode).thenReturn("RequestedPromoCode")

      val request = SetupBookingRequest(
        correlationId = Some(correlationId),
        productsRequest = ProductsRequest(propertyRequests = Seq(propertyRequestItem1, propertyRequestItem2)),
        campaignInfo = Some(campaignInfo),
        redeemRequest = Some(RedeemRequest(1, Some(2)))
      )

      val promotionInfo         = mock[PromotionInfo]
      val campaignPromotionInfo = mock[CampaignPromotionInfo]
      when(campaignPromotionInfo.promotionCode).thenReturn("AppliedPromoCode")
      when(campaignPromotionInfo.inapplicableReasonString).thenReturn(Some("Not applied"))
      when(promotionInfo.appliedCampaigns).thenReturn(Some(Seq(campaignPromotionInfo)))

      val response = SetupBookingResponse(
        bookingResponse = Some(
          SetupBookingResponseInfo(
            products = ProductItems(
              properties = Seq(
                PropertyProductItem("1", "{}", packageRequest = None),
                PropertyProductItem("2", "{}", packageRequest = None)
              )
            ),
            paymentChargeOptions = Seq(PaymentChargeOption(currencyCode = CurrencyCode.USD)),
            campaignPromotionInfo = Some(promotionInfo)
          )
        )
      )

      when(messageServiceMock.sendMessage(any[BapiProductSetupFact])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .generateAndSendBapiSetupFactMessage(
          request,
          response,
          com.agoda.mpb.common.MultiProductType.SingleProperty,
          logContext,
          "bookSession:111",
          "session:11"
        )(MockRequestContext.create(agHeaders = Seq(AgHttpHeader(AG_ENVIRONMENT_HEADER.toString, "UAT"))))
        .map { _ =>
          verify(messageServiceMock, times(2)).sendMessage(captor.capture())
          val message = captor.getValue.asInstanceOf[BapiProductSetupFact]
          assert(message.correlationId.contains(correlationId))
          assert(message.bookingSessionId.contains("bookSession:111"))
          assert(
            message.paymentCharges
              .contains(
                Seq(
                  PaymentChargeOptionSerializable(
                    currencyCode = CurrencyCode.USD,
                    payToSupplierAmount = 0.0,
                    payToAgodaAmount = 0.0,
                    totalAmount = 0.0
                  )
                )
              )
          )
          assert(message.requestedPromocode.contains("RequestedPromoCode"))
          assert(message.appliedPromocodes.exists(_.contains("AppliedPromoCode")))
          assert(message.inapplicableReasons.exists(_.contains("Not applied")))
          assert(message.inapplicableReasons.exists(_.contains("Not applied")))
          assert(message.redeemAmountRequest.contains(1))
          assert(message.cashbackRedeemAmountRequest.contains(2))
          assert(message.agEnv == Some("UAT"))
        }
    }

    "generateAndSendBapiSetupFactMessage build correctly message for capacity info" in {
      val correlationId = "correlation:123"
      val captor        = ArgumentCaptor.forClass(classOf[BapiProductSetupFact])
      val logContext    = LogContext()
      val paymentLog    = PaymentLog(Some(2.1), Some("JPY"), Some(3.1), Some(mock[PriceDisplayLog]), Some(1))
      val productLog1 = PropertyContentLog(
        searchId = Some("1"),
        pricingSearchId = Some("pr1"),
        masterRoomTypeId = Some(12L),
        supplierId = Some(44L),
        paymentModel = Some(1),
        isNoCC = Some(false),
        breakfast = Some(true),
        isNha = Some(false),
        alternativeRooms = "[{...}]",
        isCrossSellAvailable = Some(true),
        capacityNoOfAdults = Some(10),
        capacityNoOfChildren = Some(20),
        capacityNoOfExtraBed = Some(30),
        capacityOccupancy = Some(40),
        capacityMaxExtraBed = Some(50),
        capacityAllowedFreeChildrenAndInfants = Some(60),
        stayNoOfAdults = Some(5),
        stayNoOfChildren = Some(3),
        stayListOfChildAge = Some(List[Int](1, 2, 4)),
        stayNoOfRoom = Some(3),
        availabilityType = Some(AvailabilityType.Guarantee.id)
      )

      logContext.addPaymentLog(correlationId, paymentLog)
      logContext.addProductLog(correlationId, "1", productLog1)
      val propertyRequestItem1 = mock[PropertyRequestItem]
      when(propertyRequestItem1.id).thenReturn("1")
      val propertySearchCriteria = mock[PropertySearchCriteria]
      when(propertySearchCriteria.roomIdentifier).thenReturn("roomIdent:1")
      when(propertySearchCriteria.pricingRequest).thenReturn(None)
      when(propertySearchCriteria.occupancyRequest).thenReturn(OccupancyRequest(1, 2))
      when(propertySearchCriteria.simplifiedRoomSelectionRequest).thenReturn(None)
      when(propertyRequestItem1.propertySearchCriteria).thenReturn(propertySearchCriteria)
      when(propertyRequestItem1.payment).thenReturn(None)
      val request = SetupBookingRequest(
        correlationId = Some(correlationId),
        productsRequest = ProductsRequest(propertyRequests = Seq(propertyRequestItem1))
      )

      val response = SetupBookingResponse(
        bookingResponse = Some(
          SetupBookingResponseInfo(
            products = ProductItems(
              properties = Seq(
                PropertyProductItem("1", "{}", packageRequest = None)
              )
            )
          )
        )
      )

      when(messageServiceMock.sendMessage(any[BapiProductSetupFact])).thenReturn(Future.successful(()))

      hadoopMessagingService
        .generateAndSendBapiSetupFactMessage(
          request,
          response,
          com.agoda.mpb.common.MultiProductType.SingleProperty,
          logContext,
          "bookSession:111",
          "session:11"
        )(MockRequestContext.create())
        .map { _ =>
          verify(messageServiceMock, times(1)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiProductSetupFact].capacityNoOfAdults == Some(10))
          assert(captor.getValue.asInstanceOf[BapiProductSetupFact].capacityNoOfChildren == Some(20))
          assert(captor.getValue.asInstanceOf[BapiProductSetupFact].capacityNoOfExtraBed == Some(30))
          assert(captor.getValue.asInstanceOf[BapiProductSetupFact].capacityOccupancy == Some(40))
          assert(captor.getValue.asInstanceOf[BapiProductSetupFact].capacityMaxExtraBed == Some(50))
          assert(captor.getValue.asInstanceOf[BapiProductSetupFact].capacityAllowedFreeChildrenAndInfants == Some(60))
          assert(captor.getValue.asInstanceOf[BapiProductSetupFact].stayNoOfAdults == Some(5))
          assert(captor.getValue.asInstanceOf[BapiProductSetupFact].stayNoOfChildren == Some(3))
          assert(captor.getValue.asInstanceOf[BapiProductSetupFact].stayListOfChildAge == Some(List[Int](1, 2, 4)))
          assert(captor.getValue.asInstanceOf[BapiProductSetupFact].stayNoOfRoom == Some(3))
          assert(
            captor.getValue.asInstanceOf[BapiProductSetupFact].availabilityType == Some(AvailabilityType.Guarantee.id)
          )
        }
    }

    "generateAndSendBapiSetupFactMessage fallback to context's site id if campaign doesn't exist" in {
      val correlationId = "correlation:123"
      val siteId        = 1837673
      val captor        = ArgumentCaptor.forClass(classOf[BapiProductSetupFact])
      val logContext    = LogContext()
      val paymentLog    = PaymentLog(Some(2.1), Some("JPY"), Some(3.1), Some(mock[PriceDisplayLog]), Some(1))
      val productLog1 = PropertyContentLog(
        searchId = Some("1"),
        pricingSearchId = Some("pr1"),
        masterRoomTypeId = Some(12L),
        supplierId = Some(44L),
        paymentModel = Some(1),
        isNoCC = Some(false),
        breakfast = Some(true),
        isNha = Some(false),
        alternativeRooms = "[{...}]",
        isCrossSellAvailable = Some(true),
        capacityNoOfAdults = Some(10),
        capacityNoOfChildren = Some(20),
        capacityNoOfExtraBed = Some(30),
        capacityOccupancy = Some(40),
        capacityMaxExtraBed = Some(50),
        capacityAllowedFreeChildrenAndInfants = Some(60),
        stayNoOfAdults = Some(5),
        stayNoOfChildren = Some(3),
        stayListOfChildAge = Some(List[Int](1, 2, 4)),
        stayNoOfRoom = Some(3),
        availabilityType = Some(AvailabilityType.Guarantee.id)
      )
      val productLog2 = PropertyContentLog(
        searchId = Some("2"),
        pricingSearchId = Some("pr2"),
        masterRoomTypeId = Some(14L),
        supplierId = Some(45L),
        paymentModel = Some(2),
        isNoCC = Some(false),
        breakfast = Some(true),
        isNha = Some(false),
        alternativeRooms = "[{{...}}}]",
        isCrossSellAvailable = Some(true),
        capacityNoOfAdults = Some(10),
        capacityNoOfChildren = Some(20),
        capacityNoOfExtraBed = Some(30),
        capacityOccupancy = Some(40),
        capacityMaxExtraBed = Some(50),
        capacityAllowedFreeChildrenAndInfants = Some(60),
        stayNoOfAdults = Some(7),
        stayNoOfChildren = Some(1),
        stayListOfChildAge = Some(List[Int](8)),
        stayNoOfRoom = Some(1),
        availabilityType = Some(AvailabilityType.Guarantee.id)
      )

      logContext.addPaymentLog(correlationId, paymentLog)
      logContext.addProductLog(correlationId, "1", productLog1)
      logContext.addProductLog(correlationId, "2", productLog2)
      val propertyRequestItem1 = mock[PropertyRequestItem]
      when(propertyRequestItem1.id).thenReturn("1")
      val propertySearchCriteria = mock[PropertySearchCriteria]
      when(propertySearchCriteria.roomIdentifier).thenReturn("roomIdent:1")
      when(propertySearchCriteria.pricingRequest).thenReturn(None)
      when(propertySearchCriteria.occupancyRequest).thenReturn(OccupancyRequest(1, 2))
      when(propertySearchCriteria.simplifiedRoomSelectionRequest).thenReturn(None)
      when(propertyRequestItem1.propertySearchCriteria).thenReturn(propertySearchCriteria)
      when(propertyRequestItem1.payment).thenReturn(None)
      val propertyRequestItem2 = mock[PropertyRequestItem]
      when(propertyRequestItem2.id).thenReturn("2")
      when(propertyRequestItem2.propertySearchCriteria).thenReturn(propertySearchCriteria)
      when(propertyRequestItem2.payment).thenReturn(None)
      val request = SetupBookingRequest(
        correlationId = Some(correlationId),
        productsRequest = ProductsRequest(propertyRequests = Seq(propertyRequestItem1, propertyRequestItem2))
      )

      val response = SetupBookingResponse(
        bookingResponse = Some(
          SetupBookingResponseInfo(
            products = ProductItems(
              properties = Seq(
                PropertyProductItem("1", "{}", packageRequest = None),
                PropertyProductItem("2", "{}", packageRequest = None)
              )
            )
          )
        )
      )

      when(messageServiceMock.sendMessage(any[BapiProductSetupFact])).thenReturn(Future.successful(()))

      val userContext = UserContext(
        languageId = 1,
        requestOrigin = "",
        currency = CurrencyCode.USD,
        nationalityId = 1,
        experimentData = Some(
          ExperimentData(
            userId = "",
            deviceTypeId = "device-type-id",
            memberId = None,
            trafficGroup = None,
            cId = Some(siteId.toString),
            aId = Some("23092"),
            serverName = None
          )
        )
      )

      hadoopMessagingService
        .generateAndSendBapiSetupFactMessage(
          request,
          response,
          com.agoda.mpb.common.MultiProductType.SingleProperty,
          logContext,
          "bookSession:111",
          "session:11"
        )(MockRequestContext.create().copy(userContext = Some(userContext)))
        .map { _ =>
          verify(messageServiceMock, times(2)).sendMessage(captor.capture())
          assert(captor.getValue.asInstanceOf[BapiProductSetupFact].correlationId == Some(correlationId))
          assert(captor.getValue.asInstanceOf[BapiProductSetupFact].siteId == Some(siteId))
        }
    }
  }

  "sendBapiCreateBookingMissingGuests" should {
    "send message correctly when more than 1 guest and missing guest name" in {
      val guestListMock = Seq(
        HotelGuest(
          guestNo = 1,
          firstname = "Primary"
        ),
        HotelGuest(
          guestNo = 2,
          firstname = "Guest of primary"
        ),
        HotelGuest(
          guestNo = 3
        )
      )
      val captor = ArgumentCaptor.forClass(classOf[BapiCreateBookingMissingGuests])
      when(messageServiceMock.sendMessage(any[BapiCreateBookingMissingGuests])).thenReturn(Future.successful(()))
      hadoopMessagingService
        .sendBapiCreateBookingMissingGuests(
          123,
          guestListMock
        )
      verify(messageServiceMock, times(1)).sendMessage(captor.capture())
      assert(
        captor.getValue.asInstanceOf[BapiCreateBookingMissingGuests] == BapiCreateBookingMissingGuests(
          bookingId = 123,
          guestCount = 3,
          realGuestNameCount = 1
        )
      )
    }

    "not send message when more than 1 guest and all real guest name" in {
      val realGuestListMock = Seq(
        HotelGuest(
          guestNo = 1,
          firstname = "Primary"
        ),
        HotelGuest(
          guestNo = 2,
          firstname = "Secondary"
        )
      )
      hadoopMessagingService
        .sendBapiCreateBookingMissingGuests(
          456,
          realGuestListMock
        )
      verifyNoInteractions(messageServiceMock)
      succeed
    }

    "not send message when only 1 guest" in {
      val oneGuestListMock = Seq(
        HotelGuest(
          guestNo = 1,
          firstname = "Primary"
        )
      )
      hadoopMessagingService
        .sendBapiCreateBookingMissingGuests(
          456,
          oneGuestListMock
        )
      verifyNoInteractions(messageServiceMock)
      succeed
    }
  }

  "sendPapiRequestResponse" should {
    "send message correctly" in {

      val correlationId    = "correlationId"
      val sessionId        = Some("sessionId")
      val bookingSessionId = "bookingSessionId"
      val clientId         = 1
      val endpoint         = "/create/booking"
      val siteId           = Some(2)
      val affiliateId      = Some("3")
      val trackingCookieId = Some("trackingCookieId")
      val processName      = "processName"
      val requestSellIn    = Some(2.2)
      val papiRequest = PropertyRequest(
        context =
          PropertyContext(searchId = "5", locale = "6", cid = 7, memberId = 8, origin = "origin9", platform = 10)
      )
      val papiResponse = None

      val captor = ArgumentCaptor.forClass(classOf[BapiPapiMessage])
      when(messageServiceMock.sendMessage(any[BapiPapiMessage])).thenReturn(Future.successful(()))
      hadoopMessagingService
        .sendPapiRequestResponse(
          correlationId = correlationId,
          sessionId = sessionId,
          bookingSessionId = bookingSessionId,
          clientId = clientId,
          endpoint = endpoint,
          siteId = siteId,
          affiliateId = affiliateId,
          trackingCookieId = trackingCookieId,
          processName = processName,
          requestSellIn = requestSellIn,
          papiRequest = papiRequest,
          papiResponse = papiResponse
        )

      verify(messageServiceMock, times(1)).sendMessage(captor.capture())

      val hadoopMessage = captor.getValue.asInstanceOf[BapiPapiMessage]
      assert(hadoopMessage.correlationId == correlationId)
      assert(hadoopMessage.sessionId == sessionId)
      assert(hadoopMessage.bookingSessionId == bookingSessionId)
      assert(hadoopMessage.clientId == clientId)
      assert(hadoopMessage.endpoint == endpoint)
      assert(hadoopMessage.siteId == siteId)
      assert(hadoopMessage.affiliateId.contains(3))
      assert(hadoopMessage.trackingCookieId == trackingCookieId)
      assert(hadoopMessage.processName == processName)
      assert(hadoopMessage.requestSellIn == requestSellIn)
      assert(hadoopMessage.papiRequest.contains("origin9"))
      assert(hadoopMessage.papiResponse.isEmpty)
    }
  }

  "sendSetStateV2RequestResponse" should {
    val defaultVehicleBookingId = 9273421L
    val defaultVehicleDate      = DateTime.parse("2020-05-20T12:00:00")
    val defaultItineraryId      = 12345678L

    val defaultVehicleProductModel = VehicleProductModel(
      vehicleBooking = VehicleModelBooking(
        vehicleBookingId = defaultVehicleBookingId,
        itineraryId = defaultItineraryId,
        multiProductId = Some(MultiProductType.SingleVehicle.id),
        bookingDate = DateTime.parse("2020-05-20T10:00:00"),
        paymentModel = 1,
        supplierBookingId = "bid",
        recModifiedWhen = Some(defaultVehicleDate)
      ),
      vehicleBookingLocation = VehicleModelBookingPickUpDropOffLocation(
        pickUp = VehicleModelBookingLocation(
          vehicleBookingId = defaultVehicleBookingId,
          recModifiedWhen = Some(defaultVehicleDate)
        ),
        dropOff = VehicleModelBookingLocation(
          vehicleBookingId = defaultVehicleBookingId,
          recModifiedWhen = Some(defaultVehicleDate)
        )
      ),
      vehicleBookingSummary = VehicleModelBookingSummary(
        vehicleBookingId = defaultVehicleBookingId,
        recModifiedWhen = Some(defaultVehicleDate)
      ),
      vehicleBookingTrip = VehicleModelBookingTrip(
        vehicleBookingId = defaultVehicleBookingId,
        pickupDatetime = DateTime.parse("2020-07-20T12:00:00"),
        dropOffDatetime = DateTime.parse("2020-07-30T12:00:00"),
        recModifiedWhen = Some(defaultVehicleDate)
      )
    )

    val defaultItinerary = com.agoda.mpbe.state.itinerary.Itinerary(
      itineraryId = defaultItineraryId,
      memberId = 111222333,
      recCreatedWhen = DateTime.parse("2020-07-30T12:00:00")
    )

    val productModel = ProductModel(vehicles = Seq(defaultVehicleProductModel))
    val requestItineraryState = ItineraryState(
      itinerary = ItineraryModel.defaultInstance.withItinerary(defaultItinerary),
      product = productModel
    )

    val requestHeader =
      Some(
        RequestHeader(
          apiKey = "MockApiKey",
          clientId = 17
        )
      )
    val inputSetStateV2Request =
      SetStateRequest(
        state = Some(requestItineraryState),
        replicationStrategy = SetStateRequest.ReplicationStrategy.MultiDC,
        requestHeader = requestHeader
      )

    val responseItineraryState = ItineraryState(
      itinerary = ItineraryModel.defaultInstance.withItinerary(
        defaultItinerary.withRecModifiedWhen(DateTime.parse("2020-07-30T18:00:00"))
      ),
      product = productModel
    )
    val processName = HadoopMessageProcessName.SetStateV2RequestResponse
    "send message correctly with setStateResponse value" in {
      val inputSetStateV2Response =
        SetStateResponse(
          state = Some(responseItineraryState),
          responseHeader = Some(
            ResponseHeader(
              success = true,
              errorCode = ResponseErrorCode.InternalError,
              errorMessage = Some("MockErrorMSG")
            )
          )
        )
      val captor = ArgumentCaptor.forClass(classOf[SetStateV2RequestResponseMessage])
      when(messageServiceMock.sendMessage(any[SetStateV2RequestResponseMessage])).thenReturn(Future.successful(()))
      hadoopMessagingService
        .sendSetStateV2RequestResponse(
          inputSetStateV2Request,
          inputSetStateV2Response,
          processName.toString
        )

      verify(messageServiceMock, times(1)).sendMessage(captor.capture())

      val hadoopMessage  = captor.getValue.asInstanceOf[SetStateV2RequestResponseMessage]
      val actualRequest  = JsonFormat.fromJsonString[SetStateRequest](hadoopMessage.setStateV2Request)
      val actualResponse = JsonFormat.fromJsonString[SetStateResponse](hadoopMessage.setStateV2Response)

      actualRequest.replicationStrategy shouldBe inputSetStateV2Request.replicationStrategy withClue "test for enum conversion of proto to JsonString"
      actualRequest.requestHeader.map(_.apiKey) shouldBe Some("*") withClue "test for masked apiKey"
      actualRequest.state.map(_.product) shouldBe inputSetStateV2Request.state.map(_.product)
      actualRequest.state.map(_.itinerary) shouldBe inputSetStateV2Request.state.map(_.itinerary)

      actualResponse.responseHeader shouldBe inputSetStateV2Response.responseHeader
      actualResponse.state shouldBe inputSetStateV2Response.state
      hadoopMessage.processName shouldBe processName.toString
    }
  }

  "sendBapiFlightSegmentValidationLogMessage" should {
    import com.agoda.mpb.common.serialization.Serialization

    val request: RequestWithProducts = RequestWithProducts(
      request = baseReq,
      requestContext = MockRequestContext.create(),
      rooms = defaultRoom(),
      flights = defaultFlightBookingToken,
      car = None,
      protections = Seq.empty,
      bookingFlow = BookingFlow.Package,
      products = multiProduct,
      requestV2 = None
    )

    "send message correctly with Validation disable" in {
      val newUserContext = Some(
        UserContext(
          languageId = 22,
          requestOrigin = "string",
          currency = "string",
          nationalityId = 0,
          experimentData = None
        )
      )
      val newBaseReq: CreateBookingRequest = baseReq.copy(
        userContext = newUserContext
      )
      val mockFeatureAware: FeatureAware = mock[FeatureAware]
      val newRequest: RequestWithProducts = request.copy(
        request = newBaseReq,
        requestContext = MockRequestContext.create(
          userContext = newUserContext,
          featureAware = Some(mockFeatureAware)
        )
      )
      val segments = defaultFlightBookingToken.flatMap(_.info.get.slices.flatMap(_.segments))

      val captor = ArgumentCaptor.forClass(classOf[BapiFlightSegmentValidateFact])
      when(messageServiceMock.sendMessage(any[BapiFlightSegmentValidateFact])).thenReturn(Future.successful(()))
      hadoopMessagingService
        .sendBapiFlightSegmentValidationLogMessage(
          segment = segments.head,
          requestWithProduct = newRequest,
          departValidationResult = Some(ErrorCode.InvalidFlightDepartureDate),
          arriveValidationResult = Some(ErrorCode.InvalidFlightArrivalDate)
        )

      verify(messageServiceMock, times(1)).sendMessage(captor.capture())

      val hadoopMessage            = captor.getValue.asInstanceOf[BapiFlightSegmentValidateFact]
      val actualFlightBookingToken = Serialization.toJson(defaultFlightBookingToken)
      val actualRequestOrigin = newRequest.requestContext.userContext match {
        case Some(context) => Some(context.requestOrigin)
        case _             => None
      }

      hadoopMessage.userId shouldEqual newRequest.request.userId.toString
      hadoopMessage.requestedDateTime shouldEqual newRequest.requestContext.requestedDateTime.toString
      hadoopMessage.requestOrigin shouldEqual actualRequestOrigin
      hadoopMessage.flightSegmentOrigin shouldEqual segments.head.origin
      hadoopMessage.flightSegmentDestination shouldEqual segments.head.destination
      hadoopMessage.departureTime shouldEqual segments.head.departure.toString
      hadoopMessage.arrivalTime shouldEqual segments.head.arrival.toString
      hadoopMessage.isExcludeDateTimeValidation shouldBe true
      hadoopMessage.departureValidationResult shouldEqual Some(810)
      hadoopMessage.arrivalValidationResult shouldEqual Some(811)
      hadoopMessage.bookingFlow shouldEqual newRequest.bookingFlow.id
      hadoopMessage.flightBookingToken shouldEqual actualFlightBookingToken
    }
  }

}
