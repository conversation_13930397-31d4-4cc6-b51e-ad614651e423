package com.agoda.bapi.creation.service

import akka.actor.Scheduler
import com.agoda.bapi.common.config.{AgodaConfig, CancellationChargeConfig, KillSwitches, NhaHotelIdMappingConfig}
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.CustomerRiskStatus
import com.agoda.bapi.common.message.creation.{CreateBookingResponse, CreditCard}
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.proxy.EnigmaApiProxy
import com.agoda.bapi.common.util.ItineraryAssociatedBookingsTokenUtils
import com.agoda.bapi.creation.config.{DuplicateConfig, TestBookingConfig}
import com.agoda.bapi.creation.mapper.activity.ActivityMapper
import com.agoda.bapi.creation.mapper.addon.AddOnMapper
import com.agoda.bapi.creation.mapper.addon.cegFastTrack.CegFastTrackMapper
import com.agoda.bapi.creation.mapper.ebe._
import com.agoda.bapi.creation.model.RequestWithProducts
import com.agoda.bapi.creation.proxy.CreditCardApiLocalProxyV2
import com.agoda.bapi.creation.repository._
import com.agoda.bapi.creation.service.helper.MPLogHelper
import com.agoda.bapi.creation.validation.CreateBookingRequestValidator
import com.agoda.bapi.creation.{CreateBookingHelper, ExternalDIHelper}
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{reset, times, verify, when}
import org.scalatest.funspec.AsyncFunSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfterAll, BeforeAndAfterEach}
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future

class ItineraryServiceImplTest
    extends Matchers
    with AsyncFunSpecLike
    with MockitoSugar
    with BeforeAndAfterAll
    with BeforeAndAfterEach
    with CreateBookingHelper
    with ExternalDIHelper {

  val flightBookingRepository        = mock[FlightBookingRepository]
  val multiProductRepository         = mock[MultiProductRepository]
  val paymentMethodRepository        = mock[PaymentMethodRepository]
  val creditCardInfoRepository       = mock[CreditCardInfoRepository]
  val vehicleBookingRepository       = mock[VehicleBookingRepository]
  val tprmService                    = mock[TPRMService]
  val hadoopMessaging                = mock[HadoopMessagingService]
  val createBookingRequestValidator  = mock[CreateBookingRequestValidator]
  val enigmaApiProxy                 = mock[EnigmaApiProxy]
  val duplicateCheckService          = mock[DuplicateCheckService]
  val duplicateCheckServiceV2        = mock[DuplicateCheckService]
  val orchestrationMessageService    = mock[OrchestrationMessageService]
  val multiProductMasterMapper       = mock[MPMasterMapper]
  val saveBookingMapper              = mock[SaveBookingMapper]
  val flightMapper                   = mock[FlightMapper]
  val vehicleMapper                  = mock[VehicleMapper]
  val protectionMapper               = mock[ProtectionMapper]
  val urlService                     = mock[UrlService]
  val bookingDetailTokenCreatorUtils = mock[ItineraryAssociatedBookingsTokenUtils]
  val mpLogHelper                    = mock[MPLogHelper]
  val cancellationChargeConfig       = CancellationChargeConfig(1)
  val creditCardLocalProxy           = mock[CreditCardApiLocalProxyV2]
  val bookingActionMessageService    = mock[BookingActionMessageService]
  val activityBookingRepository      = mock[ActivityBookingRepository]
  val activityMapper                 = mock[ActivityMapper]
  val itineraryBookingRepository     = mock[ItineraryBookingRepository]
  val creationMdbRepository          = mock[CreationMdbRepository]
  val testBookingConfig              = TestBookingConfig(emails = Seq.empty, whitelistMemberIds = Seq.empty)
  val baseBookingRepository          = mock[BaseBookingRepository]
  val cegFastTrackRepository         = mock[CegFastTrackRepository]
  val genericAddOnRepository         = mock[GenericAddOnRepository]
  val agodaConfig                    = AgodaConfig("http://domainUrl.com", Map.empty, "c4b4acf8-9bf9-4d77-bf43-dc929cda582d")
  val cegFastTrackMapper             = mock[CegFastTrackMapper]
  val addOnMapper                    = mock[AddOnMapper]
  val scheduler                      = mock[Scheduler]
  val killSwitches                   = mock[KillSwitches]
  val duplicateConfig                = mock[DuplicateConfig]
  val creationCdbRepository          = mock[CreationCdbRepository]
  val nhaHotelIdMappingConfig        = mock[NhaHotelIdMappingConfig]

  val request =
    RequestWithProducts(
      baseReq,
      defaultRequestContext,
      Map.empty,
      Seq.empty,
      None,
      Seq.empty,
      BookingFlow.Package,
      requestV2 = None
    )

  override def beforeEach(): Unit = {
    reset(flightBookingRepository)
    reset(multiProductRepository)
    reset(paymentMethodRepository)
    reset(creditCardInfoRepository)
    reset(vehicleBookingRepository)
    reset(tprmService)
    reset(hadoopMessaging)
    reset(createBookingRequestValidator)
    reset(enigmaApiProxy)
    reset(duplicateCheckService, duplicateCheckServiceV2)
    reset(orchestrationMessageService)
    reset(multiProductMasterMapper)
    reset(saveBookingMapper)
    reset(flightMapper)
    reset(vehicleMapper)
    reset(mpLogHelper)
    reset(protectionMapper)
    reset(urlService)
    reset(bookingDetailTokenCreatorUtils)
    reset(baseBookingRepository)
    reset(cegFastTrackRepository)
    reset(genericAddOnRepository)
    reset(cegFastTrackMapper)
    reset(addOnMapper)
    reset(killSwitches)
  }

  def itineraryService() =
    new ItineraryServiceImpl(
      flightBookingRepository,
      multiProductRepository,
      paymentMethodRepository,
      creditCardInfoRepository,
      vehicleBookingRepository,
      tprmService,
      hadoopMessaging,
      createBookingRequestValidator,
      enigmaApiProxy,
      duplicateCheckService,
      duplicateCheckServiceV2,
      orchestrationMessageService,
      multiProductMasterMapper,
      saveBookingMapper,
      flightMapper,
      vehicleMapper,
      protectionMapper,
      urlService,
      mpLogHelper,
      cancellationChargeConfig,
      bookingDetailTokenCreatorUtils,
      creditCardLocalProxy,
      bookingActionMessageService,
      activityBookingRepository,
      activityMapper,
      itineraryBookingRepository,
      creationMdbRepository,
      testBookingConfig,
      baseBookingRepository,
      agodaConfig,
      cegFastTrackMapper,
      addOnMapper,
      cegFastTrackRepository,
      genericAddOnRepository,
      scheduler,
      killSwitches,
      duplicateConfig,
      creationCdbRepository,
      nhaHotelIdMappingConfig
    )

  it("create should call with correct flow with process return Left") {
    val mockGetUpdatedCreditCardInfoResult = Future.successful(request.request.payment.creditCard)
    when(creditCardInfoRepository.getUpdatedCreditCardInfo(any[CreditCard], any[Option[Boolean]])(any[RequestContext]))
      .thenReturn(mockGetUpdatedCreditCardInfoResult)

    // In case createBookingRequestValidator return response in left, the process will exit right the way
    // Fail the validation, the validator will return response with BadRequest status.
    val expectedResponse = mock[CreateBookingResponse]
    when(createBookingRequestValidator.validate(any[RequestWithProducts])(any[RequestContext]))
      .thenReturn(Future.successful(Some(expectedResponse)))

    when(tprmService.findCustomerTerrorists(request.request.customer)(request.requestContext))
      .thenReturn(Future.successful(CustomerRiskStatus.Safe))

    itineraryService().create(request)(defaultRequestContext).map { result =>
      verify(mpLogHelper, times(1)).log(ArgumentMatchers.eq(request))

      result shouldBe expectedResponse
    }
  }

  it("buildMultiProductProcessingFlow should call with initializeVehiclePreSaveStage") {
    // Arrange
    val itineraryServiceUnderTest               = itineraryService()
    implicit val requestContext: RequestContext = defaultRequestContext

    // Act
    val buildMultiProductProcessingFlowF = Future {
      itineraryServiceUnderTest.buildMultiProductProcessingFlow()
    }

    // Assert
    buildMultiProductProcessingFlowF map { _ =>
      succeed
    }
  }

}
