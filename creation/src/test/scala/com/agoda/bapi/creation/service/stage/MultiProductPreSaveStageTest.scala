package com.agoda.bapi.creation.service.stage

import com.agoda.bapi.common.config.KillSwitches
import com.agoda.bapi.common.handler.{MeasurementsContext, RequestContext}
import com.agoda.bapi.common.message.creation.CreateBookingResponse
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.common.model.consumerFintech.ConsumerFintechDetail
import com.agoda.bapi.common.model.consumerFintech.products.ConsumerFintechProductDetail
import com.agoda.bapi.common.model.consumerFintech.products.cancelAndRebookV3.CancelAndRebookV3ProductDetail
import com.agoda.bapi.common.model.creation.BAPIBooking
import com.agoda.bapi.common.model.multiproduct.{MultiProductBookingGroupDBModel, MultiProductInfoDBModel}
import com.agoda.bapi.common.model.product.{BookingFlow, ProductTypeEnum}
import com.agoda.bapi.common.model.rebookAndCancel.RebookAndCancelData
import com.agoda.bapi.common.model.relationship.{RelationshipStatuses, RelationshipTypes}
import com.agoda.bapi.common.model.{PaymentMethodFromDB, StatusToken, WhiteLabel}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.util.converters.ItineraryStateConverters.ItineraryStateToString
import com.agoda.bapi.common.util.{JodaDateTimeUtils, PaymentUtils, ServerUtils}
import com.agoda.bapi.creation.mapper.BookingRelationshipHelper
import com.agoda.bapi.creation.mapper.ebe.{MPMasterMapper, MPMasterMapperImpl}
import com.agoda.bapi.creation.model.db._
import com.agoda.bapi.creation.model.multi._
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.bapi.creation.proxy.CreditCardApiLocalProxyV2
import com.agoda.bapi.creation.repository.{FlightBookingRepository, MultiProductRepository, PaymentMethodRepository}
import com.agoda.bapi.creation.service.stage.presave.{MultiProductPreSaveRequest, NonHotelPreSaveFacade, ProductPreSaveStage, ProtectionAncillaryModel}
import com.agoda.bapi.creation.service.HadoopMessagingService
import com.agoda.bapi.creation.util.CustomerApiTokenUtils
import com.agoda.bapi.creation.{CreateMultiBookingHelper, ExternalDIHelper}
import com.agoda.mpb.common.{BookingType, MultiProductType}
import com.agoda.common.itineraryContext.{ItineraryContext, ProductGroupItem, ProductGroupType}
import com.agoda.creditcardapi.client.v2.common.model._
import com.agoda.mpb.common.errors.ErrorCode
import com.agoda.mpbe.state.booking.BaseBookingRelationship
import com.agoda.mpbe.state.common.enums.Gateway.Gateway
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.agoda.mpbe.state.itinerary.ItineraryState
import com.softwaremill.quicklens._
import mocks.DBBookingModelHelper
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers.{eq => eqTo, _}
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfterEach
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.{ExecutionContext, Future}

class MultiProductPreSaveStageTest
    extends AsyncWordSpec
    with MockitoSugar
    with Matchers
    with BeforeAndAfterEach
    with CreateMultiBookingHelper
    with DBBookingModelHelper
    with ExternalDIHelper {

  val mockPaymentMethodRepository = mock[PaymentMethodRepository]
  val mockFlightBookingRepository = mock[FlightBookingRepository]
  val mockMultiProductRepository  = mock[MultiProductRepository]
  val mockDateTimeUtils           = mock[JodaDateTimeUtils]
  val mockPaymentUtils            = mock[PaymentUtils]
  val mockFeatureAware            = mock[FeatureAware]
  val mockCustomerApiTokenUtils   = mock[CustomerApiTokenUtils]
  val killSwitches                = mock[KillSwitches]
  val mockItineraryMapper         = new MPMasterMapperImpl(mockDateTimeUtils, mockPaymentUtils, mockCustomerApiTokenUtils)

  val mockHotelPreSaveProcess =
    mock[ProductPreSaveStage[MultiProductPreSaveRequest, RoomInfo, PropertySaveStageResponse, EmptyProductReservedIds]]

  val mockCreditCardLocalProxy = mock[CreditCardApiLocalProxyV2]
  val hadoopMessaging          = mock[HadoopMessagingService]
  val nonHotelPreSaveFacade    = mock[NonHotelPreSaveFacade]
  val mockRelationshipMapper   = mock[BookingRelationshipHelper]
  implicit val requestContext  = mock[RequestContext]

  when(requestContext.featureAware).thenReturn(Some(mockFeatureAware))

  val processor = spy[MultiProductPreSaveStage](
    new MultiProductPreSaveStage(
      mockPaymentMethodRepository,
      mockFlightBookingRepository,
      mockMultiProductRepository,
      mockItineraryMapper,
      mockHotelPreSaveProcess,
      nonHotelPreSaveFacade,
      mockCreditCardLocalProxy,
      hadoopMessaging,
      mockRelationshipMapper
    )(killSwitches, requestContext)
  )

  when(hadoopMessaging.sendBapiCreateFactLogMessage(any, any[CreateBookingResponse], any[String]))
    .thenReturn(Future.successful(()))

  val createDate = DateTime.now()
  when(mockDateTimeUtils.getCurrentDateTime).thenReturn(createDate)

  val mockFlightBookingActionState = mock[BookingActionState]

  when(mockFlightBookingActionState.bookingState).thenReturn(Some(mockFlightModelInternal))
  when(mockRelationshipMapper.constructAddOnRelationships(any())).thenReturn(Seq.empty)
  when(mockRelationshipMapper.constructRebookAndCancelRelationships(any(), any())).thenReturn(Seq.empty)

  val itineraryId    = 1L
  val actionId       = 100L
  val multiProductId = 1000L
  val cartId         = 10000L
  val packageId      = 100000L
  val operationId    = 1000000L
  val relationshipId = 10000000L

  val flightBookingAction = mockBookingWorkflowAction.copy(
    state = mockFlightBookingActionState.toJson
  )
  val flightSaveStageResponse = NonPropertySaveStageResponse(
    productType = ProductTypeEnum.Flight,
    bookingAction = flightBookingAction,
    bookingActionState = mockFlightBookingActionState,
    productTokenKey = Some("Flight_1")
  )
  val hfFlightSaveStageResponse = NonPropertySaveStageResponse(
    productType = ProductTypeEnum.Flight,
    bookingAction = flightBookingAction.copy(bookingId = Some(333)),
    bookingActionState = mockFlightBookingActionState,
    productTokenKey = Some("Flight_1")
  )
  val hfFlightSaveStageResponse2 = NonPropertySaveStageResponse(
    productType = ProductTypeEnum.Flight,
    bookingAction = flightBookingAction.copy(bookingId = Some(999)),
    bookingActionState = mockFlightBookingActionState,
    productTokenKey = Some("Flight_1")
  )

  val mockPropertyBookingActionState: BookingActionState = BookingActionState(
    mock[CreationRequest],
    BookingActionStateCustomer(isUserLoggedIn = false),
    PaymentInfo(
      method = PaymentMethod.Visa,
      paymentCurrency = "THB",
      paymentAmount = 2150.0,
      paymentAmountUSD = 60.0,
      siteExchangeRate = Some(0.5),
      gateway = Some(Gateway.GMO),
      destinationCurrency = None,
      destinationExchangeRate = None,
      points = Vector.empty
    ),
    creditCardInfo = None,
    bookingState = None,
    campaignInfo = None,
    itineraryProtoState = ItineraryState.defaultInstance.toBase64OptString
  )

  val propertyBookingAction = mockBookingWorkflowAction.copy(
    state = mockPropertyBookingActionState
      .copy(
        itineraryProtoState = ItineraryState.defaultInstance.toBase64OptString
      )
      .toJson
  )
  val propertySaveStageResponse = PropertySaveStageResponse(
    bookingAction = propertyBookingAction,
    propertyCreationLocal = mockNewPropertyBookingObjectForEbeLite,
    productTokenKey = Some("Accom_1")
  )
  val propertyResponse = Right(
    Seq(propertySaveStageResponse)
  )

  val mockVehicleBookingActionState = mock[BookingActionState]
  val vehicleBookingAction = mockBookingWorkflowAction.copy(
    state = mockVehicleBookingActionState.toJson
  )
  val vehicleResponse =
    NonPropertySaveStageResponse(
      ProductTypeEnum.Car,
      vehicleBookingAction,
      mockVehicleBookingActionState,
      productTokenKey = Some("Vehicle_1")
    )

  val mockProtectionBookingActionState = mock[BookingActionState]
  val protectionBookingAction = mockBookingWorkflowAction.copy(
    state = mockProtectionBookingActionState.toJson
  )
  when(mockProtectionBookingActionState.tripProtectionBookingState).thenReturn(Some(mockNewProtectionModelInternal))
  val protectionResponse = NonPropertySaveStageResponse(
    productType = ProductTypeEnum.Protection,
    protectionBookingAction,
    mockProtectionBookingActionState,
    productTokenKey = Some("Protection_1")
  )

  val mockActivityBookingActionState = mock[BookingActionState]
  val activityBookingAction = mockBookingWorkflowAction.copy(
    state = mockActivityBookingActionState.toJson
  )
  val activityResponse =
    NonPropertySaveStageResponse(
      ProductTypeEnum.Activity,
      activityBookingAction,
      mockActivityBookingActionState,
      productTokenKey = Some("Activity_1")
    )

  val mockCegFastTrackBookingActionState = mock[BookingActionState]
  val cegFastTrackBookingAction = mockBookingWorkflowAction.copy(
    state = mockCegFastTrackBookingActionState.toJson
  )
  val cegFastTrackResponse =
    NonPropertySaveStageResponse(
      productType = ProductTypeEnum.CegFastTrack,
      mockBookingWorkflowAction.copy(
        state = mockCegFastTrackBookingActionState.toJson
      ),
      mockCegFastTrackBookingActionState,
      productTokenKey = Some("CegFastTrack_1")
    )

  val mockFlightCheckInBookingActionState: BookingActionState = mock[BookingActionState]
  val flightCheckInBookingAction: BookingWorkflowAction = mockBookingWorkflowAction.copy(
    state = mockFlightCheckInBookingActionState.toJson
  )
  val flightCheckInResponse: NonPropertySaveStageResponse =
    NonPropertySaveStageResponse(
      productType = ProductTypeEnum.FlightCheckIn,
      mockBookingWorkflowAction.copy(
        state = mockFlightCheckInBookingActionState.toJson
      ),
      mockFlightCheckInBookingActionState,
      productTokenKey = Some("FlightCheckIn_1")
    )

  val mockProtectionAddOnBookingActionState = mock[BookingActionState]
  val protectionAddOnBookingAction = mockBookingWorkflowAction.copy(
    state = mockProtectionAddOnBookingActionState.toJson
  )

  when(mockProtectionAddOnBookingActionState.tripProtectionBookingState).thenReturn(None)
  val protectionAddOnResponse =
    NonPropertySaveStageResponse(
      productType = ProductTypeEnum.Protection,
      mockBookingWorkflowAction.copy(
        state = mockProtectionAddOnBookingActionState.toJson
      ),
      mockProtectionAddOnBookingActionState,
      productTokenKey = Some("ProtectionAddOn_1")
    )

  val paymentMethodFromDB        = mock[PaymentMethodFromDB]
  val mockAddTransientCCResponse = AddTransientCCResponse()
  val mockRetrieveTransientCCResponse = RetrieveTransientCCResponse(
    CCId = Some(1234),
    CCDetail = Some(
      CreditCardDetailSimple(
        CardTypeId = Some(1),
        CardholderName = Some("test agoda"),
        ExpiryMonth = Some(3),
        ExpiryYear = Some(2029),
        CreditCardNo = Some("****************")
      )
    ),
    SecurityCode = Some("123"),
    StatusCode = Some("OK")
  )

  val baseProtectionProduct = Product(
    BookingType.Protection,
    ProtectionAncillaryModel(baseProtectionToken, Seq(flightSaveStageResponse)),
    Some(MultiProductType.Package)
  )

  val baseInput = MultiProductsRequest(
    request = baseMultiProductReq,
    requestContext = baseRequestContext,
    properties = Seq(baseHotelProduct),
    flights = Seq(baseFlightProduct),
    vehicles = Seq(baseVehicleProduct),
    protections = Seq(baseProtectionProduct),
    activities = Seq(baseActivityProduct),
    cegFastTracks = Seq(baseCegFastTrackProduct),
    addOns = Seq.empty,
    bookingFlow = BookingFlow.Package,
    commonPayment = None,
    isBookingFromCart = None
  )

  override def beforeEach: Unit = {
    reset(
      mockPaymentMethodRepository,
      mockFlightBookingRepository,
      mockMultiProductRepository,
      mockHotelPreSaveProcess,
      nonHotelPreSaveFacade,
      mockCreditCardLocalProxy,
      processor,
      mockFeatureAware
    )

    when(mockPaymentMethodRepository.getPaymentMethodInfo(any(), any(), any()))
      .thenReturn(Future.successful(Some(paymentMethodFromDB)))
  }

  "process" should {
    "process correctly" in {
      implicit val measurementContext             = baseInput.measurementsContext
      implicit val requestContext: RequestContext = baseInput.requestContext.copy(featureAware = Some(mockFeatureAware))
      when(mockHotelPreSaveProcess.process(any[MultiProductPreSaveRequest]))
        .thenReturn(Future.successful(propertyResponse))
      when(nonHotelPreSaveFacade.preSave(any[MultiProductPreSaveRequest])).thenReturn(
        Future.successful(
          Right(
            Seq(flightSaveStageResponse, vehicleResponse, activityResponse, protectionResponse)
          )
        )
      )

      when(mockMultiProductRepository.getNextMultiBookingSequenceNumber()).thenReturn(Future.successful(multiProductId))
      when(mockMultiProductRepository.getNextCartSequenceNumber()).thenReturn(Future.successful(cartId))
      when(mockMultiProductRepository.getNextPackageSequenceNumber()).thenReturn(Future.successful(packageId))
      when(mockMultiProductRepository.getNextOperationIdSequenceNumber()(any()))
        .thenReturn(Future.successful(operationId))
      when(mockMultiProductRepository.getNextBaseBookingRelationshipIdSequenceNumbers(any()))
        .thenReturn(Future.successful(Seq(relationshipId)))
      when(mockFlightBookingRepository.getNextItinerarySequenceNumber)
        .thenReturn(Future.successful(itineraryId))
      when(mockFlightBookingRepository.getNextActionIdSequenceNumber).thenReturn(Future.successful(actionId))
      when(
        mockCreditCardLocalProxy.addTransientCreditCard(
          eqTo(baseInput.request.payment.creditCard),
          any[Boolean],
          eqTo(baseInput.request.payment.ccToken)
        )(
          any[RequestContext]
        )
      )
        .thenReturn(Future.successful(mockAddTransientCCResponse))

      processor.process(baseInput).map { result =>
        verify(mockMultiProductRepository).getNextMultiBookingSequenceNumber()
        verify(mockFlightBookingRepository).getNextItinerarySequenceNumber
        verify(mockMultiProductRepository).getNextCartSequenceNumber()
        verify(mockMultiProductRepository).getNextPackageSequenceNumber()
        verify(mockHotelPreSaveProcess).process(any[MultiProductPreSaveRequest])
        verify(nonHotelPreSaveFacade).preSave(any[MultiProductPreSaveRequest])

        verify(processor)
          .generateMultiProductIdMapping(eqTo(baseInput.getAllProductInfo))(eqTo(measurementContext))
        verify(processor)
          .createItineraryPreSaveInfo(
            eqTo(baseInput),
            eqTo(itineraryId),
            eqTo(actionId),
            eqTo(cartId),
            eqTo(Some(packageId)),
            eqTo(Some(operationId)),
            eqTo(Seq.empty)
          )
        verify(mockRelationshipMapper, atLeastOnce()).constructAddOnRelationships(
          eqTo(
            Seq(
              propertySaveStageResponse,
              flightSaveStageResponse,
              vehicleResponse,
              activityResponse,
              protectionResponse
            )
          )
        )
        verify(mockRelationshipMapper, atLeastOnce()).constructRebookAndCancelRelationships(
          eqTo(
            Seq(
              propertySaveStageResponse,
              flightSaveStageResponse,
              vehicleResponse,
              activityResponse,
              protectionResponse
            )
          ),
          eqTo(None)
        )
        verify(processor).gatherPreSaveResults(
          eqTo(baseInput),
          any[ItineraryPreSaveInfo],
          any[Option[PaymentMethodFromDB]],
          any[Map[MultiProductIdentifier, MultiProductInfoDBModel]],
          any[Either[CreateBookingResponse, Seq[PropertySaveStageResponse]]],
          any[Either[CreateBookingResponse, Seq[NonPropertySaveStageResponse]]],
          any[AddTransientCCResponse],
          any[Option[FeatureAware]],
          any[Seq[BaseBookingRelationship]]
        )

        result.isRight shouldBe true
      }
    }

    "return error response when some process throw exception" in {
      implicit val measurementContext = baseInput.measurementsContext
      implicit val context            = baseInput.requestContext
      val exception                   = new Exception("mock Exception")
      when(mockFlightBookingRepository.getNextItinerarySequenceNumber)
        .thenReturn(Future.failed(exception))

      processor.process(baseInput).map { result =>
        verify(mockFlightBookingRepository).getNextItinerarySequenceNumber
        verify(mockMultiProductRepository, never).getNextMultiBookingSequenceNumber()
        verify(mockFlightBookingRepository, never).getNextActionIdSequenceNumber
        verify(mockHotelPreSaveProcess, never).process(any[MultiProductPreSaveRequest])
        verify(nonHotelPreSaveFacade, never).preSave(any[MultiProductPreSaveRequest])

        verify(processor, never)
          .generateMultiProductIdMapping(eqTo(baseInput.properties ++ baseInput.flights))(eqTo(measurementContext))
        verify(processor, never)
          .createItineraryPreSaveInfo(
            eqTo(baseInput),
            eqTo(itineraryId),
            eqTo(actionId),
            eqTo(cartId),
            eqTo(Some(packageId)),
            eqTo(Some(operationId)),
            eqTo(Seq.empty)
          )
        verify(processor, never).gatherPreSaveResults(
          eqTo(baseInput),
          any[ItineraryPreSaveInfo],
          any[Option[PaymentMethodFromDB]],
          any[Map[MultiProductIdentifier, MultiProductInfoDBModel]],
          any[Either[CreateBookingResponse, Seq[PropertySaveStageResponse]]],
          any[Either[CreateBookingResponse, Seq[NonPropertySaveStageResponse]]],
          any[AddTransientCCResponse],
          any[Option[FeatureAware]],
          any[Seq[BaseBookingRelationship]]
        )
        verify(hadoopMessaging).sendBapiCreateFactLogMessage(any, any[CreateBookingResponse], any[String])

        result shouldBe Left(CreateBookingResponse.technical(ErrorCode.UnexpectedInternalError, Some(exception)))
      }
    }
    "not call AddTransientCC when ccToken has value and BCT-667 is B" in {
      implicit val measurementContext = baseInput.measurementsContext
      when(mockFeatureAware.enableCCTokenInBookingActionState).thenReturn(true)
      val requestContext: RequestContext = baseInput.requestContext.copy(featureAware = Some(mockFeatureAware))

      val baseInputWithCCToken = baseInput.copy(
        requestContext = requestContext,
        request = baseInput.request.copy(
          payment = baseInput.request.payment.copy(
            ccToken = Some("token")
          )
        )
      )
      when(mockHotelPreSaveProcess.process(any[MultiProductPreSaveRequest]))
        .thenReturn(Future.successful(propertyResponse))
      when(nonHotelPreSaveFacade.preSave(any[MultiProductPreSaveRequest])).thenReturn(
        Future.successful(
          Right(
            Seq(flightSaveStageResponse, vehicleResponse, activityResponse, protectionResponse)
          )
        )
      )

      when(mockMultiProductRepository.getNextMultiBookingSequenceNumber()).thenReturn(Future.successful(multiProductId))
      when(mockMultiProductRepository.getNextCartSequenceNumber()).thenReturn(Future.successful(cartId))
      when(mockMultiProductRepository.getNextPackageSequenceNumber()).thenReturn(Future.successful(packageId))
      when(mockMultiProductRepository.getNextOperationIdSequenceNumber()(any()))
        .thenReturn(Future.successful(operationId))
      when(mockMultiProductRepository.getNextBaseBookingRelationshipIdSequenceNumbers(any()))
        .thenReturn(Future.successful(Seq(relationshipId)))
      when(mockFlightBookingRepository.getNextItinerarySequenceNumber)
        .thenReturn(Future.successful(itineraryId))
      when(mockFlightBookingRepository.getNextActionIdSequenceNumber).thenReturn(Future.successful(actionId))

      processor.process(baseInputWithCCToken).map { result =>
        verify(mockCreditCardLocalProxy, never()).addTransientCreditCard(any(), any(), any())(any[RequestContext])
        verify(mockMultiProductRepository).getNextMultiBookingSequenceNumber()
        verify(mockFlightBookingRepository).getNextItinerarySequenceNumber
        verify(mockMultiProductRepository).getNextCartSequenceNumber()
        verify(mockMultiProductRepository).getNextPackageSequenceNumber()
        verify(mockHotelPreSaveProcess).process(any[MultiProductPreSaveRequest])
        verify(nonHotelPreSaveFacade).preSave(any[MultiProductPreSaveRequest])

        verify(processor)
          .generateMultiProductIdMapping(eqTo(baseInputWithCCToken.getAllProductInfo))(eqTo(measurementContext))
        verify(processor)
          .createItineraryPreSaveInfo(
            eqTo(baseInputWithCCToken),
            eqTo(itineraryId),
            eqTo(actionId),
            eqTo(cartId),
            eqTo(Some(packageId)),
            eqTo(Some(operationId)),
            eqTo(Seq.empty)
          )
        verify(mockRelationshipMapper, atLeastOnce()).constructAddOnRelationships(
          eqTo(
            Seq(
              propertySaveStageResponse,
              flightSaveStageResponse,
              vehicleResponse,
              activityResponse,
              protectionResponse
            )
          )
        )
        verify(mockRelationshipMapper, atLeastOnce()).constructRebookAndCancelRelationships(
          eqTo(
            Seq(
              propertySaveStageResponse,
              flightSaveStageResponse,
              vehicleResponse,
              activityResponse,
              protectionResponse
            )
          ),
          eqTo(None)
        )
        verify(processor).gatherPreSaveResults(
          eqTo(baseInputWithCCToken),
          any[ItineraryPreSaveInfo],
          any[Option[PaymentMethodFromDB]],
          any[Map[MultiProductIdentifier, MultiProductInfoDBModel]],
          any[Either[CreateBookingResponse, Seq[PropertySaveStageResponse]]],
          any[Either[CreateBookingResponse, Seq[NonPropertySaveStageResponse]]],
          any[AddTransientCCResponse],
          any[Option[FeatureAware]],
          any[Seq[BaseBookingRelationship]]
        )

        result.isRight shouldBe true
      }
    }
    "call AddTransientCC when ccToken has value but BCT-667 is A" in {
      implicit val measurementContext = baseInput.measurementsContext
      when(mockFeatureAware.enableCCTokenInBookingActionState).thenReturn(false)
      val requestContext: RequestContext = baseInput.requestContext.copy(featureAware = Some(mockFeatureAware))

      val baseInputWithCCToken = baseInput.copy(
        requestContext = requestContext,
        request = baseInput.request.copy(
          payment = baseInput.request.payment.copy(
            ccToken = Some("token")
          )
        )
      )
      when(mockHotelPreSaveProcess.process(any[MultiProductPreSaveRequest]))
        .thenReturn(Future.successful(propertyResponse))
      when(nonHotelPreSaveFacade.preSave(any[MultiProductPreSaveRequest])).thenReturn(
        Future.successful(
          Right(
            Seq(flightSaveStageResponse, vehicleResponse, activityResponse, protectionResponse)
          )
        )
      )

      when(mockMultiProductRepository.getNextMultiBookingSequenceNumber()).thenReturn(Future.successful(multiProductId))
      when(mockMultiProductRepository.getNextCartSequenceNumber()).thenReturn(Future.successful(cartId))
      when(mockMultiProductRepository.getNextPackageSequenceNumber()).thenReturn(Future.successful(packageId))
      when(mockMultiProductRepository.getNextOperationIdSequenceNumber()(any()))
        .thenReturn(Future.successful(operationId))
      when(mockMultiProductRepository.getNextBaseBookingRelationshipIdSequenceNumbers(any()))
        .thenReturn(Future.successful(Seq(relationshipId)))
      when(mockFlightBookingRepository.getNextItinerarySequenceNumber)
        .thenReturn(Future.successful(itineraryId))
      when(mockFlightBookingRepository.getNextActionIdSequenceNumber).thenReturn(Future.successful(actionId))
      when(
        mockCreditCardLocalProxy.addTransientCreditCard(
          eqTo(baseInputWithCCToken.request.payment.creditCard),
          any[Boolean],
          eqTo(baseInputWithCCToken.request.payment.ccToken)
        )(
          any[RequestContext]
        )
      )
        .thenReturn(Future.successful(mockAddTransientCCResponse))

      processor.process(baseInputWithCCToken).map { result =>
        verify(mockCreditCardLocalProxy).addTransientCreditCard(any(), any(), any())(any[RequestContext])
        verify(mockMultiProductRepository).getNextMultiBookingSequenceNumber()
        verify(mockFlightBookingRepository).getNextItinerarySequenceNumber
        verify(mockMultiProductRepository).getNextCartSequenceNumber()
        verify(mockMultiProductRepository).getNextPackageSequenceNumber()
        verify(mockHotelPreSaveProcess).process(any[MultiProductPreSaveRequest])
        verify(nonHotelPreSaveFacade).preSave(any[MultiProductPreSaveRequest])

        verify(processor)
          .generateMultiProductIdMapping(eqTo(baseInputWithCCToken.getAllProductInfo))(eqTo(measurementContext))
        verify(processor)
          .createItineraryPreSaveInfo(
            eqTo(baseInputWithCCToken),
            eqTo(itineraryId),
            eqTo(actionId),
            eqTo(cartId),
            eqTo(Some(packageId)),
            eqTo(Some(operationId)),
            eqTo(Seq.empty)
          )
        verify(mockRelationshipMapper, atLeastOnce()).constructAddOnRelationships(
          eqTo(
            Seq(
              propertySaveStageResponse,
              flightSaveStageResponse,
              vehicleResponse,
              activityResponse,
              protectionResponse
            )
          )
        )
        verify(mockRelationshipMapper, atLeastOnce()).constructRebookAndCancelRelationships(
          eqTo(
            Seq(
              propertySaveStageResponse,
              flightSaveStageResponse,
              vehicleResponse,
              activityResponse,
              protectionResponse
            )
          ),
          eqTo(None)
        )
        verify(processor).gatherPreSaveResults(
          eqTo(baseInputWithCCToken),
          any[ItineraryPreSaveInfo],
          any[Option[PaymentMethodFromDB]],
          any[Map[MultiProductIdentifier, MultiProductInfoDBModel]],
          any[Either[CreateBookingResponse, Seq[PropertySaveStageResponse]]],
          any[Either[CreateBookingResponse, Seq[NonPropertySaveStageResponse]]],
          any[AddTransientCCResponse],
          any[Option[FeatureAware]],
          any[Seq[BaseBookingRelationship]]
        )

        result.isRight shouldBe true
      }
    }
  }

  "generateMultiProductIdMapping" should {
    "return correct result" in {
      implicit val measurementContext: MeasurementsContext = baseInput.measurementsContext
      when(mockMultiProductRepository.getNextMultiBookingSequenceNumber()).thenReturn(Future.successful(multiProductId))
      processor.generateMultiProductIdMapping(baseInput.properties ++ baseInput.flights).map { result =>
        result shouldBe Map(
          Some(MultiProductType.Package) -> MultiProductInfoDBModel(multiProductId, MultiProductType.Package)
        )
      }
    }
  }
  "createItineraryPreSaveInfo" should {
    "return correct result" in {
      val tripStart = DateTime.parse("2018-11-16T11:37:10.000Z")
      val tripEnd   = DateTime.parse("2018-11-16T11:37:10.000Z")
      val result = processor.createItineraryPreSaveInfo(
        baseInput,
        itineraryId,
        actionId,
        cartId,
        Some(packageId)
      )

      result shouldBe ItineraryPreSaveInfo(
        itineraryId = itineraryId,
        actionId = actionId,
        tripStart = Some(tripStart),
        tripEnd = Some(tripEnd),
        cartId = cartId,
        packageId = Some(packageId)
      )
    }

    "return correct tripStart and tripEnd date" in {
      val roomInfo = baseRoomInfo("1")
      val firstRoomInfo = roomInfo
        .modify(_.bapiBooking.booking.each.booking.each.hotel.each.checkIn)
        .setTo(DateTime.parse("2020-10-01T00:00:00.000Z"))
        .modify(_.bapiBooking.booking.each.booking.each.hotel.each.checkOut)
        .setTo(DateTime.parse("2020-10-04T00:00:00.000Z"))
      val secondRoomInfo = roomInfo
        .modify(_.bapiBooking.booking.each.booking.each.hotel.each.checkIn)
        .setTo(DateTime.parse("2020-10-04T00:00:00.000Z"))
        .modify(_.bapiBooking.booking.each.booking.each.hotel.each.checkOut)
        .setTo(DateTime.parse("2020-10-06T00:00:00.000Z"))
      val firstHotelProduct  = Product(BookingType.CreditCard, firstRoomInfo, Some(MultiProductType.Package))
      val secondHotelProduct = Product(BookingType.CreditCard, secondRoomInfo, Some(MultiProductType.Package))

      val baseInput = MultiProductsRequest(
        request = baseMultiProductReq,
        requestContext = baseRequestContext,
        properties = Seq(firstHotelProduct, secondHotelProduct),
        flights = Seq(baseFlightProduct),
        vehicles = Seq(baseVehicleProduct),
        protections = Seq(baseProtectionProduct),
        activities = Seq(baseActivityProduct),
        cegFastTracks = Seq(baseCegFastTrackProduct),
        addOns = Seq.empty,
        bookingFlow = BookingFlow.Package,
        commonPayment = None,
        isBookingFromCart = None
      )

      val expectedTripStart = Some(DateTime.parse("2020-10-01T00:00:00.000Z"))
      val expectedTripEnd   = Some(DateTime.parse("2020-10-06T00:00:00.000Z"))
      val result = processor.createItineraryPreSaveInfo(
        baseInput,
        itineraryId,
        actionId,
        cartId,
        Some(packageId)
      )

      result.tripStart shouldBe expectedTripStart
      result.tripEnd shouldBe expectedTripEnd
    }
  }

  "gatherPreSaveResults" should {
    val tripStart = DateTime.parse("2018-11-16T11:37:10.000Z")
    val tripEnd   = DateTime.parse("2018-11-16T11:37:10.000Z")
    val multiProductMapping = Map[MultiProductIdentifier, MultiProductInfoDBModel](
      Some(MultiProductType.Package) -> MultiProductInfoDBModel(multiProductId, MultiProductType.Package)
    )
    val itineraryInfo = ItineraryPreSaveInfo(
      itineraryId = itineraryId,
      actionId = actionId,
      tripStart = Some(tripStart),
      tripEnd = Some(tripEnd),
      cartId = cartId,
      packageId = Some(packageId),
      operationId = Some(operationId)
    )
    val statusToken = StatusToken(
      itineraryInfo.itineraryId,
      itineraryInfo.actionId,
      Set(
        ProductTypeEnum.Flight.toString,
        ProductTypeEnum.Property.toString,
        ProductTypeEnum.Protection.toString,
        ProductTypeEnum.Car.toString,
        ProductTypeEnum.Activity.toString,
        ProductTypeEnum.CegFastTrack.toString,
        ProductTypeEnum.FlightCheckIn.toString
      ),
      ServerUtils.serverDc(),
      whitelabelId = Some(WhiteLabel.Agoda.id),
      version = Some(StatusToken.Version4)
    )

    "statusToken" should {
      val defaultStatusToken = StatusToken(
        itineraryInfo.itineraryId,
        itineraryInfo.actionId,
        Set(
          ProductTypeEnum.Flight.toString
        ),
        ServerUtils.serverDc(),
        whitelabelId = Some(WhiteLabel.Agoda.id)
      )

      "return correct with each product and status token v4" in {
        val result = processor.gatherPreSaveResults(
          baseInput,
          itineraryInfo,
          Some(paymentMethodFromDB),
          multiProductMapping,
          propertyProductsE = Right(Nil),
          nonPropertyProductsE = Right(Seq(flightSaveStageResponse)),
          addTransientCCResponse = AddTransientCCResponse(),
          featureAware = Some(mockFeatureAware),
          bookingRelationships = Nil
        )

        result.right.get.statusToken shouldBe defaultStatusToken.copy(version = Some(StatusToken.Version4))
      }

      "contain version 4" in {
        val result = processor.gatherPreSaveResults(
          baseInput,
          itineraryInfo.copy(operationId = Some(operationId)),
          Some(paymentMethodFromDB),
          multiProductMapping,
          propertyProductsE = Right(Nil),
          nonPropertyProductsE = Right(Seq(flightSaveStageResponse)),
          addTransientCCResponse = AddTransientCCResponse(),
          featureAware = Some(mockFeatureAware),
          bookingRelationships = Nil
        )

        result.right.get.statusToken.version.get shouldBe 4
      }
    }

    "return correct result with operationId populated" in {
      val baseBookingRelationship = BaseBookingRelationship(
        sourceBookingId = 19293,
        targetBookingId = 232132,
        relationshipStatusId = RelationshipStatuses.Confirmed.id,
        relationshipTypeId = RelationshipTypes.AddOn.id,
        recStatus = 1,
        recCreatedWhen = java.time.LocalDateTime.parse("1970-01-01T00:00:00.000"),
        recCreatedBy = "",
        recModifiedWhen = None,
        recModifiedBy = None,
        relationshipId = 5734934L
      )

      val workflowActions = Seq(
        propertyBookingAction,
        flightBookingAction,
        vehicleBookingAction,
        protectionBookingAction,
        activityBookingAction,
        cegFastTrackBookingAction,
        protectionAddOnBookingAction,
        flightCheckInBookingAction
      )

      val multiProductBookingGroups = workflowActions.flatMap(
        _.bookingId.map(bookingId =>
          MultiProductBookingGroupDBModel(
            bookingId,
            itineraryId,
            cartId,
            Some(packageId)
          )
        )
      )

      val overwriteOperationIdOpt: Option[Long] = Some(operationId)

      val expectedItineraryModel = mockItineraryMapper.mapItineraryDbModel(
        request = baseInput,
        itineraryId = itineraryInfo.itineraryId,
        actionId = itineraryInfo.actionId,
        transientCCId = None,
        relationships = Seq(baseBookingRelationship),
        multiProductBookingGroups = multiProductBookingGroups,
        ccToken = None,
        oldHistory = Seq.empty
      )

      val masterBookingAction = mockItineraryMapper.mapBookingAction(
        multiProductsRequest = baseInput,
        itineraryId = itineraryInfo.itineraryId,
        actionId = itineraryInfo.actionId,
        itineraryModel = expectedItineraryModel,
        paymentMethodInfo = Some(paymentMethodFromDB),
        statusToken = statusToken,
        featureAware = Some(mockFeatureAware)
      )

      val expectedWorkflowActions =
        (Seq(masterBookingAction) ++ workflowActions).map(
          _.copy(whiteLabelId = Some(WhiteLabel.Agoda.id), operationId = overwriteOperationIdOpt)
        )

      val expected = MultiProductSaveStageRequest(
        request = baseMultiProductReq,
        requestContext = baseRequestContext,
        saveBookingModel = MultiProductBookingInsertionModel(
          multiProductsInfo = Seq(MultiProductInfoDBModel(multiProductId, MultiProductType.Package)),
          workflowActions = expectedWorkflowActions,
          itineraryModel = expectedItineraryModel,
          flightBookingActionStates = Seq(mockFlightBookingActionState),
          propertyModels = Seq(mockNewPropertyBookingObjectForEbeLite),
          vehicleBookingActionStates = Seq(mockVehicleBookingActionState),
          protectionBookingActionStates = Seq(mockProtectionBookingActionState),
          activityBookingActionState = Seq(mockActivityBookingActionState),
          cegFastTrackBookingActionState = Seq(mockCegFastTrackBookingActionState),
          addOnsBookingActionState = Seq(mockProtectionAddOnBookingActionState),
          multiProductBookingGroups = multiProductBookingGroups,
          operationId = overwriteOperationIdOpt
        ),
        statusToken
      )

      val result = processor.gatherPreSaveResults(
        request = baseInput,
        itineraryPreSaveInfo = itineraryInfo,
        paymentMethodInfo = Some(paymentMethodFromDB),
        multiProductIds = multiProductMapping,
        propertyProductsE = propertyResponse,
        nonPropertyProductsE = Right(
          Seq(
            flightSaveStageResponse,
            vehicleResponse,
            protectionResponse,
            activityResponse,
            cegFastTrackResponse,
            protectionAddOnResponse,
            flightCheckInResponse
          )
        ),
        addTransientCCResponse = AddTransientCCResponse(),
        featureAware = Some(mockFeatureAware),
        bookingRelationships = Seq(baseBookingRelationship)
      )

      result shouldBe Right(expected)
    }

    "return error response from each product" in {
      val exception1     = new Exception("mock Exception1")
      val errorResponse1 = Left(CreateBookingResponse.internal(exception1))

      processor.gatherPreSaveResults(
        baseInput,
        itineraryInfo,
        Some(paymentMethodFromDB),
        multiProductMapping,
        propertyResponse,
        errorResponse1,
        addTransientCCResponse = AddTransientCCResponse(),
        Some(mockFeatureAware),
        bookingRelationships = Nil
      ) shouldBe errorResponse1
    }

    "getNextItinerarySequenceNumber return cxl rebook's original itineraryId if available" in {
      val originalCxlRebookItineraryId       = 237232L
      val mockRequest                        = mock[MultiProductsRequest]
      val mockRoomInfo                       = mock[RoomInfo]
      val mockProductRoomInfo                = Product(BookingType.CreditCard, mockRoomInfo)
      val mockBapiBooking                    = mock[BAPIBooking]
      val mockConsumerFintechDetail          = mock[ConsumerFintechDetail]
      val mockConsumerFintechProductDetail   = mock[ConsumerFintechProductDetail]
      val mockCancelAndRebookV3ProductDetail = mock[CancelAndRebookV3ProductDetail]

      when(mockRequest.properties).thenReturn(Seq(mockProductRoomInfo))
      when(mockRoomInfo.bapiBooking).thenReturn(mockBapiBooking)
      when(mockBapiBooking.consumerFintechDetails).thenReturn(Some(mockConsumerFintechDetail))
      when(mockConsumerFintechDetail.products).thenReturn(mockConsumerFintechProductDetail)
      when(mockConsumerFintechProductDetail.cancelAndRebookV3).thenReturn(Some(mockCancelAndRebookV3ProductDetail))
      when(mockCancelAndRebookV3ProductDetail.originalItineraryId).thenReturn(originalCxlRebookItineraryId)
      when(mockFlightBookingRepository.getNextItinerarySequenceNumber)
        .thenReturn(Future.successful(3262673L))

      processor
        .getNextItinerarySequenceNumber(
          mockRequest
        )
        .map(res => {
          verify(mockFlightBookingRepository, never()).getNextItinerarySequenceNumber
          res shouldBe originalCxlRebookItineraryId
        })
    }

    "getNextItinerarySequenceNumber return rebook and cancel's original itineraryId if available" in {
      val originalRebookAndCancelItineraryId = 237232L
      val mockRequest                        = mock[MultiProductsRequest]
      val mockRoomInfo                       = mock[RoomInfo]
      val mockProductRoomInfo                = Product(BookingType.CreditCard, mockRoomInfo)
      val mockBapiBooking                    = mock[BAPIBooking]
      val mockRebookAndCancelData            = mock[RebookAndCancelData]

      when(mockRequest.properties).thenReturn(Seq(mockProductRoomInfo))
      when(mockRequest.rebookAndCancelData).thenReturn(Some(mockRebookAndCancelData))
      when(mockRoomInfo.bapiBooking).thenReturn(mockBapiBooking)
      when(mockBapiBooking.consumerFintechDetails).thenReturn(None)
      when(mockRebookAndCancelData.originalItineraryId).thenReturn(originalRebookAndCancelItineraryId)
      when(mockFlightBookingRepository.getNextItinerarySequenceNumber)
        .thenReturn(Future.successful(3262673L))

      processor
        .getNextItinerarySequenceNumber(
          mockRequest
        )
        .map(res => {
          verify(mockFlightBookingRepository, never()).getNextItinerarySequenceNumber
          res shouldBe originalRebookAndCancelItineraryId
        })
    }

    // TODO mpbe-5258
    /* "return correct result for CFB-1224 = A" in {
     * when(mockFeatureAware.cegFastTrackMigrationCreateFlow).thenReturn(false)
     * when(mockFeatureAware.generatePrimaryKeyWithSpecificSeq).thenReturn(false)
     *
     * val baseBookingRelationship = BaseBookingRelationship( sourceBookingId = 19293, targetBookingId = 232132,
     * relationshipStatusId = RelationshipStatuses.Confirmed.id, relationshipTypeId = RelationshipTypes.AddOn.id,
     * recStatus = 1, recCreatedWhen = java.time.LocalDateTime.parse("1970-01-01T00:00:00.000"), recCreatedBy = "",
     * recModifiedWhen = None, recModifiedBy = None, relationshipId = 5734934L )
     *
     * val result = processor.gatherPreSaveResults( request = baseInput, itineraryPreSaveInfo = itineraryInfo,
     * paymentMethodInfo = Some(paymentMethodFromDB), multiProductIds = multiProductMapping, propertyProductsE =
     * propertyResponse, nonPropertyProductsE = Right( Seq( flightSaveStageResponse, vehicleResponse,
     * protectionResponse, activityResponse, priceFreezeResponse, cegFastTrackResponse, protectionAddOnResponse,
     * flightCheckInResponse ) ), addTransientCCResponse = AddTransientCCResponse(), featureAware =
     * Some(mockFeatureAware), bookingRelationships = Seq(baseBookingRelationship) )
     *
     * result.right.get.saveBookingModel.cegFastTrackBookingActionState.size shouldBe 1
     * result.right.get.saveBookingModel.addOnsBookingActionState.size shouldBe 1 }
     *
     * "return correct result for CFB-1224 = B and cegFastTrack request is empty" in {
     * when(mockFeatureAware.cegFastTrackMigrationCreateFlow).thenReturn(true)
     * when(mockFeatureAware.generatePrimaryKeyWithSpecificSeq).thenReturn(false)
     *
     * val baseBookingRelationship = BaseBookingRelationship( sourceBookingId = 19293, targetBookingId = 232132,
     * relationshipStatusId = RelationshipStatuses.Confirmed.id, relationshipTypeId = RelationshipTypes.AddOn.id,
     * recStatus = 1, recCreatedWhen = java.time.LocalDateTime.parse("1970-01-01T00:00:00.000"), recCreatedBy = "",
     * recModifiedWhen = None, recModifiedBy = None, relationshipId = 5734934L )
     *
     * val request = baseInput.copy(cegFastTracks = Seq.empty)
     *
     * val result = processor.gatherPreSaveResults( request = request, itineraryPreSaveInfo = itineraryInfo,
     * paymentMethodInfo = Some(paymentMethodFromDB), multiProductIds = multiProductMapping, propertyProductsE =
     * propertyResponse, nonPropertyProductsE = Right( Seq( flightSaveStageResponse, vehicleResponse,
     * protectionResponse, activityResponse, priceFreezeResponse, cegFastTrackResponse, protectionAddOnResponse,
     * flightCheckInResponse ) ), addTransientCCResponse = AddTransientCCResponse(), featureAware =
     * Some(mockFeatureAware), bookingRelationships = Seq(baseBookingRelationship) )
     *
     * result.right.get.saveBookingModel.cegFastTrackBookingActionState.size shouldBe 0
     * result.right.get.saveBookingModel.addOnsBookingActionState.size shouldBe 2 }
     *
     * "return correct result for CFB-1224 = B and cegFastTrack request is not empty" in {
     * when(mockFeatureAware.cegFastTrackMigrationCreateFlow).thenReturn(true)
     * when(mockFeatureAware.generatePrimaryKeyWithSpecificSeq).thenReturn(false)
     *
     * val baseBookingRelationship = BaseBookingRelationship( sourceBookingId = 19293, targetBookingId = 232132,
     * relationshipStatusId = RelationshipStatuses.Confirmed.id, relationshipTypeId = RelationshipTypes.AddOn.id,
     * recStatus = 1, recCreatedWhen = java.time.LocalDateTime.parse("1970-01-01T00:00:00.000"), recCreatedBy = "",
     * recModifiedWhen = None, recModifiedBy = None, relationshipId = 5734934L )
     *
     * val result = processor.gatherPreSaveResults( request = baseInput, itineraryPreSaveInfo = itineraryInfo,
     * paymentMethodInfo = Some(paymentMethodFromDB), multiProductIds = multiProductMapping, propertyProductsE =
     * propertyResponse, nonPropertyProductsE = Right( Seq( flightSaveStageResponse, vehicleResponse,
     * protectionResponse, activityResponse, priceFreezeResponse, cegFastTrackResponse, protectionAddOnResponse,
     * flightCheckInResponse ) ), addTransientCCResponse = AddTransientCCResponse(), featureAware =
     * Some(mockFeatureAware), bookingRelationships = Seq(baseBookingRelationship) )
     *
     * result.right.get.saveBookingModel.cegFastTrackBookingActionState.size shouldBe 1
     * result.right.get.saveBookingModel.addOnsBookingActionState.size shouldBe 1 }
     *
     * "return correct result for CFB-1224 = B, UNIBF-1068 = A for flight booking" in {
     * when(mockFeatureAware.cegFastTrackMigrationCreateFlow).thenReturn(true)
     * when(mockFeatureAware.isCEGUpsellAddOnV2Enabled).thenReturn(false)
     * when(mockFeatureAware.generatePrimaryKeyWithSpecificSeq).thenReturn(false)
     *
     * val baseBookingRelationship = BaseBookingRelationship( sourceBookingId = 19293, targetBookingId = 232132,
     * relationshipStatusId = RelationshipStatuses.Confirmed.id, relationshipTypeId = RelationshipTypes.AddOn.id,
     * recStatus = 1, recCreatedWhen = java.time.LocalDateTime.parse("1970-01-01T00:00:00.000"), recCreatedBy = "",
     * recModifiedWhen = None, recModifiedBy = None, relationshipId = 5734934L )
     *
     * val input = baseInput.copy(properties = Seq.empty)
     *
     * val result = processor.gatherPreSaveResults( request = input, itineraryPreSaveInfo = itineraryInfo,
     * paymentMethodInfo = Some(paymentMethodFromDB), multiProductIds = multiProductMapping, propertyProductsE =
     * propertyResponse, nonPropertyProductsE = Right( Seq( flightSaveStageResponse, vehicleResponse,
     * protectionResponse, activityResponse, priceFreezeResponse, cegFastTrackResponse, protectionAddOnResponse,
     * flightCheckInResponse ) ), addTransientCCResponse = AddTransientCCResponse(), featureAware =
     * Some(mockFeatureAware), bookingRelationships = Seq(baseBookingRelationship) )
     *
     * result.right.get.saveBookingModel.cegFastTrackBookingActionState.size shouldBe 1
     * result.right.get.saveBookingModel.addOnsBookingActionState.size shouldBe 1 }
     *
     * "return correct result for UNIBF-1068 = A" in {
     * when(mockFeatureAware.isCEGUpsellAddOnV2Enabled).thenReturn(false)
     * when(mockFeatureAware.generatePrimaryKeyWithSpecificSeq).thenReturn(false)
     *
     * val baseBookingRelationship = BaseBookingRelationship( sourceBookingId = 19293, targetBookingId = 232132,
     * relationshipStatusId = RelationshipStatuses.Confirmed.id, relationshipTypeId = RelationshipTypes.AddOn.id,
     * recStatus = 1, recCreatedWhen = java.time.LocalDateTime.parse("1970-01-01T00:00:00.000"), recCreatedBy = "",
     * recModifiedWhen = None, recModifiedBy = None, relationshipId = 5734934L )
     *
     * val result = processor.gatherPreSaveResults( request = baseInput, itineraryPreSaveInfo = itineraryInfo,
     * paymentMethodInfo = Some(paymentMethodFromDB), multiProductIds = multiProductMapping, propertyProductsE =
     * propertyResponse, nonPropertyProductsE = Right( Seq( flightSaveStageResponse, vehicleResponse,
     * protectionResponse, activityResponse, priceFreezeResponse, cegFastTrackResponse, protectionAddOnResponse,
     * flightCheckInResponse ) ), addTransientCCResponse = AddTransientCCResponse(), featureAware =
     * Some(mockFeatureAware), bookingRelationships = Seq(baseBookingRelationship) )
     *
     * result.right.get.saveBookingModel.cegFastTrackBookingActionState.size shouldBe 1
     * result.right.get.saveBookingModel.addOnsBookingActionState.size shouldBe 1 }
     *
     * "return correct result for UNIBF-1068 = B" in { when(mockFeatureAware.isCEGUpsellAddOnV2Enabled).thenReturn(true)
     * when(mockFeatureAware.generatePrimaryKeyWithSpecificSeq).thenReturn(false)
     *
     * val baseBookingRelationship = BaseBookingRelationship( sourceBookingId = 19293, targetBookingId = 232132,
     * relationshipStatusId = RelationshipStatuses.Confirmed.id, relationshipTypeId = RelationshipTypes.AddOn.id,
     * recStatus = 1, recCreatedWhen = java.time.LocalDateTime.parse("1970-01-01T00:00:00.000"), recCreatedBy = "",
     * recModifiedWhen = None, recModifiedBy = None, relationshipId = 5734934L )
     *
     * val result = processor.gatherPreSaveResults( request = baseInput, itineraryPreSaveInfo = itineraryInfo,
     * paymentMethodInfo = Some(paymentMethodFromDB), multiProductIds = multiProductMapping, propertyProductsE =
     * propertyResponse, nonPropertyProductsE = Right( Seq( flightSaveStageResponse, vehicleResponse,
     * protectionResponse, activityResponse, priceFreezeResponse, cegFastTrackResponse, protectionAddOnResponse,
     * flightCheckInResponse ) ), addTransientCCResponse = AddTransientCCResponse(), featureAware =
     * Some(mockFeatureAware), bookingRelationships = Seq(baseBookingRelationship) )
     *
     * result.right.get.saveBookingModel.cegFastTrackBookingActionState.size shouldBe 0
     * result.right.get.saveBookingModel.addOnsBookingActionState.size shouldBe 2 }
     *
     * "return correct result with FlightCheckIn when SKYA-9299 = B" in {
     * when(mockFeatureAware.cegFastTrackMigrationCreateFlow).thenReturn(true)
     * when(mockFeatureAware.isCEGUpsellAddOnV2Enabled).thenReturn(true)
     * when(mockFeatureAware.EnableOnboardCheckIn).thenReturn(true)
     * when(mockFeatureAware.generatePrimaryKeyWithSpecificSeq).thenReturn(false)
     *
     * val baseBookingRelationship = BaseBookingRelationship( sourceBookingId = 19293, targetBookingId = 232132,
     * relationshipStatusId = RelationshipStatuses.Confirmed.id, relationshipTypeId = RelationshipTypes.AddOn.id,
     * recStatus = 1, recCreatedWhen = java.time.LocalDateTime.parse("1970-01-01T00:00:00.000"), recCreatedBy = "",
     * recModifiedWhen = None, recModifiedBy = None, relationshipId = 5734934L )
     *
     * val result = processor.gatherPreSaveResults( request = baseInput, itineraryPreSaveInfo = itineraryInfo,
     * paymentMethodInfo = Some(paymentMethodFromDB), multiProductIds = multiProductMapping, propertyProductsE =
     * propertyResponse, nonPropertyProductsE = Right( Seq( flightSaveStageResponse, vehicleResponse,
     * protectionResponse, activityResponse, priceFreezeResponse, cegFastTrackResponse, protectionAddOnResponse,
     * flightCheckInResponse ) ), addTransientCCResponse = AddTransientCCResponse(), featureAware =
     * Some(mockFeatureAware), bookingRelationships = Seq(baseBookingRelationship) )
     *
     * result.right.get.saveBookingModel.cegFastTrackBookingActionState.size shouldBe 0
     * result.right.get.saveBookingModel.addOnsBookingActionState.size shouldBe 3 // protection + cegUpsell +
     * flightCheckIn }
     *
     * "return correct result for UNIBF-1068 = B, CFB-1224 = A for property booking" in {
     * when(mockFeatureAware.isCEGUpsellAddOnV2Enabled).thenReturn(true)
     * when(mockFeatureAware.cegFastTrackMigrationCreateFlow).thenReturn(false)
     * when(mockFeatureAware.generatePrimaryKeyWithSpecificSeq).thenReturn(false)
     *
     * val baseBookingRelationship = BaseBookingRelationship( sourceBookingId = 19293, targetBookingId = 232132,
     * relationshipStatusId = RelationshipStatuses.Confirmed.id, relationshipTypeId = RelationshipTypes.AddOn.id,
     * recStatus = 1, recCreatedWhen = java.time.LocalDateTime.parse("1970-01-01T00:00:00.000"), recCreatedBy = "",
     * recModifiedWhen = None, recModifiedBy = None, relationshipId = 5734934L )
     *
     * val input = baseInput.copy(flights = Seq.empty)
     *
     * val result = processor.gatherPreSaveResults( request = input, itineraryPreSaveInfo = itineraryInfo,
     * paymentMethodInfo = Some(paymentMethodFromDB), multiProductIds = multiProductMapping, propertyProductsE =
     * propertyResponse, nonPropertyProductsE = Right( Seq( flightSaveStageResponse, vehicleResponse,
     * protectionResponse, activityResponse, priceFreezeResponse, cegFastTrackResponse, protectionAddOnResponse,
     * flightCheckInResponse ) ), addTransientCCResponse = AddTransientCCResponse(), featureAware =
     * Some(mockFeatureAware), bookingRelationships = Seq(baseBookingRelationship) )
     *
     * result.right.get.saveBookingModel.cegFastTrackBookingActionState.size shouldBe 1
     * result.right.get.saveBookingModel.addOnsBookingActionState.size shouldBe 1 } */
  }

  "updatedItineraryContextWithSaveResult" should {
    val stage = new MultiProductPreSaveStage(
      mock[PaymentMethodRepository],
      mock[FlightBookingRepository],
      mock[MultiProductRepository],
      mock[MPMasterMapper],
      mock[
        ProductPreSaveStage[MultiProductPreSaveRequest, RoomInfo, PropertySaveStageResponse, EmptyProductReservedIds]
      ],
      mock[NonHotelPreSaveFacade],
      mock[CreditCardApiLocalProxyV2],
      mock[HadoopMessagingService],
      mock[BookingRelationshipHelper]
    )(mock[KillSwitches], mock[RequestContext])

    val roomIdentifier =
      propertySaveStageResponse.propertyCreationLocal.reservedIds.product.info.roomIdentifier.getOrElse("room-1")
    val roomBookingId     = propertySaveStageResponse.bookingAction.bookingId
    val flightItineraryId = flightSaveStageResponse.productTokenKey.getOrElse("flight-1")
    val flightBookingId   = flightSaveStageResponse.bookingAction.bookingId

    "update the itinerary context with the correct booking IDs" in {
      val propertySaveResults    = Seq(propertySaveStageResponse)
      val nonPropertySaveResults = Seq(flightSaveStageResponse)

      val itineraryContext = ItineraryContext(
        productGroups = Seq(
          ProductGroupItem.of(
            productId = roomIdentifier,
            bookingId = None,
            productGroupId = "package-1",
            productGroupType = ProductGroupType.Package
          ),
          ProductGroupItem.of(
            productId = flightItineraryId,
            bookingId = None,
            productGroupId = "package-1",
            productGroupType = ProductGroupType.Package
          )
        )
      )

      val updatedContext =
        stage.updatedItineraryContextWithSaveResult(itineraryContext, propertySaveResults, nonPropertySaveResults)

      updatedContext.productGroups should have size 2

      every(
        updatedContext.productGroups
          .filter(_.productId == roomIdentifier)
          .map(_.bookingId)
      ) shouldBe roomBookingId

      every(
        updatedContext.productGroups
          .filter(_.productId == flightItineraryId)
          .map(_.bookingId)
      ) shouldBe flightBookingId
    }

    "update the itinerary context with the correct booking IDs when no matching booking ID in Property Save Stage Response or Non Property Save Stage Response" in {
      val propertySaveResults    = Seq(propertySaveStageResponse)
      val nonPropertySaveResults = Seq(flightSaveStageResponse)

      val itineraryContext = ItineraryContext(
        productGroups = Seq(
          ProductGroupItem(
            "random-room-identifier",
            productGroupId = "package-1",
            productGroupType = ProductGroupType.Package
          ),
          ProductGroupItem(
            "random-flight-itinerary-id",
            productGroupId = "package-1",
            productGroupType = ProductGroupType.Package
          )
        )
      )

      val updatedContext =
        stage.updatedItineraryContextWithSaveResult(itineraryContext, propertySaveResults, nonPropertySaveResults)

      updatedContext.productGroups should have size 2

      every(updatedContext.productGroups.map(_.bookingId)) shouldBe empty
    }

    "update the itinerary context with the correct bookingIDs Hackerfare usecase" in {
      val nonPropertySaveResults = Seq(hfFlightSaveStageResponse, hfFlightSaveStageResponse2)
      val expectedBookingIds     = nonPropertySaveResults.flatMap(_.bookingAction.bookingId)

      val itineraryContext = ItineraryContext(
        productGroups = Seq(
          ProductGroupItem.of(
            productId = flightItineraryId,
            bookingId = None,
            productGroupId = "hackerfare-1",
            productGroupType = ProductGroupType.HackerFare
          ),
          ProductGroupItem(
            productId = flightItineraryId,
            bookingId = None,
            productGroupId = "hackerfare-1",
            productGroupType = ProductGroupType.HackerFare
          )
        )
      )

      val updatedContext =
        stage.updatedItineraryContextWithSaveResult(itineraryContext, Seq.empty, nonPropertySaveResults)

      updatedContext.productGroups should have size 2
      updatedContext.productGroups.map(_.bookingId.get) should equal(expectedBookingIds)
    }
  }
}
