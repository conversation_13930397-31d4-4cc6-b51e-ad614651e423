package com.agoda.bapi.creation.service.stage

import com.agoda.bapi.common.model.{User<PERSON><PERSON><PERSON>t<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, WhiteLabelInfo}
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.creation.CreateBookingHelper
import com.agoda.bapi.creation.model.RequestWithProducts
import org.mockito.Mockito._
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{EitherValues, OptionValues}
import org.scalatestplus.mockito.MockitoSugar
import com.typesafe.config.ConfigFactory
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.creation.config.TestBookingConfigProvider
import com.softwaremill.quicklens._
import org.scalatest.prop.TableDrivenPropertyChecks._
import com.agoda.bapi.common.message.creation.AffiliateModel
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.mpb.common.header.AgHttpHeader

class TestBookingModificationStageSpec
    extends AsyncWordSpec
    with CreateBookingHelper
    with EitherValues
    with OptionValues
    with MockitoSugar
    with Matchers {

  val globalConfig = ConfigFactory.parseString("""
    test-booking {
      emails = ["<EMAIL>"],
      whitelistMemberIds = [286479490, 286479491]
    }
  """)

  val userContext  = UserContextMock.value.copy(requestOrigin = "TH")
  val featureAware = mock[FeatureAware]
  when(featureAware.enableIsTestBooking).thenReturn(true)

  val requestContext: RequestContext =
    defaultRequestContext
      .copy(featureAware = Some(featureAware))
      .copy(userContext = Some(userContext))
      .modify(_.whiteLabelInfo.whiteLabelId)
      .setTo(WhiteLabel.CitiUSUat1)

  val baseRequest = RequestWithProducts(
    baseReq,
    requestContext,
    Map.empty,
    Seq.empty,
    None,
    Seq.empty,
    BookingFlow.Package,
    requestV2 = None
  )

  val provider = new TestBookingConfigProvider(globalConfig)
  val config   = provider.get()
  val process  = new TestBookingModificationStage(config)

  "process on test booking" should {
    val successes = Table(
      ("memberId", "email", "whitelabelId", "isTestBookingInMPBEConfig", "doNotOverrideCustomerEmailWhiteLabel"),
      (1, "<EMAIL>", WhiteLabel.CitiUSUat1, true, false),
      (2, "<EMAIL>", WhiteLabel.CitiUSUat1, true, false),
      (1, "<EMAIL>", WhiteLabel.CitiUSUat1, true, false),
      (2, "<EMAIL>", WhiteLabel.CitiUSUat1, true, false),
      (5, "<EMAIL>", WhiteLabel.AffiliateSandbox, true, false),
      (1, "<EMAIL>", WhiteLabel.Agoda, false, false),
      (2, "<EMAIL>", WhiteLabel.Agoda, false, false),
      (1, "<EMAIL>", WhiteLabel.AgodaUat, false, true)
    )

    forAll(successes) {
      (memberId, email, whitelabelId, isTestBookingInMPBEConfig, doNotOverrideCustomerEmailWhiteLabel) =>
        s"return isTestBooking true (memberId: $memberId, email:$email, whitelabelId: $whitelabelId)" in {
          val request = baseRequest
            .modify(_.request.userContext.each.memberId)
            .setTo(Some(memberId))
            .modify(_.request.customer.email)
            .setTo(email)
            .modify(_.requestContext.whiteLabelInfo)
            .setTo(
              WhiteLabelInfo(
                whiteLabelId = whitelabelId,
                feature = requestContext.whiteLabelInfo.feature,
                token = requestContext.whiteLabelInfo.token,
                instantBookingEnabled = requestContext.whiteLabelInfo.instantBookingEnabled,
                isFeatureEnabledFunction = (_, _, _, _, _) => doNotOverrideCustomerEmailWhiteLabel
              )
            )
            .modify(_.requestContext.whiteLabelInfo.feature.mpbe.isForceTestBooking)
            .setTo(Some(isTestBookingInMPBEConfig))

          process.process(request).map { result =>
            result.value.request.isTestBooking shouldBe true
            result.value.request.customer.email shouldBe {
              if (!doNotOverrideCustomerEmailWhiteLabel) "<EMAIL>" else email
            }
          }
        }
    }

    val fails = Table(
      ("memberId", "email", "whitelabelId", "isTestBookingInMPBEConfig"),
      (286479490, "<EMAIL>", WhiteLabel.CitiUSUat1, true),
      (286479491, "<EMAIL>", WhiteLabel.CitiUSUat1, true),
      (286479490, "<EMAIL>", WhiteLabel.CitiUSUat1, true),
      (286479491, "<EMAIL>", WhiteLabel.CitiUSUat1, true),
      (1, "<EMAIL>", WhiteLabel.Agoda, false),
      (2, "<EMAIL>", WhiteLabel.Agoda, false)
    )

    forAll(fails) { (memberId, email, whitelabelId, isTestBookingInMPBEConfig) =>
      s"return isTestBooking false (memberId: $memberId, email: $email, whitelabelId: $whitelabelId)" in {
        val request = baseRequest
          .modify(_.request.userContext.each.memberId)
          .setTo(Some(memberId))
          .modify(_.requestContext.whiteLabelInfo.whiteLabelId)
          .setTo(whitelabelId)
          .modify(_.requestContext.whiteLabelInfo.feature.mpbe.isForceTestBooking)
          .setTo(Some(isTestBookingInMPBEConfig))

        val originalEmail = request.getCustomer.email

        process.process(request).map { result =>
          result.value.request.isTestBooking shouldBe false
          result.value.request.customer.email shouldBe originalEmail
        }
      }
    }

    val validateTestBooking = Table(
      ("memberId", "email", "whitelabelId", "environment", "expected"),
      (1, "<EMAIL>", WhiteLabel.CitiUSUat1, "uat", true),
      (2, "<EMAIL>", WhiteLabel.CitiUSUat1, "", false),
      (1, "<EMAIL>", WhiteLabel.Agoda, "uat", true),
      (2, "<EMAIL>", WhiteLabel.Agoda, "", false)
    )

    forAll(validateTestBooking) { (memberId, email, whitelabelId, environment, expected) =>
      s"return isTestBooking $expected using  (memberId: $memberId, email: $email, whitelabelId: $whitelabelId)" in {
        val request = baseRequest
          .modify(_.request.userContext.each.memberId)
          .setTo(Some(memberId))
          .modify(_.requestContext.whiteLabelInfo.whiteLabelId)
          .setTo(whitelabelId)
          .modify(_.requestContext.agHeaders)
          .setTo(Seq(AgHttpHeader("ag-env", environment)))

        process.process(request).map { result =>
          result.value.request.isTestBooking shouldBe expected
        }
      }
    }
  }

  "process on affiliate booking" should {
    val baseAffiliateRequest = baseRequest
      .modify(_.request.customer.email)
      .setTo("<EMAIL>")
      .modify(_.request.isTestBooking)
      .setTo(true)
      .modify(_.requestContext.whiteLabelInfo.whiteLabelId)
      .setTo(WhiteLabel.Agoda)

    "return isTestBooking true" in {
      val request = baseAffiliateRequest
        .modify(_.request.affiliateModel)
        .setTo(Some(AffiliateModel.B2B))

      process.process(request).map { result =>
        result.value.request.isTestBooking shouldBe true
        result.value.getCustomer.email shouldBe "<EMAIL>"
      }
    }

    "return isTestBooking false" in {
      val request = baseAffiliateRequest
        .modify(_.request.affiliateModel)
        .setTo(Some(AffiliateModel.NET))

      val originalEmail = request.getCustomer.email

      process.process(request).map { result =>
        result.value.request.isTestBooking shouldBe false
        result.value.getCustomer.email shouldBe originalEmail
      }
    }
  }

}
