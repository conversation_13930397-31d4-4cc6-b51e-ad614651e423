package com.agoda.bapi.creation.service.stage

import com.agoda.bapi.common.config.KillSwitches
import com.agoda.bapi.common.handler.{MeasurementsContext, RequestContext}
import com.agoda.bapi.common.message.CustomerRiskStatus
import com.agoda.bapi.common.message.creation.{BookingElement, CreateBookingResponse, DuplicateBooking, HotelGuest}
import com.agoda.bapi.common.model.product.{BookingFlow, BookingRequestTypeResolver}
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.{ProductInfo, TestSugar}
import com.agoda.bapi.creation.config.DuplicateConfig
import com.agoda.bapi.creation.model.db.DatabaseEnum
import com.agoda.bapi.creation.model.multi.{Product, ValidateProductRequest}
import com.agoda.bapi.creation.repository.{CreationCdbRepository, WorkflowRepository}
import com.agoda.bapi.creation.service.{BookingActionMessageService, DuplicateCheckService, HadoopMessagingService, TPRMService}
import com.agoda.bapi.creation.{CreateMultiBookingHelper, ExternalDIHelper}
import com.agoda.mpb.common.BookingType
import com.agoda.mpb.common.errors.ErrorCode
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito._
import org.mockito.ArgumentMatchers
import org.scalatest.BeforeAndAfterEach
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future

class PropertyValidateStageTest
    extends AsyncWordSpec
    with Matchers
    with MockitoSugar
    with BeforeAndAfterEach
    with CreateMultiBookingHelper
    with ExternalDIHelper
    with TestSugar {

  val duplicateCheckService       = mock[DuplicateCheckService]
  val tprmService                 = mock[TPRMService]
  val bookingActionMessageService = mock[BookingActionMessageService]
  val featureAware                = mock[FeatureAware]
  val workflowRepository          = mock[WorkflowRepository]
  val hadoopMessaging             = mock[HadoopMessagingService]
  val duplicateConfig             = DuplicateConfig(checkDaysAgo = 7, salt = "salt")
  val killSwitches                = mock[KillSwitches]
  val creationCdbRepository       = mock[CreationCdbRepository]
  when(hadoopMessaging.sendBapiCreateFactLogMessage(any, any[CreateBookingResponse], any[String]))
    .thenReturn(Future.successful(()))
  when(creationCdbRepository.getMasterHotelIdFromJTBMapping(any())).thenReturn(Future.successful(Some(1L)))
  val roomInfo =
    baseRoomInfo
      .get("1")
      .map(r => r.copy(bapiBooking = r.bapiBooking.copy(productTokenKey = baseProductTokenKey)))
      .get
  val processor =
    spy(
      new PropertyValidateStageTester(
        duplicateCheckService,
        tprmService,
        hadoopMessaging,
        duplicateConfig,
        creationCdbRepository
      )(killSwitches) {
        override def withMeasureAndLogWithContext[T](
            context: MeasurementsContext
        )(metricName: String, additionalTags: Map[String, String] = Map.empty)(f: Future[T]): Future[T] = f
      }
    )
  val baseRequest =
    ValidateProductRequest(
      baseMultiProductReq,
      baseRequestContext.copy(featureAware = Some(featureAware)),
      Product(BookingType.CreditCard, roomInfo, None)
    )
  val duplicatedBooking = DuplicateBooking(BookingElement.Hotel, 1, DateTime.parse("2000-01-01T00:00:00.000Z"))
  val duplicatedResult = Seq(
    duplicatedBooking.copy(bookingId = 3),
    duplicatedBooking.copy(bookingId = 4)
  )

  override def beforeEach() = {
    reset(duplicateCheckService, tprmService, featureAware, workflowRepository, hadoopMessaging)
  }

  "process" should {
    "return request correctly with duplicate check" in {
      val request = baseRequest.copy(request = baseRequest.request.copy(allowDuplicateBooking = false))
      when(duplicateCheckService.findPropertyDuplicates(*, *, *)(*, *))
        .thenReturn(Future.successful((Nil, DatabaseEnum.MDB)))

      when(tprmService.findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext]))
        .thenReturn(Future.successful(Nil))
      processor.process(request).map { result =>
        verify(duplicateCheckService, times(1)).findPropertyDuplicates(*, *, *)(*, *)
        verify(tprmService, times(1)).findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext])
        result shouldBe Right(request)
      }
    }

    "return request correctly without duplicate check" in {
      val request = baseRequest.copy(request = baseRequest.request.copy(allowDuplicateBooking = true))
      when(tprmService.findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext]))
        .thenReturn(Future.successful(Nil))

      processor.process(request).map { result =>
        verify(duplicateCheckService, never)
          .findPropertyDuplicates(any(), any(), any())(any(), any())
        verify(tprmService, times(1)).findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext])
        result shouldBe Right(request)
      }
    }

    "failed when duplicate check not success" in {
      val request = baseRequest.copy(request = baseRequest.request.copy(allowDuplicateBooking = false))
      when(duplicateCheckService.findPropertyDuplicates(*, *, *)(*, *))
        .thenReturn(Future.successful((duplicatedResult, DatabaseEnum.MDB)))

      when(tprmService.findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext]))
        .thenReturn(Future.successful(Nil))

      processor.process(request).map { result =>
        verify(duplicateCheckService, times(1))
          .findPropertyDuplicates(any(), any(), any())(any(), any())
        verify(tprmService, times(1)).findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext])
        verify(processor, times(1))
          .withMeasureAndHadoopLog(
            ArgumentMatchers.eq(duplicatedResult),
            ArgumentMatchers.eq(request),
            anyString(),
            any[CreateBookingResponse](),
            any[Option[String]](),
            any[Option[FeatureAware]]
          )(any[MeasurementsContext]())
        result shouldBe Left(CreateBookingResponse.duplicate(duplicatedResult))
      }
    }

    "Check property duplicates only once in case of non-redirect booking" in {
      val request = baseRequest.copy(
        request = baseRequest.request.copy(allowDuplicateBooking = false),
        product = Product(BookingType.CreditCard, roomInfo, None)
      )
      when(duplicateCheckService.findPropertyDuplicates(*, *, *)(*, *))
        .thenReturn(Future.successful((Nil, DatabaseEnum.MDB)))
      when(tprmService.findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext]))
        .thenReturn(Future.successful(Nil))

      processor.process(request).map { result =>
        verify(duplicateCheckService, times(1))
          .findPropertyDuplicates(any(), any(), any())(any(), any())
        result shouldBe Right(request)
      }
    }

    "Check property duplicates only once in case of redirect booking" in {
      val request = baseRequest.copy(
        request = baseRequest.request.copy(allowDuplicateBooking = false),
        product = Product(BookingType.RedirectCard, roomInfo, None)
      )
      when(duplicateCheckService.findPropertyDuplicates(*, *, *)(*, *))
        .thenReturn(Future.successful((duplicatedResult, DatabaseEnum.MDB)))
      when(tprmService.findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext]))
        .thenReturn(Future.successful(Nil))

      processor.process(request).map { result =>
        verify(duplicateCheckService, times(1))
          .findPropertyDuplicates(any(), any(), any())(any(), any())
        result shouldBe Left(CreateBookingResponse.duplicate(duplicatedResult))
      }
    }

    "failed when duplicate check during 3ds/NoCvc processing" in {
      when(duplicateCheckService.findPropertyDuplicates(*, *, *)(*, *))
        .thenReturn(Future.successful((duplicatedResult, DatabaseEnum.MDB)))
      when(tprmService.findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext]))
        .thenReturn(Future.successful(Nil))

      val request = baseRequest.copy(request = baseRequest.request.copy(allowDuplicateBooking = false))
      processor.process(request).map { result =>
        verify(duplicateCheckService, times(1))
          .findPropertyDuplicates(any(), any(), any())(any(), any())
        verify(tprmService, times(1)).findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext])
        result shouldBe Left(CreateBookingResponse.duplicate(duplicatedResult))
      }
    }

    "return Left(duplicate booking) when duplicateCheckService returns some duplicate bookings" in {
      when(duplicateCheckService.findPropertyDuplicates(*, *, *)(*, *))
        .thenReturn(Future.successful((duplicatedResult, DatabaseEnum.MDB)))
      when(tprmService.findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext]))
        .thenReturn(Future.successful(Nil))

      val request = baseRequest.copy(request = baseRequest.request.copy(allowDuplicateBooking = false))
      processor.process(request).map { result =>
        verify(duplicateCheckService, times(1))
          .findPropertyDuplicates(any(), any(), any())(any(), any())
        verify(tprmService, times(1)).findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext])
        result shouldBe Left(CreateBookingResponse.duplicate(duplicatedResult))
      }
    }

    "return Right(no duplicate booking) when duplicateCheckService returns no duplicate bookings" in {
      when(duplicateCheckService.findPropertyDuplicates(*, *, *)(*, *))
        .thenReturn(Future.successful((Seq.empty, DatabaseEnum.MDB)))
      when(tprmService.findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext]))
        .thenReturn(Future.successful(Nil))

      val request = baseRequest.copy(request = baseRequest.request.copy(allowDuplicateBooking = false))
      processor.process(request).map { result =>
        verify(duplicateCheckService, times(1))
          .findPropertyDuplicates(any(), any(), any())(any(), any())
        verify(tprmService, times(1)).findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext])
        result shouldBe Right(request)
      }
    }

    "failed when terrorist check not success" in {
      val request = baseRequest.copy(request = baseRequest.request.copy(allowDuplicateBooking = true))

      when(duplicateCheckService.findPropertyDuplicates(*, *, *)(*, *))
        .thenReturn(Future.successful((Nil, DatabaseEnum.MDB)))
      when(tprmService.findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext]))
        .thenReturn(Future.successful(Seq(CustomerRiskStatus.Risk)))

      processor.process(request).map { result =>
        verify(duplicateCheckService, never).findPropertyDuplicates(*, *, *)(*, *)
        verify(tprmService, times(1)).findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext])
        result shouldBe Left(CreateBookingResponse.error(ErrorCode.TerroristIdentifiedInBooking))
      }
    }

    "failed when terrorist check not success (incomplete data)" in {
      val request = baseRequest.copy(request = baseRequest.request.copy(allowDuplicateBooking = true))

      when(duplicateCheckService.findPropertyDuplicates(*, *, *)(*, *))
        .thenReturn(Future.successful((Nil, DatabaseEnum.MDB)))

      when(tprmService.findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext]))
        .thenReturn(Future.successful(Seq(CustomerRiskStatus.AskMoreInfo)))

      processor.process(request).map { result =>
        verify(duplicateCheckService, never).findPropertyDuplicates(*, *, *)(*, *)
        verify(tprmService, times(1)).findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext])
        result shouldBe Left(CreateBookingResponse.error(ErrorCode.TerroristIdentifiedInBooking))
      }
    }

    "failed when terrorist check not success (single property)" in {
      val request = baseRequest.copy(
        request = baseRequest.request.copy(
          allowDuplicateBooking = true,
          products = baseRequest.request.products
            .copy(
              flightItems = None,
              propertyItems = baseRequest.request.products.propertyItems.headOption,
              carItems = None
            )
        )
      )

      BookingRequestTypeResolver.getFlowType(request.request) shouldBe BookingFlow.SingleProperty
      when(duplicateCheckService.findPropertyDuplicates(*, *, *)(*, *))
        .thenReturn(Future.successful((Nil, DatabaseEnum.MDB)))
      when(tprmService.findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext]))
        .thenReturn(Future.successful(Seq(CustomerRiskStatus.AskMoreInfo)))

      processor.process(request).map { result =>
        verify(duplicateCheckService, never).findPropertyDuplicates(*, *, *)(*, *)
        verify(tprmService, times(0)).findPropertyGuestTerrorists(any[Seq[HotelGuest]])(any[RequestContext])
        result shouldBe Right(request)
      }
    }

    "log hadoop when duplicateCheck throws an exception" in {
      val mockException    = new Exception()
      val expectedResponse = CreateBookingResponse.technical(ErrorCode.UnexpectedInternalError, Some(mockException))

      when(duplicateCheckService.findPropertyDuplicates(*, *, *)(*, *))
        .thenReturn(Future.failed(mockException))

      processor.process(baseRequest).map { result =>
        verify(hadoopMessaging, times(1)).sendBapiCreateFactLogMessage(any, any[CreateBookingResponse], any[String])
        result shouldBe Left(expectedResponse)
      }
    }
  }
}

class PropertyValidateStageTester(
    duplicateCheckService: DuplicateCheckService,
    tprmService: TPRMService,
    hadoopMessagingService: HadoopMessagingService,
    duplicateConfig: DuplicateConfig,
    creationCdbRepository: CreationCdbRepository
)(implicit killSwitches: KillSwitches)
    extends PropertyValidateStage(
      duplicateCheckService: DuplicateCheckService,
      tprmService: TPRMService,
      hadoopMessagingService: HadoopMessagingService,
      duplicateConfig: DuplicateConfig,
      creationCdbRepository: CreationCdbRepository
    )(killSwitches) {
  // change access to public for verify
  override def withMeasureAndHadoopLog[T <: ProductInfo](
      duplicateBookings: Seq[DuplicateBooking],
      request: ValidateProductRequest[T],
      stage: String,
      response: CreateBookingResponse,
      source: Option[String] = None,
      featureAware: Option[FeatureAware]
  )(implicit measurementCtx: MeasurementsContext): Future[Unit] =
    super.withMeasureAndHadoopLog(duplicateBookings, request, stage, response)
}
