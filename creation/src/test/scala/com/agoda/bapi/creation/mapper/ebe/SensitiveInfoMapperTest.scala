package com.agoda.bapi.creation.mapper.ebe

import com.agoda.abspnx.client.http.request.{BookingRequest => AbsBookingRequest}
import com.agoda.abspnx.client.models.abs._
import com.agoda.bapi.common.message.creation.LanguageScriptType.Kanji
import com.agoda.bapi.common.message.creation.{ActivityPax, BirthInfo, BookingPaymentV2, CarDriver, CreateBookingRequestV2, CreditCardDetail, CreditCardExpiration, CreditCardV2, Customer, CustomerV2, FlightPax, HotelGuest, IssuingBank, LanguageScriptType, LocalizedNameV2, PaxType, PhoneContact, ProductsV2}
import com.agoda.bapi.common.model.UserContext
import com.agoda.bapi.common.util.Json4sFormat
import com.agoda.bapi.creation.util.serializer.PIISerializer
import com.agoda.bapi.creation.{<PERSON>reate<PERSON><PERSON>ing<PERSON><PERSON><PERSON>, CreateFlightBookingHelper}
import com.agoda.fraud.proto.HotelFraudCheckRequest
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.agoda.paymentapiv2.client.v2.common.model.{CustomerInfo, Guest}
import org.joda.time.LocalDate
import org.scalatest.matchers.must.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.{AppendedClues, OptionValues}

import java.time.ZonedDateTime

class SensitiveInfoMapperTest
    extends AnyWordSpec
    with Matchers
    with OptionValues
    with AppendedClues
    with CreateFlightBookingHelper
    with CreateBookingHelper {
  implicit val formats = Json4sFormat.defaultJsonFormats + PIISerializer

  "SensitiveInfoMapper sanitizing PII info" should {
    "mask GuestInfo " in {
      val maskedGuestInfo =
        """{"firstName":"*****","lastName":"*******","phoneNumber":"***","email":"************","nationality":"IN","age":0,"sex":null,"guestType":1,"guestContactType":1,"stateId":0,"localizedNames":[{"languageId":6,"script":1,"firstName":"***","lastName":"****"}]}"""
      val json = SensitiveInfoMapper.createJson(
        GuestInfo(
          Some("Raman"),
          Some("Raghvan"),
          Some("123"),
          Some("<EMAIL>"),
          Some("IN"),
          Some(23),
          Some(com.agoda.abspnx.client.models.abs.Sex.Male),
          guestType = Some(GuestType.Adult),
          guestContactType = Some(GuestContactType.Booker),
          stateId = Some(1),
          localizedNames = Seq(
            LocalizedName(
              languageId = 6,
              script = 1,
              firstName = "テスト",
              lastName = "ユーザー"
            )
          )
        )
      )
      json mustBe maskedGuestInfo
    }

    "mask HotelGuest " in {
      val maskedHotelGuest =
        """{"guestNo":1,"title":"**","firstname":"*******","lastname":"********","primary":true,"middlename":"","suffix":"","isExtrabed":false,"age":0,"nationalityId":0,"isAdult":true,"email":"*****************","gender":"*","phoneFormat":"*********","emergencyPhone":"*********","postcode":"*****","city":"*******","area":"*********","address":"**********************************","stateId":0,"localizedNames":[{"languageId":6,"scriptId":3,"firstname":"***","lastname":"****"}],"citizenshipId":"************"}"""
      val json = SensitiveInfoMapper.createJson(
        HotelGuest(
          1,
          "Mr",
          "Raghvan",
          "LastName",
          true,
          "",
          "",
          false,
          0,
          0,
          isAdult = true,
          email = Some("<EMAIL>"),
          gender = Some("F"),
          phoneFormat = Some("123456789"),
          emergencyPhone = Some("123456789"),
          postcode = Some("99999"),
          city = Some("Bangkok"),
          area = Some("Pathumwan"),
          address = Some("999/9 The offices at central World"),
          stateId = Some(1),
          localizedNames = Some(
            Seq(
              com.agoda.bapi.common.message.creation.LocalizedName(
                languageId = 6,
                scriptId = LanguageScriptType.Kana,
                firstname = "テスト",
                lastname = "ユーザー"
              )
            )
          ),
          citizenshipId = Some("123456789012")
        )
      )
      json mustBe maskedHotelGuest
    }

    "mask UserContext " in {
      val maskedUserContext =
        """{"languageId":0,"requestOrigin":"**","currency":"thb","nationalityId":0,"isLoggedInUser":false,"clientIp":"*********","capiToken":"*********"}"""
      val json = SensitiveInfoMapper.createJson(
        UserContext(
          languageId = 1,
          requestOrigin = "th",
          currency = "thb",
          nationalityId = 0,
          experimentData = None,
          clientIp = Some("127.0.0.1"),
          capiToken = Some("capiToken")
        )
      )
      json mustBe maskedUserContext
    }

    "mask FlightPax " in {
      val maskedFlightPax =
        """{"id":0,"guestNo":1,"title":"","firstname":"***","lastname":"*****","primary":true,"middlename":"******","birthDate":"1970-01-01","nationalityId":0,"isAdult":true,"gender":"*","passportNumber":"*********","passportExpires":"1970-01-01","passportCountryId":0,"shouldSaveFavorite":false,"knownTravelerNumber":"*********"}"""
      val json = SensitiveInfoMapper.createJson(
        FlightPax(
          guestNo = 1,
          firstname = "Ook",
          lastname = "Sarit",
          primary = true,
          middlename = "Flight",
          birthDate = LocalDate.parse("1988-01-14"),
          nationalityId = 106,
          gender = "M",
          passportNumber = Some("*********"),
          passportExpires = Some(LocalDate.parse("2022-01-14")),
          passportCountryId = Some(106),
          knownTravelerNumber = Some("9876543a1")
        )
      )
      json mustBe maskedFlightPax
    }
  }

  "SensitiveInfoMapper sanitizing payment-api sensitive data" should {
    "mask CreditCardInfo " in {
      val maskedCreditCardInfo =
        """{"ccv":"***","cardNumber":"********","expireMonth":"**","expireYear":"****","holderName":"************","cardType":null}"""
      val json = SensitiveInfoMapper.createJson(
        CreditCardInfo(
          Some("123"),
          "********",
          "10",
          "2020",
          "Rahul Dravid",
          com.agoda.abspnx.client.models.abs.CardType.Master
        )
      )
      json mustBe maskedCreditCardInfo
    }

    "mask CreditCardDetail " in {
      val maskedCreditCardInfo =
        """{"ccofId":0,"cardHolderName":"************","creditCardNumber":"********","expiryDate":{"month":0,"year":0},"securityCode":"***","billingAddress1":"*****","billingAddress2":"*****","billingCity":"****","billingState":"*****","phoneNumber":"**********","issuingBank":{"name":"****"}}"""
      val json = SensitiveInfoMapper.createJson(
        CreditCardDetail(
          ccofId = Some(11L),
          cardHolderName = Some("Rahul Dravid"),
          creditCardNumber = Some("********"),
          expiryDate = Some(CreditCardExpiration(10, 2099)),
          securityCode = Some("124"),
          billingAddress1 = Some("bill1"),
          billingAddress2 = Some("bill2"),
          billingCity = Some("city"),
          billingState = Some("state"),
          billingCountryId = Some(10),
          billingPostalCode = Some("11001"),
          phoneNumber = Some("**********"),
          issuingBank = Some(IssuingBank("City"))
        )
      )
      json mustBe maskedCreditCardInfo
    }

    "mask CreditCardDetail if it is a property" in {
      val maskedCreditCardInfo =
        """{"detail":{"ccofId":0,"cardHolderName":"************","creditCardNumber":"********","expiryDate":{"month":0,"year":0},"securityCode":"***","billingAddress1":"*****","billingAddress2":"*****","billingCity":"****","billingState":"*****","phoneNumber":"**********","issuingBank":{"name":"*******"}},"shouldSave":true}"""
      val json = SensitiveInfoMapper.createJson(
        CreditCardV2(
          detail = Some(
            CreditCardDetail(
              ccofId = Some(11L),
              cardHolderName = Some("Rahul Dravid"),
              creditCardNumber = Some("********"),
              expiryDate = Some(CreditCardExpiration(10, 2099)),
              securityCode = Some("124"),
              billingAddress1 = Some("bill1"),
              billingAddress2 = Some("bill2"),
              billingCity = Some("city"),
              billingState = Some("state"),
              billingCountryId = Some(10),
              billingPostalCode = Some("11001"),
              phoneNumber = Some("**********"),
              issuingBank = Some(IssuingBank("My Bank"))
            )
          ),
          shouldSave = true,
          payment3DS = None
        )
      )
      json mustBe maskedCreditCardInfo
    }

    "mask CreateV2 CreditCardInfo if it is a property" in {
      val maskedCreditCardInfo =
        """{"detail":{"ccofId":0,"cardHolderName":"************","creditCardNumber":"********","expiryDate":{"month":0,"year":0},"securityCode":"***","billingAddress1":"*****","billingAddress2":"*****","billingCity":"****","billingState":"*****","phoneNumber":"**********","issuingBank":{"name":"*******"}},"shouldSave":true}"""
      val cc = CreditCardV2(
        detail = Some(
          CreditCardDetail(
            ccofId = Some(11L),
            cardHolderName = Some("Rahul Dravid"),
            creditCardNumber = Some("********"),
            expiryDate = Some(CreditCardExpiration(10, 2099)),
            securityCode = Some("124"),
            billingAddress1 = Some("bill1"),
            billingAddress2 = Some("bill2"),
            billingCity = Some("city"),
            billingState = Some("state"),
            billingCountryId = Some(10),
            billingPostalCode = Some("11001"),
            phoneNumber = Some("**********"),
            issuingBank = Some(IssuingBank("My Bank"))
          )
        ),
        shouldSave = true,
        payment3DS = None
      )
      val createRequest = CreateBookingRequestV2(
        agentAssist = None,
        customer = CustomerV2("ab", "zx", "<EMAIL>", 1),
        payment = BookingPaymentV2(
          PaymentMethod.Visa,
          Some(cc)
        ),
        products = ProductsV2(),
        affiliate = None,
        instantBooking = None,
        fraudInfo = None,
        bookingToken = None,
        userContext = None,
        attributions = None,
        referralUrl = None,
        trackingInfo = None,
        correlationId = None
      )

      val json = SensitiveInfoMapper.createJson(createRequest)
      json.contains(maskedCreditCardInfo) mustBe true
    }

    "mask PaymentAPIV2 CreditCardInfo " in {
      val maskedPaymentApiV2CreditCardInfo =
        """{"CCId":123456,"CardHolderName":"***************","CardNumber":"************","CardExpMonth":"**","CardExpYear":"****","Cvv":"***"}"""
      val json = SensitiveInfoMapper.createJson(
        com.agoda.paymentapiv2.client.v2.common.model.CreditCardInfo(
          Some(123456),
          Some("CardHolder Name"),
          Some("411111111111"),
          Some("10"),
          Some("2020"),
          Some("123")
        )
      )
      json mustBe maskedPaymentApiV2CreditCardInfo
    }

    "mask PaymentAPIV2 Guest " in {
      val maskedPaymentApiV2GuestInfo =
        """{"FirstName":"****","LastName":"****"}"""
      val json =
        SensitiveInfoMapper.createJson(com.agoda.paymentapiv2.client.v2.common.model.Guest(Some("test"), Some("user")))
      json mustBe maskedPaymentApiV2GuestInfo
    }

    "mask PaymentAPIV2 CustomerInfo " in {
      val maskedPaymentApiV2CustomerInfo =
        """{"Name":"*****","LastName":"*******","PhoneNo":"********","PhoneCountryCode":"***","Email":"*************","IdCardNumber":"***","TaxId":"***","CountryCode":"***","PostalCode":"***","Address1":"********","Address2":"********","IdCardType":1,"City":"*****","State":"********","Guests":[{"FirstName":"*********","LastName":"************"}]}"""
      val guests = Some(List(Guest(FirstName = Some("TestGuest"), LastName = Some("TestLastname"))))
      val json = SensitiveInfoMapper.createJson(
        CustomerInfo(
          Some("Nazar"),
          Some("Bukhari"),
          Some("01911111"),
          Some("088"),
          Some("<EMAIL>"),
          Some("123"),
          Some("123"),
          Some("088"),
          Some("088"),
          Some("Address1"),
          Some("Address2"),
          Some(1),
          Some("Dhaka"),
          Some("Lalmatia"),
          Guests = guests
        )
      )
      json mustBe maskedPaymentApiV2CustomerInfo
    }

    "mask HotelFraudCheckRequest correctly" in {
      val checkInDate: ZonedDateTime  = ZonedDateTime.now()
      val checkOutDate: ZonedDateTime = ZonedDateTime.now()
      val hotelFraudCheckRequest =
        """{"storefrontId":202,"refType":1,"refNo":10001,"itineraryId":0,"phoneCountryCode":"123","phoneAreaCode":"66","phoneNo":"******","destinationCityId":1,"destinationCountryId":1,"cardType":1,"ccId":0,"creditCardNo":"***********","ccHash":"Ahoihjwe78wqrejoi","cardHolderName":"********************","billingCity":"****","billingRegion":"***********","billingPostal":"*****","billingCountry":"******","issuingBankName":"***","issuingBankPhone":"","customerFirstName":"*********","customerLastName":"**********","email":"*******************","nationalityId":106,"ip":"","forwardIp":"","sessionId":"","bookingTotal":3033.33,"averageDailyAmount":209.1,"daysUntilArrival":10,"objGuestName":[],"checkInDate":{},"checkOutDate":{},"noOfChildren":2,"hotelId":904822,"dmcId":29014,"cookieId":"","siteId":1234,"displayCurrency":"JYP","languageId":6,"isItineraryBooking":true,"tmSessionId":"","originalPaymentModelId":0,"cancellationPolicy":"","trafficGroupId":0,"agodaCashTotal":0.0,"fingerprintData":"","chargeOption":0,"paymentOption":0,"platformGroupId":0,"roomTypeId":0,"bookingTags":[],"multiProductTypeId":0,"isCrossSell":false,"memberId":0,"affiliateModelId":0,"fraudCheckFeatures":"","unknownFields":{"fields":{}}}""".stripMargin
      val json = SensitiveInfoMapper.createJson(
        HotelFraudCheckRequest(
          storefrontId = 202,
          refType = 1,
          refNo = 10001,
          itineraryId = 0,
          phoneCountryCode = "123",
          phoneAreaCode = "66",
          phoneNo = "123456",
          destinationCityId = 1,
          destinationCountryId = 1,
          cardType = 1,
          creditCardNo = "***********",
          ccHash = "Ahoihjwe78wqrejoi",
          cardHolderName = "Toothless the dragon",
          billingCity = "Berk",
          billingRegion = "Viking town",
          billingPostal = "12345",
          billingCountry = "HTTYDG",
          issuingBankName = "ABC",
          issuingBankPhone = "",
          customerFirstName = "Toothless",
          customerLastName = "The Dragon",
          email = "<EMAIL>",
          nationalityId = 106,
          ip = "",
          forwardIp = "",
          sessionId = "",
          bookingTotal = 3033.33,
          averageDailyAmount = 209.10,
          daysUntilArrival = 10,
          objGuestName = Seq.empty,
          checkInDate = Some(checkInDate),
          checkOutDate = Some(checkOutDate),
          noOfChildren = 2,
          hotelId = 904822,
          dmcId = 29014,
          cookieId = "",
          siteId = 1234,
          displayCurrency = "JYP",
          languageId = 6,
          isItineraryBooking = true,
          tmSessionId = "",
          originalPaymentModelId = 0,
          cancellationPolicy = "",
          trafficGroupId = 0,
          agodaCashTotal = 0.0,
          fingerprintData = "",
          chargeOption = 0,
          paymentOption = 0,
          platformGroupId = 0,
          roomTypeId = 0L,
          userAgent = None,
          bookingTags = Seq.empty
        )
      )
      json mustBe hotelFraudCheckRequest
    }

    "mask CustomerV2" in {
      val masked =
        """{"firstName":"****","lastName":"****","email":"********","countryId":-1,"title":"***","middleName":"****","phoneFormat":"","faxFormat":"","address1":"********","address2":"**********","postcode":"****","region":"","state":"","city":"","area":"","birthInfo":{"birthDate":"1970-01-01","birthPlace":0},"phoneContact":{"countryCallingCode":"**","phoneNumber":"************"},"stateId":0,"age":0,"gender":"*","localizedNames":[{"languageId":6,"scriptId":2,"firstname":"**","lastname":"***"}]}"""
      val json = SensitiveInfoMapper.createJson(
        CustomerV2(
          firstName = "十ヨ互丁",
          lastName = "十ヨ互丁",
          email = "test.com",
          countryId = 6,
          title = Some("Mr."),
          middleName = Some("test"),
          phoneFormat = Some(""),
          faxFormat = Some(""),
          address1 = Some("aaaaaaaa"),
          address2 = Some("aasdasdsad"),
          postcode = Some("2342"),
          region = Some(""),
          state = Some(""),
          city = Some(""),
          area = Some(""),
          birthInfo = Some(BirthInfo(Some(LocalDate.now), Some(1))),
          phoneContact = Some(PhoneContact("66", "111111111111")),
          stateId = Some(1),
          age = Some(20),
          gender = Some("M"),
          localizedNames = Some(Seq(LocalizedNameV2(6, Kanji, "互丁", "ヨ互丁")))
        )
      )

      json mustBe masked
    }

    "mask CarDriver" in {
      val masked =
        """{"title":"**","firstName":"****","lastName":"***","email":"**************","phoneNumber":"***********","middleName":"","gender":"*","flightNo":"","license":"","address":""}"""
      val json = SensitiveInfoMapper.createJson(
        CarDriver(
          title = "Mr",
          firstName = "John",
          lastName = "Doe",
          email = Some("<EMAIL>"),
          phoneNumber = Some("08123456789"),
          middleName = Some(""),
          gender = Some("Male"),
          flightNo = Some(""),
          license = Some(""),
          address = Some("")
        )
      )

      json mustBe masked
    }

    "mask CustomerV2 with empty fields" in {
      val masked = """{"firstName":"","lastName":"","email":"","countryId":-1}"""
      val json = SensitiveInfoMapper.createJson(
        CustomerV2("", "", "", 0)
      )
      json mustBe masked
    }

    "mask HotelGuest with empty fields" in {
      val masked =
        """{"guestNo":0,"title":"","firstname":"","lastname":"","primary":false,"middlename":"","suffix":"","isExtrabed":false,"age":0,"nationalityId":0,"isAdult":true}"""
      val json = SensitiveInfoMapper.createJson(
        HotelGuest()
      )
      json mustBe masked
    }

    "mask ActivityPax" in {
      val masked =
        """{"id":5,"title":"**","firstname":"****","lastname":"***","primary":true,"middlename":"******","paxType":1,"birthDate":"1970-01-01","gender":"*","nationalityId":0,"passportNumber":"************","passportExpires":"1970-01-01","email":"**************","phoneNumber":"***********","phoneCountryCode":"**","meta":[]}"""
      val json = SensitiveInfoMapper.createJson(
        ActivityPax(
          id = 5,
          title = Some("Mr"),
          firstname = Some("John"),
          lastname = Some("Doe"),
          primary = true,
          middlename = Some("Lennon"),
          paxType = PaxType.Adult,
          birthDate = Some(LocalDate.now()),
          gender = Some("Male"),
          nationalityId = Some(10),
          passportNumber = Some("gubasdljfnsf"),
          passportExpires = Some(LocalDate.now()),
          email = Some("<EMAIL>"),
          phoneNumber = Some("08123456789"),
          phoneCountryCode = Some("66")
        )
      )

      json mustBe masked
    }

    "mask bookerAnswer" in {
      val json = """"specialRequestV2": {
                   |          "selectedRequests": [
                   |            {
                   |              "i": 1
                   |            }
                   |          ],
                   |          "additionalNotes": "",
                   |          "flightArrivalNo": "",
                   |          "flightArrivalTime": "",
                   |          "arrivalTime": "20.00 - 21.00",
                   |          "bookerAnswer": "I'd like a cake"
                   |        }""".stripMargin
      val expectedResult =
        """"specialRequestV2":{"selectedRequests":[{"i":1}],"additionalNotes":"","flightArrivalNo":"","flightArrivalTime":"","arrivalTime":"20.00-21.00","bookerAnswer":"*"}""".stripMargin
      val result = SensitiveInfoMapper.maskSensitiveInfo(json, false)
      result mustBe expectedResult
    }
  }

  "SensitiveInfoMapper maskSensitiveInfo" should {
    "mask sensitive data correctly" in {
      val json =
        """{"text":"This is random string","phoneNumber":"09000999","countryId":9955,"email":"<EMAIL>","value":1,"SecurityCode":123,"b2bCustomerEmail":"<EMAIL>"}"""
      val expectedResult = "!!!NEXT JSON IS MALFORMED DUE TO MASKING OF SENSITIVE USER DATA!!!--->" +
        """{"text":"Thisisrandomstring","phoneNumber":"*","countryId":"*","email":"*","value":1,"SecurityCode":"*","b2bCustomerEmail":"*"}"""

      val result = SensitiveInfoMapper.maskSensitiveInfo(json);
      result mustBe expectedResult
    }
  }
}
