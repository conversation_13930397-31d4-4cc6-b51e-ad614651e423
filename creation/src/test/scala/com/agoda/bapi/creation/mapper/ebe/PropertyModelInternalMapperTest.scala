package com.agoda.bapi.creation.mapper.ebe

import com.agoda.bapi.common.config.{EarnGiftCardConfig, FraudConfig, PropertyBookingCreationConfig}
import com.agoda.bapi.common.handler.{MeasurementsContext, RequestContext}
import com.agoda.bapi.common.message.ChildrenType
import com.agoda.bapi.common.message.creation.{AffiliatePaymentMethod, BenefitID, BookingHoldingPartner, BookingPayment, BreakDownItemID, BreakDownTypeID, ChildOccupancy, ChildType, Occupancy, SpecialRequests, SupplierData}
import com.agoda.bapi.common.model._
import com.agoda.bapi.common.model.addOn.UserTaxCountry
import com.agoda.bapi.common.model.creation.{AvailabilityType, BAPIBooking, BookingDcProcessType, BookingItem, BookingItemBreakdown, BookingRateCategory, BookingRoom, BookingRoomBenefit, ChildPromotion, ChildRoom, EBEBooking, EBEHotel, MasterRoom, PaymentModel, RoomAllocationItemInfo, SupplierPaymentChannel}
import com.agoda.bapi.common.model.db.{CountryInfo, PlecsCampaignPromocode}
import com.agoda.bapi.common.model.flight.Supplier
import com.agoda.bapi.common.model.product.ProductTypeEnum
import com.agoda.bapi.common.model.property.PropertyBookingStateModel
import com.agoda.bapi.common.model.property.PropertyBookingStateModel._
import com.agoda.bapi.common.model.whitelabel.WhiteLabelFeatureName
import com.agoda.bapi.common.repository.CountriesRepository
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.common.util.{JacksonSerializer, JodaDateTimeUtils, ServerUtils}
import com.agoda.bapi.common.{MessageService, MockRequestContext}
import com.agoda.bapi.creation.CreatePropertyBookingHelper
import com.agoda.bapi.creation.mapper.ebe.builder.{PropertyBookingChildPromotionBuilder, PropertyBookingOccupancyBuilder, PropertyBookingSummaryBuilder, PropertyBookingSupplierBuilder}
import com.agoda.bapi.creation.model.RequestWithProducts
import com.agoda.bapi.creation.model.db.{HotelInfo, HotelPriceDisplaySetting, SurchargeInfo => SurchargeInfoDB}
import com.agoda.bapi.creation.model.messages.PriceDisplaySettingMessage
import com.agoda.bapi.creation.model.multi.{EmptyProductReservedIds, Product, ReservedIds}
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.bapi.creation.proxy.CreationCDBProxy
import com.agoda.bapi.creation.repository.EbeLiteBookingRepository
import com.agoda.bapi.creation.service.UrlService
import com.agoda.bapi.creation.util.{CancellationUtils, CustomerApiTokenUtils}
import com.agoda.mpb.common.models.state.{PayLaterProductPayment, ProductPayment, ProductPaymentInfo}
import com.agoda.mpb.common.{BookingType, CCReceived, DmcId}
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.agoda.supply.calc.proto.InventoryType
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import com.softwaremill.quicklens._
import generated.model.{BookingWorkflow, DmcControlSettingModel}
import mocks.WorkflowModelMock
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.ArgumentMatchers.{eq => eqTo, _}
import org.mockito.Mockito.{when, _}
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{BeforeAndAfterEach, TryValues}
import org.scalatestplus.mockito.MockitoSugar

import java.time.LocalDateTime
import java.util.UUID
import scala.concurrent.Future
import scala.util.Success

class PropertyModelInternalMapperTest
    extends AsyncWordSpec
    with MockitoSugar
    with Matchers
    with BeforeAndAfterEach
    with TryValues
    with WorkflowModelMock
    with CreatePropertyBookingHelper {

  val ebeLiteBookingRepository = mock[EbeLiteBookingRepository]
  val ebeConfig = PropertyBookingCreationConfig(
    earnGiftCard = EarnGiftCardConfig(defaultOffsetDay = 5),
    fraud = FraudConfig(secondFraudCheckOffsetDay = 2)
  )
  val currentDate                   = DateTime.parse("2019-11-20T08:50:00.000Z")
  val dateTimeUtils                 = mock[JodaDateTimeUtils]
  val cancellationUtils             = mock[CancellationUtils]
  val chargeUtils                   = mock[EbeBookingChargeMapperUtils]
  val creationCDBProxyMock          = mock[CreationCDBProxy]
  val customerAPITokenUtilsMock     = mock[CustomerApiTokenUtils]
  val featureAwareMock              = mock[FeatureAware]
  val messageServiceMock            = mock[MessageService]
  val urlService                    = mock[UrlService]
  val featureAware                  = mock[FeatureAware]
  val countriesRepository           = mock[CountriesRepository]
  val propertyBookingSummaryBuilder = new PropertyBookingSummaryBuilder(creationCDBProxyMock, messageServiceMock)
  val mapper = new PropertyModelInternalMapperImpl(
    ebeLiteBookingRepository,
    ebeConfig,
    dateTimeUtils,
    cancellationUtils,
    chargeUtils,
    propertyBookingSummaryBuilder,
    urlService,
    creationCDBProxyMock,
    customerAPITokenUtilsMock,
    countriesRepository
  ) {}
  val mockFeatureConfig = FeaturesConfiguration(
    bookingWorkflow = BookingWorkflow(
      dmcControlSettings = Some(
        List(
          DmcControlSettingModel(
            dmcId = None,
            isJtbDmcBooking = Some(false)
          ),
          DmcControlSettingModel(
            dmcId = Some(332),
            isJtbDmcBooking = Some(false)
          ),
          DmcControlSettingModel(
            dmcId = Some(29014),
            isJtbDmcBooking = Some(true)
          )
        )
      )
    )
  )

  implicit val measurementsContext = MeasurementsContext(
    correlationId = "",
    clientId = 1,
    endpoint = "",
    storefrontId = 1,
    cid = 1,
    languageId = 1,
    paymentMethod = PaymentMethod.None,
    paymentCurrency = "",
    bookingDcProcessType = BookingDcProcessType.Local,
    productType = ProductTypeEnum.Unknown,
    isRecheckAllotmentOnCentralDC = false,
    WhiteLabel.Agoda,
    isCCOF = false
  )
  implicit val context: RequestContext = MockRequestContext.create().copy(featureAware = Some(featureAwareMock))

  when(dateTimeUtils.getCurrentDateTime).thenReturn(currentDate)
  when(dateTimeUtils.toDateTimeWithoutMillis(any[DateTime])).thenAnswer(new Answer[DateTime]() {
    def answer(invocation: InvocationOnMock): DateTime = {
      val input = invocation.getArguments()(0).asInstanceOf[DateTime]
      JodaDateTimeUtils.toDateTimeWithoutMillis(input)
    }
  })

  override def beforeEach(): Unit = {
    reset(chargeUtils)
    reset(cancellationUtils)
    reset(ebeLiteBookingRepository)
    reset(creationCDBProxyMock)
    reset(messageServiceMock)
    reset(featureAwareMock)
  }

  "getDmcCodeByDmcId" should {
    "return bookingDate if cancelation code is non-refundable(365D100P)" in {
      val cxlCode        = "365D100P"
      val bookingContext = baseBookingContext
      when(cancellationUtils.isNonRefundable(any[String])).thenReturn(true)

      val expected = bookingContext.bookingDate

      mapper.checkDmcDueDate(cxlCode, bookingContext) shouldBe expected
    }

    "return bookingDate if cancelation code is not non-refundable(365D100P)" in {
      val cxlCode            = "5D10P"
      val bookingContext     = baseBookingContext
      val cancelationDueDate = DateTime.parse("2018-11-17T11:37:10.645Z")
      val dmcDueDate         = DateTime.parse("2018-11-18T11:37:10.645Z")

      when(cancellationUtils.isNonRefundable(any[String])).thenReturn(false)
      when(cancellationUtils.getCancellationDueDate(any[String], any[DateTime])).thenReturn(cancelationDueDate)
      when(cancellationUtils.getDmcDueDate(any[String], any[DateTime], any[DateTime], any[DateTime], any[Int]))
        .thenReturn(dmcDueDate)

      val expected = dmcDueDate
        .withZoneRetainFields(DateTimeZone.forOffsetHours(bookingContext.hotelGMTOffset))
        .withZone(DateTimeZone.getDefault)

      mapper.checkDmcDueDate(cxlCode, bookingContext) shouldEqual expected

      verify(cancellationUtils).getDmcDueDate(
        eqTo(cxlCode),
        eqTo(bookingContext.checkInDate),
        eqTo(cancelationDueDate),
        eqTo(bookingContext.bookingDate),
        eqTo(0)
      )
      verify(cancellationUtils).getCancellationDueDate(eqTo(cxlCode), eqTo(bookingContext.checkInDate))
      succeed
    }
  }

  "getDmcCodeByDmcId" should {
    "return dmc code correctly" in {
      val input    = 3038
      val supplier = Supplier(id = 3038, code = "BCOM", name = "BCOM", dmcType = 1, isPull = None)
      val expected = supplier.code
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplier)))
      mapper.getDmcCodeByDmcId(input).map { result =>
        result shouldBe expected
      }
    }

    "return empty string if supplier not found" in {
      val input    = 3038
      val expected = ""
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(None))
      mapper.getDmcCodeByDmcId(input).map { result =>
        result shouldBe expected
      }
    }

    "return empty string if db connection error" in {
      val input    = 3038
      val expected = ""
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.failed(new Exception("mock")))
      mapper.getDmcCodeByDmcId(input).map { result =>
        result shouldBe expected
      }
    }
  }

  "getHotelInfoByHotelId" should {
    "return hotelInfo correctly" in {
      val input     = 1
      val hotelInfo = mockHotelInfo
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Int])).thenReturn(Future.successful(Some(hotelInfo)))
      mapper.getHotelInfoByHotelId(input).map { result =>
        result shouldBe hotelInfo
      }
    }

    "throw HotelInfoNotFound exception if hotelInfo not found" in {
      val input = 1
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Int])).thenReturn(Future.successful(None))
      mapper.getHotelInfoByHotelId(input).failed.map { result =>
        result shouldBe a[HotelInfoNotFound]
      }
    }

    "throw DB exception if db connection error" in {
      val input     = 1
      val exception = new Exception("mock")
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Int])).thenReturn(Future.failed(exception))
      mapper.getHotelInfoByHotelId(input).failed.map { result =>
        result shouldBe exception
      }
    }
  }

  "mapToPropertyFinancialBreakdown" should {
    "map PropertyFinancialBreakdown correctly" in {
      val breakdown = sampleBreakdown.copy(
        itemId = BreakDownItemID.NetInclusive.id,
        typeId = BreakDownTypeID.RoomCharge.id
      )
      val bookingContext = mock[BookingContext]
      when(bookingContext.bookingId).thenReturn(baseBookingContext.bookingId)
      when(bookingContext.bookingDate).thenReturn(baseBookingContext.bookingDate)
      when(bookingContext.request).thenReturn(baseBookingContext.request)
      when(bookingContext.requestContext).thenReturn(
        defaultRequestContext.copy(featureAware = Some(featureAwareMock))
      )
      val productIndex = Some(0)

      // Expected Value
      val expectedReferenceId = 100010000L
      val expected = Seq(
        PropertyFinancialBreakdown(
          referenceId = expectedReferenceId,
          bookingRoomReferenceId = breakdown.roomNo,
          breakdownId = None,
          bookingId = baseBookingContext.bookingId,
          bookingRoomId = None,
          dateOfStay = breakdown.chargeDate,
          itemId = breakdown.itemId.toInt,
          typeId = Some(breakdown.typeId.toInt),
          surchargeId = breakdown.surchargeId,
          taxFeeId = breakdown.taxFeeId,
          quantity = Some(breakdown.quantity),
          localCurrency = Some(breakdown.localCurrency),
          localAmount = Some(breakdown.localAmount),
          exchangeRate = Some(breakdown.exchangeRate),
          usdAmount = Some(breakdown.usdAmount),
          supplierPaymentCurrency = None,
          supplierPaymentAmount = None,
          supplierPaymentExchangeRate = None,
          refBreakdownId = None,
          isTransfer = false,
          hotelPaymentMethodId = Some(HotelPaymentMethodId.Default),
          upcId = None,
          taxPrototypeId = None,
          subtypeId = None,
          recStatus = RecStatus.Active,
          recCreatedWhen = baseBookingContext.bookingDate,
          recCreatedBy = baseBookingContext.request.userId,
          recModifyWhen = None,
          recModifyBy = None,
          actionId = Some(10011001L)
        )
      )

      val result = mapper.mapToPropertyFinancialBreakdown(Seq(breakdown), bookingContext, productIndex, 10011001L)
      result shouldBe expected
    }

    "map PropertyFinancialBreakdown correctly with itemId and typeId not existed" in {
      val breakdown = sampleBreakdown.copy(
        itemId = 999,
        typeId = 999
      )
      val bookingContext = mock[BookingContext]
      when(bookingContext.bookingId).thenReturn(baseBookingContext.bookingId)
      when(bookingContext.bookingDate).thenReturn(baseBookingContext.bookingDate)
      when(bookingContext.request).thenReturn(baseBookingContext.request)
      when(bookingContext.requestContext).thenReturn(
        defaultRequestContext.copy(featureAware = Some(featureAwareMock))
      )
      val productIndex = None

      // Expected Value
      val expectedReferenceId = 100010000L
      val expected = Seq(
        PropertyFinancialBreakdown(
          referenceId = expectedReferenceId,
          bookingRoomReferenceId = breakdown.roomNo,
          breakdownId = None,
          bookingId = baseBookingContext.bookingId,
          bookingRoomId = None,
          dateOfStay = breakdown.chargeDate,
          itemId = BreakDownItemID.None.id,
          typeId = Some(BreakDownTypeID.None.id),
          surchargeId = breakdown.surchargeId,
          taxFeeId = breakdown.taxFeeId,
          quantity = Some(breakdown.quantity),
          localCurrency = Some(breakdown.localCurrency),
          localAmount = Some(breakdown.localAmount),
          exchangeRate = Some(breakdown.exchangeRate),
          usdAmount = Some(breakdown.usdAmount),
          supplierPaymentCurrency = None,
          supplierPaymentAmount = None,
          supplierPaymentExchangeRate = None,
          refBreakdownId = None,
          isTransfer = false,
          hotelPaymentMethodId = Some(HotelPaymentMethodId.Default),
          upcId = None,
          taxPrototypeId = None,
          subtypeId = None,
          recStatus = RecStatus.Active,
          recCreatedWhen = baseBookingContext.bookingDate,
          recCreatedBy = baseBookingContext.request.userId,
          recModifyWhen = None,
          recModifyBy = None,
          actionId = Some(10011001L)
        )
      )

      val result = mapper.mapToPropertyFinancialBreakdown(Seq(breakdown), bookingContext, productIndex, 10011001L)
      result shouldBe expected
    }

    "map PropertyFinancialBreakdown correctly when have multiple breakdowns" in {
      val breakdown1 = sampleBreakdown.copy(
        itemId = BreakDownItemID.NetInclusive.id,
        typeId = BreakDownTypeID.RoomCharge.id,
        roomNo = 1
      )
      val breakdown2 = sampleBreakdown.copy(
        itemId = BreakDownItemID.NetInclusive.id,
        typeId = BreakDownTypeID.RoomCharge.id,
        roomNo = 2
      )

      val bookingContext = mock[BookingContext]
      when(bookingContext.bookingId).thenReturn(baseBookingContext.bookingId)
      when(bookingContext.bookingDate).thenReturn(baseBookingContext.bookingDate)
      when(bookingContext.request).thenReturn(baseBookingContext.request)
      when(bookingContext.requestContext).thenReturn(
        defaultRequestContext.copy(featureAware = Some(featureAwareMock))
      )
      val productIndex = Some(1)

      // Expected Value
      val expected = Seq(
        PropertyFinancialBreakdown(
          referenceId = 101010000L,
          bookingRoomReferenceId = breakdown1.roomNo,
          breakdownId = None,
          bookingId = baseBookingContext.bookingId,
          bookingRoomId = None,
          dateOfStay = breakdown1.chargeDate,
          itemId = breakdown1.itemId.toInt,
          typeId = Some(breakdown1.typeId.toInt),
          surchargeId = breakdown1.surchargeId,
          taxFeeId = breakdown1.taxFeeId,
          quantity = Some(breakdown1.quantity),
          localCurrency = Some(breakdown1.localCurrency),
          localAmount = Some(breakdown1.localAmount),
          exchangeRate = Some(breakdown1.exchangeRate),
          usdAmount = Some(breakdown1.usdAmount),
          supplierPaymentCurrency = None,
          supplierPaymentAmount = None,
          supplierPaymentExchangeRate = None,
          refBreakdownId = None,
          isTransfer = false,
          hotelPaymentMethodId = Some(HotelPaymentMethodId.Default),
          upcId = None,
          taxPrototypeId = None,
          subtypeId = None,
          recStatus = RecStatus.Active,
          recCreatedWhen = baseBookingContext.bookingDate,
          recCreatedBy = baseBookingContext.request.userId,
          recModifyWhen = None,
          recModifyBy = None,
          actionId = Some(10011001L)
        ),
        PropertyFinancialBreakdown(
          referenceId = 101020001L,
          bookingRoomReferenceId = breakdown2.roomNo,
          breakdownId = None,
          bookingId = baseBookingContext.bookingId,
          bookingRoomId = None,
          dateOfStay = breakdown2.chargeDate,
          itemId = breakdown2.itemId.toInt,
          typeId = Some(breakdown2.typeId.toInt),
          surchargeId = breakdown2.surchargeId,
          taxFeeId = breakdown2.taxFeeId,
          quantity = Some(breakdown2.quantity),
          localCurrency = Some(breakdown2.localCurrency),
          localAmount = Some(breakdown2.localAmount),
          exchangeRate = Some(breakdown2.exchangeRate),
          usdAmount = Some(breakdown2.usdAmount),
          supplierPaymentCurrency = None,
          supplierPaymentAmount = None,
          supplierPaymentExchangeRate = None,
          refBreakdownId = None,
          isTransfer = false,
          hotelPaymentMethodId = Some(HotelPaymentMethodId.Default),
          upcId = None,
          taxPrototypeId = None,
          subtypeId = None,
          recStatus = RecStatus.Active,
          recCreatedWhen = baseBookingContext.bookingDate,
          recCreatedBy = baseBookingContext.request.userId,
          recModifyWhen = None,
          recModifyBy = None,
          actionId = Some(10011001L)
        )
      )

      val result =
        mapper.mapToPropertyFinancialBreakdown(Seq(breakdown1, breakdown2), bookingContext, productIndex, 10011001L)
      result shouldBe expected
    }
  }

  "mapToPropertyBookingHotelRoom" should {
    "map PropertyBookingHotelRoom correctly" in {
      val bookingHotelReferenceId = 1
      val isIncludeBreakfast      = true
      val breakdowns              = sampleBreakdowns
      val roomInfo = sampleRoomInfo.copy(
        numberOfRoom = 3,
        benefit = Seq(BookingRoomBenefit(BenefitID.Breakfast.id, None, 0))
      )

      val occupancy = Seq(
        Occupancy(
          roomNo = 1,
          noOfAdultMales = 1,
          noOfAdultFemales = 1,
          childOccupancy = Some(
            Seq(
              ChildOccupancy(typeId = ChildType.ChildA, count = 2)
            )
          )
        ),
        Occupancy(
          roomNo = 2,
          noOfAdultMales = 1,
          noOfAdultFemales = 1,
          childOccupancy = None
        ),
        Occupancy(
          roomNo = 3,
          noOfAdultMales = 1,
          noOfAdultFemales = 1,
          childOccupancy = None
        )
      )

      val bookingContext = baseBookingContext
      val result =
        mapper.mapToPropertyBookingHotelRoom(
          bookingHotelReferenceId,
          breakdowns,
          roomInfo,
          occupancy,
          Some(1),
          Some(1),
          bookingContext
        )

      val expected =
        for (roomNo <- 1 to roomInfo.numberOfRoom)
          yield PropertyBookingHotelRoom(
            referenceId = roomNo,
            bookingHotelReferenceId = bookingHotelReferenceId,
            bookingId = bookingContext.bookingId,
            bookingHotelId = None,
            bookingRoomId = None,
            roomTypeId = Some(roomInfo.roomTypeId.toInt),
            roomTypeName = Some(roomInfo.roomTypeName),
            roomNo = Some(roomNo),
            occupancy = Some(roomInfo.capacity.occupancy),
            noOfBeds = None,
            noOfExtrabeds = Some(0),
            breakfastIncluded = Some(isIncludeBreakfast),
            breakfastInfo = Some(roomInfo.breakfastInfo),
            roomRemark = Some(""),
            noOfMales = Some(1),
            noOfFemales = Some(1),
            recStatus = RecStatus.Active,
            recCreatedWhen = bookingContext.bookingDate,
            recCreatedBy = bookingContext.request.userId,
            recModifyWhen = None,
            recModifyBy = None,
            masterRoomTypeId = Some(1),
            dynamicMappingTypeId = Some(1)
          )
      result shouldBe expected
    }
  }

  "mapToPropertyBookingRateCategoryBenefit" should {
    "return None if roomInfo benefit is null" in {
      val rateCategoryReferenceId = 1
      val roomInfo = sampleRoomInfo.copy(
        benefit = null
      )
      val bookingContext = baseBookingContext
      val result         = mapper.mapToPropertyBookingRateCategoryBenefit(rateCategoryReferenceId, roomInfo, bookingContext)
      val expected       = Seq.empty
      result shouldBe expected
    }
    "return None if roomInfo benefit empty" in {
      val rateCategoryReferenceId = 1
      val roomInfo = sampleRoomInfo.copy(
        benefit = Seq.empty
      )
      val bookingContext = baseBookingContext
      val result         = mapper.mapToPropertyBookingRateCategoryBenefit(rateCategoryReferenceId, roomInfo, bookingContext)
      val expected       = Seq.empty
      result shouldBe expected
    }
  }

  "mapToPropertyBookingCharge" should {
    val surchargeInfo = SurchargeInfoDB(
      chargeTypeId = 1,
      chargeDescription = "mockDescription",
      defaultUnitOfMeasure = "mockUOM",
      redirectChargeTypeId = 1,
      description = Some("mockDescription"),
      descriptionYCS = Some("mockDescription")
    )
    val surchargeBreakdown = sampleBreakdown.copy(typeId = BreakDownTypeID.Surcharge.id)
    val discountBreakdown  = sampleBreakdown.copy(typeId = BreakDownTypeID.Discount.id)
    val otherBreakdown     = sampleBreakdown.copy(typeId = BreakDownTypeID.RoomCharge.id)
    val rtaEssBreakdown    = sampleBreakdown.copy(typeId = BreakDownTypeID.RtaEssCharge.id)
    val breakdowns = Seq(
      sampleBreakdown,
      sampleBreakdown,
      sampleBreakdown,
      sampleBreakdown,
      sampleBreakdown
    )
    val groupBreakdowns = Seq(
      surchargeBreakdown,
      discountBreakdown,
      otherBreakdown,
      rtaEssBreakdown
    )
    val bookingContext    = baseBookingContext
    val chargeDescription = "mockDescription"

    val expected =
      Seq(
        sampleChargeBreakdown,
        sampleChargeBreakdown,
        sampleChargeBreakdown,
        sampleChargeBreakdown,
        sampleChargeBreakdown
      )

    "return charge breakdown correctly" in {
      when(chargeUtils.groupBreakdownItemsByChargeAttribute(any[Seq[BookingItemBreakdown]])).thenReturn(groupBreakdowns)
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenReturn(Future.successful(Some(surchargeInfo)))
      when(
        chargeUtils.toChargeItem(
          any[Long],
          any[Long],
          any[UUID],
          any[BookingItemBreakdown],
          any[Seq[BookingItemBreakdown]],
          any[Int],
          any[String],
          any[DateTime],
          any[Option[Int]]
        )
      ).thenReturn(Success(sampleChargeBreakdown))
//      when(helper.getSurchargeDescription(any[Option[SurchargeInfoDB]], any[Boolean], any[Int], any[Int], any[Int])).thenReturn(chargeDescription)
      when(
        chargeUtils.getDiscountChargeItemList(
          any[Long],
          any[Long],
          any[UUID],
          any[BookingItemBreakdown],
          any[Seq[BookingItemBreakdown]],
          any[Option[String]],
          any[DateTime],
          any[Option[Int]]
        )
      ).thenReturn(Success(Seq(sampleChargeBreakdown, sampleChargeBreakdown)))

      mapper.mapToPropertyBookingCharge(breakdowns, bookingContext, None).map { result =>
        verify(ebeLiteBookingRepository, times(4))
          .getSurchargeInfoByChargeId(eqTo(sampleBreakdown.surchargeId.getOrElse(0)))
        verify(chargeUtils, times(1)).groupBreakdownItemsByChargeAttribute(eqTo(breakdowns))
        verify(chargeUtils, times(1)).toChargeItem(
          eqTo(1L),
          eqTo(bookingContext.bookingId.toLong),
          eqTo(bookingContext.request.userId),
          eqTo(surchargeBreakdown),
          eqTo(breakdowns),
          eqTo(surchargeInfo.chargeTypeId),
          eqTo(chargeDescription),
          eqTo(bookingContext.bookingDate),
          eqTo(None)
        )
        verify(chargeUtils, times(1)).getDiscountChargeItemList(
          eqTo(2L),
          eqTo(bookingContext.bookingId.toLong),
          eqTo(bookingContext.request.userId),
          eqTo(discountBreakdown),
          eqTo(breakdowns),
          eqTo(bookingContext.request.discountV1.promotionCode),
          eqTo(bookingContext.bookingDate),
          eqTo(None)
        )
        verify(chargeUtils, times(1)).toChargeItem(
          eqTo(4L),
          eqTo(bookingContext.bookingId.toLong),
          eqTo(bookingContext.request.userId),
          eqTo(otherBreakdown),
          eqTo(breakdowns),
          eqTo(0),
          eqTo(""),
          eqTo(bookingContext.bookingDate),
          eqTo(None)
        )
        result shouldBe expected
      }
    }
  }

  "mapToPropertyBookingSchedule" should {
    val fullyAuthDate   = currentDate.plusDays(2)
    val fullyChargeDate = currentDate.plusDays(3)
    val bookingContext = baseBookingContext.copy(
      chargeOption = ChargeOption.PayNow
    )
    val defaultExpected = PropertyBookingStateModel.PropertyBookingSchedule(
      bookingId = bookingContext.bookingId,
      fullyChargeDate = fullyChargeDate,
      fraudCheckDate = DateTime.now,
      isFraudChecked = false, // default value on SP
      fullyAuthDate = Some(fullyAuthDate),
      GMTOffset = Some(bookingContext.hotelGMTOffset),
      recStatus = RecStatus.Active,
      recCreatedWhen = bookingContext.bookingDate,
      recCreatedBy = bookingContext.request.userId,
      recModifyWhen = None,
      recModifyBy = None
    )
    "return None if chargeOption not BNPL or BNPC" in {
      val result = mapper.mapToPropertyBookingSchedule(bookingContext)
      result shouldBe None
    }

    "return BookingSchedule if chargeOption is BNPL or BNPC" in {
      val overrideContext = bookingContext.copy(
        request = bookingContext.request.copy(affiliatePaymentMethod = Some(AffiliatePaymentMethod.Invoice)),
        chargeOption = ChargeOption.PayLater
      )
      val secondFraudCheckDate = mapper.getSecondFraudCheckDate(
        bookingContext.fullyAuthDate,
        currentDate,
        ebeConfig.fraud.secondFraudCheckOffsetDay
      )

      val result = mapper.mapToPropertyBookingSchedule(overrideContext)
      val expected = defaultExpected.copy(
        fraudCheckDate = secondFraudCheckDate,
        fullyChargeDate = bookingContext.fullyChargeDate,
        fullyAuthDate = Some(bookingContext.fullyAuthDate)
      )
      result shouldBe Some(expected)
    }
  }

  "mapToBookingHoldingPartnerNames" should {
    "return empty list if no booking Holding partner names exist" in {
      val bookingId = 123
      val result    = mapper.mapToBookingHoldingPartnerNames(None, bookingId)
      val expected  = Seq.empty[PropertyBookingBookingHoldingPartnerName]
      result shouldBe expected
    }

    "return empty list if booking Holding partner names is empty" in {
      val bookingId = 123
      val result    = mapper.mapToBookingHoldingPartnerNames(Some(Seq.empty), bookingId)
      val expected  = Seq.empty[PropertyBookingBookingHoldingPartnerName]
      result shouldBe expected
    }

    "return wholesale if booking Holding partner names is valid" in {
      val bookingId = 123
      val data      = Some(Seq(BookingHoldingPartner(Some(1), Some(111), Some("Priceline.com"))))
      val result    = mapper.mapToBookingHoldingPartnerNames(data, bookingId)
      val expected  = Seq(PropertyBookingBookingHoldingPartnerName(bookingId, Some(1), Some(111), Some("Priceline.com")))
      result shouldBe expected
    }
  }

  "mapToPropertyBookingHotelRoomChildren" should {
    val occupancy = Seq(
      Occupancy(
        roomNo = 1,
        noOfAdultMales = 1,
        noOfAdultFemales = 1,
        childOccupancy = Some(
          Seq(
            ChildOccupancy(typeId = ChildType.ChildA, count = 2)
          )
        )
      ),
      Occupancy(
        roomNo = 2,
        noOfAdultMales = 1,
        noOfAdultFemales = 1,
        childOccupancy = Some(
          Seq(
            ChildOccupancy(typeId = ChildType.ChildB, count = 1),
            ChildOccupancy(typeId = ChildType.ChildC, count = 1)
          )
        )
      )
    )

    "map PropertyBookingHotelRoomChildren correctly" in {
      val bookingContext = baseBookingContext
      val result =
        mapper.mapToPropertyBookingHotelRoomChildren(
          occupancy,
          bookingContext,
          None,
          fallbackToJapanChildRate = false
        )

      val expected = Seq(
        PropertyBookingHotelRoomChildren(
          referenceId = 1,
          bookingRoomId = None,
          childRateTypeId = 1,
          number = 2,
          recStatus = RecStatus.Active,
          recCreatedWhen = bookingContext.bookingDate,
          recCreatedBy = bookingContext.request.userId,
          recModifiedWhen = None,
          recModifiedBy = None
        ),
        PropertyBookingHotelRoomChildren(
          referenceId = 2,
          bookingRoomId = None,
          childRateTypeId = 2,
          number = 1,
          recStatus = RecStatus.Active,
          recCreatedWhen = bookingContext.bookingDate,
          recCreatedBy = bookingContext.request.userId,
          recModifiedWhen = None,
          recModifiedBy = None
        ),
        PropertyBookingHotelRoomChildren(
          referenceId = 2,
          bookingRoomId = None,
          childRateTypeId = 3,
          number = 1,
          recStatus = RecStatus.Active,
          recCreatedWhen = bookingContext.bookingDate,
          recCreatedBy = bookingContext.request.userId,
          recModifiedWhen = None,
          recModifiedBy = None
        )
      )
      result shouldBe expected
    }
    "map PropertyBookingHotelRoomChildren correctly when RoomAllocationItemInfo exist and fallbackToJapanChildRate is false" in {
      val roomAllocationInfo = Some(
        Seq(
          RoomAllocationItemInfo(1, 2, List(ChildrenType(ChildType.ChildA, 2))),
          RoomAllocationItemInfo(1, 2, List(ChildrenType(ChildType.ChildB, 2)))
        )
      )

      val bookingContext = baseBookingContext
      val result =
        mapper.mapToPropertyBookingHotelRoomChildren(
          occupancy,
          bookingContext,
          roomAllocationInfo,
          fallbackToJapanChildRate = false
        )

      val expected = Seq(
        PropertyBookingHotelRoomChildren(
          referenceId = 1,
          bookingRoomId = None,
          childRateTypeId = 1,
          number = 2,
          recStatus = RecStatus.Active,
          recCreatedWhen = bookingContext.bookingDate,
          recCreatedBy = bookingContext.request.userId,
          recModifiedWhen = None,
          recModifiedBy = None
        ),
        PropertyBookingHotelRoomChildren(
          referenceId = 2,
          bookingRoomId = None,
          childRateTypeId = 2,
          number = 1,
          recStatus = RecStatus.Active,
          recCreatedWhen = bookingContext.bookingDate,
          recCreatedBy = bookingContext.request.userId,
          recModifiedWhen = None,
          recModifiedBy = None
        ),
        PropertyBookingHotelRoomChildren(
          referenceId = 2,
          bookingRoomId = None,
          childRateTypeId = 3,
          number = 1,
          recStatus = RecStatus.Active,
          recCreatedWhen = bookingContext.bookingDate,
          recCreatedBy = bookingContext.request.userId,
          recModifiedWhen = None,
          recModifiedBy = None
        )
      )
      result shouldBe expected
    }
    "map PropertyBookingHotelRoomChildren correctly when RoomAllocationItemInfo exist and fallbackToJapanChildRate is true" in {
      val roomAllocationInfo = Some(
        Seq(
          RoomAllocationItemInfo(1, 2, List(ChildrenType(ChildType.ChildA, 2))),
          RoomAllocationItemInfo(1, 2, List(ChildrenType(ChildType.ChildB, 2)))
        )
      )

      val bookingContext = baseBookingContext
      val result =
        mapper.mapToPropertyBookingHotelRoomChildren(
          occupancy,
          bookingContext,
          roomAllocationInfo,
          fallbackToJapanChildRate = true
        )

      val expected = Seq(
        PropertyBookingHotelRoomChildren(
          referenceId = 1,
          bookingRoomId = None,
          childRateTypeId = 1,
          number = 2,
          recStatus = RecStatus.Active,
          recCreatedWhen = bookingContext.bookingDate,
          recCreatedBy = bookingContext.request.userId,
          recModifiedWhen = None,
          recModifiedBy = None
        ),
        PropertyBookingHotelRoomChildren(
          referenceId = 2,
          bookingRoomId = None,
          childRateTypeId = 2,
          number = 2,
          recStatus = RecStatus.Active,
          recCreatedWhen = bookingContext.bookingDate,
          recCreatedBy = bookingContext.request.userId,
          recModifiedWhen = None,
          recModifiedBy = None
        )
      )
      result shouldBe expected
    }
  }

  // helper function
  "calculateDailyNet" should {
    "return correct amount" in {
      val amount          = BigDecimal(100)
      val totalBookingDay = 3
      val digit           = 3
      val result          = mapper.calculateDailyNet(amount, totalBookingDay, digit)
      val expected        = Seq(BigDecimal(33.333), BigDecimal(33.333), BigDecimal(33.334))
      result shouldBe expected
    }
    "return empty if total day is zero" in {
      val amount          = BigDecimal(100)
      val totalBookingDay = 0
      val digit           = 3
      val result          = mapper.calculateDailyNet(amount, totalBookingDay, digit)
      val expected        = Seq.empty
      result shouldBe expected
    }
  }

  "enrichBreakdownMissingChargeDate" should {
    val defaultBreakdown = sampleBreakdown
    val usdDigit         = 2
    val totalStayPeriod  = 3
    val checkInDate      = DateTime.now

    "return same breakdown if chargeDate is defined" in {
      val input = defaultBreakdown.copy(
        chargeDate = Some(DateTime.now)
      )
      val expected = Seq(input)
      mapper.enrichBreakdownMissingChargeDate(input, usdDigit, totalStayPeriod, checkInDate) shouldBe expected
    }

    "return same breakdown if chargeDate is None and type not RoomCharge and ExtraBed" in {
      val input = defaultBreakdown.copy(
        chargeDate = None,
        typeId = BreakDownTypeID.Discount.id
      )
      val expected = Seq(input)
      mapper.enrichBreakdownMissingChargeDate(input, usdDigit, totalStayPeriod, checkInDate) shouldBe expected
    }

    "return same breakdown if chargeDate is None and type RoomCharge or ExtraBed" in {
      val input = defaultBreakdown.copy(
        chargeDate = None,
        typeId = BreakDownTypeID.RoomCharge.id
      )
      val expected = Seq(
        input.copy(localAmount = 33.33, usdAmount = 33.33, chargeDate = Some(checkInDate)),
        input.copy(localAmount = 33.33, usdAmount = 33.33, chargeDate = Some(checkInDate.plusDays(1))),
        input.copy(localAmount = 33.34, usdAmount = 33.34, chargeDate = Some(checkInDate.plusDays(2)))
      )
      mapper.enrichBreakdownMissingChargeDate(input, usdDigit, totalStayPeriod, checkInDate) shouldBe expected
    }
  }

  "getBookingContext" should {
    val checkInDate    = DateTime.parse("2019-11-01T13:00:00.30")
    val checkOutDate   = DateTime.parse("2019-11-07T13:00:00.40")
    val bookingId      = 1
    val itineraryId    = 1
    val hotelId        = 1
    val noOfAdults     = 1
    val noOfChildren   = 0
    val request        = baseReq
    val requestContext = mock[RequestContext]
    val ebeHotelInfo   = mock[EBEHotel]
    val roomInfo       = mock[BookingRoom]
    val bapiBooking    = mock[BAPIBooking]
    val hotelInfo = HotelInfo(
      hotelId = hotelId,
      GMTOffSet = 1,
      isAllowAmendment = false,
      isYCSMigration = true,
      ycsAllotmentNotManagedByAgoda = false
    )
    val dmcCode         = "mock"
    val bookingDateTime = DateTime.parse("2019-11-01T13:00:00.50")
    when(ebeHotelInfo.checkIn).thenReturn(checkInDate)
    when(ebeHotelInfo.checkOut).thenReturn(checkOutDate)
    when(roomInfo.hotelId).thenReturn(hotelId)
    when(roomInfo.noOfAdults).thenReturn(noOfAdults)
    when(roomInfo.noOfChildren).thenReturn(noOfChildren)
    when(roomInfo.dmcId).thenReturn(Some(DmcId.YCS.id))
    when(roomInfo.paymentModels).thenReturn(PaymentModel.Merchant)
    when(roomInfo.isPrepaymentRequired).thenReturn(None)
    when(bapiBooking.productPayment).thenReturn(None)

    "should return bookingContextCorrectly" in {
      val expected = BookingContext(
        request = request,
        requestContext = requestContext,
        bookingId = bookingId,
        itineraryId = itineraryId,
        hotelId = hotelId,
        noOfAdults = noOfAdults,
        noOfChildren = noOfChildren,
        totalStayPeriod = 6,
        bookingDate = bookingDateTime,
        checkInDate = checkInDate.withMillisOfSecond(0),
        checkOutDate = checkOutDate.withMillisOfSecond(0),
        isYCSMigration = true,
        hotelGMTOffset = 1,
        dmcCode = dmcCode,
        chargeOption = ChargeOption.PayNow,
        isAgentAssist = true,
        fullyAuthDate = bookingDateTime,
        fullyChargeDate = bookingDateTime,
        agodaManagedYcs = true,
        isAgencyPrePay = false
      )
      val result = mapper.getBookingContext(
        request,
        requestContext,
        itineraryId,
        bookingId,
        ebeHotelInfo,
        roomInfo,
        hotelInfo,
        dmcCode,
        bookingDateTime,
        bapiBooking,
        isAgentAssistantBooking = false
      )

      result shouldBe expected
    }

    "returns AgentAssistBooking true when Agent name is not empty and CAPI token doesn't not contain AAB flag true" in {
      val expected = BookingContext(
        request,
        requestContext = requestContext,
        bookingId = bookingId,
        itineraryId = itineraryId,
        hotelId = hotelId,
        noOfAdults = noOfAdults,
        noOfChildren = noOfChildren,
        totalStayPeriod = 6,
        bookingDate = bookingDateTime,
        checkInDate = checkInDate.withMillisOfSecond(0),
        checkOutDate = checkOutDate.withMillisOfSecond(0),
        isYCSMigration = true,
        hotelGMTOffset = 1,
        dmcCode = dmcCode,
        chargeOption = ChargeOption.PayNow,
        isAgentAssist = true,
        fullyAuthDate = bookingDateTime,
        fullyChargeDate = bookingDateTime,
        agodaManagedYcs = true,
        isAgencyPrePay = false
      )
      val result = mapper.getBookingContext(
        request,
        requestContext,
        itineraryId,
        bookingId,
        ebeHotelInfo,
        roomInfo,
        hotelInfo,
        dmcCode,
        bookingDateTime,
        bapiBooking,
        isAgentAssistantBooking = true
      )

      result.isAgentAssist shouldBe expected.isAgentAssist
    }

    "returns AgentAssistBooking true when Agent name is empty and CAPI token contains AAB flag true" in {
      val requestWithoutAgentName = request.copy(agentAssist = None)
      val expected = BookingContext(
        request,
        requestContext = requestContext,
        bookingId = bookingId,
        itineraryId = itineraryId,
        hotelId = hotelId,
        noOfAdults = noOfAdults,
        noOfChildren = noOfChildren,
        totalStayPeriod = 6,
        bookingDate = bookingDateTime,
        checkInDate = checkInDate.withMillisOfSecond(0),
        checkOutDate = checkOutDate.withMillisOfSecond(0),
        isYCSMigration = true,
        hotelGMTOffset = 1,
        dmcCode = dmcCode,
        chargeOption = ChargeOption.PayNow,
        isAgentAssist = true,
        fullyAuthDate = bookingDateTime,
        fullyChargeDate = bookingDateTime,
        agodaManagedYcs = true,
        isAgencyPrePay = false
      )
      val result = mapper.getBookingContext(
        requestWithoutAgentName,
        requestContext,
        itineraryId,
        bookingId,
        ebeHotelInfo,
        roomInfo,
        hotelInfo,
        dmcCode,
        bookingDateTime,
        bapiBooking,
        isAgentAssistantBooking = true
      )

      result.isAgentAssist shouldBe expected.isAgentAssist
    }

    "returns Agency PrePay true iff Agency and prePaymentRequired" in {
      when(roomInfo.paymentModels).thenReturn(PaymentModel.Agency)
      when(roomInfo.isPrepaymentRequired).thenReturn(Some(true))
      val expected = BookingContext(
        request = request,
        requestContext = requestContext,
        bookingId = bookingId,
        itineraryId = itineraryId,
        hotelId = hotelId,
        noOfAdults = noOfAdults,
        noOfChildren = noOfChildren,
        totalStayPeriod = 6,
        bookingDate = bookingDateTime,
        checkInDate = checkInDate.withMillisOfSecond(0),
        checkOutDate = checkOutDate.withMillisOfSecond(0),
        isYCSMigration = true,
        hotelGMTOffset = 1,
        dmcCode = dmcCode,
        chargeOption = ChargeOption.PayNow,
        isAgentAssist = true,
        fullyAuthDate = bookingDateTime,
        fullyChargeDate = bookingDateTime,
        agodaManagedYcs = true,
        isAgencyPrePay = true
      )
      val result = mapper.getBookingContext(
        request,
        requestContext,
        itineraryId,
        bookingId,
        ebeHotelInfo,
        roomInfo,
        hotelInfo,
        dmcCode,
        bookingDateTime,
        bapiBooking,
        isAgentAssistantBooking = false
      )

      result shouldBe expected
    }

    "returns Agency PrePay false if prePaymentRequired but merchant" in {
      when(roomInfo.paymentModels).thenReturn(PaymentModel.Merchant)
      when(roomInfo.isPrepaymentRequired).thenReturn(Some(true))
      val expected = BookingContext(
        request = request,
        requestContext = requestContext,
        bookingId = bookingId,
        itineraryId = itineraryId,
        hotelId = hotelId,
        noOfAdults = noOfAdults,
        noOfChildren = noOfChildren,
        totalStayPeriod = 6,
        bookingDate = bookingDateTime,
        checkInDate = checkInDate.withMillisOfSecond(0),
        checkOutDate = checkOutDate.withMillisOfSecond(0),
        isYCSMigration = true,
        hotelGMTOffset = 1,
        dmcCode = dmcCode,
        chargeOption = ChargeOption.PayNow,
        isAgentAssist = true,
        fullyAuthDate = bookingDateTime,
        fullyChargeDate = bookingDateTime,
        agodaManagedYcs = true,
        isAgencyPrePay = false
      )
      val result = mapper.getBookingContext(
        request,
        requestContext,
        itineraryId,
        bookingId,
        ebeHotelInfo,
        roomInfo,
        hotelInfo,
        dmcCode,
        bookingDateTime,
        bapiBooking,
        isAgentAssistantBooking = false
      )

      result shouldBe expected
    }

    "returns Agency PrePay false if prePaymentRequired=false and agency" in {
      when(roomInfo.paymentModels).thenReturn(PaymentModel.Agency)
      when(roomInfo.isPrepaymentRequired).thenReturn(Some(false))
      val expected = BookingContext(
        request = request,
        requestContext = requestContext,
        bookingId = bookingId,
        itineraryId = itineraryId,
        hotelId = hotelId,
        noOfAdults = noOfAdults,
        noOfChildren = noOfChildren,
        totalStayPeriod = 6,
        bookingDate = bookingDateTime,
        checkInDate = checkInDate.withMillisOfSecond(0),
        checkOutDate = checkOutDate.withMillisOfSecond(0),
        isYCSMigration = true,
        hotelGMTOffset = 1,
        dmcCode = dmcCode,
        chargeOption = ChargeOption.PayNow,
        isAgentAssist = true,
        fullyAuthDate = bookingDateTime,
        fullyChargeDate = bookingDateTime,
        agodaManagedYcs = true,
        isAgencyPrePay = false
      )
      val result = mapper.getBookingContext(
        request,
        requestContext,
        itineraryId,
        bookingId,
        ebeHotelInfo,
        roomInfo,
        hotelInfo,
        dmcCode,
        bookingDateTime,
        bapiBooking,
        isAgentAssistantBooking = false
      )

      result shouldBe expected
    }

    "get BNPL from product payment" in {
      val fullyAuthDate   = "2019-11-03T11:00:00.50"
      val fullyChargeDate = "2019-11-05T13:00:00.50"

      when(bapiBooking.productPayment).thenReturn(
        Some(
          ProductPaymentInfo(
            agency = None,
            payNow = None,
            payLater = Some(
              PayLaterProductPayment(
                payment = ProductPayment(),
                exchangeRateOption = None,
                fullyAuthDate = LocalDateTime.parse(fullyAuthDate),
                fullyChargeDate = LocalDateTime.parse(fullyChargeDate)
              )
            )
          )
        )
      )

      val expectedChargeOption    = ChargeOption.PayLater
      val expectedFullyAuthDate   = DateTime.parse(fullyAuthDate)
      val expectedFullyChargeDate = DateTime.parse(fullyChargeDate)

      val result = mapper.getBookingContext(
        request,
        requestContext,
        itineraryId,
        bookingId,
        ebeHotelInfo,
        roomInfo,
        hotelInfo,
        dmcCode,
        bookingDateTime,
        bapiBooking,
        isAgentAssistantBooking = false
      )

      result.chargeOption shouldBe expectedChargeOption
      result.fullyAuthDate shouldBe expectedFullyAuthDate
      result.fullyChargeDate shouldBe expectedFullyChargeDate
    }
  }

  "getChildRoomInfo" should {
    "return ChildRoom object correctly" in {
      val input      = mock[BAPIBooking]
      val masterRoom = mock[MasterRoom]
      val expected   = mock[ChildRoom]

      when(input.masterRooms).thenReturn(Seq(masterRoom))
      when(masterRoom.childrenRooms).thenReturn(List(expected))

      val result = mapper.getChildRoomInfo(input)
      result shouldBe Some(expected)
    }
    "return None if ChildRoom object not found" in {
      val input      = mock[BAPIBooking]
      val masterRoom = mock[MasterRoom]

      when(input.masterRooms).thenReturn(Seq(masterRoom))
      when(masterRoom.childrenRooms).thenReturn(List.empty)

      val result = mapper.getChildRoomInfo(input)
      result shouldBe None
    }
  }

  "getSurchargeDescription" should {

    val templateDescription = " noOfNight:[NIGHTS], noOfAdult:[ADULTS], noOfChild:[CHILDREN]"
    val normalDescription   = "mockDesciption" + templateDescription
    val ycsDescription      = "mockDesciptionYCS" + templateDescription
    val surchargeInfo: Option[SurchargeInfoDB] = Some(
      SurchargeInfoDB(
        chargeTypeId = 1,
        chargeDescription = "mockDescription",
        defaultUnitOfMeasure = "",
        redirectChargeTypeId = 1,
        description = Some(normalDescription),
        descriptionYCS = Some(ycsDescription)
      )
    )

    "return `Surcharge` if surchargeInfo is None" in {
      val isYCSMigrationHotel = true
      val noOfNight           = 1
      val noOfAdult           = 1
      val noOfChildren        = 0
      val expected            = "Surcharge"

      val result = mapper.getSurchargeDescription(None, isYCSMigrationHotel, noOfNight, noOfAdult, noOfChildren)
      result shouldBe expected
    }

    "return ycs description if isYCSMigrationHotel true" in {
      val isYCSMigrationHotel = true
      val noOfNight           = 1
      val noOfAdult           = 1
      val noOfChildren        = 0
      val expected            = s"mockDesciptionYCS noOfNight:$noOfNight, noOfAdult:$noOfAdult, noOfChild:$noOfChildren"

      val result =
        mapper.getSurchargeDescription(surchargeInfo, isYCSMigrationHotel, noOfNight, noOfAdult, noOfChildren)
      result shouldBe expected
    }

    "return normal description if isYCSMigrationHotel false" in {
      val isYCSMigrationHotel = false
      val noOfNight           = 1
      val noOfAdult           = 1
      val noOfChildren        = 0
      val expected            = s"mockDesciption noOfNight:$noOfNight, noOfAdult:$noOfAdult, noOfChild:$noOfChildren"

      val result =
        mapper.getSurchargeDescription(surchargeInfo, isYCSMigrationHotel, noOfNight, noOfAdult, noOfChildren)
      result shouldBe expected
    }
  }

  "getSecondFraudCheckDate" should {
    val currentDate = DateTime.parse("2019-11-05T00:00:00.00")

    "return current date if current date before second fraud check date" in {
      val fullyAuthDateTime      = DateTime.parse("2019-11-07T00:00:00.00")
      val secondFraudCheckConfig = 2
      val secondFraudDate        = fullyAuthDateTime.minusDays(secondFraudCheckConfig)
      val expected               = secondFraudDate.toLocalDate.toDateTimeAtCurrentTime.withTimeAtStartOfDay().withHourOfDay(0)
      val result                 = mapper.getSecondFraudCheckDate(fullyAuthDateTime, currentDate, secondFraudCheckConfig)
      result shouldBe expected
    }

    "return current date if current date after second fraud check date" in {
      val fullyAuthDateTime      = DateTime.parse("2019-11-05T00:00:00.00")
      val secondFraudCheckConfig = 2
      val expected               = currentDate.toLocalDate.toDateTimeAtCurrentTime.withTimeAtStartOfDay().withHourOfDay(0)
      val result                 = mapper.getSecondFraudCheckDate(fullyAuthDateTime, currentDate, secondFraudCheckConfig)
      result shouldBe expected
    }
  }

  "validator" should {
    "getPaymentModel" should {
      val roomInfo     = mock[BookingRoom]
      val payment      = mock[BookingPayment]
      val rateCategory = mock[BookingRateCategory]

      "return what it is if not UnknownType and not Agency" in {
        when(roomInfo.paymentModels).thenReturn(PaymentModel.Merchant)
        val result = mapper.getPaymentModel(roomInfo, payment, WhiteLabel.Agoda)
        result.success.value shouldBe PaymentModel.Merchant
      }
      "return exception if UnknownType" in {
        when(roomInfo.paymentModels).thenReturn(PaymentModel.Unknown)
        val result = mapper.getPaymentModel(roomInfo, payment, WhiteLabel.Agoda)
        result.failure.exception shouldBe a[PaymentModelNotFound]
      }
      "return Agency if not OTARurubu Agency" in {
        when(roomInfo.paymentModels).thenReturn(PaymentModel.Agency)
        when(roomInfo.rateCategory).thenReturn(Some(rateCategory))
        when(rateCategory.InventoryTypeId).thenReturn(Some(InventoryType.OTARurubu.value))
        when(payment.method).thenReturn(PaymentMethod.None)
        val result = mapper.getPaymentModel(roomInfo, payment, WhiteLabel.Agoda)
        result.success.value shouldBe PaymentModel.Agency
      }
      "return Merchant if OTARurubu Agency with payment method exist" in {
        val whiteLabelInfo = mock[WhiteLabelInfo]
        implicit val context: RequestContext = MockRequestContext
          .create()
          .copy(
            featureAware = Some(featureAware),
            whiteLabelInfo = whiteLabelInfo
          )
        when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsRurubuWl)).thenReturn(true)
        when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Rurubu)

        when(roomInfo.paymentModels).thenReturn(PaymentModel.Agency)
        when(roomInfo.rateCategory).thenReturn(Some(rateCategory))
        when(rateCategory.InventoryTypeId).thenReturn(Some(InventoryType.OTARurubu.value))
        when(payment.method).thenReturn(PaymentMethod.Visa)

        val result = mapper.getPaymentModel(roomInfo, payment, WhiteLabel.Rurubu)
        result.success.value shouldBe PaymentModel.Merchant
      }
    }

    "getAvailabilityType" should {
      "return what it is if not UnknownType" in {
        val input  = AvailabilityType.Guarantee
        val result = mapper.getAvailabilityType(input)
        result.success.value shouldBe input
      }
      "return exception if UnknownType" in {
        val input  = AvailabilityType.Unknown
        val result = mapper.getAvailabilityType(input)
        result.failure.exception shouldBe a[AvailabilityTypeNotFound]
      }
    }

    "getEBEHotelInfo" should {
      "return what EBEHotel object correctly" in {
        val input       = mock[BAPIBooking]
        val bookingItem = mock[BookingItem]
        val ebeBooking  = mock[EBEBooking]
        val expected    = mock[EBEHotel]

        when(input.booking).thenReturn(Some(bookingItem))
        when(bookingItem.booking).thenReturn(List(ebeBooking))
        when(ebeBooking.hotel).thenReturn(List(expected))

        val result = mapper.getEBEHotelInfo(input)
        result.success.value shouldBe expected
      }
      "return exception if EBEHotel not found" in {
        val input       = mock[BAPIBooking]
        val bookingItem = mock[BookingItem]
        val ebeBooking  = mock[EBEBooking]

        when(input.booking).thenReturn(Some(bookingItem))
        when(bookingItem.booking).thenReturn(List(ebeBooking))
        when(ebeBooking.hotel).thenReturn(List.empty)

        val result = mapper.getEBEHotelInfo(input)
        result.failure.exception shouldBe a[EBEHotelNotFound]
      }
    }

    "getEBERoomInfo" should {
      "return what BookingRoom object correctly" in {
        val input    = mock[EBEHotel]
        val expected = mock[BookingRoom]

        when(input.room).thenReturn(List(expected))

        val result = mapper.getEBERoomInfo(input)
        result.success.value shouldBe expected
      }
      "return exception if BookingRoom not found" in {
        val input = mock[EBEHotel]

        when(input.room).thenReturn(List.empty)

        val result = mapper.getEBERoomInfo(input)
        result.failure.exception shouldBe a[EBEBookingRoomNotFound]
      }
    }
  }

  "mapping" should {
    val chargeUtils         = new EbeBookingChargeMapperUtilsImpl()
    val mockPropertyRequest = mock[RequestWithProducts]
    val bookingId           = 1
    val actionId            = 1
    val multiProductId      = Some(1L)
    val productTokenKey     = Some("0")
    val jtbBapiBookingToken = getObjectFromFile[BAPIBooking]("test_ebe_mapper/input/BAPIBooking_29014.json").get
    val ycsBapiBookingToken = getObjectFromFile[BAPIBooking]("test_ebe_mapper/input/BAPIBooking_332.json").get
    val bapiBookingToken    = getObjectFromFile[BAPIBooking]("test_ebe_mapper/input/BAPIBooking.json").get
    val bapi3DSSPBSToken    = getObjectFromFile[BAPIBooking]("test_ebe_mapper/input/BAPIBooking_3DS_SBPS.json").get
    val surchargeList       = getObjectFromFile[Seq[SurchargeInfoDB]]("test_ebe_mapper/input/SurchargeInfoList.json").get

    val expected =
      readStringFromSource("test_ebe_mapper/output/PropertyModelInternalTestResult.json")
        .replace("[DATA_CENTER]", ServerUtils.serverDc())
        .replace("[SERVER_NAME]", ServerUtils.serverHostName())

    val product = Product[RoomInfo](
      bookingType = BookingType.CreditCard,
      info = RoomInfo(
        roomUid = bapiBookingToken.booking.head.booking.head.hotel.head.room.head.uid,
        bapiBooking = bapiBookingToken,
        specialRequest = Some("mock special request"),
        roomTypeName = Some("mock roomTypeName"),
        greetingMessage = Some("mock greetingMessage"),
        unmodifiedSpecialRequest = Some(
          SpecialRequests(
            selectedRequests = Seq.empty,
            additionalNotes = Some("additionalNotes"),
            arrivalTime = Some("14:00"),
            bookerAnswer = Some("bookingAnswer")
          )
        )
      )
    )
    val productFor3DS = Product[RoomInfo](
      bookingType = BookingType.CreditCard,
      info = RoomInfo(
        roomUid = bapi3DSSPBSToken.booking.head.booking.head.hotel.head.room.head.uid,
        bapiBooking = bapi3DSSPBSToken,
        specialRequest = Some("mock special request"),
        roomTypeName = Some("mock roomTypeName"),
        greetingMessage = Some("mock greetingMessage"),
        unmodifiedSpecialRequest = Some(
          SpecialRequests(
            selectedRequests = Seq.empty,
            additionalNotes = Some("additionalNotes"),
            arrivalTime = Some("14:00"),
            bookerAnswer = Some("bookingAnswer")
          )
        )
      )
    )
    val productJTB = Product[RoomInfo](
      bookingType = BookingType.CreditCard,
      info = RoomInfo(
        roomUid = bapiBookingToken.booking.head.booking.head.hotel.head.room.head.uid,
        bapiBooking = jtbBapiBookingToken,
        specialRequest = Some("mock special request"),
        roomTypeName = Some("mock roomTypeName"),
        greetingMessage = Some("mock greetingMessage"),
        unmodifiedSpecialRequest = Some(
          SpecialRequests(
            selectedRequests = Seq.empty,
            additionalNotes = Some("additionalNotes"),
            arrivalTime = Some("14:00"),
            bookerAnswer = Some("bookingAnswer")
          )
        )
      )
    )
    val productYCS = Product[RoomInfo](
      bookingType = BookingType.CreditCard,
      info = RoomInfo(
        roomUid = bapiBookingToken.booking.head.booking.head.hotel.head.room.head.uid,
        bapiBooking = ycsBapiBookingToken,
        specialRequest = Some("mock special request"),
        roomTypeName = Some("mock roomTypeName"),
        greetingMessage = Some("mock greetingMessage"),
        unmodifiedSpecialRequest = Some(
          SpecialRequests(
            selectedRequests = Seq.empty,
            additionalNotes = Some("additionalNotes"),
            arrivalTime = Some("14:00"),
            bookerAnswer = Some("bookingAnswer")
          )
        )
      )
    )
    val reserveId = ReservedIds[RoomInfo, EmptyProductReservedIds](
      bookingId = bookingId,
      actionId = actionId,
      multiProductId = multiProductId,
      product = product,
      essInfoId = Some(101)
    )
    val reserveIdFor3DS =
      ReservedIds[RoomInfo, EmptyProductReservedIds](bookingId, actionId, multiProductId, productFor3DS)
    val reserveIdForJTB =
      ReservedIds[RoomInfo, EmptyProductReservedIds](bookingId, actionId, multiProductId, productJTB)
    val reserveIdForYCS =
      ReservedIds[RoomInfo, EmptyProductReservedIds](bookingId, actionId, multiProductId, productYCS)

    val ccyInfo      = CurrencyInfo(currencyId = 1, currencyCode = "USD", noDecimal = 2)
    val supplierInfo = Supplier(id = 332, code = "YCS", name = "YCS", dmcType = 1, isPull = None)
    val hotelInfo = HotelInfo(
      hotelId = 2259031,
      GMTOffSet = 5,
      isAllowAmendment = true,
      isYCSMigration = false,
      ycsAllotmentNotManagedByAgoda = false
    )
    val hotelGuests = baseReq.guests

    val overrideRequest = baseReq
      .modify(_.giftCardEarning.each.giftCardGuid)
      .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
      .modify(_.guests)
      .setTo(None)
      .modify(_.products.propertyItems.each.each.productTokenKey)
      .setTo(productTokenKey)
      .modify(_.products.propertyItems.each.each.guests)
      .setTo(hotelGuests)
    // .modify(_.userAgent.searchCluster)
    // .setTo(Some("hk-int-2x"))
    when(mockPropertyRequest.request).thenReturn(overrideRequest)
    when(mockPropertyRequest.requestContext).thenReturn(
      defaultRequestContext.copy(featureAware = Some(featureAwareMock))
    )

    when(mockPropertyRequest.userTaxCountryId).thenReturn(Some(12345))
    val userTaxCountry = UserTaxCountry(Some(12345), Some(12345), Some(12345), Some(12345))
    when(mockPropertyRequest.userTaxCountry).thenReturn(Some(userTaxCountry))
    val defaultCountries = Seq(CountryInfo(35, "INDIA", "INDIA", "IND", "IN"))
    when(countriesRepository.getCountries()).thenReturn(Future.successful(defaultCountries))
    val getSelfServiceUrl          = (bookingId: BookingId) => "test.com"
    val getJapanicanSelfServiceUrl = (bookingId: BookingId) => "http://master.qa.notyja.com/?id"
    when(urlService.getSelfServiceURLs(whitelabelId = WhiteLabel.Agoda, dmcId = Some(332)))
      .thenReturn(Future.successful(getSelfServiceUrl))
    when(urlService.getSelfServiceURLs(whitelabelId = WhiteLabel.Agoda, dmcId = Some(29014)))
      .thenReturn(Future.successful(getSelfServiceUrl))
    when(urlService.getSelfServiceURLs(whitelabelId = WhiteLabel.Rurubu, dmcId = Some(29014)))
      .thenReturn(Future.successful((_: BookingId) => ""))
    when(urlService.getSelfServiceURLs(whitelabelId = WhiteLabel.Japanican, dmcId = Some(29014)))
      .thenReturn(Future.successful(getJapanicanSelfServiceUrl))
    when(featureAwareMock.isRemoveFenceSelfServiceURLsForRurubu).thenReturn(false)

    "return expected property model from input request" in {
      prepareCreationCdbProxy()
      prepareMessageServiceMock()

      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))
      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      ) {
        override def withMeasureAndLogWithContext[T](
            context: MeasurementsContext
        )(metricName: String, additionalTags: Map[String, String] = Map.empty)(f: Future[T]): Future[T] = f
      }

      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 894981423,
        productIndex = None,
        masterActionId = 10011001L
      )

      result.map { result =>
        val booking      = result.bookingList.headOption
        val bookingToken = booking.flatMap(_.bookingToken.map(_.token)).getOrElse("")
        val recCreatedWhen =
          booking.flatMap(_.essInfos.headOption.map(_.recCreatedWhen)).getOrElse(DateTime.now).toString
        val actual = JacksonSerializer.mapper.writeValueAsString(result)
        val replaceExpected = expected
          .replace("[REC_CREATED_WHEN]", recCreatedWhen)
          .replace("[BOOKING_TOKEN]", bookingToken)
        val expectedModelWithBookingToken =
          JacksonSerializer.mapper.readValue[PropertyModelInternal](replaceExpected)
        val minifiedStringExpectedModel = JacksonSerializer.mapper.writeValueAsString(expectedModelWithBookingToken)
        result.bookingList.head.booking.preBookingId shouldBe Some(894981423)
        actual shouldEqual minifiedStringExpectedModel
        result.bookingList.head.baseBooking shouldBe empty
      }
    }

    "correctly  map cc-Received field for Pay-later" in {
      prepareCreationCdbProxy()
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })
      val cc = baseReq.payment.creditCard.map(_.copy(chargeOption = ChargeOption.PayLater))

      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)
        .modify(_.payment.creditCard)
        .setTo(cc)

      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))
      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )
      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        cancellationChargeItemId = None,
        masterActionId = 10011001L
      )
      result.map { result =>
        result.bookingList.head.booking.ccReceived shouldBe Some(CCReceived.USD_1_PreAuth.id)
      }
    }

    "correctly  map occupancy" in {
      prepareCreationCdbProxy()
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })

      val occupancy = Seq(
        Occupancy(
          roomNo = 1,
          noOfAdultMales = 1,
          noOfAdultFemales = 1,
          childOccupancy = Some(
            Seq(
              ChildOccupancy(typeId = ChildType.ChildA, count = 1)
            )
          )
        )
      )
      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.occupancy)
        .setTo(Option(occupancy))

      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))
      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )
      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )
      val expectedOccupancy = PropertyBookingOccupancyBuilder(occupancy).head
      result.map { result =>
        result.bookingList.head.additionalData.occupancy.head.head shouldBe expectedOccupancy
      }
    }

    "correctly  map supplier data" in {
      prepareCreationCdbProxy()
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })

      val supplierData = SupplierData(
        supplierSiteId = Some("siteId"),
        supplierSubSiteId = Some("subsiteId"),
        supplierTransactionId = Some("transactionId"),
        externalBookingId = Some("externalBookingId"),
        supplierAdditionalInfo = Some("supplierAdditionalInfo"),
        supplierFreeItem = Some("supplierFreeItem"),
        supplierReservationInfo = Some("supplierReservationInfo"),
        supplierChainId = Some(0),
        authenticationKey = Some("JTB123456"),
        paymentChannel = Some(SupplierPaymentChannel.PayAtProperty)
      )

      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)
        .modify(_.supplierData)
        .setTo(Some(supplierData))

      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))
      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )
      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )
      val expectedSupplier = PropertyBookingSupplierBuilder(Some(supplierData))
      result.map { result =>
        result.bookingList.head.additionalData.supplierData shouldBe expectedSupplier
      }
    }

    "correctly map booking discount information" in {
      prepareCreationCdbProxy()
      prepareCreationCdbProxy()
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })

      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)

      val whiteLabelInfo = mock[WhiteLabelInfo]
      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext.copy(whiteLabelInfo = whiteLabelInfo))

      when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsRurubuWl)).thenReturn(true)
      when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Rurubu)
      when(whiteLabelInfo.feature).thenReturn(mockFeatureConfig)

      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))
      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )
      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveIdForJTB,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )

      result.map { result =>
        result.bookingList.head.bookingDiscountInformations.size shouldBe 2
        result.bookingList.head.bookingDiscountInformations.map(_.discountId).toSet shouldBe Set(1, 2)
        result.bookingList.head.bookingDiscountInformations.map(_.appliedDate).toSet shouldBe Set("12/13", "12/14")
        result.bookingList.head.bookingDiscountInformations.map(_.discountRateDouble).toSet shouldBe Set(10.0, 10.0)
        result.bookingList.head.bookingDiscountInformations.map(_.discountName).toSet shouldBe Set(
          "channel discount 2",
          "ycs promotion 1"
        )
      }
    }

    "correctly map booking discount information for YCS DMC when enableAllDmcBookingToSaveDiscountInformation" in {
      val mockFeatureAware = mock[FeatureAware]
      prepareCreationCdbProxy()
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })

      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)

      when(mockFeatureAware.enableAllDmcBookingToSaveDiscountInformation).thenReturn(true)
      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(
        MockRequestContext.create(featureAware = Some(mockFeatureAware))
      )
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))
      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )
      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveIdForYCS,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )

      result.map { result =>
        result.bookingList.head.bookingDiscountInformations.size shouldBe 2
        result.bookingList.head.bookingDiscountInformations.map(_.discountId).toSet shouldBe Set(1, 2)
        result.bookingList.head.bookingDiscountInformations.map(_.appliedDate).toSet shouldBe Set("12/13", "12/14")
        result.bookingList.head.bookingDiscountInformations.map(_.discountRateDouble).toSet shouldBe Set(10.0, 10.0)
        result.bookingList.head.bookingDiscountInformations.map(_.discountName).toSet shouldBe Set(
          "channel discount 2",
          "ycs promotion 1"
        )
      }
    }

    "correctly  map child promotions" in {
      prepareCreationCdbProxy()
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })

      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))
      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )
      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )

      val childPromos =
        bapiBookingToken.booking.map(_.booking).map(_.flatMap(_.childPromotions)).getOrElse(List.empty).flatten
      val expectedChildPromotions = Option(PropertyBookingChildPromotionBuilder(childPromos))

      result.map { result =>
        result.bookingList.head.booking.childPromotions shouldBe expectedChildPromotions
      }
    }

    "correctly  build child promotions" in {
      val actualChildPromotions = Option(
        PropertyBookingChildPromotionBuilder(
          List(
            ChildPromotion(1, "promoCode1", 12, 13),
            ChildPromotion(2, "promoCode1", 22, 23),
            ChildPromotion(3, "promoCode1", 32, 33)
          )
        )
      )

      actualChildPromotions.get.size shouldBe 3
    }

    "isNotCCRequired" should {
      "correctly replace when WhiteLabel is Rurubu, InventoryType equal 1 and payment method not equal 0" in {
        prepareCreationCdbProxy()
        prepareMessageServiceMock()
        when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int]))
          .thenReturn(Future.successful(Some(supplierInfo)))
        when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
        when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
          .thenAnswer((invocation: InvocationOnMock) => {
            val chargeId = invocation.getArgument[Int](0)
            Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
          })
        val overrideRequestFor3DS = overrideRequest
          .modify(_.payment.method)
          .setTo(PaymentMethod.Visa)

        val featureAware   = mock[FeatureAware]
        val whiteLabelInfo = mock[WhiteLabelInfo]
        implicit val context: RequestContext = MockRequestContext
          .create()
          .copy(
            featureAware = Some(featureAware),
            whiteLabelInfo = whiteLabelInfo
          )
        when(whiteLabelInfo.isFeatureEnabled(WhiteLabelFeatureName.IsRurubuWl)).thenReturn(true)
        when(whiteLabelInfo.whiteLabelId).thenReturn(WhiteLabel.Rurubu)
        when(whiteLabelInfo.feature).thenReturn(mockFeatureConfig)

        when(mockPropertyRequest.request).thenReturn(overrideRequestFor3DS)
        when(mockPropertyRequest.requestContext).thenReturn(
          context
        )

        when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))
        when(urlService.getSelfServiceURLs(whitelabelId = WhiteLabel.Rurubu, dmcId = Some(29014))(context))
          .thenReturn(Future.successful((_: BookingId) => ""))

        val mapper = new PropertyModelInternalMapperImpl(
          ebeLiteBookingRepository,
          ebeConfig,
          dateTimeUtils,
          CancellationUtils,
          chargeUtils,
          propertyBookingSummaryBuilder,
          urlService,
          creationCDBProxyMock,
          customerAPITokenUtilsMock,
          countriesRepository
        )

        val result = mapper.mapPropertyModelInternal(
          createRequest = mockPropertyRequest,
          itineraryId = 1,
          reservedInfo = reserveIdFor3DS,
          tripStart = None,
          tripEnd = None,
          prebookingId = 0,
          productIndex = None,
          masterActionId = 10011001L
        )

        result.map { result =>
          val inventoryTypeId          = result.bookingList.flatMap(_.rateCategory).flatMap(_.inventoryTypeId).headOption
          val whitelabelId             = result.bookingList.flatMap(_.bookingSummary.whitelabelId).headOption
          val overridedIsNotCcRequired = result.bookingList.flatMap(_.bookingSummary.isNotCcRequired).headOption
          val rurubuUrl                = result.bookingList.flatMap(_.additionalData.selfServiceUrl).headOption

          rurubuUrl.get shouldBe ""
          whitelabelId.get shouldBe WhiteLabel.Rurubu.id
          inventoryTypeId.get shouldBe InventoryType.OTARurubu.value
          overridedIsNotCcRequired.get shouldBe false
        }
      }

      "correctly NOT replace when WhiteLabel is not Rurubu" in {
        prepareCreationCdbProxy()
        prepareMessageServiceMock()
        when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int]))
          .thenReturn(Future.successful(Some(supplierInfo)))
        when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
        when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
          .thenAnswer((invocation: InvocationOnMock) => {
            val chargeId = invocation.getArgument[Int](0)
            Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
          })
        val overrideRequestFor3DS = overrideRequest
          .modify(_.payment.method)
          .setTo(PaymentMethod.Visa)

        when(mockPropertyRequest.request).thenReturn(overrideRequestFor3DS)
        when(mockPropertyRequest.requestContext).thenReturn(
          MockRequestContext.create(whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Japanican, new FeaturesConfiguration))
        )
        when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))

        val mapper = new PropertyModelInternalMapperImpl(
          ebeLiteBookingRepository,
          ebeConfig,
          dateTimeUtils,
          CancellationUtils,
          chargeUtils,
          propertyBookingSummaryBuilder,
          urlService,
          creationCDBProxyMock,
          customerAPITokenUtilsMock,
          countriesRepository
        )

        val result = mapper.mapPropertyModelInternal(
          createRequest = mockPropertyRequest,
          itineraryId = 1,
          reservedInfo = reserveIdFor3DS,
          tripStart = None,
          tripEnd = None,
          prebookingId = 0,
          productIndex = None,
          masterActionId = 10011001L
        )

        result.map { result =>
          val inventoryTypeId          = result.bookingList.flatMap(_.rateCategory).flatMap(_.inventoryTypeId).headOption
          val whitelabelId             = result.bookingList.flatMap(_.bookingSummary.whitelabelId).headOption
          val overridedIsNotCcRequired = result.bookingList.flatMap(_.bookingSummary.isNotCcRequired).headOption

          whitelabelId.get shouldBe WhiteLabel.Japanican.id
          inventoryTypeId.get shouldBe InventoryType.OTARurubu.value
          overridedIsNotCcRequired.get shouldBe true
        }
      }
    }

    "correctly map PropertyBookingSupplierData" in {
      prepareCreationCdbProxy()
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })

      val supplierData = SupplierData(
        supplierSiteId = Some("siteId"),
        supplierSubSiteId = Some("subsiteId"),
        supplierTransactionId = Some("transactionId"),
        externalBookingId = Some("externalBookingId"),
        supplierAdditionalInfo = Some("supplierAdditionalInfo"),
        supplierFreeItem = Some("supplierFreeItem"),
        supplierReservationInfo = Some("supplierReservationInfo"),
        supplierChainId = Some(0),
        authenticationKey = Some("JTB123456"),
        paymentChannel = Some(SupplierPaymentChannel.PayAtProperty)
      )

      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)
        .modify(_.supplierData)
        .setTo(Some(supplierData))

      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))
      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )
      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )
      result.map { r =>
        val bookingSupplierData = r.bookingList.head.bookingSupplierData
        bookingSupplierData.flatMap(_.supplierSiteId) shouldBe Some("siteId")
        bookingSupplierData.flatMap(_.supplierSubSiteId) shouldBe Some("subsiteId")
        bookingSupplierData.flatMap(_.supplierTransactionId) shouldBe Some("transactionId")
        bookingSupplierData.flatMap(_.additionalInfo) shouldBe Some("supplierAdditionalInfo")
        bookingSupplierData.flatMap(_.freeItem) shouldBe Some("supplierFreeItem")
        bookingSupplierData.flatMap(_.reservationInfo) shouldBe Some("supplierReservationInfo")
        bookingSupplierData.flatMap(_.inventoryData) shouldBe None
        bookingSupplierData.flatMap(_.childRateSettings) shouldBe Some(
          "[{\"childRateTypeId\":\"1\",\"childRatePricingTypeId\":\"3\",\"value\":100.0,\"isCountAsRoomOcc\":true},{\"childRateTypeId\":\"1\",\"childRatePricingTypeId\":\"4\",\"value\":0.0,\"isCountAsRoomOcc\":false}]"
        )
        bookingSupplierData.flatMap(_.paymentChannel) shouldBe Some(1)
      }
    }

    "correctly map PropertyBookingHotelRoomChildren" in {
      prepareCreationCdbProxy()
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })

      val occupancy = Seq(
        Occupancy(
          roomNo = 1,
          noOfAdultMales = 1,
          noOfAdultFemales = 1,
          childOccupancy = Some(
            Seq(
              ChildOccupancy(typeId = ChildType.ChildA, count = 2)
            )
          )
        )
      )

      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.occupancy)
        .setTo(Option(occupancy))

      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))

      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )

      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )

      result.map { r =>
        val bookingHotelRoomChildren = r.bookingList.head.bookingHotelRoomChildren.headOption
        bookingHotelRoomChildren.map(_.childRateTypeId) shouldBe Some(1)
        bookingHotelRoomChildren.map(_.number) shouldBe Some(2)
      }
    }

    "correctly map PropertyBookingChildPromotions" in {
      prepareCreationCdbProxy()
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })

      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)

      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))

      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )

      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )

      result.map { r =>
        val bookingChildPromos = r.bookingList.head.bookingChildPromotions.headOption
        bookingChildPromos.map(_.campaignId) shouldBe Some(701)
        bookingChildPromos.flatMap(_.promotionCode) shouldBe Some("CHILD_CP_1")
        bookingChildPromos.map(_.amount) shouldBe Some(110.0)
        bookingChildPromos.map(_.amountUsd) shouldBe Some(110.0)
        bookingChildPromos.map(_.isActive) shouldBe Some(true)
      }
    }

    "correctly map PropertyBookingPax" should {
      "map with roomIndex" in {
        prepareCreationCdbProxy()
        prepareMessageServiceMock()
        when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int]))
          .thenReturn(Future.successful(Some(supplierInfo)))
        when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
        when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
          .thenAnswer((invocation: InvocationOnMock) => {
            val chargeId = invocation.getArgument[Int](0)
            Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
          })

        val hotelGuests = baseReq.guests
          .modify(_.each.each.roomIndex)
          .setTo(Some(2))

        val overrideRequest = baseReq
          .modify(_.giftCardEarning.each.giftCardGuid)
          .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
          .modify(_.guests)
          .setTo(None)
          .modify(_.products.propertyItems.each.each.productTokenKey)
          .setTo(productTokenKey)
          .modify(_.products.propertyItems.each.each.guests)
          .setTo(hotelGuests)
        when(mockPropertyRequest.request).thenReturn(overrideRequest)
        when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)

        when(mockPropertyRequest.request).thenReturn(overrideRequest)
        when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
        when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))

        val mapper = new PropertyModelInternalMapperImpl(
          ebeLiteBookingRepository,
          ebeConfig,
          dateTimeUtils,
          CancellationUtils,
          chargeUtils,
          propertyBookingSummaryBuilder,
          urlService,
          creationCDBProxyMock,
          customerAPITokenUtilsMock,
          countriesRepository
        )

        val result = mapper.mapPropertyModelInternal(
          createRequest = mockPropertyRequest,
          itineraryId = 1,
          reservedInfo = reserveId,
          tripStart = None,
          tripEnd = None,
          prebookingId = 0,
          productIndex = None,
          masterActionId = 10011001L
        )

        result.map { r =>
          val bookingPax = r.bookingList.head.propertyBookingPax.headOption
          bookingPax.map(_.bookingRoomReferenceId) shouldBe Some(1)
          bookingPax.map(_.referenceId) shouldBe Some(2)
        }
      }
      "not map with zero roomIndex" in {
        prepareCreationCdbProxy()
        prepareMessageServiceMock()
        when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int]))
          .thenReturn(Future.successful(Some(supplierInfo)))
        when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
        when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
          .thenAnswer((invocation: InvocationOnMock) => {
            val chargeId = invocation.getArgument[Int](0)
            Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
          })

        val hotelGuests = baseReq.guests
          .modify(_.each.each.roomIndex)
          .setTo(Some(0))

        val overrideRequest = baseReq
          .modify(_.giftCardEarning.each.giftCardGuid)
          .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
          .modify(_.guests)
          .setTo(None)
          .modify(_.products.propertyItems.each.each.productTokenKey)
          .setTo(productTokenKey)
          .modify(_.products.propertyItems.each.each.guests)
          .setTo(hotelGuests)
        when(mockPropertyRequest.request).thenReturn(overrideRequest)
        when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)

        when(mockPropertyRequest.request).thenReturn(overrideRequest)
        when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
        when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))

        val mapper = new PropertyModelInternalMapperImpl(
          ebeLiteBookingRepository,
          ebeConfig,
          dateTimeUtils,
          CancellationUtils,
          chargeUtils,
          propertyBookingSummaryBuilder,
          urlService,
          creationCDBProxyMock,
          customerAPITokenUtilsMock,
          countriesRepository
        )

        val result = mapper.mapPropertyModelInternal(
          createRequest = mockPropertyRequest,
          itineraryId = 1,
          reservedInfo = reserveId,
          tripStart = None,
          tripEnd = None,
          prebookingId = 0,
          productIndex = None,
          masterActionId = 10011001L
        )

        result.map { r =>
          val bookingPax = r.bookingList.head.propertyBookingPax.headOption
          bookingPax.map(_.bookingRoomReferenceId) shouldBe Some(1)
          bookingPax.map(_.referenceId) shouldBe Some(1)
        }
      }
    }

    "correctly map PropertyRewardEarning" in {
      prepareCreationCdbProxy()
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })

      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)

      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))

      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )

      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )

      result.map { r =>
        val rewardEarning = r.bookingList.head.rewardEarning
        rewardEarning.map(_.bookingId) shouldBe Some(1)
        rewardEarning.map(_.itineraryId) shouldBe Some(1)
        rewardEarning.map(_.rewardTypeId) shouldBe Some(2)
        rewardEarning.map(_.rewardJsonToken) shouldBe Some(
          "{\"itineraryId\":1,\"bookingId\":1,\"whiteLabelId\":1,\"dateOffset\":1,\"expiryDays\":120,\"usdAmount\":1234.0,\"cashBackGuid\":\"407df5a2-e5f6-4b94-9c23-007e5427d336\",\"cashBackPercentage\":2.0,\"cashbackVersion\":null,\"cashbackType\":null,\"agodaCashbackValue\":null,\"promocodeCashbackValue\":null,\"cofundedCashbackValue\":null,\"promotionId\":null}"
        )
        rewardEarning.map(_.recCreatedWhen) shouldBe Some(DateTime.parse("2019-11-20T08:50:00.000Z"))
        rewardEarning.map(_.recCreatedBy) shouldBe Some(UUID.fromString("7424c488-3e7b-4416-b9ad-9ffa10c0422f"))
        rewardEarning.map(_.recModifyBy) shouldBe Some(None)
        rewardEarning.map(_.recModifyWhen) shouldBe Some(None)
      }
    }

    "map selfServiceUrl in additionalBookingData" in {
      prepareCreationCdbProxy()
      prepareMessageServiceMock()

      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))

      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)
      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(
        MockRequestContext.create(whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Japanican, new FeaturesConfiguration))
      )

      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )
      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveIdForJTB,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )

      result.map { r =>
        val selfServiceUrl = r.bookingList.flatMap(_.additionalData.selfServiceUrl).headOption
        selfServiceUrl shouldBe Some("http://master.qa.notyja.com/?id")
      }
    }

    "handles failed selfServiceUrl" in {
      when(urlService.getSelfServiceURLs(WhiteLabel.Japanican))
        .thenReturn(Future.failed(new Exception("selfServiceUrl failed")))
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))
      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)
      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(
        MockRequestContext.create(whiteLabelInfo = WhiteLabelInfo(WhiteLabel.Japanican, new FeaturesConfiguration))
      )

      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )
      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )

      result.failed.map { r =>
        r shouldBe an[Exception]
      }
    }

    "correctly map PropertyBookingCampaign" in {
      prepareCreationCdbProxy()
      when(creationCDBProxyMock.getPlecsCampaignPromocode(any[Int]))
        .thenReturn(Future.successful(Some(PlecsCampaignPromocode(1, 1, isGTTCampaign = true))))
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int]))
        .thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })

      val hotelGuests = baseReq.guests
        .modify(_.each.each.roomIndex)
        .setTo(Some(2))

      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)

      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))

      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )

      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )

      result.map { r =>
        val propertyBookingCampaign = r.bookingList.head.bookingCampaign
        propertyBookingCampaign.map(_.bookingId) shouldBe Some(1)
        propertyBookingCampaign.map(_.campaignId) shouldBe Some(7)
        propertyBookingCampaign.map(_.campaignData) shouldBe Some("""{"PartnerToken":"","Last4digitsCitizenId":""}""")
      }
    }

    "correctly map booking wholesale partner" in {
      prepareCreationCdbProxy()
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })

      val bookingHoldingPartnerNames = Some(
        Seq(
          BookingHoldingPartner(Some(1), Some(123), Some("Priceline.com"))
        )
      )
      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)
        .modify(_.bookingHoldingPartnerNames)
        .setTo(bookingHoldingPartnerNames)

      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))

      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )

      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )

      val expected = Seq(PropertyBookingBookingHoldingPartnerName(bookingId, Some(1), Some(123), Some("Priceline.com")))
      result.map { result =>
        result.bookingList.head.additionalData.bookingHoldingPartnerNames shouldBe expected
      }
    }

    "correctly map PropertyBookingHotel when no occupancy in creation request" in {
      prepareCreationCdbProxy()
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })

      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)

      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))

      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )

      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )
      result.map { result =>
        result.bookingList.head.bookingHotel.noOfRooms shouldBe 1
        result.bookingList.head.bookingHotel.noOfAdults shouldBe 2
        result.bookingList.head.bookingHotel.noOfChildren shouldBe 0
      }
    }

    "correctly map PropertyBookingHotel when occupancy in creation request" in {
      prepareCreationCdbProxy()
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })

      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.occupancy)
        .setTo(occupancyWithGenderChildType)
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)

      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))

      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )

      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )
      result.map { result =>
        result.bookingList.head.bookingHotel.noOfRooms shouldBe 2
        result.bookingList.head.bookingHotel.noOfAdults shouldBe 5
        result.bookingList.head.bookingHotel.noOfChildren shouldBe 4
      }
    }

    "correctly map PropertyBookingHotel when occupancy is empty list in creation request" in {
      prepareCreationCdbProxy()
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })

      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.occupancy)
        .setTo(Some(Seq.empty))
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)

      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))

      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )

      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )
      result.map { result =>
        result.bookingList.head.bookingHotel.noOfRooms shouldBe 1
        result.bookingList.head.bookingHotel.noOfAdults shouldBe 2
        result.bookingList.head.bookingHotel.noOfChildren shouldBe 0
      }
    }

    "correctly map PropertyBookingHotel when occupancy is 0 in creation request" in {
      prepareCreationCdbProxy()
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })

      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.occupancy)
        .setTo(
          Some(
            Seq(
              Occupancy(
                roomNo = 1,
                noOfAdultMales = 0,
                noOfAdultFemales = 0,
                childOccupancy = Some(
                  Seq(
                    ChildOccupancy(typeId = ChildType.ChildA, count = 0),
                    ChildOccupancy(typeId = ChildType.ChildB, count = 0),
                    ChildOccupancy(typeId = ChildType.ChildC, count = 0),
                    ChildOccupancy(typeId = ChildType.ChildD, count = 0)
                  )
                )
              )
            )
          )
        )
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)

      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))

      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )

      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011011L
      )
      result.map { result =>
        result.bookingList.head.bookingHotel.noOfRooms shouldBe 1
        result.bookingList.head.bookingHotel.noOfAdults shouldBe 2
        result.bookingList.head.bookingHotel.noOfChildren shouldBe 0
      }
    }

    "correctly map PropertyBookingHotel when adult is 0, child is not 0 in creation request" in {
      prepareCreationCdbProxy()
      prepareMessageServiceMock()
      when(ebeLiteBookingRepository.getSupplierInfoByDmcId(any[Int])).thenReturn(Future.successful(Some(supplierInfo)))
      when(ebeLiteBookingRepository.getHotelInfoByHotelId(any[Long])).thenReturn(Future.successful(Some(hotelInfo)))
      when(ebeLiteBookingRepository.getSurchargeInfoByChargeId(any[Int]))
        .thenAnswer((invocation: InvocationOnMock) => {
          val chargeId = invocation.getArgument[Int](0)
          Future.successful(surchargeList.find(_.chargeTypeId == chargeId))
        })

      val overrideRequest = baseReq
        .modify(_.giftCardEarning.each.giftCardGuid)
        .setTo(UUID.fromString("407df5a2-e5f6-4b94-9c23-007e5427d336").toString)
        .modify(_.guests)
        .setTo(None)
        .modify(_.products.propertyItems.each.each.productTokenKey)
        .setTo(productTokenKey)
        .modify(_.products.propertyItems.each.each.occupancy)
        .setTo(
          Some(
            Seq(
              Occupancy(
                roomNo = 1,
                noOfAdultMales = 0,
                noOfAdultFemales = 0,
                childOccupancy = Some(
                  Seq(
                    ChildOccupancy(typeId = ChildType.ChildA, count = 1),
                    ChildOccupancy(typeId = ChildType.ChildB, count = 1),
                    ChildOccupancy(typeId = ChildType.ChildC, count = 0),
                    ChildOccupancy(typeId = ChildType.ChildD, count = 0)
                  )
                )
              )
            )
          )
        )
        .modify(_.products.propertyItems.each.each.guests)
        .setTo(hotelGuests)
        .modify(_.affiliatePaymentMethod)
        .setTo(Some(AffiliatePaymentMethod.CustomerCard))
        .modify(_.agentAssist)
        .setTo(None)

      when(mockPropertyRequest.request).thenReturn(overrideRequest)
      when(mockPropertyRequest.requestContext).thenReturn(defaultRequestContext)
      when(ebeLiteBookingRepository.getCurrencyInfo(any[String])).thenReturn(Future.successful(Some(ccyInfo)))

      val mapper = new PropertyModelInternalMapperImpl(
        ebeLiteBookingRepository,
        ebeConfig,
        dateTimeUtils,
        CancellationUtils,
        chargeUtils,
        propertyBookingSummaryBuilder,
        urlService,
        creationCDBProxyMock,
        customerAPITokenUtilsMock,
        countriesRepository
      )

      val result = mapper.mapPropertyModelInternal(
        createRequest = mockPropertyRequest,
        itineraryId = 1,
        reservedInfo = reserveId,
        tripStart = None,
        tripEnd = None,
        prebookingId = 0,
        productIndex = None,
        masterActionId = 10011001L
      )
      result.map { result =>
        result.bookingList.head.bookingHotel.noOfRooms shouldBe 1
        result.bookingList.head.bookingHotel.noOfAdults shouldBe 2
        result.bookingList.head.bookingHotel.noOfChildren shouldBe 0
      }
    }
  }

  private def prepareMessageServiceMock() = {
    when(
      messageServiceMock.sendMessage(
        PriceDisplaySettingMessage(
          baseBookingContext.bookingId,
          "YCS",
          "[{\"HotelId\":2259031,\"Type\":1,\"IsShowNetExclusive\":true,\"IsShowTaxFeesNet\":false,\"IsShowNetInclusive\":true,\"IsShowSellExclusive\":false,\"IsShowTaxFeesSell\":true,\"IsShowSellInclusive\":false,\"IsShowRefSellInclusive\":true,\"IsShowRefInclusiveAsSellInclusive\":false,\"PaymentModel\":1}]"
        )
      )
    ).thenReturn(Future.unit)
  }

  private def prepareCreationCdbProxy(): Unit = {
    when(creationCDBProxyMock.getAllPriceDisplaySettings(138165)).thenReturn(
      Future.successful(
        List(
          HotelPriceDisplaySetting(
            HotelId = 2259031,
            Type = 1,
            IsShowNetExclusive = true,
            IsShowTaxFeesNet = false,
            IsShowNetInclusive = true,
            IsShowSellExclusive = false,
            IsShowTaxFeesSell = true,
            IsShowSellInclusive = false,
            IsShowRefSellInclusive = true,
            IsShowRefInclusiveAsSellInclusive = false,
            PaymentModel = 1
          )
        )
      )
    )
    when(creationCDBProxyMock.getPlecsCampaignPromocode(any[Int])).thenReturn(Future.successful(None))
  }
}
