package com.agoda.bapi.creation.repository

import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.model.multiproduct.{MultiProductBookingGroupDBModel, MultiProductInfoDBModel}
import com.agoda.mpb.common.MultiProductType.MultiProductType
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.CreateMultiBookingHelper
import com.agoda.bapi.creation.model.db.MultiProductBookingInsertionModel
import com.agoda.bapi.creation.proxy.MultiProductDbProxy
import com.agoda.mpb.common.MultiProductType
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfterEach
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future

class MultiProductRepositoryTest
    extends AsyncWordSpec
    with Matchers
    with MockitoSugar
    with CreateMultiBookingHelper
    with BeforeAndAfterEach {

  implicit val requestContext = mock[RequestContext]
  val proxy                   = mock[MultiProductDbProxy]
  val repo                    = new MultiProductRepositoryImpl(proxy)

  override def beforeEach(): Unit = {
    reset(proxy)
  }

  "getNextMultiBookingSequenceNumber" should {
    "call getNextMultiBookingSequenceNumber in proxy correctly" in {
      val expected = 1L
      when(proxy.getNextMultiProductSequenceNumber).thenReturn(Future.successful(expected))
      repo.getNextMultiBookingSequenceNumber().map { result =>
        verify(proxy, times(1)).getNextMultiProductSequenceNumber
        result shouldBe expected
      }
    }
  }

  "getMultiProductType" should {
    "call getMultiProductType in proxy correctly" in {
      val multiProductId = 1L
      val expectedResult = Some(MultiProductInfoDBModel(multiProductId, MultiProductType.Package))
      when(proxy.getMultiProductInfo(any[Long])).thenReturn(Future.successful(expectedResult))
      repo.getMultiProductInfo(multiProductId).map { result =>
        verify(proxy, times(1)).getMultiProductInfo(multiProductId)
        result shouldBe expectedResult
      }
    }
  }

  "insertMultiBooking" should {
    "call insertMultiBooking in proxy correctly" in {
      val input = MultiProductBookingInsertionModel(
        Seq.empty,
        Seq.empty,
        baseItineraryDbModel,
        Seq.empty,
        Seq.empty,
        Seq.empty,
        Seq.empty,
        Seq.empty,
        cegFastTrackBookingActionState = Seq.empty,
        addOnsBookingActionState = Seq.empty,
        Seq.empty
      )
      when(proxy.insertMultiBooking(input)).thenReturn(Future.successful(input))
      repo.insertMultiBooking(input).map { result =>
        verify(proxy, times(1)).insertMultiBooking(input)
        result shouldBe input
      }
    }
  }

  "insertMultiProductInfo" should {
    "call insertMultiProductInfo in proxy correctly" in {
      val input = MultiProductInfoDBModel(10, MultiProductType.HackerFare)
      when(proxy.insertMultiProductInfo(input)).thenReturn(Future.successful(input))
      repo.insertMultiProductInfo(input).map { result =>
        verify(proxy, times(1)).insertMultiProductInfo(input)
        result shouldBe input
      }
    }
  }

  "getNextCartSequenceNumber" should {
    "call getNextCartSequenceNumber in proxy correctly" in {
      val expected = 2L
      when(proxy.getNextCartSequenceNumber()).thenReturn(Future.successful(expected))
      repo.getNextCartSequenceNumber().map { result =>
        verify(proxy, times(1)).getNextCartSequenceNumber()
        result shouldBe expected
      }
    }
  }

  "getNextOperationIdSequenceNumber" should {
    val requestContextMock = mock[RequestContext]
    val featureAwareMock   = mock[FeatureAware]
    when(requestContextMock.featureAware).thenReturn(Some(featureAwareMock))

    "call getNextOperationIdSequenceNumber in proxy" in {
      val expected = 2L
      when(proxy.getNextOperationIdSequenceNumber()).thenReturn(Future.successful(expected))
      implicit val requestContext: RequestContext = requestContextMock
      repo.getNextOperationIdSequenceNumber().map { result =>
        verify(proxy).getNextOperationIdSequenceNumber()
        result shouldBe expected
      }
    }
  }

  "getNextPackageSequenceNumber" should {
    "call getNextPackageSequenceNumber in proxy correctly" in {
      val expected = 5L
      when(proxy.getNextPackageSequenceNumber()).thenReturn(Future.successful(expected))
      repo.getNextPackageSequenceNumber().map { result =>
        verify(proxy, times(1)).getNextPackageSequenceNumber()
        result shouldBe expected
      }
    }
  }

  "getMultiProductBookingGroup" should {
    "call getMultiProductBookingGroup in proxy correctly" in {
      val bookingId = 5L
      val expectedResult =
        Some(MultiProductBookingGroupDBModel(bookingId = 11L, itineraryId = 12L, cartId = 1L, packageId = Some(5L)))
      when(proxy.getMultiProductBookingGroup(any[Long])).thenReturn(Future.successful(expectedResult))
      repo.getMultiProductBookingGroup(bookingId).map { result =>
        verify(proxy, times(1)).getMultiProductBookingGroup(bookingId)
        result shouldBe expectedResult
      }
    }
  }

  "getMultiProductBookingGroupByItineraryId" should {
    "call getMultiProductBookingGroupByItineraryId in proxy correctly" in {
      val itineraryId = 5L
      val expectedResult =
        Seq(MultiProductBookingGroupDBModel(bookingId = 11L, itineraryId = 12L, cartId = 1L, packageId = Some(5L)))
      when(proxy.getMultiProductBookingGroupByItineraryId(any[Long])).thenReturn(Future.successful(expectedResult))
      repo.getMultiProductBookingGroupByItineraryId(itineraryId).map { result =>
        verify(proxy, times(1)).getMultiProductBookingGroupByItineraryId(itineraryId)
        result shouldBe expectedResult
      }
    }
  }

  "insertMultiProductBookingGroup" should {
    "call insertMultiProductBookingGroup in proxy correctly" in {
      val input = MultiProductBookingGroupDBModel(bookingId = 11L, itineraryId = 12L, cartId = 1L, packageId = Some(5L))
      when(proxy.insertMultiProductBookingGroup(input)).thenReturn(Future.successful(input))
      repo.insertMultiProductBookingGroup(input).map { result =>
        verify(proxy, times(1)).insertMultiProductBookingGroup(input)
        result shouldBe input
      }
    }
  }
}
