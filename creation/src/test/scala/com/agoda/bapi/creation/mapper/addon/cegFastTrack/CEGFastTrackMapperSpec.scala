package com.agoda.bapi.creation.mapper.addon.cegFastTrack

import com.agoda.bapi.common.MockRequestContext
import com.agoda.bapi.common.config.AgodaConfig
import com.agoda.bapi.common.handler.RequestContext
import com.agoda.bapi.common.message.creation.MpbWorkflowPhase
import com.agoda.bapi.common.model.addOn.UserTaxCountry
import com.agoda.bapi.common.model.addOn.cegFastTrack.AddOnBookingToken
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.common.model.flight.history.ActionType
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.service.FeatureAware
import com.agoda.bapi.creation.mapper.ebe.{StateSchemaVersion, WorkFlowStateId}
import com.agoda.bapi.creation.model.db.{BookingActionState, BookingActionStateCustomer, CreationRequest, PaymentInfo}
import com.agoda.bapi.creation.model.multi.{EmptyProductReservedIds, MultiProductsRequest, Product, ReservedIds}
import com.agoda.bapi.creation.model.rooms.RoomInfo
import com.agoda.capi.enigma.shared_model.common.RecStatus
import com.agoda.mpb.common.models.state.ProductType
import com.agoda.mpb.common.{BookingType, MultiProductType, WorkflowId}
import com.agoda.mpbe.state.booking._
import com.agoda.mpbe.state.common.enums.Gateway.Gateway
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.agoda.mpbe.state.product.cegFastTrack.CegFastTrackProductModel
import com.softwaremill.diffx.Diff
import com.softwaremill.diffx.generic.auto._
import com.softwaremill.diffx.scalatest.DiffShouldMatcher._
import mocks.AddOnModelMock
import org.mockito.Mockito.when
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatest.{AppendedClues, BeforeAndAfter, OptionValues}
import org.scalatestplus.mockito.MockitoSugar

class CegFastTrackMapperSpec
    extends AsyncWordSpec
    with Matchers
    with AppendedClues
    with BeforeAndAfter
    with OptionValues
    with MockitoSugar {

  "toProductModel" should {
    "return productModel properly when all reserved IDs are generated" in {
      val featureAware                     = mock[FeatureAware]
      implicit val context: RequestContext = MockRequestContext.create().copy(featureAware = Some(featureAware))
      implicit val baseBookingMetaDiff: Diff[BaseBookingEssInfo] = Diff
        .derived[BaseBookingEssInfo]
        .ignore(_.recCreatedWhen)

      val scope = new CegFastTrackMapperSpecScope {}
      import scope._

      val expectedEssInfo = BaseBookingEssInfo(
        bookingEssInfoId = 55555L,
        bookingId = 54321,
        userTaxCountryId = 9999,
        recStatus = RecStatus.Active.value,
        recCreatedWhen = baseBookingLocalDateTime,
        recCreatedBy = bapiIdentifierUuid,
        bookerResidenceCountryId = Some(9998),
        paymentInstrumentCountryId = Some(9997),
        ipAddressCountryId = Some(9996)
      )
      val expectedFinancialBreakdowns = defaultCegFastTrackProductModel.product.breakdowns.zipWithIndex.map {
        case (breakdown, index) =>
          breakdown.copy(breakdownId = index.toLong + 1L)
      }
      val expectedProductModel: CegFastTrackProductModel = defaultCegFastTrackProductModel.copy(product =
        defaultCegFastTrackProductModel.product
          .copy(essInfo = Some(expectedEssInfo), breakdowns = expectedFinancialBreakdowns)
      )
      val resultProductModel: CegFastTrackProductModel = cegFastTrackMapper
        .toProductModel(
          request = addOnDefaultMultiProductsRequest,
          itineraryId = defaultItineraryId,
          masterActionId = defaultActionId,
          reservedIds = reservedIdsWithBreakdownKeys,
          baseBookingMetas = defaultCegFastTrackBaseBookingMetas,
          bookingDateTime = baseBookingDateTime,
          userTaxCountry = Some(
            UserTaxCountry(
              userTaxCountryId = Some(9999),
              bookerResidenceCountryId = Some(9998),
              paymentInstrumentCountryId = Some(9997),
              ipAddressCountryId = Some(9996)
            )
          )
        )

      resultProductModel shouldMatchTo expectedProductModel
    }

    "return productModel properly with overridden userTaxCountryId  when all reserved IDs are generated" in {
      val featureAware                     = mock[FeatureAware]
      implicit val context: RequestContext = MockRequestContext.create().copy(featureAware = Some(featureAware))
      implicit val baseBookingMetaDiff: Diff[BaseBookingEssInfo] = Diff
        .derived[BaseBookingEssInfo]
        .ignore(_.recCreatedWhen)

      val scope = new CegFastTrackMapperSpecScope {}
      import scope._

      val expectedEssInfo = BaseBookingEssInfo(
        bookingEssInfoId = 55555L,
        bookingId = 54321,
        userTaxCountryId = 12345678,
        recStatus = RecStatus.Active.value,
        recCreatedWhen = baseBookingLocalDateTime,
        recCreatedBy = bapiIdentifierUuid,
        bookerResidenceCountryId = Some(9998),
        paymentInstrumentCountryId = Some(9997),
        ipAddressCountryId = Some(9996)
      )
      val expectedFinancialBreakdowns = defaultCegFastTrackProductModel.product.breakdowns.zipWithIndex.map {
        case (breakdown, index) =>
          breakdown.copy(breakdownId = index.toLong + 1L)
      }
      val expectedProductModel: CegFastTrackProductModel = defaultCegFastTrackProductModel.copy(product =
        defaultCegFastTrackProductModel.product
          .copy(essInfo = Some(expectedEssInfo), breakdowns = expectedFinancialBreakdowns)
      )
      val resultProductModel: CegFastTrackProductModel = cegFastTrackMapper
        .toProductModel(
          request = addOnDefaultMultiProductsRequest,
          itineraryId = defaultItineraryId,
          masterActionId = defaultActionId,
          reservedIds = reservedIdsWithBreakdownKeys,
          baseBookingMetas = defaultCegFastTrackBaseBookingMetas,
          bookingDateTime = baseBookingDateTime,
          userTaxCountry = Some(
            UserTaxCountry(
              userTaxCountryId = Some(9999), // legacy value
              bookerResidenceCountryId = Some(9998),
              paymentInstrumentCountryId = Some(9997),
              ipAddressCountryId = Some(9996)
            )
          ),
          userTaxCountryId = Some(12345678) // overriden value
        )

      resultProductModel shouldMatchTo expectedProductModel
    }
  }

  "toBookingAction" should {
    "return bookingAction properly" in {
      val scope = new CegFastTrackMapperSpecScope {}
      import com.agoda.bapi.common.util.converters.ItineraryStateConverters._
      import scope._
      val expectedBookingAction: BookingWorkflowAction = BookingWorkflowAction(
        actionId = 1234,
        itineraryId = 12345,
        bookingType = Some(BookingType.CEGFastTrack.id),
        bookingId = Some(54321),
        memberId = 0,
        actionTypeId = ActionType.Created.id,
        correlationId = "",
        requestId = "",
        workflowId = WorkflowId.CegFastTrack.id,
        workflowStateId = WorkFlowStateId.CegFastTrack,
        productTypeId = Some(ProductType.CEGFastTrack.id),
        stateSchemaVersion = StateSchemaVersion.Default,
        state = "",
        storefrontId = Some(3),
        languageId = Some(1),
        workflowPhaseId = Option(MpbWorkflowPhase.Received.id)
      )
      val resultBookingAction: BookingWorkflowAction = cegFastTrackMapper
        .toBookingAction(
          request = addOnDefaultMultiProductsRequest,
          itineraryId = defaultItineraryId,
          reservedIds = defaultReservedIds,
          bookingActionState = BookingActionState(
            mockCreationRequest,
            BookingActionStateCustomer(isUserLoggedIn = false),
            PaymentInfo(
              method = PaymentMethod.Visa,
              paymentCurrency = "THB",
              paymentAmount = 2150.0,
              paymentAmountUSD = 60.0,
              siteExchangeRate = Some(0.5),
              gateway = Some(Gateway.GMO),
              destinationCurrency = None,
              destinationExchangeRate = None,
              points = Vector.empty
            ),
            creditCardInfo = defaultMultiProductsRequest.getCreditCardInfo,
            bookingState = None,
            campaignInfo = None,
            itineraryProtoState = toItineraryState(defaultCegFastTrackProductModel).toBase64OptString
          )
        )
        .copy(state = "")

      resultBookingAction shouldBe expectedBookingAction
    }
  }

  "toBookingActionState" should {
    "return BookingActionState properly" in {
      val scope = new CegFastTrackMapperSpecScope {}
      import com.agoda.bapi.common.util.converters.ItineraryStateConverters._
      import scope._

      val expectedBookingActionState: BookingActionState = BookingActionState(
        mockCreationRequest,
        BookingActionStateCustomer(),
        PaymentInfo(
          method = PaymentMethod.Visa,
          paymentCurrency = "THB",
          paymentAmount = 2150,
          paymentAmountUSD = 60,
          siteExchangeRate = Some(0.5),
          gateway = Some(Gateway.GMO),
          destinationCurrency = None,
          destinationExchangeRate = None,
          points = Vector.empty
        ),
        creditCardInfo = defaultMultiProductsRequest.getCreditCardInfo,
        campaignInfo = None,
        bookingState = None,
        itineraryProtoState = toItineraryState(defaultCegFastTrackProductModel).toBase64OptString,
        productPaymentInfo = baseCegFastTrackToken.productPayment,
        whitelabelToken = baseRequestContext.whiteLabelInfo.token
      )
      val resultBookingActionState: BookingActionState = cegFastTrackMapper.toBookingActionState(
        request = defaultMultiProductsRequest,
        itineraryId = defaultItineraryId,
        reservedIds = defaultReservedIds,
        productModel = defaultCegFastTrackProductModel,
        bookingDateTime = baseBookingDateTime
      )

      resultBookingActionState shouldBe expectedBookingActionState
    }
  }

  "getMetaContents" should {
    "return correct mapped meta" in {
      val scope = new CegFastTrackMapperSpecScope {}
      import scope._
      val expectedMetas: Seq[BaseBookingMeta] = Seq(
        BaseBookingMeta(
          metaName = Some("CEG_FASTTRACK_TIER"),
          metaType = MetaType.MetaType.STRING,
          metaValue = Some(
            "NormalHotelFastTrack"
          ),
          version = 1,
          metaTypeString = Some("STRING")
        )
      )
      val resF = cegFastTrackMapper.getMetaContents(defaultReservedIds)
      resF.map(res => res shouldBe expectedMetas)
    }
  }

  trait CegFastTrackMapperSpecScope extends MockitoSugar with AddOnModelMock {
    val defaultHotelBookingId     = 54320
    val defaultFastTrackBookingId = 54321
    val room1: RoomInfo           = mock[RoomInfo]
    val expectedRoom1: Product[RoomInfo] = Product(
      mock[BookingType.BookingType],
      room1,
      Some(MultiProductType.SingleProperty)
    )

    val defaultMultiProductsRequest: MultiProductsRequest = MultiProductsRequest(
      request = baseCegFastTrackReq,
      requestContext = baseRequestContext,
      properties = Seq(expectedRoom1),
      flights = Seq.empty,
      vehicles = Seq.empty,
      protections = Seq.empty,
      activities = Seq.empty,
      cegFastTracks = Seq(baseCegFastTrackProduct),
      addOns = Seq.empty,
      bookingFlow = BookingFlow.SingleProperty,
      commonPayment = None,
      isBookingFromCart = None
    )

    val mockCreationRequest: CreationRequest = defaultMultiProductsRequest.toCreationRequest

    val defaultReservedIds: ReservedIds[AddOnBookingToken, EmptyProductReservedIds] = ReservedIds(
      bookingId = defaultFastTrackBookingId,
      actionId = defaultActionId,
      multiProductId = Some(7768),
      product = Product(
        bookingType = BookingType.CEGFastTrack,
        info = baseCegFastTrackToken,
        multiProductType = Some(MultiProductType.CEGFastTrack)
      ),
      essInfoId = Some(defaultEssInfoId)
    )

    val reservedIdsWithBreakdownKeys: ReservedIds[AddOnBookingToken, EmptyProductReservedIds] = defaultReservedIds.copy(
      breakdownIds = Seq(1L, 2L, 3L)
    )

    val mockAgodaConfig: AgodaConfig = mock[AgodaConfig]
    when(mockAgodaConfig.bapiIdentifierUuid).thenReturn(bapiIdentifierUuid)
    val cegFastTrackMapper = new CegFastTrackMapperImpl(mockAgodaConfig)
  }
}
