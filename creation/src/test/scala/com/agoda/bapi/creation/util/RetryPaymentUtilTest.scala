package com.agoda.bapi.creation.util

import com.agoda.bapi.common.exception.BookingTypeNotFoundException
import com.agoda.bapi.common.model.product.ProductTypeEnum
import com.agoda.bapi.creation.RetryBookingTestHelper
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.scalatestplus.mockito.MockitoSugar

class RetryPaymentUtilTest extends AsyncWordSpec with Matchers with MockitoSugar with RetryBookingTestHelper {

  "getProductType" should {
    "throw error if No product is present in  token" in {
      a[ProductsNotFoundInBookingToken] must be thrownBy RetryPaymentUtil.getProductType(
        genMultiProductCreationBookingToken()
      )
    }

    "return ProductType 'Flight' for only flight Product in Token" in {
      val p      = genMultiProductCreationBookingToken(hasFlight = true)
      val result = RetryPaymentUtil.getProductType(p)
      result shouldBe ProductTypeEnum.Flight
    }

    "return ProductType 'Car' for only Car Product in Token" in {
      val result = RetryPaymentUtil.getProductType(genMultiProductCreationBookingToken(hasCar = true))
      result shouldBe ProductTypeEnum.Car
    }

    "return ProductType 'Activity' for only Activity Product in Token" in {
      val result = RetryPaymentUtil.getProductType(genMultiProductCreationBookingToken(hasActivity = true))
      result shouldBe ProductTypeEnum.Activity
    }
    "return ProductType 'Property' for only Property Product in Token" in {
      val result = RetryPaymentUtil.getProductType(genMultiProductCreationBookingToken(hasProperty = true))
      result shouldBe ProductTypeEnum.Property
    }

    "return ProductType 'Multi' if more than one Product in Token" in {
      val result = RetryPaymentUtil.getProductType(
        genMultiProductCreationBookingToken(hasProperty = true, hasFlight = true)
      )
      result shouldBe ProductTypeEnum.Multi
    }
  }
}
