package mocks

import com.agoda.bapi.common.message.creation.BreakDownTypeID
import com.agoda.bapi.common.message.creation.{ActivityBookingAnswer, ActivityPax, ActivityPaxMetaInfo, CreatedBookingStatus, CreditCard, Customer, PaxType, PaymentAmount}
import com.agoda.bapi.common.message.multi.product.MultiProductInfoSetState
import com.agoda.bapi.common.model.OfferType.PerPerson
import com.agoda.bapi.common.model.activity.ActivityBookingQuestionEnums.Code.SPECIAL_REQUIREMENT
import com.agoda.bapi.common.model.activity.ActivityBookingQuestionEnums.DataType.STRING
import com.agoda.bapi.common.model.activity.ActivityBookingQuestionEnums.GroupType.PER_BOOKING
import com.agoda.bapi.common.model.activity.SummaryElementEnums.DisplayType.{ADULT, CHILD}
import com.agoda.bapi.common.model.activity.{ActivityAnswerRange, ActivityBookingInfo, ActivityBookingOffer, ActivityBookingPriceSummary, ActivityBookingQuestion, ActivityBookingQuestionEnums, ActivityBookingToken, ActivityCancellationInfo, ActivityLanguageGuide, ActivityLanguageGuideEnums, ActivitySupplierInfo, ActivityTime, DisplayPromocodeItem, PaxInfo, PaxSummary}
import com.agoda.bapi.common.model.booking.BookingActionForMessage
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.common.model.flight.flightModel.{BookingPaymentState, ItineraryHistory, MultiProductItinerary, PaymentState}
import com.agoda.bapi.common.model.flight.history.ActionType
import com.agoda.bapi.common.model.multiproduct.MultiProductBookingGroupDBModel
import com.agoda.mpb.common.MultiProductType.MultiProductType
import com.agoda.bapi.common.model.payment.PaymentModel.Merchant
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.model.{CancellationClass, ChargeOption, PriceBreakdown}
import com.agoda.bapi.common.util.JodaToJavaDateTimeConversions
import com.agoda.bapi.creation.CreateMultiBookingHelper
import com.agoda.bapi.creation.model.activity.messaging.ActivityBookingMessage
import com.agoda.bapi.creation.model.multi.MultiProductsRequest
import com.agoda.capi.enigma.shared_model.booking.pax.{BaseBookingPax => EnigmaBaseBookingPax}
import com.agoda.capi.enigma.shared_model.common.{Metadata, RecStatus}
import com.agoda.commons.agprotobuf.types.TimeOfDay
import com.agoda.mpb.common.MultiProductType
import com.agoda.mpb.common.PriceType.{PerAdult, PerBooking, PerChild}
import com.agoda.mpb.common.models.state.{AccountingEntity, ExternalLoyaltyInfo, PayNowProductPayment, PointsAttributes, ProductPayment, ProductPaymentInfo, ProductType}
import com.agoda.mpbe.state.booking._
import com.agoda.mpbe.state.common.enums.PaymentMethod.PaymentMethod
import com.agoda.mpbe.state.product.activity.ActivityProductModel
import org.joda.time.format.{DateTimeFormat, DateTimeFormatter}
import org.joda.time.{DateTime, LocalDate}

import java.time.{Instant, LocalDateTime}
import java.util.UUID

trait ActivityModelMock extends CreateMultiBookingHelper {

  val defaultStaticDateTime                = LocalDateTime.parse("2021-08-19T18:05:30.00")
  private val formatter: DateTimeFormatter = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZZ")

  val defaultBookingId    = 54321L
  val defaultItineraryId  = 12345
  val defaultProductId    = 26
  val defaultOfferId      = 11111L
  val defaultMetaId       = 22222
  val defaultGeoId        = 33333
  val defaultPaxId        = 44444
  val defaultEssInfoId    = 55555L
  val defaultSupplierId   = 50001
  val defaultActionId     = 1234L
  val defaultBreakdownId1 = 6001L
  val defaultBreakdownId2 = 6002L
  val defaultBreakdownIds = Seq(defaultBreakdownId1, defaultBreakdownId2)

  val mockMultiProductInfoSetState = MultiProductInfoSetState(1, MultiProductType.SingleActivity.id)
  val defaultActivityItinerary = MultiProductItinerary(
    itineraryId = defaultItineraryId,
    memberId = 1,
    recStatus = Some(1),
    recCreatedWhen = Some(DateTime.parse("2019-08-02T16:01")),
    recModifiedWhen = Some(DateTime.parse("2019-08-02T16:01"))
  )

  val activityDefaultHistory = ItineraryHistory(
    actionId = 0,
    itineraryId = 0,
    bookingType = None,
    bookingId = None,
    actionType = ActionType.Created.id,
    version = 0,
    actionDate = DateTime.parse("2019-08-02T16:01"),
    parameters = "",
    description = "",
    recStatus = Some(1),
    recCreatedWhen = Some(DateTime.parse("2019-08-02T16:01"))
  )

  val activityDefaultPayment = PaymentState(
    referenceId = 1,
    paymentId = 0L,
    itineraryId = 0L,
    actionId = None,
    creditCardId = None,
    transactionDate = DateTime.parse("2019-08-02T16:01"),
    transactionType = 0,
    paymentState = 0,
    referenceNo = "",
    referenceType = 0,
    last4Digits = "",
    paymentMethodId = 0,
    gatewayId = 0,
    transactionId = "",
    paymentCurrency = "",
    paymentAmount = 0,
    amountUsd = 0,
    supplierCurrency = "",
    supplierAmount = 0,
    exchangeRateSupplierToPayment = 0,
    creditCardCurrency = "",
    upliftAmount = 0,
    siteExchangeRate = 0,
    upliftExchangeRate = 0,
    paymentTypeId = None,
    token = None,
    installmentPlanId = None,
    referencePaymentId = None
  )

  val activityDefaultBookingPayment = BookingPaymentState(
    paymentId = 0,
    bookingId = 0,
    paymentCurrency = "",
    paymentAmount = BigDecimal(0),
    amountUsd = BigDecimal(0),
    recStatus = Some(1),
    recCreatedWhen = Some(DateTime.parse("2019-08-02T16:01")),
    fxiUplift = Some(1.02)
  )

  val activityDefaultMultiProductsRequest = MultiProductsRequest(
    request = baseActivityReq,
    requestContext = baseRequestContext,
    properties = Seq.empty,
    flights = Seq.empty,
    vehicles = Seq.empty,
    protections = Seq.empty,
    activities = Seq(baseActivityProduct),
    cegFastTracks = Seq.empty,
    addOns = Seq.empty,
    bookingFlow = BookingFlow.SingleActivity,
    commonPayment = None,
    isBookingFromCart = None
  )

  val multiActivityDefaultMultiProductsRequest = MultiProductsRequest(
    request = baseMultiActivityReq,
    requestContext = baseRequestContext,
    properties = Seq.empty,
    flights = Seq.empty,
    vehicles = Seq.empty,
    protections = Seq.empty,
    activities = Seq(baseActivityProduct, baseActivityProduct),
    cegFastTracks = Seq.empty,
    addOns = Seq.empty,
    bookingFlow = BookingFlow.SingleActivity,
    commonPayment = None,
    isBookingFromCart = None
  )

  val defaultActivityProductModel = ActivityProductModel(
    product = BaseProductModel(
      booking = BaseBooking(
        bookingId = defaultBookingId,
        itineraryId = defaultItineraryId,
        multiProductId = 10,
        productId = defaultProductId,
        bookingDate = defaultStaticDateTime,
        bookingStartDate = LocalDateTime.MIN,
        bookingEndDate = None,
        bookingConfirmationDate = None,
        isTestBooking = false,
        paymentModel = 1,
        bookingStateId = CreatedBookingStatus.BookingProcessing.id,
        postBookingStateId = None,
        rejectReasonCode = None,
        rejectReasonMsg = None,
        recStatus = 1,
        recCreatedWhen = LocalDateTime.MIN,
        recCreatedBy = "c4b4acf8-9bf9-4d77-bf43-dc929cda582d",
        recModifiedWhen = None,
        recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d"),
        productTypeId = ProductType.Activity.id
      ),
      bookingGeos = List(
        BaseBookingGeo(
          bookingGeoId = defaultGeoId,
          bookingId = defaultBookingId,
          bookingOfferId = 0,
          geoType = GeoType.GeoType.GOOGLE,
          locationType = LocationType.LocationType.PICKUP_LOCATION,
          cityId = None,
          countryId = None,
          geoRef = Some("GOOGLE PROVIDED REFERENCE"),
          addressLine1 = Some("line1"),
          addressLine2 = None,
          locationName = None,
          postalCode = Some("postalcode"),
          supplierGeoRef = Some("VIATOR PROVIDED REFERENCE"),
          locationInstructions = None
        ),
        BaseBookingGeo(
          bookingGeoId = defaultGeoId + 1,
          bookingId = defaultBookingId,
          bookingOfferId = 0,
          geoType = GeoType.GeoType.SUPPLIER,
          locationType = LocationType.LocationType.ARRIVAL_DROP_OFF,
          cityId = None,
          countryId = None,
          geoRef = Some("SUPPLIER PROVIDED REFERENCE"),
          addressLine1 = Some("line1"),
          addressLine2 = None,
          locationName = None,
          postalCode = Some("postalcode"),
          supplierGeoRef = Some("SUPPLIER PROVIDED REFERENCE"),
          locationInstructions = None
        ),
        BaseBookingGeo(
          bookingGeoId = defaultGeoId + 2,
          bookingId = defaultBookingId,
          bookingOfferId = 0,
          geoType = GeoType.GeoType.SUPPLIER,
          locationType = LocationType.LocationType.UNKNOWN,
          cityId = None,
          countryId = None,
          geoRef = Some("LOC-6eKJ+or5y8o99Qw0C8xWyIgZ7SHfDGWlg10ctCEuuw0="),
          addressLine1 = Some("36 Phethburi Soi 13 , Phethburi Road, Phaya Thai, Rajthewee"),
          addressLine2 = None,
          locationName = None,
          postalCode = Some("10400"),
          supplierGeoRef = Some("LOC-6eKJ+or5y8o99Qw0C8xWyIgZ7SHfDGWlg10ctCEuuw0="),
          locationInstructions = None
        )
      ),
      bookingMetas = List(
        BaseBookingMeta(
          metaId = defaultMetaId,
          bookingId = defaultBookingId,
          metaName = Some("ACTIVITY_PRODUCT_TITLE"),
          metaType = MetaType.MetaType.STRING,
          metaValue = Some("Activity Title"),
          recStatus = 1,
          recModifiedWhen = LocalDateTime.MIN,
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d"),
          version = 1,
          metaTypeString = Some("STRING")
        ),
        BaseBookingMeta(
          metaId = defaultMetaId + 1,
          bookingId = defaultBookingId,
          metaName = Some("ACTIVITY_PRODUCT_OFFER_TITLE"),
          metaType = MetaType.MetaType.STRING,
          metaValue = Some("Activity Offer Title"),
          recStatus = 1,
          recModifiedWhen = LocalDateTime.MIN,
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d"),
          version = 1,
          metaTypeString = Some("STRING")
        ),
        BaseBookingMeta(
          metaId = defaultMetaId + 2,
          bookingId = defaultBookingId,
          metaName = Some("ACTIVITY_CONTENT_LANGUAGE"),
          metaType = MetaType.MetaType.NUMBER,
          metaValue = Some("1"),
          recStatus = 1,
          recModifiedWhen = LocalDateTime.MIN,
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d"),
          version = 1,
          metaTypeString = Some("NUMBER")
        ),
        BaseBookingMeta(
          metaId = defaultMetaId + 3,
          bookingId = defaultBookingId,
          metaName = Some("ACTIVITY_CONFIRM_MINUTES"),
          metaType = MetaType.MetaType.NUMBER,
          metaValue = Some("0"),
          recStatus = 1,
          recModifiedWhen = LocalDateTime.MIN,
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d"),
          version = 1,
          metaTypeString = Some("NUMBER")
        ),
        BaseBookingMeta(
          metaId = defaultMetaId + 4,
          bookingId = defaultBookingId,
          metaName = Some("ACTIVITY_VOUCHER_MINUTES"),
          metaType = MetaType.MetaType.NUMBER,
          metaValue = Some("0"),
          recStatus = 1,
          recModifiedWhen = LocalDateTime.MIN,
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d"),
          version = 1,
          metaTypeString = Some("NUMBER")
        ),
        BaseBookingMeta(
          metaId = defaultMetaId + 5,
          bookingId = defaultBookingId,
          metaName = Some("ACTIVITY_COUNTRY_ID"),
          metaType = MetaType.MetaType.NUMBER,
          metaValue = Some("1"),
          recStatus = 1,
          recModifiedWhen = LocalDateTime.MIN,
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d"),
          version = 1,
          metaTypeString = Some("NUMBER")
        ),
        BaseBookingMeta(
          metaId = defaultMetaId + 6,
          bookingId = defaultBookingId,
          metaName = Some("ACTIVITY_BOOKING_ANSWERS"),
          metaType = MetaType.MetaType.JSON,
          metaValue = Some(
            s"""[{"bookingQuestionCode":"PICKUP_POINT","questionAns":"VIATOR PROVIDED REFERENCE","ansUnit":"LOCATION_REFERENCE"},{"bookingQuestionCode":"ARRIVAL_DROP_OFF","questionAns":"SUPPLIER PROVIDED REFERENCE","ansUnit":"LOCATION_REFERENCE"},{"bookingQuestionCode":"UNKNOWN","questionAns":"LOC-6eKJ+or5y8o99Qw0C8xWyIgZ7SHfDGWlg10ctCEuuw0=","ansUnit":"LOCATION_REFERENCE"},{"bookingQuestionCode":"DEPARTURE_PICKUP","questionAns":"SOMEWHERE I BELONG","ansUnit":"FREETEXT"},{"bookingQuestionCode":"SPECIAL_REQUIREMENT","questionAns":"Need wheelchair support","ansUnit":""}]"""
          ),
          recStatus = 1,
          recModifiedWhen = LocalDateTime.MIN,
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d"),
          version = 1,
          metaTypeString = Some("JSON")
        ),
        BaseBookingMeta(
          metaId = defaultMetaId + 7,
          bookingId = defaultBookingId,
          metaName = Some("ACTIVITY_LANGUAGE_GUIDE"),
          metaType = MetaType.MetaType.JSON,
          metaValue = Some(s"""{"type":"GUIDE","language":"en"}"""),
          recStatus = 1,
          recModifiedWhen = LocalDateTime.MIN,
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d"),
          version = 1,
          metaTypeString = Some("JSON")
        ),
        BaseBookingMeta(
          metaId = defaultMetaId + 8,
          bookingId = defaultBookingId,
          metaName = Some("ACTIVITY_JSON_META"),
          metaType = MetaType.MetaType.JSON,
          metaValue = Some(s"""{"masterActivityId":"1234","masterSupplierId":50001}"""),
          recStatus = 1,
          recModifiedWhen = LocalDateTime.MIN,
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d"),
          version = 1,
          metaTypeString = Some("JSON")
        )
      ),
      bookingOffers = List(
        BaseBookingOffer(
          bookingId = defaultBookingId,
          bookingOfferId = 0,
          offerTypeId = 2,
          productOfferId = defaultOfferId,
          quantity = 3,
          startDate = LocalDateTime.MIN,
          endDate = Some(LocalDateTime.MIN),
          specialRequest = Some("Need wheelchair support"),
          recStatus = 1,
          startTime = Some(TimeOfDay(hours = 9, minutes = 0, seconds = 0)),
          endTime = None,
          recCreatedWhen = Some(LocalDateTime.MIN),
          recCreatedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d"),
          recModifiedWhen = Some(LocalDateTime.MIN),
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d")
        )
      ),
      bookingPaxes = List(
        BaseBookingPax(
          bookingId = defaultBookingId,
          paxId = 5,
          paxTypeCode = PaxTypeCode.PaxTypeCode.ADULT,
          isPrimary = true,
          recCreatedWhen = Some(LocalDateTime.MIN),
          recCreatedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d"),
          recModifiedWhen = Some(LocalDateTime.MIN),
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d")
        )
      ),
      bookingPriceSummaries = List(
        BaseBookingPriceSummary(
          bookingId = defaultBookingId,
          priceType = 1,
          quantity = 1,
          displayCurrency = "USD",
          baseFare = 202.9,
          taxAmount = 0.0,
          feeAmount = 0.0,
          surchargeAmount = 0.0,
          baseDiscount = 0.0,
          totalFare = 202.9,
          surchargeDetails = Some(""),
          recStatus = 1,
          recModifiedWhen = None,
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d")
        ),
        BaseBookingPriceSummary(
          bookingId = defaultBookingId,
          priceType = 3,
          quantity = 2,
          displayCurrency = "USD",
          baseFare = 81.4,
          taxAmount = 0.0,
          feeAmount = 0.0,
          surchargeAmount = 0.0,
          baseDiscount = 0.0,
          totalFare = 81.4,
          surchargeDetails = Some(""),
          recStatus = 1,
          recModifiedWhen = None,
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d")
        ),
        BaseBookingPriceSummary(
          bookingId = defaultBookingId,
          priceType = 4,
          quantity = 1,
          displayCurrency = "USD",
          baseFare = 40.11,
          taxAmount = 0.0,
          feeAmount = 0.0,
          surchargeAmount = 0.0,
          baseDiscount = 0.0,
          totalFare = 40.11,
          surchargeDetails = Some(""),
          recStatus = 1,
          recModifiedWhen = None,
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d")
        )
      ),
      cancellationInfo = Some(
        BaseCancellationInfo(
          bookingId = defaultBookingId,
          isCancelled = 0,
          cancellationPolicyCode = Some(
            "[{\"penaltyCode\":\"100P\",\"hoursFrom\":168,\"hoursUntil\":0},{\"penaltyCode\":\"100P\",\"hoursFrom\":0,\"hoursUntil\":-1}]"
          ),
          cancellationDate = None,
          voidWindowUntil = None,
          recCreatedWhen = Some(LocalDateTime.MIN),
          recCreatedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d"),
          recModifiedWhen = Some(LocalDateTime.MIN),
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d")
        )
      ),
      clientInfo = Some(
        BaseClientInfo(
          bookingId = defaultBookingId,
          whitelabelId = 1,
          languageId = 1,
          customerOrigin = None,
          cid = None,
          storefrontId = Some(3),
          platformId = Some(1),
          trackingCookieId = Some("awfawgagb4b43"),
          trackingCookieDate = Some(LocalDateTime.MIN),
          trackingTag = None,
          sessionId = Some("g4d4rbnsmgp0lyybci2dfdkr"),
          bookingSessionId = None,
          clientInfo = None,
          clientIpAddress = Some("************")
        )
      ),
      financeInfo = Some(
        BaseFinanceInfo(
          bookingId = defaultBookingId,
          merchantOfRecord = 5632,
          rateContract = 0,
          revenue = 0
        )
      ),
      fraudInfo = None,
      supplierInfo = Some(
        BaseSupplierInfo(
          bookingId = defaultBookingId,
          supplierId = defaultSupplierId,
          subSupplierId = None,
          supplierBookingId = "",
          providerCode = Some("Barcelona Local Experiences"),
          supplierSpecificData = Some(
            "{\"activityCode\":\"ACT1234\",\"offerCode\":\"TG1\",\"currency\":\"USD\",\"otherSpecificData\":{}}"
          ),
          supplierStatusCode = None,
          supplierStatusMessage = None,
          supplierCommissionAmount = Some(0.0),
          supplierCommissionPercentage = None,
          isAcknowledged = None,
          acknowledgedBy = None,
          acknowledgedDate = None,
          recCreatedWhen = Some(LocalDateTime.MIN),
          recCreatedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d"),
          recModifiedWhen = Some(LocalDateTime.MIN),
          recModifiedBy = Some("c4b4acf8-9bf9-4d77-bf43-dc929cda582d")
        )
      ),
      essInfo = Some(
        BaseBookingEssInfo(
          bookingEssInfoId = 55555L,
          bookingId = 54321,
          userTaxCountryId = 111,
          recStatus = 1,
          recCreatedWhen = LocalDateTime.MIN,
          recCreatedBy = "c4b4acf8-9bf9-4d77-bf43-dc929cda582d",
          recModifiedWhen = Some(LocalDateTime.MIN),
          recModifiedBy = None,
          bookerResidenceCountryId = Some(8889),
          paymentInstrumentCountryId = Some(8890),
          ipAddressCountryId = Some(8891)
        )
      )
    )
  )

  val defaultActivityPax = ActivityPax(
    id = 5,
    title = Some("Mr"),
    firstname = Some("John"),
    lastname = Some("Doe"),
    primary = true,
    middlename = Some("Lennon"),
    paxType = PaxType.Child,
    birthDate = Some(LocalDate.parse("2020-10-15")),
    gender = Some("Male"),
    nationalityId = Some(10),
    passportNumber = Some("gubasdljfnsf"),
    passportExpires = Some(LocalDate.parse("2020-10-15")),
    email = Some("<EMAIL>"),
    phoneNumber = Some("08123456789"),
    meta = Seq(ActivityPaxMetaInfo("Height", "180", "CM"), ActivityPaxMetaInfo("Weight", "14", "ST"))
  )

  val defaultCustomer = Customer(
    title = "mr",
    firstname = "Guest",
    lastname = "Lastname",
    email = "<EMAIL>",
    countryId = 106,
    newsletter = true
  )

  val defaultCreditCard = Some(
    new CreditCard(
      creditcardId = 0,
      creditcardType = PaymentMethod.Visa,
      cardholderName = "TEST USER",
      creditcardNumber = "****************",
      expiryDate = Some("11/23"),
      securityCode = Some("123"),
      issueBankCountryId = 0,
      issueBank = "string",
      billingAddress1 = "string",
      billingAddress2 = "string",
      billingCity = "string",
      billingState = "string",
      billingCountryId = Some(0),
      billingPostalcode = "string",
      chargeOption = ChargeOption.PayNow,
      fullyAuthDate = DateTime.parse("2018-11-16T11:37:10.645Z"),
      fullyChargeDate = DateTime.parse("2018-11-16T11:37:10.645Z"),
      payment3DS = None,
      internalToken = None,
      isSaveCCOF = false,
      customerIdCard = None,
      customerIdCardType = None,
      otp = None,
      phoneNumber = None,
      paymentOption = 1
    )
  )

  val defaultBookingWorkflowAction = BookingWorkflowAction(
    actionId = 1,
    itineraryId = defaultItineraryId,
    bookingType = Some(1),
    bookingId = Some(defaultBookingId),
    memberId = 1,
    actionTypeId = 1,
    correlationId = "1",
    requestId = "1",
    workflowId = 1,
    workflowStateId = 1,
    productTypeId = Some(1),
    stateSchemaVersion = 1,
    state = "",
    storefrontId = Some(1),
    languageId = Some(1)
  )

  val defaultActivityBookingMessage = ActivityBookingMessage(
    itineraryId = defaultItineraryId,
    memberId = 1,
    bookingId = defaultBookingId,
    bookingDateTime = "2021-08-19 18:05:30.0",
    bookingState = "BookingProcessing",
    action = BookingActionForMessage(
      actionId = 1,
      itineraryId = defaultItineraryId,
      bookingType = Some(1),
      bookingId = defaultBookingId,
      memberId = 1,
      actionTypeId = 1,
      correlationId = "1",
      requestId = "1",
      workflowId = 1,
      workflowStateId = 1,
      stateSchemaVersion = 1,
      state = "",
      storefrontId = Some(1),
      languageId = Some(1)
    ),
    origin = None,
    cookieId = Some("awfawgagb4b43"),
    platformId = Some(1)
  )

  val defaultActivityBookingQuestions = List(
    ActivityBookingQuestion(
      id = 9,
      code = SPECIAL_REQUIREMENT,
      dataType = STRING,
      groupType = PER_BOOKING,
      isMasterQuestion = true,
      isOptional = true,
      isPII = false,
      maxLength = 1000,
      prefillAnswers = List(),
      units = List(),
      answerRanges = Seq.empty
    ),
    ActivityBookingQuestion(
      id = 2,
      code = ActivityBookingQuestionEnums.Code.PICKUP_POINT,
      dataType = ActivityBookingQuestionEnums.DataType.STRING,
      groupType = ActivityBookingQuestionEnums.GroupType.PER_BOOKING,
      isMasterQuestion = true,
      isOptional = false,
      isPII = false,
      maxLength = 1024,
      prefillAnswers = Seq.empty,
      units = Seq(ActivityBookingQuestionEnums.Unit.LOCATION_REFERENCE),
      answerRanges = Seq.empty
    ),
    ActivityBookingQuestion(
      id = 3,
      code = ActivityBookingQuestionEnums.Code.FIRSTNAME,
      dataType = ActivityBookingQuestionEnums.DataType.STRING,
      groupType = ActivityBookingQuestionEnums.GroupType.PER_PERSON,
      isMasterQuestion = true,
      isOptional = false,
      isPII = true,
      maxLength = 1024,
      prefillAnswers = Seq.empty,
      units = Seq.empty,
      answerRanges = Seq.empty
    ),
    ActivityBookingQuestion(
      id = 4,
      code = ActivityBookingQuestionEnums.Code.ARRIVAL_DROP_OFF,
      dataType = ActivityBookingQuestionEnums.DataType.STRING,
      groupType = ActivityBookingQuestionEnums.GroupType.PER_BOOKING,
      isMasterQuestion = false,
      isOptional = false,
      isPII = false,
      maxLength = 1024,
      prefillAnswers = Seq.empty,
      units = Seq(ActivityBookingQuestionEnums.Unit.LOCATION_REFERENCE),
      answerRanges = Seq.empty
    ),
    ActivityBookingQuestion(
      id = 5,
      code = ActivityBookingQuestionEnums.Code.DEPARTURE_PICKUP,
      dataType = ActivityBookingQuestionEnums.DataType.STRING,
      groupType = ActivityBookingQuestionEnums.GroupType.PER_BOOKING,
      isMasterQuestion = false,
      isOptional = false,
      isPII = false,
      maxLength = 1024,
      prefillAnswers = Seq.empty,
      units = Seq(ActivityBookingQuestionEnums.Unit.FREETEXT),
      answerRanges = Seq.empty
    ),
    ActivityBookingQuestion(
      id = 6,
      code = ActivityBookingQuestionEnums.Code.UNKNOWN,
      dataType = ActivityBookingQuestionEnums.DataType.STRING,
      groupType = ActivityBookingQuestionEnums.GroupType.PER_BOOKING,
      isMasterQuestion = false,
      isOptional = false,
      isPII = false,
      maxLength = 1024,
      prefillAnswers = Seq.empty,
      units = Seq(ActivityBookingQuestionEnums.Unit.LOCATION_REFERENCE),
      answerRanges = Seq.empty
    ),
    ActivityBookingQuestion(
      id = 7,
      code = ActivityBookingQuestionEnums.Code.EYESIGHT_PRESCRIPTION,
      dataType = ActivityBookingQuestionEnums.DataType.NUMBER,
      groupType = ActivityBookingQuestionEnums.GroupType.PER_PERSON,
      isMasterQuestion = false,
      isOptional = false,
      isPII = false,
      maxLength = 1024,
      prefillAnswers = Seq.empty,
      units = Seq(ActivityBookingQuestionEnums.Unit.DIOPTERS),
      answerRanges = Seq(
        ActivityAnswerRange(min = 100, max = 500, unit = ActivityBookingQuestionEnums.Unit.DIOPTERS, interval = 100)
      )
    )
  )

  val defaultActivityBookingAnswers = Seq(
    ActivityBookingAnswer(ActivityBookingQuestionEnums.Code.CONTACT_INFO_ADDRESS.toString, "some address", ""),
    ActivityBookingAnswer(ActivityBookingQuestionEnums.Code.CONTACT_INFO_CITY.toString, "some city", ""),
    ActivityBookingAnswer(ActivityBookingQuestionEnums.Code.CONTACT_INFO_ZIPCODE.toString, "some zipcode", "")
  )

  def defaultActivityBookingToken(
      bookingQuestionList: List[ActivityBookingQuestion] = defaultActivityBookingQuestions
  ) = ActivityBookingToken(
    activityInfo = ActivityBookingInfo(
      activityId = defaultProductId,
      offerId = defaultOfferId.toInt,
      activityTitle = "Activity Title",
      paymentModel = Merchant,
      supplierInfo = ActivitySupplierInfo(
        supplierId = defaultSupplierId,
        providerCode = "Barcelona Local Experiences",
        supplierSpecificData =
          "{\"activityCode\":\"ACT1234\",\"offerCode\":\"TG1\",\"currency\":\"USD\",\"otherSpecificData\":{}}",
        supplierCommissionAmount = 0.0
      ),
      bookingPriceSummary = List(
        ActivityBookingPriceSummary(
          priceType = PerBooking,
          quantity = 1,
          displayCurrency = "USD",
          baseFare = 202.9,
          taxAmount = 0.0,
          feeAmount = 0.0,
          surchargeAmount = 0.0,
          baseDiscount = 0.0,
          totalFare = 202.9,
          surchargeDetails = ""
        ),
        ActivityBookingPriceSummary(
          priceType = PerAdult,
          quantity = 2,
          displayCurrency = "USD",
          baseFare = 81.4,
          taxAmount = 0.0,
          feeAmount = 0.0,
          surchargeAmount = 0.0,
          baseDiscount = 0.0,
          totalFare = 81.4,
          surchargeDetails = ""
        ),
        ActivityBookingPriceSummary(
          priceType = PerChild,
          quantity = 1,
          displayCurrency = "USD",
          baseFare = 40.11,
          taxAmount = 0.0,
          feeAmount = 0.0,
          surchargeAmount = 0.0,
          baseDiscount = 0.0,
          totalFare = 40.11,
          surchargeDetails = ""
        )
      ),
      cancellationInfo = ActivityCancellationInfo(
        cancellationPolicyCode =
          "[{\"penaltyCode\":\"100P\",\"hoursFrom\":168,\"hoursUntil\":0},{\"penaltyCode\":\"100P\",\"hoursFrom\":0,\"hoursUntil\":-1}]",
        Some(CancellationClass.FreeCancellation)
      ),
      bookingOffer = ActivityBookingOffer(
        productOfferId = defaultOfferId,
        productOfferTitle = "Activity Offer Title",
        offerType = PerPerson,
        quantity = 3,
        startDate = formatter.parseDateTime("2021-06-30T05:00:00.000Z"),
        endDate = None,
        startTime = Some(ActivityTime(hours = 9, minutes = 0, seconds = 0)),
        endTime = None
      ),
      paxSummary = PaxSummary(
        paxInfo = List(
          PaxInfo(
            paxType = ADULT,
            quantity = 2
          ),
          PaxInfo(
            paxType = CHILD,
            quantity = 1
          )
        ),
        totalPax = 3
      ),
      bookingQuestions = bookingQuestionList,
      languageGuides = Seq(
        ActivityLanguageGuide(languageGuideType = ActivityLanguageGuideEnums.Type.GUIDE, languageCode = "en"),
        ActivityLanguageGuide(languageGuideType = ActivityLanguageGuideEnums.Type.GUIDE, languageCode = "ja")
      ),
      priceBreakdown = List(
        PriceBreakdown(
          itemId = 1,
          date = "2021-06-24",
          typeId = 301,
          quantity = 1,
          usdAmount = 173.46,
          requestAmount = 173.46,
          requestedCurrency = "USD",
          requestedExchangeRate = 1.0,
          taxFeeId = 0,
          vendorAmount = 147.04,
          vendorCurrency = "EUR",
          vendorExchangeRate = 0.8477
        ),
        PriceBreakdown(
          itemId = 12,
          date = "2021-06-24",
          typeId = 301,
          quantity = 1,
          usdAmount = 202.9,
          requestAmount = 202.9,
          requestedCurrency = "USD",
          requestedExchangeRate = 1.0,
          taxFeeId = 0,
          vendorAmount = 172.0,
          vendorCurrency = "EUR",
          vendorExchangeRate = 0.8477
        ),
        PriceBreakdown(
          itemId = 41,
          date = "2021-06-24",
          typeId = 301,
          quantity = 1,
          usdAmount = 0.0,
          requestAmount = 0.0,
          requestedCurrency = "USD",
          requestedExchangeRate = 1.0,
          taxFeeId = 0,
          vendorAmount = 0.0,
          vendorCurrency = "EUR",
          vendorExchangeRate = 0.8477
        ),
        PriceBreakdown(
          itemId = 10,
          date = "2021-06-24",
          typeId = 301,
          quantity = 1,
          usdAmount = 173.46,
          requestAmount = 173.46,
          requestedCurrency = "USD",
          requestedExchangeRate = 1.0,
          taxFeeId = 0,
          vendorAmount = 147.04,
          vendorCurrency = "EUR",
          vendorExchangeRate = 0.8477
        ),
        PriceBreakdown(
          itemId = 11,
          date = "2021-06-24",
          typeId = 301,
          quantity = 1,
          usdAmount = 202.9,
          requestAmount = 202.9,
          requestedCurrency = "USD",
          requestedExchangeRate = 1.0,
          taxFeeId = 0,
          vendorAmount = 172.0,
          vendorCurrency = "EUR",
          vendorExchangeRate = 0.8477
        ),
        PriceBreakdown(
          itemId = 5,
          date = "2021-06-24",
          typeId = 301,
          quantity = 1,
          usdAmount = 0.0,
          requestAmount = 0.0,
          requestedCurrency = "USD",
          requestedExchangeRate = 1.0,
          taxFeeId = 0,
          vendorAmount = 0.0,
          vendorCurrency = "EUR",
          vendorExchangeRate = 0.8477
        ),
        PriceBreakdown(
          itemId = 3,
          date = "2021-06-24",
          typeId = 301,
          quantity = 1,
          usdAmount = 29.44,
          requestAmount = 29.44,
          requestedCurrency = "USD",
          requestedExchangeRate = 1.0,
          taxFeeId = 0,
          vendorAmount = 24.96,
          vendorCurrency = "EUR",
          vendorExchangeRate = 0.8477
        ),
        PriceBreakdown(
          itemId = 3,
          date = "2021-06-24",
          typeId = BreakDownTypeID.EssFastTrack.id,
          quantity = 1,
          usdAmount = 1.00,
          requestAmount = 100,
          requestedCurrency = "THB",
          requestedExchangeRate = 1.0,
          taxFeeId = 0,
          vendorAmount = 24.96,
          vendorCurrency = "THB",
          vendorExchangeRate = 0.8477
        )
      ),
      contentLanguageId = 1,
      confirmMinutes = 0,
      voucherMinutes = 0,
      countryId = 1,
      metadata = Some("""{"masterActivityId":"1234","masterSupplierId":50001}""")
    ),
    expiresAt = formatter.parseDateTime("2021-06-24T02:51:28.426+07:00"),
    paymentAmount = Some(
      PaymentAmount(
        paymentCurrency = "USD",
        paymentAmount = 202.9,
        paymentAmountUSD = 202.9,
        exchangeRate = 1.0,
        siteExchangeRate = 1.0,
        upliftExchangeRate = 1.0,
        destinationCurrency = Some("EUR"),
        destinationExchangeRate = 0.8477,
        paymentToken = None
      )
    ),
    productPayment = Some(
      ProductPaymentInfo(
        agency = None,
        payNow = Some(
          PayNowProductPayment(
            payment = ProductPayment(
              paymentAmount = 202.9,
              paymentAmountUsd = 202.9,
              paymentCurrency = "USD",
              siteExchangeRate = Some(1.0),
              upliftAmount = None,
              upliftExchangeRate = None,
              exchangeRateOption = None
            ),
            isBundleCharge = true
          )
        ),
        payLater = None,
        accountingEntity = Some(
          AccountingEntity(
            merchantOfRecord = 5632,
            rateContract = 0,
            revenue = 0,
            argument = None
          )
        ),
        points = Vector(
          com.agoda.mpb.common.models.state.Points(
            pointType = com.agoda.mpb.common.PointsType.RMMiles,
            pointAttributes = PointsAttributes(
              currency = "USD",
              amount = 0.0,
              localCurrency = Some("USD"),
              localAmount = Some(0.0),
              externalLoyaltyInfo = Some(
                ExternalLoyaltyInfo(
                  loyaltyToken = Some("NONE"),
                  fiatUserPayable = Some(202.9),
                  userPointsPayable = Some(0.0),
                  partnerClaim = None
                )
              )
            )
          )
        ),
        isTokenEnabled = None
      )
    ),
    productTokenKey = Some("1"),
    promotionInfo = Some(
      DisplayPromocodeItem(
        campaignId = 101,
        promotionCode = "COUPON",
        discountedValue = -1.0,
        isApplied = true,
        isAllowPricePeek = false,
        isAutoApply = false,
        isAssignedWallet = false
      )
    )
  )
  val defaultMultiProductBookingGroups = MultiProductBookingGroupDBModel(
    bookingId = defaultBookingId,
    itineraryId = defaultItineraryId,
    cartId = -3,
    packageId = Some(-4)
  )

  val enigmaPax = EnigmaBaseBookingPax(
    id = UUID.randomUUID,
    bookingId = 1,
    paxId = defaultActivityPax.id,
    productType = ProductType.Activity.id.toShort,
    paxType = defaultActivityPax.paxType.id,
    isPrimary = defaultActivityPax.primary,
    title = defaultActivityPax.title,
    firstName = defaultActivityPax.firstname,
    middleName = defaultActivityPax.middlename,
    lastName = defaultActivityPax.lastname,
    gender = defaultActivityPax.gender,
    age = None, // Not yet captured
    phoneNumber = defaultActivityPax.phoneNumber,
    phoneCountryCode = defaultActivityPax.phoneCountryCode,
    emailAddress = defaultActivityPax.email,
    passportNumber = defaultActivityPax.passportNumber,
    passportCountryId = defaultActivityPax.nationalityId,
    birthDateLocal = defaultActivityPax.birthDate.map(JodaToJavaDateTimeConversions.toJavaLocalDate),
    passportExpiryLocal = defaultActivityPax.passportExpires.map(JodaToJavaDateTimeConversions.toJavaLocalDate),
    ticket = None,
    baseMetadata = None,
    metadata = Metadata(
      status = RecStatus.Active,
      createdBy = UUID.randomUUID,
      createdWhen = Instant.now(),
      modifiedBy = None,
      modifiedWhen = None
    )
  )
}
