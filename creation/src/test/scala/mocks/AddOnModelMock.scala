package mocks

import com.agoda.bapi.common.message.creation.{BreakDownTypeID, CreateBookingRequest, CreatedBookingStatus}
import com.agoda.bapi.common.model.BreakdownItemType
import com.agoda.bapi.common.model.base.BaseBookingRelationshipInternal
import com.agoda.bapi.common.model.booking.BookingActionForMessage
import com.agoda.bapi.common.model.booking.local.BookingWorkflowAction
import com.agoda.bapi.common.model.flight.flightModel.{BookingPaymentState, ItineraryHistory, MultiProductItinerary, PaymentState}
import com.agoda.bapi.common.model.flight.history.ActionType
import com.agoda.bapi.common.model.multiproduct.{MultiProductBookingGroupDBModel, MultiProductInfoDBModel}
import com.agoda.bapi.common.model.product.BookingFlow
import com.agoda.bapi.common.util.JodaToJavaDateTimeConversions
import com.agoda.bapi.creation.CreateMultiBookingHelper
import com.agoda.bapi.creation.model.cegfasttrack.messaging.CegFastTrackBookingMessage
import com.agoda.bapi.creation.model.multi.MultiProductsRequest
import com.agoda.capi.enigma.shared_model.common.RecStatus
import com.agoda.mpb.common.MultiProductType
import com.agoda.mpb.common.models.state.ProductType
import com.agoda.mpbe.state.booking._
import com.agoda.mpbe.state.common.enums.BookingType.BookingType
import com.agoda.mpbe.state.product.addOn.AddOnProductModel
import com.agoda.mpbe.state.product.cegFastTrack.CegFastTrackProductModel
import com.agoda.mpbe.state.product.common.FinancialBreakdown
import org.joda.time.DateTime
import org.joda.time.format.{DateTimeFormat, DateTimeFormatter}

import java.sql.Timestamp
import java.time.LocalDateTime

trait AddOnModelMock extends CreateMultiBookingHelper {
  private val formatter: DateTimeFormatter = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZZ")

  val defaultBookingId   = 54321L
  val defaultItineraryId = 12345L
  val defaultProductId   = 26
  val defaultOfferId     = 11111L
  val defaultMetaId      = 22222
  val defaultGeoId       = 33333
  val defaultPaxId       = 44444
  val defaultEssInfoId   = 55555L
  val defaultSupplierId  = 50001
  val defaultActionId    = 1234L
  val bapiIdentifierUuid = "c4b4acf8-9bf9-4d77-bf43-dc929cda582d"

  val cegFastTrackMultiProductInfo = MultiProductInfoDBModel(1, MultiProductType.CEGFastTrack)

  val baseBookingDateTime: DateTime = DateTime.now()
  val baseStartDateTime: DateTime   = DateTime.parse("2019-08-02T00:00:00")
  val baseEndDateTime: DateTime     = DateTime.parse("2019-09-08T00:00:00")

  val baseBookingLocalDateTime: LocalDateTime = JodaToJavaDateTimeConversions.toJavaLocalDateTime(
    baseBookingDateTime.toLocalDateTime
  )
  val baseStartLocalDateTime: LocalDateTime = JodaToJavaDateTimeConversions.toJavaLocalDateTime(
    baseStartDateTime.toLocalDateTime
  )
  val baseEndLocalDateTime: LocalDateTime = JodaToJavaDateTimeConversions.toJavaLocalDateTime(
    baseEndDateTime.toLocalDateTime
  )

  val baseCegFastTrackRequest: CreateBookingRequest = baseMultiProductReq.copy(
    userContext = baseUserContext
  )

  val trackingCookieDateOpt: Option[LocalDateTime] = Some(
    JodaToJavaDateTimeConversions.toJavaLocalDateTime(baseCegFastTrackRequest.trackingCookieDate.toLocalDateTime)
  )

  val defaultMultiProductItinerary = MultiProductItinerary(
    itineraryId = defaultItineraryId,
    memberId = 1,
    recStatus = Some(1),
    recCreatedWhen = Some(DateTime.parse("2019-08-02T16:01")),
    recModifiedWhen = Some(DateTime.parse("2019-08-02T16:01"))
  )

  val defaultMultiProductBookingGroups = MultiProductBookingGroupDBModel(
    bookingId = defaultBookingId,
    itineraryId = defaultItineraryId,
    cartId = -3,
    packageId = Some(-4)
  )

  val defaultItineraryHistory = ItineraryHistory(
    actionId = 0,
    itineraryId = 0,
    bookingType = None,
    bookingId = None,
    actionType = ActionType.Created.id,
    version = 0,
    actionDate = DateTime.parse("2019-08-02T16:01"),
    parameters = "",
    description = "",
    recStatus = Some(1),
    recCreatedWhen = Some(DateTime.parse("2019-08-02T16:01"))
  )

  val defaultPaymentState = PaymentState(
    referenceId = 1,
    paymentId = 0L,
    itineraryId = 0L,
    actionId = None,
    creditCardId = None,
    transactionDate = DateTime.parse("2019-08-02T16:01"),
    transactionType = 0,
    paymentState = 0,
    referenceNo = "",
    referenceType = 0,
    last4Digits = "",
    paymentMethodId = 0,
    gatewayId = 0,
    transactionId = "",
    paymentCurrency = "",
    paymentAmount = 0,
    amountUsd = 0,
    supplierCurrency = "",
    supplierAmount = 0,
    exchangeRateSupplierToPayment = 0,
    creditCardCurrency = "",
    upliftAmount = 0,
    siteExchangeRate = 0,
    upliftExchangeRate = 0,
    paymentTypeId = None,
    token = None,
    installmentPlanId = None,
    referencePaymentId = None
  )

  val defaultBaseBookingRelationship = BaseBookingRelationshipInternal(
    relationshipId = 1,
    sourceBookingId = 54321,
    targetBookingId = 54322,
    relationshipStatusId = 1,
    relationshipTypeId = 0,
    recStatus = 1,
    recCreatedWhen = baseBookingDateTime,
    recCreatedBy = bapiIdentifierUuid,
    recModifiedWhen = Some(baseBookingDateTime),
    recModifiedBy = Some(bapiIdentifierUuid)
  )

  val defaultBookingPaymentState = BookingPaymentState(
    paymentId = 0,
    bookingId = 0,
    paymentCurrency = "",
    paymentAmount = BigDecimal(0),
    amountUsd = BigDecimal(0),
    recStatus = Some(1),
    recCreatedWhen = Some(DateTime.parse("2019-08-02T16:01")),
    fxiUplift = Some(1.02)
  )

  val addOnDefaultMultiProductsRequest = MultiProductsRequest(
    request = baseCegFastTrackReq,
    requestContext = baseRequestContext,
    properties = Seq.empty,
    flights = Seq.empty,
    vehicles = Seq.empty,
    protections = Seq.empty,
    activities = Seq.empty,
    cegFastTracks = Seq(baseCegFastTrackProduct),
    addOns = Seq.empty,
    bookingFlow = BookingFlow.CegFastTrack,
    commonPayment = None,
    isBookingFromCart = None
  )

  val defaultCegFastTrackBookingWorkflowAction = BookingWorkflowAction(
    actionId = 1,
    itineraryId = defaultItineraryId,
    bookingType = Some(1),
    bookingId = Some(defaultBookingId),
    memberId = 1,
    actionTypeId = 1,
    correlationId = "1",
    requestId = "1",
    workflowId = 1,
    workflowStateId = 1,
    productTypeId = Some(1),
    stateSchemaVersion = 1,
    state = "",
    storefrontId = Some(1),
    languageId = Some(1)
  )

  val defaultCegFastTrackBookingMessage = CegFastTrackBookingMessage(
    itineraryId = defaultItineraryId,
    memberId = 1,
    bookingId = defaultBookingId,
    bookingDateTime = Timestamp.valueOf(baseBookingLocalDateTime).toString,
    bookingState = "BookingProcessing",
    action = BookingActionForMessage(
      actionId = 1,
      itineraryId = defaultItineraryId,
      bookingType = Some(1),
      bookingId = defaultBookingId,
      memberId = 1,
      actionTypeId = 1,
      correlationId = "1",
      requestId = "1",
      workflowId = 1,
      workflowStateId = 1,
      stateSchemaVersion = 1,
      state = "",
      storefrontId = Some(1),
      languageId = Some(1)
    ),
    origin = None,
    cookieId = Some("awfawgagb4b43"),
    platformId = Some(1)
  )

  val defaultCegFastTrackBaseBookingMetas = Seq(
    BaseBookingMeta(
      metaId = 1,
      bookingId = 54321,
      metaName = Some("CEG_FASTTRACK_TIER"),
      metaType = MetaType.MetaType.STRING,
      metaValue = Some("NormalHotelFastTrack"),
      recStatus = 1,
      recModifiedWhen = baseBookingLocalDateTime,
      recModifiedBy = Some(bapiIdentifierUuid)
    )
  )

  val defaultCegFastTrackProductModel = CegFastTrackProductModel(
    product = BaseProductModel(
      booking = BaseBooking(
        bookingId = defaultBookingId,
        itineraryId = defaultItineraryId,
        multiProductId = 7768,
        productId = 0,
        bookingDate = baseBookingLocalDateTime,
        bookingStartDate = baseStartLocalDateTime,
        bookingEndDate = Some(baseEndLocalDateTime),
        bookingConfirmationDate = None,
        isTestBooking = false,
        paymentModel = 1,
        bookingStateId = CreatedBookingStatus.BookingProcessing.id,
        postBookingStateId = None,
        rejectReasonCode = None,
        rejectReasonMsg = None,
        recStatus = RecStatus.Active.value,
        recCreatedWhen = baseBookingLocalDateTime,
        recCreatedBy = bapiIdentifierUuid,
        recModifiedWhen = Some(baseBookingLocalDateTime),
        recModifiedBy = Some(bapiIdentifierUuid),
        productTypeId = ProductType.CEGFastTrack.id
      ),
      bookingMetas = defaultCegFastTrackBaseBookingMetas,
      breakdowns = Seq(
        FinancialBreakdown(
          referenceId = 0,
          breakdownId = 0,
          itineraryId = defaultItineraryId,
          bookingType = BookingType.CegFastTrack,
          bookingId = Option(54321),
          actionId = Option(1234),
          eventDate = DateTime.parse("2022-07-05"),
          itemId = BreakdownItemType.SalesExclusive.id,
          typeId = 1, // Modify later for ESS breakdown type
          taxFeeId = Option(0),
          quantity = 1,
          localCurrency = "USD",
          localAmount = 10,
          exchangeRate = 1.0,
          usdAmount = 10,
          refBreakdownId = None,
          requestedAmount = Option(0),
          vendorExchangeRate = 1.0,
          recStatus = Some(RecStatus.Active.value),
          recCreatedWhen = Some(baseBookingDateTime),
          requestedCurrency = "JPY"
        ),
        FinancialBreakdown(
          referenceId = 0,
          breakdownId = 0,
          itineraryId = defaultItineraryId,
          bookingType = BookingType.CegFastTrack,
          bookingId = Option(54321),
          actionId = Option(1234),
          eventDate = DateTime.parse("2022-07-05"),
          itemId = BreakdownItemType.SalesInclusive.id,
          typeId = 1,
          taxFeeId = Option(0),
          quantity = 1,
          localCurrency = "USD",
          localAmount = 10,
          exchangeRate = 1.0,
          usdAmount = 10,
          refBreakdownId = None,
          requestedAmount = Option(0),
          vendorExchangeRate = 1.0,
          recStatus = Some(RecStatus.Active.value),
          recCreatedWhen = Some(baseBookingDateTime),
          requestedCurrency = "JPY"
        ),
        FinancialBreakdown(
          referenceId = 0,
          breakdownId = 0,
          itineraryId = defaultItineraryId,
          bookingType = BookingType.CegFastTrack,
          bookingId = Option(54321),
          actionId = Option(1234),
          eventDate = DateTime.parse("2022-07-05"),
          itemId = BreakdownItemType.SalesInclusive.id,
          typeId = BreakDownTypeID.EssFastTrack.id,
          taxFeeId = Option(0),
          quantity = 1,
          localCurrency = "THB",
          localAmount = 100,
          exchangeRate = 1.0,
          usdAmount = 1,
          refBreakdownId = None,
          requestedAmount = Option(100),
          vendorExchangeRate = 1.0,
          recStatus = Some(RecStatus.Active.value),
          recCreatedWhen = Some(baseBookingDateTime),
          requestedCurrency = "THB"
        )
      ),
      clientInfo = Some(
        BaseClientInfo(
          bookingId = 54321,
          whitelabelId = 1,
          languageId = 1,
          cid = Some(1556943),
          storefrontId = Some(3),
          platformId = Some(1),
          trackingCookieId = Some("awfawgagb4b43"),
          trackingCookieDate = trackingCookieDateOpt,
          sessionId = Some("g4d4rbnsmgp0lyybci2dfdkr"),
          clientIpAddress = Some("************")
        )
      ),
      essInfo = Some(
        BaseBookingEssInfo(
          bookingEssInfoId = 55555L,
          bookingId = 54321,
          userTaxCountryId = 9999,
          recStatus = RecStatus.Active.value,
          recCreatedWhen = baseBookingLocalDateTime,
          recCreatedBy = bapiIdentifierUuid
        )
      )
    )
  )

  val protectionAddOnDefaultMultiProductsRequest = MultiProductsRequest(
    request = baseMultiProductReq.copy(
      userContext = baseUserContext
    ),
    requestContext = baseRequestContext,
    properties = Seq.empty,
    flights = Seq.empty,
    vehicles = Seq.empty,
    protections = Seq.empty,
    activities = Seq.empty,
    cegFastTracks = Seq.empty,
    addOns = Seq(baseProtectionAddOnProduct),
    bookingFlow = BookingFlow.Cart,
    commonPayment = None,
    isBookingFromCart = None
  )

  val defaultProtectionAddOnBaseBookingMetas = Seq(
    BaseBookingMeta(
      metaId = 1,
      bookingId = defaultBookingId,
      metaName = Some("TRIP_PROTECTION_TYPE"),
      metaType = MetaType.MetaType.STRING,
      metaValue = Some("FLIGHTS"),
      recStatus = 1,
      recModifiedWhen = baseBookingLocalDateTime,
      recModifiedBy = Some(bapiIdentifierUuid)
    )
  )

  val defaulProtectionAddOnProductModel = AddOnProductModel(
    product = BaseProductModel(
      booking = BaseBooking(
        bookingId = defaultBookingId,
        itineraryId = defaultItineraryId,
        multiProductId = 7768,
        productId = 0,
        bookingDate = baseBookingLocalDateTime,
        bookingStartDate = baseStartLocalDateTime,
        bookingEndDate = Some(baseEndLocalDateTime),
        bookingConfirmationDate = None,
        isTestBooking = false,
        paymentModel = 2,
        bookingStateId = CreatedBookingStatus.BookingProcessing.id,
        postBookingStateId = None,
        rejectReasonCode = None,
        rejectReasonMsg = None,
        recStatus = RecStatus.Active.value,
        recCreatedWhen = baseBookingLocalDateTime,
        recCreatedBy = bapiIdentifierUuid,
        recModifiedWhen = Some(baseBookingLocalDateTime),
        recModifiedBy = Some(bapiIdentifierUuid),
        productTypeId = ProductType.TripProtection.id
      ),
      bookingMetas = defaultProtectionAddOnBaseBookingMetas,
      breakdowns = Seq(
        FinancialBreakdown(
          referenceId = 0,
          breakdownId = 0,
          itineraryId = defaultItineraryId,
          bookingType = BookingType.Protection,
          bookingId = Option(defaultBookingId),
          actionId = Option(1234),
          eventDate = DateTime.parse("2022-07-05"),
          itemId = BreakdownItemType.SalesExclusive.id,
          typeId = 1,
          taxFeeId = Option(0),
          quantity = 1,
          localCurrency = "USD",
          localAmount = 10,
          exchangeRate = 1.0,
          usdAmount = 10,
          refBreakdownId = None,
          requestedAmount = Option(0),
          vendorExchangeRate = 1.0,
          recStatus = Some(RecStatus.Active.value),
          recCreatedWhen = Some(baseBookingDateTime),
          requestedCurrency = "REQ_USD"
        ),
        FinancialBreakdown(
          referenceId = 0,
          breakdownId = 0,
          itineraryId = defaultItineraryId,
          bookingType = BookingType.Protection,
          bookingId = Option(defaultBookingId),
          actionId = Option(1234),
          eventDate = DateTime.parse("2022-07-05"),
          itemId = BreakdownItemType.SalesInclusive.id,
          typeId = 1,
          taxFeeId = Option(0),
          quantity = 1,
          localCurrency = "USD",
          localAmount = 10,
          exchangeRate = 1.0,
          usdAmount = 10,
          refBreakdownId = None,
          requestedAmount = Option(0),
          vendorExchangeRate = 1.0,
          recStatus = Some(RecStatus.Active.value),
          recCreatedWhen = Some(baseBookingDateTime),
          requestedCurrency = "REQ_USD"
        ),
        FinancialBreakdown(
          referenceId = 0,
          breakdownId = 0,
          itineraryId = defaultItineraryId,
          bookingType = BookingType.Protection,
          bookingId = Option(defaultBookingId),
          actionId = Option(1234),
          eventDate = DateTime.parse("2022-07-05"),
          itemId = BreakdownItemType.SalesInclusive.id,
          typeId = BreakDownTypeID.EssFastTrack.id,
          taxFeeId = Option(0),
          quantity = 1,
          localCurrency = "THB",
          localAmount = 100,
          exchangeRate = 1.0,
          usdAmount = 1,
          refBreakdownId = None,
          requestedAmount = Option(100),
          vendorExchangeRate = 1.0,
          recStatus = Some(RecStatus.Active.value),
          recCreatedWhen = Some(baseBookingDateTime),
          requestedCurrency = "REQ_THB"
        )
      ),
      clientInfo = Some(
        BaseClientInfo(
          bookingId = defaultBookingId,
          whitelabelId = 1,
          languageId = 1,
          cid = Some(1556943),
          storefrontId = Some(3),
          platformId = Some(1),
          trackingCookieId = Some("awfawgagb4b43"),
          trackingCookieDate = trackingCookieDateOpt,
          sessionId = Some("g4d4rbnsmgp0lyybci2dfdkr"),
          clientIpAddress = Some("************")
        )
      ),
      essInfo = Some(
        BaseBookingEssInfo(
          bookingEssInfoId = 55555L,
          bookingId = 54321,
          userTaxCountryId = 9999,
          recStatus = RecStatus.Active.value,
          recCreatedWhen = baseBookingLocalDateTime,
          recCreatedBy = bapiIdentifierUuid
        )
      ),
      supplierInfo = Some(BaseSupplierInfo(supplierId = 100, supplierSpecificData = Some("SupplierSpecificData")))
    )
  )

  val defaultProtectionAddOnBookingWorkflowAction = BookingWorkflowAction(
    actionId = 1,
    itineraryId = defaultItineraryId,
    bookingType = Some(1),
    bookingId = Some(defaultBookingId),
    memberId = 1,
    actionTypeId = 1,
    correlationId = "1",
    requestId = "1",
    workflowId = 1,
    workflowStateId = 1,
    productTypeId = Some(MultiProductType.SingleProtection.id),
    stateSchemaVersion = 1,
    state = "",
    storefrontId = Some(1),
    languageId = Some(1)
  )
}
